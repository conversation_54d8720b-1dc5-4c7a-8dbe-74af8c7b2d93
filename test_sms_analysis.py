#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import sys

def analyze_sms_debug():
    """分析SMS调试响应"""
    
    # API服务器地址
    api_base_url = "http://medev-stage.ktvsky.com"
    
    print("=== SMS 调试分析脚本 ===\n")
    
    # 测试数据（保持服务端代码不变，只测试当前实现）
    test_cases = [
        {
            "name": "当前SMS实现测试",
            "data": {"phone": "18610210826", "testMode": False},
            "description": "测试当前的SMS发送实现并分析问题原因"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试{i}: {test_case['name']}")
        print(f"描述: {test_case['description']}")
        print(f"请求URL: {api_base_url}/api/sms/debug-send-code")
        print(f"请求体: {json.dumps(test_case['data'], ensure_ascii=False, indent=2)}")
        
        try:
            # 发送请求
            response = requests.post(
                f"{api_base_url}/api/sms/debug-send-code",
                json=test_case['data'],
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"\n响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                # 分析响应数据
                analyze_response_data(data, test_case['name'])
            else:
                print(f"请求失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
        except Exception as e:
            print(f"未知错误: {e}")
        
        print("\n" + "="*80 + "\n")
    
    # 添加详细的问题分析和解决方案
    print_detailed_analysis()

def analyze_response_data(data, test_name=""):
    """分析响应数据"""
    print(f"\n=== {test_name} - 调试信息分析 ===")
    
    if data.get("code") == 0 and "data" in data:
        response_data = data["data"]
        
        # 分析账户信息
        if "account_info" in response_data:
            account_info = response_data["account_info"]
            print("\n云片账户信息:")
            print(f"  账户余额: {account_info.get('balance', '未知')} 元")
            print(f"  绑定手机: {account_info.get('mobile', '未知')}")
            print(f"  账户昵称: {account_info.get('nick', '未知')}")
            print(f"  API版本: {account_info.get('api_version', '未知')}")
            print(f"  告警余额: {account_info.get('alarm_balance', '未知')} 元")
        
        # 分析短信发送信息
        if "sms_debug_info" in response_data:
            sms_info = response_data["sms_debug_info"]
            print("\n短信发送信息:")
            print(f"  发送成功: {sms_info.get('success', False)}")
            print(f"  验证码: {sms_info.get('code', '未知')}")
            print(f"  短信内容: {sms_info.get('text', '未知')}")
            print(f"  HTTP状态码: {sms_info.get('status_code', '未知')}")
            print(f"  API密钥(掩码): {sms_info.get('api_key_masked', '未知')}")
            
            # 分析云片API响应
            if "response" in sms_info:
                yunpian_response = sms_info["response"]
                print(f"  云片返回码: {yunpian_response.get('code', '未知')}")
                print(f"  云片返回消息: {yunpian_response.get('msg', '未知')}")
                
                if "result" in yunpian_response:
                    result = yunpian_response["result"]
                    print(f"  发送数量: {result.get('count', '未知')}")
                    print(f"  消费金额: {result.get('fee', '未知')} 元")
                    print(f"  短信ID: {result.get('sid', '未知')}")
            
            if "error" in sms_info and sms_info["error"]:
                print(f"  错误信息: {sms_info['error']}")
        
        # 分析发送状态
        analyze_sending_status(response_data)
        
    else:
        print(f"API调用失败: {data.get('message', '未知错误')}")

def analyze_sending_status(data):
    """分析发送状态"""
    print("\n=== 发送状态分析 ===")
    
    sms_info = data.get("sms_debug_info", {})
    account_info = data.get("account_info", {})
    
    sms_success = sms_info.get("success", False)
    yunpian_code = sms_info.get("response", {}).get("code", -1)
    account_balance = account_info.get("balance", 0)
    
    if sms_success and yunpian_code == 0 and account_balance > 0:
        print("✅ 短信发送技术层面成功")
        print("⚠️  如果手机未收到短信，可能原因：")
        print("   1. 运营商拦截（【汇金商户通】可能被识别为营销短信）")
        print("   2. 手机安全软件拦截")
        print("   3. 短信进入垃圾短信箱")
        print("   4. 运营商延迟（1-5分钟）")
        print("   5. 手机信号问题")
        
        print("\n=== 问题诊断结果 ===")
        print("技术层面检查通过：")
        print(f"  ✅ 云片账户余额充足：{account_balance} 元")
        print("  ✅ API密钥配置正确")
        print(f"  ✅ 短信发送成功，消费：{sms_info.get('response', {}).get('result', {}).get('fee', '未知')} 元")
        print("  ✅ 云片API返回成功状态")
        
        print("\n手机未收到短信的可能原因：")
        print("🔴 1. 运营商拦截问题（最可能）")
        print("   - 短信签名【汇金商户通】可能被识别为营销短信")
        print("   - 建议更换短信签名或联系云片客服")
        
        print("\n🟡 2. 手机端问题")
        print("   - 检查手机安全软件是否拦截")
        print("   - 查看垃圾短信箱")
        print("   - 确认手机信号正常")
        
        print("\n🟡 3. 运营商延迟")
        print("   - 等待1-5分钟再检查")
        
    else:
        print("❌ 短信发送失败")
        if not sms_success:
            print("   - SMS发送标记为失败")
        if yunpian_code != 0:
            print(f"   - 云片API返回错误码: {yunpian_code}")
        if account_balance <= 0:
            print(f"   - 账户余额不足: {account_balance} 元")

def print_detailed_analysis():
    """打印详细的问题分析"""
    print("=== 🎯 问题根因确认 ===")
    print("\n🚨 发现主要问题：频率限制")
    print("  ❌ 云片API返回错误码 53")  
    print("  ❌ 同一手机号 24小时内超过10条短信限制")
    print("  ❌ 这是云片的防刷机制，不是运营商拦截")
    
    print("\n📊 技术状态确认：")
    print("  ✅ 云片API连接正常")
    print("  ✅ 账户余额充足 (3400+ 元)")
    print("  ✅ API密钥配置正确")
    print("  ⚠️  触发频率限制保护")
    
    print("\n🔍 问题分析：")
    print("🔴 频率限制 (确认原因)")
    print("   - 云片默认限制：同一手机号24小时内最多10条")
    print("   - 这是为了防止短信轰炸和恶意刷短信")
    print("   - 需要等待24小时或使用其他手机号测试")
    
    print("\n📋 立即验证步骤：")
    print("1. 🔄 使用其他手机号码测试（如朋友或家人的号码）")
    print("2. ⏰ 等待24小时后重新测试原号码")
    print("3. 📞 联系云片客服申请提高频率限制")
    print("4. 🔧 在代码中添加频率限制检查和友好提示")

def print_solutions():
    """打印解决方案"""
    print("\n=== 🛠️ 针对频率限制的解决方案 ===")
    print("🚀 立即可执行（今天）：")
    print("1. 📱 使用其他手机号码测试")
    print("   - 找一个朋友或家人的手机号")
    print("   - 确认SMS功能正常工作")
    print("   - 验证是否是频率限制问题")
    
    print("\n2. ⏰ 等待24小时重置")
    print("   - 云片的频率限制是24小时滚动窗口")
    print("   - 明天同一时间可以重新测试")
    print("   - 建议设置提醒，避免忘记")
    
    print("\n🔧 代码优化方案：")
    print("3. 添加频率限制检测")
    print("   - 检测云片错误码53")
    print("   - 给用户友好的错误提示")
    print("   - 避免用户重复尝试")
    
    print("4. 实现发送频率控制")
    print("   - 在应用层记录发送历史")
    print("   - 限制同一手机号的发送频率")
    print("   - 提前预防触发云片限制")
    
    print("\n📞 联系云片客服方案：")
    print("5. 申请提高频率限制 (400-808-3008)")
    print("   - 说明业务需求")
    print("   - 申请提高到每日20-50条")
    print("   - 通常需要1-3个工作日审核")
    
    print("\n🔄 长期优化建议：")
    print("6. 实现多短信服务商策略")
    print("   - 云片作为主要服务商")
    print("   - 阿里云/腾讯云作为备用")
    print("   - 自动切换机制")
    
    print("\n⚠️  重要提醒：")
    print("• ✅ SMS系统本身工作正常，只是触发了保护机制")
    print("• 🚫 不要继续用同一号码测试，会延长限制时间")
    print("• 📝 记录这次经验，完善错误处理机制")
    print("• 🎯 问题已确定，不是运营商拦截或手机拦截问题")

if __name__ == "__main__":
    analyze_sms_debug()
    print_solutions() 