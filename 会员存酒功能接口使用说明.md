# 会员存酒功能接口使用说明

基于提供的UI界面需求，本文档详细说明会员存酒功能所需的接口调用方式。

## 🎯 接口通用性说明

**重要提醒：** 虽然本文档以"会员存酒"为主题，但所有取酒相关接口都是**完全通用**的，适用于：

✅ **会员客户** - 可通过会员卡号、手机号、姓名查询和取酒
✅ **非会员客户** - 可通过客户ID、手机号、姓名查询和取酒  
✅ **所有存酒记录** - 无论是否为会员，都可以进行取酒操作

### 支持的客户识别方式

| 识别方式 | 适用对象 | 示例 |
|---------|---------|------|
| 会员卡号 | 会员客户 | `TE000044` |
| 手机号 | 所有客户 | `18610210828` |
| 客户姓名 | 所有客户 | `张三` |
| 客户ID | 所有客户 | `cust001` |
| 商品名称 | 所有客户 | `长城` |
| 订单号 | 所有客户 | `PS20250627001` |

## 功能需求分析

根据UI界面，需要实现以下三个核心功能：
1. **获取单一会员的存取列表，支持查询** - 显示会员的所有存酒记录
2. **获取单一商品的存取明细** - 点击展开后显示该商品的详细操作记录
3. **针对单一商品进行取操作** - 取酒操作弹窗

## 接口清单

### 1. 会员存酒记录查询

**接口地址：** `POST /api/product-storage/member/query`

**功能说明：** 查询指定会员的所有存酒记录，支持多种查询条件和分页

**请求参数：**
```json
{
  "venueId": "94YTNnVUk",                    // 必填：门店ID
  "memberCardId": "TE000044",               // 会员卡ID（与memberCardNumber二选一）
  "memberCardNumber": "",                   // 会员卡号（与memberCardId二选一）
  "productName": "长城",                    // 可选：商品名称（模糊查询）
  "storageTimeStart": 1672531200,           // 可选：存酒开始时间戳
  "storageTimeEnd": 1704067199,             // 可选：存酒结束时间戳
  "expireStatus": "normal",                 // 可选：过期状态(normal-正常, expired-已过期, expiring-即将过期)
  "pageNum": 1,                             // 可选：页码，默认1
  "pageSize": 10,                           // 可选：每页大小，默认10
  "sortBy": "storageTime",                  // 可选：排序字段(storageTime, expireTime, productName)
  "sortOrder": "desc"                       // 可选：排序方向(asc, desc)
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 4,
    "list": [
      {
        "id": "storage123",
        "productId": "prod001",
        "productName": "长城 经典赤霞珠干红",
        "quantity": 2,
        "remainingQty": 2,
        "expireTime": 1722441600,
        "storageTime": 1722441600,
        "status": "正常",
        "daysTillExpiry": 45,
        "isExpired": false,
        "customerId": "cust001",
        "customerName": "丁",
        "phoneNumber": "18610210828",
        "memberCardNo": "TE000044",
        "venueId": "94YTNnVUk",
        "operatorId": "emp001",
        "operatorName": "张三",
        "remark": "优质红酒"
      }
    ],
    "pageInfo": {
      "pageNum": 1,
      "pageSize": 10,
      "total": 4,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    },
    "memberInfo": {
      "customerId": "cust001",
      "customerName": "丁",
      "phoneNumber": "18610210828",
      "memberCardId": "TE000044",
      "memberCardNumber": "TE000044",
      "totalStorageCount": 4,
      "totalRemainingQty": 35
    }
  }
}
```

### 2. 单一商品存取明细查询

**接口地址：** `GET /api/product-storage/detail/{id}`

**功能说明：** 获取指定存酒记录的详细操作历史，包括存入、取出等操作记录

**请求参数：**
- `{id}`: 存酒记录ID（从会员存酒列表中获取）

**响应结果：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "storage123",
    "productName": "长城 经典赤霞珠干红",
    "quantity": 2,
    "remainingQty": 2,
    "storageTime": 1722441600,
    "expireTime": 1722441600,
    "customerName": "丁",
    "memberCardNo": "TE000044",
    "operationHistory": [
      {
        "type": "storage",
        "typeName": "存取",
        "time": 1722441600,
        "quantity": 2,
        "operatorId": "emp001",
        "operatorName": "张三",
        "balanceQty": 4,
        "remark": "初次存入",
        "roomName": "豪华包厅A",
        "deliveryRoomName": ""
      },
      {
        "type": "withdraw",
        "typeName": "支取",
        "time": 1722527900,
        "quantity": 2,
        "operatorId": "emp002",
        "operatorName": "张三",
        "balanceQty": 4,
        "remark": "客户取酒",
        "roomName": "",
        "deliveryRoomName": "豪华包厅B"
      }
    ]
  }
}
```

### 3. 查询可取酒商品

**接口地址：** `POST /api/product-withdraw/query-withdrawable-items`

**功能说明：** 查询指定客户的可取酒商品列表（为取酒弹窗提供数据）

**通用性：** 此接口支持所有客户类型，通过`searchValue`字段可以灵活查询

**请求参数：**
```json
{
  "venueId": "94YTNnVUk",                    // 必填：门店ID
  "searchValue": "TE000044",                // 必填：搜索值（支持多种格式）
  "searchType": "card",                     // 可选：搜索类型提示
  "fuzzyMatch": true,                       // 可选：是否模糊匹配，默认true
  "includeExpiringSoon": true,              // 可选：是否包含即将过期商品，默认true
  "expireDays": 30                          // 可选：即将过期天数定义，默认30天
}
```

**搜索值(`searchValue`)支持格式：**
```json
// 会员客户示例
{"searchValue": "TE000044"}        // 会员卡号
{"searchValue": "18610210828"}     // 手机号  
{"searchValue": "丁"}              // 客户姓名

// 非会员客户示例  
{"searchValue": "cust001"}         // 客户ID
{"searchValue": "13912345678"}     // 手机号
{"searchValue": "李四"}            // 客户姓名

// 商品查询示例
{"searchValue": "长城"}            // 商品名称
{"searchValue": "PS20250627001"}   // 订单号
```

**响应结果：**
```json
{
  "code": 200,
  "message": "操作成功", 
  "data": {
    "customerInfo": {
      "customerId": "cust001",
      "customerName": "丁",
      "phoneNumber": "18610210828",
      "memberCardId": "TE000044"           // 仅会员客户有此字段
    },
    "withdrawableItems": [
      {
        "storageId": "storage123",
        "productId": "prod001",
        "productName": "长城 经典赤霞珠干红 750ml",
        "remainingQty": 2,
        "unit": "瓶",
        "storageTime": 1722441600,
        "expireTime": 1722441600,
        "isExpired": false,
        "daysTillExpiry": 45,
        "orderBrief": {
          "orderNo": "PS20250627001",
          "storageTime": 1722441600,
          "totalQuantity": 10
        }
      }
    ],
    "summary": {
      "totalItems": 4,
      "totalRemainingQty": 35,
      "expiredItems": 0,
      "expiringItems": 1
    }
  }
}
```

### 4. 执行取酒操作

**接口地址：** `POST /api/product-withdraw/add`

**功能说明：** 执行单个商品的取酒操作

**请求参数：**
```json
{
  "storageId": "storage123",                 // 必填：存酒记录ID
  "quantity": 1,                            // 必填：取酒数量
  "deliveryRoomId": "room001",              // 可选：送达包厅ID
  "deliveryRoomName": "豪华包厅A",           // 可选：送达包厅名称
  "withdrawTime": 1722441600,               // 必填：取酒时间戳
  "operatorId": "emp001",                   // 必填：操作员ID
  "operatorName": "张三",                    // 必填：操作员姓名
  "orderNumber": "WD20250627001",           // 可选：取酒单号
  "remark": "客户要求"                       // 可选：备注
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "withdraw123",
    "storageId": "storage123",
    "quantity": 1,
    "withdrawTime": 1722441600,
    "deliveryRoomName": "豪华包厅A",
    "operatorName": "张三",
    "remark": "客户要求"
  }
}
```

### 5. 批量取酒操作

**接口地址：** `POST /api/product-withdraw/batch-add`

**功能说明：** 批量执行多个商品的取酒操作（支持一次性多个商品取酒）

**请求参数：**
```json
{
  "customerId": "cust001",                   // 必填：客户ID
  "customerName": "丁",                      // 必填：客户姓名
  "phoneNumber": "18610210828",             // 可选：客户手机号
  "memberCardNo": "TE000044",               // 可选：会员卡号
  "deliveryRoomId": "room001",              // 可选：统一送达包厅ID
  "deliveryRoomName": "豪华包厅A",           // 可选：统一送达包厅名称
  "operatorId": "emp001",                   // 必填：操作员ID
  "operatorName": "张三",                    // 必填：操作员姓名
  "remark": "批量取酒",                      // 可选：统一备注
  "items": [
    {
      "storageId": "storage123",
      "quantity": 1
    },
    {
      "storageId": "storage124",
      "quantity": 2
    }
  ]
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "withdraw123",
      "storageId": "storage123",
      "quantity": 1,
      "customerName": "丁",
      "productName": "长城 经典赤霞珠干红",
      "operatorName": "张三"
    },
    {
      "id": "withdraw124",
      "storageId": "storage124",
      "quantity": 2,
      "customerName": "丁",
      "productName": "油菜",
      "operatorName": "张三"
    }
  ]
}
```

## 前端集成建议

### 1. 页面加载流程

1. **初始加载**：调用会员存酒记录查询接口，获取会员的存酒列表
2. **展开详情**：点击某个商品行时，调用单一商品存取明细查询接口
3. **取酒操作**：点击"取酒"按钮时，先调用查询可取酒商品接口验证，再执行取酒操作

### 2. 状态计算

- **过期状态判断**：
  - `isExpired: true` - 已过期（红色）
  - `daysTillExpiry <= 7` - 即将过期（橙色）  
  - 其他 - 正常（绿色）

### 3. 错误处理

所有接口都遵循统一的错误响应格式：
```json
{
  "code": 400,
  "message": "具体错误信息",
  "data": null
}
```

常见错误码：
- `400` - 参数错误
- `500` - 服务器内部错误

### 4. 性能优化建议

1. **分页加载**：使用分页参数避免一次性加载过多数据
2. **缓存会员信息**：避免重复查询相同会员的基本信息
3. **防抖搜索**：在商品名称搜索时使用防抖技术
4. **乐观更新**：取酒操作后立即更新本地数据，无需重新查询

## 实际应用示例

### 示例1：会员客户取酒流程

```bash
# 1. 通过会员卡号查询存酒记录
curl -X POST "http://localhost:18501/api/product-storage/member/query" \
-H "Content-Type: application/json" \
-d '{
  "venueId": "94YTNnVUk",
  "memberCardId": "TE000044",
  "pageNum": 1,
  "pageSize": 10
}'

# 2. 查询可取酒商品（验证数量）
curl -X POST "http://localhost:18501/api/product-withdraw/query-withdrawable-items" \
-H "Content-Type: application/json" \
-d '{
  "venueId": "94YTNnVUk",
  "searchValue": "TE000044"
}'

# 3. 执行取酒操作
curl -X POST "http://localhost:18501/api/product-withdraw/add" \
-H "Content-Type: application/json" \
-d '{
  "storageId": "storage123",
  "quantity": 1,
  "deliveryRoomName": "豪华包厅A",
  "withdrawTime": 1722441600,
  "operatorId": "emp001",
  "operatorName": "张三"
}'
```

### 示例2：非会员客户取酒流程

```bash
# 1. 通过手机号查询可取商品
curl -X POST "http://localhost:18501/api/product-withdraw/query-withdrawable-items" \
-H "Content-Type: application/json" \
-d '{
  "venueId": "94YTNnVUk",
  "searchValue": "13912345678"
}'

# 2. 执行取酒操作（流程相同）
curl -X POST "http://localhost:18501/api/product-withdraw/add" \
-H "Content-Type: application/json" \
-d '{
  "storageId": "storage456",
  "quantity": 2,
  "deliveryRoomName": "标准包厅B",
  "withdrawTime": 1722441600,
  "operatorId": "emp002",
  "operatorName": "李四"
}'
```

### 示例3：按商品名称查询和取酒

```bash
# 1. 按商品名称查询所有相关存酒
curl -X POST "http://localhost:18501/api/product-withdraw/query-withdrawable-items" \
-H "Content-Type: application/json" \
-d '{
  "venueId": "94YTNnVUk",
  "searchValue": "长城",
  "fuzzyMatch": true
}'

# 2. 批量取酒操作
curl -X POST "http://localhost:18501/api/product-withdraw/batch-add" \
-H "Content-Type: application/json" \
-d '{
  "customerId": "cust001",
  "customerName": "王五",
  "deliveryRoomName": "VIP包厅",
  "operatorId": "emp003",
  "operatorName": "赵六",
  "items": [
    {"storageId": "storage789", "quantity": 1},
    {"storageId": "storage790", "quantity": 3}
  ]
}'
```

## 总结

现有接口完全满足UI界面的功能需求：

✅ **需求1：获取单一会员的存取列表** - `/api/product-storage/member/query`
✅ **需求2：获取单一商品的存取明细** - `/api/product-storage/detail/{id}`  
✅ **需求3：针对单一商品进行取操作** - `/api/product-withdraw/add` 和 `/api/product-withdraw/batch-add`

所有接口都支持完整的错误处理、参数验证和数据格式化，可以直接用于前端开发。 