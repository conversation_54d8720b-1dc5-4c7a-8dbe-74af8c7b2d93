runmode: debug # 开发模式, debug, release, test
env: online # 当前环境 product, test
addr: 0.0.0.0:18501 # HTTP绑定端口
name: web_api # API Server的名字
machine_id: 1
url: http://127.0.0.1:18501 # pingServer函数请求的API服务器的ip:port
enable_auto_migrate: false
auth:
  check_token: 1  # 1:开启token验证 0:关闭token验证
  enable_permission_check: false  # 临时开关：是否启用权限检测，false为关闭
  whitelist:      # 白名单路径列表
  - /api/venue-shouyin/auth
  - /miniapp/api/login/phone
  - /shouyin/api/login/phone
  - /api/erp/user/register
  - /api/miniapp/erp/user/register
  - /shouyin/api/login/qr
  - /shouyin/api/login/qr/auto
  - /shouyin/api/login/qr/status
  - /miniapp/api/login
  - /miniapp/api/venue/bind/list
  - /api/health
  - /api/venue-shouyin/bind-info
  - /api/v3/order/pay/callback
  - /api/v3/order/refund/callback
  - /api/v3/member/pay/callback
  - /api/v3/member/refund/callback
  - /api/app-upgrade/create
  - /api/app-upgrade/check
  - /api/app-upgrade/version
  - /api/app-upgrade/version-name
  - /api/app-upgrade/list
  - /api/business/auth/login
  - /api/business/auth/logout
  
sentry:
  environment: "dev" # 设置环境，测试test、开发dev、生产prod
  debug: false # 启用打印sdk debug消息
  url: ""  # sentryurl告警url
  server_name: "" # 要上报的服务名称
  sample_rate: 1.0 # 事件提交的采样率， (0.0 - 1.0, defaults to 1.0)
  traces_sample_rate: 0.0 # 性能监控事件采样率 1.0 --> 100%， 生产根据性能调整， (defaults to 0.0 (meaning performance monitoring disabled))
max_ping_count: 5 # pingServer函数try的次数
server_auto_reload: true #是否启用修改配置文件自动重载服务程序，默认启用，适合开发环境， 生产环境禁用
log:
  logrus_json: true
  logrus_level: debug
  logrus_file: /data/torn_log/voderpltvv.log  # 服务错误日志文件
  logrus_console: true
  logrus_max_age: 72 # 日志轮转文件最大保存时间(小时)
  logrus_rotation_time: 1 # 日志切割时间间隔(小时)
  
  gin_file: /data/torn_log/voderpltvv_gin # 服务请求日志文件，自动切割轮转
  gin_console: true
  gin_max_age: 72 # 日志轮转文件最大保存时间(小时)
  gin_rotation_time: 1 # 日志切割时间间隔(小时)
cache:
  default_expiration: 30 # 本地key缓存过期时间(秒)
  cleanup_interval: 300 #  本地key缓存过期清理间隔时间(秒)
vod:
  app_id: 314bba990e26d02c0bd10afdf64ca13b
  app_key: 0d933f69aaa680a336e0032010e439fd
yunpian:
  api_key: 6df90577d22c6120f315f1866a0f3925

redis_pool_config:
  pool_size: 100 # 连接池最大socket连接数
  min_idle_conns: 10 # 在启动阶段创建指定数量的Idle连接，并长期维持idle状态的连接数不少于指定数量
  dial_timeout: 5 # 链接超时
  read_timeout: 3  # 读超时，默认3秒， -1表示取消读超时
  write_timeout: 3 # 写超时，默认等于读超时
  pool_timeout:  4 # 当所有连接都处在繁忙状态时，客户端等待可用连接的最大等待时长，默认为读超时+1秒。
  max_retries: 1   # 命令执行失败时，最多重试多少次，默认为0即不重试
  idle_timeout: 5 # 闲置超时，默认5分钟，-1表示取消闲置超时检查
redis:
  default:
    addr: 127.0.0.1:6379
    pwd: ''
    db: 0
  nats:
    addr: 127.0.0.1:6379
    pwd: ''
    db: 4

db_pool_config:
  max_open_conns: 200 #连接池最大连接数
  max_idle_conns: 100 #连接池最大空闲连接数

mysqldbMaster:
  dbtype: mysql
  name: autoapp # 数据库名字
  addr: ***********
  port: 3306
  username: root
  password: ktvsky5166

mysqldbSlave:
  dbtype: mysql
  name: autoapp # 数据库名字
  addr: ***********
  port: 3306
  username: root
  password: ktvsky5166

nats_server:
  eip: wsslerp.ktvsky.com
  port: 9876
  natsport: 4222
  iip: ************
  pinginterval: 15
  reconnectdelay: 3

gormlog: true # gorm 的日志模式, true 是详细日志, false 不记录日志

miniappqrurl:
  url: https://medev-stage.ktvsky.com/miniapp/index?action=miniProgram&qr_id=%v&client_type=%v&mac=%v&store_id=%v
miniapp:
  env_version: release  # 小程序环境版本: develop(开发版)、trial(体验版)、release(正式版)
pay:
  leshua:
    calback_url: https://medev-stage.ktvsky.com/api/order/pay/callback
    member_calback_url: https://medev-stage.ktvsky.com/api/member/callback
    refund_calback_url: https://medev-stage.ktvsky.com/api/order/refund/callback
  v3:
    leshua:
      calback_url: https://me.ktvsky.com/api/v3/order/pay/callback
      refund_calback_url: https://me.ktvsky.com/api/v3/order/refund/callback
      member_calback_url: https://me.ktvsky.com/api/v3/member/pay/callback
      member_refund_calback_url: https://me.ktvsky.com/api/v3/member/refund/callback
