package repository

import (
	"voderpltvv/erp_managent/domain/venue/model/venuepaytypesetting"

	"github.com/gin-gonic/gin"
)

// VenuePayTypeSettingRepository 门店支付类型设置仓储接口
type VenuePayTypeSettingRepository interface {
	// Save 保存门店支付类型设置
	Save(ctx *gin.Context, setting *venuepaytypesetting.VenuePayTypeSetting) error

	// FindByID 根据ID查找门店支付类型设置
	FindByID(ctx *gin.Context, id string) (*venuepaytypesetting.VenuePayTypeSetting, error)

	// FindByVenueID 根据门店ID查找门店支付类型设置
	FindByVenueID(ctx *gin.Context, venueID string) (*venuepaytypesetting.VenuePayTypeSetting, error)

	// FindByVenueIDs 根据门店ID列表查找门店支付类型设置
	FindByVenueIDs(ctx *gin.Context, venueIDs []string) ([]*venuepaytypesetting.VenuePayTypeSetting, error)

	// FindAll 查询所有门店支付类型设置
	FindAll(ctx *gin.Context) ([]*venuepaytypesetting.VenuePayTypeSetting, error)

	// Update 更新门店支付类型设置
	Update(ctx *gin.Context, setting *venuepaytypesetting.VenuePayTypeSetting) error

	// Delete 删除门店支付类型设置
	Delete(ctx *gin.Context, id string) error

	// FindLatestByVenueID 查找门店最新的支付类型设置
	FindLatestByVenueID(ctx *gin.Context, venueID string) (*venuepaytypesetting.VenuePayTypeSetting, error)

	// ExistsByVenueID 检查门店是否存在支付类型设置
	ExistsByVenueID(ctx *gin.Context, venueID string) (bool, error)
}
