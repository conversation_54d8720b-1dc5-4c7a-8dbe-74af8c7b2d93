package venuepaytypesetting

import (
	"errors"
	"time"
	"voderpltvv/erp_managent/domain/venue/model/valueobject"
)

// VenuePayTypeSetting 门店支付类型设置聚合根
type VenuePayTypeSetting struct {
	// 基本信息
	id             string                         // 唯一标识符
	venueID        string                         // 门店ID
	payTypeConfigs *valueobject.PayTypeConfigList // 支付类型配置列表
	remark         string                         // 备注

	// 元数据
	createTime time.Time // 创建时间
	updateTime time.Time // 修改时间
	state      int       // 状态
	version    int       // 版本号
}

// NewVenuePayTypeSetting 创建新的门店支付类型设置
func NewVenuePayTypeSetting(
	venueID string,
	payTypeConfigs *valueobject.PayTypeConfigList,
	remark string,
) (*VenuePayTypeSetting, error) {
	// 参数校验
	if venueID == "" {
		return nil, errors.New("门店ID不能为空")
	}
	if payTypeConfigs == nil {
		return nil, errors.New("支付类型配置不能为空")
	}

	return &VenuePayTypeSetting{
		id:             "",
		venueID:        venueID,
		payTypeConfigs: payTypeConfigs,
		remark:         remark,
		createTime:     time.Time{},
		updateTime:     time.Time{},
		state:          0,
		version:        0,
	}, nil
}

// ID 获取ID
func (v *VenuePayTypeSetting) ID() string {
	return v.id
}

// VenueID 获取门店ID
func (v *VenuePayTypeSetting) VenueID() string {
	return v.venueID
}

// PayTypeConfigs 获取支付类型配置列表
func (v *VenuePayTypeSetting) PayTypeConfigs() *valueobject.PayTypeConfigList {
	return v.payTypeConfigs
}

// Remark 获取备注
func (v *VenuePayTypeSetting) Remark() string {
	return v.remark
}

// CreateTime 获取创建时间
func (v *VenuePayTypeSetting) CreateTime() time.Time {
	return v.createTime
}

// UpdateTime 获取修改时间
func (v *VenuePayTypeSetting) UpdateTime() time.Time {
	return v.updateTime
}

// State 获取状态
func (v *VenuePayTypeSetting) State() int {
	return v.state
}

// Version 获取版本号
func (v *VenuePayTypeSetting) Version() int {
	return v.version
}

// SetPayTypeConfigs 设置支付类型配置
func (v *VenuePayTypeSetting) SetPayTypeConfigs(payTypeConfigs *valueobject.PayTypeConfigList) error {
	if payTypeConfigs == nil {
		return errors.New("支付类型配置不能为空")
	}
	v.payTypeConfigs = payTypeConfigs
	v.updateTime = time.Now()
	return nil
}

// SetRemark 设置备注
func (v *VenuePayTypeSetting) SetRemark(remark string) {
	v.remark = remark
	v.updateTime = time.Now()
}

// EnablePayType 启用指定支付类型
func (v *VenuePayTypeSetting) EnablePayType(payType string) error {
	newConfigs := v.payTypeConfigs.EnablePayType(payType)
	v.payTypeConfigs = newConfigs
	v.updateTime = time.Now()
	return nil
}

// DisablePayType 禁用指定支付类型
func (v *VenuePayTypeSetting) DisablePayType(payType string) error {
	newConfigs := v.payTypeConfigs.DisablePayType(payType)
	v.payTypeConfigs = newConfigs
	v.updateTime = time.Now()
	return nil
}

// UpdatePayTypeSort 更新支付类型排序
func (v *VenuePayTypeSetting) UpdatePayTypeSort(payType string, sort int) error {
	newConfigs, err := v.payTypeConfigs.UpdateSort(payType, sort)
	if err != nil {
		return err
	}
	v.payTypeConfigs = newConfigs
	v.updateTime = time.Now()
	return nil
}

// GetEnabledPayTypes 获取启用的支付类型
func (v *VenuePayTypeSetting) GetEnabledPayTypes() []valueobject.PayTypeConfig {
	return v.payTypeConfigs.GetEnabledConfigs()
}

// HasPayType 检查是否包含指定支付类型
func (v *VenuePayTypeSetting) HasPayType(payType string) bool {
	config := v.payTypeConfigs.GetByPayType(payType)
	return config != nil
}

// IsPayTypeEnabled 检查指定支付类型是否启用
func (v *VenuePayTypeSetting) IsPayTypeEnabled(payType string) bool {
	config := v.payTypeConfigs.GetByPayType(payType)
	if config == nil {
		return false
	}
	return config.Enabled
}

// ToJSON 将支付类型配置转换为JSON字符串
func (v *VenuePayTypeSetting) ToJSON() (string, error) {
	return v.payTypeConfigs.ToJSON()
}

// SetID 设置ID（由仓储层调用）
func (v *VenuePayTypeSetting) SetID(id string) {
	v.id = id
}

// SetCreateTime 设置创建时间（由仓储层调用）
func (v *VenuePayTypeSetting) SetCreateTime(createTime time.Time) {
	v.createTime = createTime
}

// SetUpdateTime 设置修改时间（由仓储层调用）
func (v *VenuePayTypeSetting) SetUpdateTime(updateTime time.Time) {
	v.updateTime = updateTime
}

// SetState 设置状态（由仓储层调用）
func (v *VenuePayTypeSetting) SetState(state int) {
	v.state = state
}

// SetVersion 设置版本号（由仓储层调用）
func (v *VenuePayTypeSetting) SetVersion(version int) {
	v.version = version
}
