package valueobject

import (
	"encoding/json"
	"errors"
	"sort"
)

// PayTypeConfig 支付类型配置值对象
type PayTypeConfig struct {
	PayType     string `json:"type"`        // 支付类型
	Label       string `json:"label"`       // 显示标签
	Enabled     bool   `json:"enabled"`     // 是否启用
	IsPreset    bool   `json:"isPreset"`    // 是否预设
	Sort        int    `json:"sort"`        // 排序
	IsNetProfit bool   `json:"isNetProfit"` // 是否是净收益
}

// NewPayTypeConfig 创建支付类型配置
func NewPayTypeConfig(payType, label string, enabled, isPreset bool, sort int, isNetProfit bool) (*PayTypeConfig, error) {
	if payType == "" {
		return nil, errors.New("支付类型不能为空")
	}
	if label == "" {
		return nil, errors.New("显示标签不能为空")
	}
	if sort < 0 {
		return nil, errors.New("排序不能为负数")
	}

	return &PayTypeConfig{
		PayType:     payType,
		Label:       label,
		Enabled:     enabled,
		IsPreset:    isPreset,
		Sort:        sort,
		IsNetProfit: isNetProfit,
	}, nil
}

// Enable 启用支付类型
func (p *PayTypeConfig) Enable() *PayTypeConfig {
	return &PayTypeConfig{
		PayType:     p.PayType,
		Label:       p.Label,
		Enabled:     true,
		IsPreset:    p.IsPreset,
		Sort:        p.Sort,
		IsNetProfit: p.IsNetProfit,
	}
}

// Disable 禁用支付类型
func (p *PayTypeConfig) Disable() *PayTypeConfig {
	return &PayTypeConfig{
		PayType:     p.PayType,
		Label:       p.Label,
		Enabled:     false,
		IsPreset:    p.IsPreset,
		Sort:        p.Sort,
		IsNetProfit: p.IsNetProfit,
	}
}

// UpdateSort 更新排序
func (p *PayTypeConfig) UpdateSort(sort int) (*PayTypeConfig, error) {
	if sort < 0 {
		return nil, errors.New("排序不能为负数")
	}
	return &PayTypeConfig{
		PayType:     p.PayType,
		Label:       p.Label,
		Enabled:     p.Enabled,
		IsPreset:    p.IsPreset,
		Sort:        sort,
		IsNetProfit: p.IsNetProfit,
	}, nil
}

// ToJSON 转换为JSON
func (p *PayTypeConfig) ToJSON() (string, error) {
	bytes, err := json.Marshal(p)
	if err != nil {
		return "", errors.New("序列化失败：" + err.Error())
	}
	return string(bytes), nil
}

// FromJSON 从JSON创建支付类型配置
func FromJSON(jsonStr string) (*PayTypeConfig, error) {
	var config PayTypeConfig
	err := json.Unmarshal([]byte(jsonStr), &config)
	if err != nil {
		return nil, errors.New("反序列化失败：" + err.Error())
	}

	// 验证必填字段
	if config.PayType == "" {
		return nil, errors.New("支付类型不能为空")
	}
	if config.Label == "" {
		return nil, errors.New("显示标签不能为空")
	}

	return &config, nil
}

// PayTypeConfigList 支付类型配置列表值对象
type PayTypeConfigList struct {
	configs []PayTypeConfig
}

// NewPayTypeConfigList 创建支付类型配置列表
func NewPayTypeConfigList(configs []PayTypeConfig) *PayTypeConfigList {
	// 创建副本避免外部修改
	configsCopy := make([]PayTypeConfig, len(configs))
	copy(configsCopy, configs)

	// 按排序字段排序
	sort.Slice(configsCopy, func(i, j int) bool {
		return configsCopy[i].Sort < configsCopy[j].Sort
	})

	return &PayTypeConfigList{
		configs: configsCopy,
	}
}

// NewEmptyPayTypeConfigList 创建空的支付类型配置列表
func NewEmptyPayTypeConfigList() *PayTypeConfigList {
	return &PayTypeConfigList{
		configs: []PayTypeConfig{},
	}
}

// Configs 获取所有配置
func (p *PayTypeConfigList) Configs() []PayTypeConfig {
	// 返回副本避免外部修改
	result := make([]PayTypeConfig, len(p.configs))
	copy(result, p.configs)
	return result
}

// GetByPayType 根据支付类型获取配置
func (p *PayTypeConfigList) GetByPayType(payType string) *PayTypeConfig {
	for _, config := range p.configs {
		if config.PayType == payType {
			return &config
		}
	}
	return nil
}

// GetEnabledConfigs 获取启用的配置
func (p *PayTypeConfigList) GetEnabledConfigs() []PayTypeConfig {
	var result []PayTypeConfig
	for _, config := range p.configs {
		if config.Enabled {
			result = append(result, config)
		}
	}
	return result
}

// Size 获取配置数量
func (p *PayTypeConfigList) Size() int {
	return len(p.configs)
}

// IsEmpty 是否为空
func (p *PayTypeConfigList) IsEmpty() bool {
	return len(p.configs) == 0
}

// EnablePayType 启用指定支付类型
func (p *PayTypeConfigList) EnablePayType(payType string) *PayTypeConfigList {
	newConfigs := make([]PayTypeConfig, len(p.configs))
	for i, config := range p.configs {
		if config.PayType == payType {
			newConfigs[i] = *config.Enable()
		} else {
			newConfigs[i] = config
		}
	}
	return NewPayTypeConfigList(newConfigs)
}

// DisablePayType 禁用指定支付类型
func (p *PayTypeConfigList) DisablePayType(payType string) *PayTypeConfigList {
	newConfigs := make([]PayTypeConfig, len(p.configs))
	for i, config := range p.configs {
		if config.PayType == payType {
			newConfigs[i] = *config.Disable()
		} else {
			newConfigs[i] = config
		}
	}
	return NewPayTypeConfigList(newConfigs)
}

// UpdateSort 更新指定支付类型的排序
func (p *PayTypeConfigList) UpdateSort(payType string, sort int) (*PayTypeConfigList, error) {
	newConfigs := make([]PayTypeConfig, len(p.configs))
	found := false
	for i, config := range p.configs {
		if config.PayType == payType {
			newConfig, err := config.UpdateSort(sort)
			if err != nil {
				return nil, err
			}
			newConfigs[i] = *newConfig
			found = true
		} else {
			newConfigs[i] = config
		}
	}

	if !found {
		return nil, errors.New("未找到指定的支付类型")
	}

	return NewPayTypeConfigList(newConfigs), nil
}

// ToJSON 转换为JSON字符串
func (p *PayTypeConfigList) ToJSON() (string, error) {
	bytes, err := json.Marshal(p.configs)
	if err != nil {
		return "", errors.New("序列化失败：" + err.Error())
	}
	return string(bytes), nil
}

// FromJSONArray 从JSON数组创建支付类型配置列表
func FromJSONArray(jsonStr string) (*PayTypeConfigList, error) {
	if jsonStr == "" {
		return NewEmptyPayTypeConfigList(), nil
	}

	var configs []PayTypeConfig
	err := json.Unmarshal([]byte(jsonStr), &configs)
	if err != nil {
		return nil, errors.New("反序列化失败：" + err.Error())
	}

	// 验证每个配置
	for _, config := range configs {
		if config.PayType == "" {
			return nil, errors.New("支付类型不能为空")
		}
		if config.Label == "" {
			return nil, errors.New("显示标签不能为空")
		}
	}

	return NewPayTypeConfigList(configs), nil
}
