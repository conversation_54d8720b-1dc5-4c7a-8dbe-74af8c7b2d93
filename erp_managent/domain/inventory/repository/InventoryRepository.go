package repository

import (
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/inventory/model"

	"github.com/gin-gonic/gin"
)

// InventoryRepository 库存仓储接口
type InventoryRepository interface {
	// 入库记录操作
	SaveInboundRecord(ctx *gin.Context, record *model.InboundRecord) error
	SaveInboundRecordWithTx(ctx *gin.Context, record *model.InboundRecord, tx interface{}) error
	FindInboundRecordById(ctx *gin.Context, id string) (*model.InboundRecord, error)
	FindInboundRecords(ctx *gin.Context, venueId string) ([]*model.InboundRecord, error)
	FindInboundRecordsByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*model.InboundRecord, error)
	UpdateInboundRecord(ctx *gin.Context, record *model.InboundRecord) error
	DeleteInboundRecord(ctx *gin.Context, id string) error

	// 商品库存操作
	GetProductStock(ctx *gin.Context, productId, venueId, warehouse string) (*model.ProductStock, error)
	GetVenueAllStock(ctx *gin.Context, venueId string) ([]*model.ProductStock, error)
	SaveProductStock(ctx *gin.Context, stock *model.ProductStock) error
	SaveProductStockWithTx(ctx *gin.Context, stock *model.ProductStock, tx interface{}) error

	// 推荐使用的库存同步方法
	SyncProductStock(ctx *gin.Context, newStocks []*model.ProductStock, stockChanges []vo.ProductStockChangeVO) error

	// 库存历史查询
	GetStockHistory(ctx *gin.Context, productId, venueId string, limit int) ([]*model.InboundRecord, error)

	// 库存计算
	CalculateStockFromRecords(ctx *gin.Context, productId, venueId string) (int, error)

	// 事务操作
	BeginTransaction(ctx *gin.Context) (interface{}, error)
	CommitTransaction(ctx *gin.Context, tx interface{}) error
	RollbackTransaction(ctx *gin.Context, tx interface{}) error
	WithTransaction(ctx *gin.Context, fn func(ctx *gin.Context, tx interface{}) error) error
}
