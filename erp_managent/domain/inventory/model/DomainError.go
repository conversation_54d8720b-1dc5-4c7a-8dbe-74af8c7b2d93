package model

import "fmt"

// DomainError 领域错误
type DomainError struct {
	Code    string
	Message string
}

// Error 实现error接口
func (e *DomainError) Error() string {
	if e.Code != "" {
		return fmt.Sprintf("[%s] %s", e.Code, e.Message)
	}
	return e.Message
}

// NewDomainError 创建新的领域错误
func NewDomainError(message string) *DomainError {
	return &DomainError{
		Message: message,
	}
}

// NewDomainErrorWithCode 创建带错误码的领域错误
func NewDomainErrorWithCode(code, message string) *DomainError {
	return &DomainError{
		Code:    code,
		Message: message,
	}
}

// 常用领域错误
var (
	ErrInvalidVenueId        = NewDomainErrorWithCode("INVALID_VENUE_ID", "门店ID不能为空")
	ErrInvalidHandler        = NewDomainErrorWithCode("INVALID_HANDLER", "操作人不能为空")
	ErrInvalidRecordNumber   = NewDomainErrorWithCode("INVALID_RECORD_NUMBER", "入库单号不能为空")
	ErrEmptyItems            = NewDomainErrorWithCode("EMPTY_ITEMS", "入库明细不能为空")
	ErrInvalidProductId      = NewDomainErrorWithCode("INVALID_PRODUCT_ID", "商品ID不能为空")
	ErrInvalidQuantity       = NewDomainErrorWithCode("INVALID_QUANTITY", "数量必须大于0")
	ErrInvalidUnitPrice      = NewDomainErrorWithCode("INVALID_UNIT_PRICE", "单价不能为负数")
	ErrRecordNotFound        = NewDomainErrorWithCode("RECORD_NOT_FOUND", "记录不存在")
	ErrInsufficientStock     = NewDomainErrorWithCode("INSUFFICIENT_STOCK", "库存不足")
	ErrRecordAlreadyReversed = NewDomainErrorWithCode("RECORD_ALREADY_REVERSED", "记录已被冲销")
)
