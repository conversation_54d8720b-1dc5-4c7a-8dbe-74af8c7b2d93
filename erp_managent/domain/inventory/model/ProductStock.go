package model

import (
	"time"
	"voderpltvv/erp_managent/service/po"
)

// ProductStock 商品库存领域实体
type ProductStock struct {
	id        string
	productId string
	venueId   string
	warehouse string
	stock     int
	ctime     time.Time
	utime     time.Time
	version   int
}

// NewProductStock 创建新的商品库存
func NewProductStock(productId, venueId, warehouse string, stock int) *ProductStock {
	now := time.Now()
	return &ProductStock{
		productId: productId,
		venueId:   venueId,
		warehouse: warehouse,
		stock:     stock,
		ctime:     now,
		utime:     now,
		version:   0,
	}
}

// UpdateStock 更新库存数量
func (ps *ProductStock) UpdateStock(quantity int) {
	ps.stock += quantity
	ps.utime = time.Now()
}

// SetStock 设置库存数量
func (ps *ProductStock) SetStock(stock int) {
	ps.stock = stock
	ps.utime = time.Now()
}

// Validate 验证商品库存
func (ps *ProductStock) Validate() error {
	if ps.productId == "" {
		return NewDomainError("商品ID不能为空")
	}
	if ps.venueId == "" {
		return NewDomainError("门店ID不能为空")
	}

	return nil
}

// ToPO 转换为PO实体
func (ps *ProductStock) ToPO() *po.ProductStock {
	return &po.ProductStock{
		Id:        &ps.id,
		ProductId: &ps.productId,
		VenueId:   &ps.venueId,
		Warehouse: &ps.warehouse,
		Stock:     &ps.stock,
		Ctime:     timePtr(ps.ctime),
		Utime:     timePtr(ps.utime),
		Version:   &ps.version,
	}
}

// FromProductStockPO 从PO实体创建领域实体
func FromProductStockPO(stockPO *po.ProductStock) *ProductStock {
	return &ProductStock{
		id:        *stockPO.Id,
		productId: *stockPO.ProductId,
		venueId:   *stockPO.VenueId,
		warehouse: *stockPO.Warehouse,
		stock:     *stockPO.Stock,
		ctime:     timeFromPtr(stockPO.Ctime),
		utime:     timeFromPtr(stockPO.Utime),
		version:   *stockPO.Version,
	}
}

// Getters
func (ps *ProductStock) GetId() string        { return ps.id }
func (ps *ProductStock) GetProductId() string { return ps.productId }
func (ps *ProductStock) GetVenueId() string   { return ps.venueId }
func (ps *ProductStock) GetWarehouse() string { return ps.warehouse }
func (ps *ProductStock) GetStock() int        { return ps.stock }
func (ps *ProductStock) GetCtime() time.Time  { return ps.ctime }
func (ps *ProductStock) GetUtime() time.Time  { return ps.utime }
func (ps *ProductStock) GetVersion() int      { return ps.version }

// Setters
func (ps *ProductStock) SetId(id string) { ps.id = id }
