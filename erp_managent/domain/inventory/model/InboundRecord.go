package model

import (
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/service/po"
)

// InboundRecord 入库记录领域实体
type InboundRecord struct {
	id               string
	venueId          string
	warehouse        string
	handler          string
	time             time.Time
	recordNumber     string
	originalRecordId *string
	totalAmount      float64
	remark           string
	items            []InboundRecordItem
	ctime            time.Time
	utime            time.Time
	state            int
	version          int
}

// InboundRecordItem 入库记录明细领域实体
type InboundRecordItem struct {
	id          string
	productId   string
	productName string
	unit        string
	quantity    int
	unitPrice   float64
	subtotal    float64
}

// NewInboundRecord 创建新的入库记录
func NewInboundRecord(venueId, warehouse, handler, recordNumber, remark string, items []InboundRecordItem) *InboundRecord {
	now := time.Now()

	// 计算总金额
	var totalAmount float64
	for _, item := range items {
		totalAmount += item.subtotal
	}

	return &InboundRecord{
		venueId:      venueId,
		warehouse:    warehouse,
		handler:      handler,
		time:         now,
		recordNumber: recordNumber,
		totalAmount:  totalAmount,
		remark:       remark,
		items:        items,
		ctime:        now,
		utime:        now,
		state:        0,
		version:      0,
	}
}

// NewInboundRecordItem 创建新的入库记录明细
func NewInboundRecordItem(productId, productName, unit string, quantity int, unitPrice float64) InboundRecordItem {
	return InboundRecordItem{
		productId:   productId,
		productName: productName,
		unit:        unit,
		quantity:    quantity,
		unitPrice:   unitPrice,
		subtotal:    float64(quantity) * unitPrice,
	}
}

// CreateReverseRecord 创建冲销记录
func (r *InboundRecord) CreateReverseRecord(reason string) *InboundRecord {
	now := time.Now()

	// 创建反向明细
	var reverseItems []InboundRecordItem
	for _, item := range r.items {
		reverseItem := InboundRecordItem{
			productId:   item.productId,
			productName: item.productName,
			unit:        item.unit,
			quantity:    -item.quantity, // 反向数量
			unitPrice:   item.unitPrice,
			subtotal:    -item.subtotal, // 反向金额
		}
		reverseItems = append(reverseItems, reverseItem)
	}

	return &InboundRecord{
		venueId:          r.venueId,
		warehouse:        r.warehouse,
		handler:          r.handler,
		time:             now,
		recordNumber:     r.recordNumber + "_REVERSE",
		originalRecordId: &r.id,
		totalAmount:      -r.totalAmount, // 反向金额
		remark:           "冲销：" + reason,
		items:            reverseItems,
		ctime:            now,
		utime:            now,
		state:            0,
		version:          0,
	}
}

// Validate 验证入库记录
func (r *InboundRecord) Validate() error {
	if r.venueId == "" {
		return NewDomainError("门店ID不能为空")
	}
	if r.handler == "" {
		return NewDomainError("操作人不能为空")
	}
	if r.recordNumber == "" {
		return NewDomainError("入库单号不能为空")
	}
	if len(r.items) == 0 {
		return NewDomainError("入库明细不能为空")
	}

	// 验证明细
	for _, item := range r.items {
		if err := item.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证入库记录明细
func (item *InboundRecordItem) Validate() error {
	if item.productId == "" {
		return NewDomainError("商品ID不能为空")
	}
	if item.quantity <= 0 {
		return NewDomainError("入库数量必须大于0")
	}
	if item.unitPrice < 0 {
		return NewDomainError("单价不能为负数")
	}

	return nil
}

// ToPO 转换为PO实体
func (r *InboundRecord) ToPO() *po.InventoryRecord {
	return &po.InventoryRecord{
		Id:               &r.id,
		VenueId:          &r.venueId,
		Warehouse:        &r.warehouse,
		Type:             stringPtr(_const.INVENTORY_RECORD_TYPE_INBOUND),
		Handler:          &r.handler,
		Time:             timePtr(r.time),
		RecordNumber:     &r.recordNumber,
		OriginalRecordId: r.originalRecordId,
		TotalAmount:      &r.totalAmount,
		Remark:           &r.remark,
		Ctime:            timePtr(r.ctime),
		Utime:            timePtr(r.utime),
		State:            &r.state,
		Version:          &r.version,
	}
}

// ItemsToPO 转换明细为PO实体
func (r *InboundRecord) ItemsToPO() []po.InventoryRecordItem {
	var items []po.InventoryRecordItem
	for _, item := range r.items {
		poItem := po.InventoryRecordItem{
			Id:                &item.id,
			InventoryRecordId: &r.id,
			ProductId:         &item.productId,
			ProductName:       &item.productName,
			Unit:              &item.unit,
			Quantity:          &item.quantity,
			UnitPrice:         &item.unitPrice,
			Subtotal:          &item.subtotal,
		}
		items = append(items, poItem)
	}
	return items
}

// FromPO 从PO实体创建领域实体
func FromPO(record *po.InventoryRecord, items []po.InventoryRecordItem) *InboundRecord {
	var domainItems []InboundRecordItem
	for _, item := range items {
		domainItem := InboundRecordItem{
			id:          *item.Id,
			productId:   *item.ProductId,
			productName: *item.ProductName,
			unit:        *item.Unit,
			quantity:    *item.Quantity,
			unitPrice:   *item.UnitPrice,
			subtotal:    *item.Subtotal,
		}
		domainItems = append(domainItems, domainItem)
	}

	return &InboundRecord{
		id:               *record.Id,
		venueId:          *record.VenueId,
		warehouse:        *record.Warehouse,
		handler:          *record.Handler,
		time:             timeFromPtr(record.Time),
		recordNumber:     *record.RecordNumber,
		originalRecordId: record.OriginalRecordId,
		totalAmount:      *record.TotalAmount,
		remark:           *record.Remark,
		items:            domainItems,
		ctime:            timeFromPtr(record.Ctime),
		utime:            timeFromPtr(record.Utime),
		state:            *record.State,
		version:          *record.Version,
	}
}

// Getters
func (r *InboundRecord) GetId() string                 { return r.id }
func (r *InboundRecord) GetVenueId() string            { return r.venueId }
func (r *InboundRecord) GetWarehouse() string          { return r.warehouse }
func (r *InboundRecord) GetHandler() string            { return r.handler }
func (r *InboundRecord) GetTime() time.Time            { return r.time }
func (r *InboundRecord) GetRecordNumber() string       { return r.recordNumber }
func (r *InboundRecord) GetOriginalRecordId() *string  { return r.originalRecordId }
func (r *InboundRecord) GetTotalAmount() float64       { return r.totalAmount }
func (r *InboundRecord) GetRemark() string             { return r.remark }
func (r *InboundRecord) GetItems() []InboundRecordItem { return r.items }
func (r *InboundRecord) GetState() int                 { return r.state }
func (r *InboundRecord) GetVersion() int               { return r.version }

// Setters
func (r *InboundRecord) SetId(id string)         { r.id = id }
func (r *InboundRecord) SetTime(t time.Time)     { r.time = t }
func (r *InboundRecord) SetRemark(remark string) { r.remark = remark }

// Delete 软删除记录
func (r *InboundRecord) Delete() { r.state = 1 }

// Getters for InboundRecordItem
func (item *InboundRecordItem) GetId() string          { return item.id }
func (item *InboundRecordItem) GetProductId() string   { return item.productId }
func (item *InboundRecordItem) GetProductName() string { return item.productName }
func (item *InboundRecordItem) GetUnit() string        { return item.unit }
func (item *InboundRecordItem) GetQuantity() int       { return item.quantity }
func (item *InboundRecordItem) GetUnitPrice() float64  { return item.unitPrice }
func (item *InboundRecordItem) GetSubtotal() float64   { return item.subtotal }

// Setters for InboundRecordItem
func (item *InboundRecordItem) SetId(id string) { item.id = id }

// 工具函数
func stringPtr(s string) *string { return &s }
func timePtr(t time.Time) *int64 { unix := t.Unix(); return &unix }
func timeFromPtr(ptr *int64) time.Time {
	if ptr == nil {
		return time.Time{}
	}
	return time.Unix(*ptr, 0)
}
