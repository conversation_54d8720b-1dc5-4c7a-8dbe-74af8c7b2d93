package service

import (
	"voderpltvv/erp_managent/domain/inventory/model"

	"github.com/gin-gonic/gin"
)

// InventoryDomainService 库存领域服务接口
type InventoryDomainService interface {
	// 创建入库记录
	CreateInboundRecord(ctx *gin.Context, record *model.InboundRecord) error

	// 冲销记录
	ReverseRecord(ctx *gin.Context, originalRecordId string, reason string) (*model.InboundRecord, error)

	// 校验库存
	ValidateStock(ctx *gin.Context, productId, venueId string, requiredQuantity int) error

	// 更新商品库存快照
	UpdateProductStockSnapshot(ctx *gin.Context, productId, venueId, warehouse string, quantity int) error

	// 批量更新商品库存快照
	BatchUpdateProductStockSnapshot(ctx *gin.Context, updates []StockUpdate) error

	// 校准商品库存
	ReconcileProductStock(ctx *gin.Context, productId, venueId string) error

	// 校准门店所有商品库存
	ReconcileAllProductStock(ctx *gin.Context, venueId string) error
}

// StockUpdate 库存更新信息
type StockUpdate struct {
	ProductId string
	VenueId   string
	Warehouse string
	Quantity  int // 变化数量，正数为增加，负数为减少
}
