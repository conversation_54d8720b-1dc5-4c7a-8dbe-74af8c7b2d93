package service

import (
	"encoding/json"
	"fmt"
	"time"

	"voderpltvv/erp_managent/domain/print/model/shiftchange"
	"voderpltvv/erp_managent/domain/print/model/valueobject"
	"voderpltvv/erp_managent/domain/venue/repository"
	"voderpltvv/erp_managent/infra/repository/venue"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// ShiftChangeDataAssemblyService 交班单数据组装服务接口
type ShiftChangeDataAssemblyService interface {
	// BatchAssembleShiftChangeBillData 批量组装交班单数据
	BatchAssembleShiftChangeBillData(ctx *gin.Context, venueId string, handNos []string, employeeId string) (map[string]*shiftchange.ShiftChangeBillData, string, string, error)
}

// shiftChangeDataAssemblyServiceImpl 交班单数据组装服务实现
type shiftChangeDataAssemblyServiceImpl struct {
	venueService                  *impl.VenueService
	employeeService               *impl.EmployeeService
	shiftHandoverService          *impl.ShiftHandoverFormService
	payRecordService              *impl.PayRecordService
	venuePayTypeSettingRepository repository.VenuePayTypeSettingRepository
}

// NewShiftChangeDataAssemblyService 创建交班单数据组装服务实例
func NewShiftChangeDataAssemblyService() ShiftChangeDataAssemblyService {
	return &shiftChangeDataAssemblyServiceImpl{
		venueService:                  &impl.VenueService{},
		employeeService:               &impl.EmployeeService{},
		shiftHandoverService:          &impl.ShiftHandoverFormService{},
		payRecordService:              &impl.PayRecordService{},
		venuePayTypeSettingRepository: venue.NewVenuePayTypeSettingRepositoryImpl(),
	}
}

// BatchAssembleShiftChangeBillData 批量组装交班单数据
func (s *shiftChangeDataAssemblyServiceImpl) BatchAssembleShiftChangeBillData(
	ctx *gin.Context,
	venueId string,
	handNos []string,
	employeeId string,
) (map[string]*shiftchange.ShiftChangeBillData, string, string, error) {
	// 参数验证
	if venueId == "" {
		return nil, "", "", fmt.Errorf("门店ID不能为空")
	}
	if len(handNos) == 0 {
		return nil, "", "", fmt.Errorf("交班单号数组不能为空")
	}

	// 结果映射
	billDataMap := make(map[string]*shiftchange.ShiftChangeBillData)
	var finalOperatorId, finalOperatorName string

	// 批量查询交班单信息
	shiftHandovers := make(map[string]*po.ShiftHandoverForm)
	for _, handNo := range handNos {
		if handNo == "" {
			continue
		}

		shiftHandover, err := s.shiftHandoverService.FindByHandNo(ctx, handNo)
		if err != nil {
			// 记录错误但继续处理其他交班单
			fmt.Printf("查询交班单[%s]信息失败: %v\n", handNo, err)
			continue
		}
		if shiftHandover == nil {
			fmt.Printf("交班单不存在: %s\n", handNo)
			continue
		}
		shiftHandovers[handNo] = shiftHandover
	}

	// 获取门店信息用于店铺名称
	shop, err := s.venueService.FindVenueById(ctx, venueId)
	if err != nil {
		return nil, "", "", fmt.Errorf("查询门店信息失败: %w", err)
	}

	// 店铺名称
	shopName := ""
	if shop != nil && shop.Name != nil {
		shopName = *shop.Name
	}
	// 注意：shopName 字段已从新的 ShiftChangeBillData 结构中移除
	// 如果需要可以添加到其他地方或BusinessData中
	_ = shopName // 避免未使用变量的警告

	// 查询门店支付方式配置
	payTypeMapping := make(map[string]string)
	venuePayTypeSetting, err := s.venuePayTypeSettingRepository.FindLatestByVenueID(ctx, venueId)
	if err != nil {
		// 记录错误但不阻断流程
		fmt.Printf("查询门店支付方式配置失败: %v\n", err)
	} else if venuePayTypeSetting != nil {
		// 构建支付方式映射表（paytype -> Label）
		enabledConfigs := venuePayTypeSetting.GetEnabledPayTypes()
		for _, config := range enabledConfigs {
			payTypeMapping[config.PayType] = config.Label
		}
	}

	// 确定员工信息（优先使用传入的employeeId，否则使用第一个交班单的员工ID）
	var employee *po.Employee
	if employeeId == "" {
		// 从第一个交班单中获取员工ID
		for _, shiftHandover := range shiftHandovers {
			if shiftHandover.EmployeeId != nil {
				employeeId = *shiftHandover.EmployeeId
				break
			}
		}
	}

	if employeeId != "" {
		employee, err = s.employeeService.FindEmployeeById(ctx, employeeId)
		if err != nil {
			// 记录错误但不阻断流程
			fmt.Printf("查询员工信息失败: %v\n", err)
		}
	}

	// 获取员工姓名
	employeeName := ""
	if employee != nil && employee.Name != nil {
		employeeName = *employee.Name
	}

	finalOperatorId = employeeId
	finalOperatorName = employeeName

	// 当前时间
	printTimeStr := time.Now().Format("2006-01-02 15:04:05")

	// 遍历处理每个交班单
	for handNo, shiftHandover := range shiftHandovers {
		// 构建交班时间
		handTimeStr := ""
		if shiftHandover.EndTime != nil && *shiftHandover.EndTime > 0 {
			handTime := time.Unix(*shiftHandover.EndTime-1, 0)
			handTimeStr = handTime.Format("2006-01-02 15:04:05")
		} else {
			// 如果交班时间为空，使用当前时间
			handTimeStr = printTimeStr
		}

		// 获取交班单号
		handNoValue := ""
		if shiftHandover.HandNo != nil {
			handNoValue = *shiftHandover.HandNo
		}

		// 构建交班单数据
		result := shiftchange.NewShiftChangeBillData()

		// 设置基本字段 - 按照新的字段命名
		s.setBasicFields(result, handTimeStr, printTimeStr, employeeName, handNoValue)

		// 设置交班金额
		s.setShiftAmount(result, shiftHandover)

		// 设置交班单统计数据
		if shiftHandover.OpenCount != nil {
			openCount := *shiftHandover.OpenCount
			result.OpenTableCount = &openCount
		}

		if shiftHandover.BillCount != nil {
			settledCount := *shiftHandover.BillCount
			result.SettledOrderCount = &settledCount
		}

		if shiftHandover.OrderUnpaidCount != nil {
			unsettledCount := *shiftHandover.OrderUnpaidCount
			result.UnsettledOrderCount = &unsettledCount
		}

		// 初始化BusinessData
		businessData := result.GetBusinessData()

		// 设置营业应收信息
		s.setBusinessDataFields(businessData, shiftHandover)

		// 设置支付方式信息 - 使用支付方式映射
		result.PaymentInfo = s.buildPaymentInfo(shiftHandover, payTypeMapping)

		billDataMap[handNo] = result
	}

	return billDataMap, finalOperatorId, finalOperatorName, nil
}

// buildPaymentInfo 构建支付方式信息 - 使用支付方式映射
func (s *shiftChangeDataAssemblyServiceImpl) buildPaymentInfo(shiftHandover *po.ShiftHandoverForm, payTypeMapping map[string]string) []valueobject.PaymentDetail {
	var paymentInfo []valueobject.PaymentDetail

	// 解析PayTypeInfo JSON字符串
	if shiftHandover.PayTypeInfo == nil || *shiftHandover.PayTypeInfo == "" {
		return paymentInfo
	}

	// 解析支付方式数据
	var payTypeData map[string]int64
	err := json.Unmarshal([]byte(*shiftHandover.PayTypeInfo), &payTypeData)
	if err != nil {
		fmt.Printf("解析支付方式数据失败: %v\n", err)
		return paymentInfo
	}

	// 遍历支付方式数据，使用映射表获取标签
	for payType, amount := range payTypeData {
		if amount > 0 {
			// 从映射表中获取支付方式标签
			label := payTypeMapping[payType]
			if label == "" {
				// 如果映射表中没有找到，使用原始的payType作为标签
				label = payType
			}

			paymentInfo = append(paymentInfo, valueobject.PaymentDetail{
				Method: label,
				Amount: float64(amount), // 保持分为单位，只是转换类型
			})
		}
	}

	return paymentInfo
}

// buildPaymentDetails 构建支付明细的通用方法
func (s *shiftChangeDataAssemblyServiceImpl) buildPaymentDetails(mappings []struct {
	amount     *int64
	methodName string
}) []valueobject.PaymentDetail {
	var paymentInfo []valueobject.PaymentDetail

	for _, mapping := range mappings {
		if s.isValidPaymentAmount(mapping.amount) {
			paymentInfo = append(paymentInfo, s.createPaymentDetail(mapping.methodName, *mapping.amount))
		}
	}

	return paymentInfo
}

// isValidPaymentAmount 验证支付金额是否有效
func (s *shiftChangeDataAssemblyServiceImpl) isValidPaymentAmount(amount *int64) bool {
	return amount != nil && *amount > 0
}

// createPaymentDetail 创建支付明细
func (s *shiftChangeDataAssemblyServiceImpl) createPaymentDetail(method string, amount int64) valueobject.PaymentDetail {
	return valueobject.PaymentDetail{
		Method: method,
		Amount: float64(amount), // 保持分为单位，只是转换类型
	}
}

// 金额转换相关常量
const (
	// AmountDivisor 金额转换除数（如果需要从分转换为元）
	AmountDivisor = 1.0
)

// convertAmountToFloat64 转换金额为float64类型
func (s *shiftChangeDataAssemblyServiceImpl) convertAmountToFloat64(amount *int64) *float64 {
	if amount == nil {
		return nil
	}
	converted := float64(*amount)
	return &converted
}

// setShiftAmount 设置交班金额
func (s *shiftChangeDataAssemblyServiceImpl) setShiftAmount(result *shiftchange.ShiftChangeBillData, shiftHandover *po.ShiftHandoverForm) {
	result.ShiftAmount = s.convertAmountToFloat64(shiftHandover.TotalFee)
}

// setBasicFields 设置基本字段
func (s *shiftChangeDataAssemblyServiceImpl) setBasicFields(result *shiftchange.ShiftChangeBillData, handTimeStr, printTimeStr, employeeName, handNoValue string) {
	result.ShiftTime = handTimeStr
	result.PrintTime = &printTimeStr
	result.Employee = &employeeName
	result.ShiftId = &handNoValue
}

// setBusinessDataFields 设置营业数据字段
func (s *shiftChangeDataAssemblyServiceImpl) setBusinessDataFields(businessData *valueobject.BusinessSummary, shiftHandover *po.ShiftHandoverForm) {
	businessData.Receivable = s.convertAmountToFloat64(shiftHandover.ShouldFee)
	businessData.ActualReceived = s.convertAmountToFloat64(shiftHandover.TotalFee)
	businessData.MerchantDiscount = s.convertAmountToFloat64(shiftHandover.MerchantDiscount)
	businessData.MemberDiscount = s.convertAmountToFloat64(shiftHandover.MemberDiscount)
	businessData.RoomActualReceived = s.convertAmountToFloat64(shiftHandover.RoomFee)
	businessData.ProductActualReceived = s.convertAmountToFloat64(shiftHandover.ProductFee)
	businessData.RoundingAmount = s.convertAmountToFloat64(shiftHandover.ZeroFee)
	businessData.EmployeeProductGift = s.convertAmountToFloat64(shiftHandover.EmployeeGift)

	// 设置会员充值相关信息
	businessData.RechargeAmount = s.convertAmountToFloat64(shiftHandover.RechargeAmount)
	// 注意：ShiftHandoverForm中没有充值赠送相关字段，如果需要可以从其他数据源获取
	// 办卡信息：目前ShiftHandoverForm中没有办卡相关字段，如果需要可以扩展
	// businessData.CardCount = nil
	// businessData.CardAmount = nil
}
