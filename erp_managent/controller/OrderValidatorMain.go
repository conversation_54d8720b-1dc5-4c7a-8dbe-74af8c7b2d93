package controller

import (
	"errors"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

func (validator *OrderValidator) ValidateAdditionalOrder(ctx *gin.Context, reqDto *req.AddOrderAdditionalReqDto) (session *po.Session, err error) {
	// 0. 验证基础参数 venueId,roomId,sessionId
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, errors.New("VenueId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return nil, errors.New("RoomId不能为空")
	}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return nil, errors.New("SessionId不能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return nil, errors.New("EmployeeId不能为空")
	}
	// 0.1. 检查ktv是否存在
	venue, err := venueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return nil, errors.New("门店不存在")
	}
	// 0.2. 检查房间状态
	room, err := roomService.FindRoomById(ctx, *reqDto.RoomId)
	if err != nil {
		return nil, errors.New("房间不存在")
	}
	if room.VenueId == nil || *room.VenueId != *reqDto.VenueId {
		return nil, errors.New("房间不属于该门店")
	}
	// 0.3. 检查场次是否存在
	sessions, err := sessionService.FindAllSession(ctx, &req.QuerySessionReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId})
	if err != nil || len(*sessions) == 0 {
		return nil, errors.New("场次信息不存在")
	}
	session = &((*sessions)[0])
	// 0.4. 检查员工是否存在
	employee, err := employeeService.FindEmployeeById(ctx, *reqDto.EmployeeId)
	if err != nil || employee == nil {
		return nil, errors.New("员工不存在")
	}

	// 1. 检查房间是否锁定
	if orderCommonService.IsRoomLocked(ctx, room) {
		return nil, errors.New("房间已锁定,不能点单")
	}

	// 2. 检查是否立结
	if reqDto.IsSettled != nil && *reqDto.IsSettled {
		if reqDto.PayType == nil || *reqDto.PayType == "" {
			return nil, errors.New("PayType不能为空")
		}
		if !util.InList(*reqDto.PayType, _const.PAY_TYPE_SUPPORTS) {
			// return nil, errors.New("PayType不支持")
		}
		if reqDto.PayAmount == nil || *reqDto.PayAmount < 0 {
			return nil, errors.New("PayAmount不能为空且必须大于等于0")
		}
	}

	return session, nil
}
