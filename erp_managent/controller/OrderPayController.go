package controller

import (
	"strconv"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/natsdk"
	"voderpltvv/paysdk"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type OrderPayController struct{}

var payBillService = &impl.PayBillService{}

// @Summary 乐刷支付回调
// @Description 乐刷支付回调
// @Tags 订单
// @Accept xml
// @Produce json
// @Param body body model.LeshuaPayCallbackModel true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/order/pay/callback [post]
func (controller *OrderPayController) LeshuaPayCallback(ctx *gin.Context) {
	// 1. 支付发起后，乐刷一般30s左右才会返回支付通知
	// 2. 支付通知的频率：0s/15s/30s/1m/4m/34m/64m/94m/124m/184m
	// 3. 支付交易的结果，按照以下优先级，在已配置的通知地址中，选择最高优先级的通知地址进行通知：
	// 		优先级1：交易接口中传输的回调/通知地址；
	// 		优先级2：商户入网时配置的通知地址；
	// 		优先级3：商户所属一级代理商配置的通知地址（联系乐刷技术支持进行配置）
	// a. 000000 通知成功，乐刷收到此返回后不会继续通知。
	// b. 乐刷收到回复或者未收到回复，系统会再次通知。为避免资源浪费，请核实并确保响应信息的准确性，可以是否重复通知为检查手段
	reqDto := model.LeshuaPayCallbackModel{}
	err := ctx.ShouldBindXML(&reqDto)
	if err != nil {
		ctx.String(200, "000001")
		return
	}

	err = payService.LeshuaPayCallback(ctx, &reqDto, true)
	if err != nil {
		util.Wlog(ctx).Errorf("乐刷支付回调失败: %#v", err)
		ctx.String(200, "000001")
		return
	}
	ctx.String(200, "000000")
}

// @Summary 乐刷查单
// @Description 乐刷查单
// @Tags 订单
// @Accept json
// @Produce json
// @Param body body req.QueryOrderPayLeshuaQueryReqDto true "请求体"
// @Success 200 {object} Result[paysdk.PayResult] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/order/pay/query [post]
func (controller *OrderPayController) LeshuaPayQueryOrder(ctx *gin.Context) {
	reqDto := req.QueryOrderPayLeshuaQueryReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	// 1. 查询支付账单
	payBills, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		VenueId: reqDto.VenueId, BillId: reqDto.PayId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询支付账单失败dberr")
		return
	}
	if len(*payBills) == 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付账单不存在")
		return
	}
	payBill := (*payBills)[0]
	var payResult paysdk.PayResult
	// 2. 已支付订单，直接返回成功
	// 2.1 记账直接返回成功
	if util.InList("*payBill.PayType", _const.PAY_TYPE_RECORDS) {
		payResult = paysdk.PayResult{
			PayId:   *reqDto.PayId,
			Status:  _const.PAY_STATUS_PAID,
			ErrMsg:  "已支付",
			ErrCode: "",
		}
		Result_success[any](ctx, payResult)
		return
	}
	// 2.3 乐刷扫码枪支付
	if payBill.Status != nil && *payBill.Status == _const.PAY_STATUS_PAID {
		payResult = paysdk.PayResult{
			PayId:   *reqDto.PayId,
			Status:  _const.PAY_STATUS_PAID,
			ErrMsg:  "已支付",
			ErrCode: "",
		}
		Result_success[any](ctx, payResult)
		return
	}
	// 3. 乐刷扫码枪支付查单
	// 3.1 查询商户配置
	venuePaySettings, err := venuePaySettingService.FindAllVenuePaySetting(ctx, &req.QueryVenuePaySettingReqDto{
		VenueId: payBill.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询商户配置失败dberr")
		return
	}
	if len(*venuePaySettings) == 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "商户配置不存在")
		return
	}
	venuePaySetting := (*venuePaySettings)[0]
	// 3.2 调用查单网关查询支付结果
	result := paysdk.PayQuery(ctx, model.LeshuaDto{
		Leshua_id: *venuePaySetting.SubMerchantId,
		Order_id:  *reqDto.PayId,
	})
	payResult = paysdk.PayResult{
		PayId:   *reqDto.PayId,
		ErrMsg:  result.Resp_msg,
		ErrCode: result.Resp_code,
	}
	// 4. 根据支付结果更新支付账单
	// 订单支付状态。枚举如下：
	// 0：支付中
	// 2：支付成功
	// 6：订单关闭
	// 8：支付失败
	// 4.1 支付接口异常
	if result.Resp_code == "-9999" {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付查询异常，请重试")
		return
	}
	// 4.2 支付中, 直接返回
	if result.Status == "0" {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付中")
		return
	}
	// 4.3 订单关闭，更新支付账单状态
	if result.Status == "6" {
		payResult.Status = _const.PAY_STATUS_CLOSE
		payResult.ErrMsg = "订单关闭"
		toUpdatePayBill := po.PayBill{
			Id:         payBill.Id,
			BillId:     payBill.BillId,
			Status:     util.GetItPtr(_const.PAY_STATUS_CLOSE),
			// FinishTime: util.GetItPtr(int64(util.TimeNowUnix())),
		}
		err = payBillService.UpdatePayBill(ctx, &toUpdatePayBill)
		if err != nil {
			util.Wlog(ctx).Errorf("更新支付账单失败: %#v", err)
		}
		Result_success[any](ctx, payResult)
		return
	}
	// 4.4 支付失败，更新支付账单状态
	if result.Status == "8" {
		payResult.Status = _const.PAY_STATUS_CLOSE
		payResult.ErrMsg = "支付失败"
		toUpdatePayBill := po.PayBill{
			Id:         payBill.Id,
			BillId:     payBill.BillId,
			Status:     util.GetItPtr(_const.PAY_STATUS_CLOSE),
			// FinishTime: util.GetItPtr(int64(util.TimeNowUnix())),
		}
		err = payBillService.UpdatePayBill(ctx, &toUpdatePayBill)
		if err != nil {
			util.Wlog(ctx).Errorf("更新支付账单失败: %#v", err)
		}
		Result_success[any](ctx, payResult)
		return
	}
	// 4.5 支付失败，直接返回
	if result.Status != "2" {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付状态不正确")
		return
	}
	// 4.6 支付成功，模拟支付成功回调
	err = payService.LeshuaPayCallback(ctx, &model.LeshuaPayCallbackModel{
		Error_code:     "0",
		Status:         "2",
		Third_order_id: *reqDto.PayId,
	}, false)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询失败")
		return
	}
	payResult.Status = _const.PAY_STATUS_PAID
	payResult.ErrMsg = "支付成功"
	payResult.ErrCode = ""
	Result_success[any](ctx, payResult)
}

// @Summary 乐刷退款
// @Description 乐刷退款
// @Tags 订单
// @Accept json
// @Produce json
// @Param body body req.QueryOrderRefundLeshuaReqDto true "请求体"
// @Success 200 {object} Result[paysdk.PayResult] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/order/refund [post]
func (controller *OrderPayController) LeshuaRefund(ctx *gin.Context) {
	reqDto := req.QueryOrderRefundLeshuaReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, err.Error())
		return
	}
	// 1. 查询支付账单
	payBills, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		BillId: reqDto.PayId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询支付账单失败")
		return
	}
	if len(*payBills) == 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付账单不存在")
		return
	}
	payBill := (*payBills)[0]
	// 1.1 支付账单类型不正确
	// if payBill.PayType == nil || *payBill.PayType != _const.PAY_TYPE_LESHUA_BSHOWQR {
	// 	Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付账单类型不正确")
	// 	return
	// }
	if payBill.Status != nil && *payBill.Status != _const.PAY_STATUS_PAID {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付账单状态不正确")
		return
	}
	// 1.2 退款金额必须大于0
	if reqDto.RefundAmount == nil || *reqDto.RefundAmount <= 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "退款金额必须大于0")
		return
	}
	// 1.3 查询退款中的订单
	payBillsRefundIngs, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		BillPid: reqDto.PayId,
		Status:  util.GetItPtr(_const.PAY_STATUS_REFUNDING),
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询退款账单中失败")
		return
	}
	if len(*payBillsRefundIngs) > 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "存在退款中订单")
		return
	}
	// 1.3 查询退款账单
	payBillsRefunds, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		BillPid: reqDto.PayId,
		Status:  util.GetItPtr(_const.PAY_STATUS_REFUND),
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询退款账单失败")
		return
	}
	hasRefundAmount := int64(0)
	for _, payBillRefund := range *payBillsRefunds {
		hasRefundAmount += *payBillRefund.TotalFee
	}
	// 1.4 查询场次
	sessions, err := sessionService.FindAllSession(ctx, &req.QuerySessionReqDto{
		SessionId: payBill.SessionId, VenueId: payBill.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询场次失败")
		return
	}
	if len(*sessions) == 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "场次不存在")
		return
	}
	session := (*sessions)[0]
	// 1.5 场次总金额不正确
	if session.TotalFee == nil || *session.TotalFee <= 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "场次总金额不正确")
		return
	}
	// 1.6 退款金额超过实际支付金额
	if *session.TotalFee < *reqDto.RefundAmount+hasRefundAmount {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "退款金额超过实际支付金额")
		return
	}
	// 2. 查询商户配置
	venuePaySettings, err := venuePaySettingService.FindAllVenuePaySetting(ctx, &req.QueryVenuePaySettingReqDto{
		VenueId: payBill.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询商户配置失败")
		return
	}
	venuePaySetting := (*venuePaySettings)[0]
	if venuePaySetting.SubMerchantId == nil || *venuePaySetting.SubMerchantId == "" {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "商户配置不正确")
		return
	}
	// 3. 创建退款账单
	refundId := util.GetPayId(*payBill.VenueId)
	toAddPayBillRefund := po.PayBill{
		VenueId:   payBill.VenueId,
		RoomId:    payBill.RoomId,
		SessionId: payBill.SessionId,
		BillId:    &refundId,
		BillPid:   reqDto.PayId,
		TotalFee:  payBill.TotalFee,
		Status:    util.GetItPtr(_const.PAY_STATUS_REFUNDING),
		// PayType:   payBill.PayType,
	}
	err = payBillService.CreatePayBill(ctx, &toAddPayBillRefund)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "创建退款账单失败")
		return
	}
	// 4. 调用退款网关
	result := paysdk.Refund(ctx, model.LeshuaDto{
		Leshua_id:          *venuePaySetting.SubMerchantId,
		Order_id:           *reqDto.PayId,
		Merchant_refund_id: refundId,
		Refund_amount:      strconv.FormatInt(*reqDto.RefundAmount, 10),
	})
	payResult := paysdk.PayResult{
		PayId:    *reqDto.PayId,
		RefundId: refundId,
		Status:   _const.PAY_STATUS_REFUNDING,
		ErrMsg:   result.Resp_msg,
		ErrCode:  result.Resp_code,
	}
	Result_success[any](ctx, payResult)
}

// @Summary 乐刷退款回调
// @Description 乐刷退款回调
// @Tags 订单
// @Accept xml
// @Produce json
// @Param body body model.LeshuaRefundCallbackModel true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/order/refund/callback [post]
func (controller *OrderPayController) LeshuaRefundCallback(ctx *gin.Context) {
	reqDto := model.LeshuaRefundCallbackModel{}
	err := ctx.ShouldBindXML(&reqDto)
	if err != nil {
		ctx.String(200, "000001")
		return
	}
	err = payService.LeshuaRefundCallback(ctx, &reqDto)
	if err != nil {
		util.Wlog(ctx).Errorf("乐刷退款回调失败: %#v", err)
		ctx.String(200, "000001")
		return
	}
	ctx.String(200, "000000")
}

// @Summary 乐刷查单
// @Description 乐刷查单
// @Tags 订单
// @Accept json
// @Produce json
// @Param body body req.QueryOrderPayLeshuaRefundQueryReqDto true "请求体"
// @Success 200 {object} Result[paysdk.PayResult] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/order/refund/query [post]
func (controller *OrderPayController) LeshuaRefundQuery(ctx *gin.Context) {
	reqDto := req.QueryOrderPayLeshuaRefundQueryReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	// 1. 查询退款订单
	payBills, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		BillId: reqDto.RefundId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询退款账单失败")
		return
	}
	if len(*payBills) == 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "退款账单不存在")
		return
	}
	payBill := (*payBills)[0]
	// 1.1 支付账单类型不正确
	// if payBill.PayType == nil || *payBill.PayType != _const.PAY_TYPE_LESHUA_BSHOWQR {
	// 	Result_fail[any](ctx, GeneralCodes.InternalError.Code, "支付账单类型不正确")
	// 	return
	// }
	var payResult paysdk.PayResult
	if payBill.Status != nil && *payBill.Status == _const.PAY_STATUS_REFUND {
		payResult = paysdk.PayResult{
			RefundId: *reqDto.RefundId,
			Status:   _const.PAY_STATUS_REFUND,
			ErrMsg:   "已退款",
			ErrCode:  "",
		}
		Result_success[any](ctx, payResult)
		return
	}
	// 2. 查询商户配置
	venuePaySettings, err := venuePaySettingService.FindAllVenuePaySetting(ctx, &req.QueryVenuePaySettingReqDto{
		VenueId: payBill.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "查询商户配置失败")
		return
	}
	if len(*venuePaySettings) == 0 {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "商户配置不存在")
		return
	}
	venuePaySetting := (*venuePaySettings)[0]
	if venuePaySetting.SubMerchantId == nil || *venuePaySetting.SubMerchantId == "" {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, "商户配置不正确")
		return
	}
	// 3. 调用查单网关查询退款结果
	result := paysdk.RefundQuery(ctx, model.LeshuaDto{
		Leshua_id:          *venuePaySetting.SubMerchantId,
		Order_id:           *payBill.BillPid,
		Merchant_refund_id: *reqDto.RefundId,
	})
	// 4. 根据退款结果更新退款账单
	// 订单支付状态，枚举如下： 10：退款中 11：退款成功 12：退款失败
	payResult = paysdk.PayResult{
		RefundId: *reqDto.RefundId,
		ErrMsg:   result.Resp_msg,
		ErrCode:  result.Resp_code,
	}
	// 4.1 退款中
	if result.Status == "10" {
		payResult.Status = _const.PAY_STATUS_REFUNDING
		Result_success[any](ctx, payResult)
		return
	}

	toUpdatePayBill := po.PayBill{
		BillId: payBill.BillId,
	}
	// serverTime := int64(util.TimeNowUnix())
	// 4.2 退款成功
	if result.Status == "11" {
		toUpdatePayBill.Status = util.GetItPtr(_const.PAY_STATUS_REFUND)
		// toUpdatePayBill.FinishTime = &serverTime
		payResult.Status = _const.PAY_STATUS_REFUND
	}
	// 4.3 退款失败
	if result.Resp_code == "12" {
		toUpdatePayBill.Status = util.GetItPtr(_const.PAY_STATUS_REFUND_FAIL)
		// toUpdatePayBill.FinishTime = &serverTime
		payResult.Status = _const.PAY_STATUS_REFUND_FAIL
	}
	err = payBillService.UpdatePayBill(ctx, &toUpdatePayBill)
	if err != nil {
		util.Wlog(ctx).Errorf("更新退款账单失败: %#v", err)
	}
	Result_success[any](ctx, payResult)
}

// @Summary 测试支付
// @Description 测试支付
// @Tags 订单
// @Accept json
// @Produce json
// @Param body body req.QueryOrderPayReqDto true "请求体"
// @Success 200 {object} Result[paysdk.PayResult] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/order/test-pay [post]
func (controller *OrderController) TestPay(ctx *gin.Context) {
	// reqDto := req.QueryOrderPayReqDto{}
	// err := ctx.ShouldBindJSON(&reqDto)
	// if err != nil {
	// 	Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
	// 	return
	// }
	// toAddPayBill := po.PayBill{
	// 	VenueId:      reqDto.VenueId,
	// 	PayId:        util.GetItPtr(util.GetPayOrderNo(*reqDto.VenueId)),
	// 	TotalFee:     reqDto.PayAmount,
	// 	ActualAmount: reqDto.PayAmount,
	// 	Status:       util.GetItPtr(_const.PAY_STATUS_UNPAID),
	// 	PayType:      util.GetItPtr(_const.PAY_TYPE_RECORD_CASH),
	// }
	// payResult, err := payService.TransformPayGate(ctx, &reqDto, &toAddPayBill)
	// if err != nil {
	// 	Result_fail[any](ctx, GeneralCodes.InternalError.Code, err.Error())
	// 	return
	// }
	// Result_success[any](ctx, payResult)
	data := natsdk.NatsSendToPad("105497", "/erp/huijin_call", "post", map[string]string{"test": "test"}, "test")
	util.Wlog(ctx).Infof("data: %s", string(data))
	Result_success[any](ctx, string(data))
}
