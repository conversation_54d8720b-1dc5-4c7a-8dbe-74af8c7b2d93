package controller

import (
	"encoding/json"
	"fmt"
	"net/url"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type SmsController struct{}

// @Summary 发送短信验证码
// @Description 发送短信验证码（测试接口）
// @Tags 短信
// @Accept json
// @Produce json
// @Param body body req.SendSmsCodeReqDto true "请求体"
// @Success 200 {object} Result[vo.SendSmsCodeVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/sms/send-code [post]
func (controller *SmsController) SendSmsCode(ctx *gin.Context) {
	reqDto := req.SendSmsCodeReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	if reqDto.Phone == nil || *reqDto.Phone == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "手机号码不能为空")
		return
	}

	// 默认测试模式为false
	testMode := false
	if reqDto.TestMode != nil {
		testMode = *reqDto.TestMode
	}

	// 发送短信验证码
	code, err := util.SendSMSCodeWithTestMode(*reqDto.Phone, testMode)
	if err != nil {
		logrus.Errorf("发送短信验证码失败: phone=%v, error=%v", *reqDto.Phone, err)
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 构造响应
	response := vo.SendSmsCodeVO{
		Success:  true,
		Phone:    *reqDto.Phone,
		TestMode: testMode,
		Message:  "发送成功",
	}

	// 测试模式下返回验证码
	if testMode {
		response.Code = code
	}

	Result_success[any](ctx, response)
}

// @Summary 调试短信发送
// @Description 调试短信发送功能，返回详细的调试信息
// @Tags 短信
// @Accept json
// @Produce json
// @Param body body req.SendSmsCodeReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/sms/debug-send-code [post]
func (controller *SmsController) DebugSendSmsCode(ctx *gin.Context) {
	reqDto := req.SendSmsCodeReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	if reqDto.Phone == nil || *reqDto.Phone == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "手机号码不能为空")
		return
	}

	// 验证手机号码格式
	if err := util.ValidatePhoneNumber(*reqDto.Phone); err != nil {
		logrus.Errorf("手机号码格式验证失败: %v", err)
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("手机号码格式错误: %v", err))
		return
	}

	// 检查云片账户状态
	accountInfo, err := util.CheckYunpianAccount()
	if err != nil {
		logrus.Errorf("检查云片账户失败: %v", err)
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, fmt.Sprintf("云片账户检查失败: %v", err))
		return
	}

	// 调试发送短信
	debugInfo, err := util.DebugSendSMSCode(*reqDto.Phone)
	if err != nil {
		logrus.Errorf("调试发送短信失败: %v", err)
	}

	// 构造调试响应
	response := map[string]interface{}{
		"phone_validation": "通过",
		"account_info":     accountInfo,
		"sms_debug_info":   debugInfo,
		"success":          err == nil,
	}

	if err != nil {
		response["error"] = err.Error()
	}

	Result_success[any](ctx, response)
}

// YunpianSmsStatus 云片短信状态报告结构
type YunpianSmsStatus struct {
	ErrorDetail     string `json:"error_detail"`      // 运营商反馈代码的中文解释
	Sid             int64  `json:"sid"`               // 短信ID，64位整型
	Uid             string `json:"uid"`               // 用户自定义ID
	UserReceiveTime string `json:"user_receive_time"` // 用户接收时间
	ErrorMsg        string `json:"error_msg"`         // 运营商返回的代码
	Mobile          string `json:"mobile"`            // 接收手机号
	ReportStatus    string `json:"report_status"`     // 接收状态：SUCCESS/FAIL
}

// @Summary 短信状态回调
// @Description 接收云片短信平台的状态回调
// @Tags 短信
// @Accept application/x-www-form-urlencoded
// @Produce text/plain
// @Param sms_status formData string true "短信状态数据(URL编码的JSON)"
// @Success 200 {string} string "SUCCESS"
// @Router /api/sms/callback [post]
func (controller *SmsController) SmsCallback(ctx *gin.Context) {
	// 获取sms_status参数
	smsStatusParam := ctx.PostForm("sms_status")
	if smsStatusParam == "" {
		logrus.Errorf("短信状态回调: 缺少sms_status参数")
		ctx.String(200, "0")
		return
	}

	// URL解码
	decodedData, err := url.QueryUnescape(smsStatusParam)
	if err != nil {
		logrus.Errorf("短信状态回调: URL解码失败: %v", err)
		ctx.String(200, "0")
		return
	}

	// 解析JSON数组
	var statusList []YunpianSmsStatus
	err = json.Unmarshal([]byte(decodedData), &statusList)
	if err != nil {
		logrus.Errorf("短信状态回调: JSON解析失败: %v, data: %v", err, decodedData)
		ctx.String(200, "0")
		return
	}

	// 处理每个状态报告
	for _, status := range statusList {
		logrus.Infof("短信状态回调: mobile=%v, status=%v, sid=%v, error_msg=%v, user_receive_time=%v",
			status.Mobile, status.ReportStatus, status.Sid, status.ErrorMsg, status.UserReceiveTime)

		// 根据状态进行不同处理
		switch status.ReportStatus {
		case "SUCCESS":
			logrus.Infof("短信发送成功: mobile=%v, sid=%v, receive_time=%v",
				status.Mobile, status.Sid, status.UserReceiveTime)
		case "FAIL":
			logrus.Errorf("短信发送失败: mobile=%v, sid=%v, error_msg=%v, error_detail=%v",
				status.Mobile, status.Sid, status.ErrorMsg, status.ErrorDetail)
		default:
			logrus.Warnf("未知的短信状态: %v, mobile=%v, sid=%v",
				status.ReportStatus, status.Mobile, status.Sid)
		}

		// 这里可以添加业务逻辑，比如：
		// 1. 更新数据库中的短信发送状态
		// 2. 触发相关的业务事件
		// 3. 发送通知等
		// 建议根据sid做去重处理
	}

	// 返回成功响应（云片要求返回"SUCCESS"或"0"）
	ctx.String(200, "SUCCESS")
}
