package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/product"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type ProductController struct{}

var (
	productService       = impl.ProductService{}
	productTransfer      = transfer.ProductTransfer{}
	productCommonService = impl.ProductCommonService{}
	productAppService    = product.NewProductAppService()
)

// @Summary 添加产品
// @Description 添加产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.AddProductReqDto true "请求体"
// @Success 200 {object} Result[vo.ProductVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/add [post]
func (controller *ProductController) AddProduct(ctx *gin.Context) {
	reqDto := req.AddProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}

	product := po.Product{}
	if reqDto.VenueId != nil {
		product.VenueId = reqDto.VenueId
	}
	if reqDto.Name != nil {
		product.Name = reqDto.Name
	}
	if reqDto.Type != nil {
		product.Type = reqDto.Type
	}
	if reqDto.CurrentPrice != nil {
		product.CurrentPrice = reqDto.CurrentPrice
	}
	if reqDto.Price != nil {
		product.Price = reqDto.Price
	}
	if reqDto.PayMark != nil {
		product.PayMark = reqDto.PayMark
	}
	if reqDto.Barcode != nil {
		product.Barcode = reqDto.Barcode
	}
	if reqDto.AreaPrices != nil {
		product.AreaPrices = reqDto.AreaPrices
	}
	if reqDto.BuyGiftPlan != nil {
		product.BuyGiftPlan = reqDto.BuyGiftPlan
	}
	if reqDto.TimeSlotPrices != nil {
		product.TimeSlotPrices = reqDto.TimeSlotPrices
	}
	if reqDto.DistributionChannels != nil {
		product.DistributionChannels = reqDto.DistributionChannels
	}
	if reqDto.MemberCardPaymentRestrictions != nil {
		product.MemberCardPaymentRestrictions = reqDto.MemberCardPaymentRestrictions
	}
	if reqDto.MinimumSaleQuantity != nil {
		product.MinimumSaleQuantity = reqDto.MinimumSaleQuantity
	}
	if reqDto.IsRealPriceProduct != nil {
		product.IsRealPriceProduct = reqDto.IsRealPriceProduct
	}
	if reqDto.AuxiliaryFormula != nil {
		product.AuxiliaryFormula = reqDto.AuxiliaryFormula
	}
	if reqDto.Category != nil {
		product.Category = reqDto.Category
	}
	if reqDto.IsMemberDiscountable != nil {
		product.IsMemberDiscountable = reqDto.IsMemberDiscountable
	}
	if reqDto.IsOrderDiscountable != nil {
		product.IsOrderDiscountable = reqDto.IsOrderDiscountable
	}
	if reqDto.IsOrderReduceable != nil {
		product.IsOrderReduceable = reqDto.IsOrderReduceable
	}
	if reqDto.AllowRepeatBuy != nil {
		product.AllowRepeatBuy = reqDto.AllowRepeatBuy
	}
	if reqDto.RecommendCombos != nil {
		product.RecommendCombos = reqDto.RecommendCombos
	}
	if reqDto.MemberCardLimits != nil {
		product.MemberCardLimits = reqDto.MemberCardLimits
	}
	if reqDto.Flavors != nil {
		product.Flavors = reqDto.Flavors
	}
	if reqDto.Ingredients != nil {
		product.Ingredients = reqDto.Ingredients
	}
	if reqDto.IsDisplayed != nil {
		product.IsDisplayed = reqDto.IsDisplayed
	}
	if reqDto.AllowStaffGift != nil {
		product.AllowStaffGift = reqDto.AllowStaffGift
	}
	if reqDto.CountToMinCharge != nil {
		product.CountToMinCharge = reqDto.CountToMinCharge
	}
	if reqDto.CountToPerformance != nil {
		product.CountToPerformance = reqDto.CountToPerformance
	}
	if reqDto.IsPromotion != nil {
		product.IsPromotion = reqDto.IsPromotion
	}
	if reqDto.IsSoldOut != nil {
		product.IsSoldOut = reqDto.IsSoldOut
	}
	if reqDto.AllowWineStorage != nil {
		product.AllowWineStorage = reqDto.AllowWineStorage
	}
	if reqDto.GiftVoucher != nil {
		product.GiftVoucher = reqDto.GiftVoucher
	}
	if reqDto.CalculateInventory != nil {
		product.CalculateInventory = reqDto.CalculateInventory
	}
	if reqDto.IsAreaSpecified != nil {
		product.IsAreaSpecified = reqDto.IsAreaSpecified
	}
	if reqDto.SelectedAreas != nil {
		product.SelectedAreas = reqDto.SelectedAreas
	}
	if reqDto.IsRoomTypeSpecified != nil {
		product.IsRoomTypeSpecified = reqDto.IsRoomTypeSpecified
	}
	if reqDto.SelectedRoomTypes != nil {
		product.SelectedRoomTypes = reqDto.SelectedRoomTypes
	}
	if reqDto.StartTime != nil {
		product.StartTime = reqDto.StartTime
	}
	if reqDto.EndTime != nil {
		product.EndTime = reqDto.EndTime
	}
	if reqDto.Description != nil {
		product.Description = reqDto.Description
	}
	if reqDto.Image != nil {
		product.Image = reqDto.Image
	}
	if reqDto.LowStockThreshold != nil {
		product.LowStockThreshold = reqDto.LowStockThreshold
	}
	if reqDto.DeliveryTimeout != nil {
		product.DeliveryTimeout = reqDto.DeliveryTimeout
	}
	if reqDto.SupportsExternalDelivery != nil {
		product.SupportsExternalDelivery = reqDto.SupportsExternalDelivery
	}
	if reqDto.ExternalDeliveryPrice != nil {
		product.ExternalDeliveryPrice = reqDto.ExternalDeliveryPrice
	}
	if reqDto.Unit != nil {
		product.Unit = reqDto.Unit
	}

	err = productService.CreateProduct(ctx, &product)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, productTransfer.PoToVo(product))
}

// @Summary 更新产品
// @Description 更新产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.UpdateProductReqDto true "请求体"
// @Success 200 {object} Result[vo.ProductVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/update [post]
func (controller *ProductController) UpdateProduct(ctx *gin.Context) {
	reqDto := req.UpdateProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}

	product, err := productService.FindProductById(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.VenueId != nil {
		product.VenueId = reqDto.VenueId
	}
	if reqDto.Name != nil {
		product.Name = reqDto.Name
	}
	if reqDto.Type != nil {
		product.Type = reqDto.Type
	}
	if reqDto.CurrentPrice != nil {
		product.CurrentPrice = reqDto.CurrentPrice
	}
	if reqDto.Price != nil {
		product.Price = reqDto.Price
	}
	if reqDto.PayMark != nil {
		product.PayMark = reqDto.PayMark
	}
	if reqDto.Barcode != nil {
		product.Barcode = reqDto.Barcode
	}
	if reqDto.AreaPrices != nil {
		product.AreaPrices = reqDto.AreaPrices
	}
	if reqDto.BuyGiftPlan != nil {
		product.BuyGiftPlan = reqDto.BuyGiftPlan
	}
	if reqDto.TimeSlotPrices != nil {
		product.TimeSlotPrices = reqDto.TimeSlotPrices
	}
	if reqDto.DistributionChannels != nil {
		product.DistributionChannels = reqDto.DistributionChannels
	}
	if reqDto.MemberCardPaymentRestrictions != nil {
		product.MemberCardPaymentRestrictions = reqDto.MemberCardPaymentRestrictions
	}
	if reqDto.MinimumSaleQuantity != nil {
		product.MinimumSaleQuantity = reqDto.MinimumSaleQuantity
	}
	if reqDto.IsRealPriceProduct != nil {
		product.IsRealPriceProduct = reqDto.IsRealPriceProduct
	}
	if reqDto.AuxiliaryFormula != nil {
		product.AuxiliaryFormula = reqDto.AuxiliaryFormula
	}
	if reqDto.Category != nil {
		product.Category = reqDto.Category
	}
	if reqDto.IsMemberDiscountable != nil {
		product.IsMemberDiscountable = reqDto.IsMemberDiscountable
	}
	if reqDto.IsOrderDiscountable != nil {
		product.IsOrderDiscountable = reqDto.IsOrderDiscountable
	}
	if reqDto.IsOrderReduceable != nil {
		product.IsOrderReduceable = reqDto.IsOrderReduceable
	}
	if reqDto.AllowRepeatBuy != nil {
		product.AllowRepeatBuy = reqDto.AllowRepeatBuy
	}
	if reqDto.RecommendCombos != nil {
		product.RecommendCombos = reqDto.RecommendCombos
	}
	if reqDto.MemberCardLimits != nil {
		product.MemberCardLimits = reqDto.MemberCardLimits
	}
	if reqDto.Flavors != nil {
		product.Flavors = reqDto.Flavors
	}
	if reqDto.Ingredients != nil {
		product.Ingredients = reqDto.Ingredients
	}
	if reqDto.IsDisplayed != nil {
		product.IsDisplayed = reqDto.IsDisplayed
	}
	if reqDto.AllowStaffGift != nil {
		product.AllowStaffGift = reqDto.AllowStaffGift
	}
	if reqDto.CountToMinCharge != nil {
		product.CountToMinCharge = reqDto.CountToMinCharge
	}
	if reqDto.CountToPerformance != nil {
		product.CountToPerformance = reqDto.CountToPerformance
	}
	if reqDto.IsPromotion != nil {
		product.IsPromotion = reqDto.IsPromotion
	}
	if reqDto.IsSoldOut != nil {
		product.IsSoldOut = reqDto.IsSoldOut
	}
	if reqDto.AllowWineStorage != nil {
		product.AllowWineStorage = reqDto.AllowWineStorage
	}
	if reqDto.GiftVoucher != nil {
		product.GiftVoucher = reqDto.GiftVoucher
	}
	if reqDto.CalculateInventory != nil {
		product.CalculateInventory = reqDto.CalculateInventory
	}
	if reqDto.IsAreaSpecified != nil {
		product.IsAreaSpecified = reqDto.IsAreaSpecified
	}
	if reqDto.SelectedAreas != nil {
		product.SelectedAreas = reqDto.SelectedAreas
	}
	if reqDto.IsRoomTypeSpecified != nil {
		product.IsRoomTypeSpecified = reqDto.IsRoomTypeSpecified
	}
	if reqDto.SelectedRoomTypes != nil {
		product.SelectedRoomTypes = reqDto.SelectedRoomTypes
	}
	if reqDto.StartTime != nil {
		product.StartTime = reqDto.StartTime
	}
	if reqDto.EndTime != nil {
		product.EndTime = reqDto.EndTime
	}
	if reqDto.Description != nil {
		product.Description = reqDto.Description
	}
	if reqDto.Image != nil {
		product.Image = reqDto.Image
	}
	if reqDto.LowStockThreshold != nil {
		product.LowStockThreshold = reqDto.LowStockThreshold
	}
	if reqDto.DeliveryTimeout != nil {
		product.DeliveryTimeout = reqDto.DeliveryTimeout
	}
	if reqDto.SupportsExternalDelivery != nil {
		product.SupportsExternalDelivery = reqDto.SupportsExternalDelivery
	}
	if reqDto.ExternalDeliveryPrice != nil {
		product.ExternalDeliveryPrice = reqDto.ExternalDeliveryPrice
	}
	if reqDto.Unit != nil {
		product.Unit = reqDto.Unit
	}

	err = productService.UpdateProduct(ctx, product)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, productTransfer.PoToVo(*product))
}

// @Summary 删除产品
// @Description 删除产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.DeleteProductReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/delete [post]
func (controller *ProductController) DeleteProduct(ctx *gin.Context) {
	reqDto := req.DeleteProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用应用服务处理删除逻辑
	err = productAppService.DeleteProduct(ctx, &reqDto)
	if err != nil {
		// 错误信息已经包含了详细的业务提示，直接展示
		Result_fail[any](ctx, GeneralCodes.ShowDialogError.Code, err.Error())
		return
	}

	Result_success[any](ctx, nil)
}

// @Summary 查询产品
// @Description 查询产品
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.QueryProductReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ProductVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/query [post]
func (controller *ProductController) QueryProducts(ctx *gin.Context) {
	reqDto := req.QueryProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}

	list, err := productService.FindAllProduct(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	newList := []vo.ProductVO{}
	for _, v := range *list {
		newList = append(newList, productTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &newList)
}

// @Summary 查询产品列表
// @Description 查询产品列表
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.QueryProductReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.ProductVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/list [post]
func (controller *ProductController) ListProducts(ctx *gin.Context) {
	reqDto := req.QueryProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}

	list, totalCount, err := productService.FindAllProductWithPagination(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	page := vo.PageVO[[]vo.ProductVO]{}
	page.PageNum = *reqDto.PageNum
	page.PageSize = *reqDto.PageSize
	page.Total = totalCount
	page.Data = []vo.ProductVO{}
	for _, v := range *list {
		page.Data = append(page.Data, productTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &page)
}

// @Summary 查询产品类型列表
// @Description 查询产品类型列表
// @Tags 产品
// @Accept json
// @Produce json
// @Success 200 {object} Result[vo.ProductTypeAndPackageVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/list-types [post]
func (controller *ProductController) ListTypes(ctx *gin.Context) {
	reqDto := req.QueryProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}
	productTypeAndPackageVO := vo.ProductTypeAndPackageVO{}
	// 1. 查询商品类型
	productTypes, err := productTypeService.FindAllProductType(ctx, &req.QueryProductTypeReqDto{VenueId: venueId, IsDisplayed: util.Ptr(true)})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	productTypeVOs := []vo.ProductTypeVO{}
	for _, productType := range *productTypes {
		productTypeVOs = append(productTypeVOs, productTypeTransfer.PoToVo(productType))
	}
	productTypeAndPackageVO.ProductTypeVOs = productTypeVOs

	// 2. 查询套餐类型
	productPackageTypes, err := productPackageTypeService.FindAllProductPackageType(ctx, &req.QueryProductPackageTypeReqDto{VenueId: venueId, IsDisplayed: util.Ptr(true)})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	productPackageTypeVOs := []vo.ProductPackageTypeVO{}
	for _, productPackageType := range *productPackageTypes {
		productPackageTypeVOs = append(productPackageTypeVOs, productPackageTypeTransfer.PoToVo(productPackageType))
	}
	productTypeAndPackageVO.ProductPackageTypeVOs = productPackageTypeVOs
	Result_success[any](ctx, &productTypeAndPackageVO)
}

// @Summary 查询产品详情
// @Description 查询产品详情
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.QueryProductReqDto true "请求体"
// @Success 200 {object} Result[vo.ProductOrPackageRVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/query-detail-bytype [post]
func (controller *ProductController) QueryDetailByType(ctx *gin.Context) {
	reqDto := req.QueryProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}
	reqDto.IsDisplayed = util.Ptr(true)

	returnVO, err := productCommonService.QueryProductOrPackageDetail(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, returnVO)
}

// @Summary 产品估清
// @Description 产品估清
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.UpdateProductSoldOutReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/sold-out [post]
func (controller *ProductController) SoldOut(ctx *gin.Context) {
	reqDto := req.UpdateProductSoldOutReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id为空")
		return
	}
	if reqDto.SoldOut == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "soldOut为空")
		return
	}
	if !util.InList(*reqDto.SoldOut, []bool{true, false}) {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "soldOut参数错误 true:洁清 false:不洁清")
		return
	}
	product, err := productService.FindProductById(ctx, *reqDto.Id)
	if err != nil || product == nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "产品不存在")
		return
	}
	toUpdateProduct := po.Product{
		Id:          product.Id,
		IsSoldOut:   reqDto.SoldOut,
		SoldOutTime: util.GetItPtr(int64(util.TimeNowUnix())),
	}
	err = productService.UpdateProductPartial(ctx, &toUpdateProduct)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

// @Summary 查询产品类型列表-pad端
// @Description 查询产品类型列表-pad端
// @Tags 产品
// @Accept json
// @Produce json
// @Success 200 {object} Result[vo.ProductTypeAndPackageVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/list-types-pad [post]
func (controller *ProductController) ListTypesPad(ctx *gin.Context) {
	reqDto := req.QueryProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}
	productTypeAndPackageVO := vo.ProductTypeAndPackageVO{}
	// 1. 查询商品类型
	productTypes, err := productTypeService.FindAllProductType(ctx, &req.QueryProductTypeReqDto{VenueId: venueId, IsDisplayed: util.Ptr(true)})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	productTypeVOs := []vo.ProductTypeVO{}
	for _, productType := range *productTypes {
		productTypeVOs = append(productTypeVOs, productTypeTransfer.PoToVo(productType))
	}
	productTypeAndPackageVO.ProductTypeVOs = productTypeVOs

	// 2. 查询套餐类型
	productPackageTypes, err := productPackageTypeService.FindAllProductPackageType(ctx, &req.QueryProductPackageTypeReqDto{VenueId: venueId, IsDisplayed: util.Ptr(true)})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	productPackageTypeVOs := []vo.ProductPackageTypeVO{}
	for _, productPackageType := range *productPackageTypes {
		productPackageTypeVOs = append(productPackageTypeVOs, productPackageTypeTransfer.PoToVo(productPackageType))
	}
	productTypeAndPackageVO.ProductPackageTypeVOs = productPackageTypeVOs
	Result_success[any](ctx, &productTypeAndPackageVO)
}

// @Summary 查询产品详情-pad端
// @Description 查询产品详情-pad端
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.QueryProductReqDto true "请求体"
// @Success 200 {object} Result[vo.ProductOrPackageRVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/query-detail-bytype-pad [post]
func (controller *ProductController) QueryDetailByTypePad(ctx *gin.Context) {
	reqDto := req.QueryProductReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId为空")
		return
	}
	reqDto.IsDisplayed = util.Ptr(true)

	returnVO, err := productCommonService.QueryProductOrPackageDetail(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, returnVO)
}

// @Summary 查询产品详情-pad端
// @Description 查询产品详情-pad端
// @Tags 产品
// @Accept json
// @Produce json
// @Param body body req.V3QuerySupplyReqDto true "请求体"
// @Success 200 {object} Result[vo.ProductOrPackageRVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product/query-product-supplement [post]
func (controller *ProductController) QuerySupply(ctx *gin.Context) {
	reqDto := req.V3QuerySupplyReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	reqDto.Id = util.Ptr("buchajiashangpin20250627")
	product, err := productService.FindProductById(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	productVO := productTransfer.PoToVo(*product)
	productOrPackageRVO := vo.ProductOrPackageRVO{
		ProductVOs: []vo.ProductVO{productVO},
	}
	Result_success[any](ctx, &productOrPackageRVO)
}
