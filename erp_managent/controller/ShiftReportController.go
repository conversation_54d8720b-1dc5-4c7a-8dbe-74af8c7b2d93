package controller

import (
	"encoding/json"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type ShiftReportController struct{}

var (
	shiftReportService   = impl.ShiftReportService{}
	shiftReportTransfer  = transfer.ShiftReportTransfer{}
	payBillTransfer      = transfer.PayBillTransfer{}
	orderAndPayService   = impl.OrderAndPayService{}
	shiftReportValidator = ShiftReportValidator{}
	payRecordService     = impl.PayRecordService{}
)

// @Summary 添加班次报告
// @Description 添加班次报告
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.AddShiftReportReqDto true "请求体"
// @Success 200 {object} Result[vo.ShiftReportVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/add [post]
func (controller *ShiftReportController) AddShiftReport(ctx *gin.Context) {
	reqDto := req.AddShiftReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	shiftReport := po.ShiftReport{}
	if reqDto.EmployeeId != nil {
		shiftReport.EmployeeId = reqDto.EmployeeId
	}
	if reqDto.VenueId != nil {
		shiftReport.VenueId = reqDto.VenueId
	}
	if reqDto.OpenedTables != nil {
		shiftReport.OpenedTables = reqDto.OpenedTables
	}
	if reqDto.SettledTables != nil {
		shiftReport.SettledTables = reqDto.SettledTables
	}
	if reqDto.UnsettledTables != nil {
		shiftReport.UnsettledTables = reqDto.UnsettledTables
	}
	if reqDto.OpenedAmount != nil {
		shiftReport.OpenedAmount = reqDto.OpenedAmount
	}
	if reqDto.SettledAmount != nil {
		shiftReport.SettledAmount = reqDto.SettledAmount
	}
	if reqDto.UnsettledAmount != nil {
		shiftReport.UnsettledAmount = reqDto.UnsettledAmount
	}
	if reqDto.SoldProducts != nil {
		shiftReport.SoldProducts = reqDto.SoldProducts
	}
	if reqDto.SoldProductsAmount != nil {
		shiftReport.SoldProductsAmount = reqDto.SoldProductsAmount
	}
	if reqDto.GiftedProducts != nil {
		shiftReport.GiftedProducts = reqDto.GiftedProducts
	}
	if reqDto.GiftedProductsAmount != nil {
		shiftReport.GiftedProductsAmount = reqDto.GiftedProductsAmount
	}
	if reqDto.ReturnedProducts != nil {
		shiftReport.ReturnedProducts = reqDto.ReturnedProducts
	}
	if reqDto.ReturnedProductsAmount != nil {
		shiftReport.ReturnedProductsAmount = reqDto.ReturnedProductsAmount
	}
	if reqDto.MemberRecharge != nil {
		shiftReport.MemberRecharge = reqDto.MemberRecharge
	}
	if reqDto.MemberConsumption != nil {
		shiftReport.MemberConsumption = reqDto.MemberConsumption
	}
	if reqDto.PaymentMethodStats != nil {
		shiftReport.PaymentMethodStats = reqDto.PaymentMethodStats
	}
	if reqDto.ShiftTime != nil {
		shiftReport.ShiftTime = reqDto.ShiftTime
	}

	err = shiftReportService.CreateShiftReport(ctx, &shiftReport)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, shiftReportTransfer.PoToVo(shiftReport))
}

// @Summary 更新班次报告
// @Description 更新班次报告
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.UpdateShiftReportReqDto true "请求体"
// @Success 200 {object} Result[vo.ShiftReportVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/update [post]
func (controller *ShiftReportController) UpdateShiftReport(ctx *gin.Context) {
	reqDto := req.UpdateShiftReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}
	shiftReport, err := shiftReportService.FindShiftReportById(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.OpenedTables != nil {
		shiftReport.OpenedTables = reqDto.OpenedTables
	}
	if reqDto.SettledTables != nil {
		shiftReport.SettledTables = reqDto.SettledTables
	}
	if reqDto.UnsettledTables != nil {
		shiftReport.UnsettledTables = reqDto.UnsettledTables
	}
	if reqDto.OpenedAmount != nil {
		shiftReport.OpenedAmount = reqDto.OpenedAmount
	}
	if reqDto.SettledAmount != nil {
		shiftReport.SettledAmount = reqDto.SettledAmount
	}
	if reqDto.UnsettledAmount != nil {
		shiftReport.UnsettledAmount = reqDto.UnsettledAmount
	}
	if reqDto.SoldProducts != nil {
		shiftReport.SoldProducts = reqDto.SoldProducts
	}
	if reqDto.SoldProductsAmount != nil {
		shiftReport.SoldProductsAmount = reqDto.SoldProductsAmount
	}
	if reqDto.GiftedProducts != nil {
		shiftReport.GiftedProducts = reqDto.GiftedProducts
	}
	if reqDto.GiftedProductsAmount != nil {
		shiftReport.GiftedProductsAmount = reqDto.GiftedProductsAmount
	}
	if reqDto.ReturnedProducts != nil {
		shiftReport.ReturnedProducts = reqDto.ReturnedProducts
	}
	if reqDto.ReturnedProductsAmount != nil {
		shiftReport.ReturnedProductsAmount = reqDto.ReturnedProductsAmount
	}
	if reqDto.MemberRecharge != nil {
		shiftReport.MemberRecharge = reqDto.MemberRecharge
	}
	if reqDto.MemberConsumption != nil {
		shiftReport.MemberConsumption = reqDto.MemberConsumption
	}
	if reqDto.PaymentMethodStats != nil {
		shiftReport.PaymentMethodStats = reqDto.PaymentMethodStats
	}
	if reqDto.ShiftTime != nil {
		shiftReport.ShiftTime = reqDto.ShiftTime
	}

	err = shiftReportService.UpdateShiftReport(ctx, shiftReport)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, shiftReportTransfer.PoToVo(*shiftReport))
}

// @Summary 删除班次报告
// @Description 删除班次报告
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.DeleteShiftReportReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/delete [post]
func (controller *ShiftReportController) DeleteShiftReport(ctx *gin.Context) {
	reqDto := req.DeleteShiftReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	err = shiftReportService.DeleteShiftReport(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

// @Summary 查询班次报告
// @Description 查询班次报告
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ShiftReportVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/query [post]
func (controller *ShiftReportController) QueryShiftReports(ctx *gin.Context) {
	reqDto := req.QueryShiftReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	list, err := shiftReportService.FindAllShiftReport(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	newList := []vo.ShiftReportVO{}
	for _, v := range *list {
		newList = append(newList, shiftReportTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &newList)
}

// @Summary 查询班次报告列表
// @Description 查询班次报告列表
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.ShiftReportVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/list [post]
func (a *ShiftReportController) ListShiftReports(ctx *gin.Context) {
	reqDto := req.QueryShiftReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	list, totalCount, err := shiftReportService.FindAllShiftReportWithPagination(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	page := vo.PageVO[[]vo.ShiftReportVO]{}
	page.PageNum = *reqDto.PageNum
	page.PageSize = *reqDto.PageSize
	page.Total = totalCount
	page.Data = []vo.ShiftReportVO{}
	for _, v := range *list {
		page.Data = append(page.Data, shiftReportTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &page)
}

// @Summary 查询班次报告收银单
// @Description 查询班次报告收银单
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportGetPayBillsReqDto true "请求体"
// @Success 200 {object} Result[[]vo.PayBillVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/get-pay-bills [post]
func (controller *ShiftReportController) QueryShiftReportGetPayBills(ctx *gin.Context) {
	reqDto := req.QueryShiftReportGetPayBillsReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工ID不能为空")
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "门店ID不能为空")
		return
	}
	venues, _ := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: reqDto.VenueId,
	})
	if len(*venues) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "门店不存在")
		return
	}
	venueStartHours := *(*venues)[0].StartHours
	venueStartTime := int64(0)
	if venueStartHours != "" {
		venueStartTimeStr := util.GetDateTimeStr()[:10] + venueStartHours + ":00"
		venueStartTimeObj, _ := util.ParseDatetime(venueStartTimeStr)
		venueStartTime = venueStartTimeObj.Unix()
	}
	shiftReports, _, _ := shiftReportService.FindAllShiftReportWithPagination(ctx, &req.QueryShiftReportReqDto{
		EmployeeId: reqDto.EmployeeId,
		VenueId:    reqDto.VenueId,
		OrderBy:    util.GetItPtr("ctime desc"),
		PageNum:    util.GetItPtr(1),
		PageSize:   util.GetItPtr(1),
	})
	startTimeLast := int64(0)
	if len(*shiftReports) > 0 {
		startTimeLast = *(*shiftReports)[0].Ctime
		if startTimeLast < venueStartTime {
			startTimeLast = venueStartTime
		}
	}
	// 如果startTimeLast为0，则表示没有班次报告，则查询24小时前的数据
	if startTimeLast == 0 {
		startTimeLast = int64(util.TimeNowUnix() - 24*3600)
	}
	list, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		EmployeeId: reqDto.EmployeeId,
		VenueId:    reqDto.VenueId,
		StartTime:  &startTimeLast,
		EndTime:    reqDto.EndTime,
		StatusList: &[]string{_const.PAY_STATUS_PAID, _const.PAY_STATUS_REFUND},
	})
	payIds := []string{}
	roomIds := []string{}
	for _, v := range *list {
		payIds = append(payIds, *v.BillId)
		util.AddListElement(&roomIds, *v.RoomId)
	}
	rooms, _ := roomService.FindAllRoom(ctx, &req.QueryRoomReqDto{
		Ids: &roomIds,
	})
	payId2Orders := map[string][]po.Order{}
	if len(payIds) > 0 {
		orderAndPays, _ := orderAndPayService.FindAllOrderAndPay(ctx, &req.QueryOrderAndPayReqDto{
			BillIdList: &payIds,
		})
		orderNos := []string{}
		for _, v := range *orderAndPays {
			orderNos = append(orderNos, *v.OrderNo)
		}
		orders, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{
			OrderNos: &orderNos,
		})
		// 构造对应关系
		for _, orderAndPay := range *orderAndPays {
			for _, order := range *orders {
				if *orderAndPay.OrderNo == *order.OrderNo {
					payId2Orders[*orderAndPay.BillId] = append(payId2Orders[*orderAndPay.BillId], order)
				}
			}
		}
	}
	payIdTags := map[string][]string{}
	for payId, orders := range payId2Orders {
		orderTagArray := []string{}
		for _, order := range orders {
			orderTagArray = append(orderTagArray, *order.Mark)
		}
		payIdTags[payId] = orderTagArray
	}
	payBillVOs := []vo.PayBillVO{}
	for _, v := range *list {
		orderTagArray := payIdTags[*v.BillId]
		orderTag := ""
		// 1. 有开台标签为：开台
		// 2. 其次有开台续台标签为：开台续台
		// 3. 其次有点单标签为：点单
		if util.InList(_const.ORDER_MARK_OPENING, orderTagArray) {
			orderTag = _const.ORDER_MARK_OPENING
		} else if util.InList(_const.ORDER_MARK_OPENING_CONTINUE, orderTagArray) {
			orderTag = _const.ORDER_MARK_OPENING_CONTINUE
		} else {
			orderTag = _const.ORDER_MARK_ADDITIONAL
		}
		payBillVO := payBillTransfer.PoToVo(v)
		for _, room := range *rooms {
			if *room.Id == *v.RoomId {
				payBillVO.RoomName = *room.Name
				break
			}
		}
		payBillVO.OrderTag = orderTag
		payBillVOs = append(payBillVOs, payBillVO)
	}
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, &payBillVOs)
}

// @Summary 查询班次报告营收报告
// @Description 查询班次报告营收报告
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportGetIncomeReportReqDto true "请求体"
// @Success 200 {object} Result[vo.ShiftReportGetIncomeVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/get-income-report [post]
func (controller *ShiftReportController) QueryShiftReportGetIncomeReport(ctx *gin.Context) {
	reqDto := req.QueryShiftReportGetIncomeReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	_, venueStartTime, _, err := shiftReportValidator.CheckShiftReportParam(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	// 查询最新的交班记录
	shiftHandoverForms, err := shiftHandoverFormService.FindLastOneShiftHandoverForm(ctx, *reqDto.VenueId, *reqDto.EmployeeId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	shiftHandoverForm := (*shiftHandoverForms)[0]
	startTimeFinal := int64(0)
	if shiftHandoverForm.Ctime != nil { // 如果班次报告不为空，则取班次报告的开始时间
		startTimeFinal = util.MinNumber(*shiftHandoverForm.Ctime, venueStartTime)
	}
	// 如果startTimeFinal为0，则表示没有班次报告，则查询24小时前的数据
	if startTimeFinal == 0 {
		startTimeFinal = int64(util.TimeNowUnix() - 24*3600)
	}
	status := []string{_const.PAY_STATUS_PAID, _const.PAY_STATUS_REFUND}
	payBills, _ := payBillService.FindPayBill4ShiftHoldoverForm(ctx, *reqDto.VenueId, *reqDto.EmployeeId, startTimeFinal, *reqDto.EndTime, status)
	sessionIds := []string{}
	incomeVO := vo.ShiftReportGetIncomeVO{VenueId: *reqDto.VenueId}
	for _, v := range *payBills {
		util.AddListElement(&sessionIds, *v.SessionId)
		// 营业应收
		// incomeVO.BusinessReceivable += getCorrectFee(&v, *v.OriginalFee)
		// 营业实收
		// incomeVO.BusinessActual += getCorrectFee(&v, *v.TotalFee)
		// 营业净收 = 营业应收 暂
		// incomeVO.BusinessNet += getCorrectFee(&v, *v.OriginalFee)
		// 商家优惠
		// if *v.TotalFee < *v.OriginalFee {
		// 	incomeVO.MerchantDiscount += getCorrectFee(&v, *v.DiscountRoomAmount) + getCorrectFee(&v, *v.DiscountProductAmount) + getCorrectFee(&v, *v.ReduceRoomAmount) + getCorrectFee(&v, *v.ReduceProductAmount)
		// }
		// 会员优惠
		// if *v.ReduceAmount > 0 {
		// 	incomeVO.MemberDiscount += getCorrectFee(&v, *v.ReduceAmount)
		// }
		// 低消差额调整
		incomeVO.MinimumAdjustment = 0
		// 冲账应收
		incomeVO.WriteOffReceivable += getCorrectFee(&v, *v.CreditAmount)
		// 冲账实收
		incomeVO.WriteOffActual += getCorrectFee(&v, *v.CreditAmount)
		// 冲账优惠
		incomeVO.WriteOffDiscount = 0
		// 会员卡支付
		// if *v.PayType == _const.PAY_TYPE_MEMBER_CARD {
		// 	incomeVO.MemberCardPay += getCorrectFee(&v, *v.TotalFee)
		// }
		// 挂账
		incomeVO.CreditAmount += getCorrectFee(&v, *v.CreditAmount)
		// 充值金额
		incomeVO.RechargeAmount = 0
		// 充值赠送
		incomeVO.RechargeGift = 0
		// 员工支付
		incomeVO.StaffGifts = 0

		// 支付类型
		switch "*v.PayType" {
		case _const.PAY_TYPE_RECORD_WECHAT:
			// 微信支付
			incomeVO.WechatPayAmount += getCorrectFee(&v, *v.TotalFee)
		case _const.PAY_TYPE_RECORD_ALIPAY:
			// 支付宝支付
			incomeVO.AlipayPayAmount += getCorrectFee(&v, *v.TotalFee)
		case _const.PAY_TYPE_RECORD_CASH:
			// 现金支付
			incomeVO.CashPayAmount += getCorrectFee(&v, *v.TotalFee)
		case _const.PAY_TYPE_MEMBER_CARD:
			// 会员卡本金
			incomeVO.MemberCardPrincipalAmount += getCorrectFee(&v, *v.TotalFee)
		case _const.PAY_TYPE_RECORD_BANK:
			// 银行卡支付
			incomeVO.BankCardPayAmount += getCorrectFee(&v, *v.TotalFee)
		}
	}
	incomeVO.CustomerBatches = int64(len(sessionIds))
	Result_success[any](ctx, &incomeVO)
}

// @Summary 交班
// @Description 交班
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportHandOverReqDto true "请求体"
// @Success 200 {object} Result[vo.ShiftReportGetIncomeVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/shift-report/handover [post]
func (controller *ShiftReportController) QueryShiftReportHandOver(ctx *gin.Context) {
	reqDto := req.QueryShiftReportHandOverReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	_, venueStartTime, _, err := shiftReportValidator.CheckShiftHandoverParam(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	// 查询最新的交班记录
	shiftHandoverForms, err := shiftHandoverFormService.FindLastOneShiftHandoverForm(ctx, *reqDto.VenueId, *reqDto.EmployeeId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	shiftHandoverForm := (*shiftHandoverForms)[0]
	startTimeFinal := int64(0)
	if shiftHandoverForm.Ctime != nil { // 如果班次报告不为空，则取班次报告的开始时间
		startTimeFinal = util.MinNumber(*shiftHandoverForm.Ctime, venueStartTime)
	}
	// 如果startTimeFinal为0，则表示没有班次报告，则查询24小时前的数据
	if startTimeFinal == 0 {
		startTimeFinal = int64(util.TimeNowUnix() - 24*3600)
	}
	status := []string{_const.PAY_STATUS_PAID, _const.PAY_STATUS_REFUND}
	endTime := int64(util.TimeNowUnix())
	reqDto.EndTime = &endTime
	payBills, _ := payBillService.FindPayBill4ShiftHoldoverForm(ctx, *reqDto.VenueId, *reqDto.EmployeeId, startTimeFinal, endTime, status)
	err = shiftHandoverFormService.SaveHandOverGroupByDay(ctx, startTimeFinal, endTime, *reqDto.VenueId, payBills)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

func getCorrectFee(payBill *po.PayBill, fee int64) int64 {
	if *payBill.Status == _const.PAY_STATUS_REFUND {
		return -fee
	}
	return fee
}

// @Summary 获取门店日结清单
// @Description 获取门店指定时间范围内的日结清单数据
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryDailyShiftReportReqDto true "请求体"
// @Success 200 {object} Result[vo.DailyShiftReportVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/shift-report/daily [post]
func (controller *ShiftReportController) GetDailyShiftReport(ctx *gin.Context) {
	// 1. 绑定请求参数
	reqDto := req.QueryDailyShiftReportReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 2. 调用service获取数据
	result, err := shiftReportService.GetDailyShiftReport(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 3. 返回结果
	Result_success[any](ctx, result)
}

// @Summary 查询班次报告营收报告
// @Description 查询班次报告营收报告
// @Tags 班次报告
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportGetIncomeDailyReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ShiftReportDaily] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/v3/shift-report/get-income-daily [post]
func (controller *ShiftReportController) V3QueryShiftReportGetIncomeDaily(ctx *gin.Context) {
	reqDto := req.QueryShiftReportGetIncomeDailyReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店id不能为空")
		return
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "员工id不能为空")
		return
	}
	venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: reqDto.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	employees, err := employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Id: reqDto.EmployeeId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	employee := (*employees)[0]
	venue := (*venues)[0]
	err = util.ValidateVenueEndHour(venue.StartHours)
	if err != nil {
		venue.StartHours = util.GetItPtr("07:00")
		venue.EndHours = util.GetItPtr("07:00")
	}
	startHour := *venue.StartHours

	nowTime := int64(util.TimeNowUnix())
	// 查询最新的交班记录
	shiftHandoverForms, err := shiftHandoverFormService.FindLastOneShiftHandoverForm(ctx, *reqDto.VenueId, *reqDto.EmployeeId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	var shiftHandoverForm *po.ShiftHandoverForm
	if len(*shiftHandoverForms) > 0 {
		shiftHandoverForm = &(*shiftHandoverForms)[0]
	}
	startTimeFinal := new(int64)
	if shiftHandoverForm != nil && shiftHandoverForm.EndTime != nil { // 如果班次报告不为空，则取班次报告的开始时间
		*startTimeFinal = *shiftHandoverForm.EndTime
	}

	// 查询上次交班后的订单
	currentDayOrders, err := orderService.FindOrdersLastestPaid(ctx, *reqDto.VenueId, *reqDto.EmployeeId, startTimeFinal)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	currentOrderNos := []string{}
	for _, v := range currentDayOrders {
		util.AddListElement(&currentOrderNos, *v.OrderNo)
	}
	currentOrderRoomPlans, _ := orderRoomPlanService.FindOrderRoomPlansByOrderNos(ctx, *reqDto.VenueId, currentOrderNos)
	currentOrderProducts, _ := orderProductService.FindOrderProductsByOrderNos(ctx, *reqDto.VenueId, currentOrderNos)
	currentOrderNoToOrderRoomPlanVOMap := map[string][]vo.OrderRoomPlanVO{}
	for _, v := range currentOrderRoomPlans {
		currentOrderNoToOrderRoomPlanVOMap[*v.OrderNo] = append(currentOrderNoToOrderRoomPlanVOMap[*v.OrderNo], orderRoomPlanTransfer.PoToVo(v))
	}
	currentOrderNoToOrderProductVOMap := map[string][]vo.OrderProductVO{}
	for _, v := range currentOrderProducts {
		currentOrderNoToOrderProductVOMap[*v.OrderNo] = append(currentOrderNoToOrderProductVOMap[*v.OrderNo], orderProductTransfer.PoToVo(v))
	}

	// 查询上次交班后的场次
	currentDaySessions, err := sessionService.FindSessionsLastestPaid(ctx, *reqDto.VenueId, *reqDto.EmployeeId, startTimeFinal)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	sessionIdToSessionMap := map[string]po.Session{}
	for _, v := range currentDaySessions {
		sessionIdToSessionMap[*v.Id] = v
	}
	// 查询上次交班后的赠品订单
	giftOrders, err := shiftReportService.FindGiftOrderInfos(ctx, *reqDto.VenueId, *reqDto.EmployeeId, startTimeFinal)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	// 查询上次交班后的充值金额
	memberCardOperationsRecharge, err := memberCardOperationService.FindMemberCardOperationByVenueIdAndEmployeeId(ctx, *reqDto.VenueId, *reqDto.EmployeeId, *startTimeFinal)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 查询上次交班后的消费记录
	memberCardConsumes, err := memberCardConsumeService.FindMemberCardConsumeByVenueIdAndEmployeeId(ctx, *reqDto.VenueId, *reqDto.EmployeeId, *startTimeFinal)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 查询上次交班后的支付详情
	roomIds := []string{}
	billIds := []string{}
	payBillsTmp, _ := payBillService.FindPayBillLastestPaid(ctx, *reqDto.VenueId, *reqDto.EmployeeId, startTimeFinal)
	payBills := []po.PayBill{}
	for _, v := range payBillsTmp {
		// if v.IsBack != nil && *v.IsBack {
		// 	continue
		// }
		payBills = append(payBills, v)
		util.AddListElement(&roomIds, *v.RoomId)
		util.AddListElement(&billIds, *v.BillId)
	}
	rooms, err := roomService.FindAllRoom(ctx, &req.QueryRoomReqDto{
		Ids: &roomIds,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	// 查询上次交班后的支付记录
	payRecords, _ := payRecordService.FindPayRecordByBillIds(ctx, *reqDto.VenueId, billIds)
	// 查询上次交班后的订单和支付记录
	orderAndPays, _ := orderAndPayService.FindOrderAndPaysByBillIds(ctx, billIds)
	// 查询上次交班后的订单
	orderNos := []string{}
	for _, v := range orderAndPays {
		util.AddListElement(&orderNos, *v.OrderNo)
	}
	orders, _ := orderService.FindOrdersByOrderNos(ctx, *reqDto.VenueId, orderNos)
	orderRoomPlans, _ := orderRoomPlanService.FindOrderRoomPlansByOrderNos(ctx, *reqDto.VenueId, orderNos)
	orderProducts, _ := orderProductService.FindOrderProductsByOrderNos(ctx, *reqDto.VenueId, orderNos)

	payBillShiftReportVOs := shiftReportService.BuildPayBillShiftReportVO(ctx, payBills, payRecords, orderAndPays, orders, orderRoomPlans, orderProducts)
	// retVOs := shiftReportService.BuildShiftReportDailyVO(ctx, payBillShiftReportVOs, giftOrders, currentDayOrders, startHour, startTimeFinal, reqDto, rooms, memberCardOperationsRecharge, memberCardConsumes, sessionIdToSessionMap)

	// 充值
	memberCardOperationsRechargeVOs := []vo.MemberCardOperationVO{}
	rechargeBillIds := []string{}
	for _, v := range memberCardOperationsRecharge {
		memberCardOperationsRechargeVOs = append(memberCardOperationsRechargeVOs, memberCardOperationTransfer.PoToVo(v))
		util.AddListElement(&rechargeBillIds, *v.BillId)
	}
	memberRechargeBills, _ := memberRechargeBillService.FindMemberRechargeBillsByBillIds(ctx, *reqDto.VenueId, rechargeBillIds)
	memberRechargeBillVOs := []vo.MemberRechargeBillVO{}
	rBillIdToMemberRechargeBillMap := map[string]vo.MemberRechargeBillVO{}
	for _, v := range *memberRechargeBills {
		voTmp := memberRechargeBillTransfer.PoToVo(v)
		rBillIdToMemberRechargeBillMap[*v.BillId] = voTmp
		memberRechargeBillVOs = append(memberRechargeBillVOs, voTmp)
	}
	// 更新充值账单
	for index, v := range memberCardOperationsRechargeVOs {
		billVO, ok := rBillIdToMemberRechargeBillMap[v.BillId]
		if ok {
			memberCardOperationsRechargeVOs[index].MemberRechargeBillVO = billVO
		}
	}
	// 消费账单
	consumeBillIds := []string{}
	memberCardConsumesVOs := []vo.MemberCardConsumeVO{}
	for _, v := range memberCardConsumes {
		memberCardConsumesVOs = append(memberCardConsumesVOs, memberCardConsumeTransfer.PoToVo(v))
		util.AddListElement(&consumeBillIds, *v.BillId)
	}
	cousumePayBills, _ := payBillService.FindPayBillsByBillIds(ctx, *reqDto.VenueId, consumeBillIds)
	payBillIdToPayBillMap := map[string]vo.PayBillVO{}
	cousumePayBillVOs := []vo.PayBillVO{}
	for _, v := range cousumePayBills {
		voTmp := payBillTransfer.PoToVo(v)
		payBillIdToPayBillMap[*v.BillId] = voTmp
		cousumePayBillVOs = append(cousumePayBillVOs, voTmp)
	}
	for index, v := range memberCardConsumesVOs {
		billVO, ok := payBillIdToPayBillMap[v.BillId]
		if ok {
			memberCardConsumesVOs[index].PayBillVO = billVO
		}
	}
	currentDayOrdersVOs := []vo.OrderVO{}
	for _, v := range currentDayOrders {
		voTmp := orderTransfer.PoToVo(v)
		voTmp.OrderRoomPlanVOs = currentOrderNoToOrderRoomPlanVOMap[*v.OrderNo]
		voTmp.OrderProductVOs = currentOrderNoToOrderProductVOMap[*v.OrderNo]
		currentDayOrdersVOs = append(currentDayOrdersVOs, voTmp)
	}
	currentDaySessionVOs := []vo.SessionVO{}
	for _, v := range currentDaySessions {
		currentDaySessionVOs = append(currentDaySessionVOs, sessionTransfer.PoToVo(v))
	}
	// 查询门店支付方式设置
	venuePaySettings, err := venuePayTypeSettingService.FindAllVenuePayTypeSetting(ctx, &req.QueryVenuePayTypeSettingReqDto{
		VenueId: reqDto.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	modelBaseShiftReportBO := vo.ModelBaseShiftReportBO{
		PayBillShiftReportVOs:         payBillShiftReportVOs,
		GiftOrderVOs:                  giftOrders,
		CurrentDayOrderVOs:            currentDayOrdersVOs,
		StartHour:                     startHour,
		StartTimeFinal:                startTimeFinal,
		Rooms:                         rooms,
		MemberCardOperationsRecharges: memberCardOperationsRechargeVOs,
		MemberCardConsumes:            memberCardConsumesVOs,
		SessionIdToSessionMap:         sessionIdToSessionMap,
		Venue:                         venue,
		Employee:                      employee,
		ShiftHandoverForms:            shiftHandoverForms,
		CurrentDaySessionVOs:          currentDaySessionVOs,
		MemberRechargeBillVOs:         memberRechargeBillVOs,
		ConsumePayBillVOs:             cousumePayBillVOs,
		NowTime:                       nowTime,
		PayTypeConfigs:                []_const.PayTypeConfig{},
	}
	if len(*venuePaySettings) > 0 {
		poTmp := (*venuePaySettings)[0]
		venuePayTypeSettingVO := venuePayTypeSettingTransfer.PoToVo(poTmp)
		payTypeConfigs := []_const.PayTypeConfig{}
		err := json.Unmarshal([]byte(venuePayTypeSettingVO.TypeInfo), &payTypeConfigs)
		if err == nil {
			modelBaseShiftReportBO.PayTypeConfigs = payTypeConfigs
		}
	}
	retVOs := shiftReportService.BuildShiftReportDailyVO_V2(ctx, reqDto, &modelBaseShiftReportBO)

	Result_success[any](ctx, &retVOs)
}

// @Summary 交班
// @Description 交班
// @Tags 交班
// @Accept json
// @Produce json
// @Param body body req.V3ShiftReportHandOverReqDto true "请求体"
// @Success 200 {object} Result[[]string] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/v3/shift-report/handover [post]
func (controller *ShiftReportController) V3QueryShiftReportHandOver(ctx *gin.Context) {
	reqDto := req.V3ShiftReportHandOverReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店id不能为空")
		return
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "员工id不能为空")
		return
	}
	venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: reqDto.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	_, err = employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Id: reqDto.EmployeeId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	venue := (*venues)[0]
	err = util.ValidateVenueEndHour(venue.EndHours)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if len(reqDto.ShiftReportDaily) <= 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "日报信息不能为空")
		return
	}
	filteredShiftReportDaily := []vo.ShiftReportDaily{}
	for _, v := range reqDto.ShiftReportDaily {
		if len(v.PayBillVOs) <= 0 && v.MemberCardRechargeData.RechargeAmount <= 0 && v.BusinessData.BillCount <= 0 && v.BusinessData.OpenCount <= 0 && v.BusinessData.OrderUnpaidCount <= 0 {
			continue
		}
		filteredShiftReportDaily = append(filteredShiftReportDaily, v)
	}
	if len(filteredShiftReportDaily) != len(reqDto.ShiftReportDaily) {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "存在日报信息为空的数据，交班失败")
		return
	}

	newShiftHandoverForms, _, err := shiftReportService.SaveShiftHandoverForm(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	handNos := []string{}
	if len(newShiftHandoverForms) > 0 {
		for _, v := range newShiftHandoverForms {
			if v.HandNo != nil {
				handNos = append(handNos, *v.HandNo)
			}
		}
	}

	Result_success[any](ctx, &handNos)
}

// @Summary 交班历史
// @Description 交班历史
// @Tags 交班
// @Accept json
// @Produce json
// @Param body body req.QueryShiftHandoverFormReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.ShiftHandoverFormVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/v3/shift-report/handover/history [post]
func (controller *ShiftReportController) V3QueryShiftReportHandOverHistory(ctx *gin.Context) {
	reqDto := req.QueryShiftHandoverFormReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店id不能为空")
		return
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "员工id不能为空")
		return
	}
	_, err = venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: reqDto.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	_, err = employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Id: reqDto.EmployeeId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	cloneReqDto := util.DeepClone(reqDto)
	cloneReqDto.EmployeeId = nil
	list, totalCount, err := shiftHandoverFormService.FindAllShiftHandoverFormWithPagination(ctx, &cloneReqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	employeeIds := []string{}
	for _, v := range *list {
		util.AddListElement(&employeeIds, *v.EmployeeId)
	}
	employees, err := employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Ids: &employeeIds,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	employeeMap := map[string]vo.EmployeeVO{}
	for _, v := range *employees {
		employeeMap[*v.Id] = employeeTransfer.PoToVo(v)
	}

	page := vo.PageVO[[]vo.ShiftHandoverFormVO]{}
	page.PageNum = *reqDto.PageNum
	page.PageSize = *reqDto.PageSize
	page.Total = totalCount
	page.Data = []vo.ShiftHandoverFormVO{}
	for _, v := range *list {
		fVO := shiftHandoverFormTransfer.PoToVo(v)
		findedEmployee, ok := employeeMap[*v.EmployeeId]
		if ok {
			fVO.EmployeeName = &findedEmployee.Name
		} else {
			fVO.EmployeeName = util.GetItPtr("")
		}
		page.Data = append(page.Data, fVO)
	}
	Result_success[any](ctx, &page)
}

// @Summary 交班记录详情
// @Description 交班记录详情
// @Tags 交班
// @Accept json
// @Produce json
// @Param body body req.V3QueryShiftReportHandOverDetailReqDto true "请求体"
// @Success 200 {object} Result[[]vo.ShiftReportDaily] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/v3/shift-report/handover/detail [post]
func (controller *ShiftReportController) V3QueryShiftReportHandOverDetail(ctx *gin.Context) {
	reqDto := req.V3QueryShiftReportHandOverDetailReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店id不能为空")
		return
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "员工id不能为空")
		return
	}
	if reqDto.HandNo == nil || *reqDto.HandNo == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "交班单号不能为空")
		return
	}
	_, err = venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: reqDto.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	_, err = employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Id: reqDto.EmployeeId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	shiftReportDailys, err := shiftReportService.GetV3QueryShiftReportHandOverDetail(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, shiftReportDailys)
}

// @Summary 账单详情
// @Description 账单详情
// @Tags 交班
// @Accept json
// @Produce json
// @Param body body req.QueryShiftReportBillDetailReqDto true "请求体"
// @Success 200 {object} Result[vo.ShiftReportBillDetailVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/v3/shift-report/bill/detail [post]
func (controller *ShiftReportController) V3QueryShiftReportBillDetail(ctx *gin.Context) {
	reqDto := req.QueryShiftReportBillDetailReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店id不能为空")
		return
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "员工id不能为空")
		return
	}
	if reqDto.BillId == nil || *reqDto.BillId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "账单id不能为空")
		return
	}

	shiftReportBillDetailVO, err := shiftReportService.GetV3QueryShiftReportBillDetail(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, shiftReportBillDetailVO)
}
