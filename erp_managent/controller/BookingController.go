package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type BookingController struct{}

var (
	bookingService  = impl.BookingService{}
	bookingTransfer = transfer.BookingTransfer{}
)

// @Summary 添加预订
// @Description 添加预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.AddBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.BookingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/add [post]
func (controller *BookingController) AddBooking(ctx *gin.Context) {
	reqDto := req.AddBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	booking := po.Booking{}
	if reqDto.VenueId != nil {
		booking.VenueId = reqDto.VenueId
	}
	if reqDto.CustomerName != nil {
		booking.CustomerName = reqDto.CustomerName
	}
	if reqDto.Gender != nil {
		booking.Gender = reqDto.Gender
	}
	if reqDto.CustomerPhone != nil {
		booking.CustomerPhone = reqDto.CustomerPhone
	}
	if reqDto.MemberId != nil {
		booking.MemberId = reqDto.MemberId
	}
	if reqDto.MemberCardId != nil {
		booking.MemberCardId = reqDto.MemberCardId
	}
	if reqDto.MemberCardNumber != nil {
		booking.MemberCardNumber = reqDto.MemberCardNumber
	}
	if reqDto.CustomerSource != nil {
		booking.CustomerSource = reqDto.CustomerSource
	}
	if reqDto.ArrivalTime != nil {
		booking.ArrivalTime = reqDto.ArrivalTime
	}
	if reqDto.OpenTablePlan != nil {
		booking.OpenTablePlan = reqDto.OpenTablePlan
	}
	if reqDto.RoomId != nil {
		booking.RoomId = reqDto.RoomId
	}
	if reqDto.RoomName != nil {
		booking.RoomName = reqDto.RoomName
	}
	if reqDto.BookedBy != nil {
		booking.BookedBy = reqDto.BookedBy
	}
	if reqDto.BookedByName != nil {
		booking.BookedByName = reqDto.BookedByName
	}
	if reqDto.Remark != nil {
		booking.Remark = reqDto.Remark
	}
	if reqDto.RoomId != nil {
		room, err := roomService.FindRoomById(ctx, *reqDto.RoomId)
		if err != nil {
			util.Wlog(ctx).Error("查询房间信息失败:" + err.Error())
		} else {
			booking.RoomName = room.Name
		}
	}

	err = bookingService.CreateBooking(ctx, &booking)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	notificationService.SendRoomStatusChangedMessage(ctx, *booking.VenueId)
	Result_success[any](ctx, bookingTransfer.PoToVo(booking))
}

// @Summary 更新预订
// @Description 更新预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.UpdateBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.BookingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/update [post]
func (controller *BookingController) UpdateBooking(ctx *gin.Context) {
	reqDto := req.UpdateBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}
	booking, err := bookingService.FindBookingById(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.VenueId != nil {
		booking.VenueId = reqDto.VenueId
	}
	if reqDto.CustomerName != nil {
		booking.CustomerName = reqDto.CustomerName
	}
	if reqDto.Gender != nil {
		booking.Gender = reqDto.Gender
	}
	if reqDto.CustomerPhone != nil {
		booking.CustomerPhone = reqDto.CustomerPhone
	}
	if reqDto.MemberId != nil {
		booking.MemberId = reqDto.MemberId
	}
	if reqDto.MemberCardId != nil {
		booking.MemberCardId = reqDto.MemberCardId
	}
	if reqDto.MemberCardNumber != nil {
		booking.MemberCardNumber = reqDto.MemberCardNumber
	}
	if reqDto.CustomerSource != nil {
		booking.CustomerSource = reqDto.CustomerSource
	}
	if reqDto.ArrivalTime != nil {
		booking.ArrivalTime = reqDto.ArrivalTime
	}
	if reqDto.OpenTablePlan != nil {
		booking.OpenTablePlan = reqDto.OpenTablePlan
	}
	if reqDto.RoomId != nil {
		booking.RoomId = reqDto.RoomId
	}
	if reqDto.RoomName != nil {
		booking.RoomName = reqDto.RoomName
	}
	if reqDto.BookedBy != nil {
		booking.BookedBy = reqDto.BookedBy
	}
	if reqDto.BookedByName != nil {
		booking.BookedByName = reqDto.BookedByName
	}
	if reqDto.Remark != nil {
		booking.Remark = reqDto.Remark
	}
	booking.Status = new(int)
	*booking.Status = 0
	err = bookingService.UpdateBooking(ctx, booking)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, bookingTransfer.PoToVo(*booking))
}

// @Summary 删除预订
// @Description 删除预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.DeleteBookingReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/delete [post]
func (controller *BookingController) DeleteBooking(ctx *gin.Context) {
	reqDto := req.DeleteBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	err = bookingService.DeleteBooking(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

// @Summary 查询预订
// @Description 查询预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.QueryBookingReqDto true "请求体"
// @Success 200 {object} Result[[]vo.BookingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/query [post]
func (controller *BookingController) QueryBookings(ctx *gin.Context) {
	reqDto := req.QueryBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	list, err := bookingService.FindAllBooking(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	newList := []vo.BookingVO{}
	for _, v := range *list {
		newList = append(newList, bookingTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &newList)
}

// @Summary 查询预订列表
// @Description 查询预订列表
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.QueryBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.BookingVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/list [post]
func (a *BookingController) ListBookings(ctx *gin.Context) {
	reqDto := req.QueryBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	reqDto.OrderBy = new(string)
	*reqDto.OrderBy = "arrival_time desc"
	list, err := bookingService.FindAllBooking(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	newList := []vo.BookingVO{}
	for _, v := range *list {
		newList = append(newList, bookingTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &newList)
}

// @Summary 搜索预订
// @Description 搜索预订
// @Tags 预订	
// @Accept json
// @Produce json
// @Param body body req.SearchBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.BookingVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/ls [post]
func (a *BookingController) LsBookings2(ctx *gin.Context) {
	reqDto := req.QueryBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	if reqDto.CustomerPhoneOrName != nil {
		pageNum := reqDto.PageNum
		pageSize := reqDto.PageSize
		venueId := *reqDto.VenueId
		customerPhoneOrName := *reqDto.CustomerPhoneOrName
		reqDto = req.QueryBookingReqDto{VenueId: &venueId, CustomerPhoneOrName: &customerPhoneOrName, PageNum: pageNum, PageSize: pageSize}
	}
	list, totalCount, err := bookingService.FindAllBookingWithPagination(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	page := vo.PageVO[[]vo.BookingVO]{}
	page.PageNum = *reqDto.PageNum
	page.PageSize = *reqDto.PageSize
	page.Total = totalCount
	page.Data = []vo.BookingVO{}
	for _, v := range *list {
		page.Data = append(page.Data, bookingTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &page)
}

// @Summary 取消预订
// @Description 取消预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.CancelBookingReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/cancel [post]
func (a *BookingController) CancelBooking(ctx *gin.Context) {
	reqDto := req.CancelBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	booking, err := bookingService.FindBookingById(ctx, *reqDto.BookingId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "预订不存在:"+err.Error())
		return
	}
	booking.Status = new(int)
	*booking.Status = 2
	err = bookingService.UpdateBookingPartial(ctx, booking)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "取消预订失败:"+err.Error())
		return
	}
	notificationService.SendRoomStatusChangedMessage(ctx, *booking.VenueId)
	Result_success[any](ctx, nil)
}
