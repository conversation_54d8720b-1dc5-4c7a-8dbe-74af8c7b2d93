package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/transfer"

	"github.com/gin-gonic/gin"
)

type MemberCardLevelController struct{}

var (
	memberCardLevelService      = impl.MemberCardLevelService{}
	memberCardLevelTransfer     = transfer.MemberCardLevelTransfer{}
	memberCardLevelVenueService = impl.VenueService{}
)

// @Summary 添加会员卡等级
// @Description 添加会员卡等级
// @Tags 会员卡等级
// @Accept json
// @Produce json
// @Param body body req.AddMemberCardLevelReqDto true "请求体"
// @Success 200 {object} Result[vo.MemberCardLevelVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/memberCardLevel/add [post]
func (controller *MemberCardLevelController) AddMemberCardLevel(ctx *gin.Context) {
	reqDto := req.AddMemberCardLevelReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	memberCardLevel, err := memberCardLevelService.CreateMemberCardLevel(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[vo.MemberCardLevelVO](ctx, memberCardLevelTransfer.PoToVo(*memberCardLevel))
}

// @Summary 更新会员卡等级
// @Description 更新会员卡等级
// @Tags 会员卡等级
// @Accept json
// @Produce json
// @Param body body req.UpdateMemberCardLevelReqDto true "请求体"
// @Success 200 {object} Result[vo.MemberCardLevelVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/memberCardLevel/update [post]
func (controller *MemberCardLevelController) UpdateMemberCardLevel(ctx *gin.Context) {
	reqDto := req.UpdateMemberCardLevelReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}

	memberCardLevel, err := memberCardLevelService.UpdateMemberCardLevel(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, memberCardLevelTransfer.PoToVo(*memberCardLevel))
}

// @Summary 批量更新会员卡等级
// @Description 批量更新会员卡等级，主要用于前端排序
// @Tags 会员卡等级
// @Accept json
// @Produce json
// @Param body body req.BatchUpdateMemberCardLevelReqDto true "请求体"
// @Success 200 {object} Result[[]vo.MemberCardLevelVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/memberCardLevel/batchUpdate [post]
func (controller *MemberCardLevelController) BatchUpdateMemberCardLevel(ctx *gin.Context) {
	reqDto := req.BatchUpdateMemberCardLevelReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId不能为空")
		return
	}
	if len(reqDto.Items) == 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "批量更新项不能为空")
		return
	}

	// 验证门店是否存在
	venue, err := memberCardLevelVenueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if venue == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId不存在")
		return
	}

	updatedLevels, err := memberCardLevelService.BatchUpdateMemberCardLevel(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 转换为VO
	newList := []vo.MemberCardLevelVO{}
	for _, v := range updatedLevels {
		newList = append(newList, memberCardLevelTransfer.PoToVo(*v))
	}

	Result_success[any](ctx, &newList)
}

// @Summary 删除会员卡等级
// @Description 删除会员卡等级
// @Tags 会员卡等级
// @Accept json
// @Produce json
// @Param body body req.DeleteMemberCardLevelReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/memberCardLevel/delete [post]
func (controller *MemberCardLevelController) DeleteMemberCardLevel(ctx *gin.Context) {
	reqDto := req.DeleteMemberCardLevelReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	err = memberCardLevelService.DeleteMemberCardLevel(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

// @Summary 查询会员卡等级列表-所有
// @Description 查询会员卡等级列表-所有
// @Tags 会员卡等级
// @Accept json
// @Produce json
// @Success 200 {object} Result[[]vo.MemberCardLevelVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/memberCardLevel/list [post]
func (controller *MemberCardLevelController) QueryMemberCardLevels(ctx *gin.Context) {
	reqDto := req.QueryMemberCardLevelReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId不能为空")
		return
	}
	venue, err := memberCardLevelVenueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if venue == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "venueId不存在")
		return
	}

	list, err := memberCardLevelService.GetAllMemberCardLevels(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	newList := []vo.MemberCardLevelVO{}
	for _, v := range list {
		newList = append(newList, memberCardLevelTransfer.PoToVo(*v))
	}

	Result_success[any](ctx, &newList)
}
