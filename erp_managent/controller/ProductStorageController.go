package controller

import (
	"fmt"
	"log"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type ProductStorageController struct{}

var (
	productStorageService          = impl.ProductStorageService{}
	productStorageTransfer         = transfer.ProductStorageTransfer{}
	productStorageOrderService     = impl.NewProductStorageOrderService()
	productStorageOperationService = impl.NewProductStorageOperationLogService()
	productStorageOperationManager = impl.NewProductStorageOperationService()
)

// @Summary 添加商品存储
// @Description 添加商品存储记录（支持单个商品或多个商品），返回统一的订单信息结构，包含完整的订单详情和商品项列表
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.UnifiedProductStorageReqDto true "请求体，支持单商品模式或多商品列表模式"
// @Success 200 {object} Result[vo.ProductStorageAddResultVO] "添加成功返回统一结构，包含订单信息和状态"
// @Failure 400 {object} Result[any] "参数错误，如必须字段缺失或格式不正确"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/add [post]
func (controller *ProductStorageController) AddProductStorage(ctx *gin.Context) {
	// 解析请求参数
	var reqDto req.UnifiedProductStorageReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "请求参数校验失败:"+err.Error())
		return
	}

	// 校验参数
	if reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "请选择场所")
		return
	}
	if len(reqDto.Items) == 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "请选择商品")
		return
	}

	var reqItems []req.AddProductStorageItem
	// 遍历所有存酒item,构造AddProductStorageItem
	for _, item := range reqDto.Items {
		if item.ProductId == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, "请选择商品")
			return
		}
		if item.Quantity <= 0 {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("商品:%s 存酒数量必须大于0", item.ProductName))
			return
		}
		// 如果未设置ExpireTime，保持为0，让服务层根据门店配置自动计算
		storageItem := req.AddProductStorageItem{
			ProductId:       item.ProductId,
			ProductName:     item.ProductName,
			ProductType:     item.ProductType,
			ProductUnit:     item.ProductUnit,
			ProductSpec:     item.ProductSpec,
			Quantity:        item.Quantity,
			StorageLocation: item.StorageLocation,
			ExpireTime:      0, // 忽略前端传入的过期时间，由服务层自动计算
			Remark:          item.Remark,
			StorageRoomId:   item.StorageRoomId,
			OfflineOnly:     item.OfflineOnly,
		}
		reqItems = append(reqItems, storageItem)
	}

	// 获取会员卡号（优先使用memberCardNumber，如果为空则使用memberCardId）
	memberCardNo := reqDto.MemberCardNumber
	if memberCardNo == "" && reqDto.MemberCardId != "" {
		memberCardNo = reqDto.MemberCardId
	}

	// 创建订单请求
	orderReqDto := &req.AddProductStorageOrderReqDto{
		VenueId:          reqDto.VenueId,
		CustomerId:       reqDto.CustomerId,
		CustomerName:     reqDto.CustomerName,
		PhoneNumber:      reqDto.PhoneNumber,
		MemberCardNumber: reqDto.MemberCardNumber,
		MemberCardId:     reqDto.MemberCardId,
		StorageTime:      reqDto.StorageTime,
		StorageRoomId:    reqDto.StorageRoomId,
		StorageRoomName:  reqDto.StorageRoomName,
		OfflineOnly:      reqDto.OfflineOnly,
		Remark:           reqDto.Remark,
		OperatorId:       reqDto.OperatorId,
		OperatorName:     reqDto.OperatorName,
		Items:            reqItems,
	}

	// 基本验证
	if orderReqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "场馆ID不能为空")
		return
	}

	if len(orderReqDto.Items) == 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "存酒明细不能为空")
		return
	}

	// 检查每个商品项
	for i, item := range orderReqDto.Items {
		if item.ProductId == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("第%d项商品ID不能为空", i+1))
			return
		}
		if item.ProductName == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("第%d项商品名称不能为空", i+1))
			return
		}
		if item.ProductUnit == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("第%d项商品单位不能为空", i+1))
			return
		}
		if item.Quantity <= 0 {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, fmt.Sprintf("第%d项商品数量必须大于0", i+1))
			return
		}
	}

	// 生成客户ID（如果未提供）
	if orderReqDto.CustomerId == "" {
		if orderReqDto.PhoneNumber != "" {
			orderReqDto.CustomerId = "C_" + orderReqDto.PhoneNumber
		} else {
			// 生成一个随机ID
			orderReqDto.CustomerId = "C_" + fmt.Sprintf("%d", time.Now().UnixNano())
		}
	}

	// 创建存酒单
	result, err := productStorageOrderService.CreateProductStorageOrder(ctx, orderReqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 确定返回类型
	resultType := "single"
	if len(orderReqDto.Items) > 1 {
		resultType = "multiple"
	}

	// 构建返回结果
	addResult := vo.ProductStorageAddResultVO{
		Success:   true,
		Type:      resultType,
		OrderInfo: result,
	}

	// 单个商品时设置StorageInfo以兼容旧逻辑
	if len(result.Items) == 1 {
		storage := &po.ProductStorage{
			Id:              util.GetItPtr(result.Items[0].Id),
			OrderNo:         util.GetItPtr(result.Items[0].OrderNo),
			ParentOrderNo:   util.GetItPtr(result.OrderNo),
			VenueId:         util.GetItPtr(result.VenueId),
			CustomerId:      util.GetItPtr(result.CustomerId),
			CustomerName:    util.GetItPtr(result.CustomerName),
			PhoneNumber:     util.GetItPtr(result.PhoneNumber),
			ProductId:       util.GetItPtr(result.Items[0].ProductId),
			ProductName:     util.GetItPtr(result.Items[0].ProductName),
			ProductType:     util.GetItPtr(result.Items[0].ProductType),
			ProductUnit:     util.GetItPtr(result.Items[0].ProductUnit),
			ProductSpec:     util.GetItPtr(result.Items[0].ProductSpec),
			Quantity:        util.GetItPtr(result.Items[0].Quantity),
			RemainingQty:    util.GetItPtr(result.Items[0].RemainingQty),
			StorageLocation: util.GetItPtr(result.Items[0].StorageLocation),
			StorageTime:     util.GetItPtr(result.StorageTime),
			ExpireTime:      util.GetItPtr(result.Items[0].ExpireTime),
			Remark:          util.GetItPtr(result.Items[0].Remark),
			OperatorId:      util.GetItPtr(result.OperatorId),
			OperatorName:    util.GetItPtr(result.OperatorName),
			Ctime:           util.GetItPtr(result.Ctime),
			Utime:           util.GetItPtr(result.Utime),
			State:           util.GetItPtr(result.State),
			Version:         util.GetItPtr(result.Version),
		}
		addResult.StorageInfo = productStorageTransfer.PoToVo(*storage)
	}

	Result_success[any](ctx, addResult)
}

// @Summary 查询商品存储
// @Description 查询商品存储记录，支持多种条件搜索和分页查询
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.QueryProductStorageReqDto true "请求体，支持多种查询条件（ID、订单号、场馆ID、客户信息、商品信息等）和分页参数(pageNum, pageSize)"
// @Success 200 {object} Result[vo.PageVO[[]vo.ProductStorageVO]] "成功，返回分页结果，包含总数和商品存储列表"
// @Failure 400 {object} Result[any] "参数错误，如分页参数不合法"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/query [post]
func (controller *ProductStorageController) QueryProductStorages(ctx *gin.Context) {
	reqDto := req.QueryProductStorageReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 转换DTO为指针类型
	dtoPtr := &req.QueryProductStorageReqDto{
		Id:               reqDto.Id,
		OrderNo:          reqDto.OrderNo,
		ParentOrderNo:    reqDto.ParentOrderNo,
		VenueId:          reqDto.VenueId,
		CustomerId:       reqDto.CustomerId,
		CustomerName:     reqDto.CustomerName,
		PhoneNumber:      reqDto.PhoneNumber, // 确保手机号参数传递
		ProductId:        reqDto.ProductId,
		ProductName:      reqDto.ProductName,
		ProductType:      reqDto.ProductType, // 添加商品类型参数传递
		ProductUnit:      reqDto.ProductUnit,
		ProductSpec:      reqDto.ProductSpec,
		StorageLocation:  reqDto.StorageLocation,
		OperatorId:       reqDto.OperatorId,   // 添加操作员ID
		OperatorName:     reqDto.OperatorName, // 添加操作员姓名
		StorageTimeStart: reqDto.StorageTimeStart,
		StorageTimeEnd:   reqDto.StorageTimeEnd,
		ExpireTimeStart:  reqDto.ExpireTimeStart,
		ExpireTimeEnd:    reqDto.ExpireTimeEnd,
		SearchText:       reqDto.SearchText,
		OnlyRemaining:    reqDto.OnlyRemaining,
		RoomId:           reqDto.RoomId,
		StorageRoomId:    reqDto.StorageRoomId,
		OfflineOnly:      reqDto.OfflineOnly,
		PageNum:          reqDto.PageNum,
		PageSize:         reqDto.PageSize,
	}

	// 设置默认的分页参数
	if dtoPtr.PageNum <= 0 {
		dtoPtr.PageNum = 1
	}
	if dtoPtr.PageSize <= 0 {
		dtoPtr.PageSize = 200
	}

	// 获取存酒记录列表及关联订单信息
	list, orderMap, err := productStorageService.FindAllProductStorageWithOrder(ctx, dtoPtr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 转换为VO对象
	voList := []vo.ProductStorageVO{}
	for _, v := range list {
		// 查找关联的订单信息
		var order *po.ProductStorageOrder
		if v.ParentOrderNo != nil && *v.ParentOrderNo != "" {
			order = orderMap[*v.ParentOrderNo]
		}

		// 使用带订单信息的转换方法
		voList = append(voList, productStorageTransfer.PoToVoWithOrder(*v, order))
	}

	// 获取总数以支持分页
	total, err := productStorageService.CountProductStorage(ctx, dtoPtr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用统一的PageVO结构返回结果
	page := vo.PageVO[[]vo.ProductStorageVO]{}
	page.PageNum = dtoPtr.PageNum
	page.PageSize = dtoPtr.PageSize
	page.Total = total
	page.Data = voList

	Result_success[any](ctx, page)
}

// @Summary 获取存酒明细详情
// @Description 获取存酒明细详情，包含基本信息（客户信息、会员卡号、存储位置、存储房间等）和完整的操作历史记录，如无历史记录会生成一条存入记录
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param id path string true "商品存储ID（明细ID）"
// @Success 200 {object} Result[vo.ProductStorageDetailWithHistoryVO] "成功返回存酒详情与完整的操作历史记录，包括客户信息、会员卡号、存储房间、商品信息、存入、取酒、报废等操作"
// @Failure 400 {object} Result[any] "参数错误，如ID不存在或为空"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/detail/{id} [get]
func (controller *ProductStorageController) GetProductStorageDetail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}

	// 获取存酒基本信息
	storage, err := productStorageService.FindProductStorageById(ctx, id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if storage == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "商品存储记录不存在")
		return
	}

	// 获取关联的存酒订单信息（用于获取OfflineOnly字段）
	var order *po.ProductStorageOrder
	if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
		order, err = productStorageOrderService.GetOrderByOrderNo(ctx, *storage.ParentOrderNo)
		if err != nil {
			// 记录错误但不中断流程
			log.Printf("查询存酒订单失败: %v", err)
		}
	}

	// 转换基本信息为VO，包含OfflineOnly信息
	baseInfo := productStorageTransfer.PoToVoWithOrder(*storage, order)

	// 创建结果对象
	result := vo.ProductStorageDetailWithHistoryVO{
		ProductStorageVO: baseInfo,
		OperationHistory: []vo.ProductStorageOperationVO{},
	}

	// 查询操作历史记录
	logs, err := productStorageOperationService.FindByStorageId(ctx, id)

	// 使用lo库确保logs不为nil
	safeLogs := lo.If(err != nil, []*po.ProductStorageOperationLog{}).Else(logs)

	if err != nil {
		// 操作历史查询出错不影响基本信息返回，仅记录错误
		log.Printf("查询存酒操作历史失败: %v", err)
	}

	if len(safeLogs) > 0 {
		// 遍历操作历史记录，转换为VO
		for _, log := range safeLogs {
			opVo := vo.ProductStorageOperationVO{
				Type:         *log.OperationType,
				TypeName:     *log.OperationName,
				Time:         *log.OperationTime,
				Quantity:     *log.Quantity,
				OperatorId:   *log.OperatorId,
				OperatorName: *log.OperatorName,
				BalanceQty:   *log.BalanceQty,
				Remark:       *log.Remark,
			}

			// 添加包厢信息（仅对取酒操作）
			// 无论是否有值都设置字段，确保前端能接收到完整的字段结构
			if log.RoomName != nil {
				opVo.RoomName = *log.RoomName
			} else {
				opVo.RoomName = ""
			}
			if log.DeliveryRoomName != nil {
				opVo.DeliveryRoomName = *log.DeliveryRoomName
			} else {
				opVo.DeliveryRoomName = ""
			}

			result.OperationHistory = append(result.OperationHistory, opVo)
		}
	} else {
		// 如果没有查询到操作历史，至少添加一条存入记录
		// 优先使用最后操作时间，如果为0则使用存酒时间，都为0则使用当前时间
		operationTime := baseInfo.StorageTime
		if baseInfo.LastOperationTime > 0 {
			operationTime = baseInfo.LastOperationTime
		} else if operationTime == 0 {
			operationTime = time.Now().Unix()
		}

		result.OperationHistory = append(result.OperationHistory, vo.ProductStorageOperationVO{
			Type:         "storage",
			TypeName:     "存酒",
			Time:         operationTime,
			Quantity:     baseInfo.Quantity,
			OperatorId:   baseInfo.OperatorId,
			OperatorName: baseInfo.OperatorName,
			BalanceQty:   baseInfo.Quantity,
			Remark:       baseInfo.Remark,
		})
	}

	Result_success[any](ctx, result)
}

// @Summary 获取存酒单详情
// @Description 根据单号获取存酒单详情，包含主单信息（客户信息、总数量、处理人等）及所有关联商品明细列表
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param orderNo path string true "存酒单号（主订单号）"
// @Success 200 {object} Result[vo.ProductStorageOrderWithItemsVO] "成功返回完整的存酒单及所有商品明细信息"
// @Failure 400 {object} Result[any] "参数错误，如单号不存在或为空"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/order/{orderNo} [get]
func (controller *ProductStorageController) GetProductStorageOrder(ctx *gin.Context) {
	orderNo := ctx.Param("orderNo")
	if orderNo == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "orderNo不能为空")
		return
	}

	// 获取存酒主单
	order, err := productStorageOrderService.GetOrderByOrderNo(ctx, orderNo)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if order == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "存酒单不存在")
		return
	}

	// 获取所有关联商品明细
	items, err := productStorageService.FindByParentOrderNo(ctx, orderNo)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 组装结果
	var totalItemsInt64 *int64
	var totalQuantityInt64 *int64
	if order.TotalItems != nil {
		val := int64(*order.TotalItems)
		totalItemsInt64 = &val
	}
	if order.TotalQuantity != nil {
		val := int64(*order.TotalQuantity)
		totalQuantityInt64 = &val
	}

	result := vo.ProductStorageOrderWithItemsVO{
		OrderNo:       order.OrderNo,
		CustomerInfo:  vo.CustomerInfoVO{},
		StorageTime:   order.StorageTime,
		TotalItems:    totalItemsInt64,
		TotalQuantity: totalQuantityInt64,
		OperatorId:    order.OperatorId,
		OperatorName:  order.OperatorName,
		Remark:        order.Remark,
		Items:         []vo.ProductStorageVO{},
	}

	// 设置客户信息
	if order.CustomerId != nil && *order.CustomerId != "" {
		result.CustomerInfo.CustomerId = *order.CustomerId
	}
	if order.CustomerName != nil && *order.CustomerName != "" {
		result.CustomerInfo.CustomerName = *order.CustomerName
	}
	if order.PhoneNumber != nil && *order.PhoneNumber != "" {
		result.CustomerInfo.PhoneNumber = *order.PhoneNumber
	}
	if order.MemberCardNo != nil && *order.MemberCardNo != "" {
		result.CustomerInfo.MemberCardId = *order.MemberCardNo
	}

	// 转换商品明细
	for _, item := range items {
		result.Items = append(result.Items, productStorageTransfer.PoToVo(*item))
	}

	Result_success[any](ctx, result)
}

// @Summary 获取存酒统计信息
// @Description 获取指定门店的存酒统计数据，支持两种统计类型：汇总统计(summary)和按商品维度统计(byProduct，默认)
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.GetStorageStatisticsReqDto true "请求体，必须包含venueId和统计类型statType(summary或byProduct)"
// @Success 200 {object} Result[vo.StorageStatisticsVO] "汇总统计(statType=summary)返回，包含总客户数、总商品种类数和总存酒数量"
// @Success 200 {object} Result[vo.PageVO[[]vo.ProductStorageItemStatVO]] "按商品维度统计(statType=byProduct)返回，包含按商品分组的统计列表"
// @Failure 400 {object} Result[any] "参数错误，如venueId缺失或为空"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/statistics [post]
func (controller *ProductStorageController) GetStorageStatistics(ctx *gin.Context) {
	reqDto := req.GetStorageStatisticsReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 验证必要参数
	if reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "场馆ID不能为空")
		return
	}

	// 设置默认的统计类型为按商品统计
	if reqDto.StatType == "" {
		reqDto.StatType = "byProduct"
	}

	// 设置默认的分页参数
	if reqDto.PageNum <= 0 {
		reqDto.PageNum = 1
	}
	if reqDto.PageSize <= 0 {
		reqDto.PageSize = 200
	}

	// 根据统计类型调用不同的统计方法
	if reqDto.StatType == "byProduct" {
		// 按商品维度统计
		result, err := productStorageService.GetProductStorageStatistics(ctx, &reqDto)
		if err != nil {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
			return
		}

		// 使用统一的PageVO结构返回结果
		page := vo.PageVO[[]vo.ProductStorageItemStatVO]{}
		page.PageNum = reqDto.PageNum
		page.PageSize = reqDto.PageSize
		page.Total = result.Total
		page.Data = result.List

		// 附加统计汇总数据到响应头
		ctx.Header("X-Total-Storage-Quantity", fmt.Sprint(result.TotalStorageQuantity))
		ctx.Header("X-Total-Current-Quantity", fmt.Sprint(result.TotalCurrentQuantity))
		ctx.Header("X-Total-Remaining-Quantity", fmt.Sprint(result.TotalRemainingQuantity))

		Result_success[any](ctx, page)
	} else {
		// 汇总统计
		statistics, err := productStorageService.GetStorageStatistics(ctx, &reqDto)
		if err != nil {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
			return
		}
		Result_success[any](ctx, statistics)
	}
}

// @Summary 存酒记录操作
// @Description 存酒记录操作：支持续存(extend)、报废(discard)、撤销(cancel)、更新(update)、添加商品(addItems)等多种操作类型
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.OperateProductStorageReqDto true "请求体，必须包含id和operationType字段，不同操作类型需要的其他字段不同"
// @Success 200 {object} Result[vo.ProductStorageVO] "单条存酒记录操作(续存/报废/撤销/更新)成功返回更新后的记录"
// @Success 200 {object} Result[[]vo.ProductStorageVO] "添加商品(addItems)操作成功返回新增的多条存酒记录"
// @Failure 400 {object} Result[any] "参数错误，如id不存在、操作类型无效或缺少操作所需参数"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/operate [post]
func (controller *ProductStorageController) OperateProductStorage(ctx *gin.Context) {
	reqDto := req.OperateProductStorageReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 检查必要参数
	if reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}

	if reqDto.OperationType == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "操作类型不能为空")
		return
	}

	// 特殊处理添加商品操作
	if reqDto.OperationType == "addItems" {
		if reqDto.OrderNo == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, "存酒单号不能为空")
			return
		}
		if len(reqDto.Items) == 0 {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, "存酒明细不能为空")
			return
		}

		// 向已有存酒单添加商品
		result, err := productStorageOrderService.AddItemsToOrder(ctx, reqDto.OrderNo, reqDto.Items, reqDto.OperatorId, reqDto.OperatorName, reqDto.Remark)
		if err != nil {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
			return
		}

		Result_success[any](ctx, result)
		return
	}

	// 获取存酒记录
	storage, err := productStorageService.FindProductStorageById(ctx, reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if storage == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "商品存储记录不存在")
		return
	}

	// 使用ProductStorageOperationService处理操作
	err = productStorageOperationManager.OperateProductStorage(ctx, storage, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, productStorageTransfer.PoToVo(*storage))
}

// @Summary 查询会员存酒记录
// @Description 基于会员卡ID查询会员的存酒记录，支持商品名称、时间范围、状态等多种查询条件，支持排序和分页
// @Description 查询条件：商品名称(模糊)、商品类型、存放位置、时间范围、状态过滤等
// @Description 排序支持：按存入时间、到期时间、商品名称排序，升序/降序
// @Description 分页：支持pageNum(页码)和pageSize(每页大小)参数
// @Tags 会员存酒
// @Accept json
// @Produce json
// @Param body body req.QueryMemberStorageReqDto true "会员存酒查询参数"
// @Success 200 {object} Result[vo.MemberStorageQueryResultVO] "查询成功，返回会员存酒记录列表、分页信息、会员基本信息和汇总统计"
// @Failure 400 {object} Result[any] "参数错误：门店ID为空或会员卡信息缺失"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/product-storage/member/query [post]
func (controller *ProductStorageController) QueryMemberStorage(ctx *gin.Context) {
	var reqDto req.QueryMemberStorageReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "请求参数校验失败: "+err.Error())
		return
	}

	// 参数校验
	if reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	if reqDto.MemberCardId == "" && reqDto.MemberCardNumber == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "会员卡ID或会员卡号至少提供一个")
		return
	}

	// 调用服务方法
	result, err := productStorageService.QueryMemberStorageRecords(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询会员存酒记录失败: "+err.Error())
		return
	}

	Result_success[any](ctx, result)
}
