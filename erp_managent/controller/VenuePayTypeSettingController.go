package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/application"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/transfer"

	"github.com/gin-gonic/gin"
)

type VenuePayTypeSettingController struct{}

var (
	venuePayTypeSettingAppService = application.NewVenuePayTypeSettingAppService()
	venuePayTypeSettingService    = impl.VenuePayTypeSettingService{}
	venuePayTypeSettingTransfer   = transfer.VenuePayTypeSettingTransfer{}
)

// @Summary 添加门店支付设置
// @Description 添加门店支付设置
// @Tags 门店支付设置
// @Accept json
// @Produce json
// @Param body body req.AddVenuePayTypeSettingReqDto true "请求体"
// @Success 200 {object} Result[vo.VenuePayTypeSettingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/venue-pay-type-setting/add [post]
func (controller *VenuePayTypeSettingController) AddVenuePayTypeSetting(ctx *gin.Context) {
	reqDto := req.AddVenuePayTypeSettingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	result, err := venuePayTypeSettingAppService.AddVenuePayTypeSetting(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, result)
}

// @Summary 更新门店支付设置
// @Description 更新门店支付设置
// @Tags 门店支付设置
// @Accept json
// @Produce json
// @Param body body req.UpdateVenuePayTypeSettingReqDto true "请求体"
// @Success 200 {object} Result[vo.VenuePayTypeSettingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/venue-pay-type-setting/update [post]
func (controller *VenuePayTypeSettingController) UpdateVenuePayTypeSetting(ctx *gin.Context) {
	reqDto := req.UpdateVenuePayTypeSettingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	result, err := venuePayTypeSettingAppService.UpdateVenuePayTypeSetting(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, result)
}

// @Summary 删除门店支付设置
// @Description 删除门店支付设置
// @Tags 门店支付设置
// @Accept json
// @Produce json
// @Param body body req.DeleteVenuePayTypeSettingReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/venue-pay-type-setting/delete [post]
func (controller *VenuePayTypeSettingController) DeleteVenuePayTypeSetting(ctx *gin.Context) {
	reqDto := req.DeleteVenuePayTypeSettingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	err = venuePayTypeSettingAppService.DeleteVenuePayTypeSetting(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

// @Summary 查询门店支付设置列表-mini
// @Description 查询门店支付设置列表-mini
// @Tags 门店支付设置
// @Accept json
// @Produce json
// @Param body body req.QueryVenuePayTypeSettingReqDto true "请求体"
// @Success 200 {object} vo.VenuePayTypeSettingVO "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/venue-pay-type-setting/findone [post]
func (a *VenuePayTypeSettingController) FindOneVenuePayTypeSetting(ctx *gin.Context) {
	reqDto := req.QueryVenuePayTypeSettingFindReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	result, err := venuePayTypeSettingAppService.FindOneVenuePayTypeSetting(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, result)
}
