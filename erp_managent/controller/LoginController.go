package controller

import (
	"errors"
	"fmt"
	"strings"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/rule/core"
	"voderpltvv/rule/groups"
	"voderpltvv/rule/validator"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var (
	cashierNotificationService = impl.CashierNotificationService{}
)

type LoginController struct {
	ruleEngine      *core.RuleEngine
	relationService *impl.UserVenueRelationService
	tokenService    *impl.TokenService
}

func NewLoginController() *LoginController {
	// 创建规则引擎
	engine := core.NewRuleEngine()
	if engine == nil {
		panic("failed to create rule engine")
	}

	// 创建所需服务
	erpUserAndEmployeeService := impl.NewERPUserAndEmployeeService()
	employeeService := impl.NewEmployeeService()
	venueService := impl.NewVenueService()

	// 创建关系服务
	relationService := impl.NewUserVenueRelationService(
		erpUserAndEmployeeService,
		employeeService,
		venueService,
	)
	if relationService == nil {
		panic("failed to create relation service")
	}

	// 注册规则组
	engine.RegisterGroup(groups.NewUserVenueRelationGroup(relationService))
	engine.RegisterGroup(groups.NewLoginRuleGroup(relationService))
	engine.RegisterGroup(groups.NewLoginParamGroup())
	engine.RegisterGroup(groups.NewQRLoginParamGroup())
	engine.RegisterGroup(groups.NewQRGenerateParamGroup())
	engine.RegisterGroup(groups.NewPhonePasswordLoginParamGroup())

	// 初始化TokenService
	tokenService := impl.NewTokenService(model.Redisdb)

	return &LoginController{
		ruleEngine:      engine,
		relationService: relationService,
		tokenService:    tokenService,
	}
}

// @Summary 小程序扫码
// @Description 小程序扫码
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.MiniAppLoginReqDto true "请求体"
// @Success 200 {object} Result[vo.LoginMinAppResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/miniapp/login [post]
func (controller *LoginController) MiniAppLogin(ctx *gin.Context) {
	reqDto := req.QueryMiniAppLoginReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	code := reqDto.Code
	if code == nil || *code == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "code不能为空")
		return
	}
	// 调用 小程序登录接口拿到unionid
	params := map[string]string{
		"appid":      _const.MiniAppNew["appid"],
		"secret":     _const.MiniAppNew["appsecret"],
		"js_code":    *code,
		"grant_type": "authorization_code",
	}
	util.Wlog(ctx).Infof("miniapp login params:[%v]", params)
	resp, err := model.GetWithArgs(ctx, MiniAppLoginUrl, params)
	if err != nil {
		util.Wlog(ctx).Errorf("miniapp login get err:[%s]", err.Error())
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	var respTmpl WxMiniAppLoginResp
	err = resp.JSON(&respTmpl)
	if err != nil {
		util.Wlog(ctx).Errorf("miniapp login json err:[%s]", err.Error())
	}
	util.Wlog(ctx).Infof("miniapp login resp:[%s]", util.GetPrettyJson(respTmpl))

	// 生成初始 token，不包含 venueId
	newToken, err := controller.tokenService.GenerateInitialToken(ctx, respTmpl.Unionid, _const.LOGIN_CLIENT_TYPE_MINIAPP)
	if err != nil {
		util.Wlog(ctx).Errorf("generate initial token err:[%s]", err.Error())
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "生成token失败")
		return
	}

	employees, err := employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Unionid: &respTmpl.Unionid,
	})
	if err != nil {
		return
	}
	employeeVOs := []vo.EmployeeVO{}
	venueIds := []string{}
	for _, employee := range *employees {
		employeeVOs = append(employeeVOs, employeeTransfer.PoToVo(employee))
		if employee.VenueId != nil && *employee.VenueId != "" {
			venueIds = append(venueIds, *employee.VenueId)
		}
	}
	venues, _ := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Ids: &venueIds,
	})
	venueVOs := []vo.VenueVO{}
	for _, venue := range *venues {
		venueVOs = append(venueVOs, venueTransfer.PoToVo(venue))
	}
	vo := vo.QueryMiniAppLoginVO{
		Unionid:     respTmpl.Unionid,
		Token:       newToken,
		EmployeeVOs: &employeeVOs,
		VenueVOs:    &venueVOs,
	}
	Result_success[any](ctx, &vo)
}

func (controller *LoginController) MiniAppLoginValidate(ctx *gin.Context, reqDto *req.QueryMiniAppLoginReqDto) (*vo.LoginMinAppResultVO, error) {
	unionid := reqDto.Unionid
	if unionid == nil || *unionid == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "unionid不能为空")
		return nil, errors.New("unionid不能为空")
	}
	employees, err := employeeService.FindAllEmployee(ctx, &req.QueryEmployeeReqDto{
		Unionid: unionid,
	})
	if err != nil {
		return nil, err
	}
	employeeIds := []string{}
	for _, employee := range *employees {
		if employee.Unionid != nil && *employee.Unionid != "" {
			employeeIds = append(employeeIds, *employee.Unionid)
		}
	}
	venueAndEmployees, err := venueAndEmployeeService.FindAllVenueAndEmployee(ctx, &req.QueryVenueAndEmployeeReqDto{
		EmployeeIds: &employeeIds,
	})
	if err != nil {
		return nil, err
	}
	if len(*venueAndEmployees) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "用户未创建门店")
		return nil, errors.New("用户未创建门店")
	}
	venueAndEmployeeVOs := []vo.VenueAndEmployeeVO{}
	for _, venueAndEmployee := range *venueAndEmployees {
		venueAndEmployeeVOs = append(venueAndEmployeeVOs, venueAndEmployeeTransfer.PoToVo(venueAndEmployee))
	}
	loginMinAppResultVO := &vo.LoginMinAppResultVO{
		VenueAndEmployeeVOs: &venueAndEmployeeVOs,
	}
	return loginMinAppResultVO, nil
}

// @Summary 查询小程序门店绑定列表
// @Description 查询小程序门店绑定列表
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.QueryMiniAppVenueBindListReqDto true "请求体"
// @Success 200 {object} Result[[]vo.VenueVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/miniapp/venue/bind/list [post]
func (controller *LoginController) MiniAppVenuBindList(ctx *gin.Context) {
	reqDto := req.QueryMiniAppVenueBindListReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Phone: reqDto.Phone,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	venueVOs := []vo.VenueVO{}
	for _, venue := range *venues {
		venueVOs = append(venueVOs, venueTransfer.PoToVo(venue))
	}
	Result_success[any](ctx, &venueVOs)
}

// QRLoginResultVO 二维码登录结果
type QRLoginResultVO struct {
	QRCode   string `json:"qr_code"`   // 二维码图片
	SceneStr string `json:"scene_str"` // 场景值
	LoginKey string `json:"login_key"` // 登录密钥
}

// @Summary 获取收银台授权登录小程序二维码
// @Description 获取收银台授权登录小程序二维码
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.QueryVenueShouyinGrantLoginQRReqDto true "请求体"
// @Success 200 {object} Result[QRLoginResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/login/shouyin/qr [post]
func (controller *LoginController) GetShouyinLoginQR(ctx *gin.Context) {
	reqDto := req.QueryVenueShouyinGrantLoginQRReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用工具方法验证参数
	err = validator.ValidateAtLeastOne(ctx,
		map[string]interface{}{
			"venueId": reqDto.VenueId,
			"mac":     reqDto.Mac,
		},
		"venueId", "mac",
	)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	var venueId *string

	// 1. 优先使用传入的venueId
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		venueId = reqDto.VenueId
	} else {
		// 2. 通过mac地址查询对应的venue_id
		records, err := cashierMachineService.FindCashierMachineByMac(ctx, *reqDto.Mac)
		if err != nil || records == nil || records.VenueId == nil || *records.VenueId == "" {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "未找到该mac地址对应的门店信息")
			return
		}
		venueId = (*records).VenueId
	}

	// 4. 生成场景值（包含venueId信息）
	sceneStr := fmt.Sprintf("l_%s_%s", *venueId, util.GenerateShortKey())

	// 5. 创建二维码登录信息并存储到 Redis
	gzhQrService := &impl.GzhQrService{}
	qrInfo, err := gzhQrService.CreateQRLoginInfo(ctx, sceneStr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 6. 生成二维码
	qrResult, err := gzhQrService.GetMiniAppLoginQrCode(ctx, sceneStr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 7. 返回结果
	result := QRLoginResultVO{
		QRCode:   qrResult.Url,
		SceneStr: sceneStr,
		LoginKey: qrInfo.LoginKey, // 返回 loginKey 给客户端
	}

	Result_success[any](ctx, &result)
}

// 新：二维码登录态查询接口
// @Router /api/login/shouyin/qr/status [post]
func (controller *LoginController) CheckQRLoginStatus(ctx *gin.Context) {
	reqDto := struct {
		SceneStr string `json:"scene_str"`
		LoginKey string `json:"login_key"`
	}{}

	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	gzhQrService := &impl.GzhQrService{}
	qrInfo, err := gzhQrService.GetQRLoginInfo(ctx, reqDto.SceneStr)
	if err != nil {
		// 二维码不存在或已过期
		_ = cashierNotificationService.SendQRLoginStatus(ctx, reqDto.SceneStr, impl.QR_STATUS_EXPIRED, "", "qr_expired")
		Result_fail[any](ctx, GeneralCodes.NotFound.Code, "二维码已过期")
		return
	}

	// 验证 LoginKey
	if qrInfo.LoginKey != reqDto.LoginKey {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "无效的 LoginKey")
		return
	}

	// 如果已确认登录，返回登录信息
	if qrInfo.Status == impl.QR_STATUS_CONFIRMED && qrInfo.UserId != "" {
		// 复用 AutoLogin 的逻辑取登录信息
		loginResultVO, err := controller.handleLoginWithUserId(ctx, qrInfo.UserId, reqDto.SceneStr, "", _const.LOGIN_CLIENT_TYPE_SHOUYIN)
		if err != nil {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
			return
		}
		Result_success[any](ctx, loginResultVO)
		return
	}

	// 返回当前状态
	Result_success[any](ctx, map[string]interface{}{
		"status": qrInfo.Status,
	})
}

// @Summary 自动登录
// @Description 自动登录
// @Tags 登录
// @Accept json
// @Produce json
// @Success 200 {object} Result[vo.LoginResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/login/autologin [post]
func (controller *LoginController) AutoLogin(ctx *gin.Context) {
	// 1. 从context获取userId（中间件已验证token并设置userId）
	userId, exists := ctx.Get("userId")
	reqDto := req.MiniProgramLoginReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if !exists {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "用户未登录")
		return
	}

	// 调用共用登录逻辑
	// 这里需要检查 reqDto.SceneStr 是否为 nil
	sceneStr := ""
	if reqDto.SceneStr != nil {
		sceneStr = *reqDto.SceneStr
	}

	// 2. 调用共用登录逻辑
	loginResultVO, err := controller.handleLoginWithUserId(ctx, userId.(string), sceneStr, "", _const.LOGIN_CLIENT_TYPE_MINIAPP)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, loginResultVO)
}

// @Summary 手机号密码登录
// @Description 手机号密码登录
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.PhonePasswordLoginReqDto true "请求体"
// @Success 200 {object} Result[vo.LoginResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/login/phone [post]
func (controller *LoginController) PhonePasswordLogin(ctx *gin.Context) {
	reqDto := req.PhonePasswordLoginReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用工具方法验证参数
	err = validator.ValidateParams(ctx,
		map[string]interface{}{
			"phone":    reqDto.Phone,
			"password": reqDto.Password,
		},
		"phone", "password",
	)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 根据手机号查询ERP用户
	erpUsers, err := erpUserService.FindAllERPUser(ctx, &req.QueryERPUserReqDto{
		Phone: reqDto.Phone,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 验证用户存在和密码
	if len(*erpUsers) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "用户不存在")
		return
	}
	erpUser := (*erpUsers)[0]
	if erpUser.Password == nil || *erpUser.Password != *reqDto.Password {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "密码错误")
		return
	}

	// 调用共用登录逻辑
	sceneStr := ""
	if reqDto.SceneStr != nil {
		sceneStr = *reqDto.SceneStr
	}

	loginResultVO, err := controller.handleLoginWithUserId(ctx, *erpUser.Id, sceneStr, "", _const.LOGIN_CLIENT_TYPE_MINIAPP)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, loginResultVO)
}

// @Summary 收银端手机号密码登录
// @Description 收银端手机号密码登录
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.ShouyinPhonePasswordLoginReqDto true "请求体"
// @Success 200 {object} Result[vo.LoginResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /shouyin/api/login/phone [post]
func (controller *LoginController) ShouyinPhonePasswordLogin(ctx *gin.Context) {
	reqDto := req.ShouyinPhonePasswordLoginReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用工具方法验证参数
	err = validator.ValidateParams(ctx,
		map[string]interface{}{
			"phone":    reqDto.Phone,
			"password": reqDto.Password,
			"mac":      reqDto.Mac,
		},
		"phone", "password", "mac",
	)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 根据手机号查询ERP用户
	erpUsers, err := erpUserService.FindAllERPUser(ctx, &req.QueryERPUserReqDto{
		Phone: reqDto.Phone,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 验证用户存在和密码
	if len(*erpUsers) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "用户不存在")
		return
	}
	erpUser := (*erpUsers)[0]
	if erpUser.Password == nil || *erpUser.Password != *reqDto.Password {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "密码错误")
		return
	}

	// 根据mac地址查询收银机信息
	cashierMachine, err := cashierMachineService.FindCashierMachineByMac(ctx, *reqDto.Mac)

	if err != nil || cashierMachine == nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "未找到该mac地址对应的收银机信息")
		return
	}

	if cashierMachine.VenueId == nil || *cashierMachine.VenueId == "" || !*cashierMachine.IsBind {
		// 使用新的错误码
		Result_fail[any](ctx, GeneralCodes.CashierNotBound.Code, "该收银机未绑定门店")
		return
	}

	// 检查门店审核状态
	venue, err := venueService.FindVenueById(ctx, *cashierMachine.VenueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "获取门店信息失败")
		return
	}

	// 门店未审核通过，解绑收银机并返回错误
	if venue.AuditStatus == nil || *venue.AuditStatus != 1 {
		// 解绑收银机
		venueId := *cashierMachine.VenueId
		cashierMachine.VenueId = util.GetItPtr("")
		cashierMachine.IsBind = util.GetBoolPtr(false)

		err = cashierMachineService.UpdateCashierMachine(ctx, cashierMachine)
		if err != nil {
			util.Wlog(ctx).Errorf("解绑收银机失败: %v", err)
		} else {
			// 发送解绑通知
			if cashierMachine.Mac != nil && *cashierMachine.Mac != "" {
				_ = cashierNotificationService.SendCashierMachineUnbind(ctx, venueId, *cashierMachine.Mac)
			}
		}

		Result_fail[any](ctx, GeneralCodes.VenueNotAudited.Code, "门店未审核通过")
		return
	}

	// 查询用户与门店的关联关系
	relations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
		ERPUserId: erpUser.Id,
		VenueId:   cashierMachine.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询用户门店关系失败")
		return
	}

	// 验证用户是否在该门店工作
	if len(*relations) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "您不是该门店的员工，无法登录")
		return
	}

	// 验证员工审核状态
	relation := (*relations)[0]
	if relation.EmployeeId == nil || *relation.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工信息异常，无法登录")
		return
	}

	// 查询员工信息以验证审核状态
	employee, err := employeeService.FindEmployeeById(ctx, *relation.EmployeeId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询员工信息失败")
		return
	}
	if employee == nil {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工信息不存在")
		return
	}

	// 检查员工审核状态：1-通过，0-待审核，2-拒绝
	if employee.ReviewStatus == nil || *employee.ReviewStatus != 1 {
		var statusMsg string
		if employee.ReviewStatus == nil || *employee.ReviewStatus == 0 {
			statusMsg = "您的员工账号待审核，暂时无法登录收银机"
		} else {
			statusMsg = "您的员工账号审核未通过，无法登录收银机"
		}
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, statusMsg)
		return
	}

	// 调用共用登录逻辑
	sceneStr := ""
	if reqDto.SceneStr != nil {
		sceneStr = *reqDto.SceneStr
	}

	loginResultVO, err := controller.handleLoginWithUserId(ctx, *erpUser.Id, sceneStr, *cashierMachine.VenueId, _const.LOGIN_CLIENT_TYPE_SHOUYIN)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if loginResultVO.VenueVOs != nil && len(loginResultVO.VenueVOs) > 0 {
		venueVOs := loginResultVO.VenueVOs
		venueIds := []string{}
		for _, venueVO := range venueVOs {
			venueIds = append(venueIds, venueVO.VenueVO.Id)
		}
		venuePayTypeSettings, err := venuePayTypeSettingAppService.FindLastPayTypeSettingByIds(ctx, venueIds)
		if err != nil {
			util.Wlog(ctx).Errorf("查询门店支付类型设置失败: %v", err)
		} else {
			venueIdTOVenuePayTypeSettingMap := make(map[string]vo.VenuePayTypeSettingVO)
			for _, venueVO := range venueVOs {
				venueIdTOVenuePayTypeSettingMap[venueVO.VenueVO.Id] = *venuePayTypeSettings
			}
			newVenueVOs := []vo.LoginVenueAndEmployeeVO{}
			for _, venueVO := range venueVOs {
				settingVO := *venuePayTypeSettings
				newVenueVOs = append(newVenueVOs, vo.LoginVenueAndEmployeeVO{
					VenueVO:         venueVO.VenueVO,
					LoginEmployeeVO: venueVO.LoginEmployeeVO,
					PayTypeSetting:  &settingVO,
				})
			}
			loginResultVO.VenueVOs = newVenueVOs
		}
	}

	Result_success[any](ctx, loginResultVO)
}

// @Summary 掌柜系统手机号密码登录
// @Description 掌柜系统手机号密码登录
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.ManagerPhonePasswordLoginReqDto true "请求体"
// @Success 200 {object} Result[vo.LoginResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /manager/api/login/phone [post]
func (controller *LoginController) ManagerPhonePasswordLogin(ctx *gin.Context) {
	reqDto := req.ManagerPhonePasswordLoginReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用工具方法验证参数
	err = validator.ValidateParams(ctx,
		map[string]interface{}{
			"phone":    reqDto.Phone,
			"password": reqDto.Password,
			"venueId":  reqDto.VenueId,
		},
		"phone", "password", "venueId",
	)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 根据手机号查询ERP用户
	erpUsers, err := erpUserService.FindAllERPUser(ctx, &req.QueryERPUserReqDto{
		Phone: reqDto.Phone,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 验证用户存在和密码
	if len(*erpUsers) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "用户不存在")
		return
	}
	erpUser := (*erpUsers)[0]
	if erpUser.Password == nil || *erpUser.Password != *reqDto.Password {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "密码错误")
		return
	}

	// 检查门店审核状态
	venue, err := venueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "获取门店信息失败")
		return
	}

	// 门店未审核通过，返回错误
	if venue.AuditStatus == nil || *venue.AuditStatus != 1 {
		Result_fail[any](ctx, GeneralCodes.VenueNotAudited.Code, "门店未审核通过")
		return
	}

	// 查询用户与门店的关联关系
	relations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
		ERPUserId: erpUser.Id,
		VenueId:   reqDto.VenueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询用户门店关系失败")
		return
	}

	// 验证用户是否在该门店工作
	if len(*relations) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "您不是该门店的员工，无法登录")
		return
	}

	// 验证员工审核状态
	relation := (*relations)[0]
	if relation.EmployeeId == nil || *relation.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工信息异常，无法登录")
		return
	}

	// 查询员工信息以验证审核状态
	employee, err := employeeService.FindEmployeeById(ctx, *relation.EmployeeId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询员工信息失败")
		return
	}
	if employee == nil {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工信息不存在")
		return
	}

	// 检查员工审核状态：1-通过，0-待审核，2-拒绝
	if employee.ReviewStatus == nil || *employee.ReviewStatus != 1 {
		var statusMsg string
		if employee.ReviewStatus == nil || *employee.ReviewStatus == 0 {
			statusMsg = "您的员工账号待审核，暂时无法登录掌柜系统"
		} else {
			statusMsg = "您的员工账号审核未通过，无法登录掌柜系统"
		}
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, statusMsg)
		return
	}

	// 调用共用登录逻辑
	sceneStr := ""
	if reqDto.SceneStr != nil {
		sceneStr = *reqDto.SceneStr
	}

	loginResultVO, err := controller.handleLoginWithUserId(ctx, *erpUser.Id, sceneStr, *reqDto.VenueId, _const.LOGIN_CLIENT_TYPE_MANAGER)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, loginResultVO)
}

// 权限检查方法已移除，权限检查现在由中间件统一处理

// 从场景字符串中解析门店ID
func parseVenueIdFromLoginSceneStr(sceneStr string) (string, error) {
	// 格式为 "l_venueId_shortKey"
	parts := strings.Split(sceneStr, "_")
	if len(parts) < 3 || parts[0] != "l" {
		return "", fmt.Errorf("二维码格式错误")
	}

	venueId := parts[1]
	if venueId == "" {
		return "", fmt.Errorf("无效的门店ID")
	}

	return venueId, nil
}

// 抽取共用的登录逻辑
func (controller *LoginController) handleLoginWithUserId(ctx *gin.Context, userId string, sceneStr string, venueId string, clientType string) (*vo.LoginResultVO, error) {
	// 添加日志
	fmt.Printf("handleLoginWithUserId called with userId: %s, sceneStr: %s\n", userId, sceneStr)

	ruleCtx := &core.RuleContext{
		GinContext: ctx,
		Cache: map[string]interface{}{
			"userId": userId,
		},
	}

	// 添加日志
	fmt.Printf("RuleContext created: %+v\n", ruleCtx)

	if err := controller.ruleEngine.Validate("LOGIN_PARAM", ruleCtx); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 2. 根据userId查询ERP用户信息并生成token - 这是基础信息
	erpUsers, err := erpUserService.FindAllERPUser(ctx, &req.QueryERPUserReqDto{
		Id: &userId,
	})
	if err != nil {
		return nil, err
	}
	if len(*erpUsers) == 0 {
		return nil, errors.New("用户不存在")
	}

	// 3. 生成token
	var token string

	venueId1 := ""
	// 4. 处理二维码场景
	if sceneStr != "" {
		if strings.HasPrefix(sceneStr, "l_") {
			// 从sceneStr中解析venueId
			venueId1, err = parseVenueIdFromLoginSceneStr(sceneStr)
			if err != nil {
				return nil, fmt.Errorf("解析场景字符串失败: %v", err)
			}
		} else if strings.HasPrefix(sceneStr, "sf_") {
			// 如果是添加员工扫码登录

			// 从sceneStr中解析venueId
			parts := strings.Split(sceneStr, "_")
			if len(parts) >= 2 { // 优化：防止数组越界
				venueId := parts[1]

				// 1. 根据 userId 查询 ERP 用户信息，获取 name 和 phone
				erpUser, err := erpUserService.FindERPUserById(ctx, userId)
				if err != nil || erpUser == nil {
					return nil, fmt.Errorf("查询用户信息失败: %v", err)
				}
				employeeName := *erpUser.Name
				employeePhone := *erpUser.Phone

				// 2. 检查是否已存在关联关系
				existRelations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
					ERPUserId: &userId,
					VenueId:   &venueId,
				})
				if err != nil {
					return nil, fmt.Errorf("查询关联关系失败: %v", err)
				}
				if len(*existRelations) > 0 {
					return nil, fmt.Errorf("您已在该门店存在员工账号")
				}

				// 3. 创建员工和关联关系 (使用事务)
				var newEmployee *po.Employee
				err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
					// 创建员工
					newEmployee = &po.Employee{
						Name:          &employeeName,
						Phone:         &employeePhone,
						VenueId:       &venueId,
						EmployeeGroup: util.GetItPtr(""),    // 假设默认员工分组为空
						IsBoss:        util.GetItPtr(false), // 其他员工注册时默认不是老板
						ReviewStatus:  util.GetItPtr(0),     // 0-待审核
					}
					if err := employeeService.CreateEmployeeWithTx(ctx, newEmployee, tx); err != nil {
						return err
					}

					// 创建关联关系
					relation := &po.ERPUserAndEmployee{
						ERPUserId:  &userId,
						EmployeeId: newEmployee.Id,
						VenueId:    &venueId,
					}
					return erpUserAndEmployeeService.CreateERPUserAndEmployeeWithTx(ctx, relation, tx)
				})

				if err != nil {
					return nil, fmt.Errorf("创建员工失败: %v", err)
				}

				// 4. (可选) 发送通知, 比如审核通知
				// ...

			}
		}
	}

	// 5. 构建基础返回结果
	loginResultVO := &vo.LoginResultVO{
		ERPUserVO: erpUserTransfer.PoToVo((*erpUsers)[0]),
		VenueVOs:  []vo.LoginVenueAndEmployeeVO{}, // 默认空数组
	}
	var venues []vo.LoginVenueAndEmployeeVO
	// 根据客户端类型区分处理
	if clientType == _const.LOGIN_CLIENT_TYPE_MINIAPP {
		if venueId1 != "" {
			// 如果是微信扫码登录收银机，则需要验证用户-门店关系，验证通过，发送成功状态通知给收银机
			venueId = venueId1

			// 执行用户-门店关系验证
			existRelations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
				ERPUserId: &userId,
				VenueId:   &venueId,
			})
			if err == nil && len(*existRelations) > 0 {
				// 验证通过,发送成功状态通知
				_ = cashierNotificationService.SendQRLoginStatus(ctx, sceneStr, impl.QR_STATUS_CONFIRMED, userId, "success")
			}

		}
		// 小程序端：如果没有 venueId，生成初始 token；否则生成完整 token
		if venueId == "" {
			token, err = controller.tokenService.GenerateInitialToken(ctx, userId, clientType)
		} else {
			token, err = controller.tokenService.GenerateToken(ctx, userId, clientType, venueId)
		}
		// 6. 查询关联的venues信息
		venues, err = controller.relationService.FindUserVenueEmployees(userId)
		if err != nil {
			util.Wlog(ctx).Errorf("查询用户关联门店失败: %v", err)
			// 即使查询venues失败，也返回用户基本信息和token
			return loginResultVO, nil
		}
	} else {
		// 收银端：必须要有 venueId
		if venueId == "" {
			return nil, errors.New("收银端登录必须指定门店ID")
		}

		// 对收银端再次验证门店审核状态
		if clientType == _const.LOGIN_CLIENT_TYPE_SHOUYIN {
			venue, err := venueService.FindVenueById(ctx, venueId)
			if err != nil {
				return nil, fmt.Errorf("获取门店信息失败: %v", err)
			}

			if venue.AuditStatus == nil || *venue.AuditStatus != 1 {
				// 解绑收银机已在各个入口处理，这里只返回错误
				return nil, fmt.Errorf("门店未审核通过")
			}
		}

		// 生成完整 token
		token, err = controller.tokenService.GenerateToken(ctx, userId, clientType, venueId)

		// 执行用户-门店关系验证
		existRelations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
			ERPUserId: &userId,
			VenueId:   &venueId,
		})
		if err != nil {
			return nil, fmt.Errorf("查询关联关系失败: %v", err)
		}
		if len(*existRelations) == 0 {
			return nil, fmt.Errorf("您不是该门店的员工，无权登录")
		}

		// 权限检查已移至中间件处理

		// 6. 查询关联的venues信息
		venues, err = controller.relationService.FindUserVenueEmployeesForCashier(userId, (*existRelations)[0])
		if err != nil {
			util.Wlog(ctx).Errorf("查询用户关联门店失败: %v", err)
			// 即使查询venues失败，也返回用户基本信息和token
			return loginResultVO, nil
		}
	}
	loginResultVO.Token = token

	if err != nil {
		return nil, err
	}

	loginResultVO.VenueVOs = venues
	return loginResultVO, nil
}

// 对于 websocket，我们可以先定义一个简单的实现
// 你可以根据你的实际需求替换成你的 websocket 服务
var websocket = struct {
	BroadcastMessage func(channel string, message interface{}) error
}{
	BroadcastMessage: func(channel string, message interface{}) error {
		// TODO: 实现你的 websocket 广播逻辑
		util.Wlog(nil).Infof("WebSocket broadcast to %s: %v", channel, message)
		return nil
	},
}

// @Summary 二维码自动登录
// @Description 使用loginKey和userId进行自动登录
// @Tags 登录
// @Accept json
// @Produce json
// @Param body body req.QRAutoLoginReqDto true "请求体"
// @Success 200 {object} Result[vo.LoginResultVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/login/shouyin/qr/auto [post]
func (controller *LoginController) QRAutoLogin(ctx *gin.Context) {
	reqDto := req.QRAutoLoginReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 1. 验证二维码信息
	gzhQrService := &impl.GzhQrService{}
	qrInfo, err := gzhQrService.GetQRLoginInfo(ctx, reqDto.SceneStr)
	if err != nil {
		_ = cashierNotificationService.SendQRLoginStatus(ctx, reqDto.SceneStr, impl.QR_STATUS_EXPIRED, "", "qr_expired")
		Result_fail[any](ctx, GeneralCodes.NotFound.Code, "二维码已过期")
		return
	}

	// 2. 验证 LoginKey
	if qrInfo.LoginKey != reqDto.LoginKey {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "无效的 LoginKey")
		return
	}

	// 3. 从 sceneStr 中解析出 venueId
	venueId, err := parseVenueIdFromLoginSceneStr(qrInfo.SceneStr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 检查门店审核状态
	venue, err := venueService.FindVenueById(ctx, venueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "获取门店信息失败")
		return
	}

	// 门店未审核通过，解绑收银机并返回错误
	if venue.AuditStatus == nil || *venue.AuditStatus != 1 {
		// 查找绑定该门店的收银机
		machines, err := cashierMachineService.FindAllCashierMachine(ctx, &req.QueryCashierMachineReqDto{
			VenueId: &venueId,
		})

		if err == nil && machines != nil && len(*machines) > 0 {
			// 解绑所有绑定的收银机
			for _, machine := range *machines {
				machineCopy := machine
				machineCopy.VenueId = util.GetItPtr("")
				machineCopy.IsBind = util.GetBoolPtr(false)

				err = cashierMachineService.UpdateCashierMachine(ctx, &machineCopy)
				if err != nil {
					util.Wlog(ctx).Errorf("解绑收银机失败: %v", err)
				} else {
					// 发送解绑通知
					if machine.Mac != nil && *machine.Mac != "" {
						_ = cashierNotificationService.SendCashierMachineUnbind(ctx, venueId, *machine.Mac)
					}
				}
			}
		}

		Result_fail[any](ctx, GeneralCodes.VenueNotAudited.Code, "门店未审核通过")
		return
	}

	// 查询用户与门店的关联关系以获取员工信息
	relations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
		ERPUserId: &reqDto.UserId,
		VenueId:   &venueId,
	})
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询用户门店关系失败")
		return
	}

	// 验证用户是否在该门店工作
	if len(*relations) == 0 {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "您不是该门店的员工，无法登录")
		return
	}

	// 验证员工审核状态
	relation := (*relations)[0]
	if relation.EmployeeId == nil || *relation.EmployeeId == "" {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工信息异常，无法登录")
		return
	}

	// 查询员工信息以验证审核状态
	employee, err := employeeService.FindEmployeeById(ctx, *relation.EmployeeId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "查询员工信息失败")
		return
	}
	if employee == nil {
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "员工信息不存在")
		return
	}

	// 检查员工审核状态：1-通过，0-待审核，2-拒绝
	if employee.ReviewStatus == nil || *employee.ReviewStatus != 1 {
		var statusMsg string
		if employee.ReviewStatus == nil || *employee.ReviewStatus == 0 {
			statusMsg = "您的员工账号待审核，暂时无法登录收银机"
		} else {
			statusMsg = "您的员工账号审核未通过，无法登录收银机"
		}
		Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, statusMsg)
		return
	}

	// 4. 调用共用登录逻辑
	loginResultVO, err := controller.handleLoginWithUserId(ctx, reqDto.UserId, "", venueId, _const.LOGIN_CLIENT_TYPE_SHOUYIN)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, loginResultVO)
}

// @Summary 小程序切换门店
// @Description 小程序用户登录后，如果门店数量大于1个，则需要选择门店进入，进入后需要拿到该门店的员工id，返回门店信息和在该门店的员工信息
// @Tags 门店
// @Accept json
// @Produce json
// @Param body body req.SwitchVenueReqDto true "请求体"
// @Success 200 {object} Result[vo.SwitchVenueVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/miniapp/venue/switch [post]
func (controller *LoginController) SwitchVenue(ctx *gin.Context) {
	// 1. 参数验证
	reqDto := req.SwitchVenueReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	// 获取用户信息
	userId, exists := ctx.Get("userId")
	if !exists {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "获取用户信息失败")
		return
	}

	clientType, exists := ctx.Get("clientType")
	if !exists {
		// 默认为小程序客户端
		clientType = _const.LOGIN_CLIENT_TYPE_MINIAPP
	}

	// 生成包含 venueId 的完整 token
	newToken, err := controller.tokenService.GenerateToken(ctx, userId.(string), clientType.(string), *reqDto.VenueId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, "生成token失败")
		return
	}

	// 更新 HTTP 响应头
	ctx.Header("Authorization", "Bearer "+newToken)

	// 2. 调用应用服务
	result, err := venueAppService.SwitchVenue(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 创建包含 token 的返回结果
	switchVenueVO := vo.SwitchVenueVO{
		Venue:    result.VenueVO,
		Employee: result.LoginEmployeeVO,
		Token:    newToken,
	}

	Result_success[any](ctx, switchVenueVO)
}
