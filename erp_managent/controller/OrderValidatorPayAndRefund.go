package controller

import (
	"errors"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type OrderValidator struct {
}

func (validator *OrderValidator) ValidatePayBase(ctx *gin.Context, reqDto *req.QueryOrderPayReqDto) (session *po.Session, err error) {
	if reqDto.PayType == nil || *reqDto.PayType == "" {
		return nil, errors.New("PayType不能为空")
	}
	if !util.InList(*reqDto.PayType, _const.PAY_TYPE_SUPPORTS) {
		// return nil, errors.New("PayType不支持")
	}
	if reqDto.PayAmount == nil || *reqDto.PayAmount <= 0 {
		return nil, errors.New("PayAmount不能为空且必须大于0")
	}

	// 0. 验证基础参数 venueId,roomId,sessionId
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, errors.New("VenueId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return nil, errors.New("RoomId不能为空")
	}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return nil, errors.New("SessionId不能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return nil, errors.New("EmployeeId不能为空")
	}
	// 0.1. 检查ktv是否存在
	venue, err := venueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return nil, errors.New("门店不存在")
	}
	// 0.2. 检查房间状态
	room, err := roomService.FindRoomById(ctx, *reqDto.RoomId)
	if err != nil {
		return nil, errors.New("房间不存在")
	}
	if room.VenueId == nil || *room.VenueId != *reqDto.VenueId {
		return nil, errors.New("房间不属于该门店")
	}
	// 0.3. 检查场次是否存在
	sessions, err := sessionService.FindAllSession(ctx, &req.QuerySessionReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId})
	if err != nil || len(*sessions) == 0 {
		return nil, errors.New("场次信息不存在")
	}
	session = &((*sessions)[0])
	// 0.4. 检查员工是否存在
	employee, err := employeeService.FindEmployeeById(ctx, *reqDto.EmployeeId)
	if err != nil || employee == nil {
		return nil, errors.New("员工不存在")
	}
	return session, nil
}

func (validator *OrderValidator) ValidatePayHasDiscount(reqDto *req.QueryOrderPayReqDto) bool {
	// DiscountRoomRate 房费打折
	// DiscountProductRate 商品打折
	// ReduceRoomAmount 房费减免
	// ReduceProductAmount 商品减免
	// FreeAmount 免单

	// 如果不满足以下任一条件则直接返回:
	// 1. 房费打折率在1-99之间
	// 2. 商品打折率在1-99之间
	// 3. 房费减免金额大于0
	// 4. 商品减免金额大于0
	return (reqDto.DiscountRoomRate != nil && util.Between(*reqDto.DiscountRoomRate, 1, 99)) ||
		(reqDto.DiscountProductRate != nil && util.Between(*reqDto.DiscountProductRate, 1, 99)) ||
		(reqDto.ReduceRoomAmount != nil && *reqDto.ReduceRoomAmount > 0) ||
		(reqDto.ReduceProductAmount != nil && *reqDto.ReduceProductAmount > 0)
}

func (validator *OrderValidator) ValidatePayIsMatchPayAmountInReq(ctx *gin.Context, reqPayAmount int64, toPayUpdateOrders *[]po.Order) bool {
	// 2.1 计算需要支付的订单的总金额和入参的payAmount是否一致
	lastTotalPayAmount := int64(0)
	for _, v := range *toPayUpdateOrders {
		if v.Status != nil && *v.Status == _const.ORDER_STATUS_REFUNDED {
			// lastTotalPayAmount -= *v.PayAmount
		} else {
			// lastTotalPayAmount += *v.PayAmount
		}
	}
	return lastTotalPayAmount == reqPayAmount
}

func (validator *OrderValidator) ValidatePay(ctx *gin.Context, reqDto *req.QueryOrderPayReqDto, toPayOrderVOs []vo.OrderVO, toPayOrders []po.Order, unPayAmount int64) (totalDiscountRoomAmount int64, totalDiscountProductAmount int64, toPayUpdateOrders *[]po.Order, err error) {
	// 0.5 房费打折 1-100 %
	if reqDto.DiscountRoomRate != nil && !util.Between(*reqDto.DiscountRoomRate, 1, 100) {
		return 0, 0, nil, errors.New("房费打折[1-100]")
	}
	// 0.6 商品打折 1-100 %
	if reqDto.DiscountProductRate != nil && !util.Between(*reqDto.DiscountProductRate, 1, 100) {
		return 0, 0, nil, errors.New("商品打折[1-100]")
	}
	// 0.7 免单和折扣、减免不能同时使用
	if reqDto.FreeAmount != nil {
		if *reqDto.FreeAmount < 0 {
			return 0, 0, nil, errors.New("免单金额必须大于0")
		}
		if *reqDto.FreeAmount > *reqDto.PayAmount {
			return 0, 0, nil, errors.New("免单金额不能大于支付金额")
		}
		if *reqDto.FreeAmount > 0 {
			if reqDto.DiscountRoomRate != nil && *reqDto.DiscountRoomRate > 0 {
				return 0, 0, nil, errors.New("免单和房费折扣不能同时使用")
			}
			if reqDto.DiscountProductRate != nil && *reqDto.DiscountProductRate > 0 {
				return 0, 0, nil, errors.New("免单和商品折扣不能同时使用")
			}
			if reqDto.ReduceRoomAmount != nil && *reqDto.ReduceRoomAmount > 0 {
				return 0, 0, nil, errors.New("免单和减免房费不能同时使用")
			}
			if reqDto.ReduceProductAmount != nil && *reqDto.ReduceProductAmount > 0 {
				return 0, 0, nil, errors.New("免单和减免商品不能同时使用")
			}
		}
	}
	toPayOrderNos := []string{}
	for _, v := range toPayOrderVOs {
		toPayOrderNos = append(toPayOrderNos, v.OrderNo)
	}
	if len(toPayOrderNos) == 0 {
		return 0, 0, nil, errors.New("订单不能为空")
	}
	orderRoomPlans, _ := orderRoomPlanService.FindOrderRoomPlanByOrderNos(ctx, *reqDto.VenueId, *reqDto.SessionId, toPayOrderNos)
	orderProducts, _ := orderProductService.FindOrderProductByOrderNos(ctx, *reqDto.VenueId, *reqDto.SessionId, toPayOrderNos)
	// 1.9 验证重复房费打折、减免
	// for _, v := range *orderRoomPlans {
	// 	if reqDto.DiscountRoomRate != nil && *reqDto.DiscountRoomRate > 0 && v.DiscountRate != nil && *v.DiscountRate > 0 {
	// 		return 0, 0, nil, errors.New("房费不能重复打折")
	// 	}
	// 	if reqDto.ReduceRoomAmount != nil && *reqDto.ReduceRoomAmount > 0 && v.ReduceAmount != nil && *v.ReduceAmount > 0 {
	// 		return 0, 0, nil, errors.New("房费不能重复减免")
	// 	}
	// }
	// 1.10 验证重复商品打折、减免
	// for _, v := range *orderProducts {
	// 	if reqDto.DiscountProductRate != nil && *reqDto.DiscountProductRate > 0 && v.DiscountRate != nil && *v.DiscountRate > 0 {
	// 		return 0, 0, nil, errors.New("商品不能重复打折")
	// 	}
	// 	if reqDto.ReduceProductAmount != nil && *reqDto.ReduceProductAmount > 0 && v.ReduceAmount != nil && *v.ReduceAmount > 0 {
	// 		return 0, 0, nil, errors.New("商品不能重复减免")
	// 	}
	// }
	// 2. 计算支付优惠后金额
	toPayUpdateOrders, _, _, payAmountChanged, totalDiscountRoomAmount, totalDiscountProductAmount, err := payService.CalcLastPayAmountInfo(ctx, reqDto, &toPayOrders, orderRoomPlans, orderProducts)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.InternalError.Code, err.Error())
		return
	}
	if !payAmountChanged {
		if *reqDto.PayAmount != unPayAmount {
			return 0, 0, nil, errors.New("支付金额与未付金额不匹配，请刷新页面重试")
		}
	}
	return totalDiscountRoomAmount, totalDiscountProductAmount, toPayUpdateOrders, nil
}

func (validator *OrderValidator) ValidateRefundDo(ctx *gin.Context, reqDto *req.QueryOrderRefundReqDto) (session *po.Session, refundOrderVOs []vo.OrderVO, dbOrdersAll *[]po.Order, orderAndPays *[]po.OrderAndPay, payBills *[]po.PayBill, err error) {
	// 0.1 验证入参?存在
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, nil, nil, nil, nil, errors.New("VenueId不能为空")
	}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return nil, nil, nil, nil, nil, errors.New("SessionId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return nil, nil, nil, nil, nil, errors.New("RoomId能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return nil, nil, nil, nil, nil, errors.New("EmployeeId不能为空")
	}
	if reqDto.OrderProductVos == nil || len(*reqDto.OrderProductVos) == 0 {
		return nil, nil, nil, nil, nil, errors.New("OrderProductVos不能为空")
	}
	if reqDto.RefundAmount == nil || *reqDto.RefundAmount < 0 {
		return nil, nil, nil, nil, nil, errors.New("RefundAmount不能为空")
	}
	if reqDto.RefundWayType == nil || !util.InList(*reqDto.RefundWayType, _const.REFUND_WAY_TYPES) {
		return nil, nil, nil, nil, nil, errors.New("请选择退款方式")
	}
	// 0.1 查询venue?存在
	venue, err := venueService.FindVenueById(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return nil, nil, nil, nil, nil, errors.New("Venue不存在")
	}
	// 0.2 查询场次?存在
	sessions, err := sessionService.FindAllSession(ctx, &req.QuerySessionReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId})
	if err != nil || len(*sessions) == 0 {
		return nil, nil, nil, nil, nil, errors.New("Session不存在")
	}
	session = &(*sessions)[0]
	// 0.3 查询房间?存在
	rooms, err := roomService.FindAllRoom(ctx, &req.QueryRoomReqDto{VenueId: reqDto.VenueId, Id: reqDto.RoomId})
	if err != nil || len(*rooms) == 0 {
		return nil, nil, nil, nil, nil, errors.New("房间不存在")
	}
	// 0.4 查询员工?存在
	employee, err := employeeService.FindEmployeeById(ctx, *reqDto.EmployeeId)
	if err != nil || employee == nil {
		return nil, nil, nil, nil, nil, errors.New("员工不存在")
	}
	// 0.5 查询本次退款商品?存在？一致
	orderProductIds := []string{}
	orderNosInOrderProducts4Req := []string{}
	for _, orderProduct := range *reqDto.OrderProductVos {
		orderProductIds = append(orderProductIds, orderProduct.Id)
		util.AddListElement(&orderNosInOrderProducts4Req, orderProduct.OrderNo)
	}
	if len(orderProductIds) == 0 {
		return nil, nil, nil, nil, nil, errors.New("退款商品不能为空")
	}
	dbOrderProducts4Refund, err := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId, Ids: &orderProductIds})
	if err != nil {
		return nil, nil, nil, nil, nil, errors.New("退款商品不存在")
	}
	if len(*dbOrderProducts4Refund) != len(*reqDto.OrderProductVos) {
		return nil, nil, nil, nil, nil, errors.New("退款商品不一致")
	}
	// 0.6 查询session下所有订单，并校验订单状态
	dbOrdersAll, err = orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId})
	if err != nil || len(*dbOrdersAll) == 0 {
		return nil, nil, nil, nil, nil, errors.New("订单记录不存在")
	}
	dbAllOrderNo2OrderMap := make(map[string]po.Order)
	for _, order := range *dbOrdersAll {
		dbAllOrderNo2OrderMap[*order.OrderNo] = order
	}
	// 0.7 判断退款的商品是否未支付
	for _, order := range *dbOrdersAll {
		if *order.Status == _const.ORDER_STATUS_REFUNDING {
			return nil, nil, nil, nil, nil, errors.New("存在退款中的订单，请重试")
		}
		if util.InList(*order.OrderNo, orderNosInOrderProducts4Req) {
			if *order.Status == _const.ORDER_STATUS_REFUNDED {
				return nil, nil, nil, nil, nil, errors.New("退款订单不能退款")
			}
		}
	}
	// 0.8 查询session下所有订单商品
	dbAllOrderProducts, err := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId})
	if err != nil || len(*dbAllOrderProducts) == 0 {
		return nil, nil, nil, nil, nil, errors.New("订单商品不存在")
	}
	// 0.8.1 生成sessionid下所有商品vo，并为vo填充订单状态
	dbAllOrderProductVOs := []vo.OrderProductVO{}
	for _, orderProduct := range *dbAllOrderProducts {
		dbOrderProductVO := orderProductTransfer.PoToVo(orderProduct)
		orderInfo, exists := dbAllOrderNo2OrderMap[dbOrderProductVO.OrderNo]
		if exists {
			dbOrderProductVO.StatusInOrder = *orderInfo.Status
			dbAllOrderProductVOs = append(dbAllOrderProductVOs, dbOrderProductVO)
		}
	}
	// 0.9 计算现在商品是否可退
	// 0.9.1 计算现在每个商品剩余总数量，商品ID: 现存数量 = 支付数量 - 退款数量
	dbAllOrderProductQuantityMap := make(map[string]int)
	for _, orderProductVO := range dbAllOrderProductVOs {
		if orderProductVO.StatusInOrder == _const.ORDER_STATUS_REFUNDED {
			// 退款需要查询父商品
			findedParentOrderProductVO := vo.OrderProductVO{}
			for _, parentOrderProductVO := range dbAllOrderProductVOs {
				if orderProductVO.PId == parentOrderProductVO.Id {
					findedParentOrderProductVO = parentOrderProductVO
					break
				}
			}
			if findedParentOrderProductVO.Id == "" {
				return nil, nil, nil, nil, nil, errors.New("退款商品不存在")
			}
			dbAllOrderProductQuantityMap[orderProductVO.PId] -= int(orderProductVO.Quantity)
		} else {
			dbAllOrderProductQuantityMap[orderProductVO.Id] += int(orderProductVO.Quantity)
		}
	}
	// 0.9.2 校验退款数量不能超过现存数量
	for _, requestProduct := range *reqDto.OrderProductVos {
		canRefundQuantity := dbAllOrderProductQuantityMap[requestProduct.Id]
		if canRefundQuantity < 0 {
			return nil, nil, nil, nil, nil, errors.New("商品数量不能为空")
		}
		if requestProduct.Quantity <= 0 {
			return nil, nil, nil, nil, nil, errors.New("商品数量不能退0个")
		}
		if requestProduct.Quantity > int64(canRefundQuantity) {
			return nil, nil, nil, nil, nil, errors.New("退款数量不能超过原订单数量")
		}
	}
	// 构建生成退款的订单：需要退款的订单原订单信息 + 退款的商品信息(RefundOrderProductVOs[更新Quantity])
	refundOrderVOs = []vo.OrderVO{}
	for _, orderProduct := range *reqDto.OrderProductVos {
		opIdRefund := orderProduct.Id
		opQuantityRefund := orderProduct.Quantity
		var findedOrderProduct *vo.OrderProductVO
		for _, orderProductVO := range dbAllOrderProductVOs {
			if orderProductVO.Id == opIdRefund {
				findedOrderProduct = &orderProductVO
				break
			}
		}
		if findedOrderProduct == nil {
			return nil, nil, nil, nil, nil, errors.New("退款商品不存在")
		}
		lastOrderProductVO := util.DeepCopy(*findedOrderProduct)
		// 更新退款数量
		lastOrderProductVO.Quantity = opQuantityRefund

		dbOrder, exists := dbAllOrderNo2OrderMap[findedOrderProduct.OrderNo]
		if !exists {
			return nil, nil, nil, nil, nil, errors.New("退款商品订单不存在")
		}
		var findedOrderVO *vo.OrderVO
		index := -1
		for idx, orderVO := range refundOrderVOs {
			if orderVO.OrderNo == *dbOrder.OrderNo {
				findedOrderVO = &orderVO
				index = idx
				break
			}
		}
		if findedOrderVO == nil {
			findedOrderVO = util.GetItPtr(orderTransfer.PoToVo(dbOrder))
			refundOrderVOs = append(refundOrderVOs, *findedOrderVO)
			index = len(refundOrderVOs) - 1
		}
		refundOrderVOs[index].RefundOrderProductVOs = append(refundOrderVOs[index].RefundOrderProductVOs, lastOrderProductVO)
	}

	// 1.0 查询sessionId下所有收款单
	payStatusList := []string{_const.PAY_STATUS_PAID, _const.PAY_STATUS_REFUND}
	payBills, err = payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{VenueId: reqDto.VenueId, SessionId: reqDto.SessionId, StatusList: &payStatusList})
	if err != nil {
		return nil, nil, nil, nil, nil, errors.New("收款单不存在")
	}
	payIds := []string{}
	for _, payBill := range *payBills {
		payIds = append(payIds, *payBill.BillId)
	}
	// 1.0.1 查询收款单中间表
	orderAndPays, err = orderAndPayService.FindAllOrderAndPay(ctx, &req.QueryOrderAndPayReqDto{BillIdList: &payIds})
	if err != nil {
		return nil, nil, nil, nil, nil, errors.New("收款单2不存在")
	}
	return session, refundOrderVOs, dbOrdersAll, orderAndPays, payBills, nil
}
