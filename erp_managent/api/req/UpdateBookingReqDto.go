package req

type UpdateBookingReqDto struct {
	Id             *string `json:"id"`             // ID
	VenueId        *string `json:"venueId"`        // 门店ID
	CustomerName   *string `json:"customerName"`   // 客户名称
	Gender         *string `json:"gender"`         // 性别 0 男 1 女 3 未知
	CustomerPhone  *string `json:"customerPhone"`  // 客户电话
	MemberId       *string `json:"memberId"`       // 会员id
	MemberCardId   *string `json:"memberCardId"`   // 会员卡id
	MemberCardNumber *string `json:"memberCardNumber"` // 会员卡号
	CustomerSource *string `json:"customerSource"` // 客户来源
	ArrivalTime    *int64  `json:"arrivalTime"`    // 预抵时间
	OpenTablePlan  *string `json:"openTablePlan"`  // 开台方案
	RoomId         *string `json:"roomId"`         // 房间ID
	RoomName       *string `json:"roomName"`       // 房间名称
	BookedBy       *string `json:"bookedBy"`       // 代订人ID
	BookedByName   *string `json:"bookedByName"`   // 代订人姓名
	Remark         *string `json:"remark"`         // 备注
}
