package req

// ModifyInboundRecordReqDto 修改入库记录请求DTO
type ModifyInboundRecordReqDto struct {
	VenueId      *string                         `json:"venueId" binding:"required"`      // 门店ID
	Warehouse    *string                         `json:"warehouse" binding:"required"`    // 仓库名称
	Handler      *string                         `json:"handler" binding:"required"`      // 操作人
	RecordNumber *string                         `json:"recordNumber" binding:"required"` // 单号
	Remark       *string                         `json:"remark"`                          // 备注
	Reason       *string                         `json:"reason" binding:"required"`       // 修改原因
	Items        []CreateInboundRecordItemReqDto `json:"items" binding:"required"`        // 明细项
}
