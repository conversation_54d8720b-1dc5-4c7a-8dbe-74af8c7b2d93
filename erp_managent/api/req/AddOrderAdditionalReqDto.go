package req

import (
	"voderpltvv/erp_managent/api/vo"
)

type AddOrderAdditionalReqDto struct {
	// 场次ID
	SessionId *string `json:"sessionId"`

	// 所属门店ID
	VenueId *string `json:"venueId"`

	// 关联的房间ID
	RoomId *string `json:"roomId"`

	// 支付总金额
	PayAmount *int64 `json:"payAmount"`

	// 原始金额
	OriginalAmount *int64 `json:"originalAmount"`

	// 是否立结
	IsSettled *bool `json:"isSettled"`

	// 当前时间
	CurrentTime *int64 `json:"currentTime"`

	// 点单商品信息
	OrderProductVOs []*vo.OrderProductVO `json:"orderProductVOs"`

	// 房间信息
	RoomVO *vo.RoomVO `json:"roomVO"`

	// 员工ID
	EmployeeId *string `json:"employeeId"`

	// 支付方式
	PayType *string `json:"payType"`

	// 会员金额
	MemberAmount *int64 `json:"memberAmount"`

	// 挂账金额
	CreditAmount *int64 `json:"creditAmount"`

	// 房费折扣率
	DiscountRoomRate *int64 `json:"discountRoomRate"`

	// 商品折扣率
	DiscountProductRate *int64 `json:"discountProductRate"`

	// 减免房费金额
	ReduceRoomAmount *int64 `json:"reduceRoomAmount"`

	// 减免商品金额
	ReduceProductAmount *int64 `json:"reduceProductAmount"`

	// 找零金额
	ChangeAmount *int64 `json:"changeAmount"`

	// 免单金额
	FreeAmount *int64 `json:"freeAmount"`

	// BShowQR支付方式的BQROneCode
	BQROneCode *string `json:"bQROneCode"`

	// 挂账账户ID
	CreditAccountId *string `json:"creditAccountId"`
}

type V3AddOrderAdditionalReqDto struct {
	VenueId    *string `json:"venueId"`
	RoomId     *string `json:"roomId"`
	SessionId  *string `json:"sessionId"`
	EmployeeId *string `json:"employeeId"`
	MemberId   *string `json:"memberId"`
	MemberCardId *string `json:"memberCardId"`
	MemberCardNumber *string `json:"memberCardNumber"`

	OriginalAmount *int64 `json:"originalAmount"` // 原始金额
	PayAmount      *int64 `json:"payAmount"`      // 支付总金额
	CreditAmount   *int64 `json:"creditAmount"`   // 挂账金额
	MemberAmount   *int64 `json:"memberAmount"`   // 会员金额
	ZeroFee        *int64 `json:"zeroFee"`        // 抹零金额
	ChangeAmount   *int64 `json:"changeAmount"`   // 找零金额

	IsGift       *bool      `json:"isGift"`       // 是否赠品

	OrderProductVOs []*vo.OrderProductVO `json:"orderProductVOs"` // 点单商品信息
	CreditAccountId *string              `json:"creditAccountId"` // 挂账账户ID
	CurrentTime     *int64               `json:"currentTime"`     // 当前时间
}

type V3AddOrderAdditionalPayReqDto struct {
	VenueId    *string `json:"venueId"`
	RoomId     *string `json:"roomId"`
	SessionId  *string `json:"sessionId"`
	EmployeeId *string `json:"employeeId"`
	MemberId   *string `json:"memberId"`
	MemberCardId *string `json:"memberCardId"`
	MemberCardNumber *string `json:"memberCardNumber"`

	PayRecords     *[]vo.PayRecordVO `json:"payRecords"`     // 支付记录ID
	OriginalAmount *int64            `json:"originalAmount"` // 原始金额
	PayAmount      *int64            `json:"payAmount"`      // 支付总金额
	MemberAmount   *int64            `json:"memberAmount"`   // 会员金额

	OriginalFee  *int64 `json:"originalFee"`  // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee    *int64 `json:"shouldFee"`    // 应付金额 = 实付金额 + 抹零金额
	TotalFee     *int64 `json:"totalFee"`     // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee      *int64 `json:"zeroFee"`      // 抹零金额
	CreditAmount *int64 `json:"creditAmount"` // 挂账金额

	IsGift       *bool      `json:"isGift"`       // 是否赠品

	ProductDiscount       *int64 `json:"productDiscount"`       // 商品折扣
	RoomDiscount          *int64 `json:"roomDiscount"`          // 房费折扣
	IsFree                *bool  `json:"isFree"`                // 是否免单
	ProductDiscountAmount *int64 `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    *int64 `json:"roomDiscountAmount"`    // 房费减免
	ChangeAmount          *int64 `json:"changeAmount"`          // 找零金额

	OrderProductVOs []*vo.OrderProductVO `json:"orderProductVOs"` // 点单商品信息
	CreditAccountId *string              `json:"creditAccountId"` // 挂账账户ID
	CurrentTime     *int64               `json:"currentTime"`     // 当前时间

	DiscountReason *string `json:"discountReason"` // 优惠原因
}

type V3OrderGiftProductReqDto struct {
	VenueId    *string `json:"venueId"`
	RoomId     *string `json:"roomId"`
	SessionId  *string `json:"sessionId"`
	EmployeeId *string `json:"employeeId"`
	MemberId   *string `json:"memberId"`
	GiftEmployeeId   *string `json:"giftEmployeeId"`
	GiftEmployeeName *string `json:"giftEmployeeName"`
	GiftReason       *string `json:"giftReason"`

	PayAmount       *int64               `json:"payAmount"`      // 支付总金额
	OriginalAmount  *int64               `json:"originalAmount"` // 原始金额
	OrderProductVOs []*vo.OrderProductVO `json:"orderProductVOs"`
}

type V3OrderQueryProductSalesReqDto struct {
	VenueId    *string `json:"venueId"`
	EmployeeId *string `json:"employeeId"`
	StartTime  *int64  `json:"startTime"`
	EndTime    *int64  `json:"endTime"`
}
