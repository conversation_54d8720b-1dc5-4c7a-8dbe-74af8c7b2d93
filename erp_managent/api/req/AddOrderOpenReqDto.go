package req

import "voderpltvv/erp_managent/api/vo"

type AddOrderOpenReqDto struct {
	SessionId            *string               `json:"sessionId"`            // 场次ID
	VenueId              *string               `json:"venueId"`              // 所属门店ID
	RoomId               *string               `json:"roomId"`               // 关联的房间ID
	StartTime            *int64                `json:"startTime"`            // 订单开始时间
	EndTime              *int64                `json:"endTime"`              // 订单结束时间
	PricePlanId          *string               `json:"pricePlanId"`          // 方案id
	PricePlanName        *string               `json:"pricePlanName"`        // 价格方案名称
	ConsumptionMode      *string               `json:"consumptionMode"`      // 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	SelectedAreaId       *string               `json:"selectedAreaId"`       // 选择的计费方式-套餐区域id
	SelectedRoomTypeId   *string               `json:"selectedRoomTypeId"`   // 选择的计费方式-房间类型id
	TimeChargeMode       *string               `json:"timeChargeMode"`       // 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	BuyMinute            *int                  `json:"buyMinute"`            // 买钟时长 单位：分钟
	TimeChargeAmount     *int                  `json:"timeChargeAmount"`     // 买钟金额 单位：分
	TimeChargeEndTime    *string               `json:"timeChargeEndTime"`    // 买钟结束时间 格式：HH:mm
	TimeChargeType       *string               `json:"timeChargeType"`       // 买钟价格类型 基础价格、区域价格、节假日价格
	PayAmount            *int64                `json:"payAmount"`            // 支付总金额
	OriginalAmount       *int64                `json:"originalAmount"`       // 原始金额
	IsOpenTableSettled   *bool                 `json:"isOpenTableSettled"`   // 是否开台立结
	MinimumCharge        *int64                `json:"minimumCharge"`        // 【买断】最低消费金额
	CurrentTime          *int64                `json:"currentTime"`          // 当前时间
	OrderRoomPlanVOS     []*vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"`     // 房费信息
	InOrderProductInfos  []*vo.OrderProductVO  `json:"inOrderProductInfos"`  // 商品信息-套餐内
	OutOrderProductInfos []*vo.OrderProductVO  `json:"outOrderProductInfos"` // 商品信息-套餐外
	RoomVO               *vo.RoomVO            `json:"roomVO"`               // 房间信息
	BookingId            *string               `json:"bookingId"`            // 预订ID
	EmployeeId           *string               `json:"employeeId"`           // 员工ID
	EmployeeIdPay        *string               `json:"employeeIdPay"`        // 支付员工ID
}

type V3AddOrderOpenReqDto struct {
	VenueId       *string `json:"venueId"`
	RoomId        *string `json:"roomId"`
	SessionId     *string `json:"sessionId"`
	StartTime     *int64  `json:"startTime"`
	EndTime       *int64  `json:"endTime"`
	PricePlanId   *string `json:"pricePlanId"`
	PricePlanName *string `json:"pricePlanName"`

	ConsumptionMode      *string               `json:"consumptionMode"`      // 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	SelectedAreaId       *string               `json:"selectedAreaId"`       // 选择的计费方式-套餐区域id
	SelectedRoomTypeId   *string               `json:"selectedRoomTypeId"`   // 选择的计费方式-房间类型id
	TimeChargeMode       *string               `json:"timeChargeMode"`       // 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	BuyMinute            *int                  `json:"buyMinute"`            // 买钟时长 单位：分钟
	TimeChargeAmount     *int                  `json:"timeChargeAmount"`     // 买钟金额 单位：分
	TimeChargeEndTime    *string               `json:"timeChargeEndTime"`    // 买钟结束时间 格式：HH:mm
	TimeChargeType       *string               `json:"timeChargeType"`       // 买钟价格类型 基础价格、区域价格、节假日价格
	MinimumCharge        *int64                `json:"minimumCharge"`        // 【买断】最低消费金额
	OriginalAmount       *int64                `json:"originalAmount"`       // 原始金额
	PayAmount            *int64                `json:"payAmount"`            // 支付总金额
	OrderRoomPlanVOS     []*vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"`     // 房费信息
	InOrderProductInfos  []*vo.OrderProductVO  `json:"inOrderProductInfos"`  // 商品信息-套餐内
	OutOrderProductInfos []*vo.OrderProductVO  `json:"outOrderProductInfos"` // 商品信息-套餐外

	RoomVO       *vo.RoomVO `json:"roomVO"`       // 房间信息
	BookingId    *string    `json:"bookingId"`    // 预订ID
	EmployeeId   *string    `json:"employeeId"`   // 员工ID
	MemberId     *string    `json:"memberId"`     // 会员ID
	MemberCardId *string    `json:"memberCardId"` // 会员卡ID
	MemberCardNumber *string    `json:"memberCardNumber"` // 会员卡号
	CurrentTime  *int64     `json:"currentTime"`  // 当前时间
	IsGift       *bool      `json:"isGift"`       // 是否赠品
}

type V3AddOrderOpenPayReqDto struct {
	VenueId       *string `json:"venueId"`
	SessionId     *string `json:"sessionId"`
	RoomId        *string `json:"roomId"`
	StartTime     *int64  `json:"startTime"`
	EndTime       *int64  `json:"endTime"`
	PricePlanId   *string `json:"pricePlanId"`
	PricePlanName *string `json:"pricePlanName"`

	PayRecords           *[]vo.PayRecordVO     `json:"payRecords"`           // 支付记录ID
	ConsumptionMode      *string               `json:"consumptionMode"`      // 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	SelectedAreaId       *string               `json:"selectedAreaId"`       // 选择的计费方式-套餐区域id
	SelectedRoomTypeId   *string               `json:"selectedRoomTypeId"`   // 选择的计费方式-房间类型id
	TimeChargeMode       *string               `json:"timeChargeMode"`       // 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	BuyMinute            *int                  `json:"buyMinute"`            // 买钟时长 单位：分钟
	TimeChargeAmount     *int                  `json:"timeChargeAmount"`     // 买钟金额 单位：分
	TimeChargeEndTime    *string               `json:"timeChargeEndTime"`    // 买钟结束时间 格式：HH:mm
	TimeChargeType       *string               `json:"timeChargeType"`       // 买钟价格类型 基础价格、区域价格、节假日价格
	MinimumCharge        *int64                `json:"minimumCharge"`        // 【买断】最低消费金额
	OriginalAmount       *int64                `json:"originalAmount"`       // 原始金额
	PayAmount            *int64                `json:"payAmount"`            // 支付总金额
	OrderRoomPlanVOS     []*vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"`     // 房费信息
	InOrderProductInfos  []*vo.OrderProductVO  `json:"inOrderProductInfos"`  // 商品信息-套餐内
	OutOrderProductInfos []*vo.OrderProductVO  `json:"outOrderProductInfos"` // 商品信息-套餐外

	OriginalFee  *int64 `json:"originalFee"`  // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee    *int64 `json:"shouldFee"`    // 应付金额 = 实付金额 + 抹零金额
	TotalFee     *int64 `json:"totalFee"`     // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee      *int64 `json:"zeroFee"`      // 抹零金额
	CreditAmount *int64 `json:"creditAmount"` // 挂账金额

	ProductDiscount       *int64 `json:"productDiscount"`       // 商品折扣
	RoomDiscount          *int64 `json:"roomDiscount"`          // 房费折扣
	IsFree                *bool  `json:"isFree"`                // 是否免单
	ProductDiscountAmount *int64 `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    *int64 `json:"roomDiscountAmount"`    // 房费减免
	ChangeAmount          *int64 `json:"changeAmount"`          // 找零金额

	RoomVO       *vo.RoomVO `json:"roomVO"`       // 房间信息
	BookingId    *string    `json:"bookingId"`    // 预订ID
	EmployeeId   *string    `json:"employeeId"`   // 员工ID
	MemberId     *string    `json:"memberId"`     // 会员ID
	MemberCardId *string    `json:"memberCardId"` // 会员卡ID
	MemberCardNumber *string    `json:"memberCardNumber"` // 会员卡号
	CurrentTime  *int64     `json:"currentTime"`  // 当前时间
	IsGift       *bool      `json:"isGift"`       // 是否赠品

	DiscountReason *string `json:"discountReason"` // 优惠原因
}

type V3EndTimeConsumeReqDto struct {
	VenueId          *string              `json:"venueId"`
	RoomId           *string              `json:"roomId"`
	SessionId        *string              `json:"sessionId"`
	EmployeeId       *string              `json:"employeeId"`
	MemberId         *string              `json:"memberId"`
	MemberCardId     *string              `json:"memberCardId"`
	MemberCardNumber *string              `json:"memberCardNumber"` // 会员卡号
	ConsumptionMode  *string              `json:"consumptionMode"`  // 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	OrderRoomPlanVOS []vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"` // 房费信息
}

type V3EndTimeConsumeV2ReqDto struct {
	VenueId    *string `json:"venueId"`
	RoomId     *string `json:"roomId"`
	SessionId  *string `json:"sessionId"`
	EmployeeId *string `json:"employeeId"`
	MemberId   *string `json:"memberId"`
	StartTime  *int64  `json:"startTime"`
	EndTime    *int64  `json:"endTime"`

	OrderRoomPlanId  *string               `json:"orderRoomPlanId"`  // 房费ID
	OrderRoomPlanVOS *[]vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"` // 房费信息
}

type V3OrderReopenReqDto struct {
	VenueId    *string `json:"venueId"`
	RoomId     *string `json:"roomId"`
	EmployeeId *string `json:"employeeId"`
}

type V3MiniAppPayReqDto struct {
	VenueId      *string `json:"venueId"`
	RoomId       *string `json:"roomId"`
	SessionId    *string `json:"sessionId"`
	EmployeeId   *string `json:"employeeId"`
	MemberId     *string `json:"memberId"`
	MemberCardId *string `json:"memberCardId"`
	MemberCardNumber *string `json:"memberCardNumber"` // 会员卡号

	PayRecords     *[]vo.PayRecordVO `json:"payRecords"`     // 支付记录ID
	OriginalAmount *int64            `json:"originalAmount"` // 原始金额
	PayAmount      *int64            `json:"payAmount"`      // 支付总金额
	MemberAmount   *int64            `json:"memberAmount"`   // 会员金额

	OriginalFee  *int64 `json:"originalFee"`  // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee    *int64 `json:"shouldFee"`    // 应付金额 = 实付金额 + 抹零金额
	TotalFee     *int64 `json:"totalFee"`     // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee      *int64 `json:"zeroFee"`      // 抹零金额
	CreditAmount *int64 `json:"creditAmount"` // 挂账金额

	ProductDiscount       *int64 `json:"productDiscount"`       // 商品折扣
	RoomDiscount          *int64 `json:"roomDiscount"`          // 房费折扣
	IsFree                *bool  `json:"isFree"`                // 是否免单
	ProductDiscountAmount *int64 `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    *int64 `json:"roomDiscountAmount"`    // 房费减免
	ChangeAmount          *int64 `json:"changeAmount"`          // 找零金额

	OrderProductVOs []*vo.OrderProductVO `json:"orderProductVOs"` // 点单商品信息
	CreditAccountId *string              `json:"creditAccountId"` // 挂账账户ID
	CurrentTime     *int64               `json:"currentTime"`     // 当前时间
	Openid          *string              `json:"openid"`
}
