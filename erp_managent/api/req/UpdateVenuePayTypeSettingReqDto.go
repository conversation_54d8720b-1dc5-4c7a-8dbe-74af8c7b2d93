package req

import "voderpltvv/erp_managent/domain/venue/model/valueobject"

// UpdateVenuePayTypeSettingReqDto 更新门店支付设置请求DTO
type UpdateVenuePayTypeSettingReqDto struct {
	Id           *string                     `json:"id"`           // ID
	VenueId      *string                     `json:"venueId"`      // 门店ID
	TypeInfoList []valueobject.PayTypeConfig `json:"typeInfoList"` // 支付类型信息结构化数据
	Remark       *string                     `json:"remark"`       // 备注
	OldPaytype   *string                     `json:"oldPaytype"`   // 旧的支付类型，用于验证是否有支付记录
}
