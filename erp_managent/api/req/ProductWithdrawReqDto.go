package req

// =================== 酒水取用相关请求DTO ===================

// 取用酒水请求
type AddProductWithdrawReqDto struct {
	StorageId        string `json:"storageId" binding:"required"`    // 存储记录ID
	StorageOrderNo   string `json:"storageOrderNo"`                  // 关联的存酒单号（可从存储记录获取）
	VenueId          string `json:"venueId" binding:"required"`      // 门店ID
	CustomerId       string `json:"customerId"`                      // 客户ID（可从存储记录获取）
	CustomerName     string `json:"customerName"`                    // 客户姓名（可从存储记录获取）
	OrderNumber      string `json:"orderNumber"`                     // 关联订单号
	OperatorId       string `json:"operatorId"`                      // 操作人ID
	OperatorName     string `json:"operatorName"`                    // 操作人姓名
	ProductId        string `json:"productId"`                       // 产品ID（可从存储记录获取）
	ProductName      string `json:"productName"`                     // 产品名称（可从存储记录获取）
	ProductUnit      string `json:"productUnit"`                     // 产品单位
	ProductSpec      string `json:"productSpec"`                     // 产品规格
	Quantity         int    `json:"quantity" binding:"required"`     // 取用数量
	WithdrawTime     int64  `json:"withdrawTime" binding:"required"` // 取用时间
	DeliveryRoomId   string `json:"deliveryRoomId"`                  // 送达包厢ID
	DeliveryRoomName string `json:"deliveryRoomName"`                // 送达包厢名称
	Remark           string `json:"remark"`                          // 备注
	StorageLocation  string `json:"storageLocation"`                 // 存放位置
	ParentOrderNo    string `json:"parentOrderNo"`                   // 父订单号
	IsBatch          int    `json:"isBatch"`                         // 是否批量操作的一部分
	BatchTime        int64  `json:"batchTime"`                       // 批量操作时间
}

// 更新酒水取用记录请求
type UpdateProductWithdrawReqDto struct {
	Id              string `json:"id" binding:"required"`             // ID
	StorageId       string `json:"storageId" binding:"required"`      // 存储记录ID
	StorageOrderNo  string `json:"storageOrderNo" binding:"required"` // 关联的存酒单号
	VenueId         string `json:"venueId" binding:"required"`        // 门店ID
	CustomerId      string `json:"customerId" binding:"required"`     // 客户ID
	CustomerName    string `json:"customerName" binding:"required"`   // 客户姓名
	OrderNumber     string `json:"orderNumber"`                       // 关联订单号
	ProductId       string `json:"productId" binding:"required"`      // 产品ID
	ProductName     string `json:"productName" binding:"required"`    // 产品名称
	ProductUnit     string `json:"productUnit"`                       // 产品单位
	ProductSpec     string `json:"productSpec"`                       // 产品规格
	Quantity        int    `json:"quantity" binding:"required"`       // 取用数量
	WithdrawTime    int64  `json:"withdrawTime" binding:"required"`   // 取用时间
	RoomId          string `json:"roomId"`                            // 房间ID
	RoomName        string `json:"roomName"`                          // 房间名称
	Remark          string `json:"remark"`                            // 备注
	StorageLocation string `json:"storageLocation"`                   // 存放位置
}

// 查询酒水取用记录请求
type QueryProductWithdrawReqDto struct {
	Id                string `json:"id"`                         // ID
	OrderNo           string `json:"orderNo"`                    // 取酒单号
	StorageId         string `json:"storageId"`                  // 存储记录ID
	StorageOrderNo    string `json:"storageOrderNo"`             // 关联的存酒单号
	ParentOrderNo     string `json:"parentOrderNo"`              // 父订单号
	VenueId           string `json:"venueId" binding:"required"` // 门店ID
	OperatorId        string `json:"operatorId"`                 // 操作人ID
	OperatorName      string `json:"operatorName"`               // 操作人姓名
	CustomerId        string `json:"customerId"`                 // 客户ID
	CustomerName      string `json:"customerName"`               // 客户姓名
	PhoneNumber       string `json:"phoneNumber"`                // 电话号码
	OrderNumber       string `json:"orderNumber"`                // 关联订单号
	ProductId         string `json:"productId"`                  // 产品ID
	ProductName       string `json:"productName"`                // 产品名称
	ProductUnit       string `json:"productUnit"`                // 产品单位
	ProductSpec       string `json:"productSpec"`                // 产品规格
	RoomId            string `json:"roomId"`                     // 房间ID
	RoomName          string `json:"roomName"`                   // 房间名称
	SearchText        string `json:"searchText"`                 // 搜索文本(模糊搜索客户名或手机号)
	WithdrawTimeStart int64  `json:"withdrawTimeStart"`          // 取用时间开始
	WithdrawTimeEnd   int64  `json:"withdrawTimeEnd"`            // 取用时间结束
	PageNum           int    `json:"pageNum"`                    // 页码
	PageSize          int    `json:"pageSize"`                   // 每页记录数
}

// 删除酒水取用记录请求
type DeleteProductWithdrawReqDto struct {
	Id string `json:"id" binding:"required"` // ID
}

// BatchAddProductWithdrawReqDto 批量添加酒水提取请求DTO
type BatchAddProductWithdrawReqDto struct {
	OrderNo          *string             `json:"orderNo"`                               // 订单号
	VenueId          *string             `json:"venueId" binding:"required"`            // 场所ID
	DeliveryRoomId   *string             `json:"deliveryRoomId"`                        // 送达包厢ID
	DeliveryRoomName *string             `json:"deliveryRoomName"`                      // 送达包厢名称
	WithdrawTime     *int64              `json:"withdrawTime" binding:"required"`       // 提取时间
	Remark           *string             `json:"remark"`                                // 备注
	OperatorId       *string             `json:"operatorId"`                            // 操作人ID
	OperatorName     *string             `json:"operatorName"`                          // 操作人姓名
	WithdrawItems    []BatchWithdrawItem `json:"withdrawItems" binding:"required,dive"` // 提取项列表
}

// BatchWithdrawItem 批量提取项
type BatchWithdrawItem struct {
	StorageId      *string `json:"storageId" binding:"required"`      // 存储ID
	StorageOrderNo *string `json:"storageOrderNo" binding:"required"` // 存酒单号
	Quantity       *int    `json:"quantity" binding:"required"`       // 提取数量
	ProductUnit    *string `json:"productUnit"`                       // 产品单位
	ProductSpec    *string `json:"productSpec"`                       // 产品规格
}

// 添加取酒单请求
type AddProductWithdrawOrderReqDto struct {
	VenueId      string                   `json:"venueId" binding:"required"`      // 门店ID
	CustomerId   string                   `json:"customerId" binding:"required"`   // 客户ID
	CustomerName string                   `json:"customerName" binding:"required"` // 客户姓名
	PhoneNumber  string                   `json:"phoneNumber"`                     // 电话号码
	OperatorId   string                   `json:"operatorId"`                      // 操作人ID
	OperatorName string                   `json:"operatorName"`                    // 操作人姓名
	OrderNumber  string                   `json:"orderNumber"`                     // 关联订单号
	WithdrawTime int64                    `json:"withdrawTime" binding:"required"` // 取用时间
	RoomId       string                   `json:"roomId"`                          // 房间ID
	RoomName     string                   `json:"roomName"`                        // 房间名称
	Remark       string                   `json:"remark"`                          // 备注
	Items        []AddProductWithdrawItem `json:"items" binding:"required,dive"`   // 取酒明细项
}

// 取酒明细项
type AddProductWithdrawItem struct {
	StorageId       string `json:"storageId" binding:"required"`      // 存储记录ID
	StorageOrderNo  string `json:"storageOrderNo" binding:"required"` // 关联的存酒单号
	ProductId       string `json:"productId" binding:"required"`      // 产品ID
	ProductName     string `json:"productName" binding:"required"`    // 产品名称
	ProductUnit     string `json:"productUnit"`                       // 产品单位
	ProductSpec     string `json:"productSpec"`                       // 产品规格
	Quantity        int    `json:"quantity" binding:"required"`       // 取用数量
	StorageLocation string `json:"storageLocation"`                   // 存放位置
	Remark          string `json:"remark"`                            // 备注
}

// 查询取酒单请求
type QueryProductWithdrawOrderReqDto struct {
	Id                string `json:"id"`                         // ID
	OrderNo           string `json:"orderNo"`                    // 取酒单号
	VenueId           string `json:"venueId" binding:"required"` // 场馆ID
	CustomerId        string `json:"customerId"`                 // 客户ID
	CustomerName      string `json:"customerName"`               // 客户姓名
	PhoneNumber       string `json:"phoneNumber"`                // 电话号码
	OrderNumber       string `json:"orderNumber"`                // 关联订单号
	RoomId            string `json:"roomId"`                     // 房间ID
	RoomName          string `json:"roomName"`                   // 房间名称
	OperatorId        string `json:"operatorId"`                 // 操作人ID
	OperatorName      string `json:"operatorName"`               // 操作人姓名
	WithdrawTimeStart int64  `json:"withdrawTimeStart"`          // 取用时间开始
	WithdrawTimeEnd   int64  `json:"withdrawTimeEnd"`            // 取用时间结束
	SearchText        string `json:"searchText"`                 // 搜索文本(模糊搜索客户名或手机号)
	PageNum           int    `json:"pageNum"`                    // 页码
	PageSize          int    `json:"pageSize"`                   // 每页记录数
}

// 根据ID获取取酒单的请求
type GetProductWithdrawOrderReqDto struct {
	Id string `json:"id" binding:"required"` // 取酒单ID
}

// 删除取酒单的请求
type DeleteProductWithdrawOrderReqDto struct {
	Id string `json:"id" binding:"required"` // 取酒单ID
}

// =================== 客户酒水存取记录查询 ===================

// 查询客户酒水存取记录请求
type QueryCustomerProductStorageRecordsReqDto struct {
	VenueId    string `json:"venueId"`    // 场馆ID
	CustomerId string `json:"customerId"` // 客户ID
	ProductId  string `json:"productId"`  // 产品ID
	StartTime  int64  `json:"startTime"`  // 开始时间
	EndTime    int64  `json:"endTime"`    // 结束时间
	PageNum    int    `json:"pageNum"`    // 页码
	PageSize   int    `json:"pageSize"`   // 页大小
}

// 查询客户当前可用酒水请求
type QueryCustomerAvailableProductsReqDto struct {
	VenueId    string `json:"venueId"`    // 场馆ID
	CustomerId string `json:"customerId"` // 客户ID
	SearchText string `json:"searchText"` // 搜索文本(产品名称关键字)
	PageNum    int    `json:"pageNum"`    // 页码
	PageSize   int    `json:"pageSize"`   // 页大小
}

// QueryWithdrawableItemsReqDto 查询可取商品请求
type QueryWithdrawableItemsReqDto struct {
	VenueId             string  `json:"venueId"`             // 门店ID
	SearchType          *string `json:"searchType"`          // 搜索类型: phone(手机号)/name(姓名)/id(客户ID)/card(会员卡号)
	SearchValue         string  `json:"searchValue"`         // 搜索值
	FuzzyMatch          bool    `json:"fuzzyMatch"`          // 是否模糊匹配，默认true
	IncludeExpiringSoon bool    `json:"includeExpiringSoon"` // 是否包含即将到期的商品，默认true
	ExpireDays          int     `json:"expireDays"`          // 即将到期的天数定义，默认30天
}
