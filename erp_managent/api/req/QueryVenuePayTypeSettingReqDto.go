package req

type QueryVenuePayTypeSettingReqDto struct {
	Id       *string   `json:"id"`       // ID
	VenueId  *string   `json:"venueId"`  // 门店ID
	VenueIds *[]string `json:"venueIds"` // 门店ID列表
	TypeInfo *string   `json:"typeInfo"` // 支付类型信息
	Remark   *string   `json:"remark"`   // 备注
	PageNum  *int      `json:"pageNum"`  // 页码
	PageSize *int      `json:"pageSize"` // 每页记录数
}

type QueryVenuePayTypeSettingMiniReqDto struct {
	Id      *string `json:"id"`      // ID
	VenueId *string `json:"venueId"` // 门店ID
}

type QueryVenuePayTypeSettingFindReqDto struct {
	Id      *string `json:"id"`      // ID
	VenueId *string `json:"venueId"` // 门店ID
}
