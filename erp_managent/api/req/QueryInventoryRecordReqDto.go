package req

type QueryInventoryRecordReqDto struct {
	Id           *string `json:"id"`           // ID
	VenueId      *string `json:"venueId"`      // 所属门店ID
	Warehouse    *string `json:"warehouse"`    // 仓库名称
	Type         *string `json:"type"`         // 记录类型
	Handler      *string `json:"handler"`      // 操作人
	Time         *int64  `json:"time"`         // 操作时间
	RecordNumber *string `json:"recordNumber"` // 关联单号 (入库单号/订单号)
	Products     *string `json:"products"`     // 产品信息列表
	PageNum      *int    `json:"pageNum"`      // 页码
	PageSize     *int    `json:"pageSize"`     // 每页记录数
}
