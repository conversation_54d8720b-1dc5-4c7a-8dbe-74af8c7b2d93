package req

import "voderpltvv/erp_managent/api/vo"

type AddOrderTransferRoomReqDto struct {
	VenueId          *string      `json:"venueId"`
	SessionVOOpening vo.SessionVO `json:"sessionVOOpening"`
	SessionVOIdle    vo.SessionVO `json:"sessionVOIdle"`
}

type V3TransferRoomReqDto struct {
	VenueId       *string `json:"venueId"`
	RoomId        *string `json:"roomId"`
	SessionId     *string `json:"sessionId"`
	StartTime     *int64  `json:"startTime"`
	EndTime       *int64  `json:"endTime"`
	PricePlanId   *string `json:"pricePlanId"`
	PricePlanName *string `json:"pricePlanName"`

	PayRecords           *[]vo.PayRecordVO     `json:"payRecords"`           // 支付记录ID
	RefundPayRecords     *[]vo.PayRecordVO     `json:"refundPayRecords"`     // 退款支付记录ID
	ConsumptionMode      *string               `json:"consumptionMode"`      // 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	SelectedAreaId       *string               `json:"selectedAreaId"`       // 选择的计费方式-套餐区域id
	SelectedRoomTypeId   *string               `json:"selectedRoomTypeId"`   // 选择的计费方式-房间类型id
	TimeChargeMode       *string               `json:"timeChargeMode"`       // 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	BuyMinute            *int                  `json:"buyMinute"`            // 买钟时长 单位：分钟
	TimeChargeAmount     *int                  `json:"timeChargeAmount"`     // 买钟金额 单位：分
	TimeChargeEndTime    *string               `json:"timeChargeEndTime"`    // 买钟结束时间 格式：HH:mm
	TimeChargeType       *string               `json:"timeChargeType"`       // 买钟价格类型 基础价格、区域价格、节假日价格
	MinimumCharge        *int64                `json:"minimumCharge"`        // 【买断】最低消费金额
	OriginalAmount       *int64                `json:"originalAmount"`       // 原始金额
	PayAmount            *int64                `json:"payAmount"`            // 支付总金额
	OrderRoomPlanVOS     []*vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"`     // 房费信息
	InOrderProductInfos  []*vo.OrderProductVO  `json:"inOrderProductInfos"`  // 商品信息-套餐内
	OutOrderProductInfos []*vo.OrderProductVO  `json:"outOrderProductInfos"` // 商品信息-套餐外

	OriginalFee  *int64 `json:"originalFee"`  // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee    *int64 `json:"shouldFee"`    // 应付金额 = 实付金额 + 抹零金额 + 找零金额
	TotalFee     *int64 `json:"totalFee"`     // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee      *int64 `json:"zeroFee"`      // 抹零金额
	CreditAmount *int64 `json:"creditAmount"` // 挂账金额

	ProductDiscount       *int64 `json:"productDiscount"`       // 商品折扣
	RoomDiscount          *int64 `json:"roomDiscount"`          // 房费折扣
	IsFree                *bool  `json:"isFree"`                // 是否免单
	ProductDiscountAmount *int64 `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    *int64 `json:"roomDiscountAmount"`    // 房费减免
	ChangeAmount          *int64 `json:"changeAmount"`          // 找零金额

	RoomVO           *vo.RoomVO `json:"roomVO"`           // 房间信息
	BookingId        *string    `json:"bookingId"`        // 预订ID
	EmployeeId       *string    `json:"employeeId"`       // 员工ID
	MemberId         *string    `json:"memberId"`         // 会员ID
	MemberCardId     *string    `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber *string    `json:"memberCardNumber"` // 会员卡号
	CurrentTime      *int64     `json:"currentTime"`      // 当前时间
	IsGift           *bool      `json:"isGift"`           // 是否赠品
	DiscountReason   *string    `json:"discountReason"`   // 优惠原因

	RefundOrderNos *[]string `json:"refundOrderNos"` // 退款订单号 房费+套内商品
}

type V3SwapRoomReqDto struct {
	VenueId           *string      `json:"venueId"`
	SessionVOOpeningA vo.SessionVO `json:"sessionVOOpeningA"`
	SessionVOOpeningB vo.SessionVO `json:"sessionVOOpeningB"`
	EmployeeId        *string      `json:"employeeId"`
}

type V3MergeRoomReqDto struct {
	VenueId           *string      `json:"venueId"`
	SessionVOOpeningA vo.SessionVO `json:"sessionVOOpeningA"`
	SessionVOOpeningB vo.SessionVO `json:"sessionVOOpeningB"`
	EmployeeId        *string      `json:"employeeId"`
}

type V3LockRoomReqDto struct {
	VenueId    *string `json:"venueId" form:"venueId"`
	RoomId     *string `json:"roomId" form:"roomId"`
	SessionId  *string `json:"sessionId" form:"sessionId"`
	EmployeeId *string `json:"employeeId" form:"employeeId"`
}

type V3UnlockRoomReqDto struct {
	VenueId    *string `json:"venueId" form:"venueId"`
	RoomId     *string `json:"roomId" form:"roomId"`
	SessionId  *string `json:"sessionId" form:"sessionId"`
	EmployeeId *string `json:"employeeId" form:"employeeId"`
}

type V3CancelOrderOpenReqDto struct {
	VenueId    *string `json:"venueId" form:"venueId"`
	SessionId  *string `json:"sessionId" form:"sessionId"`
	RoomId     *string `json:"roomId" form:"roomId"`
	EmployeeId *string `json:"employeeId" form:"employeeId"`

	CancelEmployeeId   *string `json:"cancelEmployeeId" form:"cancelEmployeeId"`     // 取消员工ID
	CancelEmployeeName *string `json:"cancelEmployeeName" form:"cancelEmployeeName"` // 取消员工名称
	CancelReason       *string `json:"cancelReason" form:"cancelReason"`             // 取消原因
}
