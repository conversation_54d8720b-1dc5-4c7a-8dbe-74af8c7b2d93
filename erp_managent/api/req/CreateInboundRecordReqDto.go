package req

// CreateInboundRecordReqDto 创建入库记录请求DTO
type CreateInboundRecordReqDto struct {
	VenueId      *string                         `json:"venueId" binding:"required"`      // 门店ID
	Warehouse    *string                         `json:"warehouse" binding:"required"`    // 仓库名称
	Handler      *string                         `json:"handler" binding:"required"`      // 操作人
	RecordNumber *string                         `json:"recordNumber" binding:"required"` // 单号
	Remark       *string                         `json:"remark"`                          // 备注
	Items        []CreateInboundRecordItemReqDto `json:"items" binding:"required"`        // 明细项
}

// CreateInboundRecordItemReqDto 创建入库记录明细项请求DTO
type CreateInboundRecordItemReqDto struct {
	ProductId *string  `json:"productId" binding:"required"` // 商品ID
	Quantity  *int     `json:"quantity" binding:"required"`  // 数量
	UnitPrice *float64 `json:"unitPrice" binding:"required"` // 单价
}
