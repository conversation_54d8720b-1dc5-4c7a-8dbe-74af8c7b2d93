package req

import (
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

type QueryOrderPayReqDto struct {
	// 场次ID
	SessionId *string `json:"sessionId"`

	// 所属门店ID
	VenueId *string `json:"venueId"`

	// 关联的房间ID
	RoomId *string `json:"roomId"`

	// 支付方式
	PayType *string `json:"payType"`

	// 支付金额
	PayAmount *int64 `json:"payAmount"`

	// 原始金额
	OriginalFee *int64 `json:"originalFee"`

	// 会员金额
	MemberAmount *int64 `json:"memberAmount"`

	// 优惠金额
	DiscountAmount *int64 `json:"discountAmount"`

	// 挂账金额
	CreditAmount *int64 `json:"creditAmount"`

	// 房费折扣率
	DiscountRoomRate *int64 `json:"discountRoomRate"`

	// 商品折扣率
	DiscountProductRate *int64 `json:"discountProductRate"`

	// 减免房费金额
	ReduceRoomAmount *int64 `json:"reduceRoomAmount"`

	// 减免商品金额
	ReduceProductAmount *int64 `json:"reduceProductAmount"`

	// 找零金额
	ChangeAmount *int64 `json:"changeAmount"`

	// 免单金额
	FreeAmount *int64 `json:"freeAmount"`

	// 订单ID
	OrderNos *[]string `json:"orderNos"`

	// BShowQR支付方式的BQROneCode
	BQROneCode *string `json:"bQROneCode"`

	// 员工ID
	EmployeeId *string `json:"employeeId"`

	// 挂账账户ID
	CreditAccountId *string `json:"creditAccountId"`
}

type V3QueryOrderPayReqDto struct {
	VenueId    *string `json:"venueId"`
	RoomId     *string `json:"roomId"`
	SessionId  *string `json:"sessionId"`
	EmployeeId *string `json:"employeeId"`

	PayRecords *[]vo.PayRecordVO `json:"payRecords"` // 支付记录ID
	OrderNos   *[]string         `json:"orderNos"`   // 订单ID

	OriginalFee  *int64 `json:"originalFee"`  // 原始金额
	TotalFee     *int64 `json:"totalFee"`     // 总金额-实际支付金额或会员卡的本金
	ShouldFee    *int64 `json:"shouldFee"`    // 应付金额
	ZeroFee      *int64 `json:"zeroFee"`      // 抹零金额
	CreditAmount *int64 `json:"creditAmount"` // 挂账金额

	ProductDiscount       *int64 `json:"productDiscount"`       // 商品折扣
	RoomDiscount          *int64 `json:"roomDiscount"`          // 房费折扣
	IsFree                *bool  `json:"isFree"`                // 是否免单
	ProductDiscountAmount *int64 `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    *int64 `json:"roomDiscountAmount"`    // 房费减免

	ChangeAmount *int64 `json:"changeAmount"` // 找零金额

	MemberAmount    *int64  `json:"memberAmount"`    // 会员金额
	DiscountAmount  *int64  `json:"discountAmount"`  // 优惠金额
	CreditAccountId *string `json:"creditAccountId"` // 挂账账户ID
	MemberCardId    *string `json:"memberCardId"`    // 会员卡ID-登录人
	MemberCardNumber *string `json:"memberCardNumber"` // 会员卡号

	DiscountReason *string `json:"discountReason"` // 优惠原因

	OrderRoomPlanVOS *[]vo.OrderRoomPlanVO `json:"orderRoomPlanVOS"` // 房费信息
}

// V3QueryOrderPayTransformReqDto 转换支付网关请求DTO
type V3QueryOrderPayTransformReqDto struct {
	VenueId   *string `json:"venueId"`
	RoomId    *string `json:"roomId"`
	SessionId *string `json:"sessionId"`

	// 支付方式
	PayType *string `json:"payType"`

	// 支付金额
	PayAmount *int64 `json:"payAmount"`

	// 原始金额
	OriginalFee *int64 `json:"originalFee"`

	// 会员金额
	MemberAmount *int64 `json:"memberAmount"`

	// 优惠金额
	DiscountAmount *int64 `json:"discountAmount"`

	// 挂账金额
	CreditAmount *int64 `json:"creditAmount"`

	// 房费折扣率
	DiscountRoomRate *int64 `json:"discountRoomRate"`

	// 商品折扣率
	DiscountProductRate *int64 `json:"discountProductRate"`

	// 减免房费金额
	ReduceRoomAmount *int64 `json:"reduceRoomAmount"`

	// 减免商品金额
	ReduceProductAmount *int64 `json:"reduceProductAmount"`

	// 找零金额
	ChangeAmount *int64 `json:"changeAmount"`

	// 免单金额
	FreeAmount *int64 `json:"freeAmount"`

	// 订单ID
	OrderNos *[]string `json:"orderNos"`

	// 员工ID
	EmployeeId *string `json:"employeeId"`

	// 挂账账户ID
	CreditAccountId *string `json:"creditAccountId"`

	// 支付记录ID
	PayRecords    *[]vo.PayRecordVO `json:"payRecords"`
	NewPayRecords []po.PayRecord    `json:"newPayRecords"`
}
