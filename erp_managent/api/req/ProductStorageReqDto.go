package req

// =================== 酒水存放相关请求DTO ===================

// 统一存酒请求DTO（支持单商品和多商品存酒）
type UnifiedProductStorageReqDto struct {
	// 基本信息
	VenueId          string `json:"venueId"`                    // 门店ID
	CustomerId       string `json:"customerId"`                 // 客户ID
	CustomerName     string `json:"customerName"`               // 客户姓名
	PhoneNumber      string `json:"phoneNumber"`                // 电话号码
	MemberCardNumber string `json:"memberCardNumber,omitempty"` // 会员卡号
	MemberCardId     string `json:"memberCardId,omitempty"`     // 会员卡ID

	// 存酒信息
	StorageTime     int64  `json:"storageTime"`     // 存入时间
	Remark          string `json:"remark"`          // 备注
	OperatorId      string `json:"operatorId"`      // 操作人ID
	OperatorName    string `json:"operatorName"`    // 操作人姓名
	EmployeeId      string `json:"employeeId"`      // 员工ID
	StorageRoomId   string `json:"storageRoomId"`   // 寄存包厢ID
	StorageRoomName string `json:"storageRoomName"` // 寄存包厢名称
	OfflineOnly     bool   `json:"offlineOnly"`     // 是否仅线下存酒

	// 存酒商品信息 - 支持多商品
	Items []UnifiedStorageItemDto `json:"items"` // 存酒商品列表

	// 其他系统必要字段
	GrantIp string `json:"grantIp"` // 授权IP
	Mac     string `json:"mac"`     // MAC地址

	// 单商品存酒时的额外属性
	ProductId       string `json:"productId"`       // 产品ID (单商品存酒时使用)
	ProductName     string `json:"productName"`     // 产品名称 (单商品存酒时使用)
	ProductType     string `json:"productType"`     // 产品类型 (单商品存酒时使用)
	ProductUnit     string `json:"productUnit"`     // 产品单位 (单商品存酒时使用)
	ProductSpec     string `json:"productSpec"`     // 产品规格 (单商品存酒时使用)
	Quantity        int    `json:"quantity"`        // 数量 (单商品存酒时使用)
	StorageLocation string `json:"storageLocation"` // 存放位置 (单商品存酒时使用)
	ExpireTime      int64  `json:"expireTime"`      // 到期时间 (单商品存酒时使用)
}

// 统一存酒商品项
type UnifiedStorageItemDto struct {
	ProductId       string `json:"productId"`       // 产品ID
	ProductName     string `json:"productName"`     // 产品名称
	ProductType     string `json:"productType"`     // 产品类型
	ProductUnit     string `json:"productUnit"`     // 产品单位
	ProductSpec     string `json:"productSpec"`     // 产品规格
	Quantity        int    `json:"quantity"`        // 数量
	StorageLocation string `json:"storageLocation"` // 存放位置
	ExpireTime      int64  `json:"expireTime"`      // 到期时间
	Remark          string `json:"remark"`          // 备注
	StorageRoomId   string `json:"storageRoomId"`   // 存放包厢ID
	OfflineOnly     bool   `json:"offlineOnly"`     // 是否仅线下存酒
}

// 存入酒水请求
type AddProductStorageReqDto struct {
	VenueId          string `json:"venueId"`                         // 门店ID
	CustomerId       string `json:"customerId"`                      // 客户ID
	CustomerName     string `json:"customerName"`                    // 客户姓名
	PhoneNumber      string `json:"phoneNumber"`                     // 电话号码
	MemberCardNumber string `json:"memberCardNumber,omitempty"`      // 会员卡号
	MemberCardId     string `json:"memberCardId,omitempty"`          // 会员卡ID
	ProductId        string `json:"productId" binding:"required"`    // 产品ID
	ProductName      string `json:"productName" binding:"required"`  // 产品名称
	ProductType      string `json:"productType"`                     // 产品类型
	ProductUnit      string `json:"productUnit" binding:"required"`  // 产品单位(如:瓶、杯)
	ProductSpec      string `json:"productSpec"`                     // 产品规格(如:整瓶、半瓶)
	Quantity         int    `json:"quantity" binding:"required"`     // 数量
	StorageLocation  string `json:"storageLocation"`                 // 存放位置
	StorageTime      int64  `json:"storageTime" binding:"required"`  // 存入时间
	ExpireTime       int64  `json:"expireTime" binding:"required"`   // 到期时间
	Remark           string `json:"remark"`                          // 备注
	OperatorId       string `json:"operatorId" binding:"required"`   // 操作人ID
	OperatorName     string `json:"operatorName" binding:"required"` // 操作人姓名
	ParentOrderNo    string `json:"parentOrderNo"`                   // 父订单号
	IsBatch          int    `json:"isBatch"`                         // 是否批量操作的一部分
	BatchTime        int64  `json:"batchTime"`                       // 批量操作时间
	RoomId           string `json:"roomId"`                          // 包厢ID
	StorageRoomId    string `json:"storageRoomId"`                   // 存放包厢ID
	OfflineOnly      bool   `json:"offlineOnly"`                     // 是否仅线下存酒
}

// 更新酒水存放信息请求
type UpdateProductStorageReqDto struct {
	Id              string `json:"id" binding:"required"` // ID
	VenueId         string `json:"venueId"`               // 门店ID
	CustomerId      string `json:"customerId" `           // 客户ID
	CustomerName    string `json:"customerName" `         // 客户姓名
	PhoneNumber     string `json:"phoneNumber"`           // 电话号码
	ProductId       string `json:"productId" `            // 产品ID
	ProductName     string `json:"productName" `          // 产品名称
	ProductUnit     string `json:"productUnit" `          // 产品单位
	ProductSpec     string `json:"productSpec"`           // 产品规格
	Quantity        int    `json:"quantity" `             // 数量
	RemainingQty    int    `json:"remainingQty" `         // 剩余数量
	StorageLocation string `json:"storageLocation"`       // 存放位置
	StorageTime     int64  `json:"storageTime" `          // 存入时间
	ExpireTime      int64  `json:"expireTime" `           // 到期时间
	Remark          string `json:"remark"`                // 备注
	OperatorId      string `json:"operatorId"`            // 操作人ID
	OperatorName    string `json:"operatorName"`          // 操作人姓名
	RoomId          string `json:"roomId"`                // 包厢ID
	StorageRoomId   string `json:"storageRoomId"`         // 存放包厢ID
	OfflineOnly     bool   `json:"offlineOnly"`           // 是否仅线下存酒
}

// 查询酒水存放记录请求
type QueryProductStorageReqDto struct {
	Id               string `json:"id"`               // ID
	OrderNo          string `json:"orderNo"`          // 存酒单号
	ParentOrderNo    string `json:"parentOrderNo"`    // 父订单号
	VenueId          string `json:"venueId"`          // 场馆ID
	CustomerId       string `json:"customerId"`       // 客户ID
	CustomerName     string `json:"customerName"`     // 客户姓名
	PhoneNumber      string `json:"phoneNumber"`      // 电话号码
	MemberCardId     string `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	ProductId        string `json:"productId"`        // 产品ID
	ProductName      string `json:"productName"`      // 产品名称
	ProductType      string `json:"productType"`      // 产品类型
	ProductUnit      string `json:"productUnit"`      // 产品单位
	ProductSpec      string `json:"productSpec"`      // 产品规格
	StorageLocation  string `json:"storageLocation"`  // 存放位置
	OperatorId       string `json:"operatorId"`       // 操作人ID
	OperatorName     string `json:"operatorName"`     // 操作人姓名
	StorageTimeStart int64  `json:"storageTimeStart"` // 存入时间起始
	StorageTimeEnd   int64  `json:"storageTimeEnd"`   // 存入时间截止
	ExpireTimeStart  int64  `json:"expireTimeStart"`  // 到期时间起始
	ExpireTimeEnd    int64  `json:"expireTimeEnd"`    // 到期时间截止
	SearchText       string `json:"searchText"`       // 搜索文本(模糊搜索客户名或手机号)
	OnlyRemaining    bool   `json:"onlyRemaining"`    // 只查询有剩余的记录
	RoomId           string `json:"roomId"`           // 包厢ID
	StorageRoomId    int64  `json:"storageRoomId"`    // 存放包厢ID
	OfflineOnly      bool   `json:"offlineOnly"`      // 是否仅线下存酒
	PageNum          int    `json:"pageNum"`          // 页码
	PageSize         int    `json:"pageSize"`         // 页大小
}

// 删除酒水存放记录请求
type DeleteProductStorageReqDto struct {
	Id string `json:"id" binding:"required"` // ID
}

// 客户存酒统计请求
type GetCustomerStorageStatisticsReqDto struct {
	VenueId       string `json:"venueId"`       // 门店ID
	CustomerId    string `json:"customerId"`    // 客户ID
	CustomerName  string `json:"customerName"`  // 客户姓名
	PhoneNumber   string `json:"phoneNumber"`   // 电话号码
	StorageRoomId int64  `json:"storageRoomId"` // 存放包厢ID
	OfflineOnly   bool   `json:"offlineOnly"`   // 是否仅线下存酒
	PageNum       int    `json:"pageNum"`       // 页码
	PageSize      int    `json:"pageSize"`      // 每页记录数
}

// 存酒单请求
type AddProductStorageOrderReqDto struct {
	VenueId          string                  `json:"venueId" `                        // 门店ID
	CustomerId       string                  `json:"customerId"`                      // 客户ID
	CustomerName     string                  `json:"customerName"`                    // 客户姓名
	PhoneNumber      string                  `json:"phoneNumber"`                     // 电话号码
	MemberCardNumber string                  `json:"memberCardNumber,omitempty"`      // 会员卡号
	MemberCardId     string                  `json:"memberCardId,omitempty"`          // 会员卡ID
	CustomerType     string                  `json:"customerType"`                    // 客户类型
	OperatorId       string                  `json:"operatorId" binding:"required"`   // 操作人ID
	OperatorName     string                  `json:"operatorName" binding:"required"` // 操作人姓名
	StorageTime      int64                   `json:"storageTime" binding:"required"`  // 存入时间
	StorageRoomId    string                  `json:"storageRoomId"`                   // 包厢ID
	StorageRoomName  string                  `json:"storageRoomName"`                 // 存放包厢名称
	OfflineOnly      bool                    `json:"offlineOnly"`                     // 是否仅线下存酒
	Remark           string                  `json:"remark"`                          // 备注
	Items            []AddProductStorageItem `json:"items" binding:"required,dive"`   // 存酒明细项
}

// 存酒明细项
type AddProductStorageItem struct {
	ProductId       string `json:"productId" binding:"required"`   // 产品ID
	ProductName     string `json:"productName" binding:"required"` // 产品名称
	ProductType     string `json:"productType"`                    // 产品类型
	ProductUnit     string `json:"productUnit" binding:"required"` // 产品单位
	ProductSpec     string `json:"productSpec"`                    // 产品规格
	Quantity        int    `json:"quantity" binding:"required"`    // 数量
	StorageLocation string `json:"storageLocation"`                // 存放位置
	ExpireTime      int64  `json:"expireTime" binding:"required"`  // 到期时间
	Remark          string `json:"remark"`                         // 备注
	StorageRoomId   string `json:"storageRoomId"`                  // 存放包厢ID
	OfflineOnly     bool   `json:"offlineOnly"`                    // 是否仅线下存酒
}

// 查询存酒单请求
type QueryProductStorageOrderReqDto struct {
	Id               string `json:"id"`               // ID
	OrderNo          string `json:"orderNo"`          // 存酒单号
	VenueId          string `json:"venueId"`          // 场馆ID
	CustomerId       string `json:"customerId"`       // 客户ID
	CustomerName     string `json:"customerName"`     // 客户姓名
	PhoneNumber      string `json:"phoneNumber"`      // 电话号码
	OperatorId       string `json:"operatorId"`       // 操作人ID
	OperatorName     string `json:"operatorName"`     // 操作人姓名
	StorageTimeStart int64  `json:"storageTimeStart"` // 存入时间起始
	StorageTimeEnd   int64  `json:"storageTimeEnd"`   // 存入时间截止
	SearchText       string `json:"searchText"`       // 搜索文本(模糊搜索客户名或手机号)
	OnlyRemaining    bool   `json:"onlyRemaining"`    // 只查询有剩余的记录
	StorageRoomId    int64  `json:"storageRoomId"`    // 存放包厢ID
	OfflineOnly      bool   `json:"offlineOnly"`      // 是否仅线下存酒
	PageNum          int    `json:"pageNum"`          // 页码
	PageSize         int    `json:"pageSize"`         // 页大小
}

// 根据ID获取存酒单的请求
type GetProductStorageOrderReqDto struct {
	Id string `json:"id" binding:"required"` // 存酒单ID
}

// 删除存酒单的请求
type DeleteProductStorageOrderReqDto struct {
	Id string `json:"id" binding:"required"` // 存酒单ID
}

// 存酒单续存请求
type ExtendProductStorageOrderReqDto struct {
	OrderId       string `json:"orderId" binding:"required"`       // 存酒单ID
	NewExpireTime int64  `json:"newExpireTime" binding:"required"` // 新的到期时间
	Remark        string `json:"remark"`                           // 续存备注
	OperatorId    string `json:"operatorId" binding:"required"`    // 操作人ID
	OperatorName  string `json:"operatorName" binding:"required"`  // 操作人姓名
}

// 存酒报废请求
type DiscardProductStorageOrderReqDto struct {
	OrderId       string `json:"orderId" binding:"required"`       // 存酒单ID
	DiscardReason string `json:"discardReason" binding:"required"` // 报废原因
	DiscardTime   int64  `json:"discardTime" binding:"required"`   // 报废时间
	Remark        string `json:"remark"`                           // 备注
	OperatorId    string `json:"operatorId" binding:"required"`    // 操作人ID
	OperatorName  string `json:"operatorName" binding:"required"`  // 操作人姓名
}

// 查询存取酒汇总列表请求
type QueryProductStorageSummaryReqDto struct {
	VenueId       string `json:"venueId"`       // 场馆ID
	SearchText    string `json:"searchText"`    // 搜索文本(单号/手机号/姓名)
	StartTime     int64  `json:"startTime"`     // 开始时间
	EndTime       int64  `json:"endTime"`       // 结束时间
	OperatorId    string `json:"operatorId"`    // 操作人ID
	Status        string `json:"status"`        // 状态筛选(已存/部分支取/已支取/已报废)
	StorageRoomId int64  `json:"storageRoomId"` // 存放包厢ID
	OfflineOnly   bool   `json:"offlineOnly"`   // 是否仅线下存酒
	PageNum       int    `json:"pageNum"`       // 页码
	PageSize      int    `json:"pageSize"`      // 页大小
}

// 存取统计请求
type GetStorageStatisticsReqDto struct {
	VenueId         string `json:"venueId"`                   // 门店ID
	StatType        string `json:"statType,omitempty"`        // 统计类型: summary(汇总统计)、byProduct(按商品统计)
	StartTime       int64  `json:"startTime,omitempty"`       // 开始时间
	EndTime         int64  `json:"endTime,omitempty"`         // 结束时间
	ProductType     string `json:"productType,omitempty"`     // 产品类型
	StorageLocation string `json:"storageLocation,omitempty"` // 存放位置
	CustomerId      string `json:"customerId,omitempty"`      // 客户ID
	SearchText      string `json:"searchText,omitempty"`      // 搜索文本
	OnlyRemaining   bool   `json:"onlyRemaining,omitempty"`   // 仅查询剩余
	StorageRoomId   string `json:"storageRoomId,omitempty"`   // 存放包厢ID
	OfflineOnly     bool   `json:"offlineOnly,omitempty"`     // 是否仅线下存酒
	PageNum         int    `json:"pageNum,omitempty"`         // 页码
	PageSize        int    `json:"pageSize,omitempty"`        // 页大小
}

// 存酒操作请求DTO
type OperateProductStorageReqDto struct {
	Id            string `json:"id" binding:"required"`            // ID
	OperationType string `json:"operationType" binding:"required"` // extend: 续存, discard: 报废, cancel: 撤销, addItems: 添加商品, update: 更新存酒记录
	ExpireTime    int64  `json:"expireTime,omitempty"`             // 续存时需要提供新的到期时间

	OperatorId      string                  `json:"operatorId,omitempty"`      // 操作人ID
	OperatorName    string                  `json:"operatorName,omitempty"`    // 操作人姓名
	Remark          string                  `json:"remark,omitempty"`          // 备注
	OrderNo         string                  `json:"orderNo,omitempty"`         // 当操作类型为addItems时，存酒单号
	Items           []AddProductStorageItem `json:"items,omitempty"`           // 当操作类型为addItems时，存酒明细项
	Quantity        int                     `json:"quantity,omitempty"`        // 当操作类型为update时，更新的数量
	StorageLocation string                  `json:"storageLocation,omitempty"` // 当操作类型为update时，更新的存放位置
	ProductName     string                  `json:"productName,omitempty"`     // 当操作类型为update时，更新的产品名称
	ProductUnit     string                  `json:"productUnit,omitempty"`     // 当操作类型为update时，更新的产品单位
	ProductSpec     string                  `json:"productSpec,omitempty"`     // 当操作类型为update时，更新的产品规格
	StorageRoomId   string                  `json:"storageRoomId,omitempty"`   // 当操作类型为update时，更新的存放包厢ID
	OfflineOnly     bool                    `json:"offlineOnly,omitempty"`     // 当操作类型为update时，更新的是否仅线下存酒
}

// 向已有存酒单添加商品的请求DTO
type AddToExistingOrderReqDto struct {
	OrderNo      string                  `json:"orderNo" binding:"required"`      // 已有存酒单号
	OperatorId   string                  `json:"operatorId" binding:"required"`   // 操作人ID
	OperatorName string                  `json:"operatorName" binding:"required"` // 操作人姓名
	Remark       string                  `json:"remark,omitempty"`                // 备注
	Items        []AddProductStorageItem `json:"items" binding:"required,dive"`   // 存酒明细项
}

// 新的前端存酒请求DTO
type FrontendAddProductStorageReqDto struct {
	CustomerName     string                                `json:"customerName"`               // 客户姓名
	PhoneNumber      string                                `json:"phoneNumber"`                // 客户手机号
	MemberCardNumber string                                `json:"memberCardNumber,omitempty"` // 会员卡号
	MemberCardId     string                                `json:"memberCardId,omitempty"`     // 会员卡ID
	OperatorId       string                                `json:"operatorId"`                 // 操作人ID
	OperatorName     string                                `json:"operatorName"`               // 操作人姓名
	Items            []FrontendAddProductStorageItemReqDto `json:"items"`                      // 商品项
	VenueId          string                                `json:"venueId"`                    // 场馆ID
	StorageRoomId    int64                                 `json:"storageRoomId"`              // 包厢ID
	OfflineOnly      bool                                  `json:"offlineOnly"`                // 是否仅线下存酒
	GrantIp          string                                `json:"grantIp"`                    // 授权IP
	Mac              string                                `json:"mac"`                        // MAC地址
	EmployeeId       string                                `json:"employeeId"`                 // 员工ID
}

// 前端存酒项请求DTO
type FrontendAddProductStorageItemReqDto struct {
	ProductId       string `json:"productId"`       // 商品ID
	ProductType     string `json:"productType"`     // 商品类型
	StorageLocation string `json:"storageLocation"` // 存放位置
	Quantity        int    `json:"quantity"`        // 数量
	ProductUnit     string `json:"productUnit"`     // 单位
	OfflineOnly     bool   `json:"offlineOnly"`     // 是否仅线下存酒
}

// 会员存酒查询请求DTO
type QueryMemberStorageReqDto struct {
	VenueId          string `json:"venueId" binding:"required"` // 门店ID（必填）
	MemberCardId     string `json:"memberCardId"`               // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"`           // 会员卡号

	// 查询条件
	ProductName     string `json:"productName"`     // 商品名称（模糊查询）
	ProductType     string `json:"productType"`     // 商品类型
	ProductId       string `json:"productId"`       // 商品ID
	StorageLocation string `json:"storageLocation"` // 存放位置

	// 时间范围查询
	StorageTimeStart int64 `json:"storageTimeStart"` // 存入时间起始
	StorageTimeEnd   int64 `json:"storageTimeEnd"`   // 存入时间截止
	ExpireTimeStart  int64 `json:"expireTimeStart"`  // 到期时间起始
	ExpireTimeEnd    int64 `json:"expireTimeEnd"`    // 到期时间截止

	// 状态过滤
	OnlyRemaining bool `json:"onlyRemaining"` // 仅查询有剩余数量的记录
	OnlyExpiring  bool `json:"onlyExpiring"`  // 仅查询即将过期的记录（30天内）
	OnlyExpired   bool `json:"onlyExpired"`   // 仅查询已过期的记录

	// 排序和分页
	SortBy    string `json:"sortBy"`    // 排序字段：storageTime（存入时间）, expireTime（到期时间）, productName（商品名称）
	SortOrder string `json:"sortOrder"` // 排序方式：asc（升序）, desc（降序），默认desc
	PageNum   int    `json:"pageNum"`   // 页码（从1开始）
	PageSize  int    `json:"pageSize"`  // 每页大小（默认20，最大100）
}
