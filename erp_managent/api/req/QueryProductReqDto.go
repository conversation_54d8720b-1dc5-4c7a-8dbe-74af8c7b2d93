package req

type QueryProductReqDto struct {
	Id                            *string   `json:"id"`                            // ID
	Ids                           *[]string `json:"ids"`                           // ID列表
	VenueId                       *string   `json:"venueId"`                       // 所属门店ID
	Name                          *string   `json:"name"`                          // 产品名称
	Type                          *string   `json:"type"`                          // 产品类型
	CurrentPrice                  *int64    `json:"currentPrice"`                  // 当前价格
	PayPrice                      *int64    `json:"payPrice"`                      // 支付价格
	PayMark                       *string   `json:"payMark"`                       // 支付标签
	Barcode                       *string   `json:"barcode"`                       // 条形码
	AreaPrices                    *string   `json:"areaPrices"`                    // 不同区域价格
	BuyGiftPlan                   *string   `json:"buyGiftPlan"`                   // 买赠方案
	TimeSlotPrices                *string   `json:"timeSlotPrices"`                // 时段价格
	DistributionChannels          *string   `json:"distributionChannels"`          // 分销渠道
	MemberCardPaymentRestrictions *string   `json:"memberCardPaymentRestrictions"` // 会员卡支付限制
	MinimumSaleQuantity           *int      `json:"minimumSaleQuantity"`           // 最小销售数量
	IsRealPriceProduct            *bool     `json:"isRealPriceProduct"`            // 是否为实价产品
	AuxiliaryFormula              *string   `json:"auxiliaryFormula"`              // 辅助公式
	Category                      *string   `json:"category"`                      // 商品分类
	Discounts                     *string   `json:"discounts"`                     // 支持折扣
	AllowRepeatBuy                *bool     `json:"allowRepeatBuy"`                // 支持重复购买
	RecommendCombos               *string   `json:"recommendCombos"`               // 推荐搭配
	MemberCardLimits              *string   `json:"memberCardLimits"`              // 会员卡结账限制
	Flavors                       *string   `json:"flavors"`                       // 商品口味
	Ingredients                   *string   `json:"ingredients"`                   // 辅料配方
	IsDisplayed                   *bool     `json:"isDisplayed"`                   // 是否上架展示
	AllowStaffGift                *bool     `json:"allowStaffGift"`                // 支持员工赠送
	CountToMinCharge              *bool     `json:"countToMinCharge"`              // 计入低消
	CountToPerformance            *bool     `json:"countToPerformance"`            // 计算业绩
	IsPromotion                   *bool     `json:"isPromotion"`                   // 推广
	IsSoldOut                     *bool     `json:"isSoldOut"`                     // 是否洁清
	AllowWineStorage              *bool     `json:"allowWineStorage"`              // 支持存酒
	GiftVoucher                   *string   `json:"giftVoucher"`                   // 消费赠券
	CalculateInventory            *bool     `json:"calculateInventory"`            // 计算库存
	IsAreaSpecified               *bool     `json:"isAreaSpecified"`               // 指定投放区域
	SelectedAreas                 *string   `json:"selectedAreas"`                 // 指定的投放区域
	IsRoomTypeSpecified           *bool     `json:"isRoomTypeSpecified"`           // 指定投放包厢类型
	SelectedRoomTypes             *string   `json:"selectedRoomTypes"`             // 指定的投放包厢类型
	StartTime                     *string   `json:"startTime"`                     // 投放开始时间
	EndTime                       *string   `json:"endTime"`                       // 投放结束时间
	Description                   *string   `json:"description"`                   // 商品介绍
	Image                         *string   `json:"image"`                         // 商品图片
	LowStockThreshold             *int      `json:"lowStockThreshold"`             // 低库存数
	DeliveryTimeout               *int      `json:"deliveryTimeout"`               // 送达超时时间
	SupportsExternalDelivery      *bool     `json:"supportsExternalDelivery"`      // 是否支持外送
	ExternalDeliveryPrice         *int64    `json:"externalDeliveryPrice"`         // 外送价格
	Unit                          *string   `json:"unit"`                          // 单位
	PageNum                       *int      `json:"pageNum"`                       // 页码
	PageSize                      *int      `json:"pageSize"`                      // 每页记录数

	ProductTypeId        *string `json:"productTypeId"`        // 产品类型ID
	ProductPackageTypeId *string `json:"productPackageTypeId"` // 套餐类型ID
}

type V3QueryProductReqDto struct {
	Ids     *[]string `json:"ids"`     // ID列表
	VenueId *string   `json:"venueId"` // 所属门店ID
}

type V3QuerySupplyReqDto struct {
	Id *string `json:"id"` // ID
}
