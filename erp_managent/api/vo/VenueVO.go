package vo

import "voderpltvv/erp_managent/service/po"

// VenueVO 门店信息值对象
type VenueVO struct {
	Id              string `json:"id"`              // ID
	Name            string `json:"name"`            // 门店名称
	VenueType       int    `json:"venueType"`       // 门店类型
	Logo            string `json:"logo"`            // 门店logo URL
	Province        string `json:"province"`        // 省
	City            string `json:"city"`            // 市
	District        string `json:"district"`        // 区
	Address         string `json:"address"`         // 门店地址
	IsLeshuaPay     int    `json:"isLeshuaPay"`     // 是否开通乐刷支付
	Contact         string `json:"contact"`         // 联系人
	ContactPhone    string `json:"contactPhone"`    // 联系人电话
	StartHours      string `json:"startHours"`      // 营业开始时间
	EndHours        string `json:"endHours"`        // 营业结束时间
	EarlyStartHours string `json:"earlyStartHours"` // 早班开始时间
	EarlyEndHours   string `json:"earlyEndHours"`   // 早班结束时间
	NoonStartHours  string `json:"noonStartHours"`  // 午班开始时间
	NoonEndHours    string `json:"noonEndHours"`    // 午班结束时间
	LateStartHours  string `json:"lateStartHours"`  // 晚班开始时间
	LateEndHours    string `json:"lateEndHours"`    // 晚班结束时间
	Photos          string `json:"photos"`          // 门店照片URL列表
	Description     string `json:"description"`     // 门店描述
	Ctime           int64  `json:"ctime"`           // 创建时间戳
	Utime           int64  `json:"utime"`           // 更新时间戳
	State           int    `json:"state"`           // 状态值
	Version         int    `json:"version"`         // 版本号
	AppId           string `json:"appId"`           // 用于前端的appId，与加密狗绑定
	AppKey          string `json:"appKey"`          // 用于前端的appKey，与加密狗绑定
	IsThunderVOD    int    `json:"isThunderVOD"`    // 是否是雷石VOD点歌系统
	AuditStatus     int    `json:"auditStatus"`     // 审核状态：0-待审核 1-已通过 2-已拒绝
}

type VenueBusinessReportVO struct {
	VenueId   string `json:"venueId"`   // 门店ID
	VenueName string `json:"venueName"` // 门店名称
	StartHour string `json:"startHour"` // 营业开始时间
	EndHour   string `json:"endHour"`   // 营业结束时间
	StartTime int64  `json:"startTime"` // 开始时间
	EndTime   int64  `json:"endTime"`   // 结束时间

	BusinessOverview VenueBusinessReportVOBusinessOverview `json:"businessOverview"` // 营业概览

	SessionOverview VenueBusinessReportVOSessionOverview `json:"sessionOverview"` // 场次概览

	MemberOverview VenueBusinessReportVOMemberOverview `json:"memberOverview"` // 会员概览
}

type VenueBusinessReportVOBusinessOverview struct {
	TotalFee        int64 `json:"totalFee"`        // 总营收
	TotalFeeProduct int64 `json:"totalFeeProduct"` // 商品营收
	TotalFeeRoom    int64 `json:"totalFeeRoom"`    // 包房营收
}

type VenueBusinessReportVOSessionOverview struct {
	SessionCount int `json:"sessionCount"` // 场次数量
	RoomCount    int `json:"roomCount"`    // 包房数量
}

type VenueBusinessReportVOMemberOverview struct {
	NewMemberCount   int64 `json:"newMemberCount"`   // 新增会员数
	TotalMemberCount int64 `json:"totalMemberCount"` // 总会员数
	RechargeAmount   int64 `json:"rechargeAmount"`   // 充值金额
}

type VenueBusinessReportV2VO struct {
	IncomeType int `json:"incomeType"` // 收入类型：0-实收 1-净收

	VenueId   string `json:"venueId"`   // 门店ID
	VenueName string `json:"venueName"` // 门店名称
	StartHour string `json:"startHour"` // 营业开始时间
	EndHour   string `json:"endHour"`   // 营业结束时间
	StartTime int64  `json:"startTime"` // 开始时间
	EndTime   int64  `json:"endTime"`   // 结束时间

	BusinessOverview VenueBusinessReportV2VO_BusinessOverview `json:"businessOverview"` // 营业概览
	MemberOverview   VenueBusinessReportV2VO_MemberOverview   `json:"memberOverview"`   // 会员概览
	SessionOverview  VenueBusinessReportV2VO_SessionOverview  `json:"sessionOverview"`  // 场次概览
}

type VenueBusinessReportV2VO_BusinessOverview struct {
	TotalFee        int64 `json:"totalFee"`        // 总营收
	TotalFeeProduct int64 `json:"totalFeeProduct"` // 商品营收
	TotalFeeRoom    int64 `json:"totalFeeRoom"`    // 包房营收
	RechargeFee     int64 `json:"rechargeFee"`     // 充值金额

	// 净收入
	NetTotalFee        int64 `json:"netTotalFee"`        // 净营收
	NetTotalFeeProduct int64 `json:"netTotalFeeProduct"` // 净商品营收
	NetTotalFeeRoom    int64 `json:"netTotalFeeRoom"`    // 净包房营收
	NetRechargeFee     int64 `json:"netRechargeFee"`     // 净充值金额

	// 支付类型收入
	PayTypeFee map[string]int64 `json:"payTypeFee"` // 支付类型收入

	// 优惠
	DiscountFee int64 `json:"discountFee"` // 优惠金额

	// 赠送
	GiftFee int64 `json:"giftFee"` // 赠送金额
}

type VenueBusinessReportV2VO_MemberOverview struct {
	NewMemberCount   int64 `json:"newMemberCount"`   // 新增会员数
	TotalMemberCount int64 `json:"totalMemberCount"` // 总会员数

	// 会员充值
	MemberRechargePrincipalBalance   int64 `json:"memberRechargePrincipalBalance"`   // 会员充值本金余额
	MemberRechargeRoomBonusBalance   int64 `json:"memberRechargeRoomBonusBalance"`   // 会员充值房费赠金
	MemberRechargeGoodsBonusBalance  int64 `json:"memberRechargeGoodsBonusBalance"`  // 会员充值商品赠金
	MemberRechargeCommonBonusBalance int64 `json:"memberRechargeCommonBonusBalance"` // 会员充值通用赠金

	// 会员支付
	MemberPayPrincipalBalance   int64 `json:"memberPayPrincipalBalance"`   // 会员支付本金余额
	MemberPayRoomBonusBalance   int64 `json:"memberPayRoomBonusBalance"`   // 会员支付房费赠金
	MemberPayGoodsBonusBalance  int64 `json:"memberPayGoodsBonusBalance"`  // 会员支付商品赠金
	MemberPayCommonBonusBalance int64 `json:"memberPayCommonBonusBalance"` // 会员支付通用赠金
}

type VenueBusinessReportV2VO_SessionOverview struct {
	SessionCount int `json:"sessionCount"` // 场次数量
	RoomCount    int `json:"roomCount"`    // 包房数量

	EarlySessionCount int `json:"earlySessionCount"` // 早班场次数量
	NoonSessionCount  int `json:"noonSessionCount"`  // 午班场次数量
	LateSessionCount  int `json:"lateSessionCount"`  // 晚班场次数量
}

type ModelBasePayBillBO struct {
	VenueId      string
	EmployeeId   string
	StartTimeReq int64
	EndTimeReq   int64

	Venue          *po.Venue
	PayBills       []po.PayBill
	PayRecords     []po.PayRecord
	OrderAndPays   []po.OrderAndPay
	Orders         []po.Order
	OrderProducts  []po.OrderProduct
	OrderRoomPlans []po.OrderRoomPlan

	Sessions []po.Session
	Rooms    []po.Room

	MemberCardVenues    []po.MemberCardVenue
	MemberCards         []po.MemberCard
	MemberCardRecharges []po.MemberCardOperation
	MemberCardConsumes  []po.MemberCardConsume
}
