package vo

// 小程序配置后台登录结果值对象
type LoginResultVO struct {
	ERPUserVO      ERPUserVO                 `json:"erpUserVO"`      // 用户信息
	Token          string                    `json:"token"`          // token
	VenueVOs       []LoginVenueAndEmployeeVO `json:"venues"`         // 门店列表
}

// 收银台登录结果值对象
type LoginResultClientVO struct {
	ERPUserVO ERPUserVO               `json:"erpUserVO"` // 用户信息
	Token     string                  `json:"token"`     // token
	VenueVO   LoginVenueAndEmployeeVO `json:"venue"`     // 门店
}

// 指定门店的返回值
type SelectVenueVO struct {
	EmployeeVO EmployeeVO `json:"employee"` // 员工
	VenueVO    VenueVO    `json:"venue"`    // 门店
}
