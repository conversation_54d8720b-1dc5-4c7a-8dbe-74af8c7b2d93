package vo

import (
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
)

// OrderVO 订单信息值对象
type OrderVO struct {
	Id               string `json:"id"`               // ID
	VenueId          string `json:"venueId"`          // 门店ID
	RoomId           string `json:"roomId"`           // 房间ID
	EmployeeId       string `json:"employeeId"`       // 员工ID
	MemberId         string `json:"memberId"`         // 会员ID
	MemberCardId     string `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	SessionId        string `json:"sessionId"`        // 场次ID
	OrderNo          string `json:"orderNo"`          // 订单编号
	POrderNo         string `json:"pOrderNo"`         // 父订单编号-退款时原订单
	MinimumCharge    int64  `json:"minimumCharge"`    // 最低消费金额

	Type      string `json:"type"`      // 订单类型 roomplan/product
	Direction string `json:"direction"` // 方向 normal:下单、refund:退款
	MarkType  string `json:"markType"`  // 标记类型 normal:正常、cancel:取消  转台时旧订单被标记为cancel，其他均正常
	Status    string `json:"status"`    // 订单状态  unpaid:未结、paid:已结
	Tag       string `json:"tag"`       // 标签
	Mark      string `json:"mark"`      // 备注
	RefundTag string `json:"refundTag"` // 退款类型 空为正常 已付退款:paid_refund，未付退款:unpaid_refund

	ConfigRoomMemberDiscountType    int64 `json:"configRoomMemberDiscountType"`    // 配置-包厢价格会员优惠方式 0:无，1：会员价，2：会员折扣
	ConfigProductMemberDiscountType int64 `json:"configProductMemberDiscountType"` // 配置-商品价格会员优惠方式 0:无，1：会员价，2：会员折扣

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

	BillDateStr string `json:"billDateStr"` // 账单日期
	BillDate    int64  `json:"billDate"`    // 账单日期时间戳

	// 套餐信息
	PackageInfo           OrderPricePlanVO `json:"packageInfo"`
	ProductInfos          []OrderProductVO `json:"productInfos"`
	RoomVO                RoomVO           `json:"roomVO"`
	RefundOrderProductVOs []OrderProductVO `json:"refundOrderProductVOs"`
	PayBillVO             *PayBillVO       `json:"payBillVO"`

	OrderProductVOs  []OrderProductVO  `json:"orderProductVOs"`
	OrderRoomPlanVOs []OrderRoomPlanVO `json:"orderRoomPlanVOs"`
	PayResultVOs     []PayResultVO     `json:"payResultVOs"`

	HasMultiPayWay bool `json:"hasMultiPayWay"` // 是否存在多种支付方式

	RefundOrders []OrderVO `json:"refundOrders"` // 退款订单
}

type OrderExtFeeVO struct {
	OrderVO
	TotalFee int64 `json:"totalFee"` // 订单总金额
}

type OrderAndRefundVO struct {
	HasRefund bool    `json:"hasRefund"`
	OrderVO   OrderVO `json:"orderVO"`
	POrderVO  OrderVO `json:"pOrderVO"`
}

type OrderInfoGroupVO struct { // 退款接口用
	Order                 po.Order          // 原订单
	OrdersRefund          []po.Order        // 原订单-的退款订单
	OrderProductsNormal   []po.OrderProduct // 原订单-正常订单商品
	OrderProductsRefund   []po.OrderProduct // 原订单-退款订单商品
	PayBill               po.PayBill        // 原订单-收款单
	RefundPayBills        []po.PayBill      // 原订单-退款收款单
	NormalPayRecords      []po.PayRecord    // 原订单-支付记录
	RefundPayRecords      []po.PayRecord    // 原订单-退款记录
	MergedPayRecords      []po.PayRecord    // 原订单-合并的记录
	OrderProductsToRefund []po.OrderProduct // 待退款订单商品
}

type RefundOrderInfoGroupVO struct { // 退款接口用
	Order                  po.Order       // 新的退款订单
	POrder                 po.Order       // 对应-原支付订单
	POrdersRefund          []po.Order     // 对应-原支付订单-退款订单
	POrderPayBill          po.PayBill     // 对应-原支付订单-收款单
	POrderRefundPayBills   []po.PayBill   // 原订单-退款收款单
	POrderNormalPayRecords []po.PayRecord // 原订单-支付记录
	POrderRefundPayRecords []po.PayRecord // 原订单-退款记录
	POrderMergedPayRecords []po.PayRecord // 原订单-合并的记录

	POrderNomalOrderProducts  []po.OrderProduct // 对应-原支付订单-所有正常商品
	POrderRefundOrderProducts []po.OrderProduct // 原订单对应的所有退款商品
	OrderProductsRefund       []po.OrderProduct // 新生成的退款订单商品

	POrderNormalOrderRoomPlans []po.OrderRoomPlan // 对应-原支付订单-所有正常房费
	OrderRoomPlansRefund       []po.OrderRoomPlan // 新生成的退款订单房费
}

type OrderGroupUnpaidVO struct { // 转台退原房间订单接口用
	// 支付订单信息
	OrderVO               OrderVO           // 原订单
	OrderProductVOsNormal []OrderProductVO  // 正常-商品
	OrderRoomPlanVOsNomal []OrderRoomPlanVO // 正常-房费

	// 对应退款信息
	OrderVOsRefund         []OrderVO         // 原订单的退款-订单
	OrderProductVOsRefund  []OrderProductVO  // 退款-商品
	OrderRoomPlanVOsRefund []OrderRoomPlanVO // 退款-房费

	// 对退的商品房费合并
	OrderProductVOsMerged  []OrderProductVO  // 合并后的-商品
	OrderRoomPlanVOsMerged []OrderRoomPlanVO // 合并后的-房费
}

type OrderPOInfoUnionVO struct { // 公用
	Orders         []po.Order         // 订单
	OrderProducts  []po.OrderProduct  // 商品
	OrderRoomPlans []po.OrderRoomPlan // 房费
	OrderAndPays   []po.OrderAndPay   // 支付中间表
	PayBills       []po.PayBill       // 收款单
	PayRecords     []po.PayRecord     // 支付记录
}
type OrderVOInfoMergedVO struct { // 转台退原房间订单接口用
	// 以支付订单为基准的 将对应的退款作为附属
	//   - 支付/退款/合并的 商品/房费
	// 支付订单信息
	OrderVO                OrderVO           // 原订单
	NormalOrderProductVOs  []OrderProductVO  // 正常-商品
	NormalOrderRoomPlanVOs []OrderRoomPlanVO // 正常-房费

	// 对应退款信息
	RefundOrderVOs         []OrderVO         // 原订单的退款-订单
	RefundOrderProductVOs  []OrderProductVO  // 退款-商品
	RefundOrderRoomPlanVOs []OrderRoomPlanVO // 退款-房费

	// 对退的商品房费合并
	MergedOrderProductVOs  []OrderProductVO  // 合并后的-商品
	MergedOrderRoomPlanVOs []OrderRoomPlanVO // 合并后的-房费
}

type PayBillVOInfoMergeVO struct { // 转台退原房间订单接口用
	PayBillVO            PayBillVO     // 收款单
	NormalPayRecordVOs   []PayRecordVO // 支付记录
	NormalOrderAndPayVOs []OrderAndPayVO

	RefundPayBillVOs     []PayBillVO     // 退款的收款单
	RefundPayRecordVOs   []PayRecordVO   // 退款的支付记录
	RefundOrderAndPayVOs []OrderAndPayVO // 退款的支付中间表

	MergedPayRecordVOs []PayRecordVO // 正负冲退的支付记录

	OrderVOInfoMergedVOs []OrderVOInfoMergedVO
}

type PayBillPOInfoBackVO struct { // 订单还原接口用
	PayBillPO      po.PayBill       // 收款单
	PayRecordPOs   []po.PayRecord   // 支付记录
	OrderAndPayPOs []po.OrderAndPay // 支付中间表
}

type PayBillVOInfoBackVO struct { // 订单还原接口用
	PayBillVO            PayBillVO       // 收款单
	NormalPayRecordVOs   []PayRecordVO   // 支付记录
	NormalOrderAndPayVOs []OrderAndPayVO // 支付中间表

	RefundPayBillVOs   []PayBillVO   // 退款的收款单
	RefundPayRecordVOs []PayRecordVO // 退款的支付记录

	MergedPayRecordVOs []PayRecordVO // 正负冲退的支付记录
}

type OrderShiftReportVO struct {
	OrderVO          OrderVO
	TotalFee         int64
	OrderProductVOs  []OrderProductVO
	OrderRoomPlanVOs []OrderRoomPlanVO
	BillIsBack       bool
}

type PayBillShiftReportVO struct {
	PayBillVO           PayBillVO
	PayRecordVOs        []PayRecordVO
	OrderAndPayVOs      []OrderAndPayVO
	OrderShiftReportVOs []OrderShiftReportVO
}

type GiftOrderInfoVO struct {
	OrderVO          OrderVO
	TotalFee         int64
	OrderProductVOs  []OrderProductVO
	OrderRoomPlanVOs []OrderRoomPlanVO
}

type OrderPayResultVO struct {
	TmpOrderNos  []string      `json:"tmpOrderNos"`
	PayBills     []PayBillVO   `json:"payBills"`
	PayResultVOs []PayResultVO `json:"payResultVOs"`
}

type OrderPayCallbackVO struct {
	PayId               string `json:"payId"`
	Type                string `json:"type"` // common:通用 pay:支付, refund:退款
	PayCallbackModel    *model.LeshuaPayCallbackModel
	RefundCallbackModel *model.LeshuaRefundCallbackModel
}

type OrderPayQueryResultVO struct {
	PayBillVO PayBillVO `json:"payBillVO"`
}

type OrderPayCalculateResultVO struct {
	OrderVO          OrderVO           `json:"orderVO"`
	OrderProductVOs  []OrderProductVO  `json:"orderProductVOs"`
	OrderRoomPlanVOs []OrderRoomPlanVO `json:"orderRoomPlanVOs"`
	PayBillVO        PayBillVO         `json:"payBillVO"`
}

type ProductPackageInfoVO struct {
	Id    string `json:"id"`    // product.id
	Price int64  `json:"price"` // 原价
	Count int64  `json:"count"` // 数量
}

type ModeOrderInfoBaseSessionPO struct {
	Venue            po.Venue           `json:"venue"`
	Room             po.Room            `json:"room"`
	Session          po.Session         `json:"session"`
	Employee         po.Employee        `json:"employee"`
	Orders           []po.Order         `json:"orders"`
	OrderProducts    []po.OrderProduct  `json:"orderProducts"`
	OrderRoomPlans   []po.OrderRoomPlan `json:"orderRoomPlans"`
	OrderAndPays     []po.OrderAndPay   `json:"orderAndPays"`
	PayBills         []po.PayBill       `json:"payBills"`
	PayRecords       []po.PayRecord     `json:"payRecords"`
	ToUpdateOrders   []po.Order         `json:"toUpdateOrders"`
	ToUpdatePayBills []po.PayBill       `json:"toUpdatePayBills"`
	ToUpdateSessions []po.Session       `json:"toUpdateSessions"`
}

type ModeOrderInfoBaseSessionBO struct {
	VenueId                    string                     `json:"venueId"`
	RoomId                     string                     `json:"roomId"`
	SessionId                  string                     `json:"sessionId"`
	VenueVO                    VenueVO                    `json:"venueVO"`
	RoomVO                     RoomVO                     `json:"roomVO"`
	SessionVO                  SessionVO                  `json:"sessionVO"`
	OrderVOsUnpaid             []OrderVO                  `json:"orderVOsUnpaid"`
	PayBillVOs                 []PayBillVO                `json:"payBillVOs"`
	ModeOrderInfoBaseSessionPO ModeOrderInfoBaseSessionPO `json:"modeOrderInfoBaseSessionPO"`
}

type OpPackageProductInfoVO struct {
	Id        string `json:"id"`        // product.id
	Price     int64  `json:"price"`     // 原价
	Count     int64  `json:"count"`     // 数量
	PayAmount int64  `json:"payAmount"` // 实付金额
}

type ProductStockChangeVO struct {
	Id    string `json:"id"`    // product.id
	Count int64  `json:"count"` // 变化数量允许为负数
}