package vo

// InboundRecordVO 入库记录视图对象
type InboundRecordVO struct {
	Id           string                `json:"id"`           // 记录ID
	VenueId      string                `json:"venueId"`      // 门店ID
	Warehouse    string                `json:"warehouse"`    // 仓库名称
	Handler      string                `json:"handler"`      // 操作人
	Time         int64                 `json:"time"`         // 操作时间
	RecordNumber string                `json:"recordNumber"` // 单号
	Remark       string                `json:"remark"`       // 备注
	Items        []InboundRecordItemVO `json:"items"`        // 明细项
	Ctime        int64                 `json:"ctime"`        // 创建时间
	Utime        int64                 `json:"utime"`        // 更新时间
	State        int                   `json:"state"`        // 状态
	Version      int                   `json:"version"`      // 版本
}

// InboundRecordItemVO 入库记录明细项VO
type InboundRecordItemVO struct {
	Id        string  `json:"id"`        // 明细ID
	ProductId string  `json:"productId"` // 商品ID
	Quantity  int     `json:"quantity"`  // 数量
	UnitPrice float64 `json:"unitPrice"` // 单价
	Remark    string  `json:"remark"`    // 备注
}
