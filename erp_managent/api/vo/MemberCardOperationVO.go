package vo

// MemberCardOperationVO 会员卡操作记录实体
type MemberCardOperationVO struct {
	Id            string `json:"id"`            // ID
	VenueId       string `json:"venueId"`       // 门店ID
	VenueName     string `json:"venueName"`     // 门店名称
	MemberId      string `json:"memberId"`      // 会员ID
	MemberCardId  string `json:"memberCardId"`  // 会员卡ID
	CardName      string `json:"cardName"`      // 会员卡名称
	CardPhone     string `json:"cardPhone"`     // 会员卡手机号
	CardNumber    string `json:"cardNumber"`    // 会员卡号
	CardType      string `json:"cardType"`      // 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic
	CardLevel     string `json:"cardLevel"`     // 会员卡等级 - normal/gold/diamond
	CardLevelName string `json:"cardLevelName"` // 会员卡等级名称 - 普通卡/黄金卡/钻石卡
	OperationType string `json:"operationType"` // 操作类型：open_card-开卡,cancel-注销,renew-续卡,frozen-冻结,unfrozen-解冻,lost-挂失,unlost-解挂
	Balance       int64  `json:"balance"`       // 余额
	OperatorId    string `json:"operatorId"`    // 操作人ID
	OperatorName  string `json:"operatorName"`  // 操作人姓名
	SellerId      string `json:"sellerId"`      // 销售员ID
	SellerName    string `json:"sellerName"`    // 销售员姓名
	Info          string `json:"info"`          // 备注

	BillDate    int64  `json:"billDate"`    // 账单日期
	BillDateStr string `json:"billDateStr"` // 账单日期

	BillId                  string `json:"billId"`                  // 账单ID
	BillPid                 string `json:"billPid"`                 // 账单支付ID
	PayTime                 int64  `json:"payTime"`                 // 支付时间
	TotalFee                int64  `json:"totalFee"`                // 总金额
	PrincipalAmount         int64  `json:"principalAmount"`         // 本金
	MemberRoomBonusAmount   int64  `json:"memberRoomBonusAmount"`   // 房间赠金
	MemberGoodsBonusAmount  int64  `json:"memberGoodsBonusAmount"`  // 商品赠金
	MemberCommonBonusAmount int64  `json:"memberCommonBonusAmount"` // 通用赠金

	MemberCardBalancePrincipalAmount   int64 `json:"memberCardBalancePrincipalAmount"`   // 本金
	MemberCardBalanceRoomBonusAmount   int64 `json:"memberCardBalanceRoomBonusAmount"`   // 房间赠金
	MemberCardBalanceGoodsBonusAmount  int64 `json:"memberCardBalanceGoodsBonusAmount"`  // 商品赠金
	MemberCardBalanceCommonBonusAmount int64 `json:"memberCardBalanceCommonBonusAmount"` // 通用赠金

	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号
	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳

	// extend
	MemberRechargeBillVO MemberRechargeBillVO `json:"memberRechargeBillVO"` // 会员充值账单
}
