package vo

// OrderProductVO 订单产品值对象
type OrderProductVO struct {
	Id                 string `json:"id"`                 // ID
	VenueId            string `json:"venueId"`            // 门店ID
	RoomId             string `json:"roomId"`             // 房间ID
	EmployeeId         string `json:"employeeId"`         // 员工ID
	MemberId           string `json:"memberId"`           // 会员ID
	MemberCardId       string `json:"memberCardId"`       // 会员卡ID
	MemberCardNumber   string `json:"memberCardNumber"`   // 会员卡号
	SessionId          string `json:"sessionId"`          // 场次ID
	OrderNo            string `json:"orderNo"`            // 订单ID
	PId                string `json:"pId"`                // 退款对应的原始OrderProduct.Id
	PackageId          string `json:"packageId"`          // 套餐ID
	PackageProductInfo string `json:"packageProductInfo"` // 套餐商品选择信息，json格式:[{"id": "xxx1", "count": 2}, {"id": "xxx2", "count": 3}]
	ProductId          string `json:"productId"`          // 产品ID
	ProductName        string `json:"productName"`        // 产品名称
	CategoryId         string `json:"categoryId"`         // 商品或套餐分类ID
	CategoryName       string `json:"categoryName"`       // 商品或套餐分类名称
	Flavors            string `json:"flavors"`            // 口味
	Unit               string `json:"unit"`               // 单位
	Quantity           int64  `json:"quantity"`           // 数量
	OriginalPrice      int64  `json:"originalPrice"`      // 原价
	MemberPrice        int64  `json:"memberPrice"`        // 真实原价-会员价格-白金-钻石

	ProductDiscountable bool `json:"productDiscountable"` // 是否-商品折扣
	MemberDiscountable  bool `json:"memberDiscountable"`  // 是否-会员折扣
	Giftable            bool `json:"giftable"`            // 是否-可赠送
	Freeable            bool `json:"freeable"`            // 是否-可免单
	IsFreeDrinking      bool `json:"isFreeDrinking"`      // 是否畅饮
	IsMultiProductGift  bool `json:"isMultiProductGift"`  // 是否多商品赠送
	IsPriceDiff         bool `json:"isPriceDiff"`         // 是否是补差价订单
	IsGift              bool `json:"isGift"`              // 是否-已赠送
	IsFree              bool `json:"isFree"`              // 是否-已免单

	PayAmount                int64 `json:"payAmount"`                // 总金额 - 回写
	MemberDiscount           int64 `json:"memberDiscount"`           // 会员折扣
	OrderProductDiscount     int64 `json:"orderProductDiscount"`     // 点单时商品折扣-下单时-只写一次
	PayProductDiscount       int64 `json:"payProductDiscount"`       // 支付时商品折扣 - 回写
	PayProductDiscountAmount int64 `json:"payProductDiscountAmount"` // 支付时商品减免 - 回写

	Mark         string `json:"mark"`         // 产品显示备注
	InPackageTag string `json:"inPackageTag"` // 套内商品标签
	Src          string `json:"src"`          // 套餐来源

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

	// 扩展字段
	StatusInOrder   string      `json:"statusInOrder"`   // 订单状态
	PayBillVO       *PayBillVO  `json:"payBillVO"`       // 对应收款单
	PackageProducts []ProductVO `json:"packageProducts"` // 套餐内选择的商品

	// 扩展字段2
	UnitPrice    int64   `json:"unitPrice"`    // 计算后的单价
	IsDiscounted bool    `json:"isDiscounted"` // 是否已打折
	OrderVO      OrderVO `json:"orderVO"`      // 订单

	// 临时计算
	TmpCalcFlag bool  `json:"-"` // 临时计算标记
	RefundCount int64 `josn:"-"`
	RefundFee   int64 `josn:"-"`
}

// OrderProductSalesVO 订单产品销售统计值对象
type OrderProductSalesVO struct {
	VenueId      string `json:"venueId"`      // 门店ID
	EmployeeId   string `json:"employeeId"`   // 员工ID
	ProductId    string `json:"productId"`    // 产品ID
	ProductName  string `json:"productName"`  // 产品名称
	CategoryId   string `json:"categoryId"`   // 商品或套餐分类ID
	CategoryName string `json:"categoryName"` // 商品或套餐分类名称
	ShouldFee    int64  `json:"shouldFee"`    // 应付-原价格*数量
	TotalFee     int64  `json:"totalFee"`     // 总金额-实付
	Quantity     int64  `json:"quantity"`     // 数量
	IsGift       bool   `json:"isGift"`       // 是否-已赠送
}

type ModelBasePayBillVO struct {
	PayBillVOs       []PayBillVO
	OrderAndPayVOs   []OrderAndPayVO
	OrderVOs         []OrderVO
	OrderProductVOs  []OrderProductVO
	OrderRoomPlanVOs []OrderRoomPlanVO
}
