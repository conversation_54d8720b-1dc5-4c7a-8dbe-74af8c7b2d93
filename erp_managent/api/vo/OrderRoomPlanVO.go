package vo

// OrderRoomPlanVO 价格方案值对象
type OrderRoomPlanVO struct {
	Id            string `json:"id"`            // ID
	VenueId       string `json:"venueId"`       // 门店ID
	RoomId        string `json:"roomId"`        // 房间ID
	SessionId     string `json:"sessionId"`     // 场次ID
	EmployeeId    string `json:"employeeId"`    // 员工ID
	MemberId      string `json:"memberId"`      // 会员ID
	MemberCardId  string `json:"memberCardId"`  // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	OrderNo       string `json:"orderNo"`       // 订单ID
	PId           string `json:"pId"`           // 退款对应的原始OrderRoomPlan.Id
	RoomName      string `json:"roomName"`      // 房间名称
	PricePlanId   string `json:"pricePlanId"`   // 方案id
	PricePlanName string `json:"pricePlanName"` // 价格方案名称
	StartTime     int64  `json:"startTime"`     // 开始时间
	EndTime       int64  `json:"endTime"`       // 结束时间
	Duration      int    `json:"duration"`      // 买钟时长 单位：分钟 == BuyMinute

	SelectedAreaId     string `json:"selectedAreaId"`     // --选择的计费方式-套餐区域id
	SelectedRoomTypeId string `json:"selectedRoomTypeId"` // --选择的计费方式-房间类型id
	ConsumptionMode    string `json:"consumptionMode"`    // --消费模式消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	TimeChargeMode     string `json:"timeChargeMode"`     // --买钟-类型 买钟价格类型 基础价格、区域价格、节假日价格
	TimeChargeType     string `json:"timeChargeType"`     // --买钟-类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	IsTimeConsume      bool   `json:"isTimeConsume"`      // 是否是计时消费

	MemberDiscountable bool  `json:"memberDiscountable"` // 是否-会员折扣
	IsGift             bool  `json:"isGift"`             // 是否是赠送
	OriginalPayAmount  int64 `json:"originalPayAmount"`  // 原支付金额
	MinimumCharge      int64 `json:"minimumCharge"`      // 最低消费金额
	MemberPrice        int64 `json:"memberPrice"`        // 真实原价-会员价格-白金-钻石
	MemberDiscount     int64 `json:"memberDiscount"`     // 会员折扣
	IsPriceDiff        bool  `json:"isPriceDiff"`        // 是否是补差价订单
	IsFree             bool  `json:"isFree"`             // 是否-已免单

	PayAmount             int64 `json:"payAmount"`             // 房费
	PayRoomDiscount       int64 `json:"payRoomDiscount"`       // 折扣
	PayRoomDiscountAmount int64 `json:"payRoomDiscountAmount"` // 折扣减免

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

	// 扩展字段2
	UnitPrice    int64 `json:"unitPrice"`    // 计算后的单价
	IsDiscounted bool  `json:"isDiscounted"` // 是否已打折

	// 临时计算
	TmpCalcFlag bool  `json:"-"` // 临时计算标记
	RefundCount int64 `json:"-"`
	RefundFee   int64 `josn:"-"`
}
