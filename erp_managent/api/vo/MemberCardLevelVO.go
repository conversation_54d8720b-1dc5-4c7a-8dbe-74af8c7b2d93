package vo

// MemberCardLevelVO 会员卡等级值对象
type MemberCardLevelVO struct {
	Id string `json:"id"` // ID

	VenueId     string `json:"venueId"`     // 门店ID
	Name        string `json:"name"`        // 卡名称
	Level       string `json:"level"`       // 等级
	Logo        string `json:"logo"`        // logo
	Background  string `json:"background"`  // 背景
	Description string `json:"description"` // 描述

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本
}
