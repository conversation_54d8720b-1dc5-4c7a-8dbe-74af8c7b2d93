package vo

// MemberCardVO 会员卡实体
type MemberCardVO struct {
	Id       string `json:"id"`       // ID
	Name     string `json:"name"`     // 会员姓名
	Phone    string `json:"phone"`    // 会员手机号
	Birthday int64  `json:"birthday"` // 生日
	Gender   string `json:"gender"`   // 性别 male/female

	CardNumber    string `json:"cardNumber"`    // 会员卡号
	CardNumberId  int    `json:"cardNumberId"`  // 会员卡号ID-计算用6位正整数
	CardType      string `json:"cardType"`      // 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic
	CardLevelId   string `json:"cardLevelId"`   // 会员卡等级ID
	CardLevel     string `json:"cardLevel"`     // 会员卡等级 - normal/gold/diamond
	CardLevelName string `json:"cardLevelName"` // 会员卡等级名称 - 普通卡/黄金卡/钻石卡

	OperatorId   string `json:"operatorId"`   // 开卡操作人ID
	OperatorName string `json:"operatorName"` // 开卡操作人姓名
	SellerId     string `json:"sellerId"`     // 销售员ID
	SellerName   string `json:"sellerName"`   // 销售员姓名

	Points             int    `json:"points"`             // 会员积分
	PrincipalBalance   int64  `json:"principalBalance"`   // 本金余额
	RoomBonusBalance   int64  `json:"roomBonusBalance"`   // 用于房费的赠金
	GoodsBonusBalance  int64  `json:"goodsBonusBalance"`  // 用于商品的赠金
	CommonBonusBalance int64  `json:"commonBonusBalance"` // 通用赠金（都可以用的赠金）
	Status             string `json:"status"`             // 会员状态,如: 正常:normal, 挂失:lost, 冻结:frozen, 过期:expired, 注销:cancelled
	CardStartTime      int64  `json:"cardStartTime"`      // 开卡时间
	CardEndTime        int64  `json:"cardEndTime"`        // 结束时间

	Source    string `json:"source"`    // 会员来源,如: 线下实体卡, 线上小程序, 线上APP，枚举：offline_card, mini_program, app
	IsEnabled bool   `json:"isEnabled"` // 是否启用

	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳
	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号
}
