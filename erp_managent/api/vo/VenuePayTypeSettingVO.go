package vo

import "voderpltvv/erp_managent/domain/venue/model/valueobject"

// VenuePayTypeSettingVO 门店支付类型设置信息值对象
type VenuePayTypeSettingVO struct {
	Id           string                      `json:"id"`           // ID
	VenueId      string                      `json:"venueId"`      // 门店ID
	TypeInfoList []valueobject.PayTypeConfig `json:"typeInfoList"` // 支付类型信息结构化数据
	TypeInfo     string                      `json:"typeInfo"`     // 支付类型信息 json [{"payType":"wechat","label":"微信","logo":"","sort":1,"enable":true},{"payType":"alipay","label":"支付宝","logo":"","sort":2,"enable":true}]
	Remark       string                      `json:"remark"`       // 备注
	Ctime        int64                       `json:"ctime"`        // 创建时间
	Utime        int64                       `json:"utime"`        // 更新时间
	State        int                         `json:"state"`        // 状态
	Version      int                         `json:"version"`      // 版本号
}
