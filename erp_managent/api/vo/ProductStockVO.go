package vo

// ProductStockVO 商品库存视图对象
type ProductStockVO struct {
	ProductId   string `json:"productId"`   // 商品ID
	ProductName string `json:"productName"` // 商品名称
	VenueId     string `json:"venueId"`     // 门店ID
	Warehouse   string `json:"warehouse"`   // 仓库名称
	Stock       int    `json:"stock"`       // 库存数量
	Unit        string `json:"unit"`        // 单位
	UpdateTime  int64  `json:"updateTime"`  // 更新时间
}

// StockHistoryVO 库存变动历史视图对象
type StockHistoryVO struct {
	Id           string `json:"id"`           // 记录ID
	ProductId    string `json:"productId"`    // 商品ID
	VenueId      string `json:"venueId"`      // 门店ID
	Type         string `json:"type"`         // 变动类型
	Quantity     int    `json:"quantity"`     // 变动数量
	RecordNumber string `json:"recordNumber"` // 单号
	Handler      string `json:"handler"`      // 操作人
	Time         int64  `json:"time"`         // 操作时间
	Remark       string `json:"remark"`       // 备注
}
