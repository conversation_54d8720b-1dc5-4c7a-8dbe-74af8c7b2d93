package vo

type PayRecordVO struct {
	Id           string `json:"id"`           // ID
	VenueId      string `json:"venueId"`      // 门店id
	RoomId       string `json:"roomId"`       // 房间ID
	EmployeeId   string `json:"employeeId"`   // 支付员工ID-冗余-用于统计
	MemberId     string `json:"memberId"`     // 会员ID
	MemberCardId string `json:"memberCardId"` // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	SessionId    string `json:"sessionId"`    // 场次ID

	BillId                  string `json:"billId"`                  // payBill.BillId
	PayId                   string `json:"payId"`                   // 支付单ID
	PayPid                  string `json:"payPid"`                  // 只有三方支付时此处才有值
	ThirdOrderId            string `json:"thirdOrderId"`            // 第三方支付单号
	TotalFee                int64  `json:"totalFee"`                // 总金额-实际支付金额
	PrincipalAmount         int64  `json:"principalAmount"`         // 会员卡的本金
	MemberRoomBonusAmount   int64  `json:"memberRoomBonusAmount"`   // 会员卡-用于房费的赠金
	MemberGoodsBonusAmount  int64  `json:"memberGoodsBonusAmount"`  // 会员卡-用于商品的赠金
	MemberCommonBonusAmount int64  `json:"memberCommonBonusAmount"` // 会员卡-通用赠金（都可以用的赠金）
	Status                  string `json:"status"`                  // 状态 success/refund
	PayType                 string `json:"payType"`                 // 支付类型-微信 支付宝 找零 挂账
	PaySource               string `json:"paySource"`               // 支付来源-乐刷等第三方支付方式-微信/支付宝
	ProductName             string `json:"productName"`             // 商品名称
	Info                    string `json:"info"`                    // 备注
	FinishTime              int64  `json:"finishTime"`              // 完成时间
	BillDate                int64  `json:"billDate"`                // 账单日期-冗余-用于统计
	BQROneCode              string `json:"bQROneCode"`              // BShowQR支付方式的BQROneCode
	Openid                  string `json:"openid"`                  // 微信小程序支付的openid

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

	// 扩展字段
	RefundAmount int64 `json:"-"`
	TmpDelete    bool  `json:"-"`
}
