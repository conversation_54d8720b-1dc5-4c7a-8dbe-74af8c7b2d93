package vo

import (
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/service/po"
)

// ShiftReportVO 班次报告值对象
type ShiftReportVO struct {
	Id                     string `json:"id"`                     // 唯一ID
	EmployeeId             string `json:"employeeId"`             // 员工ID
	VenueId                string `json:"venueId"`                // 门店ID
	OpenedTables           int    `json:"openedTables"`           // 开台数量
	SettledTables          int    `json:"settledTables"`          // 已结算台数
	UnsettledTables        int    `json:"unsettledTables"`        // 未结算台数
	OpenedAmount           int64  `json:"openedAmount"`           // 开台金额
	SettledAmount          int64  `json:"settledAmount"`          // 已结算金额
	UnsettledAmount        int64  `json:"unsettledAmount"`        // 未结算金额
	SoldProducts           string `json:"soldProducts"`           // 销售产品列表(JSON)
	SoldProductsAmount     int64  `json:"soldProductsAmount"`     // 销售产品总金额
	GiftedProducts         string `json:"giftedProducts"`         // 赠送产品列表(JSON)
	GiftedProductsAmount   int64  `json:"giftedProductsAmount"`   // 赠送产品总金额
	ReturnedProducts       string `json:"returnedProducts"`       // 退货产品列表(JSON)
	ReturnedProductsAmount int64  `json:"returnedProductsAmount"` // 退货产品总金额
	MemberRecharge         int64  `json:"memberRecharge"`         // 会员充值金额
	MemberConsumption      int64  `json:"memberConsumption"`      // 会员消费金额
	PaymentMethodStats     string `json:"paymentMethodStats"`     // 支付方式统计(JSON)
	ShiftTime              int64  `json:"shiftTime"`              // 班次时间
	Ctime                  int64  `json:"ctime"`                  // 创建时间
	Utime                  int64  `json:"utime"`                  // 更新时间
	State                  int    `json:"state"`                  // 状态
	Version                int    `json:"version"`                // 版本
}

type ShiftReportDaily struct {
	VenueId                string                 `json:"venueId"`        // 门店ID
	EmployeeId             string                 `json:"employeeId"`     // 员工ID
	BillDate               string                 `json:"billDate"`       // 账单日期
	VenueStartHour         string                 `json:"venueStartHour"` // 门店营业开始时间
	VenueEndHour           string                 `json:"venueEndHour"`   // 门店营业结束时间
	StartTime              int64                  `json:"startTime"`
	EndTime                int64                  `json:"endTime"`
	HandTime               int64                  `json:"handTime"`               // 交班时间
	PayBillVOs             []PayBillVO            `json:"payBillVOs"`             // 收款单VO
	BusinessOverview       BusinessOverview       `json:"businessOverview"`       // 营业概况
	BusinessData           BusinessData           `json:"businessData"`           // 营业数据
	PayTypeData            map[string]int64       `json:"payTypeData"`            // 支付方式数据
	MemberCardPayData      MemberCardPayData      `json:"memberCardPayData"`      // 会员卡支付数据 消费/退款
	MemberCardRechargeData MemberCardRechargeData `json:"memberCardRechargeData"` // 会员卡充值数据 充值/退款

	// 不返回给前端
	// PayBills []po.PayBill `json:"-"` // 收款单
	// Orders   []po.Order   `json:"-"` // 订单
	// OrderVOs []OrderVO    `json:"-"` // 订单VO
	// Sessions []po.Session `json:"-"` // 会话
	PayBillShiftReportVOs        []PayBillShiftReportVO   `json:"-"` // 收款单VO
	GiftOrderInfoVOs             []GiftOrderInfoVO        `json:"-"` // 赠送订单VO
	PayMemberCardConsumes        []po.MemberCardConsume   `json:"-"` // 会员卡消费记录 消费/退款
	RechargeMemberCardOperations []po.MemberCardOperation `json:"-"` // 会员卡充值记录 充值/退款
	CurrentDayOrderVOs           []OrderVO                `json:"-"` // 当前日期的已付的订单
	CurrentDaySessionVOs         []SessionVO              `json:"-"` // 当前日期的已付的场次
}

type MemberCardPayData struct {
	PrincipalAmount   int64 `json:"principalAmount"`   // 本金支付金额
	RoomBonusAmount   int64 `json:"roomBonusAmount"`   // 房费赠金
	GoodsBonusAmount  int64 `json:"goodsBonusAmount"`  // 商品赠金
	CommonBonusAmount int64 `json:"commonBonusAmount"` // 通用赠金
}

type MemberCardRechargeData struct {
	RechargeAmount            int64 `json:"rechargeAmount"`            // 充值金额
	RechargePrincipalAmount   int64 `json:"rechargePrincipalAmount"`   // 本金支付金额
	RechargeRoomBonusAmount   int64 `json:"rechargeRoomBonusAmount"`   // 房费赠金
	RechargeGoodsBonusAmount  int64 `json:"rechargeGoodsBonusAmount"`  // 商品赠金
	RechargeCommonBonusAmount int64 `json:"rechargeCommonBonusAmount"` // 通用赠金
}
type BusinessOverview struct {
	ShouldFee         int64 `json:"shouldFee"`         // 应收
	TotalFee          int64 `json:"totalFee"`          // 实收
	NetFee            int64 `json:"netFee"`            // 净收
	RoomFee           int64 `json:"roomFee"`           // 房费
	ProductFee        int64 `json:"productFee"`        // 商品费用
	MerchantDiscount  int64 `json:"merchantDiscount"`  // 商家优惠
	MemberDiscount    int64 `json:"memberDiscount"`    // 会员优惠
	LowConsumptionFee int64 `json:"lowConsumptionFee"` // 低消差额
	EmployeeGift      int64 `json:"employeeGift"`      // 员工赠送
	ZeroFee           int64 `json:"zeroFee"`           // 抹零金额
	UnpaidFee         int64 `json:"unpaidFee"`         // 未结金额
}

type BusinessData struct {
	OpenCount        int `json:"openCount"`        // 开台数
	OpenCountPaid    int `json:"openCountPaid"`    // 已结开台数
	OpenCountUnpaid  int `json:"openCountUnpaid"`  // 未结开台数
	OrderPaidCount   int `json:"orderPaidCount"`   // 点单数
	OrderUnpaidCount int `json:"orderUnpaidCount"` // 待结订单数
	BillCount        int `json:"billCount"`        // 账单数
}

type ShiftReportBillDetailVO struct {
	EmployeeId       string           `json:"employeeId"`       // 员工ID
	VenueId          string           `json:"venueId"`          // 门店ID
	BillId           string           `json:"billId"`           // 账单ID
	PayBillVO        PayBillVO        `json:"payBillVO"`        // 支付单VO
	BusinessOverview BusinessOverview `json:"businessOverview"` // 营业概况
	PayTypeData      map[string]int64 `json:"payTypeData"`      // 支付方式数据

	// tmp
	PayRecordVOs     []PayRecordVO     `json:"-"` // 支付记录VO
	OrderVOs         []OrderVO         `json:"-"` // 订单VO
	OrderProductVOs  []OrderProductVO  `json:"-"` // 订单产品VO
	OrderRoomPlanVOs []OrderRoomPlanVO `json:"-"` // 订单房间套餐VO
}

type ModelBaseShiftReportBO struct {
	PayBillShiftReportVOs         []PayBillShiftReportVO  `json:"payBillShiftReportVOs"`         // 收款单VO
	GiftOrderVOs                  []GiftOrderInfoVO       `json:"giftOrderVOs"`                  // 赠送订单
	CurrentDayOrderVOs            []OrderVO               `json:"currentDayOrderVOs"`            // 当前日期的已付的订单
	StartHour                     string                  `json:"startHour"`                     // 开始时间
	StartTimeFinal                *int64                  `json:"startTimeFinal"`                // 开始时间
	Rooms                         *[]po.Room              `json:"rooms"`                         // 房间
	MemberCardOperationsRecharges []MemberCardOperationVO `json:"memberCardOperationsRecharges"` // 会员卡充值记录 充值/退款
	MemberCardConsumes            []MemberCardConsumeVO   `json:"memberCardConsumes"`            // 会员卡消费记录 消费/退款
	SessionIdToSessionMap         map[string]po.Session   `json:"sessionIdToSessionMap"`         // 场次ID到场次映射

	NowTime               int64                   `json:"nowTime"`               // 当前时间
	MemberRechargeBillVOs []MemberRechargeBillVO  `json:"memberRechargeBillVOs"` // 会员充值账单
	ConsumePayBillVOs     []PayBillVO             `json:"consumePayBillVOs"`     // 消费账单
	Venue                 po.Venue                `json:"venue"`                 // 门店
	Employee              po.Employee             `json:"employee"`              // 员工
	ShiftHandoverForms    *[]po.ShiftHandoverForm `json:"shiftHandoverForms"`    // 交班记录
	CurrentDaySessionVOs  []SessionVO             `json:"currentDaySessionVOs"`  // 当前日期的已付的场次
	PayTypeConfigs        []_const.PayTypeConfig  `json:"payTypeConfigs"`        // 支付方式配置
}
