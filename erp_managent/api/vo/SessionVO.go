package vo

// SessionVO 场次信息值对象
type SessionVO struct {
	Id               string `json:"id"`               // 唯一ID
	VenueId          string `json:"venueId"`          // 门店ID
	RoomId           string `json:"roomId"`           // 房间ID
	EmployeeId       string `json:"employeeId"`       // 员工ID
	SessionId        string `json:"sessionId"`        // 开台ID
	MemberId         string `json:"memberId"`         // 会员ID
	MemberCardId     string `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	StartTime        int64  `json:"startTime"`        // 开台时间
	EndTime          int64  `json:"endTime"`          // 预期结束时间
	CloseTime        int64  `json:"closeTime"`        // 实际关房时间
	Duration         int64  `json:"duration"`         // 废弃-客户端计算-使用时长
	Status           string `json:"status"`           // 状态 开台：opening， 关台：ending
	PayStatus        string `json:"payStatus"`        // 支付状态
	IsTimeConsume    bool   `json:"isTimeConsume"`    // 是否是计时消费

	MinConsume     int64 `json:"minConsume"`     // 最低消费
	RoomFee        int64 `json:"roomFee"`        // 包厢费用
	SupermarketFee int64 `json:"supermarketFee"` // 超市费用
	TotalFee       int64 `json:"totalFee"`       // 总计费用
	UnpaidAmount   int64 `json:"unpaidAmount"`   // 未付金额
	PaidAmount     int64 `json:"paidAmount"`     // 已付金额
	PrePayBalance  int64 `json:"prePayBalance"`  // 预付余额
	GiftAmount     int64 `json:"giftAmount"`     // 赠品金额

	CancelEmployeeId   string `json:"cancelEmployeeId"`   // 取消员工ID
	CancelEmployeeName string `json:"cancelEmployeeName"` // 取消员工名称
	CancelReason       string `json:"cancelReason"`       // 取消原因

	Tag  string `json:"tag"`  // tag: lock/birthday/changyin
	Info string `json:"info"` // 备注

	OrderSource        string `json:"orderSource"`        // 订单来源
	CustomerSource     string `json:"customerSource"`     // 客户来源
	CustomerTag        string `json:"customerTag"`        // 客群标签
	AgentPerson        string `json:"agentPerson"`        // 代定人
	DutyPerson         string `json:"dutyPerson"`         // 轮房人
	RankNumber         string `json:"rankNumber"`         // 排位号码
	IsOpenTableSettled bool   `json:"isOpenTableSettled"` // 是否开台立结

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

	BillDateStr string `json:"billDateStr"` // 账单日期

	// 扩展字段
	// 价格方案信息
	OrderPricePlanVO *OrderPricePlanVO `json:"orderPricePlanVO,omitempty"`

	// 房间信息
	RoomVO *RoomVO `json:"roomVO,omitempty"`

	// 支付结果
	PayResultVOs []PayResultVO `json:"payResultVOs,omitempty"`
	RtOrderNos   []string      `json:"rtOrderNos,omitempty"`
	PayBills     []PayBillVO   `json:"payBills,omitempty"`
}

type SessionOperationVO[T any] struct {
	OrderNos         []string               `json:"orderNos"`
	PayBills         []PayBillVO            `json:"payBills"`
	SessionId        string                 `json:"sessionId"`
	JSPayInfo        WechatMiniappJSPayInfo `json:"jspayInfo"`
	LshSimplePayInfo LshSimplePayInfo       `json:"lshSimplePayInfo"`
	Data             T                      `json:"data"`
}

type SessionExtBillVO struct {
	SessionVO
	EmployeeVO EmployeeVO      `json:"employeeVO,omitempty"`
	OrderVOs   []OrderExtFeeVO `json:"orderVOs,omitempty"`
	PayBillVOs []PayBillVO     `json:"payBillVOs,omitempty"`
}
