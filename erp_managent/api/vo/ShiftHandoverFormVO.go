package vo

// ShiftHandoverFormVO 班次交接单值对象
type ShiftHandoverFormVO struct {
	Id         *string `json:"id"`
	VenueId    *string `json:"venueId"`
	EmployeeId *string `json:"employeeId"` // 交班人ID
	HandTime   *int64  `json:"handTime"`   // 交班时间
	HandNo     *string `json:"handNo"`     // 交班单号

	ShouldFee         *int64 `json:"shouldFee"`         // 营业概况-应收
	TotalFee          *int64 `json:"totalFee"`          // 营业概况-实收
	NetFee            *int64 `json:"netFee"`            // 营业概况-净收
	RoomFee           *int64 `json:"roomFee"`           // 营业概况-房费
	ProductFee        *int64 `json:"productFee"`        // 营业概况-商品费用
	MerchantDiscount  *int64 `json:"merchantDiscount"`  // 营业概况-商家优惠
	MemberDiscount    *int64 `json:"memberDiscount"`    // 营业概况-会员优惠
	LowConsumptionFee *int64 `json:"lowConsumptionFee"` // 营业概况-低消差额
	EmployeeGift      *int64 `json:"employeeGift"`      // 营业概况-员工赠送
	ZeroFee           *int64 `json:"zeroFee"`           // 营业概况-抹零金额
	UnpaidFee         *int64 `json:"unpaidFee"`         // 营业概况-未结金额

	BillCount        *int `json:"billCount"`        // 营业数据-账单数
	OpenCount        *int `json:"openCount"`        // 营业数据-开台数
	OpenCountPaid    *int `json:"openCountPaid"`    // 营业数据-已结开台数
	OpenCountUnpaid  *int `json:"openCountUnpaid"`  // 营业数据-未结开台数
	OrderPaidCount   *int `json:"orderPaidCount"`   // 营业数据-点单数
	OrderUnpaidCount *int `json:"orderUnpaidCount"` // 营业数据-待结订单数

	PayTypeInfo *string `json:"payTypeInfo"` // 支付方式数据
	// Cash    *int64 `json:"cash"`    // 支付方式-现金
	// Bank    *int64 `json:"bank"`    // 支付方式-银行卡
	// Wechat  *int64 `json:"wechat"`  // 支付方式-微信
	// Alipay  *int64 `json:"alipay"`  // 支付方式-支付宝
	// Meituan *int64 `json:"meituan"` // 支付方式-美团
	// Koubei  *int64 `json:"koubei"`  // 支付方式-口碑
	// Ticket  *int64 `json:"ticket"`  // 支付方式-招待券
	// Leshua  *int64 `json:"leshua"`  // 支付方式-扫码
	// Other   *int64 `json:"other"`   // 支付方式-其他

	PrincipalAmount   *int64 `json:"principalAmount"`   // 本金金额
	RoomBonusAmount   *int64 `json:"roomBonusAmount"`   // 用于房费的赠金
	GoodsBonusAmount  *int64 `json:"goodsBonusAmount"`  // 用于商品的赠金
	CommonBonusAmount *int64 `json:"commonBonusAmount"` // 通用赠金（都可以用的赠金）

	RechargeAmount            *int64 `json:"rechargeAmount"`            // 充值金额
	RechargePrincipalAmount   *int64 `json:"rechargePrincipalAmount"`   // 充值本金金额
	RechargeRoomBonusAmount   *int64 `json:"rechargeRoomBonusAmount"`   // 充值房费赠金
	RechargeGoodsBonusAmount  *int64 `json:"rechargeGoodsBonusAmount"`  // 充值商品赠金
	RechargeCommonBonusAmount *int64 `json:"rechargeCommonBonusAmount"` // 充值通用赠金

	Operator *string `json:"operator"`
	Remark   *string `json:"remark"`
	Ctime    *int64  `json:"ctime"`   // 创建时间
	Utime    *int64  `json:"utime"`   // 更新时间
	State    *int    `json:"state"`   // 状态
	Version  *int    `json:"version"` // 版本号

	EmployeeVO   *EmployeeVO `json:"employeeVO,omitempty"`   // 交班人
	EmployeeName *string     `json:"employeeName,omitempty"` // 交班人名称
}
