package vo

// InventoryRecordVO 库存记录值对象
type InventoryRecordVO struct {
	Id           string `json:"id"`           // ID
	VenueId      string `json:"venueId"`      // 所属门店ID
	Warehouse    string `json:"warehouse"`    // 仓库名称
	Type         string `json:"type"`         // 记录类型
	Handler      string `json:"handler"`      // 操作人
	Time         int64  `json:"time"`         // 操作时间
	RecordNumber string `json:"recordNumber"` // 关联单号 (入库单号/订单号)
	Products     string `json:"products"`     // 产品信息列表
	Ctime        int64  `json:"ctime"`        // 创建时间
	Utime        int64  `json:"utime"`        // 更新时间
	State        int    `json:"state"`        // 状态
	Version      int    `json:"version"`      // 版本号
}
