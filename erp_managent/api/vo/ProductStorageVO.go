package vo

// 酒水存放记录响应
type ProductStorageVO struct {
	Id                string `json:"id"`                // ID
	OrderNo           string `json:"orderNo"`           // 存酒单号
	ParentOrderNo     string `json:"parentOrderNo"`     // 父订单号
	VenueId           string `json:"venueId"`           // 门店ID
	CustomerId        string `json:"customerId"`        // 客户ID
	CustomerName      string `json:"customerName"`      // 客户姓名
	PhoneNumber       string `json:"phoneNumber"`       // 电话号码
	MemberCardNo      string `json:"memberCardNo"`      // 会员卡号（保留原字段）
	ProductId         string `json:"productId"`         // 产品ID
	ProductName       string `json:"productName"`       // 产品名称
	ProductType       string `json:"productType"`       // 产品类型
	ProductUnit       string `json:"productUnit"`       // 产品单位
	ProductSpec       string `json:"productSpec"`       // 产品规格
	MemberCardNumber  string `json:"memberCardNumber"`  // 会员卡号
	MemberCardId      string `json:"memberCardId"`      // 会员卡ID
	Quantity          int    `json:"quantity"`          // 数量
	RemainingQty      int    `json:"remainingQty"`      // 剩余数量
	StorageLocation   string `json:"storageLocation"`   // 存放位置
	StorageRoomId     string `json:"storageRoomId"`     // 存放包厢ID
	StorageRoomName   string `json:"storageRoomName"`   // 存放包厢名称
	StorageTime       int64  `json:"storageTime"`       // 存入时间
	ExpireTime        int64  `json:"expireTime"`        // 到期时间
	Remark            string `json:"remark"`            // 备注
	StatusCode        string `json:"statusCode"`        // 状态码(stored/partial/withdrawn/discarded)
	StatusName        string `json:"statusName"`        // 状态名称(已存/部分支取/已取完/已报废)
	RemainingRatio    int    `json:"remainingRatio"`    // 剩余比例(0-100)
	DaysToExpire      int    `json:"daysToExpire"`      // 到期剩余天数
	IsExpiringSoon    bool   `json:"isExpiringSoon"`    // 是否即将到期
	IsExpired         bool   `json:"isExpired"`         // 是否已过期
	LastOperationTime int64  `json:"lastOperationTime"` // 最后操作时间
	OperatorId        string `json:"operatorId"`        // 操作人ID
	OperatorName      string `json:"operatorName"`      // 操作人姓名
	IsBatch           int    `json:"isBatch"`           // 是否批量操作的一部分
	BatchTime         int64  `json:"batchTime"`         // 批量操作时间
	OfflineOnly       bool   `json:"offlineOnly"`       // 是否仅线下存酒
	Ctime             int64  `json:"ctime"`             // 创建时间
	Utime             int64  `json:"utime"`             // 更新时间
	State             int    `json:"state"`             // 状态
	Version           int    `json:"version"`           // 版本
}

// 存酒单响应
type ProductStorageOrderVO struct {
	Id                string             `json:"id"`                // ID
	OrderNo           string             `json:"orderNo"`           // 存酒单号
	VenueId           string             `json:"venueId"`           // 门店ID
	CustomerId        string             `json:"customerId"`        // 客户ID
	CustomerName      string             `json:"customerName"`      // 客户姓名
	PhoneNumber       string             `json:"phoneNumber"`       // 电话号码
	MemberCardNo      string             `json:"memberCardNo"`      // 会员卡号
	MemberCardNumber  string             `json:"memberCardNumber"`  // 会员卡号
	MemberCardId      string             `json:"memberCardId"`      // 会员卡ID
	StorageTime       int64              `json:"storageTime"`       // 存入时间
	OperatorId        string             `json:"operatorId"`        // 操作人ID
	OperatorName      string             `json:"operatorName"`      // 操作人姓名
	TotalItems        int                `json:"totalItems"`        // 商品总项数
	TotalQuantity     int                `json:"totalQuantity"`     // 商品总数量
	RemainingQuantity int                `json:"remainingQuantity"` // 剩余总数量
	Remark            string             `json:"remark"`            // 备注
	Ctime             int64              `json:"ctime"`             // 创建时间
	Utime             int64              `json:"utime"`             // 更新时间
	State             int                `json:"state"`             // 状态
	Version           int                `json:"version"`           // 版本
	Items             []ProductStorageVO `json:"items"`             // 存酒明细
}

// 存酒单列表响应
type ProductStorageOrderListVO struct {
	Total int64                   `json:"total"` // 总数
	List  []ProductStorageOrderVO `json:"list"`  // 列表
}

// 客户信息VO
type CustomerInfoVO struct {
	CustomerId       string `json:"customerId,omitempty"`
	CustomerName     string `json:"customerName,omitempty"`
	PhoneNumber      string `json:"phoneNumber,omitempty"`
	MemberCardId     string `json:"memberCardId,omitempty"`
	MemberCardNumber string `json:"memberCardNumber,omitempty"`
}

// 存酒单与明细VO
type ProductStorageOrderWithItemsVO struct {
	OrderNo       *string            `json:"orderNo,omitempty"`
	CustomerInfo  CustomerInfoVO     `json:"customerInfo"`
	StorageTime   *int64             `json:"storageTime,omitempty"`
	TotalItems    *int64             `json:"totalItems,omitempty"`
	TotalQuantity *int64             `json:"totalQuantity,omitempty"`
	OperatorId    *string            `json:"operatorId,omitempty"`   // 操作人ID
	OperatorName  *string            `json:"operatorName,omitempty"` // 操作人姓名
	Remark        *string            `json:"remark,omitempty"`
	Items         []ProductStorageVO `json:"items"`
}

// 存放明细
type ProductStorageDetailVO struct {
	Id              string `json:"id"`              // 存放记录ID
	OrderNo         string `json:"orderNo"`         // 存酒单号
	ParentOrderNo   string `json:"parentOrderNo"`   // 父订单号
	ProductId       string `json:"productId"`       // 产品ID
	ProductName     string `json:"productName"`     // 产品名称
	ProductType     string `json:"productType"`     // 产品类型
	ProductUnit     string `json:"productUnit"`     // 产品单位
	ProductSpec     string `json:"productSpec"`     // 产品规格
	Quantity        int    `json:"quantity"`        // 存放数量
	RemainingQty    int    `json:"remainingQty"`    // 剩余数量
	StorageLocation string `json:"storageLocation"` // 存放位置
	StorageTime     int64  `json:"storageTime"`     // 存入时间
	ExpireTime      int64  `json:"expireTime"`      // 到期时间
}

// 存取酒记录汇总项
type ProductStorageSummaryVO struct {
	OrderNo        string `json:"orderNo"`        // 单号
	PhoneNumber    string `json:"phoneNumber"`    // 手机号
	CustomerName   string `json:"customerName"`   // 姓名
	ProductName    string `json:"productName"`    // 商品名称
	Quantity       int    `json:"quantity"`       // 数量
	StorageTime    int64  `json:"storageTime"`    // 存酒日期
	StorageTimeStr string `json:"storageTimeStr"` // 存酒日期字符串
	Status         string `json:"status"`         // 状态(已存/部分支取/已支取/已报废)
	LastUpdateTime int64  `json:"lastUpdateTime"` // 最后操作时间
}

// 存取酒汇总列表响应
type ProductStorageSummaryListVO struct {
	Total int64                     `json:"total"` // 总数
	List  []ProductStorageSummaryVO `json:"list"`  // 列表
}

// 存取统计响应
type StorageStatisticsVO struct {
	TotalStorage      int `json:"totalStorage"`      // 存酒总数
	TotalWithdraw     int `json:"totalWithdraw"`     // 取酒总数
	TotalRemaining    int `json:"totalRemaining"`    // 剩余总数
	TodayStorage      int `json:"todayStorage"`      // 今日存入
	TodayWithdraw     int `json:"todayWithdraw"`     // 今日取出
	CustomerCount     int `json:"customerCount"`     // 客户数量
	ExpiringSoonCount int `json:"expiringSoonCount"` // 即将过期数量(30天内)
}

// 客户存酒统计响应
type CustomerStorageStatisticsVO struct {
	CustomerId             string                    `json:"customerId"`             // 客户ID
	CustomerName           string                    `json:"customerName"`           // 客户姓名
	PhoneNumber            string                    `json:"phoneNumber"`            // 电话号码
	MemberCardId           string                    `json:"memberCardId"`           // 会员卡ID
	MemberCardNumber       string                    `json:"memberCardNumber"`       // 会员卡号
	ProductTypeCount       int                       `json:"productTypeCount"`       // 产品类型数量
	TotalStorage           int                       `json:"totalStorage"`           // 总存放数量
	TotalWithdraw          int                       `json:"totalWithdraw"`          // 总取用数量
	TotalRemainingQuantity int                       `json:"totalRemainingQuantity"` // 剩余总数量
	EarliestStorageTime    int64                     `json:"earliestStorageTime"`    // 最早存入时间
	EarliestExpireTime     int64                     `json:"earliestExpireTime"`     // 最早到期时间
	StorageDetails         []ProductStorageDetailVO  `json:"storageDetails"`         // 存放明细
	WithdrawDetails        []ProductWithdrawDetailVO `json:"withdrawDetails"`        // 取用明细
}

// 分页信息VO
type PageInfoVO struct {
	PageNum  int `json:"pageNum"`  // 当前页码
	PageSize int `json:"pageSize"` // 每页大小
}

// 存酒记录分页响应
type ProductStoragePaginatedVO struct {
	Total    int64              `json:"total"`    // 总记录数
	List     []ProductStorageVO `json:"list"`     // 记录列表
	PageInfo PageInfoVO         `json:"pageInfo"` // 分页信息
}

// 添加存酒统一返回结构
type ProductStorageAddResultVO struct {
	Success     bool             `json:"success"`               // 是否成功
	Type        string           `json:"type"`                  // 类型：single-单个商品，multiple-多个商品
	OrderInfo   interface{}      `json:"orderInfo,omitempty"`   // 存酒单信息（type=multiple时有值）
	StorageInfo ProductStorageVO `json:"storageInfo,omitempty"` // 存酒记录信息（type=single时有值）
	Message     string           `json:"message,omitempty"`     // 提示信息
}

// 商品存酒统计项
type ProductStorageItemStatVO struct {
	ProductId         string `json:"productId"`         // 商品ID
	ProductName       string `json:"productName"`       // 商品名称
	ProductType       string `json:"productType"`       // 商品类型
	StorageLocation   string `json:"storageLocation"`   // 存放仓库
	Unit              string `json:"unit"`              // 单位
	TotalQuantity     int    `json:"totalQuantity"`     // 存酒总数量
	CurrentQuantity   int    `json:"currentQuantity"`   // 现存数量
	RemainingQuantity int    `json:"remainingQuantity"` // 剩余数量
	DiscardedQuantity int    `json:"discardedQuantity"` // 报废数量
	StorageType       string `json:"storageType"`       // 存放类型(存酒、小吃等)
}

// 按商品统计的存酒列表
type ProductStorageStatListVO struct {
	Total int64                      `json:"total"` // 总数
	List  []ProductStorageItemStatVO `json:"list"`  // 按商品统计列表
	// 汇总数据
	TotalStorageQuantity   int        `json:"totalStorageQuantity"`   // 总存入数量
	TotalCurrentQuantity   int        `json:"totalCurrentQuantity"`   // 总现存数量
	TotalRemainingQuantity int        `json:"totalRemainingQuantity"` // 总剩余数量
	TotalDiscardedQuantity int        `json:"totalDiscardedQuantity"` // 总报废数量
	PageInfo               PageInfoVO `json:"pageInfo"`               // 分页信息
}

// 会员存酒查询结果VO
type MemberStorageQueryResultVO struct {
	Total      int64                 `json:"total"`      // 总记录数
	List       []MemberStorageItemVO `json:"list"`       // 存酒记录列表
	PageInfo   PageInfoVO            `json:"pageInfo"`   // 分页信息
	MemberInfo MemberBasicInfoVO     `json:"memberInfo"` // 会员基本信息
}

// 会员存酒记录项VO
type MemberStorageItemVO struct {
	Id              string `json:"id"`              // 存酒记录ID
	OrderNo         string `json:"orderNo"`         // 存酒单号
	ParentOrderNo   string `json:"parentOrderNo"`   // 父订单号
	ProductId       string `json:"productId"`       // 商品ID
	ProductName     string `json:"productName"`     // 商品名称
	ProductType     string `json:"productType"`     // 商品类型
	ProductUnit     string `json:"productUnit"`     // 商品单位
	ProductSpec     string `json:"productSpec"`     // 商品规格
	Quantity        int    `json:"quantity"`        // 存入数量
	RemainingQty    int    `json:"remainingQty"`    // 剩余数量
	WithdrawnQty    int    `json:"withdrawnQty"`    // 已取数量
	StorageLocation string `json:"storageLocation"` // 存放位置
	StorageTime     int64  `json:"storageTime"`     // 存入时间
	StorageTimeStr  string `json:"storageTimeStr"`  // 存入时间字符串
	ExpireTime      int64  `json:"expireTime"`      // 到期时间
	ExpireTimeStr   string `json:"expireTimeStr"`   // 到期时间字符串
	DaysToExpire    int    `json:"daysToExpire"`    // 距离过期天数
	ExpiringStatus  string `json:"expiringStatus"`  // 过期状态：normal, expiring, expired
	StatusCode      string `json:"statusCode"`      // 状态码
	StatusName      string `json:"statusName"`      // 状态名称
	OperatorId      string `json:"operatorId"`      // 操作员ID
	OperatorName    string `json:"operatorName"`    // 操作员姓名
	Remark          string `json:"remark"`          // 备注
}

// 会员基本信息VO
type MemberBasicInfoVO struct {
	MemberCardId     string `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	CustomerId       string `json:"customerId"`       // 客户ID
	CustomerName     string `json:"customerName"`     // 客户姓名
	PhoneNumber      string `json:"phoneNumber"`      // 电话号码
}
