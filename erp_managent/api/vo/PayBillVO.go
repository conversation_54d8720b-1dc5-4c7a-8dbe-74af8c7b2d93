package vo

import "voderpltvv/erp_managent/service/po"

// PayBillVO 付款单值对象
type PayBillVO struct {
	Id               string `json:"id"`               // ID
	VenueId          string `json:"venueId"`          // 门店id
	RoomId           string `json:"roomId"`           // 房间ID
	EmployeeId       string `json:"employeeId"`       // 员工ID-交班用
	MemberId         string `json:"memberId"`         // 会员ID
	MemberCardId     string `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	SessionId        string `json:"sessionId"`        // 场次ID
	BillId           string `json:"billId"`           // 收款单号
	BillPid          string `json:"billPid"`          // 退款单对应的收款单号
	// 营业实收+冲账实收=（营业应收+冲账应收）-(商家优惠+会员优惠+低消差额调整+预订退款差额+会员卡支付+返利金支付+挂账+冲账优惠)+抹零
	// 营业应收=营业实收+商家优惠+会员卡支付+抹零

	// 营业实收=营业应收-(商家优惠+[会员卡支付]+抹零)
	//   600.5     = 300+100+200+0.5
	OriginalFee  int64 `json:"originalFee"`  // 原始
	ShouldFee    int64 `json:"shouldFee"`    // 应收 = 原始 (优惠 = 应收 - 实收 - 抹零)
	TotalFee     int64 `json:"totalFee"`     // 实收 = 应收 - 优惠 - 抹零 = 金额对应sum(payrecords.totalfee)
	ZeroFee      int64 `json:"zeroFee"`      // 抹零
	CreditAmount int64 `json:"creditAmount"` // 挂账
	ChangeAmount int64 `json:"changeAmount"` // 现金-找零-【无用字段】

	ProductDiscount       int64  `json:"productDiscount"`       // 商品折扣
	ProductDiscountFee    int64  `json:"productDiscountFee"`    // 商品折扣金额
	RoomDiscount          int64  `json:"roomDiscount"`          // 房费折扣
	RoomDiscountFee       int64  `json:"roomDiscountFee"`       // 房费折扣金额
	IsFree                bool   `json:"isFree"`                // 是否免单
	IsGift                bool   `json:"isGift"`                // 是否赠送
	ProductDiscountAmount int64  `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    int64  `json:"roomDiscountAmount"`    // 房费减免
	DiscountType          int64  `json:"discountType"`          // 对超过低消部分打折还是全部打折
	ForceMinimumCharge    bool   `json:"forceMinimumCharge"`    // 是否强制满低消
	RefundWay             string `json:"refundWay"`             // 退款方式

	DiscountReason   string `json:"discountReason"`   // 优惠原因
	GiftEmployeeId   string `json:"giftEmployeeId"`   // 赠送员工ID
	GiftEmployeeName string `json:"giftEmployeeName"` // 赠送员工名称
	GiftReason       string `json:"giftReason"`       // 赠送原因

	CancelEmployeeId   string `json:"cancelEmployeeId"`   // 取消员工ID
	CancelEmployeeName string `json:"cancelEmployeeName"` // 取消员工名称
	CancelReason       string `json:"cancelReason"`       // 取消原因

	Direction   string `json:"direction"`   // 方向
	Status      string `json:"status"`      // 状态
	IsBack      bool   `json:"isBack"`      // 是否还原 0: 正常 1: 账单还原
	IsCancel    bool   `json:"isCancel"`    // 是否取消 0: 正常 1: 取消
	FinishTime  int64  `json:"finishTime"`  // 完成时间
	Info        string `json:"info"`        // 备注
	BillDate    int64  `json:"billDate"`    // 账单日期
	BillDateStr string `json:"billDateStr"` // 账单日期字符串

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

	// extend fields

	OverMinProductDiscountAmount int64 `json:"overMinProductDiscountAmount"` // 超过低消商品减免 - 计算字段

	OrderTag             string             `json:"orderTag"`             // 订单标签
	RoomName             string             `json:"roomName"`             // 房间名称
	RefundSum            int64              `json:"refundSum"`            // 退款金额
	OrderAndRefundVOs    []OrderAndRefundVO `json:"orderAndRefundVOs"`    // 退款订单
	OrderAndRefundVOsSum int64              `json:"orderAndRefundVOsSum"` // 退款订单金额
	OrderAndPayVOs       []OrderAndPayVO    `json:"orderAndPayVOs"`       // 订单支付中间表

	OrderVOs     []OrderVO     `json:"orderVOs"`     // 订单
	PayRecordVOs []PayRecordVO `json:"payRecordVOs"` // 支付记录

	RefundAmount     int64       `json:"-"` // 退款总金额
	RefundPayBillVOs []PayBillVO `json:"-"` // 退款支付单

	// 实收
	TmpTotalFee   int64 `json:"-"` // 总营收
	TmpRoomFee    int64 `json:"-"` // 房费
	TmpProductFee int64 `json:"-"` // 商品费

	// 净收入
	TmpNetTotalFee   int64 `json:"-"` // 净总营收
	TmpNetRoomFee    int64 `json:"-"` // 净房费
	TmpNetProductFee int64 `json:"-"` // 净商品费
}

type PayBillUnionVO struct {
	PayBillVOs   []PayBillVO   `json:"payBills"`
	PayRecordVOs []PayRecordVO `json:"payRecords"`
	EmployeeVOs  []EmployeeVO  `json:"employeeVOs"`
}

// 从billId的角度看(支付/退款) payrecord orderandpay order orderproduct orderroomplan
type PayBillInfoSrcVO struct {
	PayBillVO        PayBillVO
	PayRecordVOs     []PayRecordVO
	OrderAndPayVOs   []OrderAndPayVO
	OrderVOs         []OrderVO
	OrderProductVOs  []OrderProductVO
	OrderRoomPlanVOs []OrderRoomPlanVO
}

// 从billId的角度看(支付单-退款作附属) payrecord orderandpay order orderproduct orderroomplan
type PayBillInfoNomalVO struct {
	PayBillVO        PayBillVO
	PayRecordVOs     []PayRecordVO
	OrderAndPayVOs   []OrderAndPayVO
	OrderVOs         []OrderVO
	OrderProductVOs  []OrderProductVO
	OrderRoomPlanVOs []OrderRoomPlanVO

	PayBillRefundVOs []PayBillInfoSrcVO // 退款单附属信息
}

// 从billId的角度看 新生成的退款记录
type PayBillInfoBillBackVO struct {
	NewPayBill           po.PayBill         // 新的支付单
	NewPayRecords        []po.PayRecord     // 新的支付记录
	UpdateOrders         []po.Order         // 需要更新的订单
	UpdatePayBillParent  po.PayBill         // 需要更新的父支付单
	UpdatePayBillsRefund []po.PayBill       // 需要更新的退款支付单
	UpdateOrderProducts  []po.OrderProduct  // 需要更新的订单商品
	UpdateOrderRoomPlans []po.OrderRoomPlan // 需要更新的订单房间计划

	TmpMergedPayBillVO  PayBillVO     // 临时合并的支付单
	TmpMergedPayRecords []PayRecordVO // 临时合并的支付记录

	PayBillInfoNomalVO PayBillInfoNomalVO // 支付单附属信息
}

type PayBillExtVO struct {
	PayBillVO
	PayRecordVOs []PayRecordVO   `json:"payRecordVOs,omitempty"`
	OrderVOs     []OrderExtFeeVO `json:"orderVOs,omitempty"`
}
