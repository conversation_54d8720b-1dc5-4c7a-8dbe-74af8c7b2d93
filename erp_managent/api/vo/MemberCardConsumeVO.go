package vo

// MemberCardConsumeVO 会员卡消费记录值对象
type MemberCardConsumeVO struct {
	Id               string `json:"id"`               // ID
	VenueId          string `json:"venueId"`          // 门店ID
	VenueName        string `json:"venueName"`        // 门店名称
	RoomId           string `json:"roomId"`           // 房间ID
	RoomName         string `json:"roomName"`         // 房间名称
	EmployeeId       string `json:"employeeId"`       // 员工ID
	EmployeeName     string `json:"employeeName"`     // 员工姓名
	EmployeePhone    string `json:"employeePhone"`    // 员工电话
	MemberId         string `json:"memberId"`         // 会员ID
	MemberCardId     string `json:"memberCardId"`     // 会员卡ID
	MemberCardNumber string `json:"memberCardNumber"` // 会员卡号
	SessionId        string `json:"sessionId"`        // 场次ID

	// paybill
	BillId      string `json:"billId"`      // 收款单号
	BillPid     string `json:"billPid"`     // 退款单对应的收款单号
	OriginalFee int64  `json:"originalFee"` // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee   int64  `json:"shouldFee"`   // 应付金额 = 实付金额 + 抹零金额 - 找零金额
	TotalFee    int64  `json:"totalFee"`    // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee     int64  `json:"zeroFee"`     // 抹零金额
	Direction   string `json:"direction"`   // 方向
	Status      string `json:"status"`      // 状态
	IsBack      bool   `json:"isBack"`      // 是否还原 0: 正常 1: 账单还原

	BillDateStr string `json:"billDateStr"` // 账单日期
	BillDate    int64  `json:"billDate"`    // 账单日期

	// payrecord
	PayId                      string `json:"payId"`                      // 支付单ID
	PayPid                     string `json:"payPid"`                     //
	PayTime                    int64  `json:"payTime"`                    // 支付时间
	PayRecordTotalAmout        int64  `json:"payRecordTotalAmout"`        // 总金额-实际支付金额或会员卡的本金+3种赠金
	PayRecordPrincipalAmount   int64  `json:"payRecordPrincipalAmount"`   // 本金
	PayRecordRoomBonusAmount   int64  `json:"payRecordRoomBonusAmount"`   // 房费赠金
	PayRecordGoodsBonusAmount  int64  `json:"payRecordGoodsBonusAmount"`  // 商品赠金
	PayRecordCommonBonusAmount int64  `json:"payRecordCommonBonusAmount"` // 通用赠金
	BizType                    string `json:"bizType"`                    // 业务类型，如：消费/退款

	// membercard
	MemberCardBalance                  int64 `json:"memberCardBalance"`                  // 会员卡总余额
	MemberCardBalancePrincipalAmount   int64 `json:"memberCardBalancePrincipalAmount"`   // 本金
	MemberCardBalanceRoomBonusAmount   int64 `json:"memberCardBalanceRoomBonusAmount"`   // 房费赠金
	MemberCardBalanceGoodsBonusAmount  int64 `json:"memberCardBalanceGoodsBonusAmount"`  // 商品赠金
	MemberCardBalanceCommonBonusAmount int64 `json:"memberCardBalanceCommonBonusAmount"` // 通用赠金

	Remark string `json:"remark"` // 备注

	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳
	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号

	// extend
	PayBillVO PayBillVO `json:"payBillVO"` // 收款单VO
}

type MemberCardConsumeUnionVO struct {
	BillId    string `json:"billId"`    // 收款单号
	BillPid   string `json:"billPid"`   // 退款单对应的收款单号
	VenueId   string `json:"venueId"`   // 门店ID
	VenueName string `json:"venueName"` // 门店名称
	BizType   string `json:"bizType"`   // 业务类型，如：消费/退款/充值

	// 消费
	OriginalFee int64 `json:"originalFee"` // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee   int64 `json:"shouldFee"`   // 应付金额 = 实付金额 + 抹零金额 - 找零金额
	TotalFee    int64 `json:"totalFee"`    // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee     int64 `json:"zeroFee"`     // 抹零金额

	// 充值/消费中会员卡部分金额
	PrincipalAmount         int64 `json:"principalAmount"`         // 本金
	MemberRoomBonusAmount   int64 `json:"memberRoomBonusAmount"`   // 房间赠金
	MemberGoodsBonusAmount  int64 `json:"memberGoodsBonusAmount"`  // 商品赠金
	MemberCommonBonusAmount int64 `json:"memberCommonBonusAmount"` // 通用赠金

	PayRecordTotalAmout int64 `json:"payRecordTotalAmout"` // 总金额-实际支付金额或会员卡的本金+3种赠金
	MemberCardBalance   int64 `json:"memberCardBalance"`   // 会员卡总余额

	Direction string `json:"direction"` // 方向
	Status    string `json:"status"`    // 状态
	IsBack    bool   `json:"isBack"`    // 是否还原 0: 正常 1: 账单还原

	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳
	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号
}
