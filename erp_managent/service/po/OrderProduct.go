package po

// OrderProduct 订单产品实体
type OrderProduct struct {
	Id                 *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                            // ID
	VenueId            *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                          // 门店ID
	RoomId             *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                            // 房间ID
	EmployeeId         *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`                    // 员工ID
	MemberId           *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`                        // 会员ID
	MemberCardId       *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`              // 会员卡ID
	MemberCardNumber   *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"`      // 会员卡号
	SessionId          *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`                      // 场次ID
	OrderNo            *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`                          // 订单ID
	PId                *string `gorm:"column:p_id;type:varchar(64);default:''" json:"pId"`                                  // 退款商品对应的原始OrderProduct.Id
	PackageId          *string `gorm:"column:package_id;type:varchar(64);default:''" json:"packageId"`                      // 套餐ID
	PackageProductInfo *string `gorm:"column:package_product_info;type:varchar(4096);default:''" json:"packageProductInfo"` // 套餐商品选择信息，json格式
	ProductId          *string `gorm:"column:product_id;type:varchar(64);default:''" json:"productId"`                      // 产品ID
	ProductName        *string `gorm:"column:product_name;type:varchar(255);default:''" json:"productName"`                 // 产品名称
	CategoryId         *string `gorm:"column:category_id;type:varchar(64);default:''" json:"categoryId"`                    // 商品或套餐分类ID
	CategoryName       *string `gorm:"column:category_name;type:varchar(255);default:''" json:"categoryName"`               // 商品或套餐分类名称
	Flavors            *string `gorm:"column:flavors;type:varchar(255);default:''" json:"flavors"`                          // 口味
	Unit               *string `gorm:"column:unit;type:varchar(64);default:''" json:"unit"`                                 // 单位
	Quantity           *int64  `gorm:"column:quantity;type:int;default:0" json:"quantity"`                                  // 数量
	OriginalPrice      *int64  `gorm:"column:original_price;type:int;default:0" json:"originalPrice"`                       // 原价
	MemberPrice        *int64  `gorm:"column:member_price;type:int;default:0" json:"memberPrice"`                           // 真实原价-会员价格-白金-钻石

	ProductDiscountable *bool `gorm:"column:product_discountable;type:tinyint(1);default:0" json:"productDiscountable"` // 是否-商品折扣
	MemberDiscountable  *bool `gorm:"column:member_discountable;type:tinyint(1);default:0" json:"memberDiscountable"`   // 是否-会员折扣
	Giftable            *bool `gorm:"column:giftable;type:tinyint(1);default:0" json:"giftable"`                        // 是否-可赠送
	Freeable            *bool `gorm:"column:freeable;type:tinyint(1);default:0" json:"freeable"`                        // 是否-可免费
	IsFreeDrinking      *bool `gorm:"column:is_free_drinking;type:tinyint(1);default:0" json:"isFreeDrinking"`          // 是否畅饮
	IsMultiProductGift  *bool `gorm:"column:is_multi_product_gift;type:tinyint(1);default:0" json:"isMultiProductGift"` // 是否多商品赠送
	IsPriceDiff         *bool `gorm:"column:is_price_diff;type:tinyint(1);default:0" json:"isPriceDiff"`                // 是否是补差价订单
	IsGift              *bool `gorm:"column:is_gift;type:tinyint(1);default:0" json:"isGift"`                           // 是否是赠送
	IsFree              *bool `gorm:"column:is_free;type:tinyint(1);default:0" json:"isFree"`                           // 是否-已免单

	PayAmount                *int64 `gorm:"column:pay_amount;type:int;default:0" json:"payAmount"`                                 // 总金额-只写一次
	OrderProductDiscount     *int64 `gorm:"column:order_product_discount;type:int;default:100" json:"orderProductDiscount"`        // 点单时商品折扣-下单时-只写一次
	MemberDiscount           *int64 `gorm:"column:member_discount;type:int;default:100" json:"memberDiscount"`                     // 会员折扣-支付时用-只写一次
	PayProductDiscount       *int64 `gorm:"column:pay_product_discount;type:int;default:100" json:"payProductDiscount"`            // 支付时商品折扣-支付时用-只写一次
	PayProductDiscountAmount *int64 `gorm:"column:pay_product_discount_amount;type:int;default:0" json:"payProductDiscountAmount"` // 支付时商品减免 - 回写

	Mark         *string `gorm:"column:mark;type:varchar(255);default:''" json:"mark"`                  // 产品显示备注
	InPackageTag *string `gorm:"column:in_package_tag;type:varchar(64);default:''" json:"inPackageTag"` // 套内商品标签
	Src          *string `gorm:"column:src;type:varchar(64);default:''" json:"src"`                     // 套餐来源

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (OrderProduct) TableName() string {
	return "order_product"
}

func (o OrderProduct) GetId() string {
	return *o.Id
}
