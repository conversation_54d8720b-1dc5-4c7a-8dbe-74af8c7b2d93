package po

// MemberCardOperation 会员卡操作记录实体
type MemberCardOperation struct {
	Id            *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                // ID
	VenueId       *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`              // 门店ID
	VenueName     *string `gorm:"column:venue_name;type:varchar(64);default:''" json:"venueName"`          // 门店名称
	MemberId      *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`            // 会员ID
	MemberCardId  *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`   // 会员卡ID
	CardName      *string `gorm:"column:card_name;type:varchar(64);default:''" json:"cardName"`            // 会员卡名称
	CardPhone     *string `gorm:"column:card_phone;type:varchar(64);default:''" json:"cardPhone"`          // 会员卡手机号
	CardNumber    *string `gorm:"column:card_number;type:varchar(64);default:''" json:"cardNumber"`        // 会员卡号
	CardType      *string `gorm:"column:card_type;type:varchar(64);default:''" json:"cardType"`            // 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic
	CardLevel     *string `gorm:"column:card_level;type:varchar(64);default:''" json:"cardLevel"`          // 会员卡等级 - normal/gold/diamond
	CardLevelName *string `gorm:"column:card_level_name;type:varchar(64);default:''" json:"cardLevelName"` // 会员卡等级名称 - 普通卡/黄金卡/钻石卡
	OperationType *string `gorm:"column:operation_type;type:varchar(64);default:''" json:"operationType"`  // 操作类型：open_card-开卡,cancel-注销,renew-续卡,frozen-冻结,unfrozen-解冻,lost-挂失,unlost-解挂
	Balance       *int64  `gorm:"column:balance;type:bigint;default:0" json:"balance"`                     // 余额
	OperatorId    *string `gorm:"column:operator_id;type:varchar(64);default:''" json:"operatorId"`        // 操作人ID
	OperatorName  *string `gorm:"column:operator_name;type:varchar(64);default:''" json:"operatorName"`    // 操作人姓名
	SellerId      *string `gorm:"column:seller_id;type:varchar(64);default:''" json:"sellerId"`            // 销售员ID
	SellerName    *string `gorm:"column:seller_name;type:varchar(64);default:''" json:"sellerName"`        // 销售员姓名
	Info          *string `gorm:"column:info;type:varchar(255);default:''" json:"info"`                    // 备注

	BillId                  *string `gorm:"column:bill_id;type:varchar(64);default:''" json:"billId"`                               // 账单ID
	BillPid                 *string `gorm:"column:bill_pid;type:varchar(64);default:''" json:"billPid"`                             // 账单支付ID
	TotalFee                *int64  `gorm:"column:total_fee;type:bigint;default:0" json:"totalFee"`                                 // 总金额
	PrincipalAmount         *int64  `gorm:"column:principal_amount;type:bigint;default:0" json:"principalAmount"`                   // 本金
	MemberRoomBonusAmount   *int64  `gorm:"column:member_room_bonus_amount;type:bigint;default:0" json:"memberRoomBonusAmount"`     // 房间赠金
	MemberGoodsBonusAmount  *int64  `gorm:"column:member_goods_bonus_amount;type:bigint;default:0" json:"memberGoodsBonusAmount"`   // 商品赠金
	MemberCommonBonusAmount *int64  `gorm:"column:member_common_bonus_amount;type:bigint;default:0" json:"memberCommonBonusAmount"` // 通用赠金

	MemberCardBalancePrincipalAmount   *int64 `gorm:"column:member_card_balance_principal_amount;type:bigint;default:0" json:"memberCardBalancePrincipalAmount"`      // 本金
	MemberCardBalanceRoomBonusAmount   *int64 `gorm:"column:member_card_balance_room_bonus_amount;type:bigint;default:0" json:"memberCardBalanceRoomBonusAmount"`     // 房间赠金
	MemberCardBalanceGoodsBonusAmount  *int64 `gorm:"column:member_card_balance_goods_bonus_amount;type:bigint;default:0" json:"memberCardBalanceGoodsBonusAmount"`   // 商品赠金
	MemberCardBalanceCommonBonusAmount *int64 `gorm:"column:member_card_balance_common_bonus_amount;type:bigint;default:0" json:"memberCardBalanceCommonBonusAmount"` // 通用赠金

	PayTime *int64 `gorm:"column:pay_time;type:int;default:0" json:"payTime"` // 支付时间

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间戳
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间戳
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态值
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (MemberCardOperation) TableName() string {
	return "member_card_operation"
}

func (m MemberCardOperation) GetId() string {
	return *m.Id
}
