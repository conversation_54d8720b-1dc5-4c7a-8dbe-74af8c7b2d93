package po

// ShiftHandoverForm 班次交接单实体
type ShiftHandoverForm struct {
	Id         *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`
	VenueId    *string `gorm:"column:venue_id;type:varchar(64);index" json:"venueId"`
	EmployeeId *string `gorm:"column:employee_id;type:varchar(64);index" json:"employeeId"` // 交班人ID
	HandTime   *int64  `gorm:"column:hand_time;type:bigint" json:"handTime"`                // 交班时间
	HandNo     *string `gorm:"column:hand_no;type:varchar(64)" json:"handNo"`               // 交班单号

	BillDate     *string `gorm:"column:bill_date;type:varchar(64)" json:"billDate"`          // 账单日期
	VenueEndHour *string `gorm:"column:venue_end_hour;type:varchar(64)" json:"venueEndHour"` // 门店营业结束时间
	StartTime    *int64  `gorm:"column:start_time;type:bigint" json:"startTime"`             // 营业开始时间
	EndTime      *int64  `gorm:"column:end_time;type:bigint" json:"endTime"`                 // 营业结束时间

	ShouldFee         *int64 `gorm:"column:should_fee;type:bigint" json:"shouldFee"`                  // 营业概况-应收
	TotalFee          *int64 `gorm:"column:total_fee;type:bigint" json:"totalFee"`                    // 营业概况-实收
	NetFee            *int64 `gorm:"column:net_fee;type:bigint" json:"netFee"`                        // 营业概况-净收
	RoomFee           *int64 `gorm:"column:room_fee;type:bigint" json:"roomFee"`                      // 营业概况-房费
	ProductFee        *int64 `gorm:"column:product_fee;type:bigint" json:"productFee"`                // 营业概况-商品费用
	MerchantDiscount  *int64 `gorm:"column:merchant_discount;type:bigint" json:"merchantDiscount"`    // 营业概况-商家优惠
	MemberDiscount    *int64 `gorm:"column:member_discount;type:bigint" json:"memberDiscount"`        // 营业概况-会员优惠
	LowConsumptionFee *int64 `gorm:"column:low_consumption_fee;type:bigint" json:"lowConsumptionFee"` // 营业概况-低消差额
	EmployeeGift      *int64 `gorm:"column:employee_gift;type:bigint" json:"employeeGift"`            // 营业概况-员工赠送
	ZeroFee           *int64 `gorm:"column:zero_fee;type:bigint" json:"zeroFee"`                      // 营业概况-抹零金额
	UnpaidFee         *int64 `gorm:"column:unpaid_fee;type:bigint" json:"unpaidFee"`                  // 营业概况-未结金额

	BillCount        *int `gorm:"column:bill_count;type:int" json:"billCount"`                // 营业数据-账单数
	OpenCount        *int `gorm:"column:open_count;type:int" json:"openCount"`                // 营业数据-开台数
	OpenCountPaid    *int `gorm:"column:open_count_paid;type:int" json:"openCountPaid"`       // 营业数据-已结开台数
	OpenCountUnpaid  *int `gorm:"column:open_count_unpaid;type:int" json:"openCountUnpaid"`   // 营业数据-未结开台数
	OrderPaidCount   *int `gorm:"column:order_paid_count;type:int" json:"orderPaidCount"`     // 营业数据-点单数
	OrderUnpaidCount *int `gorm:"column:order_unpaid_count;type:int" json:"orderUnpaidCount"` // 营业数据-待结订单数

	PayTypeInfo *string `gorm:"column:pay_type_info;type:varchar(2048)" json:"payTypeInfo"` // 支付方式数据
	// Cash        *int64  `gorm:"column:cash;type:bigint" json:"cash"`                        // 支付方式-现金
	// Bank        *int64  `gorm:"column:bank;type:bigint" json:"bank"`                        // 支付方式-银行卡
	// Wechat      *int64  `gorm:"column:wechat;type:bigint" json:"wechat"`                    // 支付方式-微信
	// Alipay      *int64  `gorm:"column:alipay;type:bigint" json:"alipay"`                    // 支付方式-支付宝
	// Meituan     *int64  `gorm:"column:meituan;type:bigint" json:"meituan"`                  // 支付方式-美团
	// Koubei      *int64  `gorm:"column:koubei;type:bigint" json:"koubei"`                    // 支付方式-口碑
	// Ticket      *int64  `gorm:"column:ticket;type:bigint" json:"ticket"`                    // 支付方式-招待券
	// Leshua      *int64  `gorm:"column:leshua;type:bigint" json:"leshua"`                    // 支付方式-扫码
	// Other       *int64  `gorm:"column:other;type:bigint" json:"other"`                      // 支付方式-其他

	PrincipalAmount   *int64 `gorm:"column:principal_amount;type:bigint" json:"principalAmount"`                // 本金金额
	RoomBonusAmount   *int64 `gorm:"column:room_bonus_amount;type:bigint;default:0" json:"roomBonusAmount"`     // 用于房费的赠金
	GoodsBonusAmount  *int64 `gorm:"column:goods_bonus_amount;type:bigint;default:0" json:"goodsBonusAmount"`   // 用于商品的赠金
	CommonBonusAmount *int64 `gorm:"column:common_bonus_amount;type:bigint;default:0" json:"commonBonusAmount"` // 通用赠金（都可以用的赠金）

	RechargeAmount            *int64 `gorm:"column:recharge_amount;type:bigint" json:"rechargeAmount"`                         // 充值金额
	RechargePrincipalAmount   *int64 `gorm:"column:recharge_principal_amount;type:bigint" json:"rechargePrincipalAmount"`      // 充值本金金额
	RechargeRoomBonusAmount   *int64 `gorm:"column:recharge_room_bonus_amount;type:bigint" json:"rechargeRoomBonusAmount"`     // 充值房费赠金
	RechargeGoodsBonusAmount  *int64 `gorm:"column:recharge_goods_bonus_amount;type:bigint" json:"rechargeGoodsBonusAmount"`   // 充值商品赠金
	RechargeCommonBonusAmount *int64 `gorm:"column:recharge_common_bonus_amount;type:bigint" json:"rechargeCommonBonusAmount"` // 充值通用赠金

	Operator *string `gorm:"column:operator;type:varchar(64)" json:"operator"`
	Remark   *string `gorm:"column:remark;type:varchar(255)" json:"remark"`
	Ctime    *int64  `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime    *int64  `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State    *int    `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version  *int    `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

func (ShiftHandoverForm) TableName() string {
	return "shift_handover_form"
}

func (s ShiftHandoverForm) GetId() string {
	return *s.Id
}
