package po

// OrderRoomPlan 价格方案实体类
type OrderRoomPlan struct {
	Id            *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                 // ID
	VenueId       *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`               // 门店ID
	RoomId        *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                 // 房间ID
	SessionId     *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`           // 场次ID
	EmployeeId    *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`         // 员工ID
	MemberId      *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`             // 会员ID
	MemberCardId  *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`     // 会员卡ID
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	OrderNo       *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`               // 订单ID
	PId           *string `gorm:"column:p_id;type:varchar(64);default:''" json:"pId"`                       // 退款对应的原始OrderRoomPlan.Id
	RoomName      *string `gorm:"column:room_name;type:varchar(128);default:''" json:"roomName"`            // 房间名称
	PricePlanId   *string `gorm:"column:price_plan_id;type:varchar(64);default:''" json:"pricePlanId"`      // 方案id
	PricePlanName *string `gorm:"column:price_plan_name;type:varchar(128);default:''" json:"pricePlanName"` // 价格方案名称
	StartTime     *int64  `gorm:"column:start_time;type:int;default:0" json:"startTime"`                    // 开始时间
	EndTime       *int64  `gorm:"column:end_time;type:int;default:0" json:"endTime"`                        // 结束时间
	Duration      *int    `gorm:"column:duration;type:int;default:0" json:"duration"`                       // 买钟时长 单位：分钟 == BuyMinute
	IsTimeConsume *bool   `gorm:"column:is_time_consume;type:tinyint(1);default:0" json:"isTimeConsume"`    // 是否是计时消费

	SelectedAreaId     *string `gorm:"column:selected_area_id;type:varchar(64);default:''" json:"selectedAreaId"`          // --选择的计费方式-套餐区域id
	SelectedRoomTypeId *string `gorm:"column:selected_room_type_id;type:varchar(64);default:''" json:"selectedRoomTypeId"` // --选择的计费方式-房间类型id
	ConsumptionMode    *string `gorm:"column:consumption_mode;type:varchar(32);default:''" json:"consumptionMode"`         // --消费模式消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	TimeChargeMode     *string `gorm:"column:time_charge_mode;type:varchar(32);default:''" json:"timeChargeMode"`          // --买钟-类型 买钟价格类型 基础价格、区域价格、节假日价格
	TimeChargeType     *string `gorm:"column:time_charge_type;type:varchar(32);default:''" json:"timeChargeType"`          // --买钟-类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时

	MemberDiscountable *bool  `gorm:"column:member_discountable;type:tinyint(1);default:0" json:"memberDiscountable"` // 是否-会员折扣
	IsGift             *bool  `gorm:"column:is_gift;type:tinyint(1);default:0" json:"isGift"`                         // 是否是赠送
	OriginalPayAmount  *int64 `gorm:"column:original_pay_amount;type:int;default:0" json:"originalPayAmount"`         // 原支付金额
	MinimumCharge      *int64 `gorm:"column:minimum_charge;type:int;default:0" json:"minimumCharge"`                  // 最低消费金额
	MemberPrice        *int64 `gorm:"column:member_price;type:int;not null" json:"memberPrice"`                       // 真实原价-会员价格-白金-钻石
	MemberDiscount     *int64 `gorm:"column:member_discount;type:int;not null" json:"memberDiscount"`                 // 会员折扣
	IsPriceDiff        *bool  `gorm:"column:is_price_diff;type:tinyint(1);default:0" json:"isPriceDiff"`              // 是否是补差价订单
	IsFree             *bool  `gorm:"column:is_free;type:tinyint(1);default:0" json:"isFree"`                         // 是否-已免单

	PayAmount             *int64 `gorm:"column:pay_amount;type:int;not null" json:"payAmount"`                           // 房费
	PayRoomDiscount       *int64 `gorm:"column:pay_room_discount;type:int;not null" json:"payRoomDiscount"`              // 折扣
	PayRoomDiscountAmount *int64 `gorm:"column:pay_room_discount_amount;type:int;not null" json:"payRoomDiscountAmount"` // 折扣减免

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (OrderRoomPlan) TableName() string {
	return "order_room_plan"
}

func (o OrderRoomPlan) GetId() string {
	return *o.Id
}
