package po

// InventoryRecord 库存记录实体
type InventoryRecord struct {
	Id               *string  `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                        // ID
	VenueId          *string  `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                      // 所属门店ID
	Warehouse        *string  `gorm:"column:warehouse;type:varchar(64);default:''" json:"warehouse"`                   // 仓库名称
	Type             *string  `gorm:"column:type;type:varchar(64);default:''" json:"type"`                             // 记录类型, INBOUND, OUTBOUND, INBOUND_REVERSE, CONSUMPTION
	Handler          *string  `gorm:"column:handler;type:varchar(64);default:''" json:"handler"`                       // 操作人
	Time             *int64   `gorm:"column:time;type:int;default:0" json:"time"`                                      // 操作时间
	RecordNumber     *string  `gorm:"column:record_number;type:varchar(64);default:''" json:"recordNumber"`            // 关联单号 (入库单号/订单号)
	OriginalRecordId *string  `gorm:"column:original_record_id;type:varchar(64);default:null" json:"originalRecordId"` // 关联的原始单据ID (用于冲销)
	TotalAmount      *float64 `gorm:"column:total_amount;type:decimal(10,2)" json:"totalAmount"`                       // 总金额
	Remark           *string  `gorm:"column:remark;type:varchar(255)" json:"remark"`                                   // 备注
	Ctime            *int64   `gorm:"column:ctime;type:int;default:0" json:"ctime"`                                    // 创建时间
	Utime            *int64   `gorm:"column:utime;type:int;default:0" json:"utime"`                                    // 更新时间
	State            *int     `gorm:"column:state;type:int;default:0" json:"state"`                                    // 状态
	Version          *int     `gorm:"column:version;type:int;default:0" json:"version"`                                // 版本号
}

// TableName 设置表名
func (InventoryRecord) TableName() string {
	return "inventory_record"
}

func (i InventoryRecord) GetId() string {
	return *i.Id
}
