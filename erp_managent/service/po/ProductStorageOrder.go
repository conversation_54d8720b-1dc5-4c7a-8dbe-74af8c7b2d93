package po

// ProductStorageOrder 存酒单主表
type ProductStorageOrder struct {
	Id                *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                      // ID
	OrderNo           *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`                    // 存酒单号
	VenueId           *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                    // 门店ID
	CustomerId        *string `gorm:"column:customer_id;type:varchar(64);default:''" json:"customerId"`              // 客户ID
	CustomerName      *string `gorm:"column:customer_name;type:varchar(64);default:''" json:"customerName"`          // 客户姓名
	PhoneNumber       *string `gorm:"column:phone_number;type:varchar(64);default:''" json:"phoneNumber"`            // 电话号码
	MemberCardNo      *string `gorm:"column:member_card_no;type:varchar(64);default:''" json:"memberCardNo"`         // 会员卡号（保留原字段）
	MemberCardNumber  *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	MemberCardId      *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`         // 会员卡ID
	StorageTime       *int64  `gorm:"column:storage_time;type:int;default:0" json:"storageTime"`                     // 存入时间
	TotalItems        *int    `gorm:"column:total_items;type:int;default:0" json:"totalItems"`                       // 商品总项数
	TotalQuantity     *int    `gorm:"column:total_quantity;type:int;default:0" json:"totalQuantity"`                 // 商品总数量
	RemainingQuantity *int    `gorm:"column:remaining_quantity;type:int;default:0" json:"remainingQuantity"`         // 剩余总数量
	Remark            *string `gorm:"column:remark;type:varchar(255);default:''" json:"remark"`                      // 备注
	StorageRoomId     *string `gorm:"column:storage_room_id;type:varchar(64);default:''" json:"storageRoomId"`       // 寄存包厢ID
	StorageRoomName   *string `gorm:"column:storage_room_name;type:varchar(64);default:''" json:"storageRoomName"`   // 寄存包厢名称
	OfflineOnly       *int    `gorm:"column:offline_only;type:int;default:0" json:"offlineOnly"`                     // 仅线下存酒标记：0-否，1-是
	OperatorId        *string `gorm:"column:operator_id;type:varchar(64);default:''" json:"operatorId"`              // 操作人ID
	OperatorName      *string `gorm:"column:operator_name;type:varchar(64);default:''" json:"operatorName"`          // 操作人姓名
	Ctime             *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                                  // 创建时间
	Utime             *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                                  // 更新时间
	State             *int    `gorm:"column:state;type:int;default:0" json:"state"`                                  // 状态
	Version           *int    `gorm:"column:version;type:int;default:0" json:"version"`                              // 版本
}

// 该表作用：记录客户存酒单主表
func (ProductStorageOrder) TableName() string {
	return "product_storage_order"
}

func (p ProductStorageOrder) GetId() string {
	return *p.Id
}
