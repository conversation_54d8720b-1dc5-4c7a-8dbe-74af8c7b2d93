package po

// PayBill 付款单实体
type PayBill struct {
	Id           *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`              // ID
	VenueId      *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`            // 门店id
	RoomId       *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`              // 房间ID
	EmployeeId   *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`      // 员工ID-交班用
	MemberId     *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`          // 会员ID
	MemberCardId *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"` // 会员卡ID
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	SessionId    *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`        // 场次ID

	BillId  *string `gorm:"column:bill_id;type:varchar(64);default:''" json:"billId"`   // 收款单号
	BillPid *string `gorm:"column:bill_pid;type:varchar(64);default:''" json:"billPid"` // 退款单对应的收款单号

	OriginalFee  *int64 `gorm:"column:original_fee;type:int;default:0" json:"originalFee"`   // 原始
	ShouldFee    *int64 `gorm:"column:should_fee;type:int;default:0" json:"shouldFee"`       // 应收 = 原始 (优惠 = 应收 - 实收 - 抹零)
	TotalFee     *int64 `gorm:"column:total_fee;type:int;default:0" json:"totalFee"`         // 实收 = 应收 - 优惠 - 抹零 = 金额对应sum(payrecords.totalfee)
	ZeroFee      *int64 `gorm:"column:zero_fee;type:int;default:0" json:"zeroFee"`           // 抹零
	CreditAmount *int64 `gorm:"column:credit_amount;type:int;default:0" json:"creditAmount"` // 挂账
	ChangeAmount *int64 `gorm:"column:change_amount;type:int;default:0" json:"changeAmount"` // 现金-找零-【无用字段】

	ProductDiscount       *int64  `gorm:"column:product_discount;type:int;default:100" json:"productDiscount"`              // 商品折扣
	ProductDiscountFee    *int64  `gorm:"column:product_discount_fee;type:int;default:0" json:"productDiscountFee"`         // 商品折扣金额
	RoomDiscount          *int64  `gorm:"column:room_discount;type:int;default:0" json:"roomDiscount"`                      // 房费折扣
	RoomDiscountFee       *int64  `gorm:"column:room_discount_fee;type:int;default:0" json:"roomDiscountFee"`               // 房费折扣金额
	IsFree                *bool   `gorm:"column:is_free;type:int(1);default:0" json:"isFree"`                               // 是否免单
	IsGift                *bool   `gorm:"column:is_gift;type:int(1);default:0" json:"isGift"`                               // 是否赠送
	ProductDiscountAmount *int64  `gorm:"column:product_discount_amount;type:int;default:100" json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    *int64  `gorm:"column:room_discount_amount;type:int;default:0" json:"roomDiscountAmount"`         // 房费减免
	DiscountType          *int64  `gorm:"column:discount_type;type:int;default:0" json:"discountType"`                      // 对超过低消部分打折还是全部打折
	ForceMinimumCharge    *bool   `gorm:"column:force_minimum_charge;type:int(1);default:0" json:"forceMinimumCharge"`      // 是否强制满低消
	RefundWay             *string `gorm:"column:refund_way;type:varchar(64);default:''" json:"refundWay"`                   // 退款方式

	DiscountReason   *string `gorm:"column:discount_reason;type:varchar(512);default:''" json:"discountReason"`     // 优惠原因
	GiftEmployeeId   *string `gorm:"column:gift_employee_id;type:varchar(64);default:''" json:"giftEmployeeId"`     // 赠送员工ID
	GiftEmployeeName *string `gorm:"column:gift_employee_name;type:varchar(64);default:''" json:"giftEmployeeName"` // 赠送员工名称
	GiftReason       *string `gorm:"column:gift_reason;type:varchar(512);default:''" json:"giftReason"`             // 赠送原因

	CancelEmployeeId   *string `gorm:"column:cancel_employee_id;type:varchar(64);default:''" json:"cancelEmployeeId"`     // 取消员工ID
	CancelEmployeeName *string `gorm:"column:cancel_employee_name;type:varchar(64);default:''" json:"cancelEmployeeName"` // 取消员工名称
	CancelReason       *string `gorm:"column:cancel_reason;type:varchar(512);default:''" json:"cancelReason"`             // 取消原因

	Direction  *string `gorm:"column:direction;type:varchar(64);default:''" json:"direction"` // 方向
	Status     *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`       // 状态
	IsBack     *bool   `gorm:"column:is_back;type:int(1);default:0" json:"isBack"`            // 是否还原 0: 正常 1: 账单还原
	IsCancel   *bool   `gorm:"column:is_cancel;type:int(1);default:0" json:"isCancel"`        // 是否取消 0: 正常 1: 取消
	Info       *string `gorm:"column:info;type:varchar(512);default:''" json:"info"`          // 备注
	FinishTime *int64  `gorm:"column:finish_time;type:bigint;default:0" json:"finishTime"`    // 完成时间
	BillDate   *int64  `gorm:"column:bill_date;type:bigint;default:0" json:"billDate"`        // 账单日期

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (PayBill) TableName() string {
	return "pay_bill"
}

func (p PayBill) GetId() string {
	return *p.Id
}
