package po

// PayRecord 付款记录实体
type PayRecord struct {
	Id           *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`              // ID
	VenueId      *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`            // 门店id
	RoomId       *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`              // 房间ID
	EmployeeId   *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`      // 支付员工ID-冗余-用于统计
	MemberId     *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`          // 会员ID
	MemberCardId *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"` // 会员卡ID
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	SessionId    *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`        // 场次ID

	BillId       *string `gorm:"column:bill_id;type:varchar(64);default:''" json:"billId"`              // payBill.BillId
	PayId        *string `gorm:"column:pay_id;type:varchar(64);default:''" json:"payId"`                // 支付单ID
	PayPid       *string `gorm:"column:pay_pid;type:varchar(64);default:''" json:"payPid"`              // 只有三方支付时此处才有值
	ThirdOrderId *string `gorm:"column:third_order_id;type:varchar(64);default:''" json:"thirdOrderId"` // 第三方支付单号

	TotalFee                *int64  `gorm:"column:total_fee;type:int;default:0" json:"totalFee"`                                    // 总金额-实际支付金额
	PrincipalAmount         *int64  `gorm:"column:principal_amount;type:bigint;default:0" json:"principalAmount"`                   // 会员卡的本金
	MemberRoomBonusAmount   *int64  `gorm:"column:member_room_bonus_amount;type:bigint;default:0" json:"memberRoomBonusAmount"`     // 会员卡-用于房费的赠金
	MemberGoodsBonusAmount  *int64  `gorm:"column:member_goods_bonus_amount;type:bigint;default:0" json:"memberGoodsBonusAmount"`   // 会员卡-用于商品的赠金
	MemberCommonBonusAmount *int64  `gorm:"column:member_common_bonus_amount;type:bigint;default:0" json:"memberCommonBonusAmount"` // 会员卡-通用赠金（都可以用的赠金）
	Status                  *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`                                // 状态 success/refund
	PayType                 *string `gorm:"column:pay_type;type:varchar(64);default:''" json:"payType"`                             // 支付类型-微信 支付宝 找零 挂账
	PaySource               *string `gorm:"column:pay_source;type:varchar(64);default:''" json:"paySource"`                         // 支付来源-乐刷等第三方支付方式-微信/支付宝
	ProductName             *string `gorm:"column:product_name;type:varchar(64);default:''" json:"productName"`                     // 商品名称
	Info                    *string `gorm:"column:info;type:varchar(64);default:''" json:"info"`                                    // 备注
	FinishTime              *int64  `gorm:"column:finish_time;type:bigint;default:0" json:"finishTime"`                             // 完成时间
	BillDate                *int64  `gorm:"column:bill_date;type:bigint;default:0" json:"billDate"`                                 // 账单日期-冗余-用于统计
	BQROneCode              *string `gorm:"column:bqr_one_code;type:varchar(64);default:''" json:"bQROneCode"`                      // BShowQR支付方式的BQROneCode
	Openid                  *string `gorm:"column:openid;type:varchar(64);default:''" json:"openid"`                                // 微信小程序支付的openid

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (PayRecord) TableName() string {
	return "pay_record"
}

func (p PayRecord) GetId() string {
	return *p.Id
}
