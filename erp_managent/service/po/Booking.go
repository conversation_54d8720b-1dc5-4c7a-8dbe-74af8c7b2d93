package po

// Booking 预订实体
type Booking struct {
	Id             *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                 // ID
	VenueId        *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`               // 门店ID
	CustomerName   *string `gorm:"column:customer_name;type:varchar(64);default:''" json:"customerName"`     // 客户名称
	Gender         *string `gorm:"column:gender;type:varchar(2);default:''" json:"gender"`                   // 性别 0 男 1 女 3 未知
	CustomerPhone  *string `gorm:"column:customer_phone;type:varchar(20);default:''" json:"customerPhone"`   // 客户电话
	MemberId       *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`             // 会员id
	MemberCardId   *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`    // 会员卡id
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	CustomerSource *string `gorm:"column:customer_source;type:varchar(64);default:''" json:"customerSource"` // 客户来源
	ArrivalTime    *int64  `gorm:"column:arrival_time;type:int;default:0" json:"arrivalTime"`                // 预抵时间
	OpenTablePlan  *string `gorm:"column:open_table_plan;type:varchar(64);default:''" json:"openTablePlan"`  // 开台方案
	RoomId         *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                 // 房间ID
	RoomName       *string `gorm:"column:room_name;type:varchar(64);default:''" json:"roomName"`             // 房间名称
	BookedBy       *string `gorm:"column:booked_by;type:varchar(64);default:''" json:"bookedBy"`             // 代订人ID
	BookedByName   *string `gorm:"column:booked_name;type:varchar(64);default:''" json:"bookedName"`         // 代订人姓名
	Status         *int    `gorm:"column:status;type:int;default:0" json:"status"`                           // 状态 0 未使用 1 已使用 2 已取消
	Remark         *string `gorm:"column:remark;type:text" json:"remark"`                                    // 备注
	Ctime          *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                             // 创建时间
	Utime          *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                             // 更新时间
	State          *int    `gorm:"column:state;type:int;default:0" json:"state"`                             // 状态
	Version        *int    `gorm:"column:version;type:int;default:0" json:"version"`                         // 版本号
}

// TableName 设置表名
func (Booking) TableName() string {
	return "booking"
}

func (b Booking) GetId() string {
	return *b.Id
}
