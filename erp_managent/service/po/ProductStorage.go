package po

// ProductStorage 客户酒水存放记录
type ProductStorage struct {
	Id                *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                      // ID
	OrderNo           *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`                    // 存酒单号
	VenueId           *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                    // 门店ID
	CustomerId        *string `gorm:"column:customer_id;type:varchar(64);default:''" json:"customerId"`              // 客户ID
	CustomerName      *string `gorm:"column:customer_name;type:varchar(64);default:''" json:"customerName"`          // 客户姓名
	PhoneNumber       *string `gorm:"column:phone_number;type:varchar(64);default:''" json:"phoneNumber"`            // 电话号码
	MemberCardNo      *string `gorm:"column:member_card_no;type:varchar(64);default:''" json:"memberCardNo"`         // 会员卡号（保留原字段）
	MemberCardNumber  *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	MemberCardId      *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`         // 会员卡ID
	ProductId         *string `gorm:"column:product_id;type:varchar(64);default:''" json:"productId"`                // 产品ID
	ProductName       *string `gorm:"column:product_name;type:varchar(64);default:''" json:"productName"`            // 产品名称
	ProductType       *string `gorm:"column:product_type;type:varchar(64);default:''" json:"productType"`            // 产品类型(如:酒水、零食、饮料)
	ProductUnit       *string `gorm:"column:product_unit;type:varchar(32);default:''" json:"productUnit"`            // 产品单位(如:瓶、杯)
	ProductSpec       *string `gorm:"column:product_spec;type:varchar(32);default:''" json:"productSpec"`            // 产品规格(如:整瓶、半瓶)
	Quantity          *int    `gorm:"column:quantity;type:int;default:0" json:"quantity"`                            // 数量
	RemainingQty      *int    `gorm:"column:remaining_qty;type:int;default:0" json:"remainingQty"`                   // 剩余数量
	StorageLocation   *string `gorm:"column:storage_location;type:varchar(64);default:''" json:"storageLocation"`    // 存放位置
	StorageRoomId     *string `gorm:"column:storage_room_id;type:varchar(64);default:''" json:"storageRoomId"`       // 存放包厢ID
	StorageRoomName   *string `gorm:"column:storage_room_name;type:varchar(64);default:''" json:"storageRoomName"`   // 存放包厢名称
	StorageTime       *int64  `gorm:"column:storage_time;type:int;default:0" json:"storageTime"`                     // 存入时间
	ExpireTime        *int64  `gorm:"column:expire_time;type:int;default:0" json:"expireTime"`                       // 到期时间
	Remark            *string `gorm:"column:remark;type:varchar(255);default:''" json:"remark"`                      // 备注
	ParentOrderNo     *string `gorm:"column:parent_order_no;type:varchar(64);default:''" json:"parentOrderNo"`       // 父订单号，用于关联同一次存酒行为的多个商品
	StatusCode        *string `gorm:"column:status_code;type:varchar(32);default:''" json:"statusCode"`              // 状态码(stored/partial/withdrawn/discarded)
	StatusName        *string `gorm:"column:status_name;type:varchar(32);default:''" json:"statusName"`              // 状态名称(已存/部分支取/已取完/已报废)
	LastOperationTime *int64  `gorm:"column:last_operation_time;type:int;default:0" json:"lastOperationTime"`        // 最后操作时间
	OperatorId        *string `gorm:"column:operator_id;type:varchar(64);default:''" json:"operatorId"`              // 操作人ID
	OperatorName      *string `gorm:"column:operator_name;type:varchar(64);default:''" json:"operatorName"`          // 操作人姓名
	IsBatch           *int    `gorm:"column:is_batch;type:int;default:0" json:"isBatch"`                             // 是否批量操作的一部分：0-否, 1-是
	BatchTime         *int64  `gorm:"column:batch_time;type:int;default:0" json:"batchTime"`                         // 批量操作时间，用于标识同一批次
	Ctime             *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                                  // 创建时间
	Utime             *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                                  // 更新时间
	State             *int    `gorm:"column:state;type:int;default:0" json:"state"`                                  // 状态
	Version           *int    `gorm:"column:version;type:int;default:0" json:"version"`                              // 版本
}

// 该表作用：记录客户酒水存放记录
func (ProductStorage) TableName() string {
	return "product_storage"
}

func (p ProductStorage) GetId() string {
	return *p.Id
}
