package po

// Session 场次实体
type Session struct {
	Id            *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`              // 唯一ID
	VenueId       *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`             // 门店ID
	RoomId        *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`              // 房间ID-权益房间-当前房间
	EmployeeId    *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`      // 员工ID
	SessionId     *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`        // 开台ID
	MemberId      *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`          // 会员ID
	MemberCardId  *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`  // 会员卡ID
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	StartTime     *int64  `gorm:"column:start_time;type:int;default:0" json:"startTime"`                 // 开台时间
	EndTime       *int64  `gorm:"column:end_time;type:int;default:0" json:"endTime"`                     // 预期结束时间
	CloseTime     *int64  `gorm:"column:close_time;type:int;default:0" json:"closeTime"`                 // 实际关房时间
	Duration      *int64  `gorm:"column:duration;type:int;default:0" json:"duration"`                    // 废弃-客户端计算-使用时长
	Status        *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`               // 状态 开台：opening， 关台：ending
	PayStatus     *string `gorm:"column:pay_status;type:varchar(64);default:''" json:"payStatus"`        // 支付状态
	IsTimeConsume *bool   `gorm:"column:is_time_consume;type:tinyint(1);default:0" json:"isTimeConsume"` // 是否是计时消费

	MinConsume     *int64 `gorm:"column:min_consume;type:int;default:0" json:"minConsume"`         // 最低消费
	RoomFee        *int64 `gorm:"column:room_fee;type:int;default:0" json:"roomFee"`               // 包厢费用
	SupermarketFee *int64 `gorm:"column:supermarket_fee;type:int;default:0" json:"supermarketFee"` // 超市费用
	TotalFee       *int64 `gorm:"column:total_fee;type:int;default:0" json:"totalFee"`             // 总计费用
	UnpaidAmount   *int64 `gorm:"column:unpaid_amount;type:int;default:0" json:"unpaidAmount"`     // 未付金额
	PaidAmount     *int64 `gorm:"column:paid_amount;type:int;default:0" json:"paidAmount"`         // 已付金额
	PrePayBalance  *int64 `gorm:"column:pre_pay_balance;type:int;default:0" json:"prePayBalance"`  // 预付余额

	CancelEmployeeId   *string `gorm:"column:cancel_employee_id;type:varchar(64);default:''" json:"cancelEmployeeId"`     // 取消员工ID
	CancelEmployeeName *string `gorm:"column:cancel_employee_name;type:varchar(64);default:''" json:"cancelEmployeeName"` // 取消员工名称
	CancelReason       *string `gorm:"column:cancel_reason;type:varchar(512);default:''" json:"cancelReason"`             // 取消原因

	Tag  *string `gorm:"column:tag;type:varchar(64);default:''" json:"tag"`     // tag: lock/birthday/changyin
	Info *string `gorm:"column:info;type:varchar(1024);default:''" json:"info"` // 备注

	OrderSource        *string `gorm:"column:order_source;type:varchar(64);default:''" json:"orderSource"`             // 订单来源
	CustomerSource     *string `gorm:"column:customer_source;type:varchar(64);default:''" json:"customerSource"`       // 客户来源
	CustomerTag        *string `gorm:"column:customer_tag;type:varchar(64);default:''" json:"customerTag"`             // 客群标签
	AgentPerson        *string `gorm:"column:agent_person;type:varchar(64);default:''" json:"agentPerson"`              // 代定人
	DutyPerson         *string `gorm:"column:duty_person;type:varchar(64);default:''" json:"dutyPerson"`               // 轮房人
	RankNumber         *string `gorm:"column:rank_number;type:varchar(64);default:''" json:"rankNumber"`               // 排位号码
	IsOpenTableSettled *bool   `gorm:"column:is_open_table_settled;type:bool;default:false" json:"isOpenTableSettled"` // 是否开台立结

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (Session) TableName() string {
	return "session"
}

func (s Session) GetId() string {
	return *s.Id
}
