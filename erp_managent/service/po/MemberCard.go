package po

// MemberCard 会员卡实体
type MemberCard struct {
	Id       *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"` // ID
	Name     *string `gorm:"column:name;type:varchar(64);default:''" json:"name"`      // 会员姓名
	Phone    *string `gorm:"column:phone;type:varchar(64);default:''" json:"phone"`    // 会员手机号
	Birthday *int64  `gorm:"column:birthday;type:int;default:0" json:"birthday"`       // 生日
	Gender   *string `gorm:"column:gender;type:varchar(64);default:''" json:"gender"`  // 性别 male/female

	CardNumber    *string `gorm:"column:card_number;type:varchar(64);default:''" json:"cardNumber"`        // 会员卡号
	CardNumberId  *int    `gorm:"column:card_number_id;type:int;default:0" json:"cardNumberId"`            // 会员卡号ID-计算用6位正整数
	CardType      *string `gorm:"column:card_type;type:varchar(64);default:''" json:"cardType"`            // 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic
	CardLevelId   *string `gorm:"column:card_level_id;type:varchar(64);default:''" json:"cardLevelId"`     // 会员卡等级ID
	CardLevel     *string `gorm:"column:card_level;type:varchar(64);default:''" json:"cardLevel"`          // 会员卡等级 - normal/gold/diamond
	CardLevelName *string `gorm:"column:card_level_name;type:varchar(64);default:''" json:"cardLevelName"` // 会员卡等级名称 - 普通卡/黄金卡/钻石卡

	OperatorId   *string `gorm:"column:operator_id;type:varchar(64);default:''" json:"operatorId"`     // 开卡操作人ID
	OperatorName *string `gorm:"column:operator_name;type:varchar(64);default:''" json:"operatorName"` // 开卡操作人姓名
	SellerId     *string `gorm:"column:seller_id;type:varchar(64);default:''" json:"sellerId"`         // 销售员ID
	SellerName   *string `gorm:"column:seller_name;type:varchar(64);default:''" json:"sellerName"`     // 销售员姓名

	Points             *int    `gorm:"column:points;type:int;default:0" json:"points"`                              // 会员积分
	PrincipalBalance   *int64  `gorm:"column:principal_balance;type:bigint;default:0" json:"principalBalance"`      // 本金余额
	RoomBonusBalance   *int64  `gorm:"column:room_bonus_balance;type:bigint;default:0" json:"roomBonusBalance"`     // 用于房费的赠金
	GoodsBonusBalance  *int64  `gorm:"column:goods_bonus_balance;type:bigint;default:0" json:"goodsBonusBalance"`   // 用于商品的赠金
	CommonBonusBalance *int64  `gorm:"column:common_bonus_balance;type:bigint;default:0" json:"commonBonusBalance"` // 通用赠金（都可以用的赠金）
	Status             *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`                     // 会员状态,如: 正常:normal, 挂失:lost, 冻结:frozen, 过期:expired, 注销:cancelled
	CardStartTime      *int64  `gorm:"column:card_start_time;type:int;default:0" json:"cardStartTime"`              // 开卡时间
	CardEndTime        *int64  `gorm:"column:card_end_time;type:int;default:0" json:"cardEndTime"`                  // 结束时间

	Source    *string `gorm:"column:source;type:varchar(64);default:''" json:"source"`      // 会员来源,如: 线下实体卡, 线上小程序, 线上APP，枚举：offline_card, mini_program, app
	IsEnabled *bool   `gorm:"column:is_enabled;type:tinyint(1);default:1" json:"isEnabled"` // 是否启用

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间戳
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间戳
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态值
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (MemberCard) TableName() string {
	return "member_card"
}

func (m MemberCard) GetId() string {
	return *m.Id
}
