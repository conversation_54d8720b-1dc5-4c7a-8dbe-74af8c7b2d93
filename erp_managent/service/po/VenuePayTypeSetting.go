package po

// VenuePayTypeSetting 门店支付设置实体
type VenuePayTypeSetting struct {
	Id       *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`       // ID
	VenueId  *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`     // 门店ID
	TypeInfo *string `gorm:"column:type_info;type:varchar(2048);default:''" json:"typeInfo"` // 支付类型信息 json [{"payType":"wechat","label":"微信","logo":"","sort":1,"enable":true},{"payType":"alipay","label":"支付宝","logo":"","sort":2,"enable":true}]
	Remark   *string `gorm:"column:remark;type:varchar(255);default:''" json:"remark"`       // 备注
	Ctime    *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                   // 创建时间
	Utime    *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                   // 更新时间
	State    *int    `gorm:"column:state;type:int;default:0" json:"state"`                   // 状态
	Version  *int    `gorm:"column:version;type:int;default:0" json:"version"`               // 版本号
}

// TableName 设置表名
func (VenuePayTypeSetting) TableName() string {
	return "venue_pay_type_setting"
}

func (v VenuePayTypeSetting) GetId() string {
	return *v.Id
}
