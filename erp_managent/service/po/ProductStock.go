package po

// ProductStock 商品库存实体 (快照表)
type ProductStock struct {
	Id        *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`     // ID
	ProductId *string `gorm:"column:product_id;type:varchar(64);not null" json:"productId"` // 商品ID
	VenueId   *string `gorm:"column:venue_id;type:varchar(64);not null" json:"venueId"`     // 门店ID
	Warehouse *string `gorm:"column:warehouse;type:varchar(64);not null" json:"warehouse"`  // 仓库名称
	Stock     *int    `gorm:"column:stock;type:int" json:"stock"`                           // 当前库存数量
	Ctime     *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                 // 创建时间
	Utime     *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                 // 更新时间
	Version   *int    `gorm:"column:version;type:int;default:0" json:"version"`             // 版本
}

// TableName 设置表名
func (ProductStock) TableName() string {
	return "product_stock"
}

func (p ProductStock) GetId() string {
	return *p.Id
}
