package po

// MemberCardConsume 会员卡消费记录实体
type MemberCardConsume struct {
	Id               *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                      // ID
	VenueId          *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                    // 门店ID
	VenueName        *string `gorm:"column:venue_name;type:varchar(64);default:''" json:"venueName"`                // 门店名称
	RoomId           *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                      // 房间ID
	RoomName         *string `gorm:"column:room_name;type:varchar(64);default:''" json:"roomName"`                  // 房间名称
	EmployeeId       *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`              // 员工ID
	EmployeeName     *string `gorm:"column:employee_name;type:varchar(64);default:''" json:"employeeName"`          // 员工姓名
	EmployeePhone    *string `gorm:"column:employee_phone;type:varchar(64);default:''" json:"employeePhone"`        // 员工电话
	MemberId         *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`                  // 会员ID
	MemberCardId     *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`         // 会员卡ID
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	SessionId        *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`                // 场次ID

	// paybill
	BillId      *string `gorm:"column:bill_id;type:varchar(64);default:''" json:"billId"`      // 收款单号
	BillPid     *string `gorm:"column:bill_pid;type:varchar(64);default:''" json:"billPid"`    // 退款单对应的收款单号
	OriginalFee *int64  `gorm:"column:original_fee;type:int;default:0" json:"originalFee"`     // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee   *int64  `gorm:"column:should_fee;type:int;default:0" json:"shouldFee"`         // 应付金额 = 实付金额 + 抹零金额 - 找零金额
	TotalFee    *int64  `gorm:"column:total_fee;type:int;default:0" json:"totalFee"`           // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee     *int64  `gorm:"column:zero_fee;type:int;default:0" json:"zeroFee"`             // 抹零金额
	Direction   *string `gorm:"column:direction;type:varchar(64);default:''" json:"direction"` // 方向

	// payrecord
	PayId                      *string `gorm:"column:pay_id;type:varchar(64);default:''" json:"payId"`                                     // 支付单ID
	PayPid                     *string `gorm:"column:pay_pid;type:varchar(64);default:''" json:"payPid"`                                   //
	PayTime                    *int64  `gorm:"column:pay_time;type:int;default:0" json:"payTime"`                                          // 支付时间
	PayRecordTotalAmout        *int64  `gorm:"column:pay_record_total_amout;type:int;default:0" json:"payRecordTotalAmout"`                // 总金额-实际支付金额或会员卡的本金+3种赠金
	PayRecordPrincipalAmount   *int64  `gorm:"column:pay_record_principal_amount;type:int;default:0" json:"payRecordPrincipalAmount"`      // 本金
	PayRecordRoomBonusAmount   *int64  `gorm:"column:pay_record_room_bonus_amount;type:int;default:0" json:"payRecordRoomBonusAmount"`     // 房费赠金
	PayRecordGoodsBonusAmount  *int64  `gorm:"column:pay_record_goods_bonus_amount;type:int;default:0" json:"payRecordGoodsBonusAmount"`   // 商品赠金
	PayRecordCommonBonusAmount *int64  `gorm:"column:pay_record_common_bonus_amount;type:int;default:0" json:"payRecordCommonBonusAmount"` // 通用赠金
	BizType                    *string `gorm:"column:biz_type;type:varchar(64);default:''" json:"bizType"`                                 // 业务类型，如：消费/退款

	// membercard
	MemberCardBalance                  *int64 `gorm:"column:member_card_balance;type:int;default:0" json:"memberCardBalance"`                                      // 会员卡总余额
	MemberCardBalancePrincipalAmount   *int64 `gorm:"column:member_card_balance_principal_amount;type:int;default:0" json:"memberCardBalancePrincipalAmount"`      // 本金
	MemberCardBalanceRoomBonusAmount   *int64 `gorm:"column:member_card_balance_room_bonus_amount;type:int;default:0" json:"memberCardBalanceRoomBonusAmount"`     // 房费赠金
	MemberCardBalanceGoodsBonusAmount  *int64 `gorm:"column:member_card_balance_goods_bonus_amount;type:int;default:0" json:"memberCardBalanceGoodsBonusAmount"`   // 商品赠金
	MemberCardBalanceCommonBonusAmount *int64 `gorm:"column:member_card_balance_common_bonus_amount;type:int;default:0" json:"memberCardBalanceCommonBonusAmount"` // 通用赠金

	Remark *string `gorm:"column:remark;type:varchar(255)" json:"remark"` // 备注

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间戳
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间戳
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态值
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (MemberCardConsume) TableName() string {
	return "member_card_consume"
}

func (m MemberCardConsume) GetId() string {
	return *m.Id
}
