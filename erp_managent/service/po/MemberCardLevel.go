package po

// MemberCardLevel 会员卡等级PO
type MemberCardLevel struct {
	Id *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"` // ID

	VenueId     *string `gorm:"column:venue_id;type:varchar(64)" json:"venueId"`         // 门店ID
	Name        *string `gorm:"column:name;type:varchar(64)" json:"name"`                // 卡名称
	Level       *string `gorm:"column:level;type:varchar(64)" json:"level"`              // 等级
	Logo        *string `gorm:"column:logo;type:varchar(255)" json:"logo"`               // logo
	Background  *string `gorm:"column:background;type:varchar(255)" json:"background"`   // 背景
	Description *string `gorm:"column:description;type:varchar(255)" json:"description"` // 描述

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本
}

// TableName 设置表名
func (MemberCardLevel) TableName() string {
	return "member_card_level"
}

func (m MemberCardLevel) GetId() string {
	return *m.Id
}
