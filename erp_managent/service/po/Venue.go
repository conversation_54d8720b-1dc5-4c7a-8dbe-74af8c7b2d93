package po

// VenueType 门店类型枚举
type VenueType int

const (
	Wholesale VenueType = iota // 量贩
	Bar                        // 清吧
	KTV                        // KTV
	Pub                        // 酒吧
)

// Venue 门店实体
type Venue struct {
	Id              *string    `gorm:"column:id;type:varchar(12);primaryKey;not null" json:"id"`                          // ID
	Name            *string    `gorm:"column:name;type:varchar(255);default:''" json:"name"`                              // 门店名称
	VenueType       *VenueType `gorm:"column:venue_type;type:int;default:0" json:"venueType"`                             // 门店类型
	Logo            *string    `gorm:"column:logo;type:varchar(255);default:''" json:"logo"`                              // 门店logo URL
	Province        *string    `gorm:"column:province;type:varchar(255);default:''" json:"province"`                      // 省
	City            *string    `gorm:"column:city;type:varchar(255);default:''" json:"city"`                              // 市
	District        *string    `gorm:"column:district;type:varchar(255);default:''" json:"district"`                      // 区
	Address         *string    `gorm:"column:address;type:varchar(255);default:''" json:"address"`                        // 门店地址
	IsLeshuaPay     *int       `gorm:"column:is_leshua_pay;type:tinyint;default:0" json:"isLeshuaPay"`                    // 是否开通乐刷支付
	StartHours      *string    `gorm:"column:start_hours;type:varchar(255);default:'06:00'" json:"startHours"`            // 营业开始时间
	EndHours        *string    `gorm:"column:end_hours;type:varchar(255);default:'06:00'" json:"endHours"`                // 营业结束时间
	EarlyStartHours *string    `gorm:"column:early_start_hours;type:varchar(255);default:'06:00'" json:"earlyStartHours"` // 早班开始时间
	EarlyEndHours   *string    `gorm:"column:early_end_hours;type:varchar(255);default:'06:00'" json:"earlyEndHours"`     // 早班结束时间
	NoonStartHours  *string    `gorm:"column:noon_start_hours;type:varchar(255);default:'06:00'" json:"noonStartHours"`   // 午班开始时间
	NoonEndHours    *string    `gorm:"column:noon_end_hours;type:varchar(255);default:'06:00'" json:"noonEndHours"`       // 午班结束时间
	LateStartHours  *string    `gorm:"column:late_start_hours;type:varchar(255);default:'06:00'" json:"lateStartHours"`   // 晚班开始时间
	LateEndHours    *string    `gorm:"column:late_end_hours;type:varchar(255);default:'06:00'" json:"lateEndHours"`       // 晚班结束时间
	Photos          *string    `gorm:"column:photos;type:text;" json:"photos"`                                            // 门店照片URL列表
	Unionid         *string    `gorm:"column:unionid;type:varchar(64);default:''" json:"unionid"`                         // 老板unionid
	Contact         *string    `gorm:"column:contact;type:varchar(64);default:''" json:"contact"`                         // 联系人
	ContactPhone    *string    `gorm:"column:contact_phone;type:varchar(64);default:''" json:"contactPhone"`              // 联系人电话，在通知客户消息的时候，使用。并不是登录的手机号
	Description     *string    `gorm:"column:description;type:text;" json:"description"`                                  // 门店描述
	Ctime           *int64     `gorm:"column:ctime;type:int;default:0" json:"ctime"`                                      // 创建时间戳
	Utime           *int64     `gorm:"column:utime;type:int;default:0" json:"utime"`                                      // 更新时间戳
	State           *int       `gorm:"column:state;type:int;default:0" json:"state"`                                      // 状态值
	Version         *int       `gorm:"column:version;type:int;default:0" json:"version"`                                  // 版本号
	Mac             *string    `gorm:"column:mac;type:varchar(255);default:''" json:"mac"`                                // mac地址
	AppId           *string    `gorm:"column:app_id;type:varchar(255);default:''" json:"appId"`                           // 用于前端的appId，与加密狗绑定
	AppKey          *string    `gorm:"column:app_key;type:varchar(255);default:''" json:"appKey"`                         // 用于前端的appKey，与加密狗绑定
	IsThunderVOD    *int       `gorm:"column:is_thunder_vod;type:int;default:0" json:"isThunderVOD"`                      // 是否是雷石VOD点歌系统
	AuditStatus     *int       `gorm:"column:audit_status;type:tinyint;not null;default:0" json:"auditStatus"`            // 审核状态：0-待审核 1-已通过 2-已拒绝
}

// TableName 设置表名
func (Venue) TableName() string {
	return "venue"
}

func (v Venue) GetId() string {
	return *v.Id
}
