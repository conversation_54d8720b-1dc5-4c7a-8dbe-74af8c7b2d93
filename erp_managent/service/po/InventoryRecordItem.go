package po

// InventoryRecordItem 库存记录明细实体
type InventoryRecordItem struct {
	Id                *string  `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                      // ID
	InventoryRecordId *string  `gorm:"column:inventory_record_id;type:varchar(64);not null" json:"inventoryRecordId"` // 关联入库单ID
	ProductId         *string  `gorm:"column:product_id;type:varchar(64);not null" json:"productId"`                  // 商品ID
	ProductName       *string  `gorm:"column:product_name;type:varchar(255)" json:"productName"`                      // 商品名称 (冗余)
	Unit              *string  `gorm:"column:unit;type:varchar(64)" json:"unit"`                                      // 单位 (冗余)
	Quantity          *int     `gorm:"column:quantity;type:int" json:"quantity"`                                      // 数量 (入库为正, 出库为负)
	UnitPrice         *float64 `gorm:"column:unit_price;type:decimal(10,2)" json:"unitPrice"`                         // 入库单价
	Subtotal          *float64 `gorm:"column:subtotal;type:decimal(10,2)" json:"subtotal"`                            // 小计
	Ctime             *int64   `gorm:"column:ctime;type:int;default:0" json:"ctime"`                                  // 创建时间
	Utime             *int64   `gorm:"column:utime;type:int;default:0" json:"utime"`                                  // 更新时间
	State             *int     `gorm:"column:state;type:int;default:0" json:"state"`                                  // 状态
	Version           *int     `gorm:"column:version;type:int;default:0" json:"version"`                              // 版本
}

// TableName 设置表名
func (InventoryRecordItem) TableName() string {
	return "inventory_record_item"
}

func (i InventoryRecordItem) GetId() string {
	return *i.Id
}
