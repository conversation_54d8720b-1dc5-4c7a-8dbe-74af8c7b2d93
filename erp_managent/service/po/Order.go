package po

// Order 订单实体
type Order struct {
	Id               *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                      // ID
	VenueId          *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                    // 门店ID
	RoomId           *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                      // 房间ID
	EmployeeId       *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`              // 员工ID
	MemberId         *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`                  // 会员ID
	MemberCardId     *string `gorm:"column:member_card_id;type:varchar(64);default:''" json:"memberCardId"`         // 会员卡ID
	MemberCardNumber *string `gorm:"column:member_card_number;type:varchar(64);default:''" json:"memberCardNumber"` // 会员卡号
	SessionId        *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`                // 场次ID
	OrderNo          *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`                    // 订单编号
	POrderNo         *string `gorm:"column:p_order_no;type:varchar(64);default:''" json:"pOrderNo"`                 // 父订单编号-退款时原订单
	MinimumCharge    *int64  `gorm:"column:minimum_charge;type:int;default:0" json:"minimumCharge"`                 // 最低消费金额

	Type      *string `gorm:"column:type;type:varchar(64);default:''" json:"type"`            // 订单类型 roomplan/product
	Direction *string `gorm:"column:direction;type:varchar(64);default:''" json:"direction"`  // 方向 normal:下单、refund:退款
	MarkType  *string `gorm:"column:mark_type;type:varchar(64);default:''" json:"markType"`   // 标记类型 normal:正常、cancel:取消  转台时旧订单被标记为cancel，其他均正常
	Status    *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`        // 订单状态  unpaid:未结、paid:已结
	Tag       *string `gorm:"column:tag;type:varchar(64);default:''" json:"tag"`              // 标签
	Mark      *string `gorm:"column:mark;type:varchar(256);default:''" json:"mark"`           // 备注
	RefundTag *string `gorm:"column:refund_tag;type:varchar(64);default:''" json:"refundTag"` // 退款类型 空为正常 已付退款:paid_refund，未付退款:unpaid_refund

	ConfigRoomMemberDiscountType    *int64 `gorm:"column:config_room_member_discount_type;type:int;default:0" json:"configRoomMemberDiscountType"`       // 配置-包厢价格会员优惠方式 0:无，1：会员价，2：会员折扣
	ConfigProductMemberDiscountType *int64 `gorm:"column:config_product_member_discount_type;type:int;default:0" json:"configProductMemberDiscountType"` // 配置-商品价格会员优惠方式 0:无，1：会员价，2：会员折扣

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (Order) TableName() string {
	return "order"
}

func (o Order) GetId() string {
	return *o.Id
}
