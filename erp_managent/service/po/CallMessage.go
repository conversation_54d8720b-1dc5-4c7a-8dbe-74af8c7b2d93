package po

// Member 会员信息实体
type CallMessage struct {
	Id           *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                   // ID
	VenueId      *string `gorm:"column:venue_id;type:varchar(64);not null" json:"venue_id"`                  // 对应agent_id
	RoomId       *string `gorm:"column:room_id;type:varchar(64);not null" json:"room_id"`                    // 房间ID
	SessionId    *string `gorm:"column:session_id;type:varchar(64);default:''" json:"session_id"`            // 场次ID
	EmployeeId   *string `gorm:"column:employee_id;type:varchar(64);not null" json:"employee_id"`            // 员工ID
	RoomName     *string `gorm:"column:room_name;type:varchar(64);not null" json:"room_name"`                // 房间名称
	CallSrc      *string `gorm:"column:call_src;type:varchar(64);default:''" json:"call_src"`                // 呼叫来源 小程序、触摸屏
	CallType     *string `gorm:"column:call_type;type:varchar(64);not null" json:"call_type"`                // 呼叫类型
	CallTypeName *string `gorm:"column:call_type_name;type:varchar(128);not null" json:"call_type_name"`     // 呼叫类型名称
	Status       *int    `gorm:"column:status;type:int(11);not null;default:0" json:"status"`                // 房间状态(0:未处理,1:已处理，2:已取消)
	OptId        *string `gorm:"column:opt_id;type:varchar(64);not null;default:''" json:"opt_id"`           // 处理人ID
	OptName      *string `gorm:"column:opt_name;type:varchar(64);not null;default:''" json:"opt_name"`       // 处理人
	OptTime      *int64  `gorm:"column:opt_time;type:int(11);not null;default:0" json:"opt_time"`            // 处理时间
	CancelId     *string `gorm:"column:cancel_id;type:varchar(64);not null;default:''" json:"cancel_id"`     // 取消人ID
	CancelName   *string `gorm:"column:cancel_name;type:varchar(64);not null;default:''" json:"cancel_name"` // 取消人
	CancelTime   *int64  `gorm:"column:cancel_time;type:int(11);not null;default:0" json:"cancel_time"`      // 取消时间

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间戳
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间戳
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态值
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (CallMessage) TableName() string {
	return "call_message"
}

func (m CallMessage) GetId() string {
	return *m.Id
}
