// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newVenue(db *gorm.DB, opts ...gen.DOOption) venue {
	_venue := venue{}

	_venue.venueDo.UseDB(db, opts...)
	_venue.venueDo.UseModel(&po.Venue{})

	tableName := _venue.venueDo.TableName()
	_venue.ALL = field.NewAsterisk(tableName)
	_venue.Id = field.NewString(tableName, "id")
	_venue.Name = field.NewString(tableName, "name")
	_venue.VenueType = field.NewInt(tableName, "venue_type")
	_venue.Logo = field.NewString(tableName, "logo")
	_venue.Province = field.NewString(tableName, "province")
	_venue.City = field.NewString(tableName, "city")
	_venue.District = field.NewString(tableName, "district")
	_venue.Address = field.NewString(tableName, "address")
	_venue.IsLeshuaPay = field.NewInt(tableName, "is_leshua_pay")
	_venue.StartHours = field.NewString(tableName, "start_hours")
	_venue.EndHours = field.NewString(tableName, "end_hours")
	_venue.EarlyStartHours = field.NewString(tableName, "early_start_hours")
	_venue.EarlyEndHours = field.NewString(tableName, "early_end_hours")
	_venue.NoonStartHours = field.NewString(tableName, "noon_start_hours")
	_venue.NoonEndHours = field.NewString(tableName, "noon_end_hours")
	_venue.LateStartHours = field.NewString(tableName, "late_start_hours")
	_venue.LateEndHours = field.NewString(tableName, "late_end_hours")
	_venue.Photos = field.NewString(tableName, "photos")
	_venue.Unionid = field.NewString(tableName, "unionid")
	_venue.Contact = field.NewString(tableName, "contact")
	_venue.ContactPhone = field.NewString(tableName, "contact_phone")
	_venue.Description = field.NewString(tableName, "description")
	_venue.Ctime = field.NewInt64(tableName, "ctime")
	_venue.Utime = field.NewInt64(tableName, "utime")
	_venue.State = field.NewInt(tableName, "state")
	_venue.Version = field.NewInt(tableName, "version")
	_venue.Mac = field.NewString(tableName, "mac")
	_venue.AppId = field.NewString(tableName, "app_id")
	_venue.AppKey = field.NewString(tableName, "app_key")
	_venue.IsThunderVOD = field.NewInt(tableName, "is_thunder_vod")
	_venue.AuditStatus = field.NewInt(tableName, "audit_status")

	_venue.fillFieldMap()

	return _venue
}

type venue struct {
	venueDo

	ALL             field.Asterisk
	Id              field.String
	Name            field.String
	VenueType       field.Int
	Logo            field.String
	Province        field.String
	City            field.String
	District        field.String
	Address         field.String
	IsLeshuaPay     field.Int
	StartHours      field.String
	EndHours        field.String
	EarlyStartHours field.String
	EarlyEndHours   field.String
	NoonStartHours  field.String
	NoonEndHours    field.String
	LateStartHours  field.String
	LateEndHours    field.String
	Photos          field.String
	Unionid         field.String
	Contact         field.String
	ContactPhone    field.String
	Description     field.String
	Ctime           field.Int64
	Utime           field.Int64
	State           field.Int
	Version         field.Int
	Mac             field.String
	AppId           field.String
	AppKey          field.String
	IsThunderVOD    field.Int
	AuditStatus     field.Int

	fieldMap map[string]field.Expr
}

func (v venue) Table(newTableName string) *venue {
	v.venueDo.UseTable(newTableName)
	return v.updateTableName(newTableName)
}

func (v venue) As(alias string) *venue {
	v.venueDo.DO = *(v.venueDo.As(alias).(*gen.DO))
	return v.updateTableName(alias)
}

func (v *venue) updateTableName(table string) *venue {
	v.ALL = field.NewAsterisk(table)
	v.Id = field.NewString(table, "id")
	v.Name = field.NewString(table, "name")
	v.VenueType = field.NewInt(table, "venue_type")
	v.Logo = field.NewString(table, "logo")
	v.Province = field.NewString(table, "province")
	v.City = field.NewString(table, "city")
	v.District = field.NewString(table, "district")
	v.Address = field.NewString(table, "address")
	v.IsLeshuaPay = field.NewInt(table, "is_leshua_pay")
	v.StartHours = field.NewString(table, "start_hours")
	v.EndHours = field.NewString(table, "end_hours")
	v.EarlyStartHours = field.NewString(table, "early_start_hours")
	v.EarlyEndHours = field.NewString(table, "early_end_hours")
	v.NoonStartHours = field.NewString(table, "noon_start_hours")
	v.NoonEndHours = field.NewString(table, "noon_end_hours")
	v.LateStartHours = field.NewString(table, "late_start_hours")
	v.LateEndHours = field.NewString(table, "late_end_hours")
	v.Photos = field.NewString(table, "photos")
	v.Unionid = field.NewString(table, "unionid")
	v.Contact = field.NewString(table, "contact")
	v.ContactPhone = field.NewString(table, "contact_phone")
	v.Description = field.NewString(table, "description")
	v.Ctime = field.NewInt64(table, "ctime")
	v.Utime = field.NewInt64(table, "utime")
	v.State = field.NewInt(table, "state")
	v.Version = field.NewInt(table, "version")
	v.Mac = field.NewString(table, "mac")
	v.AppId = field.NewString(table, "app_id")
	v.AppKey = field.NewString(table, "app_key")
	v.IsThunderVOD = field.NewInt(table, "is_thunder_vod")
	v.AuditStatus = field.NewInt(table, "audit_status")

	v.fillFieldMap()

	return v
}

func (v *venue) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := v.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (v *venue) fillFieldMap() {
	v.fieldMap = make(map[string]field.Expr, 31)
	v.fieldMap["id"] = v.Id
	v.fieldMap["name"] = v.Name
	v.fieldMap["venue_type"] = v.VenueType
	v.fieldMap["logo"] = v.Logo
	v.fieldMap["province"] = v.Province
	v.fieldMap["city"] = v.City
	v.fieldMap["district"] = v.District
	v.fieldMap["address"] = v.Address
	v.fieldMap["is_leshua_pay"] = v.IsLeshuaPay
	v.fieldMap["start_hours"] = v.StartHours
	v.fieldMap["end_hours"] = v.EndHours
	v.fieldMap["early_start_hours"] = v.EarlyStartHours
	v.fieldMap["early_end_hours"] = v.EarlyEndHours
	v.fieldMap["noon_start_hours"] = v.NoonStartHours
	v.fieldMap["noon_end_hours"] = v.NoonEndHours
	v.fieldMap["late_start_hours"] = v.LateStartHours
	v.fieldMap["late_end_hours"] = v.LateEndHours
	v.fieldMap["photos"] = v.Photos
	v.fieldMap["unionid"] = v.Unionid
	v.fieldMap["contact"] = v.Contact
	v.fieldMap["contact_phone"] = v.ContactPhone
	v.fieldMap["description"] = v.Description
	v.fieldMap["ctime"] = v.Ctime
	v.fieldMap["utime"] = v.Utime
	v.fieldMap["state"] = v.State
	v.fieldMap["version"] = v.Version
	v.fieldMap["mac"] = v.Mac
	v.fieldMap["app_id"] = v.AppId
	v.fieldMap["app_key"] = v.AppKey
	v.fieldMap["is_thunder_vod"] = v.IsThunderVOD
	v.fieldMap["audit_status"] = v.AuditStatus
}

func (v venue) clone(db *gorm.DB) venue {
	v.venueDo.ReplaceConnPool(db.Statement.ConnPool)
	return v
}

func (v venue) replaceDB(db *gorm.DB) venue {
	v.venueDo.ReplaceDB(db)
	return v
}

type venueDo struct{ gen.DO }

type IVenueDo interface {
	gen.SubQuery
	Debug() IVenueDo
	WithContext(ctx context.Context) IVenueDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IVenueDo
	WriteDB() IVenueDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IVenueDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IVenueDo
	Not(conds ...gen.Condition) IVenueDo
	Or(conds ...gen.Condition) IVenueDo
	Select(conds ...field.Expr) IVenueDo
	Where(conds ...gen.Condition) IVenueDo
	Order(conds ...field.Expr) IVenueDo
	Distinct(cols ...field.Expr) IVenueDo
	Omit(cols ...field.Expr) IVenueDo
	Join(table schema.Tabler, on ...field.Expr) IVenueDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IVenueDo
	RightJoin(table schema.Tabler, on ...field.Expr) IVenueDo
	Group(cols ...field.Expr) IVenueDo
	Having(conds ...gen.Condition) IVenueDo
	Limit(limit int) IVenueDo
	Offset(offset int) IVenueDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IVenueDo
	Unscoped() IVenueDo
	Create(values ...*po.Venue) error
	CreateInBatches(values []*po.Venue, batchSize int) error
	Save(values ...*po.Venue) error
	First() (*po.Venue, error)
	Take() (*po.Venue, error)
	Last() (*po.Venue, error)
	Find() ([]*po.Venue, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.Venue, err error)
	FindInBatches(result *[]*po.Venue, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.Venue) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IVenueDo
	Assign(attrs ...field.AssignExpr) IVenueDo
	Joins(fields ...field.RelationField) IVenueDo
	Preload(fields ...field.RelationField) IVenueDo
	FirstOrInit() (*po.Venue, error)
	FirstOrCreate() (*po.Venue, error)
	FindByPage(offset int, limit int) (result []*po.Venue, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IVenueDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.Venue, err error)
	DazzyQueryByAny(field string, value string) (result []*po.Venue, err error)
	QueryByVenueId(venueId string) (result []*po.Venue, err error)
}

// DELETE FROM @@table WHERE id = @id
func (v venueDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM venue WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = v.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (v venueDo) QueryOneByID(id string) (result *po.Venue, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM venue WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = v.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (v venueDo) DazzyQueryByAny(field string, value string) (result []*po.Venue, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM venue WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = v.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (v venueDo) QueryByVenueId(venueId string) (result []*po.Venue, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM venue WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = v.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (v venueDo) Debug() IVenueDo {
	return v.withDO(v.DO.Debug())
}

func (v venueDo) WithContext(ctx context.Context) IVenueDo {
	return v.withDO(v.DO.WithContext(ctx))
}

func (v venueDo) ReadDB() IVenueDo {
	return v.Clauses(dbresolver.Read)
}

func (v venueDo) WriteDB() IVenueDo {
	return v.Clauses(dbresolver.Write)
}

func (v venueDo) Session(config *gorm.Session) IVenueDo {
	return v.withDO(v.DO.Session(config))
}

func (v venueDo) Clauses(conds ...clause.Expression) IVenueDo {
	return v.withDO(v.DO.Clauses(conds...))
}

func (v venueDo) Returning(value interface{}, columns ...string) IVenueDo {
	return v.withDO(v.DO.Returning(value, columns...))
}

func (v venueDo) Not(conds ...gen.Condition) IVenueDo {
	return v.withDO(v.DO.Not(conds...))
}

func (v venueDo) Or(conds ...gen.Condition) IVenueDo {
	return v.withDO(v.DO.Or(conds...))
}

func (v venueDo) Select(conds ...field.Expr) IVenueDo {
	return v.withDO(v.DO.Select(conds...))
}

func (v venueDo) Where(conds ...gen.Condition) IVenueDo {
	return v.withDO(v.DO.Where(conds...))
}

func (v venueDo) Order(conds ...field.Expr) IVenueDo {
	return v.withDO(v.DO.Order(conds...))
}

func (v venueDo) Distinct(cols ...field.Expr) IVenueDo {
	return v.withDO(v.DO.Distinct(cols...))
}

func (v venueDo) Omit(cols ...field.Expr) IVenueDo {
	return v.withDO(v.DO.Omit(cols...))
}

func (v venueDo) Join(table schema.Tabler, on ...field.Expr) IVenueDo {
	return v.withDO(v.DO.Join(table, on...))
}

func (v venueDo) LeftJoin(table schema.Tabler, on ...field.Expr) IVenueDo {
	return v.withDO(v.DO.LeftJoin(table, on...))
}

func (v venueDo) RightJoin(table schema.Tabler, on ...field.Expr) IVenueDo {
	return v.withDO(v.DO.RightJoin(table, on...))
}

func (v venueDo) Group(cols ...field.Expr) IVenueDo {
	return v.withDO(v.DO.Group(cols...))
}

func (v venueDo) Having(conds ...gen.Condition) IVenueDo {
	return v.withDO(v.DO.Having(conds...))
}

func (v venueDo) Limit(limit int) IVenueDo {
	return v.withDO(v.DO.Limit(limit))
}

func (v venueDo) Offset(offset int) IVenueDo {
	return v.withDO(v.DO.Offset(offset))
}

func (v venueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IVenueDo {
	return v.withDO(v.DO.Scopes(funcs...))
}

func (v venueDo) Unscoped() IVenueDo {
	return v.withDO(v.DO.Unscoped())
}

func (v venueDo) Create(values ...*po.Venue) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Create(values)
}

func (v venueDo) CreateInBatches(values []*po.Venue, batchSize int) error {
	return v.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (v venueDo) Save(values ...*po.Venue) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Save(values)
}

func (v venueDo) First() (*po.Venue, error) {
	if result, err := v.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.Venue), nil
	}
}

func (v venueDo) Take() (*po.Venue, error) {
	if result, err := v.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.Venue), nil
	}
}

func (v venueDo) Last() (*po.Venue, error) {
	if result, err := v.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.Venue), nil
	}
}

func (v venueDo) Find() ([]*po.Venue, error) {
	result, err := v.DO.Find()
	return result.([]*po.Venue), err
}

func (v venueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.Venue, err error) {
	buf := make([]*po.Venue, 0, batchSize)
	err = v.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (v venueDo) FindInBatches(result *[]*po.Venue, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return v.DO.FindInBatches(result, batchSize, fc)
}

func (v venueDo) Attrs(attrs ...field.AssignExpr) IVenueDo {
	return v.withDO(v.DO.Attrs(attrs...))
}

func (v venueDo) Assign(attrs ...field.AssignExpr) IVenueDo {
	return v.withDO(v.DO.Assign(attrs...))
}

func (v venueDo) Joins(fields ...field.RelationField) IVenueDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Joins(_f))
	}
	return &v
}

func (v venueDo) Preload(fields ...field.RelationField) IVenueDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Preload(_f))
	}
	return &v
}

func (v venueDo) FirstOrInit() (*po.Venue, error) {
	if result, err := v.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.Venue), nil
	}
}

func (v venueDo) FirstOrCreate() (*po.Venue, error) {
	if result, err := v.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.Venue), nil
	}
}

func (v venueDo) FindByPage(offset int, limit int) (result []*po.Venue, count int64, err error) {
	result, err = v.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = v.Offset(-1).Limit(-1).Count()
	return
}

func (v venueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = v.Count()
	if err != nil {
		return
	}

	err = v.Offset(offset).Limit(limit).Scan(result)
	return
}

func (v venueDo) Scan(result interface{}) (err error) {
	return v.DO.Scan(result)
}

func (v venueDo) Delete(models ...*po.Venue) (result gen.ResultInfo, err error) {
	return v.DO.Delete(models)
}

func (v *venueDo) withDO(do gen.Dao) *venueDo {
	v.DO = *do.(*gen.DO)
	return v
}
