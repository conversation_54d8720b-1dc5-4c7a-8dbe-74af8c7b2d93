// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newProductStorageOrder(db *gorm.DB, opts ...gen.DOOption) productStorageOrder {
	_productStorageOrder := productStorageOrder{}

	_productStorageOrder.productStorageOrderDo.UseDB(db, opts...)
	_productStorageOrder.productStorageOrderDo.UseModel(&po.ProductStorageOrder{})

	tableName := _productStorageOrder.productStorageOrderDo.TableName()
	_productStorageOrder.ALL = field.NewAsterisk(tableName)
	_productStorageOrder.Id = field.NewString(tableName, "id")
	_productStorageOrder.OrderNo = field.NewString(tableName, "order_no")
	_productStorageOrder.VenueId = field.NewString(tableName, "venue_id")
	_productStorageOrder.CustomerId = field.NewString(tableName, "customer_id")
	_productStorageOrder.CustomerName = field.NewString(tableName, "customer_name")
	_productStorageOrder.PhoneNumber = field.NewString(tableName, "phone_number")
	_productStorageOrder.MemberCardNo = field.NewString(tableName, "member_card_no")
	_productStorageOrder.MemberCardNumber = field.NewString(tableName, "member_card_number")
	_productStorageOrder.MemberCardId = field.NewString(tableName, "member_card_id")
	_productStorageOrder.StorageTime = field.NewInt64(tableName, "storage_time")
	_productStorageOrder.TotalItems = field.NewInt(tableName, "total_items")
	_productStorageOrder.TotalQuantity = field.NewInt(tableName, "total_quantity")
	_productStorageOrder.RemainingQuantity = field.NewInt(tableName, "remaining_quantity")
	_productStorageOrder.Remark = field.NewString(tableName, "remark")
	_productStorageOrder.StorageRoomId = field.NewString(tableName, "storage_room_id")
	_productStorageOrder.StorageRoomName = field.NewString(tableName, "storage_room_name")
	_productStorageOrder.OfflineOnly = field.NewInt(tableName, "offline_only")
	_productStorageOrder.OperatorId = field.NewString(tableName, "operator_id")
	_productStorageOrder.OperatorName = field.NewString(tableName, "operator_name")
	_productStorageOrder.Ctime = field.NewInt64(tableName, "ctime")
	_productStorageOrder.Utime = field.NewInt64(tableName, "utime")
	_productStorageOrder.State = field.NewInt(tableName, "state")
	_productStorageOrder.Version = field.NewInt(tableName, "version")

	_productStorageOrder.fillFieldMap()

	return _productStorageOrder
}

type productStorageOrder struct {
	productStorageOrderDo

	ALL               field.Asterisk
	Id                field.String
	OrderNo           field.String
	VenueId           field.String
	CustomerId        field.String
	CustomerName      field.String
	PhoneNumber       field.String
	MemberCardNo      field.String
	MemberCardNumber  field.String
	MemberCardId      field.String
	StorageTime       field.Int64
	TotalItems        field.Int
	TotalQuantity     field.Int
	RemainingQuantity field.Int
	Remark            field.String
	StorageRoomId     field.String
	StorageRoomName   field.String
	OfflineOnly       field.Int
	OperatorId        field.String
	OperatorName      field.String
	Ctime             field.Int64
	Utime             field.Int64
	State             field.Int
	Version           field.Int

	fieldMap map[string]field.Expr
}

func (p productStorageOrder) Table(newTableName string) *productStorageOrder {
	p.productStorageOrderDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productStorageOrder) As(alias string) *productStorageOrder {
	p.productStorageOrderDo.DO = *(p.productStorageOrderDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productStorageOrder) updateTableName(table string) *productStorageOrder {
	p.ALL = field.NewAsterisk(table)
	p.Id = field.NewString(table, "id")
	p.OrderNo = field.NewString(table, "order_no")
	p.VenueId = field.NewString(table, "venue_id")
	p.CustomerId = field.NewString(table, "customer_id")
	p.CustomerName = field.NewString(table, "customer_name")
	p.PhoneNumber = field.NewString(table, "phone_number")
	p.MemberCardNo = field.NewString(table, "member_card_no")
	p.MemberCardNumber = field.NewString(table, "member_card_number")
	p.MemberCardId = field.NewString(table, "member_card_id")
	p.StorageTime = field.NewInt64(table, "storage_time")
	p.TotalItems = field.NewInt(table, "total_items")
	p.TotalQuantity = field.NewInt(table, "total_quantity")
	p.RemainingQuantity = field.NewInt(table, "remaining_quantity")
	p.Remark = field.NewString(table, "remark")
	p.StorageRoomId = field.NewString(table, "storage_room_id")
	p.StorageRoomName = field.NewString(table, "storage_room_name")
	p.OfflineOnly = field.NewInt(table, "offline_only")
	p.OperatorId = field.NewString(table, "operator_id")
	p.OperatorName = field.NewString(table, "operator_name")
	p.Ctime = field.NewInt64(table, "ctime")
	p.Utime = field.NewInt64(table, "utime")
	p.State = field.NewInt(table, "state")
	p.Version = field.NewInt(table, "version")

	p.fillFieldMap()

	return p
}

func (p *productStorageOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productStorageOrder) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 23)
	p.fieldMap["id"] = p.Id
	p.fieldMap["order_no"] = p.OrderNo
	p.fieldMap["venue_id"] = p.VenueId
	p.fieldMap["customer_id"] = p.CustomerId
	p.fieldMap["customer_name"] = p.CustomerName
	p.fieldMap["phone_number"] = p.PhoneNumber
	p.fieldMap["member_card_no"] = p.MemberCardNo
	p.fieldMap["member_card_number"] = p.MemberCardNumber
	p.fieldMap["member_card_id"] = p.MemberCardId
	p.fieldMap["storage_time"] = p.StorageTime
	p.fieldMap["total_items"] = p.TotalItems
	p.fieldMap["total_quantity"] = p.TotalQuantity
	p.fieldMap["remaining_quantity"] = p.RemainingQuantity
	p.fieldMap["remark"] = p.Remark
	p.fieldMap["storage_room_id"] = p.StorageRoomId
	p.fieldMap["storage_room_name"] = p.StorageRoomName
	p.fieldMap["offline_only"] = p.OfflineOnly
	p.fieldMap["operator_id"] = p.OperatorId
	p.fieldMap["operator_name"] = p.OperatorName
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["utime"] = p.Utime
	p.fieldMap["state"] = p.State
	p.fieldMap["version"] = p.Version
}

func (p productStorageOrder) clone(db *gorm.DB) productStorageOrder {
	p.productStorageOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p productStorageOrder) replaceDB(db *gorm.DB) productStorageOrder {
	p.productStorageOrderDo.ReplaceDB(db)
	return p
}

type productStorageOrderDo struct{ gen.DO }

type IProductStorageOrderDo interface {
	gen.SubQuery
	Debug() IProductStorageOrderDo
	WithContext(ctx context.Context) IProductStorageOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IProductStorageOrderDo
	WriteDB() IProductStorageOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IProductStorageOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IProductStorageOrderDo
	Not(conds ...gen.Condition) IProductStorageOrderDo
	Or(conds ...gen.Condition) IProductStorageOrderDo
	Select(conds ...field.Expr) IProductStorageOrderDo
	Where(conds ...gen.Condition) IProductStorageOrderDo
	Order(conds ...field.Expr) IProductStorageOrderDo
	Distinct(cols ...field.Expr) IProductStorageOrderDo
	Omit(cols ...field.Expr) IProductStorageOrderDo
	Join(table schema.Tabler, on ...field.Expr) IProductStorageOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IProductStorageOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IProductStorageOrderDo
	Group(cols ...field.Expr) IProductStorageOrderDo
	Having(conds ...gen.Condition) IProductStorageOrderDo
	Limit(limit int) IProductStorageOrderDo
	Offset(offset int) IProductStorageOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IProductStorageOrderDo
	Unscoped() IProductStorageOrderDo
	Create(values ...*po.ProductStorageOrder) error
	CreateInBatches(values []*po.ProductStorageOrder, batchSize int) error
	Save(values ...*po.ProductStorageOrder) error
	First() (*po.ProductStorageOrder, error)
	Take() (*po.ProductStorageOrder, error)
	Last() (*po.ProductStorageOrder, error)
	Find() ([]*po.ProductStorageOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.ProductStorageOrder, err error)
	FindInBatches(result *[]*po.ProductStorageOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.ProductStorageOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IProductStorageOrderDo
	Assign(attrs ...field.AssignExpr) IProductStorageOrderDo
	Joins(fields ...field.RelationField) IProductStorageOrderDo
	Preload(fields ...field.RelationField) IProductStorageOrderDo
	FirstOrInit() (*po.ProductStorageOrder, error)
	FirstOrCreate() (*po.ProductStorageOrder, error)
	FindByPage(offset int, limit int) (result []*po.ProductStorageOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IProductStorageOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.ProductStorageOrder, err error)
	DazzyQueryByAny(field string, value string) (result []*po.ProductStorageOrder, err error)
	QueryByVenueId(venueId string) (result []*po.ProductStorageOrder, err error)
}

// DELETE FROM @@table WHERE id = @id
func (p productStorageOrderDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM product_storage_order WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (p productStorageOrderDo) QueryOneByID(id string) (result *po.ProductStorageOrder, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM product_storage_order WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (p productStorageOrderDo) DazzyQueryByAny(field string, value string) (result []*po.ProductStorageOrder, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM product_storage_order WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (p productStorageOrderDo) QueryByVenueId(venueId string) (result []*po.ProductStorageOrder, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM product_storage_order WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p productStorageOrderDo) Debug() IProductStorageOrderDo {
	return p.withDO(p.DO.Debug())
}

func (p productStorageOrderDo) WithContext(ctx context.Context) IProductStorageOrderDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productStorageOrderDo) ReadDB() IProductStorageOrderDo {
	return p.Clauses(dbresolver.Read)
}

func (p productStorageOrderDo) WriteDB() IProductStorageOrderDo {
	return p.Clauses(dbresolver.Write)
}

func (p productStorageOrderDo) Session(config *gorm.Session) IProductStorageOrderDo {
	return p.withDO(p.DO.Session(config))
}

func (p productStorageOrderDo) Clauses(conds ...clause.Expression) IProductStorageOrderDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productStorageOrderDo) Returning(value interface{}, columns ...string) IProductStorageOrderDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productStorageOrderDo) Not(conds ...gen.Condition) IProductStorageOrderDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productStorageOrderDo) Or(conds ...gen.Condition) IProductStorageOrderDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productStorageOrderDo) Select(conds ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productStorageOrderDo) Where(conds ...gen.Condition) IProductStorageOrderDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productStorageOrderDo) Order(conds ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productStorageOrderDo) Distinct(cols ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productStorageOrderDo) Omit(cols ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productStorageOrderDo) Join(table schema.Tabler, on ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productStorageOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productStorageOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productStorageOrderDo) Group(cols ...field.Expr) IProductStorageOrderDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productStorageOrderDo) Having(conds ...gen.Condition) IProductStorageOrderDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productStorageOrderDo) Limit(limit int) IProductStorageOrderDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productStorageOrderDo) Offset(offset int) IProductStorageOrderDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productStorageOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IProductStorageOrderDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productStorageOrderDo) Unscoped() IProductStorageOrderDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productStorageOrderDo) Create(values ...*po.ProductStorageOrder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productStorageOrderDo) CreateInBatches(values []*po.ProductStorageOrder, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productStorageOrderDo) Save(values ...*po.ProductStorageOrder) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productStorageOrderDo) First() (*po.ProductStorageOrder, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOrder), nil
	}
}

func (p productStorageOrderDo) Take() (*po.ProductStorageOrder, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOrder), nil
	}
}

func (p productStorageOrderDo) Last() (*po.ProductStorageOrder, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOrder), nil
	}
}

func (p productStorageOrderDo) Find() ([]*po.ProductStorageOrder, error) {
	result, err := p.DO.Find()
	return result.([]*po.ProductStorageOrder), err
}

func (p productStorageOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.ProductStorageOrder, err error) {
	buf := make([]*po.ProductStorageOrder, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productStorageOrderDo) FindInBatches(result *[]*po.ProductStorageOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productStorageOrderDo) Attrs(attrs ...field.AssignExpr) IProductStorageOrderDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productStorageOrderDo) Assign(attrs ...field.AssignExpr) IProductStorageOrderDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productStorageOrderDo) Joins(fields ...field.RelationField) IProductStorageOrderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productStorageOrderDo) Preload(fields ...field.RelationField) IProductStorageOrderDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productStorageOrderDo) FirstOrInit() (*po.ProductStorageOrder, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOrder), nil
	}
}

func (p productStorageOrderDo) FirstOrCreate() (*po.ProductStorageOrder, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOrder), nil
	}
}

func (p productStorageOrderDo) FindByPage(offset int, limit int) (result []*po.ProductStorageOrder, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productStorageOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productStorageOrderDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productStorageOrderDo) Delete(models ...*po.ProductStorageOrder) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productStorageOrderDo) withDO(do gen.Dao) *productStorageOrderDo {
	p.DO = *do.(*gen.DO)
	return p
}
