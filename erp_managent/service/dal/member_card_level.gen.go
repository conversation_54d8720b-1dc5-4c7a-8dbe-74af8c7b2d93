// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newMemberCardLevel(db *gorm.DB, opts ...gen.DOOption) memberCardLevel {
	_memberCardLevel := memberCardLevel{}

	_memberCardLevel.memberCardLevelDo.UseDB(db, opts...)
	_memberCardLevel.memberCardLevelDo.UseModel(&po.MemberCardLevel{})

	tableName := _memberCardLevel.memberCardLevelDo.TableName()
	_memberCardLevel.ALL = field.NewAsterisk(tableName)
	_memberCardLevel.Id = field.NewString(tableName, "id")
	_memberCardLevel.VenueId = field.NewString(tableName, "venue_id")
	_memberCardLevel.Name = field.NewString(tableName, "name")
	_memberCardLevel.Level = field.NewString(tableName, "level")
	_memberCardLevel.Logo = field.NewString(tableName, "logo")
	_memberCardLevel.Background = field.NewString(tableName, "background")
	_memberCardLevel.Description = field.NewString(tableName, "description")
	_memberCardLevel.Ctime = field.NewInt64(tableName, "ctime")
	_memberCardLevel.Utime = field.NewInt64(tableName, "utime")
	_memberCardLevel.State = field.NewInt(tableName, "state")
	_memberCardLevel.Version = field.NewInt(tableName, "version")

	_memberCardLevel.fillFieldMap()

	return _memberCardLevel
}

type memberCardLevel struct {
	memberCardLevelDo

	ALL         field.Asterisk
	Id          field.String
	VenueId     field.String
	Name        field.String
	Level       field.String
	Logo        field.String
	Background  field.String
	Description field.String
	Ctime       field.Int64
	Utime       field.Int64
	State       field.Int
	Version     field.Int

	fieldMap map[string]field.Expr
}

func (m memberCardLevel) Table(newTableName string) *memberCardLevel {
	m.memberCardLevelDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m memberCardLevel) As(alias string) *memberCardLevel {
	m.memberCardLevelDo.DO = *(m.memberCardLevelDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *memberCardLevel) updateTableName(table string) *memberCardLevel {
	m.ALL = field.NewAsterisk(table)
	m.Id = field.NewString(table, "id")
	m.VenueId = field.NewString(table, "venue_id")
	m.Name = field.NewString(table, "name")
	m.Level = field.NewString(table, "level")
	m.Logo = field.NewString(table, "logo")
	m.Background = field.NewString(table, "background")
	m.Description = field.NewString(table, "description")
	m.Ctime = field.NewInt64(table, "ctime")
	m.Utime = field.NewInt64(table, "utime")
	m.State = field.NewInt(table, "state")
	m.Version = field.NewInt(table, "version")

	m.fillFieldMap()

	return m
}

func (m *memberCardLevel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *memberCardLevel) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 11)
	m.fieldMap["id"] = m.Id
	m.fieldMap["venue_id"] = m.VenueId
	m.fieldMap["name"] = m.Name
	m.fieldMap["level"] = m.Level
	m.fieldMap["logo"] = m.Logo
	m.fieldMap["background"] = m.Background
	m.fieldMap["description"] = m.Description
	m.fieldMap["ctime"] = m.Ctime
	m.fieldMap["utime"] = m.Utime
	m.fieldMap["state"] = m.State
	m.fieldMap["version"] = m.Version
}

func (m memberCardLevel) clone(db *gorm.DB) memberCardLevel {
	m.memberCardLevelDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m memberCardLevel) replaceDB(db *gorm.DB) memberCardLevel {
	m.memberCardLevelDo.ReplaceDB(db)
	return m
}

type memberCardLevelDo struct{ gen.DO }

type IMemberCardLevelDo interface {
	gen.SubQuery
	Debug() IMemberCardLevelDo
	WithContext(ctx context.Context) IMemberCardLevelDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMemberCardLevelDo
	WriteDB() IMemberCardLevelDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMemberCardLevelDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMemberCardLevelDo
	Not(conds ...gen.Condition) IMemberCardLevelDo
	Or(conds ...gen.Condition) IMemberCardLevelDo
	Select(conds ...field.Expr) IMemberCardLevelDo
	Where(conds ...gen.Condition) IMemberCardLevelDo
	Order(conds ...field.Expr) IMemberCardLevelDo
	Distinct(cols ...field.Expr) IMemberCardLevelDo
	Omit(cols ...field.Expr) IMemberCardLevelDo
	Join(table schema.Tabler, on ...field.Expr) IMemberCardLevelDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMemberCardLevelDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMemberCardLevelDo
	Group(cols ...field.Expr) IMemberCardLevelDo
	Having(conds ...gen.Condition) IMemberCardLevelDo
	Limit(limit int) IMemberCardLevelDo
	Offset(offset int) IMemberCardLevelDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMemberCardLevelDo
	Unscoped() IMemberCardLevelDo
	Create(values ...*po.MemberCardLevel) error
	CreateInBatches(values []*po.MemberCardLevel, batchSize int) error
	Save(values ...*po.MemberCardLevel) error
	First() (*po.MemberCardLevel, error)
	Take() (*po.MemberCardLevel, error)
	Last() (*po.MemberCardLevel, error)
	Find() ([]*po.MemberCardLevel, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.MemberCardLevel, err error)
	FindInBatches(result *[]*po.MemberCardLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.MemberCardLevel) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMemberCardLevelDo
	Assign(attrs ...field.AssignExpr) IMemberCardLevelDo
	Joins(fields ...field.RelationField) IMemberCardLevelDo
	Preload(fields ...field.RelationField) IMemberCardLevelDo
	FirstOrInit() (*po.MemberCardLevel, error)
	FirstOrCreate() (*po.MemberCardLevel, error)
	FindByPage(offset int, limit int) (result []*po.MemberCardLevel, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMemberCardLevelDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.MemberCardLevel, err error)
	DazzyQueryByAny(field string, value string) (result []*po.MemberCardLevel, err error)
	QueryByVenueId(venueId string) (result []*po.MemberCardLevel, err error)
}

// DELETE FROM @@table WHERE id = @id
func (m memberCardLevelDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM member_card_level WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (m memberCardLevelDo) QueryOneByID(id string) (result *po.MemberCardLevel, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM member_card_level WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (m memberCardLevelDo) DazzyQueryByAny(field string, value string) (result []*po.MemberCardLevel, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM member_card_level WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (m memberCardLevelDo) QueryByVenueId(venueId string) (result []*po.MemberCardLevel, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM member_card_level WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (m memberCardLevelDo) Debug() IMemberCardLevelDo {
	return m.withDO(m.DO.Debug())
}

func (m memberCardLevelDo) WithContext(ctx context.Context) IMemberCardLevelDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m memberCardLevelDo) ReadDB() IMemberCardLevelDo {
	return m.Clauses(dbresolver.Read)
}

func (m memberCardLevelDo) WriteDB() IMemberCardLevelDo {
	return m.Clauses(dbresolver.Write)
}

func (m memberCardLevelDo) Session(config *gorm.Session) IMemberCardLevelDo {
	return m.withDO(m.DO.Session(config))
}

func (m memberCardLevelDo) Clauses(conds ...clause.Expression) IMemberCardLevelDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m memberCardLevelDo) Returning(value interface{}, columns ...string) IMemberCardLevelDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m memberCardLevelDo) Not(conds ...gen.Condition) IMemberCardLevelDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m memberCardLevelDo) Or(conds ...gen.Condition) IMemberCardLevelDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m memberCardLevelDo) Select(conds ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m memberCardLevelDo) Where(conds ...gen.Condition) IMemberCardLevelDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m memberCardLevelDo) Order(conds ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m memberCardLevelDo) Distinct(cols ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m memberCardLevelDo) Omit(cols ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m memberCardLevelDo) Join(table schema.Tabler, on ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m memberCardLevelDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m memberCardLevelDo) RightJoin(table schema.Tabler, on ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m memberCardLevelDo) Group(cols ...field.Expr) IMemberCardLevelDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m memberCardLevelDo) Having(conds ...gen.Condition) IMemberCardLevelDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m memberCardLevelDo) Limit(limit int) IMemberCardLevelDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m memberCardLevelDo) Offset(offset int) IMemberCardLevelDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m memberCardLevelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMemberCardLevelDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m memberCardLevelDo) Unscoped() IMemberCardLevelDo {
	return m.withDO(m.DO.Unscoped())
}

func (m memberCardLevelDo) Create(values ...*po.MemberCardLevel) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m memberCardLevelDo) CreateInBatches(values []*po.MemberCardLevel, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m memberCardLevelDo) Save(values ...*po.MemberCardLevel) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m memberCardLevelDo) First() (*po.MemberCardLevel, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardLevel), nil
	}
}

func (m memberCardLevelDo) Take() (*po.MemberCardLevel, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardLevel), nil
	}
}

func (m memberCardLevelDo) Last() (*po.MemberCardLevel, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardLevel), nil
	}
}

func (m memberCardLevelDo) Find() ([]*po.MemberCardLevel, error) {
	result, err := m.DO.Find()
	return result.([]*po.MemberCardLevel), err
}

func (m memberCardLevelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.MemberCardLevel, err error) {
	buf := make([]*po.MemberCardLevel, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m memberCardLevelDo) FindInBatches(result *[]*po.MemberCardLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m memberCardLevelDo) Attrs(attrs ...field.AssignExpr) IMemberCardLevelDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m memberCardLevelDo) Assign(attrs ...field.AssignExpr) IMemberCardLevelDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m memberCardLevelDo) Joins(fields ...field.RelationField) IMemberCardLevelDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m memberCardLevelDo) Preload(fields ...field.RelationField) IMemberCardLevelDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m memberCardLevelDo) FirstOrInit() (*po.MemberCardLevel, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardLevel), nil
	}
}

func (m memberCardLevelDo) FirstOrCreate() (*po.MemberCardLevel, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardLevel), nil
	}
}

func (m memberCardLevelDo) FindByPage(offset int, limit int) (result []*po.MemberCardLevel, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m memberCardLevelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m memberCardLevelDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m memberCardLevelDo) Delete(models ...*po.MemberCardLevel) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *memberCardLevelDo) withDO(do gen.Dao) *memberCardLevelDo {
	m.DO = *do.(*gen.DO)
	return m
}
