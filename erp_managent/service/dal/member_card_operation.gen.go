// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newMemberCardOperation(db *gorm.DB, opts ...gen.DOOption) memberCardOperation {
	_memberCardOperation := memberCardOperation{}

	_memberCardOperation.memberCardOperationDo.UseDB(db, opts...)
	_memberCardOperation.memberCardOperationDo.UseModel(&po.MemberCardOperation{})

	tableName := _memberCardOperation.memberCardOperationDo.TableName()
	_memberCardOperation.ALL = field.NewAsterisk(tableName)
	_memberCardOperation.Id = field.NewString(tableName, "id")
	_memberCardOperation.VenueId = field.NewString(tableName, "venue_id")
	_memberCardOperation.VenueName = field.NewString(tableName, "venue_name")
	_memberCardOperation.MemberId = field.NewString(tableName, "member_id")
	_memberCardOperation.MemberCardId = field.NewString(tableName, "member_card_id")
	_memberCardOperation.CardName = field.NewString(tableName, "card_name")
	_memberCardOperation.CardPhone = field.NewString(tableName, "card_phone")
	_memberCardOperation.CardNumber = field.NewString(tableName, "card_number")
	_memberCardOperation.CardType = field.NewString(tableName, "card_type")
	_memberCardOperation.CardLevel = field.NewString(tableName, "card_level")
	_memberCardOperation.CardLevelName = field.NewString(tableName, "card_level_name")
	_memberCardOperation.OperationType = field.NewString(tableName, "operation_type")
	_memberCardOperation.Balance = field.NewInt64(tableName, "balance")
	_memberCardOperation.OperatorId = field.NewString(tableName, "operator_id")
	_memberCardOperation.OperatorName = field.NewString(tableName, "operator_name")
	_memberCardOperation.SellerId = field.NewString(tableName, "seller_id")
	_memberCardOperation.SellerName = field.NewString(tableName, "seller_name")
	_memberCardOperation.Info = field.NewString(tableName, "info")
	_memberCardOperation.BillId = field.NewString(tableName, "bill_id")
	_memberCardOperation.BillPid = field.NewString(tableName, "bill_pid")
	_memberCardOperation.TotalFee = field.NewInt64(tableName, "total_fee")
	_memberCardOperation.PrincipalAmount = field.NewInt64(tableName, "principal_amount")
	_memberCardOperation.MemberRoomBonusAmount = field.NewInt64(tableName, "member_room_bonus_amount")
	_memberCardOperation.MemberGoodsBonusAmount = field.NewInt64(tableName, "member_goods_bonus_amount")
	_memberCardOperation.MemberCommonBonusAmount = field.NewInt64(tableName, "member_common_bonus_amount")
	_memberCardOperation.PayTime = field.NewInt64(tableName, "pay_time")
	_memberCardOperation.Ctime = field.NewInt64(tableName, "ctime")
	_memberCardOperation.Utime = field.NewInt64(tableName, "utime")
	_memberCardOperation.State = field.NewInt(tableName, "state")
	_memberCardOperation.Version = field.NewInt(tableName, "version")

	_memberCardOperation.fillFieldMap()

	return _memberCardOperation
}

type memberCardOperation struct {
	memberCardOperationDo

	ALL                     field.Asterisk
	Id                      field.String
	VenueId                 field.String
	VenueName               field.String
	MemberId                field.String
	MemberCardId            field.String
	CardName                field.String
	CardPhone               field.String
	CardNumber              field.String
	CardType                field.String
	CardLevel               field.String
	CardLevelName           field.String
	OperationType           field.String
	Balance                 field.Int64
	OperatorId              field.String
	OperatorName            field.String
	SellerId                field.String
	SellerName              field.String
	Info                    field.String
	BillId                  field.String
	BillPid                 field.String
	TotalFee                field.Int64
	PrincipalAmount         field.Int64
	MemberRoomBonusAmount   field.Int64
	MemberGoodsBonusAmount  field.Int64
	MemberCommonBonusAmount field.Int64
	PayTime                 field.Int64
	Ctime                   field.Int64
	Utime                   field.Int64
	State                   field.Int
	Version                 field.Int

	fieldMap map[string]field.Expr
}

func (m memberCardOperation) Table(newTableName string) *memberCardOperation {
	m.memberCardOperationDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m memberCardOperation) As(alias string) *memberCardOperation {
	m.memberCardOperationDo.DO = *(m.memberCardOperationDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *memberCardOperation) updateTableName(table string) *memberCardOperation {
	m.ALL = field.NewAsterisk(table)
	m.Id = field.NewString(table, "id")
	m.VenueId = field.NewString(table, "venue_id")
	m.VenueName = field.NewString(table, "venue_name")
	m.MemberId = field.NewString(table, "member_id")
	m.MemberCardId = field.NewString(table, "member_card_id")
	m.CardName = field.NewString(table, "card_name")
	m.CardPhone = field.NewString(table, "card_phone")
	m.CardNumber = field.NewString(table, "card_number")
	m.CardType = field.NewString(table, "card_type")
	m.CardLevel = field.NewString(table, "card_level")
	m.CardLevelName = field.NewString(table, "card_level_name")
	m.OperationType = field.NewString(table, "operation_type")
	m.Balance = field.NewInt64(table, "balance")
	m.OperatorId = field.NewString(table, "operator_id")
	m.OperatorName = field.NewString(table, "operator_name")
	m.SellerId = field.NewString(table, "seller_id")
	m.SellerName = field.NewString(table, "seller_name")
	m.Info = field.NewString(table, "info")
	m.BillId = field.NewString(table, "bill_id")
	m.BillPid = field.NewString(table, "bill_pid")
	m.TotalFee = field.NewInt64(table, "total_fee")
	m.PrincipalAmount = field.NewInt64(table, "principal_amount")
	m.MemberRoomBonusAmount = field.NewInt64(table, "member_room_bonus_amount")
	m.MemberGoodsBonusAmount = field.NewInt64(table, "member_goods_bonus_amount")
	m.MemberCommonBonusAmount = field.NewInt64(table, "member_common_bonus_amount")
	m.PayTime = field.NewInt64(table, "pay_time")
	m.Ctime = field.NewInt64(table, "ctime")
	m.Utime = field.NewInt64(table, "utime")
	m.State = field.NewInt(table, "state")
	m.Version = field.NewInt(table, "version")

	m.fillFieldMap()

	return m
}

func (m *memberCardOperation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *memberCardOperation) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 30)
	m.fieldMap["id"] = m.Id
	m.fieldMap["venue_id"] = m.VenueId
	m.fieldMap["venue_name"] = m.VenueName
	m.fieldMap["member_id"] = m.MemberId
	m.fieldMap["member_card_id"] = m.MemberCardId
	m.fieldMap["card_name"] = m.CardName
	m.fieldMap["card_phone"] = m.CardPhone
	m.fieldMap["card_number"] = m.CardNumber
	m.fieldMap["card_type"] = m.CardType
	m.fieldMap["card_level"] = m.CardLevel
	m.fieldMap["card_level_name"] = m.CardLevelName
	m.fieldMap["operation_type"] = m.OperationType
	m.fieldMap["balance"] = m.Balance
	m.fieldMap["operator_id"] = m.OperatorId
	m.fieldMap["operator_name"] = m.OperatorName
	m.fieldMap["seller_id"] = m.SellerId
	m.fieldMap["seller_name"] = m.SellerName
	m.fieldMap["info"] = m.Info
	m.fieldMap["bill_id"] = m.BillId
	m.fieldMap["bill_pid"] = m.BillPid
	m.fieldMap["total_fee"] = m.TotalFee
	m.fieldMap["principal_amount"] = m.PrincipalAmount
	m.fieldMap["member_room_bonus_amount"] = m.MemberRoomBonusAmount
	m.fieldMap["member_goods_bonus_amount"] = m.MemberGoodsBonusAmount
	m.fieldMap["member_common_bonus_amount"] = m.MemberCommonBonusAmount
	m.fieldMap["pay_time"] = m.PayTime
	m.fieldMap["ctime"] = m.Ctime
	m.fieldMap["utime"] = m.Utime
	m.fieldMap["state"] = m.State
	m.fieldMap["version"] = m.Version
}

func (m memberCardOperation) clone(db *gorm.DB) memberCardOperation {
	m.memberCardOperationDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m memberCardOperation) replaceDB(db *gorm.DB) memberCardOperation {
	m.memberCardOperationDo.ReplaceDB(db)
	return m
}

type memberCardOperationDo struct{ gen.DO }

type IMemberCardOperationDo interface {
	gen.SubQuery
	Debug() IMemberCardOperationDo
	WithContext(ctx context.Context) IMemberCardOperationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMemberCardOperationDo
	WriteDB() IMemberCardOperationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMemberCardOperationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMemberCardOperationDo
	Not(conds ...gen.Condition) IMemberCardOperationDo
	Or(conds ...gen.Condition) IMemberCardOperationDo
	Select(conds ...field.Expr) IMemberCardOperationDo
	Where(conds ...gen.Condition) IMemberCardOperationDo
	Order(conds ...field.Expr) IMemberCardOperationDo
	Distinct(cols ...field.Expr) IMemberCardOperationDo
	Omit(cols ...field.Expr) IMemberCardOperationDo
	Join(table schema.Tabler, on ...field.Expr) IMemberCardOperationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMemberCardOperationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMemberCardOperationDo
	Group(cols ...field.Expr) IMemberCardOperationDo
	Having(conds ...gen.Condition) IMemberCardOperationDo
	Limit(limit int) IMemberCardOperationDo
	Offset(offset int) IMemberCardOperationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMemberCardOperationDo
	Unscoped() IMemberCardOperationDo
	Create(values ...*po.MemberCardOperation) error
	CreateInBatches(values []*po.MemberCardOperation, batchSize int) error
	Save(values ...*po.MemberCardOperation) error
	First() (*po.MemberCardOperation, error)
	Take() (*po.MemberCardOperation, error)
	Last() (*po.MemberCardOperation, error)
	Find() ([]*po.MemberCardOperation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.MemberCardOperation, err error)
	FindInBatches(result *[]*po.MemberCardOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.MemberCardOperation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMemberCardOperationDo
	Assign(attrs ...field.AssignExpr) IMemberCardOperationDo
	Joins(fields ...field.RelationField) IMemberCardOperationDo
	Preload(fields ...field.RelationField) IMemberCardOperationDo
	FirstOrInit() (*po.MemberCardOperation, error)
	FirstOrCreate() (*po.MemberCardOperation, error)
	FindByPage(offset int, limit int) (result []*po.MemberCardOperation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMemberCardOperationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.MemberCardOperation, err error)
	DazzyQueryByAny(field string, value string) (result []*po.MemberCardOperation, err error)
	QueryByVenueId(venueId string) (result []*po.MemberCardOperation, err error)
}

// DELETE FROM @@table WHERE id = @id
func (m memberCardOperationDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM member_card_operation WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (m memberCardOperationDo) QueryOneByID(id string) (result *po.MemberCardOperation, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM member_card_operation WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (m memberCardOperationDo) DazzyQueryByAny(field string, value string) (result []*po.MemberCardOperation, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM member_card_operation WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (m memberCardOperationDo) QueryByVenueId(venueId string) (result []*po.MemberCardOperation, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM member_card_operation WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = m.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (m memberCardOperationDo) Debug() IMemberCardOperationDo {
	return m.withDO(m.DO.Debug())
}

func (m memberCardOperationDo) WithContext(ctx context.Context) IMemberCardOperationDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m memberCardOperationDo) ReadDB() IMemberCardOperationDo {
	return m.Clauses(dbresolver.Read)
}

func (m memberCardOperationDo) WriteDB() IMemberCardOperationDo {
	return m.Clauses(dbresolver.Write)
}

func (m memberCardOperationDo) Session(config *gorm.Session) IMemberCardOperationDo {
	return m.withDO(m.DO.Session(config))
}

func (m memberCardOperationDo) Clauses(conds ...clause.Expression) IMemberCardOperationDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m memberCardOperationDo) Returning(value interface{}, columns ...string) IMemberCardOperationDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m memberCardOperationDo) Not(conds ...gen.Condition) IMemberCardOperationDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m memberCardOperationDo) Or(conds ...gen.Condition) IMemberCardOperationDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m memberCardOperationDo) Select(conds ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m memberCardOperationDo) Where(conds ...gen.Condition) IMemberCardOperationDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m memberCardOperationDo) Order(conds ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m memberCardOperationDo) Distinct(cols ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m memberCardOperationDo) Omit(cols ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m memberCardOperationDo) Join(table schema.Tabler, on ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m memberCardOperationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m memberCardOperationDo) RightJoin(table schema.Tabler, on ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m memberCardOperationDo) Group(cols ...field.Expr) IMemberCardOperationDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m memberCardOperationDo) Having(conds ...gen.Condition) IMemberCardOperationDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m memberCardOperationDo) Limit(limit int) IMemberCardOperationDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m memberCardOperationDo) Offset(offset int) IMemberCardOperationDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m memberCardOperationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMemberCardOperationDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m memberCardOperationDo) Unscoped() IMemberCardOperationDo {
	return m.withDO(m.DO.Unscoped())
}

func (m memberCardOperationDo) Create(values ...*po.MemberCardOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m memberCardOperationDo) CreateInBatches(values []*po.MemberCardOperation, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m memberCardOperationDo) Save(values ...*po.MemberCardOperation) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m memberCardOperationDo) First() (*po.MemberCardOperation, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardOperation), nil
	}
}

func (m memberCardOperationDo) Take() (*po.MemberCardOperation, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardOperation), nil
	}
}

func (m memberCardOperationDo) Last() (*po.MemberCardOperation, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardOperation), nil
	}
}

func (m memberCardOperationDo) Find() ([]*po.MemberCardOperation, error) {
	result, err := m.DO.Find()
	return result.([]*po.MemberCardOperation), err
}

func (m memberCardOperationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.MemberCardOperation, err error) {
	buf := make([]*po.MemberCardOperation, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m memberCardOperationDo) FindInBatches(result *[]*po.MemberCardOperation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m memberCardOperationDo) Attrs(attrs ...field.AssignExpr) IMemberCardOperationDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m memberCardOperationDo) Assign(attrs ...field.AssignExpr) IMemberCardOperationDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m memberCardOperationDo) Joins(fields ...field.RelationField) IMemberCardOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m memberCardOperationDo) Preload(fields ...field.RelationField) IMemberCardOperationDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m memberCardOperationDo) FirstOrInit() (*po.MemberCardOperation, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardOperation), nil
	}
}

func (m memberCardOperationDo) FirstOrCreate() (*po.MemberCardOperation, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.MemberCardOperation), nil
	}
}

func (m memberCardOperationDo) FindByPage(offset int, limit int) (result []*po.MemberCardOperation, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m memberCardOperationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m memberCardOperationDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m memberCardOperationDo) Delete(models ...*po.MemberCardOperation) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *memberCardOperationDo) withDO(do gen.Dao) *memberCardOperationDo {
	m.DO = *do.(*gen.DO)
	return m
}
