// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newPayBill(db *gorm.DB, opts ...gen.DOOption) payBill {
	_payBill := payBill{}

	_payBill.payBillDo.UseDB(db, opts...)
	_payBill.payBillDo.UseModel(&po.PayBill{})

	tableName := _payBill.payBillDo.TableName()
	_payBill.ALL = field.NewAsterisk(tableName)
	_payBill.Id = field.NewString(tableName, "id")
	_payBill.VenueId = field.NewString(tableName, "venue_id")
	_payBill.RoomId = field.NewString(tableName, "room_id")
	_payBill.EmployeeId = field.NewString(tableName, "employee_id")
	_payBill.MemberId = field.NewString(tableName, "member_id")
	_payBill.MemberCardId = field.NewString(tableName, "member_card_id")
	_payBill.MemberCardNumber = field.NewString(tableName, "member_card_number")
	_payBill.SessionId = field.NewString(tableName, "session_id")
	_payBill.BillId = field.NewString(tableName, "bill_id")
	_payBill.BillPid = field.NewString(tableName, "bill_pid")
	_payBill.OriginalFee = field.NewInt64(tableName, "original_fee")
	_payBill.ShouldFee = field.NewInt64(tableName, "should_fee")
	_payBill.TotalFee = field.NewInt64(tableName, "total_fee")
	_payBill.ZeroFee = field.NewInt64(tableName, "zero_fee")
	_payBill.CreditAmount = field.NewInt64(tableName, "credit_amount")
	_payBill.ChangeAmount = field.NewInt64(tableName, "change_amount")
	_payBill.ProductDiscount = field.NewInt64(tableName, "product_discount")
	_payBill.ProductDiscountFee = field.NewInt64(tableName, "product_discount_fee")
	_payBill.RoomDiscount = field.NewInt64(tableName, "room_discount")
	_payBill.RoomDiscountFee = field.NewInt64(tableName, "room_discount_fee")
	_payBill.IsFree = field.NewBool(tableName, "is_free")
	_payBill.IsGift = field.NewBool(tableName, "is_gift")
	_payBill.ProductDiscountAmount = field.NewInt64(tableName, "product_discount_amount")
	_payBill.RoomDiscountAmount = field.NewInt64(tableName, "room_discount_amount")
	_payBill.DiscountType = field.NewInt64(tableName, "discount_type")
	_payBill.ForceMinimumCharge = field.NewBool(tableName, "force_minimum_charge")
	_payBill.RefundWay = field.NewString(tableName, "refund_way")
	_payBill.DiscountReason = field.NewString(tableName, "discount_reason")
	_payBill.GiftEmployeeId = field.NewString(tableName, "gift_employee_id")
	_payBill.GiftEmployeeName = field.NewString(tableName, "gift_employee_name")
	_payBill.GiftReason = field.NewString(tableName, "gift_reason")
	_payBill.CancelEmployeeId = field.NewString(tableName, "cancel_employee_id")
	_payBill.CancelEmployeeName = field.NewString(tableName, "cancel_employee_name")
	_payBill.CancelReason = field.NewString(tableName, "cancel_reason")
	_payBill.Direction = field.NewString(tableName, "direction")
	_payBill.Status = field.NewString(tableName, "status")
	_payBill.IsBack = field.NewBool(tableName, "is_back")
	_payBill.IsCancel = field.NewBool(tableName, "is_cancel")
	_payBill.Info = field.NewString(tableName, "info")
	_payBill.FinishTime = field.NewInt64(tableName, "finish_time")
	_payBill.BillDate = field.NewInt64(tableName, "bill_date")
	_payBill.Ctime = field.NewInt64(tableName, "ctime")
	_payBill.Utime = field.NewInt64(tableName, "utime")
	_payBill.State = field.NewInt(tableName, "state")
	_payBill.Version = field.NewInt(tableName, "version")

	_payBill.fillFieldMap()

	return _payBill
}

type payBill struct {
	payBillDo

	ALL                   field.Asterisk
	Id                    field.String
	VenueId               field.String
	RoomId                field.String
	EmployeeId            field.String
	MemberId              field.String
	MemberCardId          field.String
	MemberCardNumber      field.String
	SessionId             field.String
	BillId                field.String
	BillPid               field.String
	OriginalFee           field.Int64
	ShouldFee             field.Int64
	TotalFee              field.Int64
	ZeroFee               field.Int64
	CreditAmount          field.Int64
	ChangeAmount          field.Int64
	ProductDiscount       field.Int64
	ProductDiscountFee    field.Int64
	RoomDiscount          field.Int64
	RoomDiscountFee       field.Int64
	IsFree                field.Bool
	IsGift                field.Bool
	ProductDiscountAmount field.Int64
	RoomDiscountAmount    field.Int64
	DiscountType          field.Int64
	ForceMinimumCharge    field.Bool
	RefundWay             field.String
	DiscountReason        field.String
	GiftEmployeeId        field.String
	GiftEmployeeName      field.String
	GiftReason            field.String
	CancelEmployeeId      field.String
	CancelEmployeeName    field.String
	CancelReason          field.String
	Direction             field.String
	Status                field.String
	IsBack                field.Bool
	IsCancel              field.Bool
	Info                  field.String
	FinishTime            field.Int64
	BillDate              field.Int64
	Ctime                 field.Int64
	Utime                 field.Int64
	State                 field.Int
	Version               field.Int

	fieldMap map[string]field.Expr
}

func (p payBill) Table(newTableName string) *payBill {
	p.payBillDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p payBill) As(alias string) *payBill {
	p.payBillDo.DO = *(p.payBillDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *payBill) updateTableName(table string) *payBill {
	p.ALL = field.NewAsterisk(table)
	p.Id = field.NewString(table, "id")
	p.VenueId = field.NewString(table, "venue_id")
	p.RoomId = field.NewString(table, "room_id")
	p.EmployeeId = field.NewString(table, "employee_id")
	p.MemberId = field.NewString(table, "member_id")
	p.MemberCardId = field.NewString(table, "member_card_id")
	p.MemberCardNumber = field.NewString(table, "member_card_number")
	p.SessionId = field.NewString(table, "session_id")
	p.BillId = field.NewString(table, "bill_id")
	p.BillPid = field.NewString(table, "bill_pid")
	p.OriginalFee = field.NewInt64(table, "original_fee")
	p.ShouldFee = field.NewInt64(table, "should_fee")
	p.TotalFee = field.NewInt64(table, "total_fee")
	p.ZeroFee = field.NewInt64(table, "zero_fee")
	p.CreditAmount = field.NewInt64(table, "credit_amount")
	p.ChangeAmount = field.NewInt64(table, "change_amount")
	p.ProductDiscount = field.NewInt64(table, "product_discount")
	p.ProductDiscountFee = field.NewInt64(table, "product_discount_fee")
	p.RoomDiscount = field.NewInt64(table, "room_discount")
	p.RoomDiscountFee = field.NewInt64(table, "room_discount_fee")
	p.IsFree = field.NewBool(table, "is_free")
	p.IsGift = field.NewBool(table, "is_gift")
	p.ProductDiscountAmount = field.NewInt64(table, "product_discount_amount")
	p.RoomDiscountAmount = field.NewInt64(table, "room_discount_amount")
	p.DiscountType = field.NewInt64(table, "discount_type")
	p.ForceMinimumCharge = field.NewBool(table, "force_minimum_charge")
	p.RefundWay = field.NewString(table, "refund_way")
	p.DiscountReason = field.NewString(table, "discount_reason")
	p.GiftEmployeeId = field.NewString(table, "gift_employee_id")
	p.GiftEmployeeName = field.NewString(table, "gift_employee_name")
	p.GiftReason = field.NewString(table, "gift_reason")
	p.CancelEmployeeId = field.NewString(table, "cancel_employee_id")
	p.CancelEmployeeName = field.NewString(table, "cancel_employee_name")
	p.CancelReason = field.NewString(table, "cancel_reason")
	p.Direction = field.NewString(table, "direction")
	p.Status = field.NewString(table, "status")
	p.IsBack = field.NewBool(table, "is_back")
	p.IsCancel = field.NewBool(table, "is_cancel")
	p.Info = field.NewString(table, "info")
	p.FinishTime = field.NewInt64(table, "finish_time")
	p.BillDate = field.NewInt64(table, "bill_date")
	p.Ctime = field.NewInt64(table, "ctime")
	p.Utime = field.NewInt64(table, "utime")
	p.State = field.NewInt(table, "state")
	p.Version = field.NewInt(table, "version")

	p.fillFieldMap()

	return p
}

func (p *payBill) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *payBill) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 45)
	p.fieldMap["id"] = p.Id
	p.fieldMap["venue_id"] = p.VenueId
	p.fieldMap["room_id"] = p.RoomId
	p.fieldMap["employee_id"] = p.EmployeeId
	p.fieldMap["member_id"] = p.MemberId
	p.fieldMap["member_card_id"] = p.MemberCardId
	p.fieldMap["member_card_number"] = p.MemberCardNumber
	p.fieldMap["session_id"] = p.SessionId
	p.fieldMap["bill_id"] = p.BillId
	p.fieldMap["bill_pid"] = p.BillPid
	p.fieldMap["original_fee"] = p.OriginalFee
	p.fieldMap["should_fee"] = p.ShouldFee
	p.fieldMap["total_fee"] = p.TotalFee
	p.fieldMap["zero_fee"] = p.ZeroFee
	p.fieldMap["credit_amount"] = p.CreditAmount
	p.fieldMap["change_amount"] = p.ChangeAmount
	p.fieldMap["product_discount"] = p.ProductDiscount
	p.fieldMap["product_discount_fee"] = p.ProductDiscountFee
	p.fieldMap["room_discount"] = p.RoomDiscount
	p.fieldMap["room_discount_fee"] = p.RoomDiscountFee
	p.fieldMap["is_free"] = p.IsFree
	p.fieldMap["is_gift"] = p.IsGift
	p.fieldMap["product_discount_amount"] = p.ProductDiscountAmount
	p.fieldMap["room_discount_amount"] = p.RoomDiscountAmount
	p.fieldMap["discount_type"] = p.DiscountType
	p.fieldMap["force_minimum_charge"] = p.ForceMinimumCharge
	p.fieldMap["refund_way"] = p.RefundWay
	p.fieldMap["discount_reason"] = p.DiscountReason
	p.fieldMap["gift_employee_id"] = p.GiftEmployeeId
	p.fieldMap["gift_employee_name"] = p.GiftEmployeeName
	p.fieldMap["gift_reason"] = p.GiftReason
	p.fieldMap["cancel_employee_id"] = p.CancelEmployeeId
	p.fieldMap["cancel_employee_name"] = p.CancelEmployeeName
	p.fieldMap["cancel_reason"] = p.CancelReason
	p.fieldMap["direction"] = p.Direction
	p.fieldMap["status"] = p.Status
	p.fieldMap["is_back"] = p.IsBack
	p.fieldMap["is_cancel"] = p.IsCancel
	p.fieldMap["info"] = p.Info
	p.fieldMap["finish_time"] = p.FinishTime
	p.fieldMap["bill_date"] = p.BillDate
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["utime"] = p.Utime
	p.fieldMap["state"] = p.State
	p.fieldMap["version"] = p.Version
}

func (p payBill) clone(db *gorm.DB) payBill {
	p.payBillDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p payBill) replaceDB(db *gorm.DB) payBill {
	p.payBillDo.ReplaceDB(db)
	return p
}

type payBillDo struct{ gen.DO }

type IPayBillDo interface {
	gen.SubQuery
	Debug() IPayBillDo
	WithContext(ctx context.Context) IPayBillDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPayBillDo
	WriteDB() IPayBillDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPayBillDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPayBillDo
	Not(conds ...gen.Condition) IPayBillDo
	Or(conds ...gen.Condition) IPayBillDo
	Select(conds ...field.Expr) IPayBillDo
	Where(conds ...gen.Condition) IPayBillDo
	Order(conds ...field.Expr) IPayBillDo
	Distinct(cols ...field.Expr) IPayBillDo
	Omit(cols ...field.Expr) IPayBillDo
	Join(table schema.Tabler, on ...field.Expr) IPayBillDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPayBillDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPayBillDo
	Group(cols ...field.Expr) IPayBillDo
	Having(conds ...gen.Condition) IPayBillDo
	Limit(limit int) IPayBillDo
	Offset(offset int) IPayBillDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPayBillDo
	Unscoped() IPayBillDo
	Create(values ...*po.PayBill) error
	CreateInBatches(values []*po.PayBill, batchSize int) error
	Save(values ...*po.PayBill) error
	First() (*po.PayBill, error)
	Take() (*po.PayBill, error)
	Last() (*po.PayBill, error)
	Find() ([]*po.PayBill, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.PayBill, err error)
	FindInBatches(result *[]*po.PayBill, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.PayBill) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPayBillDo
	Assign(attrs ...field.AssignExpr) IPayBillDo
	Joins(fields ...field.RelationField) IPayBillDo
	Preload(fields ...field.RelationField) IPayBillDo
	FirstOrInit() (*po.PayBill, error)
	FirstOrCreate() (*po.PayBill, error)
	FindByPage(offset int, limit int) (result []*po.PayBill, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPayBillDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.PayBill, err error)
	DazzyQueryByAny(field string, value string) (result []*po.PayBill, err error)
	QueryByVenueId(venueId string) (result []*po.PayBill, err error)
}

// DELETE FROM @@table WHERE id = @id
func (p payBillDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM pay_bill WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (p payBillDo) QueryOneByID(id string) (result *po.PayBill, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM pay_bill WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (p payBillDo) DazzyQueryByAny(field string, value string) (result []*po.PayBill, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM pay_bill WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (p payBillDo) QueryByVenueId(venueId string) (result []*po.PayBill, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM pay_bill WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p payBillDo) Debug() IPayBillDo {
	return p.withDO(p.DO.Debug())
}

func (p payBillDo) WithContext(ctx context.Context) IPayBillDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p payBillDo) ReadDB() IPayBillDo {
	return p.Clauses(dbresolver.Read)
}

func (p payBillDo) WriteDB() IPayBillDo {
	return p.Clauses(dbresolver.Write)
}

func (p payBillDo) Session(config *gorm.Session) IPayBillDo {
	return p.withDO(p.DO.Session(config))
}

func (p payBillDo) Clauses(conds ...clause.Expression) IPayBillDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p payBillDo) Returning(value interface{}, columns ...string) IPayBillDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p payBillDo) Not(conds ...gen.Condition) IPayBillDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p payBillDo) Or(conds ...gen.Condition) IPayBillDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p payBillDo) Select(conds ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p payBillDo) Where(conds ...gen.Condition) IPayBillDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p payBillDo) Order(conds ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p payBillDo) Distinct(cols ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p payBillDo) Omit(cols ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p payBillDo) Join(table schema.Tabler, on ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p payBillDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p payBillDo) RightJoin(table schema.Tabler, on ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p payBillDo) Group(cols ...field.Expr) IPayBillDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p payBillDo) Having(conds ...gen.Condition) IPayBillDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p payBillDo) Limit(limit int) IPayBillDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p payBillDo) Offset(offset int) IPayBillDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p payBillDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPayBillDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p payBillDo) Unscoped() IPayBillDo {
	return p.withDO(p.DO.Unscoped())
}

func (p payBillDo) Create(values ...*po.PayBill) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p payBillDo) CreateInBatches(values []*po.PayBill, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p payBillDo) Save(values ...*po.PayBill) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p payBillDo) First() (*po.PayBill, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.PayBill), nil
	}
}

func (p payBillDo) Take() (*po.PayBill, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.PayBill), nil
	}
}

func (p payBillDo) Last() (*po.PayBill, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.PayBill), nil
	}
}

func (p payBillDo) Find() ([]*po.PayBill, error) {
	result, err := p.DO.Find()
	return result.([]*po.PayBill), err
}

func (p payBillDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.PayBill, err error) {
	buf := make([]*po.PayBill, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p payBillDo) FindInBatches(result *[]*po.PayBill, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p payBillDo) Attrs(attrs ...field.AssignExpr) IPayBillDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p payBillDo) Assign(attrs ...field.AssignExpr) IPayBillDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p payBillDo) Joins(fields ...field.RelationField) IPayBillDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p payBillDo) Preload(fields ...field.RelationField) IPayBillDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p payBillDo) FirstOrInit() (*po.PayBill, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.PayBill), nil
	}
}

func (p payBillDo) FirstOrCreate() (*po.PayBill, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.PayBill), nil
	}
}

func (p payBillDo) FindByPage(offset int, limit int) (result []*po.PayBill, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p payBillDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p payBillDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p payBillDo) Delete(models ...*po.PayBill) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *payBillDo) withDO(do gen.Dao) *payBillDo {
	p.DO = *do.(*gen.DO)
	return p
}
