// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newPermissionResource(db *gorm.DB, opts ...gen.DOOption) permissionResource {
	_permissionResource := permissionResource{}

	_permissionResource.permissionResourceDo.UseDB(db, opts...)
	_permissionResource.permissionResourceDo.UseModel(&po.PermissionResource{})

	tableName := _permissionResource.permissionResourceDo.TableName()
	_permissionResource.ALL = field.NewAsterisk(tableName)
	_permissionResource.Id = field.NewString(tableName, "id")
	_permissionResource.ParentId = field.NewString(tableName, "parent_id")
	_permissionResource.Name = field.NewString(tableName, "name")
	_permissionResource.Code = field.NewString(tableName, "code")
	_permissionResource.Type = field.NewString(tableName, "type")
	_permissionResource.Path = field.NewString(tableName, "path")
	_permissionResource.Icon = field.NewString(tableName, "icon")
	_permissionResource.Sort = field.NewInt(tableName, "sort")
	_permissionResource.Description = field.NewString(tableName, "description")
	_permissionResource.IsEnabled = field.NewBool(tableName, "is_enabled")
	_permissionResource.VenueId = field.NewString(tableName, "venue_id")
	_permissionResource.Ctime = field.NewInt64(tableName, "ctime")
	_permissionResource.Utime = field.NewInt64(tableName, "utime")
	_permissionResource.State = field.NewInt(tableName, "state")
	_permissionResource.Version = field.NewInt(tableName, "version")

	_permissionResource.fillFieldMap()

	return _permissionResource
}

type permissionResource struct {
	permissionResourceDo

	ALL         field.Asterisk
	Id          field.String
	ParentId    field.String
	Name        field.String
	Code        field.String
	Type        field.String
	Path        field.String
	Icon        field.String
	Sort        field.Int
	Description field.String
	IsEnabled   field.Bool
	VenueId     field.String
	Ctime       field.Int64
	Utime       field.Int64
	State       field.Int
	Version     field.Int

	fieldMap map[string]field.Expr
}

func (p permissionResource) Table(newTableName string) *permissionResource {
	p.permissionResourceDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p permissionResource) As(alias string) *permissionResource {
	p.permissionResourceDo.DO = *(p.permissionResourceDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *permissionResource) updateTableName(table string) *permissionResource {
	p.ALL = field.NewAsterisk(table)
	p.Id = field.NewString(table, "id")
	p.ParentId = field.NewString(table, "parent_id")
	p.Name = field.NewString(table, "name")
	p.Code = field.NewString(table, "code")
	p.Type = field.NewString(table, "type")
	p.Path = field.NewString(table, "path")
	p.Icon = field.NewString(table, "icon")
	p.Sort = field.NewInt(table, "sort")
	p.Description = field.NewString(table, "description")
	p.IsEnabled = field.NewBool(table, "is_enabled")
	p.VenueId = field.NewString(table, "venue_id")
	p.Ctime = field.NewInt64(table, "ctime")
	p.Utime = field.NewInt64(table, "utime")
	p.State = field.NewInt(table, "state")
	p.Version = field.NewInt(table, "version")

	p.fillFieldMap()

	return p
}

func (p *permissionResource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *permissionResource) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 15)
	p.fieldMap["id"] = p.Id
	p.fieldMap["parent_id"] = p.ParentId
	p.fieldMap["name"] = p.Name
	p.fieldMap["code"] = p.Code
	p.fieldMap["type"] = p.Type
	p.fieldMap["path"] = p.Path
	p.fieldMap["icon"] = p.Icon
	p.fieldMap["sort"] = p.Sort
	p.fieldMap["description"] = p.Description
	p.fieldMap["is_enabled"] = p.IsEnabled
	p.fieldMap["venue_id"] = p.VenueId
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["utime"] = p.Utime
	p.fieldMap["state"] = p.State
	p.fieldMap["version"] = p.Version
}

func (p permissionResource) clone(db *gorm.DB) permissionResource {
	p.permissionResourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p permissionResource) replaceDB(db *gorm.DB) permissionResource {
	p.permissionResourceDo.ReplaceDB(db)
	return p
}

type permissionResourceDo struct{ gen.DO }

type IPermissionResourceDo interface {
	gen.SubQuery
	Debug() IPermissionResourceDo
	WithContext(ctx context.Context) IPermissionResourceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPermissionResourceDo
	WriteDB() IPermissionResourceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPermissionResourceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPermissionResourceDo
	Not(conds ...gen.Condition) IPermissionResourceDo
	Or(conds ...gen.Condition) IPermissionResourceDo
	Select(conds ...field.Expr) IPermissionResourceDo
	Where(conds ...gen.Condition) IPermissionResourceDo
	Order(conds ...field.Expr) IPermissionResourceDo
	Distinct(cols ...field.Expr) IPermissionResourceDo
	Omit(cols ...field.Expr) IPermissionResourceDo
	Join(table schema.Tabler, on ...field.Expr) IPermissionResourceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPermissionResourceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPermissionResourceDo
	Group(cols ...field.Expr) IPermissionResourceDo
	Having(conds ...gen.Condition) IPermissionResourceDo
	Limit(limit int) IPermissionResourceDo
	Offset(offset int) IPermissionResourceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPermissionResourceDo
	Unscoped() IPermissionResourceDo
	Create(values ...*po.PermissionResource) error
	CreateInBatches(values []*po.PermissionResource, batchSize int) error
	Save(values ...*po.PermissionResource) error
	First() (*po.PermissionResource, error)
	Take() (*po.PermissionResource, error)
	Last() (*po.PermissionResource, error)
	Find() ([]*po.PermissionResource, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.PermissionResource, err error)
	FindInBatches(result *[]*po.PermissionResource, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.PermissionResource) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPermissionResourceDo
	Assign(attrs ...field.AssignExpr) IPermissionResourceDo
	Joins(fields ...field.RelationField) IPermissionResourceDo
	Preload(fields ...field.RelationField) IPermissionResourceDo
	FirstOrInit() (*po.PermissionResource, error)
	FirstOrCreate() (*po.PermissionResource, error)
	FindByPage(offset int, limit int) (result []*po.PermissionResource, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPermissionResourceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.PermissionResource, err error)
	DazzyQueryByAny(field string, value string) (result []*po.PermissionResource, err error)
	QueryByVenueId(venueId string) (result []*po.PermissionResource, err error)
}

// DELETE FROM @@table WHERE id = @id
func (p permissionResourceDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM permission_resource WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (p permissionResourceDo) QueryOneByID(id string) (result *po.PermissionResource, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM permission_resource WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (p permissionResourceDo) DazzyQueryByAny(field string, value string) (result []*po.PermissionResource, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM permission_resource WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (p permissionResourceDo) QueryByVenueId(venueId string) (result []*po.PermissionResource, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM permission_resource WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p permissionResourceDo) Debug() IPermissionResourceDo {
	return p.withDO(p.DO.Debug())
}

func (p permissionResourceDo) WithContext(ctx context.Context) IPermissionResourceDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p permissionResourceDo) ReadDB() IPermissionResourceDo {
	return p.Clauses(dbresolver.Read)
}

func (p permissionResourceDo) WriteDB() IPermissionResourceDo {
	return p.Clauses(dbresolver.Write)
}

func (p permissionResourceDo) Session(config *gorm.Session) IPermissionResourceDo {
	return p.withDO(p.DO.Session(config))
}

func (p permissionResourceDo) Clauses(conds ...clause.Expression) IPermissionResourceDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p permissionResourceDo) Returning(value interface{}, columns ...string) IPermissionResourceDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p permissionResourceDo) Not(conds ...gen.Condition) IPermissionResourceDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p permissionResourceDo) Or(conds ...gen.Condition) IPermissionResourceDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p permissionResourceDo) Select(conds ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p permissionResourceDo) Where(conds ...gen.Condition) IPermissionResourceDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p permissionResourceDo) Order(conds ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p permissionResourceDo) Distinct(cols ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p permissionResourceDo) Omit(cols ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p permissionResourceDo) Join(table schema.Tabler, on ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p permissionResourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p permissionResourceDo) RightJoin(table schema.Tabler, on ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p permissionResourceDo) Group(cols ...field.Expr) IPermissionResourceDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p permissionResourceDo) Having(conds ...gen.Condition) IPermissionResourceDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p permissionResourceDo) Limit(limit int) IPermissionResourceDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p permissionResourceDo) Offset(offset int) IPermissionResourceDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p permissionResourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPermissionResourceDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p permissionResourceDo) Unscoped() IPermissionResourceDo {
	return p.withDO(p.DO.Unscoped())
}

func (p permissionResourceDo) Create(values ...*po.PermissionResource) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p permissionResourceDo) CreateInBatches(values []*po.PermissionResource, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p permissionResourceDo) Save(values ...*po.PermissionResource) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p permissionResourceDo) First() (*po.PermissionResource, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.PermissionResource), nil
	}
}

func (p permissionResourceDo) Take() (*po.PermissionResource, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.PermissionResource), nil
	}
}

func (p permissionResourceDo) Last() (*po.PermissionResource, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.PermissionResource), nil
	}
}

func (p permissionResourceDo) Find() ([]*po.PermissionResource, error) {
	result, err := p.DO.Find()
	return result.([]*po.PermissionResource), err
}

func (p permissionResourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.PermissionResource, err error) {
	buf := make([]*po.PermissionResource, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p permissionResourceDo) FindInBatches(result *[]*po.PermissionResource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p permissionResourceDo) Attrs(attrs ...field.AssignExpr) IPermissionResourceDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p permissionResourceDo) Assign(attrs ...field.AssignExpr) IPermissionResourceDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p permissionResourceDo) Joins(fields ...field.RelationField) IPermissionResourceDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p permissionResourceDo) Preload(fields ...field.RelationField) IPermissionResourceDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p permissionResourceDo) FirstOrInit() (*po.PermissionResource, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.PermissionResource), nil
	}
}

func (p permissionResourceDo) FirstOrCreate() (*po.PermissionResource, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.PermissionResource), nil
	}
}

func (p permissionResourceDo) FindByPage(offset int, limit int) (result []*po.PermissionResource, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p permissionResourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p permissionResourceDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p permissionResourceDo) Delete(models ...*po.PermissionResource) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *permissionResourceDo) withDO(do gen.Dao) *permissionResourceDo {
	p.DO = *do.(*gen.DO)
	return p
}
