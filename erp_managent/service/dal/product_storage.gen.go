// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newProductStorage(db *gorm.DB, opts ...gen.DOOption) productStorage {
	_productStorage := productStorage{}

	_productStorage.productStorageDo.UseDB(db, opts...)
	_productStorage.productStorageDo.UseModel(&po.ProductStorage{})

	tableName := _productStorage.productStorageDo.TableName()
	_productStorage.ALL = field.NewAsterisk(tableName)
	_productStorage.Id = field.NewString(tableName, "id")
	_productStorage.OrderNo = field.NewString(tableName, "order_no")
	_productStorage.VenueId = field.NewString(tableName, "venue_id")
	_productStorage.CustomerId = field.NewString(tableName, "customer_id")
	_productStorage.CustomerName = field.NewString(tableName, "customer_name")
	_productStorage.PhoneNumber = field.NewString(tableName, "phone_number")
	_productStorage.MemberCardNo = field.NewString(tableName, "member_card_no")
	_productStorage.MemberCardNumber = field.NewString(tableName, "member_card_number")
	_productStorage.MemberCardId = field.NewString(tableName, "member_card_id")
	_productStorage.ProductId = field.NewString(tableName, "product_id")
	_productStorage.ProductName = field.NewString(tableName, "product_name")
	_productStorage.ProductType = field.NewString(tableName, "product_type")
	_productStorage.ProductUnit = field.NewString(tableName, "product_unit")
	_productStorage.ProductSpec = field.NewString(tableName, "product_spec")
	_productStorage.Quantity = field.NewInt(tableName, "quantity")
	_productStorage.RemainingQty = field.NewInt(tableName, "remaining_qty")
	_productStorage.StorageLocation = field.NewString(tableName, "storage_location")
	_productStorage.StorageRoomId = field.NewString(tableName, "storage_room_id")
	_productStorage.StorageRoomName = field.NewString(tableName, "storage_room_name")
	_productStorage.StorageTime = field.NewInt64(tableName, "storage_time")
	_productStorage.ExpireTime = field.NewInt64(tableName, "expire_time")
	_productStorage.Remark = field.NewString(tableName, "remark")
	_productStorage.ParentOrderNo = field.NewString(tableName, "parent_order_no")
	_productStorage.StatusCode = field.NewString(tableName, "status_code")
	_productStorage.StatusName = field.NewString(tableName, "status_name")
	_productStorage.LastOperationTime = field.NewInt64(tableName, "last_operation_time")
	_productStorage.OperatorId = field.NewString(tableName, "operator_id")
	_productStorage.OperatorName = field.NewString(tableName, "operator_name")
	_productStorage.IsBatch = field.NewInt(tableName, "is_batch")
	_productStorage.BatchTime = field.NewInt64(tableName, "batch_time")
	_productStorage.Ctime = field.NewInt64(tableName, "ctime")
	_productStorage.Utime = field.NewInt64(tableName, "utime")
	_productStorage.State = field.NewInt(tableName, "state")
	_productStorage.Version = field.NewInt(tableName, "version")

	_productStorage.fillFieldMap()

	return _productStorage
}

type productStorage struct {
	productStorageDo

	ALL               field.Asterisk
	Id                field.String
	OrderNo           field.String
	VenueId           field.String
	CustomerId        field.String
	CustomerName      field.String
	PhoneNumber       field.String
	MemberCardNo      field.String
	MemberCardNumber  field.String
	MemberCardId      field.String
	ProductId         field.String
	ProductName       field.String
	ProductType       field.String
	ProductUnit       field.String
	ProductSpec       field.String
	Quantity          field.Int
	RemainingQty      field.Int
	StorageLocation   field.String
	StorageRoomId     field.String
	StorageRoomName   field.String
	StorageTime       field.Int64
	ExpireTime        field.Int64
	Remark            field.String
	ParentOrderNo     field.String
	StatusCode        field.String
	StatusName        field.String
	LastOperationTime field.Int64
	OperatorId        field.String
	OperatorName      field.String
	IsBatch           field.Int
	BatchTime         field.Int64
	Ctime             field.Int64
	Utime             field.Int64
	State             field.Int
	Version           field.Int

	fieldMap map[string]field.Expr
}

func (p productStorage) Table(newTableName string) *productStorage {
	p.productStorageDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productStorage) As(alias string) *productStorage {
	p.productStorageDo.DO = *(p.productStorageDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productStorage) updateTableName(table string) *productStorage {
	p.ALL = field.NewAsterisk(table)
	p.Id = field.NewString(table, "id")
	p.OrderNo = field.NewString(table, "order_no")
	p.VenueId = field.NewString(table, "venue_id")
	p.CustomerId = field.NewString(table, "customer_id")
	p.CustomerName = field.NewString(table, "customer_name")
	p.PhoneNumber = field.NewString(table, "phone_number")
	p.MemberCardNo = field.NewString(table, "member_card_no")
	p.MemberCardNumber = field.NewString(table, "member_card_number")
	p.MemberCardId = field.NewString(table, "member_card_id")
	p.ProductId = field.NewString(table, "product_id")
	p.ProductName = field.NewString(table, "product_name")
	p.ProductType = field.NewString(table, "product_type")
	p.ProductUnit = field.NewString(table, "product_unit")
	p.ProductSpec = field.NewString(table, "product_spec")
	p.Quantity = field.NewInt(table, "quantity")
	p.RemainingQty = field.NewInt(table, "remaining_qty")
	p.StorageLocation = field.NewString(table, "storage_location")
	p.StorageRoomId = field.NewString(table, "storage_room_id")
	p.StorageRoomName = field.NewString(table, "storage_room_name")
	p.StorageTime = field.NewInt64(table, "storage_time")
	p.ExpireTime = field.NewInt64(table, "expire_time")
	p.Remark = field.NewString(table, "remark")
	p.ParentOrderNo = field.NewString(table, "parent_order_no")
	p.StatusCode = field.NewString(table, "status_code")
	p.StatusName = field.NewString(table, "status_name")
	p.LastOperationTime = field.NewInt64(table, "last_operation_time")
	p.OperatorId = field.NewString(table, "operator_id")
	p.OperatorName = field.NewString(table, "operator_name")
	p.IsBatch = field.NewInt(table, "is_batch")
	p.BatchTime = field.NewInt64(table, "batch_time")
	p.Ctime = field.NewInt64(table, "ctime")
	p.Utime = field.NewInt64(table, "utime")
	p.State = field.NewInt(table, "state")
	p.Version = field.NewInt(table, "version")

	p.fillFieldMap()

	return p
}

func (p *productStorage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productStorage) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 34)
	p.fieldMap["id"] = p.Id
	p.fieldMap["order_no"] = p.OrderNo
	p.fieldMap["venue_id"] = p.VenueId
	p.fieldMap["customer_id"] = p.CustomerId
	p.fieldMap["customer_name"] = p.CustomerName
	p.fieldMap["phone_number"] = p.PhoneNumber
	p.fieldMap["member_card_no"] = p.MemberCardNo
	p.fieldMap["member_card_number"] = p.MemberCardNumber
	p.fieldMap["member_card_id"] = p.MemberCardId
	p.fieldMap["product_id"] = p.ProductId
	p.fieldMap["product_name"] = p.ProductName
	p.fieldMap["product_type"] = p.ProductType
	p.fieldMap["product_unit"] = p.ProductUnit
	p.fieldMap["product_spec"] = p.ProductSpec
	p.fieldMap["quantity"] = p.Quantity
	p.fieldMap["remaining_qty"] = p.RemainingQty
	p.fieldMap["storage_location"] = p.StorageLocation
	p.fieldMap["storage_room_id"] = p.StorageRoomId
	p.fieldMap["storage_room_name"] = p.StorageRoomName
	p.fieldMap["storage_time"] = p.StorageTime
	p.fieldMap["expire_time"] = p.ExpireTime
	p.fieldMap["remark"] = p.Remark
	p.fieldMap["parent_order_no"] = p.ParentOrderNo
	p.fieldMap["status_code"] = p.StatusCode
	p.fieldMap["status_name"] = p.StatusName
	p.fieldMap["last_operation_time"] = p.LastOperationTime
	p.fieldMap["operator_id"] = p.OperatorId
	p.fieldMap["operator_name"] = p.OperatorName
	p.fieldMap["is_batch"] = p.IsBatch
	p.fieldMap["batch_time"] = p.BatchTime
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["utime"] = p.Utime
	p.fieldMap["state"] = p.State
	p.fieldMap["version"] = p.Version
}

func (p productStorage) clone(db *gorm.DB) productStorage {
	p.productStorageDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p productStorage) replaceDB(db *gorm.DB) productStorage {
	p.productStorageDo.ReplaceDB(db)
	return p
}

type productStorageDo struct{ gen.DO }

type IProductStorageDo interface {
	gen.SubQuery
	Debug() IProductStorageDo
	WithContext(ctx context.Context) IProductStorageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IProductStorageDo
	WriteDB() IProductStorageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IProductStorageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IProductStorageDo
	Not(conds ...gen.Condition) IProductStorageDo
	Or(conds ...gen.Condition) IProductStorageDo
	Select(conds ...field.Expr) IProductStorageDo
	Where(conds ...gen.Condition) IProductStorageDo
	Order(conds ...field.Expr) IProductStorageDo
	Distinct(cols ...field.Expr) IProductStorageDo
	Omit(cols ...field.Expr) IProductStorageDo
	Join(table schema.Tabler, on ...field.Expr) IProductStorageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IProductStorageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IProductStorageDo
	Group(cols ...field.Expr) IProductStorageDo
	Having(conds ...gen.Condition) IProductStorageDo
	Limit(limit int) IProductStorageDo
	Offset(offset int) IProductStorageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IProductStorageDo
	Unscoped() IProductStorageDo
	Create(values ...*po.ProductStorage) error
	CreateInBatches(values []*po.ProductStorage, batchSize int) error
	Save(values ...*po.ProductStorage) error
	First() (*po.ProductStorage, error)
	Take() (*po.ProductStorage, error)
	Last() (*po.ProductStorage, error)
	Find() ([]*po.ProductStorage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.ProductStorage, err error)
	FindInBatches(result *[]*po.ProductStorage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.ProductStorage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IProductStorageDo
	Assign(attrs ...field.AssignExpr) IProductStorageDo
	Joins(fields ...field.RelationField) IProductStorageDo
	Preload(fields ...field.RelationField) IProductStorageDo
	FirstOrInit() (*po.ProductStorage, error)
	FirstOrCreate() (*po.ProductStorage, error)
	FindByPage(offset int, limit int) (result []*po.ProductStorage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IProductStorageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.ProductStorage, err error)
	DazzyQueryByAny(field string, value string) (result []*po.ProductStorage, err error)
	QueryByVenueId(venueId string) (result []*po.ProductStorage, err error)
}

// DELETE FROM @@table WHERE id = @id
func (p productStorageDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM product_storage WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (p productStorageDo) QueryOneByID(id string) (result *po.ProductStorage, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM product_storage WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (p productStorageDo) DazzyQueryByAny(field string, value string) (result []*po.ProductStorage, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM product_storage WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (p productStorageDo) QueryByVenueId(venueId string) (result []*po.ProductStorage, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM product_storage WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p productStorageDo) Debug() IProductStorageDo {
	return p.withDO(p.DO.Debug())
}

func (p productStorageDo) WithContext(ctx context.Context) IProductStorageDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productStorageDo) ReadDB() IProductStorageDo {
	return p.Clauses(dbresolver.Read)
}

func (p productStorageDo) WriteDB() IProductStorageDo {
	return p.Clauses(dbresolver.Write)
}

func (p productStorageDo) Session(config *gorm.Session) IProductStorageDo {
	return p.withDO(p.DO.Session(config))
}

func (p productStorageDo) Clauses(conds ...clause.Expression) IProductStorageDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productStorageDo) Returning(value interface{}, columns ...string) IProductStorageDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productStorageDo) Not(conds ...gen.Condition) IProductStorageDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productStorageDo) Or(conds ...gen.Condition) IProductStorageDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productStorageDo) Select(conds ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productStorageDo) Where(conds ...gen.Condition) IProductStorageDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productStorageDo) Order(conds ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productStorageDo) Distinct(cols ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productStorageDo) Omit(cols ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productStorageDo) Join(table schema.Tabler, on ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productStorageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productStorageDo) RightJoin(table schema.Tabler, on ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productStorageDo) Group(cols ...field.Expr) IProductStorageDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productStorageDo) Having(conds ...gen.Condition) IProductStorageDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productStorageDo) Limit(limit int) IProductStorageDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productStorageDo) Offset(offset int) IProductStorageDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productStorageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IProductStorageDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productStorageDo) Unscoped() IProductStorageDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productStorageDo) Create(values ...*po.ProductStorage) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productStorageDo) CreateInBatches(values []*po.ProductStorage, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productStorageDo) Save(values ...*po.ProductStorage) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productStorageDo) First() (*po.ProductStorage, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorage), nil
	}
}

func (p productStorageDo) Take() (*po.ProductStorage, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorage), nil
	}
}

func (p productStorageDo) Last() (*po.ProductStorage, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorage), nil
	}
}

func (p productStorageDo) Find() ([]*po.ProductStorage, error) {
	result, err := p.DO.Find()
	return result.([]*po.ProductStorage), err
}

func (p productStorageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.ProductStorage, err error) {
	buf := make([]*po.ProductStorage, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productStorageDo) FindInBatches(result *[]*po.ProductStorage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productStorageDo) Attrs(attrs ...field.AssignExpr) IProductStorageDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productStorageDo) Assign(attrs ...field.AssignExpr) IProductStorageDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productStorageDo) Joins(fields ...field.RelationField) IProductStorageDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productStorageDo) Preload(fields ...field.RelationField) IProductStorageDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productStorageDo) FirstOrInit() (*po.ProductStorage, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorage), nil
	}
}

func (p productStorageDo) FirstOrCreate() (*po.ProductStorage, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorage), nil
	}
}

func (p productStorageDo) FindByPage(offset int, limit int) (result []*po.ProductStorage, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productStorageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productStorageDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productStorageDo) Delete(models ...*po.ProductStorage) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productStorageDo) withDO(do gen.Dao) *productStorageDo {
	p.DO = *do.(*gen.DO)
	return p
}
