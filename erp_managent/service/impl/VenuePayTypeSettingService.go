package impl

import (
	"encoding/json"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type VenuePayTypeSettingService struct {
}

func (service *VenuePayTypeSettingService) CreateVenuePayTypeSetting(logCtx *gin.Context, venuePayTypeSetting *po.VenuePayTypeSetting) error {
	return Save(venuePayTypeSetting)
}

func (service *VenuePayTypeSettingService) CreateVenuePayTypeSettingWithTx(logCtx *gin.Context, venuePayTypeSetting *po.VenuePayTypeSetting, tx *gorm.DB) error {
	return SaveWithTx(venuePayTypeSetting, tx)
}

func (service *VenuePayTypeSettingService) UpdateVenuePayTypeSetting(logCtx *gin.Context, venuePayTypeSetting *po.VenuePayTypeSetting) error {
	return Update(venuePayTypeSetting)
}

func (service *VenuePayTypeSettingService) UpdateVenuePayTypeSettingPartial(logCtx *gin.Context, venuePayTypeSetting *po.VenuePayTypeSetting) error {
	return UpdateNotNull(venuePayTypeSetting)
}

func (service *VenuePayTypeSettingService) UpdateVenuePayTypeSettingPartialWithTx(logCtx *gin.Context, venuePayTypeSetting *po.VenuePayTypeSetting, tx *gorm.DB) error {
	return UpdateNotNullWithTx(venuePayTypeSetting, tx)
}

func (service *VenuePayTypeSettingService) DeleteVenuePayTypeSetting(logCtx *gin.Context, id string) error {
	return Delete(po.VenuePayTypeSetting{Id: &id})
}

func (service *VenuePayTypeSettingService) FindVenuePayTypeSettingById(logCtx *gin.Context, id string) (venuePayTypeSetting *po.VenuePayTypeSetting, err error) {
	venuePayTypeSetting = &po.VenuePayTypeSetting{}
	err = model.DBMaster.Self.Where("id=?", id).First(venuePayTypeSetting).Error
	return
}

func (service *VenuePayTypeSettingService) FindAllVenuePayTypeSetting(logCtx *gin.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (list *[]po.VenuePayTypeSetting, err error) {
	db := model.DBSlave.Self.Model(&po.VenuePayTypeSetting{})

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.VenueIds != nil && len(*reqDto.VenueIds) > 0 {
		db = db.Where("venue_id IN ?", *reqDto.VenueIds)
	}
	if reqDto.TypeInfo != nil && *reqDto.TypeInfo != "" {
		db = db.Where("type_info LIKE ?", "%"+*reqDto.TypeInfo+"%")
	}
	if reqDto.Remark != nil && *reqDto.Remark != "" {
		db = db.Where("remark LIKE ?", "%"+*reqDto.Remark+"%")
	}

	db = db.Order("ctime desc")
	list = &[]po.VenuePayTypeSetting{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *VenuePayTypeSettingService) FindAllVenuePayTypeSettingWithPagination(logCtx *gin.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (list *[]po.VenuePayTypeSetting, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.VenuePayTypeSetting{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		*reqDto.PageNum = 1
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		*reqDto.PageSize = 10
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.TypeInfo != nil && *reqDto.TypeInfo != "" {
		db = db.Where("type_info LIKE ?", "%"+*reqDto.TypeInfo+"%")
	}
	if reqDto.Remark != nil && *reqDto.Remark != "" {
		db = db.Where("remark LIKE ?", "%"+*reqDto.Remark+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.VenuePayTypeSetting{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

// extend

func (service *VenuePayTypeSettingService) FindLastPayTypeSetting(logCtx *gin.Context, venueId string) (*po.VenuePayTypeSetting, error) {
	list, err := service.FindAllVenuePayTypeSetting(logCtx, &req.QueryVenuePayTypeSettingReqDto{
		VenueId: &venueId,
	})
	if err != nil {
		return nil, err
	}
	if len(*list) == 0 {
		// 默认支付类型
		typeInfo, err := json.Marshal(_const.PAY_TYPE_ALL)
		if err != nil {
			return nil, err
		}
		typeInfoStr := string(typeInfo)
		*list = append(*list, po.VenuePayTypeSetting{
			VenueId:  &venueId,
			TypeInfo: &typeInfoStr,
		})
	}
	return &(*list)[0], nil
}

func (service *VenuePayTypeSettingService) FindLastPayTypeSettingByIds(logCtx *gin.Context, venueIds []string) (*po.VenuePayTypeSetting, error) {
	list, err := service.FindAllVenuePayTypeSetting(logCtx, &req.QueryVenuePayTypeSettingReqDto{
		VenueIds: &venueIds,
	})
	if err != nil {
		return nil, err
	}
	if len(*list) != len(venueIds) {
		dbIds := []string{}
		for _, venuePayTypeSetting := range *list {
			dbIds = append(dbIds, *venuePayTypeSetting.VenueId)
		}
		// 默认支付类型
		typeInfo, err := json.Marshal(_const.PAY_TYPE_ALL)
		if err != nil {
			return nil, err
		}
		typeInfoStr := string(typeInfo)

		notInIds := []string{}
		for _, venueId := range venueIds {
			if !util.InList(venueId, dbIds) {
				notInIds = append(notInIds, venueId)
			}
		}
		for _, venueId := range notInIds {
			*list = append(*list, po.VenuePayTypeSetting{
				VenueId:  &venueId,
				TypeInfo: &typeInfoStr,
			})
		}
	}
	return &(*list)[0], nil
}
