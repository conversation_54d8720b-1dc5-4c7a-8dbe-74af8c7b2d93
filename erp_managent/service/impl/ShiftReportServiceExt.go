package impl

import (
	"sort"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

func (service *ShiftReportService) BuildShiftReportDailyVO_V2(ctx *gin.Context, reqDto req.QueryShiftReportGetIncomeDailyReqDto, modelBaseShiftReportBO *vo.ModelBaseShiftReportBO) []vo.ShiftReportDaily {
	retVOs := []vo.ShiftReportDaily{}
	// {roomId: room}
	roomIdMap := make(map[string]po.Room)
	for _, room := range *modelBaseShiftReportBO.Rooms {
		roomIdMap[*room.Id] = room
	}
	startHour := modelBaseShiftReportBO.StartHour
	startTimeFinal := modelBaseShiftReportBO.StartTimeFinal

	// 计算归属日期 billDate
	service.V3_CalculateBillDate(modelBaseShiftReportBO)

	// 按billDate分组
	nowTime := modelBaseShiftReportBO.NowTime
	groupedVOs := service.V3_GroupShiftReportDailyVO(modelBaseShiftReportBO)
	for dayStr, dailyData := range groupedVOs {
		// 1. 填充基础数据
		dailyData.BillDate = dayStr
		dailyData.VenueStartHour = *modelBaseShiftReportBO.Venue.StartHours
		dailyData.VenueEndHour = *modelBaseShiftReportBO.Venue.EndHours
		dailyData.VenueId = *reqDto.VenueId
		dailyData.EmployeeId = *reqDto.EmployeeId
		dailyData.HandTime = nowTime

		// 2. 填充营业概况 (使用分组后的PayBills)
		service.V3_FillBusinessOverview(dailyData.PayBillShiftReportVOs, &dailyData.BusinessOverview)

		// 3 处理 GiftOrderInfoVOs 员工赠送
		service.V3_FillEmployeeGift(dailyData.GiftOrderInfoVOs, &dailyData.BusinessOverview)

		// 4 填充营业概况的RoomFee=sum(sessions.RoomFee)、ProductFee=sum(sessions.SupermarketFee)、Unpaid=sum(sessions.UnpaidAmount) (使用分组后的Sessions)
		service.V3_FillBusinessOverviewSub2(dailyData.PayBillShiftReportVOs, &dailyData.BusinessOverview, dailyData.CurrentDayOrderVOs)

		// 5. 填充支付方式数据 (使用分组后的PayBills和传入的PayRecords)
		service.V3_FillPayTypeData(dailyData.PayBillShiftReportVOs, &dailyData.PayTypeData, &dailyData.BusinessOverview, modelBaseShiftReportBO)

		// 6. 填充会员卡消费数据 (使用分组后的MemberCardConsumes)
		service.V3_FillMemberCardConsumeData(dailyData.PayMemberCardConsumes, &dailyData.MemberCardPayData)

		// 7. 填充会员卡充值数据 (使用分组后的MemberCardOperationsRecharge)
		service.V3_FillMemberCardOperationData(dailyData.RechargeMemberCardOperations, &dailyData.MemberCardRechargeData)

		// 9. 处理 PayBillVOs
		newPayBillVOs := []vo.PayBillVO{}
		for _, payBillShiftReportVO := range dailyData.PayBillShiftReportVOs { // 使用分组后的PayBills
			billVO := payBillShiftReportVO.PayBillVO
			val, exists := roomIdMap[billVO.RoomId]
			if exists {
				billVO.RoomName = *val.Name
			}
			newPayBillVOs = append(newPayBillVOs, billVO)
		}
		dailyData.PayBillVOs = newPayBillVOs

		// 10. 计算营业数据
		service.V3_FillBusinessData(dailyData, &dailyData.BusinessData)

		// 11. 调整日期时间为实际营业时间 (设置StartTime, EndTime)
		service.adjustDateTimeNew(dailyData, startHour, startTimeFinal, nowTime)

		retVOs = append(retVOs, *dailyData)
	}
	// currentDayOrders := modelBaseShiftReportBO.CurrentDayOrders
	// sessionIdToSessionMap := modelBaseShiftReportBO.SessionIdToSessionMap
	// service.V3_FillBusinessDataCurrentDay(ctx, &reqDto, currentDayOrders, &retVOs, sessionIdToSessionMap, startHour, nowTime)
	// for idx, retVO := range retVOs {
	// 	retVO.BusinessData.BillCount = len(retVO.PayBillVOs)
	// 	retVOs[idx] = retVO
	// }

	// 使用sort包进行排序
	sort.Slice(retVOs, func(i, j int) bool {
		// 按照BillDate正序排列
		return retVOs[i].BillDate < retVOs[j].BillDate
	})

	return retVOs
}

// 计算账单日期
func (service *ShiftReportService) V3_CalculateBillDate(modelBaseShiftReportBO *vo.ModelBaseShiftReportBO) {
	startHour := modelBaseShiftReportBO.StartHour

	// PayBillShiftReportVOs 中的 payBillVO 的 BillDateStr 需要计算
	newPayBillShiftReportVOs := []vo.PayBillShiftReportVO{}
	for _, payBillShiftReportVO := range modelBaseShiftReportBO.PayBillShiftReportVOs {
		payBillVO := payBillShiftReportVO.PayBillVO
		payBillVO.BillDateStr = util.CalculateBillDate(&payBillVO.FinishTime, startHour)
		payBillShiftReportVO.PayBillVO = payBillVO
		newPayBillShiftReportVOs = append(newPayBillShiftReportVOs, payBillShiftReportVO)
	}
	modelBaseShiftReportBO.PayBillShiftReportVOs = newPayBillShiftReportVOs

	// MemberCardOperationsRechargeVOs 中的 memberCardOperationVO 的 BillDateStr 需要计算
	newMemberCardOperationsRechargeVOs := []vo.MemberCardOperationVO{}
	for _, memberCardOperationVO := range modelBaseShiftReportBO.MemberCardOperationsRecharges {
		payTime := memberCardOperationVO.MemberRechargeBillVO.FinishTime
		memberCardOperationVO.BillDateStr = util.CalculateBillDate(&payTime, startHour)
		newMemberCardOperationsRechargeVOs = append(newMemberCardOperationsRechargeVOs, memberCardOperationVO)
	}
	modelBaseShiftReportBO.MemberCardOperationsRecharges = newMemberCardOperationsRechargeVOs

	// 处理 MemberCardConsumes 的 Ctime
	newMemberCardConsumes := []vo.MemberCardConsumeVO{}
	for _, memberCardConsume := range modelBaseShiftReportBO.MemberCardConsumes {
		payTime := memberCardConsume.PayBillVO.FinishTime
		memberCardConsume.BillDateStr = util.CalculateBillDate(&payTime, startHour)
		newMemberCardConsumes = append(newMemberCardConsumes, memberCardConsume)
	}
	modelBaseShiftReportBO.MemberCardConsumes = newMemberCardConsumes

	// GiftOrderVOs 中的 orderVO 的 BillDateStr 需要计算
	newGiftOrderVOs := []vo.GiftOrderInfoVO{}
	for _, giftOrderInfoVO := range modelBaseShiftReportBO.GiftOrderVOs {
		orderVO := giftOrderInfoVO.OrderVO
		orderVO.BillDateStr = util.CalculateBillDate(&orderVO.Ctime, startHour)
		giftOrderInfoVO.OrderVO = orderVO
		newGiftOrderVOs = append(newGiftOrderVOs, giftOrderInfoVO)
	}
	modelBaseShiftReportBO.GiftOrderVOs = newGiftOrderVOs

	// 处理 CurrentDayOrderVOs 的 Ctime
	newCurrentDayOrderVOs := []vo.OrderVO{}
	for _, currentDayOrderVO := range modelBaseShiftReportBO.CurrentDayOrderVOs {
		currentDayOrderVO.BillDateStr = util.CalculateBillDate(&currentDayOrderVO.Ctime, startHour)
		newCurrentDayOrderVOs = append(newCurrentDayOrderVOs, currentDayOrderVO)
	}
	modelBaseShiftReportBO.CurrentDayOrderVOs = newCurrentDayOrderVOs

	// 处理 CurrentDaySessionVOs 的 Ctime
	newCurrentDaySessionVOs := []vo.SessionVO{}
	for _, currentDaySessionVO := range modelBaseShiftReportBO.CurrentDaySessionVOs {
		currentDaySessionVO.BillDateStr = util.CalculateBillDate(&currentDaySessionVO.Ctime, startHour)
		newCurrentDaySessionVOs = append(newCurrentDaySessionVOs, currentDaySessionVO)
	}
	modelBaseShiftReportBO.CurrentDaySessionVOs = newCurrentDaySessionVOs
}

func (service *ShiftReportService) V3_GroupShiftReportDailyVO(modelBaseShiftReportBO *vo.ModelBaseShiftReportBO) map[string]*vo.ShiftReportDaily {
	dayGroups := make(map[string]*vo.ShiftReportDaily)

	// 1. PayBill
	for _, payBillShiftReportVO := range modelBaseShiftReportBO.PayBillShiftReportVOs {
		payBillVO := payBillShiftReportVO.PayBillVO
		// 如果账单日期为空，则跳过
		billDateStr := payBillVO.BillDateStr
		if billDateStr == "" {
			continue
		}
		// 如果该日期不存在，创建新的ShiftReportDaily
		if _, exists := dayGroups[billDateStr]; !exists {
			dayGroups[billDateStr] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
				CurrentDayOrderVOs:           make([]vo.OrderVO, 0),
				CurrentDaySessionVOs:         make([]vo.SessionVO, 0),
			}
		}
		dayGroups[billDateStr].PayBillShiftReportVOs = append(dayGroups[billDateStr].PayBillShiftReportVOs, payBillShiftReportVO)
	}
	// 2.GiftOrder
	for _, giftOrderInfoVO := range modelBaseShiftReportBO.GiftOrderVOs {
		billDateStr := giftOrderInfoVO.OrderVO.BillDateStr
		if billDateStr == "" {
			continue
		}
		// 如果该日期不存在，创建新的ShiftReportDaily
		if _, exists := dayGroups[billDateStr]; !exists {
			dayGroups[billDateStr] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
				CurrentDayOrderVOs:           make([]vo.OrderVO, 0),
				CurrentDaySessionVOs:         make([]vo.SessionVO, 0),
			}
		}
		dayGroups[billDateStr].GiftOrderInfoVOs = append(dayGroups[billDateStr].GiftOrderInfoVOs, giftOrderInfoVO)
	}

	// 3.MemberCardOperationsRecharges
	for _, memberCardOperationVO := range modelBaseShiftReportBO.MemberCardOperationsRecharges {
		billDateStr := memberCardOperationVO.BillDateStr
		if billDateStr == "" {
			continue
		}
		if _, exists := dayGroups[billDateStr]; !exists {
			dayGroups[billDateStr] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
				CurrentDayOrderVOs:           make([]vo.OrderVO, 0),
				CurrentDaySessionVOs:         make([]vo.SessionVO, 0),
			}
		}
		poTmp := memberCardOperationTransfer.VoToPo(memberCardOperationVO)
		dayGroups[billDateStr].RechargeMemberCardOperations = append(dayGroups[billDateStr].RechargeMemberCardOperations, poTmp)
	}

	// 4.MemberCardConsumes
	for _, memberCardConsumeVO := range modelBaseShiftReportBO.MemberCardConsumes {
		billDateStr := memberCardConsumeVO.BillDateStr
		if billDateStr == "" {
			continue
		}
		if _, exists := dayGroups[billDateStr]; !exists {
			dayGroups[billDateStr] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
				CurrentDayOrderVOs:           make([]vo.OrderVO, 0),
				CurrentDaySessionVOs:         make([]vo.SessionVO, 0),
			}
		}
		poTmp := memberCardConsumeTransfer.VoToPo(memberCardConsumeVO)
		dayGroups[billDateStr].PayMemberCardConsumes = append(dayGroups[billDateStr].PayMemberCardConsumes, poTmp)
	}

	// 5.CurrentDayOrderVOs
	for _, currentDayOrderVO := range modelBaseShiftReportBO.CurrentDayOrderVOs {
		billDateStr := currentDayOrderVO.BillDateStr
		if billDateStr == "" {
			continue
		}
		if _, exists := dayGroups[billDateStr]; !exists {
			dayGroups[billDateStr] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
				CurrentDayOrderVOs:           make([]vo.OrderVO, 0),
				CurrentDaySessionVOs:         make([]vo.SessionVO, 0),
			}
		}
		dayGroups[billDateStr].CurrentDayOrderVOs = append(dayGroups[billDateStr].CurrentDayOrderVOs, currentDayOrderVO)
	}

	// 6.CurrentDaySessionVOs
	for _, currentDaySessionVO := range modelBaseShiftReportBO.CurrentDaySessionVOs {
		billDateStr := currentDaySessionVO.BillDateStr
		if billDateStr == "" {
			continue
		}
		if _, exists := dayGroups[billDateStr]; !exists {
			dayGroups[billDateStr] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
				CurrentDayOrderVOs:           make([]vo.OrderVO, 0),
				CurrentDaySessionVOs:         make([]vo.SessionVO, 0),
			}
		}
		dayGroups[billDateStr].CurrentDaySessionVOs = append(dayGroups[billDateStr].CurrentDaySessionVOs, currentDaySessionVO)
	}

	return dayGroups
}

func (service *ShiftReportService) V3_FillEmployeeGift(giftOrderInfoVOs []vo.GiftOrderInfoVO, businessOverview *vo.BusinessOverview) {
	totalGiftFee := int64(0)
	for _, giftOrderInfoVO := range giftOrderInfoVOs {
		orderVO := giftOrderInfoVO.OrderVO
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			totalGiftFee -= giftOrderInfoVO.TotalFee
		} else {
			totalGiftFee += giftOrderInfoVO.TotalFee
		}
	}
	businessOverview.EmployeeGift = totalGiftFee
}

func (service *ShiftReportService) V3_FillPayTypeData(payBillShiftReportVOs []vo.PayBillShiftReportVO, payTypeData *map[string]int64, businessOverview *vo.BusinessOverview, modelBaseShiftReportBO *vo.ModelBaseShiftReportBO) {
	if len(payBillShiftReportVOs) == 0 {
		return
	}

	netPayTypeList := []string{}
	// todo 需要从配置中获取
	// for _, payTypeConfig := range modelBaseShiftReportBO.PayTypeConfigs {
	for _, payTypeConfig := range _const.NET_DEFAULT_PAY_TYPE_LIST {
		if payTypeConfig.IsNetProfit {
			netPayTypeList = append(netPayTypeList, payTypeConfig.PayType)
		}
	}

	// 遍历支付记录
	payTypeDataV2Tmp := make(map[string]int64)
	for _, record := range payBillShiftReportVOs {
		bill := record.PayBillVO
		for _, payRecordVO := range record.PayRecordVOs {

			// 判断支付方向，退款需要减去金额
			multiplier := int64(1)
			if bill.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
				multiplier = -1
			}

			payType := payRecordVO.PayType
			if _, ok := payTypeDataV2Tmp[payType]; !ok {
				payTypeDataV2Tmp[payType] = int64(0)
			}
			amount := payRecordVO.TotalFee * multiplier
			payTypeDataV2Tmp[payType] += amount

			if util.InList(payType, netPayTypeList) {
				businessOverview.NetFee += amount
			}

			// 根据支付类型累加金额
			// amount := payRecordVO.TotalFee * multiplier
			// switch payRecordVO.PayType {
			// case _const.PAY_TYPE_RECORD_CASH: // 现金
			// 	payTypeData.Cash += amount
			// case _const.PAY_TYPE_RECORD_BANK: // 银行卡
			// 	payTypeData.Bank += amount
			// case _const.PAY_TYPE_RECORD_WECHAT: // 微信
			// 	payTypeData.Wechat += amount
			// case _const.PAY_TYPE_RECORD_ALIPAY: // 支付宝
			// 	payTypeData.Alipay += amount
			// case _const.PAY_TYPE_LESHUA_BSHOWQR: // 乐刷付款码
			// 	payTypeData.Leshua += amount
			// }
		}
	}
	*payTypeData = payTypeDataV2Tmp
}

func (service *ShiftReportService) V3_FillMemberCardConsumeData(memberCardConsumes []po.MemberCardConsume, memberCardData *vo.MemberCardPayData) {
	for _, memberCardConsume := range memberCardConsumes {
		// 判断支付方向，退款需要减去金额
		multiplier := int64(1)
		if *memberCardConsume.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			multiplier = -1
		}

		// 根据支付类型累加金额
		// 本金
		memberCardData.PrincipalAmount += *memberCardConsume.PayRecordPrincipalAmount * multiplier
		// 房费赠金
		memberCardData.RoomBonusAmount += *memberCardConsume.PayRecordRoomBonusAmount * multiplier
		// 商品赠金
		memberCardData.GoodsBonusAmount += *memberCardConsume.PayRecordGoodsBonusAmount * multiplier
		// 通用赠金
		memberCardData.CommonBonusAmount += *memberCardConsume.PayRecordCommonBonusAmount * multiplier
	}
}

func (service *ShiftReportService) V3_FillMemberCardOperationData(memberCardOperationsRecharge []po.MemberCardOperation, memberCardRechargeData *vo.MemberCardRechargeData) {
	for _, memberCardOperation := range memberCardOperationsRecharge {
		// 只处理充值类型的支付账单
		if !util.InList(*memberCardOperation.OperationType, []string{_const.V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE, _const.V2_MEMBER_CARD_OPERATION_TYPE_REFUND}) {
			continue
		}

		// 判断支付方向，退款需要减去金额
		multiplier := int64(1)
		if *memberCardOperation.OperationType == _const.V2_MEMBER_CARD_OPERATION_TYPE_REFUND {
			multiplier = -1
		}

		// 累加充值金额
		memberCardRechargeData.RechargeAmount += *memberCardOperation.PrincipalAmount * multiplier
		memberCardRechargeData.RechargePrincipalAmount += *memberCardOperation.PrincipalAmount * multiplier
		memberCardRechargeData.RechargeRoomBonusAmount += *memberCardOperation.MemberRoomBonusAmount * multiplier
		memberCardRechargeData.RechargeGoodsBonusAmount += *memberCardOperation.MemberGoodsBonusAmount * multiplier
		memberCardRechargeData.RechargeCommonBonusAmount += *memberCardOperation.MemberCommonBonusAmount * multiplier
	}
}

func (service *ShiftReportService) V3_FillBusinessOverviewSub2(payBillShiftReportVOs []vo.PayBillShiftReportVO, businessOverview *vo.BusinessOverview, currentDayOrderVOs []vo.OrderVO) {
	totalRoomFee := int64(0)
	totalProductFee := int64(0)
	totalUnpaid := int64(0)
	orderShiftReportVOs := service.getOrderShiftReportVOs(payBillShiftReportVOs)

	for _, orderShiftReportVO := range orderShiftReportVOs {
		orderVO := orderShiftReportVO.OrderVO
		orderType := orderVO.Type
		multiplier := int64(1)
		// 账单还原的可能还会引用到未付的订单，所以这里过滤掉未付的订单
		if orderVO.Status == _const.V2_ORDER_STATUS_UNPAID {
			continue
		}
		if orderShiftReportVO.BillIsBack {
			continue
		}
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			multiplier = -1
		}
		// 正常订单
		if orderType == _const.V2_ORDER_TYPE_ROOMPLAN {
			// 房间订单
			totalRoomFee += orderShiftReportVO.TotalFee * multiplier
		} else if orderType == _const.V2_ORDER_TYPE_PRODUCT {
			// 商品订单
			totalProductFee += orderShiftReportVO.TotalFee * multiplier
		}
	}
	// 计算未付订单金额
	for _, orderVO := range currentDayOrderVOs {
		multiplier := int64(1)
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			multiplier = -1
		}
		if orderVO.Status == _const.V2_ORDER_STATUS_PAID {
			continue
		}
		orderType := orderVO.Type
		if orderType == _const.V2_ORDER_TYPE_ROOMPLAN {
			for _, orderRoomPlanVO := range orderVO.OrderRoomPlanVOs {
				totalUnpaid += orderRoomPlanVO.PayAmount * multiplier
			}
		} else if orderType == _const.V2_ORDER_TYPE_PRODUCT {
			for _, orderProductVO := range orderVO.OrderProductVOs {
				totalUnpaid += orderProductVO.PayAmount * multiplier
			}
		}
	}

	businessOverview.RoomFee = totalRoomFee
	businessOverview.ProductFee = totalProductFee
	businessOverview.UnpaidFee = totalUnpaid
}

func (service *ShiftReportService) V3_FillBusinessOverview(payBillShiftReportVOs []vo.PayBillShiftReportVO, businessOverview *vo.BusinessOverview) {
	// 填充营业概况
	for _, payBillShiftReportVO := range payBillShiftReportVOs {
		billVO := payBillShiftReportVO.PayBillVO
		if billVO.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			// 退款单处理
			if billVO.TotalFee != 0 {
				businessOverview.TotalFee -= billVO.TotalFee // 实收减去退款
			}
			if billVO.ShouldFee != 0 {
				businessOverview.ShouldFee -= billVO.ShouldFee // 应收减去退款应收
			}
			continue
		}

		// 正常收款单处理
		if billVO.ShouldFee != 0 {
			businessOverview.ShouldFee += billVO.ShouldFee // 应收
		}
		if billVO.TotalFee != 0 {
			businessOverview.TotalFee += billVO.TotalFee // 实收
		}
		if billVO.ZeroFee != 0 {
			businessOverview.ZeroFee += billVO.ZeroFee // 抹零金额
		}

		// 商家优惠 = 原始金额 - 应付金额
		if billVO.ShouldFee != 0 && billVO.ShouldFee != billVO.TotalFee {
			businessOverview.MerchantDiscount += billVO.ShouldFee - billVO.TotalFee + billVO.ZeroFee
		}

		// 会员优惠 = 原始金额 - 应付金额
		// 低消差额
		// 员工赠送
	}
}

func (service *ShiftReportService) V3_FillBusinessDataCurrentDay(ctx *gin.Context, reqDto *req.QueryShiftReportGetIncomeDailyReqDto, currentDayOrders []po.Order, retVOs *[]vo.ShiftReportDaily, sessionIdToSessionMap map[string]po.Session, startHour string, nowTime int64) {
	// 判断retVOs是否有当天数据，填充到当天营业数据，没有构造一条今天的数据
	isMatch := false
	today := util.GetDateTimeStr()[:10]
	for idx, retVO := range *retVOs {
		if retVO.BillDate == today {
			service.fillBusinessOverviewCurrentDay(ctx, reqDto, currentDayOrders, &retVO.BusinessOverview)
			service.fillBusinessData(currentDayOrders, &retVO.BusinessData, sessionIdToSessionMap)
			retVO.BusinessData.BillCount += len(retVO.PayBillVOs)
			(*retVOs)[idx] = retVO
			isMatch = true
			break
		}
	}
	if !isMatch {

		// 如果不是当前营业日，则使用标准时间计算
		startTime := service.parseTimeWithLocation(today, startHour, 0)

		// 构造一条今天的数据
		newRetVO := vo.ShiftReportDaily{
			BillDate:         today,
			BusinessOverview: vo.BusinessOverview{},
			BusinessData:     vo.BusinessData{},
			StartTime:        startTime,
			EndTime:          nowTime,
			HandTime:         nowTime,
			EmployeeId:       *reqDto.EmployeeId,
			VenueId:          *reqDto.VenueId,
			VenueEndHour:     startHour,
		}
		service.fillBusinessOverviewCurrentDay(ctx, reqDto, currentDayOrders, &newRetVO.BusinessOverview)
		service.fillBusinessData(currentDayOrders, &newRetVO.BusinessData, sessionIdToSessionMap)
		*retVOs = append(*retVOs, newRetVO)
	}
}

func (service *ShiftReportService) V3_FillBusinessData(dailyData *vo.ShiftReportDaily, businessData *vo.BusinessData) {
	orderVOs := dailyData.CurrentDayOrderVOs
	for _, orderVO := range orderVOs {
		if orderVO.Status == _const.V2_ORDER_STATUS_UNPAID {
			businessData.OrderUnpaidCount++
		} else {
			businessData.OrderPaidCount++
		}
	}
	for _, sessionVO := range dailyData.CurrentDaySessionVOs {
		if sessionVO.PayStatus == _const.V2_SESSION_PAY_STATUS_UNPAID {
			businessData.OpenCountUnpaid++
		} else {
			businessData.OpenCountPaid++
		}
	}
	sessionVOs := dailyData.CurrentDaySessionVOs
	businessData.OpenCount = len(sessionVOs)
	businessData.BillCount = len(dailyData.PayBillVOs)
}
