package impl

import (
	"fmt"
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

type MemberCardOperationService struct {
}

var memberCardOperationService *MemberCardOperationService = &MemberCardOperationService{}

var memberCardOperationTransfer = transfer.MemberCardOperationTransfer{}

func (service *MemberCardOperationService) CreateMemberCardOperation(logCtx *gin.Context, memberCardOperation *po.MemberCardOperation) error {
	return Save(memberCardOperation)
}

func (service *MemberCardOperationService) CreateMemberCardOperationWithTx(logCtx *gin.Context, memberCardOperation *po.MemberCardOperation, tx *gorm.DB) error {
	return SaveWithTx(memberCardOperation, tx)
}

func (service *MemberCardOperationService) UpdateMemberCardOperation(logCtx *gin.Context, memberCardOperation *po.MemberCardOperation) error {
	return Update(memberCardOperation)
}

func (service *MemberCardOperationService) UpdateMemberCardOperationPartial(logCtx *gin.Context, memberCardOperation *po.MemberCardOperation) error {
	return UpdateNotNull(memberCardOperation)
}

func (service *MemberCardOperationService) UpdateMemberCardOperationPartialWithTx(logCtx *gin.Context, memberCardOperation *po.MemberCardOperation, tx *gorm.DB) error {
	return UpdateNotNullWithTx(memberCardOperation, tx)
}

func (service *MemberCardOperationService) DeleteMemberCardOperation(logCtx *gin.Context, id string) error {
	return Delete(po.MemberCardOperation{Id: &id})
}

func (service *MemberCardOperationService) FindMemberCardOperationById(logCtx *gin.Context, id string) (memberCardOperation *po.MemberCardOperation, err error) {
	memberCardOperation = &po.MemberCardOperation{}
	err = model.DBMaster.Self.Where("id=?", id).First(memberCardOperation).Error
	return
}

func (service *MemberCardOperationService) FindAllMemberCardOperation(logCtx *gin.Context, reqDto *req.QueryMemberCardOperationReqDto) (list *[]po.MemberCardOperation, err error) {
	db := model.DBSlave.Self.Model(&po.MemberCardOperation{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.Ids != nil && len(*reqDto.Ids) > 0 {
		db = db.Where("id IN (?)", *reqDto.Ids)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.Name != nil && *reqDto.Name != "" {
		db = db.Where("name=?", *reqDto.Name)
	}
	if reqDto.Phone != nil && *reqDto.Phone != "" {
		db = db.Where("phone=?", *reqDto.Phone)
	}
	if reqDto.PhoneLike != nil && *reqDto.PhoneLike != "" {
		db = db.Where("(phone LIKE ? or card_number LIKE ?)", "%"+*reqDto.PhoneLike+"%", "%"+*reqDto.PhoneLike+"%")
	}
	if reqDto.CardNumber != nil && *reqDto.CardNumber != "" {
		db = db.Where("card_number=?", *reqDto.CardNumber)
	}
	if reqDto.CardName != nil && *reqDto.CardName != "" {
		db = db.Where("card_name=?", *reqDto.CardName)
	}
	if reqDto.Gender != nil && *reqDto.Gender != "" {
		db = db.Where("gender=?", *reqDto.Gender)
	}
	if reqDto.OperationType != nil && *reqDto.OperationType != "" {
		db = db.Where("operation_type=?", *reqDto.OperationType)
	}
	if reqDto.MemberId != nil && *reqDto.MemberId != "" {
		db = db.Where("member_id=?", *reqDto.MemberId)
	}
	if reqDto.MemberCardId != nil && *reqDto.MemberCardId != "" {
		db = db.Where("member_card_id=?", *reqDto.MemberCardId)
	}
	if reqDto.BillId != nil && *reqDto.BillId != "" {
		db = db.Where("bill_id=?", *reqDto.BillId)
	}

	db = db.Order("ctime desc")
	list = &[]po.MemberCardOperation{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *MemberCardOperationService) FindAllMemberCardOperationWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberCardOperationReqDto) (list *[]po.MemberCardOperation, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.MemberCardOperation{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.Ids != nil && len(*reqDto.Ids) > 0 {
		db = db.Where("id IN (?)", *reqDto.Ids)
	}
	if reqDto.Name != nil && *reqDto.Name != "" {
		db = db.Where("name=?", *reqDto.Name)
	}
	if reqDto.Phone != nil && *reqDto.Phone != "" {
		db = db.Where("phone=?", *reqDto.Phone)
	}
	if reqDto.CardNumber != nil && *reqDto.CardNumber != "" {
		db = db.Where("card_number=?", *reqDto.CardNumber)
	}
	if reqDto.CardName != nil && *reqDto.CardName != "" {
		db = db.Where("card_name=?", *reqDto.CardName)
	}
	if reqDto.Gender != nil && *reqDto.Gender != "" {
		db = db.Where("gender=?", *reqDto.Gender)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.MemberCardOperation{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

// FindAllMemberCardRange 根据场馆ID查询会员列表
func (service *MemberCardOperationService) FindAllMemberCardOperationRange(ctx *gin.Context, memberIds []string, phoneLike string) ([]po.MemberCardOperation, error) {
	if len(memberIds) <= 0 {
		return nil, nil
	}
	list2 := util.SplitList(memberIds, 20)
	memberCardOperationList := make([]po.MemberCardOperation, 0)
	for _, listTmp := range list2 {
		listTmp2, err := service.FindAllMemberCardOperation(ctx, &req.QueryMemberCardOperationReqDto{
			Ids:       &listTmp,
			PhoneLike: &phoneLike,
		})
		if err != nil {
			return nil, err
		}
		memberCardOperationList = append(memberCardOperationList, *listTmp2...)
	}
	return memberCardOperationList, nil
}

// ConvertToMemberCardOperationVO 转换为会员卡操作记录VO
func (service *MemberCardOperationService) ConvertToMemberCardOperationVO(ctx *gin.Context, memberCardOperation po.MemberCardOperation) vo.MemberCardOperationVO {
	return memberCardOperationTransfer.PoToVo(memberCardOperation)
}

// ConvertToMemberCardOperation 转换为会员卡操作记录PO
func (service *MemberCardOperationService) ConvertToMemberCardOperation(ctx *gin.Context, memberCardOperationVO vo.MemberCardOperationVO) po.MemberCardOperation {
	return memberCardOperationTransfer.VoToPo(memberCardOperationVO)
}

func (service *MemberCardOperationService) RecordMemberCardOperation(ctx *gin.Context, memberCardOperation po.MemberCardOperation, memberCard po.MemberCard) error {
	employeeId := memberCardOperation.OperatorId
	if employeeId != nil && *employeeId != "" {
		employee, err := employeeService.FindEmployeeById(ctx, *employeeId)
		if err != nil {
			return err
		}
		memberCardOperation.OperatorName = employee.Name
	}
	venue := po.Venue{Name: util.Ptr("未知场馆")}
	if memberCardOperation.VenueId != nil && *memberCardOperation.VenueId != "" {
		venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
			Id: memberCardOperation.VenueId,
		})
		if err != nil {
			return err
		}
		venue = (*venues)[0]
	}
	balance := util.GetPtrSafeDefault(memberCard.PrincipalBalance, 0) + util.GetPtrSafeDefault(memberCard.RoomBonusBalance, 0) + util.GetPtrSafeDefault(memberCard.GoodsBonusBalance, 0) + util.GetPtrSafeDefault(memberCard.CommonBonusBalance, 0)
	principalBalance := util.GetPtrSafeDefault(memberCard.PrincipalBalance, 0)
	roomBonusBalance := util.GetPtrSafeDefault(memberCard.RoomBonusBalance, 0)
	goodsBonusBalance := util.GetPtrSafeDefault(memberCard.GoodsBonusBalance, 0)
	commonBonusBalance := util.GetPtrSafeDefault(memberCard.CommonBonusBalance, 0)
	memberCardOperation.Balance = &balance
	memberCardOperation.MemberCardBalancePrincipalAmount = &principalBalance
	memberCardOperation.MemberCardBalanceRoomBonusAmount = &roomBonusBalance
	memberCardOperation.MemberCardBalanceGoodsBonusAmount = &goodsBonusBalance
	memberCardOperation.MemberCardBalanceCommonBonusAmount = &commonBonusBalance
	// 设置会员卡操作记录的会员卡ID、会员卡名称、会员卡卡号、会员卡手机号、会员卡卡类型、会员卡卡等级、会员卡卡等级名称
	memberCardOperation.VenueName = venue.Name
	memberCardOperation.MemberCardId = memberCard.Id
	memberCardOperation.CardName = memberCard.Name
	memberCardOperation.CardNumber = memberCard.CardNumber
	memberCardOperation.CardPhone = memberCard.Phone
	memberCardOperation.CardType = memberCard.CardType
	memberCardOperation.CardLevel = memberCard.CardLevel
	memberCardOperation.CardLevelName = memberCard.CardLevelName
	return Save(&memberCardOperation)
}

// FindMemberCardOperationByVenueIdAndEmployeeId 根据场馆ID和员工ID查询会员卡操作记录
func (service *MemberCardOperationService) FindMemberCardOperationByVenueIdAndEmployeeId(ctx *gin.Context, venueId string, employeeId string, startTime int64) (list []po.MemberCardOperation, err error) {
	list = []po.MemberCardOperation{}
	db := model.DBSlave.Self.Model(&po.MemberCardOperation{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("operator_id=?", employeeId)
	db = db.Where("ctime>=?", startTime)
	db = db.Where("operation_type in ?", []string{_const.V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE, _const.V2_MEMBER_CARD_OPERATION_TYPE_REFUND})
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}

// FindMemberCardOperationsByTimeRange 根据时间范围查询会员卡操作记录
func (service *MemberCardOperationService) FindMemberCardOperationsByTimeRange(ctx *gin.Context, venueId string, timeStart int64, timeEnd int64, optionType string) (list []po.MemberCardOperation, err error) {
	list = []po.MemberCardOperation{}
	db := model.DBSlave.Self.Model(&po.MemberCardOperation{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("ctime>=?", timeStart)
	db = db.Where("ctime<=?", timeEnd)
	if optionType != "" {
		db = db.Where("operation_type=?", optionType)
	}
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}

// UpdateRechargeBanlance 更新充值余额
func (service *MemberCardOperationService) UpdateRechargeBanlance(ctx *gin.Context, memberCardId string, payId string) error {
	defer func() {
		if err := recover(); err != nil {
			logrus.Printf("UpdateRechargeBanlance panic: %v", err)
		}
	}()
	logHeader := fmt.Sprintf("乐刷支付回调: V3MemberPayCallback: +++ UpdateRechargeBanlance: 更新充值余额 %s %s", memberCardId, payId)
	util.Wlog(ctx).Infof("%s", logHeader)
	// 休眠900ms，防止记录写入延迟
	time.Sleep(900 * time.Millisecond)
	// 1. 查询充值记录
	memberRechargeRecords, err := memberRechargeRecordService.FindAllMemberRechargeRecord(ctx, &req.QueryMemberRechargeRecordReqDto{
		MemberCardId: &memberCardId,
		PayId:        &payId,
	})
	if err != nil {
		util.Wlog(ctx).Errorf("%s 1.1 %s", logHeader, err)
		return err
	}
	if len(*memberRechargeRecords) <= 0 {
		util.Wlog(ctx).Infof("%s 1.2 %s", logHeader, "没有找到充值记录")
		return nil
	}
	memberRechargeRecord := (*memberRechargeRecords)[0]
	util.Wlog(ctx).Infof("%s 1.3 %s", logHeader, "找到充值记录")
	billId := memberRechargeRecord.BillId
	if billId == nil || *billId == "" {
		util.Wlog(ctx).Infof("%s 1.4 %s", logHeader, "没有找到账单ID")
		return nil
	}
	util.Wlog(ctx).Infof("%s 1.5 %s", logHeader, "找到账单ID")

	operations, err := service.FindAllMemberCardOperation(ctx, &req.QueryMemberCardOperationReqDto{
		MemberCardId: &memberCardId,
		BillId:       billId,
	})
	if err != nil {
		util.Wlog(ctx).Errorf("%s 1.6 %s", logHeader, err)
		return err
	}
	if len(*operations) <= 0 {
		util.Wlog(ctx).Infof("%s 1.7 %s", logHeader, "没有找到操作记录")
		return nil
	}
	util.Wlog(ctx).Infof("%s 1.8 %s", logHeader, "找到操作记录")
	newBalance := int64(0)
	memberCard, err := memberCardService.FindMemberCardById(ctx, memberCardId)
	if err != nil {
		util.Wlog(ctx).Errorf("%s 1.9 %s", logHeader, err)
		return err
	}
	util.Wlog(ctx).Infof("%s 1.10 %s", logHeader, "找到会员卡")
	newBalance = *memberCard.PrincipalBalance + *memberCard.RoomBonusBalance + *memberCard.GoodsBonusBalance + *memberCard.CommonBonusBalance
	operation := (*operations)[0]
	toUpdateOperation := po.MemberCardOperation{
		Id:      operation.Id,
		Balance: &newBalance,
	}
	util.Wlog(ctx).Infof("%s 1.11 %s", logHeader, "更新操作记录")
	err = service.UpdateMemberCardOperation(ctx, &toUpdateOperation)
	if err != nil {
		util.Wlog(ctx).Errorf("%s 1.12 %s", logHeader, err)
		return err
	}
	util.Wlog(ctx).Infof("%s 1.13 %s", logHeader, "更新操作记录成功")
	return nil
}
