package impl

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductStockService struct {
}

var productStockService = &ProductStockService{}

func (service *ProductStockService) CreateProductStock(logCtx *gin.Context, stock *po.ProductStock) error {
	return Save(stock)
}

func (service *ProductStockService) CreateProductStockWithTx(logCtx *gin.Context, stock *po.ProductStock, tx *gorm.DB) error {
	return SaveWithTx(stock, tx)
}

func (service *ProductStockService) DeleteProductStock(logCtx *gin.Context, id string) error {
	return Delete(po.ProductStock{Id: &id})
}

func (service *ProductStockService) FindProductStockById(logCtx *gin.Context, id string) (stock *po.ProductStock, err error) {
	stock = &po.ProductStock{}
	err = model.DBMaster.Self.Where("id=?", id).First(stock).Error
	return
}

func (service *ProductStockService) FindProductStockByCondition(logCtx *gin.Context, productId, venueId, warehouse string) (stock *po.ProductStock, err error) {
	stock = &po.ProductStock{}
	err = model.DBSlave.Self.Where("product_id = ? AND venue_id = ? AND warehouse = ?", productId, venueId, warehouse).First(stock).Error
	return
}

func (service *ProductStockService) FindAllProductStock(logCtx *gin.Context, reqDto *req.QueryProductStockReqDto) (list *[]po.ProductStock, err error) {
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.ProductId != nil && *reqDto.ProductId != "" {
		db = db.Where("product_id=?", *reqDto.ProductId)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.Warehouse != nil && *reqDto.Warehouse != "" {
		db = db.Where("warehouse=?", *reqDto.Warehouse)
	}

	db = db.Order("ctime desc")
	list = &[]po.ProductStock{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *ProductStockService) FindAllProductStockWithPagination(logCtx *gin.Context, reqDto *req.QueryProductStockReqDto) (list *[]po.ProductStock, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.ProductStock{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.ProductId != nil && *reqDto.ProductId != "" {
		db = db.Where("product_id=?", *reqDto.ProductId)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.Warehouse != nil && *reqDto.Warehouse != "" {
		db = db.Where("warehouse=?", *reqDto.Warehouse)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.ProductStock{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

// extend

// SyncProductStock 同步产品库存（推荐使用）
func (service *ProductStockService) SyncProductStock(ctx *gin.Context, newProductStockList []po.ProductStock, productStockChangeVOs []vo.ProductStockChangeVO) error {
	tx := model.DBMaster.Self.Begin()
	for _, productStock := range newProductStockList {
		if err := productStockService.CreateProductStockWithTx(ctx, &productStock, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, productStockChangeVO := range productStockChangeVOs {
		sign := "+"
		if productStockChangeVO.Count < 0 {
			sign = "-"
			productStockChangeVO.Count = -productStockChangeVO.Count
		}
		err := tx.Exec("UPDATE product_stock SET stock = stock "+sign+" ? WHERE id = ?", productStockChangeVO.Count, productStockChangeVO.Id).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (service *ProductStockService) FindProductStockByProductIds(ctx *gin.Context, venueId string, productIds []string) (productStockList []po.ProductStock, err error) {
	productStockList = []po.ProductStock{}
	db := model.DBSlave.Self.Model(&po.ProductStock{})
	db = db.Where("product_id IN (?)", productIds)
	db = db.Where("venue_id = ?", venueId)
	err = db.Find(&productStockList).Error
	return
}

func (service *ProductStockService) FindProductStocksByVenueId(logCtx *gin.Context, venueId string) (list *[]po.ProductStock, err error) {
	list = &[]po.ProductStock{}
	err = model.DBSlave.Self.Where("venue_id = ?", venueId).Find(list).Error
	return
}
