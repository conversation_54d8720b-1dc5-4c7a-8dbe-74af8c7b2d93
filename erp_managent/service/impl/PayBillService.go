package impl

import (
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"

	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PayBillService struct {
}

var payBillService = &PayBillService{}
var payBillTransfer = &transfer.PayBillTransfer{}

func (service *PayBillService) CreatePayBill(logCtx *gin.Context, payBill *po.PayBill) error {
	return Save(payBill)
}

func (service *PayBillService) CreatePayBillWithTx(logCtx *gin.Context, payBill *po.PayBill, tx *gorm.DB) error {
	return SaveWithTx(payBill, tx)
}

func (service *PayBillService) UpdatePayBill(logCtx *gin.Context, payBill *po.PayBill) error {
	return Update(payBill)
}

func (service *PayBillService) UpdatePayBillPartial(logCtx *gin.Context, payBill *po.PayBill) error {
	return UpdateNotNull(payBill)
}

func (service *PayBillService) UpdatePayBillPartialWithTx(logCtx *gin.Context, payBill *po.PayBill, tx *gorm.DB) error {
	return UpdateNotNullWithTx(payBill, tx)
}

func (service *PayBillService) DeletePayBill(logCtx *gin.Context, id string) error {
	return Delete(po.PayBill{Id: &id})
}

func (service *PayBillService) FindPayBillById(logCtx *gin.Context, id string) (payBill *po.PayBill, err error) {
	payBill = &po.PayBill{}
	err = model.DBMaster.Self.Where("id=?", id).First(payBill).Error
	return
}

func (service *PayBillService) FindPayBillByBillId(logCtx *gin.Context, billId string) (payBill *po.PayBill, err error) {
	payBill = &po.PayBill{}
	err = model.DBMaster.Self.Where("bill_id=?", billId).First(payBill).Error
	return
}

func (service *PayBillService) FindAllPayBill(logCtx *gin.Context, reqDto *req.QueryPayBillReqDto) (list *[]po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.SessionId != nil && *reqDto.SessionId != "" {
		db = db.Where("session_id=?", *reqDto.SessionId)
	}
	if reqDto.BillId != nil && *reqDto.BillId != "" {
		db = db.Where("bill_id=?", *reqDto.BillId)
	}
	if reqDto.BillIds != nil && len(*reqDto.BillIds) > 0 {
		db = db.Where("bill_id in (?)", *reqDto.BillIds)
	}
	if reqDto.BillPid != nil && *reqDto.BillPid != "" {
		db = db.Where("bill_pid=?", *reqDto.BillPid)
	}
	if reqDto.Status != nil && *reqDto.Status != "" {
		db = db.Where("status=?", *reqDto.Status)
	}
	if reqDto.StatusList != nil && len(*reqDto.StatusList) > 0 {
		db = db.Where("status in (?)", *reqDto.StatusList)
	}
	if reqDto.IsBack != nil {
		db = db.Where("is_back=?", *reqDto.IsBack)
	}
	if reqDto.PayType != nil && *reqDto.PayType != "" {
		db = db.Where("pay_type=?", *reqDto.PayType)
	}
	if reqDto.StartTime != nil && *reqDto.StartTime > 0 {
		db = db.Where("finish_time>=?", *reqDto.StartTime)
	}
	if reqDto.EndTime != nil && *reqDto.EndTime > 0 {
		db = db.Where("finish_time<=?", *reqDto.EndTime)
	}
	if reqDto.EmployeeId != nil && *reqDto.EmployeeId != "" {
		db = db.Where("employee_id=?", *reqDto.EmployeeId)
	}
	db = db.Order("ctime desc")
	list = &[]po.PayBill{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *PayBillService) FindAllPayBillWithPagination(logCtx *gin.Context, reqDto *req.QueryPayBillReqDto) (list *[]po.PayBill, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.PayBill{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

func (service *PayBillService) FindPayBillBySessionId(logCtx *gin.Context, venueId string, sessionId string) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? and session_id = ?", venueId, sessionId)
	db = db.Order("ctime desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

func (service *PayBillService) FindPayBill4ShiftHoldoverForm(logCtx *gin.Context, venueId string, sessionId string, startTime int64, endTime int64, statusList []string) (list *[]po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? and session_id = ?", venueId, sessionId)
	db = db.Where("ctime >= ? and ctime <= ?", startTime, endTime)
	db = db.Where("status in (?)", statusList)
	db = db.Order("ctime desc")
	list = &[]po.PayBill{}
	result := db.Find(list)
	return list, result.Error
}

func (service *PayBillService) FindPayBillLastestPaid(logCtx *gin.Context, venueId string, employeeId string, startTime *int64) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? and status = ? and employee_id = ?", venueId, _const.V2_PAY_BILL_STATUS_PAID, employeeId)
	if startTime != nil {
		db = db.Where("finish_time >= ?", *startTime)
	}
	db = db.Order("finish_time asc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

// ConvertToPayBillVO 转换为支付账单VO
func (service *PayBillService) ConvertToPayBillVO(logCtx *gin.Context, payBill po.PayBill) vo.PayBillVO {
	return payBillTransfer.PoToVo(payBill)
}

// ConvertToPayBill 转换为支付账单PO
func (service *PayBillService) ConvertToPayBill(logCtx *gin.Context, payBillVO vo.PayBillVO) po.PayBill {
	return payBillTransfer.VoToPo(payBillVO)
}

// FindAllBySessionId 查询所有支付账单
func (service *PayBillService) FindAllBySessionId(logCtx *gin.Context, venueId, sessionId string) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? and session_id = ?", venueId, sessionId)
	db = db.Order("ctime desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

// FindsBySessionId 查询所有支付账单
func (service *PayBillService) FindsBySessionId(logCtx *gin.Context, venueId, sessionId string) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? and session_id = ?", venueId, sessionId)
	db = db.Order("ctime desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

// FindsByTimeRange 查询所有支付账单
func (service *PayBillService) FindsByTimeRange(logCtx *gin.Context, venueId string, startTime int64, endTime int64) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ?", venueId)
	db = db.Where("ctime >= ?", startTime)
	db = db.Where("ctime <= ?", endTime)
	db = db.Order("ctime desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

// FindsByTimeRange 查询所有支付账单
func (service *PayBillService) FindsByFinishTimeRange(logCtx *gin.Context, venueId string, startTime int64, endTime int64) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ?", venueId)
	db = db.Where("finish_time >= ?", startTime)
	db = db.Where("finish_time <= ?", endTime)
	db = db.Order("finish_time desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

// FindByBillId 查询支付账单
func (service *PayBillService) FindByBillId(logCtx *gin.Context, billId, venueId, sessionId string) (payBill po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("bill_id = ? ", billId)
	db = db.Where("venue_id = ? ", venueId)
	db = db.Where("session_id = ? ", sessionId)
	db = db.Order("ctime desc")
	list := []po.PayBill{}
	err = db.Find(&list).Error
	if err != nil {
		return
	}
	if len(list) > 0 {
		payBill = list[0]
	}
	return
}

// FindsBySessionIds 查询所有支付账单
func (service *PayBillService) FindsBySessionIds(logCtx *gin.Context, venueId string, sessionIds []string) (list []po.PayBill, err error) {
	sessionIdsGroup := util.SplitList(sessionIds, 20)
	for _, sessionIds := range sessionIdsGroup {
		listTmp, err := service.FindsBySessionIds20(logCtx, venueId, sessionIds)
		if err != nil {
			return nil, err
		}
		list = append(list, listTmp...)
	}
	return list, nil
}

// FindsBySessionIds 查询所有支付账单
func (service *PayBillService) FindsBySessionIds20(logCtx *gin.Context, venueId string, sessionIds []string) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? ", venueId)
	db = db.Where("session_id in (?)", sessionIds)
	db = db.Order("ctime desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}

// FindModelBasePayBillVOsByTimeRange 查询支付账单VOs
func (service *PayBillService) FindModelBasePayBillVOsByTimeRange(logCtx *gin.Context, venueId string, startTime int64, endTime int64) (vo.ModelBasePayBillVO, error) {
	payBills, err := service.FindsByFinishTimeRange(logCtx, venueId, startTime, endTime)
	if err != nil {
		return vo.ModelBasePayBillVO{}, err
	}
	payBillVOs := []vo.PayBillVO{}
	billIds := []string{}
	for _, payBill := range payBills {
		payBillVOs = append(payBillVOs, service.ConvertToPayBillVO(logCtx, payBill))
		billIds = append(billIds, *payBill.BillId)
	}
	orderAndPays, err := orderAndPayService.FindOrderAndPaysByBillIds(logCtx, billIds)
	if err != nil {
		return vo.ModelBasePayBillVO{}, err
	}
	orderAndPayVOs := []vo.OrderAndPayVO{}
	orderNos := []string{}
	for _, orderAndPay := range orderAndPays {
		orderAndPayVOs = append(orderAndPayVOs, orderAndPayService.ConvertToOrderAndPayVO(logCtx, orderAndPay))
		orderNos = append(orderNos, *orderAndPay.OrderNo)
	}
	orders, err := orderService.FindOrdersByOrderNos(logCtx, venueId, orderNos)
	if err != nil {
		return vo.ModelBasePayBillVO{}, err
	}
	orderVOs := []vo.OrderVO{}
	for _, order := range orders {
		orderVOs = append(orderVOs, orderService.ConvertToOrderVO(logCtx, order))
	}
	orderProducts, err := orderProductService.FindOrderProductsByOrderNos(logCtx, venueId, orderNos)
	if err != nil {
		return vo.ModelBasePayBillVO{}, err
	}
	orderProductVOs := []vo.OrderProductVO{}
	for _, orderProduct := range orderProducts {
		orderProductVOs = append(orderProductVOs, orderProductService.ConvertToOrderProductVO(logCtx, orderProduct))
	}
	return vo.ModelBasePayBillVO{
		PayBillVOs: payBillVOs,
		OrderVOs: orderVOs,
		OrderAndPayVOs: orderAndPayVOs,
		OrderProductVOs: orderProductVOs,
	}, nil
}

func (service *PayBillService) FindPayBillsByBillIds(logCtx *gin.Context, venueId string, billIds []string) (list []po.PayBill, err error) {
	db := model.DBSlave.Self.Model(&po.PayBill{})
	db = db.Where("venue_id = ? ", venueId)
	db = db.Where("bill_id in (?)", billIds)
	db = db.Order("ctime desc")
	list = []po.PayBill{}
	result := db.Find(&list)
	return list, result.Error
}
