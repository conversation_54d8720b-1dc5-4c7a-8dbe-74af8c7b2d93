package impl

import (
	"errors"
	"fmt"
	"sort"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// GetOrderPOInfoBySessionId 查询session下的所有订单相关PO数据
func (service *PayService) GetOrderPOInfoBySessionId(logCtx *gin.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	orders, _ := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderProducts, _ := orderProductService.FindAllOrderProduct(logCtx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(logCtx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderAndPays, _ := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{SessionId: &sessionId})
	payBills, _ := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	payRecords, _ := payRecordService.FindAllPayRecord(logCtx, &req.QueryPayRecordReqDto{SessionId: &sessionId, VenueId: &venueId})
	return vo.OrderPOInfoUnionVO{
		Orders:         *orders,
		OrderProducts:  *orderProducts,
		OrderRoomPlans: *orderRoomPlans,
		OrderAndPays:   *orderAndPays,
		PayBills:       *payBills,
		PayRecords:     *payRecords,
	}
}

// GetPayInfoBySessionId 查询session下的所有订单相关PO数据
func (service *PayService) GetPayInfoBySessionId(logCtx *gin.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	payBills, _ := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	payRecords, _ := payRecordService.FindAllPayRecord(logCtx, &req.QueryPayRecordReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderAndPays, _ := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{SessionId: &sessionId})
	return vo.OrderPOInfoUnionVO{
		PayBills:     *payBills,
		PayRecords:   *payRecords,
		OrderAndPays: *orderAndPays,
	}
}

// GetPayInfoBillBackBySessionId 查询session下的所有订单相关PO数据
func (service *PayService) GetPayInfoBillBackBySessionId(logCtx *gin.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	payBills, _ := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	payRecords, _ := payRecordService.FindAllPayRecord(logCtx, &req.QueryPayRecordReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderAndPays, _ := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{SessionId: &sessionId})
	orders, _ := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderProducts, _ := orderProductService.FindAllOrderProduct(logCtx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
	orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(logCtx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
	return vo.OrderPOInfoUnionVO{
		PayBills:       *payBills,
		PayRecords:     *payRecords,
		OrderAndPays:   *orderAndPays,
		Orders:         *orders,
		OrderProducts:  *orderProducts,
		OrderRoomPlans: *orderRoomPlans,
	}
}

// BuildOrderVOInfoMergedVOs 构造mergevo
func (service *PayService) BuildOrderVOInfoMergedVOs(logCtx *gin.Context, oisVO vo.OrderPOInfoUnionVO) []vo.OrderVOInfoMergedVO {
	orders, orderProducts, orderRoomPlans := oisVO.Orders, oisVO.OrderProducts, oisVO.OrderRoomPlans
	rtVO := []vo.OrderVOInfoMergedVO{}
	for _, orderN := range orders {
		// 跳过退款
		if *orderN.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			continue
		}
		// 填充正常订单
		normalOrder := orderN
		normalOrderProductVOs := []vo.OrderProductVO{}
		normalOrderRoomPlanVOs := []vo.OrderRoomPlanVO{}
		// 正常商品
		for _, opTmp := range orderProducts {
			if *opTmp.OrderNo == *normalOrder.OrderNo {
				normalOrderProductVOs = append(normalOrderProductVOs, orderProductTransfer.PoToVo(opTmp))
			}
		}
		// 正常房费
		for _, omTmp := range orderRoomPlans {
			if *omTmp.OrderNo == *normalOrder.OrderNo {
				normalOrderRoomPlanVOs = append(normalOrderRoomPlanVOs, orderRoomPlanTransfer.PoToVo(omTmp))
			}
		}
		// 填充退款订单
		refundOrderVOs := []vo.OrderVO{}
		refundOrderProducts := []vo.OrderProductVO{}
		refundOrderRoomPlans := []vo.OrderRoomPlanVO{}
		// 再次遍历所有订单 填充
		for _, orderNInner := range orders {
			// 跳过 非对应的退款订单
			if !(*orderNInner.Direction == _const.V2_ORDER_DIRECTION_REFUND && *orderNInner.POrderNo == *normalOrder.OrderNo) {
				continue
			}
			refundOrder := orderNInner
			// 对应的退款订单
			refundOrderVOs = append(refundOrderVOs, orderTransfer.PoToVo(refundOrder))
			// 退款的商品
			for _, opTmp := range orderProducts {
				if *opTmp.OrderNo == *refundOrder.OrderNo {
					refundOrderProducts = append(refundOrderProducts, orderProductTransfer.PoToVo(opTmp))
				}
			}
			// 退款房费
			for _, omTmp := range orderRoomPlans {
				if *omTmp.OrderNo == *refundOrder.OrderNo {
					refundOrderRoomPlans = append(refundOrderRoomPlans, orderRoomPlanTransfer.PoToVo(omTmp))
				}
			}
		}
		// 合并商品
		mergedOrderProductVOs := []vo.OrderProductVO{}
		for _, opVOTmp := range normalOrderProductVOs {
			newOpVO := util.DeepClone(opVOTmp)
			// 退款商品
			for _, rOpVOTmp := range refundOrderProducts {
				if rOpVOTmp.PId == newOpVO.Id {
					newOpVO.RefundCount += rOpVOTmp.Quantity
					newOpVO.RefundFee += rOpVOTmp.PayAmount
				}
			}
			mergedOrderProductVOs = append(mergedOrderProductVOs, newOpVO)
		}
		// 合并房费
		mergedOrderRoomPlanVOs := []vo.OrderRoomPlanVO{}
		for _, omVOTmp := range normalOrderRoomPlanVOs {
			newOmVO := util.DeepClone(omVOTmp)
			// 退款商品
			for _, rOmVO := range refundOrderRoomPlans {
				if rOmVO.PId == newOmVO.Id {
					newOmVO.RefundCount = 1
					newOmVO.RefundFee = newOmVO.PayAmount
				}
			}
			mergedOrderRoomPlanVOs = append(mergedOrderRoomPlanVOs, newOmVO)
		}

		rtVO = append(rtVO, vo.OrderVOInfoMergedVO{
			OrderVO:                orderTransfer.PoToVo(orderN),
			NormalOrderProductVOs:  normalOrderProductVOs,
			NormalOrderRoomPlanVOs: normalOrderRoomPlanVOs,
			RefundOrderVOs:         refundOrderVOs,
			RefundOrderProductVOs:  refundOrderProducts,
			RefundOrderRoomPlanVOs: refundOrderRoomPlans,
			MergedOrderProductVOs:  mergedOrderProductVOs,
			MergedOrderRoomPlanVOs: mergedOrderRoomPlanVOs,
		})
	}
	return rtVO
}

// BuildPayBillVOInfoMergeVO 构建mergedpaybill
func (service *PayService) BuildPayBillVOInfoMergeVO(ouvo vo.OrderPOInfoUnionVO, orderVOInfoMergedVOs []vo.OrderVOInfoMergedVO) []vo.PayBillVOInfoMergeVO {
	payBills, payRecords, orderAndPays := ouvo.PayBills, ouvo.PayRecords, ouvo.OrderAndPays
	rtVOs := []vo.PayBillVOInfoMergeVO{}
	for _, payBillTmp := range payBills {
		// 跳过退款收款单
		if *payBillTmp.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			continue
		}
		// 设置 PayBillVO
		normalPayBillVO := payBillTransfer.PoToVo(payBillTmp)

		// 设置 PayRecordVOsNomal
		normalPayRecordVOs := []vo.PayRecordVO{}
		for _, prTmp := range payRecords {
			if *prTmp.BillId == normalPayBillVO.BillId {
				normalPayRecordVOs = append(normalPayRecordVOs, payRecordTransfer.PoToVo(prTmp))
			}
		}
		// 设置 OrderAndPayVOsNomal
		normalOrderAndPayVOs := []vo.OrderAndPayVO{}
		newUVO := []vo.OrderVOInfoMergedVO{}
		for _, orderAndPayTmp := range orderAndPays {
			if *orderAndPayTmp.BillId == normalPayBillVO.BillId {
				normalOrderAndPayVOs = append(normalOrderAndPayVOs, orderAndPayTransfer.PoToVo(orderAndPayTmp))
				// 设置支付的mergedVO
				for _, uvoTmp := range orderVOInfoMergedVOs {
					if uvoTmp.OrderVO.OrderNo == *orderAndPayTmp.OrderNo {
						newUVO = append(newUVO, uvoTmp)
					}
				}
			}
		}
		// 再次遍历paybills 查找退款的信息
		// 设置 PayBillVOsRefund PayRecordVOsRefund
		refundPayBillVOs := []vo.PayBillVO{}
		refundPayRecordVOs := []vo.PayRecordVO{}
		refundOrderAndPayVOs := []vo.OrderAndPayVO{}
		for _, payBillTmpInner := range payBills {
			// 跳过正常收款单
			if *payBillTmpInner.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL {
				continue
			}
			refundPayBill := payBillTmpInner
			// 设置退款的信息
			if *refundPayBill.BillPid == normalPayBillVO.BillId {
				refundPayBillVOs = append(refundPayBillVOs, payBillTransfer.PoToVo(refundPayBill))
			}
			// 设置退款payRecords
			for _, prTmp := range payRecords {
				if *prTmp.BillId == *refundPayBill.BillId {
					refundPayRecordVOs = append(refundPayRecordVOs, payRecordTransfer.PoToVo(prTmp))
					normalPayBillVO.RefundAmount += *prTmp.TotalFee
				}
			}
			// 设置退款orderAndPays
			for _, oApTmp := range orderAndPays {
				if *oApTmp.BillId == *refundPayBill.BillId {
					refundOrderAndPayVOs = append(refundOrderAndPayVOs, orderAndPayTransfer.PoToVo(oApTmp))
				}
			}
		}

		// TODO 设置 PayRecordVOsMerged

		rtVOs = append(rtVOs, vo.PayBillVOInfoMergeVO{
			PayBillVO:            normalPayBillVO,
			NormalPayRecordVOs:   normalPayRecordVOs,
			NormalOrderAndPayVOs: normalOrderAndPayVOs,
			RefundPayBillVOs:     refundPayBillVOs,
			RefundPayRecordVOs:   refundPayRecordVOs,
			RefundOrderAndPayVOs: refundOrderAndPayVOs,
			OrderVOInfoMergedVOs: newUVO,
		})
	}
	return rtVOs
}

// BuildPayBillVOInfoBackVO 构建backvo,计算每个bill最终通过哪种方式退款多少钱
// 入参： payBills - 收款单列表
// 入参： payRecords - 支付记录列表
// 出参： []vo.PayBillVOInfoBackVO - 退款信息列表
func (service *PayService) BuildPayBillVOInfoBackVO(logCtx *gin.Context, payBills []po.PayBill, payRecords []po.PayRecord, orderAndPays []po.OrderAndPay) []vo.PayBillVOInfoBackVO {
	rtVOs := []vo.PayBillVOInfoBackVO{}
	for _, payBillTmp := range payBills {
		// 跳过退款收款单
		if *payBillTmp.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			continue
		}
		// 设置 PayBillVO
		normalPayBillVO := payBillTransfer.PoToVo(payBillTmp)

		// 设置 PayRecordVOsNomal
		normalPayRecordVOs := []vo.PayRecordVO{}
		for _, prTmp := range payRecords {
			if *prTmp.BillId == normalPayBillVO.BillId {
				normalPayRecordVOs = append(normalPayRecordVOs, payRecordTransfer.PoToVo(prTmp))
			}
		}
		// 设置 OrderAndPayVOsNomal
		normalOrderAndPayVOs := []vo.OrderAndPayVO{}
		for _, orderAndPayTmp := range orderAndPays {
			if *orderAndPayTmp.BillId == normalPayBillVO.BillId {
				normalOrderAndPayVOs = append(normalOrderAndPayVOs, orderAndPayTransfer.PoToVo(orderAndPayTmp))
			}
		}

		// 再次遍历paybills 查找退款的信息
		// 设置 PayBillVOsRefund PayRecordVOsRefund
		refundPayBillVOs := []vo.PayBillVO{}
		refundPayRecordVOs := []vo.PayRecordVO{}
		for _, payBillTmpInner := range payBills {
			// 跳过正常收款单
			if *payBillTmpInner.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL {
				continue
			}
			refundPayBillTmp := payBillTmpInner
			// 设置退款的信息
			if *refundPayBillTmp.BillPid == normalPayBillVO.BillId {
				normalPayBillVO.RefundAmount += *refundPayBillTmp.TotalFee
				refundPayBillVOs = append(refundPayBillVOs, payBillTransfer.PoToVo(refundPayBillTmp))
			}
			// 设置退款payRecords
			for _, prTmp := range payRecords {
				if *prTmp.BillId == *refundPayBillTmp.BillId {
					refundPayRecordVOs = append(refundPayRecordVOs, payRecordTransfer.PoToVo(prTmp))
				}
			}
		}

		// 设置 PayRecordVOsMerged
		mergedPayRecordVOs := []vo.PayRecordVO{}

		// 按支付ID合并支付记录
		for _, normalRecord := range normalPayRecordVOs {
			mergedRecord := util.DeepClone(normalRecord)
			// 减去对应支付ID的退款金额
			for _, refundRecord := range refundPayRecordVOs {
				if refundRecord.PayPid == normalRecord.PayId {
					mergedRecord.RefundAmount -= refundRecord.TotalFee
				}
			}
			mergedPayRecordVOs = append(mergedPayRecordVOs, mergedRecord)
		}

		rtVOs = append(rtVOs, vo.PayBillVOInfoBackVO{
			PayBillVO:            normalPayBillVO,
			NormalPayRecordVOs:   normalPayRecordVOs,
			NormalOrderAndPayVOs: normalOrderAndPayVOs,
			RefundPayBillVOs:     refundPayBillVOs,
			RefundPayRecordVOs:   refundPayRecordVOs,
			MergedPayRecordVOs:   mergedPayRecordVOs,
		})
	}
	return rtVOs
}

// RefundAllOrderBySessionId 将session下的订单全部原路径退回
func (service *PayService) RefundAllOrderBySessionId(logCtx *gin.Context, sessionId string, venueId string) (vo.OrderPOInfoUnionVO, error) {
	orderPOInfoInSessionVO := service.GetOrderPOInfoBySessionId(logCtx, sessionId, venueId)
	// 校验正在支付？
	for _, payBill := range orderPOInfoInSessionVO.PayBills {
		if *payBill.Status != _const.V2_PAY_BILL_STATUS_PAID {
			return vo.OrderPOInfoUnionVO{}, fmt.Errorf("存在正在支付的订单或退款订单，请重试")
		}
	}
	// 构造订单信息中间对象
	orderVOInfoMergedVOs := service.BuildOrderVOInfoMergedVOs(logCtx, orderPOInfoInSessionVO)
	payBillInfoUnionVOs := service.BuildPayBillVOInfoMergeVO(orderPOInfoInSessionVO, orderVOInfoMergedVOs)

	// 构造退款订单信息
	rtVO, err := service.BuildRefundSeriesData(logCtx, orderVOInfoMergedVOs, payBillInfoUnionVOs)
	if err != nil {
		return rtVO, err
	}
	// 保存退款信息
	err = service.SaveRefundAllOrderBySessionIdInfo(logCtx, rtVO)
	if err != nil {
		return vo.OrderPOInfoUnionVO{}, err
	}
	return rtVO, nil
}

func (service *PayService) BuildRefundSeriesData(logCtx *gin.Context, orderVOInfoMergedVOs []vo.OrderVOInfoMergedVO, payBillInfoUnionVOs []vo.PayBillVOInfoMergeVO) (vo.OrderPOInfoUnionVO, error) {
	rtVO := vo.OrderPOInfoUnionVO{}
	// 处理已支付的
	for _, payBillInfoUnionVOTmp := range payBillInfoUnionVOs {
		payBillVONomal := payBillInfoUnionVOTmp.PayBillVO
		payBillTotalFee := payBillVONomal.TotalFee - payBillVONomal.RefundAmount
		if payBillTotalFee == 0 {
			// 跳过已全退款收款单
			continue
		}

		// 构造退款订单 orders + orderProducts + orderRoomPlans
		orders, orderProducts, orderRoomPlans, payAmountRemaining := service.BuildOrderProductRoomPlan(payBillInfoUnionVOTmp.OrderVOInfoMergedVOs)
		if payBillTotalFee != payAmountRemaining {
			util.Wlog(logCtx).Info("error 计算金额错误")
		}
		rtVO.Orders = append(rtVO.Orders, orders...)
		rtVO.OrderProducts = append(rtVO.OrderProducts, orderProducts...)
		rtVO.OrderRoomPlans = append(rtVO.OrderRoomPlans, orderRoomPlans...)

		// 构造退款收款单 payBill + payRecord + orderAndPay
		// 设置退款收款单
		newBillId := util.GetBillId(payBillVONomal.VenueId)

		// 设置退款收款单
		toAddPayBill := payBillTransfer.VoToPo(payBillVONomal)
		toAddPayBill.Id = nil
		toAddPayBill.BillId = &newBillId
		toAddPayBill.OriginalFee = &payBillTotalFee
		toAddPayBill.ShouldFee = &payBillTotalFee
		toAddPayBill.TotalFee = &payBillTotalFee
		toAddPayBill.ZeroFee = new(int64)
		toAddPayBill.ChangeAmount = new(int64)
		toAddPayBill.RefundWay = util.Ptr(string(_const.REFUND_WAY_TYPE_BACK))
		toAddPayBill.Direction = util.Ptr(string(_const.V2_PAY_BILL_DIRECTION_REFUND))
		toAddPayBill.Status = util.Ptr(string(_const.V2_PAY_BILL_STATUS_PAID))
		toAddPayBill.Ctime = nil
		toAddPayBill.Utime = nil
		toAddPayBill.State = nil
		toAddPayBill.Version = nil

		rtVO.PayBills = append(rtVO.PayBills, toAddPayBill)
	}
	// 处理未付的订单
	orderVOInfoMergedVOssUnpaid := []vo.OrderVOInfoMergedVO{}
	for _, orderVOInfoMergedVOsTmp := range orderVOInfoMergedVOs {
		orderVO := orderVOInfoMergedVOsTmp.OrderVO
		if orderVO.Status == _const.V2_ORDER_STATUS_PAID {
			continue
		}
		orderVOInfoMergedVOssUnpaid = append(orderVOInfoMergedVOssUnpaid, orderVOInfoMergedVOsTmp)
	}
	ordersUnPaid, orderProductsUnPaid, orderRoomPlansUnPaid, _ := service.BuildOrderProductRoomPlan(orderVOInfoMergedVOssUnpaid)
	rtVO.Orders = append(rtVO.Orders, ordersUnPaid...)
	rtVO.OrderProducts = append(rtVO.OrderProducts, orderProductsUnPaid...)
	rtVO.OrderRoomPlans = append(rtVO.OrderRoomPlans, orderRoomPlansUnPaid...)
	return rtVO, nil
}

func (servic *PayService) BuildOrderProductRoomPlan(orderVOInfoMergedVOs []vo.OrderVOInfoMergedVO) (orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, payAmountRemaining int64) {
	orders = []po.Order{}
	orderProducts = []po.OrderProduct{}
	orderRoomPlans = []po.OrderRoomPlan{}
	for _, uvoTmp := range orderVOInfoMergedVOs {
		// 设置退款订单
		newUVO := uvoTmp
		orderNO := util.GetOrderNo(newUVO.OrderVO.VenueId)
		toAddOrder := orderTransfer.VoToPo(newUVO.OrderVO)
		toAddOrder.Id = nil
		toAddOrder.OrderNo = &orderNO
		toAddOrder.POrderNo = &newUVO.OrderVO.OrderNo
		toAddOrder.Direction = util.Ptr(_const.V2_ORDER_DIRECTION_REFUND)
		toAddOrder.Status = util.Ptr(string(_const.V2_ORDER_STATUS_PAID))
		toAddOrder.Ctime = nil
		toAddOrder.Utime = nil
		toAddOrder.State = nil
		toAddOrder.Version = nil
		// 设置退款订单商品
		orderProductVOs := uvoTmp.MergedOrderProductVOs
		for _, opVO := range orderProductVOs {
			newOPVO := opVO
			payAmount := newOPVO.PayAmount - newOPVO.RefundFee
			if payAmount == 0 {
				// 跳过已全退款商品
				continue
			}
			toAddOrderProduct := orderProductTransfer.VoToPo(newOPVO)
			toAddOrderProduct.Id = nil
			toAddOrderProduct.PId = &newOPVO.Id
			toAddOrderProduct.OrderNo = &orderNO
			toAddOrderProduct.PayAmount = &payAmount
			toAddOrderProduct.Ctime = nil
			toAddOrderProduct.Utime = nil
			toAddOrderProduct.State = nil
			toAddOrderProduct.Version = nil
			orderProducts = append(orderProducts, toAddOrderProduct)
			payAmountRemaining += payAmount
		}
		// 设置退款订单房费
		orderRoomPlanVOs := uvoTmp.MergedOrderRoomPlanVOs
		for _, omVO := range orderRoomPlanVOs {
			newOMVO := omVO
			payAmount := newOMVO.PayAmount - newOMVO.RefundFee
			if payAmount == 0 {
				// 跳过已全退款商品
				continue
			}
			toAddOrderRoomPlan := orderRoomPlanTransfer.VoToPo(newOMVO)
			toAddOrderRoomPlan.Id = nil
			toAddOrderRoomPlan.PId = &newOMVO.Id
			toAddOrderRoomPlan.OrderNo = &orderNO
			toAddOrderRoomPlan.PayAmount = &payAmount
			toAddOrderRoomPlan.Ctime = nil
			toAddOrderRoomPlan.Utime = nil
			toAddOrderRoomPlan.State = nil
			toAddOrderRoomPlan.Version = nil
			orderRoomPlans = append(orderRoomPlans, toAddOrderRoomPlan)
			payAmountRemaining += payAmount
		}

		orders = append(orders, toAddOrder)
	}
	return
}

// SaveOrderPayInfoCallbackByPayId 直接更新指定支付类型
// payId 只存在以下场景：
// 支付组合场景: 单独现金（记账支付），单独扫码，以上组合
//
//	只有扫码支付才会有支付失败的情况？
//
// 1. payBill支付: 未支付, payRecord: success+wait(支付中+支付失败)
// 2. payBill支付: 已支付, payRecord: success
// 3. payBill退款: 未支付, payRecord: success+wait(退款中，退款失败)
// 4. payBill退款: 已支付, payRecord: success
func (service *PayService) SaveOrderPayInfoCallbackByPayId(logCtx *gin.Context, callbackVO vo.OrderPayCallbackVO) error {
	payId := callbackVO.PayId
	// 1. 查询payRecord支付记录
	payRecords, err := payRecordService.FindAllPayRecord(logCtx, &req.QueryPayRecordReqDto{PayId: &payId})
	if err != nil || len(*payRecords) <= 0 {
		return errors.New("支付记录不存在")
	}
	payRecordCurrent := (*payRecords)[0]
	// 2. 判断支付记录是否已支付或已退款
	if *payRecordCurrent.Status == _const.V2_PAY_RECORD_STATUS_SUCCESS {
		return errors.New("支付记录已支付或已退款")
	}
	billId := payRecordCurrent.BillId
	// 3. 查询billId对应的收款单
	dbPayBills, err := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{BillId: billId})
	if err != nil || len(*dbPayBills) <= 0 {
		return errors.New("收款单不存在")
	}
	payBillCurrent := (*dbPayBills)[0]
	if *payBillCurrent.Status == _const.PAY_STATUS_PAID {
		return errors.New("订单已支付,请勿重复支付")
	}
	// 4. 查询billId下所有支付单成功的支付记录
	allPayRecords, err := payRecordService.FindAllPayRecord(logCtx, &req.QueryPayRecordReqDto{BillId: billId, Status: util.GetItPtr(_const.V2_PAY_RECORD_STATUS_SUCCESS)})
	if err != nil {
		return errors.New("查询支付记录失败")
	}
	sumSuccessPayAmount := int64(0)
	for _, v := range *allPayRecords {
		sumSuccessPayAmount += *v.TotalFee
	}
	nowTime := int64(util.TimeNowUnix())
	toUpdatePayBills := []po.PayBill{}
	toUpdateOrders := []po.Order{}
	var thirdOrderId *string
	var paySource *string
	if callbackVO.Type == _const.V2_PAY_CALLBACK_TYPE_PAY {
		thirdOrderId = &callbackVO.PayCallbackModel.Leshua_order_id
		paySource = &callbackVO.PayCallbackModel.Pay_way
	} else if callbackVO.Type == _const.V2_PAY_CALLBACK_TYPE_REFUND {
		thirdOrderId = &callbackVO.RefundCallbackModel.Leshua_order_id
	}
	toUpdatePayRecords := []po.PayRecord{
		{
			Id:           payRecordCurrent.Id,
			Status:       util.GetItPtr(_const.V2_PAY_RECORD_STATUS_SUCCESS),
			ThirdOrderId: thirdOrderId,
			PaySource:    paySource,
			FinishTime:   &nowTime,
		},
	}
	// 收款单全部支付的情况
	if sumSuccessPayAmount+*payRecordCurrent.TotalFee >= *payBillCurrent.TotalFee {
		// 更新收款单为已支付
		toUpdatePayBills = append(toUpdatePayBills, po.PayBill{
			Id:         payBillCurrent.Id,
			Status:     util.GetItPtr(_const.PAY_STATUS_PAID),
			FinishTime: &nowTime,
		})
		isBack := *payBillCurrent.IsBack
		if isBack {
			// 还原的支付单
			util.Wlog(logCtx).Infof("还原的支付单: %s", *payBillCurrent.BillId)
		} else {
			// 查询对应支付订单中间表，找到订单
			orderAndPays, err := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{BillId: payBillCurrent.BillId})
			if err != nil || len(*orderAndPays) <= 0 {
				return errors.New("查询订单支付记录失败")
			}
			orderNos := []string{}
			for _, v := range *orderAndPays {
				orderNos = append(orderNos, *v.OrderNo)
			}
			orders, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{OrderNos: &orderNos, SessionId: payBillCurrent.SessionId, VenueId: payBillCurrent.VenueId})
			if err != nil {
				return errors.New("查询订单失败")
			}
			for _, v := range *orders {
				toUpdateOrders = append(toUpdateOrders, po.Order{
					Id:     v.Id,
					Status: util.GetItPtr(_const.ORDER_STATUS_PAID),
				})
			}
		}
	}
	err = service.SaveOrderPayCallbackInfo(logCtx, toUpdatePayBills, toUpdatePayRecords, toUpdateOrders)
	if err != nil {
		return errors.New("更新订单支付记录失败")
	}
	if *payBillCurrent.IsCancel {
		// 更新session未付金额
		err = service.UpdateSessionInfoUnpaidForCancel(logCtx, *payBillCurrent.SessionId, *payBillCurrent.VenueId)
		if err != nil {
			return errors.New("更新session失败")
		}
		payService.SendNATSMessageForRoomStatusChanged(logCtx, *payBillCurrent.VenueId)
	} else {
		// 更新session未付金额
		err = service.UpdateSessionInfoUnpaid(logCtx, *payBillCurrent.SessionId, *payBillCurrent.VenueId)
		if err != nil {
			return errors.New("更新session失败")
		}
	}
	return nil
}

func (service *PayService) UpdateSessionInfoUnpaid(logCtx *gin.Context, sessionId string, venueId string) error {
	session, err := sessionService.FindAllSession(logCtx, &req.QuerySessionReqDto{SessionId: &sessionId, VenueId: &venueId})
	if err != nil || len(*session) <= 0 {
		return errors.New("查询session失败")
	}
	sessionCurrent := (*session)[0]
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := service.CalculateManyFeeForCallback(logCtx, sessionId, venueId, *sessionCurrent.MinConsume)
	if err != nil {
		return errors.New("计算未付金额失败")
	}
	status := _const.V2_SESSION_PAY_STATUS_PAID
	if unpaidAmount > 0 || *sessionCurrent.IsTimeConsume {
		status = _const.V2_SESSION_PAY_STATUS_UNPAID
	}
	newEndTime, changed, err := service.CalculateSessionTime(logCtx, sessionId, venueId, sessionCurrent)
	if err != nil {
		return errors.New("计算session时间失败")
	}
	toUpdateSession := po.Session{
		Id:             sessionCurrent.Id,
		TotalFee:       util.GetItPtr(totalFee),
		UnpaidAmount:   util.GetItPtr(unpaidAmount),
		PaidAmount:     util.GetItPtr(paidAmount),
		SupermarketFee: util.GetItPtr(totalSupermarketFee),
		RoomFee:        util.GetItPtr(totalRoomFee),
		PayStatus:      &status,
	}
	if changed {
		toUpdateSession.EndTime = &newEndTime
	}
	err = sessionService.UpdateSessionPartial(logCtx, &toUpdateSession)
	if err != nil {
		return errors.New("更新session失败")
	}
	return nil
}

func (service *PayService) UpdateSessionInfoUnpaidForCancel(logCtx *gin.Context, sessionId string, venueId string) error {
	session, err := sessionService.FindAllSession(logCtx, &req.QuerySessionReqDto{SessionId: &sessionId, VenueId: &venueId})
	if err != nil || len(*session) <= 0 {
		return errors.New("查询session失败")
	}
	sessionCurrent := (*session)[0]
	toUpdateSession := po.Session{
		Id:             sessionCurrent.Id,
		TotalFee:       util.GetItPtr(int64(0)),
		UnpaidAmount:   util.GetItPtr(int64(0)),
		PaidAmount:     util.GetItPtr(int64(0)),
		SupermarketFee: util.GetItPtr(int64(0)),
		RoomFee:        util.GetItPtr(int64(0)),
		PayStatus:      util.GetItPtr(_const.V2_SESSION_PAY_STATUS_PAID),
		Status:         util.GetItPtr(string(_const.V2_SESSION_STATUS_ENDING)),
		CloseTime:      util.GetItPtr(int64(util.TimeNowUnix())),
	}
	toUpdateRoom := po.Room{
		Id:        sessionCurrent.RoomId,
		SessionId: util.Ptr(""),
		Status:    util.GetItPtr(string(_const.V2_ROOM_STATUS_CLEANING)),
		Tag:       util.Ptr(""),
	}
	err = service.SaveCallbackCancelOrderOpen(logCtx, []po.Session{toUpdateSession}, []po.Room{toUpdateRoom})
	if err != nil {
		return errors.New("更新session失败")
	}
	return nil
}

// SaveOrderPayInfoCallbackByBillId 免单专用函数
func (service *PayService) SaveOrderPayInfoCallbackByBillIdForFree(logCtx *gin.Context, billId string) error {
	// 3. 查询billId对应的收款单
	dbPayBills, err := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{BillId: &billId})
	if err != nil || len(*dbPayBills) <= 0 {
		return errors.New("收款单不存在")
	}
	payBillCurrent := (*dbPayBills)[0]
	if *payBillCurrent.Status == _const.PAY_STATUS_PAID {
		return errors.New("订单已支付,请勿重复支付")
	}
	nowTime := int64(util.TimeNowUnix())
	toUpdatePayBills := []po.PayBill{}
	toUpdateOrders := []po.Order{}

	// 更新收款单为已支付
	toUpdatePayBills = append(toUpdatePayBills, po.PayBill{
		Id:         payBillCurrent.Id,
		Status:     util.GetItPtr(_const.PAY_STATUS_PAID),
		FinishTime: &nowTime,
	})
	// 查询对应支付订单中间表，找到订单
	orderAndPays, err := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{BillId: payBillCurrent.BillId})
	if err != nil || len(*orderAndPays) <= 0 {
		return errors.New("查询订单支付记录失败")
	}
	orderNos := []string{}
	for _, v := range *orderAndPays {
		orderNos = append(orderNos, *v.OrderNo)
	}
	orders, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{OrderNos: &orderNos, SessionId: payBillCurrent.SessionId, VenueId: payBillCurrent.VenueId})
	if err != nil {
		return errors.New("查询订单失败")
	}
	for _, v := range *orders {
		toUpdateOrders = append(toUpdateOrders, po.Order{
			Id:     v.Id,
			Status: util.GetItPtr(_const.ORDER_STATUS_PAID),
		})
	}

	err = service.SaveOrderPayCallbackInfo(logCtx, toUpdatePayBills, nil, toUpdateOrders)
	if err != nil {
		return errors.New("更新订单支付记录失败")
	}
	// 更新session未付金额
	err = service.UpdateSessionInfoUnpaid(logCtx, *payBillCurrent.SessionId, *payBillCurrent.VenueId)
	if err != nil {
		return errors.New("更新session失败")
	}
	return nil
}

// V3RefundByCash 退款
func (service *PayService) V3RefundByCash(logCtx *gin.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	defaultOrderRefundInfoVOs := []vo.OrderRefundInfoVO{}

	// 分离未支付的退的订单
	groupNeedNotToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	groupNeedToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	for _, group := range orderInfoGroups {
		if *group.POrder.Status == _const.ORDER_STATUS_PAID {
			groupNeedToPay = append(groupNeedToPay, group)
		} else {
			groupNeedNotToPay = append(groupNeedNotToPay, group)
		}
	}

	// 新生成的订单+商品
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrders := make([]po.Order, 0)

	// 处理不需要发起支付的订单(未支付订单的退款)
	ordersNeedNotToPay, orderProductsNeedNotToPay, toUpdatePOrders := service.V3RefundByCashForNeedNotToPay(groupNeedNotToPay)

	// 处理需要发起支付的订单(已支付订单的退款)
	ordersNeedToPay, orderProductsNeedToPay, newOrderAndPays, toAddPayBill, newPayRecord, err := service.V3RefundByCashForNeedToPayByPriority(reqDto, groupNeedToPay, totalRefundAmount, orderPOInfoUnionVO, _const.REFUND_WAY_TYPE_CASH)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 合并所有的订单+商品+payBill+payRecord
	newOrderProducts = append(newOrderProducts, orderProductsNeedNotToPay...)
	newOrderProducts = append(newOrderProducts, orderProductsNeedToPay...)
	newOrders = append(newOrders, ordersNeedNotToPay...)
	newOrders = append(newOrders, ordersNeedToPay...)

	err = service.SaveV3RefundByCashInfo(logCtx, newOrderProducts, newOrders, newOrderAndPays, toAddPayBill, newPayRecord, toUpdatePOrders)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 同步产品库存
	venue, _ := venueService.FindVenueById(logCtx, *reqDto.VenueId)
	orderService.SyncProductStock(logCtx, venue, _const.V2_ORDER_DIRECTION_REFUND, newOrderProducts)
	// 更新session未付金额
	err = service.UpdateSessionInfoUnpaid(logCtx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}

	return defaultOrderRefundInfoVOs, nil
}

// V3RefundByCashForNeedNotToPay 处理未付的退款订单
func (service *PayService) V3RefundByCashForNeedNotToPay(groupNeedNotToPay []vo.RefundOrderInfoGroupVO) (ordersNeedNotToPay []po.Order, orderProductsNeedNotToPay []po.OrderProduct, toUpdatePOrders []po.Order) {
	if len(groupNeedNotToPay) == 0 {
		return
	}
	ordersNeedNotToPay = []po.Order{}
	toUpdatePOrders = []po.Order{}
	for _, v := range groupNeedNotToPay {
		order := v.Order
		// 判断是否全退，全退：已支付，否则：未支付
		order.Status = util.GetItPtr(string(_const.V2_ORDER_STATUS_UNPAID))
		allCount := 0
		for _, tv := range v.POrderNomalOrderProducts {
			allCount += int(*tv.Quantity)
		}
		hasRefundCount := 0
		for _, tv := range v.POrderRefundOrderProducts {
			hasRefundCount += int(*tv.Quantity)
		}
		thisRefundCount := 0
		for _, tv := range v.OrderProductsRefund {
			thisRefundCount += int(*tv.Quantity)
		}
		if allCount-hasRefundCount-thisRefundCount == 0 {
			order.Status = util.GetItPtr(_const.V2_ORDER_STATUS_PAID)
			toUpdatePOrders = append(toUpdatePOrders, po.Order{Id: v.POrder.Id, Status: util.GetItPtr(_const.V2_ORDER_STATUS_PAID)})
			// 其他退款订单也要更新为已支付
			for _, orderRefund := range v.POrdersRefund {
				toUpdatePOrders = append(toUpdatePOrders, po.Order{Id: orderRefund.Id, Status: util.GetItPtr(_const.V2_ORDER_STATUS_PAID)})
			}
		}
		order.RefundTag = util.GetItPtr(_const.V2_ORDER_REFUND_TAG_UNPAID_REFUND)
		ordersNeedNotToPay = append(ordersNeedNotToPay, order)
	}
	orderProductsNeedNotToPay = []po.OrderProduct{}
	for _, v := range groupNeedNotToPay {
		orderProductsNeedNotToPay = append(orderProductsNeedNotToPay, v.OrderProductsRefund...)
	}
	return
}

// V3RefundByCashForNeedToPay 处理已支付的退款订单
func (service *PayService) V3RefundByCashForNeedToPay(reqDto req.V3QueryOrderRefundReqDto, groupNeedToPay []vo.RefundOrderInfoGroupVO, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, refundType string) (ordersNeedToPay []po.Order, orderProductsNeedToPays []po.OrderProduct, orderAndPays *[]po.OrderAndPay, toAddPayBills *[]po.PayBill, payRecords *[]po.PayRecord, err error) {
	if len(groupNeedToPay) <= 0 {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, nil
	}
	if !util.InList(refundType, _const.REFUND_WAY_TYPES) {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, fmt.Errorf("退款方式不支持: %s", refundType)
	}
	orderAndPays = &[]po.OrderAndPay{}
	orderProductsNeedToPays = []po.OrderProduct{}
	ordersNeedToPay = []po.Order{}
	toAddPayBills = &[]po.PayBill{}
	payRecords = &[]po.PayRecord{}
	// 将退款的商品对应的点按原支付单分组 原收款单对应多个订单，每个订单的退款商品对应一个paybill
	// 根据paybill.billId进行分组
	billGroups := make(map[string][]vo.RefundOrderInfoGroupVO)
	for _, group := range groupNeedToPay {
		billId := *group.POrderPayBill.BillId
		billGroups[billId] = append(billGroups[billId], group)
	}

	// 生成新的paybill,payrecord
	refundBillSum := int64(0)
	for _, groups := range billGroups {
		// 乐刷支付的订单
		leshuaPayOrderNos := []string{}
		// 本billId的退款总金额
		thisBillRefundTotalFee := int64(0)
		// 本billId的退款原价总金额
		thisBillRefundOriginalFee := int64(0)
		for _, group := range groups {
			for _, orderProduct := range group.OrderProductsRefund {
				thisBillRefundTotalFee += *orderProduct.PayAmount
				// 计算原价总金额：原价 * 数量
				if orderProduct.OriginalPrice != nil && orderProduct.Quantity != nil {
					thisBillRefundOriginalFee += *orderProduct.OriginalPrice * *orderProduct.Quantity
				}
			}
		}
		refundBillSum += thisBillRefundTotalFee
		// 取第一个，一组的都一样
		payBillParent := groups[0].POrderPayBill

		newBillId := util.GetBillId(*reqDto.VenueId)
		nowTime := int64(util.TimeNowUnix())
		hasLeshuaPay := false
		// billId对应的支付记录的合并记录 正负冲退
		mergedPayRecords := groups[0].POrderMergedPayRecords
		// 对mergedPayRecords按支付类型权重排序，权重值越小，优先退款
		sort.Slice(mergedPayRecords, func(i, j int) bool {
			weightI := _const.REFUND_ORDER_WIGHT[*mergedPayRecords[i].PayType]
			weightJ := _const.REFUND_ORDER_WIGHT[*mergedPayRecords[j].PayType]
			return weightI < weightJ
		})
		// 还未分配的需要退款的金额  【 总共要退的钱 】
		remainingNeedRefundAmount := thisBillRefundTotalFee
		for _, mergeRecordTmp := range mergedPayRecords {
			// 余额为0，退出
			if remainingNeedRefundAmount <= 0 {
				break
			}
			// 当前记录的剩余可退款金额
			currentPayRecordAmountRemaining := *mergeRecordTmp.TotalFee
			// 当前记录的退款金额
			currentPayRecordRefundAmount := remainingNeedRefundAmount
			if currentPayRecordAmountRemaining < remainingNeedRefundAmount { // 不够退款的
				currentPayRecordRefundAmount = currentPayRecordAmountRemaining
				remainingNeedRefundAmount -= currentPayRecordAmountRemaining
			} else {
				remainingNeedRefundAmount = 0
			}
			lastPayType := *mergeRecordTmp.PayType
			if refundType == _const.REFUND_WAY_TYPE_CASH {
				lastPayType = _const.PAY_TYPE_RECORD_CASH
			}
			// 乐刷支付的订单
			lastPayStatus := _const.V2_PAY_RECORD_STATUS_SUCCESS
			if refundType == _const.REFUND_WAY_TYPE_BACK && lastPayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
				lastPayStatus = _const.V2_PAY_RECORD_STATUS_WAIT
				for _, group := range groups {
					refundOrder := group.Order
					leshuaPayOrderNos = append(leshuaPayOrderNos, *refundOrder.OrderNo)
				}
			}
			payId := util.GetPayId(*reqDto.VenueId)
			*payRecords = append(*payRecords, po.PayRecord{
				VenueId:                 reqDto.VenueId,
				RoomId:                  reqDto.RoomId,
				EmployeeId:              mergeRecordTmp.EmployeeId,
				MemberCardId:            mergeRecordTmp.MemberCardId,
				SessionId:               reqDto.SessionId,
				BillId:                  &newBillId,
				PayId:                   &payId,
				PayPid:                  mergeRecordTmp.PayId,
				TotalFee:                &currentPayRecordRefundAmount,
				PrincipalAmount:         &currentPayRecordRefundAmount,
				MemberRoomBonusAmount:   new(int64),
				MemberGoodsBonusAmount:  new(int64),
				MemberCommonBonusAmount: new(int64),
				PayType:                 &lastPayType,
				Status:                  &lastPayStatus,
				FinishTime:              &nowTime,
			})
		}
		lastPayStatus := _const.V2_PAY_BILL_STATUS_PAID
		if hasLeshuaPay {
			lastPayStatus = _const.V2_PAY_BILL_STATUS_WAIT
		}
		*toAddPayBills = append(*toAddPayBills, po.PayBill{
			VenueId:     reqDto.VenueId,
			RoomId:      reqDto.RoomId,
			EmployeeId:  reqDto.EmployeeId,
			MemberId:    payBillParent.MemberId,
			SessionId:   reqDto.SessionId,
			BillId:      &newBillId,
			BillPid:     payBillParent.BillId,
			TotalFee:    &thisBillRefundTotalFee,
			ShouldFee:   &thisBillRefundOriginalFee,
			OriginalFee: &thisBillRefundOriginalFee,
			RefundWay:   &refundType,
			Status:      &lastPayStatus,
			Direction:   util.GetItPtr(_const.V2_PAY_BILL_DIRECTION_REFUND),
			FinishTime:  &nowTime,
		})

		for _, v := range groups {
			order := v.Order
			payStatus := _const.V2_ORDER_STATUS_PAID
			if refundType == _const.REFUND_WAY_TYPE_BACK && util.InList(*order.OrderNo, leshuaPayOrderNos) {
				payStatus = _const.V2_ORDER_STATUS_UNPAID
			}
			order.Status = &payStatus
			ordersNeedToPay = append(ordersNeedToPay, order)

			*orderAndPays = append(*orderAndPays, po.OrderAndPay{
				SessionId: reqDto.SessionId,
				OrderNo:   v.Order.OrderNo,
				BillId:    &newBillId,
			})
		}

		for _, v := range groups {
			orderProductsNeedToPays = append(orderProductsNeedToPays, v.OrderProductsRefund...)
		}
	}
	if refundBillSum != totalRefundAmount {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, fmt.Errorf("退款金额不匹配, %d, %d", refundBillSum, totalRefundAmount)
	}

	return
}

// V3RefundByBack 按原路返回退款
func (service *PayService) V3RefundByBack(logCtx *gin.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	defaultOrderRefundInfoVOs := []vo.OrderRefundInfoVO{}
	defaultOrderRefundInfoVO := vo.OrderRefundInfoVO{}

	// 分离未支付的退的订单
	groupNeedNotToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	groupNeedToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	for _, group := range orderInfoGroups {
		if *group.POrder.Status == _const.ORDER_STATUS_PAID {
			groupNeedToPay = append(groupNeedToPay, group)
		} else {
			groupNeedNotToPay = append(groupNeedNotToPay, group)
		}
	}

	// 新生成的订单+商品
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrders := make([]po.Order, 0)

	// 处理不需要发起支付的订单(未支付订单的退款)
	ordersNeedNotToPay, orderProductsNeedNotToPay, toUpdatePOrders := service.V3RefundByCashForNeedNotToPay(groupNeedNotToPay)

	// 处理需要发起支付的订单(已支付订单的退款)
	ordersNeedToPay, orderProductsNeedToPay, newOrderAndPays, toAddPayBills, newPayRecords, err := service.V3RefundByCashForNeedToPayByPriority(reqDto, groupNeedToPay, totalRefundAmount, orderPOInfoUnionVO, _const.REFUND_WAY_TYPE_BACK)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 合并所有的订单+商品+payBill+payRecord
	newOrderProducts = append(newOrderProducts, orderProductsNeedNotToPay...)
	newOrderProducts = append(newOrderProducts, orderProductsNeedToPay...)
	newOrders = append(newOrders, ordersNeedNotToPay...)
	newOrders = append(newOrders, ordersNeedToPay...)

	// 处理会员退款
	for i := range *newPayRecords {
		payRecord := (*newPayRecords)[i]
		// 跳过非会员卡支付
		if *payRecord.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 查询会员卡信息
		memberCard, err := memberCardService.FindMemberCardById(logCtx, *payRecord.MemberCardId)
		if err != nil {
			return defaultOrderRefundInfoVOs, err
		}
		// 验证会员卡状态
		statusErr := util.ValidateMemberCardStatus(*memberCard.Status)
		if statusErr != nil {
			return defaultOrderRefundInfoVOs, statusErr
		}
		res, err := memberRechargeBillService.V3RPCMemberCardRefund(logCtx, req.V3RPCRefundMoneyReqDto{
			MemberCardId:      payRecord.MemberCardId,
			PayId:             payRecord.PayId,
			PayPid:            payRecord.PayPid,
			Amount:            payRecord.TotalFee,
			PrincipalAmount:   payRecord.PrincipalAmount,
			RoomBonusAmount:   payRecord.MemberRoomBonusAmount,
			GoodsBonusAmount:  payRecord.MemberGoodsBonusAmount,
			CommonBonusAmount: payRecord.MemberCommonBonusAmount,
			EmployeeId:        reqDto.EmployeeId,
			VenueId:           reqDto.VenueId,
			SessionId:         reqDto.SessionId,
		})
		if err != nil {
			return defaultOrderRefundInfoVOs, err
		}
		// 发送会员卡退款短信
		principalAmount := util.GetPtrSafeDefault(payRecord.PrincipalAmount, 0)
		roomBonusAmount := util.GetPtrSafeDefault(payRecord.MemberRoomBonusAmount, 0)
		goodsBonusAmount := util.GetPtrSafeDefault(payRecord.MemberGoodsBonusAmount, 0)
		commonBonusAmount := util.GetPtrSafeDefault(payRecord.MemberCommonBonusAmount, 0)
		orderService.SendSmsMemberCardRefund(logCtx, *reqDto.VenueId, *payRecord.MemberCardId, principalAmount, roomBonusAmount, goodsBonusAmount, commonBonusAmount)

		// 更新退款支付ID,并回写入payRecord
		payRecord.ThirdOrderId = &res.MemberRechargeBill.BillId
		(*newPayRecords)[i] = payRecord
	}

	err = service.SaveV3RefundByCashInfo(logCtx, newOrderProducts, newOrders, newOrderAndPays, toAddPayBills, newPayRecords, toUpdatePOrders)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 同步产品库存
	venue, _ := venueService.FindVenueById(logCtx, *reqDto.VenueId)
	orderService.SyncProductStock(logCtx, venue, _const.V2_ORDER_DIRECTION_REFUND, newOrderProducts)
	// 记录会员卡消费记录
	service.RecordMemberCardConsumeRefund(logCtx, newPayRecords, toAddPayBills)

	hasLeshuaPay := false
	if newPayRecords != nil {
		payRecordVOs := make([]vo.PayRecordVO, 0)
		for _, payRecord := range *newPayRecords {
			payRecordVOs = append(payRecordVOs, payRecordTransfer.PoToVo(payRecord))
			if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
			}
		}
		defaultOrderRefundInfoVO.PayRecordVOs = payRecordVOs
	}
	if !hasLeshuaPay {
		// 更新session未付金额
		err = service.UpdateSessionInfoUnpaid(logCtx, *reqDto.SessionId, *reqDto.VenueId)
		if err != nil {
			return defaultOrderRefundInfoVOs, err
		}
	}
	defaultOrderRefundInfoVOs = append(defaultOrderRefundInfoVOs, defaultOrderRefundInfoVO)
	return defaultOrderRefundInfoVOs, nil
}

func (service *PayService) RecordMemberCardConsumeRefund(logCtx *gin.Context, toAddPayRecords *[]po.PayRecord, toAddPayBills *[]po.PayBill) {
	defer func() {
		if err := recover(); err != nil {
			util.Wlog(logCtx).Errorf("RecordMemberCardConsume panic: %v", err)
		}
	}()
	var memberCardPayRecord po.PayRecord
	hasMemberCardPay := false
	memberCardPayRecordPrincipalAmount := int64(0)
	memberCardPayRecordRoomBonusAmount := int64(0)
	memberCardPayRecordGoodsBonusAmount := int64(0)
	memberCardPayRecordCommonBonusAmount := int64(0)
	for _, payRecord := range *toAddPayRecords {
		if *payRecord.PayType == _const.PAY_TYPE_MEMBER_CARD {
			memberCardPayRecord = payRecord
			hasMemberCardPay = true
			memberCardPayRecordPrincipalAmount += *payRecord.PrincipalAmount
			memberCardPayRecordRoomBonusAmount += *payRecord.MemberRoomBonusAmount
			memberCardPayRecordGoodsBonusAmount += *payRecord.MemberGoodsBonusAmount
			memberCardPayRecordCommonBonusAmount += *payRecord.MemberCommonBonusAmount
			break
		}
	}
	if !hasMemberCardPay {
		util.Wlog(logCtx).Warnf("没有会员卡支付记录, 无需记录会员卡消费记录")
		return
	}
	util.Wlog(logCtx).Infof("有会员卡支付记录, 记录会员卡消费记录")

	toAddPayBill := (*toAddPayBills)[0]
	venueId := *toAddPayBill.VenueId
	roomId := *toAddPayBill.RoomId
	sessionId := *toAddPayBill.SessionId
	employee, err := employeeService.FindEmployeeById(logCtx, *toAddPayBill.EmployeeId)
	if err != nil {
		util.Wlog(logCtx).Errorf("查询员工信息失败: %v", err)
		employee = &po.Employee{
			Id:    toAddPayBill.EmployeeId,
			Name:  util.GetItPtr("未知"),
			Phone: util.GetItPtr("未知"),
		}
	}
	venue, err := venueService.FindVenueById(logCtx, venueId)
	if err != nil {
		util.Wlog(logCtx).Errorf("查询门店信息失败: %v", err)
		venue = &po.Venue{
			Id:   &venueId,
			Name: util.GetItPtr("未知"),
		}
	}
	room, err := roomService.FindRoomById(logCtx, roomId)
	if err != nil {
		util.Wlog(logCtx).Errorf("查询房间信息失败: %v", err)
		room = &po.Room{
			Id:   &roomId,
			Name: util.GetItPtr("未知"),
		}
	}
	memberCardId := *memberCardPayRecord.MemberCardId
	memberCardNewTmp, err := memberRechargeBillService.V3RPCMemberCardQuery(logCtx, req.V3QueryMemberCardQueryReqDto{
		MemberCardId: &memberCardId,
	})
	if err != nil {
		util.Wlog(logCtx).Errorf("查询会员卡信息失败: %v", err)
		return
	}
	var memberCardRecord po.PayRecord
	for _, payRecord := range *toAddPayRecords {
		if *payRecord.PayType == _const.PAY_TYPE_MEMBER_CARD {
			memberCardRecord = payRecord
			break
		}
	}
	balance := memberCardNewTmp.PrincipalBalance + memberCardNewTmp.CommonBonusBalance + memberCardNewTmp.GoodsBonusBalance + memberCardNewTmp.RoomBonusBalance
	err = memberCardConsumeService.CreateMemberCardConsume(logCtx, &po.MemberCardConsume{
		VenueId:                            &venueId,
		VenueName:                          venue.Name,
		RoomId:                             &roomId,
		RoomName:                           room.Name,
		EmployeeId:                         employee.Id,
		EmployeeName:                       employee.Name,
		EmployeePhone:                      employee.Phone,
		MemberId:                           &memberCardId,
		MemberCardId:                       &memberCardId,
		MemberCardNumber:                   &memberCardNewTmp.CardNumber,
		MemberCardBalance:                  &balance,
		MemberCardBalancePrincipalAmount:   &memberCardNewTmp.PrincipalBalance,
		MemberCardBalanceRoomBonusAmount:   &memberCardNewTmp.RoomBonusBalance,
		MemberCardBalanceGoodsBonusAmount:  &memberCardNewTmp.GoodsBonusBalance,
		MemberCardBalanceCommonBonusAmount: &memberCardNewTmp.CommonBonusBalance,
		BizType:                            util.Ptr(_const.V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_REFUND),
		SessionId:                          &sessionId,
		BillId:                             toAddPayBill.BillId,
		OriginalFee:                        toAddPayBill.OriginalFee,
		ShouldFee:                          toAddPayBill.ShouldFee,
		TotalFee:                           toAddPayBill.TotalFee,
		ZeroFee:                            toAddPayBill.ZeroFee,
		Direction:                          toAddPayBill.Direction,
		PayId:                              memberCardRecord.PayId,
		PayPid:                             memberCardRecord.PayPid,
		PayRecordTotalAmout:                memberCardRecord.TotalFee,
		PayRecordPrincipalAmount:           &memberCardPayRecordPrincipalAmount,
		PayRecordRoomBonusAmount:           &memberCardPayRecordRoomBonusAmount,
		PayRecordGoodsBonusAmount:          &memberCardPayRecordGoodsBonusAmount,
		PayRecordCommonBonusAmount:         &memberCardPayRecordCommonBonusAmount,
	})
	if err != nil {
		util.Wlog(logCtx).Errorf("保存会员卡消费记录失败: %v", err)
		return
	}
}

// V3BillBack 还原账单
func (service *PayService) V3BillBack(logCtx *gin.Context, reqDto req.V3BillBackReqDto, newPayBillVOInfoBackVOs []vo.PayBillPOInfoBackVO) error {
	venueId := reqDto.VenueId
	sessionId := reqDto.SessionId
	toUpdateOrderNos := make([]string, 0)
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	toAddPayBill := make([]po.PayBill, 0)
	toAddPayRecord := make([]po.PayRecord, 0)
	for _, newPayBillVOInfoBackVO := range newPayBillVOInfoBackVOs {
		for _, orderAndPayPO := range newPayBillVOInfoBackVO.OrderAndPayPOs {
			toAddOrderAndPays = append(toAddOrderAndPays, orderAndPayPO)
			toUpdateOrderNos = append(toUpdateOrderNos, *orderAndPayPO.OrderNo)
		}
		toAddPayBill = append(toAddPayBill, newPayBillVOInfoBackVO.PayBillPO)
		toAddPayRecord = append(toAddPayRecord, newPayBillVOInfoBackVO.PayRecordPOs...)
	}
	dbOrders, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{VenueId: venueId, SessionId: sessionId, OrderNos: &toUpdateOrderNos})
	if err != nil {
		return err
	}
	if len(*dbOrders) <= 0 {
		return fmt.Errorf("未查到订单")
	}
	toUpdateOrders := make([]po.Order, 0)
	for _, order := range *dbOrders {
		if util.InList(*order.OrderNo, toUpdateOrderNos) {
			toUpdateOrders = append(toUpdateOrders, po.Order{Id: order.Id, Status: util.GetItPtr(string(_const.V2_ORDER_STATUS_UNPAID))})
		}
	}
	err = service.SaveBillBackInfo(logCtx, toUpdateOrders, toAddOrderAndPays, toAddPayBill, toAddPayRecord)
	if err != nil {
		return err
	}
	// 更新session未付金额
	err = service.UpdateSessionInfoUnpaid(logCtx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return err
	}

	return nil
}

func (service *PayService) SavePayBillInfoBillBackVOs(logCtx *gin.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO, reqDto req.V3BillBackReqDto) error {
	newBills := make([]po.PayBill, 0)
	newPayRecords := make([]po.PayRecord, 0)
	updateOrders := make([]po.Order, 0)
	updatePayBillParent := make([]po.PayBill, 0)
	updateOrderProducts := make([]po.OrderProduct, 0)
	updateOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, payBillInfoBillBackVO := range payBillInfoBillBackVOs {
		newBills = append(newBills, payBillInfoBillBackVO.NewPayBill)
		newPayRecords = append(newPayRecords, payBillInfoBillBackVO.NewPayRecords...)
		updateOrders = append(updateOrders, payBillInfoBillBackVO.UpdateOrders...)
		updatePayBillParent = append(updatePayBillParent, payBillInfoBillBackVO.UpdatePayBillParent)
		updatePayBillParent = append(updatePayBillParent, payBillInfoBillBackVO.UpdatePayBillsRefund...)
		updateOrderProducts = append(updateOrderProducts, payBillInfoBillBackVO.UpdateOrderProducts...)
		updateOrderRoomPlans = append(updateOrderRoomPlans, payBillInfoBillBackVO.UpdateOrderRoomPlans...)
	}
	// 处理会员退款
	for i := range newPayRecords {
		payRecord := newPayRecords[i]
		// 跳过非会员卡支付
		if *payRecord.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 查询会员卡信息
		memberCard, err := memberCardService.FindMemberCardById(logCtx, *payRecord.MemberCardId)
		if err != nil {
			return err
		}
		// 验证会员卡状态
		statusErr := util.ValidateMemberCardStatus(*memberCard.Status)
		if statusErr != nil {
			return statusErr
		}
		res, err := memberRechargeBillService.V3RPCMemberCardRefund(logCtx, req.V3RPCRefundMoneyReqDto{
			MemberCardId:      payRecord.MemberCardId,
			PayId:             payRecord.PayId,
			PayPid:            payRecord.PayPid,
			Amount:            payRecord.TotalFee,
			PrincipalAmount:   payRecord.PrincipalAmount,
			RoomBonusAmount:   payRecord.MemberRoomBonusAmount,
			GoodsBonusAmount:  payRecord.MemberGoodsBonusAmount,
			CommonBonusAmount: payRecord.MemberCommonBonusAmount,
			EmployeeId:        reqDto.EmployeeId,
			VenueId:           reqDto.VenueId,
			SessionId:         reqDto.SessionId,
		})
		if err != nil {
			return err
		}
		// 更新退款支付ID,并回写入payRecord
		payRecord.ThirdOrderId = &res.MemberRechargeBill.BillId
		newPayRecords[i] = payRecord
	}

	service.SavePayBillInfoBillBack(logCtx, newBills, newPayRecords, updateOrders, updatePayBillParent, updateOrderProducts, updateOrderRoomPlans)

	// 记录会员卡消费记录
	service.RecordMemberCardConsumeRefund(logCtx, &newPayRecords, &newBills)

	hasLeshuaPay := false
	for _, payRecord := range newPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			hasLeshuaPay = true
		}
	}
	if !hasLeshuaPay {
		// 更新session未付金额
		err := service.UpdateSessionInfoUnpaid(logCtx, *reqDto.SessionId, *reqDto.VenueId)
		if err != nil {
			return err
		}
	}
	// 乐刷退款
	// 11.b.1 发起支付
	for _, payRecord := range newPayRecords {
		payRecordVO := payRecordTransfer.PoToVo(payRecord)
		if payRecordVO.PayType != _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err := service.TransferLeshuaRefund(logCtx, payRecordVO)
		if err != nil {
			return fmt.Errorf("发起支付失败: %w", err)
		}
	}
	return nil
}

func (service *PayService) DoAndSaveCancelOrderOpenInfo(logCtx *gin.Context, venueId string, modeOrderInfoBaseSessionPO vo.ModeOrderInfoBaseSessionPO) error {
	newPayRecords := modeOrderInfoBaseSessionPO.PayRecords
	newOrderProducts := modeOrderInfoBaseSessionPO.OrderProducts
	newOrderRoomPlans := modeOrderInfoBaseSessionPO.OrderRoomPlans
	newOrderAndPays := modeOrderInfoBaseSessionPO.OrderAndPays
	newOrders := modeOrderInfoBaseSessionPO.Orders
	toUpdateOrders := modeOrderInfoBaseSessionPO.ToUpdateOrders
	toUpdatePayBills := modeOrderInfoBaseSessionPO.ToUpdatePayBills
	newBills := modeOrderInfoBaseSessionPO.PayBills
	toUpdateSessions := modeOrderInfoBaseSessionPO.ToUpdateSessions
	// 处理会员退款
	for i := range newPayRecords {
		payRecord := newPayRecords[i]
		// 跳过非会员卡支付
		if *payRecord.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 查询会员卡信息
		memberCard, err := memberCardService.FindMemberCardById(logCtx, *payRecord.MemberCardId)
		if err != nil {
			return err
		}
		// 验证会员卡状态
		statusErr := util.ValidateMemberCardStatus(*memberCard.Status)
		if statusErr != nil {
			return statusErr
		}
		res, err := memberRechargeBillService.V3RPCMemberCardRefund(logCtx, req.V3RPCRefundMoneyReqDto{
			MemberCardId:      payRecord.MemberCardId,
			PayId:             payRecord.PayId,
			PayPid:            payRecord.PayPid,
			Amount:            payRecord.TotalFee,
			PrincipalAmount:   payRecord.PrincipalAmount,
			RoomBonusAmount:   payRecord.MemberRoomBonusAmount,
			GoodsBonusAmount:  payRecord.MemberGoodsBonusAmount,
			CommonBonusAmount: payRecord.MemberCommonBonusAmount,
			EmployeeId:        payRecord.EmployeeId,
			VenueId:           payRecord.VenueId,
			SessionId:         payRecord.SessionId,
		})
		if err != nil {
			return err
		}
		// 更新退款支付ID,并回写入payRecord
		payRecord.ThirdOrderId = &res.MemberRechargeBill.BillId
		newPayRecords[i] = payRecord
	}
	service.SavePayBillInfoCancelOrderOpen(logCtx, toUpdateOrders, toUpdatePayBills, newBills, newPayRecords, newOrders, newOrderProducts, newOrderRoomPlans, newOrderAndPays, toUpdateSessions)

	// 记录会员卡消费记录
	service.RecordMemberCardConsumeRefund(logCtx, &newPayRecords, &newBills)

	hasLeshuaPay := false
	for _, payRecord := range newPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			hasLeshuaPay = true
		}
	}
	if !hasLeshuaPay {
		// 更新session未付金额
		err := service.UpdateSessionInfoUnpaidForCancel(logCtx, *modeOrderInfoBaseSessionPO.Session.SessionId, venueId)
		if err != nil {
			return err
		}
	}
	// 乐刷退款
	// 11.b.1 发起支付
	for _, payRecord := range newPayRecords {
		payRecordVO := payRecordTransfer.PoToVo(payRecord)
		if payRecordVO.PayType != _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err := service.TransferLeshuaRefund(logCtx, payRecordVO)
		if err != nil {
			return fmt.Errorf("发起支付失败: %w", err)
		}
	}
	return nil
}

// /// 退款处理
// ///
// V3RefundByCashForNeedToPayByPriority 处理已支付的退款订单-按优先级
func (service *PayService) V3RefundByCashForNeedToPayByPriority(reqDto req.V3QueryOrderRefundReqDto, groupNeedToPay []vo.RefundOrderInfoGroupVO, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, refundType string) (ordersNeedToPay []po.Order, orderProductsNeedToPays []po.OrderProduct, orderAndPays *[]po.OrderAndPay, toAddPayBills *[]po.PayBill, payRecords *[]po.PayRecord, err error) {
	if len(groupNeedToPay) <= 0 {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, nil
	}
	if !util.InList(refundType, _const.REFUND_WAY_TYPES) {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, fmt.Errorf("退款方式不支持: %s", refundType)
	}
	orderAndPays = &[]po.OrderAndPay{}
	orderProductsNeedToPays = []po.OrderProduct{}
	ordersNeedToPay = []po.Order{}
	toAddPayBills = &[]po.PayBill{}
	payRecords = &[]po.PayRecord{}
	// 将退款的商品对应的点按原支付单分组 原收款单对应多个订单，每个订单的退款商品对应一个paybill
	// 根据paybill.billId进行分组
	billGroups := make(map[string][]vo.RefundOrderInfoGroupVO)
	for _, group := range groupNeedToPay {
		billId := *group.POrderPayBill.BillId
		billGroups[billId] = append(billGroups[billId], group)
	}

	// 生成新的paybill,payrecord
	refundBillSum := int64(0)
	for _, groups := range billGroups {
		// 乐刷支付的订单
		leshuaPayOrderNos := []string{}
		// 本billId的退款总金额
		thisBillRefundTotalFee := int64(0)
		// 本billId的退款原价总金额
		thisBillRefundOriginalFee := int64(0)
		for _, group := range groups {
			for _, orderProduct := range group.OrderProductsRefund {
				thisBillRefundTotalFee += *orderProduct.PayAmount
				// 计算原价总金额：原价 * 数量
				if orderProduct.OriginalPrice != nil && orderProduct.Quantity != nil {
					thisBillRefundOriginalFee += *orderProduct.OriginalPrice * *orderProduct.Quantity
				}
			}
		}
		refundBillSum += thisBillRefundTotalFee
		// 取第一个，一组的都一样
		payBillParent := groups[0].POrderPayBill

		newBillId := util.GetBillId(*reqDto.VenueId)
		nowTime := int64(util.TimeNowUnix())
		hasLeshuaPay := false
		// billId对应的支付记录的合并记录 正负冲退
		mergedPayRecords := groups[0].POrderMergedPayRecords
		// 对mergedPayRecords按支付类型权重排序，权重值越小，优先退款
		sort.Slice(mergedPayRecords, func(i, j int) bool {
			weightI := _const.REFUND_ORDER_WIGHT[*mergedPayRecords[i].PayType]
			weightJ := _const.REFUND_ORDER_WIGHT[*mergedPayRecords[j].PayType]
			return weightI < weightJ
		})
		// 还未分配的需要退款的金额  【 总共要退的钱 】
		remainingNeedRefundAmount := thisBillRefundTotalFee
		for _, mergeRecordTmp := range mergedPayRecords {
			// 余额为0，退出
			if remainingNeedRefundAmount <= 0 {
				break
			}
			// 当前记录的剩余可退款金额
			currentPayRecordAmountRemaining := *mergeRecordTmp.TotalFee
			// 当前记录的退款金额
			currentPayRecordRefundAmount := remainingNeedRefundAmount
			if currentPayRecordAmountRemaining < remainingNeedRefundAmount { // 不够退款的
				currentPayRecordRefundAmount = currentPayRecordAmountRemaining
				remainingNeedRefundAmount -= currentPayRecordAmountRemaining
			} else {
				remainingNeedRefundAmount = 0
			}
			lastPayType := *mergeRecordTmp.PayType
			if refundType == _const.REFUND_WAY_TYPE_CASH {
				lastPayType = _const.PAY_TYPE_RECORD_CASH
			}
			// 乐刷支付的订单
			lastPayStatus := _const.V2_PAY_RECORD_STATUS_SUCCESS
			if refundType == _const.REFUND_WAY_TYPE_BACK && lastPayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
				lastPayStatus = _const.V2_PAY_RECORD_STATUS_WAIT
				for _, group := range groups {
					refundOrder := group.Order
					leshuaPayOrderNos = append(leshuaPayOrderNos, *refundOrder.OrderNo)
				}
			}
			payId := util.GetPayId(*reqDto.VenueId)

			// 会员卡内部退款优先级处理
			if lastPayType == _const.PAY_TYPE_MEMBER_CARD {
				// 计算会员卡内部退款金额分配
				remainingRefundAmount := currentPayRecordRefundAmount
				// 1. 优先从商品赠金中扣除
				goodsAndRoomBonusAmount := int64(0)
				roomBonusAmount := int64(0)
				if *mergeRecordTmp.MemberGoodsBonusAmount > 0 {
					goodsAndRoomBonusAmount = *mergeRecordTmp.MemberGoodsBonusAmount
					if goodsAndRoomBonusAmount > remainingRefundAmount {
						goodsAndRoomBonusAmount = remainingRefundAmount
					}
					remainingRefundAmount -= goodsAndRoomBonusAmount
				}

				// 2. 再退通用赠金
				commonBonusAmount := int64(0)
				if *mergeRecordTmp.MemberCommonBonusAmount > 0 && remainingRefundAmount > 0 {
					commonBonusAmount = *mergeRecordTmp.MemberCommonBonusAmount
					if commonBonusAmount > remainingRefundAmount {
						commonBonusAmount = remainingRefundAmount
					}
					remainingRefundAmount -= commonBonusAmount
				}

				// 3. 最后退本金
				principalAmount := remainingRefundAmount

				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &principalAmount,
					MemberRoomBonusAmount:   &roomBonusAmount,
					MemberGoodsBonusAmount:  &goodsAndRoomBonusAmount,
					MemberCommonBonusAmount: &commonBonusAmount,
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			} else {
				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &currentPayRecordRefundAmount,
					MemberRoomBonusAmount:   new(int64),
					MemberGoodsBonusAmount:  new(int64),
					MemberCommonBonusAmount: new(int64),
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			}
		}
		lastPayStatus := _const.V2_PAY_BILL_STATUS_PAID
		if hasLeshuaPay {
			lastPayStatus = _const.V2_PAY_BILL_STATUS_WAIT
		}
		*toAddPayBills = append(*toAddPayBills, po.PayBill{
			VenueId:     reqDto.VenueId,
			RoomId:      reqDto.RoomId,
			EmployeeId:  reqDto.EmployeeId,
			MemberId:    payBillParent.MemberId,
			SessionId:   reqDto.SessionId,
			BillId:      &newBillId,
			BillPid:     payBillParent.BillId,
			TotalFee:    &thisBillRefundTotalFee,
			ShouldFee:   &thisBillRefundOriginalFee,
			OriginalFee: &thisBillRefundOriginalFee,
			RefundWay:   &refundType,
			Status:      &lastPayStatus,
			Direction:   util.GetItPtr(_const.V2_PAY_BILL_DIRECTION_REFUND),
			FinishTime:  &nowTime,
			IsGift:      payBillParent.IsGift,
		})

		for _, v := range groups {
			order := v.Order
			payStatus := _const.V2_ORDER_STATUS_PAID
			if refundType == _const.REFUND_WAY_TYPE_BACK && util.InList(*order.OrderNo, leshuaPayOrderNos) {
				payStatus = _const.V2_ORDER_STATUS_UNPAID
			}
			order.Status = &payStatus
			order.RefundTag = util.GetItPtr(_const.V2_ORDER_REFUND_TAG_PAID_REFUND)
			ordersNeedToPay = append(ordersNeedToPay, order)

			*orderAndPays = append(*orderAndPays, po.OrderAndPay{
				SessionId: reqDto.SessionId,
				OrderNo:   v.Order.OrderNo,
				BillId:    &newBillId,
			})
		}

		for _, v := range groups {
			orderProductsNeedToPays = append(orderProductsNeedToPays, v.OrderProductsRefund...)
		}
	}
	if refundBillSum != totalRefundAmount {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, fmt.Errorf("退款金额不匹配, %d, %d", refundBillSum, totalRefundAmount)
	}

	return
}

func (service *PayService) V3RefundByCashForNeedToPayByPriorityLinear(reqDto req.V3QueryOrderRefundReqDto, groupNeedToPay []vo.RefundOrderInfoGroupVO, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, refundType string) (ordersNeedToPay []po.Order, orderProductsNeedToPays []po.OrderProduct, orderAndPays *[]po.OrderAndPay, toAddPayBills *[]po.PayBill, payRecords *[]po.PayRecord, err error) {
	if len(groupNeedToPay) <= 0 {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, nil
	}
	if !util.InList(refundType, _const.REFUND_WAY_TYPES) {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, fmt.Errorf("退款方式不支持: %s", refundType)
	}
	orderAndPays = &[]po.OrderAndPay{}
	orderProductsNeedToPays = []po.OrderProduct{}
	ordersNeedToPay = []po.Order{}
	toAddPayBills = &[]po.PayBill{}
	payRecords = &[]po.PayRecord{}
	// 将退款的商品对应的点按原支付单分组 原收款单对应多个订单，每个订单的退款商品对应一个paybill
	// 根据paybill.billId进行分组
	billGroups := make(map[string][]vo.RefundOrderInfoGroupVO)
	for _, group := range groupNeedToPay {
		billId := *group.POrderPayBill.BillId
		billGroups[billId] = append(billGroups[billId], group)
	}

	// 生成新的paybill,payrecord
	refundBillSum := int64(0)
	for _, groups := range billGroups {
		// 乐刷支付的订单
		leshuaPayOrderNos := []string{}
		// 本billId的退款总金额
		thisBillRefundTotalFee := int64(0)
		// 本billId的退款原价总金额
		thisBillRefundOriginalFee := int64(0)
		for _, group := range groups {
			for _, orderProduct := range group.OrderProductsRefund {
				thisBillRefundTotalFee += *orderProduct.PayAmount
				// 计算原价总金额：原价 * 数量
				if orderProduct.OriginalPrice != nil && orderProduct.Quantity != nil {
					thisBillRefundOriginalFee += *orderProduct.OriginalPrice * *orderProduct.Quantity
				}
			}
		}
		refundBillSum += thisBillRefundTotalFee
		// 取第一个，一组的都一样
		payBillParent := groups[0].POrderPayBill

		newBillId := util.GetBillId(*reqDto.VenueId)
		nowTime := int64(util.TimeNowUnix())
		hasLeshuaPay := false
		// billId对应的支付记录的合并记录 正负冲退
		mergedPayRecords := groups[0].POrderMergedPayRecords

		// 计算总的可退款金额
		totalAvailableRefundAmount := int64(0)
		for _, mergeRecordTmp := range mergedPayRecords {
			totalAvailableRefundAmount += *mergeRecordTmp.TotalFee
		}

		payTypeMapMoneyMax := make(map[string]int64)
		for _, payRecord := range mergedPayRecords {
			if _, ok := payTypeMapMoneyMax[*payRecord.PayType]; !ok {
				payTypeMapMoneyMax[*payRecord.PayType] = 0
			}
			payTypeMapMoneyMax[*payRecord.PayType] += *payRecord.TotalFee
		}

		// 预先计算每个支付记录的退款金额，确保精度不丢失
		calculatedRefundAmounts := make([]int64, len(mergedPayRecords))
		totalCalculatedAmount := int64(0)

		// 按比例分配退款金额
		for i, mergeRecordTmp := range mergedPayRecords {
			// 当前支付记录的可退款金额
			currentPayRecordAmountRemaining := *mergeRecordTmp.TotalFee
			// 按比例计算当前支付记录应退款金额
			currentPayRecordRefundAmount := int64(0)
			if totalAvailableRefundAmount > 0 {
				// 等比例计算：(当前支付记录金额 / 总可退款金额) * 总退款金额
				currentPayRecordRefundAmount = (currentPayRecordAmountRemaining * thisBillRefundTotalFee) / totalAvailableRefundAmount
			}

			// 确保不超过该支付方式的最大可退金额
			maxRefundForPayType := payTypeMapMoneyMax[*mergeRecordTmp.PayType]
			if currentPayRecordRefundAmount > maxRefundForPayType {
				currentPayRecordRefundAmount = maxRefundForPayType
			}

			calculatedRefundAmounts[i] = currentPayRecordRefundAmount
			totalCalculatedAmount += currentPayRecordRefundAmount
		}

		// 将精度余量分配给最后一个非零退款记录
		remainingAmount := thisBillRefundTotalFee - totalCalculatedAmount
		if remainingAmount != 0 {
			for i := len(calculatedRefundAmounts) - 1; i >= 0; i-- {
				if calculatedRefundAmounts[i] > 0 {
					// 确保调整后不超过该支付方式的最大可退金额
					maxRefundForPayType := payTypeMapMoneyMax[*mergedPayRecords[i].PayType]
					newAmount := calculatedRefundAmounts[i] + remainingAmount
					if newAmount > maxRefundForPayType {
						// 如果超过最大值，只加到最大值，剩余继续向前找
						remainingToAdd := maxRefundForPayType - calculatedRefundAmounts[i]
						calculatedRefundAmounts[i] = maxRefundForPayType
						remainingAmount -= remainingToAdd
						if remainingAmount == 0 {
							break
						}
					} else {
						calculatedRefundAmounts[i] = newAmount
						break
					}
				}
			}
		}

		// 按计算好的金额创建支付记录
		for i, mergeRecordTmp := range mergedPayRecords {
			currentPayRecordRefundAmount := calculatedRefundAmounts[i]

			// 如果计算出的退款金额为0，跳过
			if currentPayRecordRefundAmount <= 0 {
				continue
			}

			lastPayType := *mergeRecordTmp.PayType
			if refundType == _const.REFUND_WAY_TYPE_CASH {
				lastPayType = _const.PAY_TYPE_RECORD_CASH
			}
			// 乐刷支付的订单
			lastPayStatus := _const.V2_PAY_RECORD_STATUS_SUCCESS
			if refundType == _const.REFUND_WAY_TYPE_BACK && lastPayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
				lastPayStatus = _const.V2_PAY_RECORD_STATUS_WAIT
				for _, group := range groups {
					refundOrder := group.Order
					leshuaPayOrderNos = append(leshuaPayOrderNos, *refundOrder.OrderNo)
				}
			}
			payId := util.GetPayId(*reqDto.VenueId)

			// 会员卡内部退款优先级处理
			if lastPayType == _const.PAY_TYPE_MEMBER_CARD {
				// 计算原支付记录中各部分的比例
				originalTotalAmount := *mergeRecordTmp.PrincipalAmount + *mergeRecordTmp.MemberGoodsBonusAmount + *mergeRecordTmp.MemberCommonBonusAmount

				// 按比例分配退款金额
				principalAmount := int64(0)
				goodsAndRoomBonusAmount := int64(0)
				commonBonusAmount := int64(0)

				if originalTotalAmount > 0 {
					// 按原始比例分配退款金额
					principalAmount = (*mergeRecordTmp.PrincipalAmount * currentPayRecordRefundAmount) / originalTotalAmount
					goodsAndRoomBonusAmount = (*mergeRecordTmp.MemberGoodsBonusAmount * currentPayRecordRefundAmount) / originalTotalAmount
					commonBonusAmount = (*mergeRecordTmp.MemberCommonBonusAmount * currentPayRecordRefundAmount) / originalTotalAmount
				} else {
					// 如果原金额为0，全部退本金
					principalAmount = currentPayRecordRefundAmount
				}

				// 确保总和等于退款金额（处理除法精度问题）
				calculatedTotal := principalAmount + goodsAndRoomBonusAmount + commonBonusAmount
				if calculatedTotal != currentPayRecordRefundAmount {
					principalAmount += currentPayRecordRefundAmount - calculatedTotal
				}

				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:      reqDto.VenueId,
					RoomId:       reqDto.RoomId,
					EmployeeId:   mergeRecordTmp.EmployeeId,
					MemberCardId: mergeRecordTmp.MemberCardId,
					SessionId:    reqDto.SessionId,
					BillId:       &newBillId,

					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &principalAmount,
					MemberRoomBonusAmount:   new(int64),
					MemberGoodsBonusAmount:  &goodsAndRoomBonusAmount,
					MemberCommonBonusAmount: &commonBonusAmount,
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			} else {
				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &currentPayRecordRefundAmount,
					MemberRoomBonusAmount:   new(int64),
					MemberGoodsBonusAmount:  new(int64),
					MemberCommonBonusAmount: new(int64),
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			}
		}
		lastPayStatus := _const.V2_PAY_BILL_STATUS_PAID
		if hasLeshuaPay {
			lastPayStatus = _const.V2_PAY_BILL_STATUS_WAIT
		}
		*toAddPayBills = append(*toAddPayBills, po.PayBill{
			VenueId:     reqDto.VenueId,
			RoomId:      reqDto.RoomId,
			EmployeeId:  reqDto.EmployeeId,
			MemberId:    payBillParent.MemberId,
			SessionId:   reqDto.SessionId,
			BillId:      &newBillId,
			BillPid:     payBillParent.BillId,
			TotalFee:    &thisBillRefundTotalFee,
			ShouldFee:   &thisBillRefundOriginalFee,
			OriginalFee: &thisBillRefundOriginalFee,
			RefundWay:   &refundType,
			Status:      &lastPayStatus,
			Direction:   util.GetItPtr(_const.V2_PAY_BILL_DIRECTION_REFUND),
			FinishTime:  &nowTime,
			IsGift:      payBillParent.IsGift,
		})

		for _, v := range groups {
			order := v.Order
			payStatus := _const.V2_ORDER_STATUS_PAID
			if refundType == _const.REFUND_WAY_TYPE_BACK && util.InList(*order.OrderNo, leshuaPayOrderNos) {
				payStatus = _const.V2_ORDER_STATUS_UNPAID
			}
			order.Status = &payStatus
			order.RefundTag = util.GetItPtr(_const.V2_ORDER_REFUND_TAG_PAID_REFUND)
			ordersNeedToPay = append(ordersNeedToPay, order)

			*orderAndPays = append(*orderAndPays, po.OrderAndPay{
				SessionId: reqDto.SessionId,
				OrderNo:   v.Order.OrderNo,
				BillId:    &newBillId,
			})
		}

		for _, v := range groups {
			orderProductsNeedToPays = append(orderProductsNeedToPays, v.OrderProductsRefund...)
		}
	}
	if refundBillSum != totalRefundAmount {
		return []po.Order{}, []po.OrderProduct{}, nil, nil, nil, fmt.Errorf("退款金额不匹配, %d, %d", refundBillSum, totalRefundAmount)
	}

	return
}

// V3RefundByCash 退款
func (service *PayService) V3RefundByCashRoomPlan(logCtx *gin.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	defaultOrderRefundInfoVOs := []vo.OrderRefundInfoVO{}

	// 分离未支付的退的订单
	groupNeedNotToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	groupNeedToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	for _, group := range orderInfoGroups {
		if *group.POrder.Status == _const.ORDER_STATUS_PAID {
			groupNeedToPay = append(groupNeedToPay, group)
		} else {
			groupNeedNotToPay = append(groupNeedNotToPay, group)
		}
	}

	// 新生成的订单+商品
	newRoomPlans := make([]po.OrderRoomPlan, 0)
	newOrders := make([]po.Order, 0)

	// 处理不需要发起支付的订单(未支付订单的退款)
	ordersNeedNotToPay, roomPlansNeedNotToPay, toUpdatePOrders := service.V3RefundByCashForNeedNotToPayRoomPlan(groupNeedNotToPay)

	// 处理需要发起支付的订单(已支付订单的退款)
	ordersNeedToPay, roomPlansNeedToPays, newOrderAndPays, toAddPayBill, newPayRecord, err := service.V3RefundByCashForNeedToPayByPriorityRoomPlan(reqDto, groupNeedToPay, totalRefundAmount, orderPOInfoUnionVO, _const.REFUND_WAY_TYPE_CASH)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 合并所有的订单+商品+payBill+payRecord
	newRoomPlans = append(newRoomPlans, roomPlansNeedNotToPay...)
	newRoomPlans = append(newRoomPlans, roomPlansNeedToPays...)
	newOrders = append(newOrders, ordersNeedNotToPay...)
	newOrders = append(newOrders, ordersNeedToPay...)

	toUpdateSessions := []po.Session{}
	if session.IsTimeConsume != nil && *session.IsTimeConsume {
		toUpdateSessions = append(toUpdateSessions, po.Session{
			Id:            session.Id,
			IsTimeConsume: util.Ptr(false),
		})
	}
	err = service.SaveV3RefundByCashInfoRoomPlan(logCtx, newRoomPlans, newOrders, newOrderAndPays, toAddPayBill, newPayRecord, toUpdatePOrders, toUpdateSessions)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 更新session未付金额
	err = service.UpdateSessionInfoUnpaid(logCtx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}

	return defaultOrderRefundInfoVOs, nil
}

// V3RefundByCashForNeedNotToPay 处理未付的退款订单
func (service *PayService) V3RefundByCashForNeedNotToPayRoomPlan(groupNeedNotToPay []vo.RefundOrderInfoGroupVO) (ordersNeedNotToPay []po.Order, roomPlansNeedNotToPay []po.OrderRoomPlan, toUpdatePOrders []po.Order) {
	if len(groupNeedNotToPay) == 0 {
		return
	}
	ordersNeedNotToPay = []po.Order{}
	toUpdatePOrders = []po.Order{}
	for _, v := range groupNeedNotToPay {
		order := v.Order
		// 房费按订单退，退款订单状态更新为已支付
		order.Status = util.GetItPtr(_const.V2_ORDER_STATUS_PAID)
		toUpdatePOrders = append(toUpdatePOrders, po.Order{Id: v.POrder.Id, Status: util.GetItPtr(_const.V2_ORDER_STATUS_PAID)})
		// 退款订单标签更新为未支付退款
		order.RefundTag = util.GetItPtr(_const.V2_ORDER_REFUND_TAG_UNPAID_REFUND)
		ordersNeedNotToPay = append(ordersNeedNotToPay, order)
	}
	roomPlansNeedNotToPay = []po.OrderRoomPlan{}
	for _, v := range groupNeedNotToPay {
		for _, roomPlan := range v.OrderRoomPlansRefund {
			roomPlan.IsTimeConsume = util.Ptr(false)
			roomPlansNeedNotToPay = append(roomPlansNeedNotToPay, roomPlan)
		}
	}
	return
}

// /// 退款处理
// ///
// V3RefundByCashForNeedToPayByPriority 处理已支付的退款订单-按优先级
func (service *PayService) V3RefundByCashForNeedToPayByPriorityRoomPlan(reqDto req.V3QueryOrderRoomFeeRefundReqDto, groupNeedToPay []vo.RefundOrderInfoGroupVO, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, refundType string) (ordersNeedToPay []po.Order, roomPlansNeedToPays []po.OrderRoomPlan, orderAndPays *[]po.OrderAndPay, toAddPayBills *[]po.PayBill, payRecords *[]po.PayRecord, err error) {
	if len(groupNeedToPay) <= 0 {
		return []po.Order{}, []po.OrderRoomPlan{}, nil, nil, nil, nil
	}
	if !util.InList(refundType, _const.REFUND_WAY_TYPES) {
		return []po.Order{}, []po.OrderRoomPlan{}, nil, nil, nil, fmt.Errorf("退款方式不支持: %s", refundType)
	}
	orderAndPays = &[]po.OrderAndPay{}
	roomPlansNeedToPays = []po.OrderRoomPlan{}
	ordersNeedToPay = []po.Order{}
	toAddPayBills = &[]po.PayBill{}
	payRecords = &[]po.PayRecord{}
	// 将退款的商品对应的点按原支付单分组 原收款单对应多个订单，每个订单的退款商品对应一个paybill
	// 根据paybill.billId进行分组
	billGroups := make(map[string][]vo.RefundOrderInfoGroupVO)
	for _, group := range groupNeedToPay {
		billId := *group.POrderPayBill.BillId
		billGroups[billId] = append(billGroups[billId], group)
	}

	// 生成新的paybill,payrecord
	refundBillSum := int64(0)
	for _, groups := range billGroups {
		// 乐刷支付的订单
		leshuaPayOrderNos := []string{}
		// 本billId的退款总金额
		thisBillRefundTotalFee := int64(0)
		// 本billId的退款原价总金额
		thisBillRefundOriginalFee := int64(0)
		for _, group := range groups {
			for _, roomPlan := range group.OrderRoomPlansRefund {
				thisBillRefundTotalFee += *roomPlan.PayAmount
				// 计算原价总金额：原价 * 数量
				thisBillRefundOriginalFee += *roomPlan.OriginalPayAmount
			}
		}
		refundBillSum += thisBillRefundTotalFee
		// 取第一个，一组的都一样
		payBillParent := groups[0].POrderPayBill

		newBillId := util.GetBillId(*reqDto.VenueId)
		nowTime := int64(util.TimeNowUnix())
		hasLeshuaPay := false
		// billId对应的支付记录的合并记录 正负冲退
		mergedPayRecords := groups[0].POrderMergedPayRecords
		// 对mergedPayRecords按支付类型权重排序，权重值越小，优先退款
		sort.Slice(mergedPayRecords, func(i, j int) bool {
			weightI := _const.REFUND_ORDER_WIGHT[*mergedPayRecords[i].PayType]
			weightJ := _const.REFUND_ORDER_WIGHT[*mergedPayRecords[j].PayType]
			return weightI < weightJ
		})
		// 还未分配的需要退款的金额  【 总共要退的钱 】
		remainingNeedRefundAmount := thisBillRefundTotalFee
		for _, mergeRecordTmp := range mergedPayRecords {
			// 余额为0，退出
			if remainingNeedRefundAmount <= 0 {
				break
			}
			// 当前记录的剩余可退款金额
			currentPayRecordAmountRemaining := *mergeRecordTmp.TotalFee
			// 当前记录的退款金额
			currentPayRecordRefundAmount := remainingNeedRefundAmount
			if currentPayRecordAmountRemaining < remainingNeedRefundAmount { // 不够退款的
				currentPayRecordRefundAmount = currentPayRecordAmountRemaining
				remainingNeedRefundAmount -= currentPayRecordAmountRemaining
			} else {
				remainingNeedRefundAmount = 0
			}
			lastPayType := *mergeRecordTmp.PayType
			if refundType == _const.REFUND_WAY_TYPE_CASH {
				lastPayType = _const.PAY_TYPE_RECORD_CASH
			}
			// 乐刷支付的订单
			lastPayStatus := _const.V2_PAY_RECORD_STATUS_SUCCESS
			if refundType == _const.REFUND_WAY_TYPE_BACK && lastPayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
				lastPayStatus = _const.V2_PAY_RECORD_STATUS_WAIT
				for _, group := range groups {
					refundOrder := group.Order
					leshuaPayOrderNos = append(leshuaPayOrderNos, *refundOrder.OrderNo)
				}
			}
			payId := util.GetPayId(*reqDto.VenueId)

			// 会员卡内部退款优先级处理
			if lastPayType == _const.PAY_TYPE_MEMBER_CARD {
				// 计算会员卡内部退款金额分配
				remainingRefundAmount := currentPayRecordRefundAmount
				// 1. 优先从商品赠金中扣除
				goodsAndRoomBonusAmount := int64(0)
				roomBonusAmount := int64(0)
				if *mergeRecordTmp.MemberGoodsBonusAmount > 0 {
					goodsAndRoomBonusAmount = *mergeRecordTmp.MemberGoodsBonusAmount
					if goodsAndRoomBonusAmount > remainingRefundAmount {
						goodsAndRoomBonusAmount = remainingRefundAmount
					}
					remainingRefundAmount -= goodsAndRoomBonusAmount
				}

				// 2. 再退通用赠金
				commonBonusAmount := int64(0)
				if *mergeRecordTmp.MemberCommonBonusAmount > 0 && remainingRefundAmount > 0 {
					commonBonusAmount = *mergeRecordTmp.MemberCommonBonusAmount
					if commonBonusAmount > remainingRefundAmount {
						commonBonusAmount = remainingRefundAmount
					}
					remainingRefundAmount -= commonBonusAmount
				}

				// 3. 最后退本金
				principalAmount := remainingRefundAmount

				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &principalAmount,
					MemberRoomBonusAmount:   &roomBonusAmount,
					MemberGoodsBonusAmount:  &goodsAndRoomBonusAmount,
					MemberCommonBonusAmount: &commonBonusAmount,
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			} else {
				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &currentPayRecordRefundAmount,
					MemberRoomBonusAmount:   new(int64),
					MemberGoodsBonusAmount:  new(int64),
					MemberCommonBonusAmount: new(int64),
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			}
		}
		lastPayStatus := _const.V2_PAY_BILL_STATUS_PAID
		if hasLeshuaPay {
			lastPayStatus = _const.V2_PAY_BILL_STATUS_WAIT
		}
		*toAddPayBills = append(*toAddPayBills, po.PayBill{
			VenueId:     reqDto.VenueId,
			RoomId:      reqDto.RoomId,
			EmployeeId:  reqDto.EmployeeId,
			MemberId:    payBillParent.MemberId,
			SessionId:   reqDto.SessionId,
			BillId:      &newBillId,
			BillPid:     payBillParent.BillId,
			TotalFee:    &thisBillRefundTotalFee,
			ShouldFee:   &thisBillRefundOriginalFee,
			OriginalFee: &thisBillRefundOriginalFee,
			RefundWay:   &refundType,
			Status:      &lastPayStatus,
			Direction:   util.GetItPtr(_const.V2_PAY_BILL_DIRECTION_REFUND),
			FinishTime:  &nowTime,
			IsGift:      payBillParent.IsGift,
		})

		for _, v := range groups {
			order := v.Order
			payStatus := _const.V2_ORDER_STATUS_PAID
			if refundType == _const.REFUND_WAY_TYPE_BACK && util.InList(*order.OrderNo, leshuaPayOrderNos) {
				payStatus = _const.V2_ORDER_STATUS_UNPAID
			}
			order.Status = &payStatus
			order.RefundTag = util.GetItPtr(_const.V2_ORDER_REFUND_TAG_PAID_REFUND)
			ordersNeedToPay = append(ordersNeedToPay, order)

			*orderAndPays = append(*orderAndPays, po.OrderAndPay{
				SessionId: reqDto.SessionId,
				OrderNo:   v.Order.OrderNo,
				BillId:    &newBillId,
			})
		}

		for _, v := range groups {
			roomPlansNeedToPays = append(roomPlansNeedToPays, v.OrderRoomPlansRefund...)
		}
	}
	if refundBillSum != totalRefundAmount {
		return []po.Order{}, []po.OrderRoomPlan{}, nil, nil, nil, fmt.Errorf("退款金额不匹配, %d, %d", refundBillSum, totalRefundAmount)
	}

	return
}

func (service *PayService) V3RefundByCashForNeedToPayByPriorityRoomPlanLinear(reqDto req.V3QueryOrderRoomFeeRefundReqDto, groupNeedToPay []vo.RefundOrderInfoGroupVO, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, refundType string) (ordersNeedToPay []po.Order, roomPlansNeedToPays []po.OrderRoomPlan, orderAndPays *[]po.OrderAndPay, toAddPayBills *[]po.PayBill, payRecords *[]po.PayRecord, err error) {
	if len(groupNeedToPay) <= 0 {
		return []po.Order{}, []po.OrderRoomPlan{}, nil, nil, nil, nil
	}
	if !util.InList(refundType, _const.REFUND_WAY_TYPES) {
		return []po.Order{}, []po.OrderRoomPlan{}, nil, nil, nil, fmt.Errorf("退款方式不支持: %s", refundType)
	}
	orderAndPays = &[]po.OrderAndPay{}
	roomPlansNeedToPays = []po.OrderRoomPlan{}
	ordersNeedToPay = []po.Order{}
	toAddPayBills = &[]po.PayBill{}
	payRecords = &[]po.PayRecord{}
	// 将退款的房费对应的点按原支付单分组 原收款单对应多个订单，每个订单的退款房费对应一个paybill
	// 根据paybill.billId进行分组
	billGroups := make(map[string][]vo.RefundOrderInfoGroupVO)
	for _, group := range groupNeedToPay {
		billId := *group.POrderPayBill.BillId
		billGroups[billId] = append(billGroups[billId], group)
	}

	// 生成新的paybill,payrecord
	refundBillSum := int64(0)
	for _, groups := range billGroups {
		// 乐刷支付的订单
		leshuaPayOrderNos := []string{}
		// 本billId的退款总金额
		thisBillRefundTotalFee := int64(0)
		// 本billId的退款原价总金额
		thisBillRefundOriginalFee := int64(0)
		for _, group := range groups {
			for _, roomPlan := range group.OrderRoomPlansRefund {
				thisBillRefundTotalFee += *roomPlan.PayAmount
				// 计算原价总金额：原价 * 数量
				thisBillRefundOriginalFee += *roomPlan.OriginalPayAmount
			}
		}
		refundBillSum += thisBillRefundTotalFee
		// 取第一个，一组的都一样
		payBillParent := groups[0].POrderPayBill

		newBillId := util.GetBillId(*reqDto.VenueId)
		nowTime := int64(util.TimeNowUnix())
		hasLeshuaPay := false
		// billId对应的支付记录的合并记录 正负冲退
		mergedPayRecords := groups[0].POrderMergedPayRecords

		payTypeMapMoneyMax := make(map[string]int64)
		for _, payRecord := range mergedPayRecords {
			if _, ok := payTypeMapMoneyMax[*payRecord.PayType]; !ok {
				payTypeMapMoneyMax[*payRecord.PayType] = 0
			}
			payTypeMapMoneyMax[*payRecord.PayType] += *payRecord.TotalFee
		}

		// 计算总的可退款金额
		totalAvailableRefundAmount := int64(0)
		for _, mergeRecordTmp := range mergedPayRecords {
			totalAvailableRefundAmount += *mergeRecordTmp.TotalFee
		}

		// 预先计算每个支付记录的退款金额，确保精度不丢失
		calculatedRefundAmounts := make([]int64, len(mergedPayRecords))
		totalCalculatedAmount := int64(0)

		// 按比例分配退款金额
		for i, mergeRecordTmp := range mergedPayRecords {
			// 当前支付记录的可退款金额
			currentPayRecordAmountRemaining := *mergeRecordTmp.TotalFee
			// 按比例计算当前支付记录应退款金额
			currentPayRecordRefundAmount := int64(0)
			if totalAvailableRefundAmount > 0 {
				// 等比例计算：(当前支付记录金额 / 总可退款金额) * 总退款金额
				currentPayRecordRefundAmount = (currentPayRecordAmountRemaining * thisBillRefundTotalFee) / totalAvailableRefundAmount
			}

			// 确保不超过该支付方式的最大可退金额
			maxRefundForPayType := payTypeMapMoneyMax[*mergeRecordTmp.PayType]
			if currentPayRecordRefundAmount > maxRefundForPayType {
				currentPayRecordRefundAmount = maxRefundForPayType
			}

			calculatedRefundAmounts[i] = currentPayRecordRefundAmount
			totalCalculatedAmount += currentPayRecordRefundAmount
		}

		// 将精度余量分配给最后一个非零退款记录
		remainingAmount := thisBillRefundTotalFee - totalCalculatedAmount
		if remainingAmount != 0 {
			for i := len(calculatedRefundAmounts) - 1; i >= 0; i-- {
				if calculatedRefundAmounts[i] > 0 {
					// 确保调整后不超过该支付方式的最大可退金额
					maxRefundForPayType := payTypeMapMoneyMax[*mergedPayRecords[i].PayType]
					newAmount := calculatedRefundAmounts[i] + remainingAmount
					if newAmount > maxRefundForPayType {
						// 如果超过最大值，只加到最大值，剩余继续向前找
						remainingToAdd := maxRefundForPayType - calculatedRefundAmounts[i]
						calculatedRefundAmounts[i] = maxRefundForPayType
						remainingAmount -= remainingToAdd
						if remainingAmount == 0 {
							break
						}
					} else {
						calculatedRefundAmounts[i] = newAmount
						break
					}
				}
			}
		}

		// 按计算好的金额创建支付记录
		for i, mergeRecordTmp := range mergedPayRecords {
			currentPayRecordRefundAmount := calculatedRefundAmounts[i]

			// 如果计算出的退款金额为0，跳过
			if currentPayRecordRefundAmount <= 0 {
				continue
			}

			lastPayType := *mergeRecordTmp.PayType
			if refundType == _const.REFUND_WAY_TYPE_CASH {
				lastPayType = _const.PAY_TYPE_RECORD_CASH
			}
			// 乐刷支付的订单
			lastPayStatus := _const.V2_PAY_RECORD_STATUS_SUCCESS
			if refundType == _const.REFUND_WAY_TYPE_BACK && lastPayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
				lastPayStatus = _const.V2_PAY_RECORD_STATUS_WAIT
				for _, group := range groups {
					refundOrder := group.Order
					leshuaPayOrderNos = append(leshuaPayOrderNos, *refundOrder.OrderNo)
				}
			}
			payId := util.GetPayId(*reqDto.VenueId)

			// 会员卡内部退款按比例处理
			if lastPayType == _const.PAY_TYPE_MEMBER_CARD {
				// 计算会员卡内部各部分的原始金额比例
				originalPrincipalAmount := *mergeRecordTmp.PrincipalAmount
				originalGoodsBonusAmount := *mergeRecordTmp.MemberGoodsBonusAmount
				originalCommonBonusAmount := *mergeRecordTmp.MemberCommonBonusAmount
				originalTotalAmount := originalPrincipalAmount + originalGoodsBonusAmount + originalCommonBonusAmount

				// 按原始比例分配退款金额
				principalAmount := int64(0)
				goodsAndRoomBonusAmount := int64(0)
				commonBonusAmount := int64(0)
				roomBonusAmount := int64(0)

				if originalTotalAmount > 0 {
					// 按原始比例分配退款金额
					principalAmount = (originalPrincipalAmount * currentPayRecordRefundAmount) / originalTotalAmount
					goodsAndRoomBonusAmount = (originalGoodsBonusAmount * currentPayRecordRefundAmount) / originalTotalAmount
					commonBonusAmount = (originalCommonBonusAmount * currentPayRecordRefundAmount) / originalTotalAmount

					// 处理除法精度问题，确保总和等于退款金额
					calculatedTotal := principalAmount + goodsAndRoomBonusAmount + commonBonusAmount
					if calculatedTotal != currentPayRecordRefundAmount {
						principalAmount += currentPayRecordRefundAmount - calculatedTotal
					}
				} else {
					// 如果原金额为0，全部退本金
					principalAmount = currentPayRecordRefundAmount
				}

				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &principalAmount,
					MemberRoomBonusAmount:   &roomBonusAmount,
					MemberGoodsBonusAmount:  &goodsAndRoomBonusAmount,
					MemberCommonBonusAmount: &commonBonusAmount,
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			} else {
				*payRecords = append(*payRecords, po.PayRecord{
					VenueId:                 reqDto.VenueId,
					RoomId:                  reqDto.RoomId,
					EmployeeId:              mergeRecordTmp.EmployeeId,
					MemberCardId:            mergeRecordTmp.MemberCardId,
					SessionId:               reqDto.SessionId,
					BillId:                  &newBillId,
					PayId:                   &payId,
					PayPid:                  mergeRecordTmp.PayId,
					TotalFee:                &currentPayRecordRefundAmount,
					PrincipalAmount:         &currentPayRecordRefundAmount,
					MemberRoomBonusAmount:   new(int64),
					MemberGoodsBonusAmount:  new(int64),
					MemberCommonBonusAmount: new(int64),
					PayType:                 &lastPayType,
					Status:                  &lastPayStatus,
					FinishTime:              &nowTime,
				})
			}
		}
		lastPayStatus := _const.V2_PAY_BILL_STATUS_PAID
		if hasLeshuaPay {
			lastPayStatus = _const.V2_PAY_BILL_STATUS_WAIT
		}
		*toAddPayBills = append(*toAddPayBills, po.PayBill{
			VenueId:     reqDto.VenueId,
			RoomId:      reqDto.RoomId,
			EmployeeId:  reqDto.EmployeeId,
			MemberId:    payBillParent.MemberId,
			SessionId:   reqDto.SessionId,
			BillId:      &newBillId,
			BillPid:     payBillParent.BillId,
			TotalFee:    &thisBillRefundTotalFee,
			ShouldFee:   &thisBillRefundOriginalFee,
			OriginalFee: &thisBillRefundOriginalFee,
			RefundWay:   &refundType,
			Status:      &lastPayStatus,
			Direction:   util.GetItPtr(_const.V2_PAY_BILL_DIRECTION_REFUND),
			FinishTime:  &nowTime,
			IsGift:      payBillParent.IsGift,
		})

		for _, v := range groups {
			order := v.Order
			payStatus := _const.V2_ORDER_STATUS_PAID
			if refundType == _const.REFUND_WAY_TYPE_BACK && util.InList(*order.OrderNo, leshuaPayOrderNos) {
				payStatus = _const.V2_ORDER_STATUS_UNPAID
			}
			order.Status = &payStatus
			order.RefundTag = util.GetItPtr(_const.V2_ORDER_REFUND_TAG_PAID_REFUND)
			ordersNeedToPay = append(ordersNeedToPay, order)

			*orderAndPays = append(*orderAndPays, po.OrderAndPay{
				SessionId: reqDto.SessionId,
				OrderNo:   v.Order.OrderNo,
				BillId:    &newBillId,
			})
		}

		for _, v := range groups {
			roomPlansNeedToPays = append(roomPlansNeedToPays, v.OrderRoomPlansRefund...)
		}
	}
	if refundBillSum != totalRefundAmount {
		return []po.Order{}, []po.OrderRoomPlan{}, nil, nil, nil, fmt.Errorf("退款金额不匹配, %d, %d", refundBillSum, totalRefundAmount)
	}

	return
}

// V3RefundByBack 按原路返回退款
func (service *PayService) V3RefundByBackRoomPlan(logCtx *gin.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	defaultOrderRefundInfoVOs := []vo.OrderRefundInfoVO{}
	defaultOrderRefundInfoVO := vo.OrderRefundInfoVO{}

	// 分离未支付的退的订单
	groupNeedNotToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	groupNeedToPay := make([]vo.RefundOrderInfoGroupVO, 0)
	for _, group := range orderInfoGroups {
		if *group.POrder.Status == _const.ORDER_STATUS_PAID {
			groupNeedToPay = append(groupNeedToPay, group)
		} else {
			groupNeedNotToPay = append(groupNeedNotToPay, group)
		}
	}

	// 新生成的订单+商品
	newRoomPlans := make([]po.OrderRoomPlan, 0)
	newOrders := make([]po.Order, 0)

	// 处理不需要发起支付的订单(未支付订单的退款)
	ordersNeedNotToPay, roomPlansNeedNotToPay, toUpdatePOrders := service.V3RefundByCashForNeedNotToPayRoomPlan(groupNeedNotToPay)

	// 处理需要发起支付的订单(已支付订单的退款)
	ordersNeedToPay, roomPlansNeedToPays, newOrderAndPays, toAddPayBills, newPayRecords, err := service.V3RefundByCashForNeedToPayByPriorityRoomPlan(reqDto, groupNeedToPay, totalRefundAmount, orderPOInfoUnionVO, _const.REFUND_WAY_TYPE_BACK)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 合并所有的订单+商品+payBill+payRecord
	newRoomPlans = append(newRoomPlans, roomPlansNeedNotToPay...)
	newRoomPlans = append(newRoomPlans, roomPlansNeedToPays...)
	newOrders = append(newOrders, ordersNeedNotToPay...)
	newOrders = append(newOrders, ordersNeedToPay...)

	// 处理会员退款
	for i := range *newPayRecords {
		payRecord := (*newPayRecords)[i]
		// 跳过非会员卡支付
		if *payRecord.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 查询会员卡信息
		memberCard, err := memberCardService.FindMemberCardById(logCtx, *payRecord.MemberCardId)
		if err != nil {
			return defaultOrderRefundInfoVOs, err
		}
		// 验证会员卡状态
		statusErr := util.ValidateMemberCardStatus(*memberCard.Status)
		if statusErr != nil {
			return defaultOrderRefundInfoVOs, statusErr
		}
		res, err := memberRechargeBillService.V3RPCMemberCardRefund(logCtx, req.V3RPCRefundMoneyReqDto{
			MemberCardId:      payRecord.MemberCardId,
			PayId:             payRecord.PayId,
			PayPid:            payRecord.PayPid,
			Amount:            payRecord.TotalFee,
			PrincipalAmount:   payRecord.PrincipalAmount,
			RoomBonusAmount:   payRecord.MemberRoomBonusAmount,
			GoodsBonusAmount:  payRecord.MemberGoodsBonusAmount,
			CommonBonusAmount: payRecord.MemberCommonBonusAmount,
			EmployeeId:        reqDto.EmployeeId,
			VenueId:           reqDto.VenueId,
			SessionId:         reqDto.SessionId,
		})
		if err != nil {
			return defaultOrderRefundInfoVOs, err
		}
		// 发送会员卡退款短信
		principalAmount := util.GetPtrSafeDefault(payRecord.PrincipalAmount, 0)
		roomBonusAmount := util.GetPtrSafeDefault(payRecord.MemberRoomBonusAmount, 0)
		goodsBonusAmount := util.GetPtrSafeDefault(payRecord.MemberGoodsBonusAmount, 0)
		commonBonusAmount := util.GetPtrSafeDefault(payRecord.MemberCommonBonusAmount, 0)
		orderService.SendSmsMemberCardRefund(logCtx, *reqDto.VenueId, *payRecord.MemberCardId, principalAmount, roomBonusAmount, goodsBonusAmount, commonBonusAmount)

		
		// 更新退款支付ID,并回写入payRecord
		payRecord.ThirdOrderId = &res.MemberRechargeBill.BillId
		(*newPayRecords)[i] = payRecord
	}
	toUpdateSessions := []po.Session{}
	if session.IsTimeConsume != nil && *session.IsTimeConsume {
		toUpdateSessions = append(toUpdateSessions, po.Session{
			Id:            session.Id,
			IsTimeConsume: util.Ptr(false),
		})
	}
	err = service.SaveV3RefundByCashInfoRoomPlan(logCtx, newRoomPlans, newOrders, newOrderAndPays, toAddPayBills, newPayRecords, toUpdatePOrders, toUpdateSessions)
	if err != nil {
		return defaultOrderRefundInfoVOs, err
	}
	// 记录会员卡消费记录
	service.RecordMemberCardConsumeRefund(logCtx, newPayRecords, toAddPayBills)

	hasLeshuaPay := false
	if newPayRecords != nil {
		payRecordVOs := make([]vo.PayRecordVO, 0)
		for _, payRecord := range *newPayRecords {
			payRecordVOs = append(payRecordVOs, payRecordTransfer.PoToVo(payRecord))
			if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
			}
		}
		defaultOrderRefundInfoVO.PayRecordVOs = payRecordVOs
	}
	if !hasLeshuaPay {
		// 更新session未付金额
		err = service.UpdateSessionInfoUnpaid(logCtx, *reqDto.SessionId, *reqDto.VenueId)
		if err != nil {
			return defaultOrderRefundInfoVOs, err
		}
	}
	defaultOrderRefundInfoVOs = append(defaultOrderRefundInfoVOs, defaultOrderRefundInfoVO)
	return defaultOrderRefundInfoVOs, nil
}
