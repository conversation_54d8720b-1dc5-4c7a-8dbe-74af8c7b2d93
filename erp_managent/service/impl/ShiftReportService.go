package impl

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type ShiftReportService struct {
}

func (service *ShiftReportService) CreateShiftReport(logCtx *gin.Context, shiftReport *po.ShiftReport) error {
	return Save(shiftReport)
}

func (service *ShiftReportService) UpdateShiftReport(logCtx *gin.Context, shiftReport *po.ShiftReport) error {
	return Update(shiftReport)
}

func (service *ShiftReportService) UpdateShiftReportPartial(logCtx *gin.Context, shiftReport *po.ShiftReport) error {
	return UpdateNotNull(shiftReport)
}

func (service *ShiftReportService) DeleteShiftReport(logCtx *gin.Context, id string) error {
	return Delete(po.ShiftReport{Id: &id})
}

func (service *ShiftReportService) FindShiftReportById(logCtx *gin.Context, id string) (shiftReport *po.ShiftReport, err error) {
	shiftReport = &po.ShiftReport{}
	err = model.DBMaster.Self.Where("id=?", id).First(shiftReport).Error
	return
}

func (service *ShiftReportService) FindAllShiftReport(logCtx *gin.Context, reqDto *req.QueryShiftReportReqDto) (list *[]po.ShiftReport, err error) {
	db := model.DBSlave.Self.Model(&po.ShiftReport{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	db = db.Order("ctime desc")
	list = &[]po.ShiftReport{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *ShiftReportService) FindAllShiftReportWithPagination(logCtx *gin.Context, reqDto *req.QueryShiftReportReqDto) (list *[]po.ShiftReport, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.ShiftReport{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.EmployeeId != nil && *reqDto.EmployeeId != "" {
		db = db.Where("employee_id=?", *reqDto.EmployeeId)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.StartTime != nil && *reqDto.StartTime > 0 {
		db = db.Where("ctime>=?", *reqDto.StartTime)
	}
	if reqDto.EndTime != nil && *reqDto.EndTime > 0 {
		db = db.Where("ctime<=?", *reqDto.EndTime)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.ShiftReport{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	if reqDto.OrderBy != nil && *reqDto.OrderBy != "" {
		db = db.Order(*reqDto.OrderBy)
	} else {
		db = db.Order("ctime desc")
	}
	err = db.Find(list).Error
	return
}

// GetDailyShiftReport 获取门店日结清单
func (service *ShiftReportService) GetDailyShiftReport(ctx *gin.Context, reqDto *req.QueryDailyShiftReportReqDto) (*vo.DailyShiftReportVO, error) {
	// 1. 参数校验
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, errors.New("门店ID不能为空")
	}
	if reqDto.StartTime == nil || reqDto.EndTime == nil {
		return nil, errors.New("时间范围不能为空")
	}
	// 验证时间戳是否为10位
	if len(fmt.Sprint(*reqDto.StartTime)) != 10 || len(fmt.Sprint(*reqDto.EndTime)) != 10 {
		return nil, errors.New("时间戳必须为10位")
	}

	// 2. 初始化返回结果
	result := &vo.DailyShiftReportVO{}

	// 3. 查询支付账单数据 - 使用FinishTime查询
	payBills, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		VenueId:    reqDto.VenueId,
		StartTime:  reqDto.StartTime, // 使用完成时间范围查询
		EndTime:    reqDto.EndTime,
		StatusList: &[]string{_const.PAY_STATUS_PAID}, // 只统计已支付的订单
	})
	if err != nil {
		return nil, err
	}

	// 4. 统计支付数据
	for _, bill := range *payBills {
		// 4.1 收入统计
		// result.IncomePaied.BusinessIncome += *bill.OriginalFee // 营业应收
		result.IncomePaied.ActualIncome += *bill.TotalFee // 营业实收
		result.IncomePaied.NetIncome += *bill.TotalFee    // 营业净收

		// 商家优惠 = 原始金额 - 实收金额
		// if *bill.OriginalFee > *bill.TotalFee {
		// 	result.IncomePaied.MerchantDiscount += *bill.OriginalFee - *bill.TotalFee
		// }

		// 4.2 支付方式统计
		switch "*bill.PayType" {
		case _const.PAY_TYPE_RECORD_WECHAT: // 微信支付
			result.PayTypePaied.Wechat += *bill.TotalFee
		case _const.PAY_TYPE_RECORD_ALIPAY: // 支付宝支付
			result.PayTypePaied.Alipay += *bill.TotalFee
		case _const.PAY_TYPE_RECORD_CASH: // 现金支付
			result.PayTypePaied.Cash += *bill.TotalFee
		case _const.PAY_TYPE_MEMBER_CARD: // 会员卡支付
			result.PayTypePaied.MemberCardPrincipal += *bill.TotalFee
		case _const.PAY_TYPE_RECORD_BANK: // 银行卡支付
			result.PayTypePaied.BankCard += *bill.TotalFee
		case _const.PAY_TYPE_LESHUA_BSHOWQR: // 乐刷支付
			// 根据实际情况判断具体支付方式
			// TODO: 需要确认乐刷支付应该归类到哪种支付方式
		}
	}

	// 5. 查询开台数据 - 使用StartTime查询
	sessions, err := sessionService.FindAllSession(ctx, &req.QuerySessionReqDto{
		VenueId:        reqDto.VenueId,
		StartTimeStart: reqDto.StartTime, // 使用开始时间范围查询
		StartTimeEnd:   reqDto.EndTime,
	})
	if err != nil {
		return nil, err
	}

	// 6. 统计开台数据
	for _, session := range *sessions {
		result.TableOpened.TotalTables++
		if *session.EndTime == 0 {
			result.TableOpened.OpenTables++
			result.TableOpened.UnsettledOrders++
		} else {
			result.TableOpened.ClosedTables++
			result.TableOpened.SettledOrders++
		}
	}

	return result, nil
}

// preprocessData 预处理数据，将时间回拨endHour对应的秒数
// payBillShiftReportVOs: 支付账单列表
// giftOrders: 赠送订单列表
// memberCardOperationsRecharge: 会员卡充值记录列表
// memberCardConsumes: 会员卡消费记录列表
// endHour: 结束时间，格式为HH:mm
// returns: 处理后的支付账单列表、赠送订单列表、会员卡充值记录列表、会员卡消费记录列表
func (service *ShiftReportService) preprocessData(payBillShiftReportVOs []vo.PayBillShiftReportVO, giftOrders []vo.GiftOrderInfoVO, memberCardOperationsRecharge []po.MemberCardOperation, memberCardConsumes []po.MemberCardConsume, endHour string) ([]vo.PayBillShiftReportVO, []vo.GiftOrderInfoVO, []po.MemberCardOperation, []po.MemberCardConsume) {
	// 解析endHour，格式为HH:mm
	// 由于营业日是 endHour，06:00 代表 06:00 - 06:00
	// 将时钟回拨解决跨天的问题
	// paybills.FinishTime - endHour
	// orders.Ctime - endHour
	// sessions.Ctime - endHour

	hourMin := strings.Split(endHour, ":")
	if len(hourMin) != 2 {
		return payBillShiftReportVOs, giftOrders, memberCardOperationsRecharge, memberCardConsumes
	}
	endHourInt, _ := strconv.Atoi(hourMin[0])
	endMinInt, _ := strconv.Atoi(hourMin[1])

	// 计算需要回拨的秒数
	backSeconds := endHourInt*3600 + endMinInt*60

	// 处理 PayBillShiftReportVOs 的 FinishTime
	processedPayBillShiftReportVOs := make([]vo.PayBillShiftReportVO, len(payBillShiftReportVOs))
	for i, payBillShiftReportVO := range payBillShiftReportVOs {
		processedPayBillShiftReportVO := payBillShiftReportVO // 复制一份，避免修改原始数据
		if processedPayBillShiftReportVO.PayBillVO.FinishTime != 0 {
			// 将时间回拨endHour对应的秒数
			newFinishTime := processedPayBillShiftReportVO.PayBillVO.FinishTime - int64(backSeconds)
			processedPayBillShiftReportVO.PayBillVO.FinishTime = newFinishTime
		}
		processedPayBillShiftReportVOs[i] = processedPayBillShiftReportVO
	}

	// 处理 MemberCardOperationsRecharge 的 Ctime
	processedMemberCardOperationsRecharge := make([]po.MemberCardOperation, len(memberCardOperationsRecharge))
	for i, operation := range memberCardOperationsRecharge {
		processedOperation := operation // 复制一份，避免修改原始数据
		if processedOperation.Ctime != nil && *processedOperation.Ctime != 0 {
			// 将时间回拨endHour对应的秒数
			newCtime := *processedOperation.Ctime - int64(backSeconds)
			processedOperation.Ctime = &newCtime
		}
		processedMemberCardOperationsRecharge[i] = processedOperation
	}

	// 处理 MemberCardConsumes 的 Ctime
	processedMemberCardConsumes := make([]po.MemberCardConsume, len(memberCardConsumes))
	for i, consume := range memberCardConsumes {
		processedConsume := consume // 复制一份，避免修改原始数据
		if processedConsume.Ctime != nil && *processedConsume.Ctime != 0 {
			// 将时间回拨endHour对应的秒数
			newCtime := *processedConsume.Ctime - int64(backSeconds)
			processedConsume.Ctime = &newCtime
		}
		processedMemberCardConsumes[i] = processedConsume
	}

	return processedPayBillShiftReportVOs, giftOrders, processedMemberCardOperationsRecharge, processedMemberCardConsumes
}

// groupDataByDate 将数据按日期分组
// sessions: 开台记录列表
// payBills: 支付账单列表
// orders: 订单列表
// returns: 按日期分组的数据map，key为日期，value为当日数据
func (service *ShiftReportService) groupDataByDate(payBillShiftReportVOs []vo.PayBillShiftReportVO, giftOrders []vo.GiftOrderInfoVO, memberCardOperationsRecharge []po.MemberCardOperation, memberCardConsumes []po.MemberCardConsume) map[string]*vo.ShiftReportDaily {
	// 将sessions、payBills、orders 按日期分组
	dailyGroups := make(map[string]*vo.ShiftReportDaily)

	// 2. 按照FinishTime将PayBill分组 (使用回拨后的FinishTime)
	for _, payBillShiftReportVO := range payBillShiftReportVOs {
		if payBillShiftReportVO.PayBillVO.FinishTime == 0 {
			continue
		}
		// 转换时间戳为日期字符串 YYYY-MM-DD
		date := time.Unix(payBillShiftReportVO.PayBillVO.FinishTime, 0).Format("2006-01-02")

		// 如果该日期不存在，创建新的ShiftReportDaily
		if _, exists := dailyGroups[date]; !exists {
			dailyGroups[date] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
			}
		}

		// 添加PayBill到对应日期
		dailyGroups[date].PayBillShiftReportVOs = append(dailyGroups[date].PayBillShiftReportVOs, payBillShiftReportVO)
	}

	// 3. 按照CTime将GiftOrder分组 (使用回拨后的CTime)
	for _, giftOrderInfoVO := range giftOrders {
		if giftOrderInfoVO.OrderVO.Ctime == 0 {
			continue
		}
		// 转换时间戳为日期字符串 YYYY-MM-DD
		date := time.Unix(giftOrderInfoVO.OrderVO.Ctime, 0).Format("2006-01-02")

		// 如果该日期不存在，创建新的ShiftReportDaily
		if _, exists := dailyGroups[date]; !exists {
			dailyGroups[date] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
			}
		}

		// 添加GiftOrderInfoVO到对应日期
		dailyGroups[date].GiftOrderInfoVOs = append(dailyGroups[date].GiftOrderInfoVOs, giftOrderInfoVO)
	}

	// 4. 按照Ctime将MemberCardOperationsRecharge分组
	for _, operation := range memberCardOperationsRecharge {
		if operation.Ctime == nil || *operation.Ctime == 0 {
			continue
		}
		// 转换时间戳为日期字符串 YYYY-MM-DD
		date := time.Unix(*operation.Ctime, 0).Format("2006-01-02")

		// 如果该日期不存在，创建新的ShiftReportDaily
		if _, exists := dailyGroups[date]; !exists {
			dailyGroups[date] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
			}
		}

		// 添加MemberCardOperation到对应日期
		dailyGroups[date].RechargeMemberCardOperations = append(dailyGroups[date].RechargeMemberCardOperations, operation)
	}

	// 5. 按照Ctime将MemberCardConsumes分组
	for _, consume := range memberCardConsumes {
		if consume.Ctime == nil || *consume.Ctime == 0 {
			continue
		}
		// 转换时间戳为日期字符串 YYYY-MM-DD
		date := time.Unix(*consume.Ctime, 0).Format("2006-01-02")

		// 如果该日期不存在，创建新的ShiftReportDaily
		if _, exists := dailyGroups[date]; !exists {
			dailyGroups[date] = &vo.ShiftReportDaily{
				PayBillShiftReportVOs:        make([]vo.PayBillShiftReportVO, 0),
				GiftOrderInfoVOs:             make([]vo.GiftOrderInfoVO, 0),
				RechargeMemberCardOperations: make([]po.MemberCardOperation, 0),
				PayMemberCardConsumes:        make([]po.MemberCardConsume, 0),
			}
		}

		// 添加MemberCardConsume到对应日期
		dailyGroups[date].PayMemberCardConsumes = append(dailyGroups[date].PayMemberCardConsumes, consume)
	}

	return dailyGroups
}

// fillBusinessOverview 填充营业概况数据
// payBills: 当日支付账单列表
// businessOverview: 需要填充的营业概况对象
func (service *ShiftReportService) fillBusinessOverview(payBillShiftReportVOs []vo.PayBillShiftReportVO, businessOverview *vo.BusinessOverview) {
	// 填充营业概况
	for _, payBillShiftReportVO := range payBillShiftReportVOs {
		billVO := payBillShiftReportVO.PayBillVO
		if billVO.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			// 退款单处理
			if billVO.TotalFee != 0 {
				businessOverview.TotalFee -= billVO.TotalFee // 实收减去退款
			}
			if billVO.ShouldFee != 0 {
				businessOverview.ShouldFee -= billVO.ShouldFee // 应收减去退款应收
			}
			continue
		}

		// 正常收款单处理
		if billVO.ShouldFee != 0 {
			businessOverview.ShouldFee += billVO.ShouldFee // 应收
		}
		if billVO.TotalFee != 0 {
			businessOverview.TotalFee += billVO.TotalFee // 实收
		}
		if billVO.ZeroFee != 0 {
			businessOverview.ZeroFee += billVO.ZeroFee // 抹零金额
		}

		// 商家优惠 = 原始金额 - 应付金额
		if billVO.ShouldFee != 0 && billVO.ShouldFee != billVO.TotalFee {
			businessOverview.MerchantDiscount += billVO.ShouldFee - billVO.TotalFee + billVO.ZeroFee
		}

		// 会员优惠 = 原始金额 - 应付金额
		// 低消差额
		// 员工赠送
	}
}

func (service *ShiftReportService) fillBusinessDataCurrentDay(ctx *gin.Context, reqDto *req.QueryShiftReportGetIncomeDailyReqDto, currentDayOrders []po.Order, retVOs *[]vo.ShiftReportDaily, sessionIdToSessionMap map[string]po.Session, startHour string, nowTime int64) {
	// 判断retVOs是否有当天数据，填充到当天营业数据，没有构造一条今天的数据
	isMatch := false
	today := util.GetDateTimeStr()[:10]
	for idx, retVO := range *retVOs {
		if retVO.BillDate == today {
			service.fillBusinessOverviewCurrentDay(ctx, reqDto, currentDayOrders, &retVO.BusinessOverview)
			service.fillBusinessData(currentDayOrders, &retVO.BusinessData, sessionIdToSessionMap)
			retVO.BusinessData.BillCount += len(retVO.PayBillVOs)
			(*retVOs)[idx] = retVO
			isMatch = true
			break
		}
	}
	if !isMatch {

		// 如果不是当前营业日，则使用标准时间计算
		startTime := service.parseTimeWithLocation(today, startHour, 0)

		// 构造一条今天的数据
		newRetVO := vo.ShiftReportDaily{
			BillDate:         today,
			BusinessOverview: vo.BusinessOverview{},
			BusinessData:     vo.BusinessData{},
			StartTime:        startTime,
			EndTime:          nowTime,
			HandTime:         nowTime,
			EmployeeId:       *reqDto.EmployeeId,
			VenueId:          *reqDto.VenueId,
			VenueEndHour:     startHour,
		}
		service.fillBusinessOverviewCurrentDay(ctx, reqDto, currentDayOrders, &newRetVO.BusinessOverview)
		service.fillBusinessData(currentDayOrders, &newRetVO.BusinessData, sessionIdToSessionMap)
		*retVOs = append(*retVOs, newRetVO)
	}
}

func (service *ShiftReportService) fillBusinessOverviewCurrentDay(ctx *gin.Context, reqDto *req.QueryShiftReportGetIncomeDailyReqDto, currentDayOrders []po.Order, businessOverview *vo.BusinessOverview) {
	// 更新未付
	unpaidOrders := []po.Order{}
	unpaidOrderNos := []string{}
	for _, order := range currentDayOrders {
		if order.Status != nil && *order.Status == _const.V2_ORDER_STATUS_UNPAID {
			unpaidOrders = append(unpaidOrders, order)
			unpaidOrderNos = append(unpaidOrderNos, *order.OrderNo)
		}
	}
	if len(unpaidOrderNos) <= 0 {
		return
	}
	orderRoomPlans, _ := orderRoomPlanService.FindOrderRoomPlansByOrderNos(ctx, *reqDto.VenueId, unpaidOrderNos)
	orderProducts, _ := orderProductService.FindOrderProductsByOrderNos(ctx, *reqDto.VenueId, unpaidOrderNos)
	totalUnpaid := int64(0)
	for _, unpaidOrder := range unpaidOrders {
		multiplier := int64(1)
		if *unpaidOrder.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			multiplier = -1
		}
		if *unpaidOrder.Type == _const.V2_ORDER_TYPE_ROOMPLAN {
			for _, orderRoomPlan := range orderRoomPlans {
				if *orderRoomPlan.OrderNo == *unpaidOrder.OrderNo {
					totalUnpaid += *orderRoomPlan.PayAmount * multiplier
				}
			}
		}
		if *unpaidOrder.Type == _const.V2_ORDER_TYPE_PRODUCT {
			for _, orderProduct := range orderProducts {
				if *orderProduct.OrderNo == *unpaidOrder.OrderNo {
					totalUnpaid += *orderProduct.PayAmount * multiplier
				}
			}
		}
	}
	businessOverview.UnpaidFee = totalUnpaid
}

// fillBusinessData 填充营业数据
// orders: 当日订单列表
// businessData: 需要填充的营业数据对象
func (service *ShiftReportService) fillBusinessData(orders []po.Order, businessData *vo.BusinessData, sessionIdToSessionMap map[string]po.Session) {
	// 使用map来统计唯一的sessionId
	// sessionMap := make(map[string]bool)

	// 使用map来统计已支付的唯一orderNo
	paidOrderMap := make(map[string]bool)

	// 使用map来统计未支付的唯一orderNo
	unpaidOrderMap := make(map[string]bool)

	for _, order := range orders {
		// 统计开台数（通过sessionId去重）
		// if order.SessionId != nil && *order.SessionId != "" {
		// 	sessionMap[*order.SessionId] = true
		// }

		// 根据订单状态统计点单数和待结订单数
		if order.OrderNo != nil && *order.OrderNo != "" {
			if order.Status != nil {
				switch *order.Status {
				case _const.V2_ORDER_STATUS_PAID: // 已结订单
					paidOrderMap[*order.OrderNo] = true
				case _const.V2_ORDER_STATUS_UNPAID: // 未结订单
					unpaidOrderMap[*order.OrderNo] = true
				}
			}
		}
	}

	openCountPaid := 0
	openCountUnpaid := 0
	for _, session := range sessionIdToSessionMap {
		if session.PayStatus != nil && *session.PayStatus == _const.V2_SESSION_PAY_STATUS_PAID {
			openCountPaid++
		} else {
			openCountUnpaid++
		}
	}

	// 填充统计结果
	businessData.OpenCount = len(sessionIdToSessionMap) // 开台数
	businessData.OpenCountPaid = openCountPaid
	businessData.OpenCountUnpaid = openCountUnpaid
	businessData.OrderPaidCount = len(paidOrderMap)     // 点单数（已结算）
	businessData.OrderUnpaidCount = len(unpaidOrderMap) // 待结订单数
}

func (service *ShiftReportService) getOrderShiftReportVOs(payBillShiftReportVOs []vo.PayBillShiftReportVO) []vo.OrderShiftReportVO {
	orderShiftReportVOs := []vo.OrderShiftReportVO{}
	for _, payBillShiftReportVO := range payBillShiftReportVOs {
		for _, orderShiftReportVO := range payBillShiftReportVO.OrderShiftReportVOs {
			orderShiftReportVO.BillIsBack = payBillShiftReportVO.PayBillVO.IsBack
			orderShiftReportVOs = append(orderShiftReportVOs, orderShiftReportVO)
		}
	}

	return orderShiftReportVOs
}

// fillBusinessOverviewSub2 填充营业概况的子项数据（来自Session）
// sessions: 当日开台记录列表
// businessOverview: 需要填充的营业概况对象
func (service *ShiftReportService) fillBusinessOverviewSub2(payBillShiftReportVOs []vo.PayBillShiftReportVO, businessOverview *vo.BusinessOverview) {
	totalRoomFee := int64(0)
	totalProductFee := int64(0)
	totalUnpaid := int64(0)
	orderShiftReportVOs := service.getOrderShiftReportVOs(payBillShiftReportVOs)

	for _, orderShiftReportVO := range orderShiftReportVOs {
		orderVO := orderShiftReportVO.OrderVO
		orderType := orderVO.Type
		multiplier := int64(1)
		// 账单还原的可能还会引用到未付的订单，所以这里过滤掉未付的订单
		if orderVO.Status == _const.V2_ORDER_STATUS_UNPAID {
			continue
		}
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			multiplier = -1
		}
		// 正常订单
		if orderType == _const.V2_ORDER_TYPE_ROOMPLAN {
			// 房间订单
			totalRoomFee += orderShiftReportVO.TotalFee * multiplier
		} else if orderType == _const.V2_ORDER_TYPE_PRODUCT {
			// 商品订单
			totalProductFee += orderShiftReportVO.TotalFee * multiplier
		}
	}
	businessOverview.RoomFee = totalRoomFee
	businessOverview.ProductFee = totalProductFee
	businessOverview.UnpaidFee = totalUnpaid
}

// fillEmployeeGift 填充员工赠送数据
// payBillShiftReportVOs: 当日支付账单列表
// businessOverview: 需要填充的营业概况对象
func (service *ShiftReportService) fillEmployeeGift(giftOrderInfoVOs []vo.GiftOrderInfoVO, businessOverview *vo.BusinessOverview) {
	totalGiftFee := int64(0)
	for _, giftOrderInfoVO := range giftOrderInfoVOs {
		orderVO := giftOrderInfoVO.OrderVO
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			totalGiftFee -= giftOrderInfoVO.TotalFee
		} else {
			totalGiftFee += giftOrderInfoVO.TotalFee
		}
	}
	businessOverview.EmployeeGift = totalGiftFee
}

// fillPayTypeData 填充支付方式数据
// payBills: 当日支付账单列表
// payRecords: 当日支付记录列表
// payTypeData: 需要填充的支付方式数据对象
func (service *ShiftReportService) fillPayTypeData(payBillShiftReportVOs []vo.PayBillShiftReportVO) {
	if len(payBillShiftReportVOs) == 0 {
		return
	}
	payBills := []po.PayBill{}
	for _, payBillShiftReportVO := range payBillShiftReportVOs {
		payBills = append(payBills, payBillTransfer.VoToPo(payBillShiftReportVO.PayBillVO))
	}
	payRecords := []po.PayRecord{}
	for _, payBillShiftReportVO := range payBillShiftReportVOs {
		for _, payRecord := range payBillShiftReportVO.PayRecordVOs {
			payRecords = append(payRecords, payRecordTransfer.VoToPo(payRecord))
		}
	}
	// 创建billId到PayBill的映射，用于获取支付方向
	billMap := make(map[string]*po.PayBill)
	for i, bill := range payBills {
		if bill.BillId != nil {
			billMap[*bill.BillId] = &payBills[i]
		}
	}

	// 遍历支付记录
	for _, record := range payRecords {
		if record.BillId == nil || record.TotalFee == nil || record.PayType == nil {
			continue
		}

		// 获取关联的PayBill以确定支付方向
		// bill, exists := billMap[*record.BillId]
		// if !exists {
		// 	continue
		// }

		// 判断支付方向，退款需要减去金额
		// multiplier := int64(1)
		// if bill.Direction != nil && *bill.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
		// 	multiplier = -1
		// }

		// 根据支付类型累加金额
		// amount := *record.TotalFee * multiplier
		// switch *record.PayType {
		// case _const.PAY_TYPE_RECORD_CASH: // 现金
		// 	payTypeData.Cash += amount
		// case _const.PAY_TYPE_RECORD_BANK: // 银行卡
		// 	payTypeData.Bank += amount
		// case _const.PAY_TYPE_RECORD_WECHAT: // 微信
		// 	payTypeData.Wechat += amount
		// case _const.PAY_TYPE_RECORD_ALIPAY: // 支付宝
		// 	payTypeData.Alipay += amount
		// case _const.PAY_TYPE_LESHUA_BSHOWQR: // 乐刷付款码
		// 	payTypeData.Leshua += amount
		// 	// default:
		// 	// 	payTypeData.Other = 0
		// }
	}
}

// restoreDate 将日期拨回正常
// dailyReports: 日结单列表
// endHour: 结束时间，格式为HH:mm
// returns: 处理后的日结单列表
func (service *ShiftReportService) restoreDate(dailyReports []vo.ShiftReportDaily, endHour string) []vo.ShiftReportDaily {
	// 将日期拨回正常
	hourMin := strings.Split(endHour, ":")
	if len(hourMin) != 2 {
		return dailyReports
	}
	endHourInt, _ := strconv.Atoi(hourMin[0])
	endMinInt, _ := strconv.Atoi(hourMin[1])

	// 计算需要恢复的秒数
	addSeconds := endHourInt*3600 + endMinInt*60

	for i := range dailyReports {
		// 1. 处理PayBillShiftReportVOs中的PayBillVO.FinishTime
		for j := range dailyReports[i].PayBillShiftReportVOs {
			if dailyReports[i].PayBillShiftReportVOs[j].PayBillVO.FinishTime != 0 {
				// 将时间恢复endHour对应的秒数
				newFinishTime := dailyReports[i].PayBillShiftReportVOs[j].PayBillVO.FinishTime + int64(addSeconds)
				dailyReports[i].PayBillShiftReportVOs[j].PayBillVO.FinishTime = newFinishTime
			}
		}

		// 2. 同时处理PayBillVOs中的FinishTime
		for j := range dailyReports[i].PayBillVOs {
			if dailyReports[i].PayBillVOs[j].FinishTime != 0 {
				// 将时间恢复endHour对应的秒数
				newFinishTime := dailyReports[i].PayBillVOs[j].FinishTime + int64(addSeconds)
				dailyReports[i].PayBillVOs[j].FinishTime = newFinishTime
			}
		}
	}

	return dailyReports
}

// parseTimeWithLocation 将日期字符串和时间字符串转换为时间戳
// date: 日期字符串，格式为YYYY-MM-DD
// timeStr: 时间字符串，格式为HH:MM[:SS]
// addDays: 需要增加的天数，默认为0
// returns: 对应的时间戳
func (service *ShiftReportService) parseTimeWithLocation(date string, timeStr string, addDays int) int64 {
	// 获取本地时区
	loc, err := time.LoadLocation("Local")
	if err != nil {
		loc = time.FixedZone("CST", 8*3600) // 默认使用东八区
	}

	// 如果需要调整日期
	if addDays != 0 {
		// 解析原始日期
		t, err := time.ParseInLocation("2006-01-02", date, loc)
		if err == nil {
			// 加上指定的天数
			t = t.AddDate(0, 0, addDays)
			// 更新日期字符串
			date = t.Format("2006-01-02")
		}
	}

	// 根据时间字符串格式选择解析模板
	format := "2006-01-02 15:04"
	if strings.Count(timeStr, ":") == 2 {
		format = "2006-01-02 15:04:05"
	}

	// 解析时间
	t, err := time.ParseInLocation(format, date+" "+timeStr, loc)
	if err != nil {
		return 0
	}
	return t.Unix()
}

// 为了兼容现有代码，添加一个无addDays参数的重载函数
func (service *ShiftReportService) parseTimeWithLocationNoAddDays(date string, timeStr string) int64 {
	return service.parseTimeWithLocation(date, timeStr, 0)
}

// formatTimeWithLocation 将时间戳格式化为指定格式的日期字符串
// timestamp: 时间戳
// format: 日期格式，如"2006-01-02"
// returns: 格式化后的日期字符串
func (service *ShiftReportService) formatTimeWithLocation(timestamp int64, format string) string {
	// 获取本地时区
	loc, err := time.LoadLocation("Local")
	if err != nil {
		loc = time.FixedZone("CST", 8*3600) // 默认使用东八区
	}

	// 格式化时间
	return time.Unix(timestamp, 0).In(loc).Format(format)
}

// adjustDateTime 调整日期时间为实际营业时间
// dailyData: 当日数据对象
// endHour: 营业开始时间，格式为HH:mm
// startTimeFinal: 实际开始时间戳（可能为空）
// adjustDateTime 调整日期时间为实际营业时间
func (service *ShiftReportService) adjustDateTime(dailyData *vo.ShiftReportDaily, endHour string, startTimeFinal *int64, nowTime int64) {
	//   如果 startTimeFinal不为空，先将startTimeFinal-=endHour,
	//      然后判如果调整后的startTimeFinal的日期是今天，则 start_time = startTimeFinal end_time=now()
	//   如果 startTimeFinal为空
	// 		start_time = billDate + endHour 和 end_time = billDate + 1天 + endHour

	// 解析endHour获取需要增加的秒数
	hourMin := strings.Split(endHour, ":")
	if len(hourMin) != 2 {
		return
	}
	endHourInt, _ := strconv.Atoi(hourMin[0])
	endMinInt, _ := strconv.Atoi(hourMin[1])
	addSeconds := endHourInt*3600 + endMinInt*60
	// 获取当前数据的日期
	currentDate := dailyData.BillDate

	if startTimeFinal != nil && *startTimeFinal > 0 {
		// 存在交班记录
		// 先将startTimeFinal减去endHour对应的秒数
		adjustedStartTimeFinal := *startTimeFinal - int64(addSeconds)

		// 获取调整后的startTimeFinal日期
		adjustedFinalDate := service.formatTimeWithLocation(adjustedStartTimeFinal, "2006-01-02")

		// 如果调整后的startTimeFinal日期与当前数据日期相同，则设置特殊时间
		if currentDate == adjustedFinalDate {
			// 如果当前日期是今天，则使用标准时间计算
			dailyData.StartTime = *startTimeFinal
			dailyData.EndTime = nowTime
		} else {
			// 如果不是当前营业日，则使用标准时间计算
			dailyData.StartTime = service.parseTimeWithLocation(dailyData.BillDate, endHour, 0)

			// 结束时间设置为下一天的endHour
			dailyData.EndTime = service.parseTimeWithLocation(dailyData.BillDate, endHour, 1)
		}
	} else {
		// 从未交过班
		adjustedNowTime := nowTime - int64(addSeconds)
		adjustedNowDate := service.formatTimeWithLocation(adjustedNowTime, "2006-01-02")
		if currentDate == adjustedNowDate {
			// 如果当前日期是今天，则使用标准时间计算
			dailyData.StartTime = service.parseTimeWithLocation(dailyData.BillDate, endHour, 0)
			dailyData.EndTime = nowTime
		} else {
			// startTimeFinal为空的情况
			dailyData.StartTime = service.parseTimeWithLocation(dailyData.BillDate, endHour, 0)

			// 结束时间设置为下一天的endHour
			dailyData.EndTime = service.parseTimeWithLocation(dailyData.BillDate, endHour, 1)
		}
	}
}

// adjustDateTime 调整日期时间为实际营业时间
// dailyData: 当日数据对象
// endHour: 营业开始时间，格式为HH:mm
// startTimeFinal: 实际开始时间戳（可能为空）
// adjustDateTime 调整日期时间为实际营业时间
func (service *ShiftReportService) adjustDateTimeNew(dailyData *vo.ShiftReportDaily, startHour string, startTimeFinal *int64, nowTime int64) {
	//   如果 startTimeFinal不为空，先将startTimeFinal-=endHour,
	//      然后判如果调整后的startTimeFinal的日期是今天，则 start_time = startTimeFinal end_time=now()
	//   如果 startTimeFinal为空
	// 		start_time = billDate + endHour 和 end_time = billDate + 1天 + endHour

	// 解析endHour获取需要增加的秒数
	hourMin := strings.Split(startHour, ":")
	if len(hourMin) != 2 {
		return
	}
	endHourInt, _ := strconv.Atoi(hourMin[0])
	endMinInt, _ := strconv.Atoi(hourMin[1])
	addSeconds := endHourInt*3600 + endMinInt*60
	// 获取当前数据的日期
	billDate := dailyData.BillDate

	if startTimeFinal != nil && *startTimeFinal > 0 {
		// 存在交班记录

		// 获取调整后的startTimeFinal日期
		lastHandBelongFinalDate := service.formatTimeWithLocation(*startTimeFinal-int64(addSeconds), "2006-01-02")

		// 如果调整后的startTimeFinal日期与当前数据日期相同，则设置特殊时间
		if billDate == lastHandBelongFinalDate {
			// 如果当前日期是今天，则使用标准时间计算
			dailyData.StartTime = *startTimeFinal
		} else {
			// 如果不是当前营业日，则使用标准时间计算
			dailyData.StartTime = service.parseTimeWithLocation(billDate, startHour, 0)
		}

		// 当前时间属于今天，则设置结束时间为当前时间
		nowTimeBelongFinalDate := service.formatTimeWithLocation(nowTime-int64(addSeconds), "2006-01-02")
		if nowTimeBelongFinalDate == billDate {
			dailyData.EndTime = nowTime
		} else {
			dailyData.EndTime = service.parseTimeWithLocation(billDate, startHour, 1)
		}
	} else {
		// 从未交过班
		// 获取调整后的startTimeFinal日期
		dailyData.StartTime = service.parseTimeWithLocation(billDate, startHour, 0)

		// 当前时间属于今天，则设置结束时间为当前时间
		nowTimeBelongFinalDate := service.formatTimeWithLocation(nowTime-int64(addSeconds), "2006-01-02")
		if nowTimeBelongFinalDate == billDate {
			dailyData.EndTime = nowTime
		} else {
			dailyData.EndTime = service.parseTimeWithLocation(billDate, startHour, 1)
		}
	}
}

// BuildShiftReportDailyVO 构建日结单
// 0.0 endHour 一定是 HH:mm 格式
// 由于营业日是 endHour，06:00 代表 06:00 - 06:00，
// 我希望将时钟回拨解决跨天的问题，然后用日期分组，
// 最后再把日期拨回正常
//
//	paybills.FinishTime - endHour
//	orders.Ctime - endHour
//	sessions.Ctime - endHour // 使用Ctime进行分组
//
// 1 将payBills、orders、sessions 的分组时间字段 + endHour 做预处理
// 2 将payBills、orders、sessions 按处理后的日期分组
// 3. 填充营业概况 (使用分组后的PayBills)
// 3.1 填充营业概况的RoomFee=sum(sessions.RoomFee)、ProductFee=sum(sessions.SupermarketFee)、Unpaid=sum(sessions.UnpaidAmount) (使用分组后的Sessions)
// 4. 填充营业数据 (使用分组后的Orders)
// 5. 填充支付方式数据 (使用分组后的PayBills和传入的PayRecords)
// 6. 调整日期时间为实际营业时间 (设置StartTime, EndTime)
// 7. 将日期拨回正常 (恢复分组用的时间字段)
func (service *ShiftReportService) BuildShiftReportDailyVO(ctx *gin.Context, payBillShiftReportVOs []vo.PayBillShiftReportVO, giftOrders []vo.GiftOrderInfoVO, currentDayOrders []po.Order, endHour string, startTimeFinal *int64, reqDto req.QueryShiftReportGetIncomeDailyReqDto, rooms *[]po.Room, memberCardOperationsRecharge []po.MemberCardOperation, memberCardConsumes []po.MemberCardConsume, sessionIdToSessionMap map[string]po.Session) []vo.ShiftReportDaily {
	retVOs := []vo.ShiftReportDaily{}
	roomIdMap := make(map[string]po.Room)
	for _, room := range *rooms {
		roomIdMap[*room.Id] = room
	}
	// 1. 将 payBillShiftReportVOs[i].payBill.FinishTime + endHour 做预处理 (处理用于分组的时间字段)
	processedPayBillShiftReportVOs, processedGiftOrders, processedMemberCardOperationsRecharge, processedMemberCardConsumes := service.preprocessData(payBillShiftReportVOs, giftOrders, memberCardOperationsRecharge, memberCardConsumes, endHour)

	// 2. 将处理后的payBills、orders、sessions 按日期分组
	dailyGroups := service.groupDataByDate(processedPayBillShiftReportVOs, processedGiftOrders, processedMemberCardOperationsRecharge, processedMemberCardConsumes)
	nowTime := int64(util.TimeNowUnix())
	// 遍历每一天的数据
	for date, dailyData := range dailyGroups {
		dailyData.BillDate = date
		dailyData.VenueEndHour = endHour
		dailyData.VenueId = *reqDto.VenueId
		dailyData.EmployeeId = *reqDto.EmployeeId
		dailyData.HandTime = nowTime
		// 3. 填充营业概况 (使用分组后的PayBills)
		service.fillBusinessOverview(dailyData.PayBillShiftReportVOs, &dailyData.BusinessOverview)

		// 3.0 处理员工赠送
		service.fillEmployeeGift(dailyData.GiftOrderInfoVOs, &dailyData.BusinessOverview)

		// 3.1 填充营业概况的RoomFee=sum(sessions.RoomFee)、ProductFee=sum(sessions.SupermarketFee)、Unpaid=sum(sessions.UnpaidAmount) (使用分组后的Sessions)
		service.fillBusinessOverviewSub2(dailyData.PayBillShiftReportVOs, &dailyData.BusinessOverview)

		// 4. 填充营业数据 (使用分组后的Orders)
		// service.fillBusinessData(currentDayOrders, &dailyData.BusinessData)

		// 5. 填充支付方式数据 (使用分组后的PayBills和传入的PayRecords)
		// service.fillPayTypeData(dailyData.PayBillShiftReportVOs, &dailyData.PayTypeData)

		// 6. 填充会员卡消费数据 (使用分组后的MemberCardConsumes)
		service.fillMemberCardConsumeData(dailyData.PayMemberCardConsumes, &dailyData.MemberCardPayData)

		// 7. 填充会员卡充值数据 (使用分组后的MemberCardOperationsRecharge)
		service.fillMemberCardOperationData(dailyData.RechargeMemberCardOperations, &dailyData.MemberCardRechargeData)

		// 计算净收
		dailyData.BusinessOverview.NetFee = dailyData.BusinessOverview.TotalFee - (dailyData.MemberCardPayData.CommonBonusAmount + dailyData.MemberCardPayData.GoodsBonusAmount + dailyData.MemberCardPayData.RoomBonusAmount + dailyData.MemberCardPayData.PrincipalAmount)

		// 6. 调整日期时间为实际营业时间 (设置StartTime, EndTime)
		service.adjustDateTimeNew(dailyData, endHour, startTimeFinal, nowTime)

		// 清理不再需要原始分组数据，准备填充VO
		// dailyData.Orders = nil   // 保留Orders用于可能的后续处理或展示
		// dailyData.Sessions = nil // 保留Sessions用于可能的后续处理或展示

		newPayBillVOs := []vo.PayBillVO{}
		for _, payBillShiftReportVO := range dailyData.PayBillShiftReportVOs { // 使用分组后的PayBills
			billVO := payBillShiftReportVO.PayBillVO
			val, exists := roomIdMap[billVO.RoomId]
			if exists {
				billVO.RoomName = *val.Name
			}
			newPayBillVOs = append(newPayBillVOs, billVO)
		}
		dailyData.PayBillVOs = newPayBillVOs
		retVOs = append(retVOs, *dailyData)
	}

	// 4. 填充营业数据 (使用分组后的Orders)
	// 判断dailyGroups是否有当天数据，填充到当天营业数据，没有构造一条今天的数据
	// service.fillBusinessData(currentDayOrders, &dailyData.BusinessData)
	service.fillBusinessDataCurrentDay(ctx, &reqDto, currentDayOrders, &retVOs, sessionIdToSessionMap, endHour, nowTime)
	for idx, retVO := range retVOs {
		retVO.BusinessData.BillCount = len(retVO.PayBillVOs)
		retVOs[idx] = retVO
	}

	// 7. 将日期拨回正常 (恢复分组用的时间字段)
	retVOs = service.restoreDate(retVOs, endHour)
	// 使用sort包进行排序
	sort.Slice(retVOs, func(i, j int) bool {
		// 按照BillDate正序排列
		return retVOs[i].BillDate < retVOs[j].BillDate
	})
	return retVOs
}

// convertToShiftHandoverForms 将日结单数据转换为交接单数据
// shiftReportDailys: 日结单数据列表
// returns: 交接单数据列表
func (service *ShiftReportService) convertToShiftHandoverForms(ctx *gin.Context, shiftReportDailys []vo.ShiftReportDaily) ([]po.ShiftHandoverForm, []po.ShiftHandoverFormAndPayBill) {
	handoverForms := make([]po.ShiftHandoverForm, 0)
	handoverFormAndPayBills := make([]po.ShiftHandoverFormAndPayBill, 0)

	for _, daily := range shiftReportDailys {
		handNo := util.GetHandNo(daily.VenueId)
		payTypeData := daily.PayTypeData
		payTypeInfo := "{}"
		if payTypeData != nil {
			jsonData, err := json.Marshal(payTypeData)
			if err == nil {
				payTypeInfo = string(jsonData)
			}
		}
		form := po.ShiftHandoverForm{
			VenueId:    &daily.VenueId,
			EmployeeId: &daily.EmployeeId,
			HandTime:   &daily.HandTime,
			HandNo:     &handNo,

			BillDate:     &daily.BillDate,
			VenueEndHour: &daily.VenueEndHour,
			StartTime:    &daily.StartTime,
			EndTime:      &daily.EndTime,

			// 营业概况数据
			ShouldFee:         &daily.BusinessOverview.ShouldFee,
			TotalFee:          &daily.BusinessOverview.TotalFee,
			NetFee:            &daily.BusinessOverview.NetFee,
			RoomFee:           &daily.BusinessOverview.RoomFee,
			ProductFee:        &daily.BusinessOverview.ProductFee,
			MerchantDiscount:  &daily.BusinessOverview.MerchantDiscount,
			MemberDiscount:    &daily.BusinessOverview.MemberDiscount,
			LowConsumptionFee: &daily.BusinessOverview.LowConsumptionFee,
			EmployeeGift:      &daily.BusinessOverview.EmployeeGift,
			ZeroFee:           &daily.BusinessOverview.ZeroFee,
			UnpaidFee:         &daily.BusinessOverview.UnpaidFee,

			// 营业数据
			BillCount:        &daily.BusinessData.BillCount,
			OpenCount:        &daily.BusinessData.OpenCount,
			OpenCountPaid:    &daily.BusinessData.OpenCountPaid,
			OpenCountUnpaid:  &daily.BusinessData.OpenCountUnpaid,
			OrderPaidCount:   &daily.BusinessData.OrderPaidCount,
			OrderUnpaidCount: &daily.BusinessData.OrderUnpaidCount,

			PayTypeInfo: &payTypeInfo,
			// 支付方式数据
			// Cash:    &daily.PayTypeData.Cash,
			// Bank:    &daily.PayTypeData.Bank,
			// Wechat:  &daily.PayTypeData.Wechat,
			// Alipay:  &daily.PayTypeData.Alipay,
			// Meituan: &daily.PayTypeData.Meituan,
			// Koubei:  &daily.PayTypeData.Koubei,
			// Ticket:  &daily.PayTypeData.Ticket,
			// Leshua:  &daily.PayTypeData.Leshua,
			// Other:   &daily.PayTypeData.Other,

			PrincipalAmount:   &daily.MemberCardPayData.PrincipalAmount,
			RoomBonusAmount:   &daily.MemberCardPayData.RoomBonusAmount,
			GoodsBonusAmount:  &daily.MemberCardPayData.GoodsBonusAmount,
			CommonBonusAmount: &daily.MemberCardPayData.CommonBonusAmount,
			RechargeAmount:            &daily.MemberCardRechargeData.RechargeAmount,
			RechargePrincipalAmount:   &daily.MemberCardRechargeData.RechargePrincipalAmount,
			RechargeRoomBonusAmount:   &daily.MemberCardRechargeData.RechargeRoomBonusAmount,
			RechargeGoodsBonusAmount:  &daily.MemberCardRechargeData.RechargeGoodsBonusAmount,
			RechargeCommonBonusAmount: &daily.MemberCardRechargeData.RechargeCommonBonusAmount,

			// 基础字段
			Operator: &daily.EmployeeId,
		}
		handoverForms = append(handoverForms, form)

		for _, payBill := range daily.PayBillVOs {
			billId := payBill.BillId
			handoverFormAndPayBill := po.ShiftHandoverFormAndPayBill{
				VenueId: &daily.VenueId,
				HandNo:  &handNo,
				BillId:  &billId,
			}
			handoverFormAndPayBills = append(handoverFormAndPayBills, handoverFormAndPayBill)
		}
	}

	return handoverForms, handoverFormAndPayBills
}

func (service *ShiftReportService) SaveShiftHandoverForm(ctx *gin.Context, reqDto req.V3ShiftReportHandOverReqDto) ([]po.ShiftHandoverForm, []po.ShiftHandoverFormAndPayBill, error) {
	shiftReportDailysReq := reqDto.ShiftReportDaily
	// 1. 将 shiftReportDailysReq 保存为 ShiftHandoverForm 数组
	newShiftHandoverForms, newShiftHandoverFormAndPayBills := service.convertToShiftHandoverForms(ctx, shiftReportDailysReq)

	// 3. ShiftHandoverForm
	err := service.SaveBatchShiftHandoverForm(ctx, newShiftHandoverForms, newShiftHandoverFormAndPayBills)
	if err != nil {
		return nil, nil, err
	}
	return newShiftHandoverForms, newShiftHandoverFormAndPayBills, nil
}

func (service *ShiftReportService) SaveBatchShiftHandoverForm(ctx *gin.Context, pos []po.ShiftHandoverForm, ms []po.ShiftHandoverFormAndPayBill) error {
	if len(pos) == 0 {
		return nil
	}
	// 1. 保存 ShiftHandoverForm 数组到数据库
	tx := model.DBMaster.Self.Begin()
	for _, shiftHandoverForm := range pos {
		if err := shiftHandoverFormService.CreateShiftHandoverFormWithTx(ctx, &shiftHandoverForm, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	// 2. 保存 ShiftHandoverFormAndPayBill 数组到数据库
	for _, shiftHandoverFormAndPayBill := range ms {
		if err := shiftHandoverFormAndPayBillService.CreateShiftHandoverFormAndPayBillWithTx(ctx, &shiftHandoverFormAndPayBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (service *ShiftReportService) GetV3QueryShiftReportHandOverDetail(ctx *gin.Context, reqDto req.V3QueryShiftReportHandOverDetailReqDto) ([]vo.ShiftReportDaily, error) {
	handNo := *reqDto.HandNo
	shiftHandoverForms, err := shiftHandoverFormService.FindAllShiftHandoverForm(ctx, &req.QueryShiftHandoverFormReqDto{
		HandNo: &handNo,
	})
	if err != nil {
		return nil, err
	}
	if len(*shiftHandoverForms) <= 0 {
		return nil, errors.New("交班记录不存在")
	}
	shiftHandoverForm := (*shiftHandoverForms)[0]
	shiftReportDaily := vo.ShiftReportDaily{}
	shiftReportDaily.BillDate = service.formatTimeWithLocation(*shiftHandoverForm.HandTime, "2006-01-02")
	shiftReportDaily.VenueId = *shiftHandoverForm.VenueId
	shiftReportDaily.EmployeeId = *shiftHandoverForm.EmployeeId
	shiftReportDaily.HandTime = *shiftHandoverForm.HandTime
	shiftReportDaily.BusinessOverview = vo.BusinessOverview{
		ShouldFee:         *shiftHandoverForm.ShouldFee,
		TotalFee:          *shiftHandoverForm.TotalFee,
		NetFee:            *shiftHandoverForm.NetFee,
		RoomFee:           *shiftHandoverForm.RoomFee,
		ProductFee:        *shiftHandoverForm.ProductFee,
		MerchantDiscount:  *shiftHandoverForm.MerchantDiscount,
		MemberDiscount:    *shiftHandoverForm.MemberDiscount,
		LowConsumptionFee: *shiftHandoverForm.LowConsumptionFee,
		EmployeeGift:      *shiftHandoverForm.EmployeeGift,
		ZeroFee:           *shiftHandoverForm.ZeroFee,
		UnpaidFee:         *shiftHandoverForm.UnpaidFee,
	}
	shiftReportDaily.BusinessData = vo.BusinessData{
		OpenCount:        *shiftHandoverForm.OpenCount,
		OrderPaidCount:   *shiftHandoverForm.OrderPaidCount,
		OrderUnpaidCount: *shiftHandoverForm.OrderUnpaidCount,
		OpenCountPaid:    *shiftHandoverForm.OpenCountPaid,
		OpenCountUnpaid:  *shiftHandoverForm.OpenCountUnpaid,
		BillCount:        util.GetPtrSafeDefault(shiftHandoverForm.BillCount, 0),
	}
	payTypeData := make(map[string]int64)
	err = json.Unmarshal([]byte(*shiftHandoverForm.PayTypeInfo), &payTypeData)
	if err != nil {
		payTypeData = make(map[string]int64)
	}
	shiftReportDaily.PayTypeData = payTypeData
	shiftReportDaily.MemberCardPayData = vo.MemberCardPayData{
		PrincipalAmount:   *shiftHandoverForm.PrincipalAmount,
		RoomBonusAmount:   *shiftHandoverForm.RoomBonusAmount,
		GoodsBonusAmount:  *shiftHandoverForm.GoodsBonusAmount,
		CommonBonusAmount: *shiftHandoverForm.CommonBonusAmount,
	}
	shiftReportDaily.MemberCardRechargeData = vo.MemberCardRechargeData{
		RechargeAmount:            *shiftHandoverForm.RechargeAmount,
		RechargePrincipalAmount:   *shiftHandoverForm.RechargePrincipalAmount,
		RechargeRoomBonusAmount:   *shiftHandoverForm.RechargeRoomBonusAmount,
		RechargeGoodsBonusAmount:  *shiftHandoverForm.RechargeGoodsBonusAmount,
		RechargeCommonBonusAmount: *shiftHandoverForm.RechargeCommonBonusAmount,
	}
	shiftReportDaily.PayBillVOs = []vo.PayBillVO{}
	shiftHandoverFormAndPayBills, err := shiftHandoverFormAndPayBillService.FindAllShiftHandoverFormAndPayBill(ctx, &req.QueryShiftHandoverFormAndPayBillReqDto{
		HandNo: &handNo,
	})
	if err != nil {
		return nil, err
	}
	if len(*shiftHandoverFormAndPayBills) > 0 {
		BillIds := make([]string, 0)
		for _, shiftHandoverFormAndPayBill := range *shiftHandoverFormAndPayBills {
			BillIds = append(BillIds, *shiftHandoverFormAndPayBill.BillId)
		}
		payBills, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
			BillIds: &BillIds,
		})
		if err != nil {
			return nil, err
		}
		roomIds := []string{}
		for _, payBill := range *payBills {
			roomIds = append(roomIds, *payBill.RoomId)
		}
		rooms, _ := roomService.FindAllRoom(ctx, &req.QueryRoomReqDto{
			Ids: &roomIds,
		})
		roomIdMap := make(map[string]po.Room)
		for _, room := range *rooms {
			roomIdMap[*room.Id] = room
		}
		for _, payBill := range *payBills {
			payBillVO := payBillTransfer.PoToVo(payBill)
			if room, ok := roomIdMap[*payBill.RoomId]; ok && room.Name != nil {
				payBillVO.RoomName = *room.Name
			}
			shiftReportDaily.PayBillVOs = append(shiftReportDaily.PayBillVOs, payBillVO)
		}
	}
	return []vo.ShiftReportDaily{shiftReportDaily}, nil
}
func (service *ShiftReportService) BuildOrderShiftReportVO(ctx *gin.Context, orders []po.Order, orderAndPays []po.OrderAndPay, orderRoomPlans []po.OrderRoomPlan, orderProducts []po.OrderProduct) []vo.OrderShiftReportVO {
	orderShiftReportVOs := []vo.OrderShiftReportVO{}
	for _, order := range orders {
		orderShiftReportVO := vo.OrderShiftReportVO{}
		orderShiftReportVO.OrderVO = orderTransfer.PoToVo(order)

		totalFee := int64(0)
		for _, orderProduct := range orderProducts {
			if *orderProduct.OrderNo == *order.OrderNo {
				orderShiftReportVO.OrderProductVOs = append(orderShiftReportVO.OrderProductVOs, orderProductTransfer.PoToVo(orderProduct))
				totalFee += *orderProduct.PayAmount
			}
		}
		for _, orderRoomPlan := range orderRoomPlans {
			if *orderRoomPlan.OrderNo == *order.OrderNo {
				orderShiftReportVO.OrderRoomPlanVOs = append(orderShiftReportVO.OrderRoomPlanVOs, orderRoomPlanTransfer.PoToVo(orderRoomPlan))
				totalFee += *orderRoomPlan.PayAmount
			}
		}
		orderShiftReportVO.TotalFee = totalFee
		orderShiftReportVOs = append(orderShiftReportVOs, orderShiftReportVO)
	}
	return orderShiftReportVOs
}

func (service *ShiftReportService) BuildPayBillShiftReportVO(ctx *gin.Context, payBills []po.PayBill, payRecords []po.PayRecord, orderAndPays []po.OrderAndPay, orders []po.Order, orderRoomPlans []po.OrderRoomPlan, orderProducts []po.OrderProduct) []vo.PayBillShiftReportVO {
	payBillShiftReportVOs := []vo.PayBillShiftReportVO{}
	for _, payBill := range payBills {
		payBillShiftReportVO := vo.PayBillShiftReportVO{}
		for _, payRecord := range payRecords {
			if *payRecord.BillId == *payBill.BillId {
				payBillShiftReportVO.PayRecordVOs = append(payBillShiftReportVO.PayRecordVOs, payRecordTransfer.PoToVo(payRecord))
			}
		}
		tmpOrderNos := make([]string, 0)
		for _, orderAndPay := range orderAndPays {
			if *orderAndPay.BillId == *payBill.BillId {
				payBillShiftReportVO.OrderAndPayVOs = append(payBillShiftReportVO.OrderAndPayVOs, orderAndPayTransfer.PoToVo(orderAndPay))
				tmpOrderNos = append(tmpOrderNos, *orderAndPay.OrderNo)
			}
		}
		for _, order := range orders {
			orderShiftReportVO := vo.OrderShiftReportVO{}
			if !util.InList(*order.OrderNo, tmpOrderNos) {
				continue
			}
			orderShiftReportVO.OrderVO = orderTransfer.PoToVo(order)
			totalFee := int64(0)
			for _, orderProduct := range orderProducts {
				if *orderProduct.OrderNo == *order.OrderNo {
					orderShiftReportVO.OrderProductVOs = append(orderShiftReportVO.OrderProductVOs, orderProductTransfer.PoToVo(orderProduct))
					totalFee += *orderProduct.PayAmount
				}
			}
			for _, orderRoomPlan := range orderRoomPlans {
				if *orderRoomPlan.OrderNo == *order.OrderNo {
					orderShiftReportVO.OrderRoomPlanVOs = append(orderShiftReportVO.OrderRoomPlanVOs, orderRoomPlanTransfer.PoToVo(orderRoomPlan))
					totalFee += *orderRoomPlan.PayAmount
				}
			}
			orderShiftReportVO.TotalFee = totalFee
			payBillShiftReportVO.OrderShiftReportVOs = append(payBillShiftReportVO.OrderShiftReportVOs, orderShiftReportVO)
		}
		payBillShiftReportVO.PayBillVO = payBillTransfer.PoToVo(payBill)
		payBillShiftReportVOs = append(payBillShiftReportVOs, payBillShiftReportVO)
	}
	return payBillShiftReportVOs
}

func (service *ShiftReportService) FindGiftOrderInfos(ctx *gin.Context, venueId string, employeeId string, startTimeFinal *int64) (list []vo.GiftOrderInfoVO, err error) {
	orderRoomPlans, err := orderRoomPlanService.FindOrderRoomPlansGift(ctx, venueId, employeeId, startTimeFinal)
	if err != nil {
		return
	}
	orderProducts, err := orderProductService.FindOrderProductsGift(ctx, venueId, employeeId, startTimeFinal)
	if err != nil {
		return
	}
	orderNos := make([]string, 0)
	for _, orderRoomPlan := range orderRoomPlans {
		orderNos = append(orderNos, *orderRoomPlan.OrderNo)
	}
	for _, orderProduct := range orderProducts {
		orderNos = append(orderNos, *orderProduct.OrderNo)
	}
	orders, err := orderService.FindOrdersByOrderNos(ctx, venueId, orderNos)
	if err != nil {
		return
	}
	giftOrderInfoVOs := []vo.GiftOrderInfoVO{}
	for _, order := range orders {
		giftOrderInfoVO := vo.GiftOrderInfoVO{}
		giftOrderInfoVO.OrderVO = orderTransfer.PoToVo(order)
		totalFee := int64(0)
		if *order.Type == _const.V2_ORDER_TYPE_PRODUCT {
			for _, orderProduct := range orderProducts {
				if *orderProduct.OrderNo == *order.OrderNo {
					giftOrderInfoVO.OrderProductVOs = append(giftOrderInfoVO.OrderProductVOs, orderProductTransfer.PoToVo(orderProduct))
					totalFee += *orderProduct.OriginalPrice * *orderProduct.Quantity
				}
			}
		} else if *order.Type == _const.V2_ORDER_TYPE_ROOMPLAN {
			for _, orderRoomPlan := range orderRoomPlans {
				if *orderRoomPlan.OrderNo == *order.OrderNo {
					giftOrderInfoVO.OrderRoomPlanVOs = append(giftOrderInfoVO.OrderRoomPlanVOs, orderRoomPlanTransfer.PoToVo(orderRoomPlan))
					totalFee += *orderRoomPlan.OriginalPayAmount
				}
			}
		}
		giftOrderInfoVO.TotalFee = totalFee
		giftOrderInfoVOs = append(giftOrderInfoVOs, giftOrderInfoVO)
	}

	return giftOrderInfoVOs, nil
}

func (service *ShiftReportService) GetV3QueryShiftReportBillDetail(ctx *gin.Context, reqDto *req.QueryShiftReportBillDetailReqDto) (vo.ShiftReportBillDetailVO, error) {
	shiftReportBillDetailVO := vo.ShiftReportBillDetailVO{}
	shiftReportBillDetailVO.EmployeeId = *reqDto.EmployeeId
	shiftReportBillDetailVO.VenueId = *reqDto.VenueId
	shiftReportBillDetailVO.BillId = *reqDto.BillId

	// 1. 获取支付单
	payBills, err := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{
		BillId: reqDto.BillId,
	})
	if err != nil {
		return vo.ShiftReportBillDetailVO{}, err
	}
	payBillVO := (*payBills)[0]
	shiftReportBillDetailVO.PayBillVO = payBillTransfer.PoToVo(payBillVO)

	// 2. 获取支付记录
	payRecords, err := payRecordService.FindAllPayRecord(ctx, &req.QueryPayRecordReqDto{
		BillId: reqDto.BillId,
	})
	if err != nil {
		return vo.ShiftReportBillDetailVO{}, err
	}
	for _, payRecord := range *payRecords {
		shiftReportBillDetailVO.PayRecordVOs = append(shiftReportBillDetailVO.PayRecordVOs, payRecordTransfer.PoToVo(payRecord))
	}

	// 3. 获取支付中间表
	orderAndPays, err := orderAndPayService.FindAllOrderAndPay(ctx, &req.QueryOrderAndPayReqDto{
		BillId: reqDto.BillId,
	})
	if err != nil {
		return vo.ShiftReportBillDetailVO{}, err
	}
	orderNos := make([]string, 0)
	for _, orderAndPay := range *orderAndPays {
		orderNos = append(orderNos, *orderAndPay.OrderNo)
		shiftReportBillDetailVO.PayBillVO.OrderAndPayVOs = append(shiftReportBillDetailVO.PayBillVO.OrderAndPayVOs, orderAndPayTransfer.PoToVo(orderAndPay))
	}
	// 4. 获取订单房间计划
	orderRoomPlans, err := orderRoomPlanService.FindOrderRoomPlansByOrderNos(ctx, *reqDto.VenueId, orderNos)
	if err != nil {
		return vo.ShiftReportBillDetailVO{}, err
	}
	for _, orderRoomPlan := range orderRoomPlans {
		shiftReportBillDetailVO.OrderRoomPlanVOs = append(shiftReportBillDetailVO.OrderRoomPlanVOs, orderRoomPlanTransfer.PoToVo(orderRoomPlan))
	}
	// 5. 获取订单产品
	orderProducts, err := orderProductService.FindOrderProductsByOrderNos(ctx, *reqDto.VenueId, orderNos)
	if err != nil {
		return vo.ShiftReportBillDetailVO{}, err
	}
	for _, orderProduct := range orderProducts {
		shiftReportBillDetailVO.OrderProductVOs = append(shiftReportBillDetailVO.OrderProductVOs, orderProductTransfer.PoToVo(orderProduct))
	}

	service.fillBusinessOverviewSingle(shiftReportBillDetailVO, &shiftReportBillDetailVO.BusinessOverview)
	// service.fillPayTypeDataSingle(shiftReportBillDetailVO, &shiftReportBillDetailVO.PayTypeData)
	return shiftReportBillDetailVO, nil
}

func (service *ShiftReportService) fillBusinessOverviewSingle(shiftReportBillDetailVO vo.ShiftReportBillDetailVO, businessOverview *vo.BusinessOverview) {
	// 填充营业概况
	billVO := shiftReportBillDetailVO.PayBillVO
	if billVO.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
		// 退款单处理
		if billVO.TotalFee != 0 {
			businessOverview.TotalFee -= billVO.TotalFee // 实收减去退款
		}
		if billVO.ShouldFee != 0 {
			businessOverview.ShouldFee -= billVO.ShouldFee // 应收减去退款应收
		}
	}

	// 正常收款单处理
	if billVO.ShouldFee != 0 {
		businessOverview.ShouldFee += billVO.ShouldFee // 应收
	}
	if billVO.TotalFee != 0 {
		businessOverview.TotalFee += billVO.TotalFee // 实收
	}
	if billVO.ZeroFee != 0 {
		businessOverview.ZeroFee += billVO.ZeroFee // 抹零金额
	}

	// 商家优惠 = 原始金额 - 应付金额
	if billVO.OriginalFee != 0 && billVO.ShouldFee != 0 && billVO.OriginalFee > billVO.ShouldFee {
		businessOverview.MerchantDiscount += billVO.OriginalFee - billVO.ShouldFee
	}

	// 会员优惠 = 原始金额 - 应付金额
	// 低消差额
	// 员工赠送

}

func (service *ShiftReportService) fillPayTypeDataSingle(shiftReportBillDetailVO vo.ShiftReportBillDetailVO) {

	payRecords := shiftReportBillDetailVO.PayRecordVOs
	// payBill := shiftReportBillDetailVO.PayBillVO
	// 遍历支付记录
	for _, record := range payRecords {
		if record.BillId == "" || record.PayType == "" {
			continue
		}

		// 判断支付方向，退款需要减去金额
		// multiplier := int64(1)
		// if payBill.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
		// 	multiplier = -1
		// }

		// // 根据支付类型累加金额
		// amount := record.TotalFee * multiplier
		// switch record.PayType {
		// case _const.PAY_TYPE_RECORD_CASH: // 现金
		// 	payTypeData.Cash += amount
		// case _const.PAY_TYPE_RECORD_BANK: // 银行卡
		// 	payTypeData.Bank += amount
		// case _const.PAY_TYPE_RECORD_WECHAT: // 微信
		// 	payTypeData.Wechat += amount
		// case _const.PAY_TYPE_RECORD_ALIPAY: // 支付宝
		// 	payTypeData.Alipay += amount
		// case _const.PAY_TYPE_LESHUA_BSHOWQR: // 乐刷付款码
		// 	// 根据实际情况可能需要区分具体支付方式
		// 	payTypeData.Other += amount
		// case _const.PAY_TYPE_MEMBER_CARD: // 会员卡
		// 	// 会员卡支付可能需要单独统计
		// 	payTypeData.Other += amount
		// default:
		// 	payTypeData.Other += amount
		// }
	}
}

// fillMemberCardConsumeData 填充会员卡消费数据
// payBillShiftReportVOs: 支付账单列表
// memberCardData: 需要填充的会员卡数据对象
func (service *ShiftReportService) fillMemberCardConsumeData(memberCardConsumes []po.MemberCardConsume, memberCardData *vo.MemberCardPayData) {
	for _, memberCardConsume := range memberCardConsumes {
		// 判断支付方向，退款需要减去金额
		multiplier := int64(1)
		if *memberCardConsume.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			multiplier = -1
		}

		// 根据支付类型累加金额
		// 本金
		memberCardData.PrincipalAmount += *memberCardConsume.PayRecordPrincipalAmount * multiplier
		// 房费赠金
		memberCardData.RoomBonusAmount += *memberCardConsume.PayRecordRoomBonusAmount * multiplier
		// 商品赠金
		memberCardData.GoodsBonusAmount += *memberCardConsume.PayRecordGoodsBonusAmount * multiplier
		// 通用赠金
		memberCardData.CommonBonusAmount += *memberCardConsume.PayRecordCommonBonusAmount * multiplier
	}
}

// fillMemberCardOperationData 填充会员卡充值数据
// payBillShiftReportVOs: 支付账单列表
// memberCardRechargeData: 需要填充的会员卡充值数据对象
func (service *ShiftReportService) fillMemberCardOperationData(memberCardOperationsRecharge []po.MemberCardOperation, memberCardRechargeData *vo.MemberCardRechargeData) {
	for _, memberCardOperation := range memberCardOperationsRecharge {
		// 只处理充值类型的支付账单
		if !util.InList(*memberCardOperation.OperationType, []string{_const.V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE, _const.V2_MEMBER_CARD_OPERATION_TYPE_REFUND}) {
			continue
		}

		// 判断支付方向，退款需要减去金额
		multiplier := int64(1)
		if *memberCardOperation.OperationType == _const.V2_MEMBER_CARD_OPERATION_TYPE_REFUND {
			multiplier = -1
		}

		// 累加充值金额
		memberCardRechargeData.RechargeAmount += *memberCardOperation.PrincipalAmount * multiplier
	}
}
