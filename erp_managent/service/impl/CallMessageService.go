package impl

import (
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CallMessageService struct {
}

var (
	callMessageService  = CallMessageService{}
	callMessageTransfer = transfer.CallMessageTransfer{}
)

func (service *CallMessageService) CreateCallMessage(logCtx *gin.Context, record *po.CallMessage) error {
	return Save(record)
}

func (service *CallMessageService) CreateCallMessageWithTx(logCtx *gin.Context, record *po.CallMessage, tx *gorm.DB) error {
	return SaveWithTx(record, tx)
}

func (service *CallMessageService) UpdateCallMessage(logCtx *gin.Context, record *po.CallMessage) error {
	return Update(record)
}

func (service *CallMessageService) UpdateCallMessagePartial(logCtx *gin.Context, record *po.CallMessage) error {
	return UpdateNotNull(record)
}

func (service *CallMessageService) UpdateCallMessagePartialWithTx(logCtx *gin.Context, record *po.CallMessage, tx *gorm.DB) error {
	return UpdateNotNullWithTx(record, tx)
}

func (service *CallMessageService) DeleteCallMessage(logCtx *gin.Context, id string) error {
	return Delete(po.CallMessage{Id: &id})
}

func (service *CallMessageService) FindCallMessageById(logCtx *gin.Context, id string) (record *po.CallMessage, err error) {
	record = &po.CallMessage{}
	err = model.DBMaster.Self.Where("id=?", id).First(record).Error
	return
}

func (service *CallMessageService) FindAllCallMessage(logCtx *gin.Context, reqDto *req.QueryCallMessageReqDto) (list *[]po.CallMessage, err error) {
	db := model.DBSlave.Self.Model(&po.CallMessage{})
	if reqDto.ID != nil && *reqDto.ID != "" {
		db = db.Where("id=?", *reqDto.ID)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.RoomId != nil && *reqDto.RoomId != "" {
		db = db.Where("room_id=?", *reqDto.RoomId)
	}
	if reqDto.CallType != nil && *reqDto.CallType != "" {
		db = db.Where("call_type=?", *reqDto.CallType)
	}
	if reqDto.Status != nil {
		db = db.Where("status=?", *reqDto.Status)
	}

	db = db.Order("ctime desc")
	list = &[]po.CallMessage{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *CallMessageService) FindAllCallMessageWithPagination(logCtx *gin.Context, reqDto *req.QueryCallMessageReqDto) (list *[]po.CallMessage, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.CallMessage{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.ID != nil && *reqDto.ID != "" {
		db = db.Where("id=?", *reqDto.ID)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.RoomId != nil && *reqDto.RoomId != "" {
		db = db.Where("room_id=?", *reqDto.RoomId)
	}
	if reqDto.CallType != nil && *reqDto.CallType != "" {
		db = db.Where("call_type=?", *reqDto.CallType)
	}
	if reqDto.Status != nil {
		db = db.Where("status=?", *reqDto.Status)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.CallMessage{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

func (service *CallMessageService) ConvertToCallMessageVO(ctx *gin.Context, record po.CallMessage) vo.CallMessageVO {
	return callMessageTransfer.PoToVo(record)
}

func (service *CallMessageService) ConvertToCallMessagePO(ctx *gin.Context, recordVO vo.CallMessageVO) po.CallMessage {
	return callMessageTransfer.VoToPo(recordVO)
}

func (service *CallMessageService) FindCallMessagesCurrentDay(ctx *gin.Context, venueId string) (list []po.CallMessage, err error) {
	list = []po.CallMessage{}
	venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: &venueId,
	})
	if err != nil {
		return
	}
	venue := (*venues)[0]
	if venue.StartHours == nil {
		err = fmt.Errorf("venue startHours is nil")
		return
	}
	startHour := *venue.StartHours
	businessStartTime, _, err := util.GetCurrentBusinessTimeRange(startHour)
	if err != nil {
		return
	}

	db := model.DBSlave.Self.Model(&po.CallMessage{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("ctime >= ?", businessStartTime)
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}

func (service *CallMessageService) SendNATSCallMessage(ctx *gin.Context, venueId string) (err error) {
	notificationService.SendNATSCallMessage(ctx, venueId)
	return
}

// FindCallMessagesUnprocessed 查询未处理的呼叫消息
func (service *CallMessageService) FindCallMessagesUnprocessed(ctx *gin.Context, venueId string) (list []po.CallMessage, err error) {
	list = []po.CallMessage{}
	venues, err := venueService.FindAllVenue(ctx, &req.QueryVenueReqDto{
		Id: &venueId,
	})
	if err != nil {
		return
	}
	venue := (*venues)[0]
	if venue.StartHours == nil {
		err = fmt.Errorf("venue startHours is nil")
		return
	}
	startHour := *venue.StartHours
	businessStartTime, _, err := util.GetCurrentBusinessTimeRange(startHour)
	if err != nil {
		return
	}

	db := model.DBSlave.Self.Model(&po.CallMessage{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("ctime >= ?", businessStartTime)
	db = db.Where("status=?", _const.V2_CALL_MESSAGE_STATUS_UNPROCESSED)
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}

// FindCallMessagesUnprocessedByRoomId 根据房间ID查询未处理的呼叫消息
func (service *CallMessageService) FindCallMessagesUnprocessedByRoomId(ctx *gin.Context, roomId string) (list []po.CallMessage, err error) {
	list = []po.CallMessage{}
	db := model.DBSlave.Self.Model(&po.CallMessage{})
	db = db.Where("room_id=?", roomId)
	db = db.Where("status=?", _const.V2_CALL_MESSAGE_STATUS_UNPROCESSED)
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}
