package impl

import (
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InventoryRecordItemService struct {
}

// CreateInventoryRecordItem 创建库存记录明细
func (service *InventoryRecordItemService) CreateInventoryRecordItem(logCtx *gin.Context, item *po.InventoryRecordItem) error {
	return Save(item)
}

// CreateInventoryRecordItemWithTx 在事务中创建库存记录明细
func (service *InventoryRecordItemService) CreateInventoryRecordItemWithTx(logCtx *gin.Context, item *po.InventoryRecordItem, tx *gorm.DB) error {
	return SaveWithTx(item, tx)
}

// BatchCreateInventoryRecordItems 批量创建库存记录明细
func (service *InventoryRecordItemService) BatchCreateInventoryRecordItems(logCtx *gin.Context, items []po.InventoryRecordItem) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		for _, item := range items {
			if err := SaveWithTx(&item, tx); err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchCreateInventoryRecordItemsWithTx 在事务中批量创建库存记录明细
func (service *InventoryRecordItemService) BatchCreateInventoryRecordItemsWithTx(logCtx *gin.Context, items []po.InventoryRecordItem, tx *gorm.DB) error {
	for _, item := range items {
		if err := SaveWithTx(&item, tx); err != nil {
			return err
		}
	}
	return nil
}

// UpdateInventoryRecordItem 更新库存记录明细
func (service *InventoryRecordItemService) UpdateInventoryRecordItem(logCtx *gin.Context, item *po.InventoryRecordItem) error {
	return Update(item)
}

// UpdateInventoryRecordItemPartial 部分更新库存记录明细
func (service *InventoryRecordItemService) UpdateInventoryRecordItemPartial(logCtx *gin.Context, item *po.InventoryRecordItem) error {
	return UpdateNotNull(item)
}

// DeleteInventoryRecordItem 删除库存记录明细
func (service *InventoryRecordItemService) DeleteInventoryRecordItem(logCtx *gin.Context, id string) error {
	return Delete(po.InventoryRecordItem{Id: &id})
}

// DeleteInventoryRecordItemWithTx 在事务中删除库存记录明细
func (service *InventoryRecordItemService) DeleteInventoryRecordItemWithTx(logCtx *gin.Context, id string, tx *gorm.DB) error {
	return DeleteWithTx(po.InventoryRecordItem{Id: &id}, tx)
}

// FindInventoryRecordItemById 根据ID查找库存记录明细
func (service *InventoryRecordItemService) FindInventoryRecordItemById(logCtx *gin.Context, id string) (item *po.InventoryRecordItem, err error) {
	item = &po.InventoryRecordItem{}
	err = model.DBMaster.Self.Where("id=?", id).First(item).Error
	return
}

// FindInventoryRecordItemsByRecordId 根据库存记录ID查找所有明细
func (service *InventoryRecordItemService) FindInventoryRecordItemsByRecordId(logCtx *gin.Context, recordId string) (items *[]po.InventoryRecordItem, err error) {
	items = &[]po.InventoryRecordItem{}
	err = model.DBSlave.Self.Where("inventory_record_id=?", recordId).Find(items).Error
	return
}

// FindInventoryRecordItemsByProductId 根据商品ID查找所有库存记录明细
func (service *InventoryRecordItemService) FindInventoryRecordItemsByProductId(logCtx *gin.Context, productId string) (items *[]po.InventoryRecordItem, err error) {
	items = &[]po.InventoryRecordItem{}
	err = model.DBSlave.Self.Where("product_id=?", productId).Order("ctime desc").Find(items).Error
	return
}

// FindInventoryRecordItemsByProductIdAndVenueId 根据商品ID和门店ID查找库存记录明细
func (service *InventoryRecordItemService) FindInventoryRecordItemsByProductIdAndVenueId(logCtx *gin.Context, productId, venueId string) (items *[]po.InventoryRecordItem, err error) {
	items = &[]po.InventoryRecordItem{}
	err = model.DBSlave.Self.
		Joins("JOIN inventory_record ir ON inventory_record_item.inventory_record_id = ir.id").
		Where("inventory_record_item.product_id = ? AND ir.venue_id = ?", productId, venueId).
		Order("inventory_record_item.ctime desc").
		Find(items).Error
	return
}

// CalculateProductStockFromItems 根据库存记录明细计算商品库存
func (service *InventoryRecordItemService) CalculateProductStockFromItems(logCtx *gin.Context, productId, venueId string) (stock int, err error) {
	var totalQuantity int64
	err = model.DBSlave.Self.
		Table("inventory_record_item iri").
		Joins("JOIN inventory_record ir ON iri.inventory_record_id = ir.id").
		Where("iri.product_id = ? AND ir.venue_id = ? AND ir.state = 0", productId, venueId).
		Select("COALESCE(SUM(iri.quantity), 0)").
		Scan(&totalQuantity).Error

	if err != nil {
		return 0, err
	}

	return int(totalQuantity), nil
}

// DeleteInventoryRecordItemsByRecordId 根据库存记录ID删除所有明细
func (service *InventoryRecordItemService) DeleteInventoryRecordItemsByRecordId(logCtx *gin.Context, recordId string) error {
	return model.DBMaster.Self.Where("inventory_record_id=?", recordId).Delete(&po.InventoryRecordItem{}).Error
}

// DeleteInventoryRecordItemsByRecordIdWithTx 在事务中根据库存记录ID删除所有明细
func (service *InventoryRecordItemService) DeleteInventoryRecordItemsByRecordIdWithTx(logCtx *gin.Context, recordId string, tx *gorm.DB) error {
	return tx.Where("inventory_record_id=?", recordId).Delete(&po.InventoryRecordItem{}).Error
}
