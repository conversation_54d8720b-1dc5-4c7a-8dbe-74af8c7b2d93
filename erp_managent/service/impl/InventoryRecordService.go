package impl

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InventoryRecordService struct {
}

func (service *InventoryRecordService) CreateInventoryRecord(logCtx *gin.Context, record *po.InventoryRecord) error {
	return Save(record)
}

func (service *InventoryRecordService) CreateInventoryRecordWithTx(logCtx *gin.Context, record *po.InventoryRecord, tx *gorm.DB) error {
	return SaveWithTx(record, tx)
}

func (service *InventoryRecordService) UpdateInventoryRecord(logCtx *gin.Context, record *po.InventoryRecord) error {
	return Update(record)
}

func (service *InventoryRecordService) UpdateInventoryRecordPartial(logCtx *gin.Context, record *po.InventoryRecord) error {
	return UpdateNotNull(record)
}

func (service *InventoryRecordService) DeleteInventoryRecord(logCtx *gin.Context, id string) error {
	return Delete(po.InventoryRecord{Id: &id})
}

func (service *InventoryRecordService) DeleteInventoryRecordWithTx(logCtx *gin.Context, id string, tx *gorm.DB) error {
	return DeleteWithTx(po.InventoryRecord{Id: &id}, tx)
}

func (service *InventoryRecordService) FindInventoryRecordById(logCtx *gin.Context, id string) (record *po.InventoryRecord, err error) {
	record = &po.InventoryRecord{}
	err = model.DBMaster.Self.Where("id=?", id).First(record).Error
	return
}

func (service *InventoryRecordService) FindAllInventoryRecord(logCtx *gin.Context, reqDto *req.QueryInventoryRecordReqDto) (list *[]po.InventoryRecord, err error) {
	db := model.DBSlave.Self.Model(&po.InventoryRecord{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	db = db.Order("ctime desc")
	list = &[]po.InventoryRecord{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *InventoryRecordService) FindAllInventoryRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryInventoryRecordReqDto) (list *[]po.InventoryRecord, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.InventoryRecord{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.InventoryRecord{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

func (service *InventoryRecordService) FindInventoryRecordsByVenueAndType(logCtx *gin.Context, venueId, recordType string) (list *[]po.InventoryRecord, err error) {
	list = &[]po.InventoryRecord{}
	err = model.DBSlave.Self.Where("venue_id = ? AND type = ?", venueId, recordType).Find(list).Error
	return
}

func (service *InventoryRecordService) FindInventoryRecordsByConditionAndType(logCtx *gin.Context, recordType string, condition map[string]interface{}) (list *[]po.InventoryRecord, err error) {
	db := model.DBSlave.Self.Where("type = ?", recordType)

	for key, value := range condition {
		db = db.Where(key+" = ?", value)
	}

	list = &[]po.InventoryRecord{}
	err = db.Find(list).Error
	return
}

func (service *InventoryRecordService) FindInventoryRecordsByVenueAndTypeWithLimit(logCtx *gin.Context, venueId, recordType string, limit int) (list *[]po.InventoryRecord, err error) {
	db := model.DBSlave.Self.Where("venue_id = ? AND type = ?", venueId, recordType)

	if limit > 0 {
		db = db.Limit(limit)
	}

	list = &[]po.InventoryRecord{}
	err = db.Order("ctime desc").Find(list).Error
	return
}
