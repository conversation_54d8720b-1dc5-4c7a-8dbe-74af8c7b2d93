package impl

import (
	"errors"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MemberRechargeBillService struct {
}

var memberRechargeBillService = &MemberRechargeBillService{}
var memberRechageBillTransfer = transfer.MemberRechargeBillTransfer{}

func (service *MemberRechargeBillService) CreateMemberRechargeBill(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill) error {
	return Save(memberRechargeBill)
}
func (service *MemberRechargeBillService) CreateMemberRechargeBillWithTx(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill, tx *gorm.DB) error {
	return SaveWithTx(memberRechargeBill, tx)
}
func (service *MemberRechargeBillService) UpdateMemberRechargeBill(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill) error {
	return Update(memberRechargeBill)
}

func (service *MemberRechargeBillService) UpdateMemberRechargeBillPartial(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill) error {
	return UpdateNotNull(memberRechargeBill)
}

func (service *MemberRechargeBillService) UpdateMemberRechargeBillPartialWithTx(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill, tx *gorm.DB) error {
	return UpdateNotNullWithTx(memberRechargeBill, tx)
}
func (service *MemberRechargeBillService) DeleteMemberRechargeBill(logCtx *gin.Context, id string) error {
	return Delete(po.MemberRechargeBill{Id: &id})
}

func (service *MemberRechargeBillService) FindMemberRechargeBillById(logCtx *gin.Context, id string) (memberRechargeBill *po.MemberRechargeBill, err error) {
	memberRechargeBill = &po.MemberRechargeBill{}
	err = model.DBMaster.Self.Where("id=?", id).First(memberRechargeBill).Error
	return
}

func (service *MemberRechargeBillService) FindAllMemberRechargeBill(logCtx *gin.Context, reqDto *req.QueryMemberRechargeBillReqDto) (list *[]po.MemberRechargeBill, err error) {
	db := model.DBSlave.Self.Model(&po.MemberRechargeBill{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.EmployeeId != nil && *reqDto.EmployeeId != "" {
		db = db.Where("employee_id=?", *reqDto.EmployeeId)
	}
	if reqDto.BillId != nil && *reqDto.BillId != "" {
		db = db.Where("bill_id=?", *reqDto.BillId)
	}
	if reqDto.BillPid != nil && *reqDto.BillPid != "" {
		db = db.Where("bill_pid=?", *reqDto.BillPid)
	}
	if reqDto.StartTime != nil && *reqDto.StartTime > 0 {
		db = db.Where("finish_time>=?", *reqDto.StartTime)
	}
	if reqDto.ThirdPayId != nil && *reqDto.ThirdPayId != "" {
		db = db.Where("third_pay_id=?", *reqDto.ThirdPayId)
	}
	if reqDto.EndTime != nil && *reqDto.EndTime > 0 {
		db = db.Where("finish_time<=?", *reqDto.EndTime)
	}
	if reqDto.MemberCardId != nil && *reqDto.MemberCardId != "" {
		db = db.Where("member_card_id=?", *reqDto.MemberCardId)
	}

	db = db.Order("ctime desc")
	list = &[]po.MemberRechargeBill{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *MemberRechargeBillService) FindAllMemberRechargeBillWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberRechargeBillReqDto) (list *[]po.MemberRechargeBill, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.MemberRechargeBill{})

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.BillId != nil && *reqDto.BillId != "" {
		db = db.Where("bill_id=?", *reqDto.BillId)
	}
	if reqDto.BillPid != nil && *reqDto.BillPid != "" {
		db = db.Where("bill_pid=?", *reqDto.BillPid)
	}
	if reqDto.MemberCardId != nil && *reqDto.MemberCardId != "" {
		db = db.Where("member_card_id=?", *reqDto.MemberCardId)
	}

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.MemberRechargeBill{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

func (service *MemberRechargeBillService) ConvertToMemberRechargeBillVO(logCtx *gin.Context, memberRechargeBill po.MemberRechargeBill) vo.MemberRechargeBillVO {
	return memberRechageBillTransfer.PoToVo(memberRechargeBill)
}

func (service *MemberRechargeBillService) ConvertToMemberRechargeBill(logCtx *gin.Context, memberRechargeBillVO vo.MemberRechargeBillVO) po.MemberRechargeBill {
	return memberRechageBillTransfer.VoToPo(memberRechargeBillVO)
}

// FindByPayId 根据支付ID查询会员充值账单
func (service *MemberRechargeBillService) FindByPayId(logCtx *gin.Context, payId string) (po.MemberRechargeBill, error) {
	list := &[]po.MemberRechargeBill{}
	db := model.DBSlave.Self.Model(&po.MemberRechargeBill{})
	db = db.Where("pay_id=?", payId)
	err := db.Find(list).Error
	if err != nil {
		return po.MemberRechargeBill{}, err
	}
	if len(*list) <= 0 {
		return po.MemberRechargeBill{}, errors.New("未找到会员充值账单")
	}
	return (*list)[0], nil
}

// SaveMemberRechargeCallbackInfo 保存会员充值回调信息
func (service *MemberRechargeBillService) SaveMemberRechargeCallbackInfo(logCtx *gin.Context, memberRechargeBills []po.MemberRechargeBill, memberRechargeRecords []po.MemberRechargeRecord) error {
	tx := model.DBMaster.Self.Begin()
	for _, v := range memberRechargeBills {
		if err := memberRechargeBillService.UpdateMemberRechargeBillPartialWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range memberRechargeRecords {
		if err := memberRechargeRecordService.UpdateMemberRechargeRecordPartialWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	err := tx.Commit().Error
	if err != nil {
		return err
	}
	return nil
}

// SaveRPCMemberCardConsumeInfo 保存会员卡消费信息
func (service *MemberRechargeBillService) SaveRPCMemberCardConsumeInfo(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill, memberCard *po.MemberCard) error {
	tx := model.DBMaster.Self.Begin()
	if err := memberRechargeBillService.CreateMemberRechargeBillWithTx(logCtx, memberRechargeBill, tx); err != nil {
		tx.Rollback()
		return err
	}
	if err := memberCardService.UpdateMemberCardPartialWithTx(logCtx, memberCard, tx); err != nil {
		tx.Rollback()
		return err
	}
	err := tx.Commit().Error
	if err != nil {
		return err
	}
	return nil
}

// SaveRPCMemberCardRefundInfo 保存会员卡退款信息
func (service *MemberRechargeBillService) SaveRPCMemberCardRefundInfo(logCtx *gin.Context, memberRechargeBill *po.MemberRechargeBill, memberCard *po.MemberCard) error {
	tx := model.DBMaster.Self.Begin()
	if err := memberRechargeBillService.CreateMemberRechargeBillWithTx(logCtx, memberRechargeBill, tx); err != nil {
		tx.Rollback()
		return err
	}
	if err := memberCardService.UpdateMemberCardPartialWithTx(logCtx, memberCard, tx); err != nil {
		tx.Rollback()
		return err
	}
	err := tx.Commit().Error
	if err != nil {
		return err
	}
	return nil
}

// FindByBillId 根据账单ID查询会员充值账单
func (service *MemberRechargeBillService) FindByBillId(logCtx *gin.Context, venueId, billId string) (po.MemberRechargeBill, error) {
	list, err := service.FindAllMemberRechargeBill(logCtx, &req.QueryMemberRechargeBillReqDto{VenueId: &venueId, BillId: &billId})
	if err != nil {
		return po.MemberRechargeBill{}, err
	}
	if len(*list) <= 0 {
		return po.MemberRechargeBill{}, errors.New("未找到会员充值账单")
	}
	return (*list)[0], nil
}

func (service *MemberRechargeBillService) FindPaidByBillIds(logCtx *gin.Context, venueId string, billIds []string) (list *[]po.MemberRechargeBill, err error) {
	list = &[]po.MemberRechargeBill{}
	db := model.DBSlave.Self.Model(&po.MemberRechargeBill{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("bill_id IN (?)", billIds)
	db = db.Where("state=?", _const.V2_MEMBER_RECHARGE_RECORD_STATUS_SUCCESS)
	err = db.Find(list).Error
	return
}

func (service *MemberRechargeBillService) FindMemberRechargeBillsByBillIds(logCtx *gin.Context, venueId string, billIds []string) (list *[]po.MemberRechargeBill, err error) {
	list = &[]po.MemberRechargeBill{}
	db := model.DBSlave.Self.Model(&po.MemberRechargeBill{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("bill_id IN (?)", billIds)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}