package impl

import (
	"fmt"
	"strconv"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// MemberCardLevelService 会员卡等级服务
type MemberCardLevelService struct {
}

func (s *MemberCardLevelService) CreateMemberCardLevel(ctx *gin.Context, req *req.AddMemberCardLevelReqDto) (*po.MemberCardLevel, error) {
	level := &po.MemberCardLevel{}
	if req.VenueId != nil {
		level.VenueId = req.VenueId
	}
	if req.Name != nil {
		level.Name = req.Name
	}
	if req.Level != nil {
		level.Level = req.Level
	}
	if req.Logo != nil {
		level.Logo = req.Logo
	}
	if req.Background != nil {
		level.Background = req.Background
	}
	if req.Description != nil {
		level.Description = req.Description
	}

	err := Save(level)
	return level, err
}

func (s *MemberCardLevelService) UpdateMemberCardLevel(ctx *gin.Context, req *req.UpdateMemberCardLevelReqDto) (*po.MemberCardLevel, error) {
	query := dal.Use(model.DBMaster.Self)

	// 先检查记录是否存在
	level, err := query.MemberCardLevel.WithContext(ctx).
		Where(query.MemberCardLevel.Id.Eq(*req.Id)).
		First()
	if err != nil {
		return nil, err
	}
	if level == nil {
		return nil, fmt.Errorf("member card level not found: %s", *req.Id)
	}

	// 只更新 PO 中存在的字段
	updates := map[string]any{}

	if req.VenueId != nil {
		updates["venue_id"] = *req.VenueId
	}
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Level != nil {
		updates["level"] = *req.Level
	}
	if req.Logo != nil {
		updates["logo"] = *req.Logo
	}
	if req.Background != nil {
		updates["background"] = *req.Background
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}

	// 执行更新
	if len(updates) > 0 {
		_, err = query.MemberCardLevel.WithContext(ctx).
			Where(query.MemberCardLevel.Id.Eq(*req.Id)).
			Updates(updates)
		if err != nil {
			return nil, err
		}
	}

	return query.MemberCardLevel.WithContext(ctx).
		Where(query.MemberCardLevel.Id.Eq(*req.Id)).
		First()
}

// BatchUpdateMemberCardLevel 批量更新会员卡等级
func (s *MemberCardLevelService) BatchUpdateMemberCardLevel(ctx *gin.Context, req *req.BatchUpdateMemberCardLevelReqDto) ([]*po.MemberCardLevel, error) {
	query := dal.Use(model.DBMaster.Self)
	var updatedLevels []*po.MemberCardLevel

	// 先验证门店ID
	if req.VenueId == nil || *req.VenueId == "" {
		return nil, fmt.Errorf("venueId不能为空")
	}

	// 验证items不为空
	if len(req.Items) == 0 {
		return nil, fmt.Errorf("批量更新项不能为空")
	}

	// 开启事务
	tx := query.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 生成临时前缀，避免唯一约束冲突
	tempPrefix := fmt.Sprintf("TEMP_%d_", time.Now().UnixNano())

	// 第一阶段：将所有记录的level更新为临时值
	for i, item := range req.Items {
		if item.Id == nil || *item.Id == "" {
			tx.Rollback()
			return nil, fmt.Errorf("会员卡等级ID不能为空")
		}
		if item.Level == nil {
			tx.Rollback()
			return nil, fmt.Errorf("等级值不能为空")
		}

		// 先检查记录是否存在且属于指定门店
		level, err := tx.MemberCardLevel.WithContext(ctx).
			Where(tx.MemberCardLevel.Id.Eq(*item.Id)).
			Where(tx.MemberCardLevel.VenueId.Eq(*req.VenueId)).
			First()
		if err != nil {
			tx.Rollback()
			return nil, err
		}
		if level == nil {
			tx.Rollback()
			return nil, fmt.Errorf("会员卡等级不存在或不属于当前门店: %s", *item.Id)
		}

		// 更新为临时值
		tempLevel := tempPrefix + strconv.Itoa(i)
		_, err = tx.MemberCardLevel.WithContext(ctx).
			Where(tx.MemberCardLevel.Id.Eq(*item.Id)).
			Update(tx.MemberCardLevel.Level, tempLevel)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// 第二阶段：将临时值更新为目标值
	for i, item := range req.Items {
		tempLevel := tempPrefix + strconv.Itoa(i)

		// 从临时值更新为目标值
		_, err := tx.MemberCardLevel.WithContext(ctx).
			Where(tx.MemberCardLevel.Id.Eq(*item.Id)).
			Where(tx.MemberCardLevel.Level.Eq(tempLevel)).
			Update(tx.MemberCardLevel.Level, *item.Level)
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 获取更新后的记录
		updatedLevel, err := tx.MemberCardLevel.WithContext(ctx).
			Where(tx.MemberCardLevel.Id.Eq(*item.Id)).
			First()
		if err != nil {
			tx.Rollback()
			return nil, err
		}
		updatedLevels = append(updatedLevels, updatedLevel)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return updatedLevels, nil
}

func (s *MemberCardLevelService) DeleteMemberCardLevel(ctx *gin.Context, id string) error {
	query := dal.Use(model.DBMaster.Self)
	return query.MemberCardLevel.WithContext(ctx).DeleteByID(id)
}

func (s *MemberCardLevelService) GetAllMemberCardLevels(ctx *gin.Context, req *req.QueryMemberCardLevelReqDto) ([]*po.MemberCardLevel, error) {
	query := dal.Use(model.DBSlave.Self)
	return query.MemberCardLevel.WithContext(ctx).QueryByVenueId(*req.VenueId)
}

// FindAllMemberCardLevel 查询会员卡等级
func (s *MemberCardLevelService) FindAllMemberCardLevel(ctx *gin.Context, query *req.QueryMemberCardLevelReqDto) ([]*po.MemberCardLevel, error) {
	// 实现查询会员卡等级的逻辑
	return []*po.MemberCardLevel{}, nil
}
