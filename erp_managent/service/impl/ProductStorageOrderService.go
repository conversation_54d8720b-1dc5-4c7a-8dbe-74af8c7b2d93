package impl

import (
	"errors"
	"fmt"
	"strings"
	"time"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// ProductStorageOrderService 存酒单服务
type ProductStorageOrderService struct {
}

// 初始化DAO
func NewProductStorageOrderService() *ProductStorageOrderService {
	return &ProductStorageOrderService{}
}

// CreateProductStorageOrder 创建存酒单及其明细
func (service *ProductStorageOrderService) CreateProductStorageOrder(logCtx *gin.Context, reqDto *req.AddProductStorageOrderReqDto) (*vo.ProductStorageOrderVO, error) {
	if reqDto == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// 获取当前时间作为默认值
	currentTime := time.Now().Unix()

	// 确保存酒时间不为0，如果为0则使用当前时间
	if reqDto.StorageTime == 0 {
		reqDto.StorageTime = currentTime
	}

	// 生成存酒单号
	venueId := reqDto.VenueId
	orderNo := "PS" + venueId + strings.ReplaceAll(fmt.Sprintf("%d", currentTime), "-", "")

	// 获取会员卡号（为了兼容性，同时设置旧字段和新字段）
	memberCardNo := reqDto.MemberCardNumber
	if memberCardNo == "" && reqDto.MemberCardId != "" {
		memberCardNo = reqDto.MemberCardId
	}

	// 创建存酒单
	order := &po.ProductStorageOrder{
		OrderNo:           &orderNo,
		VenueId:           &venueId,
		CustomerId:        &reqDto.CustomerId,
		CustomerName:      &reqDto.CustomerName,
		PhoneNumber:       &reqDto.PhoneNumber,
		MemberCardNo:      &memberCardNo, // 保留原字段，为兼容性设置
		MemberCardNumber:  &reqDto.MemberCardNumber,
		MemberCardId:      &reqDto.MemberCardId,
		StorageTime:       &reqDto.StorageTime,
		TotalItems:        util.GetItPtr(len(reqDto.Items)),
		TotalQuantity:     util.GetItPtr(0), // 初始化为0，后面累加
		RemainingQuantity: util.GetItPtr(0), // 初始化为0，后面累加
		Remark:            &reqDto.Remark,
		StorageRoomId:     &reqDto.StorageRoomId,   // 添加存储房间ID
		StorageRoomName:   &reqDto.StorageRoomName, // 添加存储房间名称
		OperatorId:        &reqDto.OperatorId,
		OperatorName:      &reqDto.OperatorName,
		OfflineOnly:       util.GetItPtr(util.BoolToInt(reqDto.OfflineOnly)),
	}

	var totalQuantity int = 0
	var err error
	var savedStorages []*po.ProductStorage // 保存实际的存储记录

	// 实例化操作日志服务
	opLogService := NewProductStorageOperationLogService()

	// 使用事务确保存酒单和明细一起创建
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 保存存酒单
		if err := Save(order); err != nil {
			return err
		}

		// 2. 保存存酒明细
		for i, item := range reqDto.Items {
			// 生成明细单号
			detailOrderNo := orderNo + "-" + fmt.Sprintf("%03d", i+1)

			// 如果没有设置到期时间，则使用存酒配置的默认天数
			expireTime := item.ExpireTime
			if expireTime == 0 {
				// 优先从配置中获取存储天数，如果没有配置则使用兜底值
				wineService := &WineStorageSettingService{}
				storageDays := wineService.GetStorageDaysConfig(logCtx, venueId)

				expireTime = time.Unix(reqDto.StorageTime, 0).AddDate(0, 0, storageDays).Unix()
			}

			// 获取会员卡号，为兼容性同时设置旧字段和新字段
			memberCardNo := reqDto.MemberCardNumber
			if memberCardNo == "" && reqDto.MemberCardId != "" {
				memberCardNo = reqDto.MemberCardId
			}

			// 保存存酒记录
			storage := &po.ProductStorage{
				OrderNo:           &detailOrderNo,
				ParentOrderNo:     &orderNo, // 关联主订单号
				VenueId:           &venueId,
				CustomerId:        &reqDto.CustomerId,
				CustomerName:      &reqDto.CustomerName,
				PhoneNumber:       &reqDto.PhoneNumber,
				MemberCardNo:      &memberCardNo,            // 保留原字段，为兼容性设置
				MemberCardNumber:  &reqDto.MemberCardNumber, // 添加会员卡号
				MemberCardId:      &reqDto.MemberCardId,     // 添加会员卡ID
				ProductId:         &item.ProductId,
				ProductName:       &item.ProductName,
				ProductType:       &item.ProductType, // 添加商品类型字段
				ProductUnit:       &item.ProductUnit,
				ProductSpec:       &item.ProductSpec,
				Quantity:          &item.Quantity,
				RemainingQty:      &item.Quantity, // 初始剩余等于数量
				StorageLocation:   &item.StorageLocation,
				StorageRoomId:     &reqDto.StorageRoomId,   // 添加存储房间ID
				StorageRoomName:   &reqDto.StorageRoomName, // 添加存储房间名称
				StorageTime:       &reqDto.StorageTime,
				ExpireTime:        &expireTime, // 使用计算后的到期时间
				Remark:            &item.Remark,
				OperatorId:        &reqDto.OperatorId,
				OperatorName:      &reqDto.OperatorName,
				LastOperationTime: &reqDto.StorageTime, // 设置最后操作时间与存酒时间一致
			}

			if err := Save(storage); err != nil {
				return err
			}

			// 保存实际的存储记录以备后用
			savedStorages = append(savedStorages, storage)

			// 记录存酒操作日志
			operationRemark := fmt.Sprintf("存酒单号: %s，商品: %s，数量: %d，存放位置: %s",
				orderNo, *storage.ProductName, *storage.Quantity, lo.FromPtrOr(storage.StorageLocation, ""))
			if item.Remark != "" {
				operationRemark += fmt.Sprintf("，备注: %s", item.Remark)
			}

			_, err = opLogService.CreateWithinTransactionWithRoom(
				tx,
				*storage.Id,
				*storage.OrderNo,
				"storage",
				"存酒",
				reqDto.StorageTime,
				*storage.Quantity,
				*storage.OperatorId,
				*storage.OperatorName,
				*storage.Quantity, // 初始剩余量等于数量
				operationRemark,
				reqDto.StorageRoomName, // 存酒操作记录寄存包厅
				"",                     // 存酒操作不设置送达包厅
			)

			if err != nil {
				return fmt.Errorf("创建存酒操作日志记录失败: %s", err.Error())
			}

			// 累加总数量
			totalQuantity += item.Quantity
		}

		// 3. 更新存酒单总数量
		*order.TotalQuantity = totalQuantity
		*order.RemainingQuantity = totalQuantity

		return tx.Model(&po.ProductStorageOrder{}).
			Where("id = ?", order.Id).
			Updates(map[string]interface{}{
				"total_quantity":     totalQuantity,
				"remaining_quantity": totalQuantity,
			}).Error
	})

	if err != nil {
		return nil, err
	}

	// 直接构建返回结果，避免再次查询数据库
	result := &vo.ProductStorageOrderVO{
		Id:                *order.Id,
		OrderNo:           *order.OrderNo,
		VenueId:           *order.VenueId,
		CustomerId:        *order.CustomerId,
		CustomerName:      *order.CustomerName,
		PhoneNumber:       *order.PhoneNumber,
		MemberCardNumber:  lo.FromPtrOr(order.MemberCardNumber, ""),
		MemberCardId:      lo.FromPtrOr(order.MemberCardId, ""),
		StorageTime:       *order.StorageTime,
		TotalItems:        *order.TotalItems,
		TotalQuantity:     *order.TotalQuantity,
		RemainingQuantity: *order.RemainingQuantity,
		Remark:            *order.Remark,
		OperatorId:        *order.OperatorId,
		OperatorName:      *order.OperatorName,
		Ctime:             *order.Ctime,
		Utime:             *order.Utime,
		State:             *order.State,
		Version:           *order.Version,
		Items:             []vo.ProductStorageVO{},
	}

	// 构建明细列表，使用实际保存的存储记录
	for _, storage := range savedStorages {
		storageVO := vo.ProductStorageVO{
			Id:               *storage.Id, // 使用实际保存的ID
			OrderNo:          *storage.OrderNo,
			ParentOrderNo:    *storage.ParentOrderNo,
			VenueId:          *storage.VenueId,
			CustomerId:       *storage.CustomerId,
			CustomerName:     *storage.CustomerName,
			PhoneNumber:      *storage.PhoneNumber,
			MemberCardNo:     lo.FromPtrOr(storage.MemberCardNo, ""),     // 保留原字段
			MemberCardNumber: lo.FromPtrOr(storage.MemberCardNumber, ""), // 添加会员卡号
			MemberCardId:     lo.FromPtrOr(storage.MemberCardId, ""),     // 添加会员卡ID
			ProductId:        *storage.ProductId,
			ProductName:      *storage.ProductName,
			ProductUnit:      *storage.ProductUnit,
			ProductSpec:      *storage.ProductSpec,
			Quantity:         *storage.Quantity,
			RemainingQty:     *storage.RemainingQty,
			StorageLocation:  *storage.StorageLocation,
			StorageRoomId:    lo.FromPtrOr(storage.StorageRoomId, ""),   // 添加存储房间ID
			StorageRoomName:  lo.FromPtrOr(storage.StorageRoomName, ""), // 添加存储房间名称
			StorageTime:      *storage.StorageTime,
			ExpireTime:       *storage.ExpireTime,
			Remark:           *storage.Remark,
			OperatorId:       *storage.OperatorId,
			OperatorName:     *storage.OperatorName,
			Ctime:            *storage.Ctime,
			Utime:            *storage.Utime,
			State:            *storage.State,
			Version:          *storage.Version,
		}

		result.Items = append(result.Items, storageVO)
	}

	return result, nil
}

// GetProductStorageOrderById 根据ID获取存酒单详情
func (service *ProductStorageOrderService) GetProductStorageOrderById(logCtx *gin.Context, id string) (*vo.ProductStorageOrderVO, error) {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)
	order, err := query.Where(queryBuilder.ProductStorageOrder.Id.Eq(id)).First()
	if err != nil {
		return nil, err
	}

	// 查询存酒单明细
	productStorageQueryBuilder := dal.Use(model.DBSlave.Self)
	items, err := productStorageQueryBuilder.ProductStorage.WithContext(logCtx).Where(productStorageQueryBuilder.ProductStorage.ParentOrderNo.Eq(*order.OrderNo)).Find()
	if err != nil {
		return nil, err
	}

	// 构建返回结果
	result := &vo.ProductStorageOrderVO{
		Id:                *order.Id,
		OrderNo:           *order.OrderNo,
		VenueId:           *order.VenueId,
		CustomerId:        *order.CustomerId,
		CustomerName:      *order.CustomerName,
		PhoneNumber:       *order.PhoneNumber,
		MemberCardNo:      lo.FromPtrOr(order.MemberCardNumber, ""),
		MemberCardNumber:  lo.FromPtrOr(order.MemberCardNumber, ""),
		MemberCardId:      lo.FromPtrOr(order.MemberCardId, ""),
		StorageTime:       *order.StorageTime,
		TotalItems:        *order.TotalItems,
		TotalQuantity:     *order.TotalQuantity,
		RemainingQuantity: *order.RemainingQuantity,
		Remark:            *order.Remark,
		OperatorId:        *order.OperatorId,
		OperatorName:      *order.OperatorName,
		Ctime:             *order.Ctime,
		Utime:             *order.Utime,
		State:             *order.State,
		Version:           *order.Version,
	}

	// 添加明细
	result.Items = make([]vo.ProductStorageVO, 0, len(items))
	for _, item := range items {
		result.Items = append(result.Items, vo.ProductStorageVO{
			Id:               *item.Id,
			OrderNo:          *item.OrderNo,
			ParentOrderNo:    *item.ParentOrderNo,
			VenueId:          *item.VenueId,
			CustomerId:       *item.CustomerId,
			CustomerName:     *item.CustomerName,
			PhoneNumber:      *item.PhoneNumber,
			MemberCardNo:     lo.FromPtrOr(item.MemberCardNo, ""),     // 保留原字段
			MemberCardNumber: lo.FromPtrOr(item.MemberCardNumber, ""), // 添加会员卡号
			MemberCardId:     lo.FromPtrOr(item.MemberCardId, ""),     // 添加会员卡ID
			ProductId:        *item.ProductId,
			ProductName:      *item.ProductName,
			ProductUnit:      lo.FromPtrOr(item.ProductUnit, ""),
			ProductSpec:      lo.FromPtrOr(item.ProductSpec, ""),
			Quantity:         *item.Quantity,
			RemainingQty:     *item.RemainingQty,
			StorageLocation:  *item.StorageLocation,
			StorageTime:      *item.StorageTime,
			ExpireTime:       *item.ExpireTime,
			Remark:           *item.Remark,
			StatusCode:       lo.FromPtrOr(item.StatusCode, ""),
			OperatorId:       *item.OperatorId,
			OperatorName:     *item.OperatorName,
			IsBatch:          lo.FromPtrOr(item.IsBatch, 0),
			BatchTime:        lo.FromPtrOr(item.BatchTime, int64(0)),
			Ctime:            *item.Ctime,
			Utime:            *item.Utime,
			State:            *item.State,
			Version:          *item.Version,
		})
	}

	return result, nil
}

// 查询存酒单列表
func (service *ProductStorageOrderService) QueryProductStorageOrders(logCtx *gin.Context, reqDto *req.QueryProductStorageOrderReqDto) (*vo.ProductStorageOrderListVO, error) {
	// 构建查询条件
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)

	if reqDto.Id != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.Id.Eq(reqDto.Id))
	}
	if reqDto.OrderNo != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.OrderNo.Eq(reqDto.OrderNo))
	}
	if reqDto.VenueId != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.VenueId.Eq(reqDto.VenueId))
	}
	if reqDto.CustomerId != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.CustomerId.Eq(reqDto.CustomerId))
	}
	if reqDto.CustomerName != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.CustomerName.Like("%" + reqDto.CustomerName + "%"))
	}
	if reqDto.PhoneNumber != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.PhoneNumber.Eq(reqDto.PhoneNumber))
	}
	if reqDto.OperatorId != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.OperatorId.Eq(reqDto.OperatorId))
	}
	if reqDto.OperatorName != "" {
		query = query.Where(queryBuilder.ProductStorageOrder.OperatorName.Like("%" + reqDto.OperatorName + "%"))
	}
	if reqDto.StorageTimeStart > 0 {
		query = query.Where(queryBuilder.ProductStorageOrder.StorageTime.Gte(reqDto.StorageTimeStart))
	}
	if reqDto.StorageTimeEnd > 0 {
		query = query.Where(queryBuilder.ProductStorageOrder.StorageTime.Lte(reqDto.StorageTimeEnd))
	}
	if reqDto.SearchText != "" {
		query = query.Where(
			queryBuilder.ProductStorageOrder.CustomerName.Like("%" + reqDto.SearchText + "%"))
		query = query.Or(queryBuilder.ProductStorageOrder.PhoneNumber.Like("%" + reqDto.SearchText + "%"))
	}
	if reqDto.OnlyRemaining {
		query = query.Where(queryBuilder.ProductStorageOrder.RemainingQuantity.Gt(0))
	}

	// 计算总数
	total, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	if reqDto.PageNum <= 0 {
		reqDto.PageNum = 1
	}
	if reqDto.PageSize <= 0 {
		reqDto.PageSize = 10
	}

	orders, err := query.Order(queryBuilder.ProductStorageOrder.Ctime.Desc()).
		Offset((reqDto.PageNum - 1) * reqDto.PageSize).
		Limit(reqDto.PageSize).
		Find()
	if err != nil {
		return nil, err
	}

	// 组装响应
	result := &vo.ProductStorageOrderListVO{
		Total: total,
		List:  make([]vo.ProductStorageOrderVO, 0, len(orders)),
	}

	// 如果没有记录，直接返回
	if len(orders) == 0 {
		return result, nil
	}

	// 查询每个存酒单的明细
	for _, order := range orders {
		orderVo := vo.ProductStorageOrderVO{
			Id:                *order.Id,
			OrderNo:           *order.OrderNo,
			VenueId:           *order.VenueId,
			CustomerId:        *order.CustomerId,
			CustomerName:      *order.CustomerName,
			PhoneNumber:       *order.PhoneNumber,
			MemberCardNumber:  lo.FromPtrOr(order.MemberCardNumber, ""),
			MemberCardId:      lo.FromPtrOr(order.MemberCardId, ""),
			StorageTime:       *order.StorageTime,
			TotalItems:        *order.TotalItems,
			TotalQuantity:     *order.TotalQuantity,
			RemainingQuantity: *order.RemainingQuantity,
			Remark:            *order.Remark,

			OperatorId:   *order.OperatorId,
			OperatorName: *order.OperatorName,
			Ctime:        *order.Ctime,
			Utime:        *order.Utime,
			State:        *order.State,
			Version:      *order.Version,
		}

		// 只有需要明细时才查询
		if reqDto.PageSize <= 10 {
			// 查询存酒单明细
			productStorageQueryBuilder := dal.Use(model.DBSlave.Self)
			productStorageQuery := productStorageQueryBuilder.ProductStorage.WithContext(logCtx)
			items, err := productStorageQuery.Where(productStorageQueryBuilder.ProductStorage.ParentOrderNo.Eq(*order.OrderNo)).Find()
			if err != nil {
				return nil, err
			}

			// 添加明细
			orderVo.Items = make([]vo.ProductStorageVO, 0, len(items))
			for _, item := range items {
				orderVo.Items = append(orderVo.Items, vo.ProductStorageVO{
					Id:               *item.Id,
					OrderNo:          *item.OrderNo,
					ParentOrderNo:    *item.ParentOrderNo,
					VenueId:          *item.VenueId,
					CustomerId:       *item.CustomerId,
					CustomerName:     *item.CustomerName,
					PhoneNumber:      *item.PhoneNumber,
					MemberCardNumber: lo.FromPtrOr(item.MemberCardNumber, ""), // 添加会员卡号
					MemberCardId:     lo.FromPtrOr(item.MemberCardId, ""),     // 添加会员卡ID
					ProductId:        *item.ProductId,
					ProductName:      *item.ProductName,
					ProductUnit:      lo.FromPtrOr(item.ProductUnit, ""),
					ProductSpec:      lo.FromPtrOr(item.ProductSpec, ""),
					Quantity:         *item.Quantity,
					RemainingQty:     *item.RemainingQty,
					StorageLocation:  *item.StorageLocation,
					StorageTime:      *item.StorageTime,
					ExpireTime:       *item.ExpireTime,
					Remark:           *item.Remark,
					OperatorId:       *item.OperatorId,
					OperatorName:     *item.OperatorName,
					IsBatch:          lo.FromPtrOr(item.IsBatch, 0),
					BatchTime:        lo.FromPtrOr(item.BatchTime, int64(0)),
					Ctime:            *item.Ctime,
					Utime:            *item.Utime,
					State:            *item.State,
					Version:          *item.Version,
				})
			}
		}

		result.List = append(result.List, orderVo)
	}

	return result, nil
}

// 删除存酒单
func (service *ProductStorageOrderService) DeleteProductStorageOrder(logCtx *gin.Context, id string) error {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)
	order, err := query.QueryOneByID(id)
	if err != nil {
		return err
	}

	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 先删除明细
		if err := tx.Where("parent_order_no = ?", *order.OrderNo).Delete(&po.ProductStorage{}).Error; err != nil {
			return err
		}

		// 再删除主单
		if err := tx.Delete(&order).Error; err != nil {
			return err
		}

		return nil
	})
}

// 更新存酒单状态
func (service *ProductStorageOrderService) UpdateProductStorageOrderStatus(logCtx *gin.Context, orderNo string, remainingQty int) error {
	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 更新明细记录的剩余数量
		queryBuilder := dal.Use(model.DBSlave.Self)
		query := queryBuilder.ProductStorage.WithContext(logCtx)
		items, err := query.Where(queryBuilder.ProductStorage.ParentOrderNo.Eq(orderNo)).Find()
		if err != nil {
			return err
		}

		// 计算总剩余量
		var totalRemaining int
		for _, item := range items {
			totalRemaining += *item.RemainingQty
		}

		// 更新主单记录
		return tx.Model(&po.ProductStorageOrder{}).
			Where("order_no = ?", orderNo).
			Updates(map[string]interface{}{
				"remaining_quantity": totalRemaining,
				"utime":              time.Now().Unix(),
			}).Error
	})
}

// 延期存酒单
func (service *ProductStorageOrderService) ExtendProductStorageOrder(logCtx *gin.Context, reqDto *req.ExtendProductStorageOrderReqDto) error {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	order, err := queryBuilder.ProductStorageOrder.WithContext(logCtx).QueryOneByID(reqDto.OrderId)
	if err != nil {
		return err
	}

	// 参数验证
	if reqDto.NewExpireTime <= time.Now().Unix() {
		return fmt.Errorf("新的过期时间必须在当前时间之后")
	}

	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 查询存酒明细
		storageItems := []*po.ProductStorage{}
		if err := tx.Where("parent_order_no = ?", *order.OrderNo).Find(&storageItems).Error; err != nil {
			return err
		}

		// 更新每个存酒明细的到期时间
		for _, item := range storageItems {
			// 记录原始到期时间（用于操作日志）
			oldExpireTime := *item.ExpireTime

			// 更新到期时间
			item.ExpireTime = &reqDto.NewExpireTime

			// 更新数据库
			if err := tx.Model(item).Update("expire_time", item.ExpireTime).Error; err != nil {
				return err
			}

			// 添加延期操作记录
			now := util.TimeNowUnixInt64()
			opLogService := NewProductStorageOperationLogService()

			// 计算延期天数（用于日志展示）
			oldExpireDate := time.Unix(oldExpireTime, 0)
			newExpireDate := time.Unix(reqDto.NewExpireTime, 0)
			dayDiff := int(newExpireDate.Sub(oldExpireDate).Hours() / 24)

			remark := fmt.Sprintf("延期操作，将到期时间从 %s 延长至 %s（延长%d天）",
				time.Unix(oldExpireTime, 0).Format("2006-01-02"),
				time.Unix(reqDto.NewExpireTime, 0).Format("2006-01-02"),
				dayDiff)

			if reqDto.Remark != "" {
				remark += fmt.Sprintf("，备注: %s", reqDto.Remark)
			}

			_, err := opLogService.CreateWithinTransaction(
				tx,
				*item.Id,
				*item.OrderNo,
				"extend",
				"续存",
				now,
				0, // 续存不改变数量
				reqDto.OperatorId,
				reqDto.OperatorName,
				*item.RemainingQty,
				remark,
			)

			if err != nil {
				return fmt.Errorf("创建延期操作日志失败: %s", err.Error())
			}
		}

		return nil
	})
}

// 报废存酒单
func (service *ProductStorageOrderService) DiscardProductStorageOrder(logCtx *gin.Context, reqDto *req.DiscardProductStorageOrderReqDto) error {
	// 查询存酒单
	queryBuilder := dal.Use(model.DBSlave.Self)
	order, err := queryBuilder.ProductStorageOrder.WithContext(logCtx).QueryOneByID(reqDto.OrderId)
	if err != nil {
		return err
	}

	// 确保操作者名称不为空，如果为空，则使用操作者ID代替
	if reqDto.OperatorName == "" {
		reqDto.OperatorName = reqDto.OperatorId
	}

	// 使用事务
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 更新每个存酒明细的状态
		storageItems := []*po.ProductStorage{}
		if err := tx.Where("parent_order_no = ?", *order.OrderNo).Find(&storageItems).Error; err != nil {
			return err
		}

		// 更新存酒单主表的剩余数量
		if err := tx.Model(order).Update("remaining_quantity", 0).Error; err != nil {
			return err
		}

		// 更新每个存酒明细记录
		for _, item := range storageItems {
			// 备份原始剩余数量
			originalQty := *item.RemainingQty

			// 更新状态为"已报废"，剩余数量为0
			discardStatus := "discarded"
			zero := 0
			updates := map[string]interface{}{
				"status_code":   discardStatus,
				"remaining_qty": zero,
				"utime":         reqDto.DiscardTime,
			}

			if err := tx.Model(item).Updates(updates).Error; err != nil {
				return err
			}

			// 添加报废操作记录
			opLogService := NewProductStorageOperationLogService()
			_, err := opLogService.CreateWithinTransaction(
				tx,
				*item.Id,
				*item.OrderNo,
				"discard",
				"报废",
				reqDto.DiscardTime,
				originalQty, // 报废全部剩余数量
				reqDto.OperatorId,
				reqDto.OperatorName,
				0, // 报废后剩余为0
				fmt.Sprintf("报废存酒记录，数量: %d", originalQty), // 备注
			)

			if err != nil {
				return err
			}
		}

		return nil
	})
}

// GetOrderByOrderNo 根据订单号获取存酒单
func (service *ProductStorageOrderService) GetOrderByOrderNo(logCtx *gin.Context, orderNo string) (*po.ProductStorageOrder, error) {
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductStorageOrder.WithContext(logCtx)
	return query.Where(queryBuilder.ProductStorageOrder.OrderNo.Eq(orderNo)).First()
}

// AddItemsToOrder 向已有订单添加商品项
func (service *ProductStorageOrderService) AddItemsToOrder(logCtx *gin.Context, orderNo string, items []req.AddProductStorageItem, operatorId string, operatorName string, remark string) (*vo.ProductStorageOrderVO, error) {
	// 首先查询现有订单
	orderService := NewProductStorageOrderService()
	existingOrder, err := orderService.GetOrderByOrderNo(logCtx, orderNo)
	if err != nil {
		return nil, err
	}
	if existingOrder == nil {
		return nil, fmt.Errorf("存酒单不存在: %s", orderNo)
	}

	queryBuilder := dal.Use(model.DBSlave.Self)
	existingItems, err := queryBuilder.ProductStorage.WithContext(logCtx).Where(queryBuilder.ProductStorage.ParentOrderNo.Eq(orderNo)).Find()
	if err != nil {
		return nil, err
	}

	// 声明一个标志变量，用于判断订单是否已更新
	var orderUpdated bool = false
	var addedQuantity int = 0

	// 使用事务添加新的商品项
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		now := util.TimeNowUnixInt64()
		totalItems := 0
		totalQuantity := 0
		remainingQuantity := 0

		// 获取现有总数
		if len(existingItems) > 0 {
			// 计算现有的总数量
			for _, item := range existingItems {
				totalItems++
				totalQuantity += *item.Quantity
				remainingQuantity += *item.RemainingQty
			}
		}

		// 添加新的商品项
		for _, newItem := range items {
			// 生成一个新的订单号
			newOrderNo := "PS" + *existingOrder.VenueId + fmt.Sprintf("%d", time.Now().UnixNano())

			// 如果没有设置到期时间，则使用存酒配置的默认天数
			expireTime := newItem.ExpireTime
			if expireTime == 0 {
				// 优先从配置中获取存储天数，如果没有配置则使用兜底值
				wineService := &WineStorageSettingService{}
				storageDays := wineService.GetStorageDaysConfig(logCtx, *existingOrder.VenueId)

				expireTime = time.Unix(now, 0).AddDate(0, 0, storageDays).Unix()
			}

			// 获取会员卡号，优先使用MemberCardNumber，为兼容性同时设置
			memberCardNo := lo.FromPtrOr(existingOrder.MemberCardNumber, "")
			if memberCardNo == "" {
				memberCardNo = lo.FromPtrOr(existingOrder.MemberCardId, "")
			}

			// 创建存酒明细记录
			productStorage := &po.ProductStorage{
				OrderNo:           &newOrderNo,
				ParentOrderNo:     &orderNo,
				VenueId:           existingOrder.VenueId,
				CustomerId:        existingOrder.CustomerId,
				CustomerName:      existingOrder.CustomerName,
				PhoneNumber:       existingOrder.PhoneNumber,
				MemberCardNo:      &memberCardNo,                  // 保留原字段，为兼容性设置
				MemberCardNumber:  existingOrder.MemberCardNumber, // 使用现有订单的会员卡号
				MemberCardId:      existingOrder.MemberCardId,     // 使用现有订单的会员卡ID
				ProductId:         &newItem.ProductId,
				ProductName:       &newItem.ProductName,
				ProductSpec:       &newItem.ProductSpec,
				ProductUnit:       &newItem.ProductUnit,
				Quantity:          &newItem.Quantity,
				RemainingQty:      &newItem.Quantity, // 初始剩余数量等于原始数量
				StorageLocation:   &newItem.StorageLocation,
				StorageTime:       &now,
				ExpireTime:        &expireTime, // 使用计算后的到期时间
				OperatorId:        &operatorId,
				OperatorName:      &operatorName,
				LastOperationTime: &now, // 设置最后操作时间
				Remark:            &remark,
			}

			// 保存存酒记录
			if err := Save(productStorage); err != nil {
				return err
			}

			// 记录存酒操作日志
			operationRemark := fmt.Sprintf("追加存酒，商品: %s，数量: %d，存放位置: %s",
				*productStorage.ProductName, *productStorage.Quantity, lo.FromPtrOr(productStorage.StorageLocation, ""))
			if newItem.Remark != "" {
				operationRemark += fmt.Sprintf("，备注: %s", newItem.Remark)
			}

			opLogService := NewProductStorageOperationLogService()
			_, err := opLogService.CreateWithinTransactionWithRoom(
				tx,
				*productStorage.Id,
				*productStorage.OrderNo,
				"storage_add",
				"追加存酒",
				now,
				*productStorage.Quantity,
				operatorId,
				operatorName,
				*productStorage.Quantity, // 初始剩余量等于数量
				operationRemark,
				lo.FromPtrOr(existingOrder.StorageRoomName, ""), // 追加存酒使用原订单的寄存包厅
				"", // 追加存酒不设置送达包厅
			)

			if err != nil {
				return fmt.Errorf("创建追加存酒操作日志记录失败: %s", err.Error())
			}

			// 累加新增数量
			addedQuantity += newItem.Quantity
		}

		// 更新存酒单的总数量和总项数
		newTotalItems := *existingOrder.TotalItems + len(items)
		newTotalQuantity := *existingOrder.TotalQuantity + addedQuantity
		newRemainingQuantity := *existingOrder.RemainingQuantity + addedQuantity

		// 更新主单的备注信息
		remarkStr := *existingOrder.Remark
		if remark != "" {
			if remarkStr != "" {
				remarkStr += "; "
			}
			remarkStr += "追加商品: " + remark
		}

		// 更新存酒单
		err := tx.Model(&po.ProductStorageOrder{}).
			Where("order_no = ?", orderNo).
			Updates(map[string]interface{}{
				"total_items":        newTotalItems,
				"total_quantity":     newTotalQuantity,
				"remaining_quantity": newRemainingQuantity,
				"remark":             remarkStr,
				"operator_id":        operatorId,
				"operator_name":      operatorName,
				"utime":              time.Now().Unix(),
			}).Error
		if err != nil {
			return err
		}

		orderUpdated = true
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 如果订单已更新，返回更新后的存酒单完整信息
	if orderUpdated {
		return service.GetProductStorageOrderById(logCtx, *existingOrder.Id)
	}

	return nil, errors.New("添加商品失败")
}

// GetOrdersByOrderNos 批量根据订单号获取存酒单
func (service *ProductStorageOrderService) GetOrdersByOrderNos(logCtx *gin.Context, orderNos []string) ([]*po.ProductStorageOrder, error) {
	if len(orderNos) == 0 {
		return []*po.ProductStorageOrder{}, nil
	}

	query := dal.Use(model.DBSlave.Self)
	return query.ProductStorageOrder.WithContext(logCtx).Where(query.ProductStorageOrder.OrderNo.In(orderNos...)).Find()
}
