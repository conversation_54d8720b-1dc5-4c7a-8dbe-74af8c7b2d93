package impl

import (
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

func (service *PayService) QueryPaidOrderVOsBySessionId(logCtx *gin.Context, venueId, sessionId string) (int64, error) {
	// 计算已支付金额
	payBills, _ := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	hasPaidAmount := int64(0)
	for _, payBill := range *payBills {
		if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL { // 正常支付
			hasPaidAmount += *payBill.TotalFee
		} else { // 退款
			hasPaidAmount -= *payBill.TotalFee
		}
	}
	return hasPaidAmount, nil
}

// 计算多个订单的费用 排除标记无效订单
func (service *PayService) CalculateManyFeeForTransferRoom(ctx *gin.Context, orderVOMarkDelete []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (
	totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {

	orderNOs := []string{}
	// 从orderVOs中查找对应订单的状态
	for _, orderVO := range orderVOMarkDelete {
		orderNOs = append(orderNOs, orderVO.OrderNo)
	}

	// 计算已支付金额
	payBills, _ := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	for _, payBill := range *payBills {
		if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL { // 正常支付
			paidAmount += *payBill.TotalFee - *payBill.ChangeAmount
		} else { // 退款
			paidAmount -= *payBill.TotalFee
		}
	}

	// 计算订单金额
	ordersAllDB, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	if len(*ordersAllDB) > 0 {
		no2OrderMap := map[string]po.Order{}
		for _, orderTmp := range *ordersAllDB {
			no2OrderMap[*orderTmp.OrderNo] = orderTmp
		}
		orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
		orderProducts, _ := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
		for _, orderRoomPlan := range *orderRoomPlans {
			// 过滤优先级高的omIds
			if util.InList(*orderRoomPlan.OrderNo, orderNOs) {
				continue
			}
			order := no2OrderMap[*orderRoomPlan.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalRoomFee += *orderRoomPlan.PayAmount
			} else { // 退款订单
				totalRoomFee -= *orderRoomPlan.PayAmount
			}
		}
		for _, orderProduct := range *orderProducts {
			// 过滤优先级高的opIds
			if util.InList(*orderProduct.OrderNo, orderNOs) {
				continue
			}
			order := no2OrderMap[*orderProduct.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalSupermarketFee += *orderProduct.PayAmount
			} else { // 退款订单
				totalSupermarketFee -= *orderProduct.PayAmount
			}
		}
	}
	totalFee = totalRoomFee + util.MaxNumber(totalSupermarketFee, lastMinimumCharge)
	unpaidAmount = totalFee - paidAmount
	if unpaidAmount < 0 {
		util.Wlog(ctx).Errorf("计算费用错误: %d < 0", unpaidAmount)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d < 0", unpaidAmount)
	}
	return
}

// 计算多个订单的费用 重复的部分优先用opsInDB，omsInDB替换
func (service *PayService) CalculateManyFeeForPay(ctx *gin.Context, totalFeeThis int64, opsInDB []vo.OrderProductVO, omsInDB []vo.OrderRoomPlanVO, orderVOs []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (
	totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {

	// 需要取出可能重复的 ops oms
	opIds := []string{}
	omIds := []string{}
	// 计算当前选定订单
	for _, op := range opsInDB {
		if op.Id != "" {
			opIds = append(opIds, op.Id)
		}
		// 从orderVOs中查找对应订单的状态
		for _, orderVO := range orderVOs {
			if orderVO.OrderNo == op.OrderNo {
				if orderVO.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
					totalSupermarketFee += op.PayAmount
				} else { // 退款订单
					totalSupermarketFee -= op.PayAmount
				}
				break
			}
		}
	}
	for _, om := range omsInDB {
		if om.Id != "" {
			omIds = append(omIds, om.Id)
		}
		// 从orderVOs中查找对应订单的状态
		for _, orderVO := range orderVOs {
			if orderVO.OrderNo == om.OrderNo {
				if orderVO.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
					totalRoomFee += om.PayAmount
				} else { // 退款订单
					totalRoomFee -= om.PayAmount
				}
				break
			}
		}
	}
	if totalFeeThis != totalRoomFee+util.MaxNumber(totalSupermarketFee, lastMinimumCharge) {
		util.Wlog(ctx).Errorf("计算费用错误: %d != %d + %d", totalFeeThis, totalRoomFee, totalSupermarketFee)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d != %d + %d", totalFeeThis, totalRoomFee, totalSupermarketFee)
	}

	// 计算已支付金额
	payBills, _ := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	for _, payBill := range *payBills {
		if *payBill.Status == _const.V2_PAY_BILL_STATUS_PAID {
			if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL { // 正常支付
				paidAmount += *payBill.TotalFee - *payBill.ChangeAmount
			} else { // 退款
				paidAmount -= *payBill.TotalFee
			}
		}
	}

	// 计算订单金额
	ordersAllDB, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	if len(*ordersAllDB) > 0 {
		no2OrderMap := map[string]po.Order{}
		for _, orderTmp := range *ordersAllDB {
			no2OrderMap[*orderTmp.OrderNo] = orderTmp
		}
		orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
		orderProducts, _ := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
		for _, orderRoomPlan := range *orderRoomPlans {
			// 过滤优先级高的omIds
			if util.InList(*orderRoomPlan.Id, omIds) {
				continue
			}
			order := no2OrderMap[*orderRoomPlan.OrderNo]
			// 跳过取消订单 转台时产生
			if order.MarkType != nil && *order.MarkType == _const.V2_ORDER_MARK_TYPE_CANCEL {
				continue
			}
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalRoomFee += *orderRoomPlan.PayAmount
			} else { // 退款订单
				totalRoomFee -= *orderRoomPlan.PayAmount
			}
		}
		for _, orderProduct := range *orderProducts {
			// 过滤优先级高的opIds
			if util.InList(*orderProduct.Id, opIds) {
				continue
			}
			order := no2OrderMap[*orderProduct.OrderNo]
			// 跳过取消订单 转台时产生
			if order.MarkType != nil && *order.MarkType == _const.V2_ORDER_MARK_TYPE_CANCEL {
				continue
			}
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalSupermarketFee += *orderProduct.PayAmount
			} else { // 退款订单
				totalSupermarketFee -= *orderProduct.PayAmount
			}
		}
	}
	totalFee = totalRoomFee + util.MaxNumber(totalSupermarketFee, lastMinimumCharge)
	unpaidAmount = totalFee - paidAmount
	if unpaidAmount < 0 {
		util.Wlog(ctx).Errorf("计算费用错误: %d < 0", unpaidAmount)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d < 0", unpaidAmount)
	}
	return
}

// 计算多个订单的费用 计算notindb + db
func (service *PayService) CalculateManyFee(ctx *gin.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (
	totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {

	// 计算当前选定订单
	for _, op := range opsNotInDB {
		totalSupermarketFee += op.PayAmount
	}
	for _, om := range omsNotInDB {
		totalRoomFee += om.PayAmount
	}
	if totalFeeThis != totalRoomFee+util.MaxNumber(totalSupermarketFee, lastMinimumCharge) {
		util.Wlog(ctx).Errorf("计算费用错误: %d != %d + %d", totalFeeThis, totalRoomFee, totalSupermarketFee)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d != %d + %d", totalFeeThis, totalRoomFee, totalSupermarketFee)
	}

	// 计算已支付金额
	payBills, _ := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	for _, payBill := range *payBills {
		if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL { // 正常支付
			paidAmount += *payBill.TotalFee - *payBill.ChangeAmount
		} else { // 退款
			paidAmount -= *payBill.TotalFee
		}
	}

	// 计算订单金额
	ordersAllDB, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	if len(*ordersAllDB) > 0 {
		no2OrderMap := map[string]po.Order{}
		for _, orderTmp := range *ordersAllDB {
			no2OrderMap[*orderTmp.OrderNo] = orderTmp
		}
		orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
		orderProducts, _ := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
		for _, orderRoomPlan := range *orderRoomPlans {
			order := no2OrderMap[*orderRoomPlan.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalRoomFee += *orderRoomPlan.PayAmount
			} else { // 退款订单
				totalRoomFee -= *orderRoomPlan.PayAmount
			}
		}
		for _, orderProduct := range *orderProducts {
			order := no2OrderMap[*orderProduct.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalSupermarketFee += *orderProduct.PayAmount
			} else { // 退款订单
				totalSupermarketFee -= *orderProduct.PayAmount
			}
		}
	}
	totalFee = totalRoomFee + util.MaxNumber(totalSupermarketFee, lastMinimumCharge)
	unpaidAmount = totalFee - paidAmount
	if unpaidAmount < 0 {
		util.Wlog(ctx).Errorf("计算费用错误: %d < 0", unpaidAmount)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d < 0", unpaidAmount)
	}
	return
}

// 计算多个订单的费用 计算notindb + db
func (service *PayService) CalculateManyFeeForAdditionalOrder(ctx *gin.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (
	totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {

	// 计算当前选定订单
	for _, op := range opsNotInDB {
		totalSupermarketFee += op.PayAmount
	}
	for _, om := range omsNotInDB {
		totalRoomFee += om.PayAmount
	}
	if totalFeeThis != totalRoomFee+totalSupermarketFee {
		util.Wlog(ctx).Errorf("计算费用错误: %d != %d + %d", totalFeeThis, totalRoomFee, totalSupermarketFee)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d != %d + %d", totalFeeThis, totalRoomFee, totalSupermarketFee)
	}

	// 计算已支付金额
	payBills, _ := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId})
	for _, payBill := range *payBills {
		if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL { // 正常支付
			paidAmount += *payBill.TotalFee - *payBill.ChangeAmount
		} else { // 退款
			paidAmount -= *payBill.TotalFee
		}
	}

	// 计算订单金额
	ordersAllDB, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	if len(*ordersAllDB) > 0 {
		no2OrderMap := map[string]po.Order{}
		for _, orderTmp := range *ordersAllDB {
			no2OrderMap[*orderTmp.OrderNo] = orderTmp
		}
		orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
		orderProducts, _ := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
		for _, orderRoomPlan := range *orderRoomPlans {
			order := no2OrderMap[*orderRoomPlan.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalRoomFee += *orderRoomPlan.PayAmount
			} else { // 退款订单
				totalRoomFee -= *orderRoomPlan.PayAmount
			}
		}
		for _, orderProduct := range *orderProducts {
			order := no2OrderMap[*orderProduct.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalSupermarketFee += *orderProduct.PayAmount
			} else { // 退款订单
				totalSupermarketFee -= *orderProduct.PayAmount
			}
		}
	}
	totalFee = totalRoomFee + util.MaxNumber(totalSupermarketFee, lastMinimumCharge)
	unpaidAmount = totalFee - paidAmount
	if unpaidAmount < 0 {
		util.Wlog(ctx).Errorf("计算费用错误: %d < 0", unpaidAmount)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d < 0", unpaidAmount)
	}
	return
}

// 计算多个订单的费用 计算Callback
func (service *PayService) CalculateManyFeeForCallback(ctx *gin.Context, sessionId string, venueId string, lastMinimumCharge int64) (
	totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {

	// 计算已支付金额
	payBills, _ := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId, Status: util.Ptr(_const.V2_PAY_BILL_STATUS_PAID)})
	for _, payBill := range *payBills {
		if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL { // 正常支付
			paidAmount += *payBill.TotalFee - *payBill.ChangeAmount
		} else { // 退款
			paidAmount -= *payBill.TotalFee
		}
	}

	// 计算订单金额
	ordersAllDB, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	if len(*ordersAllDB) > 0 {
		no2OrderMap := map[string]po.Order{}
		for _, orderTmp := range *ordersAllDB {
			no2OrderMap[*orderTmp.OrderNo] = orderTmp
		}
		orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId})
		orderProducts, _ := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId})
		for _, orderRoomPlan := range *orderRoomPlans {
			order := no2OrderMap[*orderRoomPlan.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalRoomFee += *orderRoomPlan.PayAmount
			} else { // 退款订单
				totalRoomFee -= *orderRoomPlan.PayAmount
			}
		}
		for _, orderProduct := range *orderProducts {
			order := no2OrderMap[*orderProduct.OrderNo]
			if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
				totalSupermarketFee += *orderProduct.PayAmount
			} else { // 退款订单
				totalSupermarketFee -= *orderProduct.PayAmount
			}
		}
	}
	totalFee = totalRoomFee + util.MaxNumber(totalSupermarketFee, lastMinimumCharge)
	unpaidAmount = totalFee - paidAmount
	if unpaidAmount < 0 {
		util.Wlog(ctx).Errorf("计算费用错误: %d < 0", unpaidAmount)
		return 0, 0, 0, 0, 0, fmt.Errorf("计算费用错误: %d < 0", unpaidAmount)
	}
	return
}

func (service *PayService) CalculateSessionTime(ctx *gin.Context, sessionId string, venueId string, session po.Session) (newEndTime int64, changed bool, err error) {
	orders, err := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId})
	if err != nil {
		return 0, false, err
	}
	roomOrderVOs := []vo.OrderVO{}
	orderNos := []string{}
	for _, order := range *orders {
		orderVO := orderTransfer.PoToVo(order)
		if *order.Type == _const.V2_ORDER_TYPE_ROOMPLAN {
			roomOrderVOs = append(roomOrderVOs, orderVO)
			util.AddListElement(&orderNos, *order.OrderNo)
		}
	}

	roomPlansTmp, err := orderRoomPlanService.FindOrderRoomPlanByOrderNos(ctx, venueId, sessionId, orderNos)
	if err != nil {
		return 0, false, err
	}
	orderNoToRoomPlanVOsMap := map[string][]vo.OrderRoomPlanVO{}
	for _, roomPlan := range *roomPlansTmp {
		tmp := orderRoomPlanTransfer.PoToVo(roomPlan)
		orderNoToRoomPlanVOsMap[*roomPlan.OrderNo] = append(orderNoToRoomPlanVOsMap[*roomPlan.OrderNo], tmp)
	}
	refundOrderNoToRoomPlanVOsMap := map[string][]vo.OrderVO{}
	for index, roomOrderVO := range roomOrderVOs {
		roomOrderVO.OrderRoomPlanVOs = orderNoToRoomPlanVOsMap[roomOrderVO.OrderNo]
		roomOrderVOs[index] = roomOrderVO
		if roomOrderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			refundOrderNoToRoomPlanVOsMap[roomOrderVO.POrderNo] = append(refundOrderNoToRoomPlanVOsMap[roomOrderVO.POrderNo], roomOrderVO)
		}
	}
	mergedOrderVOs := []vo.OrderVO{}
	for _, roomOrderVO := range roomOrderVOs {
		// 退款订单不参与计算
		if roomOrderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			continue
		}
		refundOrderVO := refundOrderNoToRoomPlanVOsMap[roomOrderVO.OrderNo]
		if len(refundOrderVO) <= 0 {
			mergedOrderVOs = append(mergedOrderVOs, roomOrderVO)
		}
	}

	mergedOrderRoomPlanVOs := []vo.OrderRoomPlanVO{}
	for _, roomPlanVO := range mergedOrderVOs {
		mergedOrderRoomPlanVOs = append(mergedOrderRoomPlanVOs, roomPlanVO.OrderRoomPlanVOs...)
	}

	maxEndTime := int64(0)
	for _, roomPlan := range mergedOrderRoomPlanVOs {
		if roomPlan.EndTime > maxEndTime {
			maxEndTime = roomPlan.EndTime
		}
	}
	if session.EndTime == nil || *session.EndTime != maxEndTime {
		return maxEndTime, true, nil
	}
	return *session.EndTime, false, nil
}

func (service *PayService) GetOrdersInfoByOrderNos(ctx *gin.Context, orderNos []string, venueId string, sessionId string) ([]vo.OrderVO, error) {
	orders, _ := orderService.FindAllOrder(ctx, &req.QueryOrderReqDto{SessionId: &sessionId, VenueId: &venueId, OrderNos: &orderNos})
	if len(*orders) <= 0 {
		return nil, fmt.Errorf("订单信息不存在")
	}
	payBills, _ := payBillService.FindAllPayBill(ctx, &req.QueryPayBillReqDto{SessionId: &sessionId, VenueId: &venueId, IsBack: util.Ptr(false)})
	if len(*payBills) > 0 {
		paidPayIds := []string{}
		for _, payBill := range *payBills {
			if *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL && *payBill.Status == _const.V2_PAY_BILL_STATUS_PAID {
				paidPayIds = append(paidPayIds, *payBill.BillId)
			}
		}
		if len(paidPayIds) > 0 {
			orderAndPays, _ := orderAndPayService.FindAllOrderAndPay(ctx, &req.QueryOrderAndPayReqDto{SessionId: &sessionId, BillIdList: &paidPayIds})
			for _, orderAndPay := range *orderAndPays {
				if util.InList(*orderAndPay.OrderNo, orderNos) {
					return nil, fmt.Errorf("部分订单已支付，请重试")
				}
			}
		}
	}

	orderProducts, _ := orderProductService.FindAllOrderProduct(ctx, &req.QueryOrderProductReqDto{SessionId: &sessionId, VenueId: &venueId, OrderNos: &orderNos})
	orderRoomPlans, _ := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &req.QueryOrderRoomPlanReqDto{SessionId: &sessionId, VenueId: &venueId, OrderNos: &orderNos})
	orderVOs := []vo.OrderVO{}
	for _, order := range *orders {
		orderVO := orderTransfer.PoToVo(order)
		for _, orderProduct := range *orderProducts {
			if *orderProduct.OrderNo == orderVO.OrderNo {
				orderVO.OrderProductVOs = append(orderVO.OrderProductVOs, orderProductTransfer.PoToVo(orderProduct))
			}
		}
		for _, orderRoomPlan := range *orderRoomPlans {
			if *orderRoomPlan.OrderNo == orderVO.OrderNo {
				orderVO.OrderRoomPlanVOs = append(orderVO.OrderRoomPlanVOs, orderRoomPlanTransfer.PoToVo(orderRoomPlan))
			}
		}
		orderVOs = append(orderVOs, orderVO)
	}
	return orderVOs, nil
}

// SaveOrderPayCallbackInfo 保存支付回调信息
func (service *PayService) SaveOrderPayCallbackInfo(logCtx *gin.Context, toUpdatePayBills []po.PayBill, toUpdatePayRecords []po.PayRecord, toUpdateOrders []po.Order) error {
	tx := model.DBMaster.Self.Begin()
	for _, toUpdatePayBill := range toUpdatePayBills {
		if err := payBillService.UpdatePayBillPartialWithTx(logCtx, &toUpdatePayBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, toUpdatePayRecord := range toUpdatePayRecords {
		if err := payRecordService.UpdatePayRecordPartialWithTx(logCtx, &toUpdatePayRecord, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SaveV3RefundByCashInfo(logCtx *gin.Context, orderProducts []po.OrderProduct, orders []po.Order, orderAndPays *[]po.OrderAndPay, toAddPayBill *[]po.PayBill, payRecord *[]po.PayRecord, toUpdateOrders []po.Order) error {
	tx := model.DBMaster.Self.Begin()
	for _, orderProduct := range orderProducts {
		if err := orderProductService.CreateOrderProductWithTx(logCtx, &orderProduct, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range orders {
		if err := orderService.CreateOrderWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, toUpdateOrder := range toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &toUpdateOrder, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if orderAndPays != nil {
		for _, orderAndPay := range *orderAndPays {
			if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if toAddPayBill != nil {
		for _, payBill := range *toAddPayBill {
			if err := payBillService.CreatePayBillWithTx(logCtx, &payBill, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if payRecord != nil {
		for _, payRecord := range *payRecord {
			if err := payRecordService.CreatePayRecordWithTx(logCtx, &payRecord, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SaveRefundAllOrderBySessionIdInfo(logCtx *gin.Context, vos vo.OrderPOInfoUnionVO) error {
	tx := model.DBMaster.Self.Begin()

	for _, v := range vos.Orders {
		if err := orderService.CreateOrderWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range vos.OrderProducts {
		if err := orderProductService.CreateOrderProductWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range vos.OrderRoomPlans {
		if err := orderRoomPlanService.CreateOrderRoomPlanWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range vos.OrderAndPays {
		if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range vos.PayBills {
		if err := payBillService.CreatePayBillWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range vos.PayRecords {
		if err := payRecordService.CreatePayRecordWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

// SavePayBillBack 保存还原账单
func (service *PayService) SavePayBillBack(logCtx *gin.Context, payBillPOInfoBackVOs []vo.PayBillPOInfoBackVO) error {
	tx := model.DBMaster.Self.Begin()
	for _, payBillPOInfoBackVO := range payBillPOInfoBackVOs {
		if err := payBillService.CreatePayBillWithTx(logCtx, &payBillPOInfoBackVO.PayBillPO, tx); err != nil {
			tx.Rollback()
			return err
		}
		for _, payRecordPO := range payBillPOInfoBackVO.PayRecordPOs {
			if err := payRecordService.CreatePayRecordWithTx(logCtx, &payRecordPO, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
		for _, orderAndPayPO := range payBillPOInfoBackVO.OrderAndPayPOs {
			if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPayPO, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

// SaveBillBackInfo 保存还原账单
func (service *PayService) SaveBillBackInfo(logCtx *gin.Context, toUpdateOrders []po.Order, toAddOrderAndPays []po.OrderAndPay, toAddPayBill []po.PayBill, toAddPayRecord []po.PayRecord) error {
	tx := model.DBMaster.Self.Begin()

	for _, order := range toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderAndPay := range toAddOrderAndPays {
		if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payBill := range toAddPayBill {
		if err := payBillService.CreatePayBillWithTx(logCtx, &payBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payRecord := range toAddPayRecord {
		if err := payRecordService.CreatePayRecordWithTx(logCtx, &payRecord, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SavePayBillInfoBillBack(logCtx *gin.Context, newPayBills []po.PayBill, newPayRecords []po.PayRecord, updateOrders []po.Order, updatePayBills []po.PayBill, updateOrderProducts []po.OrderProduct, updateOrderRoomPlans []po.OrderRoomPlan) error {
	tx := model.DBMaster.Self.Begin()

	for _, payBill := range newPayBills {
		if err := payBillService.CreatePayBillWithTx(logCtx, &payBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payRecord := range newPayRecords {
		if err := payRecordService.CreatePayRecordWithTx(logCtx, &payRecord, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range updateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payBill := range updatePayBills {
		if err := payBillService.UpdatePayBillPartialWithTx(logCtx, &payBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderProduct := range updateOrderProducts {
		if err := orderProductService.UpdateOrderProductPartialWithTx(logCtx, &orderProduct, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderRoomPlan := range updateOrderRoomPlans {
		if err := orderRoomPlanService.UpdateOrderRoomPlanPartialWithTx(logCtx, &orderRoomPlan, tx); err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SavePayBillInfoCancelOrderOpen(logCtx *gin.Context, updateOrders []po.Order, toUpdatePayBills []po.PayBill, newPayBills []po.PayBill, newPayRecords []po.PayRecord, newOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan, newOrderAndPays []po.OrderAndPay, toUpdateSessions []po.Session) error {
	tx := model.DBMaster.Self.Begin()

	for _, order := range updateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payBill := range toUpdatePayBills {
		if err := payBillService.UpdatePayBillPartialWithTx(logCtx, &payBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payBill := range newPayBills {
		if err := payBillService.CreatePayBillWithTx(logCtx, &payBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, payRecord := range newPayRecords {
		if err := payRecordService.CreatePayRecordWithTx(logCtx, &payRecord, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range newOrders {
		if err := orderService.CreateOrderWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderProduct := range newOrderProducts {
		if err := orderProductService.CreateOrderProductWithTx(logCtx, &orderProduct, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderRoomPlan := range newOrderRoomPlans {
		if err := orderRoomPlanService.CreateOrderRoomPlanWithTx(logCtx, &orderRoomPlan, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderAndPay := range newOrderAndPays {
		if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, session := range toUpdateSessions {
		if err := sessionService.UpdateSessionPartialWithTx(logCtx, &session, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SaveCallbackCancelOrderOpen(logCtx *gin.Context, toUpdateSession []po.Session, toUpdateRoom []po.Room) error {
	tx := model.DBMaster.Self.Begin()

	for _, session := range toUpdateSession {
		if err := sessionService.UpdateSessionPartialWithTx(logCtx, &session, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, room := range toUpdateRoom {
		if err := roomService.UpdateRoomPartialWithTx(logCtx, &room, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SaveV3RefundByCashInfoRoomPlan(logCtx *gin.Context, roomPlans []po.OrderRoomPlan, orders []po.Order, orderAndPays *[]po.OrderAndPay, toAddPayBill *[]po.PayBill, payRecord *[]po.PayRecord, toUpdateOrders []po.Order, toUpdateSessions []po.Session) error {
	tx := model.DBMaster.Self.Begin()
	for _, roomPlan := range roomPlans {
		if err := orderRoomPlanService.CreateOrderRoomPlanWithTx(logCtx, &roomPlan, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range orders {
		if err := orderService.CreateOrderWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, toUpdateOrder := range toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &toUpdateOrder, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if orderAndPays != nil {
		for _, orderAndPay := range *orderAndPays {
			if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if toAddPayBill != nil {
		for _, payBill := range *toAddPayBill {
			if err := payBillService.CreatePayBillWithTx(logCtx, &payBill, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if payRecord != nil {
		for _, payRecord := range *payRecord {
			if err := payRecordService.CreatePayRecordWithTx(logCtx, &payRecord, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if toUpdateSessions != nil {
		for _, session := range toUpdateSessions {
			if err := sessionService.UpdateSessionPartialWithTx(logCtx, &session, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}
