package impl

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	_const "voderpltvv/const"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MemberCardConsumeService struct {
}

var memberCardConsumeService MemberCardConsumeService = MemberCardConsumeService{}

var memberCardConsumeTransfer = transfer.MemberCardConsumeTransfer{}

func (service *MemberCardConsumeService) CreateMemberCardConsume(logCtx *gin.Context, memberCardConsume *po.MemberCardConsume) error {
	return Save(memberCardConsume)
}

func (service *MemberCardConsumeService) CreateMemberCardConsumeWithTx(logCtx *gin.Context, memberCardConsume *po.MemberCardConsume, tx *gorm.DB) error {
	return SaveWithTx(memberCardConsume, tx)
}

func (service *MemberCardConsumeService) UpdateMemberCardConsume(logCtx *gin.Context, memberCardConsume *po.MemberCardConsume) error {
	return Update(memberCardConsume)
}

func (service *MemberCardConsumeService) UpdateMemberCardConsumePartial(logCtx *gin.Context, memberCardConsume *po.MemberCardConsume) error {
	return UpdateNotNull(memberCardConsume)
}

func (service *MemberCardConsumeService) UpdateMemberCardConsumePartialWithTx(logCtx *gin.Context, memberCardConsume *po.MemberCardConsume, tx *gorm.DB) error {
	return UpdateNotNullWithTx(memberCardConsume, tx)
}

func (service *MemberCardConsumeService) DeleteMemberCardConsume(logCtx *gin.Context, id string) error {
	return Delete(po.MemberCardConsume{Id: &id})
}

func (service *MemberCardConsumeService) FindMemberCardConsumeById(logCtx *gin.Context, id string) (memberCardConsume *po.MemberCardConsume, err error) {
	memberCardConsume = &po.MemberCardConsume{}
	err = model.DBMaster.Self.Where("id=?", id).First(memberCardConsume).Error
	return
}

func (service *MemberCardConsumeService) FindAllMemberCardConsume(logCtx *gin.Context, reqDto *req.QueryMemberCardConsumeReqDto) (list *[]po.MemberCardConsume, err error) {
	db := model.DBSlave.Self.Model(&po.MemberCardConsume{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.VenueName != nil && *reqDto.VenueName != "" {
		db = db.Where("venue_name=?", *reqDto.VenueName)
	}
	if reqDto.RoomId != nil && *reqDto.RoomId != "" {
		db = db.Where("room_id=?", *reqDto.RoomId)
	}
	if reqDto.RoomName != nil && *reqDto.RoomName != "" {
		db = db.Where("room_name=?", *reqDto.RoomName)
	}
	if reqDto.EmployeeId != nil && *reqDto.EmployeeId != "" {
		db = db.Where("employee_id=?", *reqDto.EmployeeId)
	}
	if reqDto.EmployeeName != nil && *reqDto.EmployeeName != "" {
		db = db.Where("employee_name=?", *reqDto.EmployeeName)
	}
	if reqDto.EmployeePhone != nil && *reqDto.EmployeePhone != "" {
		db = db.Where("employee_phone=?", *reqDto.EmployeePhone)
	}
	if reqDto.MemberId != nil && *reqDto.MemberId != "" {
		db = db.Where("member_id=?", *reqDto.MemberId)
	}
	if reqDto.MemberCardId != nil && *reqDto.MemberCardId != "" {
		db = db.Where("member_card_id=?", *reqDto.MemberCardId)
	}
	if reqDto.MemberCardNumber != nil && *reqDto.MemberCardNumber != "" {
		db = db.Where("member_card_number=?", *reqDto.MemberCardNumber)
	}
	if reqDto.MemberCardName != nil && *reqDto.MemberCardName != "" {
		db = db.Where("member_card_name=?", *reqDto.MemberCardName)
	}
	if reqDto.SessionId != nil && *reqDto.SessionId != "" {
		db = db.Where("session_id=?", *reqDto.SessionId)
	}
	if reqDto.BillId != nil && *reqDto.BillId != "" {
		db = db.Where("bill_id=?", *reqDto.BillId)
	}
	if reqDto.BillPid != nil && *reqDto.BillPid != "" {
		db = db.Where("bill_pid=?", *reqDto.BillPid)
	}
	if reqDto.OriginalFee != nil && *reqDto.OriginalFee > 0 {
		db = db.Where("original_fee=?", *reqDto.OriginalFee)
	}
	if reqDto.ShouldFee != nil && *reqDto.ShouldFee > 0 {
		db = db.Where("should_fee=?", *reqDto.ShouldFee)
	}
	if reqDto.TotalFee != nil && *reqDto.TotalFee > 0 {
		db = db.Where("total_fee=?", *reqDto.TotalFee)
	}
	if reqDto.ZeroFee != nil && *reqDto.ZeroFee > 0 {
		db = db.Where("zero_fee=?", *reqDto.ZeroFee)
	}
	if reqDto.Direction != nil && *reqDto.Direction != "" {
		db = db.Where("direction=?", *reqDto.Direction)
	}
	if reqDto.Status != nil && *reqDto.Status != "" {
		db = db.Where("status=?", *reqDto.Status)
	}
	if reqDto.IsBack != nil {
		db = db.Where("is_back=?", *reqDto.IsBack)
	}
	if reqDto.PayId != nil && *reqDto.PayId != "" {
		db = db.Where("pay_id=?", *reqDto.PayId)
	}
	if reqDto.PayPid != nil && *reqDto.PayPid != "" {
		db = db.Where("pay_pid=?", *reqDto.PayPid)
	}
	if reqDto.PayRecordTotalAmout != nil && *reqDto.PayRecordTotalAmout > 0 {
		db = db.Where("pay_record_total_amout=?", *reqDto.PayRecordTotalAmout)
	}
	if reqDto.MemberCardBalance != nil && *reqDto.MemberCardBalance > 0 {
		db = db.Where("member_card_balance=?", *reqDto.MemberCardBalance)
	}
	if reqDto.Remark != nil && *reqDto.Remark != "" {
		db = db.Where("remark=?", *reqDto.Remark)
	}
	if reqDto.Ctime != nil && *reqDto.Ctime > 0 {
		db = db.Where("ctime=?", *reqDto.Ctime)
	}
	if reqDto.Utime != nil && *reqDto.Utime > 0 {
		db = db.Where("utime=?", *reqDto.Utime)
	}
	if reqDto.State != nil {
		db = db.Where("state=?", *reqDto.State)
	}
	if reqDto.Version != nil {
		db = db.Where("version=?", *reqDto.Version)
	}

	db = db.Order("ctime desc")
	list = &[]po.MemberCardConsume{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *MemberCardConsumeService) FindAllMemberCardConsumeWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberCardConsumeReqDto) (list *[]po.MemberCardConsume, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.MemberCardConsume{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.VenueId != nil && *reqDto.VenueId != "" {
		db = db.Where("venue_id=?", *reqDto.VenueId)
	}
	if reqDto.VenueName != nil && *reqDto.VenueName != "" {
		db = db.Where("venue_name=?", *reqDto.VenueName)
	}
	if reqDto.RoomId != nil && *reqDto.RoomId != "" {
		db = db.Where("room_id=?", *reqDto.RoomId)
	}
	if reqDto.RoomName != nil && *reqDto.RoomName != "" {
		db = db.Where("room_name=?", *reqDto.RoomName)
	}
	if reqDto.EmployeeId != nil && *reqDto.EmployeeId != "" {
		db = db.Where("employee_id=?", *reqDto.EmployeeId)
	}
	if reqDto.EmployeeName != nil && *reqDto.EmployeeName != "" {
		db = db.Where("employee_name=?", *reqDto.EmployeeName)
	}
	if reqDto.EmployeePhone != nil && *reqDto.EmployeePhone != "" {
		db = db.Where("employee_phone=?", *reqDto.EmployeePhone)
	}
	if reqDto.MemberId != nil && *reqDto.MemberId != "" {
		db = db.Where("member_id=?", *reqDto.MemberId)
	}
	if reqDto.MemberCardId != nil && *reqDto.MemberCardId != "" {
		db = db.Where("member_card_id=?", *reqDto.MemberCardId)
	}
	if reqDto.MemberCardNumber != nil && *reqDto.MemberCardNumber != "" {
		db = db.Where("member_card_number=?", *reqDto.MemberCardNumber)
	}
	if reqDto.MemberCardName != nil && *reqDto.MemberCardName != "" {
		db = db.Where("member_card_name=?", *reqDto.MemberCardName)
	}
	if reqDto.SessionId != nil && *reqDto.SessionId != "" {
		db = db.Where("session_id=?", *reqDto.SessionId)
	}
	if reqDto.BillId != nil && *reqDto.BillId != "" {
		db = db.Where("bill_id=?", *reqDto.BillId)
	}
	if reqDto.BillPid != nil && *reqDto.BillPid != "" {
		db = db.Where("bill_pid=?", *reqDto.BillPid)
	}
	if reqDto.OriginalFee != nil && *reqDto.OriginalFee > 0 {
		db = db.Where("original_fee=?", *reqDto.OriginalFee)
	}
	if reqDto.ShouldFee != nil && *reqDto.ShouldFee > 0 {
		db = db.Where("should_fee=?", *reqDto.ShouldFee)
	}
	if reqDto.TotalFee != nil && *reqDto.TotalFee > 0 {
		db = db.Where("total_fee=?", *reqDto.TotalFee)
	}
	if reqDto.ZeroFee != nil && *reqDto.ZeroFee > 0 {
		db = db.Where("zero_fee=?", *reqDto.ZeroFee)
	}
	if reqDto.Direction != nil && *reqDto.Direction != "" {
		db = db.Where("direction=?", *reqDto.Direction)
	}
	if reqDto.Status != nil && *reqDto.Status != "" {
		db = db.Where("status=?", *reqDto.Status)
	}
	if reqDto.IsBack != nil {
		db = db.Where("is_back=?", *reqDto.IsBack)
	}
	if reqDto.PayId != nil && *reqDto.PayId != "" {
		db = db.Where("pay_id=?", *reqDto.PayId)
	}
	if reqDto.PayPid != nil && *reqDto.PayPid != "" {
		db = db.Where("pay_pid=?", *reqDto.PayPid)
	}
	if reqDto.PayRecordTotalAmout != nil && *reqDto.PayRecordTotalAmout > 0 {
		db = db.Where("pay_record_total_amout=?", *reqDto.PayRecordTotalAmout)
	}
	if reqDto.MemberCardBalance != nil && *reqDto.MemberCardBalance > 0 {
		db = db.Where("member_card_balance=?", *reqDto.MemberCardBalance)
	}
	if reqDto.Remark != nil && *reqDto.Remark != "" {
		db = db.Where("remark=?", *reqDto.Remark)
	}
	if reqDto.Ctime != nil && *reqDto.Ctime > 0 {
		db = db.Where("ctime=?", *reqDto.Ctime)
	}
	if reqDto.Utime != nil && *reqDto.Utime > 0 {
		db = db.Where("utime=?", *reqDto.Utime)
	}
	if reqDto.State != nil {
		db = db.Where("state=?", *reqDto.State)
	}
	if reqDto.Version != nil {
		db = db.Where("version=?", *reqDto.Version)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.MemberCardConsume{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

// ConvertToMemberCardConsumeVO 转换为会员卡消费记录VO
func (service *MemberCardConsumeService) ConvertToMemberCardConsumeVO(ctx *gin.Context, memberCardConsume po.MemberCardConsume) vo.MemberCardConsumeVO {
	return memberCardConsumeTransfer.PoToVo(memberCardConsume)
}

// ConvertToMember 转换为会员PO
func (service *MemberCardConsumeService) ConvertToMemberCardConsume(ctx *gin.Context, memberCardConsumeVO vo.MemberCardConsumeVO) po.MemberCardConsume {
	return memberCardConsumeTransfer.VoToPo(memberCardConsumeVO)
}

// FindMemberCardConsumeByVenueIdAndEmployeeId 根据场馆ID和员工ID查询会员卡消费记录
func (service *MemberCardConsumeService) FindMemberCardConsumeByVenueIdAndEmployeeId(ctx *gin.Context, venueId string, employeeId string, startTime int64) (list []po.MemberCardConsume, err error) {
	list = []po.MemberCardConsume{}
	db := model.DBSlave.Self.Model(&po.MemberCardConsume{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("employee_id=?", employeeId)
	db = db.Where("ctime>=?", startTime)
	db = db.Where("biz_type in ?", []string{_const.V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_CONSUME, _const.V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_REFUND})
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}

// FindMemberCardConsumesByTimeRange 根据时间范围查询会员卡消费记录
func (service *MemberCardConsumeService) FindMemberCardConsumesRechargeByTimeRange(ctx *gin.Context, venueId string, startTime int64, endTime int64) (list []po.MemberCardConsume, err error) {
	list = []po.MemberCardConsume{}
	db := model.DBSlave.Self.Model(&po.MemberCardConsume{})
	db = db.Where("venue_id=?", venueId)
	db = db.Where("ctime>=?", startTime)
	db = db.Where("ctime<=?", endTime)
	db = db.Where("biz_type in ?", []string{_const.V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_CONSUME, _const.V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_REFUND})
	db = db.Order("ctime desc")
	err = db.Find(&list).Error
	return
}