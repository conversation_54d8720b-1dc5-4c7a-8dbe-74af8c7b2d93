package impl

import (
	"errors"
	"strings"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func (service *OrderService) SendSmsMemberCardOpen(ctx *gin.Context, venue *po.Venue, memberCard *po.MemberCard) error {
	defer func() {
		if r := recover(); r != nil {
			logrus.Error("SendSmsMemberCardOpen panic: ", r)
		}
	}()
	if venue == nil || memberCard == nil {
		return errors.New("venue or memberCard is nil")
	}
	if venue.Name == nil || *venue.Name == "" {
		return errors.New("venue.Name is nil")
	}
	if memberCard.CardNumber == nil || *memberCard.CardNumber == "" {
		return errors.New("memberCard.CardNumber is nil")
	}

	context := "【#店铺名称#】尊敬的会员，您的会员卡开卡成功！会员卡号#会员卡号#，初始储值金额#初始金额#元，当前卡内余额#当前余额#元。感谢您成为我们的会员，期待为您提供优质服务！"
	context = strings.Replace(context, "#店铺名称#", *venue.Name, 1)
	context = strings.Replace(context, "#会员卡号#", *memberCard.CardNumber, 1)
	principalBalance := util.GetPtrSafeDefault(memberCard.PrincipalBalance, 0)
	goodsBonusBalance := util.GetPtrSafeDefault(memberCard.GoodsBonusBalance, 0)
	roomBonusBalance := util.GetPtrSafeDefault(memberCard.RoomBonusBalance, 0)
	commonBonusBalance := util.GetPtrSafeDefault(memberCard.CommonBonusBalance, 0)
	totalBonusBalance := principalBalance + goodsBonusBalance + roomBonusBalance + commonBonusBalance
	context = strings.Replace(context, "#初始金额#", util.FenToYuanString(principalBalance), 1)
	context = strings.Replace(context, "#当前余额#", util.FenToYuanString(totalBonusBalance), 1)
	// todo 调用发短信功功能
	logrus.Println("SendSmsMemberCardOpen: ", context)
	return nil
}

func (service *OrderService) SendSmsMemberCardRecharge(ctx *gin.Context, venue *po.Venue, memberCard *po.MemberCard, currentTotalAmount int64, currentPrincipalAmount int64) error {
	defer func() {
		if r := recover(); r != nil {
			logrus.Error("SendSmsMemberCardRecharge panic: ", r)
		}
	}()
	if venue == nil || memberCard == nil {
		return errors.New("venue or memberCard is nil")
	}
	if venue.Name == nil || *venue.Name == "" {
		return errors.New("venue.Name is nil")
	}
	if memberCard.CardNumber == nil || *memberCard.CardNumber == "" {
		return errors.New("memberCard.CardNumber is nil")
	}

	context := "【#店铺名称#】您的会员卡储值成功！本次储值金额#充值金额#元，当前卡内储值余额#卡内余额#元，余额总计#总余额#元。感谢您的充值，祝您消费愉快！"
	context = strings.Replace(context, "#店铺名称#", *venue.Name, 1)
	principalBalance := util.GetPtrSafeDefault(memberCard.PrincipalBalance, 0)
	goodsBonusBalance := util.GetPtrSafeDefault(memberCard.GoodsBonusBalance, 0)
	roomBonusBalance := util.GetPtrSafeDefault(memberCard.RoomBonusBalance, 0)
	commonBonusBalance := util.GetPtrSafeDefault(memberCard.CommonBonusBalance, 0)
	totalBonusBalance := principalBalance + goodsBonusBalance + roomBonusBalance + commonBonusBalance
	context = strings.Replace(context, "#充值金额#", util.FenToYuanString(currentPrincipalAmount), 1)
	context = strings.Replace(context, "#卡内余额#", util.FenToYuanString(principalBalance), 1)
	context = strings.Replace(context, "#总余额#", util.FenToYuanString(totalBonusBalance), 1)
	// todo 调用发短信功功能
	logrus.Println("SendSmsMemberCardRecharge: ", context)
	return nil
}

func (service *OrderService) SendSmsMemberCardConsume(ctx *gin.Context, venue *po.Venue, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error {
	defer func() {
		if r := recover(); r != nil {
			logrus.Error("SendSmsMemberCardConsume panic: ", r)
		}
	}()
	memberCard, err := memberCardService.FindById(ctx, memberCardId)
	if err != nil {
		return errors.New("memberCard is nil")
	}
	if venue == nil || memberCard.Id == nil {
		return errors.New("venue or memberCard.Id is nil")
	}
	if venue.Name == nil || *venue.Name == "" {
		return errors.New("venue.Name is nil")
	}
	if memberCard.CardNumber == nil || *memberCard.CardNumber == "" {
		return errors.New("memberCard.CardNumber is nil")
	}

	context := "【#店铺名称#】尊敬的会员，您本次消费金额#消费金额#元。#积分信息#剩余#剩余储值#元，余额总计#总余额#元。谢谢惠顾，欢迎下次光临！"
	context = strings.Replace(context, "#店铺名称#", *venue.Name, 1)
	currentTotalAmount := currentPrincipalAmount + currentRoomBonusAmount + currentGoodsBonusAmount + currentCommonBonusAmount
	context = strings.Replace(context, "#消费金额#", util.FenToYuanString(currentTotalAmount), 1)
	context = strings.Replace(context, "#积分信息#", "", 1)
	principalBalance := util.GetPtrSafeDefault(memberCard.PrincipalBalance, 0)
	goodsBonusBalance := util.GetPtrSafeDefault(memberCard.GoodsBonusBalance, 0)
	roomBonusBalance := util.GetPtrSafeDefault(memberCard.RoomBonusBalance, 0)
	commonBonusBalance := util.GetPtrSafeDefault(memberCard.CommonBonusBalance, 0)
	totalBonusBalance := principalBalance + goodsBonusBalance + roomBonusBalance + commonBonusBalance
	context = strings.Replace(context, "#剩余储值#", util.FenToYuanString(principalBalance), 1)
	context = strings.Replace(context, "#总余额#", util.FenToYuanString(totalBonusBalance), 1)
	// todo 调用发短信功功能
	logrus.Println("SendSmsMemberCardConsume: ", context)
	return nil
}

func (service *OrderService) SendSmsMemberCardRefund(ctx *gin.Context, venueId string, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error {
	defer func() {
		if r := recover(); r != nil {
			logrus.Error("SendSmsMemberCardRefund panic: ", r)
		}
	}()
	venue, err := venueService.FindVenueById(ctx, venueId)
	if err != nil {
		return errors.New("venue is nil")
	}
	memberCard, err := memberCardService.FindById(ctx, memberCardId)
	if err != nil {
		return errors.New("memberCard is nil")
	}
	if venue == nil || memberCard.Id == nil {
		return errors.New("venue or memberCard.Id is nil")
	}
	if venue.Name == nil || *venue.Name == "" {
		return errors.New("venue.Name is nil")
	}
	if memberCard.CardNumber == nil || *memberCard.CardNumber == "" {
		return errors.New("memberCard.CardNumber is nil")
	}

	context := "【#店铺名称#】尊敬的会员，您本次退款金额#退款金额#元。#积分信息#剩余#剩余储值#元，余额总计#总余额#元。谢谢惠顾，欢迎下次光临！"
	context = strings.Replace(context, "#店铺名称#", *venue.Name, 1)
	currentTotalAmount := currentPrincipalAmount + currentRoomBonusAmount + currentGoodsBonusAmount + currentCommonBonusAmount
	context = strings.Replace(context, "#退款金额#", util.FenToYuanString(currentTotalAmount), 1)
	context = strings.Replace(context, "#积分信息#", "", 1)
	principalBalance := util.GetPtrSafeDefault(memberCard.PrincipalBalance, 0)
	goodsBonusBalance := util.GetPtrSafeDefault(memberCard.GoodsBonusBalance, 0)
	roomBonusBalance := util.GetPtrSafeDefault(memberCard.RoomBonusBalance, 0)
	commonBonusBalance := util.GetPtrSafeDefault(memberCard.CommonBonusBalance, 0)
	totalBonusBalance := principalBalance + goodsBonusBalance + roomBonusBalance + commonBonusBalance
	context = strings.Replace(context, "#剩余储值#", util.FenToYuanString(principalBalance), 1)
	context = strings.Replace(context, "#总余额#", util.FenToYuanString(totalBonusBalance), 1)
	// todo 调用发短信功功能
	logrus.Println("SendSmsMemberCardConsume: ", context)
	return nil
}
