package impl

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// ProductWithdrawService 取酒服务 (合并后的服务)
type ProductWithdrawService struct {
}

// 初始化DAO
func NewProductWithdrawService() *ProductWithdrawService {
	return &ProductWithdrawService{}
}

// BatchAddProductWithdraw 批量添加酒水提取记录（独立提取，不关联取酒单）
func (service *ProductWithdrawService) BatchAddProductWithdraw(reqDto *req.BatchAddProductWithdrawReqDto) ([]*po.ProductWithdraw, error) {
	if reqDto == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// 返回结果
	withdrawRecords := make([]*po.ProductWithdraw, 0, len(reqDto.WithdrawItems))

	// 使用事务
	err := model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		ctx := context.Background()
		db := dal.Use(tx)

		for _, item := range reqDto.WithdrawItems {
			// 1. 查询存储记录（行锁）
			storage, err := db.ProductStorage.WithContext(ctx).
				Where(db.ProductStorage.Id.Eq(*item.StorageId)).
				First()
			if err != nil {
				return fmt.Errorf("查询存储记录失败:%w", err)
			}

			// 校验状态与库存
			if storage.State == nil || *storage.State != _const.StorageStateNormal {
				return fmt.Errorf("存酒记录状态异常, id=%s", *storage.Id)
			}
			if storage.RemainingQty == nil || *storage.RemainingQty < *item.Quantity {
				return fmt.Errorf("库存不足, id=%s", *storage.Id)
			}

			// 2. 更新库存
			newRemaining := *storage.RemainingQty - *item.Quantity
			utime := time.Now().Unix()

			updateMap := map[string]interface{}{
				"remaining_qty":       newRemaining,
				"last_operation_time": utime,
				"utime":               utime,
			}

			info, err := db.ProductStorage.WithContext(ctx).
				Where(db.ProductStorage.Id.Eq(*storage.Id)).
				Updates(updateMap)
			if err != nil {
				return fmt.Errorf("更新库存失败:%w", err)
			}
			if info.RowsAffected == 0 {
				return fmt.Errorf("更新库存未影响行, id=%s", *storage.Id)
			}

			// 2.1 根据剩余数量更新状态字段
			if newRemaining == 0 || newRemaining < *storage.Quantity {
				statusMap := map[string]interface{}{}
				if newRemaining == 0 {
					statusMap["status_code"] = _const.StatusCodeWithdrawn
					statusMap["status_name"] = _const.StatusNameWithdrawn
				} else {
					statusMap["status_code"] = _const.StatusCodePartial
					statusMap["status_name"] = _const.StatusNamePartial
				}
				if reqDto.OperatorId != nil {
					statusMap["operator_id"] = *reqDto.OperatorId
				}
				if reqDto.OperatorName != nil {
					statusMap["operator_name"] = *reqDto.OperatorName
				}
				statusMap["utime"] = utime
				_, err = db.ProductStorage.WithContext(ctx).
					Where(db.ProductStorage.Id.Eq(*storage.Id)).
					Updates(statusMap)
				if err != nil {
					return fmt.Errorf("更新状态失败:%w", err)
				}
			}

			// 3. 创建提取记录
			withdraw := &po.ProductWithdraw{
				StorageId:        item.StorageId,
				StorageOrderNo:   item.StorageOrderNo,
				VenueId:          reqDto.VenueId,
				DeliveryRoomId:   reqDto.DeliveryRoomId,
				DeliveryRoomName: reqDto.DeliveryRoomName,
				CustomerId:       storage.CustomerId,
				CustomerName:     storage.CustomerName,
				ProductId:        storage.ProductId,
				ProductName:      storage.ProductName,
				ProductUnit:      item.ProductUnit,
				ProductSpec:      item.ProductSpec,
				Quantity:         item.Quantity,
				WithdrawTime:     reqDto.WithdrawTime,
				OperatorId:       reqDto.OperatorId,
				OperatorName:     reqDto.OperatorName,
				Remark:           reqDto.Remark,
				StorageLocation:  storage.StorageLocation,
			}

			// 取酒单号
			orderNo := "PD" + (*reqDto.VenueId)[len(*reqDto.VenueId)-8:] + time.Now().Format("060102150405") + fmt.Sprintf("%04d", rand.Intn(10000))
			withdraw.OrderNo = &orderNo

			if err := SaveWithTx(withdraw, tx); err != nil {
				return fmt.Errorf("保存取酒记录失败:%w", err)
			}

			withdrawRecords = append(withdrawRecords, withdraw)

			// 4. 更新存酒单剩余数量
			if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
				if err := service.updateStorageOrderRemainingQuantity(tx, *storage.ParentOrderNo); err != nil {
					return err
				}
			}

			// 5. 记录操作日志
			opLogService := NewProductStorageOperationLogService()
			var operationTime int64
			if reqDto.WithdrawTime != nil && *reqDto.WithdrawTime > 0 {
				operationTime = *reqDto.WithdrawTime
			} else {
				operationTime = utime
			}
			originalQty := 0
			if storage.Quantity != nil {
				originalQty = *storage.Quantity
			}
			remark := fmt.Sprintf("取酒单号: %s，商品: %s，数量: %d，从 %d 减少到 %d，存放位置: %s", *withdraw.OrderNo, *storage.ProductName, *item.Quantity, originalQty, newRemaining, *storage.StorageLocation)

			// 取酒操作只设置送达包厅
			deliveryRoomName := ""
			if reqDto.DeliveryRoomName != nil && *reqDto.DeliveryRoomName != "" {
				deliveryRoomName = *reqDto.DeliveryRoomName
			}

			_, err = opLogService.CreateWithinTransactionWithRoom(
				tx,
				*storage.Id,
				*withdraw.OrderNo,
				"withdraw",
				"取酒",
				operationTime,
				*item.Quantity,
				lo.FromPtr(reqDto.OperatorId),
				lo.FromPtr(reqDto.OperatorName),
				newRemaining,
				remark,
				"",               // 取酒操作不设置寄存包厅
				deliveryRoomName, // 取酒操作只设置送达包厅
			)
			if err != nil {
				return fmt.Errorf("创建操作日志失败:%w", err)
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return withdrawRecords, nil
}

// CreateProductWithdrawOrder 创建取酒单及其明细
func (service *ProductWithdrawService) CreateProductWithdrawOrder(logCtx *gin.Context, reqDto *req.AddProductWithdrawOrderReqDto) (*vo.ProductWithdrawOrderVO, error) {
	if reqDto == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// 获取当前时间作为默认值
	currentTime := time.Now().Unix()

	// 生成取酒单号
	venueId := reqDto.VenueId
	orderNo := "PW" + venueId + strings.ReplaceAll(fmt.Sprintf("%d", currentTime), "-", "")

	// 生成取酒单ID
	withdrawOrderId := util.GetUUID()

	// 创建取酒单
	order := &po.ProductWithdrawOrder{
		Id:            &withdrawOrderId,
		OrderNo:       &orderNo,
		VenueId:       &venueId,
		CustomerId:    &reqDto.CustomerId,
		CustomerName:  &reqDto.CustomerName,
		PhoneNumber:   &reqDto.PhoneNumber,
		OrderNumber:   &reqDto.OrderNumber,
		WithdrawTime:  &reqDto.WithdrawTime,
		RoomId:        &reqDto.RoomId,
		RoomName:      &reqDto.RoomName,
		TotalItems:    util.GetItPtr(len(reqDto.Items)),
		TotalQuantity: util.GetItPtr(0), // 初始化为0，后面累加
		Remark:        &reqDto.Remark,
		OperatorId:    &reqDto.OperatorId,
		OperatorName:  &reqDto.OperatorName,
	}

	var totalQuantity int = 0
	var err error

	// 使用事务确保取酒单和明细一起创建
	err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 1. 保存取酒单
		if err := Save(order); err != nil {
			return err
		}

		// 创建临时Query对象用于事务
		tempQuery := dal.Use(tx)

		// 2. 保存取酒明细并更新对应的存酒记录
		for i, item := range reqDto.Items {
			// 生成明细单号
			detailOrderNo := orderNo + "-" + fmt.Sprintf("%03d", i+1)

			// 查询存酒记录
			storage, err := tempQuery.ProductStorage.Where(
				tempQuery.ProductStorage.Id.Eq(item.StorageId),
			).First()

			if err != nil {
				return fmt.Errorf("查询存酒记录失败: %s", err.Error())
			}

			if storage == nil {
				return fmt.Errorf("存酒记录不存在，ID: %s", item.StorageId)
			}

			// 检查商品状态
			if storage.State != nil && *storage.State != _const.StorageStateNormal {
				switch *storage.State {
				case _const.StorageStateDiscarded:
					return fmt.Errorf("商品[%s]已报废，无法取用", *storage.ProductName)
				case _const.StorageStateCancelled:
					return fmt.Errorf("商品[%s]已撤销，无法取用", *storage.ProductName)
				default:
					return fmt.Errorf("商品[%s]状态异常(%d)，无法取用", *storage.ProductName, *storage.State)
				}
			}

			// 检查剩余数量是否足够
			if *storage.RemainingQty < item.Quantity {
				return fmt.Errorf("商品[%s]剩余数量不足，要求: %d, 实际剩余: %d",
					*storage.ProductName, item.Quantity, *storage.RemainingQty)
			}

			// 保存取酒记录
			withdraw := &po.ProductWithdraw{
				Id:               util.GetItPtr(util.GetUUID()),
				OrderNo:          &detailOrderNo,
				ParentOrderNo:    &orderNo, // 关联主订单号
				StorageId:        &item.StorageId,
				StorageOrderNo:   storage.OrderNo,
				VenueId:          &venueId,
				CustomerId:       &reqDto.CustomerId,
				CustomerName:     &reqDto.CustomerName,
				OrderNumber:      &reqDto.OrderNumber,
				ProductId:        storage.ProductId,
				ProductName:      storage.ProductName,
				ProductUnit:      storage.ProductUnit,
				ProductSpec:      storage.ProductSpec,
				Quantity:         &item.Quantity,
				WithdrawTime:     &reqDto.WithdrawTime,
				DeliveryRoomId:   &reqDto.RoomId,   // Correct: Read from reqDto.RoomId (DTO still uses RoomId)
				DeliveryRoomName: &reqDto.RoomName, // Correct: Read from reqDto.RoomName (DTO still uses RoomName)
				StorageLocation:  storage.StorageLocation,
				Remark:           &item.Remark,
				OperatorId:       &reqDto.OperatorId,
				OperatorName:     &reqDto.OperatorName,
			}

			if err := Save(withdraw); err != nil {
				return err
			}

			// 记录旧剩余数量，用于日志记录
			oldRemainingQty := *storage.RemainingQty

			// 更新存酒记录的剩余数量
			newRemaining := *storage.RemainingQty - item.Quantity
			storage.RemainingQty = &newRemaining
			utime := util.TimeNowUnixInt64()
			storage.Utime = &utime
			storage.LastOperationTime = &utime          // 更新最后操作时间
			storage.OperatorId = &reqDto.OperatorId     // 更新操作人ID
			storage.OperatorName = &reqDto.OperatorName // 更新操作人姓名

			// 更新状态字段
			if newRemaining == 0 {
				storage.StatusCode = util.GetItPtr(_const.StatusCodeWithdrawn)
				storage.StatusName = util.GetItPtr(_const.StatusNameWithdrawn)
			} else if newRemaining < *storage.Quantity {
				storage.StatusCode = util.GetItPtr(_const.StatusCodePartial)
				storage.StatusName = util.GetItPtr(_const.StatusNamePartial)
			}

			// 保存更新后的存酒记录
			if err := Save(&storage); err != nil {
				return err
			}

			// 创建操作日志记录
			operationRemark := fmt.Sprintf("取酒单号: %s，商品: %s，数量: %d，从 %d 减少到 %d，存放位置: %s",
				orderNo, *storage.ProductName, item.Quantity, oldRemainingQty, newRemaining,
				*storage.StorageLocation)

			if item.Remark != "" {
				operationRemark += fmt.Sprintf("，备注: %s", item.Remark)
			}

			// 使用实际的取酒时间
			withdrawTime := utime
			if reqDto.WithdrawTime > 0 {
				withdrawTime = reqDto.WithdrawTime
			}

			// 取酒操作只设置送达包厅
			deliveryRoomName := ""
			if reqDto.RoomName != "" {
				deliveryRoomName = reqDto.RoomName // CreateProductWithdrawOrder使用RoomName作为送达包厅
			}

			// 创建操作日志记录
			opLogService := NewProductStorageOperationLogService()
			_, err = opLogService.CreateWithinTransactionWithRoom(
				tx,
				*storage.Id,
				*storage.OrderNo,
				"withdraw",
				"取酒",
				withdrawTime,
				item.Quantity,
				reqDto.OperatorId,
				reqDto.OperatorName,
				newRemaining,
				operationRemark,
				"",               // 取酒操作不设置寄存包厅
				deliveryRoomName, // 取酒操作只设置送达包厅
			)

			if err != nil {
				return fmt.Errorf("创建操作日志记录失败: %s", err.Error())
			}

			// 更新存酒单的剩余数量
			if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
				err = service.updateStorageOrderRemainingQuantity(tx, *storage.ParentOrderNo)
				if err != nil {
					return fmt.Errorf("更新存酒单剩余数量失败: %s", err.Error())
				}
			}

			// 累加总数量
			totalQuantity += item.Quantity
		}

		// 3. 更新取酒单总数量
		*order.TotalQuantity = totalQuantity

		return tx.Model(&po.ProductWithdrawOrder{}).
			Where("id = ?", withdrawOrderId).
			Updates(map[string]interface{}{
				"total_quantity": totalQuantity,
			}).Error
	})

	if err != nil {
		return nil, err
	}

	// 直接构建返回结果，避免再次查询数据库
	result := &vo.ProductWithdrawOrderVO{
		Id:            *order.Id,
		OrderNo:       *order.OrderNo,
		VenueId:       *order.VenueId,
		CustomerId:    *order.CustomerId,
		CustomerName:  *order.CustomerName,
		PhoneNumber:   *order.PhoneNumber,
		OrderNumber:   *order.OrderNumber,
		WithdrawTime:  *order.WithdrawTime,
		RoomId:        *order.RoomId,
		RoomName:      *order.RoomName,
		TotalItems:    *order.TotalItems,
		TotalQuantity: *order.TotalQuantity,
		Remark:        *order.Remark,
		OperatorId:    *order.OperatorId,
		OperatorName:  *order.OperatorName,
		Ctime:         *order.Ctime,
		Utime:         *order.Utime,
		State:         *order.State,
		Version:       *order.Version,
		Items:         []vo.ProductWithdrawVO{},
	}

	// 重新查询取酒明细来构建返回数据
	withdrawItems, err := dal.Q.ProductWithdraw.WithContext(logCtx).
		Where(dal.Q.ProductWithdraw.ParentOrderNo.Eq(orderNo)).Find()

	if err != nil {
		return result, nil // 返回主单数据，明细为空
	}

	// 构建明细列表
	for _, item := range withdrawItems {
		withdrawVO := vo.ProductWithdrawVO{
			Id:               *item.Id,
			OrderNo:          *item.OrderNo,
			ParentOrderNo:    *item.ParentOrderNo,
			StorageId:        *item.StorageId,
			StorageOrderNo:   *item.StorageOrderNo,
			VenueId:          *item.VenueId,
			CustomerId:       *item.CustomerId,
			CustomerName:     *item.CustomerName,
			OrderNumber:      *item.OrderNumber,
			ProductId:        *item.ProductId,
			ProductName:      *item.ProductName,
			ProductUnit:      *item.ProductUnit,
			ProductSpec:      *item.ProductSpec,
			Quantity:         *item.Quantity,
			WithdrawTime:     *item.WithdrawTime,
			DeliveryRoomId:   *item.DeliveryRoomId,   // Correct VO field, correct PO field
			DeliveryRoomName: *item.DeliveryRoomName, // Correct VO field, correct PO field
			StorageLocation:  *item.StorageLocation,
			Remark:           *item.Remark,
			OperatorId:       *item.OperatorId,
			OperatorName:     *item.OperatorName,
			Ctime:            *item.Ctime,
			Utime:            *item.Utime,
			State:            *item.State,
			Version:          *item.Version,
		}

		result.Items = append(result.Items, withdrawVO)
	}

	return result, nil
}

// DeleteProductWithdrawOrder 删除取酒单及其明细
func (service *ProductWithdrawService) DeleteProductWithdrawOrder(logCtx *gin.Context, id string) error {
	// 查询取酒单
	order, err := dal.Q.ProductWithdrawOrder.WithContext(logCtx).Where(dal.Q.ProductWithdrawOrder.Id.Eq(id)).First()
	if err != nil {
		return err
	}

	if order == nil {
		return fmt.Errorf("取酒单不存在, ID: %s", id)
	}

	// 使用事务确保取酒单和明细都被删除
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 创建临时Query对象
		tempQuery := dal.Use(tx)

		// 1. 查询取酒单的所有明细
		items, err := tempQuery.ProductWithdraw.WithContext(logCtx).Where(
			tempQuery.ProductWithdraw.ParentOrderNo.Eq(*order.OrderNo),
		).Find()
		if err != nil {
			return err
		}

		// 2. 还原存酒记录的剩余数量
		for _, item := range items {
			// 查询存酒记录
			storage, err := tempQuery.ProductStorage.WithContext(logCtx).Where(
				tempQuery.ProductStorage.Id.Eq(*item.StorageId),
			).First()
			if err != nil || storage == nil {
				continue // 如果存酒记录不存在，跳过处理
			}

			// 更新存酒记录的剩余数量
			newRemaining := *storage.RemainingQty + *item.Quantity
			result := tx.Model(&po.ProductStorage{}).Where("id = ?", *storage.Id).Updates(map[string]interface{}{
				"remaining_qty": newRemaining,
				"utime":         time.Now().Unix(),
			})
			if result.Error != nil {
				return result.Error
			}

			// 更新存酒单的剩余数量
			if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
				err = service.updateStorageOrderRemainingQuantity(tx, *storage.ParentOrderNo)
				if err != nil {
					return fmt.Errorf("更新存酒单剩余数量失败: %s", err.Error())
				}
			}
		}

		// 3. 删除所有取酒明细
		for _, item := range items {
			_, err := tempQuery.ProductWithdraw.WithContext(logCtx).Where(
				tempQuery.ProductWithdraw.Id.Eq(*item.Id),
			).Delete()
			if err != nil {
				return err
			}
		}

		// 4. 删除取酒单
		_, err = tempQuery.ProductWithdrawOrder.WithContext(logCtx).Where(
			tempQuery.ProductWithdrawOrder.Id.Eq(id),
		).Delete()
		return err
	})
}

// FindWithdrawableItems 根据客户信息查询可取商品列表
func (service *ProductWithdrawService) FindWithdrawableItems(logCtx *gin.Context, reqDto *req.QueryWithdrawableItemsReqDto) (*vo.WithdrawableItemsVO, error) {
	// 检查上下文和请求参数

	// 如果有搜索条件，直接使用原生SQL进行查询
	if reqDto.SearchValue != "" && reqDto.SearchValue != "undefined" {
		searchValue := strings.TrimSpace(reqDto.SearchValue)
		return service.searchWithRawSQL(logCtx, reqDto, searchValue)
	}

	// 无搜索条件时，使用dal查询
	query := dal.Use(model.DBSlave.Self)
	queryDo := query.ProductStorage.WithContext(logCtx)

	// 构建查询条件
	queryDo = queryDo.Where(query.ProductStorage.VenueId.Eq(reqDto.VenueId))
	queryDo = queryDo.Where(query.ProductStorage.RemainingQty.Gt(0))                  // 只查询剩余数量大于0的记录
	queryDo = queryDo.Where(query.ProductStorage.State.Eq(_const.StorageStateNormal)) // 只查询正常状态的记录

	// 按存储时间倒序排列（最新的在前面）
	queryDo = queryDo.Order(query.ProductStorage.StorageTime.Desc())

	storageItems, err := queryDo.Find()
	if err != nil {
		fmt.Printf("查询可取商品失败: %s\n", err.Error())
		return nil, fmt.Errorf("查询可取商品失败: %s", err.Error())
	}

	return service.buildWithdrawableItemsVO(logCtx, storageItems, reqDto)
}

// searchWithRawSQL 使用原生SQL进行搜索
func (service *ProductWithdrawService) searchWithRawSQL(logCtx *gin.Context, reqDto *req.QueryWithdrawableItemsReqDto, searchValue string) (*vo.WithdrawableItemsVO, error) {
	// 使用原生SQL查询
	var storageItems []*po.ProductStorage

	// 构建SQL条件
	sqlCondition := fmt.Sprintf(
		"venue_id = ? AND remaining_qty > 0 AND state = 0 AND (customer_name LIKE '%%%s%%' OR phone_number LIKE '%%%s%%' OR "+
			"customer_id LIKE '%%%s%%' OR member_card_no LIKE '%%%s%%' OR "+
			"order_no LIKE '%%%s%%' OR product_name LIKE '%%%s%%' OR "+
			"product_spec LIKE '%%%s%%')",
		searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue)

	// 直接使用GORM进行查询，这里使用原生SQL语句，按存储时间倒序排列
	err := model.DBSlave.Self.Where(sqlCondition, reqDto.VenueId).Order("storage_time DESC").Find(&storageItems).Error
	if err != nil {
		fmt.Printf("查询可取商品失败: %s\n", err.Error())
		return nil, fmt.Errorf("查询可取商品失败: %s", err.Error())
	}

	return service.buildWithdrawableItemsVO(logCtx, storageItems, reqDto)
}

// buildWithdrawableItemsVO 构建返回结果
func (service *ProductWithdrawService) buildWithdrawableItemsVO(logCtx *gin.Context, storageItems []*po.ProductStorage, reqDto *req.QueryWithdrawableItemsReqDto) (*vo.WithdrawableItemsVO, error) {
	// 如果没有找到记录，返回空结果
	if len(storageItems) == 0 {
		return &vo.WithdrawableItemsVO{
			CustomerInfo:      vo.CustomerInfoVO{},
			WithdrawableItems: []vo.WithdrawableItemVO{},
			StorageOrders:     []vo.StorageOrderBriefVO{},
		}, nil
	}

	// 获取客户信息
	customerInfo := vo.CustomerInfoVO{}
	if len(storageItems) > 0 {
		firstItem := storageItems[0]
		// 将必要的客户信息复制到新的对象中
		if firstItem.CustomerId != nil {
			customerInfo.CustomerId = *firstItem.CustomerId
		}
		if firstItem.CustomerName != nil {
			customerInfo.CustomerName = *firstItem.CustomerName
		}
		if firstItem.PhoneNumber != nil {
			customerInfo.PhoneNumber = *firstItem.PhoneNumber
		}
		if firstItem.MemberCardId != nil {
			customerInfo.MemberCardId = *firstItem.MemberCardId
		}
		if firstItem.MemberCardNumber != nil {
			customerInfo.MemberCardNumber = *firstItem.MemberCardNumber
		}
	}

	// 计算过期状态
	withdrawableItems := make([]vo.WithdrawableItemVO, 0, len(storageItems))
	orderNoMap := make(map[string]vo.StorageOrderBriefVO)

	now := time.Now().Unix()
	for _, item := range storageItems {
		// 空值检查
		if item.ExpireTime == nil || item.ParentOrderNo == nil || item.ProductId == nil ||
			item.ProductName == nil || item.ProductUnit == nil || item.ProductSpec == nil ||
			item.Quantity == nil || item.RemainingQty == nil || item.StorageLocation == nil ||
			item.StorageTime == nil || item.Id == nil || item.OrderNo == nil {
			continue // 跳过数据不完整的记录
		}

		// 计算到期天数
		daysToExpire := int((*item.ExpireTime - now) / 86400)
		expiringStatus := "normal"

		if *item.ExpireTime < now {
			expiringStatus = "expired"
		} else if daysToExpire <= reqDto.ExpireDays {
			expiringStatus = "soon"
		}

		// 创建可取商品项
		withdrawableItem := vo.WithdrawableItemVO{
			Id:              *item.Id,
			OrderNo:         *item.OrderNo,
			ParentOrderNo:   *item.ParentOrderNo,
			ProductId:       *item.ProductId,
			ProductName:     *item.ProductName,
			ProductUnit:     *item.ProductUnit,
			ProductSpec:     *item.ProductSpec,
			Quantity:        *item.Quantity,
			RemainingQty:    *item.RemainingQty,
			StorageLocation: *item.StorageLocation,
			StorageTime:     *item.StorageTime,
			ExpireTime:      *item.ExpireTime,
			ExpiringStatus:  expiringStatus,
			DaysToExpire:    daysToExpire,
			OperatorId:      *item.OperatorId,              // 添加操作人ID
			OperatorName:    *item.OperatorName,            // 添加操作人名称
			CustomerId:      lo.FromPtr(item.CustomerId),   // 存酒人ID
			CustomerName:    lo.FromPtr(item.CustomerName), // 存酒人姓名
			PhoneNumber:     lo.FromPtr(item.PhoneNumber),  // 存酒人手机号
		}
		withdrawableItems = append(withdrawableItems, withdrawableItem)

		// 收集存酒单信息
		if *item.ParentOrderNo != "" {
			if _, exists := orderNoMap[*item.ParentOrderNo]; !exists {
				// 使用与ProductStorageService.go相同的模式
				orderQuery := dal.Use(model.DBSlave.Self)
				storageOrder, err := orderQuery.ProductStorageOrder.WithContext(logCtx).
					Where(orderQuery.ProductStorageOrder.OrderNo.Eq(*item.ParentOrderNo)).First()

				if err == nil && storageOrder != nil &&
					storageOrder.OrderNo != nil && storageOrder.StorageTime != nil &&
					storageOrder.TotalItems != nil && storageOrder.TotalQuantity != nil &&
					storageOrder.RemainingQuantity != nil {
					orderBrief := vo.StorageOrderBriefVO{
						OrderNo:           *storageOrder.OrderNo,
						StorageTime:       *storageOrder.StorageTime,
						TotalItems:        *storageOrder.TotalItems,
						TotalQuantity:     *storageOrder.TotalQuantity,
						RemainingQuantity: *storageOrder.RemainingQuantity,
					}
					orderNoMap[*item.ParentOrderNo] = orderBrief
				}
			}
		}
	}

	// 收集存酒单信息
	storageOrders := make([]vo.StorageOrderBriefVO, 0, len(orderNoMap))
	for _, order := range orderNoMap {
		storageOrders = append(storageOrders, order)
	}

	return &vo.WithdrawableItemsVO{
		CustomerInfo:      customerInfo,
		WithdrawableItems: withdrawableItems,
		StorageOrders:     storageOrders,
	}, nil
}

// 更新存酒记录状态
func (service *ProductWithdrawService) UpdateStorageState(logCtx *gin.Context, storageId string, newState int, reason string) error {
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 查询存酒记录
		storage, err := dal.Q.ProductStorage.WithContext(logCtx).Where(
			dal.Q.ProductStorage.Id.Eq(storageId),
		).First()
		if err != nil {
			return err
		}

		if storage == nil {
			return fmt.Errorf("存酒记录不存在，ID: %s", storageId)
		}

		// 更新状态
		*storage.State = newState
		*storage.Utime = time.Now().Unix()

		// 保存更新
		if err := Save(storage); err != nil {
			return err
		}

		// 可选：记录状态变更日志
		// ...

		return nil
	})
}

// 报废存酒记录
func (service *ProductWithdrawService) DiscardStorage(logCtx *gin.Context, storageId string, reason string) error {
	return service.UpdateStorageState(logCtx, storageId, _const.StorageStateDiscarded, reason)
}

// 撤销存酒记录
func (service *ProductWithdrawService) CancelStorage(logCtx *gin.Context, storageId string, reason string) error {
	return service.UpdateStorageState(logCtx, storageId, _const.StorageStateCancelled, reason)
}

// 恢复存酒记录状态为正常
func (service *ProductWithdrawService) RestoreStorage(logCtx *gin.Context, storageId string, reason string) error {
	return service.UpdateStorageState(logCtx, storageId, _const.StorageStateNormal, reason)
}

// FindAllProductWithdraw 查询所有取酒记录
func (service *ProductWithdrawService) FindAllProductWithdraw(logCtx *gin.Context, reqDto *req.QueryProductWithdrawReqDto) ([]*po.ProductWithdraw, error) {
	queryBuilder := dal.Use(model.DBSlave.Self)
	query := queryBuilder.ProductWithdraw.WithContext(logCtx)

	// 构建查询条件
	if reqDto.VenueId != "" {
		query = query.Where(queryBuilder.ProductWithdraw.VenueId.Eq(reqDto.VenueId))
	}
	if reqDto.Id != "" {
		query = query.Where(queryBuilder.ProductWithdraw.Id.Eq(reqDto.Id))
	}
	if reqDto.StorageId != "" {
		query = query.Where(queryBuilder.ProductWithdraw.StorageId.Eq(reqDto.StorageId))
	}
	if reqDto.OrderNo != "" {
		query = query.Where(queryBuilder.ProductWithdraw.OrderNo.Eq(reqDto.OrderNo))
	}
	if reqDto.ParentOrderNo != "" {
		query = query.Where(queryBuilder.ProductWithdraw.ParentOrderNo.Eq(reqDto.ParentOrderNo))
	}
	if reqDto.CustomerId != "" {
		query = query.Where(queryBuilder.ProductWithdraw.CustomerId.Eq(reqDto.CustomerId))
	}
	if reqDto.CustomerName != "" {
		query = query.Where(queryBuilder.ProductWithdraw.CustomerName.Eq(reqDto.CustomerName))
	}
	if reqDto.ProductId != "" {
		query = query.Where(queryBuilder.ProductWithdraw.ProductId.Eq(reqDto.ProductId))
	}
	if reqDto.ProductName != "" {
		query = query.Where(queryBuilder.ProductWithdraw.ProductName.Eq(reqDto.ProductName))
	}
	if reqDto.RoomId != "" {
		query = query.Where(queryBuilder.ProductWithdraw.RoomId.Eq(reqDto.RoomId))
	}
	if reqDto.RoomName != "" {
		query = query.Where(queryBuilder.ProductWithdraw.RoomName.Eq(reqDto.RoomName))
	}
	if reqDto.OrderNumber != "" {
		query = query.Where(queryBuilder.ProductWithdraw.OrderNumber.Eq(reqDto.OrderNumber))
	}
	if reqDto.OperatorId != "" {
		query = query.Where(queryBuilder.ProductWithdraw.OperatorId.Eq(reqDto.OperatorId))
	}
	if reqDto.OperatorName != "" {
		query = query.Where(queryBuilder.ProductWithdraw.OperatorName.Like("%" + reqDto.OperatorName + "%"))
	}

	// 时间范围查询
	if reqDto.WithdrawTimeStart > 0 {
		query = query.Where(queryBuilder.ProductWithdraw.WithdrawTime.Gte(reqDto.WithdrawTimeStart))
	}
	if reqDto.WithdrawTimeEnd > 0 {
		query = query.Where(queryBuilder.ProductWithdraw.WithdrawTime.Lte(reqDto.WithdrawTimeEnd))
	}

	// 搜索文本支持
	if reqDto.SearchText != "" {
		searchText := "%" + reqDto.SearchText + "%"
		query = query.Where(queryBuilder.ProductWithdraw.CustomerName.Like(searchText)).
			Or(query.Where(queryBuilder.ProductWithdraw.ProductName.Like(searchText)))
	}

	// 添加排序
	query = query.Order(queryBuilder.ProductWithdraw.Ctime.Desc())

	// 处理分页
	if reqDto.PageNum > 0 && reqDto.PageSize > 0 {
		offset := (reqDto.PageNum - 1) * reqDto.PageSize
		query = query.Offset(offset).Limit(reqDto.PageSize)
	}

	// 执行查询
	list, err := query.Find()
	return list, err
}

// CreateProductWithdrawAndUpdateStorage 创建单个提取记录并更新存储记录
func (service *ProductWithdrawService) CreateProductWithdrawAndUpdateStorage(logCtx *gin.Context, productWithdraw *po.ProductWithdraw, storage *po.ProductStorage) error {
	// 使用事务确保操作的原子性
	return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 生成提取记录ID (如果没有)
		if productWithdraw.Id == nil || *productWithdraw.Id == "" {
			id := util.GetUUID()
			productWithdraw.Id = &id
		}

		// 生成提取单号 (如果没有)
		if productWithdraw.OrderNo == nil || *productWithdraw.OrderNo == "" {
			venueId := *productWithdraw.VenueId
			orderNo := "PD" + venueId[len(venueId)-8:] + time.Now().Format("060102150405") + fmt.Sprintf("%04d", rand.Intn(10000))
			productWithdraw.OrderNo = &orderNo
		}

		// 保存提取记录
		if err := SaveWithTx(productWithdraw, tx); err != nil {
			return fmt.Errorf("创建酒水提取记录失败: %s", err.Error())
		}

		// 更新存储记录
		utime := int64(util.TimeNowUnix())
		storage.Utime = &utime

		if err := UpdateNotNullWithTx(*storage, tx); err != nil {
			return fmt.Errorf("更新酒水存储记录剩余数量失败: %s", err.Error())
		}

		// 记录操作日志
		opLogService := NewProductStorageOperationLogService()
		var operationTime int64
		if productWithdraw.WithdrawTime != nil && *productWithdraw.WithdrawTime > 0 {
			operationTime = *productWithdraw.WithdrawTime
		} else {
			operationTime = utime
		}

		// 计算操作前的数量
		originalQty := 0
		if storage.Quantity != nil {
			originalQty = *storage.Quantity
		}

		// 计算取酒数量
		withdrawQty := 0
		if productWithdraw.Quantity != nil {
			withdrawQty = *productWithdraw.Quantity
		}

		// 计算剩余数量
		remainingQty := 0
		if storage.RemainingQty != nil {
			remainingQty = *storage.RemainingQty
		}

		// 构建操作备注
		remark := fmt.Sprintf("取酒单号: %s，商品: %s，数量: %d，从 %d 减少到 %d，存放位置: %s",
			*productWithdraw.OrderNo, *storage.ProductName, withdrawQty, originalQty, remainingQty, *storage.StorageLocation)

		// 获取送达包厅信息
		deliveryRoomName := ""
		if productWithdraw.DeliveryRoomName != nil {
			deliveryRoomName = *productWithdraw.DeliveryRoomName
		}

		// 记录取酒操作日志
		_, logErr := opLogService.CreateWithinTransactionWithRoom(
			tx,
			*storage.Id,
			*productWithdraw.OrderNo,
			"withdraw",
			"取酒",
			operationTime,
			withdrawQty,
			*productWithdraw.OperatorId,
			*productWithdraw.OperatorName,
			remainingQty,
			remark,
			"",               // 取酒操作不设置寄存包厅
			deliveryRoomName, // 取酒操作只设置送达包厅
		)
		if logErr != nil {
			return fmt.Errorf("创建操作日志失败: %s", logErr.Error())
		}

		// 如果存在父订单号，更新存酒单的剩余数量
		if storage.ParentOrderNo != nil && *storage.ParentOrderNo != "" {
			updateErr := service.updateStorageOrderRemainingQuantity(tx, *storage.ParentOrderNo)
			if updateErr != nil {
				return fmt.Errorf("更新存酒单剩余数量失败: %s", updateErr.Error())
			}
		}

		return nil
	})
}

// CountProductWithdraw 统计符合条件的取酒记录总数
func (service *ProductWithdrawService) CountProductWithdraw(logCtx *gin.Context, reqDto *req.QueryProductWithdrawReqDto) (int64, error) {
	// 创建DAO层查询对象
	query := dal.Q.ProductWithdraw.WithContext(logCtx)

	// 构建查询条件
	if reqDto.VenueId != "" {
		query = query.Where(dal.Q.ProductWithdraw.VenueId.Eq(reqDto.VenueId))
	}
	if reqDto.Id != "" {
		query = query.Where(dal.Q.ProductWithdraw.Id.Eq(reqDto.Id))
	}
	if reqDto.StorageId != "" {
		query = query.Where(dal.Q.ProductWithdraw.StorageId.Eq(reqDto.StorageId))
	}
	if reqDto.OrderNo != "" {
		query = query.Where(dal.Q.ProductWithdraw.OrderNo.Eq(reqDto.OrderNo))
	}
	if reqDto.ParentOrderNo != "" {
		query = query.Where(dal.Q.ProductWithdraw.ParentOrderNo.Eq(reqDto.ParentOrderNo))
	}
	if reqDto.CustomerId != "" {
		query = query.Where(dal.Q.ProductWithdraw.CustomerId.Eq(reqDto.CustomerId))
	}
	if reqDto.CustomerName != "" {
		query = query.Where(dal.Q.ProductWithdraw.CustomerName.Eq(reqDto.CustomerName))
	}
	if reqDto.ProductId != "" {
		query = query.Where(dal.Q.ProductWithdraw.ProductId.Eq(reqDto.ProductId))
	}
	if reqDto.ProductName != "" {
		query = query.Where(dal.Q.ProductWithdraw.ProductName.Eq(reqDto.ProductName))
	}
	if reqDto.RoomId != "" {
		query = query.Where(dal.Q.ProductWithdraw.RoomId.Eq(reqDto.RoomId))
	}
	if reqDto.RoomName != "" {
		query = query.Where(dal.Q.ProductWithdraw.RoomName.Eq(reqDto.RoomName))
	}
	if reqDto.OrderNumber != "" {
		query = query.Where(dal.Q.ProductWithdraw.OrderNumber.Eq(reqDto.OrderNumber))
	}
	if reqDto.OperatorId != "" {
		query = query.Where(dal.Q.ProductWithdraw.OperatorId.Eq(reqDto.OperatorId))
	}
	if reqDto.OperatorName != "" {
		query = query.Where(dal.Q.ProductWithdraw.OperatorName.Like("%" + reqDto.OperatorName + "%"))
	}

	// 时间范围查询
	if reqDto.WithdrawTimeStart > 0 {
		query = query.Where(dal.Q.ProductWithdraw.WithdrawTime.Gte(reqDto.WithdrawTimeStart))
	}
	if reqDto.WithdrawTimeEnd > 0 {
		query = query.Where(dal.Q.ProductWithdraw.WithdrawTime.Lte(reqDto.WithdrawTimeEnd))
	}

	// 模糊搜索
	if reqDto.SearchText != "" {
		searchText := "%%" + reqDto.SearchText + "%%"
		query = query.Where(dal.Q.ProductWithdraw.CustomerName.Like(searchText)).
			Or(query.Where(dal.Q.ProductWithdraw.ProductName.Like(searchText)))
	}

	// 执行计数查询
	count, err := query.Count()
	if err != nil {
		return 0, err
	}

	return count, nil
}

// 查询取酒明细
func (service *ProductWithdrawService) QueryOrderDetailsByOrderNo(logCtx *gin.Context, orderNo string) ([]*po.ProductWithdraw, error) {
	queryBuilder := dal.Use(model.DBSlave.Self)
	withdrawItems, err := queryBuilder.ProductWithdraw.WithContext(logCtx).
		Where(queryBuilder.ProductWithdraw.ParentOrderNo.Eq(orderNo)).Find()
	if err != nil {
		return nil, err
	}
	return withdrawItems, nil
}

// 获取取酒单详情
func (service *ProductWithdrawService) GetOrderDetail(logCtx *gin.Context, id string) (*po.ProductWithdrawOrder, error) {
	queryBuilder := dal.Use(model.DBSlave.Self)
	order, err := queryBuilder.ProductWithdrawOrder.WithContext(logCtx).Where(queryBuilder.ProductWithdrawOrder.Id.Eq(id)).First()
	if err != nil {
		return nil, err
	}
	return order, nil
}

// 更新存酒记录
func (service *ProductWithdrawService) UpdateStorageRemainingQty(logCtx *gin.Context, storageId string, decreaseQty int) error {
	// 查询存酒记录
	queryBuilder := dal.Use(model.DBSlave.Self)
	storage, err := queryBuilder.ProductStorage.WithContext(logCtx).Where(
		queryBuilder.ProductStorage.Id.Eq(storageId),
	).First()
	if err != nil {
		return err
	}

	// 检查剩余数量是否足够
	if *storage.RemainingQty < decreaseQty {
		return fmt.Errorf("剩余数量不足, 当前剩余: %d, 需要取出: %d", *storage.RemainingQty, decreaseQty)
	}

	// 更新剩余数量
	newRemainingQty := *storage.RemainingQty - decreaseQty
	storage.RemainingQty = &newRemainingQty

	// 如果剩余数量为0，更新状态为已取完
	if newRemainingQty == 0 {
		statusCompleted := "completed" // 已取完状态
		storage.StatusCode = &statusCompleted
	}

	// 保存更新
	if err := queryBuilder.ProductStorage.Save(storage); err != nil {
		return err
	}

	// 更新订单主表的剩余数量
	storageOrderService := NewProductStorageOrderService()
	err = storageOrderService.UpdateProductStorageOrderStatus(logCtx, *storage.ParentOrderNo, 0)
	return err
}

// 更新存酒单剩余数量的辅助方法
func (service *ProductWithdrawService) updateStorageOrderRemainingQuantity(tx *gorm.DB, parentOrderNo string) error {
	fmt.Printf("updateStorageOrderRemainingQuantity 开始更新存酒单剩余数量，订单号: %s\n", parentOrderNo)
	if parentOrderNo == "" {
		return fmt.Errorf("父订单号不能为空")
	}

	// 优先检查存酒单是否存在
	tempQuery := dal.Use(tx)
	storageOrder, err := tempQuery.ProductStorageOrder.Where(
		tempQuery.ProductStorageOrder.OrderNo.Eq(parentOrderNo),
	).First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			fmt.Printf("订单号 %s 不存在\n", parentOrderNo)
			return fmt.Errorf("存酒单不存在，订单号: %s", parentOrderNo)
		}
		fmt.Printf("查询存酒单失败: %s\n", err.Error())
		return fmt.Errorf("查询存酒单失败: %s", err.Error())
	}

	if storageOrder == nil {
		fmt.Printf("存酒单不存在，订单号: %s\n", parentOrderNo)
		return fmt.Errorf("存酒单不存在，订单号: %s", parentOrderNo)
	}

	// 查询该存酒单下所有明细的剩余数量总和
	var totalRemaining int64
	err = tx.Model(&po.ProductStorage{}).
		Where("parent_order_no = ? AND state = ?", parentOrderNo, 0). // 只统计正常状态的记录
		Select("COALESCE(SUM(remaining_qty), 0)").
		Row().Scan(&totalRemaining)
	if err != nil {
		fmt.Printf("计算剩余数量失败: %s\n", err.Error())
		return fmt.Errorf("计算剩余数量失败: %s", err.Error())
	}

	// 记录更新前的数量，用于日志
	originalRemaining := 0
	if storageOrder.RemainingQuantity != nil {
		originalRemaining = *storageOrder.RemainingQuantity
	}
	fmt.Printf("订单 %s 当前剩余数量: %d, 计算后的剩余数量: %d\n",
		parentOrderNo, originalRemaining, totalRemaining)

	// 如果数量没有变化，不执行更新
	if originalRemaining == int(totalRemaining) {
		fmt.Printf("订单 %s 数量未变化，跳过更新\n", parentOrderNo)
		return nil
	}

	// 更新存酒单的剩余数量
	currentTime := time.Now().Unix()
	result := tx.Model(&po.ProductStorageOrder{}).Where("order_no = ?", parentOrderNo).Updates(map[string]interface{}{
		"remaining_quantity": int(totalRemaining),
		"utime":              currentTime,
	})

	if result.Error != nil {
		fmt.Printf("更新存酒单剩余数量失败: %s\n", result.Error.Error())
		return fmt.Errorf("更新存酒单剩余数量失败: %s", result.Error.Error())
	}

	// 检查是否成功更新
	if result.RowsAffected == 0 {
		fmt.Printf("更新存酒单剩余数量失败: 未影响任何行，订单号: %s\n", parentOrderNo)
		return fmt.Errorf("更新存酒单剩余数量失败: 未影响任何行，订单号: %s", parentOrderNo)
	}

	fmt.Printf("更新存酒单成功: 订单号=%s, 旧值=%d, 新值=%d, 变化=%d\n",
		parentOrderNo, originalRemaining, totalRemaining,
		int(totalRemaining)-originalRemaining)

	return nil
}
