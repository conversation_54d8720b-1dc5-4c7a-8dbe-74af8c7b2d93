package impl

import (
	"errors"
	"strconv"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/paysdk"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type PayService struct {
}

var payService *PayService = &PayService{}
var notificationService = CashierNotificationService{}

func (service *PayService) TransformPayGate(logCtx *gin.Context, reqDto *req.QueryOrderPayReqDto, payBill *po.PayBill) (payResult *paysdk.PayResult, err error) {
	venuePaySetting := po.VenuePaySetting{}
	if !util.InList(*reqDto.PayType, _const.PAY_TYPE_RECORDS) {

		venuePaySettings, err := venuePaySettingService.FindAllVenuePaySetting(logCtx, &req.QueryVenuePaySettingReqDto{
			VenueId: reqDto.VenueId,
		})
		if err != nil {
			return nil, err
		}
		if len(*venuePaySettings) <= 0 {
			return nil, errors.New("门店不支持此支付方式")
		}
		venuePaySetting = (*venuePaySettings)[0]
		if venuePaySetting.SubMerchantId == nil || *venuePaySetting.SubMerchantId == "" {
			return nil, errors.New("门店不支持此支付方式")
		}
	}

	LeshuaId := venuePaySetting.SubMerchantId

	switch *reqDto.PayType {
	case _const.PAY_TYPE_MEMBER_CARD:
		// payResult, err = paysdk.MemberPay(logCtx, reqDto)
	case _const.PAY_TYPE_RECORD_CASH:
		payResult, err = paysdk.CashPay(logCtx, reqDto)
	case _const.PAY_TYPE_RECORD_WECHAT:
		payResult, err = paysdk.WechatRecordPay(logCtx, reqDto)
	case _const.PAY_TYPE_RECORD_ALIPAY:
		payResult, err = paysdk.AlipayRecordPay(logCtx, reqDto)
	case _const.PAY_TYPE_RECORD_BANK:
		payResult, err = paysdk.BankRecordPay(logCtx, reqDto)
	case _const.PAY_TYPE_LESHUA_BSHOWQR:
		if reqDto.BQROneCode == nil || *reqDto.BQROneCode == "" {
			return nil, errors.New("bQROneCode不能为空")
		}
		result := paysdk.PayBShowQR(logCtx, model.LeshuaDto{
			BQROneCode: *reqDto.BQROneCode,
			Leshua_id:  *LeshuaId,
			Order_id:   *payBill.BillId,
			Amount:     strconv.FormatInt(*payBill.TotalFee, 10),
			// Product_name: util.GetPtrSafe(payBill.ProductName),
			PayType: _const.PAY_TYPE_LESHUA_BSHOWQR,
		})
		if result.Resp_code != "-9999" {
			payResult = &paysdk.PayResult{
				Pay: &paysdk.PayResultInfo{
					PayId: result.Third_order_id,
				},
				ErrMsg:  result.Resp_msg,
				ErrCode: result.Resp_code,
			}
		} else {
			return nil, errors.New("乐刷简易支付下单失败:" + result.Resp_msg)
		}
	default:
		panic("not supported")
	}
	return payResult, err
}

func (service *PayService) SaveBatchTxForPay(logCtx *gin.Context, toAddPayBill *po.PayBill, toAddOrderAndPays *[]po.OrderAndPay, toUpdateOrders *[]po.Order, toUpdateOrderRoomPlans *[]po.OrderRoomPlan, toUpdateOrderProducts *[]po.OrderProduct) error {
	tx := model.DBMaster.Self.Begin()
	if toAddPayBill != nil {
		if err := payBillService.CreatePayBillWithTx(logCtx, toAddPayBill, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if toAddOrderAndPays != nil {
		for _, orderAndPay := range *toAddOrderAndPays {
			if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if toUpdateOrders != nil {
		for _, order := range *toUpdateOrders {
			if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if toUpdateOrderRoomPlans != nil {
		for _, orderRoomPlan := range *toUpdateOrderRoomPlans {
			if err := orderRoomPlanService.UpdateOrderRoomPlanPartialWithTx(logCtx, &orderRoomPlan, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if toUpdateOrderProducts != nil {
		for _, orderProduct := range *toUpdateOrderProducts {
			if err := orderProductService.UpdateOrderProductPartialWithTx(logCtx, &orderProduct, tx); err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (service *PayService) SaveUpdateOrderForPay(logCtx *gin.Context, toUpdateOrders *[]po.Order, toUpdateOrderRoomPlans *[]po.OrderRoomPlan, toUpdateOrderProducts *[]po.OrderProduct) error {
	tx := model.DBMaster.Self.Begin()
	for _, order := range *toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderRoomPlan := range *toUpdateOrderRoomPlans {
		if err := orderRoomPlanService.UpdateOrderRoomPlanPartialWithTx(logCtx, &orderRoomPlan, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, orderProduct := range *toUpdateOrderProducts {
		if err := orderProductService.UpdateOrderProductPartialWithTx(logCtx, &orderProduct, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

func (service *PayService) SavePayPre(logCtx *gin.Context, toAddPayBill *po.PayBill, toAddOrderAndPays *[]po.OrderAndPay, toUpdateOrders *[]po.Order) error {
	tx := model.DBMaster.Self.Begin()
	if err := payBillService.CreatePayBillWithTx(logCtx, toAddPayBill, tx); err != nil {
		tx.Rollback()
		return err
	}
	for _, orderAndPay := range *toAddOrderAndPays {
		if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range *toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

// 乐刷支付回调
func (service *PayService) LeshuaPayCallback(logCtx *gin.Context, reqDtoCallback *model.LeshuaPayCallbackModel, isLeshuaCallback bool) (err error) {
	logTitle := "模拟PayCallback回调"
	if isLeshuaCallback {
		logTitle = "乐刷PayCallback回调"
	}
	util.Wlog(logCtx).Infof("%s %s: 开始", logTitle, reqDtoCallback.Third_order_id)
	defer func() {
		util.Wlog(logCtx).Infof("%s %s: 结束", logTitle, reqDtoCallback.Third_order_id)
		err = nil
	}()
	// 当前仅支付成功的订单才会触发支付交易结果异步通知。
	// 支付订单状态	是否触发异步通知
	// 支付成功	（status=2）	是
	// 订单关闭	（status=6）	否
	// 支付失败	（status=8）	否
	// 回调只存在 status=2
	util.Wlog(logCtx).Infof("%s %s: %s --->>> %s", logTitle, reqDtoCallback.Third_order_id, reqDtoCallback.Status, util.GetPrettyJson(reqDtoCallback))

	// 1. 验证回调状态是否非法
	if reqDtoCallback.Error_code != "0" {
		util.Wlog(logCtx).Errorf("%s 1.1 %s: 未知的支付回调err_code", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("未知的支付回调err_code")
	}
	if reqDtoCallback.Status != "2" {
		util.Wlog(logCtx).Errorf("%s 1.2 %s: 未知的支付回调status", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("未知的支付回调status")
	}

	// 2. 查询支付信息
	payBills, err := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{BillId: util.GetItPtr(reqDtoCallback.Third_order_id)})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 2.1 查询订单-dberr %s: %s", logTitle, reqDtoCallback.Third_order_id, err.Error())
		return errors.New("查询订单-dberr")
	}
	if len(*payBills) == 0 {
		util.Wlog(logCtx).Errorf("%s 2.2 %s: 查询支付账单不存在", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("查询支付账单不存在")
	}
	payBill := (*payBills)[0]
	util.Wlog(logCtx).Infof("%s 2.查询到支付账单 %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(payBill))

	// 3. 拦截重复通知
	if payBill.Status != nil && *payBill.Status == _const.PAY_STATUS_PAID {
		util.Wlog(logCtx).Infof("%s 3.1 %s: 支付成功", logTitle, reqDtoCallback.Third_order_id)
		return nil
	}
	// 4. 验证数据库支付订单字段合法性
	venueId := payBill.VenueId
	sessionId := payBill.SessionId
	if venueId == nil || *venueId == "" {
		util.Wlog(logCtx).Errorf("%s 4.1 %s: 门店ID为空", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("门店ID为空")
	}
	if sessionId == nil || *sessionId == "" {
		util.Wlog(logCtx).Errorf("%s 4.2 %s: 场次ID为空", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("场次ID为空")
	}
	sessions, err := sessionService.FindAllSession(logCtx, &req.QuerySessionReqDto{SessionId: sessionId})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 4.3 %s: 查询场次信息失败", logTitle, reqDtoCallback.Third_order_id)
		return err
	}
	if len(*sessions) == 0 {
		util.Wlog(logCtx).Errorf("%s 4.4 %s: 未找到场次信息", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("未找到场次信息")
	}
	session := (*sessions)[0]
	if session.RoomId == nil || *session.RoomId == "" {
		util.Wlog(logCtx).Errorf("%s 4.5 %s: 场次未绑定房间", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("场次未绑定房间")
	}
	room, err := roomService.FindRoomById(logCtx, *session.RoomId)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 4.6 %s: 未找到房间信息", logTitle, reqDtoCallback.Third_order_id)
		return err
	}
	util.Wlog(logCtx).Infof("%s 4.查询到房间信息 %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(room))

	// 5. 查找支付、订单表数据
	orderAndPays, err := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{BillId: payBill.BillId})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 5.1 %s: 未找到支付及订单对应关系", logTitle, reqDtoCallback.Third_order_id)
		return err
	}
	orderNos := []string{}
	for _, orderAndPay := range *orderAndPays {
		orderNos = append(orderNos, *orderAndPay.OrderNo)
	}
	orders, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{OrderNos: &orderNos, VenueId: venueId, SessionId: sessionId})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 5.2 %s: 未找到订单", logTitle, reqDtoCallback.Third_order_id)
		return err
	}
	util.Wlog(logCtx).Infof("%s 5.查询到订单 %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(orders))
	// 6. 查询session信息
	ordersAll, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{SessionId: sessionId, VenueId: venueId})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 6.1 %s: 未找到场次信息", logTitle, reqDtoCallback.Third_order_id)
		return err
	}
	ordersAllPaid := []po.Order{}
	for _, order := range *ordersAll {
		if util.InList(*order.OrderNo, orderNos) {
			ordersAllPaid = append(ordersAllPaid, order)
		} else {
			if order.Status != nil && *order.Status == _const.ORDER_STATUS_PAID {
				ordersAllPaid = append(ordersAllPaid, order)
			}
		}
	}
	util.Wlog(logCtx).Infof("%s 6.查询到所有订单ordersAll %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(ordersAll))
	// 7. 构造更新数据
	toUpdatePayBill := po.PayBill{
		Id:     payBill.Id,
		BillId: payBill.BillId,
		// ThirdOrderId: &reqDtoCallback.Leshua_order_id,
		Status: util.GetItPtr(_const.PAY_STATUS_PAID),
	}
	toUpdateOrders := []po.Order{}
	for _, order := range *orders {
		toUpdateOrders = append(toUpdateOrders, po.Order{
			Id:     order.Id,
			Status: util.GetItPtr(_const.ORDER_STATUS_PAID),
		})
	}
	var toUpdateSession *po.Session
	var toUpdateRoom *po.Room
	if len(ordersAllPaid) == len(*ordersAll) {
		toUpdateRoom = &po.Room{
			Id:        room.Id,
			SessionId: sessionId,
			Status:    util.GetItPtr(_const.ROOM_STATUS_IN_USE),
		}
		toUpdateSession = &po.Session{
			Id:        session.Id,
			SessionId: sessionId,
			Status:    util.GetItPtr(_const.ORDER_STATUS_PAID),
		}
	} else {
		toUpdateSession = &po.Session{
			Id:        session.Id,
			SessionId: sessionId,
			Status:    util.GetItPtr(_const.ORDER_STATUS_UNPAID),
		}
	}
	util.Wlog(logCtx).Infof("%s 7.构造更新数据toUpdatePayBill %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(toUpdatePayBill))
	util.Wlog(logCtx).Infof("%s 7.构造更新数据toUpdateSession %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(toUpdateSession))
	util.Wlog(logCtx).Infof("%s 7.构造更新数据toUpdateRoom %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(toUpdateRoom))
	util.Wlog(logCtx).Infof("%s 7.构造更新数据toUpdateOrders %s: %s", logTitle, reqDtoCallback.Third_order_id, util.GetPrettyJson(toUpdateOrders))
	if err := service.SavePaySecondLeshua(logCtx, &toUpdatePayBill, toUpdateSession, toUpdateRoom, &toUpdateOrders); err != nil {
		util.Wlog(logCtx).Errorf("%s 7.1 %s: 更新支付账单状态失败", logTitle, reqDtoCallback.Third_order_id)
		return errors.New("更新支付账单事务失败")
	}
	return nil
}

func (service *PayService) SavePaySecondLeshua(logCtx *gin.Context, toUpdatePayBill *po.PayBill, toUpdateSession *po.Session, toUpdateRoom *po.Room, toUpdateOrders *[]po.Order) error {
	tx := model.DBMaster.Self.Begin()
	if err := payBillService.UpdatePayBillPartialWithTx(logCtx, toUpdatePayBill, tx); err != nil {
		tx.Rollback()
		return err
	}
	if toUpdateSession != nil {
		if err := sessionService.UpdateSessionPartialWithTx(logCtx, toUpdateSession, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if toUpdateRoom != nil {
		if err := roomService.UpdateRoomPartialWithTx(logCtx, toUpdateRoom, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range *toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SavePayByCashCallbackPre(logCtx *gin.Context, toUpdatePayBill *po.PayBill, toUpdateOrders *[]po.Order, toUpdateOrderRoomPlans *[]po.OrderRoomPlan, toUpdateOrderProducts *[]po.OrderProduct) error {
	tx := model.DBMaster.Self.Begin()
	if err := payBillService.UpdatePayBillPartialWithTx(logCtx, toUpdatePayBill, tx); err != nil {
		tx.Rollback()
		return err
	}
	for _, v := range *toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range *toUpdateOrderRoomPlans {
		if err := orderRoomPlanService.UpdateOrderRoomPlanPartialWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, v := range *toUpdateOrderProducts {
		if err := orderProductService.UpdateOrderProductPartialWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SavePayByCashCallbackSecond(logCtx *gin.Context, toUpdatePayBill *po.PayBill, toUpdateSession *po.Session, toUpdateRooms *[]po.Room, toUpdateOrders *[]po.Order) error {
	tx := model.DBMaster.Self.Begin()
	if err := payBillService.UpdatePayBillPartialWithTx(logCtx, toUpdatePayBill, tx); err != nil {
		tx.Rollback()
		return err
	}
	if err := sessionService.UpdateSessionPartialWithTx(logCtx, toUpdateSession, tx); err != nil {
		tx.Rollback()
		return err
	}
	for _, toUpdateRoom := range *toUpdateRooms {
		if err := roomService.UpdateRoomPartialWithTx(logCtx, &toUpdateRoom, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	for _, order := range *toUpdateOrders {
		if err := orderService.UpdateOrderPartialWithTx(logCtx, &order, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) AfterPayCallbackCoUpdateInfoByPayId(logCtx *gin.Context, payId string) error {
	dbPayBills, err := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{BillId: &payId})
	if err != nil || len(*dbPayBills) != 1 {
		return err
	}
	// now := int64(util.TimeNowUnix())
	dbPayBill := (*dbPayBills)[0]
	toUpdatePayBill := po.PayBill{
		Id:     dbPayBill.Id,
		Status: util.GetItPtr(_const.PAY_STATUS_PAID),
		// FinishTime: &now,
	}
	venueId := dbPayBill.VenueId
	sessionId := dbPayBill.SessionId
	orderAndPays, err := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{SessionId: sessionId, BillId: &payId})
	if err != nil || len(*orderAndPays) == 0 {
		return err
	}
	orderNos := []string{}
	for _, v := range *orderAndPays {
		orderNos = append(orderNos, *v.OrderNo)
	}
	sessions, err := sessionService.FindAllSession(logCtx, &req.QuerySessionReqDto{VenueId: venueId, SessionId: sessionId})
	if err != nil || len(*sessions) == 0 {
		return err
	}
	session := (*sessions)[0]
	dbAllOrders, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{VenueId: venueId, SessionId: sessionId})
	if err != nil || len(*dbAllOrders) == 0 {
		return err
	}
	toUpdateOrders := &[]po.Order{}
	for _, v := range *dbAllOrders {
		if util.InList(*v.OrderNo, orderNos) {
			newOrderTmp := po.Order{
				Id:      v.Id,
				OrderNo: v.OrderNo,
			}
			if v.Status == nil {
				util.Wlog(logCtx).Errorf("订单状态错误nill: %s", *v.OrderNo)
				continue
			}
			if *v.Status == _const.ORDER_STATUS_UNPAID {
				newOrderTmp.Status = util.GetItPtr(_const.ORDER_STATUS_PAID)
			} else if *v.Status == _const.ORDER_STATUS_REFUNDING {
				newOrderTmp.Status = util.GetItPtr(_const.ORDER_STATUS_REFUNDED)
			} else {
				util.Wlog(logCtx).Errorf("未知订单状态: %s, %s", *v.OrderNo, *v.Status)
			}
			*toUpdateOrders = append(*toUpdateOrders, newOrderTmp)
		}
	}
	err = service.SavePayByCashCallbackPre(logCtx, &toUpdatePayBill, toUpdateOrders, &[]po.OrderRoomPlan{}, &[]po.OrderProduct{})
	if err != nil {
		return err
	}
	service.ReCalcSessionFees(logCtx, *session.Id, *session.SessionId, *venueId)
	return nil
}

// 退款成功后通过payid更新session
func (service *PayService) AfterRefundCallbackCoUpdateInfoByPayId(logCtx *gin.Context, refundId string) error {
	dbPayBills, err := payBillService.FindAllPayBill(logCtx, &req.QueryPayBillReqDto{BillId: &refundId})
	if err != nil || len(*dbPayBills) != 1 {
		return err
	}
	// now := int64(util.TimeNowUnix())
	dbPayBill := (*dbPayBills)[0]
	toUpdatePayBill := po.PayBill{
		Id:     dbPayBill.Id,
		Status: util.GetItPtr(_const.PAY_STATUS_REFUND),
		// FinishTime: &now,
	}
	venueId := dbPayBill.VenueId
	sessionId := dbPayBill.SessionId
	sessions, err := sessionService.FindAllSession(logCtx, &req.QuerySessionReqDto{SessionId: sessionId, VenueId: venueId})
	if err != nil || len(*sessions) == 0 {
		return err
	}
	session := (*sessions)[0]
	orderAndPays, err := orderAndPayService.FindAllOrderAndPay(logCtx, &req.QueryOrderAndPayReqDto{SessionId: sessionId, BillId: &refundId})
	if err != nil || len(*orderAndPays) == 0 {
		return err
	}
	orderNos := []string{}
	for _, v := range *orderAndPays {
		orderNos = append(orderNos, *v.OrderNo)
	}
	dbOrders, err := orderService.FindAllOrder(logCtx, &req.QueryOrderReqDto{OrderNos: &orderNos, VenueId: venueId})
	if err != nil || len(*dbOrders) == 0 {
		return err
	}
	toUpdateOrders := []po.Order{}
	for _, v := range *dbOrders {
		toUpdateOrders = append(toUpdateOrders, po.Order{
			Id:      v.Id,
			OrderNo: v.OrderNo,
			Status:  util.GetItPtr(_const.ORDER_STATUS_REFUNDED),
		})
	}

	// 更新支付单状态
	err = service.SaveRefundBySourceChannelCallbackPre(logCtx, &toUpdatePayBill, &toUpdateOrders)
	if err != nil {
		return err
	}
	// 3. 重新计算费用，更新session
	err = service.ReCalcSessionFees(logCtx, *session.Id, *session.SessionId, *venueId)
	if err != nil {
		return err
	}
	return nil
}

func (service *PayService) ReCalcSessionFees(logCtx *gin.Context, sessionIdPK, sessionId, venueId string) error {
	// 3. 重新计算费用，更新session
	totalAmount, unpaidAmount, supermarketAmount, roomAmount, lastMinimumChargeInSession := orderFeeCalcService.CalculateOrderFee(logCtx, venueId, sessionId, nil)

	// 查询联台的房间,房间中sessionId一样的为联台的房间
	toUpdateRooms := []po.Room{}
	roomsAttachs, err := roomService.FindAllRoom(logCtx, &req.QueryRoomReqDto{VenueId: &venueId, SessionId: &sessionId})
	if err != nil || len(*roomsAttachs) == 0 {
		return err
	}
	// 更新room
	for _, room := range *roomsAttachs {
		tmpRoomId := *room.Id
		newRoom := po.Room{
			Id: &tmpRoomId,
		}
		if unpaidAmount == 0 && room.Status != nil && *room.Status == _const.ROOM_STATUS_AFTER_USE {
			newRoom.SessionId = new(string)
			newRoom.Status = util.GetItPtr(_const.ROOM_STATUS_CLEANING)
			newRoom.Tag = new(string)
		}
		toUpdateRooms = append(toUpdateRooms, newRoom)
	}
	toUpdateSession := po.Session{
		Id:             &sessionIdPK,
		SessionId:      &sessionId,
		MinConsume:     util.GetItPtr(lastMinimumChargeInSession),
		RoomFee:        util.GetItPtr(roomAmount),
		SupermarketFee: util.GetItPtr(supermarketAmount),
		TotalFee:       util.GetItPtr(totalAmount),
		UnpaidAmount:   util.GetItPtr(unpaidAmount),
	}
	if unpaidAmount == 0 { // 全部支付
		toUpdateSession.Status = util.GetItPtr(_const.ORDER_STATUS_PAID)
	}

	err = service.UpdateSessionAndRooms(logCtx, &toUpdateSession, &toUpdateRooms)
	if err != nil {
		return err
	}
	return nil
}

func (service *PayService) UpdateSessionAndRooms(logCtx *gin.Context, toUpdateSession *po.Session, toUpdateRooms *[]po.Room) error {
	tx := model.DBMaster.Self.Begin()
	if err := sessionService.UpdateSessionPartialWithTx(logCtx, toUpdateSession, tx); err != nil {
		tx.Rollback()
		return err
	}
	for _, v := range *toUpdateRooms {
		if err := roomService.UpdateRoomPartialWithTx(logCtx, &v, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

func (service *PayService) SendNATSMessageForRoomStatusChanged(logCtx *gin.Context, venueId string) error {
	notificationService.SendRoomStatusChangedMessage(logCtx, venueId)
	return nil
}

// GetOrderInfoBatchBySessionId 获取session下的所有订单相关PO数据
func (service *PayService) GetOrderInfoBatchBySessionId(logCtx *gin.Context, sessionId string, venueId string, roomId string, employeeId string) (vo.ModeOrderInfoBaseSessionPO, error) {
	venue, err := venueService.FindVenueById(logCtx, venueId)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	room, err := roomService.FindRoomById(logCtx, roomId)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	session, err := sessionService.FindBySessionId(logCtx, sessionId, venueId)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	employee, err := employeeService.FindEmployeeById(logCtx, employeeId)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	orders, err := orderService.FindOrderBySessionId(logCtx, venueId, sessionId, nil)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	orderNos := []string{}
	for _, v := range orders {
		orderNos = append(orderNos, *v.OrderNo)
	}
	orderRoomPlans, err := orderRoomPlanService.FindOrderRoomPlanByOrderNos(logCtx, venueId, sessionId, orderNos)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	orderProducts, err := orderProductService.FindOrderProductByOrderNos(logCtx, venueId, sessionId, orderNos)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	orderAndPays, err := orderAndPayService.FindsByOrderNos(logCtx, orderNos)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	payBills, err := payBillService.FindsBySessionIds(logCtx, venueId, []string{sessionId})
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	billIds := []string{}
	for _, v := range payBills {
		billIds = append(billIds, *v.BillId)
	}
	payRecords, err := payRecordService.FindsByBillIds(logCtx, venueId, billIds)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	orderInfoPO := vo.ModeOrderInfoBaseSessionPO{
		Venue:          *venue,
		Room:           *room,
		Session:        session,
		Employee:       *employee,
		Orders:         orders,
		OrderProducts:  *orderProducts,
		OrderRoomPlans: *orderRoomPlans,
		OrderAndPays:   orderAndPays,
		PayBills:       payBills,
		PayRecords:     payRecords,
	}
	return orderInfoPO, nil
}
