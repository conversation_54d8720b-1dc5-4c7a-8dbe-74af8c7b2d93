package application

import (
	"errors"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/venue/model/valueobject"
	"voderpltvv/erp_managent/domain/venue/model/venuepaytypesetting"
	"voderpltvv/erp_managent/domain/venue/repository"
	"voderpltvv/erp_managent/infra/repository/venue"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/transfer"

	"github.com/gin-gonic/gin"
)

var (
	// 使用repository接口而不是直接使用service
	venuePayTypeSettingRepo     repository.VenuePayTypeSettingRepository = venue.NewVenuePayTypeSettingRepositoryImpl()
	venuePayTypeSettingTransfer                                          = transfer.VenuePayTypeSettingTransfer{}
	payRecordService                                                     = &impl.PayRecordService{}
)

// VenuePayTypeSettingAppService 门店支付类型设置应用服务接口
type VenuePayTypeSettingAppService interface {
	// AddVenuePayTypeSetting 添加门店支付类型设置
	AddVenuePayTypeSetting(ctx *gin.Context, reqDto req.AddVenuePayTypeSettingReqDto) (*vo.VenuePayTypeSettingVO, error)
	// UpdateVenuePayTypeSetting 更新门店支付类型设置
	UpdateVenuePayTypeSetting(ctx *gin.Context, reqDto req.UpdateVenuePayTypeSettingReqDto) (*vo.VenuePayTypeSettingVO, error)
	// DeleteVenuePayTypeSetting 删除门店支付类型设置
	DeleteVenuePayTypeSetting(ctx *gin.Context, reqDto req.DeleteVenuePayTypeSettingReqDto) error
	// FindOneVenuePayTypeSetting 查询单个门店支付类型设置
	FindOneVenuePayTypeSetting(ctx *gin.Context, reqDto req.QueryVenuePayTypeSettingFindReqDto) (*vo.VenuePayTypeSettingVO, error)
	// FindLastPayTypeSettingByIds 根据门店ID列表查询支付类型设置
	FindLastPayTypeSettingByIds(ctx *gin.Context, venueIds []string) (*vo.VenuePayTypeSettingVO, error)
}

// VenuePayTypeSettingAppServiceImpl 门店支付类型设置应用服务实现
type VenuePayTypeSettingAppServiceImpl struct {
	repository repository.VenuePayTypeSettingRepository
}

// NewVenuePayTypeSettingAppService 创建门店支付类型设置应用服务
func NewVenuePayTypeSettingAppService() VenuePayTypeSettingAppService {
	return &VenuePayTypeSettingAppServiceImpl{
		repository: venuePayTypeSettingRepo,
	}
}

// validatePayTypeSettings 验证支付方式配置
func (s *VenuePayTypeSettingAppServiceImpl) validatePayTypeSettings(typeInfoList []valueobject.PayTypeConfig) error {
	// 检查数量是否大于等于预设支付方式数量
	if len(typeInfoList) < len(_const.PAY_TYPE_ALL) {
		return errors.New("支付方式配置数量不能少于预设支付方式数量")
	}

	if len(typeInfoList) > 0 {
		// 检查是否有重复的payType
		payTypeMap := make(map[string]bool)
		labelMap := make(map[string]bool)
		for _, typeInfo := range typeInfoList {
			if payTypeMap[typeInfo.PayType] {
				return errors.New("支付类型 " + typeInfo.PayType + " 重复，不允许添加相同的支付类型")
			}
			payTypeMap[typeInfo.PayType] = true

			// 检查是否有重复的Label
			if labelMap[typeInfo.Label] {
				return errors.New("支付类型名称 " + typeInfo.Label + " 重复，不允许添加相同的支付类型名称")
			}
			labelMap[typeInfo.Label] = true
		}

		validPayTypes := make(map[string]bool)
		for _, presetPayType := range _const.PAY_TYPE_ALL {
			validPayTypes[presetPayType.PayType] = true
		}

		// 只验证isPreset为true的支付方式
		for _, typeInfo := range typeInfoList {
			if typeInfo.IsPreset && !validPayTypes[typeInfo.PayType] {
				return errors.New("支付类型 " + typeInfo.PayType + " 不是预设的支付方式")
			}
		}
	}
	return nil
}

// AddVenuePayTypeSetting 添加门店支付类型设置
func (s *VenuePayTypeSettingAppServiceImpl) AddVenuePayTypeSetting(ctx *gin.Context, reqDto req.AddVenuePayTypeSettingReqDto) (*vo.VenuePayTypeSettingVO, error) {
	// 1. 参数校验
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, errors.New("门店ID不能为空")
	}

	// 2. 验证支付方式配置
	if err := s.validatePayTypeSettings(reqDto.TypeInfoList); err != nil {
		return nil, err
	}

	// 3. 构建支付类型配置列表
	var payTypeConfigList *valueobject.PayTypeConfigList
	if len(reqDto.TypeInfoList) > 0 {
		payTypeConfigList = valueobject.NewPayTypeConfigList(reqDto.TypeInfoList)
	} else {
		// 如果没有提供配置，创建一个空的配置列表
		payTypeConfigList = valueobject.NewPayTypeConfigList([]valueobject.PayTypeConfig{})
	}

	// 4. 构建领域模型
	remark := ""
	if reqDto.Remark != nil {
		remark = *reqDto.Remark
	}

	setting, err := venuepaytypesetting.NewVenuePayTypeSetting(
		*reqDto.VenueId,
		payTypeConfigList,
		remark,
	)
	if err != nil {
		return nil, errors.New("创建门店支付类型设置失败：" + err.Error())
	}

	// 5. 保存到仓储
	err = s.repository.Save(ctx, setting)
	if err != nil {
		return nil, err
	}

	// 6. 转换并返回VO
	vo := venuePayTypeSettingTransfer.DomainToVo(setting)
	return &vo, nil
}

// UpdateVenuePayTypeSetting 更新门店支付类型设置
func (s *VenuePayTypeSettingAppServiceImpl) UpdateVenuePayTypeSetting(ctx *gin.Context, reqDto req.UpdateVenuePayTypeSettingReqDto) (*vo.VenuePayTypeSettingVO, error) {
	// 1. 参数验证
	if reqDto.Id == nil || *reqDto.Id == "" {
		return nil, errors.New("id不能为空")
	}

	// 2. 验证旧支付类型是否有支付记录
	if reqDto.OldPaytype != nil && *reqDto.OldPaytype != "" && reqDto.VenueId != nil && *reqDto.VenueId != "" {
		// 查询该门店是否有使用该支付类型的记录
		payRecords, err := payRecordService.FindAllPayRecord(ctx, &req.QueryPayRecordReqDto{
			VenueId: reqDto.VenueId,
			PayType: reqDto.OldPaytype,
		})
		if err != nil {
			return nil, errors.New("查询支付记录失败：" + err.Error())
		}
		if payRecords != nil && len(*payRecords) > 0 {
			return nil, errors.New("支付类型 " + *reqDto.OldPaytype + " 已有支付记录，不允许修改")
		}
	}

	// 3. 验证支付方式配置
	if err := s.validatePayTypeSettings(reqDto.TypeInfoList); err != nil {
		return nil, err
	}

	// 4. 查询现有记录
	setting, err := s.repository.FindByID(ctx, *reqDto.Id)
	if err != nil {
		return nil, err
	}
	if setting == nil {
		return nil, errors.New("门店支付类型设置不存在")
	}

	// 5. 更新字段
	if len(reqDto.TypeInfoList) > 0 {
		payTypeConfigList := valueobject.NewPayTypeConfigList(reqDto.TypeInfoList)
		err = setting.SetPayTypeConfigs(payTypeConfigList)
		if err != nil {
			return nil, errors.New("更新支付类型配置失败：" + err.Error())
		}
	}

	if reqDto.Remark != nil {
		setting.SetRemark(*reqDto.Remark)
	}

	// 6. 执行更新
	err = s.repository.Update(ctx, setting)
	if err != nil {
		return nil, err
	}

	// 7. 转换并返回VO
	vo := venuePayTypeSettingTransfer.DomainToVo(setting)
	return &vo, nil
}

// DeleteVenuePayTypeSetting 删除门店支付类型设置
func (s *VenuePayTypeSettingAppServiceImpl) DeleteVenuePayTypeSetting(ctx *gin.Context, reqDto req.DeleteVenuePayTypeSettingReqDto) error {
	// 1. 参数验证
	if reqDto.Id == nil || *reqDto.Id == "" {
		return errors.New("id不能为空")
	}

	// 2. 执行删除
	err := s.repository.Delete(ctx, *reqDto.Id)
	if err != nil {
		return err
	}

	return nil
}

// FindOneVenuePayTypeSetting 查询单个门店支付类型设置
func (s *VenuePayTypeSettingAppServiceImpl) FindOneVenuePayTypeSetting(ctx *gin.Context, reqDto req.QueryVenuePayTypeSettingFindReqDto) (*vo.VenuePayTypeSettingVO, error) {
	// 1. 参数验证
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, errors.New("venueId不能为空")
	}

	// 2. 查询数据
	setting, err := s.repository.FindLatestByVenueID(ctx, *reqDto.VenueId)
	if err != nil {
		return nil, err
	}
	if setting == nil {
		return nil, errors.New("门店支付类型设置不存在")
	}

	// 3. 转换为VO
	resultVO := venuePayTypeSettingTransfer.DomainToVo(setting)

	// 4. 检查是否缺少预设支付方式，如果缺少则补全
	existingPayTypes := make(map[string]bool)
	maxSort := 0
	for _, config := range resultVO.TypeInfoList {
		existingPayTypes[config.PayType] = true
		if config.Sort > maxSort {
			maxSort = config.Sort
		}
	}

	// 查找缺失的预设支付方式
	var missingPayTypes []valueobject.PayTypeConfig
	sortOffset := 1
	for _, presetPayType := range _const.PAY_TYPE_ALL {
		if !existingPayTypes[presetPayType.PayType] {
			missingPayType := valueobject.PayTypeConfig{
				PayType:     presetPayType.PayType,
				Label:       presetPayType.Label,
				Enabled:     false, // 缺失的预设支付方式设置为false
				IsPreset:    presetPayType.IsPreset,
				Sort:        maxSort + sortOffset, // 从最大sort值开始递增
				IsNetProfit: presetPayType.IsNetProfit,
			}
			missingPayTypes = append(missingPayTypes, missingPayType)
			sortOffset++
		}
	}

	// 将缺失的支付方式添加到结果中
	resultVO.TypeInfoList = append(resultVO.TypeInfoList, missingPayTypes...)

	return &resultVO, nil
}

// FindLastPayTypeSettingByIds 根据门店ID列表查询支付类型设置
func (s *VenuePayTypeSettingAppServiceImpl) FindLastPayTypeSettingByIds(ctx *gin.Context, venueIds []string) (*vo.VenuePayTypeSettingVO, error) {
	if len(venueIds) == 0 {
		return nil, errors.New("门店ID列表不能为空")
	}

	// 1. 根据门店ID列表查询设置
	settings, err := s.repository.FindByVenueIDs(ctx, venueIds)
	if err != nil {
		return nil, err
	}

	if len(settings) == 0 {
		return nil, errors.New("未找到对应的门店支付类型设置")
	}

	// 2. 返回第一个设置（或者可以根据业务需求选择最新的）
	vo := venuePayTypeSettingTransfer.DomainToVo(settings[0])
	return &vo, nil
}
