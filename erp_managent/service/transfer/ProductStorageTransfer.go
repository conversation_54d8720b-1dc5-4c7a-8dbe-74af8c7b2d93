package transfer

import (
	"time"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductStorageTransfer 酒水存储转换器
type ProductStorageTransfer struct{}

// 辅助函数，处理nil指针
func nilToStringDefault(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func intPtrToInt(ptr *int) int {
	if ptr == nil {
		return 0
	}
	return *ptr
}

func int64PtrToInt64(ptr *int64) int64 {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// PoToVo 将PO转换为VO
func (transfer ProductStorageTransfer) PoToVo(productStorage po.ProductStorage) vo.ProductStorageVO {
	voObj := vo.ProductStorageVO{
		Id:               string(*productStorage.Id),
		OrderNo:          string(*productStorage.OrderNo),
		ParentOrderNo:    nilToStringDefault(productStorage.ParentOrderNo),
		VenueId:          string(*productStorage.VenueId),
		CustomerId:       string(*productStorage.CustomerId),
		CustomerName:     string(*productStorage.CustomerName),
		PhoneNumber:      string(*productStorage.PhoneNumber),
		MemberCardNo:     nilToStringDefault(productStorage.MemberCardNo), // 保留原字段
		MemberCardNumber: nilToStringDefault(productStorage.MemberCardNumber),
		MemberCardId:     nilToStringDefault(productStorage.MemberCardId),
		ProductId:        string(*productStorage.ProductId),
		ProductName:      string(*productStorage.ProductName),
		ProductType:      nilToStringDefault(productStorage.ProductType),
		ProductUnit:      nilToStringDefault(productStorage.ProductUnit),
		ProductSpec:      nilToStringDefault(productStorage.ProductSpec),
		Quantity:         intPtrToInt(productStorage.Quantity),
		RemainingQty:     intPtrToInt(productStorage.RemainingQty),
		StorageLocation:  string(*productStorage.StorageLocation),
		StorageRoomId:    nilToStringDefault(productStorage.StorageRoomId),
		StorageRoomName:  nilToStringDefault(productStorage.StorageRoomName),
		StorageTime:      int64PtrToInt64(productStorage.StorageTime),
		ExpireTime:       int64PtrToInt64(productStorage.ExpireTime),
		Remark:           string(*productStorage.Remark),
		OperatorId:       string(*productStorage.OperatorId),
		OperatorName:     string(*productStorage.OperatorName),
		IsBatch:          intPtrToInt(productStorage.IsBatch),
		BatchTime:        int64PtrToInt64(productStorage.BatchTime),
		Ctime:            int64PtrToInt64(productStorage.Ctime),
		Utime:            int64PtrToInt64(productStorage.Utime),
		State:            intPtrToInt(productStorage.State),
		Version:          intPtrToInt(productStorage.Version),
		OfflineOnly:      false,
	}

	// 添加新字段转换
	if productStorage.StatusCode != nil {
		voObj.StatusCode = string(*productStorage.StatusCode)
	}
	if productStorage.StatusName != nil {
		voObj.StatusName = string(*productStorage.StatusName)
	}
	if productStorage.LastOperationTime != nil {
		voObj.LastOperationTime = int64PtrToInt64(productStorage.LastOperationTime)
	}
	if productStorage.OperatorId != nil {
		voObj.OperatorId = string(*productStorage.OperatorId)
	}
	if productStorage.OperatorName != nil {
		voObj.OperatorName = string(*productStorage.OperatorName)
	}

	// 计算状态信息(如果数据库中没有)
	if voObj.StatusCode == "" {
		if voObj.State == 1 {
			voObj.StatusCode = "discarded"
			voObj.StatusName = "已报废"
		} else if voObj.RemainingQty == 0 {
			voObj.StatusCode = "withdrawn"
			voObj.StatusName = "已取完"
		} else if voObj.RemainingQty < voObj.Quantity {
			voObj.StatusCode = "partial"
			voObj.StatusName = "部分支取"
		} else {
			voObj.StatusCode = "stored"
			voObj.StatusName = "已存"
		}
	}

	// 计算剩余比例
	if voObj.Quantity > 0 {
		voObj.RemainingRatio = int(float64(voObj.RemainingQty) / float64(voObj.Quantity) * 100)
	}

	// 计算到期信息
	nowUnix := time.Now().Unix()
	if voObj.ExpireTime > 0 {
		voObj.DaysToExpire = int((voObj.ExpireTime - nowUnix) / 86400)
		voObj.IsExpired = voObj.ExpireTime < nowUnix
		voObj.IsExpiringSoon = !voObj.IsExpired && voObj.DaysToExpire <= 30
	}

	return voObj
}

// VoToPo 将VO转换为PO
func (transfer ProductStorageTransfer) VoToPo(productStorageVO vo.ProductStorageVO) po.ProductStorage {
	var id, orderNo, venueId, customerId, customerName, phoneNumber, productId, productName, storageLocation, remark string
	var productUnit, productSpec, parentOrderNo, statusCode, statusName, operatorId, operatorName *string
	var storageRoomId, storageRoomName, productType *string
	var quantity, remainingQty, isBatch, state, version *int
	var storageTime, expireTime, batchTime, ctime, utime, lastOperationTime *int64

	if productStorageVO.Id != "" {
		id = productStorageVO.Id
	}
	if productStorageVO.OrderNo != "" {
		orderNo = productStorageVO.OrderNo
	}
	if productStorageVO.ParentOrderNo != "" {
		parentOrderNo = &productStorageVO.ParentOrderNo
	}
	if productStorageVO.VenueId != "" {
		venueId = productStorageVO.VenueId
	}
	if productStorageVO.CustomerId != "" {
		customerId = productStorageVO.CustomerId
	}
	if productStorageVO.CustomerName != "" {
		customerName = productStorageVO.CustomerName
	}
	if productStorageVO.PhoneNumber != "" {
		phoneNumber = productStorageVO.PhoneNumber
	}
	var memberCardNumber, memberCardId *string
	if productStorageVO.MemberCardNumber != "" {
		memberCardNumber = &productStorageVO.MemberCardNumber
	}
	if productStorageVO.MemberCardId != "" {
		memberCardId = &productStorageVO.MemberCardId
	}
	if productStorageVO.ProductId != "" {
		productId = productStorageVO.ProductId
	}
	if productStorageVO.ProductName != "" {
		productName = productStorageVO.ProductName
	}
	if productStorageVO.ProductType != "" {
		productType = &productStorageVO.ProductType
	}
	if productStorageVO.ProductUnit != "" {
		productUnit = &productStorageVO.ProductUnit
	}
	if productStorageVO.ProductSpec != "" {
		productSpec = &productStorageVO.ProductSpec
	}
	quantity = &productStorageVO.Quantity
	remainingQty = &productStorageVO.RemainingQty
	if productStorageVO.StorageLocation != "" {
		storageLocation = productStorageVO.StorageLocation
	}
	if productStorageVO.StorageRoomId != "" {
		storageRoomId = &productStorageVO.StorageRoomId
	}
	if productStorageVO.StorageRoomName != "" {
		storageRoomName = &productStorageVO.StorageRoomName
	}
	if productStorageVO.StorageTime != 0 {
		storageTimeVal := productStorageVO.StorageTime
		storageTime = &storageTimeVal
	}
	if productStorageVO.ExpireTime != 0 {
		expireTimeVal := productStorageVO.ExpireTime
		expireTime = &expireTimeVal
	}
	if productStorageVO.Remark != "" {
		remark = productStorageVO.Remark
	}
	if productStorageVO.OperatorId != "" {
		operatorId = &productStorageVO.OperatorId
	}
	if productStorageVO.OperatorName != "" {
		operatorName = &productStorageVO.OperatorName
	}
	isBatchVal := productStorageVO.IsBatch
	isBatch = &isBatchVal
	if productStorageVO.BatchTime != 0 {
		batchTimeVal := productStorageVO.BatchTime
		batchTime = &batchTimeVal
	}
	if productStorageVO.Ctime != 0 {
		ctimeVal := productStorageVO.Ctime
		ctime = &ctimeVal
	}
	if productStorageVO.Utime != 0 {
		utimeVal := productStorageVO.Utime
		utime = &utimeVal
	}
	stateVal := productStorageVO.State
	state = &stateVal
	versionVal := productStorageVO.Version
	version = &versionVal

	// 处理新增字段
	if productStorageVO.StatusCode != "" {
		statusCode = &productStorageVO.StatusCode
	}
	if productStorageVO.StatusName != "" {
		statusName = &productStorageVO.StatusName
	}
	if productStorageVO.LastOperationTime != 0 {
		lastOperationTimeVal := productStorageVO.LastOperationTime
		lastOperationTime = &lastOperationTimeVal
	}

	return po.ProductStorage{
		Id:                &id,
		OrderNo:           &orderNo,
		ParentOrderNo:     parentOrderNo,
		VenueId:           &venueId,
		CustomerId:        &customerId,
		CustomerName:      &customerName,
		PhoneNumber:       &phoneNumber,
		MemberCardNumber:  memberCardNumber,
		MemberCardId:      memberCardId,
		ProductId:         &productId,
		ProductName:       &productName,
		ProductType:       productType,
		ProductUnit:       productUnit,
		ProductSpec:       productSpec,
		Quantity:          quantity,
		RemainingQty:      remainingQty,
		StorageLocation:   &storageLocation,
		StorageRoomId:     storageRoomId,
		StorageRoomName:   storageRoomName,
		StorageTime:       storageTime,
		ExpireTime:        expireTime,
		Remark:            &remark,
		OperatorId:        operatorId,
		OperatorName:      operatorName,
		IsBatch:           isBatch,
		BatchTime:         batchTime,
		Ctime:             ctime,
		Utime:             utime,
		State:             state,
		Version:           version,
		StatusCode:        statusCode,
		StatusName:        statusName,
		LastOperationTime: lastOperationTime,
	}
}

// PoToVoWithOrder 将PO转换为VO，并从订单中获取OfflineOnly信息
func (transfer ProductStorageTransfer) PoToVoWithOrder(productStorage po.ProductStorage, order *po.ProductStorageOrder) vo.ProductStorageVO {
	voObj := transfer.PoToVo(productStorage)

	// 从订单中获取OfflineOnly信息
	if order != nil && order.OfflineOnly != nil {
		voObj.OfflineOnly = *order.OfflineOnly == 1
	}

	return voObj
}
