package transfer

import (
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/venue/model/venuepaytypesetting"
	"voderpltvv/erp_managent/service/po"

	"github.com/jinzhu/copier"
)

type VenuePayTypeSettingTransfer struct {
}

// DomainToVo 将Domain模型转换为VO
func (transfer *VenuePayTypeSettingTransfer) DomainToVo(domain *venuepaytypesetting.VenuePayTypeSetting) vo.VenuePayTypeSettingVO {
	// 获取支付类型配置列表
	payTypeConfigList := domain.PayTypeConfigs().Configs()

	return vo.VenuePayTypeSettingVO{
		Id:           domain.ID(),
		VenueId:      domain.VenueID(),
		TypeInfoList: payTypeConfigList,
		Remark:       domain.Remark(),
		Ctime:        domain.CreateTime().Unix(),
		Utime:        domain.UpdateTime().Unix(),
		State:        domain.State(),
		Version:      domain.Version(),
	}
}

// DomainListToVoList 将Domain模型列表转换为VO列表
func (transfer *VenuePayTypeSettingTransfer) DomainListToVoList(domains []*venuepaytypesetting.VenuePayTypeSetting) []vo.VenuePayTypeSettingVO {
	result := make([]vo.VenuePayTypeSettingVO, 0, len(domains))
	for _, domain := range domains {
		result = append(result, transfer.DomainToVo(domain))
	}
	return result
}

func (transfer *VenuePayTypeSettingTransfer) PoToVo(po po.VenuePayTypeSetting) vo.VenuePayTypeSettingVO {
	vo := vo.VenuePayTypeSettingVO{}
	copier.Copy(&vo, &po)
	return vo
}

func (transfer *VenuePayTypeSettingTransfer) VoToPo(vo vo.VenuePayTypeSettingVO) po.VenuePayTypeSetting {
	po := po.VenuePayTypeSetting{}
	copier.Copy(&po, &vo)
	return po
}
