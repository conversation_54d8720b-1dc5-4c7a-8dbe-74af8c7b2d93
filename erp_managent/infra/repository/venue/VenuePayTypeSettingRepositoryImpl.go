package venue

import (
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/domain/venue/model/valueobject"
	"voderpltvv/erp_managent/domain/venue/model/venuepaytypesetting"
	"voderpltvv/erp_managent/domain/venue/repository"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"
)

// VenuePayTypeSettingRepositoryImpl 门店支付类型设置仓储实现
type VenuePayTypeSettingRepositoryImpl struct {
	venuePayTypeSettingService *impl.VenuePayTypeSettingService
}

// NewVenuePayTypeSettingRepositoryImpl 创建VenuePayTypeSettingRepositoryImpl实例
func NewVenuePayTypeSettingRepositoryImpl() repository.VenuePayTypeSettingRepository {
	return &VenuePayTypeSettingRepositoryImpl{
		venuePayTypeSettingService: &impl.VenuePayTypeSettingService{},
	}
}

// Save 保存门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) Save(ctx *gin.Context, setting *venuepaytypesetting.VenuePayTypeSetting) error {
	// 1. 将领域模型转换为PO
	settingPO, err := r.toPO(setting)
	if err != nil {
		return fmt.Errorf("转换领域模型到PO失败: %w", err)
	}

	if setting.ID() == "" {
		// 创建新记录
		err := r.venuePayTypeSettingService.CreateVenuePayTypeSetting(ctx, settingPO)
		if err != nil {
			return err
		}

		// 将生成的ID和其他元数据设置回领域模型
		if settingPO.Id != nil {
			setting.SetID(*settingPO.Id)
		}

		if settingPO.Ctime != nil {
			setting.SetCreateTime(time.Unix(*settingPO.Ctime, 0))
		}

		if settingPO.Utime != nil {
			setting.SetUpdateTime(time.Unix(*settingPO.Utime, 0))
		}

		if settingPO.State != nil {
			setting.SetState(*settingPO.State)
		}

		if settingPO.Version != nil {
			setting.SetVersion(*settingPO.Version)
		}

		return nil
	} else {
		// 更新已有记录
		return r.venuePayTypeSettingService.UpdateVenuePayTypeSetting(ctx, settingPO)
	}
}

// FindByID 根据ID查找门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) FindByID(ctx *gin.Context, id string) (*venuepaytypesetting.VenuePayTypeSetting, error) {
	// 1. 使用Service查询PO
	settingPO, err := r.venuePayTypeSettingService.FindVenuePayTypeSettingById(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询门店支付类型设置失败: %w", err)
	}

	// 2. 如果未找到记录，则返回nil
	if settingPO == nil || settingPO.Id == nil {
		return nil, nil
	}

	// 3. 将PO转换为领域模型
	setting, err := r.toDomainModel(settingPO)
	if err != nil {
		return nil, fmt.Errorf("转换PO到领域模型失败: %w", err)
	}

	return setting, nil
}

// FindByVenueID 根据门店ID查找门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) FindByVenueID(ctx *gin.Context, venueID string) (*venuepaytypesetting.VenuePayTypeSetting, error) {
	// 使用FindLatestByVenueID来获取最新的配置
	return r.FindLatestByVenueID(ctx, venueID)
}

// FindByVenueIDs 根据门店ID列表查找门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) FindByVenueIDs(ctx *gin.Context, venueIDs []string) ([]*venuepaytypesetting.VenuePayTypeSetting, error) {
	var result []*venuepaytypesetting.VenuePayTypeSetting

	for _, venueID := range venueIDs {
		setting, err := r.FindByVenueID(ctx, venueID)
		if err != nil {
			return nil, fmt.Errorf("查询门店ID为%s的支付类型设置失败: %w", venueID, err)
		}
		if setting != nil {
			result = append(result, setting)
		}
	}

	return result, nil
}

// FindAll 查询所有门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) FindAll(ctx *gin.Context) ([]*venuepaytypesetting.VenuePayTypeSetting, error) {
	// 创建查询条件
	reqDto := &req.QueryVenuePayTypeSettingReqDto{}

	// 调用服务查询
	settingPOs, err := r.venuePayTypeSettingService.FindAllVenuePayTypeSetting(ctx, reqDto)
	if err != nil {
		return nil, fmt.Errorf("查询所有门店支付类型设置失败: %w", err)
	}

	// 将PO转换为领域模型
	result := make([]*venuepaytypesetting.VenuePayTypeSetting, 0, len(*settingPOs))
	for _, settingPO := range *settingPOs {
		setting, err := r.toDomainModel(&settingPO)
		if err != nil {
			// 记录错误但不中断处理
			fmt.Printf("转换门店支付类型设置[%s]失败: %v\n", *settingPO.Id, err)
			continue
		}
		result = append(result, setting)
	}

	return result, nil
}

// Update 更新门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) Update(ctx *gin.Context, setting *venuepaytypesetting.VenuePayTypeSetting) error {
	// 将领域模型转换为PO
	settingPO, err := r.toPO(setting)
	if err != nil {
		return fmt.Errorf("转换领域模型到PO失败: %w", err)
	}

	// 更新记录
	return r.venuePayTypeSettingService.UpdateVenuePayTypeSetting(ctx, settingPO)
}

// Delete 删除门店支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) Delete(ctx *gin.Context, id string) error {
	return r.venuePayTypeSettingService.DeleteVenuePayTypeSetting(ctx, id)
}

// FindLatestByVenueID 查找门店最新的支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) FindLatestByVenueID(ctx *gin.Context, venueID string) (*venuepaytypesetting.VenuePayTypeSetting, error) {
	// 使用service的FindLastPayTypeSetting方法
	settingPO, err := r.venuePayTypeSettingService.FindLastPayTypeSetting(ctx, venueID)
	if err != nil {
		return nil, fmt.Errorf("查询门店最新支付类型设置失败: %w", err)
	}

	if settingPO == nil {
		return nil, nil
	}

	// 将PO转换为领域模型
	setting, err := r.toDomainModel(settingPO)
	if err != nil {
		return nil, fmt.Errorf("转换PO到领域模型失败: %w", err)
	}

	return setting, nil
}

// ExistsByVenueID 检查门店是否存在支付类型设置
func (r *VenuePayTypeSettingRepositoryImpl) ExistsByVenueID(ctx *gin.Context, venueID string) (bool, error) {
	setting, err := r.FindByVenueID(ctx, venueID)
	if err != nil {
		return false, err
	}
	return setting != nil, nil
}

// toPO 将领域模型转换为PO
func (r *VenuePayTypeSettingRepositoryImpl) toPO(setting *venuepaytypesetting.VenuePayTypeSetting) (*po.VenuePayTypeSetting, error) {
	settingPO := &po.VenuePayTypeSetting{}

	// 设置基本字段
	if setting.ID() != "" {
		settingPO.Id = util.GetItPtr(setting.ID())
	}

	settingPO.VenueId = util.GetItPtr(setting.VenueID())
	settingPO.Remark = util.GetItPtr(setting.Remark())

	// 将支付类型配置转换为JSON字符串
	if setting.PayTypeConfigs() != nil {
		jsonStr, err := setting.PayTypeConfigs().ToJSON()
		if err != nil {
			return nil, fmt.Errorf("支付类型配置序列化失败: %w", err)
		}
		settingPO.TypeInfo = util.GetItPtr(jsonStr)
	}

	// 设置元数据
	if !setting.CreateTime().IsZero() {
		settingPO.Ctime = util.GetItPtr(setting.CreateTime().Unix())
	}

	if !setting.UpdateTime().IsZero() {
		settingPO.Utime = util.GetItPtr(setting.UpdateTime().Unix())
	}

	settingPO.State = util.GetItPtr(setting.State())
	settingPO.Version = util.GetItPtr(setting.Version())

	return settingPO, nil
}

// toDomainModel 将PO转换为领域模型
func (r *VenuePayTypeSettingRepositoryImpl) toDomainModel(settingPO *po.VenuePayTypeSetting) (*venuepaytypesetting.VenuePayTypeSetting, error) {
	// 参数校验
	if settingPO == nil {
		return nil, errors.New("settingPO不能为nil")
	}

	if settingPO.VenueId == nil || *settingPO.VenueId == "" {
		return nil, errors.New("门店ID不能为空")
	}

	// 解析支付类型配置
	var payTypeConfigs *valueobject.PayTypeConfigList
	if settingPO.TypeInfo != nil && *settingPO.TypeInfo != "" {
		var err error
		payTypeConfigs, err = valueobject.FromJSONArray(*settingPO.TypeInfo)
		if err != nil {
			return nil, fmt.Errorf("支付类型配置反序列化失败: %w", err)
		}
	} else {
		// 如果没有配置，使用空列表
		payTypeConfigs = valueobject.NewEmptyPayTypeConfigList()
	}

	// 获取备注
	remark := ""
	if settingPO.Remark != nil {
		remark = *settingPO.Remark
	}

	// 创建领域模型
	setting, err := venuepaytypesetting.NewVenuePayTypeSetting(
		*settingPO.VenueId,
		payTypeConfigs,
		remark,
	)
	if err != nil {
		return nil, fmt.Errorf("创建领域模型失败: %w", err)
	}

	// 设置ID和元数据
	if settingPO.Id != nil {
		setting.SetID(*settingPO.Id)
	}

	if settingPO.Ctime != nil {
		setting.SetCreateTime(time.Unix(*settingPO.Ctime, 0))
	}

	if settingPO.Utime != nil {
		setting.SetUpdateTime(time.Unix(*settingPO.Utime, 0))
	}

	if settingPO.State != nil {
		setting.SetState(*settingPO.State)
	}

	if settingPO.Version != nil {
		setting.SetVersion(*settingPO.Version)
	}

	return setting, nil
}
