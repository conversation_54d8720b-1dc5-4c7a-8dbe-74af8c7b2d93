package repository

import (
	"errors"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/inventory/model"
	"voderpltvv/erp_managent/domain/inventory/repository"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	model_db "voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// InventoryRepositoryImpl 库存仓储实现
type InventoryRepositoryImpl struct {
	inventoryRecordService     *impl.InventoryRecordService
	inventoryRecordItemService *impl.InventoryRecordItemService
	productStockService        *impl.ProductStockService
}

// NewInventoryRepository 创建库存仓储实例
func NewInventoryRepository() repository.InventoryRepository {
	return &InventoryRepositoryImpl{
		inventoryRecordService:     &impl.InventoryRecordService{},
		inventoryRecordItemService: &impl.InventoryRecordItemService{},
		productStockService:        &impl.ProductStockService{},
	}
}

// SaveInboundRecord 保存入库记录
func (r *InventoryRepositoryImpl) SaveInboundRecord(ctx *gin.Context, record *model.InboundRecord) error {
	// 转换为PO实体
	recordPO := record.ToPO()
	itemsPO := record.ItemsToPO()

	// 在事务中保存主记录和明细
	return model_db.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 保存主记录
		if err := r.inventoryRecordService.CreateInventoryRecordWithTx(ctx, recordPO, tx); err != nil {
			return err
		}

		// 设置记录ID到领域实体
		record.SetId(*recordPO.Id)

		// 保存明细记录
		for _, item := range itemsPO {
			item.InventoryRecordId = recordPO.Id
			if err := r.inventoryRecordItemService.CreateInventoryRecordItemWithTx(ctx, &item, tx); err != nil {
				return err
			}
		}

		return nil
	})
}

// SaveInboundRecordWithTx 在事务中保存入库记录
func (r *InventoryRepositoryImpl) SaveInboundRecordWithTx(ctx *gin.Context, record *model.InboundRecord, tx interface{}) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return errors.New("invalid transaction type")
	}

	// 转换为PO实体
	recordPO := record.ToPO()
	itemsPO := record.ItemsToPO()

	// 保存主记录
	if err := r.inventoryRecordService.CreateInventoryRecordWithTx(ctx, recordPO, gormTx); err != nil {
		return err
	}

	// 设置记录ID到领域实体
	record.SetId(*recordPO.Id)

	// 保存明细记录
	for _, item := range itemsPO {
		item.InventoryRecordId = recordPO.Id
		if err := r.inventoryRecordItemService.CreateInventoryRecordItemWithTx(ctx, &item, gormTx); err != nil {
			return err
		}
	}

	return nil
}

// FindInboundRecordById 根据ID查找入库记录
func (r *InventoryRepositoryImpl) FindInboundRecordById(ctx *gin.Context, id string) (*model.InboundRecord, error) {
	// 查找主记录
	recordPO, err := r.inventoryRecordService.FindInventoryRecordById(ctx, id)
	if err != nil {
		return nil, err
	}

	// 查找明细记录
	itemsPO, err := r.inventoryRecordItemService.FindInventoryRecordItemsByRecordId(ctx, id)
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	return model.FromPO(recordPO, *itemsPO), nil
}

// FindInboundRecords 查找门店的入库记录
func (r *InventoryRepositoryImpl) FindInboundRecords(ctx *gin.Context, venueId string) ([]*model.InboundRecord, error) {
	// 查找主记录
	records, err := r.inventoryRecordService.FindInventoryRecordsByVenueAndType(ctx, venueId, _const.INVENTORY_RECORD_TYPE_INBOUND)
	if err != nil {
		return nil, err
	}

	var result []*model.InboundRecord
	for _, recordPO := range *records {
		// 查找明细记录
		itemsPO, err := r.inventoryRecordItemService.FindInventoryRecordItemsByRecordId(ctx, *recordPO.Id)
		if err != nil {
			return nil, err
		}

		// 转换为领域实体
		domainRecord := model.FromPO(&recordPO, *itemsPO)
		result = append(result, domainRecord)
	}

	return result, nil
}

// FindInboundRecordsByCondition 根据条件查找入库记录
func (r *InventoryRepositoryImpl) FindInboundRecordsByCondition(ctx *gin.Context, condition map[string]interface{}) ([]*model.InboundRecord, error) {
	records, err := r.inventoryRecordService.FindInventoryRecordsByConditionAndType(ctx, _const.INVENTORY_RECORD_TYPE_INBOUND, condition)
	if err != nil {
		return nil, err
	}

	var result []*model.InboundRecord
	for _, recordPO := range *records {
		// 查找明细记录
		itemsPO, err := r.inventoryRecordItemService.FindInventoryRecordItemsByRecordId(ctx, *recordPO.Id)
		if err != nil {
			return nil, err
		}

		// 转换为领域实体
		domainRecord := model.FromPO(&recordPO, *itemsPO)
		result = append(result, domainRecord)
	}

	return result, nil
}

// UpdateInboundRecord 更新入库记录
func (r *InventoryRepositoryImpl) UpdateInboundRecord(ctx *gin.Context, record *model.InboundRecord) error {
	// 转换为PO实体
	recordPO := record.ToPO()

	// 更新主记录
	return r.inventoryRecordService.UpdateInventoryRecordPartial(ctx, recordPO)
}

// DeleteInboundRecord 删除入库记录
func (r *InventoryRepositoryImpl) DeleteInboundRecord(ctx *gin.Context, id string) error {
	// 在事务中删除主记录和明细
	return model_db.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 删除明细记录
		if err := r.inventoryRecordItemService.DeleteInventoryRecordItemsByRecordIdWithTx(ctx, id, tx); err != nil {
			return err
		}

		// 删除主记录
		if err := r.inventoryRecordService.DeleteInventoryRecordWithTx(ctx, id, tx); err != nil {
			return err
		}

		return nil
	})
}

// GetProductStock 获取商品库存
func (r *InventoryRepositoryImpl) GetProductStock(ctx *gin.Context, productId, venueId, warehouse string) (*model.ProductStock, error) {
	stockPO, err := r.productStockService.FindProductStockByCondition(ctx, productId, venueId, warehouse)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 返回nil表示记录不存在
		}
		return nil, err
	}

	return model.FromProductStockPO(stockPO), nil
}

// SaveProductStock 保存商品库存
func (r *InventoryRepositoryImpl) SaveProductStock(ctx *gin.Context, stock *model.ProductStock) error {
	stockPO := stock.ToPO()
	return r.productStockService.CreateProductStock(ctx, stockPO)
}

// SaveProductStockWithTx 在事务中保存商品库存
func (r *InventoryRepositoryImpl) SaveProductStockWithTx(ctx *gin.Context, stock *model.ProductStock, tx interface{}) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return errors.New("invalid transaction type")
	}

	stockPO := stock.ToPO()
	return r.productStockService.CreateProductStockWithTx(ctx, stockPO, gormTx)
}

// SyncProductStock 同步商品库存（推荐使用）
func (r *InventoryRepositoryImpl) SyncProductStock(ctx *gin.Context, newStocks []*model.ProductStock, stockChanges []vo.ProductStockChangeVO) error {
	var newProductStockList []po.ProductStock
	for _, stock := range newStocks {
		stockPO := stock.ToPO()
		newProductStockList = append(newProductStockList, *stockPO)
	}

	return r.productStockService.SyncProductStock(ctx, newProductStockList, stockChanges)
}

// CalculateStockFromRecords 根据库存记录计算商品库存
func (r *InventoryRepositoryImpl) CalculateStockFromRecords(ctx *gin.Context, productId, venueId string) (int, error) {
	return r.inventoryRecordItemService.CalculateProductStockFromItems(ctx, productId, venueId)
}

// BeginTransaction 开始事务
func (r *InventoryRepositoryImpl) BeginTransaction(ctx *gin.Context) (interface{}, error) {
	return model_db.DBMaster.Self.Begin(), nil
}

// CommitTransaction 提交事务
func (r *InventoryRepositoryImpl) CommitTransaction(ctx *gin.Context, tx interface{}) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return errors.New("invalid transaction type")
	}
	return gormTx.Commit().Error
}

// RollbackTransaction 回滚事务
func (r *InventoryRepositoryImpl) RollbackTransaction(ctx *gin.Context, tx interface{}) error {
	gormTx, ok := tx.(*gorm.DB)
	if !ok {
		return errors.New("invalid transaction type")
	}
	return gormTx.Rollback().Error
}

// WithTransaction 在事务中执行操作
func (r *InventoryRepositoryImpl) WithTransaction(ctx *gin.Context, fn func(ctx *gin.Context, tx interface{}) error) error {
	return model_db.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		return fn(ctx, tx)
	})
}

// GetStockHistory 获取库存历史记录
func (r *InventoryRepositoryImpl) GetStockHistory(ctx *gin.Context, productId, venueId string, limit int) ([]*model.InboundRecord, error) {
	// 查找主记录
	records, err := r.inventoryRecordService.FindInventoryRecordsByVenueAndTypeWithLimit(ctx, venueId, _const.INVENTORY_RECORD_TYPE_INBOUND, limit)
	if err != nil {
		return nil, err
	}

	var result []*model.InboundRecord
	for _, recordPO := range *records {
		// 查找明细记录
		itemsPO, err := r.inventoryRecordItemService.FindInventoryRecordItemsByRecordId(ctx, *recordPO.Id)
		if err != nil {
			return nil, err
		}

		// 过滤指定商品的记录
		if productId != "" {
			hasProduct := false
			for _, item := range *itemsPO {
				if item.ProductId != nil && *item.ProductId == productId {
					hasProduct = true
					break
				}
			}
			if !hasProduct {
				continue
			}
		}

		// 转换为领域实体
		domainRecord := model.FromPO(&recordPO, *itemsPO)
		result = append(result, domainRecord)
	}

	return result, nil
}

// GetVenueAllStock 获取门店所有商品库存
func (r *InventoryRepositoryImpl) GetVenueAllStock(ctx *gin.Context, venueId string) ([]*model.ProductStock, error) {
	stocksPO, err := r.productStockService.FindProductStocksByVenueId(ctx, venueId)
	if err != nil {
		return nil, err
	}

	var result []*model.ProductStock
	for _, stockPO := range *stocksPO {
		domainStock := model.FromProductStockPO(&stockPO)
		result = append(result, domainStock)
	}

	return result, nil
}
