package inventory

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// StockQueryAppService 库存查询应用服务接口
type StockQueryAppService interface {
	// 查询单个商品库存
	GetProductStock(ctx *gin.Context, productId, venueId string) (*vo.ProductStockVO, error)

	// 批量查询商品库存
	BatchGetProductStock(ctx *gin.Context, reqDto req.BatchStockQueryReqDto) ([]*vo.ProductStockVO, error)

	// 查询门店所有商品库存
	GetVenueAllStock(ctx *gin.Context, venueId string) ([]*vo.ProductStockVO, error)

	// 校准单个商品库存
	ReconcileStock(ctx *gin.Context, productId, venueId string) error

	// 校准门店所有商品库存
	ReconcileAllStock(ctx *gin.Context, venueId string) error

	// 获取库存变动历史
	GetStockHistory(ctx *gin.Context, productId, venueId string, limit int) ([]*vo.StockHistoryVO, error)
}
