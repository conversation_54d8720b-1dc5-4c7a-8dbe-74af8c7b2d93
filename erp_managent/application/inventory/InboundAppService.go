package inventory

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// InboundAppService 入库应用服务接口
type InboundAppService interface {
	// 创建入库单
	CreateInboundRecord(ctx *gin.Context, reqDto req.CreateInboundRecordReqDto) (*vo.InboundRecordVO, error)

	// 修改入库单（冲销重开）
	ModifyInboundRecord(ctx *gin.Context, recordId string, reqDto req.ModifyInboundRecordReqDto) (*vo.InboundRecordVO, error)

	// 查询入库记录列表
	QueryInboundRecords(ctx *gin.Context, venueId string) ([]*vo.InboundRecordVO, error)

	// 根据ID查询入库记录
	GetInboundRecordById(ctx *gin.Context, id string) (*vo.InboundRecordVO, error)

	// 删除入库记录（软删除）
	DeleteInboundRecord(ctx *gin.Context, recordId string) error
}
