package impl

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/inventory"
	"voderpltvv/erp_managent/domain/inventory/model"
	"voderpltvv/erp_managent/domain/inventory/repository"
	"voderpltvv/erp_managent/domain/inventory/service"

	"github.com/gin-gonic/gin"
)

// InboundAppServiceImpl 入库应用服务实现
type InboundAppServiceImpl struct {
	inventoryDomainService service.InventoryDomainService
	inventoryRepository    repository.InventoryRepository
}

// NewInboundAppService 创建入库应用服务
func NewInboundAppService(
	inventoryDomainService service.InventoryDomainService,
	inventoryRepository repository.InventoryRepository,
) inventory.InboundAppService {
	return &InboundAppServiceImpl{
		inventoryDomainService: inventoryDomainService,
		inventoryRepository:    inventoryRepository,
	}
}

// CreateInboundRecord 创建入库单
func (s *InboundAppServiceImpl) CreateInboundRecord(ctx *gin.Context, reqDto req.CreateInboundRecordReqDto) (*vo.InboundRecordVO, error) {
	// 1. 数据验证
	if err := s.validateCreateRequest(reqDto); err != nil {
		return nil, err
	}

	// 2. 构建领域实体
	var items []model.InboundRecordItem
	for _, reqItem := range reqDto.Items {
		// 这里应该查询商品信息获取商品名称和单位，简化处理暂时使用空值
		item := model.NewInboundRecordItem(*reqItem.ProductId, "", "", *reqItem.Quantity, *reqItem.UnitPrice)
		items = append(items, item)
	}

	record := model.NewInboundRecord(*reqDto.VenueId, *reqDto.Warehouse, *reqDto.Handler, *reqDto.RecordNumber, *reqDto.Remark, items)

	// 3. 调用领域服务创建记录
	if err := s.inventoryDomainService.CreateInboundRecord(ctx, record); err != nil {
		return nil, err
	}

	// 4. 转换为VO返回
	return s.convertToInboundRecordVO(record), nil
}

// ModifyInboundRecord 修改入库单（冲销重开）
func (s *InboundAppServiceImpl) ModifyInboundRecord(ctx *gin.Context, recordId string, reqDto req.ModifyInboundRecordReqDto) (*vo.InboundRecordVO, error) {
	// 1. 数据验证
	if err := s.validateModifyRequest(reqDto); err != nil {
		return nil, err
	}

	// 2. 冲销原记录
	_, err := s.inventoryDomainService.ReverseRecord(ctx, recordId, *reqDto.Reason)
	if err != nil {
		return nil, err
	}

	// 3. 创建新记录
	var items []model.InboundRecordItem
	for _, reqItem := range reqDto.Items {
		// 这里应该查询商品信息获取商品名称和单位，简化处理暂时使用空值
		item := model.NewInboundRecordItem(*reqItem.ProductId, "", "", *reqItem.Quantity, *reqItem.UnitPrice)
		items = append(items, item)
	}

	newRecord := model.NewInboundRecord(*reqDto.VenueId, *reqDto.Warehouse, *reqDto.Handler, *reqDto.RecordNumber, *reqDto.Remark, items)

	// 4. 调用领域服务创建新记录
	if err := s.inventoryDomainService.CreateInboundRecord(ctx, newRecord); err != nil {
		return nil, err
	}

	// 5. 转换为VO返回
	return s.convertToInboundRecordVO(newRecord), nil
}

// QueryInboundRecords 查询入库记录列表
func (s *InboundAppServiceImpl) QueryInboundRecords(ctx *gin.Context, venueId string) ([]*vo.InboundRecordVO, error) {
	// 调用仓储查询记录
	records, err := s.inventoryRepository.FindInboundRecords(ctx, venueId)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	var result []*vo.InboundRecordVO
	for _, record := range records {
		voRecord := s.convertToInboundRecordVO(record)
		result = append(result, voRecord)
	}

	return result, nil
}

// GetInboundRecordById 根据ID查询入库记录
func (s *InboundAppServiceImpl) GetInboundRecordById(ctx *gin.Context, id string) (*vo.InboundRecordVO, error) {
	// 调用仓储查询记录
	record, err := s.inventoryRepository.FindInboundRecordById(ctx, id)
	if err != nil {
		return nil, err
	}

	if record == nil {
		return nil, model.ErrRecordNotFound
	}

	// 转换为VO返回
	return s.convertToInboundRecordVO(record), nil
}

// DeleteInboundRecord 删除入库记录（软删除）
func (s *InboundAppServiceImpl) DeleteInboundRecord(ctx *gin.Context, recordId string) error {
	// 调用仓储删除记录
	return s.inventoryRepository.DeleteInboundRecord(ctx, recordId)
}

// validateCreateRequest 验证创建请求
func (s *InboundAppServiceImpl) validateCreateRequest(reqDto req.CreateInboundRecordReqDto) error {
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return model.NewDomainError("门店ID不能为空")
	}
	if reqDto.Handler == nil || *reqDto.Handler == "" {
		return model.NewDomainError("操作人不能为空")
	}
	if reqDto.RecordNumber == nil || *reqDto.RecordNumber == "" {
		return model.NewDomainError("入库单号不能为空")
	}
	if len(reqDto.Items) == 0 {
		return model.NewDomainError("入库明细不能为空")
	}

	for _, item := range reqDto.Items {
		if item.ProductId == nil || *item.ProductId == "" {
			return model.NewDomainError("商品ID不能为空")
		}
		if item.Quantity == nil || *item.Quantity <= 0 {
			return model.NewDomainError("入库数量必须大于0")
		}
		if item.UnitPrice == nil || *item.UnitPrice < 0 {
			return model.NewDomainError("单价不能为负数")
		}
	}

	return nil
}

// validateModifyRequest 验证修改请求
func (s *InboundAppServiceImpl) validateModifyRequest(reqDto req.ModifyInboundRecordReqDto) error {
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return model.NewDomainError("门店ID不能为空")
	}
	if reqDto.Handler == nil || *reqDto.Handler == "" {
		return model.NewDomainError("操作人不能为空")
	}
	if reqDto.RecordNumber == nil || *reqDto.RecordNumber == "" {
		return model.NewDomainError("入库单号不能为空")
	}
	if reqDto.Reason == nil || *reqDto.Reason == "" {
		return model.NewDomainError("修改原因不能为空")
	}
	if len(reqDto.Items) == 0 {
		return model.NewDomainError("入库明细不能为空")
	}

	for _, item := range reqDto.Items {
		if item.ProductId == nil || *item.ProductId == "" {
			return model.NewDomainError("商品ID不能为空")
		}
		if item.Quantity == nil || *item.Quantity <= 0 {
			return model.NewDomainError("入库数量必须大于0")
		}
		if item.UnitPrice == nil || *item.UnitPrice < 0 {
			return model.NewDomainError("单价不能为负数")
		}
	}

	return nil
}

// convertToInboundRecordVO 转换为VO
func (s *InboundAppServiceImpl) convertToInboundRecordVO(record *model.InboundRecord) *vo.InboundRecordVO {
	var items []vo.InboundRecordItemVO
	for _, item := range record.GetItems() {
		voItem := vo.InboundRecordItemVO{
			Id:        item.GetId(),
			ProductId: item.GetProductId(),
			Quantity:  item.GetQuantity(),
			UnitPrice: item.GetUnitPrice(),
			Remark:    "",
		}
		items = append(items, voItem)
	}

	return &vo.InboundRecordVO{
		Id:           record.GetId(),
		VenueId:      record.GetVenueId(),
		Warehouse:    record.GetWarehouse(),
		Handler:      record.GetHandler(),
		Time:         record.GetTime().Unix(),
		RecordNumber: record.GetRecordNumber(),
		Remark:       record.GetRemark(),
		Items:        items,
		Ctime:        record.GetTime().Unix(), // 简化处理，实际应该使用Ctime
		Utime:        record.GetTime().Unix(), // 简化处理，实际应该使用Utime
		State:        record.GetState(),
		Version:      record.GetVersion(),
	}
}
