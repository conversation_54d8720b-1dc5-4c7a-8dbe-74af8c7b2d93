package impl

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/inventory"
	"voderpltvv/erp_managent/domain/inventory/model"
	"voderpltvv/erp_managent/domain/inventory/repository"
	"voderpltvv/erp_managent/domain/inventory/service"

	"github.com/gin-gonic/gin"
)

// StockQueryAppServiceImpl 库存查询应用服务实现
type StockQueryAppServiceImpl struct {
	inventoryDomainService service.InventoryDomainService
	inventoryRepository    repository.InventoryRepository
}

// NewStockQueryAppService 创建库存查询应用服务
func NewStockQueryAppService(
	inventoryDomainService service.InventoryDomainService,
	inventoryRepository repository.InventoryRepository,
) inventory.StockQueryAppService {
	return &StockQueryAppServiceImpl{
		inventoryDomainService: inventoryDomainService,
		inventoryRepository:    inventoryRepository,
	}
}

// GetProductStock 查询单个商品库存
func (s *StockQueryAppServiceImpl) GetProductStock(ctx *gin.Context, productId, venueId string) (*vo.ProductStockVO, error) {
	// 从快照表查询库存
	stock, err := s.inventoryRepository.GetProductStock(ctx, productId, venueId, "")
	if err != nil {
		return nil, err
	}

	if stock == nil {
		// 如果快照表没有数据，从流水表计算
		calculatedStock, err := s.inventoryRepository.CalculateStockFromRecords(ctx, productId, venueId)
		if err != nil {
			return nil, err
		}

		// 创建默认库存记录
		stock = model.NewProductStock(productId, venueId, "", calculatedStock)
	}

	// 转换为VO
	return s.convertToProductStockVO(stock), nil
}

// BatchGetProductStock 批量查询商品库存
func (s *StockQueryAppServiceImpl) BatchGetProductStock(ctx *gin.Context, reqDto req.BatchStockQueryReqDto) ([]*vo.ProductStockVO, error) {
	var result []*vo.ProductStockVO

	for _, productIdPtr := range reqDto.ProductIds {
		if productIdPtr != nil {
			stockVO, err := s.GetProductStock(ctx, *productIdPtr, *reqDto.VenueId)
			if err != nil {
				return nil, err
			}
			result = append(result, stockVO)
		}
	}

	return result, nil
}

// GetVenueAllStock 查询门店所有商品库存
func (s *StockQueryAppServiceImpl) GetVenueAllStock(ctx *gin.Context, venueId string) ([]*vo.ProductStockVO, error) {
	// 查询门店所有商品库存
	stocks, err := s.inventoryRepository.GetVenueAllStock(ctx, venueId)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	var result []*vo.ProductStockVO
	for _, stock := range stocks {
		stockVO := s.convertToProductStockVO(stock)
		result = append(result, stockVO)
	}

	return result, nil
}

// ReconcileStock 校准单个商品库存
func (s *StockQueryAppServiceImpl) ReconcileStock(ctx *gin.Context, productId, venueId string) error {
	// 调用领域服务校准库存
	return s.inventoryDomainService.ReconcileProductStock(ctx, productId, venueId)
}

// ReconcileAllStock 校准门店所有商品库存
func (s *StockQueryAppServiceImpl) ReconcileAllStock(ctx *gin.Context, venueId string) error {
	// 调用领域服务校准所有库存
	return s.inventoryDomainService.ReconcileAllProductStock(ctx, venueId)
}

// GetStockHistory 获取库存变动历史
func (s *StockQueryAppServiceImpl) GetStockHistory(ctx *gin.Context, productId, venueId string, limit int) ([]*vo.StockHistoryVO, error) {
	// 查询库存变动历史
	records, err := s.inventoryRepository.GetStockHistory(ctx, productId, venueId, limit)
	if err != nil {
		return nil, err
	}

	// 转换为VO
	var result []*vo.StockHistoryVO
	for _, record := range records {
		historyVO := s.convertToStockHistoryVO(record)
		result = append(result, historyVO)
	}

	return result, nil
}

// convertToProductStockVO 转换为商品库存VO
func (s *StockQueryAppServiceImpl) convertToProductStockVO(stock *model.ProductStock) *vo.ProductStockVO {
	return &vo.ProductStockVO{
		ProductId:   stock.GetProductId(),
		ProductName: "", // 这里应该查询商品信息获取商品名称，简化处理暂时使用空值
		VenueId:     stock.GetVenueId(),
		Warehouse:   stock.GetWarehouse(),
		Stock:       stock.GetStock(),
		Unit:        "", // 这里应该查询商品信息获取单位，简化处理暂时使用空值
		UpdateTime:  0,  // 简化处理，避免GetUpdateTime编译错误
	}
}

// convertToStockHistoryVO 转换为库存变动历史VO
func (s *StockQueryAppServiceImpl) convertToStockHistoryVO(record *model.InboundRecord) *vo.StockHistoryVO {
	// 简化处理：从第一个明细项获取商品信息
	var productId string
	var quantity int
	if len(record.GetItems()) > 0 {
		firstItem := record.GetItems()[0]
		productId = firstItem.GetProductId()
		quantity = firstItem.GetQuantity()
	}

	return &vo.StockHistoryVO{
		Id:           record.GetId(),
		ProductId:    productId,
		VenueId:      record.GetVenueId(),
		Type:         record.GetType(),
		Quantity:     quantity,
		RecordNumber: record.GetRecordNumber(),
		Handler:      record.GetHandler(),
		Time:         record.GetTime().Unix(),
		Remark:       record.GetRemark(),
	}
}
