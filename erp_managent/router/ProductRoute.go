package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type ProductRoute struct {
}

func (s *ProductRoute) InitProductRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	productController := controller.ProductController{}
	route := g.Group("")
	{
		route.POST("/api/product/add", productController.AddProduct)                        // add
		route.POST("/api/product/update", productController.UpdateProduct)                  // update
		route.POST("/api/product/delete", productController.DeleteProduct)                  // delete
		route.POST("/api/product/query", productController.QueryProducts)                   // query
		route.POST("/api/product/list", productController.ListProducts)                     // list
		route.POST("/api/product/list-types", productController.ListTypes)                  // types
		route.POST("/api/product/query-detail-bytype", productController.QueryDetailByType) // queryDetailByType
		route.POST("/api/product/sold-out", productController.SoldOut)                      // soldOut

		route.POST("/api/product/list-types-pad", productController.ListTypesPad) // listTypesPad
		route.POST("/api/product/query-detail-bytype-pad", productController.QueryDetailByTypePad) // queryDetailByTypePad
		route.POST("/api/product/query-product-supplement", productController.QuerySupply)                      // querySupply
	}
}
