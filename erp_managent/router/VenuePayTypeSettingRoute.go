package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type VenuePayTypeSettingRoute struct {
}

func (s *VenuePayTypeSettingRoute) InitVenuePayTypeSettingRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	venuePayTypeSettingController := controller.VenuePayTypeSettingController{}
	route := g.Group("")
	{
		route.POST("/api/venue-pay-type-setting/add", venuePayTypeSettingController.AddVenuePayTypeSetting)         //add
		route.POST("/api/venue-pay-type-setting/update", venuePayTypeSettingController.UpdateVenuePayTypeSetting)   //update
		route.POST("/api/venue-pay-type-setting/delete", venuePayTypeSettingController.DeleteVenuePayTypeSetting)   //delete
		route.POST("/api/venue-pay-type-setting/findone", venuePayTypeSettingController.FindOneVenuePayTypeSetting) //findone
	}
}
