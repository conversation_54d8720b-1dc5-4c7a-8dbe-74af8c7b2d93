package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type MemberCardLevelRoute struct {
}

func (s *MemberCardLevelRoute) InitMemberCardLevelRouter(g *gin.Engine) {
	memberCardLevelController := controller.MemberCardLevelController{}
	route := g.Group("")
	{
		route.POST("/api/memberCardLevel/add", memberCardLevelController.AddMemberCardLevel)                 // add
		route.POST("/api/memberCardLevel/update", memberCardLevelController.UpdateMemberCardLevel)           // update
		route.POST("/api/memberCardLevel/batchUpdate", memberCardLevelController.BatchUpdateMemberCardLevel) // batch update
		route.POST("/api/memberCardLevel/delete", memberCardLevelController.DeleteMemberCardLevel)           // delete
		route.POST("/api/memberCardLevel/list", memberCardLevelController.QueryMemberCardLevels)             // query
	}
}
