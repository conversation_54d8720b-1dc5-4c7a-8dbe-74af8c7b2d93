package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type ProductStorageRoute struct {
}

func (s *ProductStorageRoute) InitProductStorageRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	productStorageController := controller.ProductStorageController{}
	productWithdrawController := controller.ProductWithdrawController{}

	route := g.Group("")
	{
		// 存酒相关路由
		storageGroup := route.Group("/api/product-storage")
		{
			// 存酒记录相关接口
			storageGroup.POST("/add", productStorageController.AddProductStorage)
			storageGroup.POST("/query", productStorageController.QueryProductStorages)
			storageGroup.GET("/detail/:id", productStorageController.GetProductStorageDetail)
			storageGroup.GET("/order/:orderNo", productStorageController.GetProductStorageOrder)
			storageGroup.POST("/operate", productStorageController.OperateProductStorage)
			storageGroup.POST("/statistics", productStorageController.GetStorageStatistics)

			// 会员存酒专用接口
			storageGroup.POST("/member/query", productStorageController.QueryMemberStorage)
		}

		// 取酒相关路由
		withdrawGroup := route.Group("/api/product-withdraw")
		{
			// 取酒记录操作
			withdrawGroup.POST("/add", productWithdrawController.AddProductWithdraw)
			withdrawGroup.POST("/batch-add", productWithdrawController.BatchAddProductWithdraw)
			withdrawGroup.POST("/query", productWithdrawController.QueryProductWithdraws)
			// 查询可取商品
			withdrawGroup.POST("/query-withdrawable-items", productWithdrawController.QueryWithdrawableItems)
		}
	}
}
