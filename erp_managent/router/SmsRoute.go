package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type SmsRoute struct {
}

func (s *SmsRoute) InitSmsRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	smsController := controller.SmsController{}
	route := g.Group("")
	{
		route.POST("/api/sms/send-code", smsController.SendSmsCode)            // 发送短信验证码
		route.POST("/api/sms/debug-send-code", smsController.DebugSendSmsCode) // 调试短信发送
		route.POST("/api/sms/callback", smsController.SmsCallback)             // 短信状态回调
	}
}
