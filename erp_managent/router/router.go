package router

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"voderpltvv/erp_managent/controller/check"

	_ "voderpltvv/docs"

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Load 载入中间件
func Load(g *gin.Engine, mw ...gin.HandlerFunc) *gin.Engine {
	g.Use(gin.Logger())
	g.Use(gin.Recovery())
	// g.Use(middleware.NoCache())
	// g.Use(middleware.Options())
	// g.Use(middleware.Secure())
	// g.Use(middleware.RequestID()) // 已经在启动文件cmd.server.go中打开

	// 健康检查和版本信息接口 - 在中间件之前注册，无需认证
	g.GET("/health", check.HealthCheckSelf)
	g.GET("/version", check.Version)
	g.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 应用中间件到后续路由
	g.Use(mw...)

	// 404 handler
	g.NoRoute(func(ctx *gin.Context) {
		ctx.String(http.StatusNotFound, "incorrect api router")
	})

	// 权限管理路由已在EmployeeRoute中注册，无需重复注册

	// 动态表配置路由 - 添加在顶部
	new(TableConfigRoute).InitTableConfigRouter(g)

	new(GrantRoute).InitGrantRouter(g)

	new(AreaRoute).InitAreaRouter(g)
	new(AsExampleRoute).InitAsExampleRouter(g)
	new(ProductIncomeReportRoute).InitProductIncomeReportRouter(g)
	new(BookingRoute).InitBookingRouter(g)
	new(AbnormalPaymentOrderRoute).InitAbnormalPaymentOrderRouter(g)
	new(AuthorizationRecordRoute).InitAuthorizationRecordRouter(g)
	new(BirthdayGreetingRoute).InitBirthdayGreetingRouter(g)
	new(BusinessLoginRoute).InitBusinessRouter(g)
	new(CashierMachineRoute).InitCashierMachineRouter(g)
	new(CashierSystemRoute).InitCashierSystemRouter(g)
	new(CloudPrinterRoute).InitCloudPrinterRouter(g)
	new(CommissionRoute).InitCommissionRouter(g)
	new(CommissionPlanRoute).InitCommissionPlanRouter(g)
	new(CommonRemarkRoute).InitCommonRemarkRouter(g)
	new(ConstructionAssistanceRoute).InitConstructionAssistanceRouter(g)
	new(ConsumptionCashbackRoute).InitConsumptionCashbackRouter(g)
	new(CouponRoute).InitCouponRouter(g)
	new(CouponClaimRoute).InitCouponClaimRouter(g)
	new(CouponUsageRoute).InitCouponUsageRouter(g)
	new(CreditAccountRoute).InitCreditAccountRouter(g)
	new(CreditUnitRoute).InitCreditUnitRouter(g)
	new(CustomerGroupRoute).InitCustomerGroupRouter(g)
	new(CustomerSourceRoute).InitCustomerSourceRouter(g)
	new(CustomerTagRoute).InitCustomerTagRouter(g)
	new(DataCleanupRoute).InitDataCleanupRouter(g)
	new(DouyinGroupBuyingPlanRoute).InitDouyinGroupBuyingPlanRouter(g)
	new(ElectronicCardRoute).InitElectronicCardRouter(g)
	new(EmployeeRoute).InitEmployeeRouter(g)
	new(EmployeeGroupRoute).InitEmployeeGroupRouter(g)
	new(FinancialReportRoute).InitFinancialReportRouter(g)
	new(FlavorRoute).InitFlavorRouter(g)
	new(GiftGroupRoute).InitGiftGroupRouter(g)
	new(GiftRecordRoute).InitGiftRecordRouter(g)
	new(HistoricalRecordRoute).InitHistoricalRecordRouter(g)
	new(HolidayRoute).InitHolidayRouter(g)
	new(IngredientTypeRoute).InitIngredientTypeRouter(g)
	new(InventoryTransactionRoute).InitInventoryTransactionRouter(g)
	new(M1CardRoute).InitM1CardRouter(g)
	new(MarketingCampaignRoute).InitMarketingCampaignRouter(g)
	new(MarketingRoleRoute).InitMarketingRoleRouter(g)
	new(MarketServiceRoute).InitMarketServiceRouter(g)
	new(MemberRoute).InitMemberRouter(g)
	new(MemberCardRoute).InitMemberCardRouter(g)
	new(MemberCardLevelRoute).InitMemberCardLevelRouter(g)
	new(MemberCardConsumeRoute).InitMemberCardConsumeRouter(g)
	new(MemberRechargePackageRoute).InitMemberRechargePackageRouter(g)
	new(MemberDayRoute).InitMemberDayRouter(g)
	new(MemberRechargeBillRoute).InitMemberRechargeBillRouter(g)
	new(MerchantServiceRoute).InitMerchantServiceRouter(g)
	new(MobileOrderRoute).InitMobileOrderRouter(g)
	new(MonthlyGiftCouponRoute).InitMonthlyGiftCouponRouter(g)
	new(NetworkRoute).InitNetworkRouter(g)
	new(NotificationSettingRoute).InitNotificationSettingRouter(g)
	new(OnlineOperationSettingsRoute).InitOnlineOperationSettingsRouter(g)
	new(OperationSettingsRoute).InitOperationSettingsRouter(g)
	new(OrderRoute).InitOrderRouter(g)
	new(OrderPricePlanRoute).InitOrderPricePlanRouter(g)
	new(OrderProductRoute).InitOrderProductRouter(g)
	new(OrderRoomPlanRoute).InitOrderRoomPlanRouter(g)
	new(PaymentMethodRoute).InitPaymentMethodRouter(g)
	new(PermissionRoleRoute).InitPermissionRoleRouter(g)
	// 注册权限管理路由
	RegisterPermissionRoutes(g)
	new(PhoneBlacklistRoute).InitPhoneBlacklistRouter(g)
	new(PhysicalCardRoute).InitPhysicalCardRouter(g)
	new(PointsExchangeRoute).InitPointsExchangeRouter(g)
	new(PosMachineRoute).InitPosMachineRouter(g)
	new(PrepaidCardRoute).InitPrepaidCardRouter(g)
	new(PrepaidCardTypeRoute).InitPrepaidCardTypeRouter(g)
	new(PricePlanRoute).InitPricePlanRouter(g)
	new(PriceSchemeRoute).InitPriceSchemeRouter(g)
	new(PrintTemplateRoute).InitPrintTemplateRouter(g)
	new(ProductRoute).InitProductRouter(g)
	new(ProductBindingRoute).InitProductBindingRouter(g)
	new(ProductDisplayCategoryRoute).InitProductDisplayCategoryRouter(g)
	new(ProductMultipleBuyFreeRoute).InitProductMultipleBuyFreeRouter(g)
	new(ProductOutTypeRoute).InitProductOutTypeRouter(g)
	new(ProductOutRouter).InitProductOutRouter(g)
	new(PrintRecordRouter).InitPrintRecordRouter(g)
	new(ProductPackageRoute).InitProductPackageRouter(g)
	new(ProductPackageTypeRoute).InitProductPackageTypeRouter(g)
	new(ProductSalesTemplateRoute).InitProductSalesTemplateRouter(g)
	new(ProductStatisticsCategoryRoute).InitProductStatisticsCategoryRouter(g)
	new(ProductStorageRoute).InitProductStorageRouter(g)
	new(ProductTimeSlotRoute).InitProductTimeSlotRouter(g)
	new(ProductTypeRoute).InitProductTypeRouter(g)
	new(ProductUnitRoute).InitProductUnitRouter(g)
	new(RechargePackageRoute).InitRechargePackageRouter(g)
	new(RecipeRoute).InitRecipeRouter(g)
	new(RedemptionRecordRoute).InitRedemptionRecordRouter(g)
	new(ReportRoute).InitReportRouter(g)
	new(ReportTypeRoute).InitReportTypeRouter(g)
	new(RewardRoute).InitRewardRouter(g)
	new(RoomRoute).InitRoomRouter(g)
	new(RoomExceptionRoute).InitRoomExceptionRouter(g)
	new(RoomGreetingRoute).InitRoomGreetingRouter(g)
	new(RoomThemeRoute).InitRoomThemeRouter(g)
	new(RoomTypeRoute).InitRoomTypeRouter(g)
	new(RouterRoute).InitRouterRouter(g)
	new(SalesPerformanceRoute).InitSalesPerformanceRouter(g)
	new(SequencerRoute).InitSequencerRouter(g)
	new(ServiceRewardSettingsRoute).InitServiceRewardSettingsRouter(g)
	new(SessionRoute).InitSessionRouter(g)
	new(ShiftReportRoute).InitShiftReportRouter(g)
	new(SingingDeviceRoute).InitSingingDeviceRouter(g)
	new(SmsServiceRoute).InitSmsServiceRouter(g)
	new(StatisticsCategoryRoute).InitStatisticsCategoryRouter(g)
	new(StatisticsPeriodRoute).InitStatisticsPeriodRouter(g)
	new(SubtitleInfoRoute).InitSubtitleInfoRouter(g)
	new(TurnoverDataRoute).InitTurnoverDataRouter(g)
	new(TvScreenActivityRoute).InitTvScreenActivityRouter(g)
	new(VenueRoute).InitVenueRouter(g)
	new(VenueAuthRoute).InitVenueAuthRouter(g)
	new(VenueAuthCodeRoute).InitVenueAuthCodeRouter(g)
	new(VodSettingsRoute).InitVodSettingsRouter(g)
	new(VoucherRoute).InitVoucherRouter(g)
	new(WarehouseRoute).InitWarehouseRouter(g)
	new(WineStorageRoute).InitWineStorageRouter(g)
	new(WineStorageSettingRoute).InitWineStorageSettingRouter(g)
	new(VenuePaySettingRoute).InitVenuePaySettingRouter(g)
	new(VenuePayTypeSettingRoute).InitVenuePayTypeSettingRouter(g)
	new(EmployeeGroupEmployeeRoute).InitEmployeeGroupEmployeeRouter(g)
	new(WXEventRoute).InitWXEventRouter(g)
	new(LoginRoute).InitLoginRouter(g)
	new(VenueAndEmployeeRoute).InitVenueAndEmployeeRouter(g)
	new(ERPUserRoute).InitERPUserRouter(g)
	new(ERPUserAndEmployeeRoute).InitERPUserAndEmployeeRouter(g)
	new(AuthBindRoute).InitAuthBindRouter(g)
	new(ShiftHandoverFormRoute).InitShiftHandoverFormRouter(g)
	new(ShiftHandoverFormAndPayBillRoute).InitShiftHandoverFormAndPayBillRouter(g)
	new(AppUpgradeRoute).InitAppUpgradeRouter(g)
	new(MemberCardOperationRoute).InitMemberCardOperationRouter(g)
	new(EmployeeGiftRecordRoute).InitEmployeeGiftRecordRouter(g)
	new(CallTypesRoute).InitCallTypesRouter(g)
	new(CallMessageRoute).InitCallMessageRouter(g)
	new(SmsRoute).InitSmsRouter(g)

	return g
}
