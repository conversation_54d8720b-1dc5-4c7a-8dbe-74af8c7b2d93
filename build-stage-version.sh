#!/bin/bash

# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 缺少参数"
    echo "用法: $0 <tag_name>"
    echo "示例: $0 v1.0.0"
    exit 1
fi

# 获取第一个参数作为tag
SPUG_GIT_TAG=$1

echo "开始切换到tag: $SPUG_GIT_TAG"

cd /home/<USER>/online/src/erp-lt-vv;
git fetch --tags

# 切换到指定tag
git checkout $SPUG_GIT_TAG

# 检查tag切换是否成功
if [ $? -ne 0 ]; then
    echo "错误: git checkout $SPUG_GIT_TAG 失败"
    exit 1
fi

# 验证指定的tag是否指向当前HEAD（改进的验证逻辑）
CURRENT_COMMIT=$(git rev-parse HEAD)
TAG_COMMIT=$(git rev-parse $SPUG_GIT_TAG^{commit} 2>/dev/null)

if [ "$CURRENT_COMMIT" != "$TAG_COMMIT" ]; then
    echo "错误: tag切换失败，当前HEAD: $CURRENT_COMMIT，tag $SPUG_GIT_TAG 指向: $TAG_COMMIT"
    exit 1
fi

echo "成功切换到tag: $SPUG_GIT_TAG"

# 构建项目
echo "正在构建项目..."

# 设置构建变量
versionDir="voderpltvv/util/version"
buildDate=$(TZ=UTC date +%FT%T%z)
gitCommit=$(git log --pretty=format:'%H' -n 1)
ldflags="-w -X ${versionDir}.buildDate=${buildDate} -X ${versionDir}.gitCommit=${gitCommit}"

# 执行构建
go build -ldflags "${ldflags}" ./
if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

echo "项目构建成功"

# 重启服务
echo "正在重启服务..."
supervisorctl=/home/<USER>/supervisor/bin/supervisorctl
${supervisorctl} restart voderpltvv

if [ $? -ne 0 ]; then
    echo "错误: 服务重启失败"
    exit 1
fi

echo "服务重启成功"
echo "部署完成！tag: $SPUG_GIT_TAG"