package _const

// ============== 权限类型常量 ==============

// PermissionType 权限类型
type PermissionType string

const (
	PERMISSION_TYPE_SYSTEM PermissionType = "SYSTEM" // 系统级权限（控制业务端访问）
	PERMISSION_TYPE_MODULE PermissionType = "MODULE" // 模块级权限（控制功能模块）
)

// ============== 系统级权限代码（树形结构）==============
// 根权限直接使用clientType名称，避免认知负担

const (
	// 商务系统（thunder_erp_business）- 系统级权限
	SYSTEM_THUNDER_ERP_BUSINESS       = "thunder_erp_business"       // 商务系统根权限
	SYSTEM_THUNDER_ERP_BUSINESS_LOGIN = "thunder_erp_business.login" // 商务系统登录权限
	SYSTEM_THUNDER_ERP_BUSINESS_AUDIT = "thunder_erp_business.audit" // 商务系统审核权限
	SYSTEM_THUNDER_ERP_BUSINESS_ADMIN = "thunder_erp_business.admin" // 商务系统管理权限

	// 掌柜系统（thunder_erp_manager）- 系统级权限
	SYSTEM_THUNDER_ERP_MANAGER           = "thunder_erp_manager"           // 掌柜系统根权限
	SYSTEM_THUNDER_ERP_MANAGER_LOGIN     = "thunder_erp_manager.login"     // 掌柜系统登录权限
	SYSTEM_THUNDER_ERP_MANAGER_DASHBOARD = "thunder_erp_manager.dashboard" // 掌柜系统仪表盘权限
	SYSTEM_THUNDER_ERP_MANAGER_ADMIN     = "thunder_erp_manager.admin"     // 掌柜系统管理权限
	SYSTEM_THUNDER_ERP_MANAGER_CONFIG    = "thunder_erp_manager.config"    // 掌柜系统配置权限

	// 收银系统（thunder_erp_cashier）- 系统级权限
	SYSTEM_THUNDER_ERP_CASHIER         = "thunder_erp_cashier"         // 收银系统根权限
	SYSTEM_THUNDER_ERP_CASHIER_LOGIN   = "thunder_erp_cashier.login"   // 收银系统登录权限
	SYSTEM_THUNDER_ERP_CASHIER_OPERATE = "thunder_erp_cashier.operate" // 收银系统操作权限
	SYSTEM_THUNDER_ERP_CASHIER_REPORT  = "thunder_erp_cashier.report"  // 收银系统报表权限

	// 报表系统（thunder_erp_report）- 系统级权限
	SYSTEM_THUNDER_ERP_REPORT          = "thunder_erp_report"          // 报表系统根权限
	SYSTEM_THUNDER_ERP_REPORT_LOGIN    = "thunder_erp_report.login"    // 报表系统登录权限
	SYSTEM_THUNDER_ERP_REPORT_VIEW     = "thunder_erp_report.view"     // 报表系统查看权限
	SYSTEM_THUNDER_ERP_REPORT_EXPORT   = "thunder_erp_report.export"   // 报表系统导出权限
	SYSTEM_THUNDER_ERP_REPORT_ANALYSIS = "thunder_erp_report.analysis" // 报表系统分析权限

	// 配置系统（thunder_erp_config）- 系统级权限
	SYSTEM_THUNDER_ERP_CONFIG            = "thunder_erp_config"            // 配置系统根权限
	SYSTEM_THUNDER_ERP_CONFIG_LOGIN      = "thunder_erp_config.login"      // 配置系统登录权限
	SYSTEM_THUNDER_ERP_CONFIG_PERMISSION = "thunder_erp_config.permission" // 配置系统权限管理
	SYSTEM_THUNDER_ERP_CONFIG_MEMBER     = "thunder_erp_config.member"     // 配置系统会员管理
	SYSTEM_THUNDER_ERP_CONFIG_BUSINESS   = "thunder_erp_config.business"   // 配置系统业务规则

	// 移动点单系统（thunder_erp_mobile_order）- 系统级权限
	SYSTEM_THUNDER_ERP_MOBILE_ORDER        = "thunder_erp_mobile_order"        // 移动点单根权限
	SYSTEM_THUNDER_ERP_MOBILE_ORDER_LOGIN  = "thunder_erp_mobile_order.login"  // 移动点单登录权限
	SYSTEM_THUNDER_ERP_MOBILE_ORDER_ORDER  = "thunder_erp_mobile_order.order"  // 移动点单下单权限
	SYSTEM_THUNDER_ERP_MOBILE_ORDER_MEMBER = "thunder_erp_mobile_order.member" // 移动点单会员权限

	// 客用小程序（thunder_erp_customer_miniapp）- 系统级权限
	SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP        = "thunder_erp_customer_miniapp"        // 客用小程序根权限
	SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP_LOGIN  = "thunder_erp_customer_miniapp.login"  // 客用小程序登录权限
	SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP_ORDER  = "thunder_erp_customer_miniapp.order"  // 客用小程序点单权限
	SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP_MEMBER = "thunder_erp_customer_miniapp.member" // 客用小程序会员权限
)

// ============== 模块级权限代码（树形结构）==============
// 模块权限使用简化的业务名称（去掉thunder_erp_前缀）

const (
	// 掌柜系统 - 库存管理模块
	MODULE_THUNDER_ERP_MANAGER_INVENTORY           = "thunder_erp_manager.inventory"           // 库存管理主模块
	MODULE_THUNDER_ERP_MANAGER_INVENTORY_PRODUCT   = "thunder_erp_manager.inventory.product"   // 商品库存管理
	MODULE_THUNDER_ERP_MANAGER_INVENTORY_MATERIAL  = "thunder_erp_manager.inventory.material"  // 原料库存管理
	MODULE_THUNDER_ERP_MANAGER_INVENTORY_SUPPLIER  = "thunder_erp_manager.inventory.supplier"  // 供应商管理
	MODULE_THUNDER_ERP_MANAGER_INVENTORY_PURCHASE  = "thunder_erp_manager.inventory.purchase"  // 采购管理
	MODULE_THUNDER_ERP_MANAGER_INVENTORY_STOCKTAKE = "thunder_erp_manager.inventory.stocktake" // 盘点管理
	MODULE_THUNDER_ERP_MANAGER_INVENTORY_REPORT    = "thunder_erp_manager.inventory.report"    // 库存报表

	// 掌柜系统 - 营销管理模块
	MODULE_THUNDER_ERP_MANAGER_MARKETING          = "thunder_erp_manager.marketing"          // 营销管理主模块
	MODULE_THUNDER_ERP_MANAGER_MARKETING_CAMPAIGN = "thunder_erp_manager.marketing.campaign" // 营销活动管理
	MODULE_THUNDER_ERP_MANAGER_MARKETING_ANALYSIS = "thunder_erp_manager.marketing.analysis" // 营销数据分析
	MODULE_THUNDER_ERP_MANAGER_MARKETING_CONFIG   = "thunder_erp_manager.marketing.config"   // 营销配置管理
	MODULE_THUNDER_ERP_MANAGER_MARKETING_CUSTOMER = "thunder_erp_manager.marketing.customer" // 客户营销管理

	// 掌柜系统 - 优惠券模块
	MODULE_THUNDER_ERP_MANAGER_COUPON          = "thunder_erp_manager.coupon"          // 优惠券主模块
	MODULE_THUNDER_ERP_MANAGER_COUPON_TEMPLATE = "thunder_erp_manager.coupon.template" // 优惠券模板管理
	MODULE_THUNDER_ERP_MANAGER_COUPON_ISSUE    = "thunder_erp_manager.coupon.issue"    // 优惠券发放管理
	MODULE_THUNDER_ERP_MANAGER_COUPON_USE      = "thunder_erp_manager.coupon.use"      // 优惠券使用管理
	MODULE_THUNDER_ERP_MANAGER_COUPON_REPORT   = "thunder_erp_manager.coupon.report"   // 优惠券报表

	// 掌柜系统 - 员工管理模块（详细子模块）
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE            = "thunder_erp_manager.employee"            // 员工管理主模块
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_INFO       = "thunder_erp_manager.employee.info"       // 员工信息管理
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ROLE       = "thunder_erp_manager.employee.role"       // 员工角色管理
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_PERMISSION = "thunder_erp_manager.employee.permission" // 员工权限管理
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ATTENDANCE = "thunder_erp_manager.employee.attendance" // 员工考勤管理
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_SALARY     = "thunder_erp_manager.employee.salary"     // 员工薪资管理
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_TRAINING   = "thunder_erp_manager.employee.training"   // 员工培训管理
	MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ASSESSMENT = "thunder_erp_manager.employee.assessment" // 员工考核管理

	// 掌柜系统 - 业绩管理模块
	MODULE_THUNDER_ERP_MANAGER_PERFORMANCE        = "thunder_erp_manager.performance"        // 业绩管理主模块
	MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_SALES  = "thunder_erp_manager.performance.sales"  // 销售业绩管理
	MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_TARGET = "thunder_erp_manager.performance.target" // 业绩目标管理
	MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_REWARD = "thunder_erp_manager.performance.reward" // 业绩奖励管理
	MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_REPORT = "thunder_erp_manager.performance.report" // 业绩报表

	// 收银系统模块权限
	MODULE_THUNDER_ERP_CASHIER_ORDER   = "thunder_erp_cashier.order"   // 收银订单管理
	MODULE_THUNDER_ERP_CASHIER_PAYMENT = "thunder_erp_cashier.payment" // 收银支付管理
	MODULE_THUNDER_ERP_CASHIER_MEMBER  = "thunder_erp_cashier.member"  // 会员管理
	MODULE_THUNDER_ERP_CASHIER_PRODUCT = "thunder_erp_cashier.product" // 商品管理

	// 移动点单系统模块权限
	MODULE_THUNDER_ERP_MOBILE_ORDER_MENU    = "thunder_erp_mobile_order.menu"    // 移动点单菜单管理
	MODULE_THUNDER_ERP_MOBILE_ORDER_CART    = "thunder_erp_mobile_order.cart"    // 购物车管理
	MODULE_THUNDER_ERP_MOBILE_ORDER_PAYMENT = "thunder_erp_mobile_order.payment" // 支付管理
)

// ============== 预制角色常量 ==============

const (
	ROLE_ADMIN   = "admin"   // 管理员
	ROLE_CASHIER = "cashier" // 收银员
	ROLE_WAITER  = "waiter"  // 服务员
)

// ============== 权限映射函数 ==============

// MapClientTypeToSystemPermission 映射clientType到系统权限代码（现在直接相等）
func MapClientTypeToSystemPermission(clientType ClientBusinessType) string {
	switch clientType {
	case ClientBusinessTypeBusiness:
		return SYSTEM_THUNDER_ERP_BUSINESS
	case ClientBusinessTypeManager:
		return SYSTEM_THUNDER_ERP_MANAGER
	case ClientBusinessTypeCashier:
		return SYSTEM_THUNDER_ERP_CASHIER
	case ClientBusinessTypeReport:
		return SYSTEM_THUNDER_ERP_REPORT
	case ClientBusinessTypeConfig:
		return SYSTEM_THUNDER_ERP_CONFIG
	case ClientBusinessTypeMobileOrder:
		return SYSTEM_THUNDER_ERP_MOBILE_ORDER
	case ClientBusinessTypeCustomerMiniapp:
		return SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP
	default:
		return ""
	}
}

// ExtractPermissionCodeFromClientType 从clientType提取权限代码
// 根权限和clientType直接相等，所以直接返回clientType即可
func ExtractPermissionCodeFromClientType(clientType string) string {
	// 验证是否为支持的clientType
	switch clientType {
	case SYSTEM_THUNDER_ERP_BUSINESS:
		return clientType
	case SYSTEM_THUNDER_ERP_MANAGER:
		return clientType
	case SYSTEM_THUNDER_ERP_CASHIER:
		return clientType
	case SYSTEM_THUNDER_ERP_REPORT:
		return clientType
	case SYSTEM_THUNDER_ERP_CONFIG:
		return clientType
	case SYSTEM_THUNDER_ERP_MOBILE_ORDER:
		return clientType
	case SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP:
		return clientType
	default:
		return "" // 不支持的类型返回空字符串
	}
}

// ValidateClientTypePermission 验证clientType权限（保持向后兼容）
func ValidateClientTypePermission(clientType string) string {
	return ExtractPermissionCodeFromClientType(clientType)
}

// ============== 角色名称映射 ==============

// GetRoleName 获取角色名称
func GetRoleName(roleCode string) string {
	switch roleCode {
	case ROLE_ADMIN:
		return "管理员"
	case ROLE_CASHIER:
		return "收银员"
	case ROLE_WAITER:
		return "服务员"
	default:
		return roleCode // 自定义角色返回原始代码
	}
}

// IsDefaultRole 判断是否为预制角色
func IsDefaultRole(roleCode string) bool {
	switch roleCode {
	case ROLE_ADMIN, ROLE_CASHIER, ROLE_WAITER:
		return true
	default:
		return false
	}
}

// ============== 权限代码描述映射 ==============

// GetSystemPermissionName 获取系统权限名称（支持树形结构）
func GetSystemPermissionName(permissionCode string) string {
	switch permissionCode {
	// 商务系统
	case SYSTEM_THUNDER_ERP_BUSINESS:
		return "商务系统"
	case SYSTEM_THUNDER_ERP_BUSINESS_LOGIN:
		return "商务系统登录"
	case SYSTEM_THUNDER_ERP_BUSINESS_AUDIT:
		return "商务系统审核"
	case SYSTEM_THUNDER_ERP_BUSINESS_ADMIN:
		return "商务系统管理"

	// 掌柜系统
	case SYSTEM_THUNDER_ERP_MANAGER:
		return "掌柜系统"
	case SYSTEM_THUNDER_ERP_MANAGER_LOGIN:
		return "掌柜系统登录"
	case SYSTEM_THUNDER_ERP_MANAGER_DASHBOARD:
		return "掌柜系统仪表盘"
	case SYSTEM_THUNDER_ERP_MANAGER_ADMIN:
		return "掌柜系统管理"
	case SYSTEM_THUNDER_ERP_MANAGER_CONFIG:
		return "掌柜系统配置"

	// 收银系统
	case SYSTEM_THUNDER_ERP_CASHIER:
		return "收银系统"
	case SYSTEM_THUNDER_ERP_CASHIER_LOGIN:
		return "收银系统登录"
	case SYSTEM_THUNDER_ERP_CASHIER_OPERATE:
		return "收银系统操作"
	case SYSTEM_THUNDER_ERP_CASHIER_REPORT:
		return "收银系统报表"

	// 报表系统
	case SYSTEM_THUNDER_ERP_REPORT:
		return "报表系统"
	case SYSTEM_THUNDER_ERP_REPORT_LOGIN:
		return "报表系统登录"
	case SYSTEM_THUNDER_ERP_REPORT_VIEW:
		return "报表系统查看"
	case SYSTEM_THUNDER_ERP_REPORT_EXPORT:
		return "报表系统导出"
	case SYSTEM_THUNDER_ERP_REPORT_ANALYSIS:
		return "报表系统分析"

	// 配置系统
	case SYSTEM_THUNDER_ERP_CONFIG:
		return "配置系统"
	case SYSTEM_THUNDER_ERP_CONFIG_LOGIN:
		return "配置系统登录"
	case SYSTEM_THUNDER_ERP_CONFIG_PERMISSION:
		return "配置系统权限管理"
	case SYSTEM_THUNDER_ERP_CONFIG_MEMBER:
		return "配置系统会员管理"
	case SYSTEM_THUNDER_ERP_CONFIG_BUSINESS:
		return "配置系统业务规则"

	// 移动点单系统
	case SYSTEM_THUNDER_ERP_MOBILE_ORDER:
		return "移动点单"
	case SYSTEM_THUNDER_ERP_MOBILE_ORDER_LOGIN:
		return "移动点单登录"
	case SYSTEM_THUNDER_ERP_MOBILE_ORDER_ORDER:
		return "移动点单下单"
	case SYSTEM_THUNDER_ERP_MOBILE_ORDER_MEMBER:
		return "移动点单会员"

	// 客用小程序
	case SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP:
		return "客用小程序"
	case SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP_LOGIN:
		return "客用小程序登录"
	case SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP_ORDER:
		return "客用小程序点单"
	case SYSTEM_THUNDER_ERP_CUSTOMER_MINIAPP_MEMBER:
		return "客用小程序会员"

	default:
		return permissionCode
	}
}

// GetModulePermissionName 获取模块权限名称（支持树形结构）
func GetModulePermissionName(permissionCode string) string {
	switch permissionCode {
	// 掌柜系统 - 库存管理
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY:
		return "库存管理主模块"
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY_PRODUCT:
		return "商品库存管理"
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY_MATERIAL:
		return "原料库存管理"
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY_SUPPLIER:
		return "供应商管理"
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY_PURCHASE:
		return "采购管理"
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY_STOCKTAKE:
		return "盘点管理"
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY_REPORT:
		return "库存报表"

	// 掌柜系统 - 营销管理
	case MODULE_THUNDER_ERP_MANAGER_MARKETING:
		return "营销管理主模块"
	case MODULE_THUNDER_ERP_MANAGER_MARKETING_CAMPAIGN:
		return "营销活动管理"
	case MODULE_THUNDER_ERP_MANAGER_MARKETING_ANALYSIS:
		return "营销数据分析"
	case MODULE_THUNDER_ERP_MANAGER_MARKETING_CONFIG:
		return "营销配置管理"
	case MODULE_THUNDER_ERP_MANAGER_MARKETING_CUSTOMER:
		return "客户营销管理"

	// 掌柜系统 - 优惠券
	case MODULE_THUNDER_ERP_MANAGER_COUPON:
		return "优惠券主模块"
	case MODULE_THUNDER_ERP_MANAGER_COUPON_TEMPLATE:
		return "优惠券模板管理"
	case MODULE_THUNDER_ERP_MANAGER_COUPON_ISSUE:
		return "优惠券发放管理"
	case MODULE_THUNDER_ERP_MANAGER_COUPON_USE:
		return "优惠券使用管理"
	case MODULE_THUNDER_ERP_MANAGER_COUPON_REPORT:
		return "优惠券报表"

	// 掌柜系统 - 员工管理
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE:
		return "员工管理主模块"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_INFO:
		return "员工信息管理"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ROLE:
		return "员工角色管理"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_PERMISSION:
		return "员工权限管理"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ATTENDANCE:
		return "员工考勤管理"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_SALARY:
		return "员工薪资管理"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_TRAINING:
		return "员工培训管理"
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ASSESSMENT:
		return "员工考核管理"

	// 掌柜系统 - 业绩管理
	case MODULE_THUNDER_ERP_MANAGER_PERFORMANCE:
		return "业绩管理主模块"
	case MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_SALES:
		return "销售业绩管理"
	case MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_TARGET:
		return "业绩目标管理"
	case MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_REWARD:
		return "业绩奖励管理"
	case MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_REPORT:
		return "业绩报表"

	// 收银系统
	case MODULE_THUNDER_ERP_CASHIER_ORDER:
		return "收银订单管理"
	case MODULE_THUNDER_ERP_CASHIER_PAYMENT:
		return "收银支付管理"
	case MODULE_THUNDER_ERP_CASHIER_MEMBER:
		return "会员管理"
	case MODULE_THUNDER_ERP_CASHIER_PRODUCT:
		return "商品管理"

	// 移动点单系统
	case MODULE_THUNDER_ERP_MOBILE_ORDER_MENU:
		return "移动点单菜单管理"
	case MODULE_THUNDER_ERP_MOBILE_ORDER_CART:
		return "购物车管理"
	case MODULE_THUNDER_ERP_MOBILE_ORDER_PAYMENT:
		return "支付管理"

	default:
		return permissionCode
	}
}

// ============== 权限继承验证函数 ==============

// HasPermissionWithInheritance 检查是否拥有权限（支持继承）
// 如果用户拥有父级权限，则自动拥有所有子级权限
func HasPermissionWithInheritance(userPermissions []string, requiredPermission string) bool {
	// 直接检查是否拥有精确权限
	for _, perm := range userPermissions {
		if perm == requiredPermission {
			return true
		}
	}

	// 检查是否拥有父级权限（向上查找）
	// 例如：需要 manager.employee.info，检查是否有 manager.employee 或 manager
	parentPermission := getParentPermission(requiredPermission)
	for parentPermission != "" {
		for _, perm := range userPermissions {
			if perm == parentPermission {
				return true
			}
		}
		parentPermission = getParentPermission(parentPermission)
	}

	return false
}

// getParentPermission 获取父级权限
// manager.employee.info → manager.employee
// manager.employee → manager
// manager → ""
func getParentPermission(permission string) string {
	lastDotIndex := -1
	for i := len(permission) - 1; i >= 0; i-- {
		if permission[i] == '.' {
			lastDotIndex = i
			break
		}
	}

	if lastDotIndex > 0 {
		return permission[:lastDotIndex]
	}
	return ""
}

// GetChildPermissions 获取所有子级权限
func GetChildPermissions(parentPermission string) []string {
	switch parentPermission {
	// 系统级权限子权限
	case SYSTEM_THUNDER_ERP_MANAGER:
		return []string{
			SYSTEM_THUNDER_ERP_MANAGER_LOGIN,
			SYSTEM_THUNDER_ERP_MANAGER_DASHBOARD,
			SYSTEM_THUNDER_ERP_MANAGER_ADMIN,
			SYSTEM_THUNDER_ERP_MANAGER_CONFIG,
		}
	case SYSTEM_THUNDER_ERP_CASHIER:
		return []string{
			SYSTEM_THUNDER_ERP_CASHIER_LOGIN,
			SYSTEM_THUNDER_ERP_CASHIER_OPERATE,
			SYSTEM_THUNDER_ERP_CASHIER_REPORT,
		}
	case SYSTEM_THUNDER_ERP_MOBILE_ORDER:
		return []string{
			SYSTEM_THUNDER_ERP_MOBILE_ORDER_LOGIN,
			SYSTEM_THUNDER_ERP_MOBILE_ORDER_ORDER,
			SYSTEM_THUNDER_ERP_MOBILE_ORDER_MEMBER,
		}

	// 模块级权限子权限
	case MODULE_THUNDER_ERP_MANAGER_INVENTORY:
		return []string{
			MODULE_THUNDER_ERP_MANAGER_INVENTORY_PRODUCT,
			MODULE_THUNDER_ERP_MANAGER_INVENTORY_MATERIAL,
			MODULE_THUNDER_ERP_MANAGER_INVENTORY_SUPPLIER,
			MODULE_THUNDER_ERP_MANAGER_INVENTORY_PURCHASE,
			MODULE_THUNDER_ERP_MANAGER_INVENTORY_STOCKTAKE,
			MODULE_THUNDER_ERP_MANAGER_INVENTORY_REPORT,
		}
	case MODULE_THUNDER_ERP_MANAGER_MARKETING:
		return []string{
			MODULE_THUNDER_ERP_MANAGER_MARKETING_CAMPAIGN,
			MODULE_THUNDER_ERP_MANAGER_MARKETING_ANALYSIS,
			MODULE_THUNDER_ERP_MANAGER_MARKETING_CONFIG,
			MODULE_THUNDER_ERP_MANAGER_MARKETING_CUSTOMER,
		}
	case MODULE_THUNDER_ERP_MANAGER_COUPON:
		return []string{
			MODULE_THUNDER_ERP_MANAGER_COUPON_TEMPLATE,
			MODULE_THUNDER_ERP_MANAGER_COUPON_ISSUE,
			MODULE_THUNDER_ERP_MANAGER_COUPON_USE,
			MODULE_THUNDER_ERP_MANAGER_COUPON_REPORT,
		}
	case MODULE_THUNDER_ERP_MANAGER_EMPLOYEE:
		return []string{
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_INFO,
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ROLE,
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_PERMISSION,
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ATTENDANCE,
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_SALARY,
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_TRAINING,
			MODULE_THUNDER_ERP_MANAGER_EMPLOYEE_ASSESSMENT,
		}
	case MODULE_THUNDER_ERP_MANAGER_PERFORMANCE:
		return []string{
			MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_SALES,
			MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_TARGET,
			MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_REWARD,
			MODULE_THUNDER_ERP_MANAGER_PERFORMANCE_REPORT,
		}

	default:
		return []string{}
	}
}

// ============== 默认角色权限配置 ==============

// GetDefaultRolePermissions 获取默认角色的权限配置
func GetDefaultRolePermissions(roleCode string) map[string][]string {
	switch roleCode {
	case ROLE_ADMIN:
		return map[string][]string{
			"system": {SYSTEM_THUNDER_ERP_MANAGER, SYSTEM_THUNDER_ERP_CASHIER, SYSTEM_THUNDER_ERP_MOBILE_ORDER},
			"module": {MODULE_THUNDER_ERP_MANAGER_INVENTORY, MODULE_THUNDER_ERP_MANAGER_MARKETING, MODULE_THUNDER_ERP_MANAGER_COUPON, MODULE_THUNDER_ERP_MANAGER_EMPLOYEE, MODULE_THUNDER_ERP_MANAGER_PERFORMANCE},
		}
	case ROLE_CASHIER:
		return map[string][]string{
			"system": {SYSTEM_THUNDER_ERP_CASHIER, SYSTEM_THUNDER_ERP_MOBILE_ORDER},
			"module": {},
		}
	case ROLE_WAITER:
		return map[string][]string{
			"system": {SYSTEM_THUNDER_ERP_CASHIER, SYSTEM_THUNDER_ERP_MOBILE_ORDER},
			"module": {},
		}
	default:
		return map[string][]string{
			"system": {},
			"module": {},
		}
	}
}
