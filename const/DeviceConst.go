package _const

import (
	"encoding/json"
	"fmt"
)

// x-device 请求头相关常量定义
// 对应文档：docs/api/x-device_spec.md

// ============== 业务系统枚举 ==============

// ClientBusinessType 业务系统类型
type ClientBusinessType string

const (
	// ClientBusinessTypeBusiness 商务系统 - 负责审核新KTV商家的资质，管理商家入驻流程
	ClientBusinessTypeBusiness ClientBusinessType = "thunder_erp_business"
	// ClientBusinessTypeManager 掌柜系统 - 负责门店初始配置和日常运营
	ClientBusinessTypeManager ClientBusinessType = "thunder_erp_manager"
	// ClientBusinessTypeCashier 收银系统 - 负责前台的交易和收银
	ClientBusinessTypeCashier ClientBusinessType = "thunder_erp_cashier"
	// ClientBusinessTypeReport 报表系统 - 负责财务对账、营收统计、数据分析等
	ClientBusinessTypeReport ClientBusinessType = "thunder_erp_report"
	// ClientBusinessTypeConfig 配置系统 - 负责权限、会员、经营规则等具体业务配置
	ClientBusinessTypeConfig ClientBusinessType = "thunder_erp_config"
	// ClientBusinessTypeMobileOrder 移动点单系统 - 负责现场点单、加单等包厢服务
	ClientBusinessTypeMobileOrder ClientBusinessType = "thunder_erp_mobile_order"
	// ClientBusinessTypeCustomerMiniapp 客用小程序 - 顾客通过手机扫码点单、查看会员权益等
	ClientBusinessTypeCustomerMiniapp ClientBusinessType = "thunder_erp_customer_miniapp"
)

// ClientBusinessTypeInfo 业务系统信息
type ClientBusinessTypeInfo struct {
	Name        string `json:"name"`        // 中文名称
	Description string `json:"description"` // 系统描述
	IsInternal  bool   `json:"isInternal"`  // 是否内部系统
}

// GetClientBusinessTypeInfo 获取业务系统信息
func GetClientBusinessTypeInfo(clientType ClientBusinessType) *ClientBusinessTypeInfo {
	infoMap := map[ClientBusinessType]ClientBusinessTypeInfo{
		ClientBusinessTypeBusiness: {
			Name:        "商务系统",
			Description: "负责审核新KTV商家的资质，管理商家入驻流程",
			IsInternal:  true,
		},
		ClientBusinessTypeManager: {
			Name:        "掌柜系统",
			Description: "负责门店初始配置和日常运营",
			IsInternal:  false,
		},
		ClientBusinessTypeCashier: {
			Name:        "收银系统",
			Description: "负责前台的交易和收银",
			IsInternal:  false,
		},
		ClientBusinessTypeReport: {
			Name:        "报表系统",
			Description: "负责财务对账、营收统计、数据分析等",
			IsInternal:  false,
		},
		ClientBusinessTypeConfig: {
			Name:        "配置系统",
			Description: "负责权限、会员、经营规则等具体业务配置",
			IsInternal:  false,
		},
		ClientBusinessTypeMobileOrder: {
			Name:        "移动点单系统",
			Description: "负责现场点单、加单等包厢服务",
			IsInternal:  false,
		},
		ClientBusinessTypeCustomerMiniapp: {
			Name:        "客用小程序",
			Description: "顾客通过手机扫码点单、查看会员权益等",
			IsInternal:  false,
		},
	}

	if info, exists := infoMap[clientType]; exists {
		return &info
	}
	return nil
}

// IsInternalClientType 判断是否为内部系统
func IsInternalClientType(clientType ClientBusinessType) bool {
	return clientType == ClientBusinessTypeBusiness
}

// AllClientBusinessTypes 所有业务系统类型
var AllClientBusinessTypes = []ClientBusinessType{
	ClientBusinessTypeBusiness,
	ClientBusinessTypeManager,
	ClientBusinessTypeCashier,
	ClientBusinessTypeReport,
	ClientBusinessTypeConfig,
	ClientBusinessTypeMobileOrder,
	ClientBusinessTypeCustomerMiniapp,
}

// InternalClientBusinessTypes 内部系统类型
var InternalClientBusinessTypes = []ClientBusinessType{
	ClientBusinessTypeBusiness,
}

// ExternalClientBusinessTypes 外部系统类型
var ExternalClientBusinessTypes = []ClientBusinessType{
	ClientBusinessTypeManager,
	ClientBusinessTypeCashier,
	ClientBusinessTypeReport,
	ClientBusinessTypeConfig,
	ClientBusinessTypeMobileOrder,
	ClientBusinessTypeCustomerMiniapp,
}

// ============== 设备平台枚举 ==============

// DevicePlatformType 设备平台类型
type DevicePlatformType string

const (
	// DevicePlatformTypeWindows Windows 桌面端
	DevicePlatformTypeWindows DevicePlatformType = "windows"
	// DevicePlatformTypeMacOS macOS 桌面端
	DevicePlatformTypeMacOS DevicePlatformType = "macos"
	// DevicePlatformTypeAndroid Android 终端（Pad/Phone/盒子）
	DevicePlatformTypeAndroid DevicePlatformType = "android"
	// DevicePlatformTypeWeb Web 浏览器
	DevicePlatformTypeWeb DevicePlatformType = "web"
	// DevicePlatformTypeMiniapp 小程序
	DevicePlatformTypeMiniapp DevicePlatformType = "miniapp"
)

// DevicePlatformTypeInfo 设备平台信息
type DevicePlatformTypeInfo struct {
	Name        string `json:"name"`        // 中文名称
	Description string `json:"description"` // 平台描述
}

// GetDevicePlatformTypeInfo 获取设备平台信息
func GetDevicePlatformTypeInfo(platform DevicePlatformType) *DevicePlatformTypeInfo {
	infoMap := map[DevicePlatformType]DevicePlatformTypeInfo{
		DevicePlatformTypeWindows: {
			Name:        "Windows 桌面端",
			Description: "Windows 桌面应用程序",
		},
		DevicePlatformTypeMacOS: {
			Name:        "macOS 桌面端",
			Description: "macOS 桌面应用程序",
		},
		DevicePlatformTypeAndroid: {
			Name:        "Android 终端",
			Description: "Android 终端（Pad/Phone/盒子）",
		},
		DevicePlatformTypeWeb: {
			Name:        "Web 浏览器",
			Description: "Web 浏览器应用",
		},
		DevicePlatformTypeMiniapp: {
			Name:        "小程序",
			Description: "微信小程序等小程序平台",
		},
	}

	if info, exists := infoMap[platform]; exists {
		return &info
	}
	return nil
}

// AllDevicePlatformTypes 所有设备平台类型
var AllDevicePlatformTypes = []DevicePlatformType{
	DevicePlatformTypeWindows,
	DevicePlatformTypeMacOS,
	DevicePlatformTypeAndroid,
	DevicePlatformTypeWeb,
	DevicePlatformTypeMiniapp,
}

// ============== x-device 结构体定义 ==============

// XDeviceInfo x-device 请求头数据结构
type XDeviceInfo struct {
	ClientType     ClientBusinessType `json:"clientType"`               // 业务系统标识
	Platform       DevicePlatformType `json:"platform"`                 // 设备平台标识
	ClientName     string             `json:"clientName,omitempty"`     // 客户端友好展示名称（可选）
	AppVersionCode string             `json:"appVersionCode,omitempty"` // 数字版本号（可选）
	AppVersion     string             `json:"appVersion,omitempty"`     // 语义化版本名称（可选）
}

// CreateXDeviceParams x-device 创建参数
type CreateXDeviceParams struct {
	ClientType     ClientBusinessType // 业务系统标识
	Platform       DevicePlatformType // 设备平台标识
	ClientName     string             // 客户端友好展示名称（可选）
	AppVersionCode string             // 数字版本号（可选）
	AppVersion     string             // 语义化版本名称（可选）
}

// ============== 工具函数 ==============

// IsValidClientBusinessType 验证业务系统类型是否有效
func IsValidClientBusinessType(clientType string) bool {
	for _, validType := range AllClientBusinessTypes {
		if string(validType) == clientType {
			return true
		}
	}
	return false
}

// IsValidDevicePlatformType 验证设备平台类型是否有效
func IsValidDevicePlatformType(platform string) bool {
	for _, validPlatform := range AllDevicePlatformTypes {
		if string(validPlatform) == platform {
			return true
		}
	}
	return false
}

// CreateXDeviceHeader 创建 x-device 请求头 JSON 字符串
func CreateXDeviceHeader(params CreateXDeviceParams) (string, error) {
	deviceInfo := XDeviceInfo{
		ClientType:     params.ClientType,
		Platform:       params.Platform,
		ClientName:     params.ClientName,
		AppVersionCode: params.AppVersionCode,
		AppVersion:     params.AppVersion,
	}

	bytes, err := json.Marshal(deviceInfo)
	if err != nil {
		return "", fmt.Errorf("failed to marshal x-device info: %w", err)
	}

	return string(bytes), nil
}

// ParseXDeviceHeader 解析 x-device 请求头字符串
func ParseXDeviceHeader(xDeviceHeader string) (*XDeviceInfo, error) {
	var deviceInfo XDeviceInfo

	err := json.Unmarshal([]byte(xDeviceHeader), &deviceInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal x-device header: %w", err)
	}

	// 基本字段验证
	if deviceInfo.ClientType == "" || deviceInfo.Platform == "" {
		return nil, fmt.Errorf("clientType and platform are required")
	}

	return &deviceInfo, nil
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid bool     `json:"isValid"` // 是否有效
	Errors  []string `json:"errors"`  // 错误列表
}

// ValidateXDeviceInfo 验证 x-device 请求头数据
func ValidateXDeviceInfo(deviceInfo XDeviceInfo) ValidationResult {
	var errors []string

	// 验证必填字段
	if deviceInfo.ClientType == "" {
		errors = append(errors, "clientType 字段不能为空")
	} else if !IsValidClientBusinessType(string(deviceInfo.ClientType)) {
		errors = append(errors, "clientType 字段值无效")
	}

	if deviceInfo.Platform == "" {
		errors = append(errors, "platform 字段不能为空")
	} else if !IsValidDevicePlatformType(string(deviceInfo.Platform)) {
		errors = append(errors, "platform 字段值无效")
	}

	return ValidationResult{
		IsValid: len(errors) == 0,
		Errors:  errors,
	}
}

// ============== 预设配置 ==============

// PresetXDeviceConfig 预设配置
type PresetXDeviceConfig struct {
	ClientType ClientBusinessType
	ClientName string
	Platform   DevicePlatformType
}

// PresetXDeviceConfigs 常用业务场景的 x-device 配置
var PresetXDeviceConfigs = map[string]PresetXDeviceConfig{
	"CASHIER_PC": {
		ClientType: ClientBusinessTypeCashier,
		ClientName: "收银PC",
		Platform:   DevicePlatformTypeWindows,
	},
	"MANAGER_WEB": {
		ClientType: ClientBusinessTypeManager,
		ClientName: "掌柜后台",
		Platform:   DevicePlatformTypeWeb,
	},
	"MOBILE_ORDER_PAD": {
		ClientType: ClientBusinessTypeMobileOrder,
		ClientName: "移动点单Pad",
		Platform:   DevicePlatformTypeAndroid,
	},
	"CUSTOMER_MINIAPP": {
		ClientType: ClientBusinessTypeCustomerMiniapp,
		ClientName: "客用小程序",
		Platform:   DevicePlatformTypeMiniapp,
	},
}
