package _const

// ============== 登录权限点 ==============
//
// 权限点命名规范：
//   {clientType}:login:{platform}
//
// 例如：
//   thunder_erp_cashier:login:windows
//   thunder_erp_manager:login:web
//   thunder_erp_mobile_order:login:android
//
// 说明：
// 1. clientType 来自 const/DeviceConst.go 中的 7 大业务端常量（均已加前缀 thunder_erp_）。
// 2. platform 对应 DevicePlatformType，当前主要包括：windows、macos、android、web、miniapp。
// 3. 后续如需支持更多平台，在 const/DeviceConst.go 中扩充平台枚举后，直接调用 BuildLoginPermissionCode 即可生成。
//
// 本文件仅做常量集中声明，真正的角色-权限关系由权限模块或 Casbin 等框架在系统初始化时写入数据库或策略文件。

// LoginPermissionCode 登录权限点类型
// 为了保持灵活性，使用 string 而非枚举类型，方便与数据库 / Casbin 直接交互。
type LoginPermissionCode string

// 预定义的常见平台关键字
const (
	PlatformWindows = "windows"
	PlatformMacOS   = "macos"
	PlatformAndroid = "android"
	PlatformWeb     = "web"
	PlatformMiniApp = "miniapp"
)

// BuildLoginPermissionCode 构造登录权限点
func BuildLoginPermissionCode(clientType ClientBusinessType, platform DevicePlatformType) LoginPermissionCode {
	return LoginPermissionCode(string(clientType) + ":login:" + string(platform))
}

// ============== 典型组合常量（示例） ==============
// 以下列举常用组合，以便在代码中引用。若需要全部组合，可在初始化阶段通过循环自动生成。

const (
	// 商务系统
	PERM_BUSINESS_LOGIN_WEB     LoginPermissionCode = "thunder_erp_business:login:web"
	PERM_BUSINESS_LOGIN_ANDROID LoginPermissionCode = "thunder_erp_business:login:android"

	// 掌柜后台
	PERM_MANAGER_LOGIN_WEB     LoginPermissionCode = "thunder_erp_manager:login:web"
	PERM_MANAGER_LOGIN_WINDOWS LoginPermissionCode = "thunder_erp_manager:login:windows"

	// 收银系统
	PERM_CASHIER_LOGIN_WINDOWS LoginPermissionCode = "thunder_erp_cashier:login:windows"
	PERM_CASHIER_LOGIN_MACOS   LoginPermissionCode = "thunder_erp_cashier:login:macos"

	// 报表系统
	PERM_REPORT_LOGIN_WEB LoginPermissionCode = "thunder_erp_report:login:web"

	// 配置系统
	PERM_CONFIG_LOGIN_WEB LoginPermissionCode = "thunder_erp_config:login:web"

	// 移动点单
	PERM_MOBILE_ORDER_LOGIN_ANDROID LoginPermissionCode = "thunder_erp_mobile_order:login:android"
	PERM_MOBILE_ORDER_LOGIN_WEB     LoginPermissionCode = "thunder_erp_mobile_order:login:web"

	// 客户小程序
	PERM_CUSTOMER_MINIAPP_LOGIN_MINI LoginPermissionCode = "thunder_erp_customer_miniapp:login:miniapp"
)
