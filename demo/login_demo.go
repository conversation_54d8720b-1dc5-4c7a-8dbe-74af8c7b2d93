package main

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// Employee 员工实体 - 简化版本用于demo
type Employee struct {
	Id      string `gorm:"column:id;primaryKey" json:"id"`
	VenueId string `gorm:"column:venue_id" json:"venue_id"`
	Name    string `gorm:"column:name" json:"name"`
	State   int    `gorm:"column:state" json:"state"`
}

func (Employee) TableName() string {
	return "employee"
}

// Venue 门店实体 - 简化版本用于demo
type Venue struct {
	Id    string `gorm:"column:id;primaryKey" json:"id"`
	Name  string `gorm:"column:name" json:"name"`
	State int    `gorm:"column:state" json:"state"`
}

func (Venue) TableName() string {
	return "venue"
}

// LoginRequestInfo 登录请求信息
type LoginRequestInfo struct {
	Phone          string `json:"phone"`
	Password       string `json:"password"`
	Mac            string `json:"mac"`
	VenueId        string `json:"venueId"`
	GrantIp        string `json:"grantIp"`
	DeviceType     string `json:"deviceType"`
	AppVersionCode string `json:"appVersionCode"`
	EmployeeId     string `json:"employeeId"`
}

// ClientType 客户端类型
type ClientType string

const (
	ClientTypePC      ClientType = "PC"
	ClientTypePad     ClientType = "PAD"
	ClientTypeMobile  ClientType = "MOBILE"
	ClientTypeUnknown ClientType = "UNKNOWN"
)

// LoginPermissionMiddleware 登录权限中间件
type LoginPermissionMiddleware struct {
	db *gorm.DB
}

// NewLoginPermissionMiddleware 创建登录权限中间件
func NewLoginPermissionMiddleware(db *gorm.DB) *LoginPermissionMiddleware {
	return &LoginPermissionMiddleware{db: db}
}

// detectClientType 检测客户端类型
func (lpm *LoginPermissionMiddleware) detectClientType(c *gin.Context) ClientType {
	// 检查 X-Client 头部（移动端特有）
	xClient := c.GetHeader("X-Client")
	if xClient != "" {
		return ClientTypePad
	}

	// 检查 User-Agent
	userAgent := c.GetHeader("User-Agent")
	if strings.Contains(userAgent, "Mobile") || strings.Contains(userAgent, "Android") || strings.Contains(userAgent, "iPhone") {
		return ClientTypeMobile
	}

	// 检查 Referer 来判断来源
	referer := c.GetHeader("Referer")
	if strings.Contains(referer, "merpdev-stage.ktvsky.com") {
		return ClientTypePC
	}

	// 检查 Origin
	origin := c.GetHeader("Origin")
	if strings.Contains(origin, "merpdev-stage.ktvsky.com") {
		return ClientTypePC
	}

	// 默认返回PC端
	return ClientTypePC
}

// getEmployeeRole 获取员工角色（使用真实员工数据）
func (lpm *LoginPermissionMiddleware) getEmployeeRole(employeeId string) string {
	// 使用和 erp_managent/middleware/testdata.go 相同的真实员工数据
	roleMap := map[string]string{
		// === 真实员工数据 (基于生产数据库 - 上地店94YTNnVUk) ===
		// 管理员角色
		"18xs6mKEE0": "ADMIN",   // 员工:MN，门店:上地，角色:系统管理员
		"1PPeP1fdvi": "MANAGER", // 员工:晏云云云，门店:上地，角色:店长

		// 收银员角色
		"meGmGjhsI":  "CASHIER", // 员工:yuezheng，门店:上地，角色:收银员
		"1sUgsFX1Ac": "CASHIER", // 员工:黄大壮，门店:上地，角色:收银员
		"2lrxWiJR3W": "CASHIER", // 员工:王晶云，门店:上地，角色:收银员

		// 服务员角色
		"94YTOeJiw": "WAITER", // 员工:钱磊，门店:上地，角色:服务员
		"956QdaCTm": "WAITER", // 员工:貟磊，门店:上地，角色:服务员
		"9CJrCIcRq": "WAITER", // 员工:张楠，门店:上地，角色:服务员

		// 清洁员角色
		"95avxB1wQ": "CLEANER", // 员工:贠磊，门店:上地，角色:清洁员
		"kX4qXhDNu": "CLEANER", // 员工:丁亮，门店:上地，角色:清洁员
	}

	if role, exists := roleMap[employeeId]; exists {
		return role
	}
	return "WAITER" // 默认角色
}

// checkClientPermission 检查客户端权限
func (lpm *LoginPermissionMiddleware) checkClientPermission(role string, clientType ClientType) bool {
	permissions := map[string][]ClientType{
		"ADMIN":   {ClientTypePC, ClientTypePad, ClientTypeMobile},
		"MANAGER": {ClientTypePC, ClientTypePad, ClientTypeMobile},
		"CASHIER": {ClientTypePC, ClientTypePad, ClientTypeMobile},
		"WAITER":  {ClientTypePad, ClientTypeMobile},
		"CLEANER": {ClientTypeMobile},
	}

	allowedClients, exists := permissions[role]
	if !exists {
		return false
	}

	for _, allowed := range allowedClients {
		if allowed == clientType {
			return true
		}
	}
	return false
}

// getAllowedClients 获取角色允许的客户端类型
func (lpm *LoginPermissionMiddleware) getAllowedClients(role string) []string {
	permissions := map[string][]string{
		"ADMIN":   {"PC", "PAD", "MOBILE"},
		"MANAGER": {"PC", "PAD", "MOBILE"},
		"CASHIER": {"PC", "PAD", "MOBILE"},
		"WAITER":  {"PAD", "MOBILE"},
		"CLEANER": {"MOBILE"},
	}

	if clients, exists := permissions[role]; exists {
		return clients
	}
	return []string{}
}

// ValidateLoginPermission 验证登录权限
func (lpm *LoginPermissionMiddleware) ValidateLoginPermission() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 解析登录请求信息
		var loginInfo LoginRequestInfo

		// 从Body获取信息
		if err := c.ShouldBindJSON(&loginInfo); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    "INVALID_REQUEST",
				"message": "无效的登录请求格式",
				"error":   err.Error(),
			})
			c.Abort()
			return
		}

		// 从Header获取补充信息
		headerEmployeeId := c.GetHeader("X-Employee-Id")
		headerMac := c.GetHeader("X-Mac")
		headerVenueId := c.GetHeader("X-Venue-Id")

		// 优先使用Header中的信息
		if headerEmployeeId != "" {
			loginInfo.EmployeeId = headerEmployeeId
		}
		if headerMac != "" {
			loginInfo.Mac = headerMac
		}
		if headerVenueId != "" {
			loginInfo.VenueId = headerVenueId
		}

		// 检测客户端类型
		clientType := lpm.detectClientType(c)

		// 验证必要参数
		if loginInfo.EmployeeId == "" || loginInfo.VenueId == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":        "MISSING_CREDENTIALS",
				"message":     "缺少员工ID或门店ID",
				"client_type": string(clientType),
			})
			c.Abort()
			return
		}

		// 验证员工是否存在
		var employee Employee
		if err := lpm.db.Where("id = ? AND state = ?", loginInfo.EmployeeId, 0).First(&employee).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":        "EMPLOYEE_NOT_FOUND",
				"message":     "员工不存在或已禁用",
				"employee_id": loginInfo.EmployeeId,
			})
			c.Abort()
			return
		}

		// 验证门店是否存在
		var venue Venue
		if err := lpm.db.Where("id = ? AND state = ?", loginInfo.VenueId, 0).First(&venue).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":     "VENUE_NOT_FOUND",
				"message":  "门店不存在或已禁用",
				"venue_id": loginInfo.VenueId,
			})
			c.Abort()
			return
		}

		// 验证员工与门店的关系
		if employee.VenueId != venue.Id {
			c.JSON(http.StatusForbidden, gin.H{
				"code":           "EMPLOYEE_VENUE_MISMATCH",
				"message":        "员工不属于该门店",
				"employee_venue": employee.VenueId,
				"request_venue":  venue.Id,
			})
			c.Abort()
			return
		}

		// 获取员工角色
		role := lpm.getEmployeeRole(employee.Id)

		// 检查客户端权限
		if !lpm.checkClientPermission(role, clientType) {
			c.JSON(http.StatusForbidden, gin.H{
				"code":            "CLIENT_ACCESS_DENIED",
				"message":         fmt.Sprintf("角色 %s 无权限从 %s 端登录", role, clientType),
				"role":            role,
				"client_type":     string(clientType),
				"allowed_clients": lpm.getAllowedClients(role),
			})
			c.Abort()
			return
		}

		// 记录登录日志
		fmt.Printf("[LOGIN SUCCESS] Employee: %s(%s) - Venue: %s(%s) - Role: %s - Client: %s - IP: %s\n",
			employee.Name, employee.Id,
			venue.Name, venue.Id,
			role,
			string(clientType),
			c.ClientIP(),
		)

		// 设置上下文信息
		c.Set("employee_id", employee.Id)
		c.Set("employee_name", employee.Name)
		c.Set("venue_id", venue.Id)
		c.Set("venue_name", venue.Name)
		c.Set("employee_role", role)
		c.Set("client_type", string(clientType))
		c.Set("login_info", loginInfo)

		c.Next()
	}
}

// setupDB 创建数据库连接
func setupDB() *gorm.DB {
	dsn := "root:ktvsky5166@tcp(**************:3306)/autoapp?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("无法连接到数据库: " + err.Error())
	}
	return db
}

func main() {
	// 创建Gin路由器
	r := gin.Default()

	// 创建数据库连接
	db := setupDB()

	// 创建登录权限中间件
	loginMiddleware := NewLoginPermissionMiddleware(db)

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type,Authorization,X-Employee-Id,X-Venue-Id,X-Mac,X-Client")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 首页
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message":     "ERP登录权限演示系统",
			"version":     "1.0.0",
			"description": "基于您提供的真实登录接口的权限验证演示",
			"endpoints": gin.H{
				"login":     "/shouyin/api/login/phone",
				"test_data": "/test-data",
				"health":    "/health",
			},
			"features": []string{
				"真实数据库验证",
				"客户端类型检测",
				"角色权限控制",
				"Header和Body参数解析",
			},
		})
	})

	// 测试数据
	r.GET("/test-data", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"description": "基于您提供的curl示例的测试用例",
			"test_cases": gin.H{
				"pc_login": gin.H{
					"description": "PC端登录（收银员权限）",
					"curl": `curl -X POST http://localhost:8081/shouyin/api/login/phone \
  -H "Content-Type: application/json" \
  -H "Origin: https://merpdev-stage.ktvsky.com" \
  -H "Referer: https://merpdev-stage.ktvsky.com/" \
  -H "X-Employee-Id: meGmGjhsI" \
  -H "X-Mac: 3719e098d7241be6" \
  -H "X-Venue-Id: 94YTNnVUk" \
  -d '{"phone":"18500851553","password":"000000","mac":"3719e098d7241be6","venueId":"94YTNnVUk","grantIp":"*************","deviceType":"","appVersionCode":"","employeeId":"meGmGjhsI"}'`,
					"expected": "SUCCESS - 收银员可以从PC端登录",
				},
				"pad_login": gin.H{
					"description": "Pad端登录（收银员权限）",
					"curl": `curl -X POST http://localhost:8081/shouyin/api/login/phone \
  -H "Content-Type: application/json" \
  -H "X-Client: PAD_APP" \
  -H "X-Employee-Id: meGmGjhsI" \
  -H "X-Mac: 3719e098d7241be6" \
  -H "X-Venue-Id: 94YTNnVUk" \
  -d '{"phone":"18500851553","password":"000000","mac":"3719e098d7241be6","venueId":"94YTNnVUk","grantIp":"*************","deviceType":"PAD","appVersionCode":"1.0.0","employeeId":"meGmGjhsI"}'`,
					"expected": "SUCCESS - 收银员可以从Pad端登录",
				},
				"waiter_pc_denied": gin.H{
					"description": "服务员尝试PC端登录（应被拒绝）",
					"curl": `curl -X POST http://localhost:8081/shouyin/api/login/phone \
  -H "Content-Type: application/json" \
  -H "Origin: https://merpdev-stage.ktvsky.com" \
  -H "X-Employee-Id: waiter001" \
  -H "X-Venue-Id: 94YTNnVUk" \
  -d '{"phone":"18500851553","password":"000000","employeeId":"waiter001","venueId":"94YTNnVUk"}'`,
					"expected": "DENIED - 服务员无权限从PC端登录",
				},
			},
			"role_permissions": gin.H{
				"CASHIER": []string{"PC", "PAD", "MOBILE"},
				"WAITER":  []string{"PAD", "MOBILE"},
				"CLEANER": []string{"MOBILE"},
			},
		})
	})

	// 登录接口 - 使用您提供的真实路径
	r.POST("/shouyin/api/login/phone", loginMiddleware.ValidateLoginPermission(), func(c *gin.Context) {
		// 获取上下文信息
		employeeId := c.GetString("employee_id")
		employeeName := c.GetString("employee_name")
		venueId := c.GetString("venue_id")
		venueName := c.GetString("venue_name")
		role := c.GetString("employee_role")
		clientType := c.GetString("client_type")

		// 模拟登录成功响应
		c.JSON(http.StatusOK, gin.H{
			"code":    "SUCCESS",
			"message": "登录成功",
			"data": gin.H{
				"token":         "mock_jwt_token_12345",
				"employee_id":   employeeId,
				"employee_name": employeeName,
				"venue_id":      venueId,
				"venue_name":    venueName,
				"role":          role,
				"client_type":   clientType,
				"permissions": gin.H{
					"can_access_pc":     role == "ADMIN" || role == "MANAGER" || role == "CASHIER",
					"can_access_pad":    role != "CLEANER",
					"can_access_mobile": true,
				},
				"menu": []gin.H{
					{"name": "订单管理", "path": "/orders", "enabled": true},
					{"name": "商品管理", "path": "/products", "enabled": role != "WAITER"},
					{"name": "会员管理", "path": "/members", "enabled": role != "CLEANER"},
					{"name": "报表查看", "path": "/reports", "enabled": role == "ADMIN" || role == "MANAGER"},
				},
			},
		})
	})

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "ERP Login Permission Demo",
			"timestamp": "2024-01-15 12:00:00",
			"database":  "connected",
		})
	})

	// 启动服务器
	fmt.Println("🚀 ERP登录权限演示服务启动中...")
	fmt.Println("📋 服务信息:")
	fmt.Println("   - 地址: http://localhost:8081")
	fmt.Println("   - 登录接口: POST http://localhost:8081/shouyin/api/login/phone")
	fmt.Println("   - 测试数据: http://localhost:8081/test-data")
	fmt.Println("📋 真实测试账号:")
	fmt.Println("   - 员工ID: meGmGjhsI (收银员)")
	fmt.Println("   - 门店ID: 94YTNnVUk")
	fmt.Println("   - 员工姓名: yuezheng")
	fmt.Println("   - 门店名称: 上地")
	fmt.Println("📋 快速验证:")
	fmt.Println("   1. 访问首页: curl http://localhost:8081/")
	fmt.Println("   2. PC端登录: curl -X POST http://localhost:8081/shouyin/api/login/phone -H 'Content-Type: application/json' -H 'Origin: https://merpdev-stage.ktvsky.com' -H 'X-Employee-Id: meGmGjhsI' -H 'X-Venue-Id: 94YTNnVUk' -d '{\"phone\":\"18500851553\",\"employeeId\":\"meGmGjhsI\",\"venueId\":\"94YTNnVUk\"}'")
	fmt.Println("   3. Pad端登录: curl -X POST http://localhost:8081/shouyin/api/login/phone -H 'Content-Type: application/json' -H 'X-Client: PAD_APP' -H 'X-Employee-Id: meGmGjhsI' -H 'X-Venue-Id: 94YTNnVUk' -d '{\"phone\":\"18500851553\",\"employeeId\":\"meGmGjhsI\",\"venueId\":\"94YTNnVUk\"}'")

	log.Fatal(r.Run(":8081"))
}
