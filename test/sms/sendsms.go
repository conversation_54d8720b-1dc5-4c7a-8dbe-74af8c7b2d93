package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"

	"github.com/sirupsen/logrus"
)

// 云片平台API URL
const yunpianURL = "https://sms.yunpian.com/v1/sms/send.json"

// 云片平台API KEY
const apiKey = "6df90577d22c6120f315f1866a0f3925"

const phone = "15210872105"

// 发送短信
func SendSMS(phone string) error {

	code := "1234"

	txt := fmt.Sprintf("【汇金商户通】您的验证码是%v。如非本人操作，请忽略本短信。", code)

	// 准备请求数据
	form := url.Values{}
	form.Add("apikey", apiKey)
	form.Add("mobile", phone)
	form.Add("text", txt)
	logrus.Infof("send sms code:%v mobile:%v ", code, phone)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", yunpianURL, bytes.NewBufferString(form.Encode()))
	if err != nil {
		logrus.Errorf("创建HTTP请求失败: %v", err)
		return err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded;charset=utf-8;")

	// 发送HTTP请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logrus.Errorf("发送HTTP请求失败: %v", err)
		return err
	}
	defer resp.Body.Close()

	// 读取响应数据
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("读取响应数据失败: %v", err)
		return err
	}

	// 添加日志显示API返回的原始响应
	logrus.Infof("API响应状态码: %d", resp.StatusCode)
	logrus.Infof("API响应原始数据: %s", string(body))

	// 检查响应状态
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		logrus.Errorf("解析响应JSON失败: %v", err)
		return err
	}

	// 添加日志显示解析后的响应结构
	logrus.Infof("API响应解析结果: %+v", result)

	if code, ok := result["code"].(float64); ok && code != 0 {
		logrus.Errorf("发送失败: %v", result["msg"])
		return fmt.Errorf("发送失败: %v", result["msg"])
	}

	logrus.Infof("短信发送成功")
	return err
}

// 程序执行入口
func main1() {
	// 设置日志格式
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logrus.Infof("开始发送短信测试...")

	// 调用发送短信函数
	err := SendSMS(phone)
	if err != nil {
		logrus.Errorf("短信发送失败: %v", err)
	} else {
		logrus.Infof("短信发送完成")
	}
}
