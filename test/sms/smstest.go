package main

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
)

// --- 请在这里配置您的账户信息 ---
const (
	// API基础地址
	BaseURL = "http://193.112.94.226:9511"
	// 您的产品sp_id
	SpID = "537736" // <-- 替换为您的SPID
	// 您的产品密码 (原始密码，非MD5)
	Password = "fde383d7" // <-- 替换为您的密码
	// 您已报备的短信签名 (注意：必须与报备的完全一致)
	Signature = "【雷石KTV】"
)

// API 响应结构体
type SmsResponse struct {
	Code  int         `json:"code"`
	Msg   string      `json:"msg"`
	MsgID interface{} `json:"msg_id"` // msg_id 可能是数字或字符串，用interface{}更安全
	Data  interface{} `json:"data"`   // data 可能是字符串或对象，用interface{}更安全
}

// md5Hash 计算字符串的MD5值
func md5Hash(text string) string {
	hasher := md5.New()
	hasher.Write([]byte(text))
	return hex.EncodeToString(hasher.Sum(nil))
}

// sendSms 是核心的短信发送函数
// signHeader: 短信签名, 例如 "【雷石KTV】"
// mobile: 接收短信的手机号
// content: 短信正文 (不包含签名)
func sendSms(signHeader, mobile, content string) (*SmsResponse, error) {
	apiURL := BaseURL + "/api/send-sms-single"

	// 将签名和内容拼接成完整的短信
	fullContent := signHeader + content

	// 准备POST表单数据
	formData := url.Values{}
	formData.Set("sp_id", SpID)
	formData.Set("mobile", mobile)
	formData.Set("content", fullContent) // 使用拼接后的完整内容
	// 根据文档，密码是sp_id的密码进行md5处理
	formData.Set("password", md5Hash(Password))

	// 创建请求
	req, err := http.NewRequest("POST", apiURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 打印原始响应，方便调试
	log.Printf("收到原始响应: %s\n", string(body))

	// 解析JSON响应
	var smsResp SmsResponse
	if err := json.Unmarshal(body, &smsResp); err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	// 检查API返回的错误码
	if smsResp.Code != 0 {
		return &smsResp, fmt.Errorf("API返回错误: code=%d, msg=%s, data=%v", smsResp.Code, smsResp.Msg, smsResp.Data)
	}

	return &smsResp, nil
}

// --- 模板发送函数 ---

// SendMemberCardSuccess 发送会员开卡成功短信
func SendMemberCardSuccess(signHeader, mobile, shopName, cardNo, initialAmount, currentBalance string) {
	log.Println("--- 正在发送 [会员开卡成功] 短信 ---")
	// 格式化短信正文 (不含签名)
	content := fmt.Sprintf("尊敬的会员，您在[%s]的会员卡开卡成功！会员卡号%s，初始储值金额%s元，当前卡内余额%s元。",
		shopName, cardNo, initialAmount, currentBalance)

	log.Printf("发送至: %s\n", mobile)
	log.Printf("发送内容: %s%s\n", signHeader, content)

	resp, err := sendSms(signHeader, mobile, content)
	if err != nil {
		log.Printf("发送失败: %v\n", err)
		return
	}
	log.Printf("发送成功! 消息ID(msg_id): %v\n", resp.MsgID)
}

// SendMemberTopUpSuccess 发送会员充值成功短信
func SendMemberTopUpSuccess(signHeader, mobile, shopName, topUpAmount, cardBalance, totalBalance string) {
	log.Println("--- 正在发送 [会员充值成功] 短信 ---")
	// 格式化短信正文 (不含签名)
	content := fmt.Sprintf("您的会员卡储值成功！本次储值金额%s元，当前卡内储值余额%s元，余额总计%s元。",
		topUpAmount, cardBalance, totalBalance)

	log.Printf("发送至: %s\n", mobile)
	log.Printf("发送内容: %s%s\n", signHeader, content)

	resp, err := sendSms(signHeader, mobile, content)
	if err != nil {
		log.Printf("发送失败: %v\n", err)
		return
	}
	log.Printf("发送成功! 消息ID(msg_id): %v\n", resp.MsgID)
}

// SendMemberConsumptionNotice 发送会员消费通知短信
func SendMemberConsumptionNotice(signHeader, mobile, shopName, consumptionAmount, pointsInfo, remainingBalance, totalBalance string) {
	log.Println("--- 正在发送 [会员消费通知] 短信 ---")

	// 处理可选的积分信息
	var pointsText string
	if pointsInfo != "" {
		pointsText = pointsInfo + "，"
	}

	// 格式化短信正文 (不含签名)
	content := fmt.Sprintf("尊敬的会员，您在[%s]本次消费金额%s元。%s剩余储值%s元，余额总计%s元。",
		shopName, consumptionAmount, pointsText, remainingBalance, totalBalance)

	log.Printf("发送至: %s\n", mobile)
	log.Printf("发送内容: %s%s\n", signHeader, content)

	resp, err := sendSms(signHeader, mobile, content)
	if err != nil {
		log.Printf("发送失败: %v\n", err)
		return
	}
	log.Printf("发送成功! 消息ID(msg_id): %v\n", resp.MsgID)
}

func main() {
	// 检查配置是否已填写
	if SpID == "你的SPID" || Password == "你的密码" {
		log.Fatal("请在代码顶部的常量区域填写您的 SpID 和 Password。")
	}

	// --- 测试数据 ---
	testMobile := "15210872105" // <-- 替换为您的测试手机号
	testShopName := "雷石KTV(国贸店)"

	// // 1. 测试发送会员开卡短信
	// SendMemberCardSuccess(Signature, testMobile, testShopName, "888001", "200.00", "200.00")

	// fmt.Println("\n========================================\n")

	// 2. 测试发送会员充值短信
	SendMemberTopUpSuccess(Signature, testMobile, testShopName, "300.00", "500.00", "550.00")

	fmt.Println("\n========================================\n")

	// // 3. 测试发送会员消费短信
	// // a. 带积分信息
	// pointsMsg := "本次消费赠送50积分"
	// SendMemberConsumptionNotice(Signature, testMobile, testShopName, "98.00", pointsMsg, "402.00", "452.00")

	// fmt.Println("\n----------------------------------------\n")

	// b. 不带积分信息
	// SendMemberConsumptionNotice(Signature, testMobile, testShopName, "120.00", "", "282.00", "332.00")
}
