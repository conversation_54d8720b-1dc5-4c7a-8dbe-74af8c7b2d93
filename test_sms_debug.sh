#!/bin/bash

# SMS 调试测试脚本
# 使用方法: ./test_sms_debug.sh

# API服务器地址
API_BASE_URL="http://medev-stage.ktvsky.com"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== SMS 调试测试脚本 ===${NC}"

# 测试1: 调试模式 - 详细信息
echo -e "\n${YELLOW}测试1: 调试模式发送短信验证码（详细调试信息）${NC}"
echo "请求URL: $API_BASE_URL/api/sms/debug-send-code"
echo "请求体:"
DEBUG_TEST_DATA='{
  "phone": "***********",
  "testMode": false
}'
echo "$DEBUG_TEST_DATA"

echo -e "\n${YELLOW}执行调试请求...${NC}"
DEBUG_RESPONSE=$(curl -s -X POST \
  "$API_BASE_URL/api/sms/debug-send-code" \
  -H "Content-Type: application/json" \
  -d "$DEBUG_TEST_DATA")

echo -e "${GREEN}调试响应结果:${NC}"
echo "$DEBUG_RESPONSE" | jq '.' 2>/dev/null || echo "$DEBUG_RESPONSE"

# 解析调试响应中的关键信息
echo -e "\n${BLUE}=== 调试信息分析 ===${NC}"

# 检查账户信息 - 修复JSON路径
ACCOUNT_BALANCE=$(echo "$DEBUG_RESPONSE" | jq -r '.data.account_info.balance // "未知"' 2>/dev/null)
ACCOUNT_MOBILE=$(echo "$DEBUG_RESPONSE" | jq -r '.data.account_info.mobile // "未知"' 2>/dev/null)
ACCOUNT_NICK=$(echo "$DEBUG_RESPONSE" | jq -r '.data.account_info.nick // "未知"' 2>/dev/null)

echo -e "${BLUE}云片账户信息:${NC}"
echo "  账户余额: $ACCOUNT_BALANCE 元"
echo "  绑定手机: $ACCOUNT_MOBILE"
echo "  账户昵称: $ACCOUNT_NICK"

# 检查短信发送信息 - 修复JSON路径
SMS_SUCCESS=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.success // false' 2>/dev/null)
SMS_ERROR=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.error // ""' 2>/dev/null)
SMS_CODE=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.code // ""' 2>/dev/null)
SMS_TEXT=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.text // ""' 2>/dev/null)
SMS_RESPONSE_CODE=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.response.code // ""' 2>/dev/null)
SMS_RESPONSE_MSG=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.response.msg // ""' 2>/dev/null)
SMS_COUNT=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.response.result.count // ""' 2>/dev/null)
SMS_FEE=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.response.result.fee // ""' 2>/dev/null)
SMS_SID=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.response.result.sid // ""' 2>/dev/null)
SMS_STATUS_CODE=$(echo "$DEBUG_RESPONSE" | jq -r '.data.sms_debug_info.status_code // ""' 2>/dev/null)

echo -e "\n${BLUE}短信发送信息:${NC}"
echo "  发送成功: $SMS_SUCCESS"
echo "  验证码: $SMS_CODE"
echo "  短信内容: $SMS_TEXT"
echo "  HTTP状态码: $SMS_STATUS_CODE"
echo "  云片返回码: $SMS_RESPONSE_CODE"
echo "  云片返回消息: $SMS_RESPONSE_MSG"
echo "  发送数量: $SMS_COUNT"
echo "  消费金额: $SMS_FEE 元"
echo "  短信ID: $SMS_SID"

if [ "$SMS_ERROR" != "" ] && [ "$SMS_ERROR" != "null" ]; then
    echo -e "  ${RED}错误信息: $SMS_ERROR${NC}"
fi

# 分析发送状态
echo -e "\n${BLUE}=== 发送状态分析 ===${NC}"
if [ "$SMS_SUCCESS" = "true" ] && [ "$SMS_RESPONSE_CODE" = "0" ]; then
    echo -e "${GREEN}✅ 短信发送技术层面成功${NC}"
    echo -e "${YELLOW}⚠️  如果手机未收到短信，可能原因：${NC}"
    echo "   1. 运营商拦截（【汇金商户通】可能被识别为营销短信）"
    echo "   2. 手机安全软件拦截"
    echo "   3. 短信进入垃圾短信箱"
    echo "   4. 运营商延迟（1-5分钟）"
    echo "   5. 手机信号问题"
else
    echo -e "${RED}❌ 短信发送失败${NC}"
fi

# 测试2: 错误手机号码调试
echo -e "\n\n${YELLOW}测试2: 错误手机号码格式调试${NC}"
echo "请求URL: $API_BASE_URL/api/sms/debug-send-code"
echo "请求体:"
ERROR_PHONE_DATA='{
  "phone": "1234567890",
  "testMode": false
}'
echo "$ERROR_PHONE_DATA"

echo -e "\n${YELLOW}执行错误手机号测试...${NC}"
ERROR_RESPONSE=$(curl -s -X POST \
  "$API_BASE_URL/api/sms/debug-send-code" \
  -H "Content-Type: application/json" \
  -d "$ERROR_PHONE_DATA")

echo -e "${GREEN}错误测试响应:${NC}"
echo "$ERROR_RESPONSE" | jq '.' 2>/dev/null || echo "$ERROR_RESPONSE"

# 测试3: 常规发送对比
echo -e "\n\n${YELLOW}测试3: 常规发送接口对比测试${NC}"
echo "请求URL: $API_BASE_URL/api/sms/send-code"
echo "请求体:"
NORMAL_TEST_DATA='{
  "phone": "***********",
  "testMode": false
}'
echo "$NORMAL_TEST_DATA"

echo -e "\n${YELLOW}执行常规发送请求...${NC}"
NORMAL_RESPONSE=$(curl -s -X POST \
  "$API_BASE_URL/api/sms/send-code" \
  -H "Content-Type: application/json" \
  -d "$NORMAL_TEST_DATA")

echo -e "${GREEN}常规发送响应:${NC}"
echo "$NORMAL_RESPONSE" | jq '.' 2>/dev/null || echo "$NORMAL_RESPONSE"

echo -e "\n${GREEN}=== 调试测试完成 ===${NC}"

echo -e "\n${YELLOW}=== 问题诊断结果 ===${NC}"
if [ "$SMS_SUCCESS" = "true" ] && [ "$SMS_RESPONSE_CODE" = "0" ] && [ "$ACCOUNT_BALANCE" != "未知" ]; then
    echo -e "${GREEN}技术层面检查通过：${NC}"
    echo "  ✅ 云片账户余额充足：$ACCOUNT_BALANCE 元"
    echo "  ✅ API密钥配置正确"
    echo "  ✅ 短信发送成功，消费：$SMS_FEE 元"
    echo "  ✅ 云片API返回成功状态"
    
    echo -e "\n${YELLOW}手机未收到短信的可能原因：${NC}"
    echo -e "${RED}1. 运营商拦截问题（最可能）${NC}"
    echo "   - 短信签名【汇金商户通】可能被识别为营销短信"
    echo "   - 建议更换短信签名或联系云片客服"
    
    echo -e "\n${YELLOW}2. 手机端问题${NC}"
    echo "   - 检查手机安全软件是否拦截"
    echo "   - 查看垃圾短信箱"
    echo "   - 确认手机信号正常"
    
    echo -e "\n${YELLOW}3. 运营商延迟${NC}"
    echo "   - 等待1-5分钟再检查"
else
    echo -e "${RED}技术层面存在问题，需要进一步排查${NC}"
fi

echo -e "\n${BLUE}建议解决方案：${NC}"
echo "1. 尝试更换短信签名（去掉【汇金商户通】或改为【验证码】）"
echo "2. 联系云片客服确认账户状态和短信模板"
echo "3. 测试发送到其他手机号码"
echo "4. 考虑升级到云片v2 API"
echo "5. 查看服务器日志获取更详细信息"
