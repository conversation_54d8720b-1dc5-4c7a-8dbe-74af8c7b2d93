package util

import (
	"errors"
	"fmt"
	mathrand "math/rand"
	"regexp"
	"strings"
	"time"
	_const "voderpltvv/const"
)

func GetSceneStr() string {
	tm := GetDateTimeStr()
	tm = strings.ReplaceAll(tm, "-", "")
	tm = strings.ReplaceAll(tm, " ", "")
	tm = strings.ReplaceAll(tm, ":", "")
	randNum := fmt.Sprintf("%04d", mathrand.Intn(10000))
	return fmt.Sprintf("AS%s%s", tm, randNum)
}

func GetOrderNo(venueId string) string {
	return getOrderNo_(venueId, "O")
}

func GetBillId(venueId string) string {
	return getOrderNo_(venueId, "B")
}

func GetPayId(venueId string) string {
	return getOrderNo_(venueId, "P")
}

func GetProductOutId(venueId string) string {
	return getOrderNo_(venueId, "PO")
}

// GetStorageNo 生成存酒单号
func GetStorageNo(venueId string) string {
	return getOrderNo_(venueId, "W")
}

// GetWithdrawNo 生成取酒单号
func GetWithdrawNo(venueId string) string {
	return getOrderNo_(venueId, "D")
}

func GetHandNo(venueId string) string {
	return getOrderNo_(venueId, "H")
}

func GetMemberBillId(venueId string) string {
	return getOrderNo_(venueId, "BM")
}

func GetMemberPayId(venueId string) string {
	return getOrderNo_(venueId, "PM")
}

func GenerateCardNumber(number int) string {
	return "TE" + fmt.Sprintf("%06d", number)
}

func getOrderNo_(venueId string, prefix string) string {
	// 订单编号规则：TH + venueid + yymmddhhmmss + 4位随机数
	if venueId == "" {
		venueId = "0000"
	}
	if len(venueId) > 8 { // 取后8位
		venueId = venueId[len(venueId)-8:]
	}

	now := time.Now()
	timeStr := now.Format("060102150405") // yyMMddHHmmss
	randNum := fmt.Sprintf("%04d", mathrand.Intn(10000))

	return prefix + venueId + timeStr + randNum
}

func GetSessionId(venueId string) string {
	// 场次ID规则：S + venueid + yymmddhhmmss + 4位随机数
	if venueId == "" {
		venueId = "0000"
	}
	if len(venueId) > 8 { // 取后8位
		venueId = venueId[len(venueId)-8:]
	}

	now := time.Now()
	timeStr := now.Format("060102150405") // yyMMddHHmmss
	randNum := fmt.Sprintf("%04d", mathrand.Intn(10000))

	return "S" + venueId + timeStr + randNum
}

func GetVenueBusinessTime(startHours, endHours *string) (int64, int64, error) {
	// 校验时间格式 HH:mm
	timeRegex := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	if startHours != nil && !timeRegex.MatchString(*startHours) {
		return -1, -1, errors.New("startHours格式错误，应为HH:mm")
	}
	if endHours != nil && !timeRegex.MatchString(*endHours) {
		return -1, -1, errors.New("endHours格式错误，应为HH:mm")
	}

	// 2006-01-02 15:[04:05]
	nowCommonYmd := GetDateTimeStr()[:10]
	venueStartTimeStr := nowCommonYmd + " " + *startHours + ":00"
	venueStartTimeObj, _ := ParseDatetime(venueStartTimeStr)

	venueEndTimeStr := nowCommonYmd + " " + *endHours + ":00"
	venueEndTimeObj, _ := ParseDatetime(venueEndTimeStr)

	return venueStartTimeObj.Unix(), venueEndTimeObj.Unix(), nil
}

func ValidateVenueEndHour(endHour *string) error {
	// 校验时间格式 HH:mm
	timeRegex := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	if endHour == nil {
		return errors.New("endHour不能为空")
	}
	if !timeRegex.MatchString(*endHour) {
		return errors.New("endHour格式错误，应为HH:mm")
	}
	return nil
}

// RoundAmount 金额抹零
// amount: 金额(分)
// roundType: yuan-抹元 jiao-抹角 fen-抹分 round-四舍五入
func RoundAmount(amount int64, roundType string) int64 {
	switch roundType {
	case "yuan": // 抹元
		return amount / 100 * 100
	case "jiao": // 抹角
		return amount / 10 * 10
	case "fen": // 抹分
		return amount
	case "round": // 四舍五入
		if amount%100 >= 50 { // 四舍五入到元
			return (amount/100 + 1) * 100
		}
		return amount / 100 * 100
	default:
		return amount
	}
}

func CheckRoomStatusOrderOpen(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_IDLE, _const.V2_ROOM_STATUS_GUEST}) {
		return fmt.Errorf("房间状态不为空闲或带客，不能开台")
	}
	return nil
}

func CheckRoomStatusOpenContinue(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_IN_USE}) {
		return fmt.Errorf("房间状态不为使用中，不能续台")
	}
	return nil
}

func CheckRoomStatusTransferRoom(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_IDLE, _const.V2_ROOM_STATUS_GUEST}) {
		return fmt.Errorf("房间状态不为空闲或带客，不能转台")
	}
	return nil
}

func CheckRoomStatusCloseRoom(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_IN_USE}) {
		return fmt.Errorf("房间状态不为使用中，不能关房")
	}
	return nil
}

func CheckRoomStatusCleanRoomFinish(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_CLEANING}) {
		return fmt.Errorf("房间状态不为清扫中，不能清扫完成")
	}
	return nil
}

func CheckRoomStatusGiftTime(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_GUEST, _const.V2_ROOM_STATUS_IN_USE}) {
		return fmt.Errorf("房间状态不为带客或使用中，不能赠送时长")
	}
	return nil
}

func CheckRoomStatusGiftProduct(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_GUEST, _const.V2_ROOM_STATUS_IN_USE}) {
		return fmt.Errorf("房间状态不为带客或使用中，不能赠送商品")
	}
	return nil
}

func CheckRoomStatusLockRoom(roomStatus string) error {
	return nil
}

func CheckRoomStatusUnlockRoom(roomStatus string) error {
	return nil
}

func CheckRoomStatusEndTimeConsume(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_IN_USE}) {
		return fmt.Errorf("房间状态不为使用中，不能结束开台计时消费")
	}
	return nil
}

func CheckRoomStatusReopen(roomStatus string) error {
	if !InList(roomStatus, []string{_const.V2_ROOM_STATUS_CLEANING}) {
		return fmt.Errorf("房间状态不为清扫中，不能重开")
	}
	return nil
}

// SplitList 将一个列表按照指定的大小分割成多个子列表
func SplitList[T any](list []T, size int) [][]T {
	if size <= 0 {
		return nil
	}

	var result [][]T
	for i := 0; i < len(list); i += size {
		end := i + size
		if end > len(list) {
			end = len(list)
		}
		result = append(result, list[i:end])
	}
	return result
}

func getMagicVenueNameByVenueId(venueId string) string {
	runes := []rune(venueId)
	if len(runes) >= 8 {
		// 前段取前4个字符，后段取后4个字符
		prefix := string(runes[:4])
		suffix := string(runes[len(runes)-4:])
		return fmt.Sprintf("%s***%s", prefix, suffix)
	} else {
		// 在字符的中间插入足够多的*，使长度达到11
		mid := len(runes) / 2
		starsNeeded := 11 - len(runes)
		stars := strings.Repeat("*", starsNeeded)
		return string(runes[:mid]) + stars + string(runes[mid:])
	}
}

func getMagicVenueNameByVenueName(venueName string) string {
	// 将字符串转换为 rune 切片，以便正确处理中文字符
	runes := []rune(venueName)
	if len(runes) <= 8 {
		return venueName
	}
	// 前段取前4个字符，后段取后4个字符
	prefix := string(runes[:4])
	suffix := string(runes[len(runes)-4:])
	return fmt.Sprintf("%s***%s", prefix, suffix)
}

func getMagicLastVenueName(venueName *string, venueId *string) string {
	lastVenueName := ""
	if venueName == nil || *venueName == "" {
		lastVenueName = getMagicVenueNameByVenueId(*venueId)
	} else {
		lastVenueName = getMagicVenueNameByVenueName(*venueName)
	}
	return lastVenueName
}

func GetPayProductName(venueName *string, venueId *string) *string {
	pname := fmt.Sprintf("开台点单 - %s", getMagicLastVenueName(venueName, venueId))
	return &pname
}

func ValidateMemberCardStatus(status string) error {
	if status == _const.V2_MEMBER_CARD_STATUS_LOST {
		return fmt.Errorf("会员卡状态已挂失")
	}
	if status == _const.V2_MEMBER_CARD_STATUS_FROZEN {
		return fmt.Errorf("会员卡状态已冻结")
	}
	if status == _const.V2_MEMBER_CARD_STATUS_EXPIRED {
		return fmt.Errorf("会员卡状态已过期")
	}
	if status == _const.V2_MEMBER_CARD_STATUS_CANCELLED {
		return fmt.Errorf("会员卡状态已注销")
	}
	return nil
}


// FenToYuanString 将分转换为元，并保留两位小数返回字符串
// amount: 金额（分）
// 返回: 格式化的金额字符串（元，保留两位小数）
func FenToYuanString(fenAmount int64) string {
	yuanAmount := float64(fenAmount) / 100.0
	return fmt.Sprintf("%.2f", yuanAmount)
}
