package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// 云片平台模板发送API URL
const yunpianTemplateURL = "https://sms.yunpian.com/v2/sms/tpl_single_send.json"

// 短信模板配置
const (
	// 雷石KTV验证码模板ID
	smsTemplateID = 5958488
	// 模板内容：【雷石KTV】您的验证码是#code#。如非本人操作，请忽略本短信
)

// SMSDebugInfo 短信调试信息结构
type SMSDebugInfo struct {
	Phone       string                 `json:"phone"`
	Code        string                 `json:"code"`
	TemplateID  int64                  `json:"template_id"`
	CallbackURL string                 `json:"callback_url,omitempty"`
	APIKey      string                 `json:"api_key_masked"`
	RequestURL  string                 `json:"request_url"`
	RequestBody string                 `json:"request_body"`
	Response    map[string]interface{} `json:"response"`
	StatusCode  int                    `json:"status_code"`
	Success     bool                   `json:"success"`
	Error       string                 `json:"error,omitempty"`
	Timestamp   int64                  `json:"timestamp"`
}

// getYunpianCallbackURL 从viper配置获取云片回调URL
func getYunpianCallbackURL() string {
	return viper.GetString("yunpian.callback_url")
}

// DebugSendSMSCode 调试版本的短信发送功能
func DebugSendSMSCode(phone string) (*SMSDebugInfo, error) {
	debugInfo := &SMSDebugInfo{
		Phone:      phone,
		TemplateID: smsTemplateID,
		Timestamp:  time.Now().Unix(),
	}

	// 检查API密钥
	apiKey := getYunpianAPIKey()
	if apiKey == "" {
		debugInfo.Success = false
		debugInfo.Error = "未配置云片API密钥"
		logrus.Errorf("调试信息: %+v", debugInfo)
		return debugInfo, fmt.Errorf("未配置云片API密钥")
	}

	// 掩码API密钥用于调试
	if len(apiKey) > 8 {
		debugInfo.APIKey = apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
	} else {
		debugInfo.APIKey = "****"
	}

	// 生成验证码
	debugInfo.Code = generateCode()

	// 准备模板变量，需要进行URL编码
	tplValue := url.QueryEscape("#code#") + "=" + url.QueryEscape(debugInfo.Code)

	// 准备请求数据
	form := url.Values{}
	form.Add("apikey", apiKey)
	form.Add("mobile", phone)
	form.Add("tpl_id", fmt.Sprintf("%d", smsTemplateID))
	form.Add("tpl_value", tplValue)

	// 添加回调URL（如果配置了）
	callbackURL := getYunpianCallbackURL()
	if callbackURL != "" {
		form.Add("callback_url", callbackURL)
		debugInfo.CallbackURL = callbackURL
		logrus.Infof("SMS调试 - 设置回调URL: %v", callbackURL)
	}

	debugInfo.RequestURL = yunpianTemplateURL
	debugInfo.RequestBody = form.Encode()

	logrus.Infof("SMS调试 - 开始发送模板短信: %+v", debugInfo)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", yunpianTemplateURL, bytes.NewBufferString(form.Encode()))
	if err != nil {
		debugInfo.Success = false
		debugInfo.Error = fmt.Sprintf("创建HTTP请求失败: %v", err)
		logrus.Errorf("SMS调试 - 请求创建失败: %+v", debugInfo)
		return debugInfo, err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded;charset=utf-8;")

	// 发送HTTP请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		debugInfo.Success = false
		debugInfo.Error = fmt.Sprintf("发送HTTP请求失败: %v", err)
		logrus.Errorf("SMS调试 - HTTP请求失败: %+v", debugInfo)
		return debugInfo, err
	}
	defer resp.Body.Close()

	debugInfo.StatusCode = resp.StatusCode

	// 读取响应数据
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		debugInfo.Success = false
		debugInfo.Error = fmt.Sprintf("读取响应数据失败: %v", err)
		logrus.Errorf("SMS调试 - 响应读取失败: %+v", debugInfo)
		return debugInfo, err
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		debugInfo.Success = false
		debugInfo.Error = fmt.Sprintf("解析响应JSON失败: %v, 原始响应: %s", err, string(body))
		logrus.Errorf("SMS调试 - JSON解析失败: %+v", debugInfo)
		return debugInfo, err
	}

	debugInfo.Response = result

	// 检查云片API响应
	if resultCode, ok := result["code"].(float64); ok {
		if resultCode != 0 {
			debugInfo.Success = false
			msg := "未知错误"
			if msgVal, exists := result["msg"]; exists {
				msg = fmt.Sprintf("%v", msgVal)
			}
			debugInfo.Error = fmt.Sprintf("云片API错误码: %v, 错误信息: %v", resultCode, msg)
			logrus.Errorf("SMS调试 - 云片API返回错误: %+v", debugInfo)
			return debugInfo, fmt.Errorf("发送失败: %v", msg)
		}
		debugInfo.Success = true
	} else {
		debugInfo.Success = false
		debugInfo.Error = "云片API响应格式异常，未找到code字段"
		logrus.Warnf("SMS调试 - 响应格式异常: %+v", debugInfo)
	}

	logrus.Infof("SMS调试 - 完成: %+v", debugInfo)
	return debugInfo, nil
}

// CheckYunpianAccount 检查云片账户状态
func CheckYunpianAccount() (*map[string]interface{}, error) {
	apiKey := getYunpianAPIKey()
	if apiKey == "" {
		return nil, fmt.Errorf("未配置云片API密钥")
	}

	// 云片账户查询API
	accountURL := "https://sms.yunpian.com/v2/user/get.json"

	form := url.Values{}
	form.Add("apikey", apiKey)

	req, err := http.NewRequest("POST", accountURL, bytes.NewBufferString(form.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建账户查询请求失败: %v", err)
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded;charset=utf-8;")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送账户查询请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取账户查询响应失败: %v", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析账户查询响应失败: %v", err)
	}

	logrus.Infof("云片账户信息: %+v", result)
	return &result, nil
}

// ValidatePhoneNumber 验证手机号码格式
func ValidatePhoneNumber(phone string) error {
	if len(phone) != 11 {
		return fmt.Errorf("手机号码长度不正确，应为11位，实际为%d位", len(phone))
	}

	if phone[0] != '1' {
		return fmt.Errorf("手机号码格式不正确，应以1开头")
	}

	// 检查是否为纯数字
	for i, char := range phone {
		if char < '0' || char > '9' {
			return fmt.Errorf("手机号码第%d位不是数字: %c", i+1, char)
		}
	}

	return nil
}
