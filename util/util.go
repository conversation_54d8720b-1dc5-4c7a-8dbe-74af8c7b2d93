package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"database/sql/driver"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"crypto/rand"

	mathrand "math/rand"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
)

// 对array[map] 按map中的key的值排序////////////////////
// 实现sort.Interface的Len和Less方法
type OrderArrayMap struct {
	OrderColumn string
	Data        []map[string]interface{}
}

func (p OrderArrayMap) Len() int {
	return len(p.Data)
}
func (p OrderArrayMap) Less(i, j int) bool {
	switch p.Data[i][p.OrderColumn].(type) {
	case string:
		return p.Data[i][p.OrderColumn].(string) < p.Data[j][p.OrderColumn].(string)
	case int:
		return p.Data[i][p.OrderColumn].(int) < p.Data[j][p.OrderColumn].(int)
	case float64:
		return p.Data[i][p.OrderColumn].(float64) < p.Data[j][p.OrderColumn].(float64)
	}
	panic("not support type")
}

// Swap是必须实现的，因为sort.Interface要求这样做
func (p OrderArrayMap) Swap(i, j int) {
	p.Data[i], p.Data[j] = p.Data[j], p.Data[i]
}

// GetReqID 获取请求中的 X-Request-Id
func GetReqID(ctx *gin.Context) string {
	return ctx.GetHeader("X-Request-Id")
}

// TimeToStr 将日期转换为字符串
func TimeToStr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// GetTag 获取结构体中的 tag
func GetTag(structed interface{}, fieldname string, tagname string) (string, bool) {
	t := reflect.TypeOf(structed)
	field, ok := t.FieldByName(fieldname)
	if !ok {
		return "", false
	}
	tag, ok := field.Tag.Lookup(tagname)
	return tag, ok
}

// 获取get post请求中的 requestID 并返回 logrus.Fields, 提供给logrus.WithFields使用
func Wlog(ctx *gin.Context) *logrus.Entry {
	requestID, _ := ctx.Get("X-Request-Id")
	var uid string
	var ktv_id string
	if ctx.Request == nil {
		return logrus.WithFields(logrus.Fields{})
	}
	if ctx.Request.Method == "GET" {
		uid = ctx.DefaultQuery("unionid", "")
		ktv_id = ctx.DefaultQuery("_ktvId", "")
	} else {
		uid = ctx.DefaultPostForm("unionid", "")
		ktv_id = ctx.DefaultPostForm("_ktvId", "")
	}
	if ktv_id == "" {
		c_ktvId, exit := ctx.Get("c_ktvId")
		if exit {
			ktv_id = c_ktvId.(string)
		}
	}
	log_fields := logrus.Fields{
		"url":       ctx.Request.URL.Path,
		"param":     ctx.Request.URL.RawQuery,
		"requestID": requestID,
		"uid":       uid,
		"ktv_id":    ktv_id,
	}
	return logrus.WithFields(log_fields)
}

func GetWithFields(ctx *gin.Context) logrus.Fields {
	requestID, _ := ctx.Get("X-Request-Id")
	var uid string
	var ktv_id string
	if ctx.Request.Method == "GET" {
		uid = ctx.DefaultQuery("unionid", "")
		ktv_id = ctx.DefaultQuery("_ktvId", "")
	} else {
		uid = ctx.DefaultPostForm("unionid", "")
		ktv_id = ctx.DefaultPostForm("_ktvId", "")
	}
	if ktv_id == "" {
		c_ktvId, exit := ctx.Get("c_ktvId")
		if exit {
			ktv_id = c_ktvId.(string)
		}
	}
	return logrus.Fields{
		"requestID": requestID,
		"uid":       uid,
		"ktv_id":    ktv_id,
	}
}

func SliceRemove(slice []string, elem string) []string {
	if len(slice) == 0 {
		return slice
	}
	for i, v := range slice {
		if v == elem {
			slice = append(slice[:i], slice[i+1:]...)
			return SliceRemove(slice, elem)
		}
	}
	return slice
}

func GetUUID() string {
	s := uuid.New().String()
	return strings.ReplaceAll(s, "-", "")
}

// 利用map key的唯一性去重
func RemoveDuplicateElement(languages []string) []string {
	result := make([]string, 0, len(languages))
	temp := map[string]struct{}{}
	for _, item := range languages {
		if _, ok := temp[item]; !ok { //如果字典中找不到元素，ok=false，!ok为true，就往切片中append元素。
			temp[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

// 截取字符串
func TruncateString(input string, maxLength int) string {
	// 将字符串转换为 rune 数组，以便正确处理中文字符
	runes := []rune(input)

	// 如果字符串长度小于等于 maxLength，直接返回原字符串
	if len(runes) <= maxLength {
		return input
	}

	// 截取前 maxLength 个 rune，并将其转换回字符串
	truncated := string(runes[:maxLength])

	return truncated
}

func GetUrlFileMd5(picPath string) (string, error) {
	// 计算文件md5
	resp, err := http.Get(picPath)
	if err != nil || resp.StatusCode != 200 {
		return "下载文件错误", err
	}
	defer resp.Body.Close()

	hash := md5.New()
	_, err = io.Copy(hash, resp.Body)
	if err != nil {
		return "copy数据error", err
	}
	// 获取哈希值的字节数组
	hashInBytes := hash.Sum(nil)
	// 将字节数组转换为十六进制字符串
	fmd5 := hex.EncodeToString(hashInBytes)
	return fmd5, nil
}

func GenNonce_str() string {
	length := 20
	// 初始化源生随机数生成器
	mathrand.Seed(time.Now().UnixNano())

	// 字符集，可以根据需要进行扩展
	charset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[mathrand.Intn(len(charset))]
	}
	return string(b)
}

func String2IntOrZero(s string) int {
	return String2Int(s, 0)
}

func String2Int(s string, defaultVal int) int {
	sInt, err := strconv.Atoi(s)
	if err != nil {
		return defaultVal
	}
	return sInt
}

func InList[T comparable](key T, arr []T) bool {
	for _, v := range arr {
		if key == v {
			return true
		}
	}
	return false
}

// 添加元素到列表中，如果元素已存在则不添加
func AddListElement[T comparable](list *[]T, elem T) {
	for _, v := range *list {
		if v == elem {
			return
		}
	}
	*list = append(*list, elem)
}

// 删除列表中的元素
func RemoveListElement1[T comparable](list *[]T, elem T) {
	for i, v := range *list {
		if v == elem {
			*list = append((*list)[:i], (*list)[i+1:]...)
			return
		}
	}
}

func RemoveListElement(list []string, elem string) []string {
	for i, v := range list {
		if v == elem {
			return append(list[:i], list[i+1:]...)
		}
	}
	return list
}

func PrintJsonWellDebug(head string, PostData interface{}, flag bool) {
	if !flag {
		return
	}
	// 调试专用打印
	if head == "" {
		head = "xxxxxxxxxxxxxxx"
	}
	// jsondata, _ := json.MarshalIndent(PostData, "", "    ")

	// 使用 json.Encoder 避免转义 Unicode 字符
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetIndent("", "    ")
	encoder.SetEscapeHTML(false)

	// fmt.Println("\nAvoid escaping Unicode characters:")
	err := encoder.Encode(PostData)
	if err != nil {
		fmt.Println("Error encoding JSON:", err)
		return
	}
	fmt.Println(head, buffer.String())
}

func GetPrettyJson(PostData interface{}) string {
	// 使用 json.Encoder 避免转义 Unicode 字符
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetIndent("", "    ")
	encoder.SetEscapeHTML(false)

	// fmt.Println("\nAvoid escaping Unicode characters:")
	err := encoder.Encode(PostData)
	if err != nil {
		fmt.Println("Error encoding JSON:", err)
		return ""
	}
	return buffer.String()
}

func PrettyJson(PostData interface{}) string {
	if PostData == nil {
		return "[null]"
	}
	// 使用 json.Encoder 避免转义 Unicode 字符
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetIndent("", "    ")
	encoder.SetEscapeHTML(false)

	// fmt.Println("\nAvoid escaping Unicode characters:")
	err := encoder.Encode(PostData)
	if err != nil {
		fmt.Println("Error encoding JSON:", err)
		return ""
	}
	return buffer.String()
}
func TimeParseV2(dt, format string) (time.Time, error) {
	if format == "" {
		format = "2006-01-02 15:04:05"
	}
	cstZone := time.FixedZone("CST", 8*3600)
	timeDate, err := time.ParseInLocation(format, dt, cstZone)
	return timeDate, err
}
func ParseDate(dt string) (time.Time, error) {
	return TimeParseV2(dt, "2006-01-02")
}
func ParseDatetime(dt string) (time.Time, error) {
	return TimeParseV2(dt, "2006-01-02 15:04:05")
}
func ParseDatetimeRFC3339(dt string) (time.Time, error) {
	return TimeParseV2(dt, "2006-01-02T15:04:05+08:00")
}
func ParseRFC3339toYMDHMS(dt string) (string, error) {
	tm, err := ParseDatetimeRFC3339(dt)
	if err != nil {
		return dt, err
	}
	return tm.Format("2006-01-02 15:04:05"), nil
}
func ParseRFC3339toYMD(dt string) (string, error) {
	tm, err := ParseDatetimeRFC3339(dt)
	if err != nil {
		return dt, err
	}
	return tm.Format("2006-01-02"), nil
}

func TimeNow() time.Time {
	cstZone := time.FixedZone("CST", 8*3600)
	timeDate := time.Now().In(cstZone)
	return timeDate
}

func TimeNowUnix() int {
	return int(time.Now().Unix())
}

func TimeNowUnixInt64() int64 {
	return time.Now().Unix()
}

func GetYmdHms(unix int64) string {
	cstZone := time.FixedZone("CST", 8*3600)
	return time.Unix(unix, 0).In(cstZone).Format("2006-01-02 15:04:05")
}
func GetYmd(unix int64) string {
	cstZone := time.FixedZone("CST", 8*3600)
	return time.Unix(unix, 0).In(cstZone).Format("2006-01-02")
}
func GetDateTimeStr() string {
	return TimeNow().Format("2006-01-02 15:04:05")
}

func GetToday0An24Time() (int64, int64) {
	now := TimeNow()
	t0 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	t24 := t0.AddDate(0, 0, 1)
	return t0.Unix(), t24.Unix() - 1
}

// 去掉重复的项
func RemoveDuplicates(input []string) []string {
	// 使用 map 来记录已经出现过的字符串
	seen := make(map[string]bool)
	result := []string{}

	for _, str := range input {
		if _, value := seen[str]; !value {
			// 如果字符串未出现过，则添加到结果切片中
			seen[str] = true
			result = append(result, str)
		}
	}

	return result
}

type TTime struct {
	time.Time
}

const customFormatTTime = "2006-01-02 15:04:05"

// json.Marshal 的序列化方法
func (ct TTime) MarshalJSON() ([]byte, error) {
	if ct.IsZero() {
		return []byte("null"), nil
	}
	return []byte(`"` + ct.Time.Format(customFormatTTime) + `"`), nil
}

// json.Unmarshal 的反序列化方法
func (ct *TTime) UnmarshalJSON(b []byte) error {
	if string(b) == "null" {
		return nil
	}
	t, err := time.Parse(`"`+customFormatTTime+`"`, string(b))
	if err != nil {
		return err
	}
	ct.Time = t
	return nil
}

// Value 实现 driver.Valuer 接口
func (ct TTime) Value() (driver.Value, error) {
	if ct.IsZero() {
		return nil, nil
	}
	return ct.Time.Format(customFormatTTime), nil
}

// Scan 实现 sql.Scanner 接口
func (ct *TTime) Scan(value interface{}) error {
	if value == nil {
		ct.Time = time.Time{}
		return nil
	}
	switch v := value.(type) {
	case time.Time:
		ct.Time = v
		return nil
	case string:
		t, err := time.Parse(customFormatTTime, v)
		if err != nil {
			return err
		}
		ct.Time = t
		return nil
	default:
		return errors.New("invalid type for CustomTime")
	}
}

func Ptr[T any](v T) *T {
	return &v
}

func PtrSafe[T any](v *T) *T {
	if v == nil {
		return nil
	}
	return v
}

func GetItPtr[T any](v T) *T {
	return &v
}

func GetBoolPtr(v bool) *bool {
	return &v
}

func GetPtrSafe[T any](v *T) T {
	if v == nil {
		return *new(T)
	}
	return *v
}

func GetPtrSafeDefault[T any](v *T, defaultVal T) T {
	if v == nil {
		return defaultVal
	}
	return *v
}

func ParseInt(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return i
}

func MinNumber[T int | int32 | int64 | float64 | float32](a, b T) T {
	if a < b {
		return a
	}
	return b
}

func MaxNumber[T int | int32 | int64 | float64 | float32](a, b T) T {
	if a > b {
		return a
	}
	return b
}

func Between[T int | int32 | int64 | float64 | float32](main, min, max T) bool {
	realMin := MinNumber(min, max)
	realMax := MaxNumber(min, max)
	if main >= realMin && main <= realMax {
		return true
	}
	return false
}

const AES_KEY = "thunder5166thunder5166"

func TokenLoginEncrypt(text string) string {
	if text == "" {
		return ""
	}

	// 1. 准备16字节密钥
	key := []byte(AES_KEY)
	if len(key) > 16 {
		key = key[:16]
	}

	// 2. 创建 cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err) // 在开发时使用 panic 以便及早发现问题
	}

	// 3. 准备数据和填充
	textBytes := []byte(text)
	padding := aes.BlockSize - len(textBytes)%aes.BlockSize
	padText := make([]byte, len(textBytes)+padding)
	copy(padText, textBytes)
	for i := len(textBytes); i < len(padText); i++ {
		padText[i] = byte(padding)
	}

	// 4. 准备 IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	// 5. 加密
	encrypted := make([]byte, len(iv)+len(padText))
	copy(encrypted, iv)
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(encrypted[aes.BlockSize:], padText)

	return base64.StdEncoding.EncodeToString(encrypted)
}

func TokenLoginDecrypt(encrypted string) string {
	if encrypted == "" {
		return ""
	}

	// 1. Base64 解码
	ciphertext, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		panic(err)
	}

	// 2. 检查长度
	if len(ciphertext) < aes.BlockSize {
		panic("ciphertext too short")
	}

	// 3. 准备密钥
	key := []byte(AES_KEY)
	if len(key) > 16 {
		key = key[:16]
	}

	// 4. 创建 cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	// 5. 提取 IV
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]

	// 6. 解密
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertext, ciphertext)

	// 7. 去除填充
	if len(ciphertext) == 0 {
		panic("ciphertext is empty")
	}

	padding := int(ciphertext[len(ciphertext)-1])
	if padding == 0 || padding > len(ciphertext) {
		panic("invalid padding")
	}

	// 验证填充
	for i := len(ciphertext) - padding; i < len(ciphertext); i++ {
		if int(ciphertext[i]) != padding {
			panic("invalid padding")
		}
	}

	return string(ciphertext[:len(ciphertext)-padding])
}

func DeepCopy[T any](t T) T {
	copyT := new(T)
	copier.Copy(&copyT, &t)
	return *copyT
}

func DeepClone[T any](t T) T {
	return DeepCopy(t)
}

// GenerateRandomKey 生成随机字符串
func GenerateRandomKey() string {
	b := make([]byte, 16)
	rand.Read(b)
	return hex.EncodeToString(b)
}

// GenerateShortKey 生成短随机字符串 (8字符)
func GenerateShortKey() string {
	b := make([]byte, 4) // 4字节会生成8个十六进制字符
	rand.Read(b)
	return hex.EncodeToString(b)
}

// GenRandomString 生成指定长度的随机字符串
func GenRandomString(length int) string {
	charset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[mathrand.Intn(len(charset))]
	}
	return string(b)
}

// BCryptHash 对明文密码进行 bcrypt 加密，返回加密后的字符串和可能的错误
func BCryptHash(password string) (string, error) {
	// 使用 bcrypt 默认的难度生成哈希值
	hashBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashBytes), nil
}

// BCryptCheck 检查明文密码与 bcrypt 加密后的哈希是否匹配，匹配返回 true，否则返回 false
func BCryptCheck(password string, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// 用于生成62进制字符串的字符集
var digits = []byte("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

// ToBase62 将数字转换为62进制字符串
func ToBase62(num int64) string {
	var bytes []byte
	for num > 0 {
		bytes = append(bytes, digits[num%62])
		num = num / 62
	}

	// 反转字符串
	for i, j := 0, len(bytes)-1; i < j; i, j = i+1, j-1 {
		bytes[i], bytes[j] = bytes[j], bytes[i]
	}

	return string(bytes)
}

// GetShortSnowflakeID 生成62进制的短ID
func GetShortSnowflakeID() string {
	id := GetSnowflakeInstance().NextId()
	return ToBase62(id)
}

// StructToMap 将struct转换为map[string]interface{}
func StructToMap(obj interface{}) map[string]interface{} {
	// 创建返回的map容器
	out := make(map[string]interface{})

	// 获取反射值对象
	v := reflect.ValueOf(obj)

	// 处理指针类型：如果是指针则获取其指向的值
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	// 如果是切片类型，返回空map
	if v.Kind() == reflect.Slice {
		return out
	}

	// 获取反射类型信息
	t := v.Type()

	// 遍历结构体所有字段
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i) // 获取字段类型信息
		val := v.Field(i)   // 获取字段值信息

		// 跳过不可导出字段（小写字母开头的字段）
		if field.PkgPath != "" {
			continue
		}

		// 处理json标签：获取第一个逗号前的内容，处理忽略字段的情况
		tag := field.Tag.Get("json")
		fieldName := strings.Split(tag, ",")[0]
		if fieldName == "-" { // 忽略标记为-的字段
			continue
		}
		if fieldName == "" { // 没有json标签时使用字段原名
			fieldName = field.Name
		}

		// 处理嵌套结构体（非指针类型）
		if val.Kind() == reflect.Struct {
			out[fieldName] = StructToMap(val.Interface()) // 递归处理结构体
			continue
		}

		// 处理指针类型的嵌套结构体
		if val.Kind() == reflect.Ptr && val.Elem().Kind() == reflect.Struct {
			if !val.IsNil() {
				out[fieldName] = StructToMap(val.Elem().Interface()) // 递归处理非空指针
			} else {
				out[fieldName] = nil // 空指针设为nil
			}
			continue
		}

		// 处理普通指针类型字段（非结构体指针）
		if val.Kind() == reflect.Ptr {
			if val.IsNil() {
				out[fieldName] = nil // 空指针存储为nil
			} else {
				out[fieldName] = val.Elem().Interface() // 解引用指针获取实际值
			}
		} else {
			// 直接存储非指针字段的值
			out[fieldName] = val.Interface()
		}
	}

	return out
}

// StructsToMaps 将struct切片转换为map切片
func StructsToMaps(slice interface{}) []map[string]interface{} {
	// 获取反射值对象
	v := reflect.ValueOf(slice)

	// 处理指针类型：如果是指针则获取其指向的值
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	// 如果不是切片类型，返回空切片
	if v.Kind() != reflect.Slice {
		return []map[string]interface{}{}
	}

	// 创建返回的切片
	result := make([]map[string]interface{}, v.Len())

	// 遍历切片中的每个元素
	for i := 0; i < v.Len(); i++ {
		// 获取切片中的元素
		elem := v.Index(i).Interface()
		// 将每个元素转换为map
		result[i] = StructToMap(elem)
	}

	return result
}

// ChunkSlice 将切片分割成指定大小的块, 支持泛型
func ChunkSlice[T any](slice []T, maxChunkSize int) [][]T {
	if maxChunkSize <= 0 {
		return [][]T{slice} // maxChunkSize 无效时，直接返回原切片
	}
	if len(slice) <= maxChunkSize {
		return [][]T{slice} // 切片长度小于等于 maxChunkSize，直接返回原切片
	}

	numChunks := (len(slice) + maxChunkSize - 1) / maxChunkSize
	baseSize := len(slice) / numChunks
	remainder := len(slice) % numChunks
	chunks := make([][]T, 0, numChunks)
	startIndex := 0

	for i := 0; i < numChunks; i++ {
		chunkSize := baseSize
		if i < remainder {
			chunkSize++
		}
		endIndex := startIndex + chunkSize
		chunks = append(chunks, slice[startIndex:endIndex])
		startIndex = endIndex
	}
	return chunks
}

func BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

func FeeDiff(fee1, fee2 int64) (diff int64, msg string, err error) {
	diff = fee1 - fee2
	if diff < 0 {
		diff = -diff
	}
	if diff == 0 {
		return 0, "金额相等", nil
	}
	msg0 := fmt.Sprintf("fee1: %d, fee2: %d, diff: %d", fee1, fee2, diff)
	if diff > 100 {
		return 0, "金额差距大于1角, " + msg0, errors.New(msg0)
	}

	return diff, "金额差距小于1角, " + msg0, nil
}

func CalculateBillDate(finishTime *int64, startHour string) string {
	// 如果参数无效，返回空字符串
	if finishTime == nil || *finishTime <= 0 || startHour == "" {
		return ""
	}

	// 解析营业开始时间（格式如 "06:00" 或 "6:00"）
	startHourParts := strings.Split(startHour, ":")
	if len(startHourParts) != 2 {
		return ""
	}

	startHourInt, err := strconv.Atoi(startHourParts[0])
	if err != nil {
		return ""
	}
	startMinuteInt, err := strconv.Atoi(startHourParts[1])
	if err != nil {
		return ""
	}

	// 验证时间有效性
	if startHourInt < 0 || startHourInt > 23 || startMinuteInt < 0 || startMinuteInt > 59 {
		return ""
	}

	// 将时间戳转换为时间对象（使用CST时区）
	cstZone := time.FixedZone("CST", 8*3600)
	finishTimeObj := time.Unix(*finishTime, 0).In(cstZone)

	// 获取完成时间的日期部分
	finishDate := finishTimeObj.Format("2006-01-02")

	// 构造当天的营业开始时间
	businessStartTime, err := time.ParseInLocation("2006-01-02 15:04:05", fmt.Sprintf("%s %02d:%02d:00", finishDate, startHourInt, startMinuteInt), cstZone)
	if err != nil {
		return ""
	}

	// 如果完成时间在营业开始时间之前，说明是跨天营业，账单归属于前一天
	if finishTimeObj.Before(businessStartTime) {
		// 账单日期为前一天
		billDate := finishTimeObj.AddDate(0, 0, -1)
		return billDate.Format("2006-01-02")
	}

	// 否则账单归属于当天
	return finishDate
}

/**
* 判断当前时间是否在指定时间范围内
* @param currentTime 当前时间
* @param startHour 开始时间: 06:10
* @param endHour 结束时间: 10:20
* @return 是否在范围内
 */
func IsInTimeRange(currentTime int64, startHour, endHour string) bool {
	// 验证参数
	if currentTime <= 0 || startHour == "" || endHour == "" {
		return false
	}

	// 解析开始时间
	startHourParts := strings.Split(startHour, ":")
	if len(startHourParts) != 2 {
		return false
	}
	startHourInt, err := strconv.Atoi(startHourParts[0])
	if err != nil {
		return false
	}
	startMinuteInt, err := strconv.Atoi(startHourParts[1])
	if err != nil {
		return false
	}

	// 解析结束时间
	endHourParts := strings.Split(endHour, ":")
	if len(endHourParts) != 2 {
		return false
	}
	endHourInt, err := strconv.Atoi(endHourParts[0])
	if err != nil {
		return false
	}
	endMinuteInt, err := strconv.Atoi(endHourParts[1])
	if err != nil {
		return false
	}

	// 验证时间有效性
	if startHourInt < 0 || startHourInt > 23 || startMinuteInt < 0 || startMinuteInt > 59 ||
		endHourInt < 0 || endHourInt > 23 || endMinuteInt < 0 || endMinuteInt > 59 {
		return false
	}

	// 将时间戳转换为时间对象（使用CST时区）
	cstZone := time.FixedZone("CST", 8*3600)
	currentTimeObj := time.Unix(currentTime, 0).In(cstZone)

	// 获取当前时间的小时和分钟
	currentHour := currentTimeObj.Hour()
	currentMinute := currentTimeObj.Minute()

	// 将时间转换为分钟数便于比较
	currentTotalMinutes := currentHour*60 + currentMinute
	startTotalMinutes := startHourInt*60 + startMinuteInt
	endTotalMinutes := endHourInt*60 + endMinuteInt

	// 处理跨天的情况（如22:00到06:00）
	if startTotalMinutes > endTotalMinutes {
		// 跨天情况：当前时间在开始时间之后或结束时间之前
		return currentTotalMinutes >= startTotalMinutes || currentTotalMinutes <= endTotalMinutes
	} else {
		// 同一天情况：当前时间在开始和结束时间之间
		return currentTotalMinutes >= startTotalMinutes && currentTotalMinutes <= endTotalMinutes
	}
}
