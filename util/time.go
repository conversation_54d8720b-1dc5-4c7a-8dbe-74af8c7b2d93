package util

import (
	"errors"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// GetBusinessTimeRange 获取营业时间范围
func GetBusinessTimeRange(now time.Time, startHours string, endHours string) (int64, int64) {
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 解析营业时间
	startParts := strings.Split(startHours, ":")
	endParts := strings.Split(endHours, ":")

	startHour, _ := strconv.Atoi(startParts[0])
	startMin, _ := strconv.Atoi(startParts[1])
	endHour, _ := strconv.Atoi(endParts[0])
	endMin, _ := strconv.Atoi(endParts[1])

	// 计算开始和结束时间戳
	startTime := today.Add(time.Duration(startHour)*time.Hour + time.Duration(startMin)*time.Minute)
	endTime := today.Add(time.Duration(endHour)*time.Hour + time.Duration(endMin)*time.Minute)

	// 如果结束时间小于开始时间,说明跨天,需要加一天
	if endTime.Before(startTime) {
		endTime = endTime.Add(24 * time.Hour)
	}

	return startTime.Unix(), endTime.Unix()
}

// GetCurrentBusinessTimeRange 根据当前时间计算当前时间点所处的营业时间范围
//
// 参数：
//
//	startHour: 营业开始时间，格式为 "HH:mm"，例如 "06:00"
//
// 返回值：
//
//	startTime: 当前营业时间段的开始时间戳（Unix 10位）
//	endTime: 当前营业时间段的结束时间戳（Unix 10位）
//	error: 错误信息
//
// 功能说明：
//   - 营业时间为24小时制，从指定时间开始到第二天同一时间结束
//   - 例如：营业时间为 "06:00"，则一个营业日为 06:00~次日06:00
//   - 根据当前时间判断属于哪个营业日：
//   - 如果当前时间早于今天的营业开始时间，则属于昨天开始的营业时间段
//   - 如果当前时间晚于或等于今天的营业开始时间，则属于今天开始的营业时间段
//   - 例如：当前时间03:00，营业时间06:00~06:00，则当前营业时间段为昨天06:00~今天06:00
//
// 使用示例：
//
//	startTime, endTime, err := GetCurrentBusinessTimeRange("06:00")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("当前营业时间段：%s 到 %s",
//	          time.Unix(startTime, 0).Format("2006-01-02 15:04:05"),
//	          time.Unix(endTime, 0).Format("2006-01-02 15:04:05"))
func GetCurrentBusinessTimeRange(startHour string) (int64, int64, error) {
	now := time.Now()

	// 校验时间格式 HH:mm
	timeRegex := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	if !timeRegex.MatchString(startHour) {
		return 0, 0, errors.New("startHour格式错误，应为HH:mm")
	}

	// 解析营业开始时间
	startParts := strings.Split(startHour, ":")
	startHourInt, _ := strconv.Atoi(startParts[0])
	startMinInt, _ := strconv.Atoi(startParts[1])

	// 计算今天的营业开始时间
	todayBusinessStart := time.Date(now.Year(), now.Month(), now.Day(), startHourInt, startMinInt, 0, 0, now.Location())

	// 判断当前时间属于哪个营业时间段
	if now.Before(todayBusinessStart) {
		// 当前时间早于今天的营业开始时间，属于昨天开始的营业时间段
		// 营业时间段：昨天startHour ~ 今天startHour
		yesterdayBusinessStart := todayBusinessStart.Add(-24 * time.Hour)
		return yesterdayBusinessStart.Unix(), todayBusinessStart.Unix(), nil
	} else {
		// 当前时间晚于或等于今天的营业开始时间，属于今天开始的营业时间段
		// 营业时间段：今天startHour ~ 明天startHour
		tomorrowBusinessStart := todayBusinessStart.Add(24 * time.Hour)
		return todayBusinessStart.Unix(), tomorrowBusinessStart.Unix(), nil
	}
}
