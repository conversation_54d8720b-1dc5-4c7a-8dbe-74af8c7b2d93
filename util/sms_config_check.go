package util

import (
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// SMSConfigStatus SMS配置状态
type SMSConfigStatus struct {
	APIKeyConfigured bool   `json:"api_key_configured"`
	APIKeyMasked     string `json:"api_key_masked"`
	ConfigSource     string `json:"config_source"`
	ConfigValid      bool   `json:"config_valid"`
	Issues           []string `json:"issues,omitempty"`
}

// CheckSMSConfig 检查SMS配置状态
func CheckSMSConfig() *SMSConfigStatus {
	status := &SMSConfigStatus{
		Issues: make([]string, 0),
	}

	// 检查API密钥配置
	apiKey := viper.GetString("yunpian.api_key")
	if apiKey == "" {
		status.APIKeyConfigured = false
		status.Issues = append(status.Issues, "云片API密钥未配置")
		logrus.Warnf("SMS配置检查: 云片API密钥未配置")
	} else {
		status.APIKeyConfigured = true
		// 掩码显示API密钥
		if len(apiKey) > 8 {
			status.APIKeyMasked = apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
		} else {
			status.APIKeyMasked = "****"
		}
		logrus.Infof("SMS配置检查: 云片API密钥已配置 - %s", status.APIKeyMasked)
	}

	// 检查配置来源
	configFile := viper.ConfigFileUsed()
	if configFile != "" {
		status.ConfigSource = configFile
		logrus.Infof("SMS配置检查: 配置文件来源 - %s", configFile)
	} else {
		status.ConfigSource = "未知"
		status.Issues = append(status.Issues, "无法确定配置文件来源")
		logrus.Warnf("SMS配置检查: 无法确定配置文件来源")
	}

	// 验证API密钥格式
	if status.APIKeyConfigured {
		if len(apiKey) < 10 {
			status.Issues = append(status.Issues, "API密钥长度可能不正确")
			logrus.Warnf("SMS配置检查: API密钥长度可能不正确，长度为%d", len(apiKey))
		}
		
		if strings.Contains(apiKey, " ") {
			status.Issues = append(status.Issues, "API密钥包含空格")
			logrus.Warnf("SMS配置检查: API密钥包含空格")
		}
	}

	// 总体配置有效性
	status.ConfigValid = status.APIKeyConfigured && len(status.Issues) == 0

	logrus.Infof("SMS配置检查完成: 配置有效=%v, 问题数量=%d", status.ConfigValid, len(status.Issues))
	return status
}

// PrintSMSConfigStatus 打印SMS配置状态
func PrintSMSConfigStatus() {
	status := CheckSMSConfig()
	
	fmt.Println("=== SMS配置状态检查 ===")
	fmt.Printf("API密钥已配置: %v\n", status.APIKeyConfigured)
	if status.APIKeyConfigured {
		fmt.Printf("API密钥(掩码): %s\n", status.APIKeyMasked)
	}
	fmt.Printf("配置文件来源: %s\n", status.ConfigSource)
	fmt.Printf("配置有效: %v\n", status.ConfigValid)
	
	if len(status.Issues) > 0 {
		fmt.Println("发现的问题:")
		for i, issue := range status.Issues {
			fmt.Printf("  %d. %s\n", i+1, issue)
		}
	}
	
	fmt.Println("========================")
}

// GetAllSMSRelatedConfig 获取所有SMS相关配置
func GetAllSMSRelatedConfig() map[string]interface{} {
	config := make(map[string]interface{})
	
	// 云片相关配置
	config["yunpian.api_key"] = maskString(viper.GetString("yunpian.api_key"))
	
	// 其他可能的SMS相关配置
	allKeys := viper.AllKeys()
	for _, key := range allKeys {
		if strings.Contains(strings.ToLower(key), "sms") || 
		   strings.Contains(strings.ToLower(key), "yunpian") ||
		   strings.Contains(strings.ToLower(key), "message") {
			value := viper.Get(key)
			// 如果是密钥类型的配置，进行掩码处理
			if strings.Contains(strings.ToLower(key), "key") || 
			   strings.Contains(strings.ToLower(key), "secret") ||
			   strings.Contains(strings.ToLower(key), "token") {
				if strValue, ok := value.(string); ok {
					config[key] = maskString(strValue)
				} else {
					config[key] = value
				}
			} else {
				config[key] = value
			}
		}
	}
	
	return config
}

// maskString 掩码字符串
func maskString(s string) string {
	if s == "" {
		return ""
	}
	if len(s) <= 8 {
		return "****"
	}
	return s[:4] + "****" + s[len(s)-4:]
}
