package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// 云片平台API URL
const yunpianURL = "https://sms.yunpian.com/v1/sms/send.json"

// getYunpianAPIKey 从viper配置获取云片API密钥
func getYunpianAPIKey() string {
	return viper.GetString("yunpian.api_key")
}

// generateCode 生成4位数字验证码
func generateCode() string {
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%04d", rand.Intn(10000))
}

// SendSMSCode 发送短信验证码
func SendSMSCode(phone string) error {
	apiKey := getYunpianAPIKey()
	if apiKey == "" {
		return fmt.Errorf("未配置云片API密钥")
	}

	code := generateCode()
	txt := fmt.Sprintf("【汇金商户通】您的验证码是%v。如非本人操作，请忽略本短信。", code)

	// 准备请求数据
	form := url.Values{}
	form.Add("apikey", apiKey)
	form.Add("mobile", phone)
	form.Add("text", txt)

	logrus.Infof("发送短信验证码: phone=%v code=%v", phone, code)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", yunpianURL, bytes.NewBufferString(form.Encode()))
	if err != nil {
		logrus.Errorf("创建HTTP请求失败: %v", err)
		return err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded;charset=utf-8;")

	// 发送HTTP请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		logrus.Errorf("发送HTTP请求失败: %v", err)
		return err
	}
	defer resp.Body.Close()

	// 读取响应数据
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("读取响应数据失败: %v", err)
		return err
	}

	// 检查响应状态
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		logrus.Errorf("解析响应JSON失败: %v", err)
		return err
	}

	logrus.Infof("短信发送响应: %v", result)

	if code, ok := result["code"].(float64); ok && code != 0 {
		logrus.Errorf("短信发送失败: %v", result["msg"])
		return fmt.Errorf("发送失败: %v", result["msg"])
	}

	logrus.Infof("短信发送成功: phone=%v", phone)
	return nil
}

// SendSMSCodeWithTestMode 发送短信验证码（带测试模式）
func SendSMSCodeWithTestMode(phone string, isTestMode bool) (string, error) {
	var code string

	if isTestMode {
		code = "1234"
		logrus.Infof("测试模式: phone=%v code=%v", phone, code)
		return code, nil
	}

	apiKey := getYunpianAPIKey()
	if apiKey == "" {
		logrus.Errorf("云片API密钥未配置")
		return "", fmt.Errorf("未配置云片API密钥")
	}

	// 调试信息：记录API密钥前几位（安全考虑）
	maskedKey := ""
	if len(apiKey) > 8 {
		maskedKey = apiKey[:4] + "****" + apiKey[len(apiKey)-4:]
	} else {
		maskedKey = "****"
	}
	logrus.Infof("使用云片API密钥: %v", maskedKey)

	code = generateCode()
	txt := fmt.Sprintf("【汇金商户通】您的验证码是%v。如非本人操作，请忽略本短信。", code)

	// 准备请求数据
	form := url.Values{}
	form.Add("apikey", apiKey)
	form.Add("mobile", phone)
	form.Add("text", txt)

	logrus.Infof("发送短信验证码开始: phone=%v code=%v text=%v", phone, code, txt)
	logrus.Infof("请求URL: %v", yunpianURL)
	logrus.Infof("请求参数: mobile=%v, text长度=%v", phone, len(txt))

	// 创建HTTP请求
	req, err := http.NewRequest("POST", yunpianURL, bytes.NewBufferString(form.Encode()))
	if err != nil {
		logrus.Errorf("创建HTTP请求失败: %v", err)
		return "", err
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded;charset=utf-8;")

	// 发送HTTP请求
	client := &http.Client{Timeout: 30 * time.Second}
	logrus.Infof("开始发送HTTP请求到云片API...")
	resp, err := client.Do(req)
	if err != nil {
		logrus.Errorf("发送HTTP请求失败: %v", err)
		return "", err
	}
	defer resp.Body.Close()

	logrus.Infof("收到云片API响应，状态码: %v", resp.StatusCode)

	// 读取响应数据
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("读取响应数据失败: %v", err)
		return "", err
	}

	logrus.Infof("云片API原始响应: %s", string(body))

	// 检查响应状态
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		logrus.Errorf("解析响应JSON失败: %v, 原始响应: %s", err, string(body))
		return "", err
	}

	logrus.Infof("云片API解析后响应: %+v", result)

	// 详细检查响应字段
	if resultCode, ok := result["code"].(float64); ok {
		logrus.Infof("云片API返回码: %v", resultCode)
		if resultCode != 0 {
			msg := "未知错误"
			if msgVal, exists := result["msg"]; exists {
				msg = fmt.Sprintf("%v", msgVal)
			}
			logrus.Errorf("短信发送失败 - 错误码: %v, 错误信息: %v", resultCode, msg)

			// 记录更多调试信息
			if detail, exists := result["detail"]; exists {
				logrus.Errorf("错误详情: %v", detail)
			}
			if count, exists := result["count"]; exists {
				logrus.Infof("发送数量: %v", count)
			}
			if fee, exists := result["fee"]; exists {
				logrus.Infof("消费金额: %v", fee)
			}
			if unit, exists := result["unit"]; exists {
				logrus.Infof("计费单位: %v", unit)
			}
			if mobile, exists := result["mobile"]; exists {
				logrus.Infof("目标手机号: %v", mobile)
			}
			if sid, exists := result["sid"]; exists {
				logrus.Infof("短信ID: %v", sid)
			}

			return "", fmt.Errorf("发送失败: %v", msg)
		}
	} else {
		logrus.Warnf("云片API响应中未找到code字段或格式异常")
	}

	// 记录成功发送的详细信息
	if count, exists := result["count"]; exists {
		logrus.Infof("成功发送短信数量: %v", count)
	}
	if fee, exists := result["fee"]; exists {
		logrus.Infof("本次发送消费金额: %v", fee)
	}
	if unit, exists := result["unit"]; exists {
		logrus.Infof("计费单位: %v", unit)
	}
	if mobile, exists := result["mobile"]; exists {
		logrus.Infof("目标手机号确认: %v", mobile)
	}
	if sid, exists := result["sid"]; exists {
		logrus.Infof("短信发送ID: %v", sid)
	}

	logrus.Infof("短信发送成功: phone=%v, code=%v", phone, code)
	return code, nil
}
