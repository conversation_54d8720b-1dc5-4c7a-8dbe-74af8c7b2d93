package util

import (
	"errors"
	"regexp"
	"strconv"
	"strings"
	"testing"
	"time"
)

// getCurrentBusinessTimeRangeWithTime 测试版本，接受当前时间参数
func getCurrentBusinessTimeRangeWithTime(startHour string, now time.Time) (int64, int64, error) {
	// 校验时间格式 HH:mm
	timeRegex := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	if !timeRegex.MatchString(startHour) {
		return 0, 0, errors.New("startHour格式错误，应为HH:mm")
	}

	// 解析营业开始时间
	startParts := strings.Split(startHour, ":")
	startHourInt, _ := strconv.Atoi(startParts[0])
	startMinInt, _ := strconv.Atoi(startParts[1])

	// 计算今天的营业开始时间
	todayBusinessStart := time.Date(now.Year(), now.Month(), now.Day(), startHourInt, startMinInt, 0, 0, now.Location())

	// 判断当前时间属于哪个营业时间段
	if now.Before(todayBusinessStart) {
		// 当前时间早于今天的营业开始时间，属于昨天开始的营业时间段
		// 营业时间段：昨天startHour ~ 今天startHour
		yesterdayBusinessStart := todayBusinessStart.Add(-24 * time.Hour)
		return yesterdayBusinessStart.Unix(), todayBusinessStart.Unix(), nil
	} else {
		// 当前时间晚于或等于今天的营业开始时间，属于今天开始的营业时间段
		// 营业时间段：今天startHour ~ 明天startHour
		tomorrowBusinessStart := todayBusinessStart.Add(24 * time.Hour)
		return todayBusinessStart.Unix(), tomorrowBusinessStart.Unix(), nil
	}
}

func TestGetCurrentBusinessTimeRange(t *testing.T) {
	// 测试用例1：当前时间早于营业开始时间（应该属于昨天的营业时间段）
	// 模拟当前时间为凌晨3:00，营业时间为6:00~6:00
	testTime1 := time.Date(2024, 1, 2, 3, 0, 0, 0, time.Local) // 2024-01-02 03:00:00

	startTime, endTime, err := getCurrentBusinessTimeRangeWithTime("06:00", testTime1)
	if err != nil {
		t.Errorf("getCurrentBusinessTimeRangeWithTime() error = %v", err)
		return
	}

	startTimeObj := time.Unix(startTime, 0)
	endTimeObj := time.Unix(endTime, 0)

	// 验证：当前时间3:00应该属于昨天6:00~今天6:00的营业时间段
	expectedStart := time.Date(2024, 1, 1, 6, 0, 0, 0, time.Local) // 昨天6:00
	expectedEnd := time.Date(2024, 1, 2, 6, 0, 0, 0, time.Local)   // 今天6:00

	if !startTimeObj.Equal(expectedStart) {
		t.Errorf("测试用例1失败：当前时间3:00，营业时间6:00~6:00")
		t.Errorf("Expected start time %v, got %v", expectedStart, startTimeObj)
	}
	if !endTimeObj.Equal(expectedEnd) {
		t.Errorf("测试用例1失败：当前时间3:00，营业时间6:00~6:00")
		t.Errorf("Expected end time %v, got %v", expectedEnd, endTimeObj)
	}

	// 测试用例2：当前时间晚于营业开始时间（应该属于今天的营业时间段）
	// 模拟当前时间为上午9:00，营业时间为6:00~6:00
	testTime2 := time.Date(2024, 1, 2, 9, 0, 0, 0, time.Local) // 2024-01-02 09:00:00

	startTime2, endTime2, err2 := getCurrentBusinessTimeRangeWithTime("06:00", testTime2)
	if err2 != nil {
		t.Errorf("getCurrentBusinessTimeRangeWithTime() error = %v", err2)
		return
	}

	startTimeObj2 := time.Unix(startTime2, 0)
	endTimeObj2 := time.Unix(endTime2, 0)

	// 验证：当前时间9:00应该属于今天6:00~明天6:00的营业时间段
	expectedStart2 := time.Date(2024, 1, 2, 6, 0, 0, 0, time.Local) // 今天6:00
	expectedEnd2 := time.Date(2024, 1, 3, 6, 0, 0, 0, time.Local)   // 明天6:00

	if !startTimeObj2.Equal(expectedStart2) {
		t.Errorf("测试用例2失败：当前时间9:00，营业时间6:00~6:00")
		t.Errorf("Expected start time %v, got %v", expectedStart2, startTimeObj2)
	}
	if !endTimeObj2.Equal(expectedEnd2) {
		t.Errorf("测试用例2失败：当前时间9:00，营业时间6:00~6:00")
		t.Errorf("Expected end time %v, got %v", expectedEnd2, endTimeObj2)
	}

	// 测试用例3：当前时间正好等于营业开始时间
	testTime3 := time.Date(2024, 1, 2, 6, 0, 0, 0, time.Local) // 2024-01-02 06:00:00

	startTime3, endTime3, err3 := getCurrentBusinessTimeRangeWithTime("06:00", testTime3)
	if err3 != nil {
		t.Errorf("getCurrentBusinessTimeRangeWithTime() error = %v", err3)
		return
	}

	startTimeObj3 := time.Unix(startTime3, 0)
	endTimeObj3 := time.Unix(endTime3, 0)

	// 验证：当前时间6:00应该属于今天6:00~明天6:00的营业时间段
	expectedStart3 := time.Date(2024, 1, 2, 6, 0, 0, 0, time.Local) // 今天6:00
	expectedEnd3 := time.Date(2024, 1, 3, 6, 0, 0, 0, time.Local)   // 明天6:00

	if !startTimeObj3.Equal(expectedStart3) {
		t.Errorf("测试用例3失败：当前时间6:00，营业时间6:00~6:00")
		t.Errorf("Expected start time %v, got %v", expectedStart3, startTimeObj3)
	}
	if !endTimeObj3.Equal(expectedEnd3) {
		t.Errorf("测试用例3失败：当前时间6:00，营业时间6:00~6:00")
		t.Errorf("Expected end time %v, got %v", expectedEnd3, endTimeObj3)
	}

	// 测试用例4：无效的时间格式
	testTime4 := time.Date(2024, 1, 2, 12, 0, 0, 0, time.Local)
	invalidFormats := []string{"6", "25:00", "06:70", "abc:def", "06-00"}
	for _, format := range invalidFormats {
		_, _, err := getCurrentBusinessTimeRangeWithTime(format, testTime4)
		if err == nil {
			t.Errorf("Expected error for invalid format %s, but got none", format)
		}
	}

	// 测试用例5：不同的营业时间
	// 测试营业时间为22:00~22:00的情况
	testTime5 := time.Date(2024, 1, 2, 1, 0, 0, 0, time.Local) // 凌晨1:00
	startTime5, endTime5, err5 := getCurrentBusinessTimeRangeWithTime("22:00", testTime5)
	if err5 != nil {
		t.Errorf("getCurrentBusinessTimeRangeWithTime() error = %v", err5)
		return
	}

	startTimeObj5 := time.Unix(startTime5, 0)
	endTimeObj5 := time.Unix(endTime5, 0)

	// 验证：当前时间1:00应该属于昨天22:00~今天22:00的营业时间段
	expectedStart5 := time.Date(2024, 1, 1, 22, 0, 0, 0, time.Local) // 昨天22:00
	expectedEnd5 := time.Date(2024, 1, 2, 22, 0, 0, 0, time.Local)   // 今天22:00

	if !startTimeObj5.Equal(expectedStart5) {
		t.Errorf("测试用例5失败：当前时间1:00，营业时间22:00~22:00")
		t.Errorf("Expected start time %v, got %v", expectedStart5, startTimeObj5)
	}
	if !endTimeObj5.Equal(expectedEnd5) {
		t.Errorf("测试用例5失败：当前时间1:00，营业时间22:00~22:00")
		t.Errorf("Expected end time %v, got %v", expectedEnd5, endTimeObj5)
	}
}
