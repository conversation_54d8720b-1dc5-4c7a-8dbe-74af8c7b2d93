package service

import (
	"context"
	"fmt"
	"sync"
)

// serviceRegistry 服务注册中心实现
type serviceRegistry struct {
	services sync.Map // 服务映射，key为服务名称，value为Service接口
	mu       sync.RWMutex
}

// NewServiceRegistry 创建服务注册中心
func NewServiceRegistry() ServiceRegistry {
	return &serviceRegistry{}
}

// RegisterService 注册服务
func (r *serviceRegistry) RegisterService(service Service) error {
	info := service.GetServiceInfo()
	if _, loaded := r.services.LoadOrStore(info.Name, service); loaded {
		return fmt.Errorf("service %s already registered", info.Name)
	}
	return nil
}

// UnregisterService 注销服务
func (r *serviceRegistry) UnregisterService(name string) error {
	if service, ok := r.services.LoadAndDelete(name); ok {
		// 如果服务正在运行，先停止服务
		if service.(Service).IsRunning() {
			if err := service.(Service).Stop(context.Background()); err != nil {
				return fmt.Errorf("failed to stop service %s: %v", name, err)
			}
		}
		return nil
	}
	return fmt.Errorf("service %s not found", name)
}

// GetService 获取服务
func (r *serviceRegistry) GetService(name string) (Service, error) {
	if service, ok := r.services.Load(name); ok {
		return service.(Service), nil
	}
	return nil, fmt.Errorf("service %s not found", name)
}

// ListServices 获取所有服务
func (r *serviceRegistry) ListServices() []ServiceInfo {
	var services []ServiceInfo
	r.services.Range(func(key, value interface{}) bool {
		services = append(services, value.(Service).GetServiceInfo())
		return true
	})
	return services
}

// Start 启动所有服务
func (r *serviceRegistry) Start(ctx context.Context) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)

	r.services.Range(func(key, value interface{}) bool {
		service := value.(Service)
		if !service.IsRunning() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := service.Start(ctx); err != nil {
					select {
					case errChan <- fmt.Errorf("failed to start service %s: %v", service.GetServiceInfo().Name, err):
					default:
					}
				}
			}()
		}
		return true
	})

	// 等待所有服务启动完成
	wg.Wait()

	// 检查是否有错误
	select {
	case err := <-errChan:
		return err
	default:
		return nil
	}
}

// Stop 停止所有服务
func (r *serviceRegistry) Stop(ctx context.Context) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)

	r.services.Range(func(key, value interface{}) bool {
		service := value.(Service)
		if service.IsRunning() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := service.Stop(ctx); err != nil {
					select {
					case errChan <- fmt.Errorf("failed to stop service %s: %v", service.GetServiceInfo().Name, err):
					default:
					}
				}
			}()
		}
		return true
	})

	// 等待所有服务停止完成
	wg.Wait()

	// 检查是否有错误
	select {
	case err := <-errChan:
		return err
	default:
		return nil
	}
}
