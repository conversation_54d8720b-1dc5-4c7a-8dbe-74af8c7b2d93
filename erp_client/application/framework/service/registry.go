package service

import (
	"context"
	"fmt"
	"sync"
)

// ServiceStatus 服务状态
type ServiceStatus string

const (
	ServiceStatusInit    ServiceStatus = "INIT"    // 初始化
	ServiceStatusRunning ServiceStatus = "RUNNING" // 运行中
	ServiceStatusStopped ServiceStatus = "STOPPED" // 停止
	ServiceStatusError   ServiceStatus = "ERROR"   // 错误
)

// ServiceInfo 服务信息
type ServiceInfo struct {
	Name        string                 // 服务名称
	Version     string                 // 服务版本
	Description string                 // 服务描述
	Status      ServiceStatus          // 服务状态
	Metadata    map[string]interface{} // 服务元数据
}

// Service 服务接口
type Service interface {
	// GetServiceInfo 获取服务信息
	GetServiceInfo() ServiceInfo
	// Start 启动服务
	Start(ctx context.Context) error
	// Stop 停止服务
	Stop(ctx context.Context) error
	// IsRunning 检查服务是否运行中
	IsRunning() bool
}

// ServiceRegistry 服务注册中心接口
type ServiceRegistry interface {
	// RegisterService 注册服务
	RegisterService(service Service) error
	// UnregisterService 注销服务
	UnregisterService(name string) error
	// GetService 获取服务
	GetService(name string) (Service, error)
	// ListServices 获取所有服务
	ListServices() []ServiceInfo
	// Start 启动所有服务
	Start(ctx context.Context) error
	// Stop 停止所有服务
	Stop(ctx context.Context) error
}

// ServiceOption 服务选项
type ServiceOption func(*BaseService)

// WithMetadata 设置服务元数据
func WithMetadata(metadata map[string]interface{}) ServiceOption {
	return func(s *BaseService) {
		s.info.Metadata = metadata
	}
}

// WithVersion 设置服务版本
func WithVersion(version string) ServiceOption {
	return func(s *BaseService) {
		s.info.Version = version
	}
}

// WithDescription 设置服务描述
func WithDescription(description string) ServiceOption {
	return func(s *BaseService) {
		s.info.Description = description
	}
}

// BaseService 基础服务实现
type BaseService struct {
	mu     sync.RWMutex
	info   ServiceInfo
	status ServiceStatus
}

// NewBaseService 创建基础服务
func NewBaseService(name string, opts ...ServiceOption) *BaseService {
	s := &BaseService{
		info: ServiceInfo{
			Name:     name,
			Status:   ServiceStatusInit,
			Metadata: make(map[string]interface{}),
		},
	}

	for _, opt := range opts {
		opt(s)
	}

	return s
}

// GetServiceInfo 获取服务信息
func (s *BaseService) GetServiceInfo() ServiceInfo {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.info
}

// Start 启动服务
func (s *BaseService) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.status == ServiceStatusRunning {
		return fmt.Errorf("service %s is already running", s.info.Name)
	}

	s.status = ServiceStatusRunning
	s.info.Status = ServiceStatusRunning
	return nil
}

// Stop 停止服务
func (s *BaseService) Stop(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.status != ServiceStatusRunning {
		return fmt.Errorf("service %s is not running", s.info.Name)
	}

	s.status = ServiceStatusStopped
	s.info.Status = ServiceStatusStopped
	return nil
}

// IsRunning 检查服务是否运行中
func (s *BaseService) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.status == ServiceStatusRunning
}
