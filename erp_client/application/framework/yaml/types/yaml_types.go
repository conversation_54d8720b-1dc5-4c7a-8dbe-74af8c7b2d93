package types

// YAMLProcessContent YAML流程内容
type YAMLProcessContent struct {
	Definition *YAMLDefinition `yaml:"definition"`
	Steps      []YAMLStep      `yaml:"steps"`
}

// YAMLDefinition YAML流程定义
type YAMLDefinition struct {
	ID          string       `yaml:"id"`
	Name        string       `yaml:"name"`
	Description string       `yaml:"description"`
	Output      []YAMLOutput `yaml:"output"`
}

// YAMLOutput YAML输出定义
type YAMLOutput struct {
	Name string `yaml:"name"`
}

// YAMLStep YAML步骤定义
type YAMLStep struct {
	ID          string                 `yaml:"id"`
	Name        string                 `yaml:"name"`
	Description string                 `yaml:"description,omitempty"`
	Condition   string                 `yaml:"condition,omitempty"`
	Action      map[string]interface{} `yaml:"action"`
}
