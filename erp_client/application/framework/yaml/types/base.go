package types

import (
	"voderpltvv/erp_client/domain/process/model"
)

// Definition 定义YAML配置文件的定义部分
type Definition struct {
	ID          string `yaml:"id"`
	Name        string `yaml:"name"`
	Description string `yaml:"description"`
}

// Metadata 定义YAML配置文件的元数据结构
type Metadata struct {
	Input   *VariableSection `yaml:"input"`
	Output  *VariableSection `yaml:"output"`
	Context *VariableSection `yaml:"context"`
	System  *VariableSection `yaml:"system"`
}

// VariableSection 定义变量区域
type VariableSection struct {
	Variables []Variable `yaml:"variables"`
}

// Variable 定义变量
type Variable struct {
	Name         string            `yaml:"name"`
	Type         string            `yaml:"type"`
	Required     bool              `yaml:"required"`
	Fields       []model.YAMLField `yaml:"fields,omitempty"`
	DefaultValue string            `yaml:"default_value,omitempty"`
	IsArray      bool              `yaml:"is_array,omitempty"`
	Expression   string            `yaml:"expression,omitempty"`
}

// ToModelFields 将 Variable 的 Fields 转换为 model.Field 切片
func (v *Variable) ToModelFields() []model.Field {
	if v.Fields == nil {
		return nil
	}
	result := make([]model.Field, len(v.Fields))
	for i, f := range v.Fields {
		result[i] = f.ToField()
	}
	return result
}
