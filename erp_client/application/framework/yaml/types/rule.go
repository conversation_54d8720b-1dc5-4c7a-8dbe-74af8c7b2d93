package types

// RuleContent Rule内容结构
type RuleContent struct {
	Definition *Definition `yaml:"definition"`
	Metadata   *Metadata   `yaml:"metadata"`
	Rules      []Rule      `yaml:"rules"`
}

// Rule 规则定义
type Rule struct {
	Name      string                   `yaml:"name"`      // 规则名称
	Condition string                   `yaml:"condition"` // 条件表达式
	Actions   []map[string]interface{} `yaml:"actions"`   // 动作列表
}

// RuleActionType 规则动作类型
type RuleActionType string

const (
	RuleActionTypeSetField  RuleActionType = "set_field" // 设置字段（旧版）
	RuleActionTypeSet       RuleActionType = "set"       // 设置字段（新版）
	RuleActionTypeCall      RuleActionType = "call"      // 调用函数
	RuleActionTypeReturn    RuleActionType = "return"    // 返回结果
	RuleActionTypeCalculate RuleActionType = "calculate" // 计算结果
)
