package types

// Step 流程步骤
type Step struct {
	ID        string                 `yaml:"id"`
	Name      string                 `yaml:"name"`
	Condition string                 `yaml:"condition"`
	Action    map[string]interface{} `yaml:"action"`
}

// ActionType 动作类型
type ActionType string

const (
	ActionTypeService   ActionType = "service"   // 服务调用
	ActionTypeSetField  ActionType = "set_field" // 设置字段
	ActionTypeError     ActionType = "error"     // 错误处理
	ActionTypeTransform ActionType = "transform" // 数据转换
	ActionTypeValidate  ActionType = "validate"  // 验证
)
