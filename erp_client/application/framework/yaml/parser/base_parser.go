package parser

import (
	"fmt"
	"sync"

	"voderpltvv/erp_client/application/framework/metadata"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/yaml/types"

	"gopkg.in/yaml.v3"
)

// Definition 定义YAML配置文件的定义部分
type Definition struct {
	ID          string `yaml:"id"`
	Name        string `yaml:"name"`
	Description string `yaml:"description"`
}

// BaseParser 定义YAML解析器的基础接口
type BaseParser interface {
	// ParseDefinition 解析定义部分
	ParseDefinition() (*types.Definition, error)
	// ParseMetadata 解析元数据部分
	ParseMetadata() (*types.Metadata, error)
	// ParseContent 解析具体内容部分，返回类型由具体实现定义
	ParseContent() (interface{}, error)
}

// BaseParserImpl 基础解析器实现
type BaseParserImpl struct {
	content   []byte
	cache     *sync.Map
	converter metadata.MetadataConverter
}

// NewBaseParserImpl 创建基础解析器
func NewBaseParserImpl(content []byte) *BaseParserImpl {
	return &BaseParserImpl{
		content:   content,
		cache:     &sync.Map{},
		converter: metadata.NewBaseMetadataConverter(),
	}
}

// ParseDefinition 实现基础的定义解析
func (p *BaseParserImpl) ParseDefinition() (*types.Definition, error) {
	if cached, ok := p.cache.Load("definition"); ok {
		return cached.(*types.Definition), nil
	}

	var doc struct {
		Definition *types.Definition `yaml:"definition"`
	}

	if err := yaml.Unmarshal(p.content, &doc); err != nil {
		return nil, fmt.Errorf("解析definition失败: %w", err)
	}

	if doc.Definition == nil {
		return nil, fmt.Errorf("未找到definition部分")
	}

	p.cache.Store("definition", doc.Definition)
	return doc.Definition, nil
}

// ParseMetadata 实现基础的元数据解析
func (p *BaseParserImpl) ParseMetadata() (*types.Metadata, error) {
	if cached, ok := p.cache.Load("metadata"); ok {
		return cached.(*types.Metadata), nil
	}

	var doc struct {
		Metadata *types.Metadata `yaml:"metadata"`
	}

	if err := yaml.Unmarshal(p.content, &doc); err != nil {
		return nil, fmt.Errorf("解析元数据失败: %w", err)
	}

	if doc.Metadata == nil {
		return nil, fmt.Errorf("未找到metadata部分")
	}

	p.cache.Store("metadata", doc.Metadata)
	return doc.Metadata, nil
}

// ConvertToVariableMetadata 将yaml.Metadata转换为variable.Metadata
func (p *BaseParserImpl) ConvertToVariableMetadata(yamlMetadata *types.Metadata) *variable.Metadata {
	return p.converter.ConvertToRuntimeMetadata(yamlMetadata)
}

// GetRawContent 获取原始内容
func (p *BaseParserImpl) GetRawContent() []byte {
	return p.content
}

// ClearCache 清除缓存
func (p *BaseParserImpl) ClearCache() {
	p.cache = &sync.Map{}
}

// ParseContent 由具体子类实现
func (p *BaseParserImpl) ParseContent() (interface{}, error) {
	// 由具体子类实现
	return nil, nil
}
