package parser

import (
	"fmt"

	"voderpltvv/erp_client/application/framework/yaml/types"

	"gopkg.in/yaml.v3"
)

// RuleParser Rule专用解析器
type RuleParser struct {
	*BaseParserImpl
}

// NewRuleParser 创建Rule解析器实例
func NewRuleParser(content []byte) *RuleParser {
	return &RuleParser{
		BaseParserImpl: NewBaseParserImpl(content),
	}
}

// RuleContent Rule内容结构
type RuleContent struct {
	Definition *types.Definition `yaml:"definition"`
	Metadata   *types.Metadata   `yaml:"metadata"`
	Rules      []types.Rule      `yaml:"rules"`
}

// ParseContent 解析Rule内容
func (p *RuleParser) ParseContent() (*RuleContent, error) {
	if cached, ok := p.cache.Load("content"); ok {
		return cached.(*RuleContent), nil
	}

	var doc struct {
		Definition *types.Definition `yaml:"definition"`
		Metadata   *types.Metadata   `yaml:"metadata"`
		Rules      []types.Rule      `yaml:"rules"`
	}

	if err := yaml.Unmarshal(p.GetRawContent(), &doc); err != nil {
		return nil, fmt.Errorf("解析rule内容失败: %w", err)
	}

	if doc.Definition == nil {
		return nil, fmt.Errorf("未找到definition部分")
	}

	content := &RuleContent{
		Definition: doc.Definition,
		Metadata:   doc.Metadata,
		Rules:      doc.Rules,
	}

	// 验证规则
	for i, rule := range content.Rules {
		if err := p.validateRule(rule); err != nil {
			return nil, fmt.Errorf("规则[%d]验证失败: %w", i, err)
		}
	}

	p.cache.Store("content", content)
	return content, nil
}

// validateRule 验证规则
func (p *RuleParser) validateRule(rule types.Rule) error {
	// 验证基本字段
	if rule.Name == "" {
		return fmt.Errorf("规则名称不能为空")
	}
	if rule.Condition == "" {
		return fmt.Errorf("规则条件不能为空")
	}
	if len(rule.Actions) == 0 {
		return fmt.Errorf("规则动作不能为空")
	}

	// 验证动作
	for i, action := range rule.Actions {
		if err := p.validateAction(action); err != nil {
			return fmt.Errorf("动作[%d]验证失败: %w", i, err)
		}
	}

	return nil
}

// validateAction 验证动作
func (p *RuleParser) validateAction(action map[string]interface{}) error {
	actionType, ok := action["type"].(string)
	if !ok {
		return fmt.Errorf("动作类型必须是字符串")
	}

	// 根据动作类型验证
	switch types.RuleActionType(actionType) {
	case types.RuleActionTypeSetField:
		return p.validateSetFieldAction(action)
	case types.RuleActionTypeSet:
		return p.validateSetAction(action)
	case types.RuleActionTypeCall:
		return p.validateCallAction(action)
	case types.RuleActionTypeReturn:
		return p.validateReturnAction(action)
	case types.RuleActionTypeCalculate:
		return p.validateCalculateAction(action)
	default:
		return fmt.Errorf("不支持的动作类型: %s", actionType)
	}
}

// validateSetFieldAction 验证设置字段动作
func (p *RuleParser) validateSetFieldAction(action map[string]interface{}) error {
	if _, ok := action["target"].(string); !ok {
		return fmt.Errorf("字段设置动作缺少必需字段: target")
	}
	if _, exists := action["value"]; !exists {
		return fmt.Errorf("字段设置动作缺少必需字段: value")
	}
	return nil
}

// validateSetAction 验证设置动作（新版）
func (p *RuleParser) validateSetAction(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("set动作缺少必需字段: params")
	}

	if _, ok := params["target"].(string); !ok {
		return fmt.Errorf("set动作缺少必需字段: params.target")
	}
	if _, exists := params["value"]; !exists {
		return fmt.Errorf("set动作缺少必需字段: params.value")
	}
	return nil
}

// validateCallAction 验证调用函数动作
func (p *RuleParser) validateCallAction(action map[string]interface{}) error {
	if _, ok := action["function"].(string); !ok {
		return fmt.Errorf("函数调用动作缺少必需字段: function")
	}
	return nil
}

// validateReturnAction 验证返回结果动作
func (p *RuleParser) validateReturnAction(action map[string]interface{}) error {
	if _, exists := action["value"]; !exists {
		return fmt.Errorf("返回动作缺少必需字段: value")
	}
	return nil
}

// validateCalculateAction 验证计算动作
func (p *RuleParser) validateCalculateAction(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("calculate动作缺少必需字段: params")
	}

	if _, ok := params["target"].(string); !ok {
		return fmt.Errorf("calculate动作缺少必需字段: params.target")
	}
	if _, ok := params["expression"].(string); !ok {
		return fmt.Errorf("calculate动作缺少必需字段: params.expression")
	}
	return nil
}
