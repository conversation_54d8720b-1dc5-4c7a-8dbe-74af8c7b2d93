package parser

import (
	"fmt"

	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/yaml/types"

	"gopkg.in/yaml.v3"
)

// ProcessParser Process专用解析器
type ProcessParser struct {
	*BaseParserImpl
}

// NewProcessParser 创建Process解析器实例
func NewProcessParser(content []byte) *ProcessParser {
	return &ProcessParser{
		BaseParserImpl: NewBaseParserImpl(content),
	}
}

// ProcessContent Process内容结构
type ProcessContent struct {
	Definition *types.Definition `yaml:"definition"`
	Steps      []types.Step      `yaml:"steps"`
	Parser     *ProcessParser
	Metadata   *variable.Metadata
}

// ParseContent 解析Process内容
func (p *ProcessParser) ParseContent() (*ProcessContent, error) {
	if cached, ok := p.cache.Load("content"); ok {
		return cached.(*ProcessContent), nil
	}

	var doc struct {
		Definition *types.Definition `yaml:"definition"`
		Metadata   *types.Metadata   `yaml:"metadata"`
		Steps      []types.Step      `yaml:"steps"`
	}

	if err := yaml.Unmarshal(p.GetRawContent(), &doc); err != nil {
		return nil, fmt.Errorf("解析process内容失败: %w", err)
	}

	if doc.Definition == nil {
		return nil, fmt.Errorf("未找到definition部分")
	}

	// 转换元数据
	metadata := p.ConvertToVariableMetadata(doc.Metadata)

	content := &ProcessContent{
		Definition: doc.Definition,
		Steps:      doc.Steps,
		Parser:     p,
		Metadata:   metadata,
	}

	// 验证步骤
	for i, step := range content.Steps {
		if err := p.validateStep(step); err != nil {
			return nil, fmt.Errorf("步骤[%d]验证失败: %w", i, err)
		}
	}

	p.cache.Store("content", content)
	return content, nil
}

// validateStep 验证步骤
func (p *ProcessParser) validateStep(step types.Step) error {
	// 验证基本字段
	if step.ID == "" {
		return fmt.Errorf("步骤ID不能为空")
	}
	if step.Name == "" {
		return fmt.Errorf("步骤名称不能为空")
	}

	// 验证动作
	if step.Action == nil {
		return fmt.Errorf("步骤动作不能为空")
	}

	actionType, ok := step.Action["type"].(string)
	if !ok {
		return fmt.Errorf("动作类型必须是字符串")
	}

	// 根据动作类型验证
	switch types.ActionType(actionType) {
	case types.ActionTypeService:
		return p.validateServiceAction(step.Action)
	case types.ActionTypeSetField:
		return p.validateSetFieldAction(step.Action)
	case types.ActionTypeError:
		return p.validateErrorAction(step.Action)
	case types.ActionTypeTransform:
		return p.validateTransformAction(step.Action)
	case types.ActionTypeValidate:
		return p.validateValidateAction(step.Action)
	default:
		return fmt.Errorf("不支持的动作类型: %s", actionType)
	}
}

// validateServiceAction 验证服务调用动作
func (p *ProcessParser) validateServiceAction(action map[string]interface{}) error {
	required := []string{"service", "method"}
	for _, field := range required {
		if _, ok := action[field].(string); !ok {
			return fmt.Errorf("服务动作缺少必需字段: %s", field)
		}
	}
	return nil
}

// validateRuleAction 验证规则执行动作
func (p *ProcessParser) validateRuleAction(action map[string]interface{}) error {
	if _, ok := action["rule_file"].(string); !ok {
		return fmt.Errorf("规则动作缺少必需字段: rule_file")
	}

	// 验证参数映射
	if params, ok := action["params"].(map[string]interface{}); ok {
		if input, ok := params["input"].(map[string]interface{}); !ok || len(input) == 0 {
			return fmt.Errorf("规则动作必须包含输入参数映射")
		}
	} else {
		return fmt.Errorf("规则动作必须包含params字段")
	}

	return nil
}

// validateSetFieldAction 验证字段设置动作
func (p *ProcessParser) validateSetFieldAction(action map[string]interface{}) error {
	if _, ok := action["target"].(string); !ok {
		return fmt.Errorf("字段设置动作缺少必需字段: target")
	}
	if _, exists := action["value"]; !exists {
		return fmt.Errorf("字段设置动作缺少必需字段: value")
	}
	return nil
}

// validateErrorAction 验证错误处理动作
func (p *ProcessParser) validateErrorAction(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("错误动作必须包含params字段")
	}

	// 验证必需字段
	required := []string{"code", "message"}
	for _, field := range required {
		if _, ok := params[field].(string); !ok {
			return fmt.Errorf("错误动作缺少必需字段: %s", field)
		}
	}
	return nil
}

// validateTransformAction 验证数据转换动作
func (p *ProcessParser) validateTransformAction(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("转换动作必须包含params字段")
	}

	// 验证必需字段
	required := []string{"source", "target", "mapping"}
	for _, field := range required {
		if _, exists := params[field]; !exists {
			return fmt.Errorf("转换动作缺少必需字段: %s", field)
		}
	}

	// 验证mapping必须是map类型
	if _, ok := params["mapping"].(map[string]interface{}); !ok {
		return fmt.Errorf("转换动作的mapping必须是对象类型")
	}

	return nil
}

// validateValidateAction 验证验证动作
func (p *ProcessParser) validateValidateAction(action map[string]interface{}) error {
	// 验证规则列表
	rules, ok := action["rules"].([]interface{})
	if !ok {
		return fmt.Errorf("验证动作缺少rules字段")
	}

	// 验证每个规则的格式
	for i, r := range rules {
		rule, ok := r.(map[string]interface{})
		if !ok {
			return fmt.Errorf("规则[%d]格式错误", i)
		}

		// 验证必需字段
		required := []string{"type", "field", "field_name", "error_message", "error_code"}
		for _, field := range required {
			if _, ok := rule[field].(string); !ok {
				return fmt.Errorf("规则[%d]缺少必需字段: %s", i, field)
			}
		}

		// 验证规则类型
		ruleType, _ := rule["type"].(string)
		switch ruleType {
		case "required":
			// required 规则不需要额外参数
		case "enum":
			// 验证枚举值列表
			if params, ok := rule["params"].(map[string]interface{}); ok {
				if values, ok := params["values"].([]interface{}); !ok || len(values) == 0 {
					return fmt.Errorf("规则[%d]的enum类型必须提供values参数", i)
				}
			} else {
				return fmt.Errorf("规则[%d]的enum类型缺少params", i)
			}
		case "number_range":
			// 验证数值范围参数
			if params, ok := rule["params"].(map[string]interface{}); ok {
				hasValidParam := false
				if min, exists := params["min"]; exists {
					switch min.(type) {
					case float64, int, int32, int64:
						hasValidParam = true
					}
				}
				if !hasValidParam {
					if max, exists := params["max"]; exists {
						switch max.(type) {
						case float64, int, int32, int64:
							hasValidParam = true
						}
					}
				}
				if !hasValidParam {
					return fmt.Errorf("规则[%d]的number_range类型至少需要提供min或max参数", i)
				}
			} else {
				return fmt.Errorf("规则[%d]的number_range类型缺少params", i)
			}
		case "string_length":
			// 验证字符串长度参数
			if params, ok := rule["params"].(map[string]interface{}); ok {
				if _, hasMin := params["min"].(int); !hasMin {
					if _, hasMax := params["max"].(int); !hasMax {
						return fmt.Errorf("规则[%d]的string_length类型至少需要提供min或max参数", i)
					}
				}
			} else {
				return fmt.Errorf("规则[%d]的string_length类型缺少params", i)
			}
		case "compare":
			// 验证比较参数
			if params, ok := rule["params"].(map[string]interface{}); ok {
				if operator, ok := params["operator"].(string); !ok {
					return fmt.Errorf("规则[%d]的compare类型缺少operator参数", i)
				} else {
					// 验证操作符
					validOperators := map[string]bool{"==": true, "!=": true, ">": true, "<": true, ">=": true, "<=": true}
					if !validOperators[operator] {
						return fmt.Errorf("规则[%d]的compare类型使用了无效的operator: %s", i, operator)
					}
				}
				if _, ok := params["target"]; !ok {
					return fmt.Errorf("规则[%d]的compare类型缺少target参数", i)
				}
			} else {
				return fmt.Errorf("规则[%d]的compare类型缺少params", i)
			}
		case "pattern":
			// 验证正则表达式参数
			if params, ok := rule["params"].(map[string]interface{}); ok {
				if pattern, ok := params["pattern"].(string); !ok || pattern == "" {
					return fmt.Errorf("规则[%d]的pattern类型必须提供有效的pattern参数", i)
				}
			} else {
				return fmt.Errorf("规则[%d]的pattern类型缺少params", i)
			}
		default:
			return fmt.Errorf("规则[%d]使用了不支持的验证类型: %s", i, ruleType)
		}
	}

	// 验证错误处理器配置
	if errorHandler, ok := action["error_handler"].(map[string]interface{}); ok {
		// 验证错误处理器类型
		if handlerType, ok := errorHandler["type"].(string); !ok {
			return fmt.Errorf("错误处理器缺少type字段")
		} else if handlerType != "early_return" && handlerType != "continue" {
			return fmt.Errorf("不支持的错误处理器类型: %s", handlerType)
		}

		// 验证错误处理器必需字段
		required := []string{"error_message", "error_code"}
		for _, field := range required {
			if _, ok := errorHandler[field].(string); !ok {
				return fmt.Errorf("错误处理器缺少必需字段: %s", field)
			}
		}

		// 如果是continue类型，验证target和mapping
		if handlerType, _ := errorHandler["type"].(string); handlerType == "continue" {
			if target, ok := errorHandler["target"].(string); !ok || target == "" {
				return fmt.Errorf("continue类型的错误处理器必须提供target字段")
			}
			if mapping, ok := errorHandler["mapping"].(map[string]interface{}); !ok || len(mapping) == 0 {
				return fmt.Errorf("continue类型的错误处理器必须提供mapping字段")
			}
		}
	}

	return nil
}
