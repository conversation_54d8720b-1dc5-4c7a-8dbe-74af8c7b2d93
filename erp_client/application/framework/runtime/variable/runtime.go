package variable

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"

	"voderpltvv/util"
	log "voderpltvv/util"

	"github.com/expr-lang/expr"
)

// Runtime 运行时变量管理
type Runtime struct {
	mu       sync.RWMutex
	env      map[string]interface{}
	metadata *Metadata
}

// NewRuntime 创建运行时实例
func NewRuntime(store Store) *Runtime {
	r := &Runtime{
		env:      make(map[string]interface{}),
		metadata: &Metadata{},
	}

	// 初始化命名空间
	r.env["input"] = make(map[string]interface{})
	r.env["output"] = make(map[string]interface{})
	r.env["context"] = make(map[string]interface{})
	r.env["system"] = make(map[string]interface{})

	// 如果提供了已有的 store，加载其数据
	if store != nil {
		if data := store.GetData(); data != nil {
			for k, v := range data {
				r.env[k] = v
			}
		}
	}

	// 初始化内置函数
	r.initBuiltinFunctions()
	return r
}

// NewTemporaryRuntime 创建临时运行时实例（用于数据映射等临时操作）
func NewTemporaryRuntime() *Runtime {
	r := &Runtime{
		env: make(map[string]interface{}),
	}
	r.initBuiltinFunctions()
	return r
}

// initBuiltinFunctions 初始化内置函数
func (r *Runtime) initBuiltinFunctions() {
	r.env["now"] = time.Now
	r.env["formatTime"] = func(t time.Time, layout string) string {
		return t.Format(layout)
	}
	r.env["len"] = func(v interface{}) int {
		if v == nil {
			return 0
		}
		switch val := v.(type) {
		case string:
			return len(val)
		case []interface{}:
			return len(val)
		case map[string]interface{}:
			return len(val)
		default:
			return 0
		}
	}
	r.env["sum"] = func(arr []interface{}) float64 {
		var total float64
		for _, v := range arr {
			switch val := v.(type) {
			case float64:
				total += val
			case int:
				total += float64(val)
			}
		}
		return total
	}
}

// Eval 计算表达式
func (r *Runtime) Eval(expression string) (interface{}, error) {
	log.Debug("Runtime.Eval", "表达式: %s", expression)

	// 获取环境变量的副本
	r.mu.RLock()
	envCopy := make(map[string]interface{})
	for k, v := range r.env {
		envCopy[k] = v
	}
	r.mu.RUnlock()

	// 打印环境变量
	log.Debug("Runtime.Eval", "环境变量:")
	for k, v := range envCopy {
		log.Debug("Runtime.Eval", "  %s: %s", k, util.FormatValue(v))
	}

	// 执行表达式
	program, err := expr.Compile(expression, expr.Env(envCopy))
	if err != nil {
		log.Error("Runtime.Eval", "编译表达式失败: %v", err)
		return nil, fmt.Errorf("编译表达式失败: %w", err)
	}

	result, err := expr.Run(program, envCopy)
	if err != nil {
		log.Error("Runtime.Eval", "执行表达式失败: %v", err)
		return nil, fmt.Errorf("执行表达式失败: %w", err)
	}

	log.Debug("Runtime.Eval", "结果: %s", util.FormatValue(result))
	return result, nil
}

// Get 获取变量值
func (r *Runtime) Get(path string) (interface{}, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return r.Eval(path)
}

// Set 设置变量值
func (r *Runtime) Set(path string, value interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	parts := strings.Split(path, ".")
	if len(parts) == 0 {
		return fmt.Errorf("empty path")
	}

	// 验证命名空间
	ns := parts[0]
	isStandardRuntime := r.env["input"] != nil && r.env["output"] != nil && r.env["context"] != nil && r.env["system"] != nil
	if isStandardRuntime && ns != "input" && ns != "output" && ns != "context" && ns != "system" {
		log.Error("Runtime.Set", "无效的命名空间: %s", ns)
		return fmt.Errorf("invalid namespace: %s", ns)
	}

	// 如果只有命名空间，直接设置整个命名空间
	if len(parts) == 1 {
		// 对于标准运行时，要求命名空间值必须是 map[string]interface{}
		if isStandardRuntime {
			if mapValue, ok := value.(map[string]interface{}); ok {
				r.env[ns] = mapValue
				return nil
			}
			return fmt.Errorf("invalid namespace data type for standard runtime")
		}
		// 对于临时运行时，允许任何类型的值
		r.env[ns] = value
		return nil
	}

	// 获取或创建命名空间数据
	var current map[string]interface{}
	if existingData, ok := r.env[ns]; ok {
		if currentMap, ok := existingData.(map[string]interface{}); ok {
			current = currentMap
		} else {
			// 如果现有数据不是 map，创建新的
			current = make(map[string]interface{})
			r.env[ns] = current
		}
	} else {
		current = make(map[string]interface{})
		r.env[ns] = current
	}

	// 设置嵌套值
	for i := 1; i < len(parts)-1; i++ {
		next, exists := current[parts[i]]
		if !exists {
			next = make(map[string]interface{})
			current[parts[i]] = next
		}
		nextMap, ok := next.(map[string]interface{})
		if !ok {
			// 如果不是 map，创建一个新的
			nextMap = make(map[string]interface{})
			current[parts[i]] = nextMap
		}
		current = nextMap
	}

	// 设置最终值
	lastPart := parts[len(parts)-1]
	if mapValue, ok := value.(map[string]interface{}); ok {
		current[lastPart] = mapValue
	} else {
		current[lastPart] = value
	}

	log.Debug("Runtime.Set", "路径: %s, 值: %s", path, util.FormatValue(value))
	return nil
}

// Map 映射操作
func (r *Runtime) Map(target string, mappings map[string]string) error {
	log.Debug("Runtime.Map", "目标: %s, 映射规则: %s", target, util.FormatValue(mappings))

	values := make(map[string]interface{})
	for field, expr := range mappings {
		value, _ := r.Eval(expr)

		// 处理指针类型
		if value != nil {
			val := reflect.ValueOf(value)
			if val.Kind() == reflect.Ptr {
				if val.IsNil() {
					log.Debug("Runtime.Map", "字段 %s 的值为 nil 指针", field)
					values[field] = nil
					continue
				}
				value = val.Elem().Interface()
				log.Debug("Runtime.Map", "字段 %s 解引用后的值: %s", field, util.FormatValue(value))
			}
		}

		values[field] = value
	}

	log.Debug("Runtime.Map", "映射结果: %s", util.FormatValue(values))
	return r.Set(target, values)
}

// MapArray 数组映射操作
func (r *Runtime) MapArray(target string, source string, mappings map[string]string) error {
	sourceData, err := r.Eval(source)
	if err != nil {
		log.Error("Runtime.MapArray", "获取源数组失败: %v", err)
		return fmt.Errorf("获取源数组失败: %w", err)
	}

	val := reflect.ValueOf(sourceData)
	if val.Kind() != reflect.Slice {
		log.Error("Runtime.MapArray", "源数据不是数组类型: %v", val.Kind())
		return fmt.Errorf("源数据不是数组类型: %v", val.Kind())
	}

	length := val.Len()
	result := make([]interface{}, length)

	log.Debug("Runtime.MapArray", "开始处理数组，长度: %d", length)

	// 遍历切片中的每个元素
	for i := 0; i < length; i++ {
		item := val.Index(i).Interface()
		r.env["item"] = item
		mapped := make(map[string]interface{})

		for field, expr := range mappings {
			value, _ := r.Eval(expr)
			mapped[field] = value
		}
		result[i] = mapped
		log.Debug("Runtime.MapArray", "处理第 %d 个元素: %s", i, util.FormatValue(mapped))
	}

	log.Debug("Runtime.MapArray", "数组处理完成")
	return r.Set(target, result)
}

// LoadMetadata 加载元数据
func (r *Runtime) LoadMetadata(metadata *Metadata) error {
	if metadata == nil {
		return nil
	}

	r.mu.Lock()
	r.metadata = metadata
	r.mu.Unlock()

	sections := map[string]*VariableSection{
		"input":   metadata.Input,
		"output":  metadata.Output,
		"context": metadata.Context,
		"system":  metadata.System,
	}

	for ns, section := range sections {
		if section == nil {
			continue
		}

		nsData := make(map[string]interface{})
		for _, v := range section.Variables {
			var value interface{}
			var err error

			if v.Expression != "" {
				value, err = r.Eval(v.Expression)
				if err != nil {
					return fmt.Errorf("计算表达式[%s]失败: %w", v.Expression, err)
				}
			} else if v.DefaultValue != "" {
				value, err = r.Eval(v.DefaultValue)
				if err != nil {
					return fmt.Errorf("计算默认值[%s]失败: %w", v.DefaultValue, err)
				}
			} else {
				value = make(map[string]interface{})
			}
			nsData[v.Name] = value
		}

		if err := r.Set(ns, nsData); err != nil {
			return fmt.Errorf("设置命名空间[%s]数据失败: %w", ns, err)
		}
	}

	return nil
}

// GetMetadata 获取元数据
func (r *Runtime) GetMetadata() (*Metadata, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.metadata, nil
}
