package variable

import (
	"fmt"
)

// Namespace 变量命名空间
type Namespace string

const (
	// InputNamespace 输入参数命名空间
	InputNamespace Namespace = "input"
	// ContextNamespace 上下文对象命名空间
	ContextNamespace Namespace = "context"
	// SystemNamespace 系统变量命名空间
	SystemNamespace Namespace = "system"
	// OutputNamespace 输出参数命名空间
	OutputNamespace Namespace = "output"
)

// ValueType 值类型
type ValueType string

const (
	// TypeString 字符串类型
	TypeString ValueType = "string"
	// TypeInt 整数类型
	TypeInt ValueType = "int"
	// TypeFloat 浮点数类型
	TypeFloat ValueType = "float"
	// TypeBool 布尔类型
	TypeBool ValueType = "bool"
	// TypeObject 对象类型
	TypeObject ValueType = "object"
	// TypeArray 数组类型
	TypeArray ValueType = "array"
	// TypeVO VO对象类型
	TypeVO ValueType = "vo"
	// TypeTime 时间类型
	TypeTime ValueType = "time"
)

// ExprEngine 表达式引擎接口
type ExprEngine interface {
	// Eval 计算表达式并返回结果
	Eval(expression string) (interface{}, error)
	// EvalMany 批量计算表达式
	EvalMany(expressions []string) ([]interface{}, error)
	// EvalAs 计算表达式并转换为指定类型
	EvalAs(expression string, targetType ValueType) (interface{}, error)
}

// DynamicEnvironment 动态环境接口
type DynamicEnvironment interface {
	// GetEnv 获取实时的环境数据
	GetEnv() map[string]interface{}
	// GetBuiltinFunctions 获取内置函数
	GetBuiltinFunctions() map[string]interface{}
}

// Store 变量存储接口
type Store interface {
	// GetNamespaceData 获取命名空间数据
	GetNamespaceData(ns Namespace) (map[string]interface{}, error)
	// SetNamespaceData 设置命名空间数据
	SetNamespaceData(ns Namespace, data map[string]interface{}) error
	// AtomicUpdate 原子更新命名空间数据
	AtomicUpdate(ns Namespace, updater func(data map[string]interface{}) error) error
	// GetValue 获取变量值
	GetValue(path string) (interface{}, error)
	// SetValue 设置变量值
	SetValue(path string, value interface{}) error
	// GetData 获取所有数据
	GetData() map[string]interface{}
}

// IRuntime 运行时接口
type IRuntime interface {
	// 表达式计算
	Eval(expression string) (interface{}, error)

	// 变量操作
	Get(path string) (interface{}, error)
	Set(path string, value interface{}) error

	// 映射操作
	Map(target string, mappings map[string]string) error
	MapArray(target string, source string, mappings map[string]string) error

	// 元数据操作
	LoadMetadata(metadata *Metadata) error
	GetMetadata() (*Metadata, error)
}

// Metadata 元数据定义
type Metadata struct {
	Input   *VariableSection `yaml:"input,omitempty"`
	Output  *VariableSection `yaml:"output,omitempty"`
	Context *VariableSection `yaml:"context,omitempty"`
	System  *VariableSection `yaml:"system,omitempty"`
}

// VariableSection 变量区段
type VariableSection struct {
	Variables []*VariableDefinition `yaml:"variables,omitempty"`
}

// VariableDefinition 变量定义
type VariableDefinition struct {
	Name         string             `yaml:"name"`
	Required     bool               `yaml:"required,omitempty"`
	Fields       []*FieldDefinition `yaml:"fields,omitempty"`
	DefaultValue string             `yaml:"default_value,omitempty"`
	Expression   string             `yaml:"expression,omitempty"`
	IsArray      bool               `yaml:"is_array,omitempty"`
}

// FieldDefinition 字段定义
type FieldDefinition struct {
	Name     string             `yaml:"name"`
	Required bool               `yaml:"required,omitempty"`
	Fields   []*FieldDefinition `yaml:"fields,omitempty"`
	IsArray  bool               `yaml:"is_array,omitempty"`
}

// Accessor 变量访问接口
type Accessor interface {
	// GetValue 获取值，支持所有类型的路径访问（包括系统变量、表达式等）
	GetValue(data map[string]interface{}, path string) (interface{}, error)
	// SetValue 设置值
	SetValue(data map[string]interface{}, path string, value interface{}) error
	// GetSystemVariable 获取系统变量
	GetSystemVariable(name string) (interface{}, error)
	// GetFieldValue 获取对象字段值
	GetFieldValue(obj interface{}, fieldPath string) (interface{}, error)
	// SetFieldValue 设置对象字段值
	SetFieldValue(obj interface{}, fieldPath string, value interface{}) error
	// GetTypedValue 获取指定类型的值
	GetTypedValue(data map[string]interface{}, path string, targetType ValueType) (interface{}, error)
	// ValidatePath 验证路径是否有效
	ValidatePath(path string) error
	// IsSystemVariable 检查是否是系统变量
	IsSystemVariable(path string) bool
	// IsExpression 检查是否是表达式
	IsExpression(path string) bool
	// EvaluateExpression 计算表达式
	EvaluateExpression(expr string) (interface{}, error)
	// SetStore 设置数据存储
	SetStore(store Store)
}

// ValueInfo 值信息
type ValueInfo struct {
	Value    interface{}
	Required bool
}

// TypeInfo 类型信息
type TypeInfo struct {
	Type     ValueType           // 基础类型
	VOType   string              // VO类型名称
	ItemType *TypeInfo           // 数组元素类型
	Fields   map[string]TypeInfo // 对象字段类型
	Required bool                // 是否必需
}

// ArrayTypeInfo 数组类型信息
type ArrayTypeInfo struct {
	Type     ValueType // 必须是 TypeArray
	Required bool
	Items    TypeInfo // 数组元素的类型定义
}

// Error 错误定义
type Error struct {
	Code    ErrorCode
	Message string
	Path    string
}

func (e *Error) Error() string {
	return fmt.Sprintf("%s: %s (path: %s)", e.Code, e.Message, e.Path)
}

// ErrorCode 错误代码
type ErrorCode string

const (
	ErrPathNotFound     ErrorCode = "PATH_NOT_FOUND"
	ErrTypeMismatch     ErrorCode = "TYPE_MISMATCH"
	ErrInvalidPath      ErrorCode = "INVALID_PATH"
	ErrArrayIndexBounds ErrorCode = "ARRAY_INDEX_BOUNDS"
	ErrRequired         ErrorCode = "REQUIRED_FIELD_MISSING"
	ErrInvalidType      ErrorCode = "INVALID_TYPE"
	ErrExpressionError  ErrorCode = "EXPRESSION_ERROR"
)

// NewError 创建错误
func NewError(code ErrorCode, message string, path string) error {
	return &Error{
		Code:    code,
		Message: message,
		Path:    path,
	}
}

// NewEmptyData 创建空数据
func NewEmptyData() map[string]interface{} {
	data := make(map[string]interface{})
	data[string(SystemNamespace)] = make(map[string]interface{})
	data[string(InputNamespace)] = make(map[string]interface{})
	data[string(ContextNamespace)] = make(map[string]interface{})
	data[string(OutputNamespace)] = make(map[string]interface{})
	return data
}
