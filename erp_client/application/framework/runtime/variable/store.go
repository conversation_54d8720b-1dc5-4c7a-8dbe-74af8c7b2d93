package variable

import (
	"fmt"
	"strings"
	"sync"
)

// StoreImpl 存储实现
type StoreImpl struct {
	data map[Namespace]map[string]interface{}
	mu   sync.RWMutex
}

// NewStore 创建存储实例
func NewStore() Store {
	return &StoreImpl{
		data: map[Namespace]map[string]interface{}{
			InputNamespace:   make(map[string]interface{}),
			OutputNamespace:  make(map[string]interface{}),
			ContextNamespace: make(map[string]interface{}),
			SystemNamespace:  make(map[string]interface{}),
		},
	}
}

// GetNamespaceData 获取命名空间数据
func (s *StoreImpl) GetNamespaceData(ns Namespace) (map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	data, exists := s.data[ns]
	if !exists {
		return nil, fmt.Errorf("namespace not found: %s", ns)
	}

	// 返回数据的深拷贝，防止外部修改
	copied := deepCopy(data)
	if m, ok := copied.(map[string]interface{}); ok {
		return m, nil
	}
	return nil, fmt.<PERSON>rro<PERSON>("invalid data type in namespace: %s", ns)
}

// SetNamespaceData 设置命名空间数据
func (s *StoreImpl) SetNamespaceData(ns Namespace, data map[string]interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.data[ns]; !exists {
		return fmt.Errorf("invalid namespace: %s", ns)
	}

	// 存储数据的深拷贝，防止外部修改
	copied := deepCopy(data)
	if m, ok := copied.(map[string]interface{}); ok {
		s.data[ns] = m
		return nil
	}
	return fmt.Errorf("invalid data type")
}

// AtomicUpdate 原子更新命名空间数据
func (s *StoreImpl) AtomicUpdate(ns Namespace, updater func(data map[string]interface{}) error) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	data, exists := s.data[ns]
	if !exists {
		return fmt.Errorf("namespace not found: %s", ns)
	}

	// 创建数据的副本用于更新
	copied := deepCopy(data)
	workingCopy, ok := copied.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid data type in namespace: %s", ns)
	}

	// 执行更新操作
	if err := updater(workingCopy); err != nil {
		return err
	}

	// 更新成功，保存结果
	s.data[ns] = workingCopy
	return nil
}

// GetValue 获取变量值
func (s *StoreImpl) GetValue(path string) (interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	parts := strings.Split(path, ".")
	if len(parts) == 0 {
		return nil, fmt.Errorf("empty path")
	}

	// 验证命名空间
	ns := Namespace(parts[0])
	if _, exists := s.data[ns]; !exists {
		return nil, fmt.Errorf("invalid namespace: %s", ns)
	}

	// 如果只有命名空间，返回整个命名空间数据
	if len(parts) == 1 {
		return deepCopy(s.data[ns]), nil
	}

	// 获取嵌套值
	var current interface{} = s.data[ns]
	for i := 1; i < len(parts); i++ {
		if current == nil {
			return nil, fmt.Errorf("null value at path: %s", strings.Join(parts[:i], "."))
		}

		mapValue, ok := current.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("not an object at path: %s", strings.Join(parts[:i], "."))
		}

		var exists bool
		current, exists = mapValue[parts[i]]
		if !exists {
			return nil, fmt.Errorf("field not found: %s", strings.Join(parts[:i+1], "."))
		}
	}

	return deepCopy(current), nil
}

// SetValue 设置变量值
func (s *StoreImpl) SetValue(path string, value interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	parts := strings.Split(path, ".")
	if len(parts) == 0 {
		return fmt.Errorf("empty path")
	}

	// 验证命名空间
	ns := Namespace(parts[0])
	if _, exists := s.data[ns]; !exists {
		return fmt.Errorf("invalid namespace: %s", ns)
	}

	// 如果只有命名空间，直接设置整个命名空间
	if len(parts) == 1 {
		if mapValue, ok := value.(map[string]interface{}); ok {
			copied := deepCopy(mapValue)
			if m, ok := copied.(map[string]interface{}); ok {
				s.data[ns] = m
				return nil
			}
		}
		return fmt.Errorf("invalid namespace data type")
	}

	// 设置嵌套值
	current := s.data[ns]
	for i := 1; i < len(parts)-1; i++ {
		next, exists := current[parts[i]]
		if !exists {
			next = make(map[string]interface{})
			current[parts[i]] = next
		}
		nextMap, ok := next.(map[string]interface{})
		if !ok {
			return fmt.Errorf("not an object at path: %s", strings.Join(parts[:i+1], "."))
		}
		current = nextMap
	}

	current[parts[len(parts)-1]] = deepCopy(value)
	return nil
}

// GetData 获取所有数据
func (s *StoreImpl) GetData() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make(map[string]interface{})
	for ns, data := range s.data {
		copied := deepCopy(data)
		if m, ok := copied.(map[string]interface{}); ok {
			result[string(ns)] = m
		}
	}
	return result
}

// deepCopy 深拷贝辅助函数
func deepCopy(src interface{}) interface{} {
	if src == nil {
		return nil
	}

	switch val := src.(type) {
	case map[string]interface{}:
		dst := make(map[string]interface{}, len(val))
		for k, v := range val {
			dst[k] = deepCopy(v)
		}
		return dst
	case []interface{}:
		dst := make([]interface{}, len(val))
		for i, v := range val {
			dst[i] = deepCopy(v)
		}
		return dst
	default:
		// 基本类型直接返回
		return val
	}
}

// Clear 清空存储
func (s *StoreImpl) Clear() {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 重新初始化所有命名空间
	for ns := range s.data {
		s.data[ns] = make(map[string]interface{})
	}
}

// DumpData 导出数据
func (s *StoreImpl) DumpData() map[string]map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	dump := make(map[string]map[string]interface{})
	for ns, data := range s.data {
		copied := deepCopy(data)
		if m, ok := copied.(map[string]interface{}); ok {
			dump[string(ns)] = m
		}
	}
	return dump
}
