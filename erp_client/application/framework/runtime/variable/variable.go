package variable

// Manager 变量管理器接口
type Manager interface {
	// SetVariable 设置变量
	SetVariable(name string, value interface{}) error
	// GetVariable 获取变量
	GetVariable(name string) (interface{}, error)
	// HasVariable 是否存在变量
	HasVariable(name string) bool
	// DeleteVariable 删除变量
	DeleteVariable(name string) error
	// GetAllVariables 获取所有变量
	GetAllVariables() map[string]interface{}
	// Clear 清空所有变量
	Clear()
}
