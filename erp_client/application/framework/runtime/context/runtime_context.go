package context

import (
	"voderpltvv/erp_client/application/framework/runtime/expression"
	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// RuntimeContext 运行时上下文接口
type RuntimeContext interface {
	// GetVariableManager 获取变量管理器
	GetVariableManager() variable.Manager
	// GetExpressionEngine 获取表达式引擎
	GetExpressionEngine() expression.Engine
	// Clone 克隆上下文
	Clone() RuntimeContext
}

// NewRuntimeContext 创建运行时上下文
func NewRuntimeContext(variableManager variable.Manager, expressionEngine expression.Engine) RuntimeContext {
	return &runtimeContextImpl{
		variableManager:  variableManager,
		expressionEngine: expressionEngine,
	}
}

// runtimeContextImpl 运行时上下文实现
type runtimeContextImpl struct {
	variableManager  variable.Manager
	expressionEngine expression.Engine
}

// GetVariableManager 获取变量管理器
func (c *runtimeContextImpl) GetVariableManager() variable.Manager {
	return c.variableManager
}

// GetExpressionEngine 获取表达式引擎
func (c *runtimeContextImpl) GetExpressionEngine() expression.Engine {
	return c.expressionEngine
}

// Clone 克隆上下文
func (c *runtimeContextImpl) Clone() RuntimeContext {
	return &runtimeContextImpl{
		variableManager:  c.variableManager,
		expressionEngine: c.expressionEngine,
	}
}
