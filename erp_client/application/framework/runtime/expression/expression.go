package expression

// Engine 表达式引擎接口
type Engine interface {
	// Evaluate 评估表达式
	Evaluate(expr string, context map[string]interface{}) (interface{}, error)
	// ValidateExpression 验证表达式语法
	ValidateExpression(expr string) error
	// GetExpressionType 获取表达式返回类型
	GetExpressionType(expr string) (string, error)
}

// ExpressionType 表达式类型
type ExpressionType string

const (
	// TypeBoolean 布尔类型
	TypeBoolean ExpressionType = "boolean"
	// TypeNumber 数字类型
	TypeNumber ExpressionType = "number"
	// TypeString 字符串类型
	TypeString ExpressionType = "string"
	// TypeArray 数组类型
	TypeArray ExpressionType = "array"
	// TypeObject 对象类型
	TypeObject ExpressionType = "object"
	// TypeNull 空类型
	TypeNull ExpressionType = "null"
)
