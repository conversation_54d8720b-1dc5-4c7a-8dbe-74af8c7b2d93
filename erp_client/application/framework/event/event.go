package event

import (
	"context"
	"time"
)

// Event 事件接口
type Event interface {
	// GetEventID 获取事件ID
	GetEventID() string
	// GetEventType 获取事件类型
	GetEventType() string
	// GetEventTime 获取事件时间
	GetEventTime() time.Time
	// GetPayload 获取事件负载
	GetPayload() interface{}
	// GetMetadata 获取事件元数据
	GetMetadata() map[string]interface{}
}

// BaseEvent 基础事件实现
type BaseEvent struct {
	EventID   string                 // 事件ID
	EventType string                 // 事件类型
	EventTime time.Time              // 事件时间
	Payload   interface{}            // 事件负载
	Metadata  map[string]interface{} // 事件元数据
}

// GetEventID 获取事件ID
func (e *BaseEvent) GetEventID() string {
	return e.EventID
}

// GetEventType 获取事件类型
func (e *BaseEvent) GetEventType() string {
	return e.EventType
}

// GetEventTime 获取事件时间
func (e *BaseEvent) GetEventTime() time.Time {
	return e.EventTime
}

// GetPayload 获取事件负载
func (e *BaseEvent) GetPayload() interface{} {
	return e.Payload
}

// GetMetadata 获取事件元数据
func (e *BaseEvent) GetMetadata() map[string]interface{} {
	return e.Metadata
}

// EventHandler 事件处理器接口
type EventHandler interface {
	// HandleEvent 处理事件
	HandleEvent(ctx context.Context, event Event) error
}

// EventBus 事件总线接口
type EventBus interface {
	// PublishEvent 发布事件
	PublishEvent(ctx context.Context, event Event) error
	// SubscribeEvent 订阅事件
	SubscribeEvent(eventType string, handler EventHandler) error
	// UnsubscribeEvent 取消订阅事件
	UnsubscribeEvent(eventType string, handler EventHandler) error
	// HasSubscriber 检查是否有订阅者
	HasSubscriber(eventType string) bool
}

// EventOption 事件选项
type EventOption func(*BaseEvent)

// WithMetadata 设置事件元数据
func WithMetadata(metadata map[string]interface{}) EventOption {
	return func(e *BaseEvent) {
		e.Metadata = metadata
	}
}

// NewEvent 创建新事件
func NewEvent(eventType string, payload interface{}, opts ...EventOption) Event {
	event := &BaseEvent{
		EventID:   generateEventID(),
		EventType: eventType,
		EventTime: time.Now(),
		Payload:   payload,
		Metadata:  make(map[string]interface{}),
	}

	for _, opt := range opts {
		opt(event)
	}

	return event
}

// generateEventID 生成事件ID
func generateEventID() string {
	return time.Now().Format("20060102150405.000") + "-" + randomString(8)
}

// randomString 生成随机字符串
func randomString(n int) string {
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[time.Now().UnixNano()%int64(len(letters))]
	}
	return string(b)
}
