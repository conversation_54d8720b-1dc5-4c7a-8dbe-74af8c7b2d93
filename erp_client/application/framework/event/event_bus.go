package event

import (
	"context"
	"fmt"
	"sync"
)

// eventBus 事件总线实现
type eventBus struct {
	handlers sync.Map       // 事件处理器映射，key为事件类型，value为处理器列表
	mu       sync.RWMutex   // 读写锁
	buffer   chan func()    // 事件处理缓冲区
	wg       sync.WaitGroup // 等待组
}

// NewEventBus 创建事件总线
func NewEventBus() EventBus {
	bus := &eventBus{
		buffer: make(chan func(), 1000), // 设置缓冲区大小为1000
	}

	// 启动事件处理协程
	go bus.processEvents()

	return bus
}

// PublishEvent 发布事件
func (b *eventBus) PublishEvent(ctx context.Context, event Event) error {
	if !b.HasSubscriber(event.GetEventType()) {
		return nil // 没有订阅者，直接返回
	}

	// 将事件处理放入缓冲区
	b.buffer <- func() {
		if handlers, ok := b.handlers.Load(event.GetEventType()); ok {
			for _, h := range handlers.([]EventHandler) {
				h := h // 创建新的变量，避免闭包问题
				b.wg.Add(1)
				go func() {
					defer b.wg.Done()
					if err := h.HandleEvent(ctx, event); err != nil {
						// TODO: 处理错误，可以考虑添加错误处理器
						fmt.Printf("Error handling event %s: %v\n", event.GetEventID(), err)
					}
				}()
			}
		}
	}

	return nil
}

// SubscribeEvent 订阅事件
func (b *eventBus) SubscribeEvent(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	var handlers []EventHandler
	if h, ok := b.handlers.Load(eventType); ok {
		handlers = h.([]EventHandler)
		// 检查是否已经订阅
		for _, h := range handlers {
			if h == handler {
				return fmt.Errorf("handler already subscribed to event type %s", eventType)
			}
		}
	}

	handlers = append(handlers, handler)
	b.handlers.Store(eventType, handlers)
	return nil
}

// UnsubscribeEvent 取消订阅事件
func (b *eventBus) UnsubscribeEvent(eventType string, handler EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if h, ok := b.handlers.Load(eventType); ok {
		handlers := h.([]EventHandler)
		for i, h := range handlers {
			if h == handler {
				// 从切片中删除
				handlers = append(handlers[:i], handlers[i+1:]...)
				if len(handlers) == 0 {
					b.handlers.Delete(eventType)
				} else {
					b.handlers.Store(eventType, handlers)
				}
				return nil
			}
		}
	}
	return fmt.Errorf("handler not found for event type %s", eventType)
}

// HasSubscriber 检查是否有订阅者
func (b *eventBus) HasSubscriber(eventType string) bool {
	if handlers, ok := b.handlers.Load(eventType); ok {
		return len(handlers.([]EventHandler)) > 0
	}
	return false
}

// processEvents 处理事件
func (b *eventBus) processEvents() {
	for f := range b.buffer {
		f()         // 执行事件处理函数
		b.wg.Wait() // 等待所有处理器完成
	}
}

// Close 关闭事件总线
func (b *eventBus) Close() error {
	close(b.buffer)
	b.wg.Wait() // 等待所有事件处理完成
	return nil
}
