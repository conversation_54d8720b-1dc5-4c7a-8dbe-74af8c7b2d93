package metadata

import (
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/process/model"
)

// MetadataConverter 元数据转换器接口
type MetadataConverter interface {
	// ConvertToRuntimeMetadata 将YAML元数据转换为运行时元数据
	ConvertToRuntimeMetadata(yamlMetadata *types.Metadata) *variable.Metadata

	// ConvertToYAMLMetadata 将运行时元数据转换为YAML元数据
	ConvertToYAMLMetadata(runtimeMetadata *variable.Metadata) *types.Metadata

	// ConvertFields 转换字段定义
	ConvertFields(fields []model.Field) []*variable.FieldDefinition
}

// BaseMetadataConverter 基础元数据转换器实现
type BaseMetadataConverter struct{}

// NewBaseMetadataConverter 创建基础元数据转换器
func NewBaseMetadataConverter() *BaseMetadataConverter {
	return &BaseMetadataConverter{}
}

// ConvertToRuntimeMetadata 实现转换YAML元数据到运行时元数据
func (c *BaseMetadataConverter) ConvertToRuntimeMetadata(metadata *types.Metadata) *variable.Metadata {
	if metadata == nil {
		return nil
	}

	runtimeMetadata := &variable.Metadata{
		Input: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		Output: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		Context: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		System: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
	}

	// 转换输入元数据
	if metadata.Input != nil {
		for _, v := range metadata.Input.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       c.ConvertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.Input.Variables = append(runtimeMetadata.Input.Variables, def)
		}
	}

	// 转换输出元数据
	if metadata.Output != nil {
		for _, v := range metadata.Output.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       c.ConvertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.Output.Variables = append(runtimeMetadata.Output.Variables, def)
		}
	}

	// 转换上下文元数据
	if metadata.Context != nil {
		for _, v := range metadata.Context.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       c.ConvertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.Context.Variables = append(runtimeMetadata.Context.Variables, def)
		}
	}

	// 转换系统元数据
	if metadata.System != nil {
		for _, v := range metadata.System.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       c.ConvertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.System.Variables = append(runtimeMetadata.System.Variables, def)
		}
	}

	return runtimeMetadata
}

// ConvertToYAMLMetadata 实现转换运行时元数据到YAML元数据
func (c *BaseMetadataConverter) ConvertToYAMLMetadata(runtimeMetadata *variable.Metadata) *types.Metadata {
	if runtimeMetadata == nil {
		return nil
	}

	yamlMetadata := &types.Metadata{
		Input: &types.VariableSection{
			Variables: make([]types.Variable, 0),
		},
		Output: &types.VariableSection{
			Variables: make([]types.Variable, 0),
		},
		Context: &types.VariableSection{
			Variables: make([]types.Variable, 0),
		},
		System: &types.VariableSection{
			Variables: make([]types.Variable, 0),
		},
	}

	// 转换输入元数据
	if runtimeMetadata.Input != nil {
		for _, v := range runtimeMetadata.Input.Variables {
			yamlVar := types.Variable{
				Name:         v.Name,
				Required:     v.Required,
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			yamlMetadata.Input.Variables = append(yamlMetadata.Input.Variables, yamlVar)
		}
	}

	// 转换输出元数据
	if runtimeMetadata.Output != nil {
		for _, v := range runtimeMetadata.Output.Variables {
			yamlVar := types.Variable{
				Name:         v.Name,
				Required:     v.Required,
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			yamlMetadata.Output.Variables = append(yamlMetadata.Output.Variables, yamlVar)
		}
	}

	// 转换上下文元数据
	if runtimeMetadata.Context != nil {
		for _, v := range runtimeMetadata.Context.Variables {
			yamlVar := types.Variable{
				Name:         v.Name,
				Required:     v.Required,
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			yamlMetadata.Context.Variables = append(yamlMetadata.Context.Variables, yamlVar)
		}
	}

	// 转换系统元数据
	if runtimeMetadata.System != nil {
		for _, v := range runtimeMetadata.System.Variables {
			yamlVar := types.Variable{
				Name:         v.Name,
				Required:     v.Required,
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			yamlMetadata.System.Variables = append(yamlMetadata.System.Variables, yamlVar)
		}
	}

	return yamlMetadata
}

// ConvertFields 实现字段定义转换
func (c *BaseMetadataConverter) ConvertFields(fields []model.Field) []*variable.FieldDefinition {
	if fields == nil {
		return nil
	}
	result := make([]*variable.FieldDefinition, 0, len(fields))
	for _, f := range fields {
		field := &variable.FieldDefinition{
			Name:     f.Name,
			Required: f.Required,
		}
		result = append(result, field)
	}
	return result
}
