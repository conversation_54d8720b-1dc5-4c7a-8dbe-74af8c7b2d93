package transaction

import (
	"context"
	"fmt"
)

// TransactionStatus 事务状态
type TransactionStatus int

const (
	TransactionStatusActive TransactionStatus = iota
	TransactionStatusCommitted
	TransactionStatusRolledBack
	TransactionStatusFailed
)

// TransactionDefinition 事务定义
type TransactionDefinition struct {
	Timeout       int  // 超时时间(秒)
	ReadOnly      bool // 是否只读
	Isolation     int  // 隔离级别
	PropagationID int  // 传播行为
}

// DefaultTransactionDefinition 默认事务定义
var DefaultTransactionDefinition = TransactionDefinition{
	Timeout:   60,
	ReadOnly:  false,
	Isolation: 0, // 默认隔离级别
}

// TransactionCallback 事务回调
type TransactionCallback func(ctx context.Context) (interface{}, error)

// PlatformTransactionManager 平台事务管理器接口
type PlatformTransactionManager interface {
	// GetTransaction 获取事务
	GetTransaction(ctx context.Context, def TransactionDefinition) (context.Context, error)

	// Commit 提交事务
	Commit(ctx context.Context) error

	// Rollback 回滚事务
	Rollback(ctx context.Context) error

	// GetStatus 获取事务状态
	GetStatus(ctx context.Context) TransactionStatus
}

// TransactionTemplate 事务模板
type TransactionTemplate struct {
	transactionManager PlatformTransactionManager
	definition         TransactionDefinition
}

// NewTransactionTemplate 创建事务模板
func NewTransactionTemplate(
	transactionManager PlatformTransactionManager,
	definition TransactionDefinition,
) *TransactionTemplate {
	return &TransactionTemplate{
		transactionManager: transactionManager,
		definition:         definition,
	}
}

// Execute 执行事务
func (t *TransactionTemplate) Execute(ctx context.Context, callback TransactionCallback) (interface{}, error) {
	// 获取事务
	txCtx, err := t.transactionManager.GetTransaction(ctx, t.definition)
	if err != nil {
		return nil, fmt.Errorf("获取事务失败: %w", err)
	}

	var result interface{}
	var callbackErr error

	defer func() {
		// 处理panic
		if r := recover(); r != nil {
			_ = t.transactionManager.Rollback(txCtx)
			panic(r) // 重新抛出panic
		}

		// 处理错误
		if callbackErr != nil {
			_ = t.transactionManager.Rollback(txCtx)
		}
	}()

	// 执行业务逻辑
	result, callbackErr = callback(txCtx)

	// 提交事务
	if callbackErr == nil {
		if err := t.transactionManager.Commit(txCtx); err != nil {
			callbackErr = fmt.Errorf("提交事务失败: %w", err)
		}
	}

	return result, callbackErr
}
