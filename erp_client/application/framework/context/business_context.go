package context

import (
	"context"
	"sync"
)

// BusinessContext 业务上下文
type BusinessContext struct {
	ctx      context.Context
	values   map[string]interface{}
	mu       sync.RWMutex
	services map[string]interface{} // 领域服务映射
}

// NewBusinessContext 创建业务上下文
func NewBusinessContext(ctx context.Context) *BusinessContext {
	return &BusinessContext{
		ctx:      ctx,
		values:   make(map[string]interface{}),
		services: make(map[string]interface{}),
	}
}

// GetValue 获取上下文值
func (bc *BusinessContext) GetValue(key string) interface{} {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	return bc.values[key]
}

// SetValue 设置上下文值
func (bc *BusinessContext) SetValue(key string, value interface{}) {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	bc.values[key] = value
}

// GetContext 获取原始上下文
func (bc *BusinessContext) GetContext() context.Context {
	return bc.ctx
}

// GetString 获取字符串值
func (bc *BusinessContext) GetString(key string) string {
	if v, ok := bc.GetValue(key).(string); ok {
		return v
	}
	return ""
}

// GetInt64 获取int64值
func (bc *BusinessContext) GetInt64(key string) int64 {
	if v, ok := bc.GetValue(key).(int64); ok {
		return v
	}
	return 0
}

// RegisterService 注册领域服务
func (bc *BusinessContext) RegisterService(name string, service interface{}) {
	bc.mu.Lock()
	defer bc.mu.Unlock()
	bc.services[name] = service
}

// GetService 获取领域服务
func (bc *BusinessContext) GetService(name string) interface{} {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	return bc.services[name]
}

// GetServices 获取所有领域服务
func (bc *BusinessContext) GetServices() map[string]interface{} {
	bc.mu.RLock()
	defer bc.mu.RUnlock()
	services := make(map[string]interface{}, len(bc.services))
	for k, v := range bc.services {
		services[k] = v
	}
	return services
}

// Clone 克隆上下文
func (bc *BusinessContext) Clone() *BusinessContext {
	bc.mu.RLock()
	defer bc.mu.RUnlock()

	newCtx := &BusinessContext{
		ctx:      bc.ctx,
		values:   make(map[string]interface{}, len(bc.values)),
		services: make(map[string]interface{}, len(bc.services)),
	}

	// 复制值
	for k, v := range bc.values {
		newCtx.values[k] = v
	}

	// 复制服务引用
	for k, v := range bc.services {
		newCtx.services[k] = v
	}

	return newCtx
}
