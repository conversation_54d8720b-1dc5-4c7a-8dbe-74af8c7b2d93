package validate

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
	Code    string
}

func (e *ValidationError) Error() string {
	return e.Message
}

// NewValidationError 创建验证错误
func NewValidationError(field, message, code string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
		Code:    code,
	}
}

// ValidationRule 验证规则定义
type ValidationRule struct {
	Type         string                 `json:"type"`          // 验证类型
	Field        string                 `json:"field"`         // 字段名
	FieldName    string                 `json:"field_name"`    // 字段显示名称
	ErrorMessage string                 `json:"error_message"` // 错误信息
	ErrorCode    string                 `json:"error_code"`    // 错误码
	Params       map[string]interface{} `json:"params"`        // 验证参数
}

// ValidateService 验证服务接口
type ValidateService interface {
	// Validate 验证数据
	Validate(data interface{}, ruleNames []string) error

	// ValidateRule 验证单个规则
	ValidateRule(rule ValidationRule, value interface{}) error

	// ValidateRules 验证多个规则
	ValidateRules(rules []ValidationRule, data map[string]interface{}) error

	// 以下是具体的验证方法，供ValidateRule内部使用
	Required(value interface{}, fieldName string) error
	InEnum(value interface{}, enumValues []interface{}) error
	NumberRange(value float64, min, max *float64) error
	StringLength(value string, min, max *int) error
	Compare(value interface{}, operator string, target interface{}) error
	Exclusive(values map[string]interface{}) error
	DependsOn(value interface{}, dependsValue interface{}) error
	Pattern(value string, pattern string) error
}
