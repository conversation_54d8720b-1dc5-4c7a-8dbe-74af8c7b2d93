package validate

import (
	"testing"
)

func TestValidateService(t *testing.T) {
	service := NewValidateService(ValidationRules{})

	t.Run("Required", func(t *testing.T) {
		// 测试空值
		if err := service.Required(nil, "测试字段"); err == nil {
			t.Error("应该返回错误，但返回nil")
		}

		// 测试非空值
		if err := service.Required("非空", "测试字段"); err != nil {
			t.Error("不应该返回错误")
		}
	})

	t.Run("InEnum", func(t *testing.T) {
		enumValues := []interface{}{"A", "B", "C"}

		// 测试有效值
		if err := service.InEnum("A", enumValues); err != nil {
			t.Error("不应该返回错误")
		}

		// 测试无效值
		if err := service.InEnum("D", enumValues); err == nil {
			t.Error("应该返回错误，但返回nil")
		}
	})

	t.Run("NumberRange", func(t *testing.T) {
		min := float64(0)
		max := float64(100)

		// 测试范围内的值
		if err := service.NumberRange(50, &min, &max); err != nil {
			t.Error("不应该返回错误")
		}

		// 测试范围外的值
		if err := service.NumberRange(150, &min, &max); err == nil {
			t.Error("应该返回错误，但返回nil")
		}
	})

	t.Run("Compare", func(t *testing.T) {
		// 测试相等
		if err := service.Compare(100, "==", 100); err != nil {
			t.Error("不应该返回错误")
		}

		// 测试大于
		if err := service.Compare(150, ">", 100); err != nil {
			t.Error("不应该返回错误")
		}

		// 测试小于
		if err := service.Compare(50, "<", 100); err != nil {
			t.Error("不应该返回错误")
		}
	})

	t.Run("Exclusive", func(t *testing.T) {
		// 测试互斥（只有一个值）
		values := map[string]interface{}{
			"field1": "value1",
			"field2": nil,
			"field3": nil,
		}
		if err := service.Exclusive(values); err != nil {
			t.Error("不应该返回错误")
		}

		// 测试互斥（多个值）
		values = map[string]interface{}{
			"field1": "value1",
			"field2": "value2",
			"field3": nil,
		}
		if err := service.Exclusive(values); err == nil {
			t.Error("应该返回错误，但返回nil")
		}
	})

	t.Run("Pattern", func(t *testing.T) {
		// 测试有效的邮箱格式
		pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
		if err := service.Pattern("<EMAIL>", pattern); err != nil {
			t.Error("不应该返回错误")
		}

		// 测试无效的邮箱格式
		if err := service.Pattern("invalid-email", pattern); err == nil {
			t.Error("应该返回错误，但返回nil")
		}
	})
}

func TestValidateService_ValidateRule(t *testing.T) {
	service := NewValidateService(ValidationRules{})

	tests := []struct {
		name    string
		rule    ValidationRule
		value   interface{}
		wantErr bool
	}{
		{
			name: "测试必填验证-成功",
			rule: ValidationRule{
				Type:         "required",
				Field:        "input.reqDto.RoomId",
				FieldName:    "房间ID",
				ErrorMessage: "房间ID不能为空",
				ErrorCode:    "ROOM_ID_REQUIRED",
			},
			value:   "123",
			wantErr: false,
		},
		{
			name: "测试必填验证-失败",
			rule: ValidationRule{
				Type:         "required",
				Field:        "input.reqDto.RoomId",
				FieldName:    "房间ID",
				ErrorMessage: "房间ID不能为空",
				ErrorCode:    "ROOM_ID_REQUIRED",
			},
			value:   "",
			wantErr: true,
		},
		{
			name: "测试枚举验证-成功",
			rule: ValidationRule{
				Type:         "enum",
				Field:        "input.reqDto.Status",
				FieldName:    "状态",
				ErrorMessage: "状态值无效",
				ErrorCode:    "INVALID_STATUS",
				Params: map[string]interface{}{
					"values": []interface{}{"IDLE", "IN_USE", "MAINTENANCE"},
				},
			},
			value:   "IDLE",
			wantErr: false,
		},
		{
			name: "测试枚举验证-失败",
			rule: ValidationRule{
				Type:         "enum",
				Field:        "input.reqDto.Status",
				FieldName:    "状态",
				ErrorMessage: "状态值无效",
				ErrorCode:    "INVALID_STATUS",
				Params: map[string]interface{}{
					"values": []interface{}{"IDLE", "IN_USE", "MAINTENANCE"},
				},
			},
			value:   "INVALID",
			wantErr: true,
		},
		{
			name: "测试数值范围验证-成功",
			rule: ValidationRule{
				Type:         "number_range",
				Field:        "input.reqDto.Price",
				FieldName:    "价格",
				ErrorMessage: "价格必须在0-1000之间",
				ErrorCode:    "PRICE_OUT_OF_RANGE",
				Params: map[string]interface{}{
					"min": float64(0),
					"max": float64(1000),
				},
			},
			value:   float64(500),
			wantErr: false,
		},
		{
			name: "测试数值范围验证-失败",
			rule: ValidationRule{
				Type:         "number_range",
				Field:        "input.reqDto.Price",
				FieldName:    "价格",
				ErrorMessage: "价格必须在0-1000之间",
				ErrorCode:    "PRICE_OUT_OF_RANGE",
				Params: map[string]interface{}{
					"min": float64(0),
					"max": float64(1000),
				},
			},
			value:   float64(1500),
			wantErr: true,
		},
		{
			name: "测试字符串长度验证-成功",
			rule: ValidationRule{
				Type:         "string_length",
				Field:        "input.reqDto.Name",
				FieldName:    "名称",
				ErrorMessage: "名称长度必须在2-10个字符之间",
				ErrorCode:    "NAME_LENGTH_INVALID",
				Params: map[string]interface{}{
					"min": 2,
					"max": 10,
				},
			},
			value:   "测试房间",
			wantErr: false,
		},
		{
			name: "测试正则表达式验证-成功",
			rule: ValidationRule{
				Type:         "pattern",
				Field:        "input.reqDto.Phone",
				FieldName:    "手机号",
				ErrorMessage: "手机号格式不正确",
				ErrorCode:    "PHONE_FORMAT_INVALID",
				Params: map[string]interface{}{
					"pattern": "^1[3-9]\\d{9}$",
				},
			},
			value:   "13800138000",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateRule(tt.rule, tt.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateRule() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidateService_ValidateRules(t *testing.T) {
	service := NewValidateService(ValidationRules{})

	tests := []struct {
		name    string
		rules   []ValidationRule
		data    map[string]interface{}
		wantErr bool
	}{
		{
			name: "测试多规则验证-全部成功",
			rules: []ValidationRule{
				{
					Type:         "required",
					Field:        "input.reqDto.RoomId",
					FieldName:    "房间ID",
					ErrorMessage: "房间ID不能为空",
					ErrorCode:    "ROOM_ID_REQUIRED",
				},
				{
					Type:         "enum",
					Field:        "input.reqDto.Status",
					FieldName:    "状态",
					ErrorMessage: "状态值无效",
					ErrorCode:    "INVALID_STATUS",
					Params: map[string]interface{}{
						"values": []interface{}{"IDLE", "IN_USE", "MAINTENANCE"},
					},
				},
			},
			data: map[string]interface{}{
				"input.reqDto.RoomId": "123",
				"input.reqDto.Status": "IDLE",
			},
			wantErr: false,
		},
		{
			name: "测试多规则验证-部分失败",
			rules: []ValidationRule{
				{
					Type:         "required",
					Field:        "input.reqDto.RoomId",
					FieldName:    "房间ID",
					ErrorMessage: "房间ID不能为空",
					ErrorCode:    "ROOM_ID_REQUIRED",
				},
				{
					Type:         "enum",
					Field:        "input.reqDto.Status",
					FieldName:    "状态",
					ErrorMessage: "状态值无效",
					ErrorCode:    "INVALID_STATUS",
					Params: map[string]interface{}{
						"values": []interface{}{"IDLE", "IN_USE", "MAINTENANCE"},
					},
				},
			},
			data: map[string]interface{}{
				"input.reqDto.RoomId": "",
				"input.reqDto.Status": "INVALID",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateRules(tt.rules, tt.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateRules() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
