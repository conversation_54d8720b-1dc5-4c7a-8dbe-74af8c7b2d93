package validate

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
)

// ValidationRule 验证规则
type ValidationRule1 struct {
	Required  bool     `yaml:"required"`  // 是否必填
	MinLength int      `yaml:"minLength"` // 最小长度
	MaxLength int      `yaml:"maxLength"` // 最大长度
	Pattern   string   `yaml:"pattern"`   // 正则表达式
	Enum      []string `yaml:"enum"`      // 枚举值
	Rule      string   `yaml:"rule"`      // 自定义规则
	Message   string   `yaml:"message"`   // 错误消息
}

// ValidationRules 验证规则集合
type ValidationRules struct {
	Rules map[string]map[string][]ValidationRule1 `yaml:"rules"` // 规则集合
}

// ValidateServiceImpl 验证服务实现
type ValidateServiceImpl struct {
	rules ValidationRules // 验证规则
}

// NewValidateService 创建验证服务
func NewValidateService(rules ValidationRules) ValidateService {
	return &ValidateServiceImpl{
		rules: rules,
	}
}

// Validate 验证数据
func (s *ValidateServiceImpl) Validate(data interface{}, ruleNames []string) error {
	if data == nil {
		return fmt.Errorf("验证数据不能为空")
	}

	// 获取数据类型和值
	dataValue := reflect.ValueOf(data)
	if dataValue.Kind() == reflect.Ptr {
		dataValue = dataValue.Elem()
	}

	// 只支持结构体类型
	if dataValue.Kind() != reflect.Struct {
		return fmt.Errorf("验证数据必须是结构体类型")
	}

	// 遍历规则集合
	for _, ruleName := range ruleNames {
		ruleSet, ok := s.rules.Rules[ruleName]
		if !ok {
			return fmt.Errorf("规则集合 %s 不存在", ruleName)
		}

		// 验证自定义规则
		if customRules, ok := ruleSet["_custom"]; ok {
			for _, rule := range customRules {
				if err := s.validateCustomRule(rule, data); err != nil {
					return err
				}
			}
		}

		// 遍历结构体字段
		dataType := dataValue.Type()
		for i := 0; i < dataValue.NumField(); i++ {
			field := dataType.Field(i)
			fieldValue := dataValue.Field(i)

			// 获取字段名
			jsonTag := field.Tag.Get("json")
			fieldName := field.Name
			if jsonTag != "" {
				parts := strings.Split(jsonTag, ",")
				if parts[0] != "" {
					fieldName = parts[0]
				}
			}

			// 获取字段规则
			fieldRules, ok := ruleSet[fieldName]
			if !ok {
				continue
			}

			// 验证字段规则
			for _, rule := range fieldRules {
				if err := s.validateField(rule, fieldValue.Interface(), fieldName); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// validateField 验证字段
func (s *ValidateServiceImpl) validateField(rule ValidationRule1, value interface{}, fieldName string) error {
	// 转换为字符串
	strValue := ""
	if value != nil {
		strValue = fmt.Sprintf("%v", value)
	}

	// 验证必填
	if rule.Required && (value == nil || strValue == "") {
		return fmt.Errorf(rule.Message)
	}

	// 如果值为空且非必填，则跳过后续验证
	if value == nil || strValue == "" {
		return nil
	}

	// 验证最小长度
	if rule.MinLength > 0 && len(strValue) < rule.MinLength {
		return fmt.Errorf(rule.Message)
	}

	// 验证最大长度
	if rule.MaxLength > 0 && len(strValue) > rule.MaxLength {
		return fmt.Errorf(rule.Message)
	}

	// 验证正则表达式
	if rule.Pattern != "" {
		matched, err := regexp.MatchString(rule.Pattern, strValue)
		if err != nil {
			return fmt.Errorf("正则表达式错误: %s", err.Error())
		}
		if !matched {
			return fmt.Errorf(rule.Message)
		}
	}

	// 验证枚举值
	if len(rule.Enum) > 0 {
		found := false
		for _, enum := range rule.Enum {
			if enum == strValue {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf(rule.Message)
		}
	}

	return nil
}

// validateCustomRule 验证自定义规则
func (s *ValidateServiceImpl) validateCustomRule(rule ValidationRule1, data interface{}) error {
	if rule.Rule == "" {
		return nil
	}

	// 获取数据类型和值
	dataValue := reflect.ValueOf(data)
	if dataValue.Kind() == reflect.Ptr {
		dataValue = dataValue.Elem()
	}

	// 只支持结构体类型
	if dataValue.Kind() != reflect.Struct {
		return fmt.Errorf("验证数据必须是结构体类型")
	}

	// 解析规则
	// 目前只支持简单的 || 和 && 操作
	if strings.Contains(rule.Rule, "||") {
		// 或操作，有一个满足即可
		fields := strings.Split(rule.Rule, "||")
		for _, field := range fields {
			fieldName := strings.TrimSpace(field)
			fieldValue := s.getFieldValue(dataValue, fieldName)
			if fieldValue != nil && fmt.Sprintf("%v", fieldValue) != "" {
				return nil
			}
		}
		return fmt.Errorf(rule.Message)
	} else if strings.Contains(rule.Rule, "&&") {
		// 与操作，必须全部满足
		fields := strings.Split(rule.Rule, "&&")
		for _, field := range fields {
			fieldName := strings.TrimSpace(field)
			fieldValue := s.getFieldValue(dataValue, fieldName)
			if fieldValue == nil || fmt.Sprintf("%v", fieldValue) == "" {
				return fmt.Errorf(rule.Message)
			}
		}
	}

	return nil
}

// getFieldValue 获取字段值
func (s *ValidateServiceImpl) getFieldValue(dataValue reflect.Value, fieldName string) interface{} {
	dataType := dataValue.Type()
	for i := 0; i < dataValue.NumField(); i++ {
		field := dataType.Field(i)

		// 获取字段名
		jsonTag := field.Tag.Get("json")
		name := field.Name
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if parts[0] != "" {
				name = parts[0]
			}
		}

		if name == fieldName {
			return dataValue.Field(i).Interface()
		}
	}

	return nil
}

func (s *ValidateServiceImpl) Compare(value interface{}, operator string, target interface{}) error {
	return nil
}

func (s *ValidateServiceImpl) ValidateRule(rule ValidationRule, value interface{}) error {
	// TODO: 实现单个规则的验证逻辑
	return nil
}

func (s *ValidateServiceImpl) ValidateRules(rules []ValidationRule, data map[string]interface{}) error {
	// TODO: 实现多个规则的验证逻辑
	return nil
}

func (s *ValidateServiceImpl) Required(value interface{}, fieldName string) error {
	// TODO: 实现必填验证
	return nil
}

func (s *ValidateServiceImpl) InEnum(value interface{}, enumValues []interface{}) error {
	// TODO: 实现枚举值验证
	return nil
}

func (s *ValidateServiceImpl) NumberRange(value float64, min, max *float64) error {
	// TODO: 实现数字范围验证
	return nil
}

func (s *ValidateServiceImpl) StringLength(value string, min, max *int) error {
	// TODO: 实现字符串长度验证
	return nil
}

func (s *ValidateServiceImpl) Exclusive(values map[string]interface{}) error {
	// TODO: 实现互斥验证
	return nil
}

func (s *ValidateServiceImpl) DependsOn(value interface{}, dependsValue interface{}) error {
	// TODO: 实现依赖验证
	return nil
}

func (s *ValidateServiceImpl) Pattern(value string, pattern string) error {
	// TODO: 实现正则表达式验证
	return nil
}
