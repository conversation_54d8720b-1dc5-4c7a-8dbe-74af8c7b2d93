package adapter

import (
	"context"
	"fmt"
	"sync"
)

// AdapterManager 适配器管理器接口
type AdapterManager interface {
	// RegisterAdapter 注册适配器
	RegisterAdapter(adapter Adapter) error
	// UnregisterAdapter 注销适配器
	UnregisterAdapter(name string) error
	// GetAdapter 获取适配器
	GetAdapter(name string) (Adapter, error)
	// ListAdapters 获取所有适配器
	ListAdapters() []AdapterInfo
	// Start 启动所有适配器
	Start(ctx context.Context) error
	// Stop 停止所有适配器
	Stop(ctx context.Context) error
}

// adapterManager 适配器管理器实现
type adapterManager struct {
	adapters sync.Map // 适配器映射，key为适配器名称，value为Adapter接口
	mu       sync.RWMutex
}

// NewAdapterManager 创建适配器管理器
func NewAdapterManager() AdapterManager {
	return &adapterManager{}
}

// RegisterAdapter 注册适配器
func (m *adapterManager) RegisterAdapter(adapter Adapter) error {
	info := adapter.GetAdapterInfo()
	if _, loaded := m.adapters.LoadOrStore(info.Name, adapter); loaded {
		return fmt.Errorf("adapter %s already registered", info.Name)
	}
	return nil
}

// UnregisterAdapter 注销适配器
func (m *adapterManager) UnregisterAdapter(name string) error {
	if adapter, ok := m.adapters.LoadAndDelete(name); ok {
		// 如果适配器正在运行，先停止适配器
		if adapter.(Adapter).IsRunning() {
			if err := adapter.(Adapter).Stop(context.Background()); err != nil {
				return fmt.Errorf("failed to stop adapter %s: %v", name, err)
			}
		}
		return nil
	}
	return fmt.Errorf("adapter %s not found", name)
}

// GetAdapter 获取适配器
func (m *adapterManager) GetAdapter(name string) (Adapter, error) {
	if adapter, ok := m.adapters.Load(name); ok {
		return adapter.(Adapter), nil
	}
	return nil, fmt.Errorf("adapter %s not found", name)
}

// ListAdapters 获取所有适配器
func (m *adapterManager) ListAdapters() []AdapterInfo {
	var adapters []AdapterInfo
	m.adapters.Range(func(key, value interface{}) bool {
		adapters = append(adapters, value.(Adapter).GetAdapterInfo())
		return true
	})
	return adapters
}

// Start 启动所有适配器
func (m *adapterManager) Start(ctx context.Context) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)

	m.adapters.Range(func(key, value interface{}) bool {
		adapter := value.(Adapter)
		if !adapter.IsRunning() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := adapter.Start(ctx); err != nil {
					select {
					case errChan <- fmt.Errorf("failed to start adapter %s: %v", adapter.GetAdapterInfo().Name, err):
					default:
					}
				}
			}()
		}
		return true
	})

	// 等待所有适配器启动完成
	wg.Wait()

	// 检查是否有错误
	select {
	case err := <-errChan:
		return err
	default:
		return nil
	}
}

// Stop 停止所有适配器
func (m *adapterManager) Stop(ctx context.Context) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)

	m.adapters.Range(func(key, value interface{}) bool {
		adapter := value.(Adapter)
		if adapter.IsRunning() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				if err := adapter.Stop(ctx); err != nil {
					select {
					case errChan <- fmt.Errorf("failed to stop adapter %s: %v", adapter.GetAdapterInfo().Name, err):
					default:
					}
				}
			}()
		}
		return true
	})

	// 等待所有适配器停止完成
	wg.Wait()

	// 检查是否有错误
	select {
	case err := <-errChan:
		return err
	default:
		return nil
	}
}
