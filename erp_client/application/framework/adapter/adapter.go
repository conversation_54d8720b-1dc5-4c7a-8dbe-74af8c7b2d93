package adapter

import (
	"context"
	"fmt"
	"sync"
)

// AdapterType 适配器类型
type AdapterType string

const (
	AdapterTypeRPC     AdapterType = "RPC"     // RPC适配器
	AdapterTypeHTTP    AdapterType = "HTTP"    // HTTP适配器
	AdapterTypeGRPC    AdapterType = "GRPC"    // gRPC适配器
	AdapterTypeMessage AdapterType = "MESSAGE" // 消息适配器
	AdapterTypeDB      AdapterType = "DB"      // 数据库适配器
	AdapterTypeCache   AdapterType = "CACHE"   // 缓存适配器
)

// AdapterStatus 适配器状态
type AdapterStatus string

const (
	AdapterStatusInit    AdapterStatus = "INIT"    // 初始化
	AdapterStatusRunning AdapterStatus = "RUNNING" // 运行中
	AdapterStatusStopped AdapterStatus = "STOPPED" // 停止
	AdapterStatusError   AdapterStatus = "ERROR"   // 错误
)

// AdapterInfo 适配器信息
type AdapterInfo struct {
	Name        string                 // 适配器名称
	Type        AdapterType            // 适配器类型
	Version     string                 // 适配器版本
	Description string                 // 适配器描述
	Status      AdapterStatus          // 适配器状态
	Metadata    map[string]interface{} // 适配器元数据
}

// AdapterConfig 适配器配置
type AdapterConfig struct {
	Name        string                 // 适配器名称
	Type        AdapterType            // 适配器类型
	Version     string                 // 适配器版本
	Description string                 // 适配器描述
	Config      map[string]interface{} // 适配器配置
}

// Adapter 适配器接口
type Adapter interface {
	// GetAdapterInfo 获取适配器信息
	GetAdapterInfo() AdapterInfo
	// Configure 配置适配器
	Configure(config AdapterConfig) error
	// Start 启动适配器
	Start(ctx context.Context) error
	// Stop 停止适配器
	Stop(ctx context.Context) error
	// IsRunning 检查适配器是否运行中
	IsRunning() bool
	// Execute 执行适配器逻辑
	Execute(ctx context.Context, params map[string]interface{}) (interface{}, error)
}

// AdapterFactory 适配器工厂接口
type AdapterFactory interface {
	// Create 创建适配器
	Create(config AdapterConfig) (Adapter, error)
}

// AdapterOption 适配器选项
type AdapterOption func(*BaseAdapter)

// WithAdapterMetadata 设置适配器元数据
func WithAdapterMetadata(metadata map[string]interface{}) AdapterOption {
	return func(a *BaseAdapter) {
		a.info.Metadata = metadata
	}
}

// WithAdapterVersion 设置适配器版本
func WithAdapterVersion(version string) AdapterOption {
	return func(a *BaseAdapter) {
		a.info.Version = version
	}
}

// WithAdapterDescription 设置适配器描述
func WithAdapterDescription(description string) AdapterOption {
	return func(a *BaseAdapter) {
		a.info.Description = description
	}
}

// BaseAdapter 基础适配器实现
type BaseAdapter struct {
	mu     sync.RWMutex
	info   AdapterInfo
	status AdapterStatus
	config AdapterConfig
}

// NewBaseAdapter 创建基础适配器
func NewBaseAdapter(config AdapterConfig, opts ...AdapterOption) *BaseAdapter {
	a := &BaseAdapter{
		info: AdapterInfo{
			Name:     config.Name,
			Type:     config.Type,
			Status:   AdapterStatusInit,
			Metadata: make(map[string]interface{}),
		},
		config: config,
	}

	for _, opt := range opts {
		opt(a)
	}

	return a
}

// GetAdapterInfo 获取适配器信息
func (a *BaseAdapter) GetAdapterInfo() AdapterInfo {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.info
}

// Configure 配置适配器
func (a *BaseAdapter) Configure(config AdapterConfig) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if a.status == AdapterStatusRunning {
		return fmt.Errorf("adapter %s is running, cannot reconfigure", a.info.Name)
	}

	a.config = config
	return nil
}

// Start 启动适配器
func (a *BaseAdapter) Start(ctx context.Context) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if a.status == AdapterStatusRunning {
		return fmt.Errorf("adapter %s is already running", a.info.Name)
	}

	a.status = AdapterStatusRunning
	a.info.Status = AdapterStatusRunning
	return nil
}

// Stop 停止适配器
func (a *BaseAdapter) Stop(ctx context.Context) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	if a.status != AdapterStatusRunning {
		return fmt.Errorf("adapter %s is not running", a.info.Name)
	}

	a.status = AdapterStatusStopped
	a.info.Status = AdapterStatusStopped
	return nil
}

// IsRunning 检查适配器是否运行中
func (a *BaseAdapter) IsRunning() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.status == AdapterStatusRunning
}

// Execute 执行适配器逻辑
func (a *BaseAdapter) Execute(ctx context.Context, params map[string]interface{}) (interface{}, error) {
	// 子类应该重写此方法
	return nil, nil
}
