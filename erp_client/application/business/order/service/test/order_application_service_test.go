package test

import (
	"context"
	"testing"
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/api/vo"
	"voderpltvv/erp_client/config"
	testUtils "voderpltvv/test"

	"github.com/k0kubun/pp/v3"
)

// OrderService 定义测试所需的服务接口
type OrderService interface {
	OrderOpen(ctx context.Context, reqDto dto.AddOrderOpenReqDto) (*vo.SessionVO, error)
}

var (
	// 测试用例
	testCases = []struct {
		name     string
		reqDto   dto.AddOrderOpenReqDto
		validate func(t *testing.T, got *vo.SessionVO, err error, reqDto dto.AddOrderOpenReqDto)
	}{
		{
			name: "successful order open",
			reqDto: dto.AddOrderOpenReqDto{
				VenueId:    "105497",
				RoomId:     "da46c7c821e2410fb876b107a3f13ea6",
				StartTime:  1739416500,
				EndTime:    1739430900,
				PayAmount:  26800,
				EmployeeId: "73f60c762d0e4a089b04ec899f7af691",
			},
			validate: func(t *testing.T, got *vo.SessionVO, err error, reqDto dto.AddOrderOpenReqDto) {
				if err != nil {
					t.Errorf("OrderOpen() unexpected error = %v", err)
					return
				}
				if got == nil {
					t.Error("OrderOpen() returned nil result")
					return
				}
				if got.RoomId != reqDto.RoomId {
					t.Errorf("OrderOpen() RoomId = %v, want %v", got.RoomId, reqDto.RoomId)
				}
				// TODO: 添加更多字段的验证
				pp.Println("Test result:", got)
			},
		},
		{
			name: "room not found",
			reqDto: dto.AddOrderOpenReqDto{
				VenueId:    "105497",
				RoomId:     "non_existent_room",
				StartTime:  1739416500,
				EndTime:    1739430900,
				PayAmount:  26800,
				EmployeeId: "73f60c762d0e4a089b04ec899f7af691",
			},
			validate: func(t *testing.T, got *vo.SessionVO, err error, reqDto dto.AddOrderOpenReqDto) {
				if err == nil {
					t.Error("OrderOpen() expected error, got nil")
				}
			},
		},
	}
)

func TestOrderOpen(t *testing.T) {
	// 创建测试套件
	suite := testUtils.NewTestSuite(func(container *config.Container) OrderService {
		return container.GetOrderApplicationService().(OrderService)
	})

	// 使用套件的 Run 方法执行测试
	suite.Run(t, func(t *testing.T, s *testUtils.TestSuite[OrderService]) {
		// 遍历测试用例
		for _, tc := range testCases {
			// 使用套件的 RunTestCase 方法执行每个测试用例
			s.RunTestCase(t, tc.name, func(t *testing.T) {
				got, err := s.GetService().OrderOpen(context.Background(), tc.reqDto)
				tc.validate(t, got, err, tc.reqDto)
			})
		}
	})
}
