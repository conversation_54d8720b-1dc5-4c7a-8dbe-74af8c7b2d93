package service

import (
	"context"
	// "errors"
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	managentVo "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/util"
)

var payRecordTransfer = transfer.PayRecordTransfer{}

// V3OrderRefundView 退款视图
// 场景：退款视图，查看session下所有未付的订单
// 入参： order - 未支付的order，
// 发起支付
func (s *OrderApplicationServiceImpl) V3OrderRefundView(ctx context.Context, reqDto req.V3QueryOrderRefundViewReqDto) (vo.OrderRefundViewVO, error) {
	var defaultOrderRefundViewVO vo.OrderRefundViewVO = vo.OrderRefundViewVO{}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return defaultOrderRefundViewVO, fmt.Errorf("sessionId不能为空")
	}
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return defaultOrderRefundViewVO, fmt.Errorf("venueId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return defaultOrderRefundViewVO, fmt.Errorf("roomId不能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return defaultOrderRefundViewVO, fmt.Errorf("employeeId不能为空")
	}
	// 查询venue
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil {
		return defaultOrderRefundViewVO, fmt.Errorf("门店信息不存在")
	}
	// 查询场次
	session, err := s.sessionService.FindBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil || session.Id == nil || *session.Id == "" {
		return defaultOrderRefundViewVO, fmt.Errorf("场次信息不存在")
	}
	sessionVO := s.sessionService.ConvertToSessionVO(ctx, session)
	// 查询房间
	room, err := s.roomService.FindByID(ctx, *reqDto.RoomId)
	if err != nil || room == nil {
		return defaultOrderRefundViewVO, fmt.Errorf("房间信息不存在")
	}
	roomVO := s.roomService.ConvertToRoomVO(ctx, *room)
	venueVO := s.venueService.ConvertToVenueVO(ctx, *venue)
	// 查询员工
	_, err = s.employeeService.FindByID(ctx, *reqDto.EmployeeId)
	if err != nil {
		return defaultOrderRefundViewVO, fmt.Errorf("员工信息不存在")
	}

	// 查询订单信息
	orderPOInfoUnionVO := s.payService.GetOrderPOInfoBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)

	orderVOs := s.payService.CalcOrderHasMultiPayWay(ctx, orderPOInfoUnionVO)

	orderProductVOs := []vo.OrderProductVO{}
	for _, orderProduct := range orderPOInfoUnionVO.OrderProducts {
		orderProductVOs = append(orderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}

	orderRoomPlanVOs := []vo.OrderRoomPlanVO{}
	for _, orderRoomPlan := range orderPOInfoUnionVO.OrderRoomPlans {
		orderRoomPlanVOs = append(orderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}

	orderRefundViewVO := vo.OrderRefundViewVO{
		SessionVO:        &sessionVO,
		RoomVO:           &roomVO,
		VenueVO:          &venueVO,
		OrderVOs:         &orderVOs,
		OrderProductVOs:  &orderProductVOs,
		OrderRoomPlanVOs: &orderRoomPlanVOs,
	}
	return orderRefundViewVO, nil
}

// V3OrderRefundDo 退款
// 场景：退款
// 入参： 退款总金额，退款商品列表 id+quantity 商品包括已付和未付的，针对原订单生成退款订单
// 发起支付
func (s *OrderApplicationServiceImpl) V3OrderRefundDo(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto) ([]managentVo.OrderRefundInfoVO, error) {
	defaultOrderRefundVOs := []managentVo.OrderRefundInfoVO{}
	// 0.1 验证入参?存在
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("VenueId不能为空")
	}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("SessionId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("RoomId能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("EmployeeId不能为空")
	}
	if reqDto.OrderProductVOs == nil || len(*reqDto.OrderProductVOs) == 0 {
		return defaultOrderRefundVOs, fmt.Errorf("OrderProductVos不能为空")
	}
	if reqDto.RefundAmount == nil || *reqDto.RefundAmount < 0 {
		return defaultOrderRefundVOs, fmt.Errorf("RefundAmount不能为空")
	}
	if reqDto.RefundWayType == nil || !util.InList(*reqDto.RefundWayType, _const.REFUND_WAY_TYPES) {
		return defaultOrderRefundVOs, fmt.Errorf("请选择退款方式")
	}
	// 0.1 查询venue?存在
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return defaultOrderRefundVOs, fmt.Errorf("门店不存在")
	}
	// 0.2 查询场次?存在
	session, err := s.sessionService.FindBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil || session.Id == nil || *session.Id == "" {
		return defaultOrderRefundVOs, fmt.Errorf("场次不存在")
	}
	// 0.3 查询房间?存在
	room, err := s.roomService.FindByID(ctx, *reqDto.RoomId)
	if err != nil || room == nil || room.Id == nil || *room.Id == "" {
		return defaultOrderRefundVOs, fmt.Errorf("房间不存在")
	}
	// 0.4 查询员工?存在
	employee, err := s.employeeService.FindEmployeeByID(ctx, *reqDto.EmployeeId)
	if err != nil || employee.Id == nil || *employee.Id == "" {
		return defaultOrderRefundVOs, fmt.Errorf("员工不存在")
	}
	// 0.5 入参退款商品Id?存在？一致
	reqOpIds := []string{}
	for _, orderProduct := range *reqDto.OrderProductVOs {
		reqOpIds = append(reqOpIds, orderProduct.Id)
	}
	if len(reqOpIds) == 0 {
		return defaultOrderRefundVOs, fmt.Errorf("退款商品不能为空")
	}
	// 查询订单信息
	orderPOInfoUnionVO := s.payService.GetOrderPOInfoBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	// 验证传入的opid是否存在
	dbAllOpIds := []string{}
	for _, opTmp := range orderPOInfoUnionVO.OrderProducts {
		dbAllOpIds = append(dbAllOpIds, *opTmp.Id)
	}
	for _, reqOpIdn := range reqOpIds {
		if !util.InList(reqOpIdn, dbAllOpIds) {
			return defaultOrderRefundVOs, fmt.Errorf("退款商品不存在，请检查重新发起退款")
		}
	}
	// 0.6 查询session 是否有正在支付或退款的订单
	// for _, order := range orderPOInfoUnionVO.PayBills {
	// 	if *order.Status != _const.V2_PAY_BILL_STATUS_PAID {
	// 		return defaultOrderRefundVOs, fmt.Errorf("存在正在支付或退款的订单，请稍后重试")
	// 	}
	// }

	// 0.8 查询session下所有订单商品
	if len(orderPOInfoUnionVO.OrderProducts) == 0 {
		return defaultOrderRefundVOs, fmt.Errorf("订单商品不存在")
	}
	// 0.9 查询session下所有订单
	if len(orderPOInfoUnionVO.Orders) == 0 {
		return defaultOrderRefundVOs, fmt.Errorf("订单不存在")
	}
	// 计算订单是否存在多支付方式
	orderVOs := s.payService.CalcOrderHasMultiPayWay(ctx, orderPOInfoUnionVO)
	orderNOToOrderVOMap := make(map[string]vo.OrderVO)
	for _, orderVO := range orderVOs {
		orderNOToOrderVOMap[orderVO.OrderNo] = orderVO
	}
	// 现金退款时，检查订单是否存在多支付方式
	// 退款方式选择 cash: 全部现金退款, back: 原路返回
	refundWayType := *reqDto.RefundWayType
	if refundWayType == _const.REFUND_WAY_TYPE_CASH {
		for _, orderProductVO := range *reqDto.OrderProductVOs {
			orderVO := orderNOToOrderVOMap[orderProductVO.OrderNo]
			if orderVO.HasMultiPayWay {
				return defaultOrderRefundVOs, fmt.Errorf("商品订单存在多支付方式，只能选择原路返回方式退款")
			}
		}
	}
	orderVOInfoMergedVOs := s.payService.BuildOrderVOInfoMergedVOs(ctx, orderPOInfoUnionVO)
	// 0.9 计算现在商品是否可退
	for _, requestProduct := range *reqDto.OrderProductVOs {
		for _, v := range orderVOInfoMergedVOs {
			for _, kv := range v.MergedOrderProductVOs {
				if requestProduct.Id != kv.Id {
					continue
				}
				canRefundQuantity := kv.Quantity - kv.RefundCount
				if canRefundQuantity-requestProduct.Quantity < 0 {
					return defaultOrderRefundVOs, fmt.Errorf("商品数量不足")
				}
			}
		}
	}

	// 构建生成退款的订单
	refundOrderInfoGroupVOs := s.orderService.BuildRefundOrderAndOrderProduct(ctx, orderPOInfoUnionVO, *reqDto.OrderProductVOs)

	// 计算退款金额
	totalRefundAmount := int64(0)
	for _, group := range refundOrderInfoGroupVOs {
		for _, refundOrder := range group.OrderProductsRefund {
			if *group.POrder.Status == _const.ORDER_STATUS_PAID {
				totalRefundAmount += *refundOrder.PayAmount
			}
		}
	}
	if totalRefundAmount != *reqDto.RefundAmount {
		return defaultOrderRefundVOs, fmt.Errorf("退款金额不一致")
	}

	var refundInfos []managentVo.OrderRefundInfoVO

	if refundWayType == _const.REFUND_WAY_TYPE_CASH { // 全部现金退款
		refundInfos, err := s.payService.V3RefundByCash(ctx, reqDto, refundOrderInfoGroupVOs, session, *room, totalRefundAmount, orderPOInfoUnionVO)
		if err != nil {
			return defaultOrderRefundVOs, fmt.Errorf("退款失败")
		}
		return refundInfos, nil
	}
	// 原路返回
	refundInfos, err = s.payService.V3RefundByBack(ctx, reqDto, refundOrderInfoGroupVOs, session, *room, totalRefundAmount, orderPOInfoUnionVO)
	if err != nil {
		return defaultOrderRefundVOs, fmt.Errorf("退款失败")
	}
	if len(refundInfos) > 0 {
		// 13. 保存支付信息回调
		payRecordsLeshua := make([]po.PayRecord, 0)
		payRecordVosLeshua := make([]vo.PayRecordVO, 0)
		for _, refundInfo := range refundInfos {
			for _, payRecordVO := range refundInfo.PayRecordVOs {
				if payRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
					poTmp := payRecordTransfer.VoToPo(payRecordVO)
					payRecordsLeshua = append(payRecordsLeshua, poTmp)
					payRecordVosLeshua = append(payRecordVosLeshua, payRecordVO)
				}
			}
		}
		if len(payRecordsLeshua) > 0 {
			// 乐刷退款
			// 11.b.1 发起支付
			for _, payRecordVO := range payRecordVosLeshua {
				err := s.payService.TransferLeshuaRefund(ctx, payRecordVO)
				if err != nil {
					return defaultOrderRefundVOs, fmt.Errorf("发起支付失败: %w", err)
				}
			}
		}
	}
	return refundInfos, nil
}

// V3OrderRoomFeeRefundDo 退房费
// 场景：退房费
// 入参： 退款总金额，退房费列表 id+quantity 房费包括已付和未付的，针对原订单生成退款订单
// 发起支付
func (s *OrderApplicationServiceImpl) V3OrderRoomFeeRefundDo(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto) ([]managentVo.OrderRefundInfoVO, error) {
	defaultOrderRefundVOs := []managentVo.OrderRefundInfoVO{}

	// 0.1 验证入参?存在
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("VenueId不能为空")
	}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("SessionId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("RoomId能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return defaultOrderRefundVOs, fmt.Errorf("EmployeeId不能为空")
	}
	if reqDto.OrderNos == nil || len(*reqDto.OrderNos) == 0 {
		return defaultOrderRefundVOs, fmt.Errorf("OrderNos不能为空")
	}
	if reqDto.RefundAmount == nil || *reqDto.RefundAmount < 0 {
		return defaultOrderRefundVOs, fmt.Errorf("RefundAmount不能为空")
	}
	if reqDto.RefundWayType == nil || !util.InList(*reqDto.RefundWayType, _const.REFUND_WAY_TYPES) {
		return defaultOrderRefundVOs, fmt.Errorf("请选择退款方式")
	}

	// 0.1 查询venue?存在
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return defaultOrderRefundVOs, fmt.Errorf("门店不存在")
	}
	// 0.2 查询场次?存在
	session, err := s.sessionService.FindBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil || session.Id == nil || *session.Id == "" {
		return defaultOrderRefundVOs, fmt.Errorf("场次不存在")
	}
	// 0.3 查询房间?存在
	room, err := s.roomService.FindByID(ctx, *reqDto.RoomId)
	if err != nil || room == nil || room.Id == nil || *room.Id == "" {
		return defaultOrderRefundVOs, fmt.Errorf("房间不存在")
	}
	// 0.4 查询员工?存在
	employee, err := s.employeeService.FindEmployeeByID(ctx, *reqDto.EmployeeId)
	if err != nil || employee.Id == nil || *employee.Id == "" {
		return defaultOrderRefundVOs, fmt.Errorf("员工不存在")
	}

	// 查询订单信息
	orderPOInfoUnionVO := s.payService.GetOrderPOInfoBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	treeOrderVOs, orderNOToOrderVOMap := s.orderService.GetOrderTree(ctx, orderPOInfoUnionVO.Orders)
	// 验证是否已退款
	treeOrderNOToOrderVOMap := make(map[string]managentVo.OrderVO)
	for _, orderVO := range treeOrderVOs {
		treeOrderNOToOrderVOMap[orderVO.OrderNo] = orderVO
	}
	// 校验订单是否存在，是否为房费订单
	for _, orderNo := range *reqDto.OrderNos {
		orderVO, ok := orderNOToOrderVOMap[orderNo]
		if !ok {
			return defaultOrderRefundVOs, fmt.Errorf("订单不存在")
		}
		if orderVO.Direction != _const.V2_ORDER_DIRECTION_NORMAL {
			return defaultOrderRefundVOs, fmt.Errorf("订单不是房费订单")
		}
		treeOrderVO, ok := treeOrderNOToOrderVOMap[orderNo]
		if ok && len(treeOrderVO.RefundOrders) > 0 {
			return defaultOrderRefundVOs, fmt.Errorf("订单已退款")
		}
	}

	// 计算订单是否存在多支付方式
	calcOrderVOsHasMultiPayWay := s.payService.CalcOrderHasMultiPayWay(ctx, orderPOInfoUnionVO)
	calcOrderVOsHasMultiPayWayMap := make(map[string]vo.OrderVO)
	for _, orderVO := range calcOrderVOsHasMultiPayWay {
		calcOrderVOsHasMultiPayWayMap[orderVO.OrderNo] = orderVO
	}
	
	// 现金退款时，检查订单是否存在多支付方式
	// 退款方式选择 cash: 全部现金退款, back: 原路返回
	refundWayType := *reqDto.RefundWayType
	if refundWayType == _const.REFUND_WAY_TYPE_CASH {
		for _, orderNo := range *reqDto.OrderNos {
			orderVO, ok := calcOrderVOsHasMultiPayWayMap[orderNo]
			if ok && orderVO.HasMultiPayWay {
				return defaultOrderRefundVOs, fmt.Errorf("商品订单存在多支付方式，只能选择原路返回方式退款")
			}
		}
	}

	// 构建生成退款的订单
	refundOrderInfoGroupVOs := s.orderService.BuildRefundOrderAndOrderRoomPlan(ctx, orderPOInfoUnionVO, treeOrderVOs, *reqDto.OrderNos)

	// 检查包厢使用时间
	nowTime := util.TimeNowUnixInt64()
	for _, group := range refundOrderInfoGroupVOs {
		oms := group.POrderNormalOrderRoomPlans
		minStartTime := int64(0)
		maxEndTime := int64(0)
		for _, oms := range oms {
			if *oms.StartTime < minStartTime || minStartTime == 0 {
				minStartTime = *oms.StartTime
			}
			if *oms.EndTime > maxEndTime {
				maxEndTime = *oms.EndTime
			}
		}
		if nowTime > maxEndTime {
			return defaultOrderRefundVOs, fmt.Errorf("包厢使用时间已结束，无法退款")
		} else if nowTime > minStartTime {
			return defaultOrderRefundVOs, fmt.Errorf("包厢使用中，无法退款")
		}
	}

	// 计算退款金额
	totalRefundAmount := int64(0)
	for _, group := range refundOrderInfoGroupVOs {
		for _, refundOrder := range group.OrderRoomPlansRefund {
			if *group.POrder.Status == _const.ORDER_STATUS_PAID {
				totalRefundAmount += *refundOrder.PayAmount
			}
		}
	}
	if totalRefundAmount != *reqDto.RefundAmount {
		return defaultOrderRefundVOs, fmt.Errorf("退款金额不一致")
	}


	// 发起退款
	var refundInfos []managentVo.OrderRefundInfoVO

	if refundWayType == _const.REFUND_WAY_TYPE_CASH { // 全部现金退款
		refundInfos, err := s.payService.V3RefundByCashRoomPlan(ctx, reqDto, refundOrderInfoGroupVOs, session, *room, totalRefundAmount, orderPOInfoUnionVO)
		if err != nil {
			return defaultOrderRefundVOs, fmt.Errorf("退款失败")
		}
		return refundInfos, nil
	}
	// 原路返回
	refundInfos, err = s.payService.V3RefundByBackRoomPlan(ctx, reqDto, refundOrderInfoGroupVOs, session, *room, totalRefundAmount, orderPOInfoUnionVO)
	if err != nil {
		return defaultOrderRefundVOs, fmt.Errorf("退款失败")
	}
	if len(refundInfos) > 0 {
		// 13. 保存支付信息回调
		payRecordsLeshua := make([]po.PayRecord, 0)
		payRecordVosLeshua := make([]vo.PayRecordVO, 0)
		for _, refundInfo := range refundInfos {
			for _, payRecordVO := range refundInfo.PayRecordVOs {
				if payRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
					poTmp := payRecordTransfer.VoToPo(payRecordVO)
					payRecordsLeshua = append(payRecordsLeshua, poTmp)
					payRecordVosLeshua = append(payRecordVosLeshua, payRecordVO)
				}
			}
		}
		if len(payRecordsLeshua) > 0 {
			// 乐刷退款
			// 11.b.1 发起支付
			for _, payRecordVO := range payRecordVosLeshua {
				err := s.payService.TransferLeshuaRefund(ctx, payRecordVO)
				if err != nil {
					return defaultOrderRefundVOs, fmt.Errorf("发起支付失败: %w", err)
				}
			}
		}
	}

	return defaultOrderRefundVOs, nil
}
