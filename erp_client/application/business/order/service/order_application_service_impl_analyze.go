package service

import (
	"context"
	"fmt"
	"strconv"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// V3OrderQueryProductSales 商品销售统计
// 入参： om op
// 将om op 保存为 order
func (s *OrderApplicationServiceImpl) V3OrderQueryProductSales(ctx context.Context, reqDto req.V3OrderQueryProductSalesReqDto) ([]vo.OrderProductSalesVO, error) {
	// 1. 验证入参
	_, _, err := s.utilValidateBaseParamsV2(ctx, reqDto.VenueId, reqDto.EmployeeId)
	if err != nil {
		return nil, err
	}
	if reqDto.StartTime == nil {
		return nil, fmt.Errorf("startTime 不能为空")
	}
	if reqDto.EndTime == nil {
		return nil, fmt.Errorf("endTime 不能为空")
	}

	// 1. 查询账单
	modelBasePayBillVO, err := s.payBillService.FindModelBasePayBillVOsByTimeRange(ctx, *reqDto.VenueId, *reqDto.StartTime, *reqDto.EndTime)
	if err != nil {
		return nil, err
	}
	payBillVOs := s.payBillService.ConvertSrcVODataToPayBillVOs(ctx, modelBasePayBillVO)

	newOrderProductSalesVOs := s.payBillService.MergeOrderProductSalesVOs(ctx, payBillVOs)

	return newOrderProductSalesVOs, nil
}



// V3OrderQueryProductSales 商品销售统计
// 入参： om op
// 将om op 保存为 order
func (s *OrderApplicationServiceImpl) V3OrderQueryProductSales_BK(ctx context.Context, reqDto req.V3OrderQueryProductSalesReqDto) ([]vo.OrderProductSalesVO, error) {
	// 1. 验证入参
	_, _, err := s.utilValidateBaseParamsV2(ctx, reqDto.VenueId, reqDto.EmployeeId)
	if err != nil {
		return nil, err
	}
	if reqDto.StartTime == nil {
		return nil, fmt.Errorf("startTime 不能为空")
	}
	if reqDto.EndTime == nil {
		return nil, fmt.Errorf("endTime 不能为空")
	}

	// 1. 查询orderproduct, starttime, endtime employeeid
	orderProducts, err := s.orderproductService.FindOrderProductsTimeRange(ctx, *reqDto.VenueId, *reqDto.StartTime, *reqDto.EndTime)
	if err != nil {
		return nil, err
	}
	orderNos := []string{}
	for _, orderProduct := range orderProducts {
		orderNos = append(orderNos, *orderProduct.OrderNo)
	}
	orders, err := s.orderService.FindOrdersByOrderNos(ctx, *reqDto.VenueId, orderNos)
	if err != nil {
		return nil, err
	}
	// 2. 如果是套餐，则需要查询套餐内的商品
	newOrderProductVOs := s.orderproductService.ReBuildOrderProductVOs(ctx, orderProducts, orders)
	// 3. 根据producid 查询商品信息
	productIds := []string{}
	for _, orderProductVO := range newOrderProductVOs {
		productIds = append(productIds, orderProductVO.ProductId)
	}
	products, err := s.orderproductService.FindProductsByIds(ctx, *reqDto.VenueId, productIds)
	if err != nil {
		return nil, err
	}
	productIdMap := map[string]po.Product{}
	for _, product := range products {
		productIdMap[*product.Id] = product
	}
	// 4. 根据商品的categoryid 查询分类信息
	categoryIds := []string{}
	for _, product := range products {
		if product.Category != nil && *product.Category != "" {
			categoryIds = append(categoryIds, *product.Category)
		}
	}
	productTypes, err := s.orderproductService.FindCategorysByIds(ctx, *reqDto.VenueId, categoryIds)
	if err != nil {
		return nil, err
	}
	productTypeMap := map[string]po.ProductType{}
	for _, productType := range productTypes {
		productTypeMap[*productType.Id] = productType
	}
	newOrderProductSalesVOs := []vo.OrderProductSalesVO{}
	// productId+isgift -> orderProductVOs
	groupedOrderProductVOMap := map[string][]vo.OrderProductVO{}
	for _, orderProductVO := range newOrderProductVOs {
		key := orderProductVO.ProductId + "_" + strconv.FormatBool(orderProductVO.IsGift)
		groupedOrderProductVOMap[key] = append(groupedOrderProductVOMap[key], orderProductVO)
	}
	for _, orderProductVOs := range groupedOrderProductVOMap {
		// 同一个商品 支付/退款合并
		orderProductSalesVO := vo.OrderProductSalesVO{}
		for _, orderProductVO := range orderProductVOs {
			// 初始化相同的字段
			if orderProductSalesVO.ProductId == "" {
				orderProductSalesVO.VenueId = *reqDto.VenueId
				orderProductSalesVO.ProductId = orderProductVO.ProductId
				productTmp, ok := productIdMap[orderProductVO.ProductId]
				if ok {
					orderProductSalesVO.ProductName = *productTmp.Name
					orderProductSalesVO.CategoryId = *productTmp.Category
					productTypeTmp, ok := productTypeMap[*productTmp.Category]
					if ok {
						orderProductSalesVO.CategoryName = *productTypeTmp.Name
					}
				} else {
					// 跳过不存在的商品
					continue
				}
			}
			// 支付/退款合并
			if orderProductVO.IsGift {
				orderProductSalesVO.IsGift = true
			}
			direction := orderProductVO.OrderVO.Direction
			if direction == _const.V2_ORDER_DIRECTION_NORMAL {
				orderProductSalesVO.Quantity += orderProductVO.Quantity
				orderProductSalesVO.ShouldFee += orderProductVO.OriginalPrice * orderProductVO.Quantity
				orderProductSalesVO.TotalFee += orderProductVO.PayAmount
			} else {
				orderProductSalesVO.Quantity -= orderProductVO.Quantity
				orderProductSalesVO.ShouldFee -= orderProductVO.OriginalPrice * orderProductVO.Quantity
				orderProductSalesVO.TotalFee -= orderProductVO.PayAmount
			}
		}
		if orderProductSalesVO.TotalFee < 0 {
			// 跳过金额异常的
			continue
		}
		if orderProductSalesVO.Quantity <= 0 {
			// 跳过数量异常的
			continue
		}
		newOrderProductSalesVOs = append(newOrderProductSalesVOs, orderProductSalesVO)
	}

	return newOrderProductSalesVOs, nil
}
