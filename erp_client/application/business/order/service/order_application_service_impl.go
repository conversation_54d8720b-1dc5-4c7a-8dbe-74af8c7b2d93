package service

import (
	"context"
	"fmt"
	"io/ioutil"
	"sort"
	_const "voderpltvv/const"
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/api/vo"
	validateService "voderpltvv/erp_client/application/framework/validate"
	bookingService "voderpltvv/erp_client/domain/configuration/business/booking/service"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	ruleService "voderpltvv/erp_client/domain/rule/service"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	tradeService "voderpltvv/erp_client/domain/traderecord/service"
	roomplanService "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	orderproductService "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	pricePlanService "voderpltvv/erp_client/domain/valueobject/business/price_plan/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	roomTypeService "voderpltvv/erp_client/domain/valueobject/business/room_type/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	venuePaySettingService "voderpltvv/erp_client/domain/valueobject/business/venue_pay_setting/service"
	"voderpltvv/erp_managent/api/req"
	managentVo "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
)

// OrderApplicationServiceImpl 订单应用服务实现
type OrderApplicationServiceImpl struct {
	processEngine             model.Engine
	orderService              tradeService.OrderService
	bookingService            bookingService.BookingService
	roomService               roomService.Service
	ruleService               ruleService.Service
	validateService           validateService.ValidateService
	sessionService            sessionService.SessionService
	payService                tradeService.PayService
	pricePlanService          pricePlanService.Service
	roomplanService           roomplanService.Service
	orderproductService       orderproductService.Service
	venueService              venueService.VenueService
	employeeService           employeeService.Service
	payBillService            tradeService.PayBillService
	roomTypeService           roomTypeService.Service
	payRecordService          tradeService.PayRecordService
	memberRechargeBillService tradeService.MemberRechargeBillService
	memberCardConsumeService  tradeService.MemberCardConsumeService
	payCalcService            tradeService.PayCalcService
	memberCardService         tradeService.MemberCardService
	memberCardVenueService    tradeService.MemberCardVenueService
	venuePaySettingService    venuePaySettingService.VenuePaySettingService
}

// NewOrderApplicationService 创建订单应用服务实例
func NewOrderApplicationService(
	orderService tradeService.OrderService,
	bookingService bookingService.BookingService,
	roomService roomService.Service,
	ruleService ruleService.Service,
	validateService validateService.ValidateService,
	sessionService sessionService.SessionService,
	payService tradeService.PayService,
	pricePlanService pricePlanService.Service,
	roomplanService roomplanService.Service,
	orderproductService orderproductService.Service,
	venueService venueService.VenueService,
	employeeService employeeService.Service,
	payBillService tradeService.PayBillService,
	roomTypeService roomTypeService.Service,
	payRecordService tradeService.PayRecordService,
	memberRechargeBillService tradeService.MemberRechargeBillService,
	memberCardConsumeService tradeService.MemberCardConsumeService,
	payCalcService tradeService.PayCalcService,
	memberCardService tradeService.MemberCardService,
	memberCardVenueService tradeService.MemberCardVenueService,
	venuePaySettingService venuePaySettingService.VenuePaySettingService,
) OrderApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("orderService", orderService)
	engine.RegisterService("bookingService", bookingService)
	engine.RegisterService("roomService", roomService)
	engine.RegisterService("ruleService", ruleService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("sessionService", sessionService)
	engine.RegisterService("payService", payService)
	engine.RegisterService("pricePlanService", pricePlanService)
	engine.RegisterService("roomplanService", roomplanService)
	engine.RegisterService("orderproductService", orderproductService)
	engine.RegisterService("venueService", venueService)
	engine.RegisterService("employeeService", employeeService)
	engine.RegisterService("payBillService", payBillService)
	engine.RegisterService("roomTypeService", roomTypeService)
	engine.RegisterService("payRecordService", payRecordService)
	engine.RegisterService("memberRechargeBillService", memberRechargeBillService)
	engine.RegisterService("memberCardConsumeService", memberCardConsumeService)
	engine.RegisterService("payCalcService", payCalcService)
	engine.RegisterService("memberCardService", memberCardService)
	engine.RegisterService("memberCardVenueService", memberCardVenueService)
	engine.RegisterService("venuePaySettingService", venuePaySettingService)
	// 加载开台流程定义
	orderOpenProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_open_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载开台流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderOpenProcessContent); err != nil {
		panic(fmt.Errorf("解析开台流程定义失败: %w", err))
	}

	// 加载支付流程定义
	orderPayProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_pay_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载支付流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderPayProcessContent); err != nil {
		panic(fmt.Errorf("解析支付流程定义失败: %w", err))
	}

	return &OrderApplicationServiceImpl{
		processEngine:             engine,
		orderService:              orderService,
		bookingService:            bookingService,
		roomService:               roomService,
		ruleService:               ruleService,
		validateService:           validateService,
		sessionService:            sessionService,
		payService:                payService,
		pricePlanService:          pricePlanService,
		roomplanService:           roomplanService,
		orderproductService:       orderproductService,
		venueService:              venueService,
		employeeService:           employeeService,
		payBillService:            payBillService,
		roomTypeService:           roomTypeService,
		payRecordService:          payRecordService,
		memberRechargeBillService: memberRechargeBillService,
		memberCardConsumeService:  memberCardConsumeService,
		payCalcService:            payCalcService,
		memberCardService:         memberCardService,
		memberCardVenueService:    memberCardVenueService,
		venuePaySettingService:    venuePaySettingService,
	}
}

// OrderOpen 开台
func (s *OrderApplicationServiceImpl) OrderOpen(ctx context.Context, reqDto dto.AddOrderOpenReqDto) (*vo.SessionVO, error) {
	// 准备参数
	params := map[string]interface{}{
		"reqDto": reqDto,
	}

	// 执行流程
	result, err := s.processEngine.Execute(ctx, "order_open_process", params)
	if err != nil {
		return nil, fmt.Errorf("执行流程失败: %w", err)
	}

	// 转换并返回结果
	if sessionVO, exists := result["session"]; exists {
		return sessionVO.(*vo.SessionVO), nil
	}

	return nil, fmt.Errorf("获取会话对象失败")
}

// OrderPay 支付
func (s *OrderApplicationServiceImpl) OrderPay(ctx context.Context, reqDto req.QueryOrderPayReqDto) (*managentVo.PayResultVO, error) {
	// 准备参数
	params := map[string]interface{}{
		"reqDto": reqDto,
	}

	// 执行流程
	result, err := s.processEngine.Execute(ctx, "order_pay_process", params)
	if err != nil {
		return nil, fmt.Errorf("执行支付流程失败: %w", err)
	}

	// 转换并返回结果
	if payResult, exists := result["payResult"]; exists {
		return payResult.(*managentVo.PayResultVO), nil
	}

	return nil, fmt.Errorf("获取支付结果失败")
}

// ////////////////////////////////////////////////

// V3OrderOpen 开台-后付
// 入参： om op
// 将om op 保存为 order
func (s *OrderApplicationServiceImpl) V3OrderOpen(ctx context.Context, reqDto req.V3AddOrderOpenReqDto) (managentVo.SessionVO, error) {
	defaultSessionVO := managentVo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, _, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, nil, reqDto.EmployeeId, false)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 检查时间
	if reqDto.StartTime == nil || *reqDto.StartTime <= 0 {
		return defaultSessionVO, fmt.Errorf("开始时间不能小于0")
	}
	// 1.3 检查房间状态
	if err := util.CheckRoomStatusOrderOpen(*room.Status); err != nil {
		return defaultSessionVO, err
	}
	// 1.4 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultSessionVO, fmt.Errorf("房间已锁定,不能开台")
	}

	// 2. 设置最低消费
	lastMinimumCharge := int64(0)
	if reqDto.MinimumCharge != nil && *reqDto.MinimumCharge > 0 {
		lastMinimumCharge = *reqDto.MinimumCharge
	}
	isTimeConsume := false
	if len(reqDto.OrderRoomPlanVOS) > 0 {
		orderRoomPlanVO := reqDto.OrderRoomPlanVOS[0]
		if orderRoomPlanVO != nil && orderRoomPlanVO.IsTimeConsume {
			isTimeConsume = true
		}
	}

	orderVOs := make([]managentVo.OrderVO, 0)
	sessionId := util.GetSessionId(*reqDto.VenueId)

	// 3. 处理房费
	// 3.1. 创建Order对象-主开台订单
	roomOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrderForRoomPlan := &po.Order{
		VenueId:       venueId,
		RoomId:        roomId,
		SessionId:     &sessionId,
		OrderNo:       &roomOrderNo,
		EmployeeId:    reqDto.EmployeeId,
		MemberId:      reqDto.MemberId,
		MemberCardId:  reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		MinimumCharge: &lastMinimumCharge,
		Type:          util.Ptr(_const.V2_ORDER_TYPE_ROOMPLAN),
		Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING),
	}
	roomOrderVO := s.orderService.ConvertToOrderVO(ctx, *toAddOrderForRoomPlan)

	// 3.2. 构造OrderRoomPlans
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:            venueId,
			RoomId:             roomId,
			SessionId:          &sessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			MemberCardId:       reqDto.MemberCardId,
			MemberCardNumber:   reqDto.MemberCardNumber,
			OrderNo:            &roomOrderNo,
			RoomName:           util.Ptr(orderRoomPlanVO.RoomName),
			PricePlanId:        util.Ptr(orderRoomPlanVO.PricePlanId),
			PricePlanName:      util.Ptr(orderRoomPlanVO.PricePlanName),
			StartTime:          util.Ptr(orderRoomPlanVO.StartTime),
			EndTime:            util.Ptr(orderRoomPlanVO.EndTime),
			Duration:           util.Ptr(orderRoomPlanVO.Duration),
			SelectedAreaId:     reqDto.SelectedAreaId,
			SelectedRoomTypeId: reqDto.SelectedRoomTypeId,
			ConsumptionMode:    reqDto.ConsumptionMode,
			TimeChargeType:     reqDto.TimeChargeType,
			TimeChargeMode:     reqDto.TimeChargeMode,
			IsGift:             util.Ptr(false),
			OriginalPayAmount:  util.Ptr(orderRoomPlanVO.OriginalPayAmount),
			MinimumCharge:      &lastMinimumCharge,
			IsTimeConsume:      &isTimeConsume,
		}
		roomOrderVO.OrderRoomPlanVOs = append(roomOrderVO.OrderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderVOs = append(orderVOs, roomOrderVO)

	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *venueId, productIds, packageIds)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("获取商品信息失败: %w", err)
	}

	// 4. 处理商品
	// 4.1 商品信息-套餐内
	// 4.1.1 创建Order对象-套餐内订单
	subInOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productInOrderVO := managentVo.OrderVO{}
	if len(reqDto.InOrderProductInfos) > 0 {
		toAddOrderForInProduct := &po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     &sessionId,
			OrderNo:       &subInOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: &lastMinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING),
		}
		productInOrderVO = s.orderService.ConvertToOrderVO(ctx, *toAddOrderForInProduct)
	}
	// 4.1.2 处理套餐内商品
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           &sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subInOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_IN),
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productInOrderVO.OrderProductVOs = append(productInOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.InOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productInOrderVO)
	}

	// 4.2 商品信息-套餐外
	// 4.2.1 创建Order对象-套餐外订单
	subOutOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productOutOrderVO := managentVo.OrderVO{}
	if len(reqDto.OutOrderProductInfos) > 0 {
		productOutOrder := po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     &sessionId,
			OrderNo:       &subOutOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: &lastMinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING),
		}
		productOutOrderVO = s.orderService.ConvertToOrderVO(ctx, productOutOrder)
	}
	// 4.2.2 处理套餐外商品
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           &sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subOutOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productOutOrderVO.OrderProductVOs = append(productOutOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.OutOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productOutOrderVO)
	}

	// 5. 计算费用
	tmpSessionVO := managentVo.SessionVO{
		SessionId:  sessionId,
		MinConsume: lastMinimumCharge,
	}
	totalFeeThis, newOps, newOms, _, _ := s.payService.CalculateTotalPayment(ctx, tmpSessionVO, orderVOs, managentVo.PayBillVO{ForceMinimumCharge: true})

	// 6. 处理订单
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}
	for _, om := range newOms {
		omVO := s.roomplanService.ConvertToOrderRoomPlan(ctx, om)
		omNew := s.roomplanService.TrimIdToAddOrderRoomPlan(ctx, omVO)
		newOrderRoomPlans = append(newOrderRoomPlans, omNew)
	}

	// 7. 计算费用
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFee(ctx, totalFeeThis, newOps, newOms, sessionId, *reqDto.VenueId, lastMinimumCharge)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("计算费用失败: %w", err)
	}

	// 8. 处理订单
	toAddOrders := make([]po.Order, 0)
	for _, orderVO := range orderVOs {
		orderTmpVO := s.orderService.ConvertToOrderPO(ctx, orderVO)
		orderTmp := s.orderService.TrimIdToAddOrderPO(ctx, orderTmpVO)
		toAddOrders = append(toAddOrders, orderTmp)
	}

	// 9. 创建Session
	toAddsession := &po.Session{
		VenueId:            venueId,
		RoomId:             roomId,
		SessionId:          &sessionId,
		MemberId:           reqDto.MemberId,
		MemberCardId:       reqDto.MemberCardId,
		MemberCardNumber:   reqDto.MemberCardNumber,
		Status:             util.Ptr(_const.V2_SESSION_STATUS_OPENING),
		PayStatus:          util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
		IsTimeConsume:      &isTimeConsume,
		StartTime:          reqDto.StartTime,
		EndTime:            reqDto.EndTime,
		MinConsume:         &lastMinimumCharge,
		RoomFee:            &totalRoomFee,
		SupermarketFee:     &totalSupermarketFee,
		TotalFee:           &totalFee,
		UnpaidAmount:       &unpaidAmount,
		PaidAmount:         &paidAmount,
		IsOpenTableSettled: util.Ptr(false),
		EmployeeId:         reqDto.EmployeeId,
	}

	// 10. 更新房间状态
	toUpdateRoom := &po.Room{
		Id:        roomId,
		SessionId: &sessionId,
		Status:    util.Ptr(_const.ROOM_STATUS_IN_USE),
	}
	// 11. 保存订单信息
	_, _, _, _, err = s.orderService.SaveOrderOpenInfo(ctx, toAddOrders, newOrderProducts, newOrderRoomPlans, *toAddsession, *toUpdateRoom)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("保存订单信息失败: %w", err)
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, &venue, _const.V2_ORDER_DIRECTION_NORMAL, newOrderProducts)
	// 12. 更新预订状态
	if reqDto.BookingId != nil && *reqDto.BookingId != "" {
		err = s.bookingService.BookingSuccessInOpen(ctx, *reqDto.BookingId, 1)
		if err != nil {
			logrus.Error("更新预订状态失败:" + err.Error())
		}
	}
	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, *toAddsession)
	for _, orderVO := range orderVOs {
		newSessionVO.RtOrderNos = append(newSessionVO.RtOrderNos, orderVO.OrderNo)
	}
	// 13. 记录最后一个房间操作
	err = s.orderService.RecordLastRoomOperation(ctx, *venueId, *roomId, sessionId, *reqDto.EmployeeId, _const.V2_ROOM_OPERATION_TYPE_OPENING, "开台")
	if err != nil {
		logrus.Error("记录最后一个房间操作失败:" + err.Error())
	}

	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return newSessionVO, nil
}

// V3OrderOpenPay 开台-立结
// 入参： om op
// 将om op 保存为 order
// 发起支付
func (s *OrderApplicationServiceImpl) V3OrderOpenPay(ctx context.Context, reqDto req.V3AddOrderOpenPayReqDto) (managentVo.SessionVO, error) {
	defaultSessionVO := managentVo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, _, employee, err := s.utilValidateBaseParams(ctx, venueId, roomId, nil, reqDto.EmployeeId, false)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 验证支付方式是否开通
	if err = s.utilValidateBaseParamsPay(ctx, venue, reqDto.PayRecords); err != nil {
		return defaultSessionVO, err
	}
	// 1.2. 验证登录会员卡ID
	_, err = s.utilValidateMemberCard(ctx, venue, reqDto.MemberCardId)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 检查时间
	if reqDto.StartTime == nil || *reqDto.StartTime <= 0 {
		return defaultSessionVO, fmt.Errorf("开始时间不能小于0")
	}
	// 1.3 检查房间状态
	if err := util.CheckRoomStatusOrderOpen(*room.Status); err != nil {
		return defaultSessionVO, err
	}
	// 1.4 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultSessionVO, fmt.Errorf("房间已锁定,不能开台")
	}
	isTimeConsume := false
	if len(reqDto.OrderRoomPlanVOS) > 0 {
		orderRoomPlanVO := reqDto.OrderRoomPlanVOS[0]
		if orderRoomPlanVO != nil && orderRoomPlanVO.IsTimeConsume {
			isTimeConsume = true
		}
	}

	// 1.6 验证支付方式
	err = s.utilValidatePayFeeInfo(ctx, reqDto.OriginalFee, reqDto.ShouldFee, reqDto.TotalFee, reqDto.ZeroFee, reqDto.ChangeAmount, reqDto.PayRecords, true)
	if err != nil {
		return defaultSessionVO, err
	}

	// 2. 设置最低消费
	lastMinimumCharge := int64(0)
	if reqDto.MinimumCharge != nil && *reqDto.MinimumCharge > 0 {
		lastMinimumCharge = *reqDto.MinimumCharge
	}

	orderVOs := make([]managentVo.OrderVO, 0)
	sessionId := util.GetSessionId(*reqDto.VenueId)

	// 3. 处理房费
	// 3.1 创建Order对象-主开台订单
	roomOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrderForRoomPlan := &po.Order{
		VenueId:       venueId,
		RoomId:        roomId,
		SessionId:     &sessionId,
		OrderNo:       &roomOrderNo,
		EmployeeId:    reqDto.EmployeeId,
		MemberId:      reqDto.MemberId,
		MemberCardId:  reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		MinimumCharge: reqDto.MinimumCharge,
		Type:          util.Ptr(_const.V2_ORDER_TYPE_ROOMPLAN),
		Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING),
	}
	roomOrderVO := s.orderService.ConvertToOrderVO(ctx, *toAddOrderForRoomPlan)

	// 3.2. 构造OrderRoomPlans
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:            venueId,
			RoomId:             roomId,
			SessionId:          &sessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			MemberCardId:       reqDto.MemberCardId,
			MemberCardNumber:   reqDto.MemberCardNumber,
			OrderNo:            &roomOrderNo,
			RoomName:           util.Ptr(orderRoomPlanVO.RoomName),
			PricePlanId:        util.Ptr(orderRoomPlanVO.PricePlanId),
			PricePlanName:      util.Ptr(orderRoomPlanVO.PricePlanName),
			StartTime:          util.Ptr(orderRoomPlanVO.StartTime),
			EndTime:            util.Ptr(orderRoomPlanVO.EndTime),
			Duration:           util.Ptr(orderRoomPlanVO.Duration),
			SelectedAreaId:     reqDto.SelectedAreaId,
			SelectedRoomTypeId: reqDto.SelectedRoomTypeId,
			ConsumptionMode:    reqDto.ConsumptionMode,
			TimeChargeType:     reqDto.TimeChargeType,
			TimeChargeMode:     reqDto.TimeChargeMode,
			IsGift:             util.Ptr(false),
			IsFree:             reqDto.IsFree,
			OriginalPayAmount:  util.Ptr(orderRoomPlanVO.OriginalPayAmount),
			MinimumCharge:      &lastMinimumCharge,
			IsTimeConsume:      &isTimeConsume,
		}
		roomOrderVO.OrderRoomPlanVOs = append(roomOrderVO.OrderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderVOs = append(orderVOs, roomOrderVO)

	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *venueId, productIds, packageIds)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("获取商品信息失败: %w", err)
	}

	// 4. 处理商品
	// 4.1 商品信息-套餐内
	// 4.1.1 创建Order对象-套餐内订单
	subInOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productInOrderVO := managentVo.OrderVO{}
	if len(reqDto.InOrderProductInfos) > 0 {
		toAddOrderForInProduct := &po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     &sessionId,
			OrderNo:       &subInOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: &lastMinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING),
		}
		productInOrderVO = s.orderService.ConvertToOrderVO(ctx, *toAddOrderForInProduct)
	}
	// 4.1.2 处理套餐内商品
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           &sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subInOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_IN),
			IsFree:              reqDto.IsFree,
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productInOrderVO.OrderProductVOs = append(productInOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.InOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productInOrderVO)
	}

	// 4.2 商品信息-套餐外
	// 4.2.1 创建Order对象-套餐外订单
	subOutOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productOutOrderVO := managentVo.OrderVO{}
	if len(reqDto.OutOrderProductInfos) > 0 {
		productOutOrder := po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     &sessionId,
			OrderNo:       &subOutOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: &lastMinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING),
		}
		productOutOrderVO = s.orderService.ConvertToOrderVO(ctx, productOutOrder)
	}
	// 4.2.2 处理套餐外商品
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           &sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subOutOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsFree:              reqDto.IsFree,
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productOutOrderVO.OrderProductVOs = append(productOutOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.OutOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productOutOrderVO)
	}

	// 5. 计算费用
	tmpPayBillVO := managentVo.PayBillVO{
		ProductDiscount:       util.GetPtrSafeDefault(reqDto.ProductDiscount, 100),
		RoomDiscount:          util.GetPtrSafeDefault(reqDto.RoomDiscount, 100),
		IsFree:                util.GetPtrSafeDefault(reqDto.IsFree, false),
		ProductDiscountAmount: util.GetPtrSafeDefault(reqDto.ProductDiscountAmount, 0),
		RoomDiscountAmount:    util.GetPtrSafeDefault(reqDto.RoomDiscountAmount, 0),
		ForceMinimumCharge:    true,
	}
	tmpSessionVO := managentVo.SessionVO{
		SessionId:  sessionId,
		MinConsume: lastMinimumCharge,
	}
	totalFeeThis, newOps, newOms, retProductDiscountFee, retRoomDiscountFee := s.payService.CalculateTotalPayment(ctx, tmpSessionVO, orderVOs, tmpPayBillVO)

	// 6. 处理订单
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}
	for _, om := range newOms {
		omVO := s.roomplanService.ConvertToOrderRoomPlan(ctx, om)
		omNew := s.roomplanService.TrimIdToAddOrderRoomPlan(ctx, omVO)
		newOrderRoomPlans = append(newOrderRoomPlans, omNew)
	}

	// 7. 计算费用
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFee(ctx, totalFeeThis, newOps, newOms, sessionId, *reqDto.VenueId, lastMinimumCharge)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("计算费用失败: %w", err)
	}

	// 8. 处理订单
	toAddOrders := make([]po.Order, 0)
	for _, orderVO := range orderVOs {
		orderTmpVO := s.orderService.ConvertToOrderPO(ctx, orderVO)
		orderTmp := s.orderService.TrimIdToAddOrderPO(ctx, orderTmpVO)
		toAddOrders = append(toAddOrders, orderTmp)
	}

	// 8.a. 设置收款单信息
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:               reqDto.VenueId,
		RoomId:                reqDto.RoomId,
		EmployeeId:            reqDto.EmployeeId,
		MemberId:              reqDto.MemberId,
		MemberCardId:          reqDto.MemberCardId,
		MemberCardNumber:      reqDto.MemberCardNumber,
		SessionId:             &sessionId,
		BillId:                &billId,
		OriginalFee:           reqDto.OriginalFee,
		TotalFee:              reqDto.TotalFee,
		ShouldFee:             reqDto.ShouldFee,
		ZeroFee:               reqDto.ZeroFee,
		CreditAmount:          reqDto.CreditAmount,
		ProductDiscount:       reqDto.ProductDiscount,
		ProductDiscountFee:    &retProductDiscountFee,
		RoomDiscount:          reqDto.RoomDiscount,
		RoomDiscountFee:       &retRoomDiscountFee,
		IsFree:                reqDto.IsFree,
		ProductDiscountAmount: reqDto.ProductDiscountAmount,
		RoomDiscountAmount:    reqDto.RoomDiscountAmount,
		ChangeAmount:          reqDto.ChangeAmount,
		Status:                util.Ptr(_const.V2_PAY_BILL_STATUS_WAIT),
		Direction:             util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
		DiscountReason:        reqDto.DiscountReason,
	}
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	// 8.b. 设置订单支付记录信息
	for _, orderVO := range orderVOs {
		orderNoTmp := orderVO.OrderNo
		// 剔除计时支付订单
		if isTimeConsume && orderNoTmp == roomOrderNo {
			continue
		}
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   &orderNoTmp,
			SessionId: &sessionId,
			BillId:    &billId,
		})
	}
	// 8.c. 设置订单支付记录信息
	toAddPayRecords := make([]po.PayRecord, 0)
	payRecordAllPayAmount := int64(0)
	for _, payRecord := range *reqDto.PayRecords {
		payId := util.GetPayId(*reqDto.VenueId)
		totalFeeTmp := payRecord.TotalFee
		payTypeTmp := payRecord.PayType
		toAddPayRecords = append(toAddPayRecords, po.PayRecord{
			VenueId:                 reqDto.VenueId,
			RoomId:                  reqDto.RoomId,
			EmployeeId:              reqDto.EmployeeId,
			MemberId:                reqDto.MemberId,
			MemberCardId:            &payRecord.MemberCardId,
			MemberCardNumber:        reqDto.MemberCardNumber,
			SessionId:               &sessionId,
			BillId:                  &billId,
			PayId:                   &payId,
			TotalFee:                &totalFeeTmp,
			PrincipalAmount:         &payRecord.PrincipalAmount,
			MemberRoomBonusAmount:   &payRecord.MemberRoomBonusAmount,
			MemberGoodsBonusAmount:  &payRecord.MemberGoodsBonusAmount,
			MemberCommonBonusAmount: &payRecord.MemberCommonBonusAmount,
			Status:                  util.Ptr(_const.PAY_STATUS_UNPAID),
			PayType:                 &payTypeTmp,
			ProductName:             util.GetPayProductName(venue.Name, venue.Id),
			BQROneCode:              &payRecord.BQROneCode,
		})
		payRecordAllPayAmount += totalFeeTmp
	}
	if _, msg, err := util.FeeDiff(payRecordAllPayAmount, *reqDto.TotalFee); err != nil {
		return defaultSessionVO, fmt.Errorf("应付金额与支付记录金额不一致: %s", msg)
	}

	if _, msg, err := util.FeeDiff(totalFeeThis, *reqDto.TotalFee+util.GetPtrSafeDefault(reqDto.ZeroFee, 0)-util.GetPtrSafeDefault(reqDto.ChangeAmount, 0)); err != nil {
		return defaultSessionVO, fmt.Errorf("订单金额与计算订单金额不一致: %s", msg)
	}
	// 9. 创建Session
	toAddsession := &po.Session{
		VenueId:            venueId,
		RoomId:             roomId,
		SessionId:          &sessionId,
		MemberId:           reqDto.MemberId,
		MemberCardId:       reqDto.MemberCardId,
		MemberCardNumber:   reqDto.MemberCardNumber,
		Status:             util.Ptr(_const.V2_SESSION_STATUS_OPENING),
		PayStatus:          util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
		StartTime:          reqDto.StartTime,
		EndTime:            reqDto.EndTime,
		MinConsume:         &lastMinimumCharge,
		RoomFee:            &totalRoomFee,
		SupermarketFee:     &totalSupermarketFee,
		TotalFee:           &totalFee,
		UnpaidAmount:       &unpaidAmount,
		PaidAmount:         &paidAmount,
		IsOpenTableSettled: util.Ptr(false),
		IsTimeConsume:      &isTimeConsume,
		EmployeeId:         reqDto.EmployeeId,
	}

	// 10. 更新房间状态
	toUpdateRoom := &po.Room{
		Id:        roomId,
		SessionId: &sessionId,
		Status:    util.Ptr(_const.ROOM_STATUS_IN_USE),
	}
	// 1.3. 验证会员卡ID
	for _, payRecordVO := range *reqDto.PayRecords {
		if payRecordVO.PayType == _const.PAY_TYPE_MEMBER_CARD {
			roomFee := int64(0)
			supermarketFee := int64(0)
			for _, orderProductVO := range newOrderProducts {
				supermarketFee += *orderProductVO.PayAmount
			}
			for _, orderRoomPlanVO := range newOrderRoomPlans {
				roomFee += *orderRoomPlanVO.PayAmount
			}
			// 1.2.1. 验证会员卡ID-扣款的
			_, err := s.utilValidateMemberCard(ctx, venue, &payRecordVO.MemberCardId)
			if err != nil {
				return defaultSessionVO, err
			}
			_, err = s.memberRechargeBillService.V3RPCMemberCardVaildBalance(ctx, req.V3QueryMemberCardQueryBalanceReqDto{
				MemberCardId:      &payRecordVO.MemberCardId,
				TotalAmount:       &payRecordVO.TotalFee,
				PrincipalAmount:   &payRecordVO.PrincipalAmount,
				RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
				GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
				CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
				RoomTotalAmount:   &roomFee,
				GoodsTotalAmount:  &supermarketFee,
			})
			if err != nil {
				return defaultSessionVO, err
			}
		}
	}
	// 11.1 优先从会员卡中扣款
	for _, payRecord := range toAddPayRecords {
		payRecordVO := s.payRecordService.ConvertToPayRecordVO(ctx, payRecord)
		// 跳过非会员卡支付
		if payRecordVO.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 检查会员卡余额
		_, err := s.memberRechargeBillService.V3RPCMemberCardPay(ctx, req.V3RPCPayMoneyReqDto{
			VenueId:           reqDto.VenueId,
			EmployeeId:        reqDto.EmployeeId,
			MemberCardId:      &payRecordVO.MemberCardId,
			Amount:            &payRecordVO.PrincipalAmount,
			RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
			GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
			CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
			PayId:             &payRecordVO.PayId,
		})
		if err != nil {
			return defaultSessionVO, fmt.Errorf("会员卡扣款失败: %w", err)
		}
		// 发送会员卡消费短信
		s.orderService.SendSmsMemberCardConsume(ctx, &venue, payRecordVO.MemberCardId, payRecordVO.PrincipalAmount, payRecordVO.MemberRoomBonusAmount, payRecordVO.MemberGoodsBonusAmount, payRecordVO.MemberCommonBonusAmount)
	}
	newSession, _, _, _, err := s.orderService.SaveOrderOpenPayInfo(ctx, toAddOrders, newOrderProducts, newOrderRoomPlans, *toAddsession, *toUpdateRoom, toAddPayBill, toAddOrderAndPays, toAddPayRecords)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("保存订单信息失败: %w", err)
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, &venue, _const.V2_ORDER_DIRECTION_NORMAL, newOrderProducts)
	
	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, newSession)

	// 11.1 保存会员卡消费记录
	s.memberCardConsumeService.RecordMemberCardConsume(ctx, newSession, venue, room, employee, toAddPayRecords, toAddPayBill)

	// 11.b.1 发起支付
	payResultVOs, err := s.payService.V3TransformPayGate(ctx, &req.V3QueryOrderPayTransformReqDto{
		PayRecords:    reqDto.PayRecords,
		NewPayRecords: toAddPayRecords,
		VenueId:       reqDto.VenueId,
		RoomId:        reqDto.RoomId,
		SessionId:     reqDto.SessionId,
		EmployeeId:    reqDto.EmployeeId,
	}, &toAddPayBill)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("发起支付失败: %w", err)
	}

	// 12. 保存支付信息回调
	for _, payRecord := range toAddPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, managentVo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_COMMON})
		if err != nil {
			return defaultSessionVO, fmt.Errorf("保存支付信息失败: %w", err)
		}
	}
	newSessionVO.PayResultVOs = payResultVOs

	for _, orderVO := range orderVOs {
		newSessionVO.RtOrderNos = append(newSessionVO.RtOrderNos, orderVO.OrderNo)
	}
	// 12. 更新预订状态
	if reqDto.BookingId != nil && *reqDto.BookingId != "" {
		err = s.bookingService.BookingSuccessInOpen(ctx, *reqDto.BookingId, 1)
		if err != nil {
			logrus.Error("更新预订状态失败:" + err.Error())
		}
	}
	newSessionVO.PayBills = []managentVo.PayBillVO{s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)}
	// 13. 记录最后一个房间操作
	err = s.orderService.RecordLastRoomOperation(ctx, *venueId, *roomId, sessionId, *reqDto.EmployeeId, _const.V2_ROOM_OPERATION_TYPE_OPENING, "开台立结")
	if err != nil {
		logrus.Error("记录最后一个房间操作失败:" + err.Error())
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, false, toAddPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return newSessionVO, nil
}

// V3AdditionalOrder 点单-后付
// 入参： op
// 将op 保存为 order
func (s *OrderApplicationServiceImpl) V3AdditionalOrder(ctx context.Context, reqDto req.V3AddOrderAdditionalReqDto) (managentVo.OrderVO, error) {
	var defaultOrderVO managentVo.OrderVO = managentVo.OrderVO{}
	// 1. 验证入参
	// 1.1 验证基础参数 venueId,roomId,sessionId,employeeId
	venue, room, session, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.RoomId, reqDto.SessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultOrderVO, err
	}

	// 1.2 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultOrderVO, fmt.Errorf("房间已锁定,不能点单")
	}

	// 2. 创建订单
	additionalOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrder := po.Order{
		VenueId:      reqDto.VenueId,
		RoomId:       reqDto.RoomId,
		SessionId:    reqDto.SessionId,
		OrderNo:      &additionalOrderNo,
		EmployeeId:   reqDto.EmployeeId,
		MemberId:     reqDto.MemberId,
		MemberCardId: reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		Type:         util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
		Direction:    util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:     util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:       util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:         util.Ptr(_const.V2_ORDER_MARK_ADDITIONAL),
	}
	orderVO := s.orderService.ConvertToOrderVO(ctx, toAddOrder)

	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.OrderProductVOs {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *reqDto.VenueId, productIds, packageIds)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("获取商品信息失败: %w", err)
	}
	// 3. 创建订单商品
	for _, orderProductVO := range reqDto.OrderProductVOs {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             reqDto.VenueId,
			RoomId:              reqDto.RoomId,
			SessionId:           reqDto.SessionId,
			EmployeeId:          reqDto.EmployeeId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			OrderNo:             &additionalOrderNo,
			ProductId:           util.Ptr(orderProductVO.ProductId),
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Mark:                util.Ptr(orderProductVO.Mark),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		orderVO.OrderProductVOs = append(orderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}

	// 4. 计算session费用
	totalFeeThis, newOps, _, _, _ := s.payService.CalculateTotalPayment(ctx, managentVo.SessionVO{}, []managentVo.OrderVO{orderVO}, managentVo.PayBillVO{ForceMinimumCharge: true})
	if totalFeeThis != *reqDto.PayAmount+util.GetPtrSafeDefault(reqDto.ZeroFee, 0)-util.GetPtrSafeDefault(reqDto.ChangeAmount, 0) {
		return defaultOrderVO, fmt.Errorf("支付金额与计算金额不一致")
	}
	newOrderProducts := make([]po.OrderProduct, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}

	// 5. 计算session费用
	_, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFeeForAdditionalOrder(ctx, totalFeeThis, newOps, nil, *reqDto.SessionId, *reqDto.VenueId, *session.MinConsume)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("计算费用失败: %w", err)
	}
	toUpdateSession := po.Session{
		Id:             session.Id,
		SessionId:      reqDto.SessionId,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
		PayStatus:      util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
	}

	// 6. 查询联房房间状态
	roomsInSession, err := s.roomService.FindRoomsBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("查询房间状态失败")
	}
	toUpdateRooms := make([]po.Room, 0)
	for _, room := range roomsInSession {
		toUpdateRooms = append(toUpdateRooms, po.Room{
			Id:     room.Id,
			Status: util.Ptr(_const.ROOM_STATUS_IN_USE),
		})
	}

	// 5. 保存数据
	err = s.orderService.SaveOrderAdditionalInfo(ctx, toUpdateRooms, toUpdateSession, toAddOrder, newOrderProducts)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("点单失败")
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, &venue, _const.V2_ORDER_DIRECTION_NORMAL, newOrderProducts)

	// 6. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *reqDto.VenueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	defaultOrderVO = orderVO
	// 执行流程
	return defaultOrderVO, nil
}

// V3AdditionalOrderPay 点单-立结
// 入参： op
// 将op 保存为 order
// 发起支付
func (s *OrderApplicationServiceImpl) V3AdditionalOrderPay(ctx context.Context, reqDto req.V3AddOrderAdditionalPayReqDto) (managentVo.OrderVO, error) {
	var defaultOrderVO managentVo.OrderVO = managentVo.OrderVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, session, employee, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultOrderVO, err
	}
	// 1.2 验证支付方式是否开通
	if err = s.utilValidateBaseParamsPay(ctx, venue, reqDto.PayRecords); err != nil {
		return defaultOrderVO, err
	}
	// 1.2. 验证登录会员卡ID
	_, err = s.utilValidateMemberCard(ctx, venue, reqDto.MemberCardId)
	if err != nil {
		return defaultOrderVO, err
	}

	// 1.6 验证支付方式
	err = s.utilValidatePayFeeInfo(ctx, reqDto.OriginalFee, reqDto.ShouldFee, reqDto.TotalFee, reqDto.ZeroFee, reqDto.ChangeAmount, reqDto.PayRecords, true)
	if err != nil {
		return defaultOrderVO, err
	}

	// 1.7 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultOrderVO, fmt.Errorf("房间已锁定,不能点单")
	}

	// 2. 创建订单
	additionalOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrder := po.Order{
		VenueId:      reqDto.VenueId,
		RoomId:       reqDto.RoomId,
		SessionId:    reqDto.SessionId,
		OrderNo:      &additionalOrderNo,
		EmployeeId:   reqDto.EmployeeId,
		MemberId:     reqDto.MemberId,
		MemberCardId: reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		Type:         util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
		Direction:    util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:     util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:       util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:         util.Ptr(_const.V2_ORDER_MARK_ADDITIONAL),
	}
	orderVO := s.orderService.ConvertToOrderVO(ctx, toAddOrder)
	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.OrderProductVOs {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *venueId, productIds, packageIds)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("获取商品信息失败: %w", err)
	}
	// 3. 创建订单商品
	for _, orderProductVO := range reqDto.OrderProductVOs {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             reqDto.VenueId,
			RoomId:              reqDto.RoomId,
			SessionId:           reqDto.SessionId,
			EmployeeId:          reqDto.EmployeeId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			OrderNo:             &additionalOrderNo,
			ProductId:           util.Ptr(orderProductVO.ProductId),
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Mark:                util.Ptr(orderProductVO.Mark),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		orderVO.OrderProductVOs = append(orderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}

	// 4. 计算session费用
	tmpPayBillVO := managentVo.PayBillVO{
		ProductDiscount:       util.GetPtrSafeDefault(reqDto.ProductDiscount, 100),
		RoomDiscount:          util.GetPtrSafeDefault(reqDto.RoomDiscount, 100),
		IsFree:                util.GetPtrSafeDefault(reqDto.IsFree, false),
		ProductDiscountAmount: util.GetPtrSafeDefault(reqDto.ProductDiscountAmount, 0),
		RoomDiscountAmount:    util.GetPtrSafeDefault(reqDto.RoomDiscountAmount, 0),
		ForceMinimumCharge:    true,
	}
	totalFeeThis, newOps, _, retProductDiscountFee, retRoomDiscountFee := s.payService.CalculateTotalPayment(ctx, managentVo.SessionVO{}, []managentVo.OrderVO{orderVO}, tmpPayBillVO)
	if totalFeeThis != *reqDto.PayAmount+*reqDto.ZeroFee-*reqDto.ChangeAmount {
		return defaultOrderVO, fmt.Errorf("支付金额与计算金额不一致")
	}
	newOrderProducts := make([]po.OrderProduct, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}

	// 5. 计算session费用
	_, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFeeForAdditionalOrder(ctx, totalFeeThis, newOps, nil, *reqDto.SessionId, *reqDto.VenueId, *session.MinConsume)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("计算费用失败: %w", err)
	}

	toUpdateSession := po.Session{
		Id:             session.Id,
		SessionId:      reqDto.SessionId,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
		PayStatus:      util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
	}

	// 6. 查询联房房间状态
	roomsInSession, err := s.roomService.FindRoomsBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("查询房间状态失败")
	}
	toUpdateRooms := make([]po.Room, 0)
	for _, room := range roomsInSession {
		toUpdateRooms = append(toUpdateRooms, po.Room{
			Id:     room.Id,
			Status: util.Ptr(_const.ROOM_STATUS_IN_USE),
		})
	}

	// 8.a. 设置收款单信息
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:               reqDto.VenueId,
		RoomId:                reqDto.RoomId,
		EmployeeId:            reqDto.EmployeeId,
		SessionId:             reqDto.SessionId,
		BillId:                &billId,
		MemberId:              reqDto.MemberId,
		MemberCardId:          reqDto.MemberCardId,
		MemberCardNumber:      reqDto.MemberCardNumber,
		TotalFee:              reqDto.TotalFee,
		OriginalFee:           reqDto.OriginalFee,
		ShouldFee:             reqDto.ShouldFee,
		ZeroFee:               reqDto.ZeroFee,
		CreditAmount:          reqDto.CreditAmount,
		ProductDiscount:       reqDto.ProductDiscount,
		ProductDiscountFee:    &retProductDiscountFee,
		RoomDiscount:          reqDto.RoomDiscount,
		RoomDiscountFee:       &retRoomDiscountFee,
		IsFree:                reqDto.IsFree,
		ProductDiscountAmount: reqDto.ProductDiscountAmount,
		RoomDiscountAmount:    reqDto.RoomDiscountAmount,
		ChangeAmount:          reqDto.ChangeAmount,
		Status:                util.Ptr(_const.V2_PAY_BILL_STATUS_WAIT),
		Direction:             util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
		DiscountReason:        reqDto.DiscountReason,
	}
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	// 8.b. 设置订单支付记录信息
	toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
		OrderNo:   &orderVO.OrderNo,
		SessionId: reqDto.SessionId,
		BillId:    &billId,
	})

	// 8.c. 设置订单支付记录信息
	toAddPayRecords := make([]po.PayRecord, 0)
	payRecordAllPayAmount := int64(0)
	for _, payRecord := range *reqDto.PayRecords {
		payId := util.GetPayId(*reqDto.VenueId)
		toAddPayRecords = append(toAddPayRecords, po.PayRecord{
			VenueId:                 reqDto.VenueId,
			RoomId:                  reqDto.RoomId,
			EmployeeId:              reqDto.EmployeeId,
			MemberId:                reqDto.MemberId,
			MemberCardId:            &payRecord.MemberCardId,
			MemberCardNumber:        reqDto.MemberCardNumber,
			SessionId:               reqDto.SessionId,
			BillId:                  &billId,
			PayId:                   &payId,
			TotalFee:                &payRecord.TotalFee,
			PrincipalAmount:         &payRecord.PrincipalAmount,
			MemberRoomBonusAmount:   &payRecord.MemberRoomBonusAmount,
			MemberGoodsBonusAmount:  &payRecord.MemberGoodsBonusAmount,
			MemberCommonBonusAmount: &payRecord.MemberCommonBonusAmount,
			Status:                  util.Ptr(_const.PAY_STATUS_UNPAID),
			PayType:                 &payRecord.PayType,
			ProductName:             util.GetPayProductName(venue.Name, venue.Id),
			BQROneCode:              &payRecord.BQROneCode,
		})
		payRecordAllPayAmount += payRecord.TotalFee
	}
	if _, msg, err := util.FeeDiff(payRecordAllPayAmount, *reqDto.TotalFee); err != nil {
		return defaultOrderVO, fmt.Errorf("应付金额与支付记录金额不一致: %s", msg)
	}

	if _, msg, err := util.FeeDiff(totalFeeThis, *reqDto.TotalFee+*reqDto.ZeroFee-*reqDto.ChangeAmount); err != nil {
		return defaultOrderVO, fmt.Errorf("订单金额与计算订单金额不一致: %s", msg)
	}
	// 1.3. 验证会员卡ID
	for _, payRecordVO := range *reqDto.PayRecords {
		if payRecordVO.PayType == _const.PAY_TYPE_MEMBER_CARD {
			roomFee := int64(0)
			supermarketFee := int64(0)
			for _, orderProductVO := range newOrderProducts {
				supermarketFee += *orderProductVO.PayAmount
			}
			// 1.2.1. 验证会员卡ID-扣款的
			_, err := s.utilValidateMemberCard(ctx, venue, &payRecordVO.MemberCardId)
			if err != nil {
				return defaultOrderVO, err
			}
			_, err = s.memberRechargeBillService.V3RPCMemberCardVaildBalance(ctx, req.V3QueryMemberCardQueryBalanceReqDto{
				MemberCardId:      &payRecordVO.MemberCardId,
				TotalAmount:       &payRecordVO.TotalFee,
				PrincipalAmount:   &payRecordVO.PrincipalAmount,
				RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
				GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
				CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
				RoomTotalAmount:   &roomFee,
				GoodsTotalAmount:  &supermarketFee,
			})
			if err != nil {
				return defaultOrderVO, err
			}
		}
	}
	// 11.1 优先从会员卡中扣款
	for _, payRecord := range toAddPayRecords {
		payRecordVO := s.payRecordService.ConvertToPayRecordVO(ctx, payRecord)
		// 跳过非会员卡支付
		if payRecordVO.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 检查会员卡余额
		_, err := s.memberRechargeBillService.V3RPCMemberCardPay(ctx, req.V3RPCPayMoneyReqDto{
			VenueId:           reqDto.VenueId,
			EmployeeId:        reqDto.EmployeeId,
			MemberCardId:      &payRecordVO.MemberCardId,
			Amount:            &payRecordVO.PrincipalAmount,
			RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
			GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
			CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
			PayId:             &payRecordVO.PayId,
		})
		if err != nil {
			return defaultOrderVO, fmt.Errorf("会员卡扣款失败: %w", err)
		}
		// 发送会员卡消费短信
		s.orderService.SendSmsMemberCardConsume(ctx, &venue, payRecordVO.MemberCardId, payRecordVO.PrincipalAmount, payRecordVO.MemberRoomBonusAmount, payRecordVO.MemberGoodsBonusAmount, payRecordVO.MemberCommonBonusAmount)
	}

	// 5. 保存数据
	err = s.orderService.SaveOrderAdditionalPayInfo(ctx, toUpdateRooms, toUpdateSession, toAddOrder, newOrderProducts, toAddOrderAndPays, toAddPayRecords, toAddPayBill)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("点单失败")
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, &venue, _const.V2_ORDER_DIRECTION_NORMAL, newOrderProducts)

	// 11.1 保存会员卡消费记录
	s.memberCardConsumeService.RecordMemberCardConsume(ctx, session, venue, room, employee, toAddPayRecords, toAddPayBill)

	// 11. 处理支付信息

	// 11.b.1 发起支付
	payResultVOs, err := s.payService.V3TransformPayGate(ctx, &req.V3QueryOrderPayTransformReqDto{
		PayRecords:    reqDto.PayRecords,
		NewPayRecords: toAddPayRecords,
		VenueId:       reqDto.VenueId,
		RoomId:        reqDto.RoomId,
		SessionId:     reqDto.SessionId,
		EmployeeId:    reqDto.EmployeeId,
	}, &toAddPayBill)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("发起支付失败: %w", err)
	}

	// 12. 保存支付信息回调
	for _, payRecord := range toAddPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, managentVo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_COMMON})
		if err != nil {
			return defaultOrderVO, fmt.Errorf("保存支付信息失败: %w", err)
		}
	}
	orderVO.PayResultVOs = payResultVOs

	// 执行流程
	payBillVO := s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)
	orderVO.PayBillVO = &payBillVO
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *reqDto.VenueId, false, toAddPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return orderVO, nil
}

// V3OrderOpen 续台-后付
// 入参： om op
// 将om op 保存为 order
func (s *OrderApplicationServiceImpl) V3OpenContinue(ctx context.Context, reqDto req.V3AddOrderOpenContinueReqDto) (managentVo.SessionVO, error) {
	defaultSessionVO := managentVo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, session, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 检查时间
	if reqDto.StartTime == nil || *reqDto.StartTime <= 0 {
		return defaultSessionVO, fmt.Errorf("开始时间不能小于0")
	}
	// 1.3 检查房间状态
	if err := util.CheckRoomStatusOpenContinue(*room.Status); err != nil {
		return defaultSessionVO, err
	}
	// 1.4 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultSessionVO, fmt.Errorf("房间已锁定,不能续台")
	}
	// 1.5 检查是否计时模式
	isEndTimeConsume := session.IsTimeConsume != nil && *session.IsTimeConsume
	if isEndTimeConsume {
		return defaultSessionVO, fmt.Errorf("当前为计时模式，需要先结束计时")
	}
	// 2. 设置最低消费
	lastMinimumCharge := int64(0)
	if reqDto.MinimumCharge != nil && *reqDto.MinimumCharge > 0 {
		lastMinimumCharge = *reqDto.MinimumCharge
	}
	isTimeConsume := false
	if len(reqDto.OrderRoomPlanVOS) > 0 {
		orderRoomPlanVO := reqDto.OrderRoomPlanVOS[0]
		if orderRoomPlanVO != nil && orderRoomPlanVO.IsTimeConsume {
			isTimeConsume = true
		}
	}

	orderVOs := make([]managentVo.OrderVO, 0)

	// 3. 处理房费
	// 3.1. 创建Order对象-主开台订单
	roomOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrderForRoomPlan := &po.Order{
		VenueId:       venueId,
		RoomId:        roomId,
		SessionId:     sessionId,
		OrderNo:       &roomOrderNo,
		EmployeeId:    reqDto.EmployeeId,
		MemberId:      reqDto.MemberId,
		MemberCardId:  reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		MinimumCharge: &lastMinimumCharge,
		Type:          util.Ptr(_const.V2_ORDER_TYPE_ROOMPLAN),
		Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
	}
	roomOrderVO := s.orderService.ConvertToOrderVO(ctx, *toAddOrderForRoomPlan)

	// 3.2. 构造OrderRoomPlans
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:            venueId,
			RoomId:             roomId,
			SessionId:          sessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			MemberCardId:       reqDto.MemberCardId,
			MemberCardNumber:   reqDto.MemberCardNumber,
			OrderNo:            &roomOrderNo,
			RoomName:           util.Ptr(orderRoomPlanVO.RoomName),
			PricePlanId:        util.Ptr(orderRoomPlanVO.PricePlanId),
			PricePlanName:      util.Ptr(orderRoomPlanVO.PricePlanName),
			StartTime:          util.Ptr(orderRoomPlanVO.StartTime),
			EndTime:            util.Ptr(orderRoomPlanVO.EndTime),
			Duration:           util.Ptr(orderRoomPlanVO.Duration),
			SelectedAreaId:     reqDto.SelectedAreaId,
			SelectedRoomTypeId: reqDto.SelectedRoomTypeId,
			ConsumptionMode:    reqDto.ConsumptionMode,
			TimeChargeType:     reqDto.TimeChargeType,
			TimeChargeMode:     reqDto.TimeChargeMode,
			IsGift:             util.Ptr(false),
			OriginalPayAmount:  util.Ptr(orderRoomPlanVO.OriginalPayAmount),
			MinimumCharge:      &lastMinimumCharge,
			IsTimeConsume:      &isTimeConsume,
		}
		roomOrderVO.OrderRoomPlanVOs = append(roomOrderVO.OrderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderVOs = append(orderVOs, roomOrderVO)
	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *venueId, productIds, packageIds)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("获取商品信息失败: %w", err)
	}
	// 4. 处理商品
	// 4.1 商品信息-套餐内
	// 4.1.1 创建Order对象-套餐内订单
	subInOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productInOrderVO := managentVo.OrderVO{}
	if len(reqDto.InOrderProductInfos) > 0 {
		toAddOrderForInProduct := &po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     sessionId,
			OrderNo:       &subInOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: &lastMinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
		}
		productInOrderVO = s.orderService.ConvertToOrderVO(ctx, *toAddOrderForInProduct)
	}
	// 4.1.2 处理套餐内商品
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subInOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_IN),
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productInOrderVO.OrderProductVOs = append(productInOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.InOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productInOrderVO)
	}

	// 4.2 商品信息-套餐外
	// 4.2.1 创建Order对象-套餐外订单
	subOutOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productOutOrderVO := managentVo.OrderVO{}
	if len(reqDto.OutOrderProductInfos) > 0 {
		productOutOrder := po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     sessionId,
			OrderNo:       &subOutOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: &lastMinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
		}
		productOutOrderVO = s.orderService.ConvertToOrderVO(ctx, productOutOrder)
	}
	// 4.2.2 处理套餐外商品
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subOutOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productOutOrderVO.OrderProductVOs = append(productOutOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.OutOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productOutOrderVO)
	}

	// 5. 计算费用
	tmpSessionVO := managentVo.SessionVO{
		SessionId:  *sessionId,
		MinConsume: lastMinimumCharge,
	}
	totalFeeThis, newOps, newOms, _, _ := s.payService.CalculateTotalPayment(ctx, tmpSessionVO, orderVOs, managentVo.PayBillVO{ForceMinimumCharge: true})

	// 6. 处理订单
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}
	for _, om := range newOms {
		omVO := s.roomplanService.ConvertToOrderRoomPlan(ctx, om)
		omNew := s.roomplanService.TrimIdToAddOrderRoomPlan(ctx, omVO)
		newOrderRoomPlans = append(newOrderRoomPlans, omNew)
	}

	// 7. 计算费用
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFee(ctx, totalFeeThis, newOps, newOms, *sessionId, *reqDto.VenueId, lastMinimumCharge)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("计算费用失败: %w", err)
	}

	// 8. 处理订单
	toAddOrders := make([]po.Order, 0)
	for _, orderVO := range orderVOs {
		orderTmpVO := s.orderService.ConvertToOrderPO(ctx, orderVO)
		orderTmp := s.orderService.TrimIdToAddOrderPO(ctx, orderTmpVO)
		toAddOrders = append(toAddOrders, orderTmp)
	}

	// 9. 创建Session
	toUpdatesession := po.Session{
		Id:             session.Id,
		PayStatus:      util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
		IsTimeConsume:  &isTimeConsume,
		EndTime:        reqDto.EndTime,
		MinConsume:     &lastMinimumCharge,
		RoomFee:        &totalRoomFee,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
		EmployeeId:     reqDto.EmployeeId,
	}

	// 10. 更新房间状态
	toUpdateRoom := po.Room{
		Id:        roomId,
		SessionId: sessionId,
		Status:    util.Ptr(_const.ROOM_STATUS_IN_USE),
	}

	// 11. 保存订单信息
	_, _, _, _, err = s.orderService.SaveOrderOpenContinueInfo(ctx, toAddOrders, newOrderProducts, newOrderRoomPlans, toUpdatesession, toUpdateRoom)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("保存订单信息失败: %w", err)
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, &venue, _const.V2_ORDER_DIRECTION_NORMAL, newOrderProducts)

	sessionLater, err := s.sessionService.FindBySessionId(ctx, *sessionId, *reqDto.VenueId)
	if err != nil {
		sessionLater = toUpdatesession
	}
	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, sessionLater)
	for _, orderVO := range orderVOs {
		newSessionVO.RtOrderNos = append(newSessionVO.RtOrderNos, orderVO.OrderNo)
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return newSessionVO, nil
}

// V3OrderOpen 续台-立结
// 入参： om op
// 将om op 保存为 order
func (s *OrderApplicationServiceImpl) V3OpenContinuePay(ctx context.Context, reqDto req.V3AddOrderOpenContinuePayReqDto) (managentVo.SessionVO, error) {
	defaultSessionVO := managentVo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, session, employee, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 验证支付方式是否开通
	if err = s.utilValidateBaseParamsPay(ctx, venue, reqDto.PayRecords); err != nil {
		return defaultSessionVO, err
	}
	// 1.2. 验证登录会员卡ID
	_, err = s.utilValidateMemberCard(ctx, venue, reqDto.MemberCardId)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 检查时间
	if reqDto.StartTime == nil || *reqDto.StartTime <= 0 {
		return defaultSessionVO, fmt.Errorf("开始时间不能小于0")
	}
	// 1.3 检查房间状态
	_, err = s.roomService.GetRoom(ctx, *roomId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("房间不存在")
	}
	// 1.4 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultSessionVO, fmt.Errorf("房间已锁定,不能续台")
	}
	// 1.5 检查是否计时模式
	isEndTimeConsume := session.IsTimeConsume != nil && *session.IsTimeConsume
	if isEndTimeConsume {
		return defaultSessionVO, fmt.Errorf("当前为计时模式，需要先结束计时")
	}
	isTimeConsume := false
	if len(reqDto.OrderRoomPlanVOS) > 0 {
		orderRoomPlanVO := reqDto.OrderRoomPlanVOS[0]
		if orderRoomPlanVO != nil && orderRoomPlanVO.IsTimeConsume {
			isTimeConsume = true
		}
	}
	// 1.6 验证支付方式
	err = s.utilValidatePayFeeInfo(ctx, reqDto.OriginalFee, reqDto.ShouldFee, reqDto.TotalFee, reqDto.ZeroFee, reqDto.ChangeAmount, reqDto.PayRecords, true)
	if err != nil {
		return defaultSessionVO, err
	}

	// 2. 设置最低消费
	lastMinimumCharge := int64(0)
	if reqDto.MinimumCharge != nil && *reqDto.MinimumCharge > 0 {
		lastMinimumCharge = *reqDto.MinimumCharge
	}

	orderVOs := make([]managentVo.OrderVO, 0)

	// 3. 处理房费
	// 3.1 创建Order对象-主开台订单
	roomOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrderForRoomPlan := &po.Order{
		VenueId:       venueId,
		RoomId:        roomId,
		SessionId:     sessionId,
		OrderNo:       &roomOrderNo,
		EmployeeId:    reqDto.EmployeeId,
		MemberId:      reqDto.MemberId,
		MemberCardId:  reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		MinimumCharge: reqDto.MinimumCharge,
		Type:          util.Ptr(_const.V2_ORDER_TYPE_ROOMPLAN),
		Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
	}
	roomOrderVO := s.orderService.ConvertToOrderVO(ctx, *toAddOrderForRoomPlan)

	// 3.2. 构造OrderRoomPlans
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:            venueId,
			RoomId:             roomId,
			SessionId:          sessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			MemberCardId:       reqDto.MemberCardId,
			MemberCardNumber:   reqDto.MemberCardNumber,
			OrderNo:            &roomOrderNo,
			RoomName:           util.Ptr(orderRoomPlanVO.RoomName),
			PricePlanId:        util.Ptr(orderRoomPlanVO.PricePlanId),
			PricePlanName:      util.Ptr(orderRoomPlanVO.PricePlanName),
			StartTime:          util.Ptr(orderRoomPlanVO.StartTime),
			EndTime:            util.Ptr(orderRoomPlanVO.EndTime),
			Duration:           util.Ptr(orderRoomPlanVO.Duration),
			SelectedAreaId:     reqDto.SelectedAreaId,
			SelectedRoomTypeId: reqDto.SelectedRoomTypeId,
			ConsumptionMode:    reqDto.ConsumptionMode,
			TimeChargeType:     reqDto.TimeChargeType,
			TimeChargeMode:     reqDto.TimeChargeMode,
			IsGift:             util.Ptr(false),
			IsFree:             reqDto.IsFree,
			OriginalPayAmount:  util.Ptr(orderRoomPlanVO.OriginalPayAmount),
			MinimumCharge:      &lastMinimumCharge,
			IsTimeConsume:      &isTimeConsume,
		}
		roomOrderVO.OrderRoomPlanVOs = append(roomOrderVO.OrderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderVOs = append(orderVOs, roomOrderVO)

	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *venueId, productIds, packageIds)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("获取商品信息失败: %w", err)
	}

	// 4. 处理商品
	// 4.1 商品信息-套餐内
	// 4.1.1 创建Order对象-套餐内订单
	subInOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productInOrderVO := managentVo.OrderVO{}
	if len(reqDto.InOrderProductInfos) > 0 {
		toAddOrderForInProduct := &po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     sessionId,
			OrderNo:       &subInOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: reqDto.MinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
		}
		productInOrderVO = s.orderService.ConvertToOrderVO(ctx, *toAddOrderForInProduct)
	}
	// 4.1.2 处理套餐内商品
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subInOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_IN),
			IsFree:              reqDto.IsFree,
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productInOrderVO.OrderProductVOs = append(productInOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.InOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productInOrderVO)
	}

	// 4.2 商品信息-套餐外
	// 4.2.1 创建Order对象-套餐外订单
	subOutOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productOutOrderVO := managentVo.OrderVO{}
	if len(reqDto.OutOrderProductInfos) > 0 {
		productOutOrder := po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     sessionId,
			OrderNo:       &subOutOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: reqDto.MinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
		}
		productOutOrderVO = s.orderService.ConvertToOrderVO(ctx, productOutOrder)
	}
	// 4.2.2 处理套餐外商品
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subOutOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsFree:              reqDto.IsFree,
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productOutOrderVO.OrderProductVOs = append(productOutOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.OutOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productOutOrderVO)
	}

	// 5. 计算费用
	tmpPayBillVO := managentVo.PayBillVO{
		ProductDiscount:       util.GetPtrSafeDefault(reqDto.ProductDiscount, 100),
		RoomDiscount:          util.GetPtrSafeDefault(reqDto.RoomDiscount, 100),
		IsFree:                util.GetPtrSafeDefault(reqDto.IsFree, false),
		ProductDiscountAmount: util.GetPtrSafeDefault(reqDto.ProductDiscountAmount, 0),
		RoomDiscountAmount:    util.GetPtrSafeDefault(reqDto.RoomDiscountAmount, 0),
		ForceMinimumCharge:    true,
	}
	tmpSessionVO := managentVo.SessionVO{
		SessionId:  *sessionId,
		MinConsume: lastMinimumCharge,
	}
	totalFeeThis, newOps, newOms, retProductDiscountFee, retRoomDiscountFee := s.payService.CalculateTotalPayment(ctx, tmpSessionVO, orderVOs, tmpPayBillVO)

	// 6. 处理订单
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}
	for _, om := range newOms {
		omVO := s.roomplanService.ConvertToOrderRoomPlan(ctx, om)
		omNew := s.roomplanService.TrimIdToAddOrderRoomPlan(ctx, omVO)
		newOrderRoomPlans = append(newOrderRoomPlans, omNew)
	}

	// 7. 计算费用
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFee(ctx, totalFeeThis, newOps, newOms, *sessionId, *reqDto.VenueId, lastMinimumCharge)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("计算费用失败: %w", err)
	}

	// 8. 处理订单
	toAddOrders := make([]po.Order, 0)
	for _, orderVO := range orderVOs {
		orderTmpVO := s.orderService.ConvertToOrderPO(ctx, orderVO)
		orderTmp := s.orderService.TrimIdToAddOrderPO(ctx, orderTmpVO)
		toAddOrders = append(toAddOrders, orderTmp)
	}

	// 8.a. 设置收款单信息
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:               reqDto.VenueId,
		RoomId:                reqDto.RoomId,
		EmployeeId:            reqDto.EmployeeId,
		SessionId:             sessionId,
		MemberId:              reqDto.MemberId,
		MemberCardId:          reqDto.MemberCardId,
		MemberCardNumber:      reqDto.MemberCardNumber,
		BillId:                &billId,
		OriginalFee:           reqDto.OriginalFee,
		TotalFee:              reqDto.TotalFee,
		ShouldFee:             reqDto.ShouldFee,
		ZeroFee:               reqDto.ZeroFee,
		CreditAmount:          reqDto.CreditAmount,
		ProductDiscount:       reqDto.ProductDiscount,
		RoomDiscount:          reqDto.RoomDiscount,
		ProductDiscountFee:    &retProductDiscountFee,
		RoomDiscountFee:       &retRoomDiscountFee,
		IsFree:                reqDto.IsFree,
		ProductDiscountAmount: reqDto.ProductDiscountAmount,
		RoomDiscountAmount:    reqDto.RoomDiscountAmount,
		ChangeAmount:          reqDto.ChangeAmount,
		Status:                util.Ptr(_const.V2_PAY_BILL_STATUS_WAIT),
		Direction:             util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
		DiscountReason:        reqDto.DiscountReason,
	}
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	// 8.b. 设置订单支付记录信息
	for _, orderVO := range orderVOs {
		orderNoTmp := orderVO.OrderNo
		// 剔除计时支付订单
		if isTimeConsume && orderNoTmp == roomOrderNo {
			continue
		}
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   &orderNoTmp,
			SessionId: sessionId,
			BillId:    &billId,
		})
	}
	// 8.c. 设置订单支付记录信息
	toAddPayRecords := make([]po.PayRecord, 0)
	payRecordAllPayAmount := int64(0)
	for _, payRecord := range *reqDto.PayRecords {
		payId := util.GetPayId(*reqDto.VenueId)
		totalFeeTmp := payRecord.TotalFee
		payTypeTmp := payRecord.PayType
		toAddPayRecords = append(toAddPayRecords, po.PayRecord{
			VenueId:                 reqDto.VenueId,
			RoomId:                  reqDto.RoomId,
			EmployeeId:              reqDto.EmployeeId,
			MemberCardId:            &payRecord.MemberCardId,
			MemberCardNumber:        reqDto.MemberCardNumber,
			SessionId:               sessionId,
			BillId:                  &billId,
			PayId:                   &payId,
			TotalFee:                &totalFeeTmp,
			PrincipalAmount:         &payRecord.PrincipalAmount,
			MemberRoomBonusAmount:   &payRecord.MemberRoomBonusAmount,
			MemberGoodsBonusAmount:  &payRecord.MemberGoodsBonusAmount,
			MemberCommonBonusAmount: &payRecord.MemberCommonBonusAmount,
			Status:                  util.Ptr(_const.PAY_STATUS_UNPAID),
			PayType:                 &payTypeTmp,
			ProductName:             util.GetPayProductName(venue.Name, venue.Id),
			BQROneCode:              &payRecord.BQROneCode,
		})
		payRecordAllPayAmount += payRecord.TotalFee
	}
	if _, msg, err := util.FeeDiff(payRecordAllPayAmount, *reqDto.TotalFee); err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("应付金额与支付记录金额不一致: %s", msg)
	}
	if _, msg, err := util.FeeDiff(totalFeeThis, *reqDto.TotalFee+*reqDto.ZeroFee-*reqDto.ChangeAmount); err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("订单金额与计算订单金额不一致: %s", msg)
	}

	// 9. 创建Session
	toUpdatesession := po.Session{
		Id:             session.Id,
		PayStatus:      util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
		EndTime:        reqDto.EndTime,
		MinConsume:     reqDto.MinimumCharge,
		RoomFee:        &totalRoomFee,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
		EmployeeId:     reqDto.EmployeeId,
		IsTimeConsume:  &isTimeConsume,
	}

	// 10. 更新房间状态
	toUpdateRoom := po.Room{
		Id:        roomId,
		SessionId: sessionId,
		Status:    util.Ptr(_const.ROOM_STATUS_IN_USE),
	}
	// 1.3. 验证会员卡ID
	for _, payRecordVO := range *reqDto.PayRecords {
		if payRecordVO.PayType == _const.PAY_TYPE_MEMBER_CARD {
			roomFee := int64(0)
			supermarketFee := int64(0)
			for _, orderProductVO := range newOrderProducts {
				supermarketFee += *orderProductVO.PayAmount
			}
			for _, orderRoomPlanVO := range newOrderRoomPlans {
				roomFee += *orderRoomPlanVO.PayAmount
			}
			// 1.2.1. 验证会员卡ID-扣款的
			_, err := s.utilValidateMemberCard(ctx, venue, &payRecordVO.MemberCardId)
			if err != nil {
				return defaultSessionVO, err
			}
			_, err = s.memberRechargeBillService.V3RPCMemberCardVaildBalance(ctx, req.V3QueryMemberCardQueryBalanceReqDto{
				MemberCardId:      &payRecordVO.MemberCardId,
				TotalAmount:       &payRecordVO.TotalFee,
				PrincipalAmount:   &payRecordVO.PrincipalAmount,
				RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
				GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
				CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
				RoomTotalAmount:   &roomFee,
				GoodsTotalAmount:  &supermarketFee,
			})
			if err != nil {
				return defaultSessionVO, err
			}
		}
	}
	// 11.1 优先从会员卡中扣款
	for _, payRecord := range toAddPayRecords {
		payRecordVO := s.payRecordService.ConvertToPayRecordVO(ctx, payRecord)
		// 跳过非会员卡支付
		if payRecordVO.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 检查会员卡余额
		_, err := s.memberRechargeBillService.V3RPCMemberCardPay(ctx, req.V3RPCPayMoneyReqDto{
			VenueId:           reqDto.VenueId,
			EmployeeId:        reqDto.EmployeeId,
			MemberCardId:      &payRecordVO.MemberCardId,
			Amount:            &payRecordVO.PrincipalAmount,
			RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
			GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
			CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
			PayId:             &payRecordVO.PayId,
		})
		if err != nil {
			return defaultSessionVO, fmt.Errorf("会员卡扣款失败: %w", err)
		}
		// 发送会员卡消费短信
		s.orderService.SendSmsMemberCardConsume(ctx, &venue, payRecordVO.MemberCardId, payRecordVO.PrincipalAmount, payRecordVO.MemberRoomBonusAmount, payRecordVO.MemberGoodsBonusAmount, payRecordVO.MemberCommonBonusAmount)
	}

	// 11.1 保存订单信息
	_, _, _, _, err = s.orderService.SaveOrderOpenContinuePayInfo(ctx, toAddOrders, newOrderProducts, newOrderRoomPlans, toUpdatesession, toUpdateRoom, toAddPayBill, toAddOrderAndPays, toAddPayRecords)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("保存订单信息失败: %w", err)
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, &venue, _const.V2_ORDER_DIRECTION_NORMAL, newOrderProducts)
	
	sessionLater, err := s.sessionService.FindBySessionId(ctx, *sessionId, *reqDto.VenueId)
	if err != nil {
		sessionLater = toUpdatesession
	}
	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, sessionLater)

	// 11.1 保存会员卡消费记录
	s.memberCardConsumeService.RecordMemberCardConsume(ctx, session, venue, room, employee, toAddPayRecords, toAddPayBill)

	// 11. 处理支付信息
	// 11.b.1 发起支付
	payResultVOs, err := s.payService.V3TransformPayGate(ctx, &req.V3QueryOrderPayTransformReqDto{
		PayRecords:    reqDto.PayRecords,
		NewPayRecords: toAddPayRecords,
		VenueId:       reqDto.VenueId,
		RoomId:        reqDto.RoomId,
		SessionId:     reqDto.SessionId,
		EmployeeId:    reqDto.EmployeeId,
	}, &toAddPayBill)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("发起支付失败: %w", err)
	}

	// 12. 保存支付信息回调
	for _, payRecord := range toAddPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, managentVo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_COMMON})
		if err != nil {
			return managentVo.SessionVO{}, fmt.Errorf("保存支付信息失败: %w", err)
		}
	}
	newSessionVO.PayResultVOs = payResultVOs

	for _, orderVO := range orderVOs {
		newSessionVO.RtOrderNos = append(newSessionVO.RtOrderNos, orderVO.OrderNo)
	}
	newSessionVO.PayBills = []managentVo.PayBillVO{s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, false, toAddPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return newSessionVO, nil
}

// V3EndTimeConsume 结束开台计时消费
func (s *OrderApplicationServiceImpl) V3EndTimeConsume(ctx context.Context, reqDto req.V3EndTimeConsumeReqDto) (managentVo.SessionVO, error) {
	defaultSessionVO := managentVo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	_, room, session, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultSessionVO, err
	}
	isEndTimeConsume := session.IsTimeConsume != nil && *session.IsTimeConsume
	if !isEndTimeConsume {
		return defaultSessionVO, fmt.Errorf("当前不是计时模式")
	}
	// 1.3 检查房间状态
	if err := util.CheckRoomStatusEndTimeConsume(*room.Status); err != nil {
		return defaultSessionVO, err
	}
	if len(reqDto.OrderRoomPlanVOS) <= 0 {
		return defaultSessionVO, fmt.Errorf("房费信息不能为空")
	}
	lastMinimumCharge := int64(0) // 计时无低消

	// 查询roomplan找到istimeconsume为true的roomplan
	roomplans, err := s.roomplanService.GetOrderRoomPlansBySessionIds(ctx, *reqDto.VenueId, []string{*sessionId})
	if err != nil {
		return defaultSessionVO, err
	}
	// 按开始时间排序
	sort.Slice(roomplans, func(i, j int) bool {
		return *roomplans[i].StartTime < *roomplans[j].StartTime
	})
	exists := false
	var orderRoomPlanParent po.OrderRoomPlan
	lastRoomPlan := roomplans[0]
	for _, roomplan := range roomplans {
		if roomplan.IsTimeConsume != nil && *roomplan.IsTimeConsume {
			exists = true
			orderRoomPlanParent = roomplan
			break
		}
		lastRoomPlan = roomplan
	}
	if !exists {
		return defaultSessionVO, fmt.Errorf("当前不是计时模式")
	}

	// 计算最早结束时间 开台的时间 或者 上一次开台的结束时间
	earliestEndTimeShould := *lastRoomPlan.StartTime
	if lastRoomPlan.Id != orderRoomPlanParent.Id { // 场景：计时之前有开台或续台
		earliestEndTimeShould = *lastRoomPlan.EndTime
	}

	roomOrderNo := *orderRoomPlanParent.OrderNo

	orderVOs := make([]managentVo.OrderVO, 0)
	order, err := s.orderService.GetOrderByOrderNO(ctx, *reqDto.VenueId, roomOrderNo)
	if err != nil {
		return defaultSessionVO, err
	}
	roomOrderVO := s.orderService.ConvertToOrderVO(ctx, order)

	// 3.2. 构造OrderRoomPlans
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:            venueId,
			RoomId:             roomId,
			SessionId:          sessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			MemberCardId:       reqDto.MemberCardId,
			MemberCardNumber:   reqDto.MemberCardNumber,
			OrderNo:            &roomOrderNo,
			RoomName:           util.Ptr(orderRoomPlanVO.RoomName),
			PricePlanId:        util.Ptr(orderRoomPlanVO.PricePlanId),
			PricePlanName:      util.Ptr(orderRoomPlanVO.PricePlanName),
			StartTime:          util.Ptr(orderRoomPlanVO.StartTime),
			EndTime:            util.Ptr(orderRoomPlanVO.EndTime),
			Duration:           util.Ptr(orderRoomPlanVO.Duration),
			SelectedAreaId:     orderRoomPlanParent.SelectedAreaId,
			SelectedRoomTypeId: orderRoomPlanParent.SelectedRoomTypeId,
			ConsumptionMode:    reqDto.ConsumptionMode,
			IsGift:             util.Ptr(orderRoomPlanVO.IsGift),
			OriginalPayAmount:  util.Ptr(orderRoomPlanVO.OriginalPayAmount),
			IsTimeConsume:      util.Ptr(false),
		}
		roomOrderVO.OrderRoomPlanVOs = append(roomOrderVO.OrderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderVOs = append(orderVOs, roomOrderVO)

	// 5. 计算费用
	tmpSessionVO := managentVo.SessionVO{}
	totalFeeThis, _, newOms, _, _ := s.payService.CalculateTotalPayment(ctx, tmpSessionVO, orderVOs, managentVo.PayBillVO{ForceMinimumCharge: true})

	// 6. 构造OrderRoomPlans
	toAddOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	toUpdateOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	endTimeLastReq := int64(0)
	for idx, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		// 获取请求中最大的结束时间
		if endTimeLastReq < orderRoomPlanVO.EndTime {
			endTimeLastReq = orderRoomPlanVO.EndTime
		}
		orderRoomPlanTmp := s.roomplanService.ConvertToOrderRoomPlan(ctx, orderRoomPlanVO)
		orderRoomPlan := s.roomplanService.TrimIdToAddOrderRoomPlanPO(ctx, orderRoomPlanTmp)
		if idx == 0 { // 将第一个更新
			now := int64(util.TimeNowUnix())
			orderRoomPlan.Id = orderRoomPlanParent.Id
			orderRoomPlan.Ctime = &now
			orderRoomPlan.Utime = &now
			orderRoomPlan.State = new(int)
			orderRoomPlan.Version = new(int)
			orderRoomPlan.IsTimeConsume = util.Ptr(false)
			toUpdateOrderRoomPlans = append(toUpdateOrderRoomPlans, orderRoomPlan)
		} else {
			orderRoomPlan.IsTimeConsume = util.Ptr(false)
			toAddOrderRoomPlans = append(toAddOrderRoomPlans, orderRoomPlan)
		}
	}
	if endTimeLastReq <= earliestEndTimeShould {
		return defaultSessionVO, fmt.Errorf("结束时间不能小于最早结束时间")
	}

	// 7. 计算费用
	totalRoomFee, _, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFee(ctx, totalFeeThis, nil, newOms, *sessionId, *reqDto.VenueId, lastMinimumCharge)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("计算费用失败: %w", err)
	}

	// 9. 创建Session
	toUpdateSessions := []po.Session{
		{
			Id:            session.Id,
			PayStatus:     util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
			EndTime:       &endTimeLastReq,
			RoomFee:       &totalRoomFee,
			TotalFee:      &totalFee,
			UnpaidAmount:  &unpaidAmount,
			PaidAmount:    &paidAmount,
			IsTimeConsume: util.Ptr(false),
		},
	}

	// 10. 更新房间状态
	toUpdateRooms := []po.Room{
		{
			Id:     roomId,
			Status: util.Ptr(_const.ROOM_STATUS_IN_USE),
		},
	}
	// 11. 保存订单信息
	_, _, err = s.orderService.SaveInfoEndTimeConsume(ctx, toUpdateOrderRoomPlans, toAddOrderRoomPlans, toUpdateSessions, toUpdateRooms)
	if err != nil {
		return managentVo.SessionVO{}, fmt.Errorf("保存订单信息失败: %w", err)
	}
	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, session)

	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return newSessionVO, nil
}

// V3OrderReopen 重开
func (s *OrderApplicationServiceImpl) V3OrderReopen(ctx context.Context, reqDto req.V3OrderReopenReqDto) (managentVo.SessionVO, error) {
	defaultSessionVO := managentVo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	_, room, _, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, nil, reqDto.EmployeeId, false)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 验证房间状态
	if err := util.CheckRoomStatusReopen(*room.Status); err != nil {
		return defaultSessionVO, err
	}
	roomOperations, err := s.orderService.FindLastRoomOperation(ctx, *venueId, []string{*roomId})
	if err != nil {
		return defaultSessionVO, err
	}
	if len(roomOperations) > 0 {
		roomOperation := roomOperations[0]
		if roomOperation.Type != nil && util.InList(*roomOperation.Type, []string{_const.V2_ROOM_OPERATION_TYPE_ATTACH, _const.V2_ROOM_OPERATION_TYPE_MERGE}) {
			return defaultSessionVO, fmt.Errorf("房间最近有并房或联房操作,不能重开")
		}
	}

	// 2. 查找当前房间的最近的session
	session, err := s.sessionService.FindSessionLatestByRoomId(ctx, *venueId, *roomId)
	if err != nil {
		return defaultSessionVO, err
	}
	closeTime := session.CloseTime
	if closeTime != nil && *closeTime+10*60 < int64(util.TimeNowUnix()) {
		return defaultSessionVO, fmt.Errorf("关房时间超过10分钟,不能重开")
	}

	// timeNow := int64(util.TimeNowUnix())
	// 3. 验证session的结束时间
	// if session.EndTime == nil || *session.EndTime < timeNow {
	// 	return defaultSessionVO, fmt.Errorf("房间最近的场次已结束,不能重开")
	// }

	// 4. 更新room状态
	toUpdateRooms := []po.Room{
		{
			Id:        roomId,
			SessionId: session.SessionId,
			Status:    util.Ptr(_const.ROOM_STATUS_IN_USE),
		},
	}
	toUpdateSessions := []po.Session{
		{
			Id:        session.Id,
			Status:    util.Ptr(string(_const.V2_SESSION_STATUS_REOPENING)),
			CloseTime: new(int64),
		},
	}
	_, err = s.orderService.SaveInfoOrderReopen(ctx, toUpdateRooms, toUpdateSessions)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("保存订单信息失败: %w", err)
	}

	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, session)

	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}

	return newSessionVO, nil
}

// V3MiniAppPay 小程序点单-立结
// 入参： openid
// 将op 保存为 order
// 发起支付
func (s *OrderApplicationServiceImpl) V3MiniAppPay(ctx context.Context, reqDto req.V3MiniAppPayReqDto) (managentVo.OrderVO, error) {
	var defaultOrderVO managentVo.OrderVO = managentVo.OrderVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, session, employee, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultOrderVO, err
	}

	// 1.6 验证支付方式
	err = s.utilValidatePayFeeInfo(ctx, reqDto.OriginalFee, reqDto.ShouldFee, reqDto.TotalFee, reqDto.ZeroFee, reqDto.ChangeAmount, reqDto.PayRecords, false)
	if err != nil {
		return defaultOrderVO, err
	}
	// 1. 检查支付方式是否为空
	if reqDto.PayRecords == nil || len(*reqDto.PayRecords) == 0 {
		return defaultOrderVO, fmt.Errorf("PayType不能为空")
	}
	// 2. 检查支付方式是否支持
	if len(*reqDto.PayRecords) > 1 {
		return defaultOrderVO, fmt.Errorf("仅支持小程序支付方式")
	}
	for _, payRecord := range *reqDto.PayRecords {
		if payRecord.PayType != _const.PAY_TYPE_WECHAT_MINIAPP {
			return defaultOrderVO, fmt.Errorf("小程序支付方式只能为微信")
		}
	}

	// 1.7 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultOrderVO, fmt.Errorf("房间已锁定,不能点单")
	}

	// 2. 创建订单
	additionalOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrder := po.Order{
		VenueId:      reqDto.VenueId,
		RoomId:       reqDto.RoomId,
		SessionId:    reqDto.SessionId,
		OrderNo:      &additionalOrderNo,
		EmployeeId:   reqDto.EmployeeId,
		MemberId:     reqDto.MemberId,
		MemberCardId: reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		Type:         util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
		Direction:    util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:     util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:       util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:         util.Ptr(_const.V2_ORDER_MARK_MINIAPP_PAY),
	}
	orderVO := s.orderService.ConvertToOrderVO(ctx, toAddOrder)

	// 3. 创建订单商品
	for _, orderProductVO := range reqDto.OrderProductVOs {
		orderProduct := po.OrderProduct{
			VenueId:             reqDto.VenueId,
			RoomId:              reqDto.RoomId,
			SessionId:           reqDto.SessionId,
			EmployeeId:          reqDto.EmployeeId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			OrderNo:             &additionalOrderNo,
			ProductId:           util.Ptr(orderProductVO.ProductId),
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Mark:                util.Ptr(orderProductVO.Mark),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
		}
		orderVO.OrderProductVOs = append(orderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}

	// 4. 计算session费用
	tmpPayBillVO := managentVo.PayBillVO{
		ProductDiscount:       100,
		RoomDiscount:          100,
		IsFree:                false,
		ProductDiscountAmount: 0,
		RoomDiscountAmount:    0,
		ForceMinimumCharge:    false,
	}
	totalFeeThis, newOps, _, retProductDiscountFee, retRoomDiscountFee := s.payService.CalculateTotalPayment(ctx, managentVo.SessionVO{}, []managentVo.OrderVO{orderVO}, tmpPayBillVO)
	if totalFeeThis != *reqDto.PayAmount+*reqDto.ZeroFee-*reqDto.ChangeAmount {
		return defaultOrderVO, fmt.Errorf("支付金额与计算金额不一致")
	}
	newOrderProducts := make([]po.OrderProduct, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}

	// 5. 计算session费用
	_, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFeeForAdditionalOrder(ctx, totalFeeThis, newOps, nil, *reqDto.SessionId, *reqDto.VenueId, *session.MinConsume)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("计算费用失败: %w", err)
	}

	toUpdateSession := po.Session{
		Id:             session.Id,
		SessionId:      reqDto.SessionId,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
		PayStatus:      util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
	}

	// 6. 查询联房房间状态
	roomsInSession, err := s.roomService.FindRoomsBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("查询房间状态失败")
	}
	toUpdateRooms := make([]po.Room, 0)
	for _, room := range roomsInSession {
		toUpdateRooms = append(toUpdateRooms, po.Room{
			Id:     room.Id,
			Status: util.Ptr(_const.ROOM_STATUS_IN_USE),
		})
	}

	// 8.a. 设置收款单信息
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:               reqDto.VenueId,
		RoomId:                reqDto.RoomId,
		EmployeeId:            reqDto.EmployeeId,
		SessionId:             reqDto.SessionId,
		BillId:                &billId,
		MemberId:              reqDto.MemberId,
		MemberCardId:          reqDto.MemberCardId,
		MemberCardNumber:      reqDto.MemberCardNumber,
		TotalFee:              reqDto.TotalFee,
		OriginalFee:           reqDto.OriginalFee,
		ShouldFee:             reqDto.ShouldFee,
		ZeroFee:               reqDto.ZeroFee,
		CreditAmount:          reqDto.CreditAmount,
		ProductDiscount:       reqDto.ProductDiscount,
		ProductDiscountFee:    &retProductDiscountFee,
		RoomDiscountFee:       &retRoomDiscountFee,
		RoomDiscount:          reqDto.RoomDiscount,
		IsFree:                reqDto.IsFree,
		ProductDiscountAmount: reqDto.ProductDiscountAmount,
		RoomDiscountAmount:    reqDto.RoomDiscountAmount,
		ChangeAmount:          reqDto.ChangeAmount,
		Status:                util.Ptr(_const.V2_PAY_BILL_STATUS_WAIT),
		Direction:             util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
	}
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	// 8.b. 设置订单支付记录信息
	toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
		OrderNo:   &orderVO.OrderNo,
		SessionId: reqDto.SessionId,
		BillId:    &billId,
	})

	// 8.c. 设置订单支付记录信息
	toAddPayRecords := make([]po.PayRecord, 0)
	payRecordAllPayAmount := int64(0)
	for _, payRecord := range *reqDto.PayRecords {
		payId := util.GetPayId(*reqDto.VenueId)
		toAddPayRecords = append(toAddPayRecords, po.PayRecord{
			VenueId:                 reqDto.VenueId,
			RoomId:                  reqDto.RoomId,
			EmployeeId:              reqDto.EmployeeId,
			MemberId:                reqDto.MemberId,
			MemberCardId:            &payRecord.MemberCardId,
			MemberCardNumber:        reqDto.MemberCardNumber,
			SessionId:               reqDto.SessionId,
			BillId:                  &billId,
			PayId:                   &payId,
			TotalFee:                &payRecord.TotalFee,
			PrincipalAmount:         &payRecord.PrincipalAmount,
			MemberRoomBonusAmount:   &payRecord.MemberRoomBonusAmount,
			MemberGoodsBonusAmount:  &payRecord.MemberGoodsBonusAmount,
			MemberCommonBonusAmount: &payRecord.MemberCommonBonusAmount,
			Status:                  util.Ptr(_const.PAY_STATUS_UNPAID),
			PayType:                 &payRecord.PayType,
			ProductName:             util.GetPayProductName(venue.Name, venue.Id),
			BQROneCode:              &payRecord.BQROneCode,
		})
		payRecordAllPayAmount += payRecord.TotalFee
	}
	if payRecordAllPayAmount != *reqDto.TotalFee {
		return defaultOrderVO, fmt.Errorf("应付金额与支付记录金额不一致")
	}
	if totalFeeThis != *reqDto.TotalFee+*reqDto.ZeroFee-*reqDto.ChangeAmount {
		return defaultOrderVO, fmt.Errorf("订单金额与计算订单金额不一致")
	}

	// 5. 保存数据
	err = s.orderService.SaveOrderAdditionalPayInfo(ctx, toUpdateRooms, toUpdateSession, toAddOrder, newOrderProducts, toAddOrderAndPays, toAddPayRecords, toAddPayBill)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("点单失败")
	}
	// 11.1 保存会员卡消费记录
	s.memberCardConsumeService.RecordMemberCardConsume(ctx, session, venue, room, employee, toAddPayRecords, toAddPayBill)

	// 11. 处理支付信息
	// 11.b.1 发起支付
	payResultVOs, err := s.payService.V3TransformPayGate(ctx, &req.V3QueryOrderPayTransformReqDto{
		PayRecords:    reqDto.PayRecords,
		NewPayRecords: toAddPayRecords,
		VenueId:       reqDto.VenueId,
		RoomId:        reqDto.RoomId,
		SessionId:     reqDto.SessionId,
		EmployeeId:    reqDto.EmployeeId,
	}, &toAddPayBill)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("发起支付失败: %w", err)
	}

	orderVO.PayResultVOs = payResultVOs
	// 执行流程
	payBillVO := s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)
	orderVO.PayBillVO = &payBillVO

	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, false, toAddPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}

	return orderVO, nil
}
