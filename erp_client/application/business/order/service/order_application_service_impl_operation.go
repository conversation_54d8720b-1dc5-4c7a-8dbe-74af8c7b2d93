package service

import (
	"context"
	"fmt"
	"math"
	"strconv"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
)

// V3OrderOpenPayX 转台-立付
// 入参： om op
// 将om op 保存为 order
func (s *OrderApplicationServiceImpl) V3TransferRoom(ctx context.Context, reqDto req.V3TransferRoomReqDto) (vo.SessionVO, error) {
	defaultSessionVO := vo.SessionVO{}

	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId

	// 1. 验证输入参数
	// 1.1 验证基础参数
	venue, room, session, employee, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 验证支付方式是否开通
	if err = s.utilValidateBaseParamsPay(ctx, venue, reqDto.PayRecords); err != nil {
		return defaultSessionVO, err
	}
	// 1.2. 验证登录会员卡ID
	_, err = s.utilValidateMemberCard(ctx, venue, reqDto.MemberCardId)
	if err != nil {
		return defaultSessionVO, err
	}
	// 1.2 检查时间
	if reqDto.StartTime == nil || *reqDto.StartTime <= 0 {
		return defaultSessionVO, fmt.Errorf("开始时间不能小于0")
	}
	// 1.3 检查房间状态
	_, err = s.roomService.GetRoom(ctx, *roomId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("房间不存在")
	}
	// 1.4 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultSessionVO, fmt.Errorf("房间已锁定,不能续台")
	}
	// 1.5 检查是否计时模式
	isEndTimeConsume := session.IsTimeConsume != nil && *session.IsTimeConsume
	if isEndTimeConsume {
		return defaultSessionVO, fmt.Errorf("当前为计时模式，需要先结束计时")
	}
	isTimeConsume := false
	if len(reqDto.OrderRoomPlanVOS) > 0 {
		orderRoomPlanVO := reqDto.OrderRoomPlanVOS[0]
		if orderRoomPlanVO != nil && orderRoomPlanVO.IsTimeConsume {
			isTimeConsume = true
		}
	}
	// 1.6 验证支付方式
	err = s.utilValidatePayFeeInfo(ctx, reqDto.OriginalFee, reqDto.ShouldFee, reqDto.TotalFee, reqDto.ZeroFee, reqDto.ChangeAmount, reqDto.PayRecords, true)
	if err != nil {
		return defaultSessionVO, err
	}

	// 2. 设置最低消费
	lastMinimumCharge := int64(0)
	if reqDto.MinimumCharge != nil && *reqDto.MinimumCharge > 0 {
		lastMinimumCharge = *reqDto.MinimumCharge
	}

	orderVOs := make([]vo.OrderVO, 0)

	// 3. 处理房费
	// 3.1 创建Order对象-主开台订单
	roomOrderNo := util.GetOrderNo(*reqDto.VenueId)
	toAddOrderForRoomPlan := &po.Order{
		VenueId:       venueId,
		RoomId:        roomId,
		SessionId:     sessionId,
		OrderNo:       &roomOrderNo,
		EmployeeId:    reqDto.EmployeeId,
		MemberId:      reqDto.MemberId,
		MemberCardId:  reqDto.MemberCardId,
		MemberCardNumber: reqDto.MemberCardNumber,
		MinimumCharge: reqDto.MinimumCharge,
		Type:          util.Ptr(_const.V2_ORDER_TYPE_ROOMPLAN),
		Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
		Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
	}
	roomOrderVO := s.orderService.ConvertToOrderVO(ctx, *toAddOrderForRoomPlan)

	// 3.2. 构造OrderRoomPlans
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:            venueId,
			RoomId:             roomId,
			SessionId:          sessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			MemberCardId:       reqDto.MemberCardId,
			MemberCardNumber:   reqDto.MemberCardNumber,
			OrderNo:            &roomOrderNo,
			RoomName:           util.Ptr(orderRoomPlanVO.RoomName),
			PricePlanId:        util.Ptr(orderRoomPlanVO.PricePlanId),
			PricePlanName:      util.Ptr(orderRoomPlanVO.PricePlanName),
			StartTime:          util.Ptr(orderRoomPlanVO.StartTime),
			EndTime:            util.Ptr(orderRoomPlanVO.EndTime),
			Duration:           util.Ptr(orderRoomPlanVO.Duration),
			SelectedAreaId:     reqDto.SelectedAreaId,
			SelectedRoomTypeId: reqDto.SelectedRoomTypeId,
			ConsumptionMode:    reqDto.ConsumptionMode,
			TimeChargeType:     reqDto.TimeChargeType,
			TimeChargeMode:     reqDto.TimeChargeMode,
			IsGift:             util.Ptr(false),
			IsFree:             reqDto.IsFree,
			OriginalPayAmount:  util.Ptr(orderRoomPlanVO.OriginalPayAmount),
			MinimumCharge:      &lastMinimumCharge,
			IsTimeConsume:      &isTimeConsume,
		}
		roomOrderVO.OrderRoomPlanVOs = append(roomOrderVO.OrderRoomPlanVOs, s.roomplanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderVOs = append(orderVOs, roomOrderVO)

	// 4. 获取商品套餐分类信息
	productIds := []string{}
	packageIds := []string{}
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		util.AddListElement(&productIds, orderProductVO.ProductId)
		util.AddListElement(&packageIds, orderProductVO.PackageId)
	}
	_, _, _, _, productIdToProductTypeVO, productIdToProductPackageTypeVO, err := s.orderService.GetProductAndPackageInfo(ctx, *venueId, productIds, packageIds)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("获取商品信息失败: %w", err)
	}

	// 4. 处理商品
	// 4.1 商品信息-套餐内
	// 4.1.1 创建Order对象-套餐内订单
	subInOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productInOrderVO := vo.OrderVO{}
	if len(reqDto.InOrderProductInfos) > 0 {
		toAddOrderForInProduct := &po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     sessionId,
			OrderNo:       &subInOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: reqDto.MinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
		}
		productInOrderVO = s.orderService.ConvertToOrderVO(ctx, *toAddOrderForInProduct)
	}
	// 4.1.2 处理套餐内商品
	for _, orderProductVO := range reqDto.InOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subInOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_IN),
			IsFree:              reqDto.IsFree,
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productInOrderVO.OrderProductVOs = append(productInOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.InOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productInOrderVO)
	}

	// 4.2 商品信息-套餐外
	// 4.2.1 创建Order对象-套餐外订单
	subOutOrderNo := util.GetOrderNo(*reqDto.VenueId)
	productOutOrderVO := vo.OrderVO{}
	if len(reqDto.OutOrderProductInfos) > 0 {
		productOutOrder := po.Order{
			VenueId:       venueId,
			RoomId:        roomId,
			SessionId:     sessionId,
			OrderNo:       &subOutOrderNo,
			EmployeeId:    reqDto.EmployeeId,
			MemberId:      reqDto.MemberId,
			MemberCardId:  reqDto.MemberCardId,
			MemberCardNumber: reqDto.MemberCardNumber,
			MinimumCharge: reqDto.MinimumCharge,
			Type:          util.Ptr(_const.V2_ORDER_TYPE_PRODUCT),
			Direction:     util.Ptr(_const.V2_ORDER_DIRECTION_NORMAL),
			MarkType:      util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
			Status:        util.Ptr(_const.V2_ORDER_STATUS_UNPAID),
			Mark:          util.Ptr(_const.V2_ORDER_MARK_OPENING_CONTINUE),
		}
		productOutOrderVO = s.orderService.ConvertToOrderVO(ctx, productOutOrder)
	}
	// 4.2.2 处理套餐外商品
	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		categoryId, categoryName := s.orderService.GetCategoryInfo(ctx, *orderProductVO, productIdToProductTypeVO, productIdToProductPackageTypeVO)
		orderProduct := po.OrderProduct{
			VenueId:             venueId,
			RoomId:              roomId,
			SessionId:           sessionId,
			MemberId:            reqDto.MemberId,
			MemberCardId:        reqDto.MemberCardId,
			MemberCardNumber:    reqDto.MemberCardNumber,
			EmployeeId:          reqDto.EmployeeId,
			OrderNo:             &subOutOrderNo,
			PackageId:           util.Ptr(orderProductVO.PackageId),
			PackageProductInfo:  util.Ptr(orderProductVO.PackageProductInfo),
			ProductId:           util.Ptr(orderProductVO.ProductId),
			ProductName:         util.Ptr(orderProductVO.ProductName),
			CategoryId:          &categoryId,
			CategoryName:        &categoryName,
			Flavors:             util.Ptr(orderProductVO.Flavors),
			Unit:                util.Ptr(orderProductVO.Unit),
			Quantity:            util.Ptr(orderProductVO.Quantity),
			OriginalPrice:       util.Ptr(orderProductVO.OriginalPrice),
			MemberPrice:         util.Ptr(orderProductVO.MemberPrice),
			ProductDiscountable: util.Ptr(true),
			Src:                 util.Ptr(_const.V2_ORDER_PRODUCT_SRC_OUT),
			IsFree:              reqDto.IsFree,
			IsGift:              util.Ptr(orderProductVO.IsGift),
		}
		productOutOrderVO.OrderProductVOs = append(productOutOrderVO.OrderProductVOs, s.orderproductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	if len(reqDto.OutOrderProductInfos) > 0 {
		orderVOs = append(orderVOs, productOutOrderVO)
	}

	// 5. 计算费用
	tmpPayBillVO := vo.PayBillVO{
		ProductDiscount:       util.GetPtrSafeDefault(reqDto.ProductDiscount, 100),
		RoomDiscount:          util.GetPtrSafeDefault(reqDto.RoomDiscount, 100),
		IsFree:                util.GetPtrSafeDefault(reqDto.IsFree, false),
		ProductDiscountAmount: util.GetPtrSafeDefault(reqDto.ProductDiscountAmount, 0),
		RoomDiscountAmount:    util.GetPtrSafeDefault(reqDto.RoomDiscountAmount, 0),
		ForceMinimumCharge:    true,
	}
	tmpSessionVO := vo.SessionVO{
		SessionId:  *sessionId,
		MinConsume: lastMinimumCharge,
	}
	totalFeeThis, newOps, newOms, retProductDiscountFee, retRoomDiscountFee := s.payService.CalculateTotalPayment(ctx, tmpSessionVO, orderVOs, tmpPayBillVO)

	// 6. 处理订单
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, op := range newOps {
		opVO := s.orderproductService.ConvertToOrderProduct(ctx, op)
		opNew := s.orderproductService.TrimIdToAddOrderProduct(ctx, opVO)
		newOrderProducts = append(newOrderProducts, opNew)
	}
	for _, om := range newOms {
		omVO := s.roomplanService.ConvertToOrderRoomPlan(ctx, om)
		omNew := s.roomplanService.TrimIdToAddOrderRoomPlan(ctx, omVO)
		newOrderRoomPlans = append(newOrderRoomPlans, omNew)
	}

	// 7. 计算费用
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFee(ctx, totalFeeThis, newOps, newOms, *sessionId, *reqDto.VenueId, lastMinimumCharge)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("计算费用失败: %w", err)
	}

	// 8. 处理订单
	toAddOrders := make([]po.Order, 0)
	for _, orderVO := range orderVOs {
		orderTmpVO := s.orderService.ConvertToOrderPO(ctx, orderVO)
		orderTmp := s.orderService.TrimIdToAddOrderPO(ctx, orderTmpVO)
		toAddOrders = append(toAddOrders, orderTmp)
	}

	// 8.a. 设置收款单信息
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:               reqDto.VenueId,
		RoomId:                reqDto.RoomId,
		EmployeeId:            reqDto.EmployeeId,
		SessionId:             sessionId,
		MemberId:              reqDto.MemberId,
		MemberCardId:          reqDto.MemberCardId,
		MemberCardNumber:      reqDto.MemberCardNumber,
		BillId:                &billId,
		OriginalFee:           reqDto.OriginalFee,
		TotalFee:              reqDto.TotalFee,
		ShouldFee:             reqDto.ShouldFee,
		ZeroFee:               reqDto.ZeroFee,
		CreditAmount:          reqDto.CreditAmount,
		ProductDiscount:       reqDto.ProductDiscount,
		RoomDiscount:          reqDto.RoomDiscount,
		ProductDiscountFee:    &retProductDiscountFee,
		RoomDiscountFee:       &retRoomDiscountFee,
		IsFree:                reqDto.IsFree,
		ProductDiscountAmount: reqDto.ProductDiscountAmount,
		RoomDiscountAmount:    reqDto.RoomDiscountAmount,
		ChangeAmount:          reqDto.ChangeAmount,
		Status:                util.Ptr(_const.V2_PAY_BILL_STATUS_WAIT),
		Direction:             util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
		DiscountReason:        reqDto.DiscountReason,
	}
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	// 8.b. 设置订单支付记录信息
	for _, orderVO := range orderVOs {
		orderNoTmp := orderVO.OrderNo
		// 剔除计时支付订单
		if isTimeConsume && orderNoTmp == roomOrderNo {
			continue
		}
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   &orderNoTmp,
			SessionId: sessionId,
			BillId:    &billId,
		})
	}
	// 8.c. 设置订单支付记录信息
	toAddPayRecords := make([]po.PayRecord, 0)
	payRecordAllPayAmount := int64(0)
	for _, payRecord := range *reqDto.PayRecords {
		payId := util.GetPayId(*reqDto.VenueId)
		totalFeeTmp := payRecord.TotalFee
		payTypeTmp := payRecord.PayType
		toAddPayRecords = append(toAddPayRecords, po.PayRecord{
			VenueId:                 reqDto.VenueId,
			RoomId:                  reqDto.RoomId,
			EmployeeId:              reqDto.EmployeeId,
			MemberCardId:            &payRecord.MemberCardId,
			MemberCardNumber:        reqDto.MemberCardNumber,
			SessionId:               sessionId,
			BillId:                  &billId,
			PayId:                   &payId,
			TotalFee:                &totalFeeTmp,
			PrincipalAmount:         &payRecord.PrincipalAmount,
			MemberRoomBonusAmount:   &payRecord.MemberRoomBonusAmount,
			MemberGoodsBonusAmount:  &payRecord.MemberGoodsBonusAmount,
			MemberCommonBonusAmount: &payRecord.MemberCommonBonusAmount,
			Status:                  util.Ptr(_const.PAY_STATUS_UNPAID),
			PayType:                 &payTypeTmp,
			ProductName:             util.GetPayProductName(venue.Name, venue.Id),
			BQROneCode:              &payRecord.BQROneCode,
		})
		payRecordAllPayAmount += payRecord.TotalFee
	}
	if _, msg, err := util.FeeDiff(payRecordAllPayAmount, *reqDto.TotalFee); err != nil {
		return defaultSessionVO, fmt.Errorf("应付金额与支付记录金额不一致: %s", msg)
	}
	if _, msg, err := util.FeeDiff(totalFeeThis, *reqDto.TotalFee+*reqDto.ZeroFee-*reqDto.ChangeAmount); err != nil {
		return defaultSessionVO, fmt.Errorf("订单金额与计算订单金额不一致: %s", msg)
	}

	// 9. 创建Session
	toUpdatesession := po.Session{
		Id:             session.Id,
		PayStatus:      util.Ptr(_const.V2_SESSION_PAY_STATUS_UNPAID),
		EndTime:        reqDto.EndTime,
		MinConsume:     reqDto.MinimumCharge,
		RoomFee:        &totalRoomFee,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
		EmployeeId:     reqDto.EmployeeId,
		IsTimeConsume:  &isTimeConsume,
	}

	// 10. 更新房间状态
	toUpdateRoom := po.Room{
		Id:        roomId,
		SessionId: sessionId,
		Status:    util.Ptr(_const.ROOM_STATUS_IN_USE),
	}
	// 1.3. 验证会员卡ID
	for _, payRecordVO := range *reqDto.PayRecords {
		if payRecordVO.PayType == _const.PAY_TYPE_MEMBER_CARD {
			roomFee := int64(0)
			supermarketFee := int64(0)
			for _, orderProductVO := range newOrderProducts {
				supermarketFee += *orderProductVO.PayAmount
			}
			for _, orderRoomPlanVO := range newOrderRoomPlans {
				roomFee += *orderRoomPlanVO.PayAmount
			}
			// 1.2.1. 验证会员卡ID-扣款的
			_, err := s.utilValidateMemberCard(ctx, venue, &payRecordVO.MemberCardId)
			if err != nil {
				return defaultSessionVO, err
			}
			_, err = s.memberRechargeBillService.V3RPCMemberCardVaildBalance(ctx, req.V3QueryMemberCardQueryBalanceReqDto{
				MemberCardId:      &payRecordVO.MemberCardId,
				TotalAmount:       &payRecordVO.TotalFee,
				PrincipalAmount:   &payRecordVO.PrincipalAmount,
				RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
				GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
				CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
				RoomTotalAmount:   &roomFee,
				GoodsTotalAmount:  &supermarketFee,
			})
			if err != nil {
				return defaultSessionVO, err
			}
		}
	}
	// 11.1 优先从会员卡中扣款
	for _, payRecord := range toAddPayRecords {
		payRecordVO := s.payRecordService.ConvertToPayRecordVO(ctx, payRecord)
		// 跳过非会员卡支付
		if payRecordVO.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 检查会员卡余额
		_, err := s.memberRechargeBillService.V3RPCMemberCardPay(ctx, req.V3RPCPayMoneyReqDto{
			VenueId:           reqDto.VenueId,
			EmployeeId:        reqDto.EmployeeId,
			MemberCardId:      &payRecordVO.MemberCardId,
			Amount:            &payRecordVO.PrincipalAmount,
			RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
			GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
			CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
			PayId:             &payRecordVO.PayId,
		})
		if err != nil {
			return defaultSessionVO, fmt.Errorf("会员卡扣款失败: %w", err)
		}
	}

	// 11.1 保存订单信息
	_, _, _, _, err = s.orderService.SaveOrderOpenContinuePayInfo(ctx, toAddOrders, newOrderProducts, newOrderRoomPlans, toUpdatesession, toUpdateRoom, toAddPayBill, toAddOrderAndPays, toAddPayRecords)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("保存订单信息失败: %w", err)
	}
	sessionLater, err := s.sessionService.FindBySessionId(ctx, *sessionId, *reqDto.VenueId)
	if err != nil {
		sessionLater = toUpdatesession
	}
	newSessionVO := s.sessionService.ConvertToSessionVO(ctx, sessionLater)

	// 11.1 保存会员卡消费记录
	s.memberCardConsumeService.RecordMemberCardConsume(ctx, session, venue, room, employee, toAddPayRecords, toAddPayBill)

	// 11. 处理支付信息
	// 11.b.1 发起支付
	payResultVOs, err := s.payService.V3TransformPayGate(ctx, &req.V3QueryOrderPayTransformReqDto{
		PayRecords:    reqDto.PayRecords,
		NewPayRecords: toAddPayRecords,
		VenueId:       reqDto.VenueId,
		RoomId:        reqDto.RoomId,
		SessionId:     reqDto.SessionId,
		EmployeeId:    reqDto.EmployeeId,
	}, &toAddPayBill)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("发起支付失败: %w", err)
	}

	// 12. 保存支付信息回调
	for _, payRecord := range toAddPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, vo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_COMMON})
		if err != nil {
			return defaultSessionVO, fmt.Errorf("保存支付信息失败: %w", err)
		}
	}
	newSessionVO.PayResultVOs = payResultVOs

	for _, orderVO := range orderVOs {
		newSessionVO.RtOrderNos = append(newSessionVO.RtOrderNos, orderVO.OrderNo)
	}
	newSessionVO.PayBills = []vo.PayBillVO{s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, false, toAddPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return newSessionVO, nil
}

// V3GiftProduct 赠送商品
// 入参： op
// 将op 保存为 order
func (s *OrderApplicationServiceImpl) V3GiftProduct(ctx context.Context, reqDto req.V3OrderGiftProductReqDto) (vo.OrderVO, error) {
	defaultOrderVO := vo.OrderVO{}
	// 1. 验证入参
	// 0. 验证基础参数 venueId,roomId,sessionId
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return defaultOrderVO, fmt.Errorf("VenueId不能为空")
	}
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return defaultOrderVO, fmt.Errorf("RoomId不能为空")
	}
	if reqDto.SessionId == nil || *reqDto.SessionId == "" {
		return defaultOrderVO, fmt.Errorf("SessionId不能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return defaultOrderVO, fmt.Errorf("EmployeeId不能为空")
	}
	// 0.1. 检查ktv是否存在
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return defaultOrderVO, fmt.Errorf("门店不存在")
	}
	// 0.2. 检查房间状态
	room, err := s.roomService.FindByID(ctx, *reqDto.RoomId)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("房间不存在")
	}
	if room.VenueId == nil || *room.VenueId != *reqDto.VenueId {
		return defaultOrderVO, fmt.Errorf("房间不属于该门店")
	}
	// 0.3. 检查场次是否存在
	_, err = s.sessionService.FindBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("场次信息不存在")
	}
	// 0.4. 检查员工是否存在
	employee, err := s.employeeService.FindByID(ctx, *reqDto.EmployeeId)
	if err != nil || employee == nil {
		return defaultOrderVO, fmt.Errorf("员工不存在")
	}

	// 1. 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, *room) {
		return defaultOrderVO, fmt.Errorf("房间已锁定,不能赠送商品")
	}

	// 2.a. 创建订单商品数据
	additionalOrderNo := util.GetOrderNo(*reqDto.VenueId)
	// 4. 创建订单
	toAddOrder := po.Order{
		VenueId:    reqDto.VenueId,
		RoomId:     reqDto.RoomId,
		SessionId:  reqDto.SessionId,
		OrderNo:    &additionalOrderNo,
		EmployeeId: reqDto.EmployeeId,
		MemberId:   reqDto.MemberId,
		Type:       util.GetItPtr(_const.V2_ORDER_TYPE_PRODUCT),
		Direction:  util.GetItPtr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:   util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:     util.GetItPtr(string(_const.V2_ORDER_STATUS_PAID)),
		Mark:       util.GetItPtr(string(_const.V2_ORDER_MARK_GIFT_PRODUCT)),
	}
	toAddOrderProducts := make([]po.OrderProduct, 0)
	originalPayAmount := int64(0)
	for _, orderProductVO := range reqDto.OrderProductVOs {
		orderProduct := po.OrderProduct{
			VenueId:            reqDto.VenueId,
			RoomId:             reqDto.RoomId,
			SessionId:          reqDto.SessionId,
			EmployeeId:         reqDto.EmployeeId,
			MemberId:           reqDto.MemberId,
			OrderNo:            &additionalOrderNo,
			IsGift:             util.GetItPtr(true),
			ProductId:          util.GetItPtr(orderProductVO.ProductId),
			PackageId:          util.GetItPtr(orderProductVO.PackageId),
			PackageProductInfo: util.Ptr(orderProductVO.PackageProductInfo),
			ProductName:        util.GetItPtr(orderProductVO.ProductName),
			Flavors:            util.GetItPtr(orderProductVO.Flavors),
			Unit:               util.GetItPtr(orderProductVO.Unit),
			Quantity:           util.GetItPtr(orderProductVO.Quantity),
			OriginalPrice:      util.GetItPtr(orderProductVO.OriginalPrice),
			MemberPrice:        util.GetItPtr(orderProductVO.MemberPrice),
			Mark:               util.GetItPtr(orderProductVO.Mark),
			Src:                util.GetItPtr(_const.V2_ORDER_PRODUCT_SRC_OUT),
		}
		originalPayAmount += *orderProduct.OriginalPrice * *orderProduct.Quantity
		toAddOrderProducts = append(toAddOrderProducts, orderProduct)
	}

	// 生成paybill
	nowTime := util.TimeNowUnixInt64()
	defaultDiscount := int64(100)
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:          reqDto.VenueId,
		RoomId:           reqDto.RoomId,
		EmployeeId:       reqDto.EmployeeId,
		SessionId:        reqDto.SessionId,
		BillId:           &billId,
		OriginalFee:      &originalPayAmount,
		ShouldFee:        &originalPayAmount,
		TotalFee:         new(int64),
		ZeroFee:          new(int64),
		ChangeAmount:     new(int64),
		ProductDiscount:  &defaultDiscount,
		RoomDiscount:     &defaultDiscount,
		IsFree:           util.GetItPtr(false),
		Direction:        util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
		Status:           util.GetItPtr(string(_const.V2_PAY_BILL_STATUS_PAID)),
		FinishTime:       util.Ptr(nowTime),
		IsGift:           util.GetItPtr(true),
		GiftEmployeeId:   reqDto.GiftEmployeeId,
		GiftEmployeeName: reqDto.GiftEmployeeName,
		GiftReason:       reqDto.GiftReason,
	}
	// 生成OrderAndPay
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	for _, orderProduct := range toAddOrderProducts {
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   orderProduct.OrderNo,
			SessionId: orderProduct.SessionId,
			BillId:    &billId,
		})
	}

	// 6. 更新员工赠品额度
	toAddEmployeeGiftRecord := po.EmployeeGiftRecord{
		VenueId:      reqDto.VenueId,
		RoomId:       reqDto.RoomId,
		RoomName:     room.Name,
		SessionId:    reqDto.SessionId,
		EmployeeId:   reqDto.EmployeeId,
		EmployeeName: util.Ptr(""),
		OperateType:  util.GetItPtr(string(_const.V2_ORDER_TYPE_PRODUCT)),
		Amount:       &originalPayAmount,
		Quota:        new(int64),
		RemainQuota:  new(int64),
		MemberId:     util.Ptr(""),
		MemberName:   util.Ptr(""),
		Info:         util.Ptr("赠送商品"),
	}

	// 5. 保存数据
	err = s.orderService.SaveOrderInfoGiftProduct(ctx, []po.Order{toAddOrder}, toAddOrderProducts, []po.PayBill{toAddPayBill}, toAddOrderAndPays, []po.EmployeeGiftRecord{toAddEmployeeGiftRecord})
	if err != nil {
		return defaultOrderVO, fmt.Errorf("点单失败")
	}
	// 更新库存
	s.orderService.SyncProductStock(ctx, venue, _const.V2_ORDER_DIRECTION_NORMAL, toAddOrderProducts)

	// 执行流程
	defaultOrderVO = s.orderService.ConvertToOrderVO(ctx, toAddOrder)
	payBillVO := s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)
	defaultOrderVO.PayBillVO = &payBillVO
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *reqDto.VenueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return defaultOrderVO, nil
}

// V3GiftTime 赠送时长
// 入参： om
// 将om 保存为 order
func (s *OrderApplicationServiceImpl) V3GiftTime(ctx context.Context, reqDto req.V3OrderGiftTimeReqDto) (vo.OrderVO, error) {
	defaultOrderVO := vo.OrderVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId
	employeeId := reqDto.EmployeeId

	// 1. 验证输入参数
	if venueId == nil || *venueId == "" {
		return defaultOrderVO, fmt.Errorf("venueId为空")
	}
	if roomId == nil || *roomId == "" {
		return defaultOrderVO, fmt.Errorf("roomId为空")
	}
	if sessionId == nil || *sessionId == "" {
		return defaultOrderVO, fmt.Errorf("sessionId为空")
	}
	_, room, sessionDB, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, employeeId, true)
	if err != nil {
		return defaultOrderVO, err
	}
	if reqDto.GiftMinute == nil || *reqDto.GiftMinute <= 0 {
		return defaultOrderVO, fmt.Errorf("GiftMinute不能为空或小于0")
	}
	if sessionDB.EndTime != nil && *sessionDB.EndTime < int64(util.TimeNowUnix()) {
		return defaultOrderVO, fmt.Errorf("场次已结束，不能赠送时长")
	}

	// 2. 检查时间
	serverTimeSecond := int64(util.TimeNowUnix())
	if reqDto.StartTime == nil || *reqDto.StartTime <= 0 {
		reqDto.StartTime = &serverTimeSecond
	}
	if err := util.CheckRoomStatusGiftTime(*room.Status); err != nil {
		return defaultOrderVO, err
	}
	// 1.4 检查房间是否锁定
	if s.roomService.IsRoomLocked(ctx, room) {
		return defaultOrderVO, fmt.Errorf("房间已锁定,不能赠送时长")
	}

	// 4. 生成订单号和场次ID
	roomOrderNo := util.GetOrderNo(*reqDto.VenueId)
	// 5. 创建Order对象-主开台订单
	toAddOrder := po.Order{
		VenueId:    venueId,
		RoomId:     roomId,
		SessionId:  sessionId,
		OrderNo:    &roomOrderNo,
		EmployeeId: reqDto.EmployeeId,
		MemberId:   reqDto.MemberId,
		Type:       util.GetItPtr(_const.V2_ORDER_TYPE_ROOMPLAN),
		Direction:  util.GetItPtr(_const.V2_ORDER_DIRECTION_NORMAL),
		MarkType:   util.Ptr(_const.V2_ORDER_MARK_TYPE_NORMAL),
		Status:     util.GetItPtr(_const.V2_ORDER_STATUS_PAID),
		Mark:       util.GetItPtr(_const.V2_ORDER_MARK_GIFT_TIME),
	}

	roomType, err := s.roomTypeService.FindByID(ctx, *room.TypeId)
	if err != nil {
		return defaultOrderVO, fmt.Errorf("房间类型不存在")
	}
	perHourPrice, err := strconv.Atoi(*roomType.TimeChargeBasePlan)
	if err != nil {
		logrus.Error("房间类型基础价格错误", err)
		perHourPrice = 0
	}
	startTime := *sessionDB.EndTime
	endTime := startTime + int64(*reqDto.GiftMinute)*60
	duration := int(endTime - startTime)
	originalPayAmount := int64(math.Round(float64(duration) / 3600 * float64(perHourPrice)))
	// 7. 构造OrderRoomPlans
	toAddOrderRoomPlans := []po.OrderRoomPlan{
		{
			VenueId:               venueId,
			RoomId:                roomId,
			SessionId:             sessionId,
			EmployeeId:            reqDto.EmployeeId,
			MemberId:              reqDto.MemberId,
			OrderNo:               &roomOrderNo,
			RoomName:              room.Name,
			StartTime:             &startTime,
			EndTime:               &endTime,
			Duration:              &duration,
			MemberPrice:           new(int64),
			MemberDiscount:        util.Ptr(int64(100)),
			PayRoomDiscount:       util.Ptr(int64(100)),
			PayRoomDiscountAmount: new(int64),
			IsGift:                util.GetItPtr(true),
			OriginalPayAmount:     &originalPayAmount,
			PayAmount:             new(int64),
		},
	}

	// 生成paybill
	nowTime := util.TimeNowUnixInt64()
	defaultDiscount := int64(100)
	billId := util.GetBillId(*reqDto.VenueId)
	toAddPayBill := po.PayBill{
		VenueId:          reqDto.VenueId,
		RoomId:           reqDto.RoomId,
		EmployeeId:       reqDto.EmployeeId,
		SessionId:        reqDto.SessionId,
		BillId:           &billId,
		OriginalFee:      &originalPayAmount,
		ShouldFee:        &originalPayAmount,
		TotalFee:         new(int64),
		ZeroFee:          new(int64),
		ChangeAmount:     new(int64),
		ProductDiscount:  &defaultDiscount,
		RoomDiscount:     &defaultDiscount,
		IsFree:           util.GetItPtr(false),
		Direction:        util.Ptr(_const.V2_PAY_BILL_DIRECTION_NORMAL),
		Status:           util.GetItPtr(string(_const.V2_PAY_BILL_STATUS_PAID)),
		FinishTime:       util.Ptr(nowTime),
		IsGift:           util.GetItPtr(true),
		GiftEmployeeId:   reqDto.GiftEmployeeId,
		GiftEmployeeName: reqDto.GiftEmployeeName,
		GiftReason:       reqDto.GiftReason,
	}
	// 生成OrderAndPay
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	for _, orderRoomPlan := range toAddOrderRoomPlans {
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   orderRoomPlan.OrderNo,
			SessionId: orderRoomPlan.SessionId,
			BillId:    &billId,
		})
	}

	// 10. Session 更新结束时间
	toUpdateSession := po.Session{
		Id:         sessionDB.Id,
		EndTime:    &endTime,
		EmployeeId: reqDto.EmployeeId,
	}

	// 6. 更新员工赠品额度
	toAddEmployeeGiftRecord := po.EmployeeGiftRecord{
		VenueId:      reqDto.VenueId,
		RoomId:       reqDto.RoomId,
		RoomName:     room.Name,
		SessionId:    reqDto.SessionId,
		EmployeeId:   reqDto.EmployeeId,
		EmployeeName: util.Ptr(""),
		OperateType:  util.GetItPtr(string(_const.V2_ORDER_TYPE_ROOMPLAN)),
		Amount:       &originalPayAmount,
		Quota:        new(int64),
		RemainQuota:  new(int64),
		MemberId:     util.Ptr(""),
		MemberName:   util.Ptr(""),
		Info:         util.Ptr("赠送商品"),
	}

	err = s.orderService.SaveOrderInfoGiftTime(ctx, []po.Order{toAddOrder}, toAddOrderRoomPlans, toUpdateSession, []po.PayBill{toAddPayBill}, toAddOrderAndPays, []po.EmployeeGiftRecord{toAddEmployeeGiftRecord})
	if err != nil {
		return defaultOrderVO, fmt.Errorf("保存订单信息失败: %w", err)
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	defaultOrderVO = s.orderService.ConvertToOrderVO(ctx, toAddOrder)
	payBillVO := s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)
	defaultOrderVO.PayBillVO = &payBillVO
	return defaultOrderVO, nil
}

// V3CloseRoom 关房
func (s *OrderApplicationServiceImpl) V3CloseRoom(ctx context.Context, reqDto req.V3AddOrderCloseRoomReqDto) error {
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	sessionId := reqDto.SessionId
	if venueId == nil || *venueId == "" {
		return fmt.Errorf("venueId不能为空")
	}
	if roomId == nil || *roomId == "" {
		return fmt.Errorf("roomId不能为空")
	}
	if sessionId == nil || *sessionId == "" {
		return fmt.Errorf("sessionId不能为空")
	}
	room, err := s.roomService.GetRoom(ctx, *roomId)
	if err != nil {
		return fmt.Errorf("未查询到房间信息")
	}
	_, err = s.venueService.FindByVenueID(ctx, *venueId)
	if err != nil {
		return fmt.Errorf("未查询到ktv信息")
	}
	if *venueId != *room.VenueId {
		return fmt.Errorf("房间不属于当前门店")
	}
	session, err := s.sessionService.FindBySessionId(ctx, *sessionId, *venueId)
	if err != nil {
		return fmt.Errorf("未查询到session信息")
	}
	if room.SessionId == nil || *room.SessionId == "" {
		return fmt.Errorf("房间未开台")
	}
	if *room.SessionId != *sessionId {
		return fmt.Errorf("场次信息不属于当前房间")
	}
	if err = util.CheckRoomStatusCloseRoom(*room.Status); err != nil {
		return err
	}

	// 1. 房间未结账-> 未结账
	// 2. 房间已结账-> 清理
	// 3. 判断是否为联房
	roomAttachs, err := s.roomService.GetAttachRooms(ctx, room)
	if err != nil {
		return fmt.Errorf("查询出错")
	}
	toUpdateRooms := []po.Room{}

	// 通过sessionId找到所有的分组房间
	for _, room := range roomAttachs {
		tmpRoomId := *room.Id
		newRoom := &po.Room{
			Id: &tmpRoomId,
		}

		if *room.Status == _const.ROOM_STATUS_IN_USE && session.PayStatus != nil && *session.PayStatus == _const.ORDER_STATUS_PAID {
			newRoom.SessionId = new(string)
			newRoom.Status = util.Ptr(_const.ROOM_STATUS_CLEANING)
			newRoom.Tag = new(string)
			toUpdateRooms = append(toUpdateRooms, *newRoom)
		}
	}

	toUpdateSessions := []po.Session{
		{
			Id:        session.Id,
			Status:    util.Ptr(_const.V2_SESSION_STATUS_ENDING),
			CloseTime: util.Ptr(int64(util.TimeNowUnix())),
		},
	}

	err = s.roomService.CloseRoom(ctx, toUpdateRooms, toUpdateSessions)
	if err != nil {
		return err
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return nil
}

// V3CleanRoomFinish 清理房间完成
func (s *OrderApplicationServiceImpl) V3CleanRoomFinish(ctx context.Context, reqDto req.V3QueryOrderCleanRoomFinishReqDto) error {
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	if venueId == nil || *venueId == "" {
		return fmt.Errorf("venueId不能为空")
	}
	if roomId == nil || *roomId == "" {
		return fmt.Errorf("roomId不能为空")
	}
	room, err := s.roomService.GetRoom(ctx, *roomId)
	if err != nil {
		return fmt.Errorf("未查询到房间信息")
	}
	_, err = s.venueService.FindByVenueID(ctx, *venueId)
	if err != nil {
		return fmt.Errorf("未查询到ktv信息")
	}
	if *venueId != *room.VenueId {
		return fmt.Errorf("房间不属于当前门店")
	}
	if err = util.CheckRoomStatusCleanRoomFinish(*room.Status); err != nil {
		return err
	}

	toUpdateRoom := po.Room{
		Id:        roomId,
		Status:    util.Ptr(_const.ROOM_STATUS_IDLE),
		SessionId: util.Ptr(""),
	}
	err = s.roomService.CleanRoomFinish(ctx, []po.Room{toUpdateRoom})
	if err != nil {
		return err
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return nil
}

// V3SwapRoom 互换包房
func (s *OrderApplicationServiceImpl) V3SwapRoom(ctx context.Context, reqDto req.V3SwapRoomReqDto) (vo.SessionVO, error) {
	defaultSessionVO := vo.SessionVO{}

	// 1. 验证入参
	venueId := reqDto.VenueId
	roomIdA := reqDto.SessionVOOpeningA.RoomId
	roomIdB := reqDto.SessionVOOpeningB.RoomId
	sessionIdA := reqDto.SessionVOOpeningA.SessionId
	sessionIdB := reqDto.SessionVOOpeningB.SessionId
	if venueId == nil || *venueId == "" {
		return defaultSessionVO, fmt.Errorf("venueId不能为空")
	}
	if roomIdA == "" {
		return defaultSessionVO, fmt.Errorf("roomIdA不能为空")
	}
	if roomIdB == "" {
		return defaultSessionVO, fmt.Errorf("roomIdB不能为空")
	}
	if sessionIdA == "" {
		return defaultSessionVO, fmt.Errorf("sessionIdA不能为空")
	}
	if sessionIdB == "" {
		return defaultSessionVO, fmt.Errorf("sessionIdB不能为空")
	}
	if roomIdA == roomIdB {
		return defaultSessionVO, fmt.Errorf("房间不能相同")
	}
	if sessionIdA == sessionIdB {
		return defaultSessionVO, fmt.Errorf("场次不能相同")
	}
	// 2. 验证ktv是否存在
	_, err := s.venueService.FindByVenueID(ctx, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到ktv信息")
	}
	// 3. 验证A房间是否存在
	roomA, err := s.roomService.GetRoom(ctx, roomIdA)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到房间信息")
	}
	if roomA.VenueId == nil || *roomA.VenueId != *venueId {
		return defaultSessionVO, fmt.Errorf("A房间不属于当前门店")
	}
	if roomA.SessionId == nil || *roomA.SessionId == "" {
		return defaultSessionVO, fmt.Errorf("A房间未开台")
	}
	if *roomA.SessionId != sessionIdA {
		return defaultSessionVO, fmt.Errorf("A房间不属于当前场次")
	}
	// 4. 验证B房间是否存在
	roomB, err := s.roomService.GetRoom(ctx, roomIdB)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到房间信息")
	}
	if roomB.VenueId == nil || *roomB.VenueId != *venueId {
		return defaultSessionVO, fmt.Errorf("B房间不属于当前门店")
	}
	if roomB.SessionId == nil || *roomB.SessionId == "" {
		return defaultSessionVO, fmt.Errorf("B房间未开台")
	}
	if *roomB.SessionId != sessionIdB {
		return defaultSessionVO, fmt.Errorf("B房间不属于当前场次")
	}
	// 5. 验证场次是否存在
	sessionA, err := s.sessionService.FindBySessionId(ctx, sessionIdA, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到场次信息")
	}
	sessionB, err := s.sessionService.FindBySessionId(ctx, sessionIdB, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到场次信息")
	}
	// room.SessionId <>
	// session.RoomId <>
	// order.RoomId <>
	// orderRoomPlan.RoomId <>
	// orderProduct.RoomId <>
	// payBill.RoomId <>
	// payRecord.RoomId <>
	// sessionVOOpeningA -> sessionVOOpeningB
	// A -> B
	orderPOInfoUnionVOA := s.payService.GetOrderPOInfoBySessionId(ctx, sessionIdA, *venueId)
	orderPOInfoUnionVOB := s.payService.GetOrderPOInfoBySessionId(ctx, sessionIdB, *venueId)

	newOrderPOInfoUnionVOA, newSessionA, newRoomA := s.payService.GetNewSwapInfo(ctx, orderPOInfoUnionVOA, sessionA, roomA, sessionIdB, roomIdB)
	newOrderPOInfoUnionVOB, newSessionB, newRoomB := s.payService.GetNewSwapInfo(ctx, orderPOInfoUnionVOB, sessionB, roomB, sessionIdA, roomIdA)

	err = s.orderService.SaveOrderInfoSwapRoom(ctx, newOrderPOInfoUnionVOA, newOrderPOInfoUnionVOB, newSessionA, newRoomA, newSessionB, newRoomB)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("保存订单信息失败")
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return defaultSessionVO, nil
}

// V3MergeRoom 并房
func (s *OrderApplicationServiceImpl) V3MergeRoom(ctx context.Context, reqDto req.V3MergeRoomReqDto) (vo.SessionVO, error) {
	defaultSessionVO := vo.SessionVO{}
	// 1. 验证入参
	venueId := reqDto.VenueId
	roomIdA := reqDto.SessionVOOpeningA.RoomId
	roomIdB := reqDto.SessionVOOpeningB.RoomId
	sessionIdA := reqDto.SessionVOOpeningA.SessionId
	sessionIdB := reqDto.SessionVOOpeningB.SessionId
	if venueId == nil || *venueId == "" {
		return defaultSessionVO, fmt.Errorf("venueId不能为空")
	}
	if roomIdA == "" {
		return defaultSessionVO, fmt.Errorf("roomIdA不能为空")
	}
	if roomIdB == "" {
		return defaultSessionVO, fmt.Errorf("roomIdB不能为空")
	}
	if sessionIdA == "" {
		return defaultSessionVO, fmt.Errorf("sessionIdA不能为空")
	}
	if sessionIdB == "" {
		return defaultSessionVO, fmt.Errorf("sessionIdB不能为空")
	}
	if roomIdA == roomIdB {
		return defaultSessionVO, fmt.Errorf("房间不能相同")
	}
	if sessionIdA == sessionIdB {
		return defaultSessionVO, fmt.Errorf("场次不能相同")
	}
	// 2. 验证ktv是否存在
	_, err := s.venueService.FindByVenueID(ctx, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到ktv信息")
	}
	// 3. 验证A房间是否存在
	roomA, err := s.roomService.GetRoom(ctx, roomIdA)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到房间信息")
	}
	if roomA.VenueId == nil || *roomA.VenueId != *venueId {
		return defaultSessionVO, fmt.Errorf("A房间不属于当前门店")
	}
	if roomA.SessionId == nil || *roomA.SessionId == "" {
		return defaultSessionVO, fmt.Errorf("A房间未开台")
	}
	if *roomA.SessionId != sessionIdA {
		return defaultSessionVO, fmt.Errorf("A房间不属于当前场次")
	}
	// 4. 验证B房间是否存在
	roomB, err := s.roomService.GetRoom(ctx, roomIdB)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到房间信息")
	}
	if roomB.VenueId == nil || *roomB.VenueId != *venueId {
		return defaultSessionVO, fmt.Errorf("B房间不属于当前门店")
	}
	if roomB.SessionId == nil || *roomB.SessionId == "" {
		return defaultSessionVO, fmt.Errorf("B房间未开台")
	}
	if *roomB.SessionId != sessionIdB {
		return defaultSessionVO, fmt.Errorf("B房间不属于当前场次")
	}
	// 5. 验证场次是否存在
	sessionA, err := s.sessionService.FindBySessionId(ctx, sessionIdA, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到场次信息")
	}
	_, err = s.sessionService.FindBySessionId(ctx, sessionIdB, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("未查询到场次信息")
	}

	// SessionA -> SessionB 合并方向
	// 将sessionA的相关信息的sessionId设置为sessionB的sessionId
	// sessionA 更新状态为无效或删除或金额置为0
	// 重新计算sessionB的金额
	orderPOInfoUnionVOA := s.payService.GetOrderPOInfoBySessionId(ctx, sessionIdA, *venueId)

	// 1. 原房间释放
	toUpdateRooms := []po.Room{
		{
			Id:        roomA.Id,
			SessionId: new(string),
			Status:    util.GetItPtr(_const.ROOM_STATUS_CLEANING),
			Tag:       new(string),
		},
	}
	// 2.原sessionA 更新状态为无效或删除或金额置为0
	toUpdateSessions := []po.Session{
		{
			Id:             sessionA.Id,
			Status:         util.GetItPtr(string(_const.V2_SESSION_STATUS_ENDING)),
			CloseTime:      util.GetItPtr(int64(util.TimeNowUnix())),
			PayStatus:      util.GetItPtr(string(_const.ORDER_STATUS_PAID)),
			SupermarketFee: new(int64),
			RoomFee:        new(int64),
			TotalFee:       new(int64),
			UnpaidAmount:   new(int64),
			PrePayBalance:  new(int64),
		},
	}
	// 3. sessionA相关order ... sessionA换成sessionB
	// order.SessionId <>
	// orderRoomPlan.SessionId <>
	// orderProduct.SessionId <>
	// orderPricePlan.SessionId <>
	// payBill.SessionId <>
	// payRecord.SessionId <>
	// sessionVOOpeningA -> sessionVOOpeningB
	// A -> B
	newOrderPOInfoUnionVOA := s.payService.GetNewMergeInfo(ctx, orderPOInfoUnionVOA, sessionIdB)

	err = s.orderService.SaveOrderInfoMergeRoom(ctx, newOrderPOInfoUnionVOA, toUpdateRooms, toUpdateSessions)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("保存订单信息失败")
	}
	// 4. 重新计算费用
	err = s.payService.UpdateSessionInfoUnpaid(ctx, sessionIdB, *venueId)
	if err != nil {
		return defaultSessionVO, fmt.Errorf("更新session失败")
	}
	// 13. 记录最后一个房间操作
	err = s.orderService.RecordLastRoomOperation(ctx, *venueId, roomIdA, sessionIdA, *reqDto.EmployeeId, _const.V2_ROOM_OPERATION_TYPE_MERGE, "并房")
	if err != nil {
		logrus.Error("记录最后一个房间操作失败:" + err.Error())
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return defaultSessionVO, nil
}

// V3LockRoom 锁房
func (s *OrderApplicationServiceImpl) V3LockRoom(ctx context.Context, reqDto req.V3LockRoomReqDto) (vo.SessionVO, error) {
	defaultSessionVO := vo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	_, room, _, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, new(string), reqDto.EmployeeId, false)
	if err != nil {
		return defaultSessionVO, err
	}
	if err = util.CheckRoomStatusLockRoom(*room.Status); err != nil {
		return defaultSessionVO, err
	}

	lastTag, err := s.orderService.GetLockRoomTag(ctx, room.Tag)
	if err != nil {
		return defaultSessionVO, err
	}
	attachRooms, err := s.roomService.GetAttachRooms(ctx, room)
	if err != nil {
		return defaultSessionVO, err
	}
	toUpdateRooms := []po.Room{}
	for _, attachRoom := range attachRooms {
		toUpdateRooms = append(toUpdateRooms, po.Room{
			Id:  attachRoom.Id,
			Tag: util.GetItPtr(lastTag),
		})
	}
	err = s.roomService.LockRoom(ctx, toUpdateRooms)
	if err != nil {
		return defaultSessionVO, err
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return defaultSessionVO, nil
}

// V3UnlockRoom 解锁房
func (s *OrderApplicationServiceImpl) V3UnlockRoom(ctx context.Context, reqDto req.V3UnlockRoomReqDto) (vo.SessionVO, error) {
	defaultSessionVO := vo.SessionVO{}
	venueId := reqDto.VenueId
	roomId := reqDto.RoomId
	_, room, _, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, new(string), reqDto.EmployeeId, false)
	if err != nil {
		return defaultSessionVO, err
	}
	if err = util.CheckRoomStatusUnlockRoom(*room.Status); err != nil {
		return defaultSessionVO, err
	}

	lastTag, err := s.orderService.GetRemoveLockRoomTag(ctx, room.Tag)
	if err != nil {
		return defaultSessionVO, err
	}
	attachRooms, err := s.roomService.GetAttachRooms(ctx, room)
	if err != nil {
		return defaultSessionVO, err
	}
	toUpdateRooms := []po.Room{}
	for _, attachRoom := range attachRooms {
		toUpdateRooms = append(toUpdateRooms, po.Room{
			Id:  attachRoom.Id,
			Tag: util.GetItPtr(lastTag),
		})
	}
	err = s.roomService.UnlockRoom(ctx, toUpdateRooms)
	if err != nil {
		return defaultSessionVO, err
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return defaultSessionVO, nil
}

// 取消开台
func (s *OrderApplicationServiceImpl) V3CancelOrderOpen(ctx context.Context, reqDto req.V3CancelOrderOpenReqDto) (vo.SessionVO, error) {
	defaultSessionVO := vo.SessionVO{}
	venueId := reqDto.VenueId
	sessionId := reqDto.SessionId
	roomId := reqDto.RoomId
	employeeId := reqDto.EmployeeId
	_, _, _, _, err := s.utilValidateBaseParams(ctx, venueId, roomId, sessionId, employeeId, false)
	if err != nil {
		return defaultSessionVO, err
	}

	orderInfoPO, err := s.payService.GetOrderInfoBatchBySessionId(ctx, *sessionId, *venueId, *roomId, *employeeId)
	if err != nil {
		return defaultSessionVO, err
	}

	modeOrderInfoBaseSessionBO := s.payService.ConvertToModeOrderInfoBaseSessionBO(ctx, orderInfoPO)

	hasLeshuaPay, err := s.payService.DoCancelOrderOpenInfo(ctx, reqDto, modeOrderInfoBaseSessionBO)
	if err != nil {
		return defaultSessionVO, err
	}
	// 14. 发送NATS消息
	if !hasLeshuaPay {
		err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *venueId, true, nil)
		if err != nil {
			logrus.Error("发送NATS消息失败:" + err.Error())
		}
	}
	return defaultSessionVO, nil
}
