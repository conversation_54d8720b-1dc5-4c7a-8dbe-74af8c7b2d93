package service

import (
	"context"
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/api/vo"
	"voderpltvv/erp_managent/api/req"
	managentVo "voderpltvv/erp_managent/api/vo"
	"voderpltvv/model"
)

// OrderApplicationService 订单应用服务接口
type OrderApplicationService interface {
	// OrderOpen 开台
	OrderOpen(ctx context.Context, reqDto dto.AddOrderOpenReqDto) (*vo.SessionVO, error)

	// OrderPay 支付
	OrderPay(ctx context.Context, reqDto req.QueryOrderPayReqDto) (*managentVo.PayResultVO, error)

	// ////////////////////////////////////////////

	// V3OrderOpen 开台-后付
	V3OrderOpen(ctx context.Context, reqDto req.V3AddOrderOpenReqDto) (managentVo.SessionVO, error)

	// V3OrderOpenPay 开台-立结
	V3OrderOpenPay(ctx context.Context, reqDto req.V3AddOrderOpenPayReqDto) (managentVo.SessionVO, error)

	// V3AdditionalOrder 点单-后付
	V3AdditionalOrder(ctx context.Context, reqDto req.V3AddOrderAdditionalReqDto) (managentVo.OrderVO, error)

	// V3AdditionalOrderPay 点单-立结
	V3AdditionalOrderPay(ctx context.Context, reqDto req.V3AddOrderAdditionalPayReqDto) (managentVo.OrderVO, error)

	// V3OrderPay 支付-后付
	V3OrderPay(ctx context.Context, reqDto req.V3QueryOrderPayReqDto) (managentVo.OrderPayResultVO, error)

	// V3PayCallback 乐刷支付回调
	V3PayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error

	// V3RefundCallback 乐刷退款回调
	V3RefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error

	// V3PayQuery 乐刷支付查询
	V3PayQuery(ctx context.Context, reqDto req.V3QueryOrderPayQueryReqDto) (managentVo.OrderPayQueryResultVO, error)

	// V3OrderRefundView 退款视图
	V3OrderRefundView(ctx context.Context, reqDto req.V3QueryOrderRefundViewReqDto) (managentVo.OrderRefundViewVO, error)

	// V3OrderRefundDo 退款
	V3OrderRefundDo(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto) ([]managentVo.OrderRefundInfoVO, error)

	// V3OrderRoomFeeRefundDo 退房费
	V3OrderRoomFeeRefundDo(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto) ([]managentVo.OrderRefundInfoVO, error)

	// V3OpenContinue 续房-后付
	V3OpenContinue(ctx context.Context, reqDto req.V3AddOrderOpenContinueReqDto) (managentVo.SessionVO, error)

	// V3OpenContinuePay 续房-立结
	V3OpenContinuePay(ctx context.Context, reqDto req.V3AddOrderOpenContinuePayReqDto) (managentVo.SessionVO, error)

	// V3EndTimeConsume 结束时长消费
	V3EndTimeConsume(ctx context.Context, reqDto req.V3EndTimeConsumeReqDto) (managentVo.SessionVO, error)

	// V3TransferRoom 转台
	V3TransferRoom(ctx context.Context, reqDto req.V3TransferRoomReqDto) (managentVo.SessionVO, error)

	// V3GiftProduct 赠送商品
	V3GiftProduct(ctx context.Context, reqDto req.V3OrderGiftProductReqDto) (managentVo.OrderVO, error)

	// V3GiftTime 赠送时长
	V3GiftTime(ctx context.Context, reqDto req.V3OrderGiftTimeReqDto) (managentVo.OrderVO, error)

	// V3CloseRoom 转台-立结
	V3CloseRoom(ctx context.Context, reqDto req.V3AddOrderCloseRoomReqDto) error

	// V3CleanRoomFinish 清扫完成-立结
	V3CleanRoomFinish(ctx context.Context, reqDto req.V3QueryOrderCleanRoomFinishReqDto) error

	// V3SwapRoom 互换包房
	V3SwapRoom(ctx context.Context, reqDto req.V3SwapRoomReqDto) (managentVo.SessionVO, error)

	// V3MergeRoom 并房
	V3MergeRoom(ctx context.Context, reqDto req.V3MergeRoomReqDto) (managentVo.SessionVO, error)

	// V3LockRoom 锁房
	V3LockRoom(ctx context.Context, reqDto req.V3LockRoomReqDto) (managentVo.SessionVO, error)

	// V3UnlockRoom 解锁房
	V3UnlockRoom(ctx context.Context, reqDto req.V3UnlockRoomReqDto) (managentVo.SessionVO, error)

	// V3CancelOrderOpen 取消开台
	V3CancelOrderOpen(ctx context.Context, reqDto req.V3CancelOrderOpenReqDto) (managentVo.SessionVO, error)

	// V3OrderReopen 重开
	V3OrderReopen(ctx context.Context, reqDto req.V3OrderReopenReqDto) (managentVo.SessionVO, error)

	// V3OrderQueryProductSales 商品销售统计
	V3OrderQueryProductSales(ctx context.Context, reqDto req.V3OrderQueryProductSalesReqDto) ([]managentVo.OrderProductSalesVO, error)

	// V3FeeCalculate 商品金额计算
	V3FeeCalculate(ctx context.Context, reqDto req.V3QueryOrderPayCalculateReqDto) ([]managentVo.OrderPayCalculateResultVO, error)

	// V3MiniAppPay 小程序支付
	V3MiniAppPay(ctx context.Context, reqDto req.V3MiniAppPayReqDto) (managentVo.OrderVO, error)
}
