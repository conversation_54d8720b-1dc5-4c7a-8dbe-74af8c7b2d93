package service

import (
	"context"
	"errors"
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *OrderApplicationServiceImpl) utilValidateBaseParamsV2(ctx context.Context, venueId, employeeId *string) (po.Venue, po.Employee, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Employee{}, fmt.Errorf("VenueId不能为空")
	}
	if employeeId == nil || *employeeId == "" {
		return po.Venue{}, po.Employee{}, fmt.Errorf("EmployeeId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Employee{}, fmt.Errorf("门店不存在")
	}
	// 0.2. 检查员工是否存在
	employee, err := s.employeeService.FindEmployeeByID(ctx, *employeeId)
	if err != nil {
		return po.Venue{}, po.Employee{}, fmt.Errorf("员工不存在")
	}
	if !(employee.VenueId != nil && *employee.VenueId == *venueId) {
		return po.Venue{}, po.Employee{}, fmt.Errorf("员工不属于该门店")
	}
	return *venue, employee, nil
}

func (s *OrderApplicationServiceImpl) utilValidateBaseParamsPay(ctx context.Context, venue po.Venue, payRecordVOS *[]vo.PayRecordVO) error {
	if payRecordVOS == nil || len(*payRecordVOS) == 0 {
		return fmt.Errorf("支付方式不能为空")
	}
	for _, payRecordVO := range *payRecordVOS {
		// 乐刷支付校验
		if payRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			if venue.IsLeshuaPay == nil || *venue.IsLeshuaPay != 1 {
				return fmt.Errorf("门店未开通乐刷支付")
			}
			venuePaySetting, err := s.venuePaySettingService.FindVenuePaySettingByVenueId(ctx, *venue.Id)
			if err != nil {
				return fmt.Errorf("门店未开通乐刷支付")
			}
			if venuePaySetting.SubMerchantId == nil || *venuePaySetting.SubMerchantId == "" {
				return fmt.Errorf("门店未配置乐刷支付子商户ID")
			}
		}
	}
	return nil
}

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *OrderApplicationServiceImpl) utilValidateBaseParams(ctx context.Context, venueId, roomId, sessionId, employeeId *string, sessionIdRequired bool) (po.Venue, po.Room, po.Session, po.Employee, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("VenueId不能为空")
	}
	if roomId == nil || *roomId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("RoomId不能为空")
	}
	if employeeId == nil || *employeeId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("EmployeeId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("门店不存在")
	}
	// 0.2. 检查房间是否存在
	room, err := s.roomService.GetRoom(ctx, *roomId)
	if err != nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不存在")
	}
	if !(room.VenueId != nil && *room.VenueId == *venueId) {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不属于该门店")
	}
	// 0.3. 检查员工是否存在
	employee, err := s.employeeService.FindEmployeeByID(ctx, *employeeId)
	if err != nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("员工不存在")
	}
	// 0.4. 检查场次是否存在
	session := po.Session{}
	if sessionIdRequired {
		if sessionId == nil || *sessionId == "" {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("SessionId不能为空")
		}
		session, err = s.sessionService.FindBySessionId(ctx, *sessionId, *venueId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("场次信息不存在")
		}
		if !(room.SessionId != nil && *room.SessionId == *sessionId) {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不属于该场次")
		}
	}
	return *venue, room, session, employee, nil
}

func (s *OrderApplicationServiceImpl) utilValidateMemberCard(ctx context.Context, venue po.Venue, memberCardId *string) (po.MemberCard, error) {
	if memberCardId == nil || *memberCardId == "" {
		return po.MemberCard{}, nil
	}
	memberCard, err := s.memberCardService.FindById(ctx, *memberCardId)
	if err != nil {
		return po.MemberCard{}, fmt.Errorf("会员卡信息不存在")
	}
	memberCardVenues, err := s.memberCardVenueService.FindByMemberCardId(ctx, *memberCardId)
	if err != nil {
		return po.MemberCard{}, fmt.Errorf("会员卡信息不存在")
	}
	venueIds := make([]string, 0)
	for _, memberCardVenue := range memberCardVenues {
		venueIds = append(venueIds, *memberCardVenue.VenueId)
	}
	if !util.InList(*venue.Id, venueIds) {
		return po.MemberCard{}, fmt.Errorf("会员卡不属于该门店")
	}
	return memberCard, nil
}

// validatePayTypes 验证支付方式
// 入参： payRecords - 支付方式
// 出参： error - 错误信息
func (s *OrderApplicationServiceImpl) utilValidatePayTypes(ctx context.Context, payRecords *[]vo.PayRecordVO, payRecordRequired bool) error {
	if !payRecordRequired {
		return nil
	}
	// 1. 检查支付方式是否为空
	if payRecords == nil || len(*payRecords) == 0 {
		return fmt.Errorf("PayType不能为空")
	}
	// 2. 检查支付方式是否支持
	hasLeshuaPay := false
	for _, payRecord := range *payRecords {
		if !util.InList(payRecord.PayType, _const.PAY_TYPE_SUPPORTS) {
			// return fmt.Errorf("PayType不支持")
		}
		if payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			hasLeshuaPay = true
		}
	}
	if hasLeshuaPay && len(*payRecords) > 1 {
		// return fmt.Errorf("乐刷支付方式不支持混合支付")
	}
	return nil
}

// utilValidatePayFeeInfo 验证支付方式
// 入参： payRecords - 支付方式
// 出参： error - 错误信息
func (s *OrderApplicationServiceImpl) utilValidatePayFeeInfo(ctx context.Context, originalFee, shouldFee, totalFee, zeroFee, changeAmount *int64, payRecords *[]vo.PayRecordVO, payRecordRequired bool) error {
	if originalFee == nil || *originalFee < 0 {
		return fmt.Errorf("原始金额为空或小于0")
	}
	if shouldFee == nil || *shouldFee < 0 {
		return fmt.Errorf("应付金额不能为空或小于0")
	}
	if totalFee == nil || *totalFee < 0 {
		return fmt.Errorf("总金额不能为空或小于0")
	}
	if zeroFee == nil || *zeroFee < 0 {
		return fmt.Errorf("零金额不能为空或小于0")
	}
	if changeAmount == nil || *changeAmount < 0 {
		return fmt.Errorf("找零金额不能为空或小于0")
	}
	if *originalFee < *shouldFee {
		return fmt.Errorf("原始金额不能小于应付金额")
	}
	if payRecordRequired {
		// 1. 检查支付方式是否为空
		if payRecords == nil || len(*payRecords) == 0 {
			return fmt.Errorf("PayType不能为空")
		}
		// 2. 检查支付方式是否支持
		for _, payRecord := range *payRecords {
			if !util.InList(payRecord.PayType, _const.PAY_TYPE_SUPPORTS) {
				// return fmt.Errorf("PayType不支持")
			}
		}
	}
	return nil
}

// utilValidatePayFeeInfo 验证支付方式-转台
// 入参： payRecords - 支付方式
// 出参： error - 错误信息
func (s *OrderApplicationServiceImpl) utilValidatePayFeeInfoTransferRoom(ctx context.Context, originalFee, shouldFee, totalFee, zeroFee, changeAmount *int64, payRecords *[]vo.PayRecordVO, refundPayRecords *[]vo.PayRecordVO, payRecordRequired bool) error {
	if originalFee == nil || *originalFee < 0 {
		return fmt.Errorf("原始金额为空或小于0")
	}
	if shouldFee == nil || *shouldFee < 0 {
		return fmt.Errorf("应付金额不能为空或小于0")
	}
	if totalFee == nil || *totalFee < 0 {
		return fmt.Errorf("总金额不能为空或小于0")
	}
	if zeroFee == nil || *zeroFee < 0 {
		return fmt.Errorf("零金额不能为空或小于0")
	}
	if changeAmount == nil || *changeAmount < 0 {
		return fmt.Errorf("找零金额不能为空或小于0")
	}
	if *originalFee < *shouldFee {
		return fmt.Errorf("原始金额不能小于应付金额")
	}
	if *shouldFee != *totalFee+*zeroFee-*changeAmount {
		return fmt.Errorf("应付金额不等于实付金额+抹零金额+-找零金额")
	}
	if payRecordRequired {
		if payRecords != nil && len(*payRecords) > 0 && refundPayRecords != nil && len(*refundPayRecords) > 0 {
			return fmt.Errorf("支付方式和退款支付方式不能同时存在")
		}
		// 1. 检查支付方式是否为空
		if payRecords == nil || len(*payRecords) == 0 {
			return fmt.Errorf("PayType不能为空")
		}
		// 2. 检查支付方式是否支持
		for _, payRecord := range *payRecords {
			if !util.InList(payRecord.PayType, _const.PAY_TYPE_SUPPORTS) {
				// return fmt.Errorf("PayType不支持")
			}
		}
		// 退款
		// 1. 检查退款支付方式是否为空
		if refundPayRecords == nil || len(*refundPayRecords) == 0 {
			return fmt.Errorf("退款支付方式不能为空")
		}
		// 2. 检查退款支付方式是否支持
		for _, refundPayRecord := range *refundPayRecords {
			if !util.InList(refundPayRecord.PayType, _const.PAY_TYPE_SUPPORTS) {
				// return fmt.Errorf("退款支付方式不支持")
			}
		}

	}
	return nil
}

// V3OrderPay 后付-支付
// 场景：结账，将session下所有未付的订单进行支付
// 入参： order - 未支付的order，
func (s *OrderApplicationServiceImpl) V3OrderPay(ctx context.Context, reqDto req.V3QueryOrderPayReqDto) (vo.OrderPayResultVO, error) {
	var defaultPayResultVO vo.OrderPayResultVO = vo.OrderPayResultVO{}

	// 1. 验证入参
	// 1.1. 验证基础参数 venueId,roomId,sessionId,employeeId
	venue, room, session, employee, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.RoomId, reqDto.SessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultPayResultVO, err
	}
	// 1.2. 验证登录会员卡ID
	_, err = s.utilValidateMemberCard(ctx, venue, reqDto.MemberCardId)
	if err != nil {
		return defaultPayResultVO, err
	}
	sessionVO := s.sessionService.ConvertToSessionVO(ctx, session)
	// 1.2. 验证支付方式
	err = s.utilValidatePayTypes(ctx, reqDto.PayRecords, true)
	if err != nil {
		return defaultPayResultVO, err
	}
	// 1.3. 检查订单号是否为空
	orderNos := reqDto.OrderNos
	if orderNos == nil || len(*orderNos) == 0 {
		return defaultPayResultVO, fmt.Errorf("订单号不能为空")
	}
	// 1.4. 检查是否存在未支付成功的账单，不能重复支付
	err = s.payBillService.CheckHasUnpaidPayBill(ctx, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return defaultPayResultVO, err
	}

	// 2. 检查订单号和session下所有未支付的订单号是否一致
	upaidOrderNos, err := s.orderService.IsMatchAllUnpaidOrder(ctx, *orderNos, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return defaultPayResultVO, err
	}
	if len(upaidOrderNos) == 0 {
		return defaultPayResultVO, fmt.Errorf("没有需要支付的订单")
	}

	// 3. 获取订单信息，设置oms ops
	orderVOs, err := s.payService.GetOrdersInfoByOrderNos(ctx, upaidOrderNos, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return defaultPayResultVO, err
	}

	// 4. 设置收款单信息
	billId := util.GetBillId(*reqDto.VenueId)
	payBillVO := vo.PayBillVO{
		VenueId:               *reqDto.VenueId,
		RoomId:                *reqDto.RoomId,
		EmployeeId:            *reqDto.EmployeeId,
		MemberCardId:          util.GetPtrSafe(reqDto.MemberCardId),
		MemberCardNumber:      util.GetPtrSafe(reqDto.MemberCardNumber),
		SessionId:             *reqDto.SessionId,
		BillId:                billId,
		OriginalFee:           util.GetPtrSafe(reqDto.OriginalFee),
		TotalFee:              *reqDto.TotalFee,
		ShouldFee:             *reqDto.ShouldFee,
		ZeroFee:               *reqDto.ZeroFee,
		CreditAmount:          util.GetPtrSafe(reqDto.CreditAmount),
		ProductDiscount:       util.GetPtrSafeDefault(reqDto.ProductDiscount, 100),
		RoomDiscount:          util.GetPtrSafeDefault(reqDto.RoomDiscount, 100),
		IsFree:                util.GetPtrSafeDefault(reqDto.IsFree, false),
		ProductDiscountAmount: util.GetPtrSafe(reqDto.ProductDiscountAmount),
		RoomDiscountAmount:    util.GetPtrSafe(reqDto.RoomDiscountAmount),
		ChangeAmount:          util.GetPtrSafe(reqDto.ChangeAmount),
		Status:                _const.V2_PAY_BILL_STATUS_WAIT,
		Direction:             _const.V2_PAY_BILL_DIRECTION_NORMAL,
		ForceMinimumCharge:    true,
		DiscountReason:        util.GetPtrSafe(reqDto.DiscountReason),
	}

	// 5. 计算本次未付的支付金额
	totalFeeThis, newOps, newOms, retProductDiscountFee, retRoomDiscountFee := s.payService.CalculateTotalPaymentForPay(ctx, sessionVO, orderVOs, payBillVO)

	// 6. 设置可能的 op om 回写信息
	toUpdateModifyOrderProducts, toUpdateModifyOrderRoomPlans := s.orderService.BuildNewOpOmToUpdate(ctx, newOps, newOms)

	if totalFeeThis != *reqDto.TotalFee+*reqDto.ZeroFee-*reqDto.ChangeAmount {
		return defaultPayResultVO, fmt.Errorf("金额不一致，下单失败")
	}

	// 7. 计算session金额信息
	totalRoomFee, totalSupermarketFee, unpaidAmount, paidAmount, totalFee, err := s.payService.CalculateManyFeeForPay(ctx, totalFeeThis, newOps, newOms, orderVOs, *reqDto.SessionId, *reqDto.VenueId, *session.MinConsume)
	if err != nil {
		return defaultPayResultVO, fmt.Errorf("计算费用失败: %w", err)
	}

	// 8. 设置session更新信息
	toUpdateSession := po.Session{
		Id:             session.Id,
		SessionId:      reqDto.SessionId,
		RoomFee:        &totalRoomFee,
		SupermarketFee: &totalSupermarketFee,
		TotalFee:       &totalFee,
		UnpaidAmount:   &unpaidAmount,
		PaidAmount:     &paidAmount,
	}

	// 9. 调整收款单信息
	toAddPayBillCvt := s.payBillService.ConvertToPayBill(ctx, payBillVO)
	toAddPayBill := s.payBillService.TrimIdToAddPayBill(ctx, toAddPayBillCvt)
	toAddPayBill.ProductDiscountFee = &retProductDiscountFee
	toAddPayBill.RoomDiscountFee = &retRoomDiscountFee
	toAddOrderAndPays := make([]po.OrderAndPay, 0)

	// 10. 设置订单支付记录信息
	for _, orderVO := range orderVOs {
		orderNo := orderVO.OrderNo
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   &orderNo,
			SessionId: reqDto.SessionId,
			BillId:    &billId,
		})
	}
	toAddPayRecords := make([]po.PayRecord, 0)
	payAmounts := int64(0)
	for _, payRecord := range *reqDto.PayRecords {
		payId := util.GetPayId(*reqDto.VenueId)
		totalFee := payRecord.TotalFee
		payType := payRecord.PayType
		toAddPayRecords = append(toAddPayRecords, po.PayRecord{
			VenueId:                 reqDto.VenueId,
			RoomId:                  reqDto.RoomId,
			EmployeeId:              reqDto.EmployeeId,
			MemberCardId:            &payRecord.MemberCardId,
			MemberCardNumber:        reqDto.MemberCardNumber,
			SessionId:               reqDto.SessionId,
			BillId:                  &billId,
			PayId:                   &payId,
			TotalFee:                &totalFee,
			PrincipalAmount:         &payRecord.PrincipalAmount,
			MemberRoomBonusAmount:   &payRecord.MemberRoomBonusAmount,
			MemberGoodsBonusAmount:  &payRecord.MemberGoodsBonusAmount,
			MemberCommonBonusAmount: &payRecord.MemberCommonBonusAmount,
			Status:                  util.GetItPtr(_const.PAY_STATUS_UNPAID),
			PayType:                 &payType,
			ProductName:             util.GetPayProductName(venue.Name, venue.Id),
			BQROneCode:              &payRecord.BQROneCode,
		})
		payAmounts += payRecord.TotalFee
	}
	if payAmounts != *reqDto.TotalFee {
		return defaultPayResultVO, fmt.Errorf("金额不一致，请检查")
	}
	// 11.2. 验证会员卡ID
	for _, payRecordVO := range *reqDto.PayRecords {
		if payRecordVO.PayType == _const.PAY_TYPE_MEMBER_CARD {
			// 计算本次支付的房费和商品费
			roomFee := int64(0)
			supermarketFee := int64(0)
			for _, orderProductVO := range toUpdateModifyOrderProducts {
				supermarketFee += *orderProductVO.PayAmount
			}
			for _, orderRoomPlanVO := range toUpdateModifyOrderRoomPlans {
				roomFee += *orderRoomPlanVO.PayAmount
			}
			// 1.2.1. 验证会员卡ID-扣款的
			_, err := s.utilValidateMemberCard(ctx, venue, &payRecordVO.MemberCardId)
			if err != nil {
				return defaultPayResultVO, err
			}
			_, err = s.memberRechargeBillService.V3RPCMemberCardVaildBalance(ctx, req.V3QueryMemberCardQueryBalanceReqDto{
				TotalAmount:       &payRecordVO.TotalFee,
				MemberCardId:      &payRecordVO.MemberCardId,
				PrincipalAmount:   &payRecordVO.PrincipalAmount,
				RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
				GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
				CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
				RoomTotalAmount:   &roomFee,
				GoodsTotalAmount:  &supermarketFee,
			})
			if err != nil {
				return defaultPayResultVO, err
			}
		}
	}
	// 11.1 优先从会员卡中扣款
	for _, payRecord := range toAddPayRecords {
		payRecordVO := s.payRecordService.ConvertToPayRecordVO(ctx, payRecord)
		// 跳过非会员卡支付
		if payRecordVO.PayType != _const.PAY_TYPE_MEMBER_CARD {
			continue
		}
		// 检查会员卡余额
		_, err := s.memberRechargeBillService.V3RPCMemberCardPay(ctx, req.V3RPCPayMoneyReqDto{
			VenueId:           reqDto.VenueId,
			EmployeeId:        reqDto.EmployeeId,
			MemberCardId:      &payRecordVO.MemberCardId,
			PayId:             &payRecordVO.PayId,
			Amount:            &payRecordVO.PrincipalAmount,
			RoomBonusAmount:   &payRecordVO.MemberRoomBonusAmount,
			GoodsBonusAmount:  &payRecordVO.MemberGoodsBonusAmount,
			CommonBonusAmount: &payRecordVO.MemberCommonBonusAmount,
		})
		if err != nil {
			return defaultPayResultVO, fmt.Errorf("会员卡扣款失败: %w", err)
		}
	}

	// 11. 保存支付信息预处理
	err = s.orderService.SaveOrderPayInfoPre(ctx, toUpdateSession, toUpdateModifyOrderProducts, toUpdateModifyOrderRoomPlans, toAddPayBill, toAddOrderAndPays, toAddPayRecords)
	if err != nil {
		return defaultPayResultVO, fmt.Errorf("保存支付信息失败: %w", err)
	}
	// 11.1 保存会员卡消费记录
	s.memberCardConsumeService.RecordMemberCardConsume(ctx, session, venue, room, employee, toAddPayRecords, toAddPayBill)

	// 12. 发起支付
	payResults, err := s.payService.V3TransformPayGate(ctx, &req.V3QueryOrderPayTransformReqDto{
		PayRecords:    reqDto.PayRecords,
		NewPayRecords: toAddPayRecords,
		VenueId:       reqDto.VenueId,
		RoomId:        reqDto.RoomId,
		SessionId:     reqDto.SessionId,
		EmployeeId:    reqDto.EmployeeId,
	}, &toAddPayBill)
	if err != nil {
		return defaultPayResultVO, fmt.Errorf("发起支付失败: %w", err)
	}

	// 13. 保存支付信息回调
	for _, payRecord := range toAddPayRecords {
		if payRecord.PayType != nil && *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			continue
		}
		err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, vo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_COMMON})
		if err != nil {
			return defaultPayResultVO, fmt.Errorf("保存支付信息失败: %w", err)
		}
		// s.payService.StartLeshuaTimer(ctx, *payRecord.PayId)
	}
	orderPayResultVO := vo.OrderPayResultVO{TmpOrderNos: *reqDto.OrderNos, PayResultVOs: payResults, PayBills: []vo.PayBillVO{s.payBillService.ConvertToPayBillVO(ctx, toAddPayBill)}}

	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *reqDto.VenueId, false, toAddPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}

	return orderPayResultVO, nil
}

// V3PayCallback 支付回调
func (s *OrderApplicationServiceImpl) V3PayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error {
	// 当前仅支付成功的订单才会触发支付交易结果异步通知。
	// 支付订单状态	是否触发异步通知
	// 支付成功	（status=2）	是
	// 订单关闭	（status=6）	否
	// 支付失败	（status=8）	否
	// 回调只存在 status=2
	logCtx := ctx.(*gin.Context)
	logTitle := "乐刷支付回调: V3PayCallback: "
	util.Wlog(logCtx).Infof("%s %s: %s --->>> %s", logTitle, reqDto.Third_order_id, reqDto.Status, util.GetPrettyJson(reqDto))

	// 1. 验证回调状态是否非法
	if reqDto.Error_code != "0" {
		util.Wlog(logCtx).Errorf("%s 1.1 %s: 未知的支付回调err_code", logTitle, reqDto.Third_order_id)
		return errors.New("未知的支付回调err_code")
	}
	if reqDto.Status != "2" {
		util.Wlog(logCtx).Errorf("%s 1.2 %s: 未知的支付回调status", logTitle, reqDto.Third_order_id)
		return errors.New("未知的支付回调status")
	}

	payId := reqDto.Third_order_id

	payRecord, err := s.payRecordService.FindByPayId(ctx, payId)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 2.1 %s: 查询支付记录失败", logTitle, reqDto.Third_order_id)
		return err
	}
	// 3. 拦截重复通知
	if payRecord.Status != nil && *payRecord.Status == _const.V2_PAY_RECORD_STATUS_SUCCESS {
		util.Wlog(logCtx).Infof("%s 3.1 %s: 已支付成功，return", logTitle, reqDto.Third_order_id)
		return nil
	}

	err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, vo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_PAY, PayCallbackModel: &reqDto})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 4.1 %s: 保存支付记录失败", logTitle, reqDto.Third_order_id)
		return err
	}
	err = s.payService.V3CallPayCallback(ctx, reqDto)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 5.1 %s: 回调失败", logTitle, reqDto.Third_order_id)
		// return err
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *payRecord.VenueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return nil
}

// V3RefundCallback 退款回调
func (s *OrderApplicationServiceImpl) V3RefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error {
	// 退款成功和退款失败，两种情况下才会触发交易退款异步通知
	// 退款订单状态	是否触发异步通知
	// 退款中 （status=10）    否
	// 退款成功（status=11）	是
	// 退款失败（status=12）	是
	// 回调只存在 status=11 和 status=12
	logCtx := ctx.(*gin.Context)
	logTitle := "乐刷退款回调: V3RefundCallback: "
	util.Wlog(logCtx).Infof("%s %s: %s --->>> %s", logTitle, reqDto.Merchant_refund_id, reqDto.Status, util.GetPrettyJson(reqDto))

	// 1. 验证回调状态是否非法
	if reqDto.Error_code != "0" {
		util.Wlog(logCtx).Errorf("%s 1.1 %s: 未知的支付回调err_code", logTitle, reqDto.Merchant_refund_id)
		return errors.New("未知的支付回调err_code")
	}
	// 1. 验证回调code是否合法
	if !util.InList(reqDto.Status, []string{"11", "12"}) {
		util.Wlog(logCtx).Errorf("%s %s: 退款状态未知", logTitle, reqDto.Merchant_refund_id)
		return errors.New("退款状态未知")
	}

	payId := reqDto.Merchant_refund_id

	payRecord, err := s.payRecordService.FindByPayId(ctx, payId)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 2.1 %s: 查询退款记录失败", logTitle, reqDto.Merchant_refund_id)
		return err
	}
	// 3. 拦截重复通知
	if payRecord.Status != nil && *payRecord.Status == _const.V2_PAY_RECORD_STATUS_SUCCESS {
		util.Wlog(logCtx).Infof("%s 3.1 %s: 已退款成功，return", logTitle, reqDto.Merchant_refund_id)
		return nil
	}

	err = s.payService.SaveOrderPayInfoCallbackByPayId(ctx, vo.OrderPayCallbackVO{PayId: *payRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_REFUND, RefundCallbackModel: &reqDto})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 4.1 %s: 保存退款记录失败", logTitle, reqDto.Merchant_refund_id)
		return err
	}
	err = s.payService.V3CallRefundCallback(ctx, reqDto)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 5.1 %s: 回调失败", logTitle, reqDto.Merchant_refund_id)
		// return err
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *payRecord.VenueId, true, nil)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return nil
}

// V3PayQuery 乐刷支付查询
func (s *OrderApplicationServiceImpl) V3PayQuery(ctx context.Context, reqDto req.V3QueryOrderPayQueryReqDto) (vo.OrderPayQueryResultVO, error) {
	defaultOrderPayQueryResultVO := vo.OrderPayQueryResultVO{}
	_, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.RoomId, reqDto.SessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultOrderPayQueryResultVO, fmt.Errorf("查询支付账单失败: %w", err)
	}

	payBill, err := s.payBillService.FindByBillId(ctx, *reqDto.BillId, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return defaultOrderPayQueryResultVO, fmt.Errorf("查询支付账单失败: %w", err)
	}

	defaultOrderPayQueryResultVO.PayBillVO = s.payBillService.ConvertToPayBillVO(ctx, payBill)
	return defaultOrderPayQueryResultVO, nil
}

// V3FeeCalculate 价格结算
func (s *OrderApplicationServiceImpl) V3FeeCalculate(ctx context.Context, reqDto req.V3QueryOrderPayCalculateReqDto) ([]vo.OrderPayCalculateResultVO, error) {
	defaultOrderPayCalculateResultVOs := []vo.OrderPayCalculateResultVO{}

	_, _, session, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.RoomId, reqDto.SessionId, reqDto.EmployeeId, true)
	if err != nil {
		return defaultOrderPayCalculateResultVOs, fmt.Errorf("查询支付账单失败: %w", err)
	}
	sessionVO := s.sessionService.ConvertToSessionVO(ctx, session)
	_, orderProductVOs, orderRoomPlanVOs := s.payCalcService.CalculateProductRoomPayment(ctx, sessionVO, reqDto.OrderVOs, reqDto.PayBillVO)

	// 重新构造OrderVO
	orderPayCalculateResultVOs := make([]vo.OrderPayCalculateResultVO, 0)

	// 按照OrderNo分组整理商品和房间计划
	orderProductMap := make(map[string][]vo.OrderProductVO)
	orderRoomPlanMap := make(map[string][]vo.OrderRoomPlanVO)
	orderNoSet := make(map[string]bool)

	// 从orderProductVOs中获取订单号
	for _, productVO := range orderProductVOs {
		orderNo := productVO.OrderNo
		orderNoSet[orderNo] = true
		if _, ok := orderProductMap[orderNo]; !ok {
			orderProductMap[orderNo] = make([]vo.OrderProductVO, 0)
		}
		orderProductMap[orderNo] = append(orderProductMap[orderNo], productVO)
	}

	// 从orderRoomPlanVOs中获取订单号
	for _, roomPlanVO := range orderRoomPlanVOs {
		orderNo := roomPlanVO.OrderNo
		orderNoSet[orderNo] = true
		if _, ok := orderRoomPlanMap[orderNo]; !ok {
			orderRoomPlanMap[orderNo] = make([]vo.OrderRoomPlanVO, 0)
		}
		orderRoomPlanMap[orderNo] = append(orderRoomPlanMap[orderNo], roomPlanVO)
	}

	// 构建结果 - 基于商品和房间计划中的订单号
	for orderNo := range orderNoSet {
		// 创建基本的OrderVO结构
		orderVO := vo.OrderVO{
			OrderNo:    orderNo,
			VenueId:    *reqDto.VenueId,
			RoomId:     *reqDto.RoomId,
			SessionId:  *reqDto.SessionId,
			EmployeeId: *reqDto.EmployeeId,
		}

		// 提取额外信息 - 如果有商品或房间计划
		products := orderProductMap[orderNo]
		roomPlans := orderRoomPlanMap[orderNo]

		resultVO := vo.OrderPayCalculateResultVO{
			OrderVO:          orderVO,
			OrderProductVOs:  products,
			OrderRoomPlanVOs: roomPlans,
			PayBillVO:        reqDto.PayBillVO,
		}

		orderPayCalculateResultVOs = append(orderPayCalculateResultVOs, resultVO)
	}

	return orderPayCalculateResultVOs, nil
}
