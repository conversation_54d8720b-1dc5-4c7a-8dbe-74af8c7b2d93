package service

import (
	"context"
	"fmt"
	_const "voderpltvv/const"
	validateService "voderpltvv/erp_client/application/framework/validate"
	callService "voderpltvv/erp_client/domain/configuration/business/call/service"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"
)

// CallApplicationServiceImpl 呼叫应用服务实现
type CallApplicationServiceImpl struct {
	processEngine      model.Engine
	callMessageService callService.CallMessageService
	callTypesService   callService.CallTypesService
	roomService        roomService.Service
	validateService    validateService.ValidateService
	sessionService     sessionService.SessionService
	venueService       venueService.VenueService
	employeeService    employeeService.Service
}

// NewCallApplicationService 创建呼叫应用服务实例
func NewCallApplicationService(
	callMessageService callService.CallMessageService,
	callTypesService callService.CallTypesService,
	roomService roomService.Service,
	validateService validateService.ValidateService,
	sessionService sessionService.SessionService,
	venueService venueService.VenueService,
	employeeService employeeService.Service,
) CallApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("callMessageService", callMessageService)
	engine.RegisterService("callTypesService", callTypesService)
	engine.RegisterService("roomService", roomService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("sessionService", sessionService)
	engine.RegisterService("venueService", venueService)
	engine.RegisterService("employeeService", employeeService)

	return &CallApplicationServiceImpl{
		processEngine:      engine,
		callMessageService: callMessageService,
		callTypesService:   callTypesService,
		roomService:        roomService,
		validateService:    validateService,
		sessionService:     sessionService,
		venueService:       venueService,
		employeeService:    employeeService,
	}
}

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *CallApplicationServiceImpl) utilValidateBaseParams(ctx context.Context, venueId, roomId, employeeId, sessionId *string, roomIdRequired, employeeIdRequired, sessionIdRequired bool) (po.Venue, po.Room, po.Employee, po.Session, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("VenueId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("门店不存在")
	}

	// 0.2. 检查房间是否存在
	room := po.Room{}
	if roomIdRequired {
		if roomId == nil || *roomId == "" {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("RoomId不能为空")
		}
		room, err = s.roomService.GetRoom(ctx, *roomId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("房间不存在")
		}
		if !(room.VenueId != nil && *room.VenueId == *venueId) {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("房间不属于该门店")
		}
	}

	// 0.3. 检查员工是否存在
	employee := po.Employee{}
	if employeeIdRequired {
		if employeeId == nil || *employeeId == "" {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("EmployeeId不能为空")
		}
		employee, err = s.employeeService.FindEmployeeByID(ctx, *employeeId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("员工不存在")
		}

		// 验证员工是否属于指定门店
		if !(employee.VenueId != nil && *employee.VenueId == *venueId) {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("员工不属于该门店")
		}
	}

	// 0.4. 检查场次是否存在
	session := po.Session{}
	if sessionIdRequired {
		if sessionId == nil || *sessionId == "" {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("SessionId不能为空")
		}
		session, err = s.sessionService.FindBySessionId(ctx, *sessionId, *venueId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("场次信息不存在")
		}
		if !(session.VenueId != nil && *session.VenueId == *venueId) {
			return po.Venue{}, po.Room{}, po.Employee{}, po.Session{}, fmt.Errorf("场次不属于该门店")
		}
	}

	return *venue, room, employee, session, nil
}

// ////////////////////////////////////////////////
var CALLTYPES = []po.CallTypes{
	{
		Id:           util.Ptr("1"),
		CallType:     util.Ptr("呼叫"),
		CallTypeName: util.Ptr("呼叫"),
	},
	{
		Id:           util.Ptr("2"),
		CallType:     util.Ptr("需要餐具"),
		CallTypeName: util.Ptr("需要餐具"),
	},
	{
		Id:           util.Ptr("3"),
		CallType:     util.Ptr("设备问题"),
		CallTypeName: util.Ptr("设备问题"),
	},
	{
		Id:           util.Ptr("4"),
		CallType:     util.Ptr("麦克风电量低"),
		CallTypeName: util.Ptr("麦克风电量低"),
	},
	{
		Id:           util.Ptr("5"),
		CallType:     util.Ptr("需要酒杯"),
		CallTypeName: util.Ptr("需要酒杯"),
	},
	{
		Id:           util.Ptr("6"),
		CallType:     util.Ptr("结账"),
		CallTypeName: util.Ptr("结账"),
	},
	{
		Id:           util.Ptr("7"),
		CallType:     util.Ptr("打扫"),
		CallTypeName: util.Ptr("打扫"),
	},
}
var CALLTYPEMAP = func() map[string]string {
	callTypeMap := make(map[string]string)
	for _, callType := range CALLTYPES {
		callTypeMap[*callType.CallType] = *callType.CallType
	}
	return callTypeMap
}()

// V3AddCall 新增呼叫消息
func (s *CallApplicationServiceImpl) V3AddCall(ctx context.Context, reqDto req.V3AddCallReqDto) (vo.CallMessageVO, error) {
	// 验证基础参数 - sessionId 不是必需的
	venue, room, employee, session, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.RoomId, reqDto.EmployeeId, reqDto.SessionId, true, true, false)
	if err != nil {
		return vo.CallMessageVO{}, err
	}

	if reqDto.CallType == nil || *reqDto.CallType == "" {
		return vo.CallMessageVO{}, fmt.Errorf("CallType不能为空")
	}
	if reqDto.CallTypeName == nil || *reqDto.CallTypeName == "" {
		return vo.CallMessageVO{}, fmt.Errorf("CallTypeName不能为空")
	}

	// 创建呼叫消息 - SessionId 可以为空
	var sessionId *string
	if reqDto.SessionId != nil && *reqDto.SessionId != "" {
		sessionId = session.Id
	}

	callMessage := &po.CallMessage{
		VenueId:      venue.Id,
		RoomId:       room.Id,
		SessionId:    sessionId,
		EmployeeId:   employee.Id,
		RoomName:     room.Name,
		CallSrc:      reqDto.CallSrc,
		CallType:     reqDto.CallType,
		CallTypeName: reqDto.CallTypeName,
		Status:       util.Ptr(_const.V2_CALL_MESSAGE_STATUS_UNPROCESSED),
	}

	err = s.callMessageService.CreateCallMessage(ctx, callMessage)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("创建呼叫消息失败")
	}
	err = s.callMessageService.SendNATSCallMessage(ctx, *callMessage.VenueId)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("发送NATS呼叫消息失败")
	}
	return s.callMessageService.ConvertToCallMessageVO(ctx, *callMessage), nil
}

// V3DealCall 处理呼叫消息
func (s *CallApplicationServiceImpl) V3DealCall(ctx context.Context, reqDto req.V3DealCallReqDto) (vo.CallMessageVO, error) {
	if reqDto.Id == nil || *reqDto.Id == "" {
		return vo.CallMessageVO{}, fmt.Errorf("Id不能为空")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return vo.CallMessageVO{}, fmt.Errorf("EmployeeId不能为空")
	}
	employee, err := s.employeeService.FindEmployeeByID(ctx, *reqDto.EmployeeId)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("员工不存在")
	}
	callMessage, err := s.callMessageService.FindById(ctx, *reqDto.Id)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("呼叫消息不存在")
	}
	if callMessage.Status == nil || *callMessage.Status != 0 {
		return vo.CallMessageVO{}, fmt.Errorf("呼叫消息已处理或已取消")
	}
	timeNow := util.TimeNowUnixInt64()
	toUpdateCallMessage := po.CallMessage{
		Id:      callMessage.Id,
		Status:  util.Ptr(1),
		OptId:   reqDto.EmployeeId,
		OptName: employee.Name,
		OptTime: &timeNow,
	}
	err = s.callMessageService.UpdateCallMessage(ctx, &toUpdateCallMessage)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("处理呼叫消息失败")
	}
	err = s.callMessageService.SendNATSCallMessage(ctx, *callMessage.VenueId)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("发送NATS呼叫消息失败")
	}
	return vo.CallMessageVO{}, nil
}

// V3CancelCall 取消呼叫消息
func (s *CallApplicationServiceImpl) V3CancelCall(ctx context.Context, reqDto req.V3CancelCallReqDto) (vo.CallMessageVO, error) {
	if reqDto.RoomId == nil || *reqDto.RoomId == "" {
		return vo.CallMessageVO{}, fmt.Errorf("RoomId不能为空")
	}

	// 获取房间信息
	room, err := s.roomService.GetRoom(ctx, *reqDto.RoomId)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("房间不存在")
	}

	// 根据roomId查询所有未处理的呼叫消息
	callMessages, err := s.callMessageService.FindCallMessagesUnprocessedByRoomId(ctx, *reqDto.RoomId)
	if err != nil {
		return vo.CallMessageVO{}, fmt.Errorf("查询呼叫消息失败")
	}

	if len(callMessages) == 0 {
		return vo.CallMessageVO{}, fmt.Errorf("该房间没有未处理的呼叫消息")
	}

	timeNow := util.TimeNowUnixInt64()
	venueId := ""

	// 批量取消该房间所有未处理的呼叫消息
	for _, callMessage := range callMessages {
		if callMessage.Status == nil || *callMessage.Status != 0 {
			continue // 跳过已处理或已取消的消息
		}

		if venueId == "" && callMessage.VenueId != nil {
			venueId = *callMessage.VenueId
		}

		// 设置取消操作人为包厢信息
		cancelName := fmt.Sprintf("包厢：%s", *room.Name)
		toUpdateCallMessage := po.CallMessage{
			Id:         callMessage.Id,
			Status:     util.Ptr(2),
			OptId:      room.Id,
			OptName:    &cancelName,
			OptTime:    &timeNow,
			CancelId:   room.Id,
			CancelName: &cancelName,
			CancelTime: &timeNow,
		}
		err = s.callMessageService.UpdateCallMessage(ctx, &toUpdateCallMessage)
		if err != nil {
			return vo.CallMessageVO{}, fmt.Errorf("取消呼叫消息失败")
		}
	}

	// 发送NATS消息通知
	if venueId != "" {
		err = s.callMessageService.SendNATSCallMessage(ctx, venueId)
		if err != nil {
			return vo.CallMessageVO{}, fmt.Errorf("发送NATS呼叫消息失败")
		}
	}

	return vo.CallMessageVO{}, nil
}

// V3ListCall 查询呼叫消息
func (s *CallApplicationServiceImpl) V3ListCall(ctx context.Context, reqDto req.V3ListCallLastReqDto) ([]vo.CallMessageVO, error) {
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, fmt.Errorf("VenueId不能为空")
	}

	callMessages, err := s.callMessageService.FindCallMessagesCurrentDay(ctx, *reqDto.VenueId)
	if err != nil {
		return nil, fmt.Errorf("获取呼叫消息失败")
	}

	callMessagesVOs := make([]vo.CallMessageVO, len(callMessages))
	for i, callMessage := range callMessages {
		callMessagesVOs[i] = s.callMessageService.ConvertToCallMessageVO(ctx, callMessage)
	}

	return callMessagesVOs, nil
}

// V3ListCallUnprocessed 查询未处理的呼叫消息
func (s *CallApplicationServiceImpl) V3ListCallUnprocessed(ctx context.Context, reqDto req.V3ListCallUnprocessedReqDto) ([]vo.CallMessageVO, error) {
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, fmt.Errorf("VenueId不能为空")
	}
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return nil, fmt.Errorf("门店不存在")
	}
	if reqDto.EmployeeId == nil || *reqDto.EmployeeId == "" {
		return nil, fmt.Errorf("EmployeeId不能为空")
	}
	_, err = s.employeeService.FindEmployeeByID(ctx, *reqDto.EmployeeId)
	if err != nil {
		return nil, fmt.Errorf("员工不存在")
	}
	callMessages, err := s.callMessageService.FindCallMessagesUnprocessed(ctx, *reqDto.VenueId)
	if err != nil {
		return nil, fmt.Errorf("获取呼叫消息失败")
	}

	callMessagesVOs := make([]vo.CallMessageVO, len(callMessages))
	for i, callMessage := range callMessages {
		callMessagesVOs[i] = s.callMessageService.ConvertToCallMessageVO(ctx, callMessage)
	}

	return callMessagesVOs, nil
}

// V3ListCallTypes 查询呼叫类型
func (s *CallApplicationServiceImpl) V3ListCallTypes(ctx context.Context, reqDto req.V3ListCallTypesReqDto) ([]vo.CallTypesVO, error) {
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		return nil, fmt.Errorf("VenueId不能为空")
	}

	// 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil || venue == nil {
		return nil, fmt.Errorf("门店不存在")
	}

	// 获取呼叫类型列表
	callTypes := CALLTYPES

	// 转换为VO
	callTypesVOs := make([]vo.CallTypesVO, len(callTypes))
	for i, callType := range callTypes {
		callTypesVOs[i] = s.callTypesService.ConvertToCallTypesVO(ctx, callType)
	}

	return callTypesVOs, nil
}
