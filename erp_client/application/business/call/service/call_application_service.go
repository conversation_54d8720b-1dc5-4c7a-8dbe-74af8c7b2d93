package service

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
)

// CallApplicationService 呼叫应用服务接口
type CallApplicationService interface {
	// V3AddCall 新增呼叫消息
	V3AddCall(ctx context.Context, reqDto req.V3AddCallReqDto) (vo.CallMessageVO, error)

	// V3DealCall 处理呼叫消息
	V3DealCall(ctx context.Context, reqDto req.V3DealCallReqDto) (vo.CallMessageVO, error)

	// V3CancelCall 取消呼叫消息
	V3CancelCall(ctx context.Context, reqDto req.V3CancelCallReqDto) (vo.CallMessageVO, error)

	// V3ListCall 查询呼叫消息
	V3ListCall(ctx context.Context, reqDto req.V3ListCallLastReqDto) ([]vo.CallMessageVO, error)

	// V3ListCallTypes 查询呼叫类型
	V3ListCallTypes(ctx context.Context, reqDto req.V3ListCallTypesReqDto) ([]vo.CallTypesVO, error)

	// V3ListCallUnprocessed 查询未处理的呼叫消息列表
	V3ListCallUnprocessed(ctx context.Context, reqDto req.V3ListCallUnprocessedReqDto) ([]vo.CallMessageVO, error)
}
