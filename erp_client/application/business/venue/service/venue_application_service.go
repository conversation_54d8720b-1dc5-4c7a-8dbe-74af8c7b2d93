package service

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
)

// VenueApplicationService 场馆应用服务接口
type VenueApplicationService interface {
	// V3QueryBusinessReport 查询业务报表
	V3QueryBusinessReport(ctx context.Context, reqDto req.V3QueryBuisinessReportReqDto) (vo.VenueBusinessReportVO, error)

	// V3QueryBusinessReportV2 查询业务报表V2
	V3QueryBusinessReportV2(ctx context.Context, reqDto req.V3QueryBuisinessReportReqDto) (vo.VenueBusinessReportV2VO, error)
}
