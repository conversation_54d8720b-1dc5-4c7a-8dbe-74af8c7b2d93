package service

import (
	"context"
	"fmt"
	"io/ioutil"
	validateService "voderpltvv/erp_client/application/framework/validate"
	bookingService "voderpltvv/erp_client/domain/configuration/business/booking/service"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	ruleService "voderpltvv/erp_client/domain/rule/service"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	tradeService "voderpltvv/erp_client/domain/traderecord/service"
	roomplanService "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	orderproductService "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	pricePlanService "voderpltvv/erp_client/domain/valueobject/business/price_plan/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	roomTypeService "voderpltvv/erp_client/domain/valueobject/business/room_type/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"
)

// VenueApplicationServiceImpl 场馆应用服务实现
type VenueApplicationServiceImpl struct {
	processEngine       model.Engine
	orderService        tradeService.OrderService
	bookingService      bookingService.BookingService
	roomService         roomService.Service
	ruleService         ruleService.Service
	validateService     validateService.ValidateService
	sessionService      sessionService.SessionService
	payService          tradeService.PayService
	pricePlanService    pricePlanService.Service
	roomplanService     roomplanService.Service
	orderproductService orderproductService.Service
	venueService        venueService.VenueService
	employeeService     employeeService.Service
	payBillService      tradeService.PayBillService
	roomTypeService     roomTypeService.Service
	payRecordService    tradeService.PayRecordService
	orderAndPayService  tradeService.OrderAndPayService
	memberCardService   tradeService.MemberCardService
	memberCardOperationService tradeService.MemberCardOperationService
	memberCardVenueService tradeService.MemberCardVenueService
}

// NewVenueApplicationService 创建场馆应用服务实例
func NewVenueApplicationService(
	orderService tradeService.OrderService,
	bookingService bookingService.BookingService,
	roomService roomService.Service,
	ruleService ruleService.Service,
	validateService validateService.ValidateService,
	sessionService sessionService.SessionService,
	payService tradeService.PayService,
	pricePlanService pricePlanService.Service,
	roomplanService roomplanService.Service,
	orderproductService orderproductService.Service,
	venueService venueService.VenueService,
	employeeService employeeService.Service,
	payBillService tradeService.PayBillService,
	roomTypeService roomTypeService.Service,
	payRecordService tradeService.PayRecordService,
	orderAndPayService tradeService.OrderAndPayService,
	memberCardService tradeService.MemberCardService,
	memberCardOperationService tradeService.MemberCardOperationService,
	memberCardVenueService tradeService.MemberCardVenueService,
) VenueApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("orderService", orderService)
	engine.RegisterService("bookingService", bookingService)
	engine.RegisterService("roomService", roomService)
	engine.RegisterService("ruleService", ruleService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("sessionService", sessionService)
	engine.RegisterService("payService", payService)
	engine.RegisterService("pricePlanService", pricePlanService)
	engine.RegisterService("roomplanService", roomplanService)
	engine.RegisterService("orderproductService", orderproductService)
	engine.RegisterService("venueService", venueService)
	engine.RegisterService("employeeService", employeeService)
	engine.RegisterService("payBillService", payBillService)
	engine.RegisterService("roomTypeService", roomTypeService)
	engine.RegisterService("payRecordService", payRecordService)
	engine.RegisterService("orderAndPayService", orderAndPayService)
	engine.RegisterService("memberCardService", memberCardService)
	engine.RegisterService("memberCardOperationService", memberCardOperationService)
	engine.RegisterService("memberCardVenueService", memberCardVenueService)
	// 加载开台流程定义
	orderOpenProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_open_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载开台流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderOpenProcessContent); err != nil {
		panic(fmt.Errorf("解析开台流程定义失败: %w", err))
	}

	// 加载支付流程定义
	orderPayProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_pay_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载支付流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderPayProcessContent); err != nil {
		panic(fmt.Errorf("解析支付流程定义失败: %w", err))
	}

	return &VenueApplicationServiceImpl{
		processEngine:       engine,
		orderService:        orderService,
		bookingService:      bookingService,
		roomService:         roomService,
		ruleService:         ruleService,
		validateService:     validateService,
		sessionService:      sessionService,
		payService:          payService,
		pricePlanService:    pricePlanService,
		roomplanService:     roomplanService,
		orderproductService: orderproductService,
		venueService:        venueService,
		employeeService:     employeeService,
		payBillService:      payBillService,
		roomTypeService:     roomTypeService,
		payRecordService:    payRecordService,
		orderAndPayService:  orderAndPayService,
		memberCardService:   memberCardService,
		memberCardOperationService: memberCardOperationService,
		memberCardVenueService: memberCardVenueService,
	}
}

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *VenueApplicationServiceImpl) utilValidateBaseParams(ctx context.Context, venueId, employeeId *string, employeeIdRequired bool) (po.Venue, po.Employee, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Employee{}, fmt.Errorf("VenueId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Employee{}, fmt.Errorf("门店不存在")
	}

	// 0.2. 检查员工是否存在
	employee := po.Employee{}
	if employeeIdRequired {
		if employeeId == nil || *employeeId == "" {
			return po.Venue{}, po.Employee{}, fmt.Errorf("EmployeeId不能为空")
		}
		employee, err = s.employeeService.FindEmployeeByID(ctx, *employeeId)
		if err != nil {
			return po.Venue{}, po.Employee{}, fmt.Errorf("员工不存在")
		}
		if !(employee.VenueId != nil && *employee.VenueId == *venueId) {
			return po.Venue{}, po.Employee{}, fmt.Errorf("员工不属于该门店")
		}
	}
	return *venue, employee, nil
}

// V3QueryBusinessReport 查询所有场馆
func (s *VenueApplicationServiceImpl) V3QueryBusinessReport(ctx context.Context, reqDto req.V3QueryBuisinessReportReqDto) (vo.VenueBusinessReportVO, error) {
	venue, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.EmployeeId, false)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	if reqDto.StartTime == nil || *reqDto.StartTime == 0 {
		return vo.VenueBusinessReportVO{}, fmt.Errorf("StartTime不能为空")
	}
	if reqDto.EndTime == nil || *reqDto.EndTime == 0 {
		return vo.VenueBusinessReportVO{}, fmt.Errorf("EndTime不能为空")
	}
	startTimeReq := *reqDto.StartTime
	endTimeReq := *reqDto.EndTime
	if startTimeReq > endTimeReq {
		return vo.VenueBusinessReportVO{}, fmt.Errorf("StartTime不能大于EndTime")
	}
	venueVO := s.venueService.ConvertToVenueVO(ctx, venue)

	// 营业总额
	payBills, err := s.payBillService.FindsByTimeRange(ctx, *venue.Id, startTimeReq, endTimeReq)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	payBillIds := make([]string, 0)
	for _, payBill := range payBills {
		util.AddListElement(&payBillIds, *payBill.BillId)
	}
	orderAndPays, err := s.orderAndPayService.FindAllByBillIds(ctx, payBillIds)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	// orderNOs
	orderNos := make([]string, 0)
	for _, orderAndPay := range *orderAndPays {
		util.AddListElement(&orderNos, *orderAndPay.OrderNo)
	}
	orderProducts, err := s.orderproductService.FindsByOrderNos(ctx, *venue.Id, orderNos)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	orderRoomPlans, err := s.roomplanService.FindsByOrderNos(ctx, *venue.Id, orderNos)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	orders, err := s.orderService.FindOrdersByOrderNos(ctx, *venue.Id, orderNos)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}

	// 3. 计算营业概览
	businessOverview := s.payBillService.CalcBusinessOverviewForVenue(ctx, *venue.Id, orders, payBills, *orderAndPays, orderProducts, orderRoomPlans)

	// 2. 场次概览
	// 2.1. 查询场次 []{order.SessionId}
	sessions, err := s.sessionService.FindSessionsByTimeRange(ctx, *venue.Id, startTimeReq, endTimeReq)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	// 2.2. 查询房间 []{session.RoomId}
	rooms, err := s.roomService.FindRoomsByCtime(ctx, *venue.Id, startTimeReq, endTimeReq)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	// 场次概览
	sessionOverview := vo.VenueBusinessReportVOSessionOverview{
		SessionCount: len(sessions),
		RoomCount:    len(rooms),
	}

	// 3. 会员概览
	memberCardVenues, err := s.memberCardVenueService.FindAllByVenueId(ctx, *venue.Id)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	memberCardIds := make([]string, 0)
	for _, memberCardVenue := range memberCardVenues {
		util.AddListElement(&memberCardIds, *memberCardVenue.MemberCardId)
	}
	memberCards, err := s.memberCardService.FindsByIdsAndCtime(ctx, memberCardIds, startTimeReq, endTimeReq)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	
	// 新增会员数
	todayMemberCards := make([]po.MemberCard, 0)
	for _, memberCard := range memberCards {
		if *memberCard.Ctime > startTimeReq && *memberCard.Ctime < endTimeReq {
			todayMemberCards = append(todayMemberCards, memberCard)
		}
	}
	memberCardOperations, err := s.memberCardOperationService.FindsRechargeByTimeRange(ctx, *venue.Id, startTimeReq, endTimeReq)
	if err != nil {
		return vo.VenueBusinessReportVO{}, err
	}
	totalRechargeAmount := int64(0)
	for _, memberCardOperation := range memberCardOperations {
		totalRechargeAmount += *memberCardOperation.PrincipalAmount
	}

	// 会员概览
	memberOverview := vo.VenueBusinessReportVOMemberOverview{
		NewMemberCount:   int64(len(todayMemberCards)),
		TotalMemberCount: int64(len(memberCards)),
		RechargeAmount:   totalRechargeAmount,
	}
	rtVO := vo.VenueBusinessReportVO{
		VenueId:          venueVO.Id,
		VenueName:        venueVO.Name,
		StartHour:        venueVO.StartHours,
		EndHour:          venueVO.EndHours,
		StartTime:        *reqDto.StartTime,
		EndTime:          *reqDto.EndTime,
		BusinessOverview: businessOverview,
		SessionOverview:  sessionOverview,
		MemberOverview:   memberOverview,
	}
	return rtVO, nil
}

// V3QueryBusinessReport 查询所有场馆
func (s *VenueApplicationServiceImpl) V3QueryBusinessReportV2(ctx context.Context, reqDto req.V3QueryBuisinessReportReqDto) (vo.VenueBusinessReportV2VO, error) {
	venue, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.EmployeeId, false)
	if err != nil {
		return vo.VenueBusinessReportV2VO{}, err
	}
	if reqDto.StartTime == nil || *reqDto.StartTime == 0 {
		return vo.VenueBusinessReportV2VO{}, fmt.Errorf("StartTime不能为空")
	}
	if reqDto.EndTime == nil || *reqDto.EndTime == 0 {
		return vo.VenueBusinessReportV2VO{}, fmt.Errorf("EndTime不能为空")
	}
	startTimeReq := *reqDto.StartTime
	endTimeReq := *reqDto.EndTime
	if startTimeReq > endTimeReq {
		return vo.VenueBusinessReportV2VO{}, fmt.Errorf("StartTime不能大于EndTime")
	}
	// 检查时间间隔不能超过31天
	if endTimeReq - startTimeReq > int64(31 * 24 * 60 * 60) + 5 {
		return vo.VenueBusinessReportV2VO{}, fmt.Errorf("时间间隔不能超过31天")
	}
	modelBasePayBillBO, err := s.orderService.GetModelBasePayBillBO(ctx, *venue.Id, *reqDto.EmployeeId, startTimeReq, endTimeReq)
	modelBasePayBillBO.Venue = &venue

	rtVO, err := s.orderService.ConvertToVenueBusinessReportVO(ctx, modelBasePayBillBO)

	return rtVO, nil
}
