package service

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
)

// BillApplicationService 账单应用服务接口
type BillApplicationService interface {
	// V3BillBackView 查询账单
	V3BillBackView(ctx context.Context, reqDto req.V3QueryBillBackViewReqDto) (vo.PayBillUnionVO, error)
	// V3BillBack 账单还原
	V3BillBack(ctx context.Context, reqDto req.V3BillBackReqDto) (vo.PayBillUnionVO, error)
	// V3BillQuery 账单查询
	V3BillQuery(ctx context.Context, reqDto req.V3BillQueryReqDto) ([]vo.PayBillExtVO, error)
	// V3BillQueryBySession 根据sessionId查询账单
	V3BillQueryBySession(ctx context.Context, reqDto req.V3BillQueryBySessionReqDto) ([]vo.PayBillVO, error)
}
