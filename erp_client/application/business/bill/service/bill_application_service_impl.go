package service

import (
	"context"
	"fmt"
	"io/ioutil"
	validateService "voderpltvv/erp_client/application/framework/validate"
	bookingService "voderpltvv/erp_client/domain/configuration/business/booking/service"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	ruleService "voderpltvv/erp_client/domain/rule/service"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	tradeService "voderpltvv/erp_client/domain/traderecord/service"
	roomplanService "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	orderproductService "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	pricePlanService "voderpltvv/erp_client/domain/valueobject/business/price_plan/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	roomTypeService "voderpltvv/erp_client/domain/valueobject/business/room_type/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"

	"github.com/sirupsen/logrus"
)

// BillApplicationServiceImpl 账单应用服务实现
type BillApplicationServiceImpl struct {
	processEngine       model.Engine
	orderService        tradeService.OrderService
	bookingService      bookingService.BookingService
	roomService         roomService.Service
	ruleService         ruleService.Service
	validateService     validateService.ValidateService
	sessionService      sessionService.SessionService
	payService          tradeService.PayService
	pricePlanService    pricePlanService.Service
	roomplanService     roomplanService.Service
	orderproductService orderproductService.Service
	venueService        venueService.VenueService
	employeeService     employeeService.Service
	payBillService      tradeService.PayBillService
	roomTypeService     roomTypeService.Service
	payRecordService    tradeService.PayRecordService
	orderAndPayService  tradeService.OrderAndPayService
}

// NewBillApplicationService 创建账单应用服务实例
func NewBillApplicationService(
	orderService tradeService.OrderService,
	bookingService bookingService.BookingService,
	roomService roomService.Service,
	ruleService ruleService.Service,
	validateService validateService.ValidateService,
	sessionService sessionService.SessionService,
	payService tradeService.PayService,
	pricePlanService pricePlanService.Service,
	roomplanService roomplanService.Service,
	orderproductService orderproductService.Service,
	venueService venueService.VenueService,
	employeeService employeeService.Service,
	payBillService tradeService.PayBillService,
	roomTypeService roomTypeService.Service,
	payRecordService tradeService.PayRecordService,
	orderAndPayService tradeService.OrderAndPayService,
) BillApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("orderService", orderService)
	engine.RegisterService("bookingService", bookingService)
	engine.RegisterService("roomService", roomService)
	engine.RegisterService("ruleService", ruleService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("sessionService", sessionService)
	engine.RegisterService("payService", payService)
	engine.RegisterService("pricePlanService", pricePlanService)
	engine.RegisterService("roomplanService", roomplanService)
	engine.RegisterService("orderproductService", orderproductService)
	engine.RegisterService("venueService", venueService)
	engine.RegisterService("employeeService", employeeService)
	engine.RegisterService("payBillService", payBillService)
	engine.RegisterService("roomTypeService", roomTypeService)
	engine.RegisterService("payRecordService", payRecordService)
	engine.RegisterService("orderAndPayService", orderAndPayService)
	// 加载开台流程定义
	orderOpenProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_open_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载开台流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderOpenProcessContent); err != nil {
		panic(fmt.Errorf("解析开台流程定义失败: %w", err))
	}

	// 加载支付流程定义
	orderPayProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_pay_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载支付流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderPayProcessContent); err != nil {
		panic(fmt.Errorf("解析支付流程定义失败: %w", err))
	}

	return &BillApplicationServiceImpl{
		processEngine:       engine,
		orderService:        orderService,
		bookingService:      bookingService,
		roomService:         roomService,
		ruleService:         ruleService,
		validateService:     validateService,
		sessionService:      sessionService,
		payService:          payService,
		pricePlanService:    pricePlanService,
		roomplanService:     roomplanService,
		orderproductService: orderproductService,
		venueService:        venueService,
		employeeService:     employeeService,
		payBillService:      payBillService,
		roomTypeService:     roomTypeService,
		payRecordService:    payRecordService,
		orderAndPayService:  orderAndPayService,
	}
}

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *BillApplicationServiceImpl) utilValidateBaseParams(ctx context.Context, venueId, roomId, sessionId, employeeId *string, roomIdRequired, sessionIdRequired bool) (po.Venue, po.Room, po.Session, po.Employee, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("VenueId不能为空")
	}
	if roomIdRequired && (roomId == nil || *roomId == "") {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("RoomId不能为空")
	}
	if employeeId == nil || *employeeId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("EmployeeId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("门店不存在")
	}
	room := po.Room{}
	// 0.2. 检查房间是否存在
	if roomIdRequired {
		room, err = s.roomService.GetRoom(ctx, *roomId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不存在")
		}
		if !(room.VenueId != nil && *room.VenueId == *venueId) {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不属于该门店")
		}
	}
	// 0.3. 检查员工是否存在
	employee, err := s.employeeService.FindEmployeeByID(ctx, *employeeId)
	if err != nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("员工不存在")
	}
	// 0.4. 检查场次是否存在
	session := po.Session{}
	if sessionIdRequired {
		if sessionId == nil || *sessionId == "" {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("SessionId不能为空")
		}
		session, err = s.sessionService.FindBySessionId(ctx, *sessionId, *venueId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("场次信息不存在")
		}
		if roomIdRequired {
			if !(room.SessionId != nil && *room.SessionId == *sessionId) {
				return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不属于该场次")
			}
		}
	}
	return *venue, room, session, employee, nil
}

// ////////////////////////////////////////////////

// V3BillBackView 查询账单
func (s *BillApplicationServiceImpl) V3BillBackView(ctx context.Context, reqDto req.V3QueryBillBackViewReqDto) (vo.PayBillUnionVO, error) {
	_, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, reqDto.SessionId, reqDto.EmployeeId, false, true)
	if err != nil {
		return vo.PayBillUnionVO{}, err
	}
	payBills, err := s.payBillService.FindsBySessionId(ctx, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return vo.PayBillUnionVO{}, err
	}
	payRecords, err := s.payRecordService.FindsBySessionId(ctx, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return vo.PayBillUnionVO{}, err
	}
	payBillVOs := make([]vo.PayBillVO, len(payBills))
	employeeIds := make([]string, 0)
	for i, payBill := range payBills {
		payBillVOs[i] = s.payBillService.ConvertToPayBillVO(ctx, payBill)
		employeeIds = append(employeeIds, *payBill.EmployeeId)
	}
	employees, err := s.employeeService.FindsByIds(ctx, employeeIds)
	if err != nil {
		return vo.PayBillUnionVO{}, err
	}

	payRecordVOs := make([]vo.PayRecordVO, len(payRecords))
	for i, payRecord := range payRecords {
		payRecordVOs[i] = s.payRecordService.ConvertToPayRecordVO(ctx, payRecord)
	}
	employeeVOs := make([]vo.EmployeeVO, len(employees))
	for i, employee := range employees {
		employeeVOs[i] = s.employeeService.ConvertToEmployeeVO(ctx, employee)
	}
	payBillUnionVO := vo.PayBillUnionVO{
		PayBillVOs:   payBillVOs,
		PayRecordVOs: payRecordVOs,
		EmployeeVOs:  employeeVOs,
	}
	return payBillUnionVO, nil
}

// V3BillBack 账单还原
func (s *BillApplicationServiceImpl) V3BillBack(ctx context.Context, reqDto req.V3BillBackReqDto) (vo.PayBillUnionVO, error) {
	// 0. 验证基础参数
	_, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, reqDto.SessionId, reqDto.EmployeeId, false, true)
	if err != nil {
		return vo.PayBillUnionVO{}, err
	}
	if reqDto.BillIds == nil || len(*reqDto.BillIds) == 0 {
		return vo.PayBillUnionVO{}, fmt.Errorf("账单ID不能为空")
	}
	payInfoPOs := s.payService.GetPayInfoBillBackBySessionId(ctx, *reqDto.SessionId, *reqDto.VenueId)
	filterNomalBillIds := make([]string, 0)
	billId2PayBill := make(map[string]po.PayBill)
	for _, payBill := range payInfoPOs.PayBills {
		billId2PayBill[*payBill.BillId] = payBill
	}
	// 1. 验证billids是否存在
	for _, billId := range *reqDto.BillIds {
		var payBillTmp po.PayBill
		var ok bool
		payBillTmp, ok = billId2PayBill[billId]
		if !ok {
			return vo.PayBillUnionVO{}, fmt.Errorf("账单不存在")
		}
		// 筛选 账单未还原
		if !*payBillTmp.IsBack {
			filterNomalBillIds = append(filterNomalBillIds, billId)
		}
	}
	// 2. 转换为 []PayBillInfoSrcVO
	payBillInfoSrcVOs := s.payService.ParseToPayBillInfoSrcVOs(ctx, payInfoPOs, filterNomalBillIds)
	// 3. 根据 payBillInfoSrcVOs 转换为 PayBillInfoNomalVO
	payBillInfoNomalVOs := s.payService.ParseToPayBillInfoNomalVOs(ctx, payBillInfoSrcVOs)
	// 4. 根据 payBillInfoNomalVOs 生成 payBillInfoBillBackVOs
	payBillInfoBillBackVOsTmp := s.payService.BuildPayBillInfoBillBackVOs(ctx, payBillInfoNomalVOs)
	payBillInfoBillBackVOs := []vo.PayBillInfoBillBackVO{}
	for _, payBillInfoBillBackVO := range payBillInfoBillBackVOsTmp {
		isFree := payBillInfoBillBackVO.PayBillInfoNomalVO.PayBillVO.IsFree
		isGift := payBillInfoBillBackVO.PayBillInfoNomalVO.PayBillVO.IsGift
		if !isFree && !isGift && payBillInfoBillBackVO.NewPayBill.TotalFee != nil && *payBillInfoBillBackVO.NewPayBill.TotalFee <= 0 {
			// return vo.PayBillUnionVO{}, fmt.Errorf("账单%s已经全部退款", *payBillInfoBillBackVO.NewPayBill.BillPid)
		} else {
			payBillInfoBillBackVOs = append(payBillInfoBillBackVOs, payBillInfoBillBackVO)
		}
	}
	if len(payBillInfoBillBackVOs) == 0 {
		return vo.PayBillUnionVO{}, fmt.Errorf("账单已经全部退款")
	}
	// 5. 保存payBillInfoBillBackVOs, 并更新session未付金额
	err = s.payService.SavePayBillInfoBillBackVOs(ctx, payBillInfoBillBackVOs, reqDto)
	if err != nil {
		return vo.PayBillUnionVO{}, err
	}

	messageNewPayRecords := make([]po.PayRecord, 0)
	for _, payBillInfoBillBackVO := range payBillInfoBillBackVOs {
		messageNewPayRecords = append(messageNewPayRecords, payBillInfoBillBackVO.NewPayRecords...)
	}
	// 14. 发送NATS消息
	err = s.payService.SendNATSMessageForRoomStatusChanged(ctx, *reqDto.VenueId, false, messageNewPayRecords)
	if err != nil {
		logrus.Error("发送NATS消息失败:" + err.Error())
	}
	return vo.PayBillUnionVO{}, nil
}

// V3BillQuery 查询账单
func (s *BillApplicationServiceImpl) V3BillQuery(ctx context.Context, reqDto req.V3BillQueryReqDto) ([]vo.PayBillExtVO, error) {
	if reqDto.VenueId == nil {
		return nil, fmt.Errorf("venueId不能为空")
	}
	venue, err := s.venueService.FindByID(ctx, *reqDto.VenueId)
	if err != nil {
		return nil, err
	}
	if venue == nil {
		return nil, fmt.Errorf("venueId不存在")
	}
	// _, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, nil, reqDto.EmployeeId, false, false)
	// if err != nil {
	// 	return nil, err
	// }
	if reqDto.StartTime == nil {
		return nil, fmt.Errorf("startTime不能为空")
	}
	if reqDto.EndTime == nil {
		return nil, fmt.Errorf("endTime不能为空")
	}
	if *reqDto.StartTime > *reqDto.EndTime {
		return nil, fmt.Errorf("startTime不能大于endTime")
	}
	// if reqDto.PageNum == nil {
	// 	return nil, fmt.Errorf("pageNum不能为空")
	// }
	// if reqDto.PageSize == nil {
	// 	return nil, fmt.Errorf("pageSize不能为空")
	// }
	// if *reqDto.PageNum < 1 {
	// 	return nil, fmt.Errorf("pageNum不能小于1")
	// }
	// if *reqDto.PageSize < 1 {
	// 	return nil, fmt.Errorf("pageSize不能小于1")
	// }

	// // 2. 查询订单 sessionId 下的订单
	// orders, err := s.orderService.FindsBySessionIds(ctx, *reqDto.VenueId, sessionIds)
	// if err != nil {
	// 	return nil, err
	// }
	// orderNos := make([]string, len(orders))
	// for i, order := range orders {
	// 	orderNos[i] = *order.OrderNo
	// }
	// // 2.1 查询订单产品
	// orderProducts, err := s.orderproductService.FindsByOrderNos(ctx, *reqDto.VenueId, orderNos)
	// if err != nil {
	// 	return nil, err
	// }
	// // 2.2 查询订单价格计划
	// orderRoomPlans, err := s.roomplanService.FindsByOrderNos(ctx, *reqDto.VenueId, orderNos)
	// if err != nil {
	// 	return nil, err
	// }
	// orderExtFeeVOs := s.orderService.BuildOrderVOsWithRoomAndProduct(ctx, orders, orderProducts, orderRoomPlans)

	// 1. 查询账单 sessionId 下的账单
	payBills, err := s.payBillService.FindsByTimeRange(ctx, *reqDto.VenueId, *reqDto.StartTime, *reqDto.EndTime)
	if err != nil {
		return nil, err
	}
	payBillIds := make([]string, len(payBills))
	for i, payBill := range payBills {
		payBillIds[i] = *payBill.BillId
	}
	// 2.1 查询支付记录 sessionId 下的支付记录
	payRecords, err := s.payRecordService.FindsByBillIds(ctx, *reqDto.VenueId, payBillIds)
	if err != nil {
		return nil, err
	}
	billId2PayRecordVO := make(map[string][]vo.PayRecordVO)
	for _, payRecord := range payRecords {
		billId2PayRecordVO[*payRecord.BillId] = append(billId2PayRecordVO[*payRecord.BillId], s.payRecordService.ConvertToPayRecordVO(ctx, payRecord))
	}
	// 3. 查询订单 sessionId 下的订单
	orderAndPayVOs, err := s.orderAndPayService.FindAllByBillIds(ctx, payBillIds)
	if err != nil {
		return nil, err
	}
	orderNos := make([]string, 0)
	for _, orderAndPayVO := range *orderAndPayVOs {
		orderNos = append(orderNos, *orderAndPayVO.OrderNo)
	}
	// 3.1 根据orderAndPayVOs 转换为 payBillVOs
	orders, err := s.orderService.FindOrdersByOrderNos(ctx, *reqDto.VenueId, orderNos)
	if err != nil {
		return nil, err
	}
	// 3.2 查询订单产品
	orderProducts, err := s.orderproductService.FindsByOrderNos(ctx, *reqDto.VenueId, orderNos)
	if err != nil {
		return nil, err
	}
	// 3.3 查询订单价格计划
	orderRoomPlans, err := s.roomplanService.FindsByOrderNos(ctx, *reqDto.VenueId, orderNos)
	if err != nil {
		return nil, err
	}
	orderExtFeeVOs := s.orderService.BuildOrderVOsWithRoomAndProduct(ctx, orders, orderProducts, orderRoomPlans)
	orderNo2OrderExtFeeVO := make(map[string]vo.OrderExtFeeVO)
	for _, orderExtFeeVO := range orderExtFeeVOs {
		orderNo2OrderExtFeeVO[orderExtFeeVO.OrderNo] = orderExtFeeVO
	}
	billId2OrderExtFeeVO := make(map[string][]vo.OrderExtFeeVO)
	for _, orderAndPayVO := range *orderAndPayVOs {
		orderExtFeeVO, ok := orderNo2OrderExtFeeVO[*orderAndPayVO.OrderNo]
		if !ok {
			continue
		}
		billId2OrderExtFeeVO[*orderAndPayVO.BillId] = append(billId2OrderExtFeeVO[*orderAndPayVO.BillId], orderExtFeeVO)
	}

	payBillExtVOs := make([]vo.PayBillExtVO, len(payBills))
	for i, payBill := range payBills {
		payBillExtVO := vo.PayBillExtVO{
			PayBillVO: s.payBillService.ConvertToPayBillVO(ctx, payBill),
		}
		if payRecordVOs, ok := billId2PayRecordVO[*payBill.BillId]; ok {
			payBillExtVO.PayRecordVOs = payRecordVOs
		}
		if orderExtFeeVOs, ok := billId2OrderExtFeeVO[*payBill.BillId]; ok {
			payBillExtVO.OrderVOs = orderExtFeeVOs
		}
		payBillExtVOs[i] = payBillExtVO
	}

	return payBillExtVOs, nil
}

// V3BillQueryBySession 根据sessionId查询账单
func (s *BillApplicationServiceImpl) V3BillQueryBySession(ctx context.Context, reqDto req.V3BillQueryBySessionReqDto) ([]vo.PayBillVO, error) {
	// 0. 验证基础参数
	_, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, reqDto.SessionId, reqDto.EmployeeId, false, true)
	if err != nil {
		return nil, err
	}

	// 1. 查询账单
	payBills, err := s.payBillService.FindsBySessionId(ctx, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return nil, err
	}

	// 2. 转换为PayBillVO数组
	payBillVOs := make([]vo.PayBillVO, len(payBills))
	for i, payBill := range payBills {
		payBillVOs[i] = s.payBillService.ConvertToPayBillVO(ctx, payBill)
	}

	return payBillVOs, nil
}
