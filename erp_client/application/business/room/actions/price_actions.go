package actions

// import (
// 	"encoding/json"
// 	"fmt"
// 	"time"

// 	"voderpltvv/erp_client/api/vo"
// 	"voderpltvv/erp_client/domain/rule/engine"
// 	"voderpltvv/erp_client/domain/rule/model"
// )

// // PriceActionHandler 价格动作处理器
// type PriceActionHandler struct {
// 	// 可以注入需要的服务
// }

// // NewPriceActionHandler 创建价格动作处理器
// func NewPriceActionHandler() *PriceActionHandler {
// 	return &PriceActionHandler{}
// }

// // RegisterActions 注册价格相关动作
// func (h *PriceActionHandler) RegisterActions(e engine.RuleEngine) {
// 	e.RegisterAction("apply_holiday_prices", h.ApplyHolidayPrices)
// 	e.RegisterAction("calculate_member_price", h.CalculateMemberPrice)
// 	e.RegisterAction("validate_minimum_charge", h.ValidateMinimumCharge)
// }

// // ApplyHolidayPrices 应用节假日价格
// func (h *PriceActionHandler) ApplyHolidayPrices(ctx *model.RuleContext, params map[string]interface{}) error {
// 	pricePlans, err := ctx.GetTypedValue[*[]vo.PricePlanVO]("price_plans")
// 	if err != nil {
// 		return fmt.Errorf("获取价格方案失败: %w", err)
// 	}

// 	currentDate, err := ctx.GetTypedValue[string]("current_date")
// 	if err != nil {
// 		return fmt.Errorf("获取当前日期失败: %w", err)
// 	}

// 	holidays, err := ctx.GetTypedValue[[]time.Time]("holidays")
// 	if err != nil {
// 		return fmt.Errorf("获取节假日列表失败: %w", err)
// 	}

// 	// 检查是否是节假日
// 	isHoliday := false
// 	for _, holiday := range holidays {
// 		if holiday.Format("2006-01-02") == currentDate {
// 			isHoliday = true
// 			break
// 		}
// 	}

// 	if !isHoliday {
// 		return nil
// 	}

// 	// 应用节假日价格
// 	for i := range *pricePlans {
// 		plan := &(*pricePlans)[i]
// 		if plan.HolidayPriceConfig == "" {
// 			continue
// 		}

// 		var holidayPrices struct {
// 			BaseRoomFee   int64 `json:"baseRoomFee"`
// 			BirthdayFee   int64 `json:"birthdayFee"`
// 			ActivityFee   int64 `json:"activityFee"`
// 			GroupBuyFee   int64 `json:"groupBuyFee"`
// 			MinimumCharge int64 `json:"minimumCharge"`
// 		}

// 		if err := json.Unmarshal([]byte(plan.HolidayPriceConfig), &holidayPrices); err != nil {
// 			return fmt.Errorf("解析节假日价格配置失败: %w", err)
// 		}

// 		plan.BaseRoomFee = holidayPrices.BaseRoomFee
// 		plan.BirthdayFee = holidayPrices.BirthdayFee
// 		plan.ActivityFee = holidayPrices.ActivityFee
// 		plan.GroupBuyFee = holidayPrices.GroupBuyFee
// 		plan.MinimumCharge = holidayPrices.MinimumCharge
// 	}

// 	return nil
// }

// // CalculateMemberPrice 计算会员价格
// func (h *PriceActionHandler) CalculateMemberPrice(ctx *model.RuleContext, params map[string]interface{}) error {
// 	pricePlans, err := ctx.GetTypedValue[*[]vo.PricePlanVO]("price_plans")
// 	if err != nil {
// 		return fmt.Errorf("获取价格方案失败: %w", err)
// 	}

// 	memberLevel, err := ctx.GetTypedValue[int]("member_level")
// 	if err != nil {
// 		return fmt.Errorf("获取会员等级失败: %w", err)
// 	}

// 	if memberLevel <= 0 {
// 		return nil
// 	}

// 	priceFields, ok := params["price_fields"].([]string)
// 	if !ok {
// 		return fmt.Errorf("缺少价格字段配置")
// 	}

// 	for i := range *pricePlans {
// 		plan := &(*pricePlans)[i]
// 		memberDiscount := float64(plan.MemberDiscountRate) / 100.0
// 		if memberDiscount <= 0 {
// 			memberDiscount = 1.0
// 		}

// 		for _, field := range priceFields {
// 			switch field {
// 			case "baseRoomFee":
// 				plan.BaseRoomFee = int64(float64(plan.BaseRoomFee) * memberDiscount)
// 			case "birthdayFee":
// 				plan.BirthdayFee = int64(float64(plan.BirthdayFee) * memberDiscount)
// 			case "activityFee":
// 				plan.ActivityFee = int64(float64(plan.ActivityFee) * memberDiscount)
// 			case "groupBuyFee":
// 				plan.GroupBuyFee = int64(float64(plan.GroupBuyFee) * memberDiscount)
// 			}
// 		}
// 	}

// 	return nil
// }

// // ValidateMinimumCharge 验证最低消费
// func (h *PriceActionHandler) ValidateMinimumCharge(ctx *model.RuleContext, params map[string]interface{}) error {
// 	pricePlans, err := ctx.GetTypedValue[*[]vo.PricePlanVO]("price_plans")
// 	if err != nil {
// 		return fmt.Errorf("获取价格方案失败: %w", err)
// 	}

// 	for i := range *pricePlans {
// 		plan := &(*pricePlans)[i]
// 		if plan.MinimumCharge > 0 {
// 			plan.HasMinimumCharge = true
// 			if plan.BaseRoomFee < plan.MinimumCharge {
// 				plan.BaseRoomFee = plan.MinimumCharge
// 			}
// 		}
// 	}

// 	return nil
// }
