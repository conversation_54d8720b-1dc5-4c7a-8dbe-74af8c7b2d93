package room

import (
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/api/vo"

	"github.com/gin-gonic/gin"
)

// ApplicationService 房间应用服务接口
type ApplicationService interface {
	// GetRoomOpenView 获取房间开放视图
	GetRoomOpenView(ctx *gin.Context, reqDto dto.QueryRoomStageReqDto) (*vo.RoomVO, error)

	// V3GetRoomStage 获取房间阶段
	V3GetRoomStage(ctx *gin.Context, reqDto dto.V3QueryRoomStageReqDto) (*vo.RoomVO, error)

	// V3GetRoomInfoByQRCode 获取房间信息
	V3GetRoomInfoByQRCode(ctx *gin.Context, reqDto dto.V3QueryRoomInfoByQRCodeReqDto) (*vo.SessionVO, error)
}
