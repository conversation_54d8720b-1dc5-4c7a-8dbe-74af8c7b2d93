package test

import (
	"context"
	"testing"
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/config"
	testUtils "voderpltvv/test"

	"github.com/k0kubun/pp/v3"
)

// RoomService 定义测试所需的服务接口
type RoomService interface {
	GetRoomOpenView(ctx context.Context, reqDto dto.QueryRoomStageReqDto) (map[string]interface{}, error)
}

var (
	// 测试数据
	roomId   = "31f667da29864db6aede2d5ccb3d50bb"
	typeId   = "66dfa2f2155ffb38a019c9ba"
	areaId   = "670ca275aec75a21bd096e7a"
	venueId  = "105497"
	emptyStr = ""

	// 测试用例集
	testCases = []struct {
		name     string
		reqDto   dto.QueryRoomStageReqDto
		validate func(t *testing.T, got map[string]interface{}, err error, reqDto dto.QueryRoomStageReqDto)
	}{
		{
			name: "successful room open view query",
			reqDto: dto.QueryRoomStageReqDto{
				RoomId:    &roomId,
				SessionId: &emptyStr,
				TypeId:    &typeId,
				AreaId:    &areaId,
				VenueId:   &venueId,
			},
			validate: func(t *testing.T, got map[string]interface{}, err error, reqDto dto.QueryRoomStageReqDto) {
				if err != nil {
					t.Errorf("GetRoomOpenView() unexpected error = %v", err)
					return
				}
				if got == nil {
					t.Error("GetRoomOpenView() returned nil result")
					return
				}
				if roomId, ok := got["roomId"].(string); !ok || roomId != *reqDto.RoomId {
					t.Errorf("GetRoomOpenView() roomId = %v, want %v", roomId, *reqDto.RoomId)
				}
				// TODO: 添加更多字段的验证
				pp.Println("Test result:", got)
			},
		},
		{
			name: "room not found",
			reqDto: dto.QueryRoomStageReqDto{
				RoomId:  &emptyStr,
				TypeId:  &typeId,
				AreaId:  &areaId,
				VenueId: &venueId,
			},
			validate: func(t *testing.T, got map[string]interface{}, err error, reqDto dto.QueryRoomStageReqDto) {
				if err == nil {
					t.Error("GetRoomOpenView() expected error, got nil")
				}
			},
		},
	}
)

func TestGetRoomOpenView(t *testing.T) {
	// 创建测试套件
	suite := testUtils.NewTestSuite(func(container *config.Container) RoomService {
		return container.GetRoomApplicationService().(RoomService)
	})

	// 使用套件的 Run 方法执行测试
	suite.Run(t, func(t *testing.T, s *testUtils.TestSuite[RoomService]) {
		// 遍历测试用例
		for _, tc := range testCases {
			// 使用套件的 RunTestCase 方法执行每个测试用例
			s.RunTestCase(t, tc.name, func(t *testing.T) {
				got, err := s.GetService().GetRoomOpenView(context.Background(), tc.reqDto)
				tc.validate(t, got, err, tc.reqDto)
			})
		}
	})
}
