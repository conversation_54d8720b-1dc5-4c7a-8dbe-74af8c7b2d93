package service

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"voderpltvv/erp_client/api/dto"
	validateService "voderpltvv/erp_client/application/framework/validate"
	bookingService "voderpltvv/erp_client/domain/configuration/business/booking/service"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	ruleService "voderpltvv/erp_client/domain/rule/service"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	orderService "voderpltvv/erp_client/domain/traderecord/service"
	areaService "voderpltvv/erp_client/domain/valueobject/business/area/service"
	holidayService "voderpltvv/erp_client/domain/valueobject/business/holiday/service"
	orderRoomPlanService "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	orderProductService "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	pricePlanService "voderpltvv/erp_client/domain/valueobject/business/price_plan/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	roomFaultService "voderpltvv/erp_client/domain/valueobject/business/room_fault/service"
	roomOperationService "voderpltvv/erp_client/domain/valueobject/business/room_operation/service"
	roomThemeService "voderpltvv/erp_client/domain/valueobject/business/room_theme/service"
	roomTypeService "voderpltvv/erp_client/domain/valueobject/business/room_type/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	appService "voderpltvv/erp_managent/application/roomprice"
	"voderpltvv/erp_managent/domain/roomprice/repository"
	domainService "voderpltvv/erp_managent/domain/roomprice/service"
	"voderpltvv/erp_managent/infra/repository/roomprice"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

// RoomApplicationService 房间应用服务
type RoomApplicationService interface {
	// GetRoomOpenView 获取房间开放视图
	GetRoomOpenView(ctx context.Context, reqDto dto.QueryRoomStageReqDto) (map[string]interface{}, error)
	V3GetRoomOpenView(ctx context.Context, reqDto dto.QueryRoomStageReqDto) (map[string]interface{}, error)
	V3GetRoomStage(ctx context.Context, reqDto dto.V3QueryRoomStageReqDto) ([]map[string]interface{}, error)
	V3GetRoomInfoByQRCode(ctx context.Context, reqDto dto.V3QueryRoomInfoByQRCodeReqDto) (vo.SessionVO, error)
	V3QueryRoomFault(ctx context.Context, reqDto req.V3QueryRoomFaultReqDto) (vo.RoomFaultVO, error)
}

type roomApplicationService struct {
	processEngine               model.Engine
	roomService                 roomService.Service
	areaService                 areaService.Service
	roomTypeService             roomTypeService.Service
	pricePlanService            pricePlanService.Service
	holidayService              holidayService.Service
	ruleService                 ruleService.Service
	validateService             validateService.ValidateService
	roomThemeService            roomThemeService.RoomThemeService
	sessionService              sessionService.SessionService
	bookingService              bookingService.BookingService
	orderPricePlanService       orderService.OrderPricePlanService
	pricePlanDomainService      domainService.PricePlanDomainService
	roomTypeTimePriceRepository repository.RoomTypeTimePriceRepository
	orderRoomPlanService        orderRoomPlanService.Service
	orderService                orderService.OrderService
	roomOperationService        roomOperationService.RoomOperationService
	venueService                venueService.VenueService
	orderProductService         orderProductService.Service
	employeeService             employeeService.Service
	roomFaultService            roomFaultService.RoomFaultService
}

// NewRoomApplicationService 创建房间应用服务
func NewRoomApplicationService(
	roomService roomService.Service,
	roomTypeService roomTypeService.Service,
	areaService areaService.Service,
	pricePlanService pricePlanService.Service,
	holidayService holidayService.Service,
	ruleService ruleService.Service,
	validateService validateService.ValidateService,
	roomThemeService roomThemeService.RoomThemeService,
	sessionService sessionService.SessionService,
	bookingService bookingService.BookingService,
	orderPricePlanService orderService.OrderPricePlanService,
	orderRoomPlanService orderRoomPlanService.Service,
	orderService orderService.OrderService,
	roomOperationService roomOperationService.RoomOperationService,
	venueService venueService.VenueService,
	orderProductService orderProductService.Service,
	employeeService employeeService.Service,
	roomFaultService roomFaultService.RoomFaultService,
) RoomApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("roomService", roomService)
	engine.RegisterService("roomTypeService", roomTypeService)
	engine.RegisterService("areaService", areaService)
	engine.RegisterService("pricePlanService", pricePlanService)
	engine.RegisterService("holidayService", holidayService)
	engine.RegisterService("ruleService", ruleService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("roomThemeService", roomThemeService)
	engine.RegisterService("sessionService", sessionService)
	engine.RegisterService("bookingService", bookingService)
	engine.RegisterService("orderPricePlanService", orderPricePlanService)
	engine.RegisterService("orderRoomPlanService", orderRoomPlanService)
	engine.RegisterService("orderService", orderService)
	engine.RegisterService("roomOperationService", roomOperationService)
	engine.RegisterService("venueService", venueService)
	engine.RegisterService("orderProductService", orderProductService)
	engine.RegisterService("employeeService", employeeService)
	engine.RegisterService("roomFaultService", roomFaultService)
	// 加载流程定义
	processContent, err := ioutil.ReadFile("erp_client/config/processes/room_open_view_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(processContent); err != nil {
		panic(fmt.Errorf("解析流程定义失败: %w", err))
	}

	// 创建领域服务
	pricePlanDomainService := domainService.NewPricePlanDomainService()

	// 创建房间类型基准钟点价仓储
	roomTypeTimePriceRepository := roomprice.NewRoomTypeTimePriceRepositoryImpl()

	return &roomApplicationService{
		processEngine:               engine,
		roomService:                 roomService,
		roomTypeService:             roomTypeService,
		areaService:                 areaService,
		pricePlanService:            pricePlanService,
		holidayService:              holidayService,
		ruleService:                 ruleService,
		validateService:             validateService,
		roomThemeService:            roomThemeService,
		sessionService:              sessionService,
		bookingService:              bookingService,
		orderPricePlanService:       orderPricePlanService,
		pricePlanDomainService:      pricePlanDomainService,
		roomTypeTimePriceRepository: roomTypeTimePriceRepository,
		orderRoomPlanService:        orderRoomPlanService,
		orderService:                orderService,
		roomOperationService:        roomOperationService,
		venueService:                venueService,
		orderProductService:         orderProductService,
		employeeService:             employeeService,
		roomFaultService:            roomFaultService,
	}
}

// GetRoomOpenView 获取房间开放视图
func (s *roomApplicationService) GetRoomOpenView(ctx context.Context, reqDto dto.QueryRoomStageReqDto) (map[string]interface{}, error) {
	// 准备参数
	params := map[string]interface{}{
		"reqDto": reqDto,
	}

	// 执行流程
	result, err := s.processEngine.Execute(ctx, "room_open_view", params)
	if err != nil {
		return nil, fmt.Errorf("执行流程失败: %w", err)
	}

	// 直接返回 roomVO 对应的 map
	if roomVO, exists := result["roomVO"]; exists {
		return roomVO.(map[string]interface{}), nil
	}

	return nil, fmt.Errorf("获取房间视图对象失败")
}

// V3GetRoomOpenView 获取房间开放视图
func (s *roomApplicationService) V3GetRoomOpenView(ctx context.Context, reqDto dto.QueryRoomStageReqDto) (map[string]interface{}, error) {
	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		return nil, errors.New("venueId为空")
	}
	typeId := reqDto.TypeId
	areaId := reqDto.AreaId
	if typeId == nil || *typeId == "" {
		return nil, errors.New("类型不能为空")
	}

	roomVO := vo.RoomVO{}
	// 1. 查询区域信息
	area, err := s.areaService.GetArea(ctx, *areaId)
	if err == nil {
		areaVO := s.areaService.ConvertToAreaVO(ctx, &area)
		roomVO.AreaVO = &areaVO
	}

	// 2. 查询房间类型信息
	roomType, err := s.roomTypeService.FindByID(ctx, *typeId)
	if err != nil {
		return nil, fmt.Errorf("查询房间类型信息失败: %w", err)
	} else {
		roomTypeVO := s.roomTypeService.ConvertToRoomTypeVO(ctx, roomType)
		roomVO.RoomTypeVO = &roomTypeVO
	}

	// 3. 查询基准钟点价
	// 转换为gin.Context
	ginCtx, ok := ctx.(*gin.Context)
	if !ok {
		return nil, errors.New("无法转换为gin.Context")
	}

	roomTypeTimePrice, err := s.roomTypeTimePriceRepository.FindByRoomTypeID(ginCtx, *typeId)
	if err != nil {
		util.Wlog(ginCtx).Error(fmt.Sprintf("查询基准钟点价失败: %s", err.Error()))
	} else if roomTypeTimePrice != nil && roomTypeTimePrice.BaseTimePrice != nil {
		// 设置基准钟点价
		roomVO.BaseTimePriceFee = roomTypeTimePrice.BaseTimePrice.Amount()
	}

	// 4. 查询买断价格计划
	buyoutPricePlans, err := s.pricePlanDomainService.GetEnabledBuyoutPricePlansByVenueID(ginCtx, *venueId)
	if err != nil {
		util.Wlog(ginCtx).Error(fmt.Sprintf("查询买断价格计划失败: %s", err.Error()))
	} else {
		// 过滤不是本房间类型的买断价格计划
		filteredBuyoutPricePlanVOs := []vo.BuyoutPricePlanVO{}

		// 收集所有productIds和packageIds
		allBuyoutProductIds := []string{}
		allBuyoutPackageIds := []string{}

		// 创建转换器
		buyoutPricePlanTransfer := appService.NewBuyoutPricePlanTransfer()

		// 第一遍循环：转换数据并收集所有ID
		for _, plan := range buyoutPricePlans {
			// 转换为VO
			planVO := buyoutPricePlanTransfer.ConvertToBuyoutPricePlanVO(plan)

			// 提取商品和套餐信息
			productIds, packageIds := buyoutPricePlanTransfer.ExtractProductAndPackageIds(planVO.PlanProducts)

			// 收集所有ID
			allBuyoutProductIds = append(allBuyoutProductIds, productIds...)
			allBuyoutPackageIds = append(allBuyoutPackageIds, packageIds...)

			// 检查房间类型配置是否包含当前房间类型
			if planVO.RoomTypeConfig != nil {
				// 解析房间类型配置
				for _, roomType := range planVO.RoomTypeConfig.RoomTypes {
					if roomType.ID == *typeId {
						filteredBuyoutPricePlanVOs = append(filteredBuyoutPricePlanVOs, planVO)
						break
					}
				}
			}
		}

		// 查询计时价格计划
		timePricePlans, err := s.pricePlanDomainService.GetEnabledTimePricePlansByVenueID(ginCtx, *venueId)
		if err != nil {
			util.Wlog(ginCtx).Error(fmt.Sprintf("查询计时价格计划失败: %s", err.Error()))
		} else {
			// 过滤不是本房间类型的计时价格计划
			filteredTimePricePlanVOs := []vo.TimePricePlanVO{}

			// 收集所有productIds和packageIds
			allTimeProductIds := []string{}
			allTimePackageIds := []string{}

			// 创建转换器
			timePricePlanTransfer := appService.NewTimePricePlanTransfer()

			// 第一遍循环：转换数据并收集所有ID
			for _, plan := range timePricePlans {
				// 转换为VO
				planVO := timePricePlanTransfer.ConvertToTimePricePlanVO(plan)

				// 提取商品和套餐信息
				productIds, packageIds := timePricePlanTransfer.ExtractProductAndPackageIds(planVO.PlanProducts)

				// 收集所有ID
				allTimeProductIds = append(allTimeProductIds, productIds...)
				allTimePackageIds = append(allTimePackageIds, packageIds...)

				// 检查房间类型配置是否包含当前房间类型
				if planVO.RoomTypeConfig != nil {
					// 解析房间类型配置
					for _, roomType := range planVO.RoomTypeConfig.RoomTypes {
						if roomType.ID == *typeId {
							filteredTimePricePlanVOs = append(filteredTimePricePlanVOs, planVO)
							break
						}
					}
				}
			}

			// 合并买断和买钟的所有ID
			allProductIds := append(allBuyoutProductIds, allTimeProductIds...)
			allPackageIds := append(allBuyoutPackageIds, allTimePackageIds...)

			// 去重productIds
			productIdMap := make(map[string]bool)
			uniqueProductIds := make([]string, 0)
			for _, id := range allProductIds {
				if !productIdMap[id] && id != "" {
					productIdMap[id] = true
					uniqueProductIds = append(uniqueProductIds, id)
				}
			}

			// 去重packageIds
			packageIdMap := make(map[string]bool)
			uniquePackageIds := make([]string, 0)
			for _, id := range allPackageIds {
				if !packageIdMap[id] && id != "" {
					packageIdMap[id] = true
					uniquePackageIds = append(uniquePackageIds, id)
				}
			}

			// 批量查询所有商品和套餐信息
			var productVOs []vo.ProductVO
			var packageVOs []vo.ProductPackageVO

			// 一次性查询所有商品
			if len(uniqueProductIds) > 0 {
				productVOs, err = s.pricePlanDomainService.FindProductsByIds(ginCtx, uniqueProductIds)
				if err != nil {
					util.Wlog(ginCtx).Error(fmt.Sprintf("批量查询商品失败: %s", err.Error()))
				}
			}

			// 一次性查询所有套餐
			if len(uniquePackageIds) > 0 {
				packageVOs, err = s.pricePlanDomainService.FindProductPackagesByIds(ginCtx, uniquePackageIds)
				if err != nil {
					util.Wlog(ginCtx).Error(fmt.Sprintf("批量查询套餐失败: %s", err.Error()))
				}
			}

			// 创建ID到VO的映射，方便快速查找
			productVOMap := make(map[string]vo.ProductVO)
			for _, productVO := range productVOs {
				productVOMap[productVO.Id] = productVO
			}

			packageVOMap := make(map[string]vo.ProductPackageVO)
			for _, packageVO := range packageVOs {
				packageVOMap[packageVO.Id] = packageVO
			}

			// 第二遍循环：将查询到的商品和套餐信息填充到买断价格计划
			for i := range filteredBuyoutPricePlanVOs {
				planVO := &filteredBuyoutPricePlanVOs[i]

				// 获取当前方案的商品和套餐ID
				planProductIds, planPackageIds := buyoutPricePlanTransfer.ExtractProductAndPackageIds(planVO.PlanProducts)

				// 填充商品信息
				if len(planProductIds) > 0 {
					planProductVOs := []vo.ProductVO{}
					for _, productId := range planProductIds {
						if productVO, exists := productVOMap[productId]; exists {
							planProductVOs = append(planProductVOs, productVO)
						}
					}
					if len(planProductVOs) > 0 {
						planVO.PricePlanSubProductVO = planProductVOs
					}
				}

				// 填充套餐信息
				if len(planPackageIds) > 0 {
					planPackageVOs := []vo.ProductPackageVO{}
					for _, packageId := range planPackageIds {
						if packageVO, exists := packageVOMap[packageId]; exists {
							planPackageVOs = append(planPackageVOs, packageVO)
						}
					}
					if len(planPackageVOs) > 0 {
						planVO.ProductPackageVOs = planPackageVOs
					}
				}
			}

			// 第二遍循环：将查询到的商品和套餐信息填充到买钟价格计划
			for i := range filteredTimePricePlanVOs {
				planVO := &filteredTimePricePlanVOs[i]

				// 获取当前方案的商品和套餐ID
				planProductIds, planPackageIds := timePricePlanTransfer.ExtractProductAndPackageIds(planVO.PlanProducts)

				// 填充商品信息
				if len(planProductIds) > 0 {
					planProductVOs := []vo.ProductVO{}
					for _, productId := range planProductIds {
						if productVO, exists := productVOMap[productId]; exists {
							planProductVOs = append(planProductVOs, productVO)
						}
					}
					if len(planProductVOs) > 0 {
						planVO.PricePlanSubProductVO = planProductVOs
					}
				}

				// 填充套餐信息
				if len(planPackageIds) > 0 {
					planPackageVOs := []vo.ProductPackageVO{}
					for _, packageId := range planPackageIds {
						if packageVO, exists := packageVOMap[packageId]; exists {
							planPackageVOs = append(planPackageVOs, packageVO)
						}
					}
					if len(planPackageVOs) > 0 {
						planVO.ProductPackageVOs = planPackageVOs
					}
				}
			}

			if len(filteredBuyoutPricePlanVOs) > 0 {
				roomVO.BuyoutPricePlanVOs = &filteredBuyoutPricePlanVOs
			}

			if len(filteredTimePricePlanVOs) > 0 {
				roomVO.TimePricePlanVOs = &filteredTimePricePlanVOs
			}
		}
	}
	// 查询book信息
	if reqDto.BookingId != nil && *reqDto.BookingId != "" {
		booking, err := s.bookingService.FindBookingById(ctx, *reqDto.BookingId)
		if err != nil {
			util.Wlog(ginCtx).Error(fmt.Sprintf("查询预约信息失败: %s", err.Error()))
		} else {
			bookingVO := s.bookingService.ConvertToBookingVO(ctx, *booking)
			roomVO.BookingVOs = &[]vo.BookingVO{bookingVO}
		}
	}
	roomVO.CurrentTime = int64(util.TimeNowUnix())

	return util.StructToMap(roomVO), nil
}

// V3GetRoomStage 获取房间阶段
func (s *roomApplicationService) V3GetRoomStage(ctx context.Context, reqDto dto.V3QueryRoomStageReqDto) ([]map[string]interface{}, error) {
	// 准备参数
	// params := map[string]interface{}{
	// 	"reqDto": reqDto,
	// }

	venueId := reqDto.VenueId
	if venueId == nil || *venueId == "" {
		return nil, errors.New("venueId为空")
	}
	// 查询所有房间
	rooms, err := s.roomService.FindRoomsByVenueId(ctx, *venueId, false, true)
	if err != nil || len(rooms) <= 0 { // 没有房间,直接返回
		return nil, errors.New("没有房间")
	}
	roomThemeIds := []string{}
	areaIds := []string{}
	roomTypeIds := []string{}
	sessionIds := []string{}
	roomIds := []string{}
	for _, room := range rooms {
		if room.ThemeId != nil && *room.ThemeId != "" {
			util.AddListElement(&roomThemeIds, *room.ThemeId)
		}
		if room.AreaId != nil && *room.AreaId != "" {
			util.AddListElement(&areaIds, *room.AreaId)
		}
		if room.TypeId != nil && *room.TypeId != "" {
			util.AddListElement(&roomTypeIds, *room.TypeId)
		}
		if room.SessionId != nil && *room.SessionId != "" {
			util.AddListElement(&sessionIds, *room.SessionId)
		}
		util.AddListElement(&roomIds, *room.Id)
	}
	roomThemeAll, _ := s.roomThemeService.FindRoomThemesByRoomIds(ctx, *venueId, roomThemeIds)
	areaAll, _ := s.areaService.FindAreasByAreaIds(ctx, *venueId, areaIds)
	roomTypeAll, _ := s.roomTypeService.FindRoomTypesByRoomTypeIds(ctx, *venueId, roomTypeIds)
	sessionAll, _ := s.sessionService.FindSessionsBySessionIds(ctx, *venueId, sessionIds)
	orderRoomPlanAll, _ := s.orderRoomPlanService.GetOrderRoomPlansBySessionIds(ctx, *venueId, sessionIds)
	giftOrderRoomPlanAll, _ := s.orderRoomPlanService.FindOrderRoomPlansGiftBySessionIds(ctx, *venueId, sessionIds)
	giftOrderProductAll, _ := s.orderProductService.FindOrderProductsGiftBySessionIds(ctx, *venueId, sessionIds)
	themeIdToRoomThemeMap := map[string]vo.RoomThemeVO{}
	for _, roomTheme := range roomThemeAll {
		themeIdToRoomThemeMap[*roomTheme.Id] = s.roomThemeService.ConvertToRoomThemeVO(ctx, roomTheme)
	}
	areaIdToAreaMap := map[string]vo.AreaVO{}
	for _, area := range areaAll {
		areaIdToAreaMap[*area.Id] = s.areaService.ConvertToAreaVO(ctx, &area)
	}
	roomTypeIdToRoomTypeMap := map[string]vo.RoomTypeVO{}
	for _, roomType := range roomTypeAll {
		roomTypeIdToRoomTypeMap[*roomType.Id] = s.roomTypeService.ConvertToRoomTypeVO(ctx, &roomType)
	}
	sessionIdToSessionMap := map[string]vo.SessionVO{}
	for _, session := range sessionAll {
		sessionIdToSessionMap[*session.SessionId] = s.sessionService.ConvertToSessionVO(ctx, session)
	}
	sessionIdToOrderRoomPlanMap := map[string][]vo.OrderRoomPlanVO{}
	for _, orderRoomPlan := range orderRoomPlanAll {
		sessionIdToOrderRoomPlanMap[*orderRoomPlan.SessionId] = append(sessionIdToOrderRoomPlanMap[*orderRoomPlan.SessionId], s.orderRoomPlanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	sessionIdToGiftOrderProductMap := map[string][]po.OrderProduct{}
	for _, giftOrderProduct := range giftOrderProductAll {
		sessionIdToGiftOrderProductMap[*giftOrderProduct.SessionId] = append(sessionIdToGiftOrderProductMap[*giftOrderProduct.SessionId], giftOrderProduct)
	}
	sessionIdToGiftOrderRoomPlanMap := map[string][]po.OrderRoomPlan{}
	for _, giftOrderRoomPlan := range giftOrderRoomPlanAll {
		sessionIdToGiftOrderRoomPlanMap[*giftOrderRoomPlan.SessionId] = append(sessionIdToGiftOrderRoomPlanMap[*giftOrderRoomPlan.SessionId], giftOrderRoomPlan)
	}
	// roomOperationAll, _ := s.orderService.FindLastRoomOperation(ctx, *venueId, roomIds)
	nowTime := util.TimeNowUnix()
	timeStart := int64(nowTime - 12*60*60)
	bookingAllQuery, _ := s.bookingService.FindBookingsByVenueIdAndTimeRange(ctx, *venueId, timeStart, 0, "arrival_time asc")
	bookingAll := &[]po.Booking{}
	for _, booking := range *bookingAllQuery {
		if util.InList(*booking.RoomId, roomIds) {
			*bookingAll = append(*bookingAll, booking)
		}
	}
	roomIdToBookingMap := map[string][]vo.BookingVO{}
	for _, booking := range *bookingAll {
		roomIdToBookingMap[*booking.RoomId] = append(roomIdToBookingMap[*booking.RoomId], s.bookingService.ConvertToBookingVO(ctx, booking))
	}
	roomGroup := map[string][]string{}
	for _, room := range rooms {
		if room.SessionId != nil && *room.SessionId != "" {
			roomGroup[*room.SessionId] = append(roomGroup[*room.SessionId], *room.Id)
		}
	}

	roomStageVOs := []vo.RoomStageVO{}
	for _, room := range rooms {
		roomVO := s.roomService.ConvertToRoomVO(ctx, room)
		roomStageVO := vo.RoomStageVO{}
		// 1. 查询主题信息
		if room.ThemeId != nil && *room.ThemeId != "" {
			roomThemeTmp, ok := themeIdToRoomThemeMap[*room.ThemeId]
			if ok {
				if !roomThemeTmp.IsDisplayed {
					continue
				}
				roomStageVO.RoomThemeVO = roomThemeTmp
			}
		}
		// 2. 查询区域信息
		if room.AreaId != nil && *room.AreaId != "" {
			areaTmp, ok := areaIdToAreaMap[*room.AreaId]
			if ok {
				if !areaTmp.IsDisplayed {
					continue
				}
				roomStageVO.AreaVO = areaTmp
			}
		}
		// 3. 查询房间类型信息
		if room.TypeId != nil && *room.TypeId != "" {
			roomTypeTmp, ok := roomTypeIdToRoomTypeMap[*room.TypeId]
			if ok {
				if !roomTypeTmp.IsDisplayed {
					continue
				}
				roomStageVO.RoomTypeVO = roomTypeTmp
			}
		}
		// 4. 查询价格计划信息
		orderRoomPlanVOs := make([]vo.OrderRoomPlanVO, 0)
		if room.SessionId != nil && *room.SessionId != "" {
			orderRoomPlanVosTmp, ok := sessionIdToOrderRoomPlanMap[*room.SessionId]
			if ok {
				orderRoomPlanVOs = orderRoomPlanVosTmp
			}
		}
		roomStageVO.OrderRoomPlanVOs = orderRoomPlanVOs

		// 5. 查询session信息
		if room.SessionId != nil && *room.SessionId != "" {
			sessionTmp, ok := sessionIdToSessionMap[*room.SessionId]
			if ok {
				roomStageVO.SessionVO = sessionTmp
			}
			if roomStageVO.SessionVO.RoomId != "" && *room.Id != roomStageVO.SessionVO.RoomId {
				roomStageVO.UnionRoomId = roomStageVO.SessionVO.RoomId
			} else {
				roomIdsTmp, ok := roomGroup[*room.SessionId]
				if ok && len(roomIdsTmp) > 1 {
					roomStageVO.UnionRoomId = roomStageVO.SessionVO.RoomId
				}
			}
		}
		// 5.1 查询gift订单信息
		if room.SessionId != nil && *room.SessionId != "" {
			giftOrderRoomPlanVOs := sessionIdToGiftOrderRoomPlanMap[*room.SessionId]
			giftOrderProductVOs := sessionIdToGiftOrderProductMap[*room.SessionId]
			totalGiftAmount := int64(0)
			for _, giftOrderProduct := range giftOrderProductVOs {
				totalGiftAmount += *giftOrderProduct.OriginalPrice * *giftOrderProduct.Quantity
			}
			for _, giftOrderRoomPlan := range giftOrderRoomPlanVOs {
				totalGiftAmount += *giftOrderRoomPlan.OriginalPayAmount
			}
			roomStageVO.SessionVO.GiftAmount = totalGiftAmount
		}
		// 6. 查询预定信息
		bookingVOs, ok := roomIdToBookingMap[*room.Id]
		if ok {
			roomStageVO.BookingVOs = bookingVOs
		}
		if roomStageVO.UnionRoomId != "" {
			for _, room := range rooms {
				if roomStageVO.UnionRoomId == *room.Id {
					roomStageVO.UnionRoomName = *room.Name
					break
				}
			}
		}
		roomStageVO.RoomVO = roomVO
		roomStageVOs = append(roomStageVOs, roomStageVO)
	}
	return util.StructsToMaps(roomStageVOs), nil
}

func (s *roomApplicationService) V3GetRoomInfoByQRCode(ctx context.Context, reqDto dto.V3QueryRoomInfoByQRCodeReqDto) (vo.SessionVO, error) {
	roomId := reqDto.RoomId
	if roomId == nil || *roomId == "" {
		return vo.SessionVO{}, errors.New("roomId为空")
	}
	room, err := s.roomService.FindByID(ctx, *roomId)
	if err != nil {
		return vo.SessionVO{}, err
	}
	venue, err := s.venueService.FindByID(ctx, *room.VenueId)
	if err != nil {
		return vo.SessionVO{}, err
	}
	if room.VenueId == nil || *room.VenueId == "" {
		return vo.SessionVO{}, errors.New("venueId为空")
	}
	if room.VenueId != venue.Id {
		return vo.SessionVO{}, errors.New("房间不属于当前门店")
	}
	sessions, err := s.sessionService.FindSessionsBySessionIds(ctx, *room.VenueId, []string{*room.SessionId})
	if err != nil {
		return vo.SessionVO{}, err
	}
	if len(sessions) <= 0 {
		return vo.SessionVO{}, errors.New("sessionId为空")
	}
	session := sessions[0]
	sessionVO := s.sessionService.ConvertToSessionVO(ctx, session)
	return sessionVO, nil
}

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *roomApplicationService) utilValidateBaseParams(ctx context.Context, venueId, roomId, employeeId *string, employeeIdRequired bool) (po.Venue, po.Room, po.Employee, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("VenueId不能为空")
	}
	if roomId == nil || *roomId == "" {
		return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("RoomId不能为空")
	}
	if employeeId == nil || *employeeId == "" {
		return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("EmployeeId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("门店不存在")
	}
	// 0.2. 检查房间是否存在
	room, err := s.roomService.GetRoom(ctx, *roomId)
	if err != nil {
		return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("房间不存在")
	}
	if !(room.VenueId != nil && *room.VenueId == *venueId) {
		return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("房间不属于该门店")
	}
	// 0.3. 检查员工是否存在
	employee := po.Employee{}
	if employeeIdRequired {
		employee, err = s.employeeService.FindEmployeeByID(ctx, *employeeId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Employee{}, fmt.Errorf("员工不存在")
		}
	}
	return *venue, room, employee, nil
}

func (s *roomApplicationService) V3QueryRoomFault(ctx context.Context, reqDto req.V3QueryRoomFaultReqDto) (vo.RoomFaultVO, error) {
	_, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, reqDto.RoomId, reqDto.EmployeeId, true)
	if err != nil {
		return vo.RoomFaultVO{}, err
	}
	roomFaults, err := s.roomFaultService.FindRoomFaultsByRoomId(ctx, *reqDto.VenueId, *reqDto.RoomId)
	if err != nil {
		return vo.RoomFaultVO{}, err
	}
	if len(roomFaults) <= 0 {
		return vo.RoomFaultVO{}, nil
	}
	roomFaultVO := s.roomFaultService.ConvertToRoomFaultVO(ctx, roomFaults[0])
	return roomFaultVO, nil
}
