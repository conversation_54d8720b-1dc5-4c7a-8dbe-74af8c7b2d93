package service

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
)

// MemberRechageApplicationService 会员充值应用服务接口
type MemberRechageApplicationService interface {
	// V3MemberRechage 会员充值
	V3MemberRechage(ctx context.Context, reqDto req.V3QueryMemberRechargeReqDto) ([]vo.MemberRechargeBillExVO, error)

	// V3OpenCard 会员开卡
	V3OpenCard(ctx context.Context, reqDto req.V3OpenCardReqDto) (po.MemberCard, error)

	// V3MemberPayCallback 会员支付回调
	V3MemberPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error

	// V3MemberRefundCallback 会员退款回调
	V3MemberRefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error

	// V3MemberPayQuery 会员支付查询
	V3MemberPayQuery(ctx context.Context, reqDto req.V3QueryMemberPayQueryReqDto) (vo.MemberRechargeBillVO, error)
}
