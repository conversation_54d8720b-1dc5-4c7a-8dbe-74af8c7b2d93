package service

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"slices"
	"time"
	_const "voderpltvv/const"
	validateService "voderpltvv/erp_client/application/framework/validate"
	bookingService "voderpltvv/erp_client/domain/configuration/business/booking/service"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	ruleService "voderpltvv/erp_client/domain/rule/service"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	tradeService "voderpltvv/erp_client/domain/traderecord/service"
	roomplanService "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	orderproductService "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	pricePlanService "voderpltvv/erp_client/domain/valueobject/business/price_plan/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	roomTypeService "voderpltvv/erp_client/domain/valueobject/business/room_type/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	newModel "voderpltvv/model"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// MemberRechageApplicationServiceImpl 会员充值应用服务实现
type MemberRechageApplicationServiceImpl struct {
	processEngine               model.Engine
	orderService                tradeService.OrderService
	bookingService              bookingService.BookingService
	roomService                 roomService.Service
	ruleService                 ruleService.Service
	validateService             validateService.ValidateService
	sessionService              sessionService.SessionService
	payService                  tradeService.PayService
	pricePlanService            pricePlanService.Service
	roomplanService             roomplanService.Service
	orderproductService         orderproductService.Service
	venueService                venueService.VenueService
	employeeService             employeeService.Service
	payBillService              tradeService.PayBillService
	roomTypeService             roomTypeService.Service
	payRecordService            tradeService.PayRecordService
	orderAndPayService          tradeService.OrderAndPayService
	memberRechargeBillService   tradeService.MemberRechargeBillService
	memberRechargeRecordService tradeService.MemberRechargeRecordService
	memberService               tradeService.MemberService
	memberCardService           tradeService.MemberCardService
	memberCardOperationService  tradeService.MemberCardOperationService
}

// NewMemberRechageApplicationService 创建会员充值应用服务实例
func NewMemberRechageApplicationService(
	orderService tradeService.OrderService,
	bookingService bookingService.BookingService,
	roomService roomService.Service,
	ruleService ruleService.Service,
	validateService validateService.ValidateService,
	sessionService sessionService.SessionService,
	payService tradeService.PayService,
	pricePlanService pricePlanService.Service,
	roomplanService roomplanService.Service,
	orderproductService orderproductService.Service,
	venueService venueService.VenueService,
	employeeService employeeService.Service,
	payBillService tradeService.PayBillService,
	roomTypeService roomTypeService.Service,
	payRecordService tradeService.PayRecordService,
	orderAndPayService tradeService.OrderAndPayService,
	memberRechargeBillService tradeService.MemberRechargeBillService,
	memberRechargeRecordService tradeService.MemberRechargeRecordService,
	memberService tradeService.MemberService,
	memberCardService tradeService.MemberCardService,
	memberCardOperationService tradeService.MemberCardOperationService,
) MemberRechageApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("orderService", orderService)
	engine.RegisterService("bookingService", bookingService)
	engine.RegisterService("roomService", roomService)
	engine.RegisterService("ruleService", ruleService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("sessionService", sessionService)
	engine.RegisterService("payService", payService)
	engine.RegisterService("pricePlanService", pricePlanService)
	engine.RegisterService("roomplanService", roomplanService)
	engine.RegisterService("orderproductService", orderproductService)
	engine.RegisterService("venueService", venueService)
	engine.RegisterService("employeeService", employeeService)
	engine.RegisterService("payBillService", payBillService)
	engine.RegisterService("roomTypeService", roomTypeService)
	engine.RegisterService("payRecordService", payRecordService)
	engine.RegisterService("orderAndPayService", orderAndPayService)
	engine.RegisterService("memberRechargeBillService", memberRechargeBillService)
	engine.RegisterService("memberRechargeRecordService", memberRechargeRecordService)
	engine.RegisterService("memberService", memberService)
	engine.RegisterService("memberCardService", memberCardService)
	engine.RegisterService("memberCardOperationService", memberCardOperationService)
	// 加载开台流程定义
	orderOpenProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_open_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载开台流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderOpenProcessContent); err != nil {
		panic(fmt.Errorf("解析开台流程定义失败: %w", err))
	}

	// 加载支付流程定义
	orderPayProcessContent, err := ioutil.ReadFile("erp_client/config/processes/order_pay_process.yaml")
	if err != nil {
		panic(fmt.Errorf("加载支付流程定义失败: %w", err))
	}
	if err := engine.LoadProcess(orderPayProcessContent); err != nil {
		panic(fmt.Errorf("解析支付流程定义失败: %w", err))
	}

	return &MemberRechageApplicationServiceImpl{
		processEngine:               engine,
		orderService:                orderService,
		bookingService:              bookingService,
		roomService:                 roomService,
		ruleService:                 ruleService,
		validateService:             validateService,
		sessionService:              sessionService,
		payService:                  payService,
		pricePlanService:            pricePlanService,
		roomplanService:             roomplanService,
		orderproductService:         orderproductService,
		venueService:                venueService,
		employeeService:             employeeService,
		payBillService:              payBillService,
		roomTypeService:             roomTypeService,
		payRecordService:            payRecordService,
		orderAndPayService:          orderAndPayService,
		memberRechargeBillService:   memberRechargeBillService,
		memberRechargeRecordService: memberRechargeRecordService,
		memberService:               memberService,
		memberCardService:           memberCardService,
		memberCardOperationService:  memberCardOperationService,
	}
}

// validateBaseParams 验证基础参数
// 入参： venueId,roomId,employeeId,sessionId - 基础参数
// 出参： venue,room,session,employee,error - 基础参数和错误信息
func (s *MemberRechageApplicationServiceImpl) utilValidateBaseParams(ctx context.Context, venueId, roomId, sessionId, employeeId *string, roomIdRequired, sessionIdRequired bool) (po.Venue, po.Room, po.Session, po.Employee, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("VenueId不能为空")
	}
	if roomIdRequired && (roomId == nil || *roomId == "") {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("RoomId不能为空")
	}
	if employeeId == nil || *employeeId == "" {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("EmployeeId不能为空")
	}
	// 0.1. 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("门店不存在")
	}
	room := po.Room{}
	// 0.2. 检查房间是否存在
	if roomIdRequired {
		room, err = s.roomService.GetRoom(ctx, *roomId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不存在")
		}
		if !(room.VenueId != nil && *room.VenueId == *venueId) {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不属于该门店")
		}
	}
	// 0.3. 检查员工是否存在
	employee, err := s.employeeService.FindEmployeeByID(ctx, *employeeId)
	if err != nil {
		return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("员工不存在")
	}
	// 0.4. 检查场次是否存在
	session := po.Session{}
	if sessionIdRequired {
		if sessionId == nil || *sessionId == "" {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("SessionId不能为空")
		}
		session, err = s.sessionService.FindBySessionId(ctx, *sessionId, *venueId)
		if err != nil {
			return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("场次信息不存在")
		}
		if roomIdRequired {
			if !(room.SessionId != nil && *room.SessionId == *sessionId) {
				return po.Venue{}, po.Room{}, po.Session{}, po.Employee{}, fmt.Errorf("房间不属于该场次")
			}
		}
	}
	return *venue, room, session, employee, nil
}

// ////////////////////////////////////////////////

// V3OpenCard 会员开卡
func (s *MemberRechageApplicationServiceImpl) V3OpenCard(ctx context.Context, reqDto req.V3OpenCardReqDto) (po.MemberCard, error) {
	// 1. 验证基础参数
	venue, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, nil, reqDto.SellerId, false, false)
	if err != nil {
		return po.MemberCard{}, err
	}

	// 2. 验证会员开卡特有参数
	if reqDto.CardLevel == nil || *reqDto.CardLevel == "" {
		return po.MemberCard{}, fmt.Errorf("会员卡等级不能为空")
	}
	if reqDto.Gender == nil || *reqDto.Gender == "" {
		return po.MemberCard{}, fmt.Errorf("性别不能为空")
	}
	if reqDto.Phone == nil || *reqDto.Phone == "" {
		return po.MemberCard{}, fmt.Errorf("手机号不能为空")
	}
	if reqDto.Birthday == nil {
		// return fmt.Errorf("生日不能为空")
	}
	if reqDto.Name == nil || *reqDto.Name == "" {
		return po.MemberCard{}, fmt.Errorf("姓名不能为空")
	}
	if reqDto.SellerId == nil || *reqDto.SellerId == "" {
		return po.MemberCard{}, fmt.Errorf("销售员ID不能为空")
	}
	if reqDto.SellerName == nil || *reqDto.SellerName == "" {
		return po.MemberCard{}, fmt.Errorf("销售员姓名不能为空")
	}
	if reqDto.CardType == nil || *reqDto.CardType == "" {
		return po.MemberCard{}, fmt.Errorf("会员卡类型不能为空")
	}
	if !slices.Contains(_const.SUPPORT_MEMBER_GENDERS, *reqDto.Gender) {
		return po.MemberCard{}, fmt.Errorf("性别不支持")
	}
	timeNow := int64(util.TimeNowUnix())
	timeEnd := timeNow
	if reqDto.CardEndTime == nil {
		// 计算一个自然年后的时间（而不是简单加365天）
		t := time.Unix(timeNow, 0)
		nextYear := t.AddDate(1, 0, 0)
		timeEnd = nextYear.Unix()
	} else {
		if *reqDto.CardEndTime <= timeNow {
			return po.MemberCard{}, fmt.Errorf("会员卡结束时间不能小于当前时间")
		}
		timeEnd = *reqDto.CardEndTime
	}
	maxMemberIdInDB, err := s.memberCardService.GetMaxMemberCardId(ctx)
	if err != nil {
		return po.MemberCard{}, fmt.Errorf("获取最大会员ID失败")
	}
	newMemberId := maxMemberIdInDB + 1
	newCardNumber := util.GenerateCardNumber(newMemberId)
	if reqDto.CardNumber != nil && *reqDto.CardNumber != "" {
		newCardNumber = *reqDto.CardNumber
		newMemberId = 0
	}
	employee, err := s.employeeService.FindEmployeeByID(ctx, *reqDto.EmployeeId)
	if err != nil {
		return po.MemberCard{}, fmt.Errorf("员工不存在")
	}
	memberCard := po.MemberCard{
		Name:          reqDto.Name,
		Phone:         reqDto.Phone,
		Birthday:      reqDto.Birthday,
		Gender:        reqDto.Gender,
		CardNumber:    &newCardNumber,
		CardNumberId:  &newMemberId,
		CardType:      reqDto.CardType,
		CardLevel:     reqDto.CardLevel,
		CardLevelName: reqDto.CardLevelName,
		OperatorId:    reqDto.EmployeeId,
		OperatorName:  employee.Name,
		SellerId:      reqDto.SellerId,
		SellerName:    reqDto.SellerName,
		Status:        util.Ptr(_const.V2_MEMBER_CARD_STATUS_NORMAL),
		CardStartTime: &timeNow,
		CardEndTime:   &timeEnd,
		IsEnabled:     util.Ptr(true),
	}
	memberCardNew, err := s.memberCardService.SaveMemberCard(ctx, memberCard, reqDto)
	if err != nil {
		return po.MemberCard{}, err
	}

	// 3. 记录会员开卡操作
	memberCardOperation := po.MemberCardOperation{
		VenueId:                 reqDto.VenueId,
		OperationType:           util.Ptr(string(_const.V2_MEMBER_CARD_OPERATION_TYPE_OPEN_CARD)),
		Balance:                 new(int64),
		Info:                    util.Ptr("开卡"),
		TotalFee:                new(int64),
		PrincipalAmount:         new(int64),
		MemberRoomBonusAmount:   new(int64),
		MemberGoodsBonusAmount:  new(int64),
		MemberCommonBonusAmount: new(int64),
		OperatorId:              reqDto.EmployeeId,
		OperatorName:            employee.Name,
		SellerId:                reqDto.SellerId,
		SellerName:              reqDto.SellerName,
	}
	s.memberCardOperationService.RecordMemberCardOperation(ctx, memberCardOperation, memberCardNew)

	// 发送短信
	s.orderService.SendSmsMemberCardOpen(ctx, &venue, &memberCardNew)

	return memberCardNew, nil
}

// V3MemberRechage 会员充值
func (s *MemberRechageApplicationServiceImpl) V3MemberRechage(ctx context.Context, reqDto req.V3QueryMemberRechargeReqDto) ([]vo.MemberRechargeBillExVO, error) {
	// 1. 验证基础参数
	venue, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, nil, reqDto.EmployeeId, false, false)
	if err != nil {
		return nil, err
	}

	// 2. 验证会员充值特有参数
	if reqDto.MemberCardId == nil || *reqDto.MemberCardId == "" {
		return nil, fmt.Errorf("会员卡ID不能为空")
	}
	if reqDto.TotalFee == nil {
		return nil, fmt.Errorf("充值金额不能为空")
	}
	if *reqDto.TotalFee <= 0 {
		return nil, fmt.Errorf("充值金额必须大于0")
	}
	if reqDto.RoomBonusAmount == nil {
		reqDto.RoomBonusAmount = new(int64)
	}
	if *reqDto.RoomBonusAmount < 0 {
		return nil, fmt.Errorf("用于房费的赠金不能小于0")
	}
	if reqDto.GoodsBonusAmount == nil {
		reqDto.GoodsBonusAmount = new(int64)
	}
	if *reqDto.GoodsBonusAmount < 0 {
		return nil, fmt.Errorf("用于商品的赠金不能小于0")
	}
	if reqDto.CommonBonusAmount == nil {
		reqDto.CommonBonusAmount = new(int64)
	}
	if *reqDto.CommonBonusAmount < 0 {
		return nil, fmt.Errorf("通用赠金不能小于0")
	}
	memberCard, err := s.memberCardService.FindById(ctx, *reqDto.MemberCardId)
	if err != nil {
		return nil, err
	}
	statusErr := util.ValidateMemberCardStatus(*memberCard.Status)
	if statusErr != nil {
		return nil, statusErr
	}
	// 3. 验证会员充值记录
	if len(reqDto.MemberRechargeRecordVOs) <= 0 {
		return nil, fmt.Errorf("会员充值记录不能为空")
	}

	totalPayAmount := int64(0)
	for i, record := range reqDto.MemberRechargeRecordVOs {
		if record.PayType == "" {
			return nil, fmt.Errorf("第%d条充值记录的支付类型不能为空", i+1)
		}
		if !slices.Contains(_const.PAY_TYPE_SUPPORTS_RECHARGE, record.PayType) {
			// return nil, fmt.Errorf("第%d条充值记录的支付类型不支持", i+1)
		}
		if record.TotalFee <= 0 {
			return nil, fmt.Errorf("第%d条充值记录的支付金额必须大于0", i+1)
		}
		totalPayAmount += record.TotalFee
	}

	// 4. 验证充值总金额与支付记录总金额是否一致
	if totalPayAmount != *reqDto.TotalFee {
		return nil, fmt.Errorf("充值总金额(%d)与支付记录总金额(%d)不一致", *reqDto.TotalFee, totalPayAmount)
	}

	// 实现会员充值业务逻辑
	memberRechargeContext, err := s.memberRechargeBillService.V3MemberRechage(ctx, reqDto)
	if err != nil {
		return nil, err
	}
	logrus.Printf("memberRechargeContext: %+v", memberRechargeContext)
	voTmp := s.memberRechargeBillService.ConvertToMemberRechargeBillVO(ctx, memberRechargeContext.AddRechargeBills[0])
	rts := []vo.MemberRechargeBillExVO{
		{
			MemberRechargeBillVO: voTmp,
		},
	}
	memberCardNew, _ := s.memberCardService.FindById(ctx, *reqDto.MemberCardId)
	totalFeeTmp := memberRechargeContext.TotalFee + memberRechargeContext.RoomBonusAmount + memberRechargeContext.GoodsBonusAmount + memberRechargeContext.CommonBonusAmount
	rechargeBill := memberRechargeContext.AddRechargeBills[0]
	employee, err := s.employeeService.FindEmployeeByID(ctx, *reqDto.EmployeeId)
	if err != nil {
		return nil, fmt.Errorf("员工不存在")
	}
	memberCardOperation := po.MemberCardOperation{
		VenueId:                 reqDto.VenueId,
		OperationType:           util.Ptr(string(_const.V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE)),
		Balance:                 new(int64),
		Info:                    util.Ptr("充值"),
		TotalFee:                &totalFeeTmp,
		BillId:                  rechargeBill.BillId,
		BillPid:                 rechargeBill.BillPid,
		PayTime:                 rechargeBill.FinishTime,
		PrincipalAmount:         &memberRechargeContext.TotalFee,
		MemberRoomBonusAmount:   &memberRechargeContext.RoomBonusAmount,
		MemberGoodsBonusAmount:  &memberRechargeContext.GoodsBonusAmount,
		MemberCommonBonusAmount: &memberRechargeContext.CommonBonusAmount,
		OperatorId:              reqDto.EmployeeId,
		OperatorName:            employee.Name,
	}
	s.memberCardOperationService.RecordMemberCardOperation(ctx, memberCardOperation, memberCardNew)

	// 发送短信
	totalAmount := *reqDto.TotalFee + *reqDto.RoomBonusAmount + *reqDto.GoodsBonusAmount + *reqDto.CommonBonusAmount
	s.orderService.SendSmsMemberCardRecharge(ctx, &venue, &memberCardNew, totalAmount, *reqDto.TotalFee)
	return rts, nil
}

// V3MemberPayCallback 会员支付回调
func (s *MemberRechageApplicationServiceImpl) V3MemberPayCallback(ctx context.Context, reqDto newModel.LeshuaPayCallbackModel) error {
	// 当前仅支付成功的订单才会触发支付交易结果异步通知。
	// 支付订单状态	是否触发异步通知
	// 支付成功	（status=2）	是
	// 订单关闭	（status=6）	否
	// 支付失败	（status=8）	否
	// 回调只存在 status=2
	logCtx := ctx.(*gin.Context)
	logTitle := "乐刷支付回调: V3MemberPayCallback: "
	util.Wlog(logCtx).Infof("%s %s: %s --->>> %s", logTitle, reqDto.Third_order_id, reqDto.Status, util.GetPrettyJson(reqDto))

	// 1. 验证回调状态是否非法
	if reqDto.Error_code != "0" {
		util.Wlog(logCtx).Errorf("%s 1.1 %s: 未知的支付回调err_code", logTitle, reqDto.Third_order_id)
		return errors.New("未知的支付回调err_code")
	}
	if reqDto.Status != "2" {
		util.Wlog(logCtx).Errorf("%s 1.2 %s: 未知的支付回调status", logTitle, reqDto.Third_order_id)
		return errors.New("未知的支付回调status")
	}

	payId := reqDto.Third_order_id

	util.Wlog(logCtx).Infof("%s 2.1 %s: 查询支付记录", logTitle, reqDto.Third_order_id)
	rechargeRecord, err := s.memberRechargeRecordService.FindByPayId(ctx, payId)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 2.1.1 %s: 查询支付记录失败", logTitle, reqDto.Third_order_id)
		return err
	}
	util.Wlog(logCtx).Infof("%s 2.1.2 %s: 查询支付记录成功", logTitle, reqDto.Third_order_id)
	// 3. 拦截重复通知
	if rechargeRecord.Status != nil && *rechargeRecord.Status == _const.V2_MEMBER_RECHARGE_RECORD_STATUS_SUCCESS {
		util.Wlog(logCtx).Infof("%s 2.2.1 %s: 已支付成功，return", logTitle, reqDto.Third_order_id)
		return nil
	}

	err = s.memberRechargeRecordService.SaveMemberRechargeRecordPayInfoCallbackByPayId(ctx, vo.MemberPayCallbackVO{PayId: *rechargeRecord.PayId, Type: _const.V2_PAY_CALLBACK_TYPE_PAY, PayCallbackModel: &reqDto})
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 2.3.1 %s: 保存支付记录失败 %s", logTitle, reqDto.Third_order_id, err)
		return err
	}
	util.Wlog(logCtx).Infof("%s 2.3.2 %s: 回调支付记录", logTitle, reqDto.Third_order_id)
	err = s.memberRechargeRecordService.V3CallPayCallback(ctx, reqDto)
	if err != nil {
		util.Wlog(logCtx).Errorf("%s 2.3.3 %s: 回调失败 %s", logTitle, reqDto.Third_order_id, err)
		// return err
	}
	util.Wlog(logCtx).Infof("%s 2.3.4 %s: 更新充值记录", logTitle, reqDto.Third_order_id)
	// 更新充值记录
	if rechargeRecord.MemberCardId != nil && *rechargeRecord.MemberCardId != "" && rechargeRecord.PayId != nil && *rechargeRecord.PayId != "" {
		util.Wlog(logCtx).Infof("%s 2.3.5 %s: 更新充值记录", logTitle, reqDto.Third_order_id)
		s.memberCardOperationService.UpdateRechargeBanlance(ctx, *rechargeRecord.MemberCardId, *rechargeRecord.PayId)
		util.Wlog(logCtx).Infof("%s 2.3.6 %s: 更新充值记录成功", logTitle, reqDto.Third_order_id)
	}
	return nil
}

// V3MemberRefundCallback 会员退款回调
func (s *MemberRechageApplicationServiceImpl) V3MemberRefundCallback(ctx context.Context, reqDto newModel.LeshuaRefundCallbackModel) error {
	return s.memberRechargeBillService.V3MemberRefundCallback(ctx, reqDto)
}

// V3PayQuery 会员支付查询
func (s *MemberRechageApplicationServiceImpl) V3MemberPayQuery(ctx context.Context, reqDto req.V3QueryMemberPayQueryReqDto) (vo.MemberRechargeBillVO, error) {
	// 验证基础参数
	_, _, _, _, err := s.utilValidateBaseParams(ctx, reqDto.VenueId, nil, nil, reqDto.EmployeeId, false, false)
	if err != nil {
		return vo.MemberRechargeBillVO{}, err
	}

	// 验证支付单号
	if reqDto.BillId == nil || *reqDto.BillId == "" {
		return vo.MemberRechargeBillVO{}, errors.New("支付单号不能为空")
	}

	// 查询会员充值记录
	rechargeBill, err := s.memberRechargeBillService.FindByBillId(ctx, *reqDto.VenueId, *reqDto.BillId)
	if err != nil {
		return vo.MemberRechargeBillVO{}, err
	}

	voRet := s.memberRechargeBillService.ConvertToMemberRechargeBillVO(ctx, rechargeBill)

	return voRet, nil
}
