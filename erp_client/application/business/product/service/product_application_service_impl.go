package service

import (
	"context"
	"fmt"
	validateService "voderpltvv/erp_client/application/framework/validate"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	productService "voderpltvv/erp_client/domain/valueobject/business/product/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductApplicationServiceImpl 产品应用服务实现
type ProductApplicationServiceImpl struct {
	processEngine   model.Engine
	productService  productService.ProductService
	validateService validateService.ValidateService
	venueService    venueService.VenueService
}

// NewProductApplicationService 创建产品应用服务实例
func NewProductApplicationService(
	productService productService.ProductService,
	validateService validateService.ValidateService,
	venueService venueService.VenueService,
) ProductApplicationService {
	// 创建流程引擎
	engine := processEngine.NewEngine()

	// 注册服务
	engine.RegisterService("productService", productService)
	engine.RegisterService("validateService", validateService)
	engine.RegisterService("venueService", venueService)

	return &ProductApplicationServiceImpl{
		processEngine:   engine,
		productService:  productService,
		validateService: validateService,
		venueService:    venueService,
	}
}

// validateBaseParams 验证基础参数
func (s *ProductApplicationServiceImpl) validateBaseParams(ctx context.Context, venueId *string) (po.Venue, error) {
	if venueId == nil || *venueId == "" {
		return po.Venue{}, fmt.Errorf("VenueId不能为空")
	}
	// 检查门店是否存在
	venue, err := s.venueService.FindByID(ctx, *venueId)
	if err != nil || venue == nil {
		return po.Venue{}, fmt.Errorf("门店不存在")
	}
	return *venue, nil
}

// QueryProducts 查询产品
func (s *ProductApplicationServiceImpl) V3QueryProducts(ctx context.Context, reqDto req.V3QueryProductReqDto) ([]vo.ProductVO, error) {

	if reqDto.Ids == nil || len(*reqDto.Ids) <= 0 {
		return nil, fmt.Errorf("ids不能为空")
	}

	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return nil, err
	}

	products, err := s.productService.FindsByIds(ctx, *reqDto.VenueId, *reqDto.Ids)
	if err != nil {
		return nil, err
	}

	productVOs := make([]vo.ProductVO, 0)
	for _, product := range products {
		productVOs = append(productVOs, s.productService.ConvertToProductVO(ctx, product))
	}

	return productVOs, nil
}

// AddProduct 新增产品
func (s *ProductApplicationServiceImpl) AddProduct(ctx context.Context, reqDto req.AddProductReqDto) (vo.ProductVO, error) {
	// 验证基础参数
	venue, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return vo.ProductVO{}, err
	}

	// 创建产品
	product := &po.Product{
		VenueId:                       venue.Id,
		Name:                          reqDto.Name,
		Type:                          reqDto.Type,
		CurrentPrice:                  reqDto.CurrentPrice,
		Price:                         reqDto.Price,
		PayMark:                       reqDto.PayMark,
		Barcode:                       reqDto.Barcode,
		AreaPrices:                    reqDto.AreaPrices,
		BuyGiftPlan:                   reqDto.BuyGiftPlan,
		TimeSlotPrices:                reqDto.TimeSlotPrices,
		DistributionChannels:          reqDto.DistributionChannels,
		MemberCardPaymentRestrictions: reqDto.MemberCardPaymentRestrictions,
		MinimumSaleQuantity:           reqDto.MinimumSaleQuantity,
		IsRealPriceProduct:            reqDto.IsRealPriceProduct,
		AuxiliaryFormula:              reqDto.AuxiliaryFormula,
		Category:                      reqDto.Category,
		IsMemberDiscountable:          reqDto.IsMemberDiscountable,
		IsOrderDiscountable:           reqDto.IsOrderDiscountable,
		IsOrderReduceable:             reqDto.IsOrderReduceable,
		AllowRepeatBuy:                reqDto.AllowRepeatBuy,
		RecommendCombos:               reqDto.RecommendCombos,
		MemberCardLimits:              reqDto.MemberCardLimits,
		Flavors:                       reqDto.Flavors,
		Ingredients:                   reqDto.Ingredients,
		IsDisplayed:                   reqDto.IsDisplayed,
		AllowStaffGift:                reqDto.AllowStaffGift,
		CountToMinCharge:              reqDto.CountToMinCharge,
		CountToPerformance:            reqDto.CountToPerformance,
		IsPromotion:                   reqDto.IsPromotion,
		IsSoldOut:                     reqDto.IsSoldOut,
		AllowWineStorage:              reqDto.AllowWineStorage,
		GiftVoucher:                   reqDto.GiftVoucher,
		CalculateInventory:            reqDto.CalculateInventory,
		IsAreaSpecified:               reqDto.IsAreaSpecified,
		SelectedAreas:                 reqDto.SelectedAreas,
		IsRoomTypeSpecified:           reqDto.IsRoomTypeSpecified,
		SelectedRoomTypes:             reqDto.SelectedRoomTypes,
		StartTime:                     reqDto.StartTime,
		EndTime:                       reqDto.EndTime,
		Description:                   reqDto.Description,
		Image:                         reqDto.Image,
		LowStockThreshold:             reqDto.LowStockThreshold,
		DeliveryTimeout:               reqDto.DeliveryTimeout,
		SupportsExternalDelivery:      reqDto.SupportsExternalDelivery,
		ExternalDeliveryPrice:         reqDto.ExternalDeliveryPrice,
		Unit:                          reqDto.Unit,
	}

	err = s.productService.Create(ctx, *product)
	if err != nil {
		return vo.ProductVO{}, err
	}

	return s.productService.ConvertToProductVO(ctx, *product), nil
}

// UpdateProduct 更新产品
func (s *ProductApplicationServiceImpl) UpdateProduct(ctx context.Context, reqDto req.UpdateProductReqDto) (vo.ProductVO, error) {
	// 验证基础参数
	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return vo.ProductVO{}, err
	}

	if reqDto.Id == nil || *reqDto.Id == "" {
		return vo.ProductVO{}, fmt.Errorf("id不能为空")
	}

	// 查询产品
	product, err := s.productService.FindByID(ctx, *reqDto.Id)
	if err != nil {
		return vo.ProductVO{}, err
	}

	// 更新产品
	if reqDto.Name != nil {
		product.Name = reqDto.Name
	}
	if reqDto.Type != nil {
		product.Type = reqDto.Type
	}
	if reqDto.CurrentPrice != nil {
		product.CurrentPrice = reqDto.CurrentPrice
	}
	if reqDto.Price != nil {
		product.Price = reqDto.Price
	}
	if reqDto.PayMark != nil {
		product.PayMark = reqDto.PayMark
	}
	if reqDto.Barcode != nil {
		product.Barcode = reqDto.Barcode
	}
	if reqDto.AreaPrices != nil {
		product.AreaPrices = reqDto.AreaPrices
	}
	if reqDto.BuyGiftPlan != nil {
		product.BuyGiftPlan = reqDto.BuyGiftPlan
	}
	if reqDto.TimeSlotPrices != nil {
		product.TimeSlotPrices = reqDto.TimeSlotPrices
	}
	if reqDto.DistributionChannels != nil {
		product.DistributionChannels = reqDto.DistributionChannels
	}
	if reqDto.MemberCardPaymentRestrictions != nil {
		product.MemberCardPaymentRestrictions = reqDto.MemberCardPaymentRestrictions
	}
	if reqDto.MinimumSaleQuantity != nil {
		product.MinimumSaleQuantity = reqDto.MinimumSaleQuantity
	}
	if reqDto.IsRealPriceProduct != nil {
		product.IsRealPriceProduct = reqDto.IsRealPriceProduct
	}
	if reqDto.AuxiliaryFormula != nil {
		product.AuxiliaryFormula = reqDto.AuxiliaryFormula
	}
	if reqDto.Category != nil {
		product.Category = reqDto.Category
	}
	if reqDto.IsMemberDiscountable != nil {
		product.IsMemberDiscountable = reqDto.IsMemberDiscountable
	}
	if reqDto.IsOrderDiscountable != nil {
		product.IsOrderDiscountable = reqDto.IsOrderDiscountable
	}
	if reqDto.IsOrderReduceable != nil {
		product.IsOrderReduceable = reqDto.IsOrderReduceable
	}
	if reqDto.AllowRepeatBuy != nil {
		product.AllowRepeatBuy = reqDto.AllowRepeatBuy
	}
	if reqDto.RecommendCombos != nil {
		product.RecommendCombos = reqDto.RecommendCombos
	}
	if reqDto.MemberCardLimits != nil {
		product.MemberCardLimits = reqDto.MemberCardLimits
	}
	if reqDto.Flavors != nil {
		product.Flavors = reqDto.Flavors
	}
	if reqDto.Ingredients != nil {
		product.Ingredients = reqDto.Ingredients
	}
	if reqDto.IsDisplayed != nil {
		product.IsDisplayed = reqDto.IsDisplayed
	}
	if reqDto.AllowStaffGift != nil {
		product.AllowStaffGift = reqDto.AllowStaffGift
	}
	if reqDto.CountToMinCharge != nil {
		product.CountToMinCharge = reqDto.CountToMinCharge
	}
	if reqDto.CountToPerformance != nil {
		product.CountToPerformance = reqDto.CountToPerformance
	}
	if reqDto.IsPromotion != nil {
		product.IsPromotion = reqDto.IsPromotion
	}
	if reqDto.IsSoldOut != nil {
		product.IsSoldOut = reqDto.IsSoldOut
	}
	if reqDto.AllowWineStorage != nil {
		product.AllowWineStorage = reqDto.AllowWineStorage
	}
	if reqDto.GiftVoucher != nil {
		product.GiftVoucher = reqDto.GiftVoucher
	}
	if reqDto.CalculateInventory != nil {
		product.CalculateInventory = reqDto.CalculateInventory
	}
	if reqDto.IsAreaSpecified != nil {
		product.IsAreaSpecified = reqDto.IsAreaSpecified
	}
	if reqDto.SelectedAreas != nil {
		product.SelectedAreas = reqDto.SelectedAreas
	}
	if reqDto.IsRoomTypeSpecified != nil {
		product.IsRoomTypeSpecified = reqDto.IsRoomTypeSpecified
	}
	if reqDto.SelectedRoomTypes != nil {
		product.SelectedRoomTypes = reqDto.SelectedRoomTypes
	}
	if reqDto.StartTime != nil {
		product.StartTime = reqDto.StartTime
	}
	if reqDto.EndTime != nil {
		product.EndTime = reqDto.EndTime
	}
	if reqDto.Description != nil {
		product.Description = reqDto.Description
	}
	if reqDto.Image != nil {
		product.Image = reqDto.Image
	}
	if reqDto.LowStockThreshold != nil {
		product.LowStockThreshold = reqDto.LowStockThreshold
	}
	if reqDto.DeliveryTimeout != nil {
		product.DeliveryTimeout = reqDto.DeliveryTimeout
	}
	if reqDto.SupportsExternalDelivery != nil {
		product.SupportsExternalDelivery = reqDto.SupportsExternalDelivery
	}
	if reqDto.ExternalDeliveryPrice != nil {
		product.ExternalDeliveryPrice = reqDto.ExternalDeliveryPrice
	}
	if reqDto.Unit != nil {
		product.Unit = reqDto.Unit
	}

	err = s.productService.Update(ctx, *product)
	if err != nil {
		return vo.ProductVO{}, err
	}

	return s.productService.ConvertToProductVO(ctx, *product), nil
}

// DeleteProduct 删除产品
func (s *ProductApplicationServiceImpl) DeleteProduct(ctx context.Context, reqDto req.DeleteProductReqDto) error {
	// 验证基础参数
	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return err
	}

	if reqDto.Id == nil || *reqDto.Id == "" {
		return fmt.Errorf("id不能为空")
	}

	return s.productService.Delete(ctx, *reqDto.Id)
}

// QueryProducts 查询产品
func (s *ProductApplicationServiceImpl) QueryProducts(ctx context.Context, reqDto req.QueryProductReqDto) ([]vo.ProductVO, error) {
	// 验证基础参数
	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return nil, err
	}

	products, err := s.productService.FindAll(ctx, &reqDto)
	if err != nil {
		return nil, err
	}

	productVOs := make([]vo.ProductVO, 0)
	for _, product := range *products {
		productVOs = append(productVOs, s.productService.ConvertToProductVO(ctx, product))
	}

	return productVOs, nil
}

// ListProducts 分页查询产品列表
func (s *ProductApplicationServiceImpl) ListProducts(ctx context.Context, reqDto req.QueryProductReqDto) (*vo.PageVO[[]vo.ProductVO], error) {
	// 验证基础参数
	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return nil, err
	}

	products, total, err := s.productService.FindAllWithPagination(ctx, &reqDto)
	if err != nil {
		return nil, err
	}

	page := &vo.PageVO[[]vo.ProductVO]{
		PageNum:  *reqDto.PageNum,
		PageSize: *reqDto.PageSize,
		Total:    total,
		Data:     make([]vo.ProductVO, 0),
	}

	for _, product := range *products {
		page.Data = append(page.Data, s.productService.ConvertToProductVO(ctx, product))
	}

	return page, nil
}

// SoldOut 产品估清
func (s *ProductApplicationServiceImpl) SoldOut(ctx context.Context, reqDto req.UpdateProductSoldOutReqDto) error {
	// 验证基础参数
	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return err
	}

	if reqDto.Id == nil || *reqDto.Id == "" {
		return fmt.Errorf("id不能为空")
	}

	product, err := s.productService.FindByID(ctx, *reqDto.Id)
	if err != nil {
		return err
	}

	product.IsSoldOut = reqDto.SoldOut
	err = s.productService.Update(ctx, *product)
	if err != nil {
		return err
	}

	return nil
}

// QueryDetailByType 查询产品详情
func (s *ProductApplicationServiceImpl) QueryDetailByType(ctx context.Context, reqDto req.QueryProductReqDto) (*vo.ProductOrPackageRVO, error) {
	// 验证基础参数
	_, err := s.validateBaseParams(ctx, reqDto.VenueId)
	if err != nil {
		return nil, err
	}

	products, err := s.productService.FindAll(ctx, &reqDto)
	if err != nil {
		return nil, err
	}

	returnVO := &vo.ProductOrPackageRVO{
		ProductVOs: make([]vo.ProductVO, 0),
	}

	for _, product := range *products {
		returnVO.ProductVOs = append(returnVO.ProductVOs, s.productService.ConvertToProductVO(ctx, product))
	}

	return returnVO, nil
}
