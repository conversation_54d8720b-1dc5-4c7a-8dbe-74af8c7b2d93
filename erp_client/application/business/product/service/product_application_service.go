package service

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
)

// ProductApplicationService 产品应用服务接口
type ProductApplicationService interface {

	// V3QueryProducts 查询产品
	V3QueryProducts(ctx context.Context, reqDto req.V3QueryProductReqDto) ([]vo.ProductVO, error)

	// AddProduct 新增产品
	AddProduct(ctx context.Context, reqDto req.AddProductReqDto) (vo.ProductVO, error)

	// UpdateProduct 更新产品
	UpdateProduct(ctx context.Context, reqDto req.UpdateProductReqDto) (vo.ProductVO, error)

	// DeleteProduct 删除产品
	DeleteProduct(ctx context.Context, reqDto req.DeleteProductReqDto) error

	// QueryProducts 查询产品
	QueryProducts(ctx context.Context, reqDto req.QueryProductReqDto) ([]vo.ProductVO, error)

	// ListProducts 分页查询产品列表
	ListProducts(ctx context.Context, reqDto req.QueryProductReqDto) (*vo.PageVO[[]vo.ProductVO], error)

	// SoldOut 产品估清
	SoldOut(ctx context.Context, reqDto req.UpdateProductSoldOutReqDto) error

	// QueryDetailByType 查询产品详情
	QueryDetailByType(ctx context.Context, reqDto req.QueryProductReqDto) (*vo.ProductOrPackageRVO, error)
}
