definition:
  id: "order_open_rules"
  name: "开台特定规则组"
  description: "开台特定规则配置，包含订单费用计算、最低消费检查等"

metadata:
  input:
    variables:
      - name: "reqDto"
        type: "object"
        required: true
        fields:
          - name: "payAmount"
            type: "number"
            required: true
          - name: "minimumCharge"
            type: "number"
            required: false
      - name: "orderProducts"
        type: "array"
        required: false
        fields:
          - name: "payAmount"
            type: "number"
            required: true
          - name: "originalAmount"
            type: "number"
            required: true
  output:
    variables:
      - name: "validation"
        type: "object"
        fields:
          - name: "payAmountValid"
            type: "boolean"
          - name: "minimumChargeValid"
            type: "boolean"
      - name: "feeResult"
        type: "object"
        fields:
          - name: "productFee"
            type: "number"
          - name: "originalProductFee"
            type: "number"

rules:
  - name: "支付金额验证"
    condition: "input.reqDto.payAmount >= 0"
    actions:
      - type: "set_field"
        target: "output.validation.payAmountValid"
        value: true

  - name: "最低消费检查"
    condition: "input.reqDto.minimumCharge != nil && input.reqDto.payAmount >= input.reqDto.minimumCharge"
    actions:
      - type: "set_field"
        target: "output.validation.minimumChargeValid"
        value: true

  - name: "商品费用计算"
    condition: "input.orderProducts != nil"
    actions:
      - type: "set_field"
        target: "output.feeResult.productFee"
        value: "sum(input.orderProducts.*.input.reqDto.payAmount)"
      - type: "set_field"
        target: "output.feeResult.originalProductFee"
        value: "sum(input.orderProducts.*.originalAmount)" 