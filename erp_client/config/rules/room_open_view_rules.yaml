definition:
  id: "room_open_view_rules"
  name: "房间开放视图规则"
  description: "控制房间开放视图的显示规则"

metadata:
  description: "控制房间开放视图的显示规则"
  version: "1.0"
  # 输入定义（只读）
  input:
    variables:
      - name: "BaseInfo"
        required: true
        fields:
          - name: "AreaId"
          - name: "CloseTime"
          - name: "Color"
          - name: "ConsumptionMode"
          - name: "DisplayItems"
          - name: "Id"
          - name: "InteriorPhoto"
          - name: "IsDisplayed"
          - name: "Name"
          - name: "OpenTime"
          - name: "QrCode"
          - name: "Status"
          - name: "Tag"
          - name: "TypeId"
          - name: "VenueId"
      - name: "AreaVO"
        required: true
        fields:
          - name: "Capacity"
          - name: "Description"
          - name: "Id"
          - name: "IsDisplayed"
          - name: "Name"
          - name: "VenueId"
      - name: "RoomTypeVO"
        required: true
        fields:
          - name: "Id"
          - name: "IsDisplayed"
          - name: "Name"
      - name: "PricePlanVOs"
        required: true
        is_array: true
        fields:
          - name: "BaseRoomFee"
          - name: "Id"
          - name: "Name"
      - name: "HolidayVOs"
        required: true
        is_array: true
        fields:
          - name: "Date"
          - name: "Id"
          - name: "Name"
          - name: "VenueId"
      - name: "current_date"
        required: true
      - name: "current_time"
        required: true
      - name: "current_week"
        required: true
      - name: "current_hour"
        required: true

  # 输出定义（带默认值）
  output:
    variables:
      - name: "BaseInfo"
        required: true
        default_value: "input.BaseInfo"  # 默认值从输入复制
        fields:
          - name: "AreaId"
          - name: "CloseTime"
          - name: "Color"
          - name: "ConsumptionMode"
          - name: "DisplayItems"
          - name: "Id"
          - name: "InteriorPhoto"
          - name: "IsDisplayed"
          - name: "Name"
          - name: "OpenTime"
          - name: "QrCode"
          - name: "Status"
          - name: "Tag"
          - name: "TypeId"
          - name: "VenueId"
      - name: "AreaVO"
        required: true
        default_value: "input.AreaVO"
        fields:
          - name: "Capacity"
          - name: "Description"
          - name: "Id"
          - name: "IsDisplayed"
          - name: "Name"
          - name: "VenueId"
      - name: "RoomTypeVO"
        required: true
        default_value: "input.RoomTypeVO"
        fields:
          - name: "Id"
          - name: "IsDisplayed"
          - name: "Name"
      - name: "PricePlanVOs"
        required: true
        default_value: "input.PricePlanVOs"
        is_array: true
        fields:
          - name: "BaseRoomFee"
          - name: "Id"
          - name: "Name"
      - name: "HolidayVOs"
        required: true
        default_value: "input.HolidayVOs"
        is_array: true
        fields:
          - name: "Date"
          - name: "Id"
          - name: "Name"
          - name: "VenueId"
      - name: "current_date"
        required: true
        default_value: "input.current_date"
      - name: "current_time"
        required: true
        default_value: "input.current_time"
      - name: "current_week"
        required: true
        default_value: "input.current_week"
      - name: "current_hour"
        required: true
        default_value: "input.current_hour"

rules:
  - id: "check_room_display"
    name: "检查房间显示状态"
    priority: 100
    condition: "input.BaseInfo.IsDisplayed == true"
    actions:
      - type: "set"
        params:
          target: "output.BaseInfo.IsDisplayed"
          value: true

  - id: "check_area_display"
    name: "检查区域显示状态"
    priority: 90
    condition: "input.AreaVO.IsDisplayed == true"
    actions:
      - type: "set"
        params:
          target: "output.AreaVO.IsDisplayed"
          value: true

  - id: "check_room_type_display"
    name: "检查房间类型显示状态"
    priority: 80
    condition: "input.RoomTypeVO.IsDisplayed == true"
    actions:
      - type: "set"
        params:
          target: "output.RoomTypeVO.IsDisplayed"
          value: true

  # - id: "check_holiday_price"
  #   name: "检查节假日价格"
  #   priority: 70
  #   condition: "contains(input.HolidayVOs.*.Date, input.current_date)"
  #   actions:
  #     - type: "calculate"
  #       params:
  #         target: "output.PricePlanVOs.BaseRoomFee"
  #         expression: "multiply(input.PricePlanVOs.BaseRoomFee, 1.5)"

  # - id: "check_peak_time"
  #   name: "检查高峰时段"
  #   priority: 60
  #   condition: "input.current_time >= '18:00' && input.current_time <= '22:00'"
  #   actions:
  #     - type: "calculate"
  #       params:
  #         target: "output.PricePlanVOs.BaseRoomFee"
  #         expression: "multiply(input.PricePlanVOs.BaseRoomFee, 1.2)" 