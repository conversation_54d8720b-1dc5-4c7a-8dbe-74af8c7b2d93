definition:
  id: "physical_card_register_rules"
  name: "物理卡会员注册规则组"
  description: "物理卡会员注册的验证规则，包含必填字段验证、手机号格式验证和卡号验证"

metadata:
  input:
    variables:
      - name: "reqDto"
        type: "object"
        required: true
        fields:
          - name: "venueId"
            type: "string"
            required: true
          - name: "name"
            type: "string"
            required: true
          - name: "phone"
            type: "string"
            required: true
          - name: "cardNumber"
            type: "string"
            required: true
          - name: "cardLevelId"
            type: "string"
            required: true
          - name: "gender"
            type: "string"
            required: false
          - name: "birthday"
            type: "number"
            required: false
          - name: "operatorId"
            type: "string"
            required: true
          - name: "operatorName"
            type: "string"
            required: true
  output:
    variables:
      - name: "validation"
        type: "object"
        fields:
          - name: "isValid"
            type: "boolean"
          - name: "message"
            type: "string"

rules:
  - name: "验证必填字段"
    condition: "input.reqDto.venueId != nil && input.reqDto.name != nil && input.reqDto.phone != nil && input.reqDto.cardLevelId != nil && input.reqDto.cardNumber != nil && input.reqDto.operatorId != nil && input.reqDto.operatorName != nil"
    actions:
      - type: "set_field"
        target: "output.validation.isValid"
        value: true

  - name: "验证手机号格式"
    condition: "len(input.reqDto.phone) == 11"
    actions:
      - type: "set_field"
        target: "output.validation.isValid"
        value: true
      - type: "set_field"
        target: "output.validation.message"
        value: "手机号格式正确"

  - name: "验证卡号格式"
    condition: "input.reqDto.cardNumber != nil && len(input.reqDto.cardNumber) > 0"
    actions:
      - type: "set_field"
        target: "output.validation.isValid"
        value: true
      - type: "set_field"
        target: "output.validation.message"
        value: "卡号格式正确" 