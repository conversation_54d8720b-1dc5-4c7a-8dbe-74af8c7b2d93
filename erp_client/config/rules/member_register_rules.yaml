definition:
  id: "member_register_rules"
  name: "会员注册规则组"
  description: "会员注册的验证规则，包含必填字段验证、手机号格式验证和会员卡类型验证"

metadata:
  input:
    variables:
      - name: "reqDto"
        type: "object"
        required: true
        fields:
          - name: "venueId"
            type: "string"
            required: true
          - name: "name"
            type: "string"
            required: true
          - name: "phone"
            type: "string"
            required: true
          - name: "cardLevelId"
            type: "string"
            required: true
          - name: "cardType"
            type: "string"
            required: true
          - name: "gender"
            type: "string"
            required: false
          - name: "birthday"
            type: "number"
            required: false
          - name: "operatorId"
            type: "string"
            required: true
          - name: "operatorName"
            type: "string"
            required: true
  output:
    variables:
      - name: "validation"
        type: "object"
        fields:
          - name: "isValid"
            type: "boolean"
          - name: "message"
            type: "string"

rules:
  - name: "验证必填字段"
    condition: "input.reqDto.venueId != nil && input.reqDto.name != nil && input.reqDto.phone != nil && input.reqDto.cardLevelId != nil && input.reqDto.cardType != nil && input.reqDto.operatorId != nil && input.reqDto.operatorName != nil"
    actions:
      - type: "set_field"
        target: "output.validation.isValid"
        value: true

  - name: "验证手机号格式"
    condition: "len(input.reqDto.phone) == 11"
    actions:
      - type: "set_field"
        target: "output.validation.isValid"
        value: true
      - type: "set_field"
        target: "output.validation.message"
        value: "手机号格式正确"

  - name: "验证会员卡类型"
    condition: "input.reqDto.cardType == 'VIRTUAL' || input.reqDto.cardType == 'PHYSICAL'"
    actions:
      - type: "set_field"
        target: "output.validation.isValid"
        value: true
      - type: "set_field"
        target: "output.validation.message"
        value: "会员卡类型正确" 