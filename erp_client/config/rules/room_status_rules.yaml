definition:
  id: "room_status_rules"
  name: "房间状态规则组"
  description: "房间状态规则配置，用于控制房间状态的变更和显示"

metadata:
  input:
    variables:
      - name: "room"
        required: true
        fields:
          - name: "status"
            required: true
          - name: "currentOrder"
            required: false
            fields:
              - name: "status"
                required: true
              - name: "endTime"
                required: true
  output:
    variables:
      - name: "statusResult"
        required: true
        fields:
          - name: "displayStatus"
            required: true
          - name: "color"
            required: true
          - name: "endTime"
            required: false

rules:
  - id: "check_room_idle"
    name: "检查房间空闲状态"
    priority: 100
    condition: "input.room.status == 'IDLE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "空闲"
      - type: "set"
        params:
          target: "output.statusResult.color"
          value: "green"

  - id: "check_room_in_use"
    name: "检查房间使用状态"
    priority: 90
    condition: "input.room.status == 'IN_USE' && input.room.currentOrder != nil"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "使用中"
      - type: "set"
        params:
          target: "output.statusResult.color"
          value: "red"
      - type: "set"
        params:
          target: "output.statusResult.endTime"
          value: "input.room.currentOrder.endTime"

  - id: "check_room_cleaning"
    name: "检查房间清洁状态"
    priority: 80
    condition: "input.room.status == 'CLEANING'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "清洁中"
      - type: "set"
        params:
          target: "output.statusResult.color"
          value: "yellow"