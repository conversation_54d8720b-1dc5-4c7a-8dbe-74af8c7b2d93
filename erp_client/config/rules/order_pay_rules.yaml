definition:
  id: "order_pay_rules"
  name: "订单支付规则组"
  description: "订单支付相关的规则配置，包括支付方式验证、金额验证、折扣验证等"

metadata:
  input:
    variables:
      - name: "reqDto"
        required: true
        fields:
          - name: "PayType"
            required: true
          - name: "PayAmount"
            required: true
          - name: "VenueId"
            required: true
          - name: "RoomId"
            required: true
          - name: "SessionId"
            required: true
          - name: "EmployeeId"
            required: true
          - name: "DiscountRoomRate"
            required: false
          - name: "DiscountProductRate"
            required: false
          - name: "ReduceRoomAmount"
            required: false
          - name: "ReduceProductAmount"
            required: false
          - name: "FreeAmount"
            required: false
      - name: "orderRoomPlans"
        required: false
        is_array: true
        fields:
          - name: "DiscountRate"
            required: false
          - name: "ReduceAmount"
            required: false
      - name: "orderProducts"
        required: false
        is_array: true
        fields:
          - name: "DiscountRate"
            required: false
          - name: "ReduceAmount"
            required: false
  
  system:
    variables:
      - name: "PAY_TYPE_SUPPORTS"
        required: true
        is_array: true
        default_value: ["CASH", "WECHAT", "ALIPAY", "BANK", "BSHOWQR"]
      - name: "PAY_TYPE_RECORDS"
        required: true
        is_array: true
        default_value: ["RECORD_CASH", "RECORD_WECHAT", "RECORD_ALIPAY", "RECORD_BANK"]

rules:
  - id: "validate_pay_type"
    name: "支付方式验证"
    priority: 100
    condition: "input.reqDto.PayType != nil && input.reqDto.PayType != ''"
    actions:
      - type: "validate"
        params:
          expression: "contains(system.PAY_TYPE_SUPPORTS, input.reqDto.PayType)"
          error_message: "不支持的支付方式"

  - id: "validate_pay_amount"
    name: "支付金额验证"
    priority: 90
    condition: "input.reqDto.PayAmount != nil"
    actions:
      - type: "validate"
        params:
          expression: "input.reqDto.PayAmount > 0"
          error_message: "支付金额必须大于0"

  - id: "validate_discount_rate"
    name: "折扣率验证"
    priority: 80
    condition: "input.reqDto.DiscountRoomRate != nil || input.reqDto.DiscountProductRate != nil"
    actions:
      - type: "validate"
        params:
          expression: "(input.reqDto.DiscountRoomRate == nil || between(input.reqDto.DiscountRoomRate, 1, 100)) && (input.reqDto.DiscountProductRate == nil || between(input.reqDto.DiscountProductRate, 1, 100))"
          error_message: "折扣率必须在1-100之间"

  - id: "validate_free_amount"
    name: "免单金额验证"
    priority: 70
    condition: "input.reqDto.FreeAmount != nil && input.reqDto.FreeAmount > 0"
    actions:
      - type: "validate"
        params:
          expression: "input.reqDto.FreeAmount <= input.reqDto.PayAmount"
          error_message: "免单金额不能大于支付金额"

  - id: "validate_free_discount_conflict"
    name: "免单与折扣冲突验证"
    priority: 60
    condition: "input.reqDto.FreeAmount != nil && input.reqDto.FreeAmount > 0"
    actions:
      - type: "validate"
        params:
          expression: "(input.reqDto.DiscountRoomRate == nil || input.reqDto.DiscountRoomRate == 0) && (input.reqDto.DiscountProductRate == nil || input.reqDto.DiscountProductRate == 0) && (input.reqDto.ReduceRoomAmount == nil || input.reqDto.ReduceRoomAmount == 0) && (input.reqDto.ReduceProductAmount == nil || input.reqDto.ReduceProductAmount == 0)"
          error_message: "免单不能与折扣、减免同时使用"

  - id: "validate_repeat_room_discount"
    name: "重复房费折扣验证"
    priority: 50
    condition: "input.reqDto.DiscountRoomRate != nil && input.reqDto.DiscountRoomRate > 0 && exists(input.orderRoomPlans)"
    actions:
      - type: "validate"
        params:
          expression: '!exists(input.orderRoomPlans.*.DiscountRate, rate -> rate > 0)'
          error_message: "房费不能重复打折"

  - id: "validate_repeat_room_reduce"
    name: "重复房费减免验证"
    priority: 40
    condition: "input.reqDto.ReduceRoomAmount != nil && input.reqDto.ReduceRoomAmount > 0 && exists(input.orderRoomPlans)"
    actions:
      - type: "validate"
        params:
          expression: '!exists(input.orderRoomPlans.*.ReduceAmount, amount -> amount > 0)'
          error_message: "房费不能重复减免"

  - id: "validate_repeat_product_discount"
    name: "重复商品折扣验证"
    priority: 30
    condition: "input.reqDto.DiscountProductRate != nil && input.reqDto.DiscountProductRate > 0 && exists(input.orderProducts)"
    actions:
      - type: "validate"
        params:
          expression: '!exists(input.orderProducts.*.DiscountRate, rate -> rate > 0)'
          error_message: "商品不能重复打折"

  - id: "validate_repeat_product_reduce"
    name: "重复商品减免验证"
    priority: 20
    condition: "input.reqDto.ReduceProductAmount != nil && input.reqDto.ReduceProductAmount > 0 && exists(input.orderProducts)"
    actions:
      - type: "validate"
        params:
          expression: '!exists(input.orderProducts.*.ReduceAmount, amount -> amount > 0)'
          error_message: "商品不能重复减免" 