definition:
  id: "price_calculation_rules"
  name: "价格计算规则组"
  description: "价格计算规则配置，用于开台计价、加钟计价等场景"

metadata:
  input:
    variables:
      - name: "orders"
        type: "objectArray"
        required: true
        fields:
          - name: "type"
            type: "string"
            required: true
          - name: "payAmount"
            type: "number"
            required: true
          - name: "status"
            type: "string"
            required: true
      - name: "orderProducts"
        type: "objectArray"
        required: false
        fields:
          - name: "payAmount"
            type: "number"
            required: true
          - name: "originalAmount"
            type: "number"
            required: true
          - name: "src"
            type: "string"
            required: true

rules:
  - id: "calculate_room_fee"
    name: "房费计算"
    priority: 100
    condition: "exists(orders)"
    actions:
      - type: "calculate"
        params:
          target: "feeResult.roomAmount"
          expression: "sum(orders[?type=='ROOMPLAN'].payAmount * (status=='REFUNDED' ? -1 : 1))"

  - id: "calculate_supermarket_fee"
    name: "商品费用计算"
    priority: 90
    condition: "exists(orders)"
    actions:
      - type: "calculate"
        params:
          target: "feeResult.supermarketAmount"
          expression: "sum(orders[?type!='ROOMPLAN'].payAmount * (status=='REFUNDED' ? -1 : 1))"

  - id: "calculate_total_fee"
    name: "总费用计算"
    priority: 80
    condition: "exists(feeResult.supermarketAmount) && exists(feeResult.roomAmount) && exists(lastMinimumCharge)"
    actions:
      - type: "calculate"
        params:
          target: "feeResult.totalAmount"
          expression: "feeResult.roomAmount + (feeResult.supermarketAmount > lastMinimumCharge ? feeResult.supermarketAmount : lastMinimumCharge)"