rules:
  # 会员注册验证规则
  member_register_rules:
    venueId:
      - required: true
        message: "门店ID不能为空"
    name:
      - required: true
        message: "会员姓名不能为空"
      - maxLength: 50
        message: "会员姓名长度不能超过50个字符"
    phone:
      - required: true
        message: "手机号不能为空"
      - pattern: "^1[3-9]\\d{9}$"
        message: "手机号格式不正确"
    cardLevelId:
      - required: true
        message: "卡等级ID不能为空"
    cardType:
      - required: true
        message: "卡类型不能为空"
      - enum: ["VIRTUAL", "ELECTRONIC"]
        message: "卡类型只能是虚拟卡或电子卡"
    operatorId:
      - required: true
        message: "操作人ID不能为空"
    operatorName:
      - required: true
        message: "操作人姓名不能为空"

  # 实体卡会员注册验证规则
  physical_card_register_rules:
    venueId:
      - required: true
        message: "门店ID不能为空"
    name:
      - required: true
        message: "会员姓名不能为空"
      - maxLength: 50
        message: "会员姓名长度不能超过50个字符"
    phone:
      - required: true
        message: "手机号不能为空"
      - pattern: "^1[3-9]\\d{9}$"
        message: "手机号格式不正确"
    cardNumber:
      - required: true
        message: "实体卡号不能为空"
      - minLength: 8
        message: "实体卡号长度不能少于8位"
    cardLevelId:
      - required: true
        message: "卡等级ID不能为空"
    operatorId:
      - required: true
        message: "操作人ID不能为空"
    operatorName:
      - required: true
        message: "操作人姓名不能为空"

  # 会员信息验证规则
  member_verify_rules:
    venueId:
      - required: true
        message: "门店ID不能为空"
    phone:
      - pattern: "^1[3-9]\\d{9}$"
        message: "手机号格式不正确"
    cardNumber:
      - minLength: 8
        message: "卡号长度不能少于8位"
    _custom:
      - rule: "phone || cardNumber"
        message: "手机号和卡号至少提供一项" 