definition:
  id: "order_pay_process"
  name: "订单支付流程"
  description: "处理订单支付的完整流程，包括参数验证、折扣计算、支付处理等"

metadata:
  input:
    variables:
      - name: "reqDto"
        required: true
        fields:
          - name: "VenueId"
            required: true
          - name: "RoomId"
            required: true
          - name: "SessionId"
            required: true
          - name: "PayType"
            required: true
          - name: "PayAmount"
            required: true
          - name: "EmployeeId"
            required: true
          - name: "DiscountRoomRate"
            required: false
          - name: "DiscountProductRate"
            required: false
          - name: "ReduceRoomAmount"
            required: false
          - name: "ReduceProductAmount"
            required: false
          - name: "FreeAmount"
            required: false
  
  output:
    variables:
      - name: "payResult"
        required: true
        fields:
          - name: "PayId"
            required: true
          - name: "ErrMsg"
            required: false
          - name: "ErrCode"
            required: false

  system:
    variables:
      - name: "current_time"
        required: true
        expression: "now()"
   
  context:
    variables:
      - name: "session"
        required: true
        fields:
          - name: "Id"
            required: true
          - name: "UnpaidAmount"
            required: true
      - name: "toPayOrderVOs"
        required: true
        is_array: true
      - name: "toPayOrders"
        required: true
        is_array: true
      - name: "orderRoomPlans"
        required: false
        is_array: true
      - name: "orderProducts"
        required: false
        is_array: true
      - name: "hasDiscount"
        required: true
      - name: "toPayBill"
        required: true
      - name: "toAddOrderAndPays"
        required: true
        is_array: true
      - name: "roomDiscountResult"
        required: true
        fields:
          - name: "updatedRoomPlans"
            required: true
          - name: "totalDiscountRoomAmount"
            required: true
      - name: "productDiscountResult"
        required: true
        fields:
          - name: "updatedProducts"
            required: true
          - name: "totalDiscountProductAmount"
            required: true
      - name: "orderAmountResult"
        required: true
        fields:
          - name: "updatedOrders"
            required: true
      - name: "validationResult"
        required: true
        fields:
          - name: "success"
            required: true
      - name: "saveResult"
        required: true
        fields:
          - name: "success"
            required: true

steps:
  # - id: "validate_base_params"
  #   name: "基础参数验证"
  #   action:
  #     type: "service"
  #     service: "ruleService"
  #     method: "Evaluate"
  #     params:
  #       - name: "ruleGroupId"
  #         value: "order_pay_rules"
  #       - name: "input"
  #         value:
  #           reqDto: "input.reqDto"
  #           orderRoomPlans: "context.orderRoomPlans"
  #           orderProducts: "context.orderProducts"
  #     output:
  #       target: "context.validationResult"

  - id: "get_session_info"
    name: "获取场次信息"
    # condition: "context.validationResult.success == true"
    action:
      type: "service"
      service: "sessionService"
      method: "FindAllSession"
      params:
        - "input.reqDto.VenueId"
        - "input.reqDto.SessionId"
      output:
        target: "context.session"
        mapping:
          Id: "result.Id"
          UnpaidAmount: "result.UnpaidAmount"
      error_handler:
        type: "early_return"
        error_code: "VALIDATION_ERROR"
        error_message: "订单支付验证失败"

  - id: "get_pay_orders"
    name: "获取需支付订单"
    condition: "context.session != nil"
    action:
      type: "service"
      service: "payService"
      method: "GetPayOrderInfos"
      params:
        - "input.reqDto"
      output:
        target: "context"
        mapping:
          toPayOrderVOs: "result.orderVOs"
          toPayOrders: "result.orders"

  - id: "check_has_discount"
    name: "检查是否有折扣"
    condition: "context.toPayOrders != nil"
    action:
      type: "service"
      service: "orderValidator"
      method: "ValidatePayHasDiscount"
      params:
        - "input.reqDto"
      output:
        target: "context.hasDiscount"
        mapping:
          value: "result"

  - id: "get_room_and_product_info"
    name: "获取房间和商品信息"
    condition: "context.hasDiscount == true"
    action:
      type: "service"
      service: "payService"
      method: "GetPayOrderInfosWithRoomAndProductBySessionId"
      params:
        - "input.reqDto.VenueId"
        - "input.reqDto.SessionId"
        - "context.toPayOrderVOs"
      output:
        target: "context"
        mapping:
          orderRoomPlans: "result.orderRoomPlans"
          orderProducts: "result.orderProducts"

  - id: "validate_discount"
    name: "验证折扣"
    condition: "context.hasDiscount == true"
    action:
      type: "service"
      service: "payService"
      method: "ValidatePayDiscountIsValid"
      params:
        - "input.reqDto"
        - "context.orderRoomPlans"
        - "context.orderProducts"
      output:
        target: "context.validationResult"
        mapping:
          success: "result.success"

  - id: "calc_room_discount"
    name: "计算房费折扣"
    condition: "context.hasDiscount == true"
    action:
      type: "service"
      service: "discountCalculateService"
      method: "CalcRoomDiscount"
      params:
        - name: "reqDto"
          value: "input.reqDto"
        - name: "orderRoomPlans"
          value: "context.orderRoomPlans"
        - name: "orderNo2OrderVO"
          value: "context.orderNo2OrderVO"
      output:
        target: "context.roomDiscountResult"
        mapping:
          updatedRoomPlans: "result.UpdatedRoomPlans"
          totalDiscountRoomAmount: "result.TotalDiscountRoomAmount"

  - id: "calc_product_discount"
    name: "计算商品折扣"
    condition: "context.hasDiscount == true"
    action:
      type: "service"
      service: "discountCalculateService"
      method: "CalcProductDiscount"
      params:
        - name: "reqDto"
          value: "input.reqDto"
        - name: "orderProducts"
          value: "context.orderProducts"
        - name: "orderNo2OrderVO"
          value: "context.orderNo2OrderVO"
      output:
        target: "context.productDiscountResult"
        mapping:
          updatedProducts: "result.UpdatedProducts"
          totalDiscountProductAmount: "result.TotalDiscountProductAmount"

  - id: "update_order_amount"
    name: "更新订单金额"
    condition: "context.hasDiscount == true"
    action:
      type: "service"
      service: "orderAmountService"
      method: "UpdateOrderAmount"
      params:
        - name: "orders"
          value: "context.toPayOrders"
        - name: "roomPlans"
          value: "context.roomDiscountResult.updatedRoomPlans"
        - name: "products"
          value: "context.productDiscountResult.updatedProducts"
      output:
        target: "context.orderAmountResult"
        mapping:
          updatedOrders: "result.UpdatedOrders"

  - id: "build_pay_data"
    name: "构建支付数据"
    action:
      type: "service"
      service: "payService"
      method: "BuildPayDataForPay"
      params:
        - "input.reqDto"
        - "context.orderAmountResult.updatedOrders"
        - "context.session"
        - "context.roomDiscountResult.totalDiscountRoomAmount"
        - "context.productDiscountResult.totalDiscountProductAmount"
      output:
        target: "context"
        mapping:
          toPayBill: "result.payBill"
          toAddOrderAndPays: "result.orderAndPays"

  - id: "save_pay_info"
    name: "保存支付信息"
    action:
      type: "service"
      service: "payService"
      method: "SaveBatchTxForPay"
      params:
        - "context.toPayBill"
        - "context.toAddOrderAndPays"
        - "context.orderAmountResult.updatedOrders"
        - "context.roomDiscountResult.updatedRoomPlans"
        - "context.productDiscountResult.updatedProducts"
      output:
        target: "context.saveResult"
        mapping:
          success: "result.success"

  - id: "transform_pay_gate"
    name: "发起支付"
    action:
      type: "service"
      service: "payService"
      method: "TransformPayGate"
      params:
        - "input.reqDto"
        - "context.toPayBill"
      output:
        target: "output.payResult"
        mapping:
          PayId: "result.PayId"
          ErrMsg: "result.ErrMsg"
          ErrCode: "result.ErrCode"

  - id: "after_pay_callback"
    name: "支付后处理"
    action:
      type: "service"
      service: "payService"
      method: "AfterPayCallbackCoUpdateInfoByPayId"
      params:
        - "context.toPayBill.PayId" 