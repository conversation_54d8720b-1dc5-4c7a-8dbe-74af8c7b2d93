definition:
  id: "room_open_view"
  name: "房间开放视图流程"
  description: "获取房间开放视图所需的各种信息"

metadata:
  input:
    variables:
      - name: "reqDto"
        required: true
        fields:
          - name: "RoomId"
          - name: "VenueId"
          - name: "TypeId"
          - name: "AreaId"
  
  output:
    variables:
      - name: "roomVO"
        required: true
        fields:
          - name: "BaseInfo"
          - name: "AreaVO"
          - name: "RoomTypeVO"
          - name: "PricePlanVOs"
            is_array: true
          - name: "HolidayVOs"
            is_array: true

  context:
    variables:
      - name: "roomVO"
        required: true
        fields:
          - name: "BaseInfo"
            fields:
              - name: "Id"
              - name: "Name"
              - name: "Code"
              - name: "Status"
              - name: "OpenTime"
              - name: "CloseTime"
              - name: "VenueId"
              - name: "TypeId"
              - name: "AreaId"
              - name: "IsDisplayed"
              - name: "ConsumptionMode"
              - name: "InteriorPhoto"
              - name: "QrCode"
              - name: "Color"
              - name: "DisplayItems"
              - name: "Tag"
          - name: "AreaVO"
            fields:
              - name: "Id"
              - name: "VenueId"
              - name: "Name"
              - name: "Capacity"
              - name: "Description"
              - name: "IsDisplayed"
          - name: "RoomTypeVO"
            fields:
              - name: "Id"
              - name: "Name"
              - name: "IsDisplayed"
          - name: "PricePlanVOs"
            is_array: true
            fields:
              - name: "Id"
              - name: "Name"
              - name: "BaseRoomFee"
          - name: "HolidayVOs"
            is_array: true
            fields:
              - name: "Id"
              - name: "Name"
              - name: "Date"
              - name: "VenueId"
  
  system:
    variables:
      - name: "current_date"
        required: true
        expression: "formatTime(now(), '2006-01-02')"
      - name: "current_time"
        required: true
        expression: "formatTime(now(), '15:04')"

steps:
  - id: "validate_room_id"
    name: "验证房间ID"
    action:
      type: "validate"
      rules:
        - type: "required"
          field: "input.reqDto.RoomId"
          field_name: "房间ID"
          error_message: "房间ID不能为空"
          error_code: "ROOM_ID_REQUIRED"
      error_handler:
        type: "early_return"
        http_code: 400
        error_message: "房间参数验证失败"
        error_code: "ROOM_VALIDATION_ERROR"

  - id: "fetch_room_info"
    name: "获取房间基础信息"
    condition: "input.reqDto.RoomId != ''"
    action:
      type: "service"
      service: "roomService"
      method: "GetRoom"
      params:
        - "input.reqDto.RoomId"
      output:
        target: "context.roomVO.BaseInfo"
        mapping:
          Id: "result.Id"
          Name: "result.Name"
          Code: "result.Code"
          Status: "result.Status"
          OpenTime: "result.OpenTime"
          CloseTime: "result.CloseTime"
          VenueId: "result.VenueId"
          TypeId: "result.TypeId"
          AreaId: "result.AreaId"
          IsDisplayed: "result.IsDisplayed"
          ConsumptionMode: "result.ConsumptionMode"
          InteriorPhoto: "result.InteriorPhoto"
          QrCode: "result.QrCode"
          Color: "result.Color"
          DisplayItems: "result.DisplayItems"
          Tag: "result.Tag"
          
  - id: "fetch_area_info"
    name: "获取区域信息"
    condition: "input.reqDto.AreaId != nil && input.reqDto.AreaId != ''"
    action:
      type: "service"
      service: "areaService"
      method: "GetArea"
      params:
        - "input.reqDto.AreaId"
      output:
        target: "context.roomVO.AreaVO"
        mapping:
          Id: "result.Id"
          VenueId: "result.VenueId"
          Name: "result.Name"
          Capacity: "result.Capacity"
          Description: "result.Description"
          IsDisplayed: "result.IsDisplayed"

  - id: "fetch_room_type_info"
    name: "获取房间类型信息"
    condition: "input.reqDto.TypeId != nil && input.reqDto.TypeId != ''"
    action:
      type: "service"
      service: "roomTypeService"
      method: "GetRoomType"
      params:
        - "input.reqDto.TypeId"
      output:
        target: "context.roomVO.RoomTypeVO"
        mapping:
          Id: "result.Id"
          Name: "result.Name"
          IsDisplayed: "result.IsDisplayed"

  - id: "fetch_price_plan_info"
    name: "获取价格方案信息"
    condition: "input.reqDto.TypeId != nil && input.reqDto.TypeId != ''"
    action:
      type: "service"
      service: "pricePlanService"
      method: "FindByRoomType"
      params:
        - "input.reqDto.TypeId"
      output:
        target: "context.roomVO.PricePlanVOs"
        mapping:
          foreach: "result"
          fields:
            Id: "item.Id"
            Name: "item.Name"
            BaseRoomFee: "item.BaseRoomFee"

  - id: "fetch_holiday_info"
    name: "获取节假日信息"
    condition: "input.reqDto.VenueId != nil && input.reqDto.VenueId != ''"
    action:
      type: "service"
      service: "holidayService"
      method: "FindByVenueID"
      params:
        - "input.reqDto.VenueId"
      output:
        target: "context.roomVO.HolidayVOs"
        mapping:
          foreach: "result"
          fields:
            Id: "item.Id"
            Name: "item.Name"
            Date: "item.Date"
            VenueId: "item.VenueId"

  - id: "apply_rules"
    name: "应用规则"
    action:
      type: "service"
      service: "ruleService"
      method: "Evaluate"
      params:
        - name: "ruleGroupId"
          value: "room_open_view_rules"
        - name: "input"
          value:
            BaseInfo: "context.roomVO.BaseInfo"
            AreaVO: "context.roomVO.AreaVO"
            RoomTypeVO: "context.roomVO.RoomTypeVO"
            PricePlanVOs: "context.roomVO.PricePlanVOs"
            HolidayVOs: "context.roomVO.HolidayVOs"
            current_date: "system.current_date"
            current_time: "system.current_time"
            current_week: "system.current_week"
            current_hour: "system.current_hour"
      output:
        - target: "context.roomVO"
          mapping:
            BaseInfo: "result.Data.BaseInfo"
            PricePlanVOs: "result.Data.PricePlanVOs"
            AreaVO: "result.Data.AreaVO"
            RoomTypeVO: "result.Data.RoomTypeVO"
            HolidayVOs: "result.Data.HolidayVOs"
            current_time: "result.Data.current_time"
            current_date: "result.Data.current_date"
            current_week: "result.Data.current_week"
            current_hour: "result.Data.current_hour"

  - id: "set_output"
    name: "设置输出结果"
    action:
      type: "set_field"
      target: "output.roomVO"
      value: "context.roomVO"

  # - id: "set_current_time"
  #   name: "设置当前时间"
  #   action:
  #     type: "set_field"
  #     target: "context.roomVO.CurrentTime"
  #     value: "now().unix()" 