definition:
  id: "order_open_process"
  name: "开台流程"
  description: "处理开台业务流程"

metadata:
  input:
    variables:
      - name: "reqDto"
        required: true
        fields:
          - name: "RoomId"
            required: true
          - name: "VenueId"
            required: true
          - name: "PayAmount"
            required: true
          - name: "MinimumCharge"
            required: true
          - name: "BookingId"
            required: false
          - name: "EndTime"
            required: false
  
  context:
    variables:
      - name: "room"
        required: true
        fields:
          - name: "id"
          - name: "venueId"
          - name: "status"
          - name: "name"
          - name: "code"
          - name: "openTime"
          - name: "closeTime"
          - name: "typeId"
          - name: "areaId"
          - name: "isDisplayed"
          - name: "consumptionMode"
      - name: "orderInfo"
        required: true
        fields:
          - name: "orderNo"
          - name: "sessionId"
      - name: "validation"
        required: true
        fields:
          - name: "roomExists"
          - name: "roomStatusValid"
          - name: "roomVenueValid"
          - name: "payAmountValid"
          - name: "minimumChargeValid"
      - name: "feeResult"
        required: true
        fields:
          - name: "roomAmount"
          - name: "supermarketAmount"
          - name: "totalAmount"
          - name: "unpaidAmount"
  
  system:
    variables:
      - name: "current_time"
        required: true
        expression: "now()"
      
  output:
    variables:
      - name: "session"
        required: true
        fields:
          - name: "id"
          - name: "sessionId"
          - name: "venueId"
          - name: "roomId"
          - name: "startTime"
          - name: "endTime"
          - name: "duration"
          - name: "status"
          - name: "minConsume"
          - name: "roomFee"
          - name: "supermarketFee"
          - name: "totalFee"
          - name: "unpaidAmount"
          - name: "prePayBalance"
          - name: "isOpenTableSettled"
          - name: "info"
          - name: "employeeId"
          - name: "employeeIdPay"
          - name: "orderPricePlanVO"
          - name: "roomVO"
      
steps:
  - id: "fetch_room_info"
    name: "获取房间信息"
    action:
      type: "service"
      service: "roomService"
      method: "GetRoom"
      params:
        - "input.reqDto.RoomId"
      output:
        target: "context.room"
        mapping:
          id: "result.Id"
          venueId: "result.VenueId"
          status: "result.Status"
          name: "result.Name"
          code: "result.Code"
          openTime: "result.OpenTime"
          closeTime: "result.CloseTime"
          typeId: "result.TypeId"
          areaId: "result.AreaId"
          isDisplayed: "result.IsDisplayed"
          consumptionMode: "result.ConsumptionMode"

  - id: "validate_room_status"
    name: "验证房间状态"
    action:
      type: "service"
      service: "ruleService"
      method: "Evaluate"
      params:
        - name: "ruleGroupId"
          value: "room_status_rules"
        - name: "input"
          value:
            room:
              id: "context.room.id"
              status: "context.room.status"
              venueId: "context.room.venueId"
            reqDto:
              venueId: "input.reqDto.VenueId"
              roomId: "input.reqDto.RoomId"
            validation:
              roomExists: false
              roomStatusValid: false
              roomVenueValid: false
      output:
        - target: "context.validation"
          mapping:
            roomExists: "result.Data.validation.roomExists"
            roomStatusValid: "result.Data.validation.roomStatusValid"
            roomVenueValid: "result.Data.validation.roomVenueValid"

  - id: "check_room_validation"
    name: "检查房间验证结果"
    condition: "!context.validation.roomExists || !context.validation.roomStatusValid || !context.validation.roomVenueValid"
    action:
      type: "error"
      params:
        code: "ROOM_VALIDATION_ERROR"
        message: "房间状态验证失败"

  - id: "validate_order_rules"
    name: "验证订单规则"
    action:
      type: "service"
      service: "ruleService"
      method: "Evaluate"
      params:
        - name: "ruleGroupId"
          value: "order_open_rules"
        - name: "input"
          value:
            reqDto:
              payAmount: "input.reqDto.PayAmount"
              minimumCharge: "input.reqDto.MinimumCharge"
            orderProducts: "context.toAddOrderProducts"
      output:
        target: "context.validation"
        mapping:
          payAmountValid: "result.Data.validation.payAmountValid"
          minimumChargeValid: "result.Data.validation.minimumChargeValid"

  - id: "check_order_validation"
    name: "检查订单验证结果"
    condition: "!context.validation.payAmountValid || !context.validation.minimumChargeValid"
    action:
      type: "error"
      params:
        code: "ORDER_VALIDATION_ERROR"
        message: "订单规则验证失败"

  - id: "generate_order_info"
    name: "生成订单信息"
    action:
      type: "service"
      service: "orderService"
      method: "GenerateOrderInfo"
      params:
        - "input.reqDto.VenueId"
      output:
        target: "context.orderInfo"
        mapping:
          orderNo: "result.OrderNo"
          sessionId: "result.SessionId"

  - id: "prepare_orders"
    name: "准备订单数据"
    action:
      type: "service"
      service: "orderService"
      method: "PrepareOrders"
      params:
        - "input.reqDto"
        - "context.orderInfo"
      output:
        target: "context"
        mapping:
          toAddSession: "result.session"
          toAddAllOrders: "result.orders"
          toAddOrderProducts: "result.orderProducts"
          toAddOrderPricePlan: "result.orderPricePlan"
          toAddOrderRoomPlans: "result.orderRoomPlans"
          toUpdateRoom: "result.room"

  - id: "calculate_fees"
    name: "计算费用"
    action:
      type: "service"
      service: "ruleService"
      method: "Evaluate"
      params:
        - name: "ruleGroupId"
          value: "price_calculation_rules"
        - name: "input"
          value:
            orders: "context.toAddAllOrders"
            orderProducts: "context.toAddOrderProducts"
            lastMinimumCharge: "input.reqDto.MinimumCharge"
      output:
        target: "context.feeResult"
        mapping:
          roomAmount: "result.Data.feeResult.roomAmount"
          supermarketAmount: "result.Data.feeResult.supermarketAmount"
          totalAmount: "result.Data.feeResult.totalAmount"
          unpaidAmount: "result.Data.feeResult.unpaidAmount"

  - id: "create_orders"
    name: "创建订单"
    action:
      type: "service"
      service: "orderService"
      method: "OrderOpen"
      params:
        - "context.toAddSession"
        - "context.toAddAllOrders"
        - "context.toAddOrderProducts"
        - "context.toAddOrderPricePlan"

  - id: "update_booking"
    name: "更新预订状态"
    condition: "input.reqDto.BookingId != ''"
    action:
      type: "service"
      service: "bookingService"
      method: "UpdateBookingStatus"
      params:
        - type: "Booking"
          value:
            id: "input.reqDto.BookingId"

  - id: "set_room_timer"
    name: "设置房间定时器"
    condition: "context.createResult.success && input.reqDto.EndTime > 0"
    action:
      type: "service"
      service: "roomTimerService"
      method: "SetRoomTimer"
      params:
        - "input.reqDto.VenueId"
        - "input.reqDto.RoomId"
        - "context.orderInfo.sessionId"
        - "input.reqDto.EndTime"

  - id: "prepare_result"
    name: "准备返回结果"
    action:
      type: "transform"
      params:
        source: "context.toAddSession"
        target: "output.session"
        mapping:
          id: "Id"
          sessionId: "SessionId"
          venueId: "VenueId"
          roomId: "RoomId"
          startTime: "StartTime"
          endTime: "EndTime"
          duration: "Duration"
          status: "Status"
          minConsume: "MinConsume"
          roomFee: "RoomFee"
          supermarketFee: "SupermarketFee"
          totalFee: "TotalFee"
          unpaidAmount: "UnpaidAmount"
          prePayBalance: "PrePayBalance"
          isOpenTableSettled: "IsOpenTableSettled"
          info: "Info"
          employeeId: "EmployeeId"
          employeeIdPay: "EmployeeIdPay"
          orderPricePlanVO: "context.toAddOrderPricePlan"
          roomVO: "context.toUpdateRoom"

error_handlers:
  - condition: "error != nil"
    action: "rollback_transaction"

completion_handlers:
  - condition: "success"
    action: "commit_transaction" 