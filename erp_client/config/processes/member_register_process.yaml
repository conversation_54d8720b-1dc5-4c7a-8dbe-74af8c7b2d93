definition:
  id: "member_register_process"
  name: "会员注册流程"
  description: "处理会员注册和开卡的业务流程"

metadata:
  input:
    variables:
      - name: "reqDto"
        type: "object"
  output:
    variables:
      - name: "memberVO"
        type: "object"
  context:
    variables:
      - name: "memberInfo"
        type: "object"
      - name: "cardLevelInfo"
        type: "object"
      - name: "validationResult"
        type: "object"

steps:
  - id: "validate_member_info"
    name: "验证会员信息"
    condition: "true"
    action:
      type: "validate"
      # skipValidation: 不传或不设置时默认为false，设置为true时跳过所有验证规则
      skipValidation: false
      rules:
        - type: "required"
          field: "input.reqDto.venueId"
          field_name: "场馆ID"
          error_message: "场馆ID不能为空"
          error_code: "REQUIRED_VENUE_ID"
        - type: "required"
          field: "input.reqDto.name"
          field_name: "会员姓名"
          error_message: "会员姓名不能为空"
          error_code: "REQUIRED_NAME"
        - type: "required"
          field: "input.reqDto.phone"
          field_name: "手机号"
          error_message: "手机号不能为空"
          error_code: "REQUIRED_PHONE"
        - type: "pattern"
          field: "input.reqDto.phone"
          field_name: "手机号"
          error_message: "手机号格式不正确"
          error_code: "INVALID_PHONE"
          params:
            pattern: "^1[3-9]\\d{9}$"
        - type: "required"
          field: "input.reqDto.cardLevelId"
          field_name: "会员卡等级"
          error_message: "会员卡等级不能为空"
          error_code: "REQUIRED_CARD_LEVEL"
        - type: "required"
          field: "input.reqDto.cardType"
          field_name: "会员卡类型"
          error_message: "会员卡类型不能为空"
          error_code: "REQUIRED_CARD_TYPE"
        - type: "enum"
          field: "input.reqDto.cardType"
          field_name: "会员卡类型"
          error_message: "会员卡类型必须是VIRTUAL或PHYSICAL"
          error_code: "INVALID_CARD_TYPE"
          params:
            values: ["VIRTUAL", "PHYSICAL"]
      error_handler:
        type: "early_return"
        error_code: "VALIDATION_ERROR"
        error_message: "会员信息验证失败"
    output_mapping:
      - target: "context.validationResult"
        source: "result"

  - id: "check_member_exists"
    name: "检查会员是否已存在"
    condition: "true"
    action:
      type: "service"
      service: "memberDomainService"
      method: "CheckMemberExists"
      params:
        - name: "venueId"
          value: "input.reqDto.venueId"
        - name: "phone"
          value: "input.reqDto.phone"
    output_mapping:
      - target: "context.memberExists"
        source: "exists"
      - target: "context.existingMember"
        source: "member"

  - id: "validate_card_level"
    name: "验证会员卡等级"
    condition: "context.memberExists == false"
    action:
      type: "service"
      service: "cardLevelDomainService"
      method: "ValidateCardLevel"
      params:
        - name: "cardLevelId"
          value: "input.reqDto.cardLevelId"
        - name: "venueId"
          value: "input.reqDto.venueId"
    output_mapping:
      - target: "context.cardLevelInfo"
        source: "cardLevel"
      - target: "context.cardLevelValid"
        source: "isValid"

  - id: "create_member"
    name: "创建会员"
    condition: "context.cardLevelValid == true"
    action:
      type: "service"
      service: "memberDomainService"
      method: "CreateMember"
      params:
        - name: "memberInfo"
          value: "input.reqDto"
        - name: "cardLevelInfo"
          value: "context.cardLevelInfo"
    output_mapping:
      - target: "context.memberInfo"
        source: "member"

  - id: "create_venue_member_relation"
    name: "创建门店会员关联"
    condition: "context.memberInfo != null"
    action:
      type: "service"
      service: "memberDomainService"
      method: "CreateVenueMemberRelation"
      params:
        - name: "venueId"
          value: "input.reqDto.venueId"
        - name: "memberId"
          value: "context.memberInfo.id"
        - name: "remark"
          value: "input.reqDto.remark"
    output_mapping:
      - target: "context.relationCreated"
        source: "success"

  - id: "record_card_operation"
    name: "记录卡操作"
    condition: "context.relationCreated == true"
    action:
      type: "service"
      service: "memberCardOperationDomainService"
      method: "RecordCardOperation"
      params:
        - name: "memberId"
          value: "context.memberInfo.id"
        - name: "cardNumber"
          value: "context.memberInfo.cardNumber"
        - name: "operationType"
          value: "'open_card'"
        - name: "operatorId"
          value: "input.reqDto.operatorId"
        - name: "operatorName"
          value: "input.reqDto.operatorName"
        - name: "remark"
          value: "input.reqDto.remark"
    output_mapping:
      - target: "context.operationRecorded"
        source: "success"

  - id: "prepare_response"
    name: "准备响应数据"
    condition: "context.operationRecorded == true"
    action:
      type: "service"
      service: "memberDomainService"
      method: "ConvertToMemberVO"
      params:
        - name: "member"
          value: "context.memberInfo"
        - name: "cardLevel"
          value: "context.cardLevelInfo"
    output_mapping:
      - target: "output.memberVO"
        source: "memberVO" 