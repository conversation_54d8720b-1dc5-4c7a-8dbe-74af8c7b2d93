package config

import (
	"sync"
	billApplicationService "voderpltvv/erp_client/application/business/bill/service"
	callApplicationService "voderpltvv/erp_client/application/business/call/service"
	memberApplicationService "voderpltvv/erp_client/application/business/member/service"
	orderApplicationService "voderpltvv/erp_client/application/business/order/service"
	productApplicationService "voderpltvv/erp_client/application/business/product/service"
	"voderpltvv/erp_client/application/business/room/service"
	roomApplicaitonService "voderpltvv/erp_client/application/business/room/service"
	venueApplicationService "voderpltvv/erp_client/application/business/venue/service"
	"voderpltvv/erp_client/application/framework/validate"
	validateService "voderpltvv/erp_client/application/framework/validate"
	bookingService "voderpltvv/erp_client/domain/configuration/business/booking/service"
	callService "voderpltvv/erp_client/domain/configuration/business/call/service"
	callServiceImpl "voderpltvv/erp_client/domain/configuration/business/call/service/impl"
	processEngine "voderpltvv/erp_client/domain/process/engine"
	"voderpltvv/erp_client/domain/process/model"
	ruleEngine "voderpltvv/erp_client/domain/rule/engine"
	"voderpltvv/erp_client/domain/rule/repository"
	ruleService "voderpltvv/erp_client/domain/rule/service"
	employeeService "voderpltvv/erp_client/domain/subject/business/employee/service"
	orderService "voderpltvv/erp_client/domain/traderecord/service"
	payService "voderpltvv/erp_client/domain/traderecord/service"
	orderImpl "voderpltvv/erp_client/domain/traderecord/service/impl"
	payImpl "voderpltvv/erp_client/domain/traderecord/service/impl"
	payRecordServiceImpl "voderpltvv/erp_client/domain/traderecord/service/impl"
	areaService "voderpltvv/erp_client/domain/valueobject/business/area/service"
	holidayService "voderpltvv/erp_client/domain/valueobject/business/holiday/service"
	order_roomplanService "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	orderproductService "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	pricePlanService "voderpltvv/erp_client/domain/valueobject/business/price_plan/service"
	productService "voderpltvv/erp_client/domain/valueobject/business/product/service"
	roomService "voderpltvv/erp_client/domain/valueobject/business/room/service"
	roomFaultService "voderpltvv/erp_client/domain/valueobject/business/room_fault/service"
	roomOperationService "voderpltvv/erp_client/domain/valueobject/business/room_operation/service"
	roomThemeService "voderpltvv/erp_client/domain/valueobject/business/room_theme/service"
	roomTypeService "voderpltvv/erp_client/domain/valueobject/business/room_type/service"
	sessionService "voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueService "voderpltvv/erp_client/domain/valueobject/business/venue/service"
	venuePaySettingService "voderpltvv/erp_client/domain/valueobject/business/venue_pay_setting/service"
	"voderpltvv/erp_client/infrastructure/proxy"
)

// Container 依赖注入容器
type Container struct {
	mu sync.RWMutex

	// 基础设施层
	processEngine model.Engine
	ruleEngine    ruleEngine.RuleEngine

	// 领域服务层
	areaService           areaService.Service
	roomTypeService       roomTypeService.Service
	pricePlanService      pricePlanService.Service
	orderPricePlanService payService.OrderPricePlanService
	holidayService        holidayService.Service
	ruleService           ruleService.Service
	roomService           roomService.Service
	orderService          orderService.OrderService
	bookingService        bookingService.BookingService
	validateService       validateService.ValidateService
	sessionService        sessionService.SessionService
	payService            payService.PayService
	payCalcService        payService.PayCalcService
	orderRoomPlanService  order_roomplanService.Service
	orderProductService   orderproductService.Service
	orderAndPayService    orderService.OrderAndPayService
	payBillService        payService.PayBillService
	employeeService       employeeService.Service
	venueService          venueService.VenueService
	roomThemeService      roomThemeService.RoomThemeService
	payRecordService      payService.PayRecordService
	roomOperationService  roomOperationService.RoomOperationService
	callMessageService    callService.CallMessageService
	callTypesService      callService.CallTypesService
	roomFaultService      roomFaultService.RoomFaultService
	// 会员相关领域服务
	memberRechargeBillService   payService.MemberRechargeBillService
	memberRechargeRecordService payService.MemberRechargeRecordService
	memberService               payService.MemberService
	memberCardService           payService.MemberCardService
	memberCardVenueService      payService.MemberCardVenueService
	venuePaySettingService      venuePaySettingService.VenuePaySettingService
	// 应用服务层
	roomApplicationService           roomApplicaitonService.RoomApplicationService
	orderApplicationService          orderApplicationService.OrderApplicationService
	billApplicationService           billApplicationService.BillApplicationService
	venueApplicationService          venueApplicationService.VenueApplicationService
	memberRechargeApplicationService memberApplicationService.MemberRechageApplicationService
	callApplicationService           callApplicationService.CallApplicationService
	// 会员相关应用服务
	memberCardConsumeService   payService.MemberCardConsumeService
	memberCardOperationService payService.MemberCardOperationService
	// 产品相关应用服务
	productApplicationService productApplicationService.ProductApplicationService
	productService            productService.ProductService
}

var (
	container *Container
	once      sync.Once
)

// GetContainer 获取容器单例
func GetContainer() *Container {
	once.Do(func() {
		container = &Container{}
		container.init()
	})
	return container
}

// init 初始化容器
func (c *Container) init() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 1. 初始化基础设施层
	c.processEngine = processEngine.NewEngine()
	c.validateService = validate.NewValidateService(validate.ValidationRules{}) // 传入空的ValidationRules

	// 2. 初始化领域服务层
	c.initDomainServices()

	// 3. 初始化应用服务层
	c.initApplicationServices()
}

// initDomainServices 初始化领域服务
func (c *Container) initDomainServices() {
	// 初始化仓储
	areaRepo := proxy.NewAreaProxy()
	roomTypeRepo := proxy.NewRoomTypeProxy()
	pricePlanRepo := proxy.NewPricePlanProxy()
	holidayRepo := proxy.NewHolidayProxy()
	ruleRepo := proxy.NewRuleProxy()
	roomRepo := proxy.NewRoomProxy()
	sessionRepo := proxy.NewSessionProxy()
	orderRoomPlanRepo := proxy.NewOrderRoomPlanProxy()
	orderProductRepo := proxy.NewOrderProductProxy()
	orderRepo := proxy.NewOrderRepository()
	paybillRepo := proxy.NewPayBillRepository()
	orderPricePlanRepo := proxy.NewOrderPricePlanRepository()
	employeeRepo := proxy.NewEmployeeProxy(nil)
	venueRepo := proxy.NewVenueProxy()
	roomThemeRepo := proxy.NewRoomThemeProxy()
	bookingRepo := proxy.NewBookingProxy()
	orderAndPayRepo := proxy.NewOrderAndPayRepository()
	// payRepository := payRepository.NewPayRepository()
	payRecordRepo := proxy.NewPayRecordRepository()
	roomOperationRepo := proxy.NewRoomOperationProxy()
	memberCardOperationRepo := proxy.NewMemberCardOperationRepository()
	// 初始化会员相关仓储
	memberRechargeBillRepo := proxy.NewMemberRechageBillRepository()
	memberRechargeRecordRepo := proxy.NewMemberRechageRecordRepository()
	memberLegalRepo := proxy.NewMemberProxy()
	memberCardRepo := proxy.NewMemberCardRepository()
	memberCardConsumeRepo := proxy.NewMemberCardConsumeRepository()
	memberCardVenueRepo := proxy.NewMemberCardVenueRepository()
	payRepo := proxy.NewPayProxy()
	callMessageRepo := proxy.NewCallMessageRepository()
	callTypesRepo := proxy.NewCallTypesRepository()
	productRepo := proxy.NewProductProxy()
	productTypeRepo := proxy.NewProductTypeProxy()
	productPackageRepo := proxy.NewProductPackageProxy()
	productPackageTypeRepo := proxy.NewProductPackageTypeProxy()
	roomFaultRepo := proxy.NewRoomFaultProxy()
	venuePaySettingRepo := proxy.NewVenuePaySettingProxy()
	venuePayTypeSettingRepo := proxy.NewVenuePayTypeSettingProxy()
	// 初始化规则引擎
	c.ruleEngine = ruleEngine.NewRuleEngine()

	// 初始化服务
	c.roomFaultService = roomFaultService.NewService(roomFaultRepo)
	c.roomOperationService = roomOperationService.NewService(roomOperationRepo)
	c.areaService = areaService.NewService(areaRepo)
	c.roomTypeService = roomTypeService.NewService(roomTypeRepo)
	c.pricePlanService = pricePlanService.NewService(pricePlanRepo)
	c.holidayService = holidayService.NewService(holidayRepo)
	c.ruleService = ruleService.NewService(ruleRepo)
	c.roomService = roomService.NewService(roomRepo)
	c.sessionService = sessionService.NewService(sessionRepo)
	c.orderRoomPlanService = order_roomplanService.NewService(orderRoomPlanRepo)
	c.payBillService = payImpl.NewPayBillService(paybillRepo)
	c.orderAndPayService = orderImpl.NewOrderAndPayService(orderAndPayRepo)
	c.payRecordService = payRecordServiceImpl.NewPayRecordService(payRecordRepo)
	c.orderPricePlanService = payImpl.NewOrderPricePlanService(orderPricePlanRepo)
	c.memberRechargeBillService = payImpl.NewMemberRechargeBillService(memberRechargeBillRepo)
	c.memberRechargeRecordService = payImpl.NewMemberRechargeRecordService(memberRechargeRecordRepo)
	c.memberService = payImpl.NewMemberService(memberLegalRepo)
	c.memberCardService = payImpl.NewMemberCardService(memberCardRepo)
	c.memberCardVenueService = payImpl.NewMemberCardVenueService(memberCardVenueRepo)
	c.memberCardOperationService = payImpl.NewMemberCardOperationService(memberCardOperationRepo)
	c.memberCardConsumeService = payImpl.NewMemberCardConsumeService(memberCardConsumeRepo, memberRechargeBillRepo, paybillRepo)
	c.orderProductService = orderproductService.NewService(orderProductRepo, orderRepo)
	c.orderService = orderImpl.NewOrderService(
		proxy.NewOrderRepository(), c.orderPricePlanService,
			c.payBillService, c.pricePlanService, c.orderProductService, c.orderRoomPlanService, orderAndPayRepo, productRepo,
			productTypeRepo, productPackageRepo, productPackageTypeRepo, payRecordRepo, sessionRepo, roomRepo, memberCardVenueRepo, 
			memberCardRepo, memberCardOperationRepo, venueRepo, venuePayTypeSettingRepo, memberCardConsumeRepo)
	c.payService = payImpl.NewPayService(
		proxy.NewPayProxy(),
		c.sessionService,
		c.payRecordService,
		c.payBillService,
		c.orderAndPayService,
		c.orderService,
		orderRoomPlanRepo,
		orderProductRepo,
		venueRepo,
		roomRepo,
		orderAndPayRepo,
		payRecordRepo,
		paybillRepo,
	)
	c.payCalcService = payImpl.NewPayCalcService(payRepo)
	c.bookingService = bookingService.NewBookingService(bookingRepo)
	c.roomThemeService = roomThemeService.NewService(roomThemeRepo)
	c.employeeService = employeeService.NewService(employeeRepo)
	c.venueService = venueService.NewService(venueRepo)
	// 初始化会员相关领域服务
	c.callMessageService = callServiceImpl.NewCallMessageService(callMessageRepo)
	c.callTypesService = callServiceImpl.NewCallTypesService(callTypesRepo)
	c.productService = productService.NewService(productRepo)
	c.venuePaySettingService = venuePaySettingService.NewService(venuePaySettingRepo)
}

// initApplicationServices 初始化应用服务
func (c *Container) initApplicationServices() {
	c.roomApplicationService = service.NewRoomApplicationService(
		c.roomService,
		c.roomTypeService,
		c.areaService,
		c.pricePlanService,
		c.holidayService,
		c.ruleService,
		c.validateService,
		c.roomThemeService,
		c.sessionService,
		c.bookingService,
		c.orderPricePlanService,
		c.orderRoomPlanService,
		c.orderService,
		c.roomOperationService,
		c.venueService,
		c.orderProductService,
		c.employeeService,
		c.roomFaultService,
	)

	c.orderApplicationService = orderApplicationService.NewOrderApplicationService(
		c.orderService,
		c.bookingService,
		c.roomService,
		c.ruleService,
		c.validateService,
		c.sessionService,
		c.payService,
		c.pricePlanService,
		c.orderRoomPlanService,
		c.orderProductService,
		c.venueService,
		c.employeeService,
		c.payBillService,
		c.roomTypeService,
		c.payRecordService,
		c.memberRechargeBillService,
		c.memberCardConsumeService,
		c.payCalcService,
		c.memberCardService,
		c.memberCardVenueService,
		c.venuePaySettingService,
	)
	c.billApplicationService = billApplicationService.NewBillApplicationService(
		c.orderService,
		c.bookingService,
		c.roomService,
		c.ruleService,
		c.validateService,
		c.sessionService,
		c.payService,
		c.pricePlanService,
		c.orderRoomPlanService,
		c.orderProductService,
		c.venueService,
		c.employeeService,
		c.payBillService,
		c.roomTypeService,
		c.payRecordService,
		c.orderAndPayService,
	)
	c.venueApplicationService = venueApplicationService.NewVenueApplicationService(
		c.orderService,
		c.bookingService,
		c.roomService,
		c.ruleService,
		c.validateService,
		c.sessionService,
		c.payService,
		c.pricePlanService,
		c.orderRoomPlanService,
		c.orderProductService,
		c.venueService,
		c.employeeService,
		c.payBillService,
		c.roomTypeService,
		c.payRecordService,
		c.orderAndPayService,
		c.memberCardService,
		c.memberCardOperationService,
		c.memberCardVenueService,
	)
	c.memberRechargeApplicationService = memberApplicationService.NewMemberRechageApplicationService(
		c.orderService,
		c.bookingService,
		c.roomService,
		c.ruleService,
		c.validateService,
		c.sessionService,
		c.payService,
		c.pricePlanService,
		c.orderRoomPlanService,
		c.orderProductService,
		c.venueService,
		c.employeeService,
		c.payBillService,
		c.roomTypeService,
		c.payRecordService,
		c.orderAndPayService,
		c.memberRechargeBillService,
		c.memberRechargeRecordService,
		c.memberService,
		c.memberCardService,
		c.memberCardOperationService,
	)

	c.callApplicationService = callApplicationService.NewCallApplicationService(
		c.callMessageService,
		c.callTypesService,
		c.roomService,
		c.validateService,
		c.sessionService,
		c.venueService,
		c.employeeService,
	)
	c.productApplicationService = productApplicationService.NewProductApplicationService(
		c.productService,
		c.validateService,
		c.venueService,
	)
}

// GetProcessEngine 获取流程引擎
func (c *Container) GetProcessEngine() model.Engine {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.processEngine
}

// GetBillApplicationService 获取房间应用服务
func (c *Container) GetBillApplicationService() billApplicationService.BillApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.billApplicationService
}

// GetVenueApplicationService 获取场馆应用服务
func (c *Container) GetVenueApplicationService() venueApplicationService.VenueApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.venueApplicationService
}

// GetMemberRechageApplicationService 获取会员充值应用服务
func (c *Container) GetMemberRechageApplicationService() memberApplicationService.MemberRechageApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.memberRechargeApplicationService
}

// GetRoomApplicationService 获取房间应用服务
func (c *Container) GetRoomApplicationService() service.RoomApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.roomApplicationService
}

// GetAreaService 获取区域服务
func (c *Container) GetAreaService() areaService.Service {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.areaService
}

// GetRoomTypeService 获取房间类型服务
func (c *Container) GetRoomTypeService() roomTypeService.Service {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.roomTypeService
}

// GetPricePlanService 获取价格方案服务
func (c *Container) GetPricePlanService() pricePlanService.Service {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.pricePlanService
}

// GetHolidayService 获取节假日服务
func (c *Container) GetHolidayService() holidayService.Service {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.holidayService
}

// GetRuleService 获取规则服务
func (c *Container) GetRuleService() ruleService.Service {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.ruleService
}

// GetRuleEngine 获取规则引擎
func (c *Container) GetRuleEngine() ruleEngine.RuleEngine {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.ruleEngine
}

// initRuleRepository 初始化规则仓储
func (c *Container) initRuleRepository() repository.Repository {
	return proxy.NewRuleProxy()
}

// GetOrderApplicationService 获取订单应用服务
func (c *Container) GetOrderApplicationService() orderApplicationService.OrderApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.orderApplicationService
}

// GetValidateService 获取验证服务
func (c *Container) GetValidateService() validate.ValidateService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.validateService
}

// GetSessionService 获取会话服务
func (c *Container) GetSessionService() sessionService.SessionService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.sessionService
}

// GetPayService 获取支付服务
func (c *Container) GetPayService() payService.PayService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.payService
}

// GetCallApplicationService 获取呼叫应用服务
func (c *Container) GetCallApplicationService() callApplicationService.CallApplicationService {
	return c.callApplicationService
}

// GetProductApplicationService 获取产品应用服务
func (c *Container) GetProductApplicationService() productApplicationService.ProductApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.productApplicationService
}
