package config

// Config 配置结构
type Config struct {
	// 数据库配置
	Database DatabaseConfig
	// 规则引擎配置
	RuleEngine RuleEngineConfig
	// 流程引擎配置
	ProcessEngine ProcessEngineConfig
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Database string
}

// RuleEngineConfig 规则引擎配置
type RuleEngineConfig struct {
	RuleBasePath string // 规则文件基础路径
}

// ProcessEngineConfig 流程引擎配置
type ProcessEngineConfig struct {
	MaxConcurrent int // 最大并发数
}

var defaultConfig = Config{
	Database: DatabaseConfig{
		Host:     "localhost",
		Port:     3306,
		User:     "root",
		Password: "root",
		Database: "erp",
	},
	RuleEngine: RuleEngineConfig{
		RuleBasePath: "./rules",
	},
	ProcessEngine: ProcessEngineConfig{
		MaxConcurrent: 100,
	},
}

// GetConfig 获取配置
func GetConfig() *Config {
	return &defaultConfig
}
