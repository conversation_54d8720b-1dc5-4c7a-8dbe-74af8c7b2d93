# 房间开放视图API重构笔记

## 1. 现状分析 (2024-01-22)

### 1.1 当前API结构
- 路径: `/api/room/open-view`
- Controller: `RoomController.OpenView`
- 主要职责: 获取房间开放视图数据，包括区域、房间类型、价格方案、节假日等信息

### 1.2 现有业务规则（硬编码）
1. 参数校验规则:
   - venueId不能为空
   - typeId不能为空
   
2. 价格计划筛选规则:
   - 根据房间类型过滤价格计划
   - 通过roomTypeIds判断是否属于当前房间类型

3. 节假日判断规则:
   - 通过当前日期判断是否为节假日

### 1.3 现有配置项
1. 区域配置:
   - 区域名称、容量、是否显示等

2. 房间类型配置:
   - 类型名称、消费模式、高消费警报金额等

3. 价格计划配置:
   - 基础房费、会员价格、节假日价格等
   - 时间段配置
   - 优惠配置
   - 商品配置

4. 节假日配置:
   - 节假日名称、日期、类型

## 2. 重构计划

### 2.1 架构调整
1. 引入规则引擎:
   - 将硬编码的业务规则转换为可配置的YAML规则
   - 规则组织为不同的规则组（价格规则、时间规则等）

2. 分层优化:
   - Controller层: 保持轻量，只负责请求处理和参数校验
   - Application层: 引入ProcessEngine处理业务流程
   - Domain层: 利用已有的领域模型和服务
   - Infrastructure层: 复用现有的代理实现

### 2.2 规则设计（待实现）
1. 价格计算规则组:
   - 节假日价格规则
   - 会员价格规则
   - 时段价格规则

2. 房间展示规则组:
   - 区域显示规则
   - 类型筛选规则
   - 状态判断规则

### 2.3 流程设计（待实现）
1. 数据获取流程:
   - 区域信息获取
   - 房间类型信息获取
   - 价格方案信息获取
   - 节假日信息获取

2. 规则应用流程:
   - 规则上下文构建
   - 规则执行
   - 结果处理

## 3. 待办事项
- [ ] 设计并实现YAML格式的业务规则
- [ ] 创建RoomOpenViewProcess流程类
- [ ] 实现规则引擎集成
- [ ] 编写单元测试
- [ ] 性能测试和优化

## 4. 开发日志
### 2024-01-22
- 完成现状分析
- 制定重构计划
- 创建开发笔记

### 2024-01-22 (规则配置)
- 创建YAML格式的业务规则配置
- 规则组包含：
  1. 节假日价格规则 (20%上浮)
  2. 会员折扣规则 (9折优惠)
  3. 高峰时段规则 (10%上浮)
  4. 区域显示规则
  5. 价格方案筛选规则

### 2024-01-22 (流程实现)
- 创建RoomOpenViewProcess流程类
- 实现主要功能：
  1. 数据获取流程
     - 区域信息获取
     - 房间类型信息获取
     - 价格方案信息获取
     - 节假日信息获取
  2. 规则引擎集成
     - 构建规则上下文
     - 执行规则组
     - 处理规则执行结果
  3. 错误处理
     - 统一的错误处理机制
     - 流程状态管理
     - 详细的错误日志

### 2024-01-22 (Controller实现)
- 创建V2版本的RoomController
- 实现主要功能：
  1. 参数绑定和基础校验
  2. 调用ProcessEngine执行业务流程
  3. 统一的错误处理和响应
  4. 完善的日志记录

- 配置V2版本路由
  - 路径: `/api/v2/room/open-view`
  - 方法: POST
  - 版本隔离，便于后续升级

### 2024-01-22 (路由统一化)
- 统一路由注册方式：
  1. 采用Router结构体模式
  2. 支持依赖注入
  3. 提供Setup方法配置路由
  4. 保留RegisterRoutes函数向后兼容

- 统一API路径规范：
  - 修改为 `/api/v2/room/open-view`
  - 与其他模块保持一致的版本命名

- 改进依赖注入：
  1. 通过Router构造函数注入所有依赖
  2. 在Router中统一管理流程和控制器的创建
  3. 使用别名导入避免包名冲突

### 测试方法 (更新)
```bash
# 测试API (更新后的路径)
curl -X POST http://localhost:8080/api/v2/room/open-view \
  -H "Content-Type: application/json" \
  -d '{
    "venueId": "test_venue_001",
    "typeId": "test_type_001",
    "areaId": "test_area_001"
  }'
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "areaVO": {
      "id": "test_area_001",
      "name": "测试区域",
      "capacity": 100,
      "isDisplayed": true
    },
    "roomTypeVO": {
      "id": "test_type_001",
      "name": "测试房型",
      "consumptionMode": "time"
    },
    "pricePlanVOs": [...],
    "holidayVOs": [...],
    "currentTime": 1234567890
  }
}
```

### 重构总结
1. 架构改进：
   - 引入规则引擎，实现业务规则的配置化
   - 使用ProcessEngine统一处理业务流程
   - 清晰的分层结构，职责明确

2. 代码质量：
   - 统一的错误处理机制
   - 完善的日志记录
   - 规范的注释和文档

3. 可维护性：
   - 业务规则可配置
   - 流程步骤清晰
   - 版本控制友好

## 5. 下一步计划
1. 完善错误处理
   - 添加详细的错误码
   - 规范错误信息格式
   - 完善错误日志记录

2. 添加接口文档
   - 使用Swagger注解
   - 补充请求/响应示例
   - 添加错误码说明

3. 性能优化
   - 添加缓存机制
   - 优化数据库查询
   - 监控接口响应时间 