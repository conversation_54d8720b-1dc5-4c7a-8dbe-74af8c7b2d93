# 领域服务开发指导文档

## 一、概述

本文档主要描述order_open业务所需的领域服务开发指南，包括服务路径、接口定义、依赖关系等。

## 二、领域服务目录结构

### 2.1 交易记录领域 (traderecord)
```erp_client/domain/traderecord/
├── base/
│   ├── model/
│   │   ├── order.go         # 订单基础模型
│   │   ├── order_product.go # 订单商品模型
│   │   ├── order_room_plan.go # 订单房间计划模型
│   │   └── session.go       # 场次模型
│   ├── repository/
│   │   ├── order_repository.go
│   │   ├── order_product_repository.go
│   │   ├── order_room_plan_repository.go
│   │   └── session_repository.go
│   └── service/
│       ├── order_service.go      # 订单服务接口
│       ├── order_service_impl.go # 订单服务实现
│       ├── session_service.go    # 场次服务接口
│       ├── session_service_impl.go # 场次服务实现
│       ├── order_fee_calc_service.go # 费用计算服务接口
│       └── order_fee_calc_service_impl.go # 费用计算服务实现
```

### 2.2 配置领域 (config)
```
erp_client/domain/config/
├── business/
│   ├── priceplan/          # 价格方案配置
│   │   ├── model/
│   │   │   └── price_plan.go
│   │   ├── repository/
│   │   │   └── price_plan_repository.go
│   │   └── service/
│   │       ├── price_plan_service.go
│   │       └── price_plan_service_impl.go
│   └── booking/           # 预订配置
│       ├── model/
│       │   └── booking.go
│       ├── repository/
│       │   └── booking_repository.go
│       └── service/
│           ├── booking_service.go
│           └── booking_service_impl.go
```

### 2.3 值对象领域 (valueobject)
```
erp_client/domain/valueobject/
└── business/
    └── room/              # 房间值对象
        ├── model/
        │   └── room.go
        ├── repository/
        │   └── room_repository.go
        └── service/
            ├── room_service.go
            ├── room_service_impl.go
            ├── room_timer_service.go
            └── room_timer_service_impl.go
```

## 三、领域服务接口定义

### 3.1 交易记录领域服务

#### OrderService (订单服务)
**路径**: `domain/traderecord/base/service/order_service.go`
```go
type OrderService interface {
    // 创建开台订单
    OrderOpen(ctx context.Context, session *po.Session, orders *[]po.Order, 
        orderProducts *[]po.OrderProduct, orderPricePlan *po.OrderPricePlan, 
        orderRoomPlans *[]po.OrderRoomPlan, room *po.Room) bool
    
    // 创建订单商品
    CreateOrderProducts(ctx context.Context, orderProducts *[]po.OrderProduct) error
    
    // 创建订单房间计划
    CreateOrderRoomPlans(ctx context.Context, orderRoomPlans *[]po.OrderRoomPlan) error
    
    // 查找所有订单
    FindAllOrder(ctx context.Context, query *req.QueryOrderReqDto) (*[]po.Order, error)
}
```

#### SessionService (场次服务)
**路径**: `domain/traderecord/base/service/session_service.go`
```go
type SessionService interface {
    // 创建场次
    CreateSession(ctx context.Context, session *po.Session) error
    
    // 更新场次状态
    UpdateSessionStatus(ctx context.Context, sessionId string, status string) error
}
```

#### OrderFeeCalcService (费用计算服务)
**路径**: `domain/traderecord/base/service/order_fee_calc_service.go`
```go
type OrderFeeCalcService interface {
    // 计算订单费用
    CalculateOrderFee(ctx context.Context, venueId string, sessionId string, toAddOrders *[]po.Order) (
        totalAmount, unpaidAmount, supermarketAmount, roomAmount int64, lastMinimumCharge int64)
    
    // 计算商品费用
    CalculateProductFee(ctx context.Context, products *[]po.OrderProduct) (
        totalAmount, originalAmount int64, err error)
    
    // 验证最低消费
    ValidateMinimumCharge(ctx context.Context, orderAmount int64, minimumCharge int64) error
}
```

### 3.2 配置领域服务

#### PricePlanService (价格方案服务)
**路径**: `domain/config/business/priceplan/service/price_plan_service.go`
```go
type PricePlanService interface {
    // 根据ID查找价格方案
    FindPricePlanById(ctx context.Context, id string) (*po.PricePlan, error)
    
    // 计算价格
    CalculatePrice(ctx context.Context, priceplan *po.PricePlan, params map[string]interface{}) (*dto.PriceResult, error)
    
    // 验证订单价格方案
    ValidateOrderPricePlan(ctx context.Context, orderPricePlan *po.OrderPricePlan, reqDto *req.AddOrderOpenReqDto) error
}
```

#### BookingService (预订服务)
**路径**: `domain/config/business/booking/service/booking_service.go`
```go
type BookingService interface {
    // 更新预订状态
    UpdateBookingPartial(ctx context.Context, booking *po.Booking) error
    
    // 验证预订信息
    ValidateBooking(ctx context.Context, bookingId string) error
}
```

### 3.3 值对象领域服务

#### RoomService (房间服务)
**路径**: `domain/valueobject/business/room/service/room_service.go`
```go
type RoomService interface {
    // 根据ID查找房间
    FindRoomById(ctx context.Context, id string) (*po.Room, error)
    
    // 更新房间状态
    UpdateRoomStatus(ctx context.Context, room *po.Room) error
    
    // 验证房间状态
    ValidateRoomStatus(ctx context.Context, roomId string, expectedStatus []string) error
}
```

#### RoomTimerService (房间定时器服务)
**路径**: `domain/valueobject/business/room/service/room_timer_service.go`
```go
type RoomTimerService interface {
    // 设置房间定时器
    SetRoomTimer(ctx context.Context, venueId string, roomId string, sessionId string, endTime int64)
    
    // 取消房间定时器
    CancelRoomTimer(ctx context.Context, roomId string) error
}
```

## 四、服务依赖关系

### 4.1 服务间依赖
```mermaid
graph TD
    A[OrderService] --> B[OrderFeeCalcService]
    A --> C[SessionService]
    D[PricePlanService] --> B
```

### 4.2 服务与仓储依赖
```mermaid
graph TD
    A[OrderService] --> B[OrderRepository]
    A --> C[OrderProductRepository]
    A --> D[OrderRoomPlanRepository]
    E[SessionService] --> F[SessionRepository]
    G[PricePlanService] --> H[PricePlanRepository]
    I[BookingService] --> J[BookingRepository]
    K[RoomService] --> L[RoomRepository]
```

## 五、开发优先级建议

1. 基础服务实现 (已有部分功能)
   - RoomService
   - OrderService
   - PricePlanService
   - BookingService

2. 核心业务服务实现
   - OrderFeeCalcService
   - SessionService

3. 辅助服务实现
   - RoomTimerService

## 六、注意事项

1. 服务接口设计要保持稳定，避免频繁变更
2. 实现类要注意并发安全
3. 错误处理要统一，使用统一的错误码
4. 日志记录要完善，便于问题排查
5. 单元测试覆盖率要达到80%以上

## 七、开发计划

1. 第一阶段：基础服务开发 (3天)
   - 完成基础服务的接口实现
   - 编写单元测试
   - 进行代码审查

2. 第二阶段：核心业务服务开发 (4天)
   - 完成核心业务服务的实现
   - 编写单元测试
   - 进行代码审查

3. 第三阶段：辅助服务开发 (2天)
   - 完成辅助服务的实现
   - 编写单元测试
   - 进行代码审查

4. 第四阶段：集成测试 (3天)
   - 编写集成测试用例
   - 进行服务集成测试
   - 修复发现的问题

## 八、后续优化建议

1. 性能优化
   - 添加缓存层
   - 优化数据库查询
   - 添加性能监控

2. 可靠性提升
   - 添加熔断机制
   - 完善重试机制
   - 增加监控告警

3. 可维护性提升
   - 完善文档
   - 规范代码风格
   - 添加更多单元测试

## 九、开发进度

### 9.1 配置文件开发 (已完成)
1. 规则配置文件
   - 创建 `erp_client/config/rules/order_open_rules.yaml`
   - 实现参数验证规则
   - 实现房间状态检查规则
   - 实现时间检查规则
   - 实现金额检查规则

2. 流程配置文件
   - 创建 `erp_client/config/processes/order_open_process.yaml`
   - 定义输入输出参数
   - 配置上下文对象
   - 实现完整流程步骤
     - 规则验证
     - 获取房间信息
     - 生成订单信息
     - 创建订单
     - 更新预订状态
     - 设置房间定时器

### 9.2 待开发内容
1. 领域服务实现
   - OrderService 实现类
   - SessionService 实现类
   - OrderFeeCalcService 实现类
   - PricePlanService 实现类
   - BookingService 实现类
   - RoomService 实现类
   - RoomTimerService 实现类

2. 单元测试开发
   - 规则引擎测试用例
   - 流程引擎测试用例
   - 各服务实现类的测试用例

3. 集成测试开发
   - 端到端测试用例
   - 性能测试用例 