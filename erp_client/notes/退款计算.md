# 第一章 VO定义

## 1.1 SessionVO 场次信息值对象
```go
type SessionVO struct {
	Id            string `json:"id"`            // 唯一ID
	VenueId       string `json:"venueId"`       // 门店ID
	RoomId        string `json:"roomId"`        // 房间ID
	EmployeeId    string `json:"employeeId"`    // 员工ID
	SessionId     string `json:"sessionId"`     // 开台ID
	StartTime     int64  `json:"startTime"`     // 开台时间
	EndTime       int64  `json:"endTime"`       // 预期结束时间
	CloseTime     int64  `json:"closeTime"`     // 实际关房时间
	Duration      int64  `json:"duration"`      // 废弃-客户端计算-使用时长
	Status        string `json:"status"`        // 状态 开台：opening， 关台：ending
	PayStatus     string `json:"payStatus"`     // 支付状态
	IsTimeConsume bool   `json:"isTimeConsume"` // 是否是计时消费

	MinConsume     int64 `json:"minConsume"`     // 最低消费
	RoomFee        int64 `json:"roomFee"`        // 包厢费用
	SupermarketFee int64 `json:"supermarketFee"` // 超市费用
	TotalFee       int64 `json:"totalFee"`       // 总计费用
	UnpaidAmount   int64 `json:"unpaidAmount"`   // 未付金额
	PaidAmount     int64 `json:"paidAmount"`     // 已付金额
	PrePayBalance  int64 `json:"prePayBalance"`  // 预付余额

	Tag  string `json:"tag"`  // tag: lock/birthday/changyin
	Info string `json:"info"` // 备注

	OrderSource        string `json:"orderSource"`        // 订单来源
	CustomerSource     string `json:"customerSource"`     // 客户来源
	CustomerTag        string `json:"customerTag"`        // 客群标签
	AgentPerson        string `json:"agentPerson"`        // 代定人
	DutyPerson         string `json:"dutyPerson"`         // 轮房人
	RankNumber         string `json:"rankNumber"`         // 排位号码
	IsOpenTableSettled bool   `json:"isOpenTableSettled"` // 是否开台立结

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

}
```

## 1.2 OrderVO 订单信息值对象
```go
type OrderVO struct {
	Id            string `json:"id"`            // ID
	VenueId       string `json:"venueId"`       // 门店ID
	RoomId        string `json:"roomId"`        // 房间ID
	EmployeeId    string `json:"employeeId"`    // 员工ID
	MemberId      string `json:"memberId"`      // 会员ID
	SessionId     string `json:"sessionId"`     // 场次ID
	OrderNo       string `json:"orderNo"`       // 订单编号
	POrderNo      string `json:"pOrderNo"`      // 父订单编号-退款时原订单
	MinimumCharge int64  `json:"minimumCharge"` // 最低消费金额

	Type      string `json:"type"`      // 订单类型 roomplan/product
	Direction string `json:"direction"` // 方向 normal:下单、refund:退款
	MarkType  string `json:"markType"`  // 标记类型 normal:正常、cancel:取消  转台时旧订单被标记为cancel，其他均正常
	Status    string `json:"status"`    // 订单状态  unpaid:未结、paid:已结
	Tag       string `json:"tag"`       // 标签
	Mark      string `json:"mark"`      // 备注

	ConfigRoomMemberDiscountType    int64 `json:"configRoomMemberDiscountType"`    // 配置-包厢价格会员优惠方式 0:无，1：会员价，2：会员折扣
	ConfigProductMemberDiscountType int64 `json:"configProductMemberDiscountType"` // 配置-商品价格会员优惠方式 0:无，1：会员价，2：会员折扣

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号

}
```

## 1.3 OrderProductVO 订单产品值对象
```go
type OrderProductVO struct {
	Id                 string `json:"id"`                 // ID
	VenueId            string `json:"venueId"`            // 门店ID
	RoomId             string `json:"roomId"`             // 房间ID
	EmployeeId         string `json:"employeeId"`         // 员工ID
	MemberId           string `json:"memberId"`           // 会员ID
	SessionId          string `json:"sessionId"`          // 场次ID
	OrderNo            string `json:"orderNo"`            // 订单ID
	PId                string `json:"pId"`                // 退款对应的原始OrderProduct.Id
	PackageId          string `json:"packageId"`          // 套餐ID
	PackageProductInfo string `json:"packageProductInfo"` // 套餐商品选择信息，json格式:[{"id": "xxx1", "count": 2}, {"id": "xxx2", "count": 3}]
	ProductId          string `json:"productId"`          // 产品ID
	ProductName        string `json:"productName"`        // 产品名称
	Flavors            string `json:"flavors"`            // 口味
	Unit               string `json:"unit"`               // 单位
	Quantity           int64  `json:"quantity"`           // 数量
	OriginalPrice      int64  `json:"originalPrice"`      // 原价
	MemberPrice        int64  `json:"memberPrice"`        // 真实原价-会员价格-白金-钻石

	ProductDiscountable bool `json:"productDiscountable"` // 是否-商品折扣
	MemberDiscountable  bool `json:"memberDiscountable"`  // 是否-会员折扣
	Giftable            bool `json:"giftable"`            // 是否-可赠送
	Freeable            bool `json:"freeable"`            // 是否-可免单
	IsFreeDrinking      bool `json:"isFreeDrinking"`      // 是否畅饮
	IsMultiProductGift  bool `json:"isMultiProductGift"`  // 是否多商品赠送
	IsPriceDiff         bool `json:"isPriceDiff"`         // 是否是补差价订单
	IsGift              bool `json:"isGift"`              // 是否-已赠送
	IsFree              bool `json:"isFree"`              // 是否-已免单

	PayAmount                int64 `json:"payAmount"`                // 总金额 - 回写
	MemberDiscount           int64 `json:"memberDiscount"`           // 会员折扣
	OrderProductDiscount     int64 `json:"orderProductDiscount"`     // 点单时商品折扣-下单时-只写一次
	PayProductDiscount       int64 `json:"payProductDiscount"`       // 支付时商品折扣 - 回写
	PayProductDiscountAmount int64 `json:"payProductDiscountAmount"` // 支付时商品减免 - 回写

	Mark         string `json:"mark"`         // 产品显示备注
	InPackageTag string `json:"inPackageTag"` // 套内商品标签
	Src          string `json:"src"`          // 套餐来源

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号
}
```

## 1.4 OrderRoomPlanVO 价格方案值对象
```go
type OrderRoomPlanVO struct {
	Id            string `json:"id"`            // ID
	VenueId       string `json:"venueId"`       // 门店ID
	RoomId        string `json:"roomId"`        // 房间ID
	SessionId     string `json:"sessionId"`     // 场次ID
	EmployeeId    string `json:"employeeId"`    // 员工ID
	MemberId      string `json:"memberId"`      // 会员ID
	OrderNo       string `json:"orderNo"`       // 订单ID
	PId           string `json:"pId"`           // 退款对应的原始OrderRoomPlan.Id
	RoomName      string `json:"roomName"`      // 房间名称
	PricePlanId   string `json:"pricePlanId"`   // 方案id
	PricePlanName string `json:"pricePlanName"` // 价格方案名称
	StartTime     int64  `json:"startTime"`     // 开始时间
	EndTime       int64  `json:"endTime"`       // 结束时间
	Duration      int    `json:"duration"`      // 买钟时长 单位：分钟 == BuyMinute

	SelectedAreaId     string `json:"selectedAreaId"`     // --选择的计费方式-套餐区域id
	SelectedRoomTypeId string `json:"selectedRoomTypeId"` // --选择的计费方式-房间类型id
	ConsumptionMode    string `json:"consumptionMode"`    // --消费模式消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	TimeChargeMode     string `json:"timeChargeMode"`     // --买钟-类型 买钟价格类型 基础价格、区域价格、节假日价格
	TimeChargeType     string `json:"timeChargeType"`     // --买钟-类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	IsTimeConsume      bool   `json:"isTimeConsume"`      // 是否是计时消费

	MemberDiscountable bool  `json:"memberDiscountable"` // 是否-会员折扣
	IsGift             bool  `json:"isGift"`             // 是否是赠送
	OriginalPayAmount  int64 `json:"originalPayAmount"`  // 原支付金额
	MinimumCharge      int64 `json:"minimumCharge"`      // 最低消费金额
	MemberPrice        int64 `json:"memberPrice"`        // 真实原价-会员价格-白金-钻石
	MemberDiscount     int64 `json:"memberDiscount"`     // 会员折扣
	IsPriceDiff        bool  `json:"isPriceDiff"`        // 是否是补差价订单
	IsFree             bool  `json:"isFree"`             // 是否-已免单

	PayAmount             int64 `json:"payAmount"`             // 房费
	PayRoomDiscount       int64 `json:"payRoomDiscount"`       // 折扣
	PayRoomDiscountAmount int64 `json:"payRoomDiscountAmount"` // 折扣减免

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号
}
```

## 1.5 PayBillVO 付款单值对象
```go
type PayBillVO struct {
	Id         string `json:"id"`         // ID
	VenueId    string `json:"venueId"`    // 门店id
	RoomId     string `json:"roomId"`     // 房间ID
	EmployeeId string `json:"employeeId"` // 员工ID-交班用
	SessionId  string `json:"sessionId"`  // 场次ID
	BillId     string `json:"billId"`     // 收款单号
	BillPid    string `json:"billPid"`    // 退款单对应的收款单号

	OriginalFee  int64 `json:"originalFee"`  // 原始金额 优惠金额 = 原始金额 - 应付金额
	ShouldFee    int64 `json:"shouldFee"`    // 应付金额 = 实付金额 + 抹零金额 + 找零金额
	TotalFee     int64 `json:"totalFee"`     // 实付金额 金额对应sum(payrecords.totalfee)
	ZeroFee      int64 `json:"zeroFee"`      // 抹零金额
	CreditAmount int64 `json:"creditAmount"` // 挂账金额
	ChangeAmount int64 `json:"changeAmount"` // 现金-找零金额

	ProductDiscount       int64  `json:"productDiscount"`       // 商品折扣
	RoomDiscount          int64  `json:"roomDiscount"`          // 房费折扣
	IsFree                bool   `json:"isFree"`                // 是否免单
	ProductDiscountAmount int64  `json:"productDiscountAmount"` // 商品减免
	RoomDiscountAmount    int64  `json:"roomDiscountAmount"`    // 房费减免
	DiscountType          int64  `json:"discountType"`          // 对超过低消部分打折还是全部打折
	ForceMinimumCharge    bool   `json:"forceMinimumCharge"`    // 是否强制满低消
	RefundWay             string `json:"refundWay"`             // 退款方式

	Direction  string `json:"direction"`  // 方向
	Status     string `json:"status"`     // 状态
	IsBack     bool   `json:"isBack"`     // 是否还原 0: 正常 1: 账单还原
	FinishTime int64  `json:"finishTime"` // 完成时间
	Info       string `json:"info"`       // 备注
	BillDate   int64  `json:"billDate"`   // 账单日期

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号
}
```

## 1.6 PayRecordVO 支付记录对象
```go
type PayRecordVO struct {
	Id         string `json:"id"`         // ID
	VenueId    string `json:"venueId"`    // 门店id
	RoomId     string `json:"roomId"`     // 房间ID
	EmployeeId string `json:"employeeId"` // 支付员工ID-冗余-用于统计
	SessionId  string `json:"sessionId"`  // 场次ID

	BillId       string `json:"billId"`       // payBill.BillId
	PayId        string `json:"payId"`        // 支付单ID
	PayPid       string `json:"payPid"`       // 只有三方支付时此处才有值
	ThirdOrderId string `json:"thirdOrderId"` // 第三方支付单号
	TotalFee     int64  `json:"totalFee"`     // 总金额-实际支付金额
	Status       string `json:"status"`       // 状态 success/refund
	PayType      string `json:"payType"`      // 支付类型-微信 支付宝 找零 挂账
	ProductName  string `json:"productName"`  // 商品名称
	Info         string `json:"info"`         // 备注
	FinishTime   int64  `json:"finishTime"`   // 完成时间
	BillDate     int64  `json:"billDate"`     // 账单日期-冗余-用于统计
	BQROneCode   string `json:"bQROneCode"` // BShowQR支付方式的BQROneCode

	Ctime   int64 `json:"ctime"`   // 创建时间
	Utime   int64 `json:"utime"`   // 更新时间
	State   int   `json:"state"`   // 状态
	Version int   `json:"version"` // 版本号
}

```

## 1.7 OrderAndPayVO 订单和支付单值对象
```go
type OrderAndPayVO struct {
	Id        string `json:"id"`        // ID
	OrderNo   string `json:"orderNo"`   // 单号
	SessionId string `json:"sessionId"` // 场次ID
	BillId    string `json:"billId"`    // 收款单号
	Ctime     int64  `json:"ctime"`     // 创建时间
	Utime     int64  `json:"utime"`     // 更新时间
	State     int    `json:"state"`     // 状态
	Version   int    `json:"version"`   // 版本号

}
```

## 1.8 重要说明

以下是各 VO 在业务流程中的关系和作用：

1.  **核心场次 (SessionVO):**
    *   代表一次完整的顾客开台到结束的过程。
    *   每次开台生成一条唯一的 `SessionVO` 记录。

2.  **订单记录 (OrderVO, OrderProductVO, OrderRoomPlanVO):**
    *   在一个场次 (`SessionVO`) 中，顾客可能会多次下单（包括首次开台、点单、续房等），每次下单行为都会生成一条 `OrderVO` 记录。
    *   `OrderVO` 通过 `OrderNo` 字段关联其详细内容：
        *   如果是商品订单 (`Type="product"`)，关联一条或多条 `OrderProductVO` 记录。
        *   如果是房费计划订单 (`Type="roomplan"`)，关联一条 `OrderRoomPlanVO` 记录。 **注意：** 如果顾客中途续房（续钟或续买断），会产生新的 `OrderVO` (Type="roomplan") 和对应的 `OrderRoomPlanVO`，因此一个 `SessionVO` 下可能存在多条 `OrderRoomPlanVO` 记录，但每条都对应不同的 `OrderVO`。
    *   `OrderVO` 的 `Direction` 字段区分是正常下单 (`normal`) 还是退款 (`refund`)。退款订单会通过 `POrderNo` 关联原始订单。
    *   `OrderProductVO` 和 `OrderRoomPlanVO` 在退款时，也会通过 `PId` 字段关联原始的被退款项。

3.  **结算付款 (PayBillVO, PayRecordVO):**
    *   当进行结账操作（无论是正常收款还是退款操作）时，会生成一条 `PayBillVO` 记录。
    *   `PayBillVO` 汇总了该次结算的总金额、应付金额、实付金额、优惠、抹零等信息。`Direction` 字段区分收款和退款。退款时通过 `BillPid` 关联原始收款单。
    *   一次结算 (`PayBillVO`) 可能涉及多种支付方式（如微信、支付宝、现金），每种方式的支付详情记录在一条 `PayRecordVO` 中。
    *   `PayRecordVO` 通过 `BillId` 字段与对应的 `PayBillVO` 关联。

4.  **订单与付款关联 (OrderAndPayVO):**
    *   此表用于明确记录哪些订单 (`OrderVO`) 是由哪次付款/结算 (`PayBillVO`) 处理的。
    *   每次结账完成后，会为涉及的 `OrderVO` 和生成的 `PayBillVO` 创建关联记录。
    *   通过 `OrderNo` 关联 `OrderVO`，通过 `BillId` 关联 `PayBillVO`。

5.  **统一场次标识 (SessionId):**
    *   以上所有核心业务 VO (`OrderVO`, `OrderProductVO`, `OrderRoomPlanVO`, `PayBillVO`, `PayRecordVO`, `OrderAndPayVO`) 都包含 `SessionId` 字段。
    *   `SessionId` 是将所有这些分散的记录关联回同一个顾客场次 (`SessionVO`) 的关键。

# 第二章 退款需求
## 2.1 退款请求reqDto定义
```go
type V3QueryOrderRefundReqDto struct {
	// 场次ID
	SessionId *string `json:"sessionId"`

	// 所属门店ID
	VenueId *string `json:"venueId"`

	// 关联的房间ID
	RoomId *string `json:"roomId"`

	// 退款金额
	RefundAmount *int64 `json:"refundAmount"`

	// 退款商品
	OrderProductVOs *[]vo.OrderProductVO `json:"orderProductVOs"`

	// 退款类型 back: 原路径返回 cash: 现金退款
	RefundWayType *string `json:"refundWayType"`

	// 员工ID
	EmployeeId *string `json:"employeeId"`
}

type OrderProductVO struct {
	Id                 string `json:"id"`                 // ID
	// ... 其他字段
	Quantity           int64  `json:"quantity"`           // 数量/退款数量
	// ... 其他字段
}
// 支付类型退款顺序
var REFUND_ORDER_WIGHT = map[string]int{
	PAY_TYPE_RECORD_CASH:    1,
	PAY_TYPE_RECORD_WECHAT:  2,
	PAY_TYPE_RECORD_ALIPAY:  3,
	PAY_TYPE_RECORD_BANK:    4,
	PAY_TYPE_LESHUA_BSHOWQR: 5,
	PAY_TYPE_MEMBER_CARD:    6,
}

// 支付类型
PAY_TYPE_RECORD_CASH    = "cash"          // 现金
PAY_TYPE_RECORD_WECHAT  = "wechat_record" // 微信-记账
PAY_TYPE_RECORD_ALIPAY  = "alipay_record" // 支付宝-记账
PAY_TYPE_RECORD_BANK    = "bank_record"   // 银联-记账
PAY_TYPE_LESHUA_BSHOWQR = "bShowQR"       // 乐刷付款码
```

## 2.2 退款约定
1. 入参  
	- 接口传入了哪些商品退了多少个
	- 退款总金额
	- 校验 商品是否仍可退，退款金额计算是否正确
2. 退款方式（RefundWayType）
	- 现金退款
		- 需要将退款的总金额分拆到原收款单的付款记录中
		- 金额分配优先顺序: REFUND_ORDER_WIGHT
		- 需要生成一笔新的收款单，类型为退款
		- 需要生成新收款单对应的支付记录：退款的支付记录，支付记录PayPid指向分拆的付款记录
		- 直接将退款记录更新为已支付
	- 原路径返回
		- 按原收款单对应的支付记录的退款方式进行退款
		- 金额分配优先顺序: REFUND_ORDER_WIGHT
		- 需要生成一笔新的收款单，类型为退款
		- 需要生成新收款单对应的支付记录：退款的支付记录，支付记录PayPid指向分拆的付款记录
		- 如果为第三方支付（乐刷支付）需要经记录直接置为未付，其他记账方式为已付，并发起第三方退款
	- 订单的处理
		- 新增订单（orderVO），类型为退款，POrderNo指向退款订单
		- 新增退款商品(OrderProductVO)，通过OrderNo指向新增退款订单
		- 房费不可退，暂不处理
	- 订单和收款单的关联
		- 将新增的订单和收款单通过OrderAndPayVO关联 (Order.OrderNo, PayBillVO.BillId)

# 第三章 退款实施

## 3.1 退款伪代码

## 3.2 更新后的退款系统伪代码（基于上下文对象）

### 1. 上下文结构体定义

```go
// 退款流程上下文 - 贯穿整个退款流程的数据容器
type RefundProcessContext struct {
    // 输入参数
    RefundRequest *V3QueryOrderRefundReqDto
    
    // 场次原始数据
    SessionInfo          *SessionVO                   // 当前场次信息
    AllOrders            []OrderVO                    // 场次所有订单
    AllOrderProducts     []OrderProductVO             // 场次所有订单商品
    AllOrderRoomPlans    []OrderRoomPlanVO            // 场次所有房费计划
    AllPayBills          []PayBillVO                  // 场次所有支付单
    AllPayRecords        []PayRecordVO                // 场次所有支付记录
    AllOrderAndPays      []OrderAndPayVO              // 场次所有订单支付关联
    
    // 退款处理相关数据
    ProductGroups        map[string][]OrderProductVO  // 按订单分组的退款商品
    OriginalOrders       map[string]OrderVO           // 原始订单信息
    OriginalPayBills     []PayBillVO                  // 原始支付单
    OriginalPayRecords   map[string][]PayRecordVO     // 原始支付记录
    
    // 待新增的数据对象
    NewRefundOrders      []OrderVO                    // 待新增的退款订单
    NewRefundProducts    []OrderProductVO             // 待新增的退款商品
    NewRefundPayBill     *PayBillVO                   // 待新增的退款支付单
    NewRefundPayRecords  []PayRecordVO                // 待新增的退款支付记录
    NewOrderAndPays      []OrderAndPayVO              // 待新增的订单支付关联
    
    // 处理结果映射
    RefundOrders           map[string]string          // 每个原始订单对应的退款订单号
    RefundBillId           string                     // 退款收款单号
    AmountAllocations      map[string]int64           // 退款金额分配结果
    ThirdPartyPaymentStatus map[string]string         // 第三方支付状态记录
    
    // 错误跟踪
    Errors                 map[string]error           // 每个步骤可能出现的错误
    
    // 计算结果
    CalculatedRefundAmount int64                      // 计算得出的应退金额
    
    // 处理状态
    ProcessStep            string                     // 当前处理步骤
    IsTransactionStarted   bool                       // 事务是否已开始
    TransactionObj         any                        // 事务对象
}
```

### 2. 参数验证服务（ValidatorService）

```go
// 校验退款请求参数合法性
func ValidateRefundRequest(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "ValidateRefundRequest"
    // 检查必要字段是否为空：SessionId, VenueId, RoomId, EmployeeId, RefundAmount, RefundWayType
    // 验证退款类型是否为支持的类型："cash" 或 "back"
    // 检查退款商品列表不为空
}

// 校验商品是否可退
func ValidateProductRefundable(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "ValidateProductRefundable"
    
    // 利用ctx中的场次全量数据
    // 检查每个商品是否存在
    // 查询已退款数量（从ctx.AllOrderProducts中筛选Direction为"refund"的记录）
    // 检查每个商品的剩余可退数量是否足够
}

// 校验退款金额是否正确
func ValidateRefundAmount(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "ValidateRefundAmount"
    
    // 计算所有退款商品应退金额总和，使用ctx中的场次全量数据计算准确金额
    // 保存计算结果到ctx.CalculatedRefundAmount
    // 与请求中传入的退款总金额比较是否一致
}
```

### 3. 退款数据准备服务（RefundDataService）

```go
// 获取当前场次全部信息
func LoadSessionFullInfo(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "LoadSessionFullInfo"
    
    // 从请求中获取venueId和sessionId
    venueId := *ctx.RefundRequest.VenueId
    sessionId := *ctx.RefundRequest.SessionId
    
    // 查询当前场次信息
    // 设置ctx.SessionInfo
    
    // 查询场次所有订单
    // 设置ctx.AllOrders
    
    // 查询场次所有订单商品
    // 设置ctx.AllOrderProducts
    
    // 查询场次所有房费计划
    // 设置ctx.AllOrderRoomPlans
    
    // 查询场次所有支付单
    // 设置ctx.AllPayBills
    
    // 查询场次所有支付记录
    // 设置ctx.AllPayRecords
    
    // 查询场次所有订单支付关联
    // 设置ctx.AllOrderAndPays
    
    return nil
}

// 准备退款所需原始数据
func PrepareOriginalData(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "PrepareOriginalData"
    
    // 从已加载的全量数据中筛选出与退款商品相关的原始订单
    // 构建ctx.ProductGroups和ctx.OriginalOrders
    
    // 找出与原始订单相关的支付单
    // 设置ctx.OriginalPayBills
    
    // 找出与原始支付单相关的支付记录
    // 设置ctx.OriginalPayRecords
    
    return nil
}
```

### 4. 订单服务（OrderService）

```go
// 创建退款订单
func CreateRefundOrder(ctx *RefundProcessContext, orderNo string, products []OrderProductVO) (string, error) {
    // 获取原始订单
    originalOrder := ctx.OriginalOrders[orderNo]
    
    // 创建新的退款订单记录(OrderVO)
    // 设置Direction为"refund"
    // 设置POrderNo关联到原始订单
    // 设置Type为"product"
    // 设置状态为"unpaid"
    
    // 添加到ctx.NewRefundOrders列表
    
    // 返回新创建的订单号
}

// 创建退款订单商品
func CreateRefundOrderProducts(ctx *RefundProcessContext, refundOrderNo string, products []OrderProductVO) error {
    // 遍历需退款的商品
    // 为每个退款商品创建新的OrderProductVO记录
    // 设置OrderNo关联新创建的退款订单
    // 设置PId关联原始商品记录
    // 设置退款数量
    
    // 添加到ctx.NewRefundProducts列表
}

// 创建所有退款订单及商品
func CreateAllRefundOrders(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "CreateAllRefundOrders"
    ctx.RefundOrders = make(map[string]string)
    ctx.NewRefundOrders = make([]OrderVO, 0)
    ctx.NewRefundProducts = make([]OrderProductVO, 0)
    
    // 遍历ctx.ProductGroups中的所有订单和商品组
    for orderNo, products := range ctx.ProductGroups {
        // 为每个原订单创建对应的退款订单
        refundOrderNo, err := CreateRefundOrder(ctx, orderNo, products)
        if err != nil {
            ctx.Errors["CreateRefundOrder_"+orderNo] = err
            return err
        }
        
        // 记录原订单与退款订单的对应关系
        ctx.RefundOrders[orderNo] = refundOrderNo
        
        // 创建退款商品记录
        if err := CreateRefundOrderProducts(ctx, refundOrderNo, products); err != nil {
            ctx.Errors["CreateRefundOrderProducts_"+refundOrderNo] = err
            return err
        }
    }
    
    return nil
}
```

### 5. 支付账单服务（PayBillService）

```go
// 创建退款收款单
func CreateRefundPayBill(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "CreateRefundPayBill"
    
    // 选择一个原始收款单作为模板
    originalPayBill := ctx.OriginalPayBills[0]
    
    // 创建新的退款收款单(PayBillVO)
    // 设置Direction为"refund"
    // 设置BillPid关联到原始收款单
    // 设置退款金额为负数
    // 设置退款方式
    // 设置退款状态初始为"unpaid"
    
    // 保存到ctx.NewRefundPayBill
    // 保存收款单号到ctx.RefundBillId
    
    return nil
}
```

### 6. 支付记录服务（PayRecordService）

```go
// 分配退款金额到支付记录
func AllocateRefundAmount(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "AllocateRefundAmount"
    
    // 从上下文中收集所有原始支付记录
    var allPayRecords []PayRecordVO
    for _, records := range ctx.OriginalPayRecords {
        allPayRecords = append(allPayRecords, records...)
    }
    
    // 按REFUND_ORDER_WIGHT中定义的优先级对支付记录进行排序
    sortedPayRecords := SortPayRecordsByPriority(allPayRecords)
    
    // 分配退款金额
    refundAmount := *ctx.RefundRequest.RefundAmount
    ctx.AmountAllocations = make(map[string]int64)
    
    // 按优先级依次将退款金额分配到各支付记录
    // 如果当前支付记录金额不足，则分配全部可用金额，剩余金额继续分配给下一个支付记录
    // 直到退款金额完全分配或支付记录用尽
    
    return nil
}

// 创建退款支付记录
func CreateRefundPayRecords(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "CreateRefundPayRecords"
    ctx.NewRefundPayRecords = make([]PayRecordVO, 0)
    
    // 遍历分配结果(ctx.AmountAllocations)
    // 为每条原始支付记录创建对应的退款支付记录
    // 根据退款方式和支付类型设置初始支付状态:
    //   - 现金退款: 直接设置为"success"
    //   - 第三方支付(乐刷付款码): 设置为"unpaid"
    //   - 其他记账方式: 设置为"success"
    
    // 添加到ctx.NewRefundPayRecords列表
    
    return nil
}
```

### 7. 关联服务（RelationService）

```go
// 创建订单和支付单的关联
func CreateOrderAndPayRelations(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "CreateOrderAndPayRelations"
    ctx.NewOrderAndPays = make([]OrderAndPayVO, 0)
    
    // 遍历ctx.RefundOrders中的所有退款订单
    for _, refundOrderNo := range ctx.RefundOrders {
        // 创建新的OrderAndPayVO记录
        // 关联退款订单号和退款收款单号
        // 设置SessionId
        
        // 添加到ctx.NewOrderAndPays列表
    }
    
    return nil
}
```

### 8. 第三方支付服务（ThirdPayService）

```go
// 处理第三方支付退款
func ProcessThirdPartyRefunds(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "ProcessThirdPartyRefunds"
    
    // 如果是原路返回退款
    if *ctx.RefundRequest.RefundWayType == "back" {
        // 遍历ctx.NewRefundPayRecords中的所有退款支付记录
        for _, record := range ctx.NewRefundPayRecords {
            // 如果是乐刷支付记录
            if record.PayType == PAY_TYPE_LESHUA_BSHOWQR {
                // 调用乐刷支付的退款接口
                // 处理接口响应
                // 如果接口调用失败，更新支付记录状态为"failed"
                // 不回滚整个事务
                
                // 记录第三方支付状态
                ctx.ThirdPartyPaymentStatus[record.Id] = "processing"
            }
        }
    }
    
    return nil
}

// 处理第三方支付退款回调
func HandleThirdPartyRefundCallback(payId string, status string) error {
    // 查询对应的支付记录
    // 更新支付记录状态
    // 如果成功，更新为"success"
    // 如果失败，更新为"failed"
    
    return nil
}
```

### 9. 事务管理服务（TransactionService）

```go
// 开始事务
func BeginTransaction(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "BeginTransaction"
    
    // 开始数据库事务
    tx, err := db.Begin()
    if err != nil {
        return err
    }
    
    // 保存事务对象到上下文
    ctx.TransactionObj = tx
    ctx.IsTransactionStarted = true
    
    return nil
}

// 提交事务
func CommitTransaction(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "CommitTransaction"
    
    // 如果事务已开始
    if ctx.IsTransactionStarted {
        // 提交事务
        tx := ctx.TransactionObj
        // ...
    }
    
    return nil
}

// 回滚事务
func RollbackTransaction(ctx *RefundProcessContext) error {
    // 如果事务已开始
    if ctx.IsTransactionStarted {
        // 回滚事务
        tx := ctx.TransactionObj
        // ...
    }
    
    return nil
}
```

### 10. 日志和上下文记录服务（LoggingService）

```go
// 记录退款处理上下文
func SaveRefundContext(ctx *RefundProcessContext) error {
    // 将关键退款上下文信息序列化并保存，包括所有新增的数据对象
    // 可用于后续查询和分析
    
    return nil
}

// 记录退款处理日志
func LogRefundProcess(ctx *RefundProcessContext, stage string, info map[string]interface{}) {
    // 记录详细的退款处理日志
    // 包括处理阶段、输入参数、处理结果等
}
```

### 11. 统一退款流程（RefundService）

```go
// 初始化退款上下文
func InitRefundContext(req *V3QueryOrderRefundReqDto) *RefundProcessContext {
    return &RefundProcessContext{
        RefundRequest: req,
        Errors: make(map[string]error),
        RefundOrders: make(map[string]string),
        ThirdPartyPaymentStatus: make(map[string]string),
        ProcessStep: "Initialize",
    }
}

// 执行退款流程
func ProcessRefund(req *V3QueryOrderRefundReqDto) error {
    // 初始化上下文
    ctx := InitRefundContext(req)
    
    // 第一步：校验基本请求参数
    if err := ValidateRefundRequest(ctx); err != nil {
        return err
    }
    
    // 第二步：加载场次全量信息
    if err := LoadSessionFullInfo(ctx); err != nil {
        return err
    }
    
    // 第三步：准备原始数据
    if err := PrepareOriginalData(ctx); err != nil {
        return err
    }
    
    // 第四步：校验商品是否可退
    if err := ValidateProductRefundable(ctx); err != nil {
        return err
    }
    
    // 第五步：校验退款金额是否正确
    if err := ValidateRefundAmount(ctx); err != nil {
        return err
    }
    
    // 第六步：开始事务
    if err := BeginTransaction(ctx); err != nil {
        return err
    }
    
    // 第七步：创建退款订单及商品
    if err := CreateAllRefundOrders(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第八步：创建退款收款单
    if err := CreateRefundPayBill(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第九步：分配退款金额
    if err := AllocateRefundAmount(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第十步：创建退款支付记录
    if err := CreateRefundPayRecords(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第十一步：创建订单与支付单关联
    if err := CreateOrderAndPayRelations(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第十二步：持久化所有数据到数据库
    if err := PersistRefundDataToDB(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第十三步：提交事务
    if err := CommitTransaction(ctx); err != nil {
        RollbackTransaction(ctx)
        return err
    }
    
    // 第十四步：处理第三方支付退款（不影响主事务）
    ProcessThirdPartyRefunds(ctx)
    
    // 第十五步：保存退款处理上下文
    SaveRefundContext(ctx)
    
    return nil
}
```

### 12. 持久化退款相关数据到数据库

```go
// 持久化退款相关数据到数据库
func PersistRefundDataToDB(ctx *RefundProcessContext) error {
    ctx.ProcessStep = "PersistRefundDataToDB"
    
    // 保存退款订单
    if err := SaveRefundOrders(ctx); err != nil {
        return err
    }
    
    // 保存退款商品
    if err := SaveRefundProducts(ctx); err != nil {
        return err
    }
    
    // 保存退款支付单
    if err := SaveRefundPayBill(ctx); err != nil {
        return err
    }
    
    // 保存退款支付记录
    if err := SaveRefundPayRecords(ctx); err != nil {
        return err
    }
    
    // 保存订单支付关联
    if err := SaveOrderAndPayRelations(ctx); err != nil {
        return err
    }
    
    return nil
}

// 保存退款订单
func SaveRefundOrders(ctx *RefundProcessContext) error {
    // 将ctx.NewRefundOrders中的数据保存到数据库
    return nil
}

// 保存退款商品
func SaveRefundProducts(ctx *RefundProcessContext) error {
    // 将ctx.NewRefundProducts中的数据保存到数据库
    return nil
}

// 保存退款支付单
func SaveRefundPayBill(ctx *RefundProcessContext) error {
    // 将ctx.NewRefundPayBill保存到数据库
    return nil
}

// 保存退款支付记录
func SaveRefundPayRecords(ctx *RefundProcessContext) error {
    // 将ctx.NewRefundPayRecords中的数据保存到数据库
    return nil
}

// 保存订单支付关联
func SaveOrderAndPayRelations(ctx *RefundProcessContext) error {
    // 将ctx.NewOrderAndPays中的数据保存到数据库
    return nil
}
