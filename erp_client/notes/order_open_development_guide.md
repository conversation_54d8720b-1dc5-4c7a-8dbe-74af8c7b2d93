# Order Open 开发指导文档（更新版）

## 一、领域服务归属与接口设计

### 1.1 交易记录领域 (traderecord/base)
- **OrderService**
  ```go
  type OrderService interface {
      // OrderOpen 创建开台订单（主要接口）
      OrderOpen(ctx context.Context, session *po.Session, orders *[]po.Order, 
          orderProducts *[]po.OrderProduct, orderPricePlan *po.OrderPricePlan, 
          orderRoomPlans *[]po.OrderRoomPlan, room *po.Room) error
      
      // GenerateOrderInfo 生成订单基础信息
      GenerateOrderInfo(ctx context.Context, venueId string) (*vo.OrderInfoVO, error)
      
      // PrepareOrders 准备订单数据
      PrepareOrders(ctx context.Context, reqDto *vo.OrderOpenReqVO, 
          orderInfo *vo.OrderInfoVO) (*vo.OrderPrepareResultVO, error)
  }
  ```

- **OrderFeeCalcService**
  ```go
  type OrderFeeCalcService interface {
      // CalculateOrderFee 计算订单费用
      CalculateOrderFee(ctx context.Context, orders *[]po.Order, 
          products *[]po.OrderProduct, minimumCharge int64) (*vo.FeeResultVO, error)
      
      // ValidateMinimumCharge 验证最低消费
      ValidateMinimumCharge(ctx context.Context, orderAmount int64, 
          minimumCharge int64) error
  }
  ```

### 1.2 值对象领域 (valueobject/business)
- **SessionService** (session/service)
  ```go
  type SessionService interface {
      // CreateSession 创建场次
      CreateSession(ctx context.Context, session *po.Session) error
      
      // UpdateSessionStatus 更新场次状态
      UpdateSessionStatus(ctx context.Context, sessionId string, status string) error
  }
  ```

- **RoomService** (room/service)
  ```go
  type RoomService interface {
      // 已实现的方法
      FindRoomById(ctx context.Context, id string) (*po.Room, error)
      
      // 需要新增的方法
      UpdateRoomStatus(ctx context.Context, room *po.Room) error
      ValidateRoomStatus(ctx context.Context, roomId string, expectedStatus []string) error
      
      // 定时器相关方法（调用已实现的RoomTimerService）
      SetRoomTimer(ctx context.Context, venueId, roomId, sessionId string, endTime int64) error
      CancelRoomTimer(ctx context.Context, roomId string) error
      UpdateRoomTimer(ctx context.Context,roomId string) error

  }
  ```

### 1.3 配置领域 (config/business)
- **PricePlanService** (priceplan/service)
  ```go
  type PricePlanService interface {
      // FindPricePlanById 查找价格方案
      FindPricePlanById(ctx context.Context, id string) (*po.PricePlan, error)
      
      // CalculatePrice 计算价格
      CalculatePrice(ctx context.Context, priceplan *po.PricePlan, 
          params map[string]interface{}) (*po.PriceResult, error)
  }
  ```

- **BookingService** (booking/service)
  ```go
  type BookingService interface {
      // UpdateBookingStatus 更新预订状态
      UpdateBookingStatus(ctx context.Context, bookingId string, status int) error
      
      // ValidateBooking 验证预订信息
      ValidateBooking(ctx context.Context, bookingId string) error
  }
  ```

## 二、数据一致性保证

1. **事务控制**
   - 所有事务由process层控制
   - 领域服务不负责事务管理
   - 使用context传递事务上下文

2. **错误处理**
   - 统一使用domain error
   - 每个服务定义自己的错误类型
   - 错误信息需包含足够上下文

3. **状态一致性**
   - 使用乐观锁控制并发
   - 关键操作添加版本号
   - 状态变更需要校验前置状态

## 三、开发优先级

1. **第一阶段**
   - OrderService基础实现
   - OrderFeeCalcService实现
   - SessionService实现

2. **第二阶段**
   - RoomService扩展实现
   - PricePlanService实现
   - BookingService实现

3. **第三阶段**
   - 单元测试编写
   - 集成测试
   - 文档完善

## 四、开发进度跟踪

### 4.1 服务开发状态
| 服务名称 | 所属领域 | 状态 | 完成度 | 备注 |
|---------|---------|------|--------|------|
| OrderService | traderecord/base | 待开发 | 0% | - |
| OrderFeeCalcService | traderecord/base | 待开发 | 0% | - |
| SessionService | valueobject/business | 待开发 | 0% | - |
| RoomService | valueobject/business | 待扩展 | 50% | 基础方法已实现 |
| PricePlanService | config/business | 待开发 | 0% | - |
| BookingService | config/business | 待开发 | 0% | - |

### 4.2 开发日志
- [2024-02-12] 项目启动
  * 完成开发指南编写
  * 确定服务边界和接口设计
  * 制定开发计划
- [2024-02-12] 开发进行中
  * 创建 OrderService 和 OrderFeeCalcService 接口
  * 创建必要的PO对象：OrderInfo、OrderOpenReqDto
  * 修复导入路径问题
  * 下一步：实现服务接口
- [2024-02-12] 重构VO对象
  * 将业务对象从PO迁移到VO
  * 创建 order_vo.go 和 fee_vo.go
  * 更新服务接口使用VO对象
  * 优化了领域对象的职责划分
- [2024-02-12] 服务实现
  * 创建 OrderFeeCalcService 实现
  * 创建 OrderService 实现（基础框架）
  * 创建 SessionService 实现（基础框架）
  * 标记了所有需要实现的TODO项
- [2024-02-12] 配置领域服务
  * 创建 PricePlanService 接口和实现框架
  * 创建 BookingService 接口和实现框架
  * 待完善具体实现逻辑
- [2024-02-12] Booking服务实现
  * 完善 BookingService 接口定义
  * 实现预订状态管理
  * 实现预订信息验证
  * 添加预订错误处理

### 4.3 待办事项
1. **当前任务**
   - [x] 创建 traderecord/base/service 目录结构
   - [x] 实现 OrderService 接口（已创建接口定义）
   - [x] 实现 OrderFeeCalcService 接口（已创建接口定义）
   - [x] 重构业务对象为VO
   - [x] 创建服务实现框架
   - [x] 创建配置领域服务框架
   - [ ] 完善 OrderService 具体实现
   - [ ] 完善 OrderFeeCalcService 具体实现
   - [ ] 完善 SessionService 具体实现
   - [ ] 完善 PricePlanService 具体实现
   - [x] 完善 BookingService 具体实现

2. **下一步计划**
   - [ ] 创建 valueobject/business/session 目录结构
   - [ ] 实现 SessionService 接口
   - [ ] 扩展 RoomService 接口

3. **待解决问题**
   - [ ] 确认 OrderPrepareResult 结构定义
   - [ ] 确认 FeeResult 结构定义
   - [ ] 确认各服务错误类型定义

### 4.4 已完成事项
- [x] 服务边界划分确认
- [x] 接口设计完成
- [x] 开发文档初始化
- [x] VO对象重构
- [x] 服务实现框架搭建
- [x] 配置领域服务框架搭建

## 五、测试计划

### 5.1 单元测试覆盖
| 服务名称 | 测试文件 | 状态 | 覆盖率 |
|---------|---------|------|--------|
| OrderService | service_test.go | 未开始 | 0% |
| OrderFeeCalcService | service_test.go | 未开始 | 0% |
| SessionService | service_test.go | 未开始 | 0% |
| RoomService | service_test.go | 部分完成 | 30% |
| PricePlanService | service_test.go | 未开始 | 0% |
| BookingService | service_test.go | 未开始 | 0% |

### 5.2 集成测试场景
- [ ] 正常开台流程
- [ ] 预订转开台流程
- [ ] 并台场景
- [ ] 错误处理场景
- [ ] 并发处理场景

## 五、对象模型

### 5.1 值对象（VO）
1. **OrderInfoVO**
   - 用途：订单基础信息
   - 属性：订单号、场次ID

2. **OrderOpenReqVO**
   - 用途：开台请求数据
   - 属性：场馆ID、房间ID、开始时间、结束时间等

3. **OrderPrepareResultVO**
   - 用途：订单准备结果
   - 属性：场次信息、订单列表、商品列表等

4. **FeeResultVO**
   - 用途：费用计算结果
   - 属性：总金额、未支付金额、超市金额等

### 5.2 持久化对象（PO）
1. **Order**
   - 用途：订单数据表映射
   - 来源：数据库表

2. **Session**
   - 用途：场次数据表映射
   - 来源：数据库表

## 六、实现细节

### 6.1 待实现的TODO项
1. **OrderService**
   - [ ] 订单号生成逻辑
   - [ ] 场次ID生成逻辑
   - [ ] 订单创建逻辑
   - [ ] 订单准备逻辑

2. **OrderFeeCalcService**
   - [ ] 商品金额计算逻辑完善
   - [ ] 房费计算逻辑完善
   - [ ] 最低消费验证逻辑完善

3. **SessionService**
   - [ ] 场次创建逻辑
   - [ ] 场次状态更新逻辑
   - [ ] 状态转换验证

4. **PricePlanService**
   - [ ] 价格方案查询逻辑
   - [ ] 价格计算规则实现
   - [ ] 参数验证逻辑

5. **BookingService**
   - [x] 预订状态定义
     * CREATED(1): 已创建
     * CONFIRMED(2): 已确认
     * CANCELLED(3): 已取消
     * COMPLETED(4): 已完成
   - [x] 预订状态转换规则
     * CREATED -> CONFIRMED: 预订确认
     * CREATED -> CANCELLED: 预订取消
     * CONFIRMED -> COMPLETED: 预订完成(开台)
     * CONFIRMED -> CANCELLED: 预订取消
   - [x] 预订验证规则
     * 预订时间验证
     * 房间状态验证
     * 预订状态验证
   - [x] 错误类型定义
     * ErrBookingNotFound: 预订不存在
     * ErrInvalidBookingStatus: 无效的预订状态
     * ErrInvalidStatusTransition: 无效的状态转换
     * ErrInvalidBookingTime: 无效的预订时间
     * ErrRoomNotAvailable: 房间不可用

### 6.2 依赖注入
1. **OrderService**
   - 依赖 OrderFeeCalcService
   - 依赖 SessionService
   - 依赖 PricePlanService（待添加）
   - 依赖 BookingService（待添加）

2. **SessionService**
   - 暂无外部依赖

3. **PricePlanService**
   - 暂无外部依赖

4. **BookingService**
   - 依赖 RoomService (用于验证房间状态)
   - 依赖 BookingRepository (用于数据持久化)
   - 依赖 BookingValidator (用于业务规则验证)

### 6.3 错误处理
1. **BookingError**
   ```go
   type BookingError struct {
       Code    string
       Message string
       Cause   error
   }
   ```

2. **错误码定义**
   ```go
   const (
       ErrCodeBookingNotFound         = "BOOKING_NOT_FOUND"
       ErrCodeInvalidBookingStatus    = "INVALID_BOOKING_STATUS"
       ErrCodeInvalidStatusTransition = "INVALID_STATUS_TRANSITION"
       ErrCodeInvalidBookingTime      = "INVALID_BOOKING_TIME"
       ErrCodeRoomNotAvailable        = "ROOM_NOT_AVAILABLE"
   )
   ```

[其他内容保持不变...]