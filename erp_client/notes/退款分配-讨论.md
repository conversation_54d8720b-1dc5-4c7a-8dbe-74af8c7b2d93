订单支付系统，
    下单-》支付

支持多种支付方式
   现金
   微信/支付宝记账
   线上扫码支付
   会员卡支付（本金、通用赠金、房费赠金、商品赠金）

订单    商品  
o1     [op1x3 op2x2 op3x5 op4x7]   
o2     [op5x5]

订单         收款单    支付方式
[o1 o2 o3]   b1     [p1 p2]
[o4 o5]      b2     [p3 p1]

退款：
请求：退款商品
op1x2
op3x2
op5x3

退款原则：沿着原订单进行退款，将属于原订单的退款商品放到同一个退款订单下管理
数据表示：
订单  父订单  商品
o11   o1    [op1x2 op3x2]
o12   o2    [op5x3]

订单      收款单  父收款单  
o11       b11    b1
o12       b12    b2

问题：
1. 支持支原路径返回
2. 退款的支付方式怎么表示
3. 如果会员卡怎么处理，数据怎么展示，（本金、通用赠金、房费赠金、商品赠金）
4. 没有会员卡的情况应该是等比例退款还是有扣款顺序
5. 会员卡不适用等比例退款吧？

## 问题分析与解决方案

### 1. 支持支原路径返回
分析：
- 原订单支付时可能使用了多种支付方式，如[p1 p2]
- 退款时需要按照原支付方式的比例进行退款
- 需要记录原支付方式的详细信息，包括：
  - 支付方式类型（现金/微信/支付宝/会员卡）
  - 支付金额
  - 支付时间
  - 支付流水号
- 退款时需要验证原支付方式是否仍然有效
- 对于线上支付，需要调用对应的退款接口
- 对于现金支付，需要走现金退款流程
- 对于会员卡支付，需要走会员卡退款流程

### 2. 退款的支付方式怎么表示
分析：
- 需要设计退款支付方式的表示方式，建议：
  - 创建退款支付方式表，关联原支付方式
  - 记录退款金额、退款时间、退款状态
  - 记录退款流水号
  - 记录退款原因
  - 记录退款操作人
- 数据结构设计：
  - 退款支付方式ID
  - 原支付方式ID
  - 退款金额
  - 退款时间
  - 退款状态
  - 退款流水号
  - 退款原因
  - 退款操作人
  - 退款备注
- 状态流转：
  - 待退款
  - 退款中
  - 退款成功
  - 退款失败

### 3. 会员卡处理
分析：
- 会员卡支付包含多种资金类型：
  - 本金：会员卡实际充值金额
  - 通用赠金：可用于所有消费的赠金
  - 房费赠金：仅可用于房费的赠金
  - 商品赠金：仅可用于商品消费的赠金
- 退款规则设计：
  - 优先退还赠金，再退还本金
  - 赠金退还顺序：商品赠金 > 房费赠金 > 通用赠金
  - 如果退款金额超过赠金总额，剩余部分退还本金
- 数据结构设计：
  - 记录原支付时各种资金类型的支付金额
  - 记录退款时各种资金类型的退款金额
  - 记录退款后的会员卡余额变动
- 业务规则：
  - 赠金退款后可能直接作废
  - 本金退款后需要更新会员卡余额
  - 需要记录退款后的会员卡交易流水
  - 需要更新会员卡的使用记录

### 4. 非会员卡支付方式的退款
分析：
- 支持的支付方式：
  - 现金
  - 微信/支付宝记账
  - 线上扫码支付
- 退款原则：
  - 应该使用等比例退款
  - 原因：
    - 这些支付方式都是实际货币支付
    - 没有使用限制和优先级
    - 等比例退款更公平合理
- 实现方式：
  - 计算原订单各支付方式的支付比例
  - 按照相同比例分配退款金额
  - 记录每笔退款的支付方式

### 5. 会员卡不适用等比例退款
分析：
- 原因：
  - 会员卡资金类型有使用限制
  - 赠金有使用期限
  - 不同赠金类型有不同使用范围
  - 本金和赠金的性质不同
- 正确的处理方式：
  - 按照资金类型优先级顺序退款
  - 优先退还赠金，再退还本金
  - 赠金退还顺序：商品赠金 > 房费赠金 > 通用赠金
  - 如果退款金额超过赠金总额，剩余部分退还本金

### 7. 混合支付方式下的退款策略
分析：
- 场景：订单同时使用会员卡和其他支付方式
- 退款原则：
  1. 优先从会员卡中退款
     - 原因：
       - 会员卡资金有使用限制和期限
       - 赠金可能过期作废
       - 本金是实际充值金额
  2. 会员卡退款顺序：
     - 商品赠金
     - 房费赠金
     - 通用赠金
     - 本金
  3. 会员卡退款后，剩余金额的处理：
     - 按原支付比例退款
     - 原因：
       - 其他支付方式都是实际货币
       - 没有使用限制和优先级
       - 等比例退款更公平合理

实现方式：
1. 计算退款总金额
2. 优先从会员卡中退款：
   - 按照资金类型优先级顺序计算退款金额
   - 如果会员卡余额不足，记录剩余退款金额
3. 剩余金额按原支付比例退款：
   - 计算原订单各支付方式的支付比例
   - 按照相同比例分配剩余退款金额
4. 记录每笔退款的支付方式和金额

示例：
```
原订单支付：
- 会员卡：100元（商品赠金50元，本金50元）
- 微信：100元
- 支付宝：100元

退款金额：200元

退款流程：
1. 会员卡退款：100元
   - 商品赠金：50元
   - 本金：50元
2. 剩余100元按原支付比例退款：
   - 微信：50元
   - 支付宝：50元
```

## 建议的解决方案

### 1. 创建退款支付方式表
```sql
CREATE TABLE refund_payment (
    id VARCHAR(32) PRIMARY KEY,
    original_payment_id VARCHAR(32),  -- 原支付方式ID
    refund_amount DECIMAL(10,2),      -- 退款金额
    refund_time BIGINT,               -- 退款时间
    refund_status INT,                -- 退款状态
    refund_no VARCHAR(32),            -- 退款流水号
    refund_reason VARCHAR(255),       -- 退款原因
    operator_id VARCHAR(32),          -- 操作人ID
    remark VARCHAR(255),              -- 备注
    ctime BIGINT,                     -- 创建时间
    utime BIGINT,                     -- 更新时间
    state INT,                        -- 状态
    version INT                       -- 版本
);
```

### 2. 创建会员卡退款明细表
```sql
CREATE TABLE member_card_refund_detail (
    id VARCHAR(32) PRIMARY KEY,
    refund_payment_id VARCHAR(32),    -- 退款支付方式ID
    member_card_id VARCHAR(32),       -- 会员卡ID
    principal_amount DECIMAL(10,2),   -- 本金退款金额
    general_bonus_amount DECIMAL(10,2), -- 通用赠金退款金额
    room_bonus_amount DECIMAL(10,2),  -- 房费赠金退款金额
    goods_bonus_amount DECIMAL(10,2), -- 商品赠金退款金额
    ctime BIGINT,                     -- 创建时间
    utime BIGINT,                     -- 更新时间
    state INT,                        -- 状态
    version INT                       -- 版本
);
```

### 3. 创建退款规则配置表
```sql
CREATE TABLE refund_rule (
    id VARCHAR(32) PRIMARY KEY,
    payment_type VARCHAR(32),         -- 支付方式类型
    refund_type VARCHAR(32),          -- 退款类型（等比例/优先级）
    priority INT,                     -- 优先级（用于会员卡）
    ctime BIGINT,                     -- 创建时间
    utime BIGINT,                     -- 更新时间
    state INT,                        -- 状态
    version INT                       -- 版本
);
```

### 4. 创建退款分配表
```sql
CREATE TABLE refund_distribution (
    id VARCHAR(32) PRIMARY KEY,
    refund_order_id VARCHAR(32),      -- 退款订单ID
    original_payment_id VARCHAR(32),   -- 原支付方式ID
    payment_type VARCHAR(32),         -- 支付方式类型
    amount DECIMAL(10,2),             -- 退款金额
    distribution_type VARCHAR(32),    -- 分配类型（等比例/优先级）
    priority INT,                     -- 优先级
    ctime BIGINT,                     -- 创建时间
    utime BIGINT,                     -- 更新时间
    state INT,                        -- 状态
    version INT                       -- 版本
);
```

### 5. 退款流程设计
- 判断支付方式类型
- 根据支付方式类型选择退款规则
- 会员卡支付：
  - 按照资金类型优先级顺序计算退款金额
  - 优先退还赠金，再退还本金
  - 更新会员卡余额
- 非会员卡支付：
  - 计算原支付方式比例
  - 按照相同比例分配退款金额
  - 执行退款操作

### 6. 数据展示
- 退款订单明细
- 退款支付方式明细
- 会员卡退款明细（包含各种资金类型）
- 退款状态跟踪
- 退款凭证
