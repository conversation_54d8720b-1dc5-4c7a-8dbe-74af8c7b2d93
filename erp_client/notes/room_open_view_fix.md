# Room Open View 服务修复笔记

## 问题概述
房间开放视图服务(v2/room/open-view)目前存在多个编译错误，需要系统性修复。

## 已发现的问题

### 1. RoomOpenViewProcess 结构体问题
- `process.BaseProcess` 未定义
  - 实际上 BaseProcess 已在 `application/framework/process/base_process.go` 中定义
  - 需要正确导入 process 包
- `rule.Engine` 类型未定义
  - 规则引擎接口已在 `domain/rule/engine/engine.go` 中定义为 RuleEngine
  - 需要正确导入规则引擎包并使用正确的类型名

### 2. 依赖导入问题
- service 包未定义:
  - service.NewAreaService()
  - service.NewRoomTypeService()
  - service.NewPricePlanService()
  - service.NewHolidayService()
- 需要确认这些服务的正确导入路径和实现位置

### 3. 类型兼容性问题
- context.Context 和 *gin.Context 类型不匹配
  - appService.GetRoomOpenView 方法期望 *gin.Context 类型
  - 当前传入 context.Context 类型
  - 需要在流程中正确处理上下文转换

### 4. 流程注册问题
- RoomOpenViewProcess 无法作为 Process 接口使用
- 原因：缺少必要的接口方法实现
  - Execute(ctx context.Context, params map[string]interface{}) (*ProcessContext, error)
  - Cancel(ctx context.Context) error
  - GetStatus(ctx context.Context) (ProcessStatus, error)
  - GetResult(ctx context.Context) (interface{}, error)

### 5. 规则引擎集成问题
- 规则引擎接口定义在 `domain/rule/engine/engine.go`
- 需要正确实现规则引擎的调用逻辑
- 需要实现相关的规则定义和执行逻辑

## 影响范围
1. API 层：room_controller.go
2. 流程层：room_open_view_process.go
3. 应用服务层：涉及 room.NewApplicationService 的调用
4. 规则引擎层：涉及规则定义和执行

## 修复计划
1. 修复导入问题
   - 添加正确的包导入语句
   - 确保包路径正确

2. 实现 Process 接口
   - 继承 BaseProcess
   - 实现所有必需的接口方法
   - 确保正确处理上下文转换

3. 规则引擎集成
   - 正确导入和使用规则引擎
   - 实现必要的规则定义
   - 实现规则执行逻辑

4. 服务依赖处理
   - 确认所有服务的实现位置
   - 添加正确的服务导入
   - 实现服务注入逻辑

## 注意事项
- 修改涉及多个层级的代码
- 需要确保修改后的代码符合既有的架构设计
- 需要保持与其他模块的一致性
- 需要确保规则引擎的正确集成和使用

## 支付实现细节

### 1. 支付流程
1. 入参校验
   - 验证基础参数(sessionId, venueId, roomId, payType等)
   - 获取需要支付的订单
   - 校验支付逻辑,获取需要修改的订单

2. 组装支付数据
   - 生成支付单号
   - 组装支付账单信息(PayBill)
   - 组装订单支付关联信息(OrderAndPay)

3. 保存支付信息
   - 保存支付账单
   - 保存订单支付关联信息
   - 事务处理保证数据一致性

4. 发起支付
   - 根据支付方式调用对应的支付网关
   - 支持的支付方式:
     - 现金记账(RECORD_CASH)
     - 微信记账(RECORD_WECHAT) 
     - 支付宝记账(RECORD_ALIPAY)
     - 银行卡记账(RECORD_BANK)
     - 乐刷B扫码支付(LESHUA_BSHOWQR)

5. 支付后处理
   - 记账支付:更新订单状态
   - 非记账支付:返回支付结果

### 2. 金额计算逻辑

1. 订单金额组成
   - 房费金额
     - 原始房费(original_amount)
     - 房费折扣金额(discount_room_amount) = 原始房费 × (1 - discount_room_rate)
     - 房费减免金额(reduce_room_amount)
     - 最终房费 = 原始房费 - 房费折扣金额 - 房费减免金额
   
   - 商品金额
     - 原始商品金额(original_amount)
     - 商品折扣金额(discount_product_amount) = 原始商品金额 × (1 - discount_product_rate)
     - 商品减免金额(reduce_product_amount)
     - 最终商品金额 = 原始商品金额 - 商品折扣金额 - 商品减免金额

2. 特殊场景处理
   - 多订单合并支付
     - 合并计算所有订单的总金额
     - 按比例分配折扣金额到各个订单
     - 分别更新每个订单的支付状态
   
   - 部分支付
     - 记录已支付金额
     - 更新订单未支付金额
     - 保持订单状态为部分支付

### 3. 数据一致性保证

1. 事务处理
   ```go
   tx := model.DBMaster.Self.Begin()
   // 1. 保存支付账单
   if err := payBillService.CreatePayBillWithTx(logCtx, toAddPayBill, tx); err != nil {
       tx.Rollback()
       return err
   }
   // 2. 保存订单支付关联
   for _, orderAndPay := range *toAddOrderAndPays {
       if err := orderAndPayService.CreateOrderAndPayWithTx(logCtx, &orderAndPay, tx); err != nil {
           tx.Rollback()
           return err
       }
   }
   // 3. 提交事务
   if err := tx.Commit().Error; err != nil {
       return err
   }
   ```

2. 状态更新
   - 支付前状态检查
   - 支付中状态锁定
   - 支付后状态更新
   - 异常回滚处理

3. 支付记录追踪
   - 生成唯一支付单号
   - 记录支付流水
   - 关联订单信息
   - 记录操作日志

### 4. 注意事项

1. 支付前需要校验订单状态
2. 支付金额需要与订单金额一致
3. 记账支付需要有相应权限
4. B扫码支付需要配置门店支付设置
5. 支付完成后会更新订单状态和场次未支付金额
6. 金额计算需要注意精度问题,使用分为单位
7. 多订单支付时需要正确分配折扣金额
8. 支付失败时需要正确处理回滚逻辑 

## 支付回调实现

### 1. 支付回调流程

1. 乐刷支付回调
   - 支付发起后，乐刷一般30s左右返回支付通知
   - 通知频率：0s/15s/30s/1m/4m/34m/64m/94m/124m/184m
   - 通知优先级：
     1. 交易接口中传输的回调地址
     2. 商户入网时配置的通知地址
     3. 商户所属一级代理商配置的通知地址

2. 回调处理流程
   - 验证回调状态是否合法
   - 查询支付账单信息
   - 拦截重复通知
   - 验证数据库支付订单字段合法性
   - 更新支付状态和相关订单信息

3. 支付状态处理
   ```go
   // 订单支付状态枚举：
   // 0：支付中
   // 2：支付成功
   // 6：订单关闭
   // 8：支付失败
   ```

### 2. 支付后处理

1. 记账支付处理
   ```go
   func AfterPayCallbackCoUpdateInfoByPayId(logCtx *gin.Context, payId string) error {
       // 1. 更新支付账单状态
       toUpdatePayBill := po.PayBill{
           Id:         dbPayBill.Id,
           Status:     util.GetItPtr(_const.PAY_STATUS_PAID),
           FinishTime: &now,
       }
       
       // 2. 更新订单状态
       for _, v := range *dbAllOrders {
           if util.InList(*v.OrderNo, orderNos) {
               if *v.Status == _const.ORDER_STATUS_UNPAID {
                   newOrderTmp.Status = util.GetItPtr(_const.ORDER_STATUS_PAID)
               } else if *v.Status == _const.ORDER_STATUS_REFUNDING {
                   newOrderTmp.Status = util.GetItPtr(_const.ORDER_STATUS_REFUNDED)
               }
           }
       }
       
       // 3. 重新计算场次费用
       service.ReCalcSessionFees(logCtx, sessionId, venueId)
   }
   ```

2. 退款回调处理
   ```go
   // 退款订单状态枚举：
   // 10：退款中
   // 11：退款成功
   // 12：退款失败
   
   func LeshuaRefundCallback(logCtx *gin.Context, reqDtoCallback *model.LeshuaRefundCallbackModel) error {
       // 1. 验证回调状态
       if !util.InList(reqDtoCallback.Status, []string{"11", "12"}) {
           return errors.New("退款状态未知")
       }
       
       // 2. 更新退款信息
       toUpdatePayBill := po.PayBill{
           Id:           payBill.Id,
           ThirdOrderId: &reqDtoCallback.Leshua_refund_id,
           Status:       util.GetItPtr(_const.PAY_STATUS_REFUND),
           FinishTime:   util.GetItPtr(int64(util.TimeNowUnix())),
       }
   }
   ```

### 3. 数据一致性保证

1. 事务处理
   ```go
   func SavePayByCashCallbackPre(logCtx *gin.Context, toUpdatePayBill *po.PayBill, toUpdateOrders *[]po.Order, toUpdateOrderRoomPlans *[]po.OrderRoomPlan, toUpdateOrderProducts *[]po.OrderProduct) error {
       tx := model.DBMaster.Self.Begin()
       
       // 1. 更新支付账单
       if err := payBillService.UpdatePayBillPartialWithTx(logCtx, toUpdatePayBill, tx); err != nil {
           tx.Rollback()
           return err
       }
       
       // 2. 更新订单信息
       for _, v := range *toUpdateOrders {
           if err := orderService.UpdateOrderPartialWithTx(logCtx, &v, tx); err != nil {
               tx.Rollback()
               return err
           }
       }
       
       // 3. 更新房间订单信息
       for _, v := range *toUpdateOrderRoomPlans {
           if err := orderRoomPlanService.UpdateOrderRoomPlanPartialWithTx(logCtx, &v, tx); err != nil {
               tx.Rollback()
               return err
           }
       }
       
       // 4. 更新商品订单信息
       for _, v := range *toUpdateOrderProducts {
           if err := orderProductService.UpdateOrderProductPartialWithTx(logCtx, &v, tx); err != nil {
               tx.Rollback()
               return err
           }
       }
       
       return tx.Commit().Error
   }
   ```

2. 并发控制
   - 使用Redis锁防止重复处理
   - 支付查单和回调互斥处理
   - 状态变更的原子性保证

### 4. 注意事项

1. 支付回调处理
   - 需要正确处理重复通知
   - 支付状态变更需要保证原子性
   - 异常情况需要正确回滚
   - 需要记录详细的日志信息

2. 退款处理
   - 退款前需要验证订单状态
   - 退款金额不能超过原支付金额
   - 退款状态变更需要同步更新相关订单
   - 退款失败需要有相应的处理机制

3. 数据一致性
   - 使用事务确保状态更新的一致性
   - 关键操作需要加锁防止并发问题
   - 异常情况需要能够正确回滚
   - 需要保证订单金额计算的准确性 

## 订单和场次状态管理

### 1. 订单状态定义

1. 基础状态
   ```go
   const (
       ORDER_STATUS_UNPAID    = "unpaid"    // 未支付
       ORDER_STATUS_PAID      = "paid"      // 已支付
       ORDER_STATUS_REFUNDING = "refunding" // 退款中
       ORDER_STATUS_REFUNDED  = "refunded"  // 已退款
   )
   ```

2. 订单标记类型
   ```go
   const (
       ORDER_TAG_OPENING    = "opening"    // 开台订单
       ORDER_TAG_ADDITIONAL = "additional" // 追加订单
   )
   ```

### 2. 场次状态流转

1. 开台流程
   ```go
   func OrderOpen(logCtx *gin.Context, toAddSession *po.Session, toAddAllOrders *[]po.Order, toAddOrderProducts *[]po.OrderProduct, toAddOrderPricePlan *po.OrderPricePlan, toAddOrderRoomPlans *[]po.OrderRoomPlan, toUpdateRoom *po.Room) bool {
       tx := model.DBMaster.Self.Begin()
       
       // 1. 保存场次信息
       if err := tx.Save(WrapAddData(toAddSession)).Error; err != nil {
           tx.Rollback()
           return false
       }
       
       // 2. 保存订单信息
       for _, toAddOrder := range *toAddAllOrders {
           if err := tx.Save(WrapAddData(&toAddOrder)).Error; err != nil {
               tx.Rollback()
               return false
           }
       }
       
       // 3. 保存商品订单
       for _, toAddOrderProduct := range *toAddOrderProducts {
           if err := tx.Save(WrapAddData(&toAddOrderProduct)).Error; err != nil {
               tx.Rollback()
               return false
           }
       }
       
       // 4. 保存价格方案
       if err := tx.Save(WrapAddData(toAddOrderPricePlan)).Error; err != nil {
           tx.Rollback()
           return false
       }
       
       // 5. 保存房间计划
       for _, toAddOrderRoomPlan := range *toAddOrderRoomPlans {
           if err := tx.Save(WrapAddData(&toAddOrderRoomPlan)).Error; err != nil {
               tx.Rollback()
               return false
           }
       }
       
       return tx.Commit().Error == nil
   }
   ```

2. 续台流程
   ```go
   func OrderOpenContinue(logCtx *gin.Context, roomUpdate *po.Room, sessionUpdate *po.Session, toAddAllOrders *[]po.Order, toAddOrderProducts *[]po.OrderProduct, toAddOrderPricePlan *po.OrderPricePlan, toAddOrderRoomPlans *[]po.OrderRoomPlan) bool {
       tx := model.DBMaster.Self.Begin()
       
       // 1. 更新房间状态
       if err := roomService.UpdateRoomPartialWithTx(logCtx, roomUpdate, tx); err != nil {
           tx.Rollback()
           return false
       }
       
       // 2. 更新场次状态
       if err := sessionService.UpdateSessionPartialWithTx(logCtx, sessionUpdate, tx); err != nil {
           tx.Rollback()
           return false
       }
       
       // 3. 创建新订单
       for _, order := range *toAddAllOrders {
           if err := orderService.CreateOrderWithTx(logCtx, &order, tx); err != nil {
               tx.Rollback()
               return false
           }
       }
       
       // 4. 保存商品订单
       for _, orderProduct := range *toAddOrderProducts {
           if err := orderProductService.CreateOrderProductWithTx(logCtx, &orderProduct, tx); err != nil {
               tx.Rollback()
               return false
           }
       }
       
       return tx.Commit().Error == nil
   }
   ```

3. 取消订单流程
   ```go
   func CancelOrder(logCtx *gin.Context, room *po.Room, orders []po.Order, session *po.Session) error {
       tx := model.DBMaster.Self.Begin()
       
       // 1. 更新房间状态
       if room.Id != nil && *room.Id != "" {
           err := tx.Model(&po.Room{}).Select(GetNotNilFields(room)).Where("id=?", room.Id).Updates(room).Error
           if err != nil {
               tx.Rollback()
               return err
           }
       }
       
       // 2. 更新订单状态
       for _, order := range orders {
           err := tx.Model(&po.Order{}).Select(GetNotNilFields(&order)).Where("id=?", order.Id).Updates(&order).Error
           if err != nil {
               tx.Rollback()
               return err
           }
       }
       
       // 3. 更新场次状态
       if session.Id != nil && *session.Id != "" {
           err := tx.Model(&po.Session{}).Select(GetNotNilFields(session)).Where("id=?", session.Id).Updates(session).Error
           if err != nil {
               tx.Rollback()
               return err
           }
       }
       
       return tx.Commit().Error
   }
   ```

### 3. 状态变更注意事项

1. 开台流程
   - 需要同时更新房间状态和场次状态
   - 订单状态默认为未支付
   - 如果有预订单需要更新预订状态
   - 需要设置房间定时器

2. 续台流程
   - 需要验证原场次状态
   - 更新房间使用时间
   - 更新场次结束时间
   - 更新房间定时器

3. 取消流程
   - 需要验证订单是否可以取消
   - 更新房间状态为空闲
   - 更新场次状态为已取消
   - 清除房间定时器

4. 支付流程
   - 支付成功后需要更新订单状态
   - 如果场次内所有订单都已支付，需要更新场次状态
   - 需要重新计算场次费用

5. 退款流程
   - 退款前需要验证订单状态
   - 退款成功后需要更新订单状态
   - 需要重新计算场次费用

### 4. 数据一致性保证

1. 事务处理
   - 所有状态变更操作都需要在事务中进行
   - 任何步骤失败都需要回滚整个事务
   - 需要处理并发情况下的状态冲突

2. 状态校验
   - 每次状态变更前都需要验证当前状态
   - 需要处理状态变更的前置条件
   - 需要验证状态变更的合法性

3. 异常处理
   - 需要正确处理事务回滚
   - 需要记录状态变更日志
   - 异常情况需要有补偿机制 

## 房间状态和定时器实现

### 1. 房间状态定义

1. 基础状态
   ```go
   const (
       ROOM_STATUS_IDLE       = "idle"       // 空闲
       ROOM_STATUS_WITH_GUEST = "with_guest" // 带客
       ROOM_STATUS_IN_USE     = "in_use"     // 使用中
       ROOM_STATUS_AFTER_USE  = "after_use"  // 使用后
       ROOM_STATUS_CLEANING   = "cleaning"   // 清扫中
       ROOM_STATUS_FAULT      = "fault"      // 故障
   )
   ```

2. 房间标记
   ```go
   const (
       ROOM_TAG_LOCKED = "locked" // 锁房
       ROOM_MARK_UNION = "union"  // 联台
       ROOM_MARK_BOOKED = "booked" // 预定
   )
   ```

### 2. 房间定时器实现

1. 定时器结构
   ```go
   type RoomTimer struct {
       tw     *timingwheel.TimingWheel // 时间轮
       timers sync.Map                 // 存储房间ID -> timer的映射
   }
   ```

2. 定时器初始化
   ```go
   func GetRoomTimer() *RoomTimer {
       once.Do(func() {
           tw := timingwheel.NewTimingWheel(time.Second, 60)
           tw.Start()
           instance = &RoomTimer{
               tw: tw,
           }
       })
       return instance
   }
   ```

3. 定时任务管理
   ```go
   // 添加定时任务
   func (rt *RoomTimer) AddTimer(logCtx *gin.Context, venueId string, roomId string, sessionId string, endTime int64) {
       // 先删除已存在的定时器
       rt.RemoveTimer(logCtx, venueId, roomId, sessionId)
       
       // 计算延迟时间
       delay := time.Duration(endTime-time.Now().Unix()) * time.Second
       
       // 创建新的定时器
       timer := rt.tw.AfterFunc(delay, func() {
           rt.closeRoom(logCtx, venueId, roomId, sessionId)
       })
       
       // 保存定时器
       key := venueId + "-" + sessionId
       rt.timers.Store(key, timer)
   }
   ```

### 3. 状态流转实现

1. 开台流程
   ```go
   // 更新房间状态
   toUpdateRoom := &po.Room{
       Id:        roomId,
       SessionId: &sessionId,
       Status:    util.GetItPtr(_const.ROOM_STATUS_IN_USE),
       OpenTime:  reqDto.StartTime,
       CloseTime: reqDto.EndTime,
   }
   ```

2. 关台流程
   ```go
   func (rt *RoomTimer) closeRoom(logCtx *gin.Context, venueId string, roomId string, sessionId string) {
       // 从Map中删除定时器
       key := venueId + "-" + sessionId
       rt.timers.Delete(key)
       
       // 更新房间状态
       if *room.Status == _const.ROOM_STATUS_IN_USE && session.Status != nil && *session.Status == _const.ORDER_STATUS_PAID {
           newRoom.Status = util.GetItPtr(_const.ROOM_STATUS_CLEANING)
       } else {
           newRoom.Status = util.GetItPtr(_const.ROOM_STATUS_AFTER_USE)
       }
   }
   ```

### 4. 状态管理注意事项

1. 定时器管理
   - 系统启动时需要初始化所有活动房间的定时器
   - 每次修改房间结束时间时需要更新定时器
   - 需要处理定时器的并发访问问题
   - 需要正确处理定时器的取消和清理

2. 状态一致性
   - 状态变更需要在事务中进行
   - 需要处理房间状态和场次状态的同步
   - 需要考虑联房情况下的状态同步
   - 需要正确处理异常情况下的状态回滚

3. 性能优化
   - 使用时间轮算法提高定时器性能
   - 避免频繁创建和销毁定时器
   - 合理设置定时器的时间精度
   - 控制定时器的内存占用

4. 容错处理
   - 系统重启时需要重新加载定时器
   - 需要处理定时器执行失败的情况
   - 需要有定时任务的重试机制
   - 需要记录关键操作日志 