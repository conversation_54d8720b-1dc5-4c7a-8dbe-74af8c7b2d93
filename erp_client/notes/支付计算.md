# 第一章 PO定义

## 1.1 Order 订单实体
```go
type Order struct {
	Id             *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                // ID
	VenueId        *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`              // 门店ID
	RoomId         *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                // 房间ID
	SessionId      *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`          // 场次ID
	OrderNo        *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`              // 订单编号
	POrderNo       *string `gorm:"column:p_order_no;type:varchar(64);default:''" json:"pOrderNo"`           // 父订单编号-退款时原订单
	EmployeeId     *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employeeId"`        // 员工ID
	EmployeeIdPay  *string `gorm:"column:employee_id_pay;type:varchar(64);default:''" json:"employeeIdPay"` // 支付员工ID
	MemberId       *string `gorm:"column:member_id;type:varchar(64);default:''" json:"memberId"`            // 会员ID
	StartTime      *int64  `gorm:"column:start_time;type:int;default:0" json:"startTime"`                   // 订单开始时间
	EndTime        *int64  `gorm:"column:end_time;type:int;default:0" json:"endTime"`                       // 订单结束时间
	PayAmount      *int64  `gorm:"column:pay_amount;type:int;default:0" json:"payAmount"`                   // 订单总金额（单位：分）
	OriginalAmount *int64  `gorm:"column:original_amount;type:int;default:0" json:"originalAmount"`         // 订单原总金额（单位：分）
	MinimumCharge  *int64  `gorm:"column:minimum_charge;type:int;default:0" json:"minimumCharge"`           // 最低消费金额（单位：分）
	Tag            *string `gorm:"column:tag;type:varchar(64);default:''" json:"tag"`                       // 标签
	Mark           *string `gorm:"column:mark;type:varchar(1024);default:''" json:"mark"`                   // 备注
	Type           *string `gorm:"column:type;type:varchar(64);default:''" json:"type"`                     // 订单类型
	Status         *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`                 // 订单状态
	Ctime          *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                            // 创建时间
	Utime          *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                            // 更新时间
	State          *int    `gorm:"column:state;type:int;default:0" json:"state"`                            // 状态
	Version        *int    `gorm:"column:version;type:int;default:0" json:"version"`                        // 版本号
}
```

## 1.2 OrderProduct 订单产品实体
```go
type OrderProduct struct {
	Id                  *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                            // ID
	VenueId             *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                          // 门店ID
	RoomId              *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                            // 房间ID
	SessionId           *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`                      // 场次ID
	PId                 *string `gorm:"column:p_id;type:varchar(64);default:''" json:"pId"`                                  // 退款商品对应的原始OrderProduct.Id
	OrderNo             *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`                          // 订单ID
	ProductId           *string `gorm:"column:product_id;type:varchar(64);default:''" json:"productId"`                      // 产品ID
	ProductName         *string `gorm:"column:product_name;type:varchar(255);default:''" json:"productName"`                 // 产品名称
	Flavors             *string `gorm:"column:flavors;type:varchar(255);default:''" json:"flavors"`                          // 口味
	Unit                *string `gorm:"column:unit;type:varchar(64);default:''" json:"unit"`                                 // 单位
	Quantity            *int64  `gorm:"column:quantity;type:int;default:0" json:"quantity"`                                  // 数量
	PayPrice            *int64  `gorm:"column:pay_price;type:bigint;default:0" json:"payPrice"`                              // 支付单价（单位：分）
	OriginalPrice       *int64  `gorm:"column:original_price;type:bigint;default:0" json:"originalPrice"`                    // 原价（单位：分）
	PayAmount           *int64  `gorm:"column:pay_amount;type:bigint;default:0" json:"payAmount"`                            // 总金额（单位：分）
	OriginalAmount      *int64  `gorm:"column:original_amount;type:bigint;default:0" json:"originalAmount"`                  // 原始总金额（单位：分）
	IsProductDiscount   *bool   `gorm:"column:is_product_discount;type:tinyint(1);default:0" json:"isProductDiscount"`       // 是否是产品折扣
	ProductDiscountRate *int64  `gorm:"column:product_discount_rate;type:bigint;default:100" json:"productDiscountRate"`     // 产品折扣率
	DiscountRate        *int64  `gorm:"column:discount_rate;type:bigint;default:0" json:"discountRate"`                      // 折扣率
	ReduceAmount        *int64  `gorm:"column:reduce_amount;type:bigint;default:0" json:"reduceAmount"`                      // 减免金额
	FreeAmount          *int64  `gorm:"column:free_amount;type:bigint;default:0" json:"freeAmount"`                          // 免单金额
	Mark                *string `gorm:"column:mark;type:varchar(255);default:''" json:"mark"`                                // 产品显示备注
	InPackageTag        *string `gorm:"column:in_package_tag;type:varchar(64);default:''" json:"inPackageTag"`               // 套内商品标签
	Src                 *string `gorm:"column:src;type:varchar(64);default:''" json:"src"`                                   // 套餐来源
	Ctime               *int64  `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`                                     // 创建时间
	Utime               *int64  `gorm:"column:utime;type:bigint;default:0" json:"utime"`                                     // 更新时间
	State               *int    `gorm:"column:state;type:int;default:0" json:"state"`                                        // 状态
	Version             *int    `gorm:"column:version;type:int;default:0" json:"version"`                                    // 版本号
}
```

## 1.3 OrderRoomPlan 价格方案实体
```go
type OrderRoomPlan struct {
	Id             *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                 // ID
	VenueId        *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`               // 门店ID
	RoomId         *string `gorm:"column:room_id;type:varchar(64);default:''" json:"roomId"`                 // 房间ID
	SessionId      *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"`           // 场次ID
	OrderNo        *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`               // 订单ID
	RoomName       *string `gorm:"column:room_name;type:varchar(128);default:''" json:"roomName"`            // 房间名称
	PricePlanId    *string `gorm:"column:price_plan_id;type:varchar(64);default:''" json:"pricePlanId"`      // 方案id
	PricePlanName  *string `gorm:"column:price_plan_name;type:varchar(128);default:''" json:"pricePlanName"` // 价格方案名称
	StartTime      *int64  `gorm:"column:start_time;type:int;default:0" json:"startTime"`                    // 开始时间
	EndTime        *int64  `gorm:"column:end_time;type:int;default:0" json:"endTime"`                        // 结束时间
	Duration       *int    `gorm:"column:duration;type:int;default:0" json:"duration"`                       // 买钟时长
	PayAmount      *int64  `gorm:"column:pay_amount;type:int;default:0" json:"payAmount"`                    // 房费（单位：分）
	OriginalAmount *int64  `gorm:"column:original_amount;type:int;default:0" json:"originalAmount"`          // 原始房费（单位：分）
	DiscountRate   *int64  `gorm:"column:discount_rate;type:int;default:0" json:"discountRate"`              // 折扣率
	ReduceAmount   *int64  `gorm:"column:reduce_amount;type:int;default:0" json:"reduceAmount"`              // 减免金额
	FreeAmount     *int64  `gorm:"column:free_amount;type:int;default:0" json:"freeAmount"`                  // 免单金额
	PricePlanType  *string `gorm:"column:price_plan_type;type:varchar(32);default:''" json:"pricePlanType"`  // 买钟价格类型
	Ctime          *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                             // 创建时间
	Utime          *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                             // 更新时间
	State          *int    `gorm:"column:state;type:int;default:0" json:"state"`                             // 状态
	Version        *int    `gorm:"column:version;type:int;default:0" json:"version"`                         // 版本号
}
```

## 1.4 PayBill 付款单实体
```go
type PayBill struct {
	Id                    *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                            // ID
	VenueId               *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venue_id"`                         // 门店id
	RoomId                *string `gorm:"column:room_id;type:varchar(64);default:''" json:"room_id"`                           // 房间ID
	SessionId             *string `gorm:"column:session_id;type:varchar(64);default:''" json:"session_id"`                     // 场次ID
	PayId                 *string `gorm:"column:pay_id;type:varchar(64);default:''" json:"pay_id"`                             // 支付号
	PPayId                *string `gorm:"column:p_pay_id;type:varchar(64);default:''" json:"p_pay_id"`                         // 退款单对应的支付单号
	ThirdOrderId          *string `gorm:"column:third_order_id;type:varchar(64);default:''" json:"third_order_id"`             // 第三方支付单号
	TotalFee              *int64  `gorm:"column:total_fee;type:bigint;default:0" json:"total_fee"`                             // 总金额-实际支付金额（单位：分）
	ZeroFee               *int64  `gorm:"column:zero_fee;type:bigint;default:0" json:"zero_fee"`                               // 抹零金额-Zero+Total=应付（单位：分）
	PayFee                *int64  `gorm:"column:pay_fee;type:bigint;default:0" json:"pay_fee"`                                 // 应付金额-应付=订单金额（单位：分）
	OriginalFee           *int64  `gorm:"column:original_fee;type:bigint;default:0" json:"original_fee"`                       // 原始金额（单位：分）
	CreditAmount          *int64  `gorm:"column:credit_amount;type:bigint;default:0" json:"credit_amount"`                     // 挂账金额
	DiscountRoomRate      *int64  `gorm:"column:discount_room_rate;type:bigint;default:0" json:"discount_room_rate"`           // 折扣房费率
	DiscountProductRate   *int64  `gorm:"column:discount_product_rate;type:bigint;default:0" json:"discount_product_rate"`     // 折扣商品率
	DiscountRoomAmount    *int64  `gorm:"column:discount_room_amount;type:bigint;default:0" json:"discount_room_amount"`       // 折扣房费金额
	DiscountProductAmount *int64  `gorm:"column:discount_product_amount;type:bigint;default:0" json:"discount_product_amount"` // 折扣商品金额
	ReduceRoomAmount      *int64  `gorm:"column:reduce_room_amount;type:bigint;default:0" json:"reduce_room_amount"`           // 减免房费金额
	ReduceProductAmount   *int64  `gorm:"column:reduce_product_amount;type:bigint;default:0" json:"reduce_product_amount"`     // 减免商品金额
	ChangeAmount          *int64  `gorm:"column:change_amount;type:bigint;default:0" json:"change_amount"`                     // 现金-找零金额
	FreeAmount            *int64  `gorm:"column:free_amount;type:bigint;default:0" json:"free_amount"`                         // 免单金额
	MemberAmount          *int64  `gorm:"column:member_amount;type:bigint;default:0" json:"member_amount"`                     // 会员金额
	DiscountAmount        *int64  `gorm:"column:discount_amount;type:bigint;default:0" json:"discount_amount"`                 // 优惠金额
	Status                *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`                             // 状态
	RefundWayType         *string `gorm:"column:refund_way_type;type:varchar(64);default:''" json:"refund_way_type"`           // 退款方式
	PayType               *string `gorm:"column:pay_type;type:varchar(64);default:''" json:"pay_type"`                         // 支付类型
	BusinessType          *string `gorm:"column:business_type;type:varchar(64);default:''" json:"business_type"`               // 业务类型,如会员'member',默认为空
	ProductName           *string `gorm:"column:product_name;type:varchar(64);default:''" json:"product_name"`                 // 商品名称
	Info                  *string `gorm:"column:info;type:varchar(512);default:''" json:"info"`                                // 备注
	FinishTime            *int64  `gorm:"column:finish_time;type:bigint;default:0" json:"finish_time"`                         // 完成时间
	BillDate              *int64  `gorm:"column:bill_date;type:bigint;default:0" json:"bill_date"`                             // 账单日期
	EmployeeId            *string `gorm:"column:employee_id;type:varchar(64);default:''" json:"employee_id"`                   // 员工ID-交班用
	CreditAccountId       *string `gorm:"column:credit_account_id;type:varchar(64);default:''" json:"credit_account_id"`       // 挂账单位ID
	Ctime                 *int64  `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`                                     // 创建时间
	Utime                 *int64  `gorm:"column:utime;type:bigint;default:0" json:"utime"`                                     // 更新时间
	State                 *int    `gorm:"column:state;type:int;default:0" json:"state"`                                        // 状态
	Version               *int    `gorm:"column:version;type:int;default:0" json:"version"`                                    // 版本号
}
```

## 1.5 OrderAndPay 订单支付关联实体
```go
type OrderAndPay struct {
	Id        *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`        // ID
	OrderNo   *string `gorm:"column:order_no;type:varchar(64);default:''" json:"order_no"`     // 单号
	SessionId *string `gorm:"column:session_id;type:varchar(64);default:''" json:"session_id"` // 场次ID
	PayId     *string `gorm:"column:pay_id;type:varchar(64);default:''" json:"pay_id"`         // 支付号
	Ctime     *int64  `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`                 // 创建时间
	Utime     *int64  `gorm:"column:utime;type:bigint;default:0" json:"utime"`                 // 更新时间
	State     *int    `gorm:"column:state;type:int;default:0" json:"state"`                    // 状态
	Version   *int    `gorm:"column:version;type:int;default:0" json:"version"`                // 版本号
}
```

# 第二章 订单业务规则

## 2.1 订单组成体系
### 业务规则
1. 房费与商品分离存储：
   - 房费明细 → OrderRoomPlan 实体
   - 商品明细 → OrderProduct 实体
2. 订单类型标识：
   ```go
   // 房费订单示例
   Order{Type: "roomplan"}
   // 商品订单示例
   Order{Type: "product"}
   ```

## 2.2 支付结算流程
### 核心过程
```mermaid
flowchart TD
    A[开始结算] --> B{商家调整}
    B -->|无调整| C[直接生成支付单]
    B -->|有调整| D[执行优惠计算]
    D --> E[生成最终支付单]
    E --> F[完成支付]
```

### 金额计算规范
| 金额类型       | 计算方式                              | 数据来源                  |
|----------------|-------------------------------------|--------------------------|
| 实际支付金额   | Σ(商品PayAmount) + Σ(房费PayAmount) | OrderProduct,OrderRoomPlan |
| 原始金额       | Σ(商品原价×数量) + Σ(房费原价)       | OrderProduct,OrderRoomPlan |


# 第三章 优惠计算算法

## 3.1 基础约定
### 3.1.1 金额单位规则
```go
// 所有金额字段均以分为单位存储
type Order struct {
    PayAmount *int64 `gorm:"..."` // 单位：分（支付时生成）
}

type OrderProduct struct {
    PayAmount *int64 `gorm:"..."` // 单位：分（支付时生成）
}
```

### 3.1.2 金额关系公式
```math
应付金额 = 实际支付金额 + 抹零金额
即：PayFee = TotalFee + ZeroFee
```

### 3.1.3 字段生成时机
| 字段名称               | 生成阶段       | 说明                          |
|------------------------|--------------|-----------------------------|
| Order.PayAmount        | 支付完成时     | 汇总关联商品和房费的PayAmount  |
| OrderProduct.PayAmount | 支付完成时     | 根据优惠计算后的最终商品金额    |
| OrderRoomPlan.PayAmount| 支付完成时     | 最终房费（含优惠调整）          |
| PayBill.TotalFee       | 支付单创建时   | 实际支付金额（扣除优惠后）      |

### 3.1.4 空值约束规则
```go
// 支付前字段状态示例
unpaidOrder := Order{
    PayAmount: nil,  // 支付前为空
    Products: []OrderProduct{
        {PayAmount: nil},  // 支付前为空
    },
    RoomPlans: []OrderRoomPlan{
        {PayAmount: nil},  // 支付前为空
    }
}

// 支付后字段状态示例
paidOrder := Order{
    PayAmount: &1499,  // 支付时计算
    Products: []OrderProduct{
        {PayAmount: &999},  // 商品最终金额
    },
    RoomPlans: []OrderRoomPlan{
        {PayAmount: &500},  // 房费最终金额
    }
}
```

## 3.2 支付金额计算流程
### 3.2.1 核心算法步骤
```mermaid
flowchart TD
    A[开始支付] --> B[初始化空值字段]
    B --> C["计算商品PayAmount（首次赋值）"]
    C --> D["计算房费PayAmount（首次赋值）"]
    D --> E["汇总生成Order.PayAmount"]
    E --> F[生成PayBill记录]
    F --> G[写入抹零金额]
    G --> H[完成支付]
```

### 3.2.2 金额计算实现（更新）
```go
// 支付时计算商品金额
func CalculateProductPayAmount(product *OrderProduct) {
    if product.PayAmount == nil { // 仅首次计算
        base := *product.PayPrice * *product.Quantity
        *product.PayAmount = ApplyDiscount(base, product.DiscountRate)
    }
}

// 支付时计算房费金额 
func CalculateRoomPlanAmount(plan *OrderRoomPlan) {
    if plan.PayAmount == nil { // 仅首次计算
        base := plan.OriginalAmount
        *plan.PayAmount = ApplyRoomDiscount(base, plan.DiscountRate)
    }
}

// 生成订单总金额
func CalculateOrderTotal(order *Order) {
    if order.PayAmount == nil {
        total := 0
        for _, p := range order.Products {
            total += *p.PayAmount // 依赖已计算的商品金额
        }
        for _, r := range order.RoomPlans {
            total += *r.PayAmount // 依赖已计算的房费金额
        }
        *order.PayAmount = total
    }
}
```

## 3.3 商家调整算法
### 核心流程
1. 过滤已打折商品（IsProductDiscount=true）
2. 计算有效商品原价总额
3. 按比例分摊优惠金额
4. 末位吸收计算误差

### 代码实现
```go
func CalculateAdjustment(products []OrderProduct, discount int64) {
    totalOriginal := calcValidOriginal(products)
    remaining := discount
    
    for i, p := range products {
        if p.IsProductDiscount { continue }
        
        ratio := float64(p.OriginalTotal()) / float64(totalOriginal)
        currentDiscount := int64(float64(discount) * ratio)
        
        if isLastItem(i, products) {
            currentDiscount = remaining
        }
        remaining -= currentDiscount
        
        p.PayAmount = p.OriginalTotal() - currentDiscount
    }
}
```

## 3.4 精度处理规范
### 舍入规则（基于分单位）
```mermaid
flowchart TD
    A[金额值（分）] --> B{舍入规则}
    B -->|分位处理| C[保留分位不处理]  // 示例：123分 → 123分
    B -->|角位处理| D[舍去个位分]     // 示例：178分 → 170分
    B -->|元位处理| E[舍去十/个位分]  // 示例：299分 → 200分
    B -->|四舍五入| F[个位分≥5进1]    // 示例：125分 → 130分
```

**规则说明表**：
| 规则类型 | 处理方式                | 分单位示例     | 等价金额  | 数学表达式       |
|----------|-----------------------|--------------|----------|-----------------|
| 分位处理 | 保留原始分位值          | 123 → 123    | ¥1.23    | amount          |
| 角位处理 | 舍去个位分（整除10）     | 178 → 170    | ¥1.70    | amount//10*10   |
| 元位处理 | 舍去十/个位分（整除100） | 299 → 200    | ¥2.00    | amount//100*100 |
| 四舍五入 | 个位分≥5时十位进1       | 125 → 130    | ¥1.30    | (amount+5)//10*10 |

**计算函数更新**：
```go
// 分位处理（保持原值）
func KeepAsIs(amount int64) int64 {
    return amount // 123 → 123
}

// 角位处理（舍去个位分）
func TruncateTo10Cent(amount int64) int64 {
    return amount / 10 * 10  // 178 → 170
}

// 四舍五入处理
func RoundTo10Cent(amount int64) int64 {
    return (amount + 5) / 10 * 10  // 125 → 130
}

// 元位处理（舍去十/个位分）
func TruncateToYuan(amount int64) int64 {
    return amount / 100 * 100  // 299 → 200
}
```

**业务应用场景修正**：
1. 现金交易：使用元位处理（示例：¥2.99 → ¥2.00）
2. 电子支付：使用四舍五入（示例：¥1.25 → ¥1.30）
3. 优惠计算：使用角位处理（示例：¥1.78 → ¥1.70）
4. 精确计算：使用分位处理（示例：¥1.23 → ¥1.23）


