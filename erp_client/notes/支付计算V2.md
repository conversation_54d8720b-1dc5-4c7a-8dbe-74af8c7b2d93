# 第一章 VO定义

## 1.1 SessionVO 场次信息值对象
```go
type SessionVO struct {
	Id                 string `json:"id"`                 // 唯一ID
	SessionId          string `json:"sessionId"`          // 开台ID
	VenueId            string `json:"venueId"`            // 门店ID
	RoomId             string `json:"roomId"`             // 房间ID
	StartTime          int64  `json:"startTime"`          // 开台时间
	EndTime            int64  `json:"endTime"`            // 关房时间
	Duration           int64  `json:"duration"`           // 使用时长
	Status             string `json:"status"`             // 支付状态
	OrderSource        string `json:"orderSource"`        // 订单来源
	CustomerSource     string `json:"customerSource"`     // 客户来源
	CustomerTag        string `json:"customerTag"`        // 客群标签
	AgentPerson        string `json:"agentPerson"`        // 代定人
	DutyPerson         string `json:"dutyPerson"`         // 轮房人
	RankNumber         string `json:"rankNumber"`         // 排位号码
	MinConsume         int64  `json:"minConsume"`         // 最低消费
	RoomFee            int64  `json:"roomFee"`            // 包厢费用
	SupermarketFee     int64  `json:"supermarketFee"`     // 超市费用
	TotalFee           int64  `json:"totalFee"`           // 总计费用
	UnpaidAmount       int64  `json:"unpaidAmount"`       // 未付金额
	PrePayBalance      int64  `json:"prePayBalance"`      // 预付余额
	IsOpenTableSettled bool   `json:"isOpenTableSettled"` // 是否开台立结
	Info               string `json:"info"`               // 备注 attach：联房，union：并房，transfer：转房, opening:开台, swap:互换
	EmployeeId         string `json:"employeeId"`         // 员工ID
	EmployeeIdPay      string `json:"employeeIdPay"`      // 支付员工ID
	Ctime              int64  `json:"ctime"`              // 创建时间
	Utime              int64  `json:"utime"`              // 更新时间
	State              int    `json:"state"`              // 状态
	Version            int    `json:"version"`            // 版本号
}
```

## 1.2 OrderVO 订单信息值对象
```go
type OrderVO struct {
	Id            string `json:"id"`            // ID
	VenueId       string `json:"venueId"`       // 门店ID
	RoomId        string `json:"roomId"`        // 房间ID
	SessionId     string `json:"sessionId"`     // 场次ID
	OrderNo       string `json:"orderNo"`       // 订单编号
	POrderNo      string `json:"pOrderNo"`      // 父订单编号-退款时原订单
	EmployeeId    string `json:"employeeId"`    // 员工ID
	MinimumCharge int64  `json:"minimumCharge"` // 最低消费金额

	ConfigRoomMemberDiscountType    int64 `json:"ConfigRoomMemberDiscountType"`    // 配置-包厢价格会员优惠方式 0:无，1：会员价，2：会员折扣
	ConfigProductMemberDiscountType int64 `json:"ConfigProductMemberDiscountType"` // 配置-商品价格会员优惠方式 0:无，1：会员价，2：会员折扣

	Tag     string `json:"tag"`     // 标签
	Mark    string `json:"mark"`    // 备注
	Type    string `json:"type"`    // 订单类型
	Status  string `json:"status"`  // 订单状态
	Ctime   int64  `json:"ctime"`   // 创建时间
	Utime   int64  `json:"utime"`   // 更新时间
	State   int    `json:"state"`   // 状态
	Version int    `json:"version"` // 版本号

	OrderProductVOs  []OrderProductVO  `json:"orderProductVOs"`
	OrderRoomPlanVOs []OrderRoomPlanVO `json:"orderRoomPlanVOs"`
}
```

## 1.3 OrderProductVO 订单产品值对象
```go
type OrderProductVO struct {
	Id                  string `json:"id"`                  // ID
	VenueId             string `json:"venueId"`             // 门店ID
	RoomId              string `json:"roomId"`              // 房间ID
	SessionId           string `json:"sessionId"`           // 场次ID
	MemberId            string `json:"memberId"`            // 会员ID
	PId                 string `json:"pId"`                 // 退款商品对应的原始OrderProduct.Id
	OrderNo             string `json:"orderNo"`             // 订单ID
	PackageId           string `json:"packageId"`           // 套餐ID
	ProductId           string `json:"productId"`           // 产品ID
	ProductName         string `json:"productName"`         // 产品名称
	Flavors             string `json:"flavors"`             // 口味
	Unit                string `json:"unit"`                // 单位
	Quantity            int64  `json:"quantity"`            // 数量
	OriginalPrice       int64  `json:"originalPrice"`       // 原价
	MemberPrice         int64  `json:"memberPrice"`         // 真实原价-会员价格-白金-钻石
	ProductDiscountable bool   `json:"ProductDiscountable"` // 是否-商品折扣
	MemberDiscountable  bool   `json:"memberDiscountable"`  // 是否-会员折扣
	Giftable            bool   `json:"giftable"`            // 是否-可赠送
	Freeable            bool   `json:"freeable"`            // 是否-可免费
	IsFreeDrinking      bool   `json:"isFreeDrinking"`      // 是否畅饮
	IsMultiProductGift  bool   `json:"isMultiProductGift"`  // 是否多商品赠送

	PayAmount            int64 `json:"payAmount"`            // 总金额-只写一次
	OrderProductDiscount int64 `json:"orderProductDiscount"` // 点单时商品折扣-下单时-只写一次
	MemberDiscount       int64 `json:"memberDiscount"`       // 会员折扣-支付时用-只写一次
	PayProductDiscount   int64 `json:"PayProductDiscount"`   // 支付时商品折扣-支付时用-只写一次

	Mark         string `json:"mark"`         // 产品显示备注
	InPackageTag string `json:"inPackageTag"` // 套内商品标签
	Src          string `json:"src"`          // 套餐来源
	Ctime        int64  `json:"ctime"`        // 创建时间
	Utime        int64  `json:"utime"`        // 更新时间
	State        int    `json:"state"`        // 状态
	Version      int    `json:"version"`      // 版本号

	UnitPrice int64 `json:"unitPrice"` // 计算后的单价
	IsDiscounted bool `json:"isDiscounted"` // 是否已打折
}
```

## 1.4 OrderRoomPlanVO 价格方案值对象
```go
type OrderRoomPlanVO struct {
	Id                 string `json:"id"`                 // ID
	VenueId            string `json:"venueId"`            // 门店ID
	RoomId             string `json:"roomId"`             // 房间ID
	SessionId          string `json:"sessionId"`          // 场次ID
	OrderNo            string `json:"orderNo"`            // 订单ID
	RoomName           string `json:"roomName"`           // 房间名称
	PricePlanId        string `json:"pricePlanId"`        // 方案id
	PricePlanName      string `json:"pricePlanName"`      // 价格方案名称
	StartTime          int64  `json:"startTime"`          // 开始时间
	EndTime            int64  `json:"endTime"`            // 结束时间
	Duration           int    `json:"duration"`           // 买钟时长
	MemberDiscountable bool   `json:"memberDiscountable"` // 是否-会员折扣
	IsGift             bool   `json:"isGift"`             // 是否是赠送
	OriginalPayAmount  int64  `json:"originalPayAmount"`  // 原支付金额
	MinimumCharge      int64  `json:"minimumCharge"`      // 最低消费金额

	PayAmount      int64 `json:"payAmount"`      // 房费
	RoomDiscount   int64 `json:"roomDiscount"`   // 折扣
	MemberDiscount int64 `json:"MemberDiscount"` // 会员折扣

	PricePlanType string `json:"pricePlanType"` // 买钟价格类型
	Ctime         int64  `json:"ctime"`         // 创建时间
	Utime         int64  `json:"utime"`         // 更新时间
	State         int    `json:"state"`         // 状态
	Version       int    `json:"version"`       // 版本号
}
```

## 1.5 PayBillVO 付款单值对象
```go
type PayBillVO struct {
	Id                    string `json:"id"`                    // ID
	VenueId               string `json:"venue_id"`              // 门店id
	RoomId                string `json:"room_id"`               // 房间ID
	SessionId             string `json:"session_id"`            // 场次ID
	PayId                 string `json:"pay_id"`                // 支付号
	PPayId                string `json:"p_pay_id"`              // 退款单对应的支付单号
	ThirdOrderId          string `json:"third_order_id"`        // 第三方支付单号
	TotalFee              int64  `json:"total_fee"`             // 总金额-实际支付金额
	ShouldFee             int64  `json:"should_fee"`            // 应付金额
	ZeroFee               int64  `json:"ZeroFee"`               // 抹零金额
	CreditAmount          int64  `json:"credit_amount"`         // 挂账金额
	ProductDiscount       int64  `json:"ProductDiscount"`       // 商品折扣
	RoomDiscount          int64  `json:"roomDiscount"`          // 房费折扣
	IsFree                bool   `json:"is_free"`               // 是否免单
	ProductDiscountAmount int64  `json:"ProductDiscountAmount"` // 商品减免
	RoomDiscountAmount    int64  `json:"roomDiscountAmount"`    // 房费减免
	ChangeAmount          int64  `json:"change_amount"`         // 现金-找零金额
	DiscountType          int64  `json:"discount_type"`         // 对超过低消部分打折还是全部打折
	ForceMinimumCharge    bool   `json:"force_minimum_charge"`  // 是否强制满低消
	Status                string `json:"status"`                // 状态
	Info                  string `json:"info"`                  // 备注
	EmployeeId            string `json:"employee_id"`           // 员工ID-交班用
	Ctime                 int64  `json:"ctime"`                 // 创建时间
	Utime                 int64  `json:"utime"`                 // 更新时间
	State                 int    `json:"state"`                 // 状态
	Version               int    `json:"version"`               // 版本号

	OrderVOs []OrderVO
}
```

## 1.6 PayRecordVO 支付记录对象
```go
type PayRecordVO struct {
	Id            string `json:"id"`              // ID
	VenueId       string `json:"venue_id"`        // 门店id
	RoomId        string `json:"room_id"`         // 房间ID
	SessionId     string `json:"session_id"`      // 场次ID
	Pid           string `json:"pid"`             // 退款单对应的支付单号
	PayBillId     string `json:"pay_bill_id"`     // paybillId
	ThirdOrderId  string `json:"third_order_id"`  // 第三方支付单号
	TotalFee      int64  `json:"total_fee"`       // 总金额-实际支付金额
	Status        string `json:"status"`          // 状态
	RefundWayType string `json:"refund_way_type"` // 退款方式
	PayType       string `json:"pay_type"`        // 支付类型-微信 支付宝 找零 挂账
	ProductName   string `json:"product_name"`    // 商品名称
	Info          string `json:"info"`            // 备注
	FinishTime    int64  `json:"finish_time"`     // 完成时间
	BillDate      int64  `json:"bill_date"`       // 账单日期
	EmployeeId    string `json:"employee_id"`     // 员工ID-交班用
	Ctime         int64  `json:"ctime"`           // 创建时间
	Utime         int64  `json:"utime"`           // 更新时间
	State         int    `json:"state"`           // 状态
	Version       int    `json:"version"`         // 版本号
}

```

## 1.7 OrderAndPayVO 订单和支付单值对象
```go
type OrderAndPayVO struct {
	Id        string `json:"id"`        // ID
	OrderNo   string `json:"orderNo"`   // 单号
	SessionId string `json:"sessionId"` // 场次ID
	PayId     string `json:"payId"`     // 支付号
	Ctime     int64  `json:"ctime"`     // 创建时间
	Utime     int64  `json:"utime"`     // 更新时间
	State     int    `json:"state"`     // 状态
	Version   int    `json:"version"`   // 版本号
}
```

# 第二章 计算支付总金额
## 2.1 计算价格准则

1. 计算支付总金额涉及到第一章的VO实体
2. 总金额公式：
	- 总支付金额 = (商品总金额 + 房费总金额) - 优惠
   	- 商品总金额 = sum(商品折后价 × 数量)
	- 房费总金额 = sum(房费方案折后价)
	- 折后价 = 会员价（若有）或原价 × 商品折扣
	- 优惠=商家优惠 或 低消折扣
   最低消费逻辑：当商品总金额 < 最低消费时，按最低消费金额计算
4. 金额单位处理：所有金额单位为分（1元=100分）
5. 折扣表示方式：
   - 折扣值使用百分制（85折传值85）
   - Discount为折扣值，DiscountAmount为折扣金额
6. 写出伪代码，计算逻辑写到函数里边，方法体里伪代码最多写5行

### 低消计算 在方法CalculateTotalPayment中的低消部分
    DiscountType          int64  `json:"discount_type"`         // 对超过低消部分打折还是全部打折
	ForceMinimumCharge    bool   `json:"force_minimum_charge"`  // 是否强制满低消
  0. 方法calcProductUnitPrice中 如果命中"4. 商品折扣处理"，标记商品为打折状态，会影响第一步的执行
  1. 提取未打折的商品列表
  2. 第一步：针对未打折商品 根据DiscountType 计算折后价格（商品折扣）
  3. 第二步：针对未打折商品 折扣价格-商品支付减免
  4. 第三步：房间计算折扣价格（房费折扣）
  5. 第四步：折扣价格-房费支付减免
  6. 第五步：根据 ForceMinimumCharge 判断最后支付金额
  7. 超低消折扣 只能对商品打折


## 2.2 伪代码实现
```go
// 计算商品单价，并标记打折状态
func calcProductUnitPrice(product *OrderProductVO, memberDiscountType int64) int64 {
    basePrice := product.OriginalPrice

	// 1. 特殊情况处理
    if product.Freeable || product.IsFreeDrinking || product.IsMultiProductGift || product.Giftable {
        return 0
    }
    
    // 2. 会员价处理
    if product.MemberDiscountable && memberDiscountType == PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE {
        if product.MemberPrice > 0 {
            basePrice = product.MemberPrice
        }
    }
    
    // 3. 会员折扣处理
    if product.MemberDiscountable && memberDiscountType == PRODUCT_MEMBER_DISCOUNT_TYPE_DISCOUNT {
        if product.MemberDiscount > 0 {
            basePrice = basePrice * product.MemberDiscount / 100
        }
    }
    
    // 4. 商品折扣处理 - 标记打折状态
    if product.ProductDiscountable && product.OrderProductDiscount > 0 {
        basePrice = basePrice * product.OrderProductDiscount / 100
        product.IsDiscounted = true  // 标记为已打折
    }
    
    return basePrice
}

// 计算商品支付金额
func calcProductPayment(products []OrderProductVO, order OrderVO) int64 {
    var total int64 = 0
    for _, p := range products {
        // 计算单价
        unitPrice := calcProductUnitPrice(&p, order.ConfigProductMemberDiscountType)
        // 计算总价 = 单价 * 数量
        productTotal := unitPrice * p.Quantity
        total += productTotal
    }
    return total
}

// 计算房费单价
func calcRoomUnitPrice(plan OrderRoomPlanVO, memberDiscountType int64) int64 {
    // 基础价格判断
    basePrice := plan.OriginalPayAmount
    
    // 1. 会员价处理
    if plan.MemberDiscountable && memberDiscountType == ROOM_MEMBER_DISCOUNT_TYPE_PRICE {
        if plan.MemberPrice > 0 {
            basePrice = plan.MemberPrice
        }
    }
    
    // 2. 会员折扣处理
    if plan.MemberDiscountable && memberDiscountType == ROOM_MEMBER_DISCOUNT_TYPE_DISCOUNT {
        if plan.MemberDiscount > 0 {
            basePrice = basePrice * plan.MemberDiscount / 100
        }
    }
    
    // 3. 房费折扣处理
    if plan.RoomDiscount > 0 {
        basePrice = basePrice * plan.RoomDiscount / 100
    }
    
    return basePrice
}

// 计算房费支付金额
func calcRoomPayment(plans []OrderRoomPlanVO, order OrderVO) int64 {
    var total int64 = 0
    for _, p := range plans {
        // 计算房费
        roomFee := calcRoomUnitPrice(p, order.ConfigRoomMemberDiscountType)
        total += roomFee
    }
    return total
}

// 计算最终支付总金额
func CalculateTotalPayment(session SessionVO, order OrderVO, payBill PayBillVO) int64 {
    // 0. 计算商品单价并标记打折状态
    var products []OrderProductVO = order.OrderProductVOs
    for i := range products {
        products[i].UnitPrice = calcProductUnitPrice(&products[i], order.ConfigProductMemberDiscountType)
    }
    
    // 1. 提取未打折的商品列表
    var undiscountedProducts []OrderProductVO
    var discountedAmount int64 = 0
    for _, p := range products {
        if !p.IsDiscounted {
            undiscountedProducts = append(undiscountedProducts, p)
        } else {
            discountedAmount += p.UnitPrice * p.Quantity
        }
    }
    
    // 2. 针对未打折商品计算折后价格
    var undiscountedAmount int64 = 0
    for _, p := range undiscountedProducts {
        productAmount := p.UnitPrice * p.Quantity
        undiscountedAmount += productAmount
    }
    
    if payBill.ProductDiscount > 0 {
        if payBill.DiscountType == DISCOUNT_TYPE_ALL {
            // 全部打折
            undiscountedAmount = undiscountedAmount * payBill.ProductDiscount / 100
        } else {
            // 仅超过低消部分打折
            totalProductAmount := undiscountedAmount + discountedAmount
            if totalProductAmount > session.MinConsume {
                overMinAmount := totalProductAmount - session.MinConsume
                discountedOverMin := overMinAmount * payBill.ProductDiscount / 100
                undiscountedAmount = undiscountedAmount - overMinAmount + discountedOverMin
            }
        }
    }
    
    // 3. 未打折商品减免
    if payBill.ProductDiscountAmount > 0 {
        undiscountedAmount = max(0, undiscountedAmount - payBill.ProductDiscountAmount)
    }
    
    // 4. 房费折扣计算
    roomAmount := calcRoomPayment(order.OrderRoomPlanVOs, order)
    if payBill.RoomDiscount > 0 {
        roomAmount = roomAmount * payBill.RoomDiscount / 100
    }
    
    // 5. 房费减免
    if payBill.RoomDiscountAmount > 0 {
        roomAmount = max(0, roomAmount - payBill.RoomDiscountAmount)
    }
    
    // 6. 计算总金额并判断是否强制满足最低消费
    totalAmount := undiscountedAmount + discountedAmount + roomAmount
    
    if payBill.ForceMinimumCharge && totalAmount < session.MinConsume {
        totalAmount = session.MinConsume
    }
    
    return totalAmount
}
```

说明：
1. 商品打折状态：
   - 只在商品折扣处理时标记打折状态
   - 会员价和会员折扣不影响打折状态

2. 计算步骤严格按照要求：
   - 先提取未打折商品列表
   - 分别处理未打折和已打折商品
   - 房费单独计算折扣
   - 最后判断是否强制满足最低消费

3. 低消折扣特点：
   - 只对商品部分生效
   - 根据DiscountType决定折扣范围
   - 不影响房费计算

4. 金额处理：
   - 所有金额单位为分
   - 折扣采用百分制(85折=85)
   - 使用max函数确保金额不为负

# 第三章 支付时OrderProduct.PayAmount值的回写
## 3.1 计算公式如下：
 1. 

## 3.2 伪代码实现

```go

```




