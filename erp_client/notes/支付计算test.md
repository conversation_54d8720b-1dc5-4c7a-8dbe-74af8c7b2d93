graph TD
    A[开始计算] --> B[计算已支付订单金额]
    B --> C[计算实际最低消费金额]
    C --> D[计算所有商品单价]
    D --> E{商品是否特殊?}
    E -- 是 --> F[返回0]
    E -- 否 --> G{是否有会员价?}
    G -- 是 --> H[使用会员价]
    G -- 否 --> I{是否有会员折扣?}
    I -- 是 --> J[应用会员折扣]
    I -- 否 --> K{是否有商品折扣?}
    K -- 是 --> L[应用商品折扣并标记]
    K -- 否 --> M[使用原价]

    N[区分商品类型] --> O[已打折商品]
    N --> P[未打折可打折商品]
    N --> Q[未打折不可打折商品]

    O --> R[计算已打折总金额]
    P --> S[计算可打折总金额]
    Q --> T[计算不可打折总金额]

    U{是否有商品折扣?} -- 是 --> V{折扣类型?}
    V -- 全部打折 --> W[对可打折金额应用折扣]
    V -- 超低消部分打折 --> X[计算超低消金额]
    X --> Y[仅对超低消部分应用折扣]

    Z[计算房费] --> AA{是否有会员价?}
    AA -- 是 --> AB[使用会员价]
    AA -- 否 --> AC{是否有会员折扣?}
    AC -- 是 --> AD[应用会员折扣]
    AC -- 否 --> AE{是否有房费折扣?}
    AE -- 是 --> AF[应用房费折扣]

    AG[合计总金额] --> AH{是否强制低消?}
    AH -- 是 --> AI[使用低消金额]
    AH -- 否 --> AJ[使用计算金额]
    AI --> AK[结束]
    AJ --> AK



graph TD
    A[开始回写商品价格] --> B[复制原始商品数据]
    B --> C[筛选有效商品]
    C --> D{是否特殊商品?}
    D -- 是 --> E[设置金额为0]
    D -- 否 --> F[保留计算]

    F --> G[计算商品总金额]
    G --> H{单个商品计算}
    H --> I[基础金额=单价×数量]
    I --> J{是否有PayProductDiscount?}
    J -- 是 --> K[应用商品折扣]
    J -- 否 --> L[保持基础金额]

    M{是否有优惠金额?} -- 否 --> N[直接设置PayAmount]
    M -- 是 --> O[计算总优惠金额]
    O --> P[按比例分摊优惠]
    P --> Q[计算每个商品优惠]
    Q --> R[设置最终PayAmount]

    R --> S[累计实际分摊金额]
    S --> T{是否有精度损失?}
    T -- 是 --> U[差额计入最后商品]
    T -- 否 --> V[完成回写]
    U --> V



graph TD
    A[开始回写房费价格] --> B[复制原始房费数据]
    B --> C[筛选有效房费]
    C --> D{是否赠送房费?}
    D -- 是 --> E[设置金额为0]
    D -- 否 --> F[保留计算]

    F --> G[计算房费总金额]
    G --> H{单个房费计算}
    H --> I[使用原始金额]
    I --> J{是否有PayRoomDiscount?}
    J -- 是 --> K[应用房费折扣]
    J -- 否 --> L[保持原始金额]

    M{是否有优惠金额?} -- 否 --> N[直接设置PayAmount]
    M -- 是 --> O[获取房费优惠总额]
    O --> P[按比例分摊优惠]
    P --> Q[计算每个房费优惠]
    Q --> R[设置最终PayAmount]

    R --> S[累计实际分摊金额]
    S --> T{是否有精度损失?}
    T -- 是 --> U[差额计入最后房费]
    T -- 否 --> V[完成回写]
    U --> V





-------------------------------------------------------------------------------------


graph TD
    A[开始] --> B[计算实际最低消费]
    B --> C[计算所有商品单价]
    C --> D[区分商品类型]
    D --> E[已打折商品]
    D --> F[未打折商品]
    F --> G[可打折商品]
    F --> H[不可打折商品]
    
    E --> I[计算已打折总额]
    G --> J[计算可打折总额]
    H --> K[计算不可打折总额]
    
    J --> L{是否有商品折扣?}
    L -- 是 --> M{折扣类型}
    M -- 全部打折 --> N[计算折后金额]
    M -- 超低消打折 --> O[计算超低消部分]
    
    I --> P[合计总金额]
    N --> P
    O --> P
    K --> P
    
    Q[计算房费] --> R[应用房费折扣]
    R --> S[应用房费减免]
    S --> P
    
    P --> T{是否强制低消?}
    T -- 是 --> U[使用低消金额]
    T -- 否 --> V[使用计算金额]



graph TD
    A[开始] --> B[复制原始商品数据]
    B --> C[筛选有效商品]
    C --> D[计算有效商品总金额]
    D --> E{是否有优惠金额?}
    E -- 否 --> F[直接设置PayAmount]
    E -- 是 --> G[计算总优惠金额]
    G --> H[按比例分摊优惠]
    H --> I[处理精度损失]
    F --> J[结束]
    I --> J


graph TD
    A[开始] --> B[复制原始房费数据]
    B --> C[筛选有效房费]
    C --> D[计算有效房费总金额]
    D --> E{是否有优惠金额?}
    E -- 否 --> F[直接设置PayAmount]
    E -- 是 --> G[计算总优惠金额]
    G --> H[按比例分摊优惠]
    H --> I[处理精度损失]
    F --> J[结束]
    I --> J



classDiagram
    class SessionVO {
        <<场次信息>>
        +String id
        +String sessionId
        +Int64 minConsume
        +Int64 roomFee
        +Int64 totalFee
        +Bool isOpenTableSettled
        +其他基础字段...
    }

    class OrderVO {
        <<订单信息>>
        +String id
        +String sessionId
        +Int64 minimumCharge
        +Int64 configRoomMemberDiscountType
        +Int64 configProductMemberDiscountType
        +OrderProductVO[] orderProductVOs
        +OrderRoomPlanVO[] orderRoomPlanVOs
    }

    class OrderProductVO {
        <<订单商品>>
        +String id
        +String sessionId
        +String orderNo
        +Int64 quantity
        +Int64 originalPrice
        +Int64 memberPrice
        +Bool productDiscountable
        +Bool memberDiscountable
        +Int64 payAmount
        +Int64 orderProductDiscount
        +Int64 memberDiscount
        +Bool isDiscounted
    }

    class OrderRoomPlanVO {
        <<房费方案>>
        +String id
        +String sessionId
        +String orderNo
        +Int64 originalPayAmount
        +Int64 minimumCharge
        +Int64 payAmount
        +Int64 roomDiscount
        +Int64 memberDiscount
        +Bool memberDiscountable
        +Bool isGift
    }

    class PayBillVO {
        <<付款单>>
        +String id
        +String sessionId
        +Int64 totalFee
        +Int64 shouldFee
        +Int64 productDiscount
        +Int64 roomDiscount
        +Int64 productDiscountAmount
        +Int64 roomDiscountAmount
        +Int64 discountType
        +Bool forceMinimumCharge
        +OrderVO[] orderVOs
    }

    class PayRecordVO {
        <<支付记录>>
        +String id
        +String sessionId
        +String payBillId
        +Int64 totalFee
        +String payType
        +String status
    }

    class OrderAndPayVO {
        <<订单支付关联>>
        +String id
        +String orderNo
        +String sessionId
        +String payId
    }

    SessionVO "1" -- "*" OrderVO : 包含
    OrderVO "1" -- "*" OrderProductVO : 包含
    OrderVO "1" -- "*" OrderRoomPlanVO : 包含
    PayBillVO "1" -- "*" OrderVO : 包含
    PayBillVO "1" -- "*" PayRecordVO : 关联
    OrderAndPayVO -- OrderVO : 关联
    OrderAndPayVO -- PayBillVO : 关联