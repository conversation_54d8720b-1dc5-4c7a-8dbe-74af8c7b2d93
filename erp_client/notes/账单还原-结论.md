订单支付系统，
    下单-》支付

支持多种支付方式
   现金
   微信/支付宝记账
   线上扫码支付
   会员卡支付（本金、通用赠金、房费赠金、商品赠金）

支付：
订单    商品                            房费
o1     [op1x3 op2x2 op3x5 op4x7]      无
o2     [op5x5]                        无
o6     无                              [om1 om2]   

订单         收款单    支付方式
[o1 o2 o3]   b1     [p1 p2]
[o4 o5 o6]      b2     [p3 p1]

退款：
退款原则：沿着原订单进行退款，将属于原订单的退款商品放到同一个退款订单下管理
数据表示：
订单  父订单  商品
o11   o1    [op1x2 op3x2]
o12   o2    [op5x3]

订单      收款单  父收款单  
o11       b11    b1
o12       b12    b2

账单还原请求参数
[b1 b2]

账单还原原则：
1. 将[b1 b2]剩余的的金额（可能存在退款）原路径返回，并将[b1 b2]所涉及的账单（包括对应的退款）均标记为isback=1
2. 退款方式需要考虑会员卡退款（本金、通用赠金、房费赠金、商品赠金）
3. 不需要操作订单

