# 订单支付开发指南

## 1. 领域服务分析

### 1.1 已有服务

1. RuleService (规则服务)
   - 位置: erp_client/domain/rule/service/service.go
   - 已实现方法:
     - Evaluate: 评估规则组
     - LoadRuleGroup: 加载规则组
     - Apply: 应用单个规则
     - Combine: 组合多个规则
     - CRUD操作

2. SessionService (场次服务)
   - 位置: erp_client/domain/valueobject/business/session/service/session_service.go
   - 已实现方法:
     - CreateSession: 创建场次
     - UpdateSessionStatus: 更新场次状态
   - 需要新增方法:
     - FindAllSession: 查询所有场次信息

3. PayBillService (支付账单服务)
   - 位置: erp_client/domain/traderecord/service/pay_bill_service.go
   - 已实现方法:
     - FindAllPayBill: 查询所有支付账单

4. OrderService (订单服务)
   - 位置: erp_client/domain/traderecord/service/order_service.go
   - 已实现方法:
     - CreateOrdersWithTransaction: 在事务中创建订单相关数据
     - GenerateOrderInfo: 生成订单基础信息
     - PrepareOrders: 准备订单数据
     - FindAllOrder: 查询所有订单

### 1.2 需要新建的服务

1. PayService (支付服务)
   - 需要实现的方法:
     - GetPayOrderInfos: 获取需支付订单信息
     - GetPayOrderInfosWithRoomAndProductBySessionId: 获取需支付订单的房间和商品信息
     - ValidatePayDiscountIsValid: 验证支付折扣是否合法
     - Calc2UpdateOrdersAndRoomPlansAndProducts: 计算更新订单、房间计划和商品
     - BuildPayDataForPay: 构建支付数据
     - SaveBatchTxForPay: 批量保存支付数据
     - TransformPayGate: 转换支付网关
     - AfterPayCallbackCoUpdateInfoByPayId: 支付回调后更新信息

2. OrderValidator (订单验证器)
   - 需要实现的方法:
     - ValidatePayHasDiscount: 验证支付是否有折扣

## 2. 开发任务

1. 实现 PayService
   - 创建接口定义
   - 实现具体服务类
   - 实现所有必要的方法
   - 添加单元测试

2. 实现 OrderValidator
   - 创建接口定义
   - 实现具体验证器类
   - 实现验证方法
   - 添加单元测试

3. 扩展 SessionService
   - 添加 FindAllSession 方法实现
   - 更新单元测试

## 3. 注意事项

1. 所有新增服务都需要遵循领域驱动设计原则
2. 确保新增服务与现有服务的良好集成
3. 完善的错误处理和日志记录
4. 添加充分的单元测试覆盖
5. 文档化所有新增的服务和方法

## 4. 后续计划

1. 完善服务实现
2. 进行集成测试
3. 编写详细的API文档
4. 进行性能测试和优化 