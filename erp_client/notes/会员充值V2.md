# 会员充值接口实现
## 第一章 会员充值VO定义
### 1.1 会员信息VO
```go

// MemberVO 会员信息值对象
type MemberVO struct {
	Id         string `json:"id"`         // ID
	VenueId    string `json:"venueId"`    // 场馆 ID
	Name       string `json:"name"`       // 会员姓名
	Phone      string `json:"phone"`      // 会员手机号
	CardNumber string `json:"cardNumber"` // 会员卡号 UNI_key
	Points     int    `json:"points"`     // 会员卡积分
	Balance    int64  `json:"balance"`    // 会员卡余额
	CardType   string `json:"cardType"`   // 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical_card, virtual_card, electronic_card
	CardName   string `json:"cardName"`   // 卡名称

	Birthday int64  `json:"birthday"` // 生日
	Gender   string `json:"gender"`   // 性别

	PrincipalBalance int64  `json:"principalBalance"` // 本金余额
	BonusBalance     int64  `json:"bonusBalance"`     // 赠金余额
	Source           string `json:"source"`           // 来源

	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳
	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号
}
```

### 1.2 会员充值账单实体
```go
// MemberRechargeBill 会员充值账单实体
type MemberRechargeBillVO struct {
	Id         string `json:"id"`         // ID
	VenueId    string `json:"venueId"`    // 门店ID
	RoomId     string `json:"roomId"`     // 房间ID
	EmployeeId string `json:"employeeId"` // 员工ID-操作人
	MemberId   string `json:"memberId"`   // 会员ID
	CardId     string `json:"cardId"`     // 会员卡ID
	CardNumber string `json:"cardNumber"` // 会员卡号

	BillId  string `json:"billId"`  // 充值账单号
	BillPid string `json:"billPid"` // 退款账单号

	TotalFee    int64 `json:"totalFee"`    // 充值金额
	BonusAmount int64 `json:"bonusAmount"` // 赠送金额

	Direction  string `json:"direction"`  // 充值方向,如: 充值, 退款，枚举：recharge, refund
	Status     string `json:"status"`     // 状态
	Info       string `json:"info"`       // 备注
	FinishTime int64  `json:"finishTime"` // 完成时间
	BillDate   int64  `json:"billDate"`   // 账单日期

	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳
	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号
}
```

### 1.3 会员充值记录实体
```go
// MemberRechargeRecord 会员充值记录实体
type MemberRechargeRecordVO struct {
	Id         string `json:"id"`         // ID
	VenueId    string `json:"venueId"`    // 门店ID
	RoomId     string `json:"roomId"`     // 房间ID
	EmployeeId string `json:"employeeId"` // 员工ID-操作人
	MemberId   string `json:"memberId"`   // 会员ID

	BillId       string `json:"billId"`       // 充值账单号
	PayId        string `json:"payId"`        // 支付单ID
	PayPid       string `json:"payPid"`       // 退款单ID
	ThirdOrderId string `json:"thirdOrderId"` // 第三方支付单号
	TotalFee     int64  `json:"totalFee"`     // 总金额-实际支付金额
	Status       string `json:"status"`       // 状态 success/refund
	PayType      string `json:"payType"`      // 支付类型-微信 支付宝 找零 挂账
	PaySource    string `json:"paySource"`    // 支付来源-乐刷等第三方支付方式-微信/支付宝
	ProductName  string `json:"productName"`  // 商品名称
	Info         string `json:"info"`         // 备注
	FinishTime   int64  `json:"finishTime"`   // 完成时间
	BillDate     int64  `json:"billDate"`     // 账单日期-冗余-用于统计
	BQROneCode   string `json:"bQROneCode"`   // BShowQR支付方式的BQROneCode

	Ctime   int64 `json:"ctime"`   // 创建时间戳
	Utime   int64 `json:"utime"`   // 更新时间戳
	State   int   `json:"state"`   // 状态值
	Version int   `json:"version"` // 版本号
}

```
### 1.4 充值请求入参
```go
type V3QueryMemberRechargeReqDto struct {
	VenueId                 *string                     `json:"venueId"`                 // 门店ID
	EmployeeId              *string                     `json:"employeeId"`              // 员工ID-操作人
	MemberId                *string                     `json:"memberId"`                // 会员ID
	CardId                  *string                     `json:"cardId"`                  // 会员卡ID
	CardNumber              *string                     `json:"cardNumber"`              // 会员卡号
	TotalFee                *int64                      `json:"totalFee"`                // 充值金额
	BonusAmount             *int64                      `json:"bonusAmount"`             // 赠送金额
	MemberRechargeRecordVOs []vo.MemberRechargeRecordVO `json:"memberRechargeRecordVOs"` // 会员充值记录
}

// MemberRechargeContext 会员充值上下文
type MemberRechargeContext struct {
	// 入参
	ReqDto V3QueryMemberRechargeReqDto // 请求参数

	// 中间状态
	Member po.Member // 会员信息

	// 需要传入事务的数据（加Add前缀）
	AddRechargeBill    po.MemberRechargeBill     // 待保存的充值账单
	AddRechargeRecords []po.MemberRechargeRecord // 待保存的充值记录
	UpdateMembers      []po.Member               // 可能需要更新的余额信息，如果没有第三方支付，此字段有值，兼容保存多个会员

	// 计算结果
	TotalFee            int64 // 充值总金额
	BonusAmount         int64 // 赠送金额（设为0）
	NewPrincipalBalance int64 // 新的本金余额
	NewBonusBalance     int64 // 新的赠送余额
	NewTotalBalance     int64 // 新的总余额

	// 处理标记
	PaymentResults map[string]string // 支付结果信息（如支付URL等）
}
```

## 第二章 需求分析
### 2.1 实现目标
- 会员充值涉及到的实体 收款单、支付记录、会员卡余额表
- 会员卡充值后数据表现
    - 在收款单中增加一条记录用来记录充值账单号、充值金额、赠金、类型（normal）、状态等
    - 在支付记录中增加记录（可能多条）来记录支付单号、充值账单号、支付金额、状态、完成时间等
        - 支付记录中如有第三方支付（乐刷等聚合支付），在回调中来更新支付状态、第三方支付单号、支付来源、完成时间
    - 会员表余额的更新方式
        - 支付方式均为记账支付，直接更新余额
        - 回调中更新余额记录
- 定时脚本用来更新支付回调异常的三方订单

### 2.2 实现步骤及服务函数拆分

#### 2.2.1 服务函数整体拆分

##### 1. 参数校验
- 验证充值金额有效性（大于0）
- 验证会员信息完整性（会员ID或卡号必须提供一个）
- 验证支付记录列表非空且格式正确
- 验证场馆ID和操作员ID

##### 2. 准备数据
- 根据会员ID或卡号查询会员信息
- 获取会员当前余额信息（本金、赠金）
- 查询场馆信息（如需要）
- 查询充值规则（如有赠金规则）

##### 3. 构造数据
- 生成充值账单号
- 构建MemberRechargeBill对象
- 构建MemberRechargeRecord对象列表
- 计算充值后的新余额
- 缓存所有中间计算结果至上下文

##### 4. 事务处理
- 保存充值账单
- 保存充值记录
- 若全部为记账类型支付，直接更新会员余额
- 若有第三方支付，仅保存订单信息，不更新余额

##### 5. 处理回调（异步）
- 第三方支付回调处理
- 根据回调结果更新支付记录状态
- 若支付成功，更新会员余额
- 处理回调结果返回

##### 6. 定时任务（异步补偿）
- 扫描5分钟内创建的未完成订单
- 向第三方查询订单状态
- 更新本地订单状态和会员余额

#### 2.2.2 重要说明事项

1. **本次仅实现充值功能**，暂不考虑退款流程
2. **并发控制**：后续会对单一卡号充值加锁，暂时不考虑并发问题
3. **事务管理**：维持基本的事务一致性，不深入讨论复杂场景
4. **回调异常处理**：通过定时任务扫描5分钟内创建的新订单来补偿
5. **支付验签**：使用第三方接口提供的验签功能，本服务不处理

#### 2.2.3 后续文档完善建议

1. 服务整体流程图（充值发起→处理→回调→补偿）
2. 关键服务函数定义及职责
3. 数据流转示意图（会员→账单→支付记录→余额更新）
4. 异常处理逻辑（超时、回调失败等）
5. 补偿机制详细说明

### 2.3 伪代码及服务函数定义
