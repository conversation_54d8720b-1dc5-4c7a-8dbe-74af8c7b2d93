订单支付系统，
    下单-》支付

支持多种支付方式
   现金
   微信/支付宝记账
   线上扫码支付
   会员卡支付（本金、通用赠金、房费赠金、商品赠金）

订单    商品                                房费
o1     [op1x3 op2x2 op3x5 op4x7]           无
o2     [op5x5]                             无
o3     [op6x5]                             无
o4     [op7x5]                             无
o5     无                                  [om1 om2]

订单         收款单    支付方式
[o1 o2 o3]   b1     [p1 p2]
[o4 o5]      b2     [p3 p1]


如果p1为会员卡支付
入参（本金、通用赠金、房费赠金、商品赠金）

问题：
1. 怎么校验订单支付的关于会员卡的使用限制

会员卡使用限制说明：

1. 会员卡状态校验：
   - 会员卡必须存在且状态正常（未锁定、未注销）
   - 会员卡必须属于当前门店
   - 会员卡必须在有效期内

2. 余额校验：
   - 本金余额必须足够支付本金部分
   - 房费赠金余额必须足够支付房费赠金部分
   - 商品赠金余额必须足够支付商品赠金部分
   - 通用赠金余额必须足够支付通用赠金部分
   - 总余额必须等于本金余额+房费赠金+商品赠金+通用赠金

3. 支付限制：
   - 会员卡等级可以设置支付限制，包括：
     - 允许的支付方式（WECHAT、ALIPAY、BALANCE等）
     - 单次最小支付金额
     - 单次最大支付金额

4. 消费时间限制：
   - 会员卡等级可以设置消费时间限制，包括：
     - 允许消费的时间段（开始时间、结束时间）
     - 允许消费的星期几（1-7分别代表周一到周日）

5. 其他限制：
   - 每日房间使用限制
   - 最低消费金额限制
   - 享受折扣的最低余额限制

6. 入参和订单内容匹配校验：
   - 如果入参使用了房费赠金，订单中必须包含房费项目
   - 如果入参使用了商品赠金，订单中必须包含商品项目
   - 如果入参使用了通用赠金，订单中可以包含任意类型的项目
   - 如果入参使用了本金，订单中可以包含任意类型的项目
   - 入参的各类金额必须与订单中对应类型的金额相匹配
   - 入参的总金额必须等于订单的总金额

7. 金额匹配详细规则：
   - 房费赠金使用规则：
     - 如果订单中没有房费项目，入参中不能使用房费赠金
     - 如果订单中有房费项目，入参中使用的房费赠金不能超过订单中的房费总额
     - 如果订单中有房费项目，入参中使用的房费赠金必须小于等于订单中的房费总额
   - 商品赠金使用规则：
     - 如果订单中没有商品项目，入参中不能使用商品赠金
     - 如果订单中有商品项目，入参中使用的商品赠金不能超过订单中的商品总额
     - 如果订单中有商品项目，入参中使用的商品赠金必须小于等于订单中的商品总额
   - 通用赠金使用规则：
     - 通用赠金可以用于支付任意类型的项目
     - 通用赠金的使用金额不能超过订单中对应类型项目的剩余未支付金额
   - 本金使用规则：
     - 本金可以用于支付任意类型的项目
     - 本金的使用金额不能超过订单中对应类型项目的剩余未支付金额
   - 总金额规则：
     - 入参中所有类型金额（本金+房费赠金+商品赠金+通用赠金）的总和必须等于订单的总金额
     - 每种类型的金额都不能为负数
     - 每种类型的金额都不能超过订单中对应类型项目的总额

8. 支付优先级规则：
   - 房费项目支付优先级：
     1. 优先使用房费赠金
     2. 房费赠金不足时，使用通用赠金
     3. 通用赠金不足时，使用本金
   - 商品项目支付优先级：
     1. 优先使用商品赠金
     2. 商品赠金不足时，使用通用赠金
     3. 通用赠金不足时，使用本金
   - 其他项目支付优先级：
     1. 优先使用通用赠金
     2. 通用赠金不足时，使用本金

实现方式：
1. 在会员卡等级配置中设置合适的限制条件
2. 在支付前进行完整的校验，包括：
   - 会员卡状态
   - 余额是否足够
   - 支付金额是否在限制范围内
   - 消费时间是否在允许范围内
   - 入参和订单内容的匹配性
   - 各类金额的匹配性
   - 支付优先级的合理性
3. 在支付时进行事务处理，确保扣款操作的原子性
4. 记录会员卡消费记录，方便后续查询和统计


