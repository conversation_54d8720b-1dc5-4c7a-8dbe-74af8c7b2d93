订单支付系统，
    下单-》支付

支持多种支付方式
   现金
   微信/支付宝记账
   线上扫码支付
   会员卡支付（本金、通用赠金、房费赠金、商品赠金）

订单    商品  
o1     [op1x3 op2x2 op3x5 op4x7]   
o2     [op5x5]

订单         收款单    支付方式
[o1 o2 o3]   b1     [p1 p2]
[o4 o5]      b2     [p3 p1]

退款：
请求：退款商品
op1x2
op3x2
op5x3

退款原则：沿着原订单进行退款，将属于原订单的退款商品放到同一个退款订单下管理
数据表示：
订单  父订单  商品
o11   o1    [op1x2 op3x2]
o12   o2    [op5x3]

订单      收款单  父收款单  
o11       b11    b1
o12       b12    b2


退款支付原则：
退款需要按支付方式退款
```go
// 支付类型退款顺序,值越小，优先级越高
// 会员卡>乐刷>微信记账>支付宝记账>银联记账>现金
// 退款时，先退现金，再退乐刷，再退微信记账，再退支付宝记账，再退银联记账，最后退会员卡
// 会员卡内 退款顺序，商品赠金/房费赠金>通用赠金>本金
var REFUND_ORDER_WIGHT = map[string]int{
	PAY_TYPE_MEMBER_CARD:    1, // 会员卡 商品赠金/房费赠金>通用赠金>本金
	PAY_TYPE_LESHUA_BSHOWQR: 2, // 乐刷付款码
	PAY_TYPE_RECORD_WECHAT:  3, // 微信记账
	PAY_TYPE_RECORD_ALIPAY:  4, // 支付宝记账
	PAY_TYPE_RECORD_BANK:    5, // 银联记账
	PAY_TYPE_RECORD_CASH:    6, // 现金
}
```

让我分析一下退款分配-结论.md中的退款算法规划：

1. 退款的基本流程：
- 输入：需要退款的商品列表（包含商品和数量）
- 输出：按原订单分组的退款订单

2. 退款订单分组原则：
- 按照原订单进行分组
- 将属于同一个原订单的退款商品放在同一个退款订单下管理
- 每个退款订单都会关联到原订单（通过父订单字段）

3. 退款支付分配原则：
- 退款需要按照支付方式进行分配
- 支付方式的退款优先级（从高到低）：
  1. 会员卡（商品赠金/房费赠金 > 通用赠金 > 本金）
  2. 乐刷付款码
  3. 微信记账
  4. 支付宝记账
  5. 银联记账
  6. 现金

4. 数据结构设计：
- 退款订单(RefundOrder)：
  - 关联原订单（父订单）
  - 包含退款商品列表
- 退款收款单(RefundBill)：
  - 关联原收款单（父收款单）
  - 关联退款订单

5. 退款算法步骤：
```
1. 接收退款商品列表
2. 按原订单对退款商品进行分组
3. 为每个分组创建退款订单
4. 为每个退款订单创建对应的退款收款单
5. 按照支付方式优先级顺序进行退款金额分配
6. 对于会员卡支付，按照赠金类型优先级进行分配
```

6. 需要注意的问题：
- 会员卡退款需要特别处理，因为涉及多种赠金类型
- 退款金额的分配需要严格按照支付方式的优先级顺序
- 需要保证退款金额的准确性，避免出现金额不匹配的情况

这个退款算法设计考虑了实际业务场景，通过优先级顺序确保了退款分配的合理性和可追溯性。
