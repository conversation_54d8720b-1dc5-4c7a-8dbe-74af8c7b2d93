package router

import (
	"voderpltvv/erp_client/api/controller/employee"

	"github.com/gin-gonic/gin"
)

// Router API路由
type Router struct {
	engine             *gin.Engine
	employeeController *employee.EmployeeController
}

// NewRouter 创建路由
func NewRouter(engine *gin.Engine, employeeController *employee.EmployeeController) *Router {
	return &Router{
		engine:             engine,
		employeeController: employeeController,
	}
}

// Setup 设置路由
func (r *Router) Setup() {
	// API版本组
	v1 := r.engine.Group("/api/v1")

	// 员工相关路由
	employees := v1.Group("/employees")
	{
		employees.GET("/:id", r.employeeController.GetByID) // 获取单个员工
		employees.GET("", r.employeeController.List)        // 获取员工列表
		employees.POST("/batch", r.employeeController.List) // 批量获取员工
	}
}

// RegisterEmployeeRoutes 注册员工相关路由
func RegisterEmployeeRoutes(r *gin.Engine) {
	controller := employee.NewEmployeeController()

	// 员工管理路由组
	employeeGroup := r.Group("/api/v1/employees")
	{
		// 基本CRUD操作
		employeeGroup.GET("/:id", controller.GetByID)
		employeeGroup.PUT("/:id", controller.Update)
		employeeGroup.DELETE("/:id", controller.Delete)
		employeeGroup.GET("", controller.List)

		// 特定业务查询
		employeeGroup.GET("/venue/:venue_id", controller.FindByVenueID)
		employeeGroup.GET("/role/:role", controller.FindByRole)
		employeeGroup.GET("/enabled", controller.FindEnabled)
	}
}
