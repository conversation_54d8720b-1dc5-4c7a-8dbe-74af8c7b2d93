package venue

import (
	"voderpltvv/erp_client/api/controller/v2/venue"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册场馆相关路由
func RegisterRoutes(engine *gin.Engine) {
	controller := venue.NewController()

	v3VenueGroup := engine.Group("/api/v3/venue")
	{
		v3VenueGroup.POST("/query-business-report", controller.V3QueryBusinessReport) // 查询业务报表
		v3VenueGroup.POST("/query-business-report-v2", controller.V3QueryBusinessReportV2) // 查询业务报表V2
	}

}
