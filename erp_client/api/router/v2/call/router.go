package call

import (
	"voderpltvv/erp_client/api/controller/v2/call"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册呼叫相关路由
func RegisterRoutes(engine *gin.Engine) {
	controller := call.NewController()

	v3CallGroup := engine.Group("/api/v3/call")
	{
		v3CallGroup.POST("/add", controller.V3AddCall)                          // 新增呼叫消息
		v3CallGroup.POST("/deal", controller.V3DealCall)                        // 处理呼叫消息
		v3CallGroup.POST("/cancel", controller.V3CancelCall)                    // 取消呼叫消息
		v3CallGroup.POST("/list", controller.V3ListCall)                        // 查询呼叫消息列表
		v3CallGroup.POST("/unprocessed/list", controller.V3ListCallUnprocessed) // 查询未处理的呼叫消息列表
		v3CallGroup.POST("/types", controller.V3ListCallTypes)                  // 查询呼叫类型列表
	}
}
