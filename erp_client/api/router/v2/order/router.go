package order

import (
	"voderpltvv/erp_client/api/controller/v2/order"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册订单相关路由
func RegisterRoutes(engine *gin.Engine) {
	controller := order.NewController()

	// orderGroup := engine.Group("/api/v2/order")
	// {
	// 	orderGroup.POST("/open", controller.Open) // 开台API
	// 	orderGroup.POST("/pay", controller.Pay)   // 支付API
	// }
	v3orderGroup := engine.Group("/api/v3/order")
	{
		v3orderGroup.POST("/open", controller.V3Open)                        // 开台-后付
		v3orderGroup.POST("/additional-order", controller.V3AdditionalOrder) // 点单-后付
		v3orderGroup.POST("/open-continue", controller.V3OpenContinue)       // 续房-后付

		v3orderGroup.POST("/pay", controller.V3Pay) // 支付-后付

		v3orderGroup.POST("/pay/callback", controller.V3PayCallback)       // 乐刷支付回调
		v3orderGroup.POST("/refund/callback", controller.V3RefundCallback) // 乐刷退款回调
		v3orderGroup.POST("/pay/query", controller.V3PayQuery)             // 乐刷支付查询

		v3orderGroup.POST("/open-pay", controller.V3OpenPay)                        // 开台-立结
		v3orderGroup.POST("/additional-order-pay", controller.V3AdditionalOrderPay) // 点单-立结
		v3orderGroup.POST("/open-continue-pay", controller.V3OpenContinuePay)       // 续房-立结

		v3orderGroup.POST("/refund-view", controller.V3RefundView)             // 退款视图
		v3orderGroup.POST("/refund-do", controller.V3RefundDo)                 // 退款
		v3orderGroup.POST("/room-fee-refund-do", controller.V3RoomFeeRefundDo) // 退房费

		v3orderGroup.POST("/transfer-room", controller.V3TransferRoom)      // 转台-后付
		v3orderGroup.POST("/end-time-consume", controller.V3EndTimeConsume) // 结束时长消费
		v3orderGroup.POST("/gift-time", controller.V3GiftTime)              // 赠送时长
		v3orderGroup.POST("/gift-product", controller.V3GiftProduct)        // 赠送商品

		v3orderGroup.POST("/close-room", controller.V3CloseRoom)        // 关房
		v3orderGroup.POST("/clean-room-finish", controller.V3CloseRoom) // 清扫完成

		v3orderGroup.POST("/swap-room", controller.V3SwapRoom)   // 互换包房
		v3orderGroup.POST("/merge-room", controller.V3MergeRoom) // 并房

		v3orderGroup.POST("/lock-room", controller.V3LockRoom)     // 锁房
		v3orderGroup.POST("/unlock-room", controller.V3UnlockRoom) // 解锁房

		v3orderGroup.POST("/cancel-order-open", controller.V3CancelOrderOpen) // 取消开台

		v3orderGroup.POST("/order-reopen", controller.V3OrderReopen) // 重开

		v3orderGroup.POST("/query/product/sales", controller.V3OrderQueryProductSales) // 商品销售统计

		v3orderGroup.POST("/fee-calculate", controller.V3FeeCalculate) // 商品金额计算
	}

}
