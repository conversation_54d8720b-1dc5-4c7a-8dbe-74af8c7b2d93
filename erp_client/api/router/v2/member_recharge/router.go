package member_recharge

import (
	"voderpltvv/erp_client/api/controller/v2/member_recharge"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册订单相关路由
func RegisterRoutes(engine *gin.Engine) {
	controller := member_recharge.NewController()

	v3MemberGroup := engine.Group("/api/v3/member")
	{
		v3MemberGroup.POST("/open-card", controller.V3OpenCard)      // 会员开卡
		v3MemberGroup.POST("/recharge", controller.V3MemberRecharge) // 账单查询

		v3MemberGroup.POST("/pay/callback", controller.V3MemberPayCallback)       // 会员支付回调
		v3MemberGroup.POST("/refund/callback", controller.V3MemberRefundCallback) // 会员退款回调
		v3MemberGroup.POST("/pay/query", controller.V3PayQuery)                   // 会员支付查询
	}

}
