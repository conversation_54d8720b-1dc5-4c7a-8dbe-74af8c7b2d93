package room

import (
	"voderpltvv/erp_client/api/controller/v2/room"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册路由
func RegisterRoutes(engine *gin.Engine) {
	controller := room.NewController()

	v2 := engine.Group("/api/v2/room")
	{
		v2.POST("/open-view", controller.OpenView)
	}

	v3 := engine.Group("/api/v3/room")
	{
		v3.POST("/stage", controller.V3Stage)
		v3.POST("/info/qr-code", controller.V3GetRoomInfoByQRCode)
		v3.POST("/open-view", controller.V3OpenView)
		v3.POST("/fault", controller.V3QueryRoomFault)
	}
}
