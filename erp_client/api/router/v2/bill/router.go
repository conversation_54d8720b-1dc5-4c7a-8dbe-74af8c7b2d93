package bill

import (
	"voderpltvv/erp_client/api/controller/v2/bill"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册订单相关路由
func RegisterRoutes(engine *gin.Engine) {
	controller := bill.NewController()

	v3BillGroup := engine.Group("/api/v3/bill")
	{
		v3BillGroup.POST("/bill-back-view", controller.V3BillBackView)         // 账单还原视图
		v3BillGroup.POST("/bill-back", controller.V3BillBack)                  // 账单还原
		v3BillGroup.POST("/query", controller.V3BillQuery)                     // 账单查询
		v3BillGroup.POST("/query-by-session", controller.V3BillQueryBySession) // 根据sessionId查询账单
	}

}
