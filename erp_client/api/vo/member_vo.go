package vo

// MemberVO 会员视图对象
type MemberVO struct {
	MemberId     string  `json:"memberId"`     // 会员ID
	MemberNumber string  `json:"memberNumber"` // 会员卡号
	Name         string  `json:"name"`         // 姓名
	Phone        string  `json:"phone"`        // 手机号
	Status       string  `json:"status"`       // 状态
	Level        string  `json:"level"`        // 会员等级
	Balance      float64 `json:"balance"`      // 账户余额
	Points       int     `json:"points"`       // 当前积分
	RegisterTime int64   `json:"registerTime"` // 注册时间
	VenueId      string  `json:"venueId"`      // 所属场馆ID
}

// MemberDetailVO 会员详情视图对象
type MemberDetailVO struct {
	MemberVO
	Gender         string   `json:"gender"`         // 性别
	Birthday       int64    `json:"birthday"`       // 生日
	TotalRecharge  float64  `json:"totalRecharge"`  // 总充值金额
	TotalConsume   float64  `json:"totalConsume"`   // 总消费金额
	Source         string   `json:"source"`         // 会员来源
	LastVisitTime  int64    `json:"lastVisitTime"`  // 最后到访时间
	RegisterSource string   `json:"registerSource"` // 注册来源
	TotalPoints    int      `json:"totalPoints"`    // 历史总积分
	ConsumeCount   int      `json:"consumeCount"`   // 消费次数
	Tags           []string `json:"tags"`           // 标签
	Remark         string   `json:"remark"`         // 备注
}

// RechargeResultVO 充值结果视图对象
type RechargeResultVO struct {
	MemberId      string  `json:"memberId"`      // 会员ID
	RechargeTime  int64   `json:"rechargeTime"`  // 充值时间
	Amount        float64 `json:"amount"`        // 充值金额
	Balance       float64 `json:"balance"`       // 充值后余额
	BonusPoints   int     `json:"bonusPoints"`   // 赠送积分
	BonusAmount   float64 `json:"bonusAmount"`   // 赠送金额
	TransactionId string  `json:"transactionId"` // 交易ID
}

// PointsAccountVO 积分账户视图对象
type PointsAccountVO struct {
	MemberId    string `json:"memberId"`    // 会员ID
	Points      int    `json:"points"`      // 当前积分
	TotalPoints int    `json:"totalPoints"` // 历史总积分
	UpdateTime  int64  `json:"updateTime"`  // 更新时间
}

// PointsHistoryItemVO 积分历史记录视图对象
type PointsHistoryItemVO struct {
	RecordId   string `json:"recordId"`   // 记录ID
	MemberId   string `json:"memberId"`   // 会员ID
	Points     int    `json:"points"`     // 变动积分
	Type       string `json:"type"`       // 类型(增加/减少)
	Reason     string `json:"reason"`     // 原因
	Balance    int    `json:"balance"`    // 变动后余额
	CreateTime int64  `json:"createTime"` // 创建时间
	OperatorId string `json:"operatorId"` // 操作员ID
}

// PointsHistoryVO 积分历史记录视图对象
type PointsHistoryVO struct {
	Total int                   `json:"total"` // 总记录数
	Items []PointsHistoryItemVO `json:"items"` // 记录项
}
