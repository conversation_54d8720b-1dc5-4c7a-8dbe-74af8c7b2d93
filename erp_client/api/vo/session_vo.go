package vo

// SessionVO 场次信息
type SessionVO struct {
	Id         string `json:"id"`         // 场次ID
	VenueId    string `json:"venueId"`    // 场馆ID
	RoomId     string `json:"roomId"`     // 房间ID
	StartTime  int64  `json:"startTime"`  // 开始时间
	EndTime    int64  `json:"endTime"`    // 结束时间
	Status     string `json:"status"`     // 状态
	CreateTime int64  `json:"createTime"` // 创建时间
	UpdateTime int64  `json:"updateTime"` // 更新时间
}
