package vo

import "voderpltvv/erp_managent/service/po"

// OrderInfoVO 订单信息VO
type OrderInfoVO struct {
	OrderNo   string `json:"orderNo"`   // 订单号
	SessionId string `json:"sessionId"` // 场次ID
}

// OrderPrepareResultVO 订单准备结果VO
type OrderPrepareResultVO struct {
	Session        *po.Session         `json:"session"`
	Orders         *[]po.Order         `json:"orders"`
	OrderProducts  *[]po.OrderProduct  `json:"orderProducts"`
	OrderPricePlan *po.OrderPricePlan  `json:"orderPricePlan"`
	OrderRoomPlans *[]po.OrderRoomPlan `json:"orderRoomPlans"`
	Room           *RoomVO             `json:"room"`
}
