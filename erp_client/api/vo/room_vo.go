package vo

// RoomVO 房间VO
type RoomVO struct {
	Id                   string                 `json:"id"`                   // ID
	VenueId              string                 `json:"venueId"`              // 所属门店ID
	SessionId            string                 `json:"sessionId"`            // 场次ID
	Name                 string                 `json:"name"`                 // 房间名称
	TypeId               string                 `json:"typeId"`               // 房间类型ID
	AreaId               string                 `json:"areaId"`               // 区域ID
	ThemeId              string                 `json:"themeId"`              // 主题ID
	PricePlanId          string                 `json:"pricePlanId"`          // 价格方案ID
	HighConsumptionAlert int64                  `json:"highConsumptionAlert"` // 高消费警报阈值
	Status               string                 `json:"status"`               // 房间状态
	OpenTime             int64                  `json:"openTime"`             // 开放时间
	CloseTime            int64                  `json:"closeTime"`            // 关闭时间
	ConsumptionMode      string                 `json:"consumptionMode"`      // 消费模式
	InteriorPhoto        string                 `json:"interiorPhoto"`        // 室内照片
	IsDisplayed          bool                   `json:"isDisplayed"`          // 是否显示
	QrCode               string                 `json:"qrCode"`               // 二维码
	Color                string                 `json:"color"`                // 颜色
	DisplayItems         string                 `json:"displayItems"`         // 显示项目
	Tag                  string                 `json:"tag"`                  // 标签
	IsDeleted            bool                   `json:"isDeleted"`            // 是否删除
	Ctime                int64                  `json:"ctime"`                // 创建时间
	Utime                int64                  `json:"utime"`                // 更新时间
	State                int                    `json:"state"`                // 状态
	Version              int                    `json:"version"`              // 版本
	AreaVO               *AreaVO                `json:"areaVO"`               // 区域信息
	RoomTypeVO           *RoomTypeVO            `json:"roomTypeVO"`           // 房间类型信息
	PricePlanVOs         *[]PricePlanVO         `json:"pricePlanVOs"`         // 价格计划信息
	HolidayVOs           *[]HolidayVO           `json:"holidayVOs"`           // 节假日信息
	CurrentTime          int64                  `json:"currentTime"`          // 当前时间
	RuleResults          map[string]interface{} `json:"ruleResults"`          // 规则结果
}

// AreaVO 区域视图对象
type AreaVO struct {
	Id          string `json:"id"`          // 区域ID
	VenueId     string `json:"venueId"`     // 门店ID
	Name        string `json:"name"`        // 区域名称
	Capacity    int    `json:"capacity"`    // 容量
	Description string `json:"description"` // 描述
	IsDisplayed bool   `json:"isDisplayed"` // 是否显示
}

// RoomTypeVO 房间类型视图对象
type RoomTypeVO struct {
	Id                   string `json:"id"`                   // ID
	VenueId              string `json:"venueId"`              // 所属门店ID
	Name                 string `json:"name"`                 // 房间类型名称
	ConsumptionMode      string `json:"consumptionMode"`      // 消费模式
	HighConsumptionAlert int64  `json:"highConsumptionAlert"` // 高消费警报金额
	Photo                string `json:"photo"`                // 房间类型照片
	Remark               string `json:"remark"`               // 备注
	DistributionChannel  string `json:"distributionChannel"`  // 分销渠道
	IsDisplayed          bool   `json:"isDisplayed"`          // 是否显示
}

// PricePlanVO 价格方案视图对象
type PricePlanVO struct {
	Id                  string `json:"id"`                  // ID
	VenueId             string `json:"venueId"`             // 门店id
	Name                string `json:"name"`                // 价格方案名称
	RoomType            string `json:"roomType"`            // 房间类型
	DistributionChannel string `json:"distributionChannel"` // 分销渠道
	ConsumptionMode     string `json:"consumptionMode"`     // 消费模式
	HasMinimumCharge    bool   `json:"hasMinimumCharge"`    // 是否有最低消费
	MinimumCharge       int64  `json:"minimumCharge"`       // 最低消费金额
	IsEnabled           bool   `json:"isEnabled"`           // 是否启用
	SupportsPoints      bool   `json:"supportsPoints"`      // 是否支持积分
	TimeType            string `json:"timeType"`            // 时间类型
	Duration            int    `json:"duration"`            // 买断持续时长
	IsAreaSpecified     bool   `json:"isAreaSpecified"`     // 是否指定投放区域
	BaseRoomFee         int64  `json:"baseRoomFee"`         // 基础房费
	IsExcessIncluded    bool   `json:"isExcessIncluded"`    // 多余部分是否计入房费
	BirthdayFee         int64  `json:"birthdayFee"`         // 生日价格
	GroupBuyFee         int64  `json:"groupBuyFee"`         // 团购价格
	ActivityFee         int64  `json:"activityFee"`         // 活动价格
	DiscountMode        string `json:"discountMode"`        // 优惠模式
	DiscountDuration    int    `json:"discountDuration"`    // 买钟优惠时长
	GiftDuration        int    `json:"giftDuration"`        // 赠送时长
	MaxDeductibleAmount int64  `json:"maxDeductibleAmount"` // 累计最高可抵扣金额
	MinimumConsumption  int64  `json:"minimumConsumption"`  // 超市消费最低金额
	StatisticsCategory  string `json:"statisticsCategory"`  // 统计分类
}

// HolidayVO 节假日视图对象
type HolidayVO struct {
	Id      string `json:"id"`      // ID
	Name    string `json:"name"`    // 节假日名称
	Date    string `json:"date"`    // 日期
	Type    string `json:"type"`    // 节假日类型 1:节假日 2:工作日
	VenueId string `json:"venueId"` // 所属店铺ID
}
