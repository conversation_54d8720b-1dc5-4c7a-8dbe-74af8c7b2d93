package vo

// OrderOpenReqVO 开台请求VO
type OrderOpenReqVO struct {
	// 场次ID
	SessionId string `json:"sessionId"`

	// 所属门店ID
	VenueId string `json:"venueId"`

	// 关联的房间ID
	RoomId string `json:"roomId"`

	// 订单开始时间
	StartTime int64 `json:"startTime"`

	// 订单结束时间
	EndTime int64 `json:"endTime"`

	// 方案id
	PricePlanId string `json:"pricePlanId"`

	// 价格方案名称
	PricePlanName string `json:"pricePlanName"`

	// 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
	ConsumptionMode string `json:"consumptionMode"`

	// 选择的计费方式-套餐区域id
	SelectedAreaId string `json:"selectedAreaId"`

	// 选择的计费方式-房间类型id
	SelectedRoomTypeId string `json:"selectedRoomTypeId"`

	// 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时
	TimeChargeMode string `json:"timeChargeMode"`

	// 买钟时长 单位：分钟
	BuyMinute int `json:"buyMinute"`

	// 买钟金额 单位：分
	TimeChargeAmount int `json:"timeChargeAmount"`

	// 买钟结束时间 格式：HH:mm
	TimeChargeEndTime string `json:"timeChargeEndTime"`

	// 买钟价格类型 基础价格、区域价格、节假日价格
	TimeChargeType string `json:"timeChargeType"`

	// 支付总金额
	PayAmount int64 `json:"payAmount"`

	// 原始金额
	OriginalAmount int64 `json:"originalAmount"`

	// 是否开台立结
	IsOpenTableSettled bool `json:"isOpenTableSettled"`

	// 【买断】最低消费金额
	MinimumCharge int64 `json:"minimumCharge"`

	// 当前时间
	CurrentTime int64 `json:"currentTime"`

	// 房费信息
	OrderRoomPlanVOS []*OrderRoomPlanVO `json:"orderRoomPlanVOS"`

	// 商品信息-套餐内
	InOrderProductInfos []*OrderProductVO `json:"inOrderProductInfos"`

	// 商品信息-套餐外
	OutOrderProductInfos []*OrderProductVO `json:"outOrderProductInfos"`

	// 房间信息
	RoomVO *RoomVO `json:"roomVO"`

	// 预订ID
	BookingId string `json:"bookingId"`

	// 员工ID
	EmployeeId string `json:"employeeId"`

	// 支付员工ID
	EmployeeIdPay string `json:"employeeIdPay"`
}

// OrderRoomPlanVO 订单房间计划VO
type OrderRoomPlanVO struct {
	RoomName       string `json:"roomName"`
	PricePlanId    string `json:"pricePlanId"`
	PricePlanName  string `json:"pricePlanName"`
	StartTime      int64  `json:"startTime"`
	EndTime        int64  `json:"endTime"`
	Duration       int    `json:"duration"`
	PayAmount      int64  `json:"payAmount"`
	OriginalAmount int64  `json:"originalAmount"`
	DiscountRate   int64  `json:"discountRate"`
	ReduceAmount   int64  `json:"reduceAmount"`
	FreeAmount     int64  `json:"freeAmount"`
	PricePlanType  string `json:"pricePlanType"`
}

// OrderProductVO 订单商品VO
type OrderProductVO struct {
	ProductId      string `json:"productId"`
	ProductName    string `json:"productName"`
	Flavors        string `json:"flavors"`
	Unit           string `json:"unit"`
	Quantity       int64  `json:"quantity"`
	PayPrice       int64  `json:"payPrice"`
	OriginalPrice  int64  `json:"originalPrice"`
	PayAmount      int64  `json:"payAmount"`
	OriginalAmount int64  `json:"originalAmount"`
	Mark           string `json:"mark"`
}
