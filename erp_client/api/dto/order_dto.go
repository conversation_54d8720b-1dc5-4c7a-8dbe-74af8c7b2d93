package dto

// AddOrderOpenReqDto 开台请求DTO
type AddOrderOpenReqDto struct {
	VenueId       string  `json:"venueId" binding:"required"`   // 场馆ID
	RoomId        string  `json:"roomId" binding:"required"`    // 房间ID
	BookingId     string  `json:"bookingId"`                    // 预订ID
	StartTime     int64   `json:"startTime" binding:"required"` // 开始时间
	EndTime       int64   `json:"endTime"`                      // 结束时间
	MinimumCharge int64   `json:"minimumCharge"`                // 最低消费
	PayAmount     float64 `json:"payAmount"`                    // 支付金额
	EmployeeId    string  `json:"employeeId"`                   // 员工ID
	EmployeeIdPay string  `json:"employeeIdPay"`                // 收银员工ID
}
