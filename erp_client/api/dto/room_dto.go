package dto

// QueryRoomStageReqDto 查询房间阶段请求DTO
type QueryRoomStageReqDto struct {
	RoomId    *string `json:"roomId,omitempty"`    // 房间ID
	VenueId   *string `json:"venueId,omitempty"`   // 门店ID
	TypeId    *string `json:"typeId,omitempty"`    // 房间类型ID
	AreaId    *string `json:"areaId,omitempty"`    // 区域ID
	SessionId *string `json:"sessionId,omitempty"` // 场次ID
	BookingId *string `json:"bookingId,omitempty"` // 预约ID
}

// V3QueryRoomStageReqDto 查询房间阶段请求DTO
type V3QueryRoomStageReqDto struct {
	RoomId    *string `json:"roomId,omitempty"`    // 房间ID
	VenueId   *string `json:"venueId,omitempty"`   // 门店ID
	TypeId    *string `json:"typeId,omitempty"`    // 房间类型ID
	AreaId    *string `json:"areaId,omitempty"`    // 区域ID
	SessionId *string `json:"sessionId,omitempty"` // 场次ID
}

type V3QueryRoomInfoByQRCodeReqDto struct {
	RoomId    *string `json:"roomId,omitempty"`    // 房间ID
	VenueId   *string `json:"venueId,omitempty"`   // 门店ID
}
