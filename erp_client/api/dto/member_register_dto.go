package dto

// MemberRegisterDTO 会员注册请求DTO
type MemberRegisterDTO struct {
	VenueId        string `json:"venueId"`        // 门店ID
	Name           string `json:"name"`           // 会员姓名
	Phone          string `json:"phone"`          // 手机号
	CardLevelId    string `json:"cardLevelId"`    // 卡等级ID
	CardType       string `json:"cardType"`       // 卡类型
	Gender         string `json:"gender"`         // 性别
	Birthday       int64  `json:"birthday"`       // 生日时间戳
	OperatorId     string `json:"operatorId"`     // 操作人ID
	OperatorName   string `json:"operatorName"`   // 操作人姓名
	InitialBalance int64  `json:"initialBalance"` // 初始余额
	InitialPoints  int    `json:"initialPoints"`  // 初始积分
	Remark         string `json:"remark"`         // 备注
}

// PhysicalCardRegisterDTO 实体卡注册请求DTO
type PhysicalCardRegisterDTO struct {
	VenueId        string `json:"venueId"`        // 门店ID
	Name           string `json:"name"`           // 会员姓名
	Phone          string `json:"phone"`          // 手机号
	CardNumber     string `json:"cardNumber"`     // 实体卡号
	CardLevelId    string `json:"cardLevelId"`    // 卡等级ID
	Gender         string `json:"gender"`         // 性别
	Birthday       int64  `json:"birthday"`       // 生日时间戳
	OperatorId     string `json:"operatorId"`     // 操作人ID
	OperatorName   string `json:"operatorName"`   // 操作人姓名
	InitialBalance int64  `json:"initialBalance"` // 初始余额
	InitialPoints  int    `json:"initialPoints"`  // 初始积分
	Remark         string `json:"remark"`         // 备注
}

// MemberVerifyDTO 会员信息验证请求DTO
type MemberVerifyDTO struct {
	VenueId    string `json:"venueId"`    // 门店ID
	Phone      string `json:"phone"`      // 手机号
	CardNumber string `json:"cardNumber"` // 卡号
}
