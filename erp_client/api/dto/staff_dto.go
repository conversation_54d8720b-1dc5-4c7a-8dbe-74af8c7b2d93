package dto

// ListStaffRequest 获取员工列表请求
type ListStaffRequest struct {
	Name    string `form:"name"`    // 员工姓名
	VenueID string `form:"venueId"` // 场馆ID
}

// ListStaffResponse 获取员工列表响应
type ListStaffResponse struct {
	Total int64           `json:"total"` // 总数
	List  []StaffResponse `json:"list"`  // 列表
}

// StaffResponse 员工信息响应
type StaffResponse struct {
	ID             string `json:"id"`             // 员工ID
	Name           string `json:"name"`           // 员工姓名
	EmployeeNumber string `json:"employeeNumber"` // 员工工号
	Phone          string `json:"phone"`          // 联系电话
	VenueID        string `json:"venueId"`        // 场馆ID
	VenueName      string `json:"venueName"`      // 场馆名称
}
