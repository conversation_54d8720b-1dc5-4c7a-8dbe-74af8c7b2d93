package dto

import "time"

// MemberRegisterDto 会员注册请求
type MemberRegisterDto struct {
	Name          string  `json:"name"`          // 会员姓名
	Phone         string  `json:"phone"`         // 手机号
	VenueId       string  `json:"venueId"`       // 场馆ID
	Source        string  `json:"source"`        // 注册来源
	Gender        string  `json:"gender"`        // 性别(可选)
	Birthday      *string `json:"birthday"`      // 生日(可选)
	RegisterBonus int     `json:"registerBonus"` // 注册奖励积分
	Remark        string  `json:"remark"`        // 备注
}

// MemberUpdateDto 会员信息更新请求
type MemberUpdateDto struct {
	MemberId string     `json:"memberId"` // 会员ID
	Name     string     `json:"name"`     // 姓名
	Phone    string     `json:"phone"`    // 手机号
	Gender   string     `json:"gender"`   // 性别
	Birthday *time.Time `json:"birthday"` // 生日
	Tags     []string   `json:"tags"`     // 标签
	Remark   string     `json:"remark"`   // 备注
}

// MemberRechargeDto 会员充值请求
type MemberRechargeDto struct {
	MemberId      string  `json:"memberId"`      // 会员ID
	Amount        float64 `json:"amount"`        // 充值金额
	PackageId     string  `json:"packageId"`     // 充值套餐ID
	PaymentMethod string  `json:"paymentMethod"` // 支付方式
	OperatorId    string  `json:"operatorId"`    // 操作员ID
	Remark        string  `json:"remark"`        // 备注
}

// PointsConsumeDto 积分消费请求
type PointsConsumeDto struct {
	MemberId   string `json:"memberId"`   // 会员ID
	Points     int    `json:"points"`     // 消费积分数量
	Reason     string `json:"reason"`     // 消费原因
	OperatorId string `json:"operatorId"` // 操作员ID
}

// PointsAwardDto 积分奖励请求
type PointsAwardDto struct {
	MemberId string `json:"memberId"` // 会员ID
	Points   int    `json:"points"`   // 奖励积分数量
	Reason   string `json:"reason"`   // 奖励原因
	OrderId  string `json:"orderId"`  // 关联订单ID(可选)
}

// PageQueryDto 分页查询请求
type PageQueryDto struct {
	PageNum  int `json:"pageNum"`  // 页码
	PageSize int `json:"pageSize"` // 每页条数
}
