package dto

// ListEmployeeRequest 查询员工列表请求
type ListEmployeeRequest struct {
	Name    string `form:"name"`     // 员工姓名，支持模糊查询
	VenueID string `form:"venue_id"` // 场馆ID
}

// EmployeeResponse 员工信息响应
type EmployeeResponse struct {
	ID             string `json:"id"`              // 员工ID
	Name           string `json:"name"`            // 员工姓名
	EmployeeNumber string `json:"employee_number"` // 员工编号
	Phone          string `json:"phone"`           // 手机号
	VenueID        string `json:"venue_id"`        // 场馆ID
	VenueName      string `json:"venue_name"`      // 场馆名称
}

// ListEmployeeResponse 查询员工列表响应
type ListEmployeeResponse struct {
	Total int64              `json:"total"` // 总数
	List  []EmployeeResponse `json:"list"`  // 员工列表
}
