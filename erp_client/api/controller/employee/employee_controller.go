package employee

import (
	"voderpltvv/erp_client/domain/subject/business/employee/model"
	"voderpltvv/erp_client/domain/subject/business/employee/repository"
	"voderpltvv/erp_client/infrastructure/proxy"

	"github.com/gin-gonic/gin"
)

// EmployeeController 员工控制器
type EmployeeController struct {
	employeeRepo repository.IEmployeeRepository
}

// NewEmployeeController 创建员工控制器
func NewEmployeeController() *EmployeeController {
	return &EmployeeController{
		employeeRepo: proxy.NewEmployeeProxy(nil),
	}
}

// GetByID 根据ID获取员工
func (c *EmployeeController) GetByID(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(400, gin.H{"error": "员工ID不能为空"})
		return
	}

	employee, err := c.employeeRepo.FindByID(ctx, id)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	if employee == nil {
		ctx.JSON(404, gin.H{"error": "员工不存在"})
		return
	}

	ctx.JSON(200, employee)
}

// FindByVenueID 根据场所ID查询员工
func (c *EmployeeController) FindByVenueID(ctx *gin.Context) {
	venueID := ctx.Query("venue_id")
	if venueID == "" {
		ctx.JSON(400, gin.H{"error": "场所ID不能为空"})
		return
	}

	employees, err := c.employeeRepo.FindByVenueID(ctx, venueID)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, employees)
}

// FindByRole 根据角色查询员工
func (c *EmployeeController) FindByRole(ctx *gin.Context) {
	role := ctx.Query("role")
	if role == "" {
		ctx.JSON(400, gin.H{"error": "角色不能为空"})
		return
	}

	employees, err := c.employeeRepo.FindByRole(ctx, role)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, employees)
}

// FindEnabled 查询启用的员工
func (c *EmployeeController) FindEnabled(ctx *gin.Context) {
	employees, err := c.employeeRepo.FindByCondition(ctx, map[string]interface{}{"enabled": true})
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, employees)
}

// Update 更新员工信息
func (c *EmployeeController) Update(ctx *gin.Context) {
	var employee model.Employee
	if err := ctx.ShouldBindJSON(&employee); err != nil {
		ctx.JSON(400, gin.H{"error": err.Error()})
		return
	}

	if err := c.employeeRepo.Update(ctx, employee); err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, gin.H{"message": "更新成功"})
}

// Delete 删除员工
func (c *EmployeeController) Delete(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		ctx.JSON(400, gin.H{"error": "员工ID不能为空"})
		return
	}

	if err := c.employeeRepo.Delete(ctx, id); err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, gin.H{"message": "删除成功"})
}

// List 获取员工列表
func (c *EmployeeController) List(ctx *gin.Context) {
	conditions := make(map[string]interface{})

	// 从查询参数中获取条件
	if name := ctx.Query("name"); name != "" {
		conditions["name"] = name
	}
	if venueID := ctx.Query("venue_id"); venueID != "" {
		conditions["venue_id"] = venueID
	}

	employees, err := c.employeeRepo.FindByCondition(ctx, conditions)
	if err != nil {
		ctx.JSON(500, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(200, employees)
}
