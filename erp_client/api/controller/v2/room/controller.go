package room

import (
	"net/http"
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/application/business/room/service"
	"voderpltvv/erp_client/config"
	_ "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/api/req"

	"github.com/gin-gonic/gin"
)

// Controller 房间控制器
type Controller struct {
	roomApplicationService service.RoomApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		roomApplicationService: config.GetContainer().GetRoomApplicationService(),
	}
}

// OpenView 获取房间开放视图
func (c *Controller) OpenView(ctx *gin.Context) {
	var reqDto dto.QueryRoomStageReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 通过应用服务获取房间开放视图
	roomVO, err := c.roomApplicationService.GetRoomOpenView(ctx, reqDto)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": roomVO})
}

// OpenView 获取房间开放视图
// @Summary 获取房间开放视图
// @Description 获取房间开放视图
// @Accept json
// @Produce json
// @Param body body dto.QueryRoomStageReqDto true "房间开放视图请求参数"
// @Success 200 {object} vo.RoomVO "房间开放视图响应参数"
// @Router /api/v3/room/open-view [post]
func (c *Controller) V3OpenView(ctx *gin.Context) {
	var reqDto dto.QueryRoomStageReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		// ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务获取房间开放视图
	roomVO, err := c.roomApplicationService.V3GetRoomOpenView(ctx, reqDto)
	if err != nil {
		// ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	// ctx.JSON(http.StatusOK, gin.H{"data": roomVO})
	controller.HttpResult_success(ctx, roomVO)
}

// Stage 获取房间开放视图
// @Summary 获取房间开放视图
// @Description 获取房间开放视图
// @Accept json
// @Produce json
// @Param body body dto.V3QueryRoomStageReqDto true "房间开放视图请求参数"
// @Success 200 {object} vo.RoomVO "房间开放视图响应参数"
// @Router /api/v3/room/stage [post]
func (c *Controller) V3Stage(ctx *gin.Context) {
	var reqDto dto.V3QueryRoomStageReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		// ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务获取房间开放视图
	roomVO, err := c.roomApplicationService.V3GetRoomStage(ctx, reqDto)
	if err != nil {
		// ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	// ctx.JSON(http.StatusOK, gin.H{"data": roomVO})
	controller.HttpResult_success(ctx, roomVO)
}

// V3GetRoomInfoByQRCode 获取房间信息
// @Summary 获取房间信息
// @Description 获取房间信息
// @Accept json
// @Produce json
// @Param body body dto.V3QueryRoomInfoByQRCodeReqDto true "房间信息请求参数"
// @Success 200 {object} vo.SessionVO "房间信息响应参数"
// @Router /api/v3/room/info/qr-code [post]
func (c *Controller) V3GetRoomInfoByQRCode(ctx *gin.Context) {
	var reqDto dto.V3QueryRoomInfoByQRCodeReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务获取房间信息
	sessionVO, err := c.roomApplicationService.V3GetRoomInfoByQRCode(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3QueryRoomFault 获取房间故障
// @Summary 获取房间故障
// @Description 获取房间故障
// @Accept json
// @Produce json
// @Param body body req.V3QueryRoomFaultReqDto true "房间故障请求参数"
// @Success 200 {object} vo.RoomFaultVO "房间故障响应参数"
// @Router /api/v3/room/fault [post]
func (c *Controller) V3QueryRoomFault(ctx *gin.Context) {
	var reqDto req.V3QueryRoomFaultReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务获取房间信息
	roomFaultVO, err := c.roomApplicationService.V3QueryRoomFault(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, roomFaultVO)
}
