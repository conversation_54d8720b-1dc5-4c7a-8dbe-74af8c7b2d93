package product

import (
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/application/business/product/service"
	"voderpltvv/erp_client/config"
	"voderpltvv/erp_managent/api/req"
	_ "voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// Controller 呼叫控制器
type Controller struct {
	productApplicationService service.ProductApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		productApplicationService: config.GetContainer().GetProductApplicationService(),
	}
}

// V3QueryProducts 查询产品API
// @Summary 查询产品
// @Description 查询产品
// @Accept json
// @Produce json
// @Param body body req.V3QueryProductReqDto true "查询产品请求"
// @Success 200 {object} []vo.ProductVO "查询产品响应"
// @Router /api/v3/product/query [post]
func (c *Controller) V3QueryProducts(ctx *gin.Context) {
	var reqDto req.V3QueryProductReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询产品请求
	productVOs, err := c.productApplicationService.V3QueryProducts(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, productVOs)
}
