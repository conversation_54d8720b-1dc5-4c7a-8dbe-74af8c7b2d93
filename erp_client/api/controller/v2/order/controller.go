package order

import (
	"net/http"
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/api/dto"
	"voderpltvv/erp_client/application/business/order/service"
	"voderpltvv/erp_client/config"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	_ "voderpltvv/erp_managent/api/vo"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// Controller 订单控制器
type Controller struct {
	orderApplicationService service.OrderApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		orderApplicationService: config.GetContainer().GetOrderApplicationService(),
	}
}

// Open 开台API
func (c *Controller) Open(ctx *gin.Context) {
	var reqDto dto.AddOrderOpenReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.OrderOpen(ctx, reqDto)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": sessionVO})
}

// Pay 支付API
func (c *Controller) Pay(ctx *gin.Context) {
	var reqDto req.QueryOrderPayReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 通过应用服务处理支付请求
	payResult, err := c.orderApplicationService.OrderPay(ctx, reqDto)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": payResult})
}

// V3Open 开台-后付
// @Summary 开台-后付
// @Description 开台-后付
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderOpenReqDto true "开台-后付请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.SessionVO] "开台-后付响应参数"
// @Router /api/v3/order/open [post]
func (c *Controller) V3Open(ctx *gin.Context) {
	var reqDto req.V3AddOrderOpenReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3OrderOpen(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	rtVO := vo.SessionOperationVO[vo.SessionVO]{
		OrderNos: sessionVO.RtOrderNos,
		Data:     sessionVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3OpenPay 开台-立结
// @Summary 开台-立结
// @Description 开台-立结
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderOpenPayReqDto true "开台-立结请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.SessionVO] "开台-立结响应参数"
// @Router /api/v3/order/open-pay [post]
func (c *Controller) V3OpenPay(ctx *gin.Context) {
	var reqDto req.V3AddOrderOpenPayReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3OrderOpenPay(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	LshSimplePayInfo := vo.LshSimplePayInfo{}
	for _, payResultVO := range sessionVO.PayResultVOs {
		if payResultVO.LshSimplePayInfo.PrepayUrl != "" {
			LshSimplePayInfo = payResultVO.LshSimplePayInfo
			break
		}
	}
	rtVO := vo.SessionOperationVO[vo.SessionVO]{
		OrderNos:         sessionVO.RtOrderNos,
		PayBills:         sessionVO.PayBills,
		SessionId:        sessionVO.SessionId,
		LshSimplePayInfo: LshSimplePayInfo,
		Data:             sessionVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3AdditionalOrder 点单-后付
// @Summary 点单-后付
// @Description 点单-后付
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderAdditionalReqDto true "点单-后付请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.OrderVO] "点单-后付响应参数"
// @Router /api/v3/order/additional-order [post]
func (c *Controller) V3AdditionalOrder(ctx *gin.Context) {
	var reqDto req.V3AddOrderAdditionalReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderVO, err := c.orderApplicationService.V3AdditionalOrder(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	rtVO := vo.SessionOperationVO[vo.OrderVO]{
		OrderNos: []string{orderVO.OrderNo},
		Data:     orderVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3AdditionalOrderPay 点单-立结
// @Summary 点单-立结
// @Description 点单-立结
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderAdditionalPayReqDto true "点单-立结请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.OrderVO] "点单-立结响应参数"
// @Router /api/v3/order/additional-order-pay [post]
func (c *Controller) V3AdditionalOrderPay(ctx *gin.Context) {
	var reqDto req.V3AddOrderAdditionalPayReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderVO, err := c.orderApplicationService.V3AdditionalOrderPay(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	LshSimplePayInfo := vo.LshSimplePayInfo{}
	for _, payResultVO := range orderVO.PayResultVOs {
		if payResultVO.LshSimplePayInfo.PrepayUrl != "" {
			LshSimplePayInfo = payResultVO.LshSimplePayInfo
			break
		}
	}
	rtVO := vo.SessionOperationVO[vo.OrderVO]{
		OrderNos:         []string{orderVO.OrderNo},
		PayBills:         []vo.PayBillVO{*orderVO.PayBillVO},
		SessionId:        orderVO.SessionId,
		LshSimplePayInfo: LshSimplePayInfo,
		Data:             orderVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3OpenContinue 续房-后付
// @Summary 续房-后付
// @Description 续房-后付
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderOpenContinueReqDto true "续房-后付-请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.SessionVO] "续房-后付-响应参数"
// @Router /api/v3/order/open-continue [post]
func (c *Controller) V3OpenContinue(ctx *gin.Context) {
	var reqDto req.V3AddOrderOpenContinueReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3OpenContinue(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	rtVO := vo.SessionOperationVO[vo.SessionVO]{
		OrderNos: sessionVO.RtOrderNos,
		PayBills: sessionVO.PayBills,
		Data:     sessionVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3OpenContinuePay 续房-立结
// @Summary 续房-立结
// @Description 续房-立结
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderOpenContinuePayReqDto true "续房-立结数"
// @Success 200 {object} vo.SessionOperationVO[vo.SessionVO] "续房-立结数"
// @Router /api/v3/order/open-continue-pay [post]
func (c *Controller) V3OpenContinuePay(ctx *gin.Context) {
	var reqDto req.V3AddOrderOpenContinuePayReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3OpenContinuePay(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	LshSimplePayInfo := vo.LshSimplePayInfo{}
	for _, payResultVO := range sessionVO.PayResultVOs {
		if payResultVO.LshSimplePayInfo.PrepayUrl != "" {
			LshSimplePayInfo = payResultVO.LshSimplePayInfo
			break
		}
	}
	rtVO := vo.SessionOperationVO[vo.SessionVO]{
		OrderNos:         sessionVO.RtOrderNos,
		SessionId:        sessionVO.SessionId,
		PayBills:         sessionVO.PayBills,
		LshSimplePayInfo: LshSimplePayInfo,
		Data:             sessionVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3EndTimeConsume 结束时长消费
// @Summary 结束时长消费
// @Description 结束时长消费
// @Accept json
// @Produce json
// @Param body body req.V3EndTimeConsumeReqDto true "结束时长消费请求参数"
// @Success 200 {object} vo.SessionVO "结束时长消费响应参数"
// @Router /api/v3/order/end-time-consume [post]
func (c *Controller) V3EndTimeConsume(ctx *gin.Context) {
	var reqDto req.V3EndTimeConsumeReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3EndTimeConsume(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3Pay 支付-后付
// @Summary 支付-后付
// @Description 支付-后付
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderPayReqDto true "支付请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.PayResultVO] "支付响应参数"
// @Router /api/v3/order/pay [post]
func (c *Controller) V3Pay(ctx *gin.Context) {
	var reqDto req.V3QueryOrderPayReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderPayResultVO, err := c.orderApplicationService.V3OrderPay(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	LshSimplePayInfo := vo.LshSimplePayInfo{}
	for _, payResultVO := range orderPayResultVO.PayResultVOs {
		if payResultVO.LshSimplePayInfo.PrepayUrl != "" {
			LshSimplePayInfo = payResultVO.LshSimplePayInfo
			break
		}
	}
	rtVO := vo.SessionOperationVO[vo.OrderPayResultVO]{
		OrderNos:         orderPayResultVO.TmpOrderNos,
		PayBills:         orderPayResultVO.PayBills,
		SessionId:        orderPayResultVO.PayBills[0].SessionId,
		LshSimplePayInfo: LshSimplePayInfo,
		Data:             orderPayResultVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3PayCallback 乐刷支付回调
// @Summary 乐刷支付回调
// @Description 乐刷支付回调
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderPayReqDto true "乐刷支付回调请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.PayResultVO] "乐刷支付回调响应参数"
// @Router /api/v3/order/pay/callback [post]
func (c *Controller) V3PayCallback(ctx *gin.Context) {
	var reqDto model.LeshuaPayCallbackModel
	if err := ctx.ShouldBindXML(&reqDto); err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}

	// 通过应用服务处理开台请求
	err := c.orderApplicationService.V3PayCallback(ctx, reqDto)
	if err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}
	// 成功返回000000
	ctx.String(200, "000000")
}

// V3RefundCallback 乐刷退款回调
// @Summary 乐刷退款回调
// @Description 乐刷退款回调
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderPayReqDto true "乐刷退款回调请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.PayResultVO] "乐刷退款回调响应参数"
// @Router /api/v3/order/refund/callback [post]
func (c *Controller) V3RefundCallback(ctx *gin.Context) {
	var reqDto model.LeshuaRefundCallbackModel
	if err := ctx.ShouldBindXML(&reqDto); err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}

	// 通过应用服务处理开台请求
	err := c.orderApplicationService.V3RefundCallback(ctx, reqDto)
	if err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}
	// 成功返回000000
	ctx.String(200, "000000")
}

// V3PayQuery 乐刷支付查询
// @Summary 乐刷支付查询
// @Description 乐刷支付查询
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderPayQueryReqDto true "乐刷支付查询请求参数"
// @Success 200 {object} vo.OrderPayQueryResultVO "乐刷支付查询响应参数"
// @Router /api/v3/order/pay/query [post]
func (c *Controller) V3PayQuery(ctx *gin.Context) {
	var reqDto req.V3QueryOrderPayQueryReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderPayQueryResultVO, err := c.orderApplicationService.V3PayQuery(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	controller.HttpResult_success(ctx, orderPayQueryResultVO)
}

// V3RefundView 退款视图
// @Summary 退款视图
// @Description 退款视图
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderRefundViewReqDto true "退款视图请求参数"
// @Success 200 {object} vo.OrderRefundViewVO "退款视图响应参数"
// @Router /api/v3/order/refund-view [post]
func (c *Controller) V3RefundView(ctx *gin.Context) {
	var reqDto req.V3QueryOrderRefundViewReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	refundViewVO, err := c.orderApplicationService.V3OrderRefundView(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	// ctx.JSON(http.StatusOK, gin.H{"data": refundViewVO})
	controller.HttpResult_success(ctx, refundViewVO)
}

// V3RefundDo 退款
// @Summary 退款
// @Description 退款
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderRefundReqDto true "退款请求参数"
// @Success 200 {object} []vo.OrderRefundInfoVO "退款响应参数"
// @Router /api/v3/order/refund-do [post]
func (c *Controller) V3RefundDo(ctx *gin.Context) {
	var reqDto req.V3QueryOrderRefundReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	refundDoVO, err := c.orderApplicationService.V3OrderRefundDo(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	// ctx.JSON(http.StatusOK, gin.H{"data": refundDoVO})
	controller.HttpResult_success(ctx, refundDoVO)
}

// V3RoomFeeRefundDo 退房费
// @Summary 退房费
// @Description 退房费
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderRoomFeeRefundReqDto true "退房费请求参数"
// @Success 200 {object} []vo.OrderRefundInfoVO "退房费响应参数"
// @Router /api/v3/order/room-fee-refund-do [post]
func (c *Controller) V3RoomFeeRefundDo(ctx *gin.Context) {
	var reqDto req.V3QueryOrderRoomFeeRefundReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理退房费请求
	refundDoVO, err := c.orderApplicationService.V3OrderRoomFeeRefundDo(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, refundDoVO)
}

// V3TransferRoom 转台
// @Summary 转台
// @Description 转台
// @Accept json
// @Produce json
// @Param body body req.V3TransferRoomReqDto true "转台请求参数"
// @Success 200 {object} vo.SessionVO "转台响应参数"
// @Router /api/v3/order/transfer-room [post]
func (c *Controller) V3TransferRoom(ctx *gin.Context) {
	var reqDto req.V3TransferRoomReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3TransferRoom(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3GiftTime 赠送时长
// @Summary 赠送时长
// @Description 赠送时长
// @Accept json
// @Produce json
// @Param body body req.V3OrderGiftTimeReqDto true "赠送时长请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.OrderVO] "赠送时长响应参数"
// @Router /api/v3/order/gift-time [post]
func (c *Controller) V3GiftTime(ctx *gin.Context) {
	var reqDto req.V3OrderGiftTimeReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderVO, err := c.orderApplicationService.V3GiftTime(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	rtVO := vo.SessionOperationVO[vo.OrderVO]{
		OrderNos: []string{orderVO.OrderNo},
		PayBills: []vo.PayBillVO{*orderVO.PayBillVO},
		Data:     orderVO,
	}

	controller.HttpResult_success(ctx, rtVO)
}

// V3GiftProduct 赠送商品
// @Summary 赠送商品
// @Description 赠送商品
// @Accept json
// @Produce json
// @Param body body req.V3OrderGiftProductReqDto true "赠送商品请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.OrderVO] "赠送商品响应参数"
// @Router /api/v3/order/gift-product [post]
func (c *Controller) V3GiftProduct(ctx *gin.Context) {
	var reqDto req.V3OrderGiftProductReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderVO, err := c.orderApplicationService.V3GiftProduct(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	rtVO := vo.SessionOperationVO[vo.OrderVO]{
		OrderNos: []string{orderVO.OrderNo},
		PayBills: []vo.PayBillVO{*orderVO.PayBillVO},
		Data:     orderVO,
	}

	controller.HttpResult_success(ctx, rtVO)
}

// V3CloseRoom 关房
// @Summary 关房
// @Description 关房
// @Accept json
// @Produce json
// @Param body body req.V3AddOrderCloseRoomReqDto true "关房请求参数"
// @Success 200 {object} vo.SessionVO "关房响应参数"
// @Router /api/v3/order/close-room [post]
func (c *Controller) V3CloseRoom(ctx *gin.Context) {
	var reqDto req.V3AddOrderCloseRoomReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	err := c.orderApplicationService.V3CloseRoom(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success[any](ctx, nil)
}

// V3CleanRoomFinish 清扫完成
// @Summary 清扫完成
// @Description 清扫完成
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderCleanRoomFinishReqDto true "清扫完成请求参数"
// @Success 200 {object} vo.SessionVO "清扫完成响应参数"
// @Router /api/v3/order/clean-room-finish [post]
func (c *Controller) V3CleanRoomFinish(ctx *gin.Context) {
	var reqDto req.V3QueryOrderCleanRoomFinishReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	err := c.orderApplicationService.V3CleanRoomFinish(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success[any](ctx, nil)
}

// V3SwapRoom 互换包房
// @Summary 互换包房
// @Description 互换包房
// @Accept json
// @Produce json
// @Param body body req.V3SwapRoomReqDto true "互换包房请求参数"
// @Success 200 {object} vo.SessionVO "互换包房响应参数"
// @Router /api/v3/order/swap-room [post]
func (c *Controller) V3SwapRoom(ctx *gin.Context) {
	var reqDto req.V3SwapRoomReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3SwapRoom(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3MergeRoom 并房
// @Summary 并房
// @Description 并房
// @Accept json
// @Produce json
// @Param body body req.V3MergeRoomReqDto true "并房请求参数"
// @Success 200 {object} vo.SessionVO "并房响应参数"
// @Router /api/v3/order/merge-room [post]
func (c *Controller) V3MergeRoom(ctx *gin.Context) {
	var reqDto req.V3MergeRoomReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3MergeRoom(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3LockRoom 锁房
// @Summary 锁房
// @Description 锁房
// @Accept json
// @Produce json
// @Param body body req.V3LockRoomReqDto true "锁房请求参数"
// @Success 200 {object} vo.SessionVO "锁房响应参数"
// @Router /api/v3/order/lock-room [post]
func (c *Controller) V3LockRoom(ctx *gin.Context) {
	var reqDto req.V3LockRoomReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3LockRoom(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3UnlockRoom 解锁房
// @Summary 解锁房
// @Description 解锁房
// @Accept json
// @Produce json
// @Param body body req.V3UnlockRoomReqDto true "解锁房请求参数"
// @Success 200 {object} vo.SessionVO "解锁房响应参数"
// @Router /api/v3/order/lock-room [post]
func (c *Controller) V3UnlockRoom(ctx *gin.Context) {
	var reqDto req.V3UnlockRoomReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3UnlockRoom(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3CancelOrderOpen 取消开台
// @Summary 取消开台
// @Description 取消开台
// @Accept json
// @Produce json
// @Param body body req.V3CancelOrderOpenReqDto true "取消开台请求参数"
// @Success 200 {object} vo.SessionVO "取消开台响应参数"
// @Router /api/v3/order/cancel-order-open [post]
func (c *Controller) V3CancelOrderOpen(ctx *gin.Context) {
	var reqDto req.V3CancelOrderOpenReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3CancelOrderOpen(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success[any](ctx, sessionVO)
}

// V3OrderReopen 重开
// @Summary 重开
// @Description 重开
// @Accept json
// @Produce json
// @Param body body req.V3OrderReopenReqDto true "重开请求参数"
// @Success 200 {object} vo.SessionVO "重开响应参数"
// @Router /api/v3/order/order-reopen [post]
func (c *Controller) V3OrderReopen(ctx *gin.Context) {
	var reqDto req.V3OrderReopenReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	sessionVO, err := c.orderApplicationService.V3OrderReopen(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3MiniAppPay 小程序支付
// @Summary 小程序支付
// @Description 小程序支付
// @Accept json
// @Produce json
// @Param body body req.V3MiniAppPayReqDto true "小程序支付请求参数"
// @Success 200 {object} vo.SessionOperationVO[vo.OrderVO] "小程序支付响应参数"
// @Router /api/v3/order/mini-app-pay [post]
func (c *Controller) V3MiniAppPay(ctx *gin.Context) {
	var reqDto req.V3MiniAppPayReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderVO, err := c.orderApplicationService.V3MiniAppPay(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	if len(orderVO.PayResultVOs) <= 0 {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, "支付结果为空")
		return
	}
	payResultVO := orderVO.PayResultVOs[0]
	rtVO := vo.SessionOperationVO[vo.OrderVO]{
		OrderNos:  []string{orderVO.OrderNo},
		PayBills:  []vo.PayBillVO{*orderVO.PayBillVO},
		SessionId: orderVO.SessionId,
		JSPayInfo: payResultVO.JSPayInfo,
		Data:      orderVO,
	}
	controller.HttpResult_success(ctx, rtVO)
}

// V3OrderQueryProductSales 商品销售统计
// @Summary 商品销售统计
// @Description 商品销售统计
// @Accept json
// @Produce json
// @Param body body req.V3OrderQueryProductSalesReqDto true "商品销售统计请求参数"
// @Success 200 {object} []vo.OrderProductSalesVO "商品销售统计响应参数"
// @Router /api/v3/order/query/product/sales [post]
func (c *Controller) V3OrderQueryProductSales(ctx *gin.Context) {
	var reqDto req.V3OrderQueryProductSalesReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderProductSalesVOs, err := c.orderApplicationService.V3OrderQueryProductSales(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, orderProductSalesVOs)
}

// V3FeeCalculate 商品金额计算
// @Summary 商品金额计算
// @Description 商品金额计算
// @Accept json
// @Produce json
// @Param body body req.V3QueryOrderPayCalculateReqDto true "商品金额计算请求参数"
// @Success 200 {object} []vo.OrderPayCalculateResultVO "商品金额计算响应参数"
// @Router /api/v3/order/fee-calculate [post]
func (c *Controller) V3FeeCalculate(ctx *gin.Context) {
	var reqDto req.V3QueryOrderPayCalculateReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理开台请求
	orderPayCalculateResultVOs, err := c.orderApplicationService.V3FeeCalculate(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, orderPayCalculateResultVOs)
}
