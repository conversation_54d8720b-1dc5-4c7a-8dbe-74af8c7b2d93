package member_recharge

import (
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/application/business/member/service"
	"voderpltvv/erp_client/config"
	"voderpltvv/erp_managent/api/req"
	_ "voderpltvv/erp_managent/api/vo"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// Controller 订单控制器
type Controller struct {
	memberRechargeApplicationService service.MemberRechageApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		memberRechargeApplicationService: config.GetContainer().GetMemberRechageApplicationService(),
	}
}

// V3OpenCard 会员开卡API
// @Summary 会员开卡
// @Description 会员开卡
// @Accept json
// @Produce json
// @Param body body req.V3OpenCardReqDto true "会员开卡请求"
// @Success 200 {object} po.MemberCard "会员开卡响应"
// @Router /api/v3/member/open-card [post]
func (c *Controller) V3OpenCard(ctx *gin.Context) {
	var reqDto req.V3OpenCardReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	memberCard, err := c.memberRechargeApplicationService.V3OpenCard(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, memberCard)
}

// V3MemberRechargeBill 会员充值账单查询API
// @Summary 会员充值账单查询
// @Description 会员充值账单查询
// @Accept json
// @Produce json
// @Param body body req.V3QueryMemberRechargeReqDto true "会员充值账单查询请求"
// @Success 200 {object} []vo.MemberRechargeBillExVO "会员充值账单查询响应"
// @Router /api/v3/member/recharge [post]
func (c *Controller) V3MemberRecharge(ctx *gin.Context) {
	var reqDto req.V3QueryMemberRechargeReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	memberRechargeBillExVOs, err := c.memberRechargeApplicationService.V3MemberRechage(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, memberRechargeBillExVOs)
}

// V3MemberPayCallback 乐刷支付回调
// @Summary 乐刷支付回调
// @Description 乐刷支付回调
// @Accept json
// @Produce json
// @Param body body string true "乐刷支付回调请求参数"
// @Success 200 {object} nil "乐刷支付回调响应参数"
// @Router /api/v3/member/pay/callback [post]
func (c *Controller) V3MemberPayCallback(ctx *gin.Context) {
	var reqDto model.LeshuaPayCallbackModel
	if err := ctx.ShouldBindXML(&reqDto); err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}

	// 通过应用服务处理开台请求
	err := c.memberRechargeApplicationService.V3MemberPayCallback(ctx, reqDto)
	if err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}
	// 成功返回000000
	ctx.String(200, "000000")
}

// V3RefundCallback 乐刷退款回调
// @Summary 乐刷退款回调
// @Description 乐刷退款回调
// @Accept json
// @Produce json
// @Param body body string true "乐刷退款回调请求参数"
// @Success 200 {object} nil "乐刷退款回调响应参数"
// @Router /api/v3/member/refund/callback [post]
func (c *Controller) V3MemberRefundCallback(ctx *gin.Context) {
	var reqDto model.LeshuaRefundCallbackModel
	if err := ctx.ShouldBindXML(&reqDto); err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}

	// 通过应用服务处理开台请求
	err := c.memberRechargeApplicationService.V3MemberRefundCallback(ctx, reqDto)
	if err != nil {
		// 失败返回000001
		ctx.String(200, "000001")
		return
	}
	// 成功返回000000
	ctx.String(200, "000000")
}

// V3PayQuery 会员支付查询
// @Summary 会员支付查询
// @Description 会员支付查询
// @Accept json
// @Produce json
// @Param body body req.V3QueryMemberPayQueryReqDto true "会员支付查询请求参数"
// @Success 200 {object} vo.MemberRechargeBillVO "会员支付查询响应参数"
// @Router /api/v3/member/pay/query [post]
func (c *Controller) V3PayQuery(ctx *gin.Context) {
	var reqDto req.V3QueryMemberPayQueryReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理支付查询请求
	memberRechargeBillVO, err := c.memberRechargeApplicationService.V3MemberPayQuery(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}
	controller.HttpResult_success(ctx, memberRechargeBillVO)
}
