package bill

import (
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/application/business/bill/service"
	"voderpltvv/erp_client/config"
	"voderpltvv/erp_managent/api/req"
	_ "voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// Controller 订单控制器
type Controller struct {
	billApplicationService service.BillApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		billApplicationService: config.GetContainer().GetBillApplicationService(),
	}
}

// V3BillBackView 账单还原视图API
// @Summary 账单还原视图
// @Description 账单还原视图
// @Accept json
// @Produce json
// @Param body body req.V3QueryBillBackViewReqDto true "查询账单请求"
// @Success 200 {object} vo.PayBillUnionVO "查询账单响应"
// @Router /api/v3/bill/bill-back-view [post]
func (c *Controller) V3BillBackView(ctx *gin.Context) {
	var reqDto req.V3QueryBillBackViewReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	payBillUnionVO, err := c.billApplicationService.V3BillBackView(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, payBillUnionVO)
}

// V3BillBack 账单还原API
// @Summary 账单还原
// @Description 账单还原
// @Accept json
// @Produce json
// @Param body body req.V3BillBackReqDto true "账单还原请求"
// @Success 200 {object} nil "账单还原响应"
// @Router /api/v3/bill/bill-back [post]
func (c *Controller) V3BillBack(ctx *gin.Context) {
	var reqDto req.V3BillBackReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	sessionVO, err := c.billApplicationService.V3BillBack(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, sessionVO)
}

// V3BillQuery 账单查询API
// @Summary 账单查询
// @Description 账单查询
// @Accept json
// @Produce json
// @Param body body req.V3BillQueryReqDto true "账单查询请求"
// @Success 200 {object} []vo.PayBillExtVO "账单查询响应"
// @Router /api/v3/bill/query [post]
func (c *Controller) V3BillQuery(ctx *gin.Context) {
	var reqDto req.V3BillQueryReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	payBillExtVOs, err := c.billApplicationService.V3BillQuery(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, payBillExtVOs)
}

// V3BillQueryBySession 根据sessionId查询账单API
// @Summary 根据sessionId查询账单
// @Description 根据sessionId查询账单
// @Accept json
// @Produce json
// @Param body body req.V3BillQueryBySessionReqDto true "根据sessionId查询账单请求"
// @Success 200 {object} []vo.PayBillVO "根据sessionId查询账单响应"
// @Router /api/v3/bill/query-by-session [post]
func (c *Controller) V3BillQueryBySession(ctx *gin.Context) {
	var reqDto req.V3BillQueryBySessionReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	payBillVOs, err := c.billApplicationService.V3BillQueryBySession(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, payBillVOs)
}
