package call

import (
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/application/business/call/service"
	"voderpltvv/erp_client/config"
	"voderpltvv/erp_managent/api/req"
	_ "voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// Controller 呼叫控制器
type Controller struct {
	callApplicationService service.CallApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		callApplicationService: config.GetContainer().GetCallApplicationService(),
	}
}

// V3AddCall 新增呼叫消息API
// @Summary 新增呼叫消息
// @Description 新增呼叫消息
// @Accept json
// @Produce json
// @Param body body req.V3AddCallReqDto true "新增呼叫消息请求"
// @Success 200 {object} vo.CallMessageVO "新增呼叫消息响应"
// @Router /api/v3/call/add [post]
func (c *Controller) V3AddCall(ctx *gin.Context) {
	var reqDto req.V3AddCallReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理新增呼叫消息请求
	callMessageVO, err := c.callApplicationService.V3AddCall(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, callMessageVO)
}

// V3DealCall 处理呼叫消息API
// @Summary 处理呼叫消息
// @Description 处理呼叫消息
// @Accept json
// @Produce json
// @Param body body req.V3DealCallReqDto true "处理呼叫消息请求"
// @Success 200 {object} vo.CallMessageVO "处理呼叫消息响应"
// @Router /api/v3/call/deal [post]
func (c *Controller) V3DealCall(ctx *gin.Context) {
	var reqDto req.V3DealCallReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理呼叫消息请求
	callMessageVO, err := c.callApplicationService.V3DealCall(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, callMessageVO)
}

// V3CancelCall 取消呼叫消息API
// @Summary 取消呼叫消息
// @Description 取消呼叫消息
// @Accept json
// @Produce json
// @Param body body req.V3CancelCallReqDto true "取消呼叫消息请求"
// @Success 200 {object} vo.CallMessageVO "取消呼叫消息响应"
// @Router /api/v3/call/cancel [post]
func (c *Controller) V3CancelCall(ctx *gin.Context) {
	var reqDto req.V3CancelCallReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理呼叫消息请求
	callMessageVO, err := c.callApplicationService.V3CancelCall(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, callMessageVO)
}

// V3ListCall 查询呼叫消息列表API
// @Summary 查询呼叫消息列表
// @Description 查询呼叫消息列表
// @Accept json
// @Produce json
// @Param body body req.V3ListCallLastReqDto true "查询呼叫消息列表请求"
// @Success 200 {array} vo.CallMessageVO "查询呼叫消息列表响应"
// @Router /api/v3/call/list [post]
func (c *Controller) V3ListCall(ctx *gin.Context) {
	var reqDto req.V3ListCallLastReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询呼叫消息列表请求
	callMessageVOs, err := c.callApplicationService.V3ListCall(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, callMessageVOs)
}

// V3ListCallUnprocessed 查询未处理的呼叫消息列表API
// @Summary 查询未处理的呼叫消息列表
// @Description 查询未处理的呼叫消息列表
// @Accept json
// @Produce json
// @Param body body req.V3ListCallUnprocessedReqDto true "查询未处理的呼叫消息列表请求"
// @Success 200 {array} vo.CallMessageVO "查询未处理的呼叫消息列表响应"
// @Router /api/v3/call/unprocessed/list [post]
func (c *Controller) V3ListCallUnprocessed(ctx *gin.Context) {
	var reqDto req.V3ListCallUnprocessedReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询未处理的呼叫消息列表请求
	callMessageVOs, err := c.callApplicationService.V3ListCallUnprocessed(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, callMessageVOs)
}

// V3ListCallTypes 查询呼叫类型列表API
// @Summary 查询呼叫类型列表
// @Description 查询呼叫类型列表
// @Accept json
// @Produce json
// @Param body body req.V3ListCallTypesReqDto true "查询呼叫类型列表请求"
// @Success 200 {array} vo.CallTypesVO "查询呼叫类型列表响应"
// @Router /api/v3/call/types [post]
func (c *Controller) V3ListCallTypes(ctx *gin.Context) {
	var reqDto req.V3ListCallTypesReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询呼叫类型列表请求
	callTypesVOs, err := c.callApplicationService.V3ListCallTypes(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, callTypesVOs)
}
