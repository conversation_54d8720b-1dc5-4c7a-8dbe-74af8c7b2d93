package venue

import (
	"voderpltvv/erp_client/api/controller"
	"voderpltvv/erp_client/application/business/venue/service"
	"voderpltvv/erp_client/config"
	"voderpltvv/erp_managent/api/req"
	_ "voderpltvv/erp_managent/api/vo"

	"github.com/gin-gonic/gin"
)

// Controller 订单控制器
type Controller struct {
	venueApplicationService service.VenueApplicationService
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		venueApplicationService: config.GetContainer().GetVenueApplicationService(),
	}
}

// V3QueryBusinessReport 查询业务报表API
// @Summary 查询业务报表
// @Description 查询业务报表
// @Accept json
// @Produce json
// @Param body body req.V3QueryBuisinessReportReqDto true "查询业务报表请求"
// @Success 200 {object} vo.VenueBusinessReportVO "查询业务报表响应"
// @Router /api/v3/venue/query-business-report [post]
func (c *Controller) V3QueryBusinessReport(ctx *gin.Context) {
	var reqDto req.V3QueryBuisinessReportReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	venueBusinessReportVO, err := c.venueApplicationService.V3QueryBusinessReport(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, venueBusinessReportVO)
}

// V3QueryBusinessReport 查询业务报表API
// @Summary 查询业务报表
// @Description 查询业务报表
// @Accept json
// @Produce json
// @Param body body req.V3QueryBuisinessReportReqDto true "查询业务报表请求"
// @Success 200 {object} vo.VenueBusinessReportV2VO "查询业务报表响应"
// @Router /api/v3/venue/query-business-report-v2 [post]
func (c *Controller) V3QueryBusinessReportV2(ctx *gin.Context) {
	var reqDto req.V3QueryBuisinessReportReqDto
	if err := ctx.ShouldBindJSON(&reqDto); err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
		return
	}

	// 通过应用服务处理查询账单请求
	venueBusinessReportVO, err := c.venueApplicationService.V3QueryBusinessReportV2(ctx, reqDto)
	if err != nil {
		controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
		return
	}

	controller.HttpResult_success(ctx, venueBusinessReportVO)
}
