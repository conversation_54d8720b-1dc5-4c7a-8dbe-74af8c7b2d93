package controller

import (
	"net/http"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type ErrorCode struct {
	Code int
}

var GeneralCodes = struct {
	OK                  ErrorCode
	InternalError       ErrorCode
	ServerIsBuzy        ErrorCode
	ParamError          ErrorCode
	NotAuthorized       ErrorCode
	Forbidden           ErrorCode
	NotFound            ErrorCode
	TokenExpired        ErrorCode
	Throttled           ErrorCode
	OrderAmountNotMatch ErrorCode
	ShowDialogError     ErrorCode
	NotLogin            ErrorCode
	AccountLocked       ErrorCode
	AccountNotExist     ErrorCode
	AccountPending      ErrorCode
	InvalidCredentials  ErrorCode
	CashierNotBound     ErrorCode
}{
	OK:                  ErrorCode{Code: 0},
	InternalError:       ErrorCode{Code: 500},
	ServerIsBuzy:        ErrorCode{Code: 50013},
	ParamError:          ErrorCode{Code: 400},
	NotAuthorized:       ErrorCode{Code: 401},
	NotLogin:            ErrorCode{Code: 402},
	Forbidden:           ErrorCode{Code: 403},
	NotFound:            ErrorCode{Code: 404},
	TokenExpired:        ErrorCode{Code: 407},
	Throttled:           ErrorCode{Code: 418},
	OrderAmountNotMatch: ErrorCode{Code: 4001},
	ShowDialogError:     ErrorCode{Code: 6000},
	AccountLocked:       ErrorCode{Code: 4002},
	AccountNotExist:     ErrorCode{Code: 4003},
	AccountPending:      ErrorCode{Code: 4004},
	InvalidCredentials:  ErrorCode{Code: 4005},
	CashierNotBound:     ErrorCode{Code: 4100},
}

type HttpResult[T any] struct {
	Code        int               `json:"code"`
	Message     string            `json:"message"`
	Data        T                 `json:"data,omitempty"`
	TraceId     string            `json:"traceId,omitempty"`
	Attachments map[string]string `json:"attachments,omitempty"`
	RequestID   *string           `json:"requestID"`
	ServerTime  *int              `json:"serverTime"`
}

func HttpResult_success[T any](ctx *gin.Context, t T) {
	requestIDAny, exists := ctx.Get("X-Request-Id")
	if !exists {
		requestIDAny = ""
	}
	requestID := requestIDAny.(string)

	ctx.JSON(http.StatusOK, HttpResult[T]{
		Code:       GeneralCodes.OK.Code,
		Message:    "success",
		Data:       t,
		RequestID:  &requestID,
		ServerTime: util.GetItPtr(util.TimeNowUnix()),
	})
}

func HttpResult_success_attachments[T any](ctx *gin.Context, t T, attachments map[string]string) {
	requestIDAny, exists := ctx.Get("X-Request-Id")
	if !exists {
		requestIDAny = ""
	}
	requestID := requestIDAny.(string)

	ctx.JSON(http.StatusOK, HttpResult[T]{
		Code:        GeneralCodes.OK.Code,
		Message:     "success",
		Data:        t,
		RequestID:   &requestID,
		Attachments: attachments,
		ServerTime:  util.GetItPtr(util.TimeNowUnix()),
	})
}

func HttpResult_fail[T any](ctx *gin.Context, errcode int, errmsg string, attachments ...map[string]string) {
	requestIDAny, exists := ctx.Get("X-Request-Id")
	if !exists {
		requestIDAny = ""
	}
	requestID := requestIDAny.(string)

	res := HttpResult[T]{
		Code:       errcode,
		Message:    errmsg,
		RequestID:  &requestID,
		ServerTime: util.GetItPtr(util.TimeNowUnix()),
	}

	if len(attachments) > 0 {
		res.Attachments = attachments[0]
	}

	ctx.JSON(http.StatusOK, res)
}
