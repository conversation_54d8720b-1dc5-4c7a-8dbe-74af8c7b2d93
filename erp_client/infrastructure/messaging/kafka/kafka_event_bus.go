package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"voderpltvv/erp_client/infrastructure/messaging"

	"github.com/IBM/sarama"
)

// KafkaEventBus Kafka事件总线实现
type KafkaEventBus struct {
	producer sarama.SyncProducer
	consumer sarama.Consumer
	handlers map[string][]messaging.EventHandler
	mu       sync.RWMutex
}

// NewKafkaEventBus 创建Kafka事件总线实例
func NewKafkaEventBus(brokers []string) (*KafkaEventBus, error) {
	// 1. 配置生产者
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 5
	config.Producer.Return.Successes = true

	// 2. 创建生产者
	producer, err := sarama.NewSyncProducer(brokers, config)
	if err != nil {
		return nil, fmt.Errorf("创建生产者失败: %w", err)
	}

	// 3. 创建消费者
	consumer, err := sarama.NewConsumer(brokers, config)
	if err != nil {
		producer.Close()
		return nil, fmt.Errorf("创建消费者失败: %w", err)
	}

	return &KafkaEventBus{
		producer: producer,
		consumer: consumer,
		handlers: make(map[string][]messaging.EventHandler),
	}, nil
}

// Publish 发布事件
func (b *KafkaEventBus) Publish(ctx context.Context, event messaging.Event) error {
	// 1. 序列化事件
	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("序列化事件失败: %w", err)
	}

	// 2. 创建消息
	msg := &sarama.ProducerMessage{
		Topic: event.GetEventType(),
		Value: sarama.StringEncoder(data),
	}

	// 3. 发送消息
	_, _, err = b.producer.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	return nil
}

// Subscribe 订阅事件
func (b *KafkaEventBus) Subscribe(eventType string, handler messaging.EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	// 1. 注册处理器
	if b.handlers[eventType] == nil {
		b.handlers[eventType] = make([]messaging.EventHandler, 0)
	}
	b.handlers[eventType] = append(b.handlers[eventType], handler)

	// 2. 创建消费者
	partitionConsumer, err := b.consumer.ConsumePartition(eventType, 0, sarama.OffsetNewest)
	if err != nil {
		return fmt.Errorf("创建分区消费者失败: %w", err)
	}

	// 3. 启动消费协程
	go func() {
		for msg := range partitionConsumer.Messages() {
			// 3.1 反序列化消息
			var event messaging.Event
			if err := json.Unmarshal(msg.Value, &event); err != nil {
				fmt.Printf("反序列化消息失败: %v\n", err)
				continue
			}

			// 3.2 调用处理器
			b.mu.RLock()
			handlers := b.handlers[eventType]
			b.mu.RUnlock()

			for _, h := range handlers {
				if err := h.Handle(context.Background(), event); err != nil {
					fmt.Printf("处理事件失败: %v\n", err)
				}
			}
		}
	}()

	return nil
}

// Unsubscribe 取消订阅
func (b *KafkaEventBus) Unsubscribe(eventType string, handler messaging.EventHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	handlers := b.handlers[eventType]
	if handlers == nil {
		return nil
	}

	// 移除处理器
	for i, h := range handlers {
		if h == handler {
			b.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
			break
		}
	}

	return nil
}

// Close 关闭事件总线
func (b *KafkaEventBus) Close() error {
	if err := b.producer.Close(); err != nil {
		return fmt.Errorf("关闭生产者失败: %w", err)
	}
	if err := b.consumer.Close(); err != nil {
		return fmt.Errorf("关闭消费者失败: %w", err)
	}
	return nil
}
