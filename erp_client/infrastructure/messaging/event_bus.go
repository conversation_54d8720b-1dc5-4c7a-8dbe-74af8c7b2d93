package messaging

import "context"

// Event 事件接口
type Event interface {
	GetEventID() string
	GetEventType() string
}

// EventBus 事件总线接口
type EventBus interface {
	// Publish 发布事件
	Publish(ctx context.Context, event Event) error

	// Subscribe 订阅事件
	Subscribe(eventType string, handler EventHandler) error

	// Unsubscribe 取消订阅
	Unsubscribe(eventType string, handler EventHandler) error
}

// EventHandler 事件处理器
type EventHandler interface {
	Handle(ctx context.Context, event Event) error
}
