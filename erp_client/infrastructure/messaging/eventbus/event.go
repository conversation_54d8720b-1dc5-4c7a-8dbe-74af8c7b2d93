package eventbus

import "time"

// EventType 事件类型
type EventType string

// Event 事件结构
type Event struct {
	ID        string                 `json:"id"`
	Type      EventType              `json:"type"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// EventHandler 事件处理器
type EventHandler func(event Event) error

// EventBus 事件总线接口
type EventBus interface {
	// Publish 发布事件
	Publish(event Event) error

	// Subscribe 订阅事件
	Subscribe(eventType EventType, handler EventHandler) error

	// Unsubscribe 取消订阅
	Unsubscribe(eventType EventType, handler EventHandler) error
}
