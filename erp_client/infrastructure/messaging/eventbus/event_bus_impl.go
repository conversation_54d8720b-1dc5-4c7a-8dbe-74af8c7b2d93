package eventbus

import (
	"sync"
)

// EventBusImpl 事件总线实现
type EventBusImpl struct {
	handlers map[EventType][]EventHandler
	mu       sync.RWMutex
}

// NewEventBus 创建新的事件总线
func NewEventBus() EventBus {
	return &EventBusImpl{
		handlers: make(map[EventType][]EventHandler),
	}
}

// Publish 发布事件
func (eb *EventBusImpl) Publish(event Event) error {
	eb.mu.RLock()
	handlers := eb.handlers[event.Type]
	eb.mu.RUnlock()

	for _, handler := range handlers {
		if err := handler(event); err != nil {
			return err
		}
	}
	return nil
}

// Subscribe 订阅事件
func (eb *EventBusImpl) Subscribe(eventType EventType, handler EventHandler) error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	eb.handlers[eventType] = append(eb.handlers[eventType], handler)
	return nil
}

// Unsubscribe 取消订阅
func (eb *EventBusImpl) Unsubscribe(eventType EventType, handler EventHandler) error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	handlers := eb.handlers[eventType]
	for i, h := range handlers {
		if &h == &handler {
			eb.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
			break
		}
	}
	return nil
}
