package response

import (
	"github.com/gin-gonic/gin"
)

const (
	CodeSuccess       = 0
	CodeInvalidParam  = 400
	CodeInternalError = 500
)

// Success 成功响应
func Success(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": CodeSuccess,
		"data": data,
	})
}

// Fail 失败响应
func Fail(ctx *gin.Context, code int, message string) {
	ctx.JSON(200, gin.H{
		"code":    code,
		"message": message,
	})
}
