package logger

import (
	"io"

	"github.com/sirupsen/logrus"
)

// Logger 日志接口
type Logger interface {
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})
	WithFields(fields logrus.Fields) *logrus.Entry
}

// Config 日志配置
type Config struct {
	Level      string `json:"level"`       // 日志级别
	Filename   string `json:"filename"`    // 日志文件名
	MaxSize    int    `json:"max_size"`    // 单个文件最大大小，单位MB
	MaxBackups int    `json:"max_backups"` // 最大保留文件数
	MaxAge     int    `json:"max_age"`     // 最大保留天数
	Compress   bool   `json:"compress"`    // 是否压缩
}

// GetLogger 获取全局日志实例
func GetLogger() Logger {
	return logrus.StandardLogger()
}

// WithFields 创建带字段的日志条目
func WithFields(fields map[string]interface{}) *logrus.Entry {
	return logrus.WithFields(fields)
}

// SetLevel 设置日志级别
func SetLevel(level string) error {
	l, err := logrus.ParseLevel(level)
	if err != nil {
		return err
	}
	logrus.SetLevel(l)
	return nil
}

// SetFormatter 设置日志格式化器
func SetFormatter(formatter logrus.Formatter) {
	logrus.SetFormatter(formatter)
}

// SetOutput 设置日志输出
func SetOutput(output io.Writer) {
	logrus.SetOutput(output)
}
