package config

import (
	"encoding/json"
	"os"
	"sync"
)

// Config 配置管理器
type Config struct {
	mu     sync.RWMutex
	values map[string]interface{}
}

// NewConfig 创建新的配置管理器
func NewConfig() *Config {
	return &Config{
		values: make(map[string]interface{}),
	}
}

// LoadFromFile 从文件加载配置
func (c *Config) LoadFromFile(filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	return json.Unmarshal(data, &c.values)
}

// Get 获取配置值
func (c *Config) Get(key string) interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.values[key]
}

// GetString 获取字符串配置值
func (c *Config) GetString(key string) string {
	if v, ok := c.Get(key).(string); ok {
		return v
	}
	return ""
}

// GetInt 获取整数配置值
func (c *Config) GetInt(key string) int {
	if v, ok := c.Get(key).(float64); ok {
		return int(v)
	}
	return 0
}

// GetBool 获取布尔配置值
func (c *Config) GetBool(key string) bool {
	if v, ok := c.Get(key).(bool); ok {
		return v
	}
	return false
}

// Set 设置配置值
func (c *Config) Set(key string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.values[key] = value
}

// SaveToFile 保存配置到文件
func (c *Config) SaveToFile(filename string) error {
	c.mu.RLock()
	data, err := json.MarshalIndent(c.values, "", "  ")
	c.mu.RUnlock()
	if err != nil {
		return err
	}

	return os.WriteFile(filename, data, 0644)
}
