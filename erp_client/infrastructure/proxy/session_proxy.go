package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// SessionProxy 会话代理
type SessionProxy struct {
	*BaseSubjectProxy
	legacyService *impl.SessionService
}

// NewSessionProxy 创建会话代理
func NewSessionProxy() *SessionProxy {
	return &SessionProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("session"),
		legacyService:    &impl.SessionService{},
	}
}

// Create 创建场次
func (p *SessionProxy) Create(ctx context.Context, session *po.Session) error {
	// TODO: 实现远程调用
	return nil
}

// Update 更新场次
func (p *SessionProxy) Update(ctx context.Context, session *po.Session) error {
	// TODO: 实现远程调用
	return nil
}

// Delete 删除房间类型
func (p *SessionProxy) Delete(ctx context.Context, id string) error {
	return nil
}
func (p *SessionProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Session, error) {
	return nil, nil
}

// FindByID 根据ID查询场次
func (p *SessionProxy) FindByID(ctx context.Context, id string) (*po.Session, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindSessionById(ginCtx, id)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindByVenueID 根据场所ID查询场次
func (p *SessionProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.Session, error) {
	// TODO: 实现远程调用
	return []po.Session{}, nil
}

// FindAll 查询所有场次
func (p *SessionProxy) FindAll(ctx context.Context, venueId, sessionId string) (*[]po.Session, error) {
	// TODO: 实现远程调用
	return &[]po.Session{}, nil
}

// ConvertToSessionVO 转换为房间类型VO
func (p *SessionProxy) ConvertToSessionVO(ctx context.Context, session po.Session) vo.SessionVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.SessionVO{}
	}
	val := p.legacyService.ConvertToSessionVO(ginCtx, session)
	return val
}

// ConvertToSessionPO 转换为房间类型PO
func (p *SessionProxy) ConvertToSession(ctx context.Context, sessionVO vo.SessionVO) po.Session {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Session{}
	}
	val := p.legacyService.ConvertToSession(ginCtx, sessionVO)
	return val
}

// FindBySessionId 根据sessionId查询场次
func (p *SessionProxy) FindBySessionId(ctx context.Context, sessionId string, venueId string) (po.Session, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Session{}, err
	}
	return p.legacyService.FindBySessionId(ginCtx, sessionId, venueId)
}

// FindSessionsBySessionIds 根据sessionId查询场次列表
func (p *SessionProxy) FindSessionsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Session, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []po.Session{}, err
	}
	val, err := p.legacyService.FindSessionsBySessionIds(ginCtx, venueId, sessionIds)
	if err != nil {
		return []po.Session{}, err
	}
	return *val, nil
}

// FindSessionLatestByRoomId 根据roomId查询最新场次
func (p *SessionProxy) FindSessionLatestByRoomId(ctx context.Context, venueId string, roomId string) (po.Session, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Session{}, err
	}
	return p.legacyService.FindSessionLatestByRoomId(ginCtx, venueId, roomId)
}

// FindSessionsByTimeRange 根据时间范围查询场次列表
func (p *SessionProxy) FindSessionsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Session, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []po.Session{}, err
	}
	return p.legacyService.FindSessionsByTimeRange(ginCtx, venueId, startTime, endTime)
}
