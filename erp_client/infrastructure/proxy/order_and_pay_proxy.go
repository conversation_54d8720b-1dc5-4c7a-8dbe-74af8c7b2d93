package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// OrderAndPayRepositoryProxy 订单和支付仓储代理
type OrderAndPayRepositoryProxy struct {
	*BaseSubjectProxy
	orderAndPayService *impl.OrderAndPayService
}

// NewOrderAndPayRepository 创建订单和支付仓储代理实例
func NewOrderAndPayRepository() repository.OrderAndPayRepository {
	return &OrderAndPayRepositoryProxy{
		BaseSubjectProxy:   NewBaseSubjectProxy("orderAndPay"),
		orderAndPayService: &impl.OrderAndPayService{},
	}
}

// FindAllByBillIds 查询所有支付账单
func (r *OrderAndPayRepositoryProxy) FindAllByBillIds(ctx context.Context, billIds []string) (*[]po.OrderAndPay, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	val, err := r.orderAndPayService.FindAllByBillIds(ginCtx, billIds)
	if err != nil {
		return nil, err
	}
	return &val, nil
}

// ConvertToOrderAndPayVO 转换为订单和支付VO
func (r *OrderAndPayRepositoryProxy) ConvertToOrderAndPayVO(ctx context.Context, orderAndPay po.OrderAndPay) vo.OrderAndPayVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.OrderAndPayVO{}
	}
	val := r.orderAndPayService.ConvertToOrderAndPayVO(ginCtx, orderAndPay)
	return val
}

// ConvertToOrderAndPay 转换为订单和支付PO
func (r *OrderAndPayRepositoryProxy) ConvertToOrderAndPay(ctx context.Context, orderAndPayVO vo.OrderAndPayVO) po.OrderAndPay {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.OrderAndPay{}
	}
	val := r.orderAndPayService.ConvertToOrderAndPay(ginCtx, orderAndPayVO)
	return val
}

// FindAllBySessionId 查询所有支付账单
func (r *OrderAndPayRepositoryProxy) FindAllBySessionId(ctx context.Context, sessionId string) ([]po.OrderAndPay, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.orderAndPayService.FindAllBySessionId(ginCtx, sessionId)
}

// FindsByOrderNos 查询订单和支付
func (r *OrderAndPayRepositoryProxy) FindsByOrderNos(ctx context.Context, orderNos []string) ([]po.OrderAndPay, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.orderAndPayService.FindsByOrderNos(ginCtx, orderNos)
}
