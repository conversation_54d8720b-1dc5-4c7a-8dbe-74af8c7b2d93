package proxy

import (
	"context"
	"fmt"

	"voderpltvv/erp_client/domain/configuration/business/booking/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// BookingProxy Booking代理实现
type BookingProxy struct {
	*BaseSubjectProxy
	legacyService *impl.BookingService
}

// NewBookingProxy 创建Booking代理
func NewBookingProxy() repository.Repository {
	return &BookingProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("booking"),
		legacyService:    &impl.BookingService{},
	}
}

// FindById 根据ID查询预订信息
func (p *BookingProxy) FindById(ctx context.Context, id string) (*po.Booking, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.FindBookingById(ginCtx, id)
}

// UpdateStatus 更新预订状态
func (p *BookingProxy) UpdateStatus(ctx context.Context, booking *po.Booking) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.UpdateBookingPartial(ginCtx, booking)
}

// FindByCondition 根据条件查询预订信息
func (p *BookingProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Booking, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to convert context: %v", err)
	}

	// 构建查询条件
	reqDto := &req.QueryBookingReqDto{}
	if venueId, ok := condition["venue_id"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if roomId, ok := condition["room_id"].(string); ok {
		reqDto.RoomId = &roomId
	}
	if status, ok := condition["status"].(int); ok {
		reqDto.Status = &status
	}
	if arrivalTime, ok := condition["arrival_time"].(int64); ok {
		reqDto.ArrivalTime = &arrivalTime
	}

	bookings, err := p.legacyService.FindAllBooking(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	if bookings == nil {
		emptyResult := make([]po.Booking, 0)
		return &emptyResult, nil
	}

	return bookings, nil
}

// Create 创建预订
func (p *BookingProxy) Create(ctx context.Context, booking *po.Booking) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.CreateBooking(ginCtx, booking)
}

// Update 更新预订
func (p *BookingProxy) Update(ctx context.Context, booking *po.Booking) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.UpdateBooking(ginCtx, booking)
}

// Delete 删除预订
func (p *BookingProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.DeleteBooking(ginCtx, id)
}

// FindByVenueIdAndTimeRange 根据场所ID和时间范围查找预订信息
func (p *BookingProxy) FindByVenueIdAndTimeRange(ctx context.Context, venueId string, arrivalTimeStart int64, status int, orderBy string) (*[]po.Booking, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.FindBookingsByVenueIdAndTimeRange(ginCtx, venueId, arrivalTimeStart, status, orderBy)
}

// ConvertToBookingVO 转换为预订VO
func (p *BookingProxy) ConvertToBookingVO(ctx context.Context, booking po.Booking) vo.BookingVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.BookingVO{}
	}
	return p.legacyService.ConvertToBookingVO(ginCtx, booking)
}

// ConvertToBooking 转换为预订PO
func (p *BookingProxy) ConvertToBooking(ctx context.Context, bookingVO vo.BookingVO) po.Booking {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Booking{}
	}

	return p.legacyService.ConvertToBooking(ginCtx, bookingVO)
}

// BookingSuccessInOpen 预订成功后开台
func (p *BookingProxy) BookingSuccessInOpen(ctx context.Context, bookingId string, status int) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return fmt.Errorf("failed to convert context: %v", err)
	}

	return p.legacyService.BookingSuccessInOpen(ginCtx, bookingId, status)
}
