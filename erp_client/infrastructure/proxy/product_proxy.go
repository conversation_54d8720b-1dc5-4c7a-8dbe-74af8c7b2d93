package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
)

// ProductProxy 产品代理
type ProductProxy struct {
	*BaseSubjectProxy
	legacyService   *impl.ProductService
	productTransfer *transfer.ProductTransfer
}

// NewProductProxy 创建产品代理
func NewProductProxy() *ProductProxy {
	return &ProductProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("product"),
		legacyService:    &impl.ProductService{},
		productTransfer:  &transfer.ProductTransfer{},
	}
}

// Create 创建产品
func (p *ProductProxy) Create(ctx context.Context, product *po.Product) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateProduct(ginCtx, product)
}

// Update 更新产品
func (p *ProductProxy) Update(ctx context.Context, product *po.Product) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProduct(ginCtx, product)
}

// UpdatePartial 部分更新产品
func (p *ProductProxy) UpdatePartial(ctx context.Context, product *po.Product) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductPartial(ginCtx, product)
}

// Delete 删除产品
func (p *ProductProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteProduct(ginCtx, id)
}

// FindByID 根据ID查询产品
func (p *ProductProxy) FindByID(ctx context.Context, id string) (*po.Product, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindProductById(ginCtx, id)
}

// FindAll 查询所有产品
func (p *ProductProxy) FindAll(ctx context.Context, reqDto *req.QueryProductReqDto) (*[]po.Product, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindAllProduct(ginCtx, reqDto)
}

// FindAllWithPagination 分页查询所有产品
func (p *ProductProxy) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductReqDto) (*[]po.Product, int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, 0, err
	}
	return p.legacyService.FindAllProductWithPagination(ginCtx, reqDto)
}

// FindProductsByIds 根据ID列表查询产品
func (p *ProductProxy) FindProductsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindProductsByIds(ginCtx, venueId, productIds)
}

// ConvertToProductVO 转换为产品VO
func (p *ProductProxy) ConvertToProductVO(ctx context.Context, product po.Product) vo.ProductVO {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.ProductVO{}
	}
	return p.productTransfer.PoToVo(product)
}

// ConvertToProduct 转换为产品PO
func (p *ProductProxy) ConvertToProduct(ctx context.Context, productVO vo.ProductVO) po.Product {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Product{}
	}
	return p.productTransfer.VoToPo(productVO)
}

// FindsByIds 根据ID列表查询产品
func (p *ProductProxy) FindsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindProductsByIds(ginCtx, venueId, productIds)
}

// FindByCondition 根据条件查询产品
func (p *ProductProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Product, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryProductReqDto{}

	// 转换查询条件
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	if ids, ok := condition["ids"].([]string); ok {
		reqDto.Ids = &ids
	}
	if productType, ok := condition["type"].(string); ok {
		reqDto.Type = &productType
	}

	result, err := p.legacyService.FindAllProduct(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}
