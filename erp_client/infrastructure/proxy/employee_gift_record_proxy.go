package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/employee_gift_record/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// EmployeeGiftRecordRepositoryProxy 员工赠金记录仓储代理
type EmployeeGiftRecordRepositoryProxy struct {
	*BaseSubjectProxy
	employeeGiftRecordService *impl.EmployeeGiftRecordService
}

// NewEmployeeGiftRecordRepository 创建员工赠金记录仓储代理实例
func NewEmployeeGiftRecordRepository() repository.EmployeeGiftRecordRepository {
	return &EmployeeGiftRecordRepositoryProxy{
		BaseSubjectProxy:          NewBaseSubjectProxy("employeeGiftRecord"),
		employeeGiftRecordService: &impl.EmployeeGiftRecordService{},
	}
}

// CreateEmployeeGiftRecord 创建员工赠金记录
func (r *EmployeeGiftRecordRepositoryProxy) CreateEmployeeGiftRecord(ctx context.Context, record *po.EmployeeGiftRecord) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.employeeGiftRecordService.CreateEmployeeGiftRecord(ginCtx, record)
}

// UpdateEmployeeGiftRecord 更新员工赠金记录
func (r *EmployeeGiftRecordRepositoryProxy) UpdateEmployeeGiftRecord(ctx context.Context, record *po.EmployeeGiftRecord) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.employeeGiftRecordService.UpdateEmployeeGiftRecordPartial(ginCtx, record)
}

// FindById 根据ID查询员工赠金记录
func (r *EmployeeGiftRecordRepositoryProxy) FindById(ctx context.Context, id string) (po.EmployeeGiftRecord, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.EmployeeGiftRecord{}, err
	}
	record, err := r.employeeGiftRecordService.FindEmployeeGiftRecordById(ginCtx, id)
	if err != nil {
		return po.EmployeeGiftRecord{}, err
	}
	return *record, nil
}

// ConvertToEmployeeGiftRecordVO 转换为员工赠金记录VO
func (r *EmployeeGiftRecordRepositoryProxy) ConvertToEmployeeGiftRecordVO(ctx context.Context, record po.EmployeeGiftRecord) vo.EmployeeGiftRecordVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.EmployeeGiftRecordVO{}
	}
	return r.employeeGiftRecordService.ConvertToEmployeeGiftRecordVO(ginCtx, record)
}

// ConvertToEmployeeGiftRecord 转换为员工赠金记录PO
func (r *EmployeeGiftRecordRepositoryProxy) ConvertToEmployeeGiftRecord(ctx context.Context, recordVO vo.EmployeeGiftRecordVO) po.EmployeeGiftRecord {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.EmployeeGiftRecord{}
	}
	return r.employeeGiftRecordService.ConvertToEmployeeGiftRecordPO(ginCtx, recordVO)
}
