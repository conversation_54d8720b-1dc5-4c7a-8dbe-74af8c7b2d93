package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/call/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// CallMessageRepositoryProxy 呼叫消息仓储代理
type CallMessageRepositoryProxy struct {
	*BaseSubjectProxy
	callMessageService *impl.CallMessageService
}

// NewCallMessageRepository 创建呼叫消息仓储代理实例
func NewCallMessageRepository() repository.CallMessageRepository {
	return &CallMessageRepositoryProxy{
		BaseSubjectProxy:   NewBaseSubjectProxy("callMessage"),
		callMessageService: &impl.CallMessageService{},
	}
}

// CreateCallMessage 创建呼叫消息
func (r *CallMessageRepositoryProxy) CreateCallMessage(ctx context.Context, callMessage *po.CallMessage) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.callMessageService.CreateCallMessage(ginCtx, callMessage)
}

// ConvertToCallMessageVO 转换为呼叫消息VO
func (r *CallMessageRepositoryProxy) ConvertToCallMessageVO(ctx context.Context, callMessage po.CallMessage) vo.CallMessageVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.CallMessageVO{}
	}
	return r.callMessageService.ConvertToCallMessageVO(ginCtx, callMessage)
}

// ConvertToCallMessage 转换为呼叫消息PO
func (r *CallMessageRepositoryProxy) ConvertToCallMessage(ctx context.Context, callMessageVO vo.CallMessageVO) po.CallMessage {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.CallMessage{}
	}
	return r.callMessageService.ConvertToCallMessagePO(ginCtx, callMessageVO)
}

// FindCallMessagesCurrentDay 查询当前日呼叫消息
func (r *CallMessageRepositoryProxy) FindCallMessagesCurrentDay(ctx context.Context, venueId string) ([]po.CallMessage, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.callMessageService.FindCallMessagesCurrentDay(ginCtx, venueId)
}

// FindById 查询呼叫消息
func (r *CallMessageRepositoryProxy) FindById(ctx context.Context, id string) (po.CallMessage, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.CallMessage{}, err
	}
	record, err := r.callMessageService.FindCallMessageById(ginCtx, id)
	if err != nil {
		return po.CallMessage{}, err
	}
	return *record, nil
}

// UpdateCallMessage 更新呼叫消息
func (r *CallMessageRepositoryProxy) UpdateCallMessage(ctx context.Context, callMessage *po.CallMessage) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.callMessageService.UpdateCallMessagePartial(ginCtx, callMessage)
}

// SendNATSCallMessage 发送NATS呼叫消息
func (r *CallMessageRepositoryProxy) SendNATSCallMessage(ctx context.Context, venueId string) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.callMessageService.SendNATSCallMessage(ginCtx, venueId)
}

// FindCallMessagesUnprocessed 查询未处理的呼叫消息
func (r *CallMessageRepositoryProxy) FindCallMessagesUnprocessed(ctx context.Context, venueId string) ([]po.CallMessage, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.callMessageService.FindCallMessagesUnprocessed(ginCtx, venueId)
}

// FindCallMessagesUnprocessedByRoomId 根据房间ID查询未处理的呼叫消息
func (r *CallMessageRepositoryProxy) FindCallMessagesUnprocessedByRoomId(ctx context.Context, roomId string) ([]po.CallMessage, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.callMessageService.FindCallMessagesUnprocessedByRoomId(ginCtx, roomId)
}
