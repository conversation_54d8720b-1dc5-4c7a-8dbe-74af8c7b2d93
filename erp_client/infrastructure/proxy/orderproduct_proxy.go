package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/orderproduct/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// OrderProductProxy 订单房间计划代理实现
type OrderProductProxy struct {
	*BaseSubjectProxy
	legacyService *impl.OrderProductService
}

// NewOrderProductProxy 创建OrderProduct代理
func NewOrderProductProxy() repository.Repository {
	return &OrderProductProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("order_Product"),
		legacyService:    &impl.OrderProductService{},
	}
}

// Create 创建订单房间计划
func (p *OrderProductProxy) Create(ctx context.Context, orderProduct *po.OrderProduct) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateOrderProduct(ginCtx, orderProduct)
}

// Update 更新订单房间计划
func (p *OrderProductProxy) Update(ctx context.Context, orderProduct *po.OrderProduct) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateOrderProduct(ginCtx, orderProduct)
}

// Delete 删除订单房间计划
func (p *OrderProductProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteOrderProduct(ginCtx, id)
}

// FindByCondition 根据条件查询订单房间计划
func (p *OrderProductProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (list *[]po.OrderProduct, err error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryOrderProductReqDto{}

	result, err := p.legacyService.FindAllOrderProduct(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindByID 根据ID查询订单房间计划
func (p *OrderProductProxy) FindByID(ctx context.Context, id string) (*po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	orderProduct, err := p.legacyService.FindOrderProductById(ginCtx, id)
	if err != nil {
		return nil, err
	}

	if orderProduct == nil {
		return nil, nil
	}

	return orderProduct, nil
}

// GetOrderProduct 获取订单房间计划
func (p *OrderProductProxy) GetOrderProduct(ctx context.Context, id string) (po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.OrderProduct{}, err
	}
	val, err := p.legacyService.FindOrderProductById(ginCtx, id)
	if err != nil {
		return po.OrderProduct{}, err
	}
	return *val, nil
}

func (p *OrderProductProxy) ConvertToOrderProductVO(ctx context.Context, orderProduct po.OrderProduct) vo.OrderProductVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.OrderProductVO{}
	}
	val := p.legacyService.ConvertToOrderProductVO(ginCtx, orderProduct)
	return val
}

func (p *OrderProductProxy) ConvertToOrderProduct(ctx context.Context, orderProductVO vo.OrderProductVO) po.OrderProduct {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.OrderProduct{}
	}
	val := p.legacyService.ConvertToOrderProduct(ginCtx, orderProductVO)
	return val
}

// FindBySessionId 根据sessionId查询订单商品
func (p *OrderProductProxy) FindAllBySessionId(ctx context.Context, sessionId string, venueId string) (*[]po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindAllBySessionId(ginCtx, sessionId, venueId)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindAllByIds 根据ids查询订单商品
func (p *OrderProductProxy) FindAllByIds(ctx context.Context, sessionId string, venueId string, ids []string) ([]po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindAllByIds(ginCtx, sessionId, venueId, ids)
	if err != nil {
		return nil, err
	}
	return *val, nil
}

// FindsByOrderNos 根据orderNos查询订单商品
func (p *OrderProductProxy) FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindsByOrderNos(ginCtx, venueId, orderNos)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindOrderProductsTimeRange 查询订单商品时间范围
func (p *OrderProductProxy) FindOrderProductsTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindOrderProductsTimeRange(ginCtx, venueId, startTime, endTime)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindProductsByIds 查询商品信息
func (p *OrderProductProxy) FindProductsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindProductsByIds(ginCtx, venueId, productIds)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindCategorysByIds 查询分类信息
func (p *OrderProductProxy) FindCategorysByIds(ctx context.Context, venueId string, categoryIds []string) ([]po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindCategorysByIds(ginCtx, venueId, categoryIds)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindOrderProductsGiftBySessionIds 查询订单商品时间范围
func (p *OrderProductProxy) FindOrderProductsGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderProduct, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindOrderProductsGiftBySessionIds(ginCtx, venueId, sessionIds)
	if err != nil {
		return nil, err
	}
	return val, nil
}