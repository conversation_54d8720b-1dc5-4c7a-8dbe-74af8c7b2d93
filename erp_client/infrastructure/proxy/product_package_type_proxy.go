package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
)

// ProductPackageTypeProxy 产品套餐类型代理
type ProductPackageTypeProxy struct {
	*BaseSubjectProxy
	legacyService              *impl.ProductPackageTypeService
	productPackageTypeTransfer *transfer.ProductPackageTypeTransfer
}

// NewProductPackageTypeProxy 创建产品套餐类型代理
func NewProductPackageTypeProxy() *ProductPackageTypeProxy {
	return &ProductPackageTypeProxy{
		BaseSubjectProxy:           NewBaseSubjectProxy("product_package_type"),
		legacyService:              &impl.ProductPackageTypeService{},
		productPackageTypeTransfer: &transfer.ProductPackageTypeTransfer{},
	}
}

// Create 创建产品套餐类型
func (p *ProductPackageTypeProxy) Create(ctx context.Context, productPackageType *po.ProductPackageType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateProductPackageType(ginCtx, productPackageType)
}

// Update 更新产品套餐类型
func (p *ProductPackageTypeProxy) Update(ctx context.Context, productPackageType *po.ProductPackageType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductPackageType(ginCtx, productPackageType)
}

// UpdatePartial 部分更新产品套餐类型
func (p *ProductPackageTypeProxy) UpdatePartial(ctx context.Context, productPackageType *po.ProductPackageType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductPackageTypePartial(ginCtx, productPackageType)
}

// Delete 删除产品套餐类型
func (p *ProductPackageTypeProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteProductPackageType(ginCtx, id)
}

// FindByID 根据ID查询产品套餐类型
func (p *ProductPackageTypeProxy) FindByID(ctx context.Context, id string) (*po.ProductPackageType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindProductPackageTypeById(ginCtx, id)
}

// FindAll 查询所有产品套餐类型
func (p *ProductPackageTypeProxy) FindAll(ctx context.Context, reqDto *req.QueryProductPackageTypeReqDto) (*[]po.ProductPackageType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindAllProductPackageType(ginCtx, reqDto)
}

// FindAllWithPagination 分页查询所有产品套餐类型
func (p *ProductPackageTypeProxy) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageTypeReqDto) (*[]po.ProductPackageType, int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, 0, err
	}
	return p.legacyService.FindAllProductPackageTypeWithPagination(ginCtx, reqDto)
}

// FindProductPackageTypesByIds 根据ID列表查询产品套餐类型
func (p *ProductPackageTypeProxy) FindProductPackageTypesByIds(ctx context.Context, venueId string, productPackageTypeIds []string) ([]po.ProductPackageType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	// 由于QueryProductPackageTypeReqDto没有Ids字段，我们需要逐个查询
	var results []po.ProductPackageType
	for _, id := range productPackageTypeIds {
		reqDto := &req.QueryProductPackageTypeReqDto{
			VenueId: &venueId,
			Id:      &id,
		}
		result, err := p.legacyService.FindAllProductPackageType(ginCtx, reqDto)
		if err != nil {
			return nil, err
		}
		if result != nil && len(*result) > 0 {
			results = append(results, (*result)...)
		}
	}
	return results, nil
}

// ConvertToProductPackageTypeVO 转换为产品套餐类型VO
func (p *ProductPackageTypeProxy) ConvertToProductPackageTypeVO(ctx context.Context, productPackageType po.ProductPackageType) vo.ProductPackageTypeVO {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.ProductPackageTypeVO{}
	}
	return p.productPackageTypeTransfer.PoToVo(productPackageType)
}

// ConvertToProductPackageType 转换为产品套餐类型PO
func (p *ProductPackageTypeProxy) ConvertToProductPackageType(ctx context.Context, productPackageTypeVO vo.ProductPackageTypeVO) po.ProductPackageType {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return po.ProductPackageType{}
	}
	return p.productPackageTypeTransfer.VoToPo(productPackageTypeVO)
}

// FindsByIds 根据ID列表查询产品套餐类型
func (p *ProductPackageTypeProxy) FindsByIds(ctx context.Context, venueId string, productPackageTypeIds []string) ([]po.ProductPackageType, error) {
	return p.FindProductPackageTypesByIds(ctx, venueId, productPackageTypeIds)
}

// FindByCondition 根据条件查询产品套餐类型
func (p *ProductPackageTypeProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.ProductPackageType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryProductPackageTypeReqDto{}

	// 转换查询条件
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	// QueryProductPackageTypeReqDto 不支持 Ids 字段，跳过这个条件
	if distributionChannels, ok := condition["distributionChannels"].(string); ok {
		reqDto.DistributionChannels = &distributionChannels
	}
	if isDisplayed, ok := condition["isDisplayed"].(bool); ok {
		reqDto.IsDisplayed = &isDisplayed
	}
	if supportPoints, ok := condition["supportPoints"].(bool); ok {
		reqDto.SupportPoints = &supportPoints
	}

	result, err := p.legacyService.FindAllProductPackageType(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}
