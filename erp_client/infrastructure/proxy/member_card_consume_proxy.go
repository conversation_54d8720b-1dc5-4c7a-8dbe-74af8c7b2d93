package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardConsumeRepositoryProxy 会员卡消费记录仓储代理
type MemberCardConsumeRepositoryProxy struct {
	*BaseSubjectProxy
	memberCardConsumeService *impl.MemberCardConsumeService
}

// NewMemberCardConsumeRepository 创建会员卡消费记录仓储代理实例
func NewMemberCardConsumeRepository() repository.MemberCardConsumeRepository {
	return &MemberCardConsumeRepositoryProxy{
		BaseSubjectProxy:         NewBaseSubjectProxy("memberCardConsume"),
		memberCardConsumeService: &impl.MemberCardConsumeService{},
	}
}

// CreateMemberCardConsume 创建会员卡消费记录
func (r *MemberCardConsumeRepositoryProxy) CreateMemberCardConsume(ctx context.Context, memberCardConsume *po.MemberCardConsume) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberCardConsumeService.CreateMemberCardConsume(ginCtx, memberCardConsume)
}

// ConvertToMemberCardConsumeVO 转换为会员卡消费记录VO
func (r *MemberCardConsumeRepositoryProxy) ConvertToMemberCardConsumeVO(ctx context.Context, memberCardConsume po.MemberCardConsume) vo.MemberCardConsumeVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberCardConsumeVO{}
	}
	return r.memberCardConsumeService.ConvertToMemberCardConsumeVO(ginCtx, memberCardConsume)
}

// ConvertToMemberCardConsume 转换为会员卡消费记录PO
func (r *MemberCardConsumeRepositoryProxy) ConvertToMemberCardConsume(ctx context.Context, memberCardConsumeVO vo.MemberCardConsumeVO) po.MemberCardConsume {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberCardConsume{}
	}
	return r.memberCardConsumeService.ConvertToMemberCardConsume(ginCtx, memberCardConsumeVO)
}

// FindMemberCardConsumesByTimeRange 根据时间范围查询会员卡消费记录
func (r *MemberCardConsumeRepositoryProxy) FindMemberCardConsumesRechargeByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.MemberCardConsume, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.memberCardConsumeService.FindMemberCardConsumesRechargeByTimeRange(ginCtx, venueId, startTime, endTime)
}