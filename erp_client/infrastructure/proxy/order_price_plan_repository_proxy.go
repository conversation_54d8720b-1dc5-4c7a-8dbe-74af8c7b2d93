package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// OrderPricePlanRepositoryProxy 订单价格方案仓储代理
type OrderPricePlanRepositoryProxy struct {
	*BaseSubjectProxy
	orderPricePlanService *impl.OrderPricePlanService
}

// NewOrderPricePlanRepository 创建订单价格方案仓储代理实例
func NewOrderPricePlanRepository() repository.OrderPricePlanRepository {
	return &OrderPricePlanRepositoryProxy{
		orderPricePlanService: &impl.OrderPricePlanService{},
	}
}

// FindAllOrderPricePlan 查询所有订单价格方案
func (r *OrderPricePlanRepositoryProxy) FindAllOrderPricePlan(ctx context.Context, venueId, sessionId string) (*[]po.OrderPricePlan, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryOrderPricePlanReqDto{
		VenueId:   &venueId,
		SessionId: &sessionId,
	}
	return r.orderPricePlanService.FindAllOrderPricePlan(ginCtx, reqDto)
}

// FindOrderPricePlansByVenueIDAndSessionId 根据场所ID和场次ID查询订单价格计划
func (r *OrderPricePlanRepositoryProxy) FindOrderPricePlansByVenueIDAndSessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderPricePlan, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	list, err := r.orderPricePlanService.FindOrderPricePlansByVenueIDAndSessionId(ginCtx, venueId, sessionId)
	if err != nil {
		return nil, err
	}
	return &list, nil
}

// FindOrderPricePlansByVenueIDAndSessionId 根据场所ID和场次ID查询订单价格计划
func (r *OrderPricePlanRepositoryProxy) FindOrderPricePlansByVenueIDAndSessionIds(ctx context.Context, venueId string, sessionIds []string) (*[]po.OrderPricePlan, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	list, err := r.orderPricePlanService.FindOrderPricePlansByVenueIDAndSessionIds(ginCtx, venueId, sessionIds)
	if err != nil {
		return nil, err
	}
	return &list, nil
}

// ConvertToPricePlanVO 转换为价格方案VO
func (r *OrderPricePlanRepositoryProxy) ConvertToPricePlanVO(ctx context.Context, orderPricePlan po.OrderPricePlan) vo.OrderPricePlanVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.OrderPricePlanVO{}
	}
	return r.orderPricePlanService.ConvertToPricePlanVO(ginCtx, orderPricePlan)
}

// ConvertToPricePlan 转换为价格方案PO
func (r *OrderPricePlanRepositoryProxy) ConvertToPricePlan(ctx context.Context, orderPricePlanVO vo.OrderPricePlanVO) po.OrderPricePlan {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.OrderPricePlan{}
	}
	return r.orderPricePlanService.ConvertToPricePlan(ginCtx, orderPricePlanVO)
}
