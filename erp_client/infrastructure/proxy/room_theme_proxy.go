package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// RoomThemeProxy 门店代理实现
type RoomThemeProxy struct {
	*BaseSubjectProxy
	legacyService *impl.RoomThemeService
}

// NewRoomThemeProxy 创建门店代理实例
func NewRoomThemeProxy() *RoomThemeProxy {
	return &RoomThemeProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("room_theme"),
		legacyService:    &impl.RoomThemeService{},
	}
}

// Create 创建场所
func (p *RoomThemeProxy) Create(ctx context.Context, roomTheme *po.RoomTheme) error {
	// TODO: 实现远程调用
	return nil
}

// Update 更新场所
func (p *RoomThemeProxy) Update(ctx context.Context, roomTheme *po.RoomTheme) error {
	// TODO: 实现远程调用
	return nil
}

// Delete 删除场所
func (p *RoomThemeProxy) Delete(ctx context.Context, id string) error {
	// TODO: 实现远程调用
	return nil
}

// FindByCondition 根据条件查询场所
func (p *RoomThemeProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.RoomTheme, error) {
	// TODO: 实现远程调用
	return nil, nil
}

// FindByID 根据ID查询场所
func (p *RoomThemeProxy) FindByID(ctx context.Context, id string) (*po.RoomTheme, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindRoomThemeById(ginCtx, id)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindByVenueID 根据场所ID查询场所
func (p *RoomThemeProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.RoomTheme, error) {
	// TODO: 实现远程调用
	return []po.RoomTheme{}, nil
}

// FindAll 查询所有场所
func (p *RoomThemeProxy) FindAll(ctx context.Context, venueId, sessionId string) (*[]po.RoomTheme, error) {
	// TODO: 实现远程调用
	return &[]po.RoomTheme{}, nil
}

// ConvertToRoomThemeVO 转换为场所VO
func (p *RoomThemeProxy) ConvertToRoomThemeVO(ctx context.Context, roomTheme po.RoomTheme) vo.RoomThemeVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.RoomThemeVO{}
	}
	val := p.legacyService.ConvertToRoomThemeVO(ginCtx, roomTheme)
	return val
}

// ConvertToRoomThemePO 转换为场所PO
func (p *RoomThemeProxy) ConvertToRoomTheme(ctx context.Context, roomThemeVO vo.RoomThemeVO) po.RoomTheme {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.RoomTheme{}
	}
	val := p.legacyService.ConvertToRoomTheme(ginCtx, roomThemeVO)
	return val
}

// FindRoomThemesByRoomIds 根据房间ID查询场所列表
func (p *RoomThemeProxy) FindRoomThemesByRoomIds(ctx context.Context, venueId string, roomIds []string) ([]po.RoomTheme, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []po.RoomTheme{}, err
	}
	val, err := p.legacyService.FindRoomThemesByRoomIds(ginCtx, venueId, roomIds)
	if err != nil {
		return []po.RoomTheme{}, err
	}
	return *val, nil
}
