package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// PayProxy 支付代理
type PayProxy struct {
	*BaseSubjectProxy
	legacyService *impl.PayService
}

// NewPayProxy 创建支付代理
func NewPayProxy() repository.PayRepository {
	return &PayProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("pay"),
		legacyService:    &impl.PayService{},
	}
}

// FindOrdersBySessionId 根据场次ID查询订单
func (p *PayProxy) FindOrdersBySessionId(ctx context.Context, venueId, sessionId string) (*[]po.Order, error) {
	// TODO: 实现远程调用
	return &[]po.Order{}, nil
}

// FindOrderRoomPlansBySessionId 根据场次ID查询订单房间计划
func (p *PayProxy) FindOrderRoomPlansBySessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderRoomPlan, error) {
	// TODO: 实现远程调用
	return &[]po.OrderRoomPlan{}, nil
}

// FindOrderProductsBySessionId 根据场次ID查询订单商品
func (p *PayProxy) FindOrderProductsBySessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderProduct, error) {
	// TODO: 实现远程调用
	return &[]po.OrderProduct{}, nil
}

// SavePayBill 保存支付账单
func (p *PayProxy) SavePayBill(ctx context.Context, payBill *po.PayBill) error {
	// TODO: 实现远程调用
	return nil
}

// SaveOrderAndPays 保存订单支付关系
func (p *PayProxy) SaveOrderAndPays(ctx context.Context, orderAndPays *[]po.OrderAndPay) error {
	// TODO: 实现远程调用
	return nil
}

// UpdateOrders 更新订单
func (p *PayProxy) UpdateOrders(ctx context.Context, orders *[]po.Order) error {
	// TODO: 实现远程调用
	return nil
}

// UpdateOrderRoomPlans 更新订单房间计划
func (p *PayProxy) UpdateOrderRoomPlans(ctx context.Context, orderRoomPlans *[]po.OrderRoomPlan) error {
	// TODO: 实现远程调用
	return nil
}

// UpdateOrderProducts 更新订单商品
func (p *PayProxy) UpdateOrderProducts(ctx context.Context, orderProducts *[]po.OrderProduct) error {
	// TODO: 实现远程调用
	return nil
}

// SaveBatchTxForPay 批量保存支付数据（事务）
func (p *PayProxy) SaveBatchTxForPay(ctx context.Context, payBill *po.PayBill,
	orderAndPays *[]po.OrderAndPay, orders *[]po.Order,
	orderRoomPlans *[]po.OrderRoomPlan, orderProducts *[]po.OrderProduct) error {
	// TODO: 实现远程调用
	return nil
}

// FindPayBillById 根据ID查询支付账单
func (p *PayProxy) FindPayBillById(ctx context.Context, payId string) (*po.PayBill, error) {
	// TODO: 实现远程调用
	return &po.PayBill{}, nil
}

// QueryPaidOrderVOsBySessionId 根据场次ID查询已支付订单
func (p *PayProxy) QueryPaidOrderVOsBySessionId(ctx context.Context, venueId, sessionId string) (int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return 0, err
	}
	hasPaidAmount, err := p.legacyService.QueryPaidOrderVOsBySessionId(ginCtx, venueId, sessionId)
	if err != nil {
		return 0, err
	}
	return hasPaidAmount, nil
}

// ConvertOrderVOToPO 转换订单VO为PO
func (p *PayProxy) ConvertOrderVOToPO(ctx context.Context, orderVO *vo.OrderVO) *po.Order {
	// TODO: 实现远程调用
	return &po.Order{}
}

// CalculateManyFee 计算多个费用
func (p *PayProxy) CalculateManyFee(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return p.legacyService.CalculateManyFee(ginCtx, totalFeeThis, opsNotInDB, omsNotInDB, sessionId, venueId, lastMinimumCharge)
}

// CalculateManyFeeForAdditionalOrder 计算多个费用-新增订单
func (p *PayProxy) CalculateManyFeeForAdditionalOrder(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return p.legacyService.CalculateManyFeeForAdditionalOrder(ginCtx, totalFeeThis, opsNotInDB, omsNotInDB, sessionId, venueId, lastMinimumCharge)
}

// CalculateManyFeeForPay 计算多个费用-支付-后付
func (p *PayProxy) CalculateManyFeeForPay(ctx context.Context, totalFeeThis int64, opsInDB []vo.OrderProductVO, omsInDB []vo.OrderRoomPlanVO, orderVOs []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return p.legacyService.CalculateManyFeeForPay(ginCtx, totalFeeThis, opsInDB, omsInDB, orderVOs, sessionId, venueId, lastMinimumCharge)
}

// CalculateManyFeeForTransferRoom 计算多个费用-支付-后付
func (p *PayProxy) CalculateManyFeeForTransferRoom(ctx context.Context, orderVOMarkDelete []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return p.legacyService.CalculateManyFeeForTransferRoom(ginCtx, orderVOMarkDelete, sessionId, venueId, lastMinimumCharge)
}

// GetOrdersInfoByOrderNos 根据订单号查询订单信息
func (p *PayProxy) GetOrdersInfoByOrderNos(ctx context.Context, orderNos []string, venueId string, sessionId string) ([]vo.OrderVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	orderVOs, err := p.legacyService.GetOrdersInfoByOrderNos(ginCtx, orderNos, venueId, sessionId)
	if err != nil {
		return nil, err
	}
	return orderVOs, nil
}

// SaveOrderPayInfoCallbackByPayId 保存支付信息回调
func (p *PayProxy) SaveOrderPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.OrderPayCallbackVO) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.SaveOrderPayInfoCallbackByPayId(ginCtx, callbackVO)
}

// SaveOrderPayInfoCallbackByBillIdForFree 保存支付信息回调
func (p *PayProxy) SaveOrderPayInfoCallbackByBillIdForFree(ctx context.Context, billId string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.SaveOrderPayInfoCallbackByBillIdForFree(ginCtx, billId)
}

// V3RefundByCash 按现金退款
func (p *PayProxy) V3RefundByCash(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	orderRefundInfoVO, err := p.legacyService.V3RefundByCash(ginCtx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	return orderRefundInfoVO, nil
}

// V3RefundByBack 按原路返回退款
func (p *PayProxy) V3RefundByBack(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	orderRefundInfoVOs, err := p.legacyService.V3RefundByBack(ginCtx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	return orderRefundInfoVOs, nil
}

// RefundAllOrderBySessionId
func (p *PayProxy) RefundAllOrderBySessionId(ctx context.Context, sessionId string, venueId string) (vo.OrderPOInfoUnionVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.OrderPOInfoUnionVO{}, err
	}
	OrderPOInfoUnionVOs, err := p.legacyService.RefundAllOrderBySessionId(ginCtx, sessionId, venueId)
	if err != nil {
		return vo.OrderPOInfoUnionVO{}, err
	}
	return OrderPOInfoUnionVOs, nil
}

// GetOrderPOInfoBySessionId 查询session下的所有订单相关PO数据
func (p *PayProxy) GetOrderPOInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.OrderPOInfoUnionVO{}
	}
	return p.legacyService.GetOrderPOInfoBySessionId(ginCtx, sessionId, venueId)
}

// GetPayInfoBySessionId 查询session下的所有支付相关PO数据
func (p *PayProxy) GetPayInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.OrderPOInfoUnionVO{}
	}
	return p.legacyService.GetPayInfoBySessionId(ginCtx, sessionId, venueId)
}

// GetPayInfoBillBackBySessionId 查询session下的所有支付相关PO数据
func (p *PayProxy) GetPayInfoBillBackBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.OrderPOInfoUnionVO{}
	}
	return p.legacyService.GetPayInfoBillBackBySessionId(ginCtx, sessionId, venueId)
}
// BuildOrderVOInfoMergedVOs 构造mergevo
func (p *PayProxy) BuildOrderVOInfoMergedVOs(ctx context.Context, oisVO vo.OrderPOInfoUnionVO) []vo.OrderVOInfoMergedVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []vo.OrderVOInfoMergedVO{}
	}
	return p.legacyService.BuildOrderVOInfoMergedVOs(ginCtx, oisVO)
}

// UpdateSessionInfoUnpaid 更新session信息-未支付
func (p *PayProxy) UpdateSessionInfoUnpaid(ctx context.Context, sessionId string, venueId string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateSessionInfoUnpaid(ginCtx, sessionId, venueId)
}

// BuildPayBillVOInfoBackVO 构建backvo,计算每个bill最终通过哪种方式退款多少钱
func (p *PayProxy) BuildPayBillVOInfoBackVO(ctx context.Context, payBills []po.PayBill, payRecords []po.PayRecord, orderAndPays []po.OrderAndPay) []vo.PayBillVOInfoBackVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []vo.PayBillVOInfoBackVO{}
	}
	return p.legacyService.BuildPayBillVOInfoBackVO(ginCtx, payBills, payRecords, orderAndPays)
}

// SavePayBillBack 保存还原账单
func (p *PayProxy) SavePayBillBack(ctx context.Context, payBillPOInfoBackVOs []vo.PayBillPOInfoBackVO) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.SavePayBillBack(ginCtx, payBillPOInfoBackVOs)
}

// V3BillBack 还原账单
func (p *PayProxy) V3BillBack(ctx context.Context, reqDto req.V3BillBackReqDto, newPayBillVOInfoBackVOs []vo.PayBillPOInfoBackVO) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.V3BillBack(ginCtx, reqDto, newPayBillVOInfoBackVOs)
}

// V3TransformPayGate 转换支付网关
func (p *PayProxy) V3TransformPayGate(ctx context.Context, reqDto *req.V3QueryOrderPayTransformReqDto, toPayBill *po.PayBill) ([]vo.PayResultVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.V3TransformPayGate(ginCtx, reqDto, toPayBill)
}

// V3CallPayCallback 乐刷支付回调
func (p *PayProxy) V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.V3CallPayCallback(ginCtx, reqDto)
}

// V3CallRefundCallback 乐刷退款回调
func (p *PayProxy) V3CallRefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.V3CallRefundCallback(ginCtx, reqDto)
}

// TransferLeshuaRefund 乐刷退款
func (p *PayProxy) TransferLeshuaRefund(ctx context.Context, payRecordVO vo.PayRecordVO) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.TransferLeshuaRefund(ginCtx, payRecordVO)
}

// StartLeshuaTimer 启动乐刷支付定时器
func (p *PayProxy) StartLeshuaTimer(ctx context.Context, key string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.StartLeshuaTimer(ginCtx, key)
}

// SavePayBillInfoBillBackVOs 保存还原账单
func (p *PayProxy) SavePayBillInfoBillBackVOs(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO, reqDto req.V3BillBackReqDto) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.SavePayBillInfoBillBackVOs(ginCtx, payBillInfoBillBackVOs, reqDto)
}

// SendNATSMessageForRoomStatusChanged 发送NATS消息
func (p *PayProxy) SendNATSMessageForRoomStatusChanged(ctx context.Context, venueId string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.SendNATSMessageForRoomStatusChanged(ginCtx, venueId)
}
// V3RefundByCashRoomPlan 按现金退款
func (p *PayProxy) V3RefundByCashRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	orderRefundInfoVO, err := p.legacyService.V3RefundByCashRoomPlan(ginCtx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	return orderRefundInfoVO, nil
}

// V3RefundByBackRoomPlan 按原路返回退款
func (p *PayProxy) V3RefundByBackRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	orderRefundInfoVOs, err := p.legacyService.V3RefundByBackRoomPlan(ginCtx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
	if err != nil {
		return []vo.OrderRefundInfoVO{}, err
	}
	return orderRefundInfoVOs, nil
}

// GetOrderInfoBatchBySessionId 获取session下的所有订单相关PO数据
func (p *PayProxy) GetOrderInfoBatchBySessionId(ctx context.Context, sessionId string, venueId string, roomId string, employeeId string) (vo.ModeOrderInfoBaseSessionPO, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.ModeOrderInfoBaseSessionPO{}, err
	}
	return p.legacyService.GetOrderInfoBatchBySessionId(ginCtx, sessionId, venueId, roomId, employeeId)
}

// DoAndSaveCancelOrderOpenInfo 取消开台
func (p *PayProxy) DoAndSaveCancelOrderOpenInfo(ctx context.Context, venueId string, modeOrderInfoBaseSessionPO vo.ModeOrderInfoBaseSessionPO) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DoAndSaveCancelOrderOpenInfo(ginCtx, venueId, modeOrderInfoBaseSessionPO)
}