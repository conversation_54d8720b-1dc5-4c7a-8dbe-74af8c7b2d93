package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// RoomOperationProxy 会话代理
type RoomOperationProxy struct {
	*BaseSubjectProxy
	legacyService *impl.RoomOperationService
}

// NewRoomOperationProxy 创建会话代理
func NewRoomOperationProxy() *RoomOperationProxy {
	return &RoomOperationProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("room_operation"),
		legacyService:    &impl.RoomOperationService{},
	}
}

// Create 创建场次
func (p *RoomOperationProxy) Create(ctx context.Context, roomOperation *po.RoomOperation) error {
	// TODO: 实现远程调用
	return nil
}

// Update 更新场次
func (p *RoomOperationProxy) Update(ctx context.Context, roomOperation *po.RoomOperation) error {
	// TODO: 实现远程调用
	return nil
}

// Delete 删除房间类型
func (p *RoomOperationProxy) Delete(ctx context.Context, id string) error {
	return nil
}
func (p *RoomOperationProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.RoomOperation, error) {
	return nil, nil
}

// FindByID 根据ID查询场次
func (p *RoomOperationProxy) FindByID(ctx context.Context, id string) (*po.RoomOperation, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindRoomOperationById(ginCtx, id)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindByVenueID 根据场所ID查询场次
func (p *RoomOperationProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.RoomOperation, error) {
	// TODO: 实现远程调用
	return []po.RoomOperation{}, nil
}

// FindAll 查询所有场次
func (p *RoomOperationProxy) FindAll(ctx context.Context, venueId, sessionId string) (*[]po.RoomOperation, error) {
	// TODO: 实现远程调用
	return &[]po.RoomOperation{}, nil
}

// ConvertToRoomOperationVO 转换为房间类型VO
func (p *RoomOperationProxy) ConvertToRoomOperationVO(ctx context.Context, roomOperation po.RoomOperation) vo.RoomOperationVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.RoomOperationVO{}
	}
	val := p.legacyService.ConvertToRoomOperationVO(ginCtx, roomOperation)
	return val
}

// ConvertToRoomOperationPO 转换为房间类型PO
func (p *RoomOperationProxy) ConvertToRoomOperation(ctx context.Context, roomOperationVO vo.RoomOperationVO) po.RoomOperation {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.RoomOperation{}
	}
	val := p.legacyService.ConvertToRoomOperation(ginCtx, roomOperationVO)
	return val
}
