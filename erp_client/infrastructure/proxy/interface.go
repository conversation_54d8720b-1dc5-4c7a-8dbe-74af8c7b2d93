package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/subject/base/model"
	"voderpltvv/erp_managent/service/po"
)

// ISubjectProxy 主体代理接口
type ISubjectProxy interface {
	// 查询操作
	GetByID(ctx context.Context, id string) (model.Subject, error)
	List(ctx context.Context, conditions map[string]interface{}) ([]model.Subject, error)
	GetByIDs(ctx context.Context, ids []string) ([]model.Subject, error)
}

// Proxy 代理接口
type Proxy interface {
	// GetArea 获取区域信息
	GetArea(ctx context.Context, id string) (*po.Area, error)

	// GetAreasByVenueID 根据场馆ID获取区域列表
	GetAreasByVenueID(ctx context.Context, venueID string) ([]*po.Area, error)

	// ... 其他代理方法
}
