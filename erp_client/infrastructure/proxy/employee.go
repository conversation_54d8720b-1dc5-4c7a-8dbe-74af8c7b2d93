package proxy

import (
	"context"
	"errors"
	"strings"
	"voderpltvv/erp_client/domain/subject/business/employee/model"
	"voderpltvv/erp_client/domain/subject/business/employee/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	managementService "voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// EmployeeProxy Employee代理实现
type EmployeeProxy struct {
	*BaseSubjectProxy
	legacyService *managementService.EmployeeService
}

// NewEmployeeProxy 创建Employee代理
func NewEmployeeProxy(employeeService *managementService.EmployeeService) repository.IEmployeeRepository {
	if employeeService == nil {
		employeeService = managementService.NewEmployeeService()
	}
	return &EmployeeProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("employee"),
		legacyService:    employeeService,
	}
}

// convertToGinContext 转换context为gin.Context
func (p *EmployeeProxy) convertToGinContext(ctx context.Context) (*gin.Context, error) {
	if ginCtx, ok := ctx.(*gin.Context); ok {
		return ginCtx, nil
	}
	if ginCtx, ok := ctx.Value("ginContext").(*gin.Context); ok {
		return ginCtx, nil
	}
	return nil, errors.New("无法转换为gin.Context")
}

// convertEmployeePOToModel 统一的PO到领域模型转换
func (p *EmployeeProxy) convertEmployeePOToModel(emp *po.Employee) model.Employee {
	if emp == nil {
		return nil
	}

	// 获取角色列表 - 如果需要可从PermissionRole字段解析
	roles := []string{}
	if emp.PermissionRole != nil && *emp.PermissionRole != "" {
		// 如果角色是以逗号分隔的字符串，则进行分割
		// 此处仅示例，实际可能需要根据权限角色设计调整
		if strings.Contains(*emp.PermissionRole, ",") {
			roles = strings.Split(*emp.PermissionRole, ",")
		} else {
			roles = append(roles, *emp.PermissionRole)
		}
	}

	// 处理可能为空的字段
	venueID := ""
	if emp.VenueId != nil {
		venueID = *emp.VenueId
	}

	position := ""
	if emp.Type != nil {
		position = *emp.Type
	}

	phone := ""
	if emp.Phone != nil {
		phone = *emp.Phone
	}

	// 判断是否启用
	isEnabled := false
	if emp.State != nil {
		isEnabled = *emp.State == 1
	}

	return model.NewEmployee(
		*emp.Id,
		venueID,
		*emp.Name,
		position,
		phone,
		isEnabled,
		roles,
	)
}

// convertEmployeeModelToPO 将领域模型转换为PO
func (p *EmployeeProxy) convertEmployeeModelToPO(employee model.Employee) *po.Employee {
	if employee == nil {
		return nil
	}

	id := employee.GetID()
	name := employee.GetName()
	venueID := employee.GetVenueID()
	position := employee.GetPosition()
	phone := employee.GetPhone()
	state := 0
	if employee.IsEnabled() {
		state = 1
	}

	// 角色可能需要转换为逗号分隔的字符串
	permissionRole := strings.Join(employee.GetRoles(), ",")

	return &po.Employee{
		Id:             &id,
		Name:           &name,
		VenueId:        &venueID,
		Type:           &position,
		Phone:          &phone,
		PermissionRole: &permissionRole,
		State:          &state,
	}
}

// convertEmployeeReqCondition 转换查询条件为请求DTO
func (p *EmployeeProxy) convertEmployeeReqCondition(conditions map[string]interface{}) *req.QueryEmployeeReqDto {
	reqDto := &req.QueryEmployeeReqDto{}

	if name, ok := conditions["name"].(string); ok {
		reqDto.Name = &name
	}
	if venueID, ok := conditions["venue_id"].(string); ok {
		reqDto.VenueId = &venueID
	}
	// 支持venueID字段名变体
	if venueID, ok := conditions["venueId"].(string); ok && reqDto.VenueId == nil {
		reqDto.VenueId = &venueID
	}
	if permissionRole, ok := conditions["permission_role"].(string); ok {
		reqDto.PermissionRole = &permissionRole
	}
	// 支持permissionRole字段名变体
	if permissionRole, ok := conditions["permissionRole"].(string); ok && reqDto.PermissionRole == nil {
		reqDto.PermissionRole = &permissionRole
	}
	if ids, ok := conditions["ids"].([]string); ok {
		reqDto.Ids = &ids
	}
	if phone, ok := conditions["phone"].(string); ok {
		reqDto.Phone = &phone
	}

	return reqDto
}

// Update 更新主体
func (p *EmployeeProxy) Update(ctx context.Context, employee model.Employee) error {
	if employee == nil {
		return NewInvalidArgumentError("员工对象不能为空", nil)
	}

	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return NewInternalError("上下文转换失败", err)
	}

	// 转换为PO对象
	employeePO := p.convertEmployeeModelToPO(employee)

	err = p.legacyService.UpdateEmployee(ginCtx, employeePO)
	if err != nil {
		return NewInternalError("更新员工失败", err)
	}

	return nil
}

// Delete 删除主体
func (p *EmployeeProxy) Delete(ctx context.Context, id string) error {
	if id == "" {
		return NewInvalidArgumentError("ID不能为空", nil)
	}

	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return NewInternalError("上下文转换失败", err)
	}

	err = p.legacyService.DeleteEmployee(ginCtx, id)
	if err != nil {
		return NewInternalError("删除员工失败", err)
	}

	return nil
}

// FindByID 根据ID查询主体
func (p *EmployeeProxy) FindByID(ctx context.Context, id string) (model.Employee, error) {
	if id == "" {
		return nil, NewInvalidArgumentError("ID不能为空", nil)
	}

	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return nil, NewInternalError("上下文转换失败", err)
	}

	emp, err := p.legacyService.FindEmployeeById(ginCtx, id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return nil, NewNotFoundError("员工不存在", err)
		}
		return nil, NewInternalError("查询员工失败", err)
	}

	if emp == nil {
		return nil, NewNotFoundError("员工不存在", nil)
	}

	return p.convertEmployeePOToModel(emp), nil
}

// FindByCondition 根据条件查询主体
func (p *EmployeeProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]model.Employee, error) {
	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return nil, NewInternalError("上下文转换失败", err)
	}

	reqDto := p.convertEmployeeReqCondition(condition)

	employees, err := p.legacyService.FindAllEmployee(ginCtx, reqDto)
	if err != nil {
		return nil, NewInternalError("查询员工失败", err)
	}

	if employees == nil {
		return []model.Employee{}, nil
	}

	result := make([]model.Employee, 0, len(*employees))
	for _, emp := range *employees {
		employee := p.convertEmployeePOToModel(&emp)
		if employee != nil {
			result = append(result, employee)
		}
	}

	return result, nil
}

// FindByVenueID 根据场所ID查询员工
func (p *EmployeeProxy) FindByVenueID(ctx context.Context, venueID string) ([]model.Employee, error) {
	if venueID == "" {
		return nil, NewInvalidArgumentError("场所ID不能为空", nil)
	}

	return p.FindByCondition(ctx, map[string]interface{}{
		"venue_id": venueID,
	})
}

// FindByRole 根据角色查询员工
func (p *EmployeeProxy) FindByRole(ctx context.Context, role string) ([]model.Employee, error) {
	if role == "" {
		return nil, NewInvalidArgumentError("角色不能为空", nil)
	}

	return p.FindByCondition(ctx, map[string]interface{}{
		"permission_role": role,
	})
}

// List 获取员工列表
func (p *EmployeeProxy) List(ctx context.Context, conditions map[string]interface{}) ([]model.Employee, error) {
	return p.FindByCondition(ctx, conditions)
}

// GetByID 根据ID获取员工
func (p *EmployeeProxy) GetByID(ctx context.Context, id string) (model.Employee, error) {
	return p.FindByID(ctx, id)
}

// GetByIDs 根据ID列表获取员工列表
func (p *EmployeeProxy) GetByIDs(ctx context.Context, ids []string) ([]model.Employee, error) {
	if len(ids) == 0 {
		return []model.Employee{}, nil
	}

	return p.FindByCondition(ctx, map[string]interface{}{
		"ids": ids,
	})
}

// FindEmployeeByID 根据ID查询员工
func (p *EmployeeProxy) FindEmployeeByID(ctx context.Context, id string) (po.Employee, error) {
	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return po.Employee{}, NewInternalError("上下文转换失败", err)
	}
	employee, err := p.legacyService.FindEmployeeByID(ginCtx, id)
	if err != nil {
		return po.Employee{}, NewInternalError("查询员工失败", err)
	}
	if employee == nil {
		return po.Employee{}, NewNotFoundError("员工不存在", nil)
	}
	return *employee, nil
}

// FindEmployeesByIds 根据ID查询员工
func (p *EmployeeProxy) FindsByIds(ctx context.Context, ids []string) ([]po.Employee, error) {
	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return nil, NewInternalError("上下文转换失败", err)
	}
	employees, err := p.legacyService.FindsByIds(ginCtx, ids)
	if err != nil {
		return nil, NewInternalError("查询员工失败", err)
	}
	return *employees, nil
}

// ConvertToEmployeeVO 转换为员工VO
func (p *EmployeeProxy) ConvertToEmployeeVO(ctx context.Context, employee po.Employee) vo.EmployeeVO {
	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return vo.EmployeeVO{}
	}
	return p.legacyService.ConvertToEmployeeVO(ginCtx, employee)
}

// ConvertToEmployee 转换为员工
func (p *EmployeeProxy) ConvertToEmployee(ctx context.Context, employeeVO vo.EmployeeVO) po.Employee {
	ginCtx, err := p.convertToGinContext(ctx)
	if err != nil {
		return po.Employee{}
	}
	return p.legacyService.ConvertToEmployee(ginCtx, employeeVO)
}
