package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// RoomFaultProxy 房间故障代理
type RoomFaultProxy struct {
	*BaseSubjectProxy
	legacyService *impl.RoomFaultService
}

// NewRoomFaultProxy 创建房间故障代理
func NewRoomFaultProxy() *RoomFaultProxy {
	return &RoomFaultProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("room_fault"),
		legacyService:    &impl.RoomFaultService{},
	}
}

// Create 创建房间故障
func (p *RoomFaultProxy) Create(ctx context.Context, roomFault *po.RoomFault) error {
	// TODO: 实现远程调用
	return nil
}

// Update 更新房间故障
func (p *RoomFaultProxy) Update(ctx context.Context, roomFault *po.RoomFault) error {
	// TODO: 实现远程调用
	return nil
}

// Delete 删除房间故障
func (p *RoomFaultProxy) Delete(ctx context.Context, id string) error {
	return nil
}

func (p *RoomFaultProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.RoomFault, error) {
	return nil, nil
}

// FindByID 根据ID查询房间故障
func (p *RoomFaultProxy) FindByID(ctx context.Context, id string) (*po.RoomFault, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindRoomFaultById(ginCtx, id)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindByVenueID 根据场所ID查询房间故障
func (p *RoomFaultProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.RoomFault, error) {
	// TODO: 实现远程调用
	return []po.RoomFault{}, nil
}

// FindAll 查询所有房间故障
func (p *RoomFaultProxy) FindAll(ctx context.Context, venueId, roomId string) (*[]po.RoomFault, error) {
	// TODO: 实现远程调用
	return &[]po.RoomFault{}, nil
}

// ConvertToRoomFaultVO 转换为房间故障VO
func (p *RoomFaultProxy) ConvertToRoomFaultVO(ctx context.Context, roomFault po.RoomFault) vo.RoomFaultVO {
	// TODO: 实现转换
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.RoomFaultVO{}
	}
	return p.legacyService.ConvertToRoomFaultVO(ginCtx, roomFault)
}

// ConvertToRoomFault 转换为房间故障PO
func (p *RoomFaultProxy) ConvertToRoomFault(ctx context.Context, roomFaultVO vo.RoomFaultVO) po.RoomFault {
	// TODO: 实现转换
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.RoomFault{}
	}
	return p.legacyService.ConvertToRoomFault(ginCtx, roomFaultVO)
}

func (p *RoomFaultProxy) FindRoomFaultsByRoomId(ctx context.Context, venueId, roomId string) ([]po.RoomFault, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return []po.RoomFault{}, err
	}
	return p.legacyService.FindRoomFaultsByRoomId(ginCtx, venueId, roomId)
}