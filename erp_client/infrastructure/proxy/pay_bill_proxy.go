package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// PayBillRepositoryProxy 支付账单仓储代理
type PayBillRepositoryProxy struct {
	*BaseSubjectProxy
	payBillService *impl.PayBillService
}

// NewPayBillRepository 创建支付账单仓储代理实例
func NewPayBillRepository() repository.PayBillRepository {
	return &PayBillRepositoryProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("payBill"),
		payBillService:    &impl.PayBillService{},
	}
}

// FindAllPayBill 查询所有支付账单
func (r *PayBillRepositoryProxy) FindAllPayBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryPayBillReqDto{
		VenueId:    &venueId,
		SessionId:  &sessionId,
		StatusList: &statusList,
	}
	return r.payBillService.FindAllPayBill(ginCtx, reqDto)
}

// ConvertToPayBillVO 转换为支付账单VO
func (r *PayBillRepositoryProxy) ConvertToPayBillVO(ctx context.Context, payBill po.PayBill) vo.PayBillVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.PayBillVO{}
	}
	val := r.payBillService.ConvertToPayBillVO(ginCtx, payBill)
	return val
}

// ConvertToPayBill 转换为支付账单PO
func (r *PayBillRepositoryProxy) ConvertToPayBill(ctx context.Context, payBillVO vo.PayBillVO) po.PayBill {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.PayBill{}
	}
	val := r.payBillService.ConvertToPayBill(ginCtx, payBillVO)
	return val
}

// FindAllBySessionId 查询所有支付账单
func (r *PayBillRepositoryProxy) FindAllBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payBillService.FindAllBySessionId(ginCtx, venueId, sessionId)
}

// FindsBySessionId 查询所有支付账单
func (r *PayBillRepositoryProxy) FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payBillService.FindsBySessionId(ginCtx, venueId, sessionId)
}

// FindsByTimeRange 查询所有支付账单
func (r *PayBillRepositoryProxy) FindsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.PayBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payBillService.FindsByTimeRange(ginCtx, venueId, startTime, endTime)
}

// FindByBillId 查询支付账单
func (r *PayBillRepositoryProxy) FindByBillId(ctx context.Context, billId, venueId, sessionId string) (po.PayBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payBillService.FindByBillId(ginCtx, billId, venueId, sessionId)
}

// FindsBySessionIds 查询所有支付账单
func (r *PayBillRepositoryProxy) FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.PayBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payBillService.FindsBySessionIds(ginCtx, venueId, sessionIds)
}

// FindModelBasePayBillVOsByTimeRange 查询支付账单VOs
func (r *PayBillRepositoryProxy) FindModelBasePayBillVOsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) (vo.ModelBasePayBillVO, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payBillService.FindModelBasePayBillVOsByTimeRange(ginCtx, venueId, startTime, endTime)
}