package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/subject/base/model"

	"github.com/gin-gonic/gin"
)

// BaseSubjectProxy Subject代理基础实现
type BaseSubjectProxy struct {
	subjectType string
}

// NewBaseSubjectProxy 创建基础Subject代理
func NewBaseSubjectProxy(subjectType string) *BaseSubjectProxy {
	return &BaseSubjectProxy{
		subjectType: subjectType,
	}
}

// ValidateSubjectType 验证主体类型
func (p *BaseSubjectProxy) ValidateSubjectType(subject model.Subject) bool {
	return subject.GetType() == p.subjectType
}

// ConvertContext 转换上下文
func (p *BaseSubjectProxy) ConvertContext(ctx context.Context) (*gin.Context, error) {
	ginCtx, ok := ctx.(*gin.Context)
	if !ok {
		return nil, ErrInvalidContext
	}
	return ginCtx, nil
}
