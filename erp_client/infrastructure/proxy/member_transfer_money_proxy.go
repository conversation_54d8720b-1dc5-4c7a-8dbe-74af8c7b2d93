package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// MemberTransferMoneyRepositoryProxy 会员转账记录仓储代理
type MemberTransferMoneyRepositoryProxy struct {
	*BaseSubjectProxy
	memberTransferMoneyService *impl.MemberTransferMoneyService
}

// NewMemberTransferMoneyRepository 创建会员转账记录仓储代理实例
func NewMemberTransferMoneyRepository() repository.MemberTransferMoneyRepository {
	return &MemberTransferMoneyRepositoryProxy{
		BaseSubjectProxy:           NewBaseSubjectProxy("memberTransferMoney"),
		memberTransferMoneyService: &impl.MemberTransferMoneyService{},
	}
}

// CreateMemberTransferMoney 创建会员转账记录
func (r *MemberTransferMoneyRepositoryProxy) CreateMemberTransferMoney(ctx context.Context, memberTransferMoney *po.MemberTransferMoney) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberTransferMoneyService.CreateMemberTransferMoney(ginCtx, memberTransferMoney)
}

// ConvertToMemberTransferMoneyVO 转换为会员转账记录VO
func (r *MemberTransferMoneyRepositoryProxy) ConvertToMemberTransferMoneyVO(ctx context.Context, memberTransferMoney po.MemberTransferMoney) vo.MemberTransferMoneyVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberTransferMoneyVO{}
	}
	return r.memberTransferMoneyService.ConvertToMemberTransferMoneyVO(ginCtx, memberTransferMoney)
}

// ConvertToMemberTransferMoney 转换为会员转账记录PO
func (r *MemberTransferMoneyRepositoryProxy) ConvertToMemberTransferMoney(ctx context.Context, memberTransferMoneyVO vo.MemberTransferMoneyVO) po.MemberTransferMoney {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberTransferMoney{}
	}
	return r.memberTransferMoneyService.ConvertToMemberTransferMoney(ginCtx, memberTransferMoneyVO)
}
