package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/call/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// CallTypesRepositoryProxy 呼叫类型仓储代理
type CallTypesRepositoryProxy struct {
	*BaseSubjectProxy
	callTypesService *impl.CallTypesService
}

// NewCallTypesRepository 创建呼叫类型仓储代理实例
func NewCallTypesRepository() repository.CallTypesRepository {
	return &CallTypesRepositoryProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("callTypes"),
		callTypesService: &impl.CallTypesService{},
	}
}

// CreateCallTypes 创建呼叫类型
func (r *CallTypesRepositoryProxy) CreateCallTypes(ctx context.Context, callTypes *po.CallTypes) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.callTypesService.CreateCallTypes(ginCtx, callTypes)
}

// ConvertToCallTypesVO 转换为呼叫类型VO
func (r *CallTypesRepositoryProxy) ConvertToCallTypesVO(ctx context.Context, callTypes po.CallTypes) vo.CallTypesVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.CallTypesVO{}
	}
	return r.callTypesService.ConvertToCallTypesVO(ginCtx, callTypes)
}

// ConvertToCallTypes 转换为呼叫类型PO
func (r *CallTypesRepositoryProxy) ConvertToCallTypes(ctx context.Context, callTypesVO vo.CallTypesVO) po.CallTypes {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.CallTypes{}
	}
	return r.callTypesService.ConvertToCallTypesPO(ginCtx, callTypesVO)
}
