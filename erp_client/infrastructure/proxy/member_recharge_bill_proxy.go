package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	newModel "voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// MemberRechageBillRepositoryProxy 会员充值账单仓储代理
type MemberRechageBillRepositoryProxy struct {
	*BaseSubjectProxy
	memberRechageBillService *impl.MemberRechargeBillService
}

// NewMemberRechageBillRepository 创建会员充值账单仓储代理实例
func NewMemberRechageBillRepository() repository.MemberRechargeBillRepository {
	return &MemberRechageBillRepositoryProxy{
		BaseSubjectProxy:         NewBaseSubjectProxy("memberRechageBill"),
		memberRechageBillService: &impl.MemberRechargeBillService{},
	}
}

// FindAllMemberRechageBill 查询所有会员充值账单
func (r *MemberRechageBillRepositoryProxy) FindAllMemberRechargeBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberRechargeBill, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryMemberRechargeBillReqDto{
		VenueId: &venueId,
	}
	return r.memberRechageBillService.FindAllMemberRechargeBill(ginCtx, reqDto)
}

// ConvertToMemberRechageBillVO 转换为会员充值账单VO
func (r *MemberRechageBillRepositoryProxy) ConvertToMemberRechargeBillVO(ctx context.Context, memberRechageBill po.MemberRechargeBill) vo.MemberRechargeBillVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberRechargeBillVO{}
	}
	val := r.memberRechageBillService.ConvertToMemberRechargeBillVO(ginCtx, memberRechageBill)
	return val
}

// ConvertToMemberRechageBill 转换为会员充值账单PO
func (r *MemberRechageBillRepositoryProxy) ConvertToMemberRechargeBill(ctx context.Context, memberRechageBillVO vo.MemberRechargeBillVO) po.MemberRechargeBill {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberRechargeBill{}
	}
	val := r.memberRechageBillService.ConvertToMemberRechargeBill(ginCtx, memberRechageBillVO)
	return val
}

// V3MemberRechage 会员充值
func (r *MemberRechageBillRepositoryProxy) V3MemberRechage(ctx context.Context, reqDto req.V3QueryMemberRechargeReqDto) (req.MemberRechargeContext, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return req.MemberRechargeContext{}, err
	}
	val, err := r.memberRechageBillService.V3MemberRechage(ginCtx, reqDto)
	if err != nil {
		return req.MemberRechargeContext{}, err
	}
	return val, nil
}

// V3MemberPayCallback 会员充值回调
func (r *MemberRechageBillRepositoryProxy) V3MemberPayCallback(ctx context.Context, reqDto newModel.LeshuaPayCallbackModel) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberRechageBillService.V3MemberPayCallback(ginCtx, reqDto)
}

// V3MemberRefundCallback 会员退款回调
func (r *MemberRechageBillRepositoryProxy) V3MemberRefundCallback(ctx context.Context, reqDto newModel.LeshuaRefundCallbackModel) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberRechageBillService.V3MemberRefundCallback(ginCtx, reqDto)
}

// FindByBillId 根据ID查询会员充值账单
func (r *MemberRechageBillRepositoryProxy) FindByBillId(ctx context.Context, venueId, billId string) (po.MemberRechargeBill, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberRechargeBill{}, err
	}
	val, err := r.memberRechageBillService.FindByBillId(ginCtx, venueId, billId)
	if err != nil {
		return po.MemberRechargeBill{}, err
	}
	return val, nil
}

// V3RPCMemberCardQuery 会员信息查询
func (r *MemberRechageBillRepositoryProxy) V3RPCMemberCardQuery(ctx context.Context, reqDto req.V3QueryMemberCardQueryReqDto) (vo.MemberCardVO, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberCardVO{}, err
	}
	val, err := r.memberRechageBillService.V3RPCMemberCardQuery(ginCtx, reqDto)
	if err != nil {
		return vo.MemberCardVO{}, err
	}
	return val, nil
}

// V3RPCMemberCardVaildBalance 会员卡余额验证
func (r *MemberRechageBillRepositoryProxy) V3RPCMemberCardVaildBalance(ctx context.Context, reqDto req.V3QueryMemberCardQueryBalanceReqDto) (vo.MemberCardVO, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberCardVO{}, err
	}
	val, err := r.memberRechageBillService.V3RPCMemberCardVaildBalance(ginCtx, reqDto)
	if err != nil {
		return vo.MemberCardVO{}, err
	}
	return val, nil
}

// V3RPCMemberCardPay 会员卡消费
func (r *MemberRechageBillRepositoryProxy) V3RPCMemberCardPay(ctx context.Context, reqDto req.V3RPCPayMoneyReqDto) (req.V3RPCPayMoneyRespDto, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return req.V3RPCPayMoneyRespDto{}, err
	}
	val, err := r.memberRechageBillService.V3RPCMemberCardPay(ginCtx, reqDto)
	if err != nil {
		return req.V3RPCPayMoneyRespDto{}, err
	}
	return val, nil
}