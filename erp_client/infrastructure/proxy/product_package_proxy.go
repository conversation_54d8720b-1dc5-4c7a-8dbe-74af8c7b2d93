package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
)

// ProductPackageProxy 产品套餐代理
type ProductPackageProxy struct {
	*BaseSubjectProxy
	legacyService          *impl.ProductPackageService
	productPackageTransfer *transfer.ProductPackageTransfer
}

// NewProductPackageProxy 创建产品套餐代理
func NewProductPackageProxy() *ProductPackageProxy {
	return &ProductPackageProxy{
		BaseSubjectProxy:       NewBaseSubjectProxy("product_package"),
		legacyService:          &impl.ProductPackageService{},
		productPackageTransfer: &transfer.ProductPackageTransfer{},
	}
}

// Create 创建产品套餐
func (p *ProductPackageProxy) Create(ctx context.Context, productPackage *po.ProductPackage) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateProductPackage(ginCtx, productPackage)
}

// Update 更新产品套餐
func (p *ProductPackageProxy) Update(ctx context.Context, productPackage *po.ProductPackage) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductPackage(ginCtx, productPackage)
}

// UpdatePartial 部分更新产品套餐
func (p *ProductPackageProxy) UpdatePartial(ctx context.Context, productPackage *po.ProductPackage) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductPackagePartial(ginCtx, productPackage)
}

// Delete 删除产品套餐
func (p *ProductPackageProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteProductPackage(ginCtx, id)
}

// FindByID 根据ID查询产品套餐
func (p *ProductPackageProxy) FindByID(ctx context.Context, id string) (*po.ProductPackage, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindProductPackageById(ginCtx, id)
}

// FindAll 查询所有产品套餐
func (p *ProductPackageProxy) FindAll(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindAllProductPackage(ginCtx, reqDto)
}

// FindAllWithPagination 分页查询所有产品套餐
func (p *ProductPackageProxy) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, 0, err
	}
	return p.legacyService.FindAllProductPackageWithPagination(ginCtx, reqDto)
}

// FindProductPackagesByIds 根据ID列表查询产品套餐
func (p *ProductPackageProxy) FindProductPackagesByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryProductPackageReqDto{
		VenueId: &venueId,
		Ids:     &productPackageIds,
	}
	result, err := p.legacyService.FindAllProductPackage(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	if result == nil {
		return []po.ProductPackage{}, nil
	}
	return *result, nil
}

// ConvertToProductPackageVO 转换为产品套餐VO
func (p *ProductPackageProxy) ConvertToProductPackageVO(ctx context.Context, productPackage po.ProductPackage) vo.ProductPackageVO {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.ProductPackageVO{}
	}
	return p.productPackageTransfer.PoToVo(productPackage)
}

// ConvertToProductPackage 转换为产品套餐PO
func (p *ProductPackageProxy) ConvertToProductPackage(ctx context.Context, productPackageVO vo.ProductPackageVO) po.ProductPackage {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return po.ProductPackage{}
	}
	return p.productPackageTransfer.VoToPo(productPackageVO)
}

// FindsByIds 根据ID列表查询产品套餐
func (p *ProductPackageProxy) FindsByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error) {
	return p.FindProductPackagesByIds(ctx, venueId, productPackageIds)
}

// FindByCondition 根据条件查询产品套餐
func (p *ProductPackageProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.ProductPackage, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryProductPackageReqDto{}

	// 转换查询条件
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	if ids, ok := condition["ids"].([]string); ok {
		reqDto.Ids = &ids
	}
	if category, ok := condition["category"].(string); ok {
		reqDto.Category = &category
	}
	if series, ok := condition["series"].(string); ok {
		reqDto.Series = &series
	}
	if barcode, ok := condition["barcode"].(string); ok {
		reqDto.Barcode = &barcode
	}
	if isOnShelf, ok := condition["isOnShelf"].(bool); ok {
		reqDto.IsOnShelf = &isOnShelf
	}

	result, err := p.legacyService.FindAllProductPackage(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}
