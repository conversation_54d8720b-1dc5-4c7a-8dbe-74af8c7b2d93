package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
)

// VenuePaySettingProxy 门店支付设置代理
type VenuePaySettingProxy struct {
	*BaseSubjectProxy
	legacyService           *impl.VenuePaySettingService
	venuePaySettingTransfer *transfer.VenuePaySettingTransfer
}

// NewVenuePaySettingProxy 创建门店支付设置代理
func NewVenuePaySettingProxy() *VenuePaySettingProxy {
	return &VenuePaySettingProxy{
		BaseSubjectProxy:        NewBaseSubjectProxy("venue_pay_setting"),
		legacyService:           &impl.VenuePaySettingService{},
		venuePaySettingTransfer: &transfer.VenuePaySettingTransfer{},
	}
}

// Create 创建门店支付设置
func (p *VenuePaySettingProxy) Create(ctx context.Context, venuePaySetting *po.VenuePaySetting) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateVenuePaySetting(ginCtx, venuePaySetting)
}

// Update 更新门店支付设置
func (p *VenuePaySettingProxy) Update(ctx context.Context, venuePaySetting *po.VenuePaySetting) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateVenuePaySetting(ginCtx, venuePaySetting)
}

// UpdatePartial 部分更新门店支付设置
func (p *VenuePaySettingProxy) UpdatePartial(ctx context.Context, venuePaySetting *po.VenuePaySetting) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateVenuePaySettingPartial(ginCtx, venuePaySetting)
}

// Delete 删除门店支付设置
func (p *VenuePaySettingProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteVenuePaySetting(ginCtx, id)
}

// FindByID 根据ID查询门店支付设置
func (p *VenuePaySettingProxy) FindByID(ctx context.Context, id string) (*po.VenuePaySetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindVenuePaySettingById(ginCtx, id)
}

// FindAll 查询所有门店支付设置
func (p *VenuePaySettingProxy) FindAll(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindAllVenuePaySetting(ginCtx, reqDto)
}

// FindAllWithPagination 分页查询所有门店支付设置
func (p *VenuePaySettingProxy) FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, 0, err
	}
	return p.legacyService.FindAllVenuePaySettingWithPagination(ginCtx, reqDto)
}

// FindVenuePaySettingsByIds 根据ID列表查询门店支付设置
func (p *VenuePaySettingProxy) FindVenuePaySettingsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryVenuePaySettingReqDto{
		VenueId: &venueId,
		// Note: VenuePaySetting doesn't have Ids field in QueryVenuePaySettingReqDto,
		// so we'll need to query by individual IDs or modify the DTO
	}
	result, err := p.legacyService.FindAllVenuePaySetting(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	if result == nil {
		return []po.VenuePaySetting{}, nil
	}

	// Filter by IDs if provided
	if len(venuePaySettingIds) > 0 {
		filtered := make([]po.VenuePaySetting, 0)
		for _, setting := range *result {
			for _, id := range venuePaySettingIds {
				if setting.Id != nil && *setting.Id == id {
					filtered = append(filtered, setting)
					break
				}
			}
		}
		return filtered, nil
	}

	return *result, nil
}

// ConvertToVenuePaySettingVO 转换为门店支付设置VO
func (p *VenuePaySettingProxy) ConvertToVenuePaySettingVO(ctx context.Context, venuePaySetting po.VenuePaySetting) vo.VenuePaySettingVO {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.VenuePaySettingVO{}
	}
	return p.venuePaySettingTransfer.PoToVo(venuePaySetting)
}

// ConvertToVenuePaySetting 转换为门店支付设置PO
func (p *VenuePaySettingProxy) ConvertToVenuePaySetting(ctx context.Context, venuePaySettingVO vo.VenuePaySettingVO) po.VenuePaySetting {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return po.VenuePaySetting{}
	}
	return p.venuePaySettingTransfer.VoToPo(venuePaySettingVO)
}

// FindsByIds 根据ID列表查询门店支付设置
func (p *VenuePaySettingProxy) FindsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error) {
	return p.FindVenuePaySettingsByIds(ctx, venueId, venuePaySettingIds)
}

// FindByCondition 根据条件查询门店支付设置
func (p *VenuePaySettingProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.VenuePaySetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryVenuePaySettingReqDto{}

	// 转换查询条件
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	if ktvid, ok := condition["ktvid"].(string); ok {
		reqDto.Ktvid = &ktvid
	}
	if subMerchantId, ok := condition["subMerchantId"].(string); ok {
		reqDto.SubMerchantId = &subMerchantId
	}
	if remark, ok := condition["remark"].(string); ok {
		reqDto.Remark = &remark
	}

	result, err := p.legacyService.FindAllVenuePaySetting(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindVenuePaySettingByVenueId 根据门店ID查询支付设置
func (p *VenuePaySettingProxy) FindVenuePaySettingByVenueId(ctx context.Context, venueId string) (po.VenuePaySetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.VenuePaySetting{}, err
	}
	return p.legacyService.FindVenuePaySettingByVenueId(ginCtx, venueId)
}