package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// VenueProxy 门店代理实现
type VenueProxy struct {
	*BaseSubjectProxy
	legacyService *impl.VenueService
}

// NewVenueProxy 创建门店代理实例
func NewVenueProxy() *VenueProxy {
	return &VenueProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("venue"),
		legacyService:    &impl.VenueService{},
	}
}

// Create 创建场所
func (p *VenueProxy) Create(ctx context.Context, venue *po.Venue) error {
	// TODO: 实现远程调用
	return nil
}

// Update 更新场所
func (p *VenueProxy) Update(ctx context.Context, venue *po.Venue) error {
	// TODO: 实现远程调用
	return nil
}

// Delete 删除场所
func (p *VenueProxy) Delete(ctx context.Context, id string) error {
	// TODO: 实现远程调用
	return nil
}

// FindByCondition 根据条件查询场所
func (p *VenueProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Venue, error) {
	// TODO: 实现远程调用
	return nil, nil
}

// FindByID 根据ID查询场所
func (p *VenueProxy) FindByID(ctx context.Context, id string) (*po.Venue, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindVenueById(ginCtx, id)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindByVenueID 根据场所ID查询场所
func (p *VenueProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.Venue, error) {
	// TODO: 实现远程调用
	return []po.Venue{}, nil
}

// FindAll 查询所有场所
func (p *VenueProxy) FindAll(ctx context.Context, venueId, sessionId string) (*[]po.Venue, error) {
	// TODO: 实现远程调用
	return &[]po.Venue{}, nil
}

// ConvertToVenueVO 转换为场所VO
func (p *VenueProxy) ConvertToVenueVO(ctx context.Context, venue po.Venue) vo.VenueVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.VenueVO{}
	}
	val := p.legacyService.ConvertToVenueVO(ginCtx, venue)
	return val
}

// ConvertToVenuePO 转换为场所PO
func (p *VenueProxy) ConvertToVenue(ctx context.Context, venueVO vo.VenueVO) po.Venue {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Venue{}
	}
	val := p.legacyService.ConvertToVenue(ginCtx, venueVO)
	return val
}
