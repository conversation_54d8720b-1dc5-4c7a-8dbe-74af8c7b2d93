package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/holiday/model"
	"voderpltvv/erp_client/domain/valueobject/business/holiday/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// HolidayProxy Holiday代理实现
type HolidayProxy struct {
	*BaseSubjectProxy
	legacyService *impl.HolidayService
}

// NewHolidayProxy 创建Holiday代理
func NewHolidayProxy() repository.Repository {
	return &HolidayProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("holiday"),
		legacyService:    &impl.HolidayService{},
	}
}

// Create 创建节假日
func (p *HolidayProxy) Create(ctx context.Context, holiday *po.Holiday) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateHoliday(ginCtx, holiday)
}

// Update 更新节假日
func (p *HolidayProxy) Update(ctx context.Context, holiday *po.Holiday) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateHoliday(ginCtx, holiday)
}

// Delete 删除节假日
func (p *HolidayProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteHoliday(ginCtx, id)
}

// FindByCondition 根据条件查询节假日
func (p *HolidayProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Holiday, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryHolidayReqDto{}
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if date, ok := condition["date"].(string); ok {
		reqDto.Date = &date
	}

	holidays, err := p.legacyService.FindAllHoliday(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	if holidays == nil {
		emptyResult := make([]po.Holiday, 0)
		return &emptyResult, nil
	}

	result := make([]po.Holiday, len(*holidays))
	for i := range *holidays {
		result[i] = (*holidays)[i]
	}

	return &result, nil
}

// FindByID 根据ID查询节假日
func (p *HolidayProxy) FindByID(ctx context.Context, id string) (*po.Holiday, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	holiday, err := p.legacyService.FindHolidayById(ginCtx, id)
	if err != nil {
		return nil, err
	}

	if holiday == nil {
		return nil, nil
	}

	return holiday, nil
}

// FindByVenueID 根据场馆ID查询节假日列表
func (p *HolidayProxy) FindByVenueID(ctx context.Context, venueID string) ([]model.Holiday, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryHolidayReqDto{
		VenueId: &venueID,
	}
	holidays, err := p.legacyService.FindAllHoliday(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	result := make([]model.Holiday, len(*holidays))
	for i := range *holidays {
		result[i] = model.NewHolidayFromPO(&(*holidays)[i])
	}
	return result, nil
}

// FindByDate 根据日期查询节假日列表
func (p *HolidayProxy) FindByDate(ctx context.Context, date string) ([]model.Holiday, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryHolidayReqDto{
		Date: &date,
	}
	holidays, err := p.legacyService.FindAllHoliday(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	result := make([]model.Holiday, len(*holidays))
	for i := range *holidays {
		result[i] = model.NewHolidayFromPO(&(*holidays)[i])
	}
	return result, nil
}

// FindByVenueIDAndDate 根据场所ID和日期查询节假日列表
func (p *HolidayProxy) FindByVenueIDAndDate(ctx context.Context, venueID string, date string) ([]model.Holiday, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryHolidayReqDto{
		VenueId: &venueID,
		Date:    &date,
	}
	holidays, err := p.legacyService.FindAllHoliday(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	result := make([]model.Holiday, len(*holidays))
	for i, holiday := range *holidays {
		result[i] = model.NewHolidayFromPO(&holiday)
	}
	return result, nil
}
