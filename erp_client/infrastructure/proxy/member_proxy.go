package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// MemberProxy 会员仓储代理
type MemberProxy struct {
	*BaseSubjectProxy
	memberService *impl.MemberService
}

// NewMemberRechageBillRepository 创建会员充值账单仓储代理实例
func NewMemberProxy() repository.MemberRepository {
	return &MemberProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("member"),
		memberService:    &impl.MemberService{},
	}
}

// FindAllMember 查询所有会员
func (r *MemberProxy) FindAllMember(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.Member, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return nil, nil
}
