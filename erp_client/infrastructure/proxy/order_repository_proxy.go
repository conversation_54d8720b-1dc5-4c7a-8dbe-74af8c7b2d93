package proxy

import (
	"context"
	"fmt"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	managentVo "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// OrderRepositoryProxy 订单仓储代理
type OrderRepositoryProxy struct {
	orderService *impl.OrderService
}

// NewOrderRepository 创建订单仓储代理实例
func NewOrderRepository() repository.OrderRepository {
	return &OrderRepositoryProxy{
		orderService: &impl.OrderService{},
	}
}

// FindAllOrder 查询所有订单
func (r *OrderRepositoryProxy) FindAllOrder(ctx context.Context, venueId, sessionId string) (*[]po.Order, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryOrderReqDto{
		VenueId:   &venueId,
		SessionId: &sessionId,
	}
	return r.orderService.FindAllOrder(ginCtx, reqDto)
}

// CreateOrdersWithTransaction 在事务中创建订单相关数据
func (p *OrderRepositoryProxy) CreateOrdersWithTransaction(ctx context.Context, session *po.Session,
	orders *[]po.Order, orderProducts *[]po.OrderProduct,
	orderPricePlan *po.OrderPricePlan, orderRoomPlans *[]po.OrderRoomPlan,
	room *po.Room) error {

	ginCtx := &gin.Context{} // 创建一个新的gin上下文

	// 调用已有的OrderService.OrderOpen实现
	success := p.orderService.OrderOpen(ginCtx, session, orders, orderProducts, orderPricePlan, orderRoomPlans, room)
	if !success {
		return fmt.Errorf("failed to create orders in transaction")
	}

	return nil
}

// GetOrderByOrderNO 查询订单
func (r *OrderRepositoryProxy) GetOrderByOrderNO(ctx context.Context, venueId, orderNO string) (po.Order, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.GetOrderByOrderNO(ginCtx, venueId, orderNO)
}

// ConvertToOrderVO 转换为订单VO
func (r *OrderRepositoryProxy) ConvertToOrderVO(ctx context.Context, order po.Order) managentVo.OrderVO {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.ConvertToOrderVO(ginCtx, order)
}

// ConvertToOrderPO 转换为订单PO
func (r *OrderRepositoryProxy) ConvertToOrderPO(ctx context.Context, orderVO managentVo.OrderVO) po.Order {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.ConvertToOrderPO(ginCtx, orderVO)
}

// 保存 开台-后付
func (r *OrderRepositoryProxy) SaveOrderOpenInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderOpenInfo(ginCtx, orders, orderProducts, orderRoomPlans, session, room)
}

// 保存 开台-立结
func (r *OrderRepositoryProxy) SaveOrderOpenPayInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room, payBill po.PayBill, orderAndPays []po.OrderAndPay, payRecords []po.PayRecord) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderOpenPayInfo(ginCtx, orders, orderProducts, orderRoomPlans, session, room, payBill, orderAndPays, payRecords)
}

// 保存 续台-后付
func (r *OrderRepositoryProxy) SaveOrderOpenContinueInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderOpenContinueInfo(ginCtx, orders, orderProducts, orderRoomPlans, session, room)
}

// 保存 续台-立结
func (r *OrderRepositoryProxy) SaveOrderOpenContinuePayInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room, payBill po.PayBill, orderAndPays []po.OrderAndPay, payRecords []po.PayRecord) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderOpenContinuePayInfo(ginCtx, orders, orderProducts, orderRoomPlans, session, room, payBill, orderAndPays, payRecords)
}

// 保存 点单-后付
func (r *OrderRepositoryProxy) SaveOrderAdditionalInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toAddOrder po.Order, toUpdateOrderProducts []po.OrderProduct) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderAdditionalInfo(ginCtx, toUpdateRooms, toUpdateSession, toAddOrder, toUpdateOrderProducts)
}

// 保存 点单-立结
func (r *OrderRepositoryProxy) SaveOrderAdditionalPayInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toAddOrder po.Order, toUpdateOrderProducts []po.OrderProduct, toAddOrderAndPays []po.OrderAndPay, toAddPayRecords []po.PayRecord, toAddPayBill po.PayBill) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderAdditionalPayInfo(ginCtx, toUpdateRooms, toUpdateSession, toAddOrder, toUpdateOrderProducts, toAddOrderAndPays, toAddPayRecords, toAddPayBill)
}

// 保存 转台-后付
func (r *OrderRepositoryProxy) SaveTransferRoomInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toUpdateOrders []po.Order, toAddOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan) (po.Session, []po.Room, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveTransferRoomInfo(ginCtx, toUpdateRooms, toUpdateSession, toUpdateOrders, toAddOrders, newOrderProducts, newOrderRoomPlans)
}

// 保存 转台-立结
func (r *OrderRepositoryProxy) SaveTransferRoomPayInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toUpdateOrders []po.Order, toAddOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan, toAddPayBill *po.PayBill, toAddPayRecords []po.PayRecord, toAddOrderAndPays []po.OrderAndPay) (po.Session, []po.Room, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveTransferRoomPayInfo(ginCtx, toUpdateRooms, toUpdateSession, toUpdateOrders, toAddOrders, newOrderProducts, newOrderRoomPlans, toAddPayBill, toAddPayRecords, toAddOrderAndPays)
}

// 保存 赠送时长
func (r *OrderRepositoryProxy) SaveOrderInfoGiftTime(ctx context.Context, toAddOrder []po.Order, toAddOrderRoomPlans []po.OrderRoomPlan, toUpdateSession po.Session, toAddPayBill []po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddEmployeeGiftRecords []po.EmployeeGiftRecord) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderInfoGiftTime(ginCtx, toAddOrder, toAddOrderRoomPlans, toUpdateSession, toAddPayBill, toAddOrderAndPays, toAddEmployeeGiftRecords)
}

// 保存 赠送商品
func (r *OrderRepositoryProxy) SaveOrderInfoGiftProduct(ctx context.Context, toAddOrder []po.Order, toAddOrderProducts []po.OrderProduct, toAddPayBill []po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddEmployeeGiftRecords []po.EmployeeGiftRecord) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderInfoGiftProduct(ginCtx, toAddOrder, toAddOrderProducts, toAddPayBill, toAddOrderAndPays, toAddEmployeeGiftRecords)
}

// 保存 结束时长消费
func (r *OrderRepositoryProxy) SaveInfoEndTimeConsume(ctx context.Context, toUpdateOrderRoomPlans []po.OrderRoomPlan, toAddOrderRoomPlans []po.OrderRoomPlan, toUpdateSessions []po.Session, toUpdateRooms []po.Room) (po.Session, []po.Room, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveInfoEndTimeConsume(ginCtx, toUpdateOrderRoomPlans, toAddOrderRoomPlans, toUpdateSessions, toUpdateRooms)
}

// 保存 重开
func (r *OrderRepositoryProxy) SaveInfoOrderReopen(ctx context.Context, toUpdateRooms []po.Room, toUpdateSessions []po.Session) ([]po.Room, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveInfoOrderReopen(ginCtx, toUpdateRooms, toUpdateSessions)
}

// SaveOrderPayInfoPre 保存订单支付信息预处理
func (r *OrderRepositoryProxy) SaveOrderPayInfoPre(ctx context.Context, toUpdateSession po.Session, toUpdateModifyOrderProducts []po.OrderProduct, toUpdateModifyOrderRoomPlans []po.OrderRoomPlan, toAddPayBill po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddPayRecords []po.PayRecord) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderPayInfoPre(ginCtx, toUpdateSession, toUpdateModifyOrderProducts, toUpdateModifyOrderRoomPlans, toAddPayBill, toAddOrderAndPays, toAddPayRecords)
}

// IsMatchAllUnpaidOrder 检查是否存在未支付的订单
func (r *OrderRepositoryProxy) IsMatchAllUnpaidOrder(ctx context.Context, orderNos []string, venueId string, sessionId string) (unpaidOrderNos []string, err error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.IsMatchAllUnpaidOrder(ginCtx, orderNos, venueId, sessionId)
}

// 保存订单信息换台
func (r *OrderRepositoryProxy) SaveOrderInfoSwapRoom(ctx context.Context, newOrderPOInfoUnionVOA managentVo.OrderPOInfoUnionVO, newOrderPOInfoUnionVOB managentVo.OrderPOInfoUnionVO, newSessionA po.Session, newRoomA po.Room, newSessionB po.Session, newRoomB po.Room) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderInfoSwapRoom(ginCtx, newOrderPOInfoUnionVOA, newOrderPOInfoUnionVOB, newSessionA, newRoomA, newSessionB, newRoomB)
}

// 保存订单信息并房
func (r *OrderRepositoryProxy) SaveOrderInfoMergeRoom(ctx context.Context, newOrderPOInfoUnionVOA managentVo.OrderPOInfoUnionVO, toUpdateRooms []po.Room, toUpdateSessions []po.Session) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SaveOrderInfoMergeRoom(ginCtx, newOrderPOInfoUnionVOA, toUpdateRooms, toUpdateSessions)
}

// 查询所有订单
func (r *OrderRepositoryProxy) FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Order, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.FindsBySessionIds(ginCtx, venueId, sessionIds)
}

// 根据订单号查询订单
func (r *OrderRepositoryProxy) FindOrdersByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.Order, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.FindOrdersByOrderNos(ginCtx, venueId, orderNos)
}

// FindLastRoomOperation 查询最后一个房间操作
func (r *OrderRepositoryProxy) FindLastRoomOperation(ctx context.Context, venueId string, roomIds []string) ([]po.RoomOperation, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.FindLastRoomOperation(ginCtx, venueId, roomIds)
}

// RecordLastRoomOperation 记录最后一个房间操作
func (r *OrderRepositoryProxy) RecordLastRoomOperation(ctx context.Context, venueId, roomId, sessionId, employeeId, opType, info string) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.RecordLastRoomOperation(ginCtx, venueId, roomId, sessionId, employeeId, opType, info)
}

// FindsByTimeRange 根据时间范围查询订单
func (r *OrderRepositoryProxy) FindsByTimeRange(ctx context.Context, venueId string, startTime, endTime int64) ([]po.Order, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.FindsByTimeRange(ginCtx, venueId, startTime, endTime)
}

// 发送开卡短信
func (r *OrderRepositoryProxy) SendSmsMemberCardOpen(ctx context.Context, venue *po.Venue, memberCard *po.MemberCard) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SendSmsMemberCardOpen(ginCtx, venue, memberCard)
}

// 发送会员卡充值短信
func (r *OrderRepositoryProxy) SendSmsMemberCardRecharge(ctx context.Context, venue *po.Venue, memberCard *po.MemberCard, currentTotalAmount int64, currentPrincipalAmount int64) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SendSmsMemberCardRecharge(ginCtx, venue, memberCard, currentTotalAmount, currentPrincipalAmount)
}

// 发送会员卡消费短信
func (r *OrderRepositoryProxy) SendSmsMemberCardConsume(ctx context.Context, venue *po.Venue, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SendSmsMemberCardConsume(ginCtx, venue, memberCardId, currentPrincipalAmount, currentRoomBonusAmount, currentGoodsBonusAmount, currentCommonBonusAmount)
}

// 发送会员卡退款短信
func (r *OrderRepositoryProxy) SendSmsMemberCardRefund(ctx context.Context, venueId string, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SendSmsMemberCardRefund(ginCtx, venueId, memberCardId, currentPrincipalAmount, currentRoomBonusAmount, currentGoodsBonusAmount, currentCommonBonusAmount)
}

// 同步产品库存
func (r *OrderRepositoryProxy) SyncProductStock(ctx context.Context, venue *po.Venue, orderDirection string, orderProducts []po.OrderProduct) error {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	return r.orderService.SyncProductStock(ginCtx, venue, orderDirection, orderProducts)
}