package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
)

// ProductProxy 产品代理
type ProductTypeProxy struct {
	*BaseSubjectProxy
	legacyService       *impl.ProductTypeService
	productTypeTransfer *transfer.ProductTypeTransfer
}

// FindProductTypesByIds implements repository.Repository.
func (p *ProductTypeProxy) FindProductTypesByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindCategorysByIds(ginCtx, venueId, productTypeIds)
}

// NewProductProxy 创建产品代理
func NewProductTypeProxy() *ProductTypeProxy {
	return &ProductTypeProxy{
		BaseSubjectProxy:    NewBaseSubjectProxy("product_type"),
		legacyService:       &impl.ProductTypeService{},
		productTypeTransfer: &transfer.ProductTypeTransfer{},
	}
}

// Create 创建产品
func (p *ProductTypeProxy) Create(ctx context.Context, product *po.ProductType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateProductType(ginCtx, product)
}

// Update 更新产品
func (p *ProductTypeProxy) Update(ctx context.Context, productType *po.ProductType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductType(ginCtx, productType)
}

// UpdatePartial 部分更新产品
func (p *ProductTypeProxy) UpdatePartial(ctx context.Context, productType *po.ProductType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateProductTypePartial(ginCtx, productType)
}

// Delete 删除产品
func (p *ProductTypeProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteProductType(ginCtx, id)
}

// FindByID 根据ID查询产品
func (p *ProductTypeProxy) FindByID(ctx context.Context, id string) (*po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindProductTypeById(ginCtx, id)
}

// FindAll 查询所有产品
func (p *ProductTypeProxy) FindAll(ctx context.Context, reqDto *req.QueryProductTypeReqDto) (*[]po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindAllProductType(ginCtx, reqDto)
}

// FindAllWithPagination 分页查询所有产品
func (p *ProductTypeProxy) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductTypeReqDto) (*[]po.ProductType, int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, 0, err
	}
	return p.legacyService.FindAllProductTypeWithPagination(ginCtx, reqDto)
}

// FindProductsByIds 根据ID列表查询产品
func (p *ProductTypeProxy) FindProductsByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindCategorysByIds(ginCtx, venueId, productTypeIds)
}

// ConvertToProductVO 转换为产品VO
func (p *ProductTypeProxy) ConvertToProductTypeVO(ctx context.Context, productType po.ProductType) vo.ProductTypeVO {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.ProductTypeVO{}
	}
	return p.productTypeTransfer.PoToVo(productType)
}

// ConvertToProduct 转换为产品PO
func (p *ProductTypeProxy) ConvertToProductType(ctx context.Context, productTypeVO vo.ProductTypeVO) po.ProductType {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return po.ProductType{}
	}
	return p.productTypeTransfer.VoToPo(productTypeVO)
}

// FindsByIds 根据ID列表查询产品
func (p *ProductTypeProxy) FindsByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindCategorysByIds(ginCtx, venueId, productTypeIds)
}

// FindByCondition 根据条件查询产品
func (p *ProductTypeProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.ProductType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryProductTypeReqDto{}

	// 转换查询条件
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	result, err := p.legacyService.FindAllProductType(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}
