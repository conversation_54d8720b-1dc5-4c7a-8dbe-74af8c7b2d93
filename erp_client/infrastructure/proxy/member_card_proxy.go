package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// MemberCardRepositoryProxy 会员充值账单仓储代理
type MemberCardRepositoryProxy struct {
	*BaseSubjectProxy
	memberCardService *impl.MemberCardService
}

// NewMemberCardRepository 创建会员充值账单仓储代理实例
func NewMemberCardRepository() repository.MemberCardRepository {
	return &MemberCardRepositoryProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("memberCard"),
		memberCardService: &impl.MemberCardService{},
	}
}

// FindAllMemberCard 查询所有会员充值账单
func (r *MemberCardRepositoryProxy) FindAllMemberCard(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberCard, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryMemberCardReqDto{
		Ids: &[]string{venueId},
	}
	return r.memberCardService.FindAllMemberCard(ginCtx, reqDto)
}

// ConvertToMemberCardVO 转换为会员充值账单VO
func (r *MemberCardRepositoryProxy) ConvertToMemberCardVO(ctx context.Context, memberCard po.MemberCard) vo.MemberCardVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberCardVO{}
	}
	val := r.memberCardService.ConvertToMemberCardVO(ginCtx, memberCard)
	return val
}

// ConvertToMemberCard 转换为会员充值账单PO
func (r *MemberCardRepositoryProxy) ConvertToMemberCard(ctx context.Context, memberCardVO vo.MemberCardVO) po.MemberCard {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberCard{}
	}
	val := r.memberCardService.ConvertToMemberCard(ginCtx, memberCardVO)
	return val
}

// GetMaxMemberCardId 获取最大会员ID
func (r *MemberCardRepositoryProxy) GetMaxMemberCardId(ctx context.Context) (int, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return 0, err
	}
	val, err := r.memberCardService.GetMaxMemberCardId(ginCtx)
	if err != nil {
		return 0, err
	}
	return val, nil
}

// SaveMemberCard 保存会员
func (r *MemberCardRepositoryProxy) SaveMemberCard(ctx context.Context, memberCard po.MemberCard, reqDto req.V3OpenCardReqDto) (po.MemberCard, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberCard{}, err
	}
	val, err := r.memberCardService.SaveMemberCard(ginCtx, memberCard, reqDto)
	return val, err
}

// FindById 根据ID查询会员
func (r *MemberCardRepositoryProxy) FindById(ctx context.Context, id string) (po.MemberCard, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberCard{}, err
	}
	return r.memberCardService.FindById(ginCtx, id)
}

// FindsByVenueId 根据场馆ID查询所有会员
func (r *MemberCardRepositoryProxy) FindsByVenueId(ctx context.Context, venueId string) ([]po.MemberCard, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return []po.MemberCard{}, err
	}
	return r.memberCardService.FindsByVenueId(ginCtx, venueId)
}

// FindsByIdsAndCtime 根据ID查询所有会员
func (r *MemberCardRepositoryProxy) FindsByIdsAndCtime(ctx context.Context, ids []string, startTime int64, endTime int64) ([]po.MemberCard, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return []po.MemberCard{}, err
	}
	return r.memberCardService.FindsByIdsAndCtime(ginCtx, ids, startTime, endTime)
}
