package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/order_roomplan/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// OrderRoomPlanProxy 订单房间计划代理实现
type OrderRoomPlanProxy struct {
	*BaseSubjectProxy
	legacyService *impl.OrderRoomPlanService
}

// NewOrderRoomPlanProxy 创建OrderRoomPlan代理
func NewOrderRoomPlanProxy() repository.Repository {
	return &OrderRoomPlanProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("order_roomplan"),
		legacyService:    &impl.OrderRoomPlanService{},
	}
}

// Create 创建订单房间计划
func (p *OrderRoomPlanProxy) Create(ctx context.Context, orderRoomPlan *po.OrderRoomPlan) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateOrderRoomPlan(ginCtx, orderRoomPlan)
}

// Update 更新订单房间计划
func (p *OrderRoomPlanProxy) Update(ctx context.Context, orderRoomPlan *po.OrderRoomPlan) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateOrderRoomPlan(ginCtx, orderRoomPlan)
}

// Delete 删除订单房间计划
func (p *OrderRoomPlanProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteOrderRoomPlan(ginCtx, id)
}

// FindByCondition 根据条件查询订单房间计划
func (p *OrderRoomPlanProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (list *[]po.OrderRoomPlan, err error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryOrderRoomPlanReqDto{}

	result, err := p.legacyService.FindAllOrderRoomPlan(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindByID 根据ID查询订单房间计划
func (p *OrderRoomPlanProxy) FindByID(ctx context.Context, id string) (*po.OrderRoomPlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	orderRoomPlan, err := p.legacyService.FindOrderRoomPlanById(ginCtx, id)
	if err != nil {
		return nil, err
	}

	if orderRoomPlan == nil {
		return nil, nil
	}

	return orderRoomPlan, nil
}

// GetOrderRoomPlan 获取订单房间计划
func (p *OrderRoomPlanProxy) GetOrderRoomPlan(ctx context.Context, id string) (po.OrderRoomPlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.OrderRoomPlan{}, err
	}
	val, err := p.legacyService.FindOrderRoomPlanById(ginCtx, id)
	if err != nil {
		return po.OrderRoomPlan{}, err
	}
	return *val, nil
}

func (p *OrderRoomPlanProxy) ConvertToOrderRoomPlanVO(ctx context.Context, orderRoomPlan po.OrderRoomPlan) vo.OrderRoomPlanVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.OrderRoomPlanVO{}
	}
	val := p.legacyService.ConvertToOrderRoomPlanVO(ginCtx, orderRoomPlan)
	return val
}

func (p *OrderRoomPlanProxy) ConvertToOrderRoomPlan(ctx context.Context, orderRoomPlanVO vo.OrderRoomPlanVO) po.OrderRoomPlan {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.OrderRoomPlan{}
	}
	val := p.legacyService.ConvertToOrderRoomPlan(ginCtx, orderRoomPlanVO)
	return val
}

func (p *OrderRoomPlanProxy) GetOrderRoomPlansBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error)  {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.GetOrderRoomPlansBySessionIds(ginCtx, venueId, sessionIds)
	if err != nil {
		return nil, err
	}
	return val, nil
}

func (p *OrderRoomPlanProxy) GetOrderRoomPlanById(ctx context.Context, venueId string, orderRoomPlanId string) (po.OrderRoomPlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.OrderRoomPlan{}, err
	}
	val, err := p.legacyService.GetOrderRoomPlanById(ginCtx, venueId, orderRoomPlanId)
	if err != nil {
		return po.OrderRoomPlan{}, err
	}
	return val, nil
}

// FindsByOrderNos 根据orderNos查询订单房间计划
func (p *OrderRoomPlanProxy) FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderRoomPlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindsByOrderNos(ginCtx, venueId, orderNos)
	if err != nil {
		return nil, err
	}
	return val, nil
}

func (p *OrderRoomPlanProxy) FindOrderRoomPlansGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindOrderRoomPlansGiftBySessionIds(ginCtx, venueId, sessionIds)
	if err != nil {
		return nil, err
	}
	return val, nil
}
