package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// MemberCardVenueRepositoryProxy 会员卡门店关联仓储代理
type MemberCardVenueRepositoryProxy struct {
	*BaseSubjectProxy
	memberCardVenueService *impl.MemberCardVenueService
}

// NewMemberCardVenueRepository 创建会员卡门店关联仓储代理实例
func NewMemberCardVenueRepository() repository.MemberCardVenueRepository {
	return &MemberCardVenueRepositoryProxy{
		BaseSubjectProxy:       NewBaseSubjectProxy("memberCardVenue"),
		memberCardVenueService: &impl.MemberCardVenueService{},
	}
}

// FindAllMemberCardVenue 查询所有会员卡门店关联
func (r *MemberCardVenueRepositoryProxy) FindAllMemberCardVenue(ctx context.Context, venueId string) (*[]po.MemberCardVenue, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryMemberCardVenueReqDto{
		VenueId: &venueId,
	}
	return r.memberCardVenueService.FindAllMemberCardVenue(ginCtx, reqDto)
}

// ConvertToMemberCardVenueVO 转换为会员卡门店关联VO
func (r *MemberCardVenueRepositoryProxy) ConvertToMemberCardVenueVO(ctx context.Context, memberCardVenue po.MemberCardVenue) vo.MemberCardVenueVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberCardVenueVO{}
	}
	val := r.memberCardVenueService.ConvertToMemberCardVenueVO(ginCtx, memberCardVenue)
	return val
}

// ConvertToMemberCardVenue 转换为会员卡门店关联PO
func (r *MemberCardVenueRepositoryProxy) ConvertToMemberCardVenue(ctx context.Context, memberCardVenueVO vo.MemberCardVenueVO) po.MemberCardVenue {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberCardVenue{}
	}
	val := r.memberCardVenueService.ConvertToMemberCardVenue(ginCtx, memberCardVenueVO)
	return val
}

// FindByMemberCardId 根据会员卡ID查询会员卡门店关联
func (r *MemberCardVenueRepositoryProxy) FindByMemberCardId(ctx context.Context, memberCardId string) ([]po.MemberCardVenue, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return []po.MemberCardVenue{}, err
	}
	return r.memberCardVenueService.FindByMemberCardId(ginCtx, memberCardId)
}

// FindAllByVenueId 根据场馆ID查询所有会员卡门店关联
func (r *MemberCardVenueRepositoryProxy) FindAllByVenueId(ctx context.Context, venueId string) ([]po.MemberCardVenue, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return []po.MemberCardVenue{}, err
	}
	return r.memberCardVenueService.FindAllByVenueId(ginCtx, venueId)
}
