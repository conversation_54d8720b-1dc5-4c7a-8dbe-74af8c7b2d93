package proxy

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"voderpltvv/erp_client/domain/rule/model"
	"voderpltvv/erp_client/domain/rule/parser"
	"voderpltvv/erp_client/domain/rule/repository"
)

// RuleProxy 规则代理
type RuleProxy struct {
	configPath string
	parser     *parser.YAMLRuleParser
	cache      map[string]*model.RuleGroup
	mutex      sync.RWMutex
}

// NewRuleProxy 创建规则代理
func NewRuleProxy() repository.Repository {
	return &RuleProxy{
		configPath: "erp_client/config/rules", // 直接使用项目根目录的相对路径
		parser:     parser.NewYAMLRuleParser(),
		cache:      make(map[string]*model.RuleGroup),
	}
}

// GetGroup 获取规则组
func (p *RuleProxy) GetGroup(ctx context.Context, groupID string) (*model.RuleGroup, error) {
	p.mutex.RLock()
	if group, exists := p.cache[groupID]; exists {
		p.mutex.RUnlock()
		return group, nil
	}
	p.mutex.RUnlock()

	// 从文件加载
	filePath := filepath.Join(p.configPath, groupID+".yaml")
	fmt.Printf("尝试加载规则文件: %s\n", filePath) // 添加日志

	content, err := os.ReadFile(filePath)

	if err != nil {
		return nil, fmt.Errorf("读取规则文件失败: %w (path: %s)", err, filePath)
	}

	// 使用parser解析YAML内容
	group, err := p.parser.ParseRuleGroup(content)
	fmt.Printf("解析规则组: %+v\n", group)
	fmt.Printf("解析规则组错误: %+v\n", err)
	if err != nil {
		return nil, fmt.Errorf("解析规则组失败: %w", err)
	}

	// 更新缓存
	p.mutex.Lock()
	p.cache[groupID] = group
	p.mutex.Unlock()

	fmt.Printf("成功加载规则组: %+v\n", group) // 添加日志
	return group, nil
}

// SaveGroup 保存规则组（暂不实现）
func (p *RuleProxy) SaveGroup(ctx context.Context, group *model.RuleGroup) error {
	return fmt.Errorf("not implemented")
}

// DeleteGroup 删除规则组（暂不实现）
func (p *RuleProxy) DeleteGroup(ctx context.Context, groupID string) error {
	return fmt.Errorf("not implemented")
}

// GetRule 获取规则
func (p *RuleProxy) GetRule(ctx context.Context, ruleID string) (*model.Rule, error) {
	// 遍历所有规则组查找指定规则
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	for _, group := range p.cache {
		for _, rule := range group.Rules {
			if rule.ID == ruleID {
				return rule, nil
			}
		}
	}

	return nil, fmt.Errorf("rule not found: %s", ruleID)
}

// SaveRule 保存规则（暂不实现）
func (p *RuleProxy) SaveRule(ctx context.Context, rule *model.Rule) error {
	return fmt.Errorf("not implemented")
}

// DeleteRule 删除规则（暂不实现）
func (p *RuleProxy) DeleteRule(ctx context.Context, ruleID string) error {
	return fmt.Errorf("not implemented")
}

// FindRulesByGroup 查找规则组中的规则
func (p *RuleProxy) FindRulesByGroup(ctx context.Context, groupID string) ([]*model.Rule, error) {
	group, err := p.GetGroup(ctx, groupID)
	if err != nil {
		return nil, err
	}
	return group.Rules, nil
}

// FindRulesByCondition 根据条件查找规则（暂不实现）
func (p *RuleProxy) FindRulesByCondition(ctx context.Context, condition map[string]interface{}) ([]*model.Rule, error) {
	return nil, fmt.Errorf("not implemented")
}
