package proxy

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
)

// VenuePayTypeSettingProxy 门店支付设置代理
type VenuePayTypeSettingProxy struct {
	*BaseSubjectProxy
	legacyService               *impl.VenuePayTypeSettingService
	venuePayTypeSettingTransfer *transfer.VenuePayTypeSettingTransfer
}

// NewVenuePayTypeSettingProxy 创建门店支付设置代理
func NewVenuePayTypeSettingProxy() *VenuePayTypeSettingProxy {
	return &VenuePayTypeSettingProxy{
		BaseSubjectProxy:            NewBaseSubjectProxy("venue_pay_type_setting"),
		legacyService:               &impl.VenuePayTypeSettingService{},
		venuePayTypeSettingTransfer: &transfer.VenuePayTypeSettingTransfer{},
	}
}

// Create 创建门店支付设置
func (p *VenuePayTypeSettingProxy) Create(ctx context.Context, venuePayTypeSetting *po.VenuePayTypeSetting) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateVenuePayTypeSetting(ginCtx, venuePayTypeSetting)
}

// Update 更新门店支付设置
func (p *VenuePayTypeSettingProxy) Update(ctx context.Context, venuePayTypeSetting *po.VenuePayTypeSetting) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateVenuePayTypeSetting(ginCtx, venuePayTypeSetting)
}

// UpdatePartial 部分更新门店支付设置
func (p *VenuePayTypeSettingProxy) UpdatePartial(ctx context.Context, venuePayTypeSetting *po.VenuePayTypeSetting) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateVenuePayTypeSettingPartial(ginCtx, venuePayTypeSetting)
}

// Delete 删除门店支付设置
func (p *VenuePayTypeSettingProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteVenuePayTypeSetting(ginCtx, id)
}

// FindByID 根据ID查询门店支付设置
func (p *VenuePayTypeSettingProxy) FindByID(ctx context.Context, id string) (*po.VenuePayTypeSetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindVenuePayTypeSettingById(ginCtx, id)
}

// FindAll 查询所有门店支付设置
func (p *VenuePayTypeSettingProxy) FindAll(ctx context.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (*[]po.VenuePayTypeSetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindAllVenuePayTypeSetting(ginCtx, reqDto)
}

// FindAllWithPagination 分页查询所有门店支付设置
func (p *VenuePayTypeSettingProxy) FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (*[]po.VenuePayTypeSetting, int64, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, 0, err
	}
	return p.legacyService.FindAllVenuePayTypeSettingWithPagination(ginCtx, reqDto)
}

// FindVenuePayTypeSettingsByIds 根据ID列表查询门店支付设置
func (p *VenuePayTypeSettingProxy) FindVenuePayTypeSettingsByIds(ctx context.Context, venueId string, venuePayTypeSettingIds []string) ([]po.VenuePayTypeSetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryVenuePayTypeSettingReqDto{
		VenueId: &venueId,
		// Note: VenuePayTypeSetting doesn't have Ids field in QueryVenuePayTypeSettingReqDto,
		// so we'll need to query by individual IDs or modify the DTO
	}
	result, err := p.legacyService.FindAllVenuePayTypeSetting(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	if result == nil {
		return []po.VenuePayTypeSetting{}, nil
	}

	// Filter by IDs if provided
	if len(venuePayTypeSettingIds) > 0 {
		filtered := make([]po.VenuePayTypeSetting, 0)
		for _, setting := range *result {
			for _, id := range venuePayTypeSettingIds {
				if setting.Id != nil && *setting.Id == id {
					filtered = append(filtered, setting)
					break
				}
			}
		}
		return filtered, nil
	}

	return *result, nil
}

// ConvertToVenuePayTypeSettingVO 转换为门店支付设置VO
func (p *VenuePayTypeSettingProxy) ConvertToVenuePayTypeSettingVO(ctx context.Context, venuePayTypeSetting po.VenuePayTypeSetting) vo.VenuePayTypeSettingVO {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.VenuePayTypeSettingVO{}
	}
	return p.venuePayTypeSettingTransfer.PoToVo(venuePayTypeSetting)
}

// ConvertToVenuePayTypeSetting 转换为门店支付设置PO
func (p *VenuePayTypeSettingProxy) ConvertToVenuePayTypeSetting(ctx context.Context, venuePayTypeSettingVO vo.VenuePayTypeSettingVO) po.VenuePayTypeSetting {
	_, err := p.ConvertContext(ctx)
	if err != nil {
		return po.VenuePayTypeSetting{}
	}
	return p.venuePayTypeSettingTransfer.VoToPo(venuePayTypeSettingVO)
}

// FindsByIds 根据ID列表查询门店支付设置
func (p *VenuePayTypeSettingProxy) FindsByIds(ctx context.Context, venueId string, venuePayTypeSettingIds []string) ([]po.VenuePayTypeSetting, error) {
	return p.FindVenuePayTypeSettingsByIds(ctx, venueId, venuePayTypeSettingIds)
}

// FindByCondition 根据条件查询门店支付设置
func (p *VenuePayTypeSettingProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.VenuePayTypeSetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryVenuePayTypeSettingReqDto{}

	// 转换查询条件
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	if remark, ok := condition["remark"].(string); ok {
		reqDto.Remark = &remark
	}

	result, err := p.legacyService.FindAllVenuePayTypeSetting(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindVenuePayTypeSettingByVenueId 根据门店ID查询支付设置
func (p *VenuePayTypeSettingProxy) FindVenuePayTypeSettingByVenueId(ctx context.Context, venueId string) (*po.VenuePayTypeSetting, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindLastPayTypeSetting(ginCtx, venueId)
}
