package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/price_plan/model"
	"voderpltvv/erp_client/domain/valueobject/business/price_plan/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// PricePlanProxy PricePlan代理实现
type PricePlanProxy struct {
	*BaseSubjectProxy
	legacyService *impl.PricePlanService
}

// NewPricePlanProxy 创建PricePlan代理
func NewPricePlanProxy() repository.Repository {
	return &PricePlanProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("price_plan"),
		legacyService:    &impl.PricePlanService{},
	}
}

// Create 创建价格方案
func (p *PricePlanProxy) Create(ctx context.Context, pricePlan *po.PricePlan) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreatePricePlan(ginCtx, pricePlan)
}

// Update 更新价格方案
func (p *PricePlanProxy) Update(ctx context.Context, pricePlan *po.PricePlan) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdatePricePlan(ginCtx, pricePlan)
}

// Delete 删除价格方案
func (p *PricePlanProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeletePricePlan(ginCtx, id)
}

// FindByCondition 根据条件查询价格方案
func (p PricePlanProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryPricePlanReqDto{}
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}
	if roomType, ok := condition["roomType"].(string); ok {
		reqDto.RoomType = &roomType
	}
	if distributionChannel, ok := condition["distributionChannel"].(string); ok {
		reqDto.DistributionChannel = &distributionChannel
	}
	if consumptionMode, ok := condition["consumptionMode"].(string); ok {
		reqDto.ConsumptionMode = &consumptionMode
	}

	result, err := p.legacyService.FindAllPricePlan(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindByID 根据ID查询价格方案
func (p *PricePlanProxy) FindByID(ctx context.Context, id string) (*po.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	pricePlan, err := p.legacyService.FindPricePlanById(ginCtx, id)
	if err != nil {
		return nil, err
	}

	if pricePlan == nil {
		return nil, nil
	}

	return pricePlan, nil
}

// FindByVenueID 根据场馆ID查询价格方案列表
func (p *PricePlanProxy) FindByVenueID(ctx context.Context, venueID string) ([]model.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryPricePlanReqDto{
		VenueId: &venueID,
	}
	pricePlans, err := p.legacyService.FindAllPricePlan(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	result := make([]model.PricePlan, len(*pricePlans))
	for i := range *pricePlans {
		result[i] = model.NewPricePlanFromPO(&(*pricePlans)[i])
	}
	return result, nil
}

// FindByRoomType 根据房间类型查询价格方案列表
func (p *PricePlanProxy) FindByRoomType(ctx context.Context, roomType string) ([]model.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryPricePlanReqDto{
		RoomTypeLike: &roomType,
	}
	pricePlans, err := p.legacyService.FindAllPricePlan(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	result := make([]model.PricePlan, len(*pricePlans))
	for i := range *pricePlans {
		result[i] = model.NewPricePlanFromPO(&(*pricePlans)[i])
	}
	return result, nil
}

// FindByVenueIDAndRoomType 根据场所ID和房间类型查询价格方案列表
func (p *PricePlanProxy) FindByVenueIDAndRoomType(ctx context.Context, venueID string, roomType string) ([]model.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryPricePlanReqDto{
		VenueId:  &venueID,
		RoomType: &roomType,
	}
	pricePlans, err := p.legacyService.FindAllPricePlan(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	result := make([]model.PricePlan, len(*pricePlans))
	for i := range *pricePlans {
		result[i] = model.NewPricePlanFromPO(&(*pricePlans)[i])
	}
	return result, nil
}

// FindPricePlansByRoomType 根据房间类型查询价格方案列表
func (p *PricePlanProxy) FindPricePlansByRoomType(ctx context.Context, roomType string, venueID string) ([]po.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryPricePlanReqDto{
		RoomType: &roomType,
		VenueId:  &venueID,
	}
	pricePlans, err := p.legacyService.FindAllPricePlan(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	if pricePlans == nil {
		return nil, nil
	}
	return *pricePlans, nil
}

// FillProductAndPackageProductsVOsForPricePlan 填充产品及套餐产品VO
func (p *PricePlanProxy) FillProductAndPackageProductsVOsForPricePlan(ctx context.Context, venueID string, pricePlanVO *vo.PricePlanVO) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return
	}
	p.legacyService.FillProductAndPackageProductsVOsForPricePlan(ginCtx, venueID, pricePlanVO)
}

// ConvertToPricePlanVO 转换为价格方案VO
func (p *PricePlanProxy) ConvertToPricePlanVO(ctx context.Context, pricePlan *po.PricePlan) vo.PricePlanVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.PricePlanVO{}
	}
	return p.legacyService.ConvertToPricePlanVO(ginCtx, pricePlan)
}

// ConvertToPricePlan 转换为价格方案
func (p *PricePlanProxy) ConvertToPricePlan(ctx context.Context, pricePlanVO *vo.PricePlanVO) po.PricePlan {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.PricePlan{}
	}
	return p.legacyService.ConvertToPricePlan(ginCtx, pricePlanVO)
}

// GetPricePlan 获取价格方案
func (p *PricePlanProxy) GetPricePlan(ctx context.Context, id string) (po.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.PricePlan{}, err
	}
	pricePlan, err := p.legacyService.FindPricePlanById(ginCtx, id)
	if err != nil {
		return po.PricePlan{}, err
	}
	if pricePlan == nil {
		return po.PricePlan{}, nil
	}
	return *pricePlan, nil
}

// FindByVenueIDAndSessionId 根据场所ID和场次ID查询价格方案列表
func (p *PricePlanProxy) FindByVenueIDAndSessionId(ctx context.Context, venueID string, sessionID string) ([]po.PricePlan, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindByVenueIDAndSessionId(ginCtx, venueID, sessionID)
}
