package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardOperationRepositoryProxy 会员卡操作记录仓储代理
type MemberCardOperationRepositoryProxy struct {
	*BaseSubjectProxy
	memberCardOperationService *impl.MemberCardOperationService
}

// NewMemberCardOperationRepository 创建会员卡操作记录仓储代理实例
func NewMemberCardOperationRepository() repository.MemberCardOperationRepository {
	return &MemberCardOperationRepositoryProxy{
		BaseSubjectProxy:           NewBaseSubjectProxy("memberCardOperation"),
		memberCardOperationService: &impl.MemberCardOperationService{},
	}
}

// CreateMemberCardOperation 创建会员卡操作记录
func (r *MemberCardOperationRepositoryProxy) CreateMemberCardOperation(ctx context.Context, memberCardOperation *po.MemberCardOperation) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberCardOperationService.CreateMemberCardOperation(ginCtx, memberCardOperation)
}

// ConvertToMemberCardOperationVO 转换为会员卡操作记录VO
func (r *MemberCardOperationRepositoryProxy) ConvertToMemberCardOperationVO(ctx context.Context, memberCardOperation po.MemberCardOperation) vo.MemberCardOperationVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberCardOperationVO{}
	}
	return r.memberCardOperationService.ConvertToMemberCardOperationVO(ginCtx, memberCardOperation)
}

// ConvertToMemberCardOperation 转换为会员卡操作记录PO
func (r *MemberCardOperationRepositoryProxy) ConvertToMemberCardOperation(ctx context.Context, memberCardOperationVO vo.MemberCardOperationVO) po.MemberCardOperation {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberCardOperation{}
	}
	return r.memberCardOperationService.ConvertToMemberCardOperation(ginCtx, memberCardOperationVO)
}

// RecordMemberCardOperation 记录会员卡操作记录
func (r *MemberCardOperationRepositoryProxy) RecordMemberCardOperation(ctx context.Context, memberCardOperation po.MemberCardOperation, memberCard po.MemberCard) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberCardOperationService.RecordMemberCardOperation(ginCtx, memberCardOperation, memberCard)
}

// FindMemberCardOperationsByTimeRange 根据时间范围查询会员卡操作记录
func (r *MemberCardOperationRepositoryProxy) FindMemberCardOperationsByTimeRange(ctx context.Context, venueId string, timeStart int64, timeEnd int64, optionType string) ([]po.MemberCardOperation, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.memberCardOperationService.FindMemberCardOperationsByTimeRange(ginCtx, venueId, timeStart, timeEnd, optionType)
}

// UpdateRechargeBanlance 更新充值余额
func (r *MemberCardOperationRepositoryProxy) UpdateRechargeBanlance(ctx context.Context, memberCardId string, payId string) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberCardOperationService.UpdateRechargeBanlance(ginCtx, memberCardId, payId)
}