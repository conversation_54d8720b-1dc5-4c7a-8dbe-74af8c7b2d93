package proxy

import "errors"

// 简单错误常量
var (
	// ErrInvalidContext 无效的上下文类型
	ErrInvalidContext = errors.New("invalid context type")
	// ErrInvalidSubjectType 无效的主体类型
	ErrInvalidSubjectType = errors.New("invalid subject type")
)

// ProxyError 定义代理错误类型
type ProxyError struct {
	Code    string
	Message string
	Cause   error
}

func (e *ProxyError) Error() string {
	if e.Cause != nil {
		return e.Message + ": " + e.Cause.Error()
	}
	return e.Message
}

// NewNotFoundError 创建未找到错误
func NewNotFoundError(message string, cause error) error {
	return &ProxyError{
		Code:    "NOT_FOUND",
		Message: message,
		Cause:   cause,
	}
}

// NewInternalError 创建内部错误
func NewInternalError(message string, cause error) error {
	return &ProxyError{
		Code:    "INTERNAL_ERROR",
		Message: message,
		Cause:   cause,
	}
}

// NewInvalidArgumentError 创建参数无效错误
func NewInvalidArgumentError(message string, cause error) error {
	return &ProxyError{
		Code:    "INVALID_ARGUMENT",
		Message: message,
		Cause:   cause,
	}
}

// NewConflictError 创建冲突错误
func NewConflictError(message string, cause error) error {
	return &ProxyError{
		Code:    "CONFLICT",
		Message: message,
		Cause:   cause,
	}
}

// NewForbiddenError 创建权限不足错误
func NewForbiddenError(message string, cause error) error {
	return &ProxyError{
		Code:    "FORBIDDEN",
		Message: message,
		Cause:   cause,
	}
}
