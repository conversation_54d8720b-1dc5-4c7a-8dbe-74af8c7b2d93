# 会员领域代理

本目录包含与会员领域相关的所有代理实现，遵循领域驱动设计原则，将会员领域的不同职责划分为多个代理类。

## 代理结构

会员领域代理按照不同的职责进行了划分：

1. **会员基本信息代理** (`member_repository_proxy.go`)
   - 负责会员基本信息的增删改查
   - 包括会员个人资料、联系方式等基础信息管理

2. **会员卡等级代理** (`card_level_repository_proxy.go`)
   - 负责会员卡等级的查询
   - 包括根据场馆ID查询所有会员卡等级

3. **场馆会员关联代理** (`venue_and_member_repository_proxy.go`)
   - 负责场馆与会员之间的关联关系管理
   - 包括会员在特定场馆的注册、查询等

4. **实体卡代理** (`physical_card_repository_proxy.go`)
   - 负责实体会员卡的管理
   - 包括根据卡号查询实体卡、更新实体卡状态等

5. **会员卡操作记录代理** (`member_card_operation_repository_proxy.go`)
   - 负责会员卡操作记录的管理
   - 包括记录会员卡的开卡、挂失、补卡等操作

6. **会员充值记录代理** (`member_recharge_repository_proxy.go`)
   - 负责会员充值记录的管理
   - 包括保存充值记录、查询会员的充值历史等

## 服务工厂

`service_factory.go` 文件提供了获取各种会员相关服务实例的方法，确保服务实例只被初始化一次。

## 设计原则

1. **单一职责原则**：每个代理类只负责一个特定的功能领域
2. **领域驱动设计**：代理类的划分基于业务领域概念，而非数据库表
3. **适当的粒度控制**：避免创建过大的"上帝类"，将不同职责划分到不同的代理中
4. **隔离外部依赖**：代理类作为外观(Facade)，隔离领域模型与外部服务实现细节

## 使用方式

在领域服务中，通过工厂方法获取相应的代理实例：

```go
// 获取会员基本信息代理
memberRepo := member.NewMemberRepository()

// 获取会员卡等级代理
cardLevelRepo := member.NewCardLevelRepository()

// 获取场馆会员关联代理
venueAndMemberRepo := member.NewVenueAndMemberRepository()
```

通过这种方式，领域服务可以专注于业务逻辑实现，而不需要关心外部服务的具体实现细节。 