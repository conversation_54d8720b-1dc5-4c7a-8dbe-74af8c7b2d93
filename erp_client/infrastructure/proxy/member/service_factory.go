package member

import (
	"sync"

	"voderpltvv/erp_managent/service/impl"
)

var (
	// 会员领域服务实例
	venueAndMemberService *impl.VenueAndMemberService
	memberService         *impl.MemberService
	physicalCardService   *impl.PhysicalCardService

	// 确保服务只初始化一次
	once sync.Once
)

// 初始化所有会员相关服务
func initServices() {
	once.Do(func() {
		venueAndMemberService = &impl.VenueAndMemberService{}
		memberService = &impl.MemberService{}
		physicalCardService = &impl.PhysicalCardService{}
	})
}

// GetVenueAndMemberService 获取VenueAndMemberService实例
func GetVenueAndMemberService() *impl.VenueAndMemberService {
	initServices()
	return venueAndMemberService
}

// GetMemberService 获取MemberService实例
func GetMemberService() *impl.MemberService {
	initServices()
	return memberService
}

// GetPhysicalCardService 获取PhysicalCardService实例
func GetPhysicalCardService() *impl.PhysicalCardService {
	initServices()
	return physicalCardService
}
