package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/room/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// RoomProxy Room代理实现
type RoomProxy struct {
	*BaseSubjectProxy
	legacyService *impl.RoomService
}

// NewRoomProxy 创建Room代理
func NewRoomProxy() repository.Repository {
	return &RoomProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("room"),
		legacyService:    &impl.RoomService{},
	}
}

// Create 创建场所
func (p *RoomProxy) Create(ctx context.Context, room *po.Room) error {
	// TODO: 实现远程调用
	return nil
}

// Update 更新场所
func (p *RoomProxy) Update(ctx context.Context, room *po.Room) error {
	// TODO: 实现远程调用
	return nil
}

// Delete 删除场所
func (p *RoomProxy) Delete(ctx context.Context, id string) error {
	// TODO: 实现远程调用
	return nil
}

// FindByCondition 根据条件查询场所
func (p *RoomProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Room, error) {
	// TODO: 实现远程调用
	return nil, nil
}

// FindByID 根据ID查询场所
func (p *RoomProxy) FindByID(ctx context.Context, id string) (*po.Room, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindRoomById(ginCtx, id)
	if err != nil {
		return nil, err
	}
	return val, nil
}

// FindByVenueID 根据场所ID查询包厢
func (p *RoomProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.Room, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryRoomReqDto{
		VenueId: &venueID,
	}
	rooms, err := p.legacyService.FindAllRoom(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	if rooms == nil {
		return []po.Room{}, nil
	}

	result := make([]po.Room, len(*rooms))
	for i := range *rooms {
		result[i] = (*rooms)[i]
	}

	return result, nil
}

// ConvertToRoomVO 转换为包厢场所
func (p *RoomProxy) ConvertToRoomVO(ctx context.Context, room po.Room) vo.RoomVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.RoomVO{}
	}
	val := p.legacyService.ConvertToRoomVO(ginCtx, room)
	return val
}

// ConvertToRoomPO 转换为包厢PO
func (p *RoomProxy) ConvertToRoom(ctx context.Context, roomVO vo.RoomVO) po.Room {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Room{}
	}
	val := p.legacyService.ConvertToRoom(ginCtx, roomVO)
	return val
}

// IsRoomLocked 检查包厢是否锁定
func (p *RoomProxy) IsRoomLocked(ctx context.Context, room po.Room) bool {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return false
	}
	return p.legacyService.IsRoomLocked(ginCtx, room)
}

// GetAttachRooms 获取联房的其他房间信息
func (p *RoomProxy) GetAttachRooms(ctx context.Context, room po.Room) ([]po.Room, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, nil
	}
	return p.legacyService.GetAttachRooms(ginCtx, room)
}

// FindRoomsBySessionId 根据sessionId查询包厢
func (p *RoomProxy) FindRoomsBySessionId(ctx context.Context, sessionId string, venueId string) ([]po.Room, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindRoomsBySessionId(ginCtx, sessionId, venueId)
}

// FindRoomsByVenueId 根据场所ID查询包厢
func (p *RoomProxy) FindRoomsByVenueId(ctx context.Context, venueId string, isDeleted bool, isDisplay bool) ([]po.Room, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindRoomsByVenueId(ginCtx, venueId, isDeleted, isDisplay)
}

// CloseRoom 关房
func (p *RoomProxy) CloseRoom(ctx context.Context, rooms []po.Room, toUpdateSessions []po.Session) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CloseRoom(ginCtx, rooms, toUpdateSessions)
}

// CleanRoomFinish 关房
func (p *RoomProxy) CleanRoomFinish(ctx context.Context, rooms []po.Room) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CleanRoomFinish(ginCtx, rooms)
}

// LockRoom 锁房
func (p *RoomProxy) LockRoom(ctx context.Context, rooms []po.Room) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.LockRoom(ginCtx, rooms)
}

// UnlockRoom 解锁房
func (p *RoomProxy) UnlockRoom(ctx context.Context, rooms []po.Room) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UnlockRoom(ginCtx, rooms)
}

// FindRoomsByCtime 根据创建时间查询包厢
func (p *RoomProxy) FindRoomsByCtime(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Room, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return p.legacyService.FindRoomsByCtime(ginCtx, venueId, startTime, endTime)
}
