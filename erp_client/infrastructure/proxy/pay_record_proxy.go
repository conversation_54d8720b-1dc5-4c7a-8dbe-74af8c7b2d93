package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
)

// PayRecordRepositoryProxy 支付记录仓储代理
type PayRecordRepositoryProxy struct {
	*BaseSubjectProxy
	payRecordService *impl.PayRecordService
}

// NewPayRecordRepository 创建支付记录仓储代理实例
func NewPayRecordRepository() repository.PayRecordRepository {
	return &PayRecordRepositoryProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("payRecord"),
		payRecordService: &impl.PayRecordService{},
	}
}

// FindAllPayRecord 查询所有支付记录
func (r *PayRecordRepositoryProxy) FindAllPayRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayRecord, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryPayRecordReqDto{
		VenueId:    &venueId,
		SessionId:  &sessionId,
		StatusList: &statusList,
	}
	return r.payRecordService.FindAllPayRecord(ginCtx, reqDto)
}

// ConvertToPayRecordVO 转换为支付记录VO
func (r *PayRecordRepositoryProxy) ConvertToPayRecordVO(ctx context.Context, payRecord po.PayRecord) vo.PayRecordVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.PayRecordVO{}
	}
	val := r.payRecordService.ConvertToPayRecordVO(ginCtx, payRecord)
	return val
}

// ConvertToPayRecord 转换为支付记录PO
func (r *PayRecordRepositoryProxy) ConvertToPayRecord(ctx context.Context, payRecordVO vo.PayRecordVO) po.PayRecord {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.PayRecord{}
	}
	val := r.payRecordService.ConvertToPayRecord(ginCtx, payRecordVO)
	return val
}

// FindsBySessionId 查询所有支付记录
func (r *PayRecordRepositoryProxy) FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayRecord, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payRecordService.FindsBySessionId(ginCtx, venueId, sessionId)
}

// FindsByBillIds 查询所有支付记录
func (r *PayRecordRepositoryProxy) FindsByBillIds(ctx context.Context, venueId string, billIds []string) ([]po.PayRecord, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payRecordService.FindsByBillIds(ginCtx, venueId, billIds)
}

// FindByPayId 查询支付记录
func (r *PayRecordRepositoryProxy) FindByPayId(ctx context.Context, payId string) (*po.PayRecord, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}
	return r.payRecordService.FindByPayId(ginCtx, payId)
}
