package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/area/model"
	"voderpltvv/erp_client/domain/valueobject/business/area/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// AreaProxy Area代理实现
type AreaProxy struct {
	*BaseSubjectProxy
	legacyService *impl.AreaService
}

// NewAreaProxy 创建Area代理
func NewAreaProxy() repository.Repository {
	return &AreaProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("area"),
		legacyService:    &impl.AreaService{},
	}
}

// Create 创建区域
func (p *AreaProxy) Create(ctx context.Context, area *po.Area) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.CreateArea(ginCtx, area)
}

// Update 更新区域
func (p *AreaProxy) Update(ctx context.Context, area *po.Area) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.UpdateArea(ginCtx, area)
}

// Delete 删除区域
func (p *AreaProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return p.legacyService.DeleteArea(ginCtx, id)
}

// FindByCondition 根据条件查询区域
func (p AreaProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (list *[]po.Area, err error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	reqDto := &req.QueryAreaReqDto{}
	if venueId, ok := condition["venueId"].(string); ok {
		reqDto.VenueId = &venueId
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}
	if id, ok := condition["id"].(string); ok {
		reqDto.Id = &id
	}

	if ids, ok := condition["ids"].([]string); ok {
		reqDto.Ids = &ids
	}

	result, err := p.legacyService.FindAllArea(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// FindByID 根据ID查询区域
func (p *AreaProxy) FindByID(ctx context.Context, id string) (*po.Area, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	area, err := p.legacyService.FindAreaById(ginCtx, id)
	if err != nil {
		return nil, err
	}

	if area == nil {
		return nil, nil
	}

	return area, nil
}

// FindByVenueID 根据场馆ID查询区域列表
func (p *AreaProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.Area, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryAreaReqDto{
		VenueId: &venueID,
	}
	areas, err := p.legacyService.FindAllArea(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	return *areas, nil
}

// convertPOToModel 将PO对象转换为领域模型
func (p *AreaProxy) convertPOToModel(area *po.Area) model.Area {
	if area == nil {
		return nil
	}

	// 处理可能为nil的字段
	id := ""
	if area.Id != nil {
		id = *area.Id
	}
	venueID := ""
	if area.VenueId != nil {
		venueID = *area.VenueId
	}
	name := ""
	if area.Name != nil {
		name = *area.Name
	}
	capacity := 0
	if area.Capacity != nil {
		capacity = *area.Capacity
	}
	description := ""
	if area.Description != nil {
		description = *area.Description
	}
	isDisplayed := false
	if area.IsDisplayed != nil {
		isDisplayed = *area.IsDisplayed
	}
	ctime := int64(0)
	if area.Ctime != nil {
		ctime = *area.Ctime
	}
	utime := int64(0)
	if area.Utime != nil {
		utime = *area.Utime
	}
	state := 0
	if area.State != nil {
		state = *area.State
	}
	version := 0
	if area.Version != nil {
		version = *area.Version
	}

	// 创建领域模型
	return model.NewArea(&po.Area{
		Id:          &id,
		VenueId:     &venueID,
		Name:        &name,
		Capacity:    &capacity,
		Description: &description,
		IsDisplayed: &isDisplayed,
		Ctime:       &ctime,
		Utime:       &utime,
		State:       &state,
		Version:     &version,
	})
}

func (p *AreaProxy) ConvertToAreaVO(ctx context.Context, area *po.Area) vo.AreaVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.AreaVO{}
	}
	val := p.legacyService.ConvertToAreaVO(ginCtx, area)
	return val
}

func (p *AreaProxy) ConvertAreaVOToPO(ctx context.Context, areaVO *vo.AreaVO) po.Area {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.Area{}
	}
	val := p.legacyService.ConvertToArea(ginCtx, areaVO)
	return val
}

// FindAreasByAreaIds 根据区域ID查询区域列表
func (p *AreaProxy) FindAreasByAreaIds(ctx context.Context, venueId string, areaIds []string) ([]po.Area, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindAreasByAreaIds(ginCtx, venueId, areaIds)
	if err != nil {
		return nil, err
	}
	return *val, nil
}
