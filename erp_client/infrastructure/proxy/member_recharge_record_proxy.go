package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// MemberRechageRecordRepositoryProxy 会员充值记录仓储代理
type MemberRechageRecordRepositoryProxy struct {
	*BaseSubjectProxy
	memberRechargeRecordService *impl.MemberRechargeRecordService
}

// NewMemberRechageRecordRepository 创建会员充值记录仓储代理实例
func NewMemberRechageRecordRepository() repository.MemberRechargeRecordRepository {
	return &MemberRechageRecordRepositoryProxy{
		BaseSubjectProxy:            NewBaseSubjectProxy("memberRechageRecord"),
		memberRechargeRecordService: &impl.MemberRechargeRecordService{},
	}
}

// FindAllMemberRechageBill 查询所有会员充值账单
func (r *MemberRechageRecordRepositoryProxy) FindAllMemberRechargeRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberRechargeRecord, error) {
	ginCtx := &gin.Context{} // 创建一个新的gin上下文
	if reqID, ok := ctx.Value("X-Request-Id").(string); ok {
		ginCtx.Set("X-Request-Id", reqID)
	}

	reqDto := &req.QueryMemberRechargeRecordReqDto{}
	return r.memberRechargeRecordService.FindAllMemberRechargeRecord(ginCtx, reqDto)
}

// ConvertToMemberRechageRecordVO 转换为会员充值记录VO
func (r *MemberRechageRecordRepositoryProxy) ConvertToMemberRechargeRecordVO(ctx context.Context, memberRechageRecord po.MemberRechargeRecord) vo.MemberRechargeRecordVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.MemberRechargeRecordVO{}
	}
	val := r.memberRechargeRecordService.ConvertToMemberRechargeRecordVO(ginCtx, memberRechageRecord)
	return val
}

// ConvertToMemberRechageRecord 转换为会员充值记录PO
func (r *MemberRechageRecordRepositoryProxy) ConvertToMemberRechargeRecord(ctx context.Context, memberRechageRecordVO vo.MemberRechargeRecordVO) po.MemberRechargeRecord {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberRechargeRecord{}
	}
	val := r.memberRechargeRecordService.ConvertToMemberRechargeRecord(ginCtx, memberRechageRecordVO)
	return val
}

// FindByPayId 根据支付ID查询会员充值记录
func (r *MemberRechageRecordRepositoryProxy) FindByPayId(ctx context.Context, payId string) (po.MemberRechargeRecord, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.MemberRechargeRecord{}, err
	}
	val, err := r.memberRechargeRecordService.FindByPayId(ginCtx, payId)
	if err != nil {
		return po.MemberRechargeRecord{}, err
	}
	return val, nil
}

// SaveMemberRechargeRecordPayInfoCallbackByPayId 保存会员充值记录支付信息回调
func (r *MemberRechageRecordRepositoryProxy) SaveMemberRechargeRecordPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.MemberPayCallbackVO) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberRechargeRecordService.SaveMemberRechargeRecordPayInfoCallbackByPayId(ginCtx, callbackVO)
}

// V3CallPayCallback 调用支付回调
func (r *MemberRechageRecordRepositoryProxy) V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.memberRechargeRecordService.V3CallPayCallback(ginCtx, reqDto)
}
