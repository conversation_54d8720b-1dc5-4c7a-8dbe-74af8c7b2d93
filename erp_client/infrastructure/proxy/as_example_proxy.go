package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/as_example/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// AsExampleRepositoryProxy 员工赠金记录仓储代理
type AsExampleRepositoryProxy struct {
	*BaseSubjectProxy
	asExampleService *impl.AsExampleService
}

// NewAsExampleRepository 创建员工赠金记录仓储代理实例
func NewAsExampleRepository() repository.AsExampleRepository {
	return &AsExampleRepositoryProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("asExample"),
		asExampleService: &impl.AsExampleService{},
	}
}

// FindByCondition implements repository.AsExampleRepository.
func (r *AsExampleRepositoryProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (list *[]po.AsExample, err error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	return r.asExampleService.FindAllAsExample(ginCtx, &req.QueryAsExampleReqDto{})
}

// CreateAsExample 创建员工赠金记录
func (r *AsExampleRepositoryProxy) CreateAsExample(ctx context.Context, record *po.AsExample) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.asExampleService.CreateAsExample(ginCtx, record)
}

// UpdateEmployeeGiftRecord 更新员工赠金记录
func (r *AsExampleRepositoryProxy) UpdateAsExample(ctx context.Context, record *po.AsExample) error {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return err
	}
	return r.asExampleService.UpdateAsExamplePartial(ginCtx, record)
}

// FindById 根据ID查询员工赠金记录
func (r *AsExampleRepositoryProxy) FindById(ctx context.Context, id string) (po.AsExample, error) {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.AsExample{}, err
	}
	record, err := r.asExampleService.FindAsExampleById(ginCtx, id)
	if err != nil {
		return po.AsExample{}, err
	}
	return *record, nil
}

// ConvertToEmployeeGiftRecordVO 转换为员工赠金记录VO
func (r *AsExampleRepositoryProxy) ConvertToAsExampleVO(ctx context.Context, record po.AsExample) vo.AsExampleVO {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return vo.AsExampleVO{}
	}
	return r.asExampleService.ConvertToAsExampleVO(ginCtx, record)
}

// ConvertToEmployeeGiftRecord 转换为员工赠金记录PO
func (r *AsExampleRepositoryProxy) ConvertToAsExample(ctx context.Context, recordVO vo.AsExampleVO) po.AsExample {
	ginCtx, err := r.ConvertContext(ctx)
	if err != nil {
		return po.AsExample{}
	}
	return r.asExampleService.ConvertToAsExample(ginCtx, recordVO)
}
