package proxy

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/room_type/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
)

// RoomTypeProxy RoomType代理实现
type RoomTypeProxy struct {
	*BaseSubjectProxy
	legacyService *impl.RoomTypeService
}

// NewRoomTypeProxy 创建RoomType代理
func NewRoomTypeProxy() repository.Repository {
	return &RoomTypeProxy{
		BaseSubjectProxy: NewBaseSubjectProxy("room_type"),
		legacyService:    &impl.RoomTypeService{},
	}
}

// FindByID 根据ID查询房间类型
func (p *RoomTypeProxy) FindByID(ctx context.Context, id string) (*po.RoomType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	return p.legacyService.FindRoomTypeById(ginCtx, id)
}

// FindByVenueID 根据场所ID查询房间类型列表
func (p *RoomTypeProxy) FindByVenueID(ctx context.Context, venueID string) ([]po.RoomType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	reqDto := &req.QueryRoomTypeReqDto{
		VenueId: &venueID,
	}
	types, err := p.legacyService.FindAllRoomType(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	if types == nil {
		return []po.RoomType{}, nil
	}

	result := make([]po.RoomType, len(*types))
	for i, t := range *types {
		result[i] = t
	}

	return result, nil
}

// FindByCondition 根据条件查询房间类型
func (p *RoomTypeProxy) FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.RoomType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}

	// 构建查询条件
	reqDto := &req.QueryRoomTypeReqDto{}
	if venueID, ok := condition["venue_id"].(string); ok {
		reqDto.VenueId = &venueID
	}
	if name, ok := condition["name"].(string); ok {
		reqDto.Name = &name
	}

	types, err := p.legacyService.FindAllRoomType(ginCtx, reqDto)
	if err != nil {
		return nil, err
	}

	if types == nil {
		emptyResult := make([]po.RoomType, 0)
		return &emptyResult, nil
	}

	result := make([]po.RoomType, len(*types))
	for i := range *types {
		result[i] = (*types)[i]
	}

	return &result, nil
}

// Create 创建房间类型
func (p *RoomTypeProxy) Create(ctx context.Context, roomType *po.RoomType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}

	return p.legacyService.CreateRoomType(ginCtx, roomType)
}

// Update 更新房间类型
func (p *RoomTypeProxy) Update(ctx context.Context, roomType *po.RoomType) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}

	return p.legacyService.UpdateRoomType(ginCtx, roomType)
}

// Delete 删除房间类型
func (p *RoomTypeProxy) Delete(ctx context.Context, id string) error {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return err
	}

	return p.legacyService.DeleteRoomType(ginCtx, id)
}

// ConvertToRoomTypeVO 转换为房间类型VO
func (p *RoomTypeProxy) ConvertToRoomTypeVO(ctx context.Context, roomType *po.RoomType) vo.RoomTypeVO {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return vo.RoomTypeVO{}
	}
	val := p.legacyService.ConvertToRoomTypeVO(ginCtx, roomType)
	return val
}

// ConvertToRoomTypePO 转换为房间类型PO
func (p *RoomTypeProxy) ConvertToRoomTypePO(ctx context.Context, roomTypeVO *vo.RoomTypeVO) po.RoomType {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return po.RoomType{}
	}
	val := p.legacyService.ConvertToRoomType(ginCtx, roomTypeVO)
	return val
}

// FindRoomTypesByRoomTypeIds 根据房间类型ID查询房间类型列表
func (p *RoomTypeProxy) FindRoomTypesByRoomTypeIds(ctx context.Context, venueId string, roomTypeIds []string) ([]po.RoomType, error) {
	ginCtx, err := p.ConvertContext(ctx)
	if err != nil {
		return nil, err
	}
	val, err := p.legacyService.FindRoomTypesByRoomTypeIds(ginCtx, venueId, roomTypeIds)
	if err != nil {
		return nil, err
	}
	return *val, nil
}