package repository

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"voderpltvv/erp_client/domain/subject/business/venue/model"
	"voderpltvv/erp_client/domain/subject/business/venue/repository"

	"gorm.io/gorm"
)

// VenuePO 门店持久化对象
type VenuePO struct {
	ID           string `gorm:"column:id;primaryKey"`
	Name         string `gorm:"column:name"`
	VenueType    int    `gorm:"column:venue_type"`
	Logo         string `gorm:"column:logo"`
	Province     string `gorm:"column:province"`
	City         string `gorm:"column:city"`
	District     string `gorm:"column:district"`
	Address      string `gorm:"column:address"`
	StartHours   string `gorm:"column:start_hours"`
	EndHours     string `gorm:"column:end_hours"`
	Photos       string `gorm:"column:photos"`
	Phone        string `gorm:"column:phone"`
	Unionid      string `gorm:"column:unionid"`
	Contact      string `gorm:"column:contact"`
	ContactPhone string `gorm:"column:contact_phone"`
	State        int    `gorm:"column:state"`
	Version      int    `gorm:"column:version"`
	Mac          string `gorm:"column:mac"`
	Ctime        int    `gorm:"column:ctime"`
	Utime        int    `gorm:"column:utime"`
}

// TableName 表名
func (VenuePO) TableName() string {
	return "venue"
}

// VenueRepositoryImpl 门店仓储实现
type VenueRepositoryImpl struct {
	db      *gorm.DB
	idMutex sync.Mutex
}

// NewVenueRepository 创建门店仓储实例
func NewVenueRepository(db *gorm.DB) repository.Repository {
	return &VenueRepositoryImpl{
		db: db,
	}
}

// Save 保存门店
func (r *VenueRepositoryImpl) Save(ctx context.Context, venue model.Venue) error {
	po := r.toPO(venue)
	return r.db.WithContext(ctx).Create(po).Error
}

// FindByID 根据ID查询门店
func (r *VenueRepositoryImpl) FindByID(ctx context.Context, id string) (model.Venue, error) {
	var po VenuePO
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&po).Error; err != nil {
		return nil, err
	}
	return r.toDomain(&po), nil
}

// FindByCondition 根据条件查询门店列表
func (r *VenueRepositoryImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]model.Venue, error) {
	var pos []VenuePO
	if err := r.db.WithContext(ctx).Where(condition).Find(&pos).Error; err != nil {
		return nil, err
	}

	venues := make([]model.Venue, len(pos))
	for i, po := range pos {
		venues[i] = r.toDomain(&po)
	}
	return venues, nil
}

// Update 更新门店
func (r *VenueRepositoryImpl) Update(ctx context.Context, venue model.Venue) error {
	po := r.toPO(venue)
	return r.db.WithContext(ctx).Save(po).Error
}

// Delete 删除门店
func (r *VenueRepositoryImpl) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&VenuePO{}, "id = ?", id).Error
}

// GetNextID 获取下一个可用的门店ID
func (r *VenueRepositoryImpl) GetNextID(ctx context.Context) (string, error) {
	r.idMutex.Lock()
	defer r.idMutex.Unlock()

	var maxID string
	err := r.db.WithContext(ctx).
		Model(&VenuePO{}).
		Select("COALESCE(MAX(CAST(id AS UNSIGNED)), 100000) as max_id").
		Where("id REGEXP '^[0-9]+$'").
		Row().
		Scan(&maxID)

	if err != nil {
		return "", fmt.Errorf("获取最大ID失败: %w", err)
	}

	currentID, err := strconv.Atoi(maxID)
	if err != nil {
		return "", fmt.Errorf("转换ID失败: %w", err)
	}

	nextID := currentID + 1
	if nextID > 999999 {
		return "", fmt.Errorf("ID超出最大值")
	}

	return fmt.Sprintf("%06d", nextID), nil
}

// FindEmployees 查询门店的员工列表
func (r *VenueRepositoryImpl) FindEmployees(ctx context.Context, venueID string) ([]string, error) {
	var employeeIDs []string
	err := r.db.WithContext(ctx).
		Table("employee").
		Select("id").
		Where("venue_id = ?", venueID).
		Pluck("id", &employeeIDs).
		Error
	return employeeIDs, err
}

// FindByName 根据名称查询场所
func (r *VenueRepositoryImpl) FindByName(ctx context.Context, name string) (model.Venue, error) {
	var po VenuePO
	if err := r.db.WithContext(ctx).Where("name = ?", name).First(&po).Error; err != nil {
		return nil, err
	}
	return r.toDomain(&po), nil
}

// FindEnabled 查询启用的场所
func (r *VenueRepositoryImpl) FindEnabled(ctx context.Context) ([]model.Venue, error) {
	var pos []VenuePO
	if err := r.db.WithContext(ctx).Where("state = ?", 1).Find(&pos).Error; err != nil {
		return nil, err
	}

	venues := make([]model.Venue, len(pos))
	for i, po := range pos {
		venues[i] = r.toDomain(&po)
	}
	return venues, nil
}

// toPO 领域模型转换为持久化对象
func (r *VenueRepositoryImpl) toPO(venue model.Venue) *VenuePO {
	return &VenuePO{
		ID:         venue.GetID(),
		Name:       venue.GetName(),
		State:      venue.GetState(),
		Version:    venue.GetVersion(),
		Address:    venue.GetAddress(),
		Contact:    venue.GetContact(),
		StartHours: venue.GetBusinessHours(),
		EndHours:   "",
		// ... 其他字段转换
	}
}

// toDomain 持久化对象转换为领域模型
func (r *VenueRepositoryImpl) toDomain(po *VenuePO) model.Venue {
	return model.NewVenue(
		po.ID,
		po.Name,
		po.Address,
		po.Contact,
		fmt.Sprintf("%s-%s", po.StartHours, po.EndHours),
		po.State == 1,
	)
}
