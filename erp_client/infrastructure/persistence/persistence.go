package persistence

import (
	"context"

	"gorm.io/gorm"
)

// Create 创建记录
func Create(ctx context.Context, db *gorm.DB, model interface{}) error {
	return db.WithContext(ctx).Create(model).Error
}

// Update 更新记录
func Update(ctx context.Context, db *gorm.DB, model interface{}) error {
	return db.WithContext(ctx).Save(model).Error
}

// Delete 删除记录
func Delete(ctx context.Context, db *gorm.DB, model interface{}, id string) error {
	return db.WithContext(ctx).Delete(model, "id = ?", id).Error
}

// FindByID 根据ID查询记录
func FindByID(ctx context.Context, db *gorm.DB, model interface{}, id string) error {
	return db.WithContext(ctx).First(model, "id = ?", id).Error
}

// FindByCondition 根据条件查询记录
func FindByCondition(ctx context.Context, db *gorm.DB, model interface{}, condition map[string]interface{}) error {
	query := db.WithContext(ctx)
	for key, value := range condition {
		query = query.Where(key+" = ?", value)
	}
	return query.Find(model).Error
}
