# 流程引擎与规则引擎变量使用指南

## 1. 变量定义

### 1.1 变量命名空间

变量在引擎中分为四个命名空间：

- `input`: 输入变量，用于接收外部传入的参数
- `output`: 输出变量，用于存储处理结果
- `context`: 上下文变量，用于流程执行过程中的临时存储
- `system`: 系统变量，包含系统预定义的变量

### 1.2 变量定义结构

```yaml
variables:
  - name: "变量名"    # 必填，变量的唯一标识
    required: true    # 可选，是否必填
    is_array: false   # 可选，是否为数组类型
    fields:           # 可选，对象类型的字段定义
      - name: "字段名"
        required: true
    default_value: "" # 可选，默认值表达式
```

### 1.3 变量类型示例

```yaml
# 简单变量
- name: "current_date"
  required: true

# 数组变量
- name: "PricePlanVOs"
  required: true
  is_array: true
  fields:
    - name: "Id"
    - name: "Name"
    - name: "BaseRoomFee"

# 对象变量
- name: "BaseInfo"
  required: true
  fields:
    - name: "Id"
    - name: "Name"
    - name: "Status"
```

## 2. 变量访问语法

### 2.1 基本访问语法

- 直接访问：`input.变量名`
- 对象属性访问：`input.变量名.属性名`
- 数组访问：`input.变量名[索引]`
- 数组遍历：`input.变量名.*`

### 2.2 访问示例

```yaml
# 简单变量访问
input.current_date

# 对象属性访问
input.BaseInfo.Status

# 数组元素访问
input.PricePlanVOs[0].BaseRoomFee

# 数组遍历访问
input.HolidayVOs.*.Date
```

## 3. 表达式语法

### 3.1 基本表达式

```yaml
# 字符串比较
condition: "input.BaseInfo.Status == 'OPEN'"

# 数值比较
condition: "input.PricePlanVOs[0].BaseRoomFee > 100"

# 布尔表达式
condition: "input.BaseInfo.IsDisplayed == true"

# 空值判断
condition: "input.reqDto.RoomId != nil && input.reqDto.RoomId != ''"
```

### 3.2 函数调用

```yaml
# 时间格式化
expression: "formatTime(now(), '2006-01-02')"

# 数组操作
condition: "contains(input.HolidayVOs.*.Date, input.current_date)"

# 数值计算
expression: "multiply(input.PricePlanVOs.BaseRoomFee, 1.5)"
```

## 4. 动作类型

### 4.1 设置字段 (set_field)

```yaml
actions:
  - type: "set_field"
    target: "output.roomVO"
    value: "context.roomVO"
```

### 4.2 设置值 (set)

```yaml
actions:
  - type: "set"
    params:
      target: "output.BaseInfo.IsDisplayed"
      value: true
```

### 4.3 计算 (calculate)

```yaml
actions:
  - type: "calculate"
    params:
      target: "output.PricePlanVOs.BaseRoomFee"
      expression: "multiply(input.PricePlanVOs.BaseRoomFee, 1.2)"
```

## 5. 完整配置示例

### 5.1 流程配置示例

```yaml
metadata:
  input:
    variables:
      - name: "reqDto"
        required: true
        fields:
          - name: "RoomId"
  output:
    variables:
      - name: "roomVO"
        required: true
        fields:
          - name: "BaseInfo"
  context:
    variables:
      - name: "temp_data"
        required: false

steps:
  - id: "fetch_data"
    name: "获取数据"
    condition: "input.reqDto.RoomId != ''"
    action:
      type: "service"
      method: "GetData"
      output:
        target: "context.temp_data"
```

### 5.2 规则配置示例

```yaml
rules:
  - id: "check_display"
    name: "检查显示状态"
    priority: 100
    condition: "input.BaseInfo.IsDisplayed == true"
    actions:
      - type: "set"
        params:
          target: "output.BaseInfo.IsDisplayed"
          value: true
```

## 6. 最佳实践

1. 变量命名
   - 使用驼峰命名法
   - 名称要有意义，避免使用无意义的缩写
   - 数组类型变量建议使用复数形式

2. 条件表达式
   - 优先使用简单条件
   - 复杂条件建议拆分成多个规则
   - 注意处理空值情况

3. 动作配置
   - 每个动作只做一件事
   - 合理使用动作类型
   - 注意数据类型的一致性

4. 错误处理
   - 必填字段要做非空校验
   - 数值计算要考虑除零等异常情况
   - 数组操作要考虑越界问题