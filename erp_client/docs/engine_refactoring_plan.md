# 流程引擎和规则引擎重构计划

## 重构目标

1. 统一代码结构，为后续的引擎逻辑和YAML格式统一奠定基础
2. 保持现有功能和逻辑不变
3. 消除重复实现
4. 提高代码质量

## 重构原则

1. 不修改现有逻辑
2. 不添加新功能
3. 专注于删除重复代码
4. 保持文件结构稳定
5. 保持向后兼容

## 重构步骤

### 阶段一：删除流程引擎重复代码（进行中）

1. 变量处理相关：
   - ✅ 统一运行时实现：
     - 使用 framework 的 Runtime 作为基础实现
     - 通过 FrameworkRuntimeAdapter 适配到 IRuntime 接口
     - 移除重复的运行时实现
   - ✅ 优化变量管理：
     - 使用 RuntimeFactory 统一创建和管理运行时实例
     - 使用 MetadataManager 专门处理元数据
     - VariableManager 专注于变量和元数据管理
   - ✅ 改进代码结构：
     - 将运行时相关实现统一移到 runtime 目录
     - 分离元数据处理职责
     - 统一接口和实现方式
   - ✅ 整合运行时功能：
     - 实现基于 expr-lang/expr 的表达式引擎
     - 统一运行时上下文管理
     - 完善工厂方法支持
   - 🔄 代码复用优化：
     - 第一步：统一接口（进行中）
       1. 在 framework/runtime/variable 中创建统一的 VariableManager 接口
       2. 合并现有的 Manager、Store 和 IRuntime 接口
       3. 简化接口方法，去除重复功能
       4. 添加新的类型系统支持
     
     - 第二步：简化元数据结构（待开始）
       1. 重新设计 Metadata 结构，使用 map 存储变量
       2. 简化 VariableDefinition，整合到新的 Variable 类型
       3. 添加 Schema 支持复杂类型定义
       4. 实现向后兼容的转换方法
     
     - 第三步：实现基础管理器（待开始）
       1. 创建 BaseVariableManager 实现统一接口
       2. 实现所有基础功能
       3. 添加类型转换支持
       4. 添加表达式计算支持
       5. 实现元数据处理
     
     - 第四步：迁移现有代码（待开始）
       1. 更新 FrameworkRuntimeAdapter 使用新接口
       2. 删除 domain/engine/common/runtime/variable 中的重复代码
       3. 创建必要的适配层代码
       4. 实现引擎特有的扩展功能

2. 元数据处理相关：
   - 第一步：创建统一转换器（✅ 已完成）
     1. ✅ 在 application/framework/metadata 中创建统一的转换器接口
     2. ✅ 实现基础的转换器
     3. ✅ 添加字段定义转换功能
     4. ✅ 确保向后兼容性
   
   - 第二步：更新现有实现（✅ 已完成）
     1. ✅ 更新 process/engine/metadata/metadata_manager.go
     2. ✅ 删除重复的转换代码
     3. ✅ 使用统一的转换器
     4. ✅ 保持原有功能不变
   
   - 第三步：更新规则引擎（✅ 已完成）
     1. ✅ 更新 rule/engine/metadata/metadata_handler.go
     2. ✅ 删除重复的转换代码
     3. ✅ 使用统一的转换器
     4. ✅ 保持原有功能不变
   
   - 第四步：清理重复代码（✅ 已完成）
     1. ✅ 删除 yaml_engine.go 中的重复转换代码
     2. ✅ 删除其他位置的重复实现（base_parser.go）
     3. ✅ 统一使用新的转换器
     4. ✅ 验证功能完整性

3. YAML解析相关：
   - 🔄 更新解析器实现（进行中）
     1. ✅ 统一基础解析器接口
     2. ✅ 实现 BaseParserImpl
     3. ✅ 更新 ProcessParser 使用基础实现
     4. ✅ 更新 RuleParser 使用基础实现
     5. ⬜️ 删除重复的解析逻辑
     6. ⬜️ 添加错误处理支持

4. 错误处理相关：
   - ⬜️ 统一使用公共错误处理
   - ⬜️ 删除重复的错误处理代码

### 阶段二：删除规则引擎重复代码（待开始）

1. 变量处理相关：
   - ⬜️ 更新规则引擎使用 VariableManager
   - ⬜️ 删除重复的变量处理逻辑

2. 元数据处理相关：
   - ⬜️ 更新规则引擎使用 BaseMetadataHandler
   - ⬜️ 删除重复的元数据处理逻辑

3. YAML解析相关：
   - ⬜️ 更新规则引擎使用 BaseYAMLParser
   - ⬜️ 删除重复的解析逻辑

4. 错误处理相关：
   - ⬜️ 统一使用公共错误处理
   - ⬜️ 删除重复的错误处理代码

### 阶段三：优化目录结构（待开始）

1. 规范化目录结构：
```
erp_client/
├── application/
│   └── framework/
│       ├── runtime/     (基础运行时实现)
│       └── variable/    (基础变量管理)
└── domain/
    └── engine/
        ├── interfaces/  (接口定义)
        ├── common/      (公共实现)
        │   ├── runtime/    (运行时适配和扩展)
        │   ├── metadata/   (元数据管理)
        │   └── utils/      (工具类)
        ├── process/     (流程引擎)
        └── rule/        (规则引擎)
```

2. 调整引用关系：
   - ⬜️ 确保所有组件正确引用公共实现
   - ⬜️ 检查并修复循环依赖
   - ⬜️ 优化 import 路径

## 当前进展

### 已完成的工作
1. ✅ 变量处理重构
   - 统一运行时接口（IRuntime）
   - 基础运行时实现（framework.Runtime）
   - 运行时适配器（FrameworkRuntimeAdapter）
   - 统一变量管理（VariableManager）
   - 统一元数据管理（MetadataManager）
   - 统一类型转换（TypeConverter）
   - 优化代码结构，消除循环依赖
   - 添加运行时工厂（RuntimeFactory）
   - 实现表达式引擎（ExprEngine）
   - 统一运行时上下文（RuntimeContext）

### 进行中的工作
1. 🔄 变量处理优化
   - 第一步：统一接口（进行中）
     - 设计新的 VariableManager 接口
     - 合并现有接口
     - 简化方法定义
     - 完善类型系统

2. 🔄 元数据处理优化
   - 第一步：创建统一转换器（进行中）
     - 设计转换器接口
     - 实现基础功能
     - 确保兼容性
     - 准备代码迁移

### 下一步计划
1. 完成变量处理优化的第一步
2. 开始简化元数据结构
3. 实现基础变量管理器
4. 进行代码迁移
5. 完成测试和验证

## 验收标准

1. 重复代码显著减少
   - 删除所有重复的变量处理代码
   - 只保留必要的适配层代码
   - 统一使用基础实现

2. 所有功能正常运行
   - 所有现有功能保持不变
   - 所有测试用例通过
   - 没有引入新的bug

3. 代码结构清晰
   - 清晰的职责划分
   - 合理的依赖关系
   - 统一的代码风格

4. 测试覆盖完整
   - 单元测试覆盖所有新代码
   - 集成测试验证功能完整性
   - 性能测试确保无退化

## 风险管理

1. 功能影响
   - 风险：重构过程可能影响现有功能
   - 解决方案：
     - 每个步骤后进行完整测试
     - 保持现有接口不变
     - 增加日志和监控
     - 分步骤进行变更

2. 依赖关系
   - 风险：组件间依赖复杂，可能产生循环依赖
   - 解决方案：
     - 仔细规划依赖关系
     - 使用接口解耦
     - 合理放置代码位置
     - 定期检查依赖图

3. 性能影响
   - 风险：重构可能导致性能下降
   - 解决方案：
     - 进行性能基准测试
     - 监控关键指标
     - 优化关键路径
     - 保持资源使用效率

4. 代码质量
   - 风险：重构过程可能引入新的问题
   - 解决方案：
     - 遵循代码规范
     - 进行代码审查
     - 使用静态分析工具
     - 保持良好的测试覆盖率 