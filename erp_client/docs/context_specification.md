# 上下文规范 v1.0

## 1. 概述

本文档定义了流程引擎和规则引擎的上下文规范，包括上下文类型、数据结构、访问规则等。本规范直接对应 `process/core` 和 `rule/model` 包的实现能力。

## 2. 通用规范

### 2.1 Store 接口

所有上下文都必须通过 Store 接口访问变量：

```go
type Store interface {
    GetData() map[string]interface{}
    GetValue(path string) (interface{}, error)
    SetValue(path string, value interface{}) error
    GetTypeInfo(path string) (*TypeInfo, error)
    SetTypeInfo(path string, info TypeInfo)
    GetModified() map[string]interface{}
    GetDebugLogs() []string
}
```

### 2.2 基础约束

1. 变量访问：
   - 必须通过 Store 接口访问
   - 遵循变量存储规范中的路径规则
   - 保证类型安全

2. 错误处理：
   - 所有错误必须向上传播
   - 必须包含详细的错误信息
   - 支持错误恢复

3. 调试支持：
   - 记录所有操作日志
   - 跟踪值的变化
   - 支持上下文克隆

## 3. 流程上下文 (ProcessContext)

### 3.1 接口定义

```go
type ProcessContext interface {
    GetProcessID() string
    GetStore() Store
}
```

### 3.2 数据结构

1. 系统变量：
   - current_date: 当前日期
   - current_time: 当前时间
   - current_week: 当前星期
   - current_hour: 当前小时

2. 输入参数：
   - 支持任意JSON格式数据
   - 参数名必须唯一
   - 支持必需参数验证

3. VO对象：
   - 支持自定义VO类型
   - 支持VO间引用
   - 支持数组类型

### 3.3 使用约束

1. 流程定义：
   - 必须指定流程ID
   - 输入参数必须声明
   - VO对象必须预定义

2. 变量访问：
   - 系统变量只读
   - 输入参数只读
   - VO对象可读写

3. 生命周期：
   - 创建：流程启动时
   - 销毁：流程结束时
   - 不支持跨流程共享

## 4. 规则上下文 (RuleContext)

### 4.1 接口定义

```go
type RuleContext struct {
    store Store
}
```

### 4.2 特有功能

1. 类型转换：
```go
ConvertValue(value interface{}, targetType ValueType) (interface{}, error)
ConvertToType(value interface{}, targetType reflect.Type) (interface{}, error)
```

2. 便捷访问：
```go
GetString(path string) (string, error)
GetInt(path string) (int64, error)
GetFloat(path string) (float64, error)
GetBool(path string) (bool, error)
GetTime(path string) (time.Time, error)
GetObject(path string) (map[string]interface{}, error)
GetArray(path string) ([]interface{}, error)
```

### 4.3 使用约束

1. 数据访问：
   - 支持所有命名空间
   - 支持类型安全访问
   - 支持值转换

2. 上下文操作：
   - 支持克隆
   - 支持合并
   - 支持调试日志

3. 生命周期：
   - 创建：规则评估开始
   - 销毁：规则评估结束
   - 支持多规则共享

## 5. 类型定义示例

### 5.1 流程上下文类型

```yaml
# VO类型定义
RoomVO:
  type: "vo"
  fields:
    BaseInfo:
      type: "vo"
      required: true
      fields:
        Id: 
          type: "string"
          required: true
        Name: 
          type: "string"
          required: true
        Status: 
          type: "string"
          required: true

# 输入参数定义
Input:
  RoomId:
    type: "string"
    required: true
  Operation:
    type: "string"
    required: true
```

### 5.2 规则上下文类型

```yaml
# 条件类型定义
Condition:
  type: "vo"
  fields:
    Field:
      type: "string"
      required: true
    Operator:
      type: "string"
      required: true
    Value:
      type: "any"
      required: true

# 动作类型定义
Action:
  type: "vo"
  fields:
    Type:
      type: "string"
      required: true
    Target:
      type: "string"
      required: true
    Parameters:
      type: "map"
      required: false
``` 