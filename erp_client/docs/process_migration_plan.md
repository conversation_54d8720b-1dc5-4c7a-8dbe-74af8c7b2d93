# Process 模块 DDD 迁移计划

## 背景
为了更好地符合 DDD 分层架构，需要将 Process 模块的实现从 framework 层迁移到 domain 层。这个迁移需要谨慎进行，确保不影响现有业务功能。

## 迁移原则
1. 一次只迁移一个文件
2. 保持向后兼容
3. 优先迁移基础类型和接口
4. 每次迁移后进行充分测试
5. 确保所有引用都被正确更新

## 迁移清单

### 已完成
- [x] 1. `framework/process/status.go` -> `domain/process/model/status.go`
   - 完全迁移到 domain 层
   - 删除了 framework 层文件
   - 更新了相关引用

- [x] 2. `framework/process/core/types.go` -> `domain/process/model/core_types.go`
   - 完全迁移到 domain 层
   - 删除了 framework 层文件
   - 所有引用改为直接使用 domain 层的定义

- [x] 3. `framework/process/core/context.go` -> `domain/process/model/core_context.go`
   - 完全迁移到 domain 层
   - 删除了 framework 层文件
   - 保留了对 framework/variable 包的依赖

- [x] 4. `framework/process/context.go` -> `domain/process/model/context.go`
   - 完全迁移到 domain 层
   - 删除了 framework 层文件
   - 重命名为 ExtendedProcessContext 以更好地表达其用途
   - 实现了 ProcessContext 接口

- [x] 5. `framework/process/step.go` -> `domain/process/model/step.go`
   - 完全迁移到 domain 层
   - 创建了基础步骤实现 BaseStep
   - 保持了接口的简洁性和清晰性
   - 删除了 framework 层文件

- [x] 6. `framework/process/flow.go` -> `domain/process/model/flow.go`
   - 完全迁移到 domain 层
   - 创建了基础流程实现 BaseFlow
   - 简化了接口设计，移除了不必要的抽象层
   - 增强了流程执行的功能，支持步骤间数据传递
   - 删除了 framework 层文件

- [x] 7. `framework/process/yaml/types/process.go` -> `domain/process/model/yaml_types.go`
   - 完全迁移到 domain 层
   - 添加了更多的类型定义和常量
   - 改进了类型的文档注释
   - 删除了 framework 层文件
   - 添加了 YAMLField 类型和相关转换方法，优化了 YAML 解析和类型转换
   - 改进了字段定义的类型系统，使其更加类型安全

- [x] 8. `framework/process/yaml/engine.go` -> `domain/process/engine/yaml_engine.go`
   - 完全迁移到 domain 层
   - 保持了所有功能和逻辑不变
   - 更新了必要的导入包
   - 修复了代码中的引用问题
   - 删除了 framework 层文件
   - 更新了所有使用此包的服务的引用路径

## 迁移完成总结
1. 所有 Process 模块的代码都已经成功迁移到 domain 层
2. 保持了所有功能的完整性和向后兼容性
3. 更新了所有相关引用
4. 删除了 framework 层的旧文件
5. 优化了部分接口设计，使其更符合 DDD 原则
6. 完善了文档和类型注释
7. 处理了与 framework/variable 包的依赖关系

## 注意事项
1. 确保每次迁移后业务功能正常
2. 保持代码结构清晰
3. 遵循 DDD 最佳实践
4. 及时更新文档
5. 处理好与 framework/variable 包的依赖关系

## 回滚计划
如果迁移过程中发现问题：
1. 立即恢复原有文件
2. 更新引用回原始路径
3. 记录问题，分析原因
4. 调整迁移策略 