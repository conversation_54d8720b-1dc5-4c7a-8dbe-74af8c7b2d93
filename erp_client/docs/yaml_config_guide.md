# YAML配置文件格式说明

## 一、概述

本文档详细说明了系统中使用的两种YAML配置文件的格式规范：
1. 流程配置文件（Process Configuration）
2. 规则配置文件（Rule Configuration）

## 二、流程配置文件（Process）

### 2.1 基本结构

```yaml
definition:
  id: "process_id"           # 流程唯一标识
  name: "流程名称"           # 流程名称
  description: "流程描述"    # 流程描述

metadata:
  input:
    variables:              # 输入变量定义
      - name: "变量名"      
        required: true      # 是否必填
        fields:            # 对象类型字段定义
          - name: "字段名"
            required: true
  
  output:
    variables:              # 输出变量定义
      - name: "变量名"
        required: true
        fields:
          - name: "字段名"
            required: true
  
  context:
    variables:              # 上下文变量定义
      - name: "变量名"
        required: true
        fields:
          - name: "字段名"
  
  system:
    variables:              # 系统变量定义
      - name: "current_date"
        required: true
        expression: "formatTime(now(), '2006-01-02')"

steps:                      # 流程步骤定义
  - id: "step_id"          # 步骤唯一标识
    name: "步骤名称"        # 步骤名称
    condition: "条件表达式"  # 执行条件
    action:                # 步骤动作
      type: "service"      # 动作类型
      service: "服务名"     # 服务名称
      method: "方法名"      # 方法名称
      params:             # 方法参数
        - "参数1"
        - "参数2"
      output:             # 输出映射
        target: "目标变量"
        mapping:          # 字段映射
          field1: "result.field1"
          field2: "result.field2"
```

### 2.2 变量定义规范

1. **命名空间**：
   - `input`: 输入变量
   - `output`: 输出变量
   - `context`: 上下文变量
   - `system`: 系统变量

2. **变量定义属性**：
   ```yaml
   - name: "变量名"        # 必填，变量名称
     required: true       # 可选，是否必填
     is_array: false     # 可选，是否为数组
     fields:             # 可选，对象字段定义
       - name: "字段名"
         required: true
     default_value: ""   # 可选，默认值表达式
   ```

3. **变量访问语法**：
   - 直接访问：`input.变量名`
   - 对象属性：`input.变量名.属性名`
   - 数组访问：`input.变量名[索引]`
   - 数组遍历：`input.变量名.*`

### 2.3 步骤定义规范

1. **步骤基本属性**：
   ```yaml
   - id: "step_id"        # 必填，步骤唯一标识
     name: "步骤名称"      # 必填，步骤名称
     condition: "条件表达式" # 可选，执行条件
   ```

2. **动作类型**：
   - `service`: 调用服务
   - `set_field`: 设置字段值
   - `calculate`: 计算表达式
   - `rule`: 应用规则

3. **服务调用示例**：
   ```yaml
   action:
     type: "service"
     service: "roomService"
     method: "GetRoom"
     params:
       - "input.reqDto.RoomId"
     output:
       target: "context.roomVO.BaseInfo"
       mapping:
         Id: "result.Id"
         Name: "result.Name"
   ```

## 三、规则配置文件（Rule）

### 3.1 基本结构

```yaml
definition:
  id: "rule_group_id"     # 规则组唯一标识
  name: "规则组名称"        # 规则组名称
  description: "规则组描述" # 规则组描述

metadata:
  input:
    variables:            # 输入变量定义
      - name: "变量名"
        required: true
        fields:
          - name: "字段名"
            required: true
  
  output:
    variables:            # 输出变量定义
      - name: "变量名"
        required: true
        fields:
          - name: "字段名"
            required: true

rules:                    # 规则列表
  - id: "rule_id"        # 规则唯一标识
    name: "规则名称"       # 规则名称
    priority: 100        # 规则优先级
    condition: "条件表达式" # 规则条件
    actions:             # 规则动作
      - type: "动作类型"
        params:          # 动作参数
          target: "目标变量"
          value: "值"
```

### 3.2 条件表达式规范

1. **基本操作符**：
   - 比较：`==`、`!=`、`>`、`<`、`>=`、`<=`
   - 逻辑：`&&`（与）、`||`（或）
   - 空值：`!= nil`（不要使用exists函数）

2. **示例**：
   ```yaml
   # 字符串比较
   condition: "input.room.status == 'IDLE'"
   
   # 数值比较
   condition: "input.price > 100"
   
   # 复合条件
   condition: "input.room.status == 'IN_USE' && input.room.currentOrder != nil"
   ```

### 3.3 动作定义规范

1. **动作类型**：
   - `set`: 设置变量值
   - `calculate`: 计算表达式

2. **动作参数**：
   ```yaml
   actions:
     - type: "set"
       params:
         target: "output.statusResult.displayStatus"
         value: "空闲"
   ```

### 3.4 内置函数

1. **时间函数**：
   - `now()`: 获取当前时间
   - `formatTime(时间, 格式)`: 格式化时间

2. **数组函数**：
   - `len()`: 获取数组长度

3. **示例**：
   ```yaml
   expression: "formatTime(now(), '2006-01-02')"
   ```

## 四、验证服务（Validate Service）

### 4.1 基本概念

验证服务用于对流程中的数据进行验证，支持多种验证规则类型。验证失败时会返回相应的错误信息。

### 4.2 验证规则类型

1. **必填验证 (required)**
   ```yaml
   - type: "required"
     field: "input.reqDto.RoomId"    # 要验证的字段路径
     field_name: "房间ID"            # 字段显示名称
     error_message: "房间ID不能为空"  # 错误提示
     error_code: "ROOM_ID_REQUIRED"  # 错误码
   ```

2. **枚举验证 (enum)**
   ```yaml
   - type: "enum"
     field: "input.reqDto.Status"
     field_name: "状态"
     error_message: "状态值无效"
     error_code: "INVALID_STATUS"
     params:
       values:                      # 允许的枚举值列表
         - "IDLE"
         - "IN_USE"
         - "MAINTENANCE"
   ```

3. **数值范围验证 (number_range)**
   ```yaml
   - type: "number_range"
     field: "input.reqDto.Price"
     field_name: "价格"
     error_message: "价格必须在0-1000之间"
     error_code: "PRICE_OUT_OF_RANGE"
     params:
       min: 0                      # 最小值
       max: 1000                   # 最大值
   ```

4. **字符串长度验证 (string_length)**
   ```yaml
   - type: "string_length"
     field: "input.reqDto.Name"
     field_name: "名称"
     error_message: "名称长度必须在2-10个字符之间"
     error_code: "NAME_LENGTH_INVALID"
     params:
       min: 2                      # 最小长度
       max: 10                     # 最大长度
   ```

5. **正则表达式验证 (pattern)**
   ```yaml
   - type: "pattern"
     field: "input.reqDto.Phone"
     field_name: "手机号"
     error_message: "手机号格式不正确"
     error_code: "PHONE_FORMAT_INVALID"
     params:
       pattern: "^1[3-9]\\d{9}$"   # 正则表达式
   ```

6. **比较验证 (compare)**
   ```yaml
   - type: "compare"
     field: "input.reqDto.EndTime"
     field_name: "结束时间"
     error_message: "结束时间必须大于开始时间"
     error_code: "INVALID_TIME_RANGE"
     params:
       operator: ">"               # 比较操作符: ==, !=, >, <, >=, <=
       target: "input.reqDto.StartTime"
   ```

7. **互斥验证 (exclusive)**
   ```yaml
   - type: "exclusive"
     field: "input.reqDto"
     field_name: "预订信息"
     error_message: "固定时段和自定义时段只能选择其中一个"
     error_code: "MUTUALLY_EXCLUSIVE"
     params:
       values:
         fixed_period: "input.reqDto.FixedPeriod"
         custom_period: "input.reqDto.CustomPeriod"
   ```

### 4.3 在流程中使用验证

1. **单个验证规则**
   ```yaml
   steps:
     - id: "validate_room_id"
       name: "验证房间ID"
       action:
         type: "validate"
         rules:
           - type: "required"
             field: "input.reqDto.RoomId"
             field_name: "房间ID"
             error_message: "房间ID不能为空"
             error_code: "ROOM_ID_REQUIRED"
         error_handler:
           type: "early_return"
           http_code: 400
           error_message: "房间参数验证失败"
           error_code: "ROOM_VALIDATION_ERROR"
   ```

2. **多个验证规则**
   ```yaml
   steps:
     - id: "validate_booking"
       name: "验证预订信息"
       action:
         type: "validate"
         rules:
           - type: "required"
             field: "input.reqDto.RoomId"
             field_name: "房间ID"
             error_message: "房间ID不能为空"
             error_code: "ROOM_ID_REQUIRED"
           
           - type: "pattern"
             field: "input.reqDto.Phone"
             field_name: "手机号"
             error_message: "手机号格式不正确"
             error_code: "PHONE_FORMAT_INVALID"
             params:
               pattern: "^1[3-9]\\d{9}$"
           
           - type: "compare"
             field: "input.reqDto.EndTime"
             field_name: "结束时间"
             error_message: "结束时间必须大于开始时间"
             error_code: "INVALID_TIME_RANGE"
             params:
               operator: ">"
               target: "input.reqDto.StartTime"
         error_handler:
           type: "early_return"
           http_code: 400
           error_message: "预订参数验证失败"
           error_code: "BOOKING_VALIDATION_ERROR"
   ```

### 4.4 错误处理

1. **提前返回 (early_return)**
   ```yaml
   error_handler:
     type: "early_return"          # 验证失败时立即返回错误
     http_code: 400               # HTTP状态码
     error_message: "验证失败"     # 错误信息
     error_code: "VALIDATION_ERROR" # 错误码
   ```

2. **继续执行 (continue)**
   ```yaml
   error_handler:
     type: "continue"             # 验证失败时继续执行
     target: "context.errors"     # 错误信息保存位置
     mapping:                     # 错误信息映射
       message: "error_message"
       code: "error_code"
   ```

### 4.5 最佳实践

1. **验证规则命名规范**
   - 使用有意义的错误码，便于定位问题
   - 错误信息应该清晰明确，指出具体问题
   - 字段名称使用用户友好的展示名称

2. **验证顺序**
   - 必填验证放在最前面
   - 格式验证其次
   - 业务规则验证最后

3. **错误处理选择**
   - 参数验证通常使用early_return
   - 业务规则验证可以使用continue收集多个错误

4. **性能考虑**
   - 合理组织验证规则，避免不必要的验证
   - 对于关联验证，确保数据已经加载

## 五、配置示例

### 5.1 流程配置示例

```yaml
definition:
  id: "room_open_view_process"
  name: "房间开放视图流程"
  description: "获取房间开放视图所需的各种信息"

metadata:
  input:
    variables:
      - name: "reqDto"
        required: true
        fields:
          - name: "RoomId"
          - name: "VenueId"
  output:
    variables:
      - name: "roomVO"
        required: true
        fields:
          - name: "BaseInfo"
          - name: "AreaVO"

steps:
  - id: "fetch_room_info"
    name: "获取房间基础信息"
    condition: "input.reqDto.RoomId != ''"
    action:
      type: "service"
      service: "roomService"
      method: "GetRoom"
      params:
        - "input.reqDto.RoomId"
      output:
        target: "context.roomVO.BaseInfo"
        mapping:
          Id: "result.Id"
          Name: "result.Name"
```

### 5.2 规则配置示例

```yaml
definition:
  id: "room_status_rules"
  name: "房间状态规则组"
  description: "房间状态规则配置"

metadata:
  input:
    variables:
      - name: "room"
        required: true
        fields:
          - name: "status"
            required: true
          - name: "currentOrder"
            required: false

rules:
  - id: "check_room_idle"
    name: "检查房间空闲状态"
    priority: 100
    condition: "input.room.status == 'IDLE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "空闲"
      - type: "set"
        params:
          target: "output.statusResult.color"
          value: "green"
``` 