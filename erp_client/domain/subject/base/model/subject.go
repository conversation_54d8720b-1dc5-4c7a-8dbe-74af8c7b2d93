package model

// Subject 主体接口
type Subject interface {
	// GetID 获取主体ID
	GetID() string
	// GetType 获取主体类型
	GetType() string
	// GetState 获取主体状态
	GetState() int
	// GetVersion 获取主体版本
	GetVersion() int
	// Validate 验证主体
	Validate() error
	// Clone 克隆主体
	Clone() Subject
}

// BaseSubject 基础主体实现
type BaseSubject struct {
	ID      string `json:"id"`      // 主体ID
	Type    string `json:"type"`    // 主体类型
	State   int    `json:"state"`   // 主体状态
	Version int    `json:"version"` // 主体版本
}

// GetID 获取主体ID
func (s *BaseSubject) GetID() string {
	return s.ID
}

// GetType 获取主体类型
func (s *BaseSubject) GetType() string {
	return s.Type
}

// GetState 获取主体状态
func (s *BaseSubject) GetState() int {
	return s.State
}

// GetVersion 获取主体版本
func (s *BaseSubject) GetVersion() int {
	return s.Version
}

// Validate 验证主体
func (s *BaseSubject) Validate() error {
	// 基础验证逻辑
	return nil
}

// Clone 克隆主体
func (s *BaseSubject) Clone() Subject {
	return &BaseSubject{
		ID:      s.ID,
		Type:    s.Type,
		State:   s.State,
		Version: s.Version,
	}
}
