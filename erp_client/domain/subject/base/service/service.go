package service

import (
	"context"
	"voderpltvv/erp_client/domain/subject/base/model"
)

// Service ERP客户端主体服务接口
type Service interface {
	// Identify 身份识别
	Identify(ctx context.Context, credentials map[string]interface{}) (model.Subject, error)
	// Authorize 授权验证
	Authorize(ctx context.Context, subject model.Subject, resource string, action string) (bool, error)
	// GetProfile 信息获取
	GetProfile(ctx context.Context, id string) (model.Subject, error)
	// Query 查询主体
	Query(ctx context.Context, condition map[string]interface{}) ([]model.Subject, error)
}
