package repository

import (
	"context"
)

// Repository 主体仓储接口
type Repository[T any] interface {
	// Update 更新主体
	Update(ctx context.Context, entity T) error
	// Delete 删除主体
	Delete(ctx context.Context, id string) error
	// FindByID 根据ID查询主体
	FindByID(ctx context.Context, id string) (T, error)
	// FindByCondition 根据条件查询主体
	FindByCondition(ctx context.Context, condition map[string]interface{}) ([]T, error)
}
