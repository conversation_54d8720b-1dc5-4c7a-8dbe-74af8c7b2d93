package test

import (
	"context"
	"errors"
	"os"
	"testing"

	baseModel "voderpltvv/erp_client/domain/subject/base/model"
	"voderpltvv/erp_client/domain/subject/business/venue/model"
	"voderpltvv/erp_client/domain/subject/business/venue/repository"
	"voderpltvv/erp_client/domain/subject/business/venue/service"
	infraRepo "voderpltvv/erp_client/infrastructure/persistence/repository"
	testUtils "voderpltvv/test"

	"github.com/k0kubun/pp/v3"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// VenueRepository 场所仓储接口的Mock实现
type VenueRepository struct {
	*testUtils.MockRepository[model.Venue]
}

func (m *VenueRepository) FindByID(ctx context.Context, id string) (model.Venue, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(model.Venue), args.Error(1)
}

func (m *VenueRepository) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]model.Venue, error) {
	args := m.Called(ctx, condition)
	return args.Get(0).([]model.Venue), args.Error(1)
}

func (m *VenueRepository) Save(ctx context.Context, venue model.Venue) error {
	args := m.Called(ctx, venue)
	return args.Error(0)
}

func (m *VenueRepository) Update(ctx context.Context, venue model.Venue) error {
	args := m.Called(ctx, venue)
	return args.Error(0)
}

func (m *VenueRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *VenueRepository) FindByName(ctx context.Context, name string) (model.Venue, error) {
	args := m.Called(ctx, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(model.Venue), args.Error(1)
}

func (m *VenueRepository) FindEnabled(ctx context.Context) ([]model.Venue, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.Venue), args.Error(1)
}

// CreateTestVenue 创建测试场所
func CreateTestVenue(id string, enabled bool) model.Venue {
	return model.NewVenue(
		id,
		"Test Venue",
		"Test Address",
		"123456",
		"9-5",
		enabled,
	)
}

// NewTestRepository 创建测试用的仓储实例
func NewTestRepository(useMock bool, t *testing.T) repository.Repository {
	if useMock {
		mockRepo := new(testUtils.MockRepository[model.Venue])
		return &VenueRepository{MockRepository: mockRepo}
	}

	// 获取测试事务
	tx := testUtils.GetTestTx(t)

	// 返回真实的仓储实现
	return infraRepo.NewVenueRepository(tx)
}

// TestMain 是 Go 测试框架的特殊入口函数，会在执行测试包中的任何测试之前自动被调用。
// 它负责初始化测试环境（如数据库连接）并在所有测试完成后进行清理。
// m.Run() 会执行包中的所有测试函数，并返回退出码。
func TestMain(m *testing.M) {
	// 初始化测试环境，主要用于使用真实数据库的测试
	// 注意：使用 ServiceTestSuite 的测试会在 Setup() 中再次初始化
	testUtils.InitTestEnv()

	code := m.Run()

	// 清理测试环境资源
	testUtils.CleanupTestEnv()

	os.Exit(code)
}

func TestServiceImpl_GetVenue(t *testing.T) {
	// 使用真实数据库进行测试
	useMock := false

	var suite *testUtils.ServiceTestSuite[service.Service, model.Venue]
	var tx *gorm.DB

	if useMock {
		suite = testUtils.NewServiceTestSuite[service.Service, model.Venue](t, func(repo *testUtils.MockRepository[model.Venue]) service.Service {
			venueRepo := &VenueRepository{MockRepository: repo}
			return service.NewService(venueRepo)
		})
	} else {
		// 使用真实数据库时的设置
		tx = testUtils.GetTestTx(t)
		if tx == nil {
			t.Fatal("Failed to get test transaction")
		}
		realRepo := infraRepo.NewVenueRepository(tx)
		suite = testUtils.NewRealServiceTestSuite[service.Service, model.Venue](t, service.NewService(realRepo))
	}

	defer func() {
		if r := recover(); r != nil {
			t.Errorf("Test panicked: %v", r)
		}
		suite.TearDown()
		if !useMock && tx != nil {
			if err := tx.Rollback().Error; err != nil {
				t.Errorf("Failed to rollback test data: %v", err)
			}
		}
	}()

	suite.Setup()

	// 使用已存在的场所数据进行测试
	existingVenue := model.NewVenue(
		"105497",
		"台北纯K(打浦桥店)",
		"徐家汇路618号日月光中心三楼台北纯K",
		"贠磊",
		"06:00",
		false, // state = 0
	)

	testCases := []struct {
		name        string
		id          string
		setupMock   func()
		expectVenue model.Venue
		expectError error
	}{
		{
			name: "成功获取场所",
			id:   "105497",
			setupMock: func() {
				if useMock {
					suite.MockRepo().On("FindByID", suite.Context(), "105497").Return(existingVenue, nil)
				}
			},
			expectVenue: existingVenue,
			expectError: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testUtils.LogTestCase(t, tc.name, "Input ID: "+tc.id)

			tc.setupMock()
			venue, err := suite.Service().GetVenue(suite.Context(), tc.id)
			// 打印venue
			pp.Print(venue)
			testUtils.AssertError(t, tc.expectError, err)
			if err != nil {
				return
			}

			if venue == nil {
				t.Fatal("Received nil venue from service")
			}

			venueID := venue.GetID()
			if venueID == "" {
				t.Fatal("Venue ID is empty")
			}

			pp.Println("Retrieved venue ID:", venueID)
			assert.Equal(t, tc.expectVenue.GetID(), venueID)
		})
	}
}

func TestServiceImpl_Identify(t *testing.T) {
	suite := testUtils.NewServiceTestSuite[service.Service, model.Venue](t, func(repo *testUtils.MockRepository[model.Venue]) service.Service {
		venueRepo := &VenueRepository{MockRepository: repo}
		return service.NewService(venueRepo)
	})
	defer suite.TearDown()
	suite.Setup()

	testCases := []struct {
		name          string
		credentials   map[string]any
		setupMock     func()
		expectSubject baseModel.Subject
		expectError   error
	}{
		{
			name: "成功场景-有效场所ID",
			credentials: map[string]any{
				"venue_id": "venue-123",
			},
			setupMock: func() {
				venue := CreateTestVenue("venue-123", true)
				suite.MockRepo().On("FindByID", suite.Context(), "venue-123").Return(venue, nil)
			},
			expectSubject: CreateTestVenue("venue-123", true),
			expectError:   nil,
		},
		{
			name: "失败场景-无效凭证格式",
			credentials: map[string]any{
				"invalid_key": "value",
			},
			setupMock:     func() {},
			expectSubject: nil,
			expectError:   errors.New("invalid credentials"),
		},
		{
			name: "失败场景-场所ID不存在",
			credentials: map[string]any{
				"venue_id": "non-existent",
			},
			setupMock: func() {
				suite.MockRepo().On("FindByID", suite.Context(), "non-existent").Return(nil, errors.New("venue not found"))
			},
			expectSubject: nil,
			expectError:   errors.New("venue not found"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testUtils.LogTestCase(t, tc.name,
				"Credentials: "+testUtils.FormatValue(tc.credentials))

			tc.setupMock()
			subject, err := suite.Service().Identify(suite.Context(), tc.credentials)

			testUtils.AssertError(t, tc.expectError, err)
			assert.Equal(t, tc.expectSubject, subject)
		})
	}
}

func TestServiceImpl_Authorize(t *testing.T) {
	suite := testUtils.NewServiceTestSuite[service.Service, model.Venue](t, func(repo *testUtils.MockRepository[model.Venue]) service.Service {
		venueRepo := &VenueRepository{MockRepository: repo}
		return service.NewService(venueRepo)
	})
	defer suite.TearDown()
	suite.Setup()

	testCases := []struct {
		name         string
		subject      baseModel.Subject
		resource     string
		action       string
		expectResult bool
		expectError  error
	}{
		{
			name:         "enabled venue",
			subject:      CreateTestVenue("venue-123", true),
			resource:     "test-resource",
			action:       "read",
			expectResult: true,
			expectError:  nil,
		},
		{
			name:         "disabled venue",
			subject:      CreateTestVenue("venue-123", false),
			resource:     "test-resource",
			action:       "read",
			expectResult: false,
			expectError:  nil,
		},
		{
			name:         "invalid subject type",
			subject:      &baseModel.BaseSubject{},
			resource:     "test-resource",
			action:       "read",
			expectResult: false,
			expectError:  errors.New("invalid subject type"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testUtils.LogTestCase(t, tc.name,
				"Resource: "+tc.resource,
				"Action: "+tc.action)

			result, err := suite.Service().Authorize(suite.Context(), tc.subject, tc.resource, tc.action)

			testUtils.AssertError(t, tc.expectError, err)
			assert.Equal(t, tc.expectResult, result)
			t.Logf("Authorization result: %v", result)
		})
	}
}
