package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/subject/base/service"
	"voderpltvv/erp_client/domain/subject/business/venue/model"
)

// Service 场所服务接口
type Service interface {
	baseService.Service

	// GetVenue 获取场所信息
	GetVenue(ctx context.Context, id string) (model.Venue, error)
	// FindByName 根据名称查询场所
	FindByName(ctx context.Context, name string) (model.Venue, error)
	// FindEnabled 查询启用的场所
	FindEnabled(ctx context.Context) ([]model.Venue, error)
}
