package service

import (
	"context"
	"errors"
	baseModel "voderpltvv/erp_client/domain/subject/base/model"
	"voderpltvv/erp_client/domain/subject/business/venue/model"
	"voderpltvv/erp_client/domain/subject/business/venue/repository"
)

// serviceImpl 场所服务实现
type serviceImpl struct {
	repo repository.Repository
}

// NewService 创建场所服务
func NewService(repo repository.Repository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// Identify 身份识别
func (s *serviceImpl) Identify(ctx context.Context, credentials map[string]interface{}) (baseModel.Subject, error) {
	// 场所身份识别逻辑
	if venueID, ok := credentials["venue_id"].(string); ok {
		return s.repo.FindByID(ctx, venueID)
	}
	return nil, errors.New("invalid credentials")
}

// Authorize 授权验证
func (s *serviceImpl) Authorize(ctx context.Context, subject baseModel.Subject, resource string, action string) (bool, error) {
	venue, ok := subject.(model.Venue)
	if !ok {
		return false, errors.New("invalid subject type")
	}

	// 场所授权逻辑
	if !venue.IsEnabled() {
		return false, nil
	}

	// TODO: 实现具体的授权逻辑
	return true, nil
}

// GetProfile 信息获取
func (s *serviceImpl) GetProfile(ctx context.Context, id string) (baseModel.Subject, error) {
	return s.repo.FindByID(ctx, id)
}

// Query 查询主体
func (s *serviceImpl) Query(ctx context.Context, condition map[string]interface{}) ([]baseModel.Subject, error) {
	venues, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	subjects := make([]baseModel.Subject, len(venues))
	for i, venue := range venues {
		subjects[i] = venue
	}
	return subjects, nil
}

// GetVenue 获取场所信息
func (s *serviceImpl) GetVenue(ctx context.Context, id string) (model.Venue, error) {
	subject, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if venue, ok := subject.(model.Venue); ok {
		return venue, nil
	}
	return nil, errors.New("invalid subject type")
}

// FindByName 根据名称查询场所
func (s *serviceImpl) FindByName(ctx context.Context, name string) (model.Venue, error) {
	return s.repo.FindByName(ctx, name)
}

// FindEnabled 查询启用的场所
func (s *serviceImpl) FindEnabled(ctx context.Context) ([]model.Venue, error) {
	return s.repo.FindEnabled(ctx)
}
