package model

import (
	baseModel "voderpltvv/erp_client/domain/subject/base/model"
)

// Venue 场所主体接口
type Venue interface {
	baseModel.Subject

	// GetName 获取场所名称
	GetName() string
	// GetAddress 获取场所地址
	GetAddress() string
	// GetContact 获取联系方式
	GetContact() string
	// GetBusinessHours 获取营业时间
	GetBusinessHours() string
	// IsEnabled 是否启用
	IsEnabled() bool
}

// NewVenue 创建场所
func NewVenue(
	id string,
	name string,
	address string,
	contact string,
	businessHours string,
	isEnabled bool,
) Venue {
	return &venueImpl{
		BaseSubject: &baseModel.BaseSubject{
			ID:      id,
			Type:    "venue",
			State:   1,
			Version: 1,
		},
		name:          name,
		address:       address,
		contact:       contact,
		businessHours: businessHours,
		isEnabled:     isEnabled,
	}
}

// venueImpl 场所主体实现
type venueImpl struct {
	*baseModel.BaseSubject
	name          string
	address       string
	contact       string
	businessHours string
	isEnabled     bool
}

// GetName 获取场所名称
func (v *venueImpl) GetName() string {
	return v.name
}

// GetAddress 获取场所地址
func (v *venueImpl) GetAddress() string {
	return v.address
}

// GetContact 获取联系方式
func (v *venueImpl) GetContact() string {
	return v.contact
}

// GetBusinessHours 获取营业时间
func (v *venueImpl) GetBusinessHours() string {
	return v.businessHours
}

// IsEnabled 是否启用
func (v *venueImpl) IsEnabled() bool {
	return v.isEnabled
}

// Validate 验证场所
func (v *venueImpl) Validate() error {
	if err := v.BaseSubject.Validate(); err != nil {
		return err
	}
	// TODO: 实现场所特定的验证逻辑
	return nil
}

// Clone 克隆场所
func (v *venueImpl) Clone() baseModel.Subject {
	return &venueImpl{
		BaseSubject:   v.BaseSubject.Clone().(*baseModel.BaseSubject),
		name:          v.name,
		address:       v.address,
		contact:       v.contact,
		businessHours: v.businessHours,
		isEnabled:     v.isEnabled,
	}
}
