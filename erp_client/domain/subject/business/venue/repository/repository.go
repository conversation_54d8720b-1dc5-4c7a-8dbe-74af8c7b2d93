package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/subject/base/repository"
	"voderpltvv/erp_client/domain/subject/business/venue/model"
)

// Repository 场所仓储接口
type Repository interface {
	baseRepository.Repository[model.Venue]

	// FindByName 根据名称查询场所
	FindByName(ctx context.Context, name string) (model.Venue, error)
	// FindEnabled 查询启用的场所
	FindEnabled(ctx context.Context) ([]model.Venue, error)
}
