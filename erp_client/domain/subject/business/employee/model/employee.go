package model

import (
	baseModel "voderpltvv/erp_client/domain/subject/base/model"
)

// Employee 员工主体接口
type Employee interface {
	baseModel.Subject

	// GetVenueID 获取所属场所ID
	GetVenueID() string
	// GetName 获取员工姓名
	GetName() string
	// GetPosition 获取职位
	GetPosition() string
	// GetPhone 获取联系电话
	GetPhone() string
	// IsEnabled 是否启用
	IsEnabled() bool
	// GetRoles 获取角色列表
	GetRoles() []string
	// HasRole 是否拥有指定角色
	HasRole(role string) bool
	// HasPermission 是否拥有指定权限
	HasPermission(resource string, action string) bool
}

// NewEmployee 创建员工
func NewEmployee(
	id string,
	venueID string,
	name string,
	position string,
	phone string,
	isEnabled bool,
	roles []string,
) Employee {
	return &employeeImpl{
		BaseSubject: &baseModel.BaseSubject{
			ID:      id,
			Type:    "employee",
			State:   1,
			Version: 1,
		},
		venueID:   venueID,
		name:      name,
		position:  position,
		phone:     phone,
		isEnabled: isEnabled,
		roles:     roles,
	}
}

// employeeImpl 员工主体实现
type employeeImpl struct {
	*baseModel.BaseSubject
	venueID   string
	name      string
	position  string
	phone     string
	email     string
	isEnabled bool
	roles     []string
}

// GetVenueID 获取所属场所ID
func (e *employeeImpl) GetVenueID() string {
	return e.venueID
}

// GetName 获取员工姓名
func (e *employeeImpl) GetName() string {
	return e.name
}

// GetPosition 获取职位
func (e *employeeImpl) GetPosition() string {
	return e.position
}

// GetPhone 获取联系电话
func (e *employeeImpl) GetPhone() string {
	return e.phone
}

// IsEnabled 是否启用
func (e *employeeImpl) IsEnabled() bool {
	return e.isEnabled
}

// GetRoles 获取角色列表
func (e *employeeImpl) GetRoles() []string {
	return e.roles
}

// HasRole 是否拥有指定角色
func (e *employeeImpl) HasRole(role string) bool {
	for _, r := range e.roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasPermission 是否拥有指定权限
func (e *employeeImpl) HasPermission(resource string, action string) bool {
	// TODO: 实现权限检查逻辑，可能需要注入权限服务
	return true
}

// Validate 验证员工
func (e *employeeImpl) Validate() error {
	if err := e.BaseSubject.Validate(); err != nil {
		return err
	}
	// TODO: 实现员工特定的验证逻辑
	return nil
}

// Clone 克隆员工
func (e *employeeImpl) Clone() baseModel.Subject {
	roles := make([]string, len(e.roles))
	copy(roles, e.roles)

	return &employeeImpl{
		BaseSubject: e.BaseSubject.Clone().(*baseModel.BaseSubject),
		venueID:     e.venueID,
		name:        e.name,
		position:    e.position,
		phone:       e.phone,
		email:       e.email,
		isEnabled:   e.isEnabled,
		roles:       roles,
	}
}
