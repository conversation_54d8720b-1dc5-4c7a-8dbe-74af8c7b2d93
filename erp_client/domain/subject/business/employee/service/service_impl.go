package service

import (
	"context"
	"errors"
	baseModel "voderpltvv/erp_client/domain/subject/base/model"
	"voderpltvv/erp_client/domain/subject/business/employee/model"
	"voderpltvv/erp_client/domain/subject/business/employee/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// serviceImpl 员工服务实现
type serviceImpl struct {
	repo repository.IEmployeeRepository
}

// NewService 创建员工服务
func NewService(repo repository.IEmployeeRepository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// Identify 身份识别
func (s *serviceImpl) Identify(ctx context.Context, credentials map[string]interface{}) (baseModel.Subject, error) {
	// 员工身份识别逻辑
	if employeeID, ok := credentials["employee_id"].(string); ok {
		return s.repo.FindByID(ctx, employeeID)
	}
	return nil, errors.New("invalid credentials")
}

// Authorize 授权验证
func (s *serviceImpl) Authorize(ctx context.Context, subject baseModel.Subject, resource string, action string) (bool, error) {
	employee, ok := subject.(model.Employee)
	if !ok {
		return false, errors.New("invalid subject type")
	}

	// 员工授权逻辑
	if !employee.IsEnabled() {
		return false, nil
	}

	return employee.HasPermission(resource, action), nil
}

// GetProfile 信息获取
func (s *serviceImpl) GetProfile(ctx context.Context, id string) (baseModel.Subject, error) {
	return s.repo.FindByID(ctx, id)
}

// Query 查询主体
func (s *serviceImpl) Query(ctx context.Context, condition map[string]interface{}) ([]baseModel.Subject, error) {
	employees, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}

	// 转换为Subject类型切片
	subjects := make([]baseModel.Subject, len(employees))
	for i, emp := range employees {
		subjects[i] = emp
	}

	return subjects, nil
}

// GetEmployee 获取员工信息
func (s *serviceImpl) GetEmployee(ctx context.Context, id string) (model.Employee, error) {
	subject, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if employee, ok := subject.(model.Employee); ok {
		return employee, nil
	}
	return nil, errors.New("invalid subject type")
}

// FindByVenueID 根据场所ID查询员工
func (s *serviceImpl) FindByVenueID(ctx context.Context, venueID string) ([]model.Employee, error) {
	return s.repo.FindByVenueID(ctx, venueID)
}

// FindByRole 根据角色查询员工
func (s *serviceImpl) FindByRole(ctx context.Context, role string) ([]model.Employee, error) {
	return s.repo.FindByRole(ctx, role)
}

// FindEnabled 查询启用的员工
func (s *serviceImpl) FindEnabled(ctx context.Context) ([]model.Employee, error) {
	// 使用条件查询过滤已启用的员工
	condition := map[string]interface{}{
		"state": 1, // 假设状态1表示启用
	}
	return s.repo.FindByCondition(ctx, condition)
}

// CheckPermission 检查权限
func (s *serviceImpl) CheckPermission(ctx context.Context, employeeID string, resource string, action string) (bool, error) {
	employee, err := s.GetEmployee(ctx, employeeID)
	if err != nil {
		return false, err
	}

	if !employee.IsEnabled() {
		return false, nil
	}

	return employee.HasPermission(resource, action), nil
}

// FindByID 根据ID查询员工
func (s *serviceImpl) FindByID(ctx context.Context, id string) (*model.Employee, error) {
	employee, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return &employee, nil
}

// FindEmployeeByID 根据ID查询员工
func (s *serviceImpl) FindEmployeeByID(ctx context.Context, id string) (po.Employee, error) {
	employee, err := s.repo.FindEmployeeByID(ctx, id)
	if err != nil {
		return po.Employee{}, err
	}
	return employee, nil
}

// FindEmployeesByIds 根据ID查询员工
func (s *serviceImpl) FindsByIds(ctx context.Context, ids []string) ([]po.Employee, error) {
	return s.repo.FindsByIds(ctx, ids)
}

// ConvertToEmployeeVO 转换为员工VO
func (s *serviceImpl) ConvertToEmployeeVO(ctx context.Context, employee po.Employee) vo.EmployeeVO {
	return s.repo.ConvertToEmployeeVO(ctx, employee)
}

// ConvertToEmployee 转换为员工
func (s *serviceImpl) ConvertToEmployee(ctx context.Context, employeeVO vo.EmployeeVO) po.Employee {
	return s.repo.ConvertToEmployee(ctx, employeeVO)
}
