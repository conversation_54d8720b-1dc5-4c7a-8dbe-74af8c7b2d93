package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/subject/base/service"
	"voderpltvv/erp_client/domain/subject/business/employee/model"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Service 员工服务接口
type Service interface {
	baseService.Service

	// FindByID 根据ID查询员工
	FindByID(ctx context.Context, id string) (*model.Employee, error)
	// GetEmployee 获取员工信息
	GetEmployee(ctx context.Context, id string) (model.Employee, error)
	// FindByVenueID 根据场所ID查询员工
	FindByVenueID(ctx context.Context, venueID string) ([]model.Employee, error)
	// FindByRole 根据角色查询员工
	FindByRole(ctx context.Context, role string) ([]model.Employee, error)
	// FindEnabled 查询启用的员工
	FindEnabled(ctx context.Context) ([]model.Employee, error)
	// CheckPermission 检查权限
	CheckPermission(ctx context.Context, employeeID string, resource string, action string) (bool, error)

	// FindEmployeeByID 根据ID查询员工
	FindEmployeeByID(ctx context.Context, id string) (po.Employee, error)

	// FindEmployeesByIds 根据ID查询员工
	FindsByIds(ctx context.Context, ids []string) ([]po.Employee, error)

	// ConvertToEmployeeVO 转换为员工VO
	ConvertToEmployeeVO(ctx context.Context, employee po.Employee) vo.EmployeeVO
	// ConvertToEmployee 转换为员工
	ConvertToEmployee(ctx context.Context, employeeVO vo.EmployeeVO) po.Employee
}
