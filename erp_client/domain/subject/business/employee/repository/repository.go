package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/subject/base/repository"
	"voderpltvv/erp_client/domain/subject/business/employee/model"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// IEmployeeRepository 员工仓储接口
type IEmployeeRepository interface {
	baseRepository.Repository[model.Employee]

	// FindByVenueID 根据场所ID查询员工
	FindByVenueID(ctx context.Context, venueID string) ([]model.Employee, error)
	// FindByRole 根据角色查询员工
	FindByRole(ctx context.Context, role string) ([]model.Employee, error)
	// FindByID 根据ID查询员工

	// FindEmployeeByID 根据ID查询员工
	FindEmployeeByID(ctx context.Context, id string) (po.Employee, error)

	// FindEmployeesByIds 根据ID查询员工
	FindsByIds(ctx context.Context, ids []string) ([]po.Employee, error)

	// ConvertToEmployeeVO 转换为员工VO
	ConvertToEmployeeVO(ctx context.Context, employee po.Employee) vo.EmployeeVO
	// ConvertToEmployee 转换为员工
	ConvertToEmployee(ctx context.Context, employeeVO vo.EmployeeVO) po.Employee
}
