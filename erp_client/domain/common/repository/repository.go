package repository

import (
	"context"
	"voderpltvv/erp_client/domain/common/model"
)

// Repository 基础仓储接口
type Repository interface {
	Create(ctx context.Context, entity model.Entity) error
	Update(ctx context.Context, entity model.Entity) error
	Delete(ctx context.Context, id string) error
	FindByID(ctx context.Context, id string) (model.Entity, error)
	FindByCondition(ctx context.Context, condition map[string]interface{}) ([]model.Entity, error)
}
