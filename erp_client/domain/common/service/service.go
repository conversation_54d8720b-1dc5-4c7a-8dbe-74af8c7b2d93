package service

import (
	"context"
	"voderpltvv/erp_client/domain/common/model"
)

// Service 基础服务接口
type Service interface {
	Create(ctx context.Context, entity model.Entity) error
	Update(ctx context.Context, entity model.Entity) error
	Delete(ctx context.Context, id string) error
	Get(ctx context.Context, id string) (model.Entity, error)
	List(ctx context.Context, condition map[string]interface{}) ([]model.Entity, error)
}
