package repository

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/base/model"
)

// Repository 配置仓储接口
type Repository interface {
	// Create 创建配置
	Create(ctx context.Context, config model.Config) error
	// Update 更新配置
	Update(ctx context.Context, config model.Config) error
	// Delete 删除配置
	Delete(ctx context.Context, id string) error
	// FindByID 根据ID查询配置
	FindByID(ctx context.Context, id string) (model.Config, error)
	// FindByType 根据类型查询配置
	FindByType(ctx context.Context, configType string) ([]model.Config, error)
	// FindByKey 根据键查询配置
	FindByKey(ctx context.Context, key string) (model.Config, error)
	// FindEnabled 查询启用的配置
	FindEnabled(ctx context.Context) ([]model.Config, error)
	// FindByVersion 根据版本号查询配置
	FindByVersion(ctx context.Context, version int64) ([]model.Config, error)
	// FindByCondition 根据条件查询配置
	FindByCondition(ctx context.Context, condition map[string]interface{}) ([]model.Config, error)
}
