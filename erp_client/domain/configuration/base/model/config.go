package model

import (
	"errors"
	"time"
)

// Config 配置接口
type Config interface {
	// GetID 获取配置ID
	GetID() string
	// GetType 获取配置类型
	GetType() string
	// GetKey 获取配置键
	GetKey() string
	// GetValue 获取配置值
	GetValue() interface{}
	// GetVersion 获取版本号
	GetVersion() int64
	// GetCreatedAt 获取创建时间
	GetCreatedAt() time.Time
	// GetUpdatedAt 获取更新时间
	GetUpdatedAt() time.Time
	// IsEnabled 是否启用
	IsEnabled() bool
	// Validate 验证配置
	Validate() error
	// Clone 克隆配置
	Clone() Config
}

// BaseConfig 基础配置实现
type BaseConfig struct {
	ID        string      `json:"id"`
	Type      string      `json:"type"`
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	Version   int64       `json:"version"`
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
	Enabled   bool        `json:"enabled"`
}

// GetID 获取配置ID
func (c *BaseConfig) GetID() string {
	return c.ID
}

// GetType 获取配置类型
func (c *BaseConfig) GetType() string {
	return c.Type
}

// GetKey 获取配置键
func (c *BaseConfig) GetKey() string {
	return c.Key
}

// GetValue 获取配置值
func (c *BaseConfig) GetValue() interface{} {
	return c.Value
}

// GetVersion 获取版本号
func (c *BaseConfig) GetVersion() int64 {
	return c.Version
}

// GetCreatedAt 获取创建时间
func (c *BaseConfig) GetCreatedAt() time.Time {
	return c.CreatedAt
}

// GetUpdatedAt 获取更新时间
func (c *BaseConfig) GetUpdatedAt() time.Time {
	return c.UpdatedAt
}

// IsEnabled 是否启用
func (c *BaseConfig) IsEnabled() bool {
	return c.Enabled
}

// Validate 验证配置
func (c *BaseConfig) Validate() error {
	if c.ID == "" {
		return ErrInvalidID
	}
	if c.Type == "" {
		return ErrInvalidType
	}
	if c.Key == "" {
		return ErrInvalidKey
	}
	if c.Value == nil {
		return ErrInvalidValue
	}
	return nil
}

// Clone 克隆配置
func (c *BaseConfig) Clone() Config {
	return &BaseConfig{
		ID:        c.ID,
		Type:      c.Type,
		Key:       c.Key,
		Value:     c.Value,
		Version:   c.Version,
		CreatedAt: c.CreatedAt,
		UpdatedAt: c.UpdatedAt,
		Enabled:   c.Enabled,
	}
}

// 错误定义
var (
	ErrInvalidID              = errors.New("invalid config id")
	ErrInvalidType            = errors.New("invalid config type")
	ErrInvalidKey             = errors.New("invalid config key")
	ErrInvalidValue           = errors.New("invalid config value")
	ErrInvalidName            = errors.New("invalid name")
	ErrInvalidStartTime       = errors.New("invalid start time")
	ErrInvalidEndTime         = errors.New("invalid end time")
	ErrInvalidTimeRange       = errors.New("invalid time range")
	ErrInvalidDayOfWeek       = errors.New("invalid day of week")
	ErrInvalidTimeRanges      = errors.New("invalid time ranges")
	ErrInvalidRoomTypes       = errors.New("invalid room types")
	ErrInvalidStartDate       = errors.New("invalid start date")
	ErrInvalidEndDate         = errors.New("invalid end date")
	ErrInvalidDateRange       = errors.New("invalid date range")
	ErrInvalidPriceAdjustment = errors.New("invalid price adjustment")
	ErrInvalidVenueID         = errors.New("invalid venue id")
	ErrInvalidCapacity        = errors.New("invalid capacity")
	ErrInvalidMinCharge       = errors.New("invalid min charge")
	ErrInvalidHourPrice       = errors.New("invalid hour price")
	ErrInvalidServiceFee      = errors.New("invalid service fee")
)
