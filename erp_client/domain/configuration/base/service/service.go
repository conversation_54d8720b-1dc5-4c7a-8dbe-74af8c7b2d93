package service

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/base/model"
)

// Service 配置服务接口
type Service interface {
	// Create 创建配置
	Create(ctx context.Context, config model.Config) error
	// Update 更新配置
	Update(ctx context.Context, config model.Config) error
	// Delete 删除配置
	Delete(ctx context.Context, id string) error
	// Get 获取配置
	Get(ctx context.Context, id string) (model.Config, error)
	// Query 查询配置
	Query(ctx context.Context, condition map[string]interface{}) ([]model.Config, error)
	// GetByKey 根据键获取配置
	GetByKey(ctx context.Context, key string) (model.Config, error)
	// GetByType 根据类型获取配置
	GetByType(ctx context.Context, configType string) ([]model.Config, error)
	// GetEnabled 获取启用的配置
	GetEnabled(ctx context.Context) ([]model.Config, error)
	// GetByVersion 根据版本号获取配置
	GetByVersion(ctx context.Context, version int64) ([]model.Config, error)
	// Enable 启用配置
	Enable(ctx context.Context, id string) error
	// Disable 禁用配置
	Disable(ctx context.Context, id string) error
	// Validate 验证配置
	Validate(ctx context.Context, config model.Config) error
}
