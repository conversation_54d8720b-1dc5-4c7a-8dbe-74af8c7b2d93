package service

import (
	"context"
	"time"

	bookingerror "voderpltvv/erp_client/domain/configuration/business/booking/error"
	"voderpltvv/erp_client/domain/configuration/business/booking/model"
	"voderpltvv/erp_client/domain/configuration/business/booking/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"
)

// BookingServiceImpl 预订服务实现
type BookingServiceImpl struct {
	repository repository.Repository
}

// NewBookingService 创建预订服务
func NewBookingService(repository repository.Repository) BookingService {
	return &BookingServiceImpl{
		repository: repository,
	}
}

// UpdateBookingStatus 更新预订状态
func (s *BookingServiceImpl) UpdateBookingStatus(ctx context.Context, bookingId string) error {
	// 查找预订信息
	booking, err := s.repository.FindById(ctx, bookingId)
	if err != nil {
		return err
	}

	// 更新状态
	booking.Status = util.GetItPtr(1)
	return s.repository.UpdateStatus(ctx, booking)
}

// ValidateBooking 验证预订信息
func (s *BookingServiceImpl) ValidateBooking(ctx context.Context, bookingId string) error {
	// 查找预订信息
	booking, err := s.FindBookingById(ctx, bookingId)
	if err != nil {
		return err
	}

	// 验证预订状态
	if *booking.Status != model.BookingStatusConfirmed {
		return bookingerror.NewInvalidBookingStatusError(*booking.Status)
	}

	// 验证预订时间
	if err := s.ValidateBookingTime(ctx, *booking.ArrivalTime); err != nil {
		return err
	}

	// 验证房间状态
	// if err := s.roomService.ValidateRoomStatus(ctx, *booking.RoomId, []string{"available"}); err != nil {
	// 	return bookingerror.NewRoomNotAvailableError(*booking.RoomId, err)
	// }

	return nil
}

// FindBookingById 查找预订信息
func (s *BookingServiceImpl) FindBookingById(ctx context.Context, bookingId string) (*po.Booking, error) {
	if bookingId == "" {
		return nil, bookingerror.NewBookingNotFoundError(bookingId)
	}

	return s.repository.FindById(ctx, bookingId)
}

// ValidateBookingTime 验证预订时间
func (s *BookingServiceImpl) ValidateBookingTime(ctx context.Context, arrivalTime int64) error {
	now := time.Now().Unix()

	// 验证开始时间
	if arrivalTime < now {
		return bookingerror.NewInvalidBookingTimeError("arrival time cannot be in the past")
	}

	// TODO: 验证营业时间
	// 1. 获取场馆营业时间配置
	// 2. 验证预订时间是否在营业时间内

	return nil
}

// FindBookingsByVenueIdAndTimeRange 根据场所ID和时间范围查找预订信息
func (s *BookingServiceImpl) FindBookingsByVenueIdAndTimeRange(ctx context.Context, venueId string, arrivalTimeStart int64, status int, orderBy string) (*[]po.Booking, error) {
	return s.repository.FindByVenueIdAndTimeRange(ctx, venueId, arrivalTimeStart, status, orderBy)
}

// ConvertToBookingVO 转换为预订VO
func (s *BookingServiceImpl) ConvertToBookingVO(ctx context.Context, booking po.Booking) vo.BookingVO {
	return s.repository.ConvertToBookingVO(ctx, booking)
}

// ConvertToBookingPO 转换为预订PO
func (s *BookingServiceImpl) ConvertToBooking(ctx context.Context, bookingVO vo.BookingVO) po.Booking {
	return s.repository.ConvertToBooking(ctx, bookingVO)
}

// BookingSuccessInOpen 预订成功后开台
func (s *BookingServiceImpl) BookingSuccessInOpen(ctx context.Context, bookingId string, status int) error {
	return s.repository.BookingSuccessInOpen(ctx, bookingId, status)
}
