package service

import (
	"context"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// BookingService 预订服务接口
type BookingService interface {
	// UpdateBookingStatus 更新预订状态
	UpdateBookingStatus(ctx context.Context, bookingId string) error

	// ValidateBooking 验证预订信息
	ValidateBooking(ctx context.Context, bookingId string) error

	// FindBookingById 查找预订信息
	FindBookingById(ctx context.Context, bookingId string) (*po.Booking, error)

	// ValidateBookingTime 验证预订时间
	ValidateBookingTime(ctx context.Context, arrivalTime int64) error

	// FindBookingsByVenueIdAndTimeRange 根据场所ID和时间范围查找预订信息
	FindBookingsByVenueIdAndTimeRange(ctx context.Context, venueId string, arrivalTimeStart int64, status int, orderBy string) (*[]po.Booking, error)

	// ConvertToBookingVO 转换为预订VO
	ConvertToBookingVO(ctx context.Context, booking po.Booking) vo.BookingVO

	// ConvertToBookingPO 转换为预订PO
	ConvertToBooking(ctx context.Context, bookingVO vo.BookingVO) po.Booking

	// BookingSuccessInOpen 预订成功后开台
	BookingSuccessInOpen(ctx context.Context, bookingId string, status int) error
}
