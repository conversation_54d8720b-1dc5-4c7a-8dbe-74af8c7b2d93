package error

import "fmt"

// BookingError 预订错误
type BookingError struct {
	Code    string
	Message string
	Cause   error
}

// Error 实现error接口
func (e *BookingError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (cause: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// 错误码定义
const (
	ErrCodeBookingNotFound         = "BOOKING_NOT_FOUND"
	ErrCodeInvalidBookingStatus    = "INVALID_BOOKING_STATUS"
	ErrCodeInvalidStatusTransition = "INVALID_STATUS_TRANSITION"
	ErrCodeInvalidBookingTime      = "INVALID_BOOKING_TIME"
	ErrCodeRoomNotAvailable        = "ROOM_NOT_AVAILABLE"
)

// NewBookingError 创建预订错误
func NewBookingError(code string, message string, cause error) *BookingError {
	return &BookingError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// NewBookingNotFoundError 创建预订不存在错误
func NewBookingNotFoundError(bookingId string) *BookingError {
	return NewBookingError(
		ErrCodeBookingNotFound,
		fmt.Sprintf("booking not found: %s", bookingId),
		nil,
	)
}

// NewInvalidBookingStatusError 创建无效预订状态错误
func NewInvalidBookingStatusError(status int) *BookingError {
	return NewBookingError(
		ErrCodeInvalidBookingStatus,
		fmt.Sprintf("invalid booking status: %d", status),
		nil,
	)
}

// NewInvalidStatusTransitionError 创建无效状态转换错误
func NewInvalidStatusTransitionError(from, to int) *BookingError {
	return NewBookingError(
		ErrCodeInvalidStatusTransition,
		fmt.Sprintf("invalid status transition: %d -> %d", from, to),
		nil,
	)
}

// NewInvalidBookingTimeError 创建无效预订时间错误
func NewInvalidBookingTimeError(message string) *BookingError {
	return NewBookingError(
		ErrCodeInvalidBookingTime,
		message,
		nil,
	)
}

// NewRoomNotAvailableError 创建房间不可用错误
func NewRoomNotAvailableError(roomId string, cause error) *BookingError {
	return NewBookingError(
		ErrCodeRoomNotAvailable,
		fmt.Sprintf("room not available: %s", roomId),
		cause,
	)
}
