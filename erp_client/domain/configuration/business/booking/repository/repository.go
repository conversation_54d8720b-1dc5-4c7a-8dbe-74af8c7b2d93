package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository Booking仓储接口
type Repository interface {
	// FindById 根据ID查询预订信息
	FindById(ctx context.Context, id string) (*po.Booking, error)

	// UpdateStatus 更新预订状态
	UpdateStatus(ctx context.Context, booking *po.Booking) error

	// FindByCondition 根据条件查询预订信息
	FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]po.Booking, error)

	// Create 创建预订
	Create(ctx context.Context, booking *po.Booking) error

	// Update 更新预订
	Update(ctx context.Context, booking *po.Booking) error

	// Delete 删除预订
	Delete(ctx context.Context, id string) error

	// FindByVenueIdAndTimeRange 根据场所ID和时间范围查找预订信息
	FindByVenueIdAndTimeRange(ctx context.Context, venueId string, arrivalTimeStart int64, status int, orderBy string) (*[]po.Booking, error)

	// ConvertToBookingVO 转换为预订VO
	ConvertToBookingVO(ctx context.Context, booking po.Booking) vo.BookingVO

	// ConvertToBooking 转换为预订PO
	ConvertToBooking(ctx context.Context, bookingVO vo.BookingVO) po.Booking

	// BookingSuccessInOpen 预订成功后开台
	BookingSuccessInOpen(ctx context.Context, bookingId string, status int) error
}
