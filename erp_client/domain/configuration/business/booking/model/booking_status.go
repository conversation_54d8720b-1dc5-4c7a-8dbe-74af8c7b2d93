package model

// 预订状态定义
const (
	BookingStatusCreated   = 1 // 已创建
	BookingStatusConfirmed = 2 // 已确认
	BookingStatusCancelled = 3 // 已取消
	BookingStatusCompleted = 4 // 已完成
)

// IsValidBookingStatus 判断预订状态是否有效
func IsValidBookingStatus(status int) bool {
	return status >= BookingStatusCreated && status <= BookingStatusCompleted
}

// IsValidStatusTransition 判断状态转换是否有效
func IsValidStatusTransition(from, to int) bool {
	switch from {
	case BookingStatusCreated:
		return to == BookingStatusConfirmed || to == BookingStatusCancelled
	case BookingStatusConfirmed:
		return to == BookingStatusCompleted || to == BookingStatusCancelled
	default:
		return false
	}
}
