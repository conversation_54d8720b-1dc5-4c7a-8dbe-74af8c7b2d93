package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// AsExampleService 员工赠金记录服务接口
type AsExampleService interface {
	// CreateAsExample 创建员工赠金记录
	CreateAsExample(ctx context.Context, record *po.AsExample) error

	// UpdateAsExample 更新员工赠金记录
	UpdateAsExample(ctx context.Context, record *po.AsExample) error

	// FindById 根据ID查询员工赠金记录
	FindById(ctx context.Context, id string) (po.AsExample, error)

	// ConvertToAsExampleVO 转换为员工赠金记录VO
	ConvertToAsExampleVO(ctx context.Context, record po.AsExample) vo.AsExampleVO

	// ConvertToAsExample 转换为员工赠金记录PO
	ConvertToAsExample(ctx context.Context, recordVO vo.AsExampleVO) po.AsExample
}
