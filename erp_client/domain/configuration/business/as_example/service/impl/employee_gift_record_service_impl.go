package impl

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/as_example/repository"
	"voderpltvv/erp_client/domain/configuration/business/as_example/service"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// AsExampleServiceImpl 员工赠金记录服务实现
type AsExampleServiceImpl struct {
	asExampleRepo repository.AsExampleRepository
}

// NewEmployeeGiftRecordService 创建员工赠金记录服务实例
func NewAsExampleService(asExampleRepo repository.AsExampleRepository) service.AsExampleService {
	return &AsExampleServiceImpl{
		asExampleRepo: asExampleRepo,
	}
}

// CreateEmployeeGiftRecord 创建员工赠金记录
func (s *AsExampleServiceImpl) CreateAsExample(ctx context.Context, record *po.AsExample) error {
	return s.asExampleRepo.CreateAsExample(ctx, record)
}

// UpdateEmployeeGiftRecord 更新员工赠金记录
func (s *AsExampleServiceImpl) UpdateAsExample(ctx context.Context, record *po.AsExample) error {
	return s.asExampleRepo.UpdateAsExample(ctx, record)
}

// FindById 根据ID查询员工赠金记录
func (s *AsExampleServiceImpl) FindById(ctx context.Context, id string) (po.AsExample, error) {
	return s.asExampleRepo.FindById(ctx, id)
}

// ConvertToEmployeeGiftRecordVO 转换为员工赠金记录VO
func (s *AsExampleServiceImpl) ConvertToAsExampleVO(ctx context.Context, record po.AsExample) vo.AsExampleVO {
	return s.asExampleRepo.ConvertToAsExampleVO(ctx, record)
}

// ConvertToEmployeeGiftRecord 转换为员工赠金记录PO
func (s *AsExampleServiceImpl) ConvertToAsExample(ctx context.Context, recordVO vo.AsExampleVO) po.AsExample {
	return s.asExampleRepo.ConvertToAsExample(ctx, recordVO)
}
