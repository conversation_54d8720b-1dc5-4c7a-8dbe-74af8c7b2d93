package service

import (
	"context"
	"time"
	baseService "voderpltvv/erp_client/domain/configuration/base/service"
	"voderpltvv/erp_client/domain/configuration/business/holiday/model"
)

// Service 节假日配置服务接口
type Service interface {
	baseService.Service

	// GetHoliday 获取节假日
	GetHoliday(ctx context.Context, id string) (model.Holiday, error)
	// FindByVenueID 根据场所ID查询节假日
	FindByVenueID(ctx context.Context, venueID string) ([]model.Holiday, error)
	// FindByName 根据名称查询节假日
	FindByName(ctx context.Context, venueID string, name string) (model.Holiday, error)
	// FindByDate 根据日期查询节假日
	FindByDate(ctx context.Context, venueID string, date time.Time) ([]model.Holiday, error)
	// FindByDateRange 根据日期范围查询节假日
	FindByDateRange(ctx context.Context, venueID string, startDate time.Time, endDate time.Time) ([]model.Holiday, error)
	// FindByRoomType 根据包厢类型查询节假日
	FindByRoomType(ctx context.Context, venueID string, roomType string) ([]model.Holiday, error)
	// FindDisplayed 查询显示的节假日
	FindDisplayed(ctx context.Context, venueID string) ([]model.Holiday, error)
	// UpdateDisplay 更新显示状态
	UpdateDisplay(ctx context.Context, id string, isDisplayed bool) error
	// UpdateDateRange 更新日期范围
	UpdateDateRange(ctx context.Context, id string, startDate time.Time, endDate time.Time) error
	// UpdatePriceAdjustment 更新价格调整系数
	UpdatePriceAdjustment(ctx context.Context, id string, priceAdjustment float64) error
	// UpdateTimeRanges 更新时间段列表
	UpdateTimeRanges(ctx context.Context, id string, timeRanges []model.TimeRange) error
	// UpdateRoomTypes 更新适用包厢类型
	UpdateRoomTypes(ctx context.Context, id string, roomTypes []string) error
}
