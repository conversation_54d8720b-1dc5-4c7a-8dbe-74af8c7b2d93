package repository

import (
	"context"
	"time"
	baseRepository "voderpltvv/erp_client/domain/configuration/base/repository"
	"voderpltvv/erp_client/domain/configuration/business/holiday/model"
)

// Repository 节假日配置仓储接口
type Repository interface {
	baseRepository.Repository

	// FindByVenueID 根据场所ID查询节假日
	FindByVenueID(ctx context.Context, venueID string) ([]model.Holiday, error)
	// FindByName 根据名称查询节假日
	FindByName(ctx context.Context, venueID string, name string) (model.Holiday, error)
	// FindByDate 根据日期查询节假日
	FindByDate(ctx context.Context, venueID string, date time.Time) ([]model.Holiday, error)
	// FindByDateRange 根据日期范围查询节假日
	FindByDateRange(ctx context.Context, venueID string, startDate time.Time, endDate time.Time) ([]model.Holiday, error)
	// FindByRoomType 根据包厢类型查询节假日
	FindByRoomType(ctx context.Context, venueID string, roomType string) ([]model.Holiday, error)
	// FindDisplayed 查询显示的节假日
	FindDisplayed(ctx context.Context, venueID string) ([]model.Holiday, error)
}
