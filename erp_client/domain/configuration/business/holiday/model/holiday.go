package model

import (
	"encoding/json"
	"time"
	baseModel "voderpltvv/erp_client/domain/configuration/base/model"
)

// Holiday 节假日配置接口
type Holiday interface {
	baseModel.Config

	// GetVenueID 获取所属场所ID
	GetVenueID() string
	// GetName 获取节假日名称
	GetName() string
	// GetDescription 获取描述
	GetDescription() string
	// GetStartDate 获取开始日期
	GetStartDate() time.Time
	// GetEndDate 获取结束日期
	GetEndDate() time.Time
	// GetPriceAdjustment 获取价格调整系数
	GetPriceAdjustment() float64
	// GetTimeRanges 获取时间段列表
	GetTimeRanges() []TimeRange
	// GetRoomTypes 获取适用包厢类型
	GetRoomTypes() []string
	// IsDisplayed 是否显示
	IsDisplayed() bool
}

// TimeRange 时间段
type TimeRange struct {
	StartTime string  `json:"start_time"` // 格式: HH:mm
	EndTime   string  `json:"end_time"`   // 格式: HH:mm
	Price     float64 `json:"price"`      // 价格
}

// HolidayValue 节假日值对象
type HolidayValue struct {
	VenueID         string      `json:"venue_id"`
	Name            string      `json:"name"`
	Description     string      `json:"description"`
	StartDate       time.Time   `json:"start_date"`
	EndDate         time.Time   `json:"end_date"`
	PriceAdjustment float64     `json:"price_adjustment"` // 价格调整系数，例如1.2表示上浮20%
	TimeRanges      []TimeRange `json:"time_ranges"`      // 特殊时段价格
	RoomTypes       []string    `json:"room_types"`       // 适用包厢类型
	IsDisplayed     bool        `json:"is_displayed"`
}

// holidayImpl 节假日配置实现
type holidayImpl struct {
	*baseModel.BaseConfig
	value *HolidayValue
}

// NewHoliday 创建节假日配置
func NewHoliday(
	id string,
	venueID string,
	name string,
	description string,
	startDate time.Time,
	endDate time.Time,
	priceAdjustment float64,
	timeRanges []TimeRange,
	roomTypes []string,
	isDisplayed bool,
	enabled bool,
) (Holiday, error) {
	value := &HolidayValue{
		VenueID:         venueID,
		Name:            name,
		Description:     description,
		StartDate:       startDate,
		EndDate:         endDate,
		PriceAdjustment: priceAdjustment,
		TimeRanges:      timeRanges,
		RoomTypes:       roomTypes,
		IsDisplayed:     isDisplayed,
	}

	// 序列化值对象
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}

	return &holidayImpl{
		BaseConfig: &baseModel.BaseConfig{
			ID:      id,
			Type:    "holiday",
			Key:     venueID + ":" + name,
			Value:   string(valueBytes),
			Version: 1,
			Enabled: enabled,
		},
		value: value,
	}, nil
}

// GetVenueID 获取所属场所ID
func (h *holidayImpl) GetVenueID() string {
	return h.value.VenueID
}

// GetName 获取节假日名称
func (h *holidayImpl) GetName() string {
	return h.value.Name
}

// GetDescription 获取描述
func (h *holidayImpl) GetDescription() string {
	return h.value.Description
}

// GetStartDate 获取开始日期
func (h *holidayImpl) GetStartDate() time.Time {
	return h.value.StartDate
}

// GetEndDate 获取结束日期
func (h *holidayImpl) GetEndDate() time.Time {
	return h.value.EndDate
}

// GetPriceAdjustment 获取价格调整系数
func (h *holidayImpl) GetPriceAdjustment() float64 {
	return h.value.PriceAdjustment
}

// GetTimeRanges 获取时间段列表
func (h *holidayImpl) GetTimeRanges() []TimeRange {
	return h.value.TimeRanges
}

// GetRoomTypes 获取适用包厢类型
func (h *holidayImpl) GetRoomTypes() []string {
	return h.value.RoomTypes
}

// IsDisplayed 是否显示
func (h *holidayImpl) IsDisplayed() bool {
	return h.value.IsDisplayed
}

// Validate 验证配置
func (h *holidayImpl) Validate() error {
	if err := h.BaseConfig.Validate(); err != nil {
		return err
	}

	if h.value.VenueID == "" {
		return baseModel.ErrInvalidID
	}
	if h.value.Name == "" {
		return baseModel.ErrInvalidName
	}
	if h.value.StartDate.IsZero() {
		return baseModel.ErrInvalidStartDate
	}
	if h.value.EndDate.IsZero() {
		return baseModel.ErrInvalidEndDate
	}
	if h.value.EndDate.Before(h.value.StartDate) {
		return baseModel.ErrInvalidDateRange
	}
	if h.value.PriceAdjustment <= 0 {
		return baseModel.ErrInvalidPriceAdjustment
	}
	if len(h.value.RoomTypes) == 0 {
		return baseModel.ErrInvalidRoomTypes
	}

	return nil
}

// Clone 克隆配置
func (h *holidayImpl) Clone() baseModel.Config {
	timeRanges := make([]TimeRange, len(h.value.TimeRanges))
	copy(timeRanges, h.value.TimeRanges)

	roomTypes := make([]string, len(h.value.RoomTypes))
	copy(roomTypes, h.value.RoomTypes)

	value := &HolidayValue{
		VenueID:         h.value.VenueID,
		Name:            h.value.Name,
		Description:     h.value.Description,
		StartDate:       h.value.StartDate,
		EndDate:         h.value.EndDate,
		PriceAdjustment: h.value.PriceAdjustment,
		TimeRanges:      timeRanges,
		RoomTypes:       roomTypes,
		IsDisplayed:     h.value.IsDisplayed,
	}

	return &holidayImpl{
		BaseConfig: h.BaseConfig.Clone().(*baseModel.BaseConfig),
		value:      value,
	}
}
