package model

import (
	"encoding/json"
	"time"

	baseModel "voderpltvv/erp_client/domain/configuration/base/model"
)

// PricePlan 价格方案配置接口
type PricePlan interface {
	baseModel.Config

	// GetVenueID 获取所属场所ID
	GetVenueID() string
	// GetName 获取方案名称
	GetName() string
	// GetDescription 获取描述
	GetDescription() string
	// GetStartTime 获取开始时间
	GetStartTime() time.Time
	// GetEndTime 获取结束时间
	GetEndTime() time.Time
	// GetDayOfWeek 获取适用星期
	GetDayOfWeek() []int
	// GetTimeRanges 获取时间段列表
	GetTimeRanges() []TimeRange
	// GetRoomTypes 获取适用包厢类型
	GetRoomTypes() []string
	// IsDisplayed 是否显示
	IsDisplayed() bool
}

// TimeRange 时间段
type TimeRange struct {
	StartTime string  `json:"start_time"` // 格式: HH:mm
	EndTime   string  `json:"end_time"`   // 格式: HH:mm
	Price     float64 `json:"price"`      // 价格
}

// PricePlanValue 价格方案值对象
type PricePlanValue struct {
	VenueID     string      `json:"venue_id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	StartTime   time.Time   `json:"start_time"`
	EndTime     time.Time   `json:"end_time"`
	DayOfWeek   []int       `json:"day_of_week"` // 1-7 代表周一到周日
	TimeRanges  []TimeRange `json:"time_ranges"`
	RoomTypes   []string    `json:"room_types"`
	IsDisplayed bool        `json:"is_displayed"`
}

// pricePlanImpl 价格方案配置实现
type pricePlanImpl struct {
	*baseModel.BaseConfig
	value *PricePlanValue
}

// NewPricePlan 创建价格方案配置
func NewPricePlan(
	id string,
	venueID string,
	name string,
	description string,
	startTime time.Time,
	endTime time.Time,
	dayOfWeek []int,
	timeRanges []TimeRange,
	roomTypes []string,
	isDisplayed bool,
	enabled bool,
) (PricePlan, error) {
	value := &PricePlanValue{
		VenueID:     venueID,
		Name:        name,
		Description: description,
		StartTime:   startTime,
		EndTime:     endTime,
		DayOfWeek:   dayOfWeek,
		TimeRanges:  timeRanges,
		RoomTypes:   roomTypes,
		IsDisplayed: isDisplayed,
	}

	// 序列化值对象
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}

	return &pricePlanImpl{
		BaseConfig: &baseModel.BaseConfig{
			ID:      id,
			Type:    "price_plan",
			Key:     venueID + ":" + name,
			Value:   string(valueBytes),
			Version: 1,
			Enabled: enabled,
		},
		value: value,
	}, nil
}

// GetVenueID 获取所属场所ID
func (p *pricePlanImpl) GetVenueID() string {
	return p.value.VenueID
}

// GetName 获取方案名称
func (p *pricePlanImpl) GetName() string {
	return p.value.Name
}

// GetDescription 获取描述
func (p *pricePlanImpl) GetDescription() string {
	return p.value.Description
}

// GetStartTime 获取开始时间
func (p *pricePlanImpl) GetStartTime() time.Time {
	return p.value.StartTime
}

// GetEndTime 获取结束时间
func (p *pricePlanImpl) GetEndTime() time.Time {
	return p.value.EndTime
}

// GetDayOfWeek 获取适用星期
func (p *pricePlanImpl) GetDayOfWeek() []int {
	return p.value.DayOfWeek
}

// GetTimeRanges 获取时间段列表
func (p *pricePlanImpl) GetTimeRanges() []TimeRange {
	return p.value.TimeRanges
}

// GetRoomTypes 获取适用包厢类型
func (p *pricePlanImpl) GetRoomTypes() []string {
	return p.value.RoomTypes
}

// IsDisplayed 是否显示
func (p *pricePlanImpl) IsDisplayed() bool {
	return p.value.IsDisplayed
}

// Validate 验证配置
func (p *pricePlanImpl) Validate() error {
	if err := p.BaseConfig.Validate(); err != nil {
		return err
	}

	if p.value.VenueID == "" {
		return baseModel.ErrInvalidID
	}
	if p.value.Name == "" {
		return baseModel.ErrInvalidName
	}
	if p.value.StartTime.IsZero() {
		return baseModel.ErrInvalidStartTime
	}
	if p.value.EndTime.IsZero() {
		return baseModel.ErrInvalidEndTime
	}
	if p.value.EndTime.Before(p.value.StartTime) {
		return baseModel.ErrInvalidTimeRange
	}
	if len(p.value.DayOfWeek) == 0 {
		return baseModel.ErrInvalidDayOfWeek
	}
	for _, day := range p.value.DayOfWeek {
		if day < 1 || day > 7 {
			return baseModel.ErrInvalidDayOfWeek
		}
	}
	if len(p.value.TimeRanges) == 0 {
		return baseModel.ErrInvalidTimeRanges
	}
	if len(p.value.RoomTypes) == 0 {
		return baseModel.ErrInvalidRoomTypes
	}

	return nil
}

// Clone 克隆配置
func (p *pricePlanImpl) Clone() baseModel.Config {
	dayOfWeek := make([]int, len(p.value.DayOfWeek))
	copy(dayOfWeek, p.value.DayOfWeek)

	timeRanges := make([]TimeRange, len(p.value.TimeRanges))
	copy(timeRanges, p.value.TimeRanges)

	roomTypes := make([]string, len(p.value.RoomTypes))
	copy(roomTypes, p.value.RoomTypes)

	value := &PricePlanValue{
		VenueID:     p.value.VenueID,
		Name:        p.value.Name,
		Description: p.value.Description,
		StartTime:   p.value.StartTime,
		EndTime:     p.value.EndTime,
		DayOfWeek:   dayOfWeek,
		TimeRanges:  timeRanges,
		RoomTypes:   roomTypes,
		IsDisplayed: p.value.IsDisplayed,
	}

	return &pricePlanImpl{
		BaseConfig: p.BaseConfig.Clone().(*baseModel.BaseConfig),
		value:      value,
	}
}
