package service

import (
	"context"
	"time"
	baseService "voderpltvv/erp_client/domain/configuration/base/service"
	"voderpltvv/erp_client/domain/configuration/business/priceplan/model"
)

// Service 价格方案配置服务接口
type Service interface {
	baseService.Service

	// GetPricePlan 获取价格方案
	GetPricePlan(ctx context.Context, id string) (model.PricePlan, error)
	// FindByVenueID 根据场所ID查询价格方案
	FindByVenueID(ctx context.Context, venueID string) ([]model.PricePlan, error)
	// FindByName 根据名称查询价格方案
	FindByName(ctx context.Context, venueID string, name string) (model.PricePlan, error)
	// FindByRoomType 根据包厢类型查询价格方案
	FindByRoomType(ctx context.Context, venueID string, roomType string) ([]model.PricePlan, error)
	// FindByTimeRange 根据时间范围查询价格方案
	FindByTimeRange(ctx context.Context, venueID string, startTime string, endTime string) ([]model.PricePlan, error)
	// FindDisplayed 查询显示的价格方案
	FindDisplayed(ctx context.Context, venueID string) ([]model.PricePlan, error)
	// UpdateDisplay 更新显示状态
	UpdateDisplay(ctx context.Context, id string, isDisplayed bool) error
	// UpdateTimeRange 更新时间范围
	UpdateTimeRange(ctx context.Context, id string, startTime time.Time, endTime time.Time) error
	// UpdateDayOfWeek 更新适用星期
	UpdateDayOfWeek(ctx context.Context, id string, dayOfWeek []int) error
	// UpdateTimeRanges 更新时间段列表
	UpdateTimeRanges(ctx context.Context, id string, timeRanges []model.TimeRange) error
	// UpdateRoomTypes 更新适用包厢类型
	UpdateRoomTypes(ctx context.Context, id string, roomTypes []string) error
}
