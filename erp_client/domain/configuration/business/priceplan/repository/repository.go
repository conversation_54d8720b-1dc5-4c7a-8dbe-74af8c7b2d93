package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/configuration/base/repository"
	"voderpltvv/erp_client/domain/configuration/business/priceplan/model"
)

// Repository 价格方案配置仓储接口
type Repository interface {
	baseRepository.Repository

	// FindByVenueID 根据场所ID查询价格方案
	FindByVenueID(ctx context.Context, venueID string) ([]model.PricePlan, error)
	// FindByName 根据名称查询价格方案
	FindByName(ctx context.Context, venueID string, name string) (model.PricePlan, error)
	// FindByRoomType 根据包厢类型查询价格方案
	FindByRoomType(ctx context.Context, venueID string, roomType string) ([]model.PricePlan, error)
	// FindByTimeRange 根据时间范围查询价格方案
	FindByTimeRange(ctx context.Context, venueID string, startTime string, endTime string) ([]model.PricePlan, error)
	// FindDisplayed 查询显示的价格方案
	FindDisplayed(ctx context.Context, venueID string) ([]model.PricePlan, error)
}
