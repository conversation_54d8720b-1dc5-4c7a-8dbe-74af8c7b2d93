package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// CallTypesService 呼叫类型服务接口
type CallTypesService interface {
	// CreateCallTypes 创建呼叫类型
	CreateCallTypes(ctx context.Context, callTypes *po.CallTypes) error

	// ConvertToCallTypesVO 转换为呼叫类型VO
	ConvertToCallTypesVO(ctx context.Context, callTypes po.CallTypes) vo.CallTypesVO

	// ConvertToCallTypes 转换为呼叫类型PO
	ConvertToCallTypes(ctx context.Context, callTypesVO vo.CallTypesVO) po.CallTypes

	// GetCallTypesByVenue 获取场馆的呼叫类型列表
	GetCallTypesByVenue(ctx context.Context, venueId string) ([]po.CallTypes, error)
}
