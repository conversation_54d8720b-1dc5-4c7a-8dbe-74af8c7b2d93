package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// CallMessageService 呼叫消息服务接口
type CallMessageService interface {
	// CreateCallMessage 创建呼叫消息
	CreateCallMessage(ctx context.Context, callMessage *po.CallMessage) error

	// ConvertToCallMessageVO 转换为呼叫消息VO
	ConvertToCallMessageVO(ctx context.Context, callMessage po.CallMessage) vo.CallMessageVO

	// ConvertToCallMessage 转换为呼叫消息PO
	ConvertToCallMessage(ctx context.Context, callMessageVO vo.CallMessageVO) po.CallMessage

	// FindCallMessagesCurrentDay 查询当前日呼叫消息
	FindCallMessagesCurrentDay(ctx context.Context, venueId string) ([]po.CallMessage, error)

	// FindCallMessagesUnprocessed 查询未处理的呼叫消息
	FindCallMessagesUnprocessed(ctx context.Context, venueId string) ([]po.CallMessage, error)

	// FindCallMessagesUnprocessedByRoomId 根据房间ID查询未处理的呼叫消息
	FindCallMessagesUnprocessedByRoomId(ctx context.Context, roomId string) ([]po.CallMessage, error)

	// FindById 查询呼叫消息
	FindById(ctx context.Context, id string) (po.CallMessage, error)

	// UpdateCallMessage 更新呼叫消息
	UpdateCallMessage(ctx context.Context, callMessage *po.CallMessage) error

	// SendNATSCallMessage 发送NATS呼叫消息
	SendNATSCallMessage(ctx context.Context, venueId string) error
}
