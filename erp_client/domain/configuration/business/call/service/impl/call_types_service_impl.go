package impl

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/call/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"

	"github.com/sirupsen/logrus"
)

// CallTypesServiceImpl 呼叫类型服务实现
type CallTypesServiceImpl struct {
	callTypesRepo repository.CallTypesRepository
}

// NewCallTypesService 创建呼叫类型服务实例
func NewCallTypesService(callTypesRepo repository.CallTypesRepository) *CallTypesServiceImpl {
	return &CallTypesServiceImpl{
		callTypesRepo: callTypesRepo,
	}
}

// CreateCallTypes 创建呼叫类型
func (s *CallTypesServiceImpl) CreateCallTypes(ctx context.Context, callTypes *po.CallTypes) error {
	return s.callTypesRepo.CreateCallTypes(ctx, callTypes)
}

// ConvertToCallTypesVO 转换为呼叫类型VO
func (s *CallTypesServiceImpl) ConvertToCallTypesVO(ctx context.Context, callTypes po.CallTypes) vo.CallTypesVO {
	return s.callTypesRepo.ConvertToCallTypesVO(ctx, callTypes)
}

// ConvertToCallTypes 转换为呼叫类型PO
func (s *CallTypesServiceImpl) ConvertToCallTypes(ctx context.Context, callTypesVO vo.CallTypesVO) po.CallTypes {
	return s.callTypesRepo.ConvertToCallTypes(ctx, callTypesVO)
}

// GetCallTypesByVenue 获取场馆的呼叫类型列表
func (s *CallTypesServiceImpl) GetCallTypesByVenue(ctx context.Context, venueId string) ([]po.CallTypes, error) {
	defer func() {
		if err := recover(); err != nil {
			logrus.Printf("GetCallTypesByVenue panic: %v", err)
		}
	}()

	// TODO: 实现获取场馆呼叫类型列表的逻辑
	return nil, nil
}
