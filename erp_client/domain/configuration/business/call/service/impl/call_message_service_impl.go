package impl

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/call/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// CallMessageServiceImpl 呼叫消息服务实现
type CallMessageServiceImpl struct {
	callMessageRepo repository.CallMessageRepository
}

// NewCallMessageService 创建呼叫消息服务实例
func NewCallMessageService(callMessageRepo repository.CallMessageRepository) *CallMessageServiceImpl {
	return &CallMessageServiceImpl{
		callMessageRepo: callMessageRepo,
	}
}

// CreateCallMessage 创建呼叫消息
func (s *CallMessageServiceImpl) CreateCallMessage(ctx context.Context, callMessage *po.CallMessage) error {
	return s.callMessageRepo.CreateCallMessage(ctx, callMessage)
}

// ConvertToCallMessageVO 转换为呼叫消息VO
func (s *CallMessageServiceImpl) ConvertToCallMessageVO(ctx context.Context, callMessage po.CallMessage) vo.CallMessageVO {
	return s.callMessageRepo.ConvertToCallMessageVO(ctx, callMessage)
}

// ConvertToCallMessage 转换为呼叫消息PO
func (s *CallMessageServiceImpl) ConvertToCallMessage(ctx context.Context, callMessageVO vo.CallMessageVO) po.CallMessage {
	return s.callMessageRepo.ConvertToCallMessage(ctx, callMessageVO)
}

// FindCallMessagesCurrentDay 查询当前日呼叫消息
func (s *CallMessageServiceImpl) FindCallMessagesCurrentDay(ctx context.Context, venueId string) ([]po.CallMessage, error) {
	return s.callMessageRepo.FindCallMessagesCurrentDay(ctx, venueId)
}

// FindCallMessageById 查询呼叫消息
func (s *CallMessageServiceImpl) FindById(ctx context.Context, id string) (po.CallMessage, error) {
	return s.callMessageRepo.FindById(ctx, id)
}

// UpdateCallMessage 更新呼叫消息
func (s *CallMessageServiceImpl) UpdateCallMessage(ctx context.Context, callMessage *po.CallMessage) error {
	return s.callMessageRepo.UpdateCallMessage(ctx, callMessage)
}

// SendNATSCallMessage 发送NATS呼叫消息
func (s *CallMessageServiceImpl) SendNATSCallMessage(ctx context.Context, venueId string) error {
	return s.callMessageRepo.SendNATSCallMessage(ctx, venueId)
}

// FindCallMessagesUnprocessed 查询未处理的呼叫消息
func (s *CallMessageServiceImpl) FindCallMessagesUnprocessed(ctx context.Context, venueId string) ([]po.CallMessage, error) {
	return s.callMessageRepo.FindCallMessagesUnprocessed(ctx, venueId)
}

// FindCallMessagesUnprocessedByRoomId 根据房间ID查询未处理的呼叫消息
func (s *CallMessageServiceImpl) FindCallMessagesUnprocessedByRoomId(ctx context.Context, roomId string) ([]po.CallMessage, error) {
	return s.callMessageRepo.FindCallMessagesUnprocessedByRoomId(ctx, roomId)
}
