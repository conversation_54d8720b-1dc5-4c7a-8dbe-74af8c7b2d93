package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// CallTypesRepository 呼叫类型仓储接口
type CallTypesRepository interface {
	// CreateCallTypes 创建呼叫类型
	CreateCallTypes(ctx context.Context, callTypes *po.CallTypes) error

	// ConvertToCallTypesVO 转换为呼叫类型VO
	ConvertToCallTypesVO(ctx context.Context, callTypes po.CallTypes) vo.CallTypesVO

	// ConvertToCallTypes 转换为呼叫类型PO
	ConvertToCallTypes(ctx context.Context, callTypesVO vo.CallTypesVO) po.CallTypes
}
