package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// EmployeeGiftRecordService 员工赠金记录服务接口
type EmployeeGiftRecordService interface {
	// CreateEmployeeGiftRecord 创建员工赠金记录
	CreateEmployeeGiftRecord(ctx context.Context, record *po.EmployeeGiftRecord) error

	// UpdateEmployeeGiftRecord 更新员工赠金记录
	UpdateEmployeeGiftRecord(ctx context.Context, record *po.EmployeeGiftRecord) error

	// FindById 根据ID查询员工赠金记录
	FindById(ctx context.Context, id string) (po.EmployeeGiftRecord, error)

	// ConvertToEmployeeGiftRecordVO 转换为员工赠金记录VO
	ConvertToEmployeeGiftRecordVO(ctx context.Context, record po.EmployeeGiftRecord) vo.EmployeeGiftRecordVO

	// ConvertToEmployeeGiftRecord 转换为员工赠金记录PO
	ConvertToEmployeeGiftRecord(ctx context.Context, recordVO vo.EmployeeGiftRecordVO) po.EmployeeGiftRecord
}
