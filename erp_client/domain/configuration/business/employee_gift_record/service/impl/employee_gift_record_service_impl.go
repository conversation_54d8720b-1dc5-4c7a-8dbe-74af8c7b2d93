package impl

import (
	"context"
	"voderpltvv/erp_client/domain/configuration/business/employee_gift_record/repository"
	"voderpltvv/erp_client/domain/configuration/business/employee_gift_record/service"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// EmployeeGiftRecordServiceImpl 员工赠金记录服务实现
type EmployeeGiftRecordServiceImpl struct {
	employeeGiftRecordRepo repository.EmployeeGiftRecordRepository
}

// NewEmployeeGiftRecordService 创建员工赠金记录服务实例
func NewEmployeeGiftRecordService(employeeGiftRecordRepo repository.EmployeeGiftRecordRepository) service.EmployeeGiftRecordService {
	return &EmployeeGiftRecordServiceImpl{
		employeeGiftRecordRepo: employeeGiftRecordRepo,
	}
}

// CreateEmployeeGiftRecord 创建员工赠金记录
func (s *EmployeeGiftRecordServiceImpl) CreateEmployeeGiftRecord(ctx context.Context, record *po.EmployeeGiftRecord) error {
	return s.employeeGiftRecordRepo.CreateEmployeeGiftRecord(ctx, record)
}

// UpdateEmployeeGiftRecord 更新员工赠金记录
func (s *EmployeeGiftRecordServiceImpl) UpdateEmployeeGiftRecord(ctx context.Context, record *po.EmployeeGiftRecord) error {
	return s.employeeGiftRecordRepo.UpdateEmployeeGiftRecord(ctx, record)
}

// FindById 根据ID查询员工赠金记录
func (s *EmployeeGiftRecordServiceImpl) FindById(ctx context.Context, id string) (po.EmployeeGiftRecord, error) {
	return s.employeeGiftRecordRepo.FindById(ctx, id)
}

// ConvertToEmployeeGiftRecordVO 转换为员工赠金记录VO
func (s *EmployeeGiftRecordServiceImpl) ConvertToEmployeeGiftRecordVO(ctx context.Context, record po.EmployeeGiftRecord) vo.EmployeeGiftRecordVO {
	return s.employeeGiftRecordRepo.ConvertToEmployeeGiftRecordVO(ctx, record)
}

// ConvertToEmployeeGiftRecord 转换为员工赠金记录PO
func (s *EmployeeGiftRecordServiceImpl) ConvertToEmployeeGiftRecord(ctx context.Context, recordVO vo.EmployeeGiftRecordVO) po.EmployeeGiftRecord {
	return s.employeeGiftRecordRepo.ConvertToEmployeeGiftRecord(ctx, recordVO)
}
