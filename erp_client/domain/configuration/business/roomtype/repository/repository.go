package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/configuration/base/repository"
	"voderpltvv/erp_client/domain/configuration/business/roomtype/model"
)

// Repository 包厢类型配置仓储接口
type Repository interface {
	baseRepository.Repository

	// FindByVenueID 根据场所ID查询包厢类型
	FindByVenueID(ctx context.Context, venueID string) ([]model.RoomType, error)
	// FindByName 根据名称查询包厢类型
	FindByName(ctx context.Context, venueID string, name string) (model.RoomType, error)
	// FindDisplayed 查询显示的包厢类型
	FindDisplayed(ctx context.Context, venueID string) ([]model.RoomType, error)
}
