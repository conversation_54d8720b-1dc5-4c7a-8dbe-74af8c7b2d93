package model

import (
	"encoding/json"
	baseModel "voderpltvv/erp_client/domain/configuration/base/model"
)

// RoomType 包厢类型配置接口
type RoomType interface {
	baseModel.Config

	// GetVenueID 获取所属场所ID
	GetVenueID() string
	// GetName 获取类型名称
	GetName() string
	// GetDescription 获取描述
	GetDescription() string
	// GetCapacity 获取容纳人数
	GetCapacity() int
	// GetMinCharge 获取最低消费
	GetMinCharge() float64
	// GetHourPrice 获取时段价格
	GetHourPrice() float64
	// GetServiceFee 获取服务费率
	GetServiceFee() float64
	// IsDisplayed 是否显示
	IsDisplayed() bool
}

// RoomTypeValue 包厢类型值对象
type RoomTypeValue struct {
	VenueID     string  `json:"venue_id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Capacity    int     `json:"capacity"`
	MinCharge   float64 `json:"min_charge"`
	HourPrice   float64 `json:"hour_price"`
	ServiceFee  float64 `json:"service_fee"`
	IsDisplayed bool    `json:"is_displayed"`
}

// roomTypeImpl 包厢类型配置实现
type roomTypeImpl struct {
	*baseModel.BaseConfig
	value *RoomTypeValue
}

// NewRoomType 创建包厢类型配置
func NewRoomType(
	id string,
	venueID string,
	name string,
	description string,
	capacity int,
	minCharge float64,
	hourPrice float64,
	serviceFee float64,
	isDisplayed bool,
	enabled bool,
) (RoomType, error) {
	value := &RoomTypeValue{
		VenueID:     venueID,
		Name:        name,
		Description: description,
		Capacity:    capacity,
		MinCharge:   minCharge,
		HourPrice:   hourPrice,
		ServiceFee:  serviceFee,
		IsDisplayed: isDisplayed,
	}

	// 序列化值对象
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return nil, err
	}

	return &roomTypeImpl{
		BaseConfig: &baseModel.BaseConfig{
			ID:      id,
			Type:    "room_type",
			Key:     venueID + ":" + name,
			Value:   string(valueBytes),
			Version: 1,
			Enabled: enabled,
		},
		value: value,
	}, nil
}

// GetVenueID 获取所属场所ID
func (r *roomTypeImpl) GetVenueID() string {
	return r.value.VenueID
}

// GetName 获取类型名称
func (r *roomTypeImpl) GetName() string {
	return r.value.Name
}

// GetDescription 获取描述
func (r *roomTypeImpl) GetDescription() string {
	return r.value.Description
}

// GetCapacity 获取容纳人数
func (r *roomTypeImpl) GetCapacity() int {
	return r.value.Capacity
}

// GetMinCharge 获取最低消费
func (r *roomTypeImpl) GetMinCharge() float64 {
	return r.value.MinCharge
}

// GetHourPrice 获取时段价格
func (r *roomTypeImpl) GetHourPrice() float64 {
	return r.value.HourPrice
}

// GetServiceFee 获取服务费率
func (r *roomTypeImpl) GetServiceFee() float64 {
	return r.value.ServiceFee
}

// IsDisplayed 是否显示
func (r *roomTypeImpl) IsDisplayed() bool {
	return r.value.IsDisplayed
}

// Validate 验证配置
func (r *roomTypeImpl) Validate() error {
	if err := r.BaseConfig.Validate(); err != nil {
		return err
	}

	if r.value.VenueID == "" {
		return baseModel.ErrInvalidVenueID
	}
	if r.value.Name == "" {
		return baseModel.ErrInvalidName
	}
	if r.value.Capacity <= 0 {
		return baseModel.ErrInvalidCapacity
	}
	if r.value.MinCharge < 0 {
		return baseModel.ErrInvalidMinCharge
	}
	if r.value.HourPrice < 0 {
		return baseModel.ErrInvalidHourPrice
	}
	if r.value.ServiceFee < 0 || r.value.ServiceFee > 1 {
		return baseModel.ErrInvalidServiceFee
	}

	return nil
}

// Clone 克隆配置
func (r *roomTypeImpl) Clone() baseModel.Config {
	value := &RoomTypeValue{
		VenueID:     r.value.VenueID,
		Name:        r.value.Name,
		Description: r.value.Description,
		Capacity:    r.value.Capacity,
		MinCharge:   r.value.MinCharge,
		HourPrice:   r.value.HourPrice,
		ServiceFee:  r.value.ServiceFee,
		IsDisplayed: r.value.IsDisplayed,
	}

	return &roomTypeImpl{
		BaseConfig: r.BaseConfig.Clone().(*baseModel.BaseConfig),
		value:      value,
	}
}
