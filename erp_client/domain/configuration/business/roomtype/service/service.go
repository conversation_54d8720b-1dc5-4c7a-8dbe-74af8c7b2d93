package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/configuration/base/service"
	"voderpltvv/erp_client/domain/configuration/business/roomtype/model"
)

// Service 包厢类型配置服务接口
type Service interface {
	baseService.Service

	// GetRoomType 获取包厢类型
	GetRoomType(ctx context.Context, id string) (model.RoomType, error)
	// FindByVenueID 根据场所ID查询包厢类型
	FindByVenueID(ctx context.Context, venueID string) ([]model.RoomType, error)
	// FindByName 根据名称查询包厢类型
	FindByName(ctx context.Context, venueID string, name string) (model.RoomType, error)
	// FindDisplayed 查询显示的包厢类型
	FindDisplayed(ctx context.Context, venueID string) ([]model.RoomType, error)
	// UpdateDisplay 更新显示状态
	UpdateDisplay(ctx context.Context, id string, isDisplayed bool) error
	// UpdatePrice 更新价格
	UpdatePrice(ctx context.Context, id string, minCharge float64, hourPrice float64, serviceFee float64) error
	// UpdateCapacity 更新容纳人数
	UpdateCapacity(ctx context.Context, id string, capacity int) error
}
