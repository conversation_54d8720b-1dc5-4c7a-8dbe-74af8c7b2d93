package model

import (
	"fmt"
	"reflect"
	"strconv"
	"time"
)

// TypeConverter 类型转换器
type TypeConverter struct {
	context *RuleContext
}

// NewTypeConverter 创建类型转换器
func NewTypeConverter(context *RuleContext) *TypeConverter {
	return &TypeConverter{
		context: context,
	}
}

// Convert 转换值到目标类型
func (c *TypeConverter) Convert(value interface{}, targetType ValueType) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	c.context.AddDebugLog("开始类型转换: value=%+v, targetType=%s", value, targetType)

	// 获取值的类型
	valueType := reflect.TypeOf(value)
	valueKind := valueType.Kind()

	// 根据目标类型进行转换
	switch targetType {
	case TypeString:
		return c.toString(value, valueKind)
	case TypeInt:
		return c.toInt(value, valueKind)
	case TypeFloat:
		return c.toFloat(value, valueKind)
	case TypeBool:
		return c.toBool(value, valueKind)
	case TypeTime:
		return c.toTime(value, valueKind)
	default:
		return value, nil
	}
}

// toString 转换为字符串
func (c *TypeConverter) toString(value interface{}, kind reflect.Kind) (interface{}, error) {
	switch kind {
	case reflect.String:
		return value, nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return fmt.Sprintf("%d", value), nil
	case reflect.Float32, reflect.Float64:
		return fmt.Sprintf("%f", value), nil
	case reflect.Bool:
		return strconv.FormatBool(value.(bool)), nil
	case reflect.Struct:
		if t, ok := value.(time.Time); ok {
			return t.Format(time.RFC3339), nil
		}
	}
	return fmt.Sprintf("%v", value), nil
}

// toInt 转换为整数
func (c *TypeConverter) toInt(value interface{}, kind reflect.Kind) (interface{}, error) {
	switch kind {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return value, nil
	case reflect.String:
		str := value.(string)
		return strconv.ParseInt(str, 10, 64)
	case reflect.Float32, reflect.Float64:
		f := reflect.ValueOf(value).Float()
		return int64(f), nil
	case reflect.Bool:
		if value.(bool) {
			return int64(1), nil
		}
		return int64(0), nil
	}
	return nil, fmt.Errorf("cannot convert %v to int", value)
}

// toFloat 转换为浮点数
func (c *TypeConverter) toFloat(value interface{}, kind reflect.Kind) (interface{}, error) {
	switch kind {
	case reflect.Float32, reflect.Float64:
		return value, nil
	case reflect.String:
		str := value.(string)
		return strconv.ParseFloat(str, 64)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		i := reflect.ValueOf(value).Int()
		return float64(i), nil
	case reflect.Bool:
		if value.(bool) {
			return float64(1), nil
		}
		return float64(0), nil
	}
	return nil, fmt.Errorf("cannot convert %v to float", value)
}

// toBool 转换为布尔值
func (c *TypeConverter) toBool(value interface{}, kind reflect.Kind) (interface{}, error) {
	switch kind {
	case reflect.Bool:
		return value, nil
	case reflect.String:
		str := value.(string)
		return strconv.ParseBool(str)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		i := reflect.ValueOf(value).Int()
		return i != 0, nil
	case reflect.Float32, reflect.Float64:
		f := reflect.ValueOf(value).Float()
		return f != 0, nil
	}
	return nil, fmt.Errorf("cannot convert %v to bool", value)
}

// toTime 转换为时间
func (c *TypeConverter) toTime(value interface{}, kind reflect.Kind) (interface{}, error) {
	switch kind {
	case reflect.String:
		str := value.(string)
		// 尝试多种时间格式
		formats := []string{
			time.RFC3339,
			"2006-01-02",
			"2006-01-02 15:04:05",
			"15:04:05",
		}
		for _, format := range formats {
			if t, err := time.Parse(format, str); err == nil {
				return t, nil
			}
		}
		return nil, fmt.Errorf("cannot parse time string: %s", str)
	case reflect.Struct:
		if t, ok := value.(time.Time); ok {
			return t, nil
		}
	}
	return nil, fmt.Errorf("cannot convert %v to time", value)
}

// ConvertToType 转换为指定的具体类型
func (c *TypeConverter) ConvertToType(value interface{}, targetType reflect.Type) (interface{}, error) {
	if value == nil {
		return reflect.Zero(targetType).Interface(), nil
	}

	c.context.AddDebugLog("开始类型转换: value=%+v, targetType=%s", value, targetType)

	// 获取值的反射值
	valueValue := reflect.ValueOf(value)

	// 如果类型已经匹配，直接返回
	if valueValue.Type().AssignableTo(targetType) {
		return value, nil
	}

	// 创建目标类型的新值
	targetValue := reflect.New(targetType).Elem()

	// 尝试转换
	switch targetType.Kind() {
	case reflect.String:
		str, err := c.toString(value, valueValue.Kind())
		if err != nil {
			return nil, err
		}
		targetValue.SetString(str.(string))
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		i, err := c.toInt(value, valueValue.Kind())
		if err != nil {
			return nil, err
		}
		targetValue.SetInt(i.(int64))
	case reflect.Float32, reflect.Float64:
		f, err := c.toFloat(value, valueValue.Kind())
		if err != nil {
			return nil, err
		}
		targetValue.SetFloat(f.(float64))
	case reflect.Bool:
		b, err := c.toBool(value, valueValue.Kind())
		if err != nil {
			return nil, err
		}
		targetValue.SetBool(b.(bool))
	case reflect.Struct:
		if targetType == reflect.TypeOf(time.Time{}) {
			t, err := c.toTime(value, valueValue.Kind())
			if err != nil {
				return nil, err
			}
			targetValue.Set(reflect.ValueOf(t))
		} else {
			return nil, fmt.Errorf("unsupported struct type: %s", targetType)
		}
	default:
		return nil, fmt.Errorf("unsupported target type: %s", targetType)
	}

	return targetValue.Interface(), nil
}
