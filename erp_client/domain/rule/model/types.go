package model

import (
	"time"
)

// RuleResult 规则执行结果
type RuleResult struct {
	// 规则ID
	RuleID string
	// 是否匹配
	Matched bool
	// 是否成功
	Success bool
	// 执行效果
	Effect RuleEffect
	// 执行时间
	ExecuteTime time.Time
	// 执行消息
	Message string
	// 执行数据
	Data map[string]interface{}
	// 执行上下文
	Context map[string]interface{}
	// 覆盖的规则ID列表
	Overrides []string
}

// RuleEffect 规则执行效果
type RuleEffect string

const (
	// EffectAllow 允许
	EffectAllow RuleEffect = "allow"
	// EffectDeny 拒绝
	EffectDeny RuleEffect = "deny"
	// EffectOverride 覆盖
	EffectOverride RuleEffect = "override"
)

// NewRuleResult 创建规则执行结果
func NewRuleResult(ruleID string) *RuleResult {
	return &RuleResult{
		RuleID:      ruleID,
		ExecuteTime: time.Now(),
		Data:        make(map[string]interface{}),
		Context:     make(map[string]interface{}),
		Overrides:   make([]string, 0),
	}
}

// IsModified 是否有修改
func (r *RuleResult) IsModified() bool {
	return r.Matched && r.Success && len(r.Data) > 0
}

// GetModifiedData 获取修改的数据
func (r *RuleResult) GetModifiedData() map[string]interface{} {
	if !r.IsModified() {
		return nil
	}
	return r.Data
}

// AddOverride 添加覆盖的规则ID
func (r *RuleResult) AddOverride(ruleID string) {
	r.Overrides = append(r.Overrides, ruleID)
}

// SetData 设置执行数据
func (r *RuleResult) SetData(key string, value interface{}) {
	r.Data[key] = value
}

// GetData 获取执行数据
func (r *RuleResult) GetData(key string) interface{} {
	return r.Data[key]
}

// SetContext 设置执行上下文
func (r *RuleResult) SetContext(key string, value interface{}) {
	r.Context[key] = value
}

// GetContext 获取执行上下文
func (r *RuleResult) GetContext(key string) interface{} {
	return r.Context[key]
}

// MergeContext 合并执行上下文
func (r *RuleResult) MergeContext(ctx map[string]interface{}) {
	for k, v := range ctx {
		r.Context[k] = v
	}
}

// MergeData 合并执行数据
func (r *RuleResult) MergeData(data map[string]interface{}) {
	for k, v := range data {
		r.Data[k] = v
	}
}

// Clone 克隆结果
func (r *RuleResult) Clone() *RuleResult {
	result := NewRuleResult(r.RuleID)
	result.Matched = r.Matched
	result.Success = r.Success
	result.Effect = r.Effect
	result.ExecuteTime = r.ExecuteTime
	result.Message = r.Message
	result.MergeData(r.Data)
	result.MergeContext(r.Context)
	result.Overrides = append(result.Overrides, r.Overrides...)
	return result
}

// Condition 规则条件
type Condition struct {
	Operator string      `yaml:"operator"`
	Left     interface{} `yaml:"left"`
	Right    interface{} `yaml:"right"`
}

// Action 规则动作
type Action struct {
	Type       string                 `yaml:"type"`
	Parameters map[string]interface{} `yaml:"params"`
}
