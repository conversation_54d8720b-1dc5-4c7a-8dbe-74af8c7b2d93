package model

import (
	"fmt"
	"reflect"
	"time"

	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// ValueType 值类型
type ValueType string

const (
	TypeUnknown ValueType = "unknown"
	TypeVO      ValueType = "vo"
	TypeArray   ValueType = "array"
	TypeMap     ValueType = "map"
	TypeString  ValueType = "string"
	TypeInt     ValueType = "int"
	TypeFloat   ValueType = "float"
	TypeBool    ValueType = "bool"
	TypeTime    ValueType = "time"
)

// ValueInfo 值信息
type ValueInfo struct {
	Type     ValueType
	VOType   string      // VO类型名称，如 "vo.BaseInfoVO"
	Required bool        // 是否必需
	Value    interface{} // 实际值
}

// RuleContext 规则上下文
type RuleContext struct {
	store variable.Store
}

// NewRuleContext 创建规则上下文
func NewRuleContext() *RuleContext {
	store := variable.NewStore()
	return &RuleContext{
		store: store,
	}
}

// GetValue 获取变量值
func (c *RuleContext) GetValue(path string) (interface{}, error) {
	return c.store.GetValue(path)
}

// SetValue 设置变量值
func (c *RuleContext) SetValue(path string, value interface{}) error {
	return c.store.SetValue(path, value)
}

// GetData 获取所有数据
func (c *RuleContext) GetData() map[string]interface{} {
	return c.store.GetData()
}

// GetTypeInfo 获取类型信息
func (c *RuleContext) GetTypeInfo(path string) (*variable.TypeInfo, error) {
	// 暂时不支持类型信息
	return nil, nil
}

// SetTypeInfo 设置类型信息
func (c *RuleContext) SetTypeInfo(path string, info *ValueInfo) {
	// 暂时不支持类型信息
}

// GetModified 获取已修改的值
func (c *RuleContext) GetModified() map[string]interface{} {
	// 暂时不支持修改跟踪
	return nil
}

// GetDebugLogs 获取调试日志
func (c *RuleContext) GetDebugLogs() []string {
	// 暂时不支持调试日志
	return nil
}

// AddDebugLog 添加调试日志
func (c *RuleContext) AddDebugLog(format string, args ...interface{}) {
	// 通过设置一个特殊的值来添加日志
	c.SetValue("__debug_log__", fmt.Sprintf(format, args...))
}

// Clone 克隆上下文
func (c *RuleContext) Clone() *RuleContext {
	store := variable.NewStore()
	// 复制数据
	data := c.GetData()
	for k, v := range data {
		store.SetValue(k, v)
	}
	return &RuleContext{
		store: store,
	}
}

// MergeContext 合并上下文
func (c *RuleContext) MergeContext(other *RuleContext) {
	for k, v := range other.GetData() {
		c.store.SetValue(k, v)
	}
}

// GetFieldValue 获取字段值（支持路径表达式）
func (c *RuleContext) GetFieldValue(obj interface{}, fieldPath string) (interface{}, error) {
	if obj == nil {
		return nil, fmt.Errorf("object is nil")
	}
	c.AddDebugLog("获取字段值: obj=%+v, fieldPath=%s", obj, fieldPath)
	return c.store.GetValue(fieldPath)
}

// SetFieldValue 设置字段值（支持路径表达式）
func (c *RuleContext) SetFieldValue(obj interface{}, fieldPath string, value interface{}) error {
	if obj == nil {
		return fmt.Errorf("object is nil")
	}
	c.AddDebugLog("设置字段值: obj=%+v, fieldPath=%s, value=%+v", obj, fieldPath, value)
	return c.store.SetValue(fieldPath, value)
}

// GetString 获取字符串值
func (c *RuleContext) GetString(path string) (string, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return "", err
	}
	if value == nil {
		return "", nil
	}
	if str, ok := value.(string); ok {
		return str, nil
	}
	return "", fmt.Errorf("value is not a string: %v", value)
}

// GetInt 获取整数值
func (c *RuleContext) GetInt(path string) (int64, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return 0, err
	}
	if value == nil {
		return 0, nil
	}
	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int64:
		return v, nil
	case float64:
		return int64(v), nil
	default:
		return 0, fmt.Errorf("value is not an integer: %v", value)
	}
}

// GetFloat 获取浮点数值
func (c *RuleContext) GetFloat(path string) (float64, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return 0, err
	}
	if value == nil {
		return 0, nil
	}
	return value.(float64), nil
}

// GetBool 获取布尔值
func (c *RuleContext) GetBool(path string) (bool, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return false, err
	}
	if value == nil {
		return false, nil
	}
	return value.(bool), nil
}

// GetTime 获取时间值
func (c *RuleContext) GetTime(path string) (time.Time, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return time.Time{}, err
	}
	if value == nil {
		return time.Time{}, nil
	}
	return value.(time.Time), nil
}

// GetObject 获取对象值
func (c *RuleContext) GetObject(path string) (map[string]interface{}, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return nil, err
	}
	if value == nil {
		return nil, nil
	}
	obj, ok := value.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("value at path %s is not an object", path)
	}
	return obj, nil
}

// GetArray 获取数组值
func (c *RuleContext) GetArray(path string) ([]interface{}, error) {
	value, err := c.store.GetValue(path)
	if err != nil {
		return nil, err
	}
	if value == nil {
		return nil, nil
	}
	arr, ok := value.([]interface{})
	if !ok {
		return nil, fmt.Errorf("value at path %s is not an array", path)
	}
	return arr, nil
}

// Rule 规则定义
type Rule struct {
	ID         string                 `json:"id"`
	GroupID    string                 `json:"group_id"`
	Name       string                 `json:"name"`
	Priority   int                    `json:"priority"`
	Condition  string                 `json:"condition"`
	Action     string                 `json:"action"`
	Parameters map[string]interface{} `json:"parameters"`
	ValidFrom  time.Time              `json:"valid_from"`
	ValidTo    time.Time              `json:"valid_to"`
	CreateTime time.Time              `json:"create_time"`
	UpdateTime time.Time              `json:"update_time"`
}

// RuleGroup 规则组定义
type RuleGroup struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Priority   int                    `json:"priority"`
	Rules      []*Rule                `json:"rules"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreateTime time.Time              `json:"create_time"`
	UpdateTime time.Time              `json:"update_time"`
}

// ConvertValue 转换值到指定类型
func (c *RuleContext) ConvertValue(value interface{}, targetType ValueType) (interface{}, error) {
	converter := NewTypeConverter(c)
	return converter.Convert(value, targetType)
}

// ConvertToType 转换值到指定的具体类型
func (c *RuleContext) ConvertToType(value interface{}, targetType reflect.Type) (interface{}, error) {
	converter := NewTypeConverter(c)
	return converter.ConvertToType(value, targetType)
}
