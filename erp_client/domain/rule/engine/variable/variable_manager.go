package variable

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/variable"
	"voderpltvv/erp_client/domain/engine/common/utils"
)

// VariableManager 变量管理器
type VariableManager struct {
	runtime   variable.IRuntime
	parser    *utils.ExpressionParser
	converter *utils.TypeConverter
}

// NewVariableManager 创建变量管理器
func NewVariableManager(runtime variable.IRuntime) *VariableManager {
	manager := &VariableManager{
		runtime:   runtime,
		converter: utils.NewTypeConverter(),
	}
	// 创建表达式解析器,使用manager自身作为变量解析器
	manager.parser = utils.NewExpressionParser(manager, nil)
	return manager
}

// SetVariable 设置变量值
func (m *VariableManager) SetVariable(name string, value interface{}) error {
	return m.runtime.Set(name, value)
}

// GetVariable 获取变量值
func (m *VariableManager) GetVariable(name string) (interface{}, error) {
	return m.runtime.Get(name)
}

// SetVariables 批量设置变量
func (m *VariableManager) SetVariables(variables map[string]interface{}) error {
	for name, value := range variables {
		if err := m.SetVariable(name, value); err != nil {
			return fmt.Errorf("设置变量[%s]失败: %w", name, err)
		}
	}
	return nil
}

// GetVariables 批量获取变量
func (m *VariableManager) GetVariables(names []string) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	for _, name := range names {
		value, err := m.GetVariable(name)
		if err != nil {
			return nil, fmt.Errorf("获取变量[%s]失败: %w", name, err)
		}
		result[name] = value
	}
	return result, nil
}

// EvalExpression 计算表达式
func (m *VariableManager) EvalExpression(expression string) (interface{}, error) {
	// 使用表达式解析器解析表达式
	return m.parser.Parse(expression)
}

// HasVariable 检查变量是否存在
func (m *VariableManager) HasVariable(name string) bool {
	_, err := m.GetVariable(name)
	return err == nil
}

// DeleteVariable 删除变量
func (m *VariableManager) DeleteVariable(name string) error {
	return m.runtime.Set(name, nil)
}

// ClearVariables 清除所有变量
func (m *VariableManager) ClearVariables() error {
	// 创建新的存储并重置运行时
	store := variable.NewStore()
	m.runtime = variable.NewRuntime(store)
	return nil
}

// GetNamespace 获取命名空间下的所有变量
func (m *VariableManager) GetNamespace(namespace string) (map[string]interface{}, error) {
	value, err := m.runtime.Get(namespace)
	if err != nil {
		return nil, fmt.Errorf("获取命名空间[%s]失败: %w", namespace, err)
	}

	if result, ok := value.(map[string]interface{}); ok {
		return result, nil
	}

	return nil, fmt.Errorf("命名空间[%s]类型错误", namespace)
}

// ConvertValue 转换值类型
func (m *VariableManager) ConvertValue(value interface{}, targetType string) (interface{}, error) {
	switch targetType {
	case "string":
		return m.converter.ToString(value)
	case "int":
		return m.converter.ToInt(value)
	case "float":
		return m.converter.ToFloat(value)
	case "bool":
		return m.converter.ToBool(value)
	case "slice":
		return m.converter.ToSlice(value)
	case "map":
		return m.converter.ToMap(value)
	default:
		return nil, fmt.Errorf("不支持的类型转换: %s", targetType)
	}
}
