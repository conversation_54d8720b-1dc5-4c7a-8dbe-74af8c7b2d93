package parser

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common"
)

// RuleParserAdapter 规则解析器适配器
type RuleParserAdapter struct {
	*common.BaseYAMLParser
	ruleParser *RuleParser
}

// NewRuleParserAdapter 创建规则解析器适配器
func NewRuleParserAdapter(content []byte) *RuleParserAdapter {
	baseParser := common.NewBaseYAMLParser(nil)
	ruleParser := NewRuleParser(content)

	return &RuleParserAdapter{
		BaseYAMLParser: baseParser,
		ruleParser:     ruleParser,
	}
}

// Parse 解析规则内容
func (p *RuleParserAdapter) Parse(content []byte) error {
	// 首先调用基础实现的Parse
	if err := p.BaseYAMLParser.Parse(content); err != nil {
		return fmt.Errorf("基础解析失败: %w", err)
	}

	// 创建新的规则解析器
	p.ruleParser = NewRuleParser(content)
	return nil
}

// ParseMetadata 解析元数据
func (p *RuleParserAdapter) ParseMetadata() (*types.Metadata, error) {
	// 解析规则组
	ruleContent, err := p.ruleParser.ParseRuleGroup()
	if err != nil {
		return nil, fmt.Errorf("解析规则组失败: %w", err)
	}

	return ruleContent.Metadata, nil
}

// GetRuleContent 获取规则内容
func (p *RuleParserAdapter) GetRuleContent() (*types.RuleContent, error) {
	return p.ruleParser.ParseRuleGroup()
}
