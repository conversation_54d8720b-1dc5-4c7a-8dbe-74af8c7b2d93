package parser

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/yaml/parser"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common/utils"
	"voderpltvv/erp_client/domain/process/model"
)

// RuleParser 规则解析器
type RuleParser struct {
	content    []byte
	parser     *utils.ExpressionParser
	yamlParser *parser.RuleParser
}

// NewRuleParser 创建规则解析器
func NewRuleParser(content []byte) *RuleParser {
	p := &RuleParser{
		content:    content,
		yamlParser: parser.NewRuleParser(content),
	}
	// 创建表达式解析器,使用parser自身作为变量解析器
	p.parser = utils.NewExpressionParser(p, nil)
	return p
}

// GetVariable 实现VariableResolver接口
func (p *RuleParser) GetVariable(name string) (interface{}, error) {
	// 在解析阶段,变量解析直接返回变量名
	return fmt.Sprintf("${%s}", name), nil
}

// ParseRuleGroup 解析规则组
func (p *RuleParser) ParseRuleGroup() (*types.RuleContent, error) {
	fmt.Printf("[ParseRuleGroup] 开始解析规则组\n")

	// 使用YAML解析器解析规则内容
	parsedContent, err := p.yamlParser.ParseContent()
	if err != nil {
		fmt.Printf("[ParseRuleGroup] 解析规则组内容失败: %v\n", err)
		return nil, fmt.Errorf("解析规则组内容失败: %w", err)
	}

	// 转换为types.RuleContent
	ruleContent := &types.RuleContent{
		Definition: parsedContent.Definition,
		Metadata:   parsedContent.Metadata,
		Rules:      parsedContent.Rules,
	}

	// 处理规则组中的表达式
	if err := p.processRuleContent(ruleContent); err != nil {
		fmt.Printf("[ParseRuleGroup] 处理规则组表达式失败: %v\n", err)
		return nil, fmt.Errorf("处理规则组表达式失败: %w", err)
	}

	return ruleContent, nil
}

// processRuleContent 处理规则内容中的表达式
func (p *RuleParser) processRuleContent(content *types.RuleContent) error {
	// 处理规则组中的每个规则
	for i := range content.Rules {
		rule := &content.Rules[i]

		// 处理条件表达式
		if rule.Condition != "" {
			processed, err := p.parser.Parse(rule.Condition)
			if err != nil {
				return fmt.Errorf("处理规则条件表达式失败: %w", err)
			}
			if processedStr, ok := processed.(string); ok {
				rule.Condition = processedStr
			}
		}

		// 处理动作参数中的表达式
		for j := range rule.Actions {
			action := rule.Actions[j]
			if err := p.processActionParams(action); err != nil {
				return fmt.Errorf("处理动作参数失败: %w", err)
			}
		}
	}

	return nil
}

// processActionParams 处理动作参数中的表达式
func (p *RuleParser) processActionParams(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return nil
	}

	for key, value := range params {
		processed, err := p.processValue(value)
		if err != nil {
			return fmt.Errorf("处理参数[%s]失败: %w", key, err)
		}
		params[key] = processed
	}

	return nil
}

// processValue 处理值中的表达式
func (p *RuleParser) processValue(value interface{}) (interface{}, error) {
	switch v := value.(type) {
	case string:
		// 使用表达式解析器解析字符串值
		return p.parser.Parse(v)
	case map[string]interface{}:
		// 递归处理map中的值
		for key, val := range v {
			processed, err := p.processValue(val)
			if err != nil {
				return nil, err
			}
			v[key] = processed
		}
		return v, nil
	case []interface{}:
		// 递归处理数组中的值
		for i, val := range v {
			processed, err := p.processValue(val)
			if err != nil {
				return nil, err
			}
			v[i] = processed
		}
		return v, nil
	default:
		// 其他类型直接返回
		return v, nil
	}
}

// ConvertFields 转换字段定义
func ConvertFields(fields []model.Field) []*variable.FieldDefinition {
	if len(fields) == 0 {
		return nil
	}

	result := make([]*variable.FieldDefinition, len(fields))
	for i, f := range fields {
		result[i] = &variable.FieldDefinition{
			Name:     f.Name,
			Required: f.Required,
		}
	}
	return result
}
