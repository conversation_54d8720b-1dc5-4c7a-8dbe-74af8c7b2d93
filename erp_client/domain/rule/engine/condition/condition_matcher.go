package condition

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/variable"
)

// ConditionMatcher 条件匹配器
type ConditionMatcher struct {
	runtime variable.IRuntime
}

// NewConditionMatcher 创建条件匹配器
func NewConditionMatcher(runtime variable.IRuntime) *ConditionMatcher {
	return &ConditionMatcher{
		runtime: runtime,
	}
}

// Match 匹配条件
func (m *ConditionMatcher) Match(condition string) (bool, error) {
	if condition == "" {
		return true, nil // 空条件默认为真
	}

	// 评估条件表达式
	result, err := m.runtime.Eval(condition)
	if err != nil {
		return false, fmt.Errorf("评估条件表达式失败: %w", err)
	}

	// 转换结果为布尔值
	matched, ok := result.(bool)
	if !ok {
		return false, fmt.Errorf("条件表达式结果类型错误，期望布尔类型，实际为: %T", result)
	}

	return matched, nil
}

// MatchWithContext 带上下文的条件匹配
func (m *ConditionMatcher) MatchWithContext(condition string, context map[string]interface{}) (bool, error) {
	if condition == "" {
		return true, nil
	}

	// 设置上下文变量
	for key, value := range context {
		if err := m.runtime.Set(key, value); err != nil {
			return false, fmt.Errorf("设置上下文变量[%s]失败: %w", key, err)
		}
	}

	// 匹配条件
	return m.Match(condition)
}

// MatchMultiple 匹配多个条件
func (m *ConditionMatcher) MatchMultiple(conditions []string) (bool, error) {
	if len(conditions) == 0 {
		return true, nil
	}

	for _, condition := range conditions {
		matched, err := m.Match(condition)
		if err != nil {
			return false, err
		}
		if !matched {
			return false, nil
		}
	}

	return true, nil
}

// MatchAny 匹配任意一个条件
func (m *ConditionMatcher) MatchAny(conditions []string) (bool, error) {
	if len(conditions) == 0 {
		return true, nil
	}

	for _, condition := range conditions {
		matched, err := m.Match(condition)
		if err != nil {
			return false, err
		}
		if matched {
			return true, nil
		}
	}

	return false, nil
}
