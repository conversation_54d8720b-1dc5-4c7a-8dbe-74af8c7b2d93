package metadata

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/metadata"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/yaml/types"
)

// MetadataHandler 元数据处理器
type MetadataHandler struct {
	runtime   variable.IRuntime
	converter metadata.MetadataConverter
}

// NewMetadataHandler 创建元数据处理器
func NewMetadataHandler(runtime variable.IRuntime) *MetadataHandler {
	return &MetadataHandler{
		runtime:   runtime,
		converter: metadata.NewBaseMetadataConverter(),
	}
}

// LoadMetadata 加载元数据
func (h *MetadataHandler) LoadMetadata(metadata *types.Metadata) error {
	if metadata == nil {
		return nil
	}

	// 转换元数据
	runtimeMetadata := h.converter.ConvertToRuntimeMetadata(metadata)

	// 加载元数据到Runtime
	return h.runtime.LoadMetadata(runtimeMetadata)
}

// ValidateInput 验证输入参数
func (h *MetadataHandler) ValidateInput(params map[string]interface{}) error {
	metadata, err := h.runtime.GetMetadata()
	if err != nil {
		return fmt.Errorf("获取元数据失败: %w", err)
	}

	if metadata.Input == nil || len(metadata.Input.Variables) == 0 {
		return nil
	}

	// 验证每个输入变量
	for _, v := range metadata.Input.Variables {
		value, exists := params[v.Name]

		// 检查必需参数
		if v.Required && !exists {
			return fmt.Errorf("缺少必需的输入参数: %s", v.Name)
		}

		if exists {
			// 验证数组类型
			if v.IsArray {
				if err := h.validateArray(v, value); err != nil {
					return fmt.Errorf("验证数组参数[%s]失败: %w", v.Name, err)
				}
			} else if len(v.Fields) > 0 {
				// 验证对象类型
				if err := h.validateObject(v, value); err != nil {
					return fmt.Errorf("验证对象参数[%s]失败: %w", v.Name, err)
				}
			}
		}
	}

	return nil
}

// validateArray 验证数组类型参数
func (h *MetadataHandler) validateArray(def *variable.VariableDefinition, value interface{}) error {
	array, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("类型错误，期望是数组类型")
	}

	// 如果有字段定义，验证每个元素
	if len(def.Fields) > 0 {
		for i, item := range array {
			obj, ok := item.(map[string]interface{})
			if !ok {
				return fmt.Errorf("第%d个元素类型错误，期望是对象类型", i+1)
			}

			// 验证对象字段
			for _, field := range def.Fields {
				fieldValue, exists := obj[field.Name]
				if field.Required && !exists {
					return fmt.Errorf("第%d个元素缺少必需字段: %s", i+1, field.Name)
				}
				if exists && fieldValue == nil && field.Required {
					return fmt.Errorf("第%d个元素的字段[%s]不能为空", i+1, field.Name)
				}
			}
		}
	}

	return nil
}

// validateObject 验证对象类型参数
func (h *MetadataHandler) validateObject(def *variable.VariableDefinition, value interface{}) error {
	obj, ok := value.(map[string]interface{})
	if !ok {
		return fmt.Errorf("类型错误，期望是对象类型")
	}

	// 验证每个字段
	for _, field := range def.Fields {
		fieldValue, exists := obj[field.Name]
		if field.Required && !exists {
			return fmt.Errorf("缺少必需字段: %s", field.Name)
		}
		if exists && fieldValue == nil && field.Required {
			return fmt.Errorf("字段[%s]不能为空", field.Name)
		}
	}

	return nil
}

// GetOutputVariables 获取输出变量
func (h *MetadataHandler) GetOutputVariables() (map[string]interface{}, error) {
	metadata, err := h.runtime.GetMetadata()
	if err != nil {
		return nil, fmt.Errorf("获取元数据失败: %w", err)
	}

	if metadata.Output == nil || len(metadata.Output.Variables) == 0 {
		return nil, nil
	}

	result := make(map[string]interface{})
	for _, v := range metadata.Output.Variables {
		outputPath := fmt.Sprintf("%s.%s", variable.OutputNamespace, v.Name)
		value, err := h.runtime.Get(outputPath)
		if err != nil {
			if v.Required {
				return nil, fmt.Errorf("获取必需的输出参数失败 [%s]: %w", v.Name, err)
			}
			continue
		}
		result[v.Name] = value
	}

	return result, nil
}
