package adapter

import (
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common"
)

// RuleDefinitionAdapter 规则定义适配器
type RuleDefinitionAdapter struct {
	definition *types.Definition
	metadata   common.IEngineMetadata
}

// NewRuleDefinitionAdapter 创建规则定义适配器
func NewRuleDefinitionAdapter(definition *types.Definition, metadata *types.Metadata) common.IEngineDefinition {
	return &RuleDefinitionAdapter{
		definition: definition,
		metadata:   NewRuleMetadataAdapter(metadata),
	}
}

// GetID 获取定义ID
func (a *RuleDefinitionAdapter) GetID() string {
	if a.definition == nil {
		return ""
	}
	return a.definition.ID
}

// GetName 获取定义名称
func (a *RuleDefinitionAdapter) GetName() string {
	if a.definition == nil {
		return ""
	}
	return a.definition.Name
}

// GetMetadata 获取元数据
func (a *RuleDefinitionAdapter) GetMetadata() common.IEngineMetadata {
	return a.metadata
}
