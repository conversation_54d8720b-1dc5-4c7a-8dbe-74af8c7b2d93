package adapter

import (
	"fmt"

	"voderpltvv/erp_client/domain/engine/common"
	"voderpltvv/erp_client/domain/rule/engine"
)

// RuleLoaderAdapter 规则加载器适配器
type RuleLoaderAdapter struct {
	engine engine.RuleEngine
}

// NewRuleLoaderAdapter 创建规则加载器适配器
func NewRuleLoaderAdapter(engine engine.RuleEngine) common.IEngineLoader {
	return &RuleLoaderAdapter{
		engine: engine,
	}
}

// Load 加载定义
func (a *RuleLoaderAdapter) Load(content []byte) error {
	if err := a.engine.LoadRuleGroup(content); err != nil {
		return fmt.Errorf("加载规则组失败: %w", err)
	}
	return nil
}
