package adapter

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common"
)

// RuleMetadataAdapter 规则元数据适配器
type RuleMetadataAdapter struct {
	metadata *types.Metadata
}

// NewRuleMetadataAdapter 创建规则元数据适配器
func NewRuleMetadataAdapter(metadata *types.Metadata) common.IEngineMetadata {
	return &RuleMetadataAdapter{
		metadata: metadata,
	}
}

// GetInputVariables 获取输入变量定义
func (a *RuleMetadataAdapter) GetInputVariables() []string {
	if a.metadata == nil || a.metadata.Input == nil {
		return nil
	}

	vars := make([]string, len(a.metadata.Input.Variables))
	for i, v := range a.metadata.Input.Variables {
		vars[i] = v.Name
	}
	return vars
}

// GetOutputVariables 获取输出变量定义
func (a *RuleMetadataAdapter) GetOutputVariables() []string {
	if a.metadata == nil || a.metadata.Output == nil {
		return nil
	}

	vars := make([]string, len(a.metadata.Output.Variables))
	for i, v := range a.metadata.Output.Variables {
		vars[i] = v.Name
	}
	return vars
}

// ValidateInput 验证输入参数
func (a *RuleMetadataAdapter) ValidateInput(params map[string]interface{}) error {
	if a.metadata == nil || a.metadata.Input == nil {
		return nil
	}

	// 验证必需参数
	for _, v := range a.metadata.Input.Variables {
		if v.Required {
			if _, exists := params[v.Name]; !exists {
				return fmt.Errorf("缺少必需的输入参数: %s", v.Name)
			}
		}
	}

	return nil
}
