package adapter

import (
	"voderpltvv/erp_client/domain/engine/common"
	"voderpltvv/erp_client/domain/rule/model"
)

// RuleContextAdapter 规则上下文适配器
type RuleContextAdapter struct {
	ctx *model.RuleContext
	err error
}

// NewRuleContextAdapter 创建规则上下文适配器
func NewRuleContextAdapter(ctx *model.RuleContext) common.IEngineContext {
	return &RuleContextAdapter{
		ctx: ctx,
	}
}

// GetValue 获取值
func (a *RuleContextAdapter) GetValue(key string) interface{} {
	value, err := a.ctx.GetValue(key)
	if err != nil {
		a.err = err
		return nil
	}
	return value
}

// SetValue 设置值
func (a *RuleContextAdapter) SetValue(key string, value interface{}) {
	if err := a.ctx.SetValue(key, value); err != nil {
		a.err = err
	}
}

// DeleteValue 删除值
func (a *RuleContextAdapter) DeleteValue(key string) {
	// 在原有的 RuleContext 中没有直接的删除方法
	// 我们通过设置 nil 来模拟删除
	if err := a.ctx.SetValue(key, nil); err != nil {
		a.err = err
	}
}

// SetError 设置错误
func (a *RuleContextAdapter) SetError(err error) {
	a.err = err
}

// GetError 获取错误
func (a *RuleContextAdapter) GetError() error {
	return a.err
}

// HasError 是否有错误
func (a *RuleContextAdapter) HasError() bool {
	return a.err != nil
}
