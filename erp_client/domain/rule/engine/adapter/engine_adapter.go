package adapter

import (
	"context"
	"fmt"

	"voderpltvv/erp_client/domain/engine/common"
	"voderpltvv/erp_client/domain/rule/engine"
	"voderpltvv/erp_client/domain/rule/model"
)

// RuleEngineAdapter 规则引擎适配器
type RuleEngineAdapter struct {
	engine engine.RuleEngine
}

// NewRuleEngineAdapter 创建规则引擎适配器
func NewRuleEngineAdapter(engine engine.RuleEngine) common.IEngine {
	return &RuleEngineAdapter{
		engine: engine,
	}
}

// Execute 执行引擎
func (a *RuleEngineAdapter) Execute(ctx context.Context, id string, params map[string]interface{}) (map[string]interface{}, error) {
	// 1. 创建规则上下文
	ruleCtx := model.NewRuleContext()
	if params != nil {
		// 设置输入参数
		if err := ruleCtx.SetValue("input", params); err != nil {
			return nil, fmt.Errorf("设置规则输入参数失败: %w", err)
		}
	}

	// 2. 执行规则引擎
	result, err := a.engine.Execute(ctx, id, params)
	if err != nil {
		return nil, fmt.Errorf("执行规则引擎失败: %w", err)
	}

	return result, nil
}
