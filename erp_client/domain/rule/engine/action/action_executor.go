package action

import (
	"context"
	"fmt"
	"voderpltvv/erp_client/application/framework/variable"
)

// ActionFunc 动作处理函数类型
type ActionFunc func(ctx context.Context, runtime variable.IRuntime, params map[string]interface{}) error

// ActionExecutor 动作执行器
type ActionExecutor struct {
	runtime variable.IRuntime
	actions map[string]ActionFunc
}

// NewActionExecutor 创建动作执行器
func NewActionExecutor(runtime variable.IRuntime) *ActionExecutor {
	return &ActionExecutor{
		runtime: runtime,
		actions: make(map[string]ActionFunc),
	}
}

// RegisterAction 注册动作处理器
func (e *ActionExecutor) RegisterAction(name string, action ActionFunc) {
	e.actions[name] = action
}

// Execute 执行动作
func (e *ActionExecutor) Execute(ctx context.Context, action map[string]interface{}) error {
	actionType, ok := action["type"].(string)
	if !ok {
		return fmt.Errorf("动作类型未定义")
	}

	switch actionType {
	case "set_field":
		return e.executeSetFieldAction(action)
	case "set":
		return e.executeSetAction(action)
	case "calculate":
		return e.executeCalculateAction(action)
	case "call":
		return e.executeCallAction(ctx, action)
	case "return":
		return e.executeReturnAction(action)
	default:
		return fmt.Errorf("不支持的动作类型: %s", actionType)
	}
}

// executeSetFieldAction 执行设置字段动作
func (e *ActionExecutor) executeSetFieldAction(action map[string]interface{}) error {
	field, ok := action["field"].(string)
	if !ok {
		return fmt.Errorf("字段名未定义")
	}

	value, ok := action["value"]
	if !ok {
		return fmt.Errorf("字段值未定义")
	}

	// 如果值是表达式，计算表达式
	if strValue, ok := value.(string); ok {
		result, err := e.runtime.Eval(strValue)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %w", err)
		}
		value = result
	}

	return e.runtime.Set(field, value)
}

// executeSetAction 执行设置动作
func (e *ActionExecutor) executeSetAction(action map[string]interface{}) error {
	target, ok := action["target"].(string)
	if !ok {
		return fmt.Errorf("目标变量未定义")
	}

	value, ok := action["value"]
	if !ok {
		return fmt.Errorf("设置值未定义")
	}

	// 如果值是表达式，计算表达式
	if strValue, ok := value.(string); ok {
		result, err := e.runtime.Eval(strValue)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %w", err)
		}
		value = result
	}

	return e.runtime.Set(target, value)
}

// executeCalculateAction 执行计算动作
func (e *ActionExecutor) executeCalculateAction(action map[string]interface{}) error {
	expression, ok := action["expression"].(string)
	if !ok {
		return fmt.Errorf("表达式未定义")
	}

	target, ok := action["target"].(string)
	if !ok {
		return fmt.Errorf("目标变量未定义")
	}

	result, err := e.runtime.Eval(expression)
	if err != nil {
		return fmt.Errorf("计算表达式失败: %w", err)
	}

	return e.runtime.Set(target, result)
}

// executeCallAction 执行调用动作
func (e *ActionExecutor) executeCallAction(ctx context.Context, action map[string]interface{}) error {
	actionName, ok := action["action"].(string)
	if !ok {
		return fmt.Errorf("动作名称未定义")
	}

	actionFunc, exists := e.actions[actionName]
	if !exists {
		return fmt.Errorf("动作未注册: %s", actionName)
	}

	params, ok := action["params"].(map[string]interface{})
	if !ok {
		params = make(map[string]interface{})
	}

	// 处理参数中的表达式
	for name, value := range params {
		if strValue, ok := value.(string); ok {
			// 如果参数值是表达式，计算表达式
			result, err := e.runtime.Eval(strValue)
			if err != nil {
				return fmt.Errorf("计算参数表达式失败 [%s]: %w", name, err)
			}
			params[name] = result
		}
	}

	return actionFunc(ctx, e.runtime, params)
}

// executeReturnAction 执行返回动作
func (e *ActionExecutor) executeReturnAction(action map[string]interface{}) error {
	value, ok := action["value"]
	if !ok {
		return fmt.Errorf("返回值未定义")
	}

	// 如果值是表达式，计算表达式
	if strValue, ok := value.(string); ok {
		result, err := e.runtime.Eval(strValue)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %w", err)
		}
		value = result
	}

	return e.runtime.Set("return", value)
}
