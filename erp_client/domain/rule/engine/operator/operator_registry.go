package operator

import (
	"fmt"
	"sync"
)

// OperatorFunc 操作符函数类型
type OperatorFunc func(args ...interface{}) (interface{}, error)

// Registry 操作符注册表
type Registry struct {
	mu        sync.RWMutex
	operators map[string]OperatorFunc
}

// NewRegistry 创建新的操作符注册表
func NewRegistry() *Registry {
	return &Registry{
		operators: make(map[string]OperatorFunc),
	}
}

// Register 注册操作符
func (r *Registry) Register(name string, op OperatorFunc) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.operators[name]; exists {
		return fmt.Errorf("操作符[%s]已存在", name)
	}

	r.operators[name] = op
	return nil
}

// Get 获取操作符
func (r *Registry) Get(name string) (OperatorFunc, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	op, exists := r.operators[name]
	if !exists {
		return nil, fmt.Errorf("操作符[%s]不存在", name)
	}

	return op, nil
}

// Has 检查操作符是否存在
func (r *Registry) Has(name string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.operators[name]
	return exists
}

// List 列出所有已注册的操作符
func (r *Registry) List() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var names []string
	for name := range r.operators {
		names = append(names, name)
	}
	return names
}

// Unregister 注销操作符
func (r *Registry) Unregister(name string) {
	r.mu.Lock()
	defer r.mu.Unlock()

	delete(r.operators, name)
}

// Clear 清除所有操作符
func (r *Registry) Clear() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.operators = make(map[string]OperatorFunc)
}
