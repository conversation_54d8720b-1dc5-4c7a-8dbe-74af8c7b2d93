package operator

import (
	"fmt"
	"reflect"
	"time"
)

// Executor 操作符执行器
type Executor struct {
	registry *Registry
}

// NewExecutor 创建新的操作符执行器
func NewExecutor(registry *Registry) *Executor {
	executor := &Executor{
		registry: registry,
	}
	executor.registerBuiltinOperators()
	return executor
}

// Execute 执行操作符
func (e *Executor) Execute(name string, args ...interface{}) (interface{}, error) {
	op, err := e.registry.Get(name)
	if err != nil {
		return nil, err
	}

	return op(args...)
}

// registerBuiltinOperators 注册内置操作符
func (e *Executor) registerBuiltinOperators() {
	e.registry.Register("timeMatch", e.timeMatch)
	e.registry.Register("holidayMatch", e.holidayMatch)
	e.registry.Register("hasField", e.hasField)
}

// timeMatch 时间匹配操作符
func (e *Executor) timeMatch(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return nil, fmt.Errorf("timeMatch需要2个参数")
	}

	ctx, ok := args[0].(map[string]interface{})
	if !ok {
		return false, fmt.Errorf("无效的上下文类型")
	}

	plan, ok := args[1].(map[string]interface{})
	if !ok {
		return false, fmt.Errorf("无效的价格计划类型")
	}

	// 获取当前时间信息
	currentTime := ctx["current_time"].(string)
	currentWeek := ctx["current_week"].(int)
	currentDate := ctx["current_date"].(string)

	// 解析当前时间
	now, err := time.Parse("15:04", currentTime)
	if err != nil {
		return false, fmt.Errorf("无效的当前时间格式: %w", err)
	}

	// 检查时间类型
	timeType := plan["timeType"].(string)
	switch timeType {
	case "week":
		// 检查星期是否匹配
		weeks := plan["weeks"].([]int)
		weekMatch := false
		for _, w := range weeks {
			if w == currentWeek {
				weekMatch = true
				break
			}
		}
		if !weekMatch {
			return false, nil
		}

	case "date":
		// 检查日期范围
		startDate := plan["dayStart"].(string)
		endDate := plan["dayEnd"].(string)
		if currentDate < startDate || currentDate > endDate {
			return false, nil
		}
	}

	// 检查时间范围
	startTime, err := time.Parse("15:04", plan["hourMinuteStart"].(string))
	if err != nil {
		return false, fmt.Errorf("无效的开始时间格式: %w", err)
	}

	endTime, err := time.Parse("15:04", plan["hourMinuteEnd"].(string))
	if err != nil {
		return false, fmt.Errorf("无效的结束时间格式: %w", err)
	}

	// 处理跨天的情况
	if endTime.Before(startTime) {
		return now.After(startTime) || now.Before(endTime), nil
	}

	return now.After(startTime) && now.Before(endTime), nil
}

// holidayMatch 节假日匹配操作符
func (e *Executor) holidayMatch(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return nil, fmt.Errorf("holidayMatch需要2个参数")
	}

	currentDate, ok := args[0].(string)
	if !ok {
		return false, fmt.Errorf("无效的日期类型")
	}

	holidays, ok := args[1].(*[]interface{})
	if !ok {
		return false, fmt.Errorf("无效的节假日类型")
	}

	for _, h := range *holidays {
		holiday, ok := h.(map[string]interface{})
		if !ok {
			continue
		}

		if holiday["date"].(string) == currentDate {
			return true, nil
		}
	}

	return false, nil
}

// hasField 字段存在性检查操作符
func (e *Executor) hasField(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return nil, fmt.Errorf("hasField需要2个参数")
	}

	obj := args[0]
	if obj == nil {
		return false, nil
	}

	fieldName, ok := args[1].(string)
	if !ok {
		return false, fmt.Errorf("无效的字段名类型")
	}

	val := reflect.ValueOf(obj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return false, fmt.Errorf("对象必须是结构体类型")
	}

	field := val.FieldByName(fieldName)
	return field.IsValid(), nil
}
