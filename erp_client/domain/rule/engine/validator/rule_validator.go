package validator

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/yaml/types"
)

// RuleValidator 规则验证器
type RuleValidator struct{}

// NewRuleValidator 创建规则验证器
func NewRuleValidator() *RuleValidator {
	return &RuleValidator{}
}

// ValidateRuleContent 验证规则内容
func (v *RuleValidator) ValidateRuleContent(content *types.RuleContent) error {
	// 检查规则组定义
	if content.Definition == nil {
		fmt.Printf("[ValidateRuleContent] 规则组定义为空\n")
		return fmt.Errorf("规则组定义为空")
	}

	if content.Definition.ID == "" {
		fmt.Printf("[ValidateRuleContent] 规则组ID为空\n")
		return fmt.Errorf("规则组ID为空")
	}

	return nil
}

// ValidateRuleGroupID 验证规则组ID
func (v *RuleValidator) ValidateRuleGroupID(ruleGroupID string, ruleDefMap map[string]*types.RuleContent) error {
	_, exists := ruleDefMap[ruleGroupID]
	if !exists {
		return fmt.Errorf("规则组未定义: %s", ruleGroupID)
	}
	return nil
}
