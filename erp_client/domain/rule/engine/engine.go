package engine

import (
	"context"
	"fmt"
	"time"

	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/yaml/parser"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/process/model"
)

// ActionFunc 动作处理函数类型
type ActionFunc func(ctx context.Context, runtime variable.IRuntime, params map[string]interface{}) error

// RuleEngine 规则引擎接口
type RuleEngine interface {
	// RegisterAction 注册动作处理器
	RegisterAction(name string, action ActionFunc)

	// Execute 执行规则组
	Execute(ctx context.Context, ruleGroupID string, params map[string]interface{}) (map[string]interface{}, error)

	// ExecuteRule 执行单个规则
	ExecuteRule(ctx context.Context, rule *types.Rule, runtime variable.IRuntime) error

	// ExecuteRules 执行多个规则
	ExecuteRules(ctx context.Context, rules []types.Rule, runtime variable.IRuntime) error

	// LoadRuleGroup 加载规则组
	LoadRuleGroup(content []byte) error
}

// RuleEngineImpl 规则引擎实现
type RuleEngineImpl struct {
	ruleDefMap map[string]*types.RuleContent
	actions    map[string]ActionFunc
	runtime    variable.IRuntime
}

// NewRuleEngine 创建规则引擎
func NewRuleEngine() RuleEngine {
	store := variable.NewStore()
	runtime := variable.NewRuntime(store)

	return &RuleEngineImpl{
		ruleDefMap: make(map[string]*types.RuleContent),
		actions:    make(map[string]ActionFunc),
		runtime:    runtime,
	}
}

// convertFields 转换字段定义
func (e *RuleEngineImpl) convertFields(fields []model.Field) []*variable.FieldDefinition {
	if len(fields) == 0 {
		return nil
	}

	result := make([]*variable.FieldDefinition, len(fields))
	for i, f := range fields {
		result[i] = &variable.FieldDefinition{
			Name:     f.Name,
			Required: f.Required,
		}
	}
	return result
}

// LoadRuleGroup 加载规则组
func (e *RuleEngineImpl) LoadRuleGroup(content []byte) error {
	fmt.Printf("[LoadRuleGroup] 开始加载规则组\n")
	ruleParser := parser.NewRuleParser(content)

	// 解析规则内容
	parsedContent, err := ruleParser.ParseContent()
	if err != nil {
		fmt.Printf("[LoadRuleGroup] 解析规则组内容失败: %v\n", err)
		return fmt.Errorf("解析规则组内容失败: %w", err)
	}

	// 转换为 types.RuleContent
	ruleContent := &types.RuleContent{
		Definition: parsedContent.Definition,
		Metadata:   parsedContent.Metadata,
		Rules:      parsedContent.Rules,
	}

	// 检查规则组定义
	if ruleContent.Definition == nil {
		fmt.Printf("[LoadRuleGroup] 规则组定义为空\n")
		return fmt.Errorf("规则组定义为空")
	}

	if ruleContent.Definition.ID == "" {
		fmt.Printf("[LoadRuleGroup] 规则组ID为空\n")
		return fmt.Errorf("规则组ID为空")
	}

	ruleGroupID := ruleContent.Definition.ID
	fmt.Printf("[LoadRuleGroup] 规则组ID: %s\n", ruleGroupID)

	// 转换元数据
	runtimeMetadata := &variable.Metadata{
		Input:   &variable.VariableSection{},
		Output:  &variable.VariableSection{},
		Context: &variable.VariableSection{},
		System:  &variable.VariableSection{},
	}

	// 转换输入变量
	if ruleContent.Metadata != nil && ruleContent.Metadata.Input != nil {
		runtimeMetadata.Input.Variables = make([]*variable.VariableDefinition, len(ruleContent.Metadata.Input.Variables))
		for i, v := range ruleContent.Metadata.Input.Variables {
			runtimeMetadata.Input.Variables[i] = &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       e.convertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				IsArray:      v.IsArray,
			}
		}
	}

	// 转换输出变量
	if ruleContent.Metadata != nil && ruleContent.Metadata.Output != nil {
		runtimeMetadata.Output.Variables = make([]*variable.VariableDefinition, len(ruleContent.Metadata.Output.Variables))
		for i, v := range ruleContent.Metadata.Output.Variables {
			runtimeMetadata.Output.Variables[i] = &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       e.convertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
			}
		}
	}

	// 加载元数据到Runtime
	if err := e.runtime.LoadMetadata(runtimeMetadata); err != nil {
		fmt.Printf("[LoadRuleGroup] 加载规则组元数据失败: %v\n", err)
		return fmt.Errorf("加载规则组元数据失败: %w", err)
	}

	// 保存规则组定义到ruleDefMap
	e.ruleDefMap[ruleGroupID] = ruleContent
	fmt.Printf("[LoadRuleGroup] 规则组[%s]加载完成，规则数量: %d\n", ruleGroupID, len(ruleContent.Rules))

	return nil
}

// RegisterAction 注册动作处理器
func (e *RuleEngineImpl) RegisterAction(name string, action ActionFunc) {
	e.actions[name] = action
}

// Execute 执行规则组
func (e *RuleEngineImpl) Execute(ctx context.Context, ruleGroupID string, params map[string]interface{}) (map[string]interface{}, error) {
	ruleContent, exists := e.ruleDefMap[ruleGroupID]
	if !exists {
		return nil, fmt.Errorf("规则组未定义: %s", ruleGroupID)
	}

	// 清理Runtime状态
	store := variable.NewStore()
	e.runtime = variable.NewRuntime(store)

	// 根据元数据定义设置输入参数
	if ruleContent.Metadata != nil && ruleContent.Metadata.Input != nil {
		for _, v := range ruleContent.Metadata.Input.Variables {
			// 构造完整的输入路径
			inputPath := fmt.Sprintf("%s.%s", variable.InputNamespace, v.Name)

			// 从params中获取对应的值
			value, exists := params[v.Name]

			// 检查必需参数
			if v.Required && !exists {
				return nil, fmt.Errorf("缺少必需的输入参数: %s", v.Name)
			}

			if exists {
				// 如果参数存在，验证并设置值
				if v.IsArray {
					// 处理数组类型
					valueArray, ok := value.([]interface{})
					if !ok {
						return nil, fmt.Errorf("输入参数[%s]类型错误，期望是数组类型", v.Name)
					}

					// 创建结果数组
					resultArray := make([]interface{}, len(valueArray))

					// 处理数组中的每个元素
					for i, item := range valueArray {
						if len(v.Fields) > 0 {
							// 如果有字段定义，处理对象数组
							itemMap, ok := item.(map[string]interface{})
							if !ok {
								return nil, fmt.Errorf("输入参数[%s]的第%d个元素类型错误，期望是对象类型", v.Name, i+1)
							}

							// 创建元素对象
							resultItem := make(map[string]interface{})

							// 检查并设置每个字段
							for _, field := range v.Fields {
								fieldValue, fieldExists := itemMap[field.Name]
								if field.Required && !fieldExists {
									return nil, fmt.Errorf("输入参数[%s]的第%d个元素缺少必需字段: %s", v.Name, i+1, field.Name)
								}
								if fieldExists {
									resultItem[field.Name] = fieldValue
								}
							}
							resultArray[i] = resultItem
						} else {
							// 如果没有字段定义，直接使用元素值
							resultArray[i] = item
						}
					}

					// 一次性设置整个数组
					if err := e.runtime.Set(inputPath, resultArray); err != nil {
						return nil, fmt.Errorf("设置数组参数失败 [%s]: %w", v.Name, err)
					}
				} else {
					// 处理非数组类型
					if len(v.Fields) > 0 {
						// 处理对象类型
						if valueMap, ok := value.(map[string]interface{}); ok {
							// 创建结果对象
							resultObj := make(map[string]interface{})

							// 检查并设置每个字段
							for _, field := range v.Fields {
								fieldValue, fieldExists := valueMap[field.Name]
								if field.Required && !fieldExists {
									return nil, fmt.Errorf("输入参数[%s]缺少必需字段: %s", v.Name, field.Name)
								}
								if fieldExists {
									resultObj[field.Name] = fieldValue
								}
							}

							// 一次性设置整个对象
							if err := e.runtime.Set(inputPath, resultObj); err != nil {
								return nil, fmt.Errorf("设置输入参数失败 [%s]: %w", v.Name, err)
							}
						} else {
							// 如果不是map类型，直接设置值
							if err := e.runtime.Set(inputPath, value); err != nil {
								return nil, fmt.Errorf("设置输入参数失败 [%s]: %w", v.Name, err)
							}
						}
					} else {
						// 如果没有字段定义，直接设置值
						if err := e.runtime.Set(inputPath, value); err != nil {
							return nil, fmt.Errorf("设置输入参数失败 [%s]: %w", v.Name, err)
						}
					}
				}
			} else if v.DefaultValue != "" {
				// 如果参数不存在但有默认值，计算并设置默认值
				defaultValue, err := e.runtime.Eval(v.DefaultValue)
				if err != nil {
					return nil, fmt.Errorf("计算输入参数[%s]默认值失败: %w", v.Name, err)
				}
				if err := e.runtime.Set(inputPath, defaultValue); err != nil {
					return nil, fmt.Errorf("设置输入参数默认值失败 [%s]: %w", v.Name, err)
				}
			}
		}
	}

	// 设置系统变量
	if err := e.runtime.Set("system.startTime", time.Now()); err != nil {
		return nil, fmt.Errorf("设置系统变量失败: %w", err)
	}
	if err := e.runtime.Set("system.ruleGroupID", ruleGroupID); err != nil {
		return nil, fmt.Errorf("设置系统变量失败: %w", err)
	}

	// 初始化输出变量的默认值
	if ruleContent.Metadata != nil && ruleContent.Metadata.Output != nil {
		for _, v := range ruleContent.Metadata.Output.Variables {
			if v.DefaultValue != "" {
				// 计算默认值表达式
				result, err := e.runtime.Eval(v.DefaultValue)
				if err != nil {
					return nil, fmt.Errorf("计算输出变量[%s]默认值失败: %w", v.Name, err)
				}
				// 设置默认值
				path := fmt.Sprintf("%s.%s", variable.OutputNamespace, v.Name)
				if err := e.runtime.Set(path, result); err != nil {
					return nil, fmt.Errorf("设置输出变量[%s]默认值失败: %w", v.Name, err)
				}
			}
		}
	}

	// 执行规则
	if err := e.ExecuteRules(ctx, ruleContent.Rules, e.runtime); err != nil {
		return nil, err
	}

	// 获取输出
	output, err := e.runtime.Get("output")
	if err != nil {
		return nil, fmt.Errorf("获取输出失败: %w", err)
	}

	result, ok := output.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("输出数据类型错误")
	}

	return result, nil
}

// ExecuteRule 执行单个规则
func (e *RuleEngineImpl) ExecuteRule(ctx context.Context, rule *types.Rule, runtime variable.IRuntime) error {
	// 评估条件
	matched, err := runtime.Eval(rule.Condition)
	if err != nil {
		return fmt.Errorf("评估条件失败: %w", err)
	}

	if !matched.(bool) {
		return nil // 条件不满足，跳过规则
	}

	// 执行动作列表
	for _, action := range rule.Actions {
		actionType := action["type"].(string)
		switch actionType {
		case string(types.RuleActionTypeSetField):
			if err := e.executeSetFieldAction(action, runtime); err != nil {
				return fmt.Errorf("执行设置字段动作失败: %w", err)
			}
		case string(types.RuleActionTypeSet):
			if err := e.executeSetAction(action, runtime); err != nil {
				return fmt.Errorf("执行设置动作失败: %w", err)
			}
		case string(types.RuleActionTypeCalculate):
			if err := e.executeCalculateAction(action, runtime); err != nil {
				return fmt.Errorf("执行计算动作失败: %w", err)
			}
		case string(types.RuleActionTypeCall):
			if err := e.executeCallAction(ctx, action, runtime); err != nil {
				return fmt.Errorf("执行调用函数动作失败: %w", err)
			}
		case string(types.RuleActionTypeReturn):
			if err := e.executeReturnAction(action, runtime); err != nil {
				return fmt.Errorf("执行返回动作失败: %w", err)
			}
		default:
			return fmt.Errorf("不支持的动作类型: %s", actionType)
		}
	}

	return nil
}

// ExecuteRules 执行多个规则
func (e *RuleEngineImpl) ExecuteRules(ctx context.Context, rules []types.Rule, runtime variable.IRuntime) error {
	for _, rule := range rules {
		if err := e.ExecuteRule(ctx, &rule, runtime); err != nil {
			return fmt.Errorf("执行规则[%s]失败: %w", rule.Name, err)
		}
	}
	return nil
}

// executeSetFieldAction 执行设置字段动作
func (e *RuleEngineImpl) executeSetFieldAction(action map[string]interface{}, runtime variable.IRuntime) error {
	target := action["target"].(string)
	value := action["value"]

	// 如果值是表达式，计算表达式
	if strValue, ok := value.(string); ok {
		result, err := runtime.Eval(strValue)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %w", err)
		}
		value = result
	}

	// 设置字段值
	return runtime.Set(target, value)
}

// executeSetAction 执行设置动作
func (e *RuleEngineImpl) executeSetAction(action map[string]interface{}, runtime variable.IRuntime) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("set动作缺少必需字段: params")
	}

	target := params["target"].(string)
	value := params["value"]

	// 如果值是表达式，计算表达式
	if strValue, ok := value.(string); ok {
		result, err := runtime.Eval(strValue)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %w", err)
		}
		value = result
	}

	// 设置字段值
	return runtime.Set(target, value)
}

// executeCalculateAction 执行计算动作
func (e *RuleEngineImpl) executeCalculateAction(action map[string]interface{}, runtime variable.IRuntime) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("calculate动作缺少必需字段: params")
	}

	target := params["target"].(string)
	expression := params["expression"].(string)

	// 计算表达式
	result, err := runtime.Eval(expression)
	if err != nil {
		return fmt.Errorf("计算表达式失败: %w", err)
	}

	// 设置计算结果
	return runtime.Set(target, result)
}

// executeCallAction 执行调用函数动作
func (e *RuleEngineImpl) executeCallAction(ctx context.Context, action map[string]interface{}, runtime variable.IRuntime) error {
	function := action["function"].(string)
	actionFunc, exists := e.actions[function]
	if !exists {
		return fmt.Errorf("函数未注册: %s", function)
	}

	// 处理参数
	params := make(map[string]interface{})
	if paramsMap, ok := action["params"].(map[string]interface{}); ok {
		for name, value := range paramsMap {
			if strValue, ok := value.(string); ok {
				// 如果参数值是表达式，计算表达式
				result, err := runtime.Eval(strValue)
				if err != nil {
					return fmt.Errorf("计算参数表达式失败 [%s]: %w", name, err)
				}
				params[name] = result
			} else {
				params[name] = value
			}
		}
	}

	return actionFunc(ctx, runtime, params)
}

// executeReturnAction 执行返回动作
func (e *RuleEngineImpl) executeReturnAction(action map[string]interface{}, runtime variable.IRuntime) error {
	value := action["value"]

	// 如果值是表达式，计算表达式
	if strValue, ok := value.(string); ok {
		result, err := runtime.Eval(strValue)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %w", err)
		}
		value = result
	}

	// 设置返回值
	return runtime.Set("output", value)
}
