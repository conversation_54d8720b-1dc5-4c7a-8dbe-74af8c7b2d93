package executor

import (
	"context"
	"fmt"
	"voderpltvv/erp_client/application/framework/variable"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common/utils"
)

// ActionFunc 动作处理函数类型
type ActionFunc func(ctx context.Context, runtime variable.IRuntime, params map[string]interface{}) error

// RuleExecutor 规则执行器
type RuleExecutor struct {
	actions   map[string]ActionFunc
	parser    *utils.ExpressionParser
	converter *utils.TypeConverter
}

// NewRuleExecutor 创建规则执行器
func NewRuleExecutor() *RuleExecutor {
	executor := &RuleExecutor{
		actions:   make(map[string]ActionFunc),
		converter: utils.NewTypeConverter(),
	}
	// 创建表达式解析器,使用executor自身作为变量解析器
	executor.parser = utils.NewExpressionParser(executor, nil)
	return executor
}

// RegisterAction 注册动作处理器
func (e *RuleExecutor) RegisterAction(name string, action ActionFunc) {
	e.actions[name] = action
}

// GetVariable 实现VariableResolver接口
func (e *RuleExecutor) GetVariable(name string) (interface{}, error) {
	// TODO: 从运行时获取变量值
	return nil, fmt.Errorf("未实现")
}

// ExecuteRule 执行单个规则
func (e *RuleExecutor) ExecuteRule(ctx context.Context, rule *types.Rule, runtime variable.IRuntime) error {
	if rule == nil {
		return fmt.Errorf("规则为空")
	}

	// 执行规则中的动作
	for _, action := range rule.Actions {
		if err := e.executeAction(ctx, action, runtime); err != nil {
			return err
		}
	}

	return nil
}

// ExecuteRules 执行多个规则
func (e *RuleExecutor) ExecuteRules(ctx context.Context, rules []types.Rule, runtime variable.IRuntime) error {
	for _, rule := range rules {
		if err := e.ExecuteRule(ctx, &rule, runtime); err != nil {
			return err
		}
	}
	return nil
}

// executeAction 执行动作
func (e *RuleExecutor) executeAction(ctx context.Context, action map[string]interface{}, runtime variable.IRuntime) error {
	// 获取动作处理器
	actionType, ok := action["type"].(string)
	if !ok {
		return fmt.Errorf("动作类型未定义")
	}

	actionFunc, exists := e.actions[actionType]
	if !exists {
		return fmt.Errorf("未注册的动作类型: %s", actionType)
	}

	// 处理动作参数中的表达式
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		params = make(map[string]interface{})
	}

	processedParams, err := e.processActionParams(params, runtime)
	if err != nil {
		return fmt.Errorf("处理动作参数失败: %w", err)
	}

	// 执行动作
	if err := actionFunc(ctx, runtime, processedParams); err != nil {
		return fmt.Errorf("执行动作失败: %w", err)
	}

	return nil
}

// processActionParams 处理动作参数
func (e *RuleExecutor) processActionParams(params map[string]interface{}, runtime variable.IRuntime) (map[string]interface{}, error) {
	if params == nil {
		return nil, nil
	}

	result := make(map[string]interface{})
	for key, value := range params {
		processed, err := e.processValue(value, runtime)
		if err != nil {
			return nil, fmt.Errorf("处理参数[%s]失败: %w", key, err)
		}
		result[key] = processed
	}

	return result, nil
}

// processValue 处理值
func (e *RuleExecutor) processValue(value interface{}, runtime variable.IRuntime) (interface{}, error) {
	switch v := value.(type) {
	case string:
		// 使用表达式解析器解析字符串值
		return e.parser.Parse(v)
	case map[string]interface{}:
		// 递归处理map中的值
		result := make(map[string]interface{})
		for key, val := range v {
			processed, err := e.processValue(val, runtime)
			if err != nil {
				return nil, err
			}
			result[key] = processed
		}
		return result, nil
	case []interface{}:
		// 递归处理数组中的值
		result := make([]interface{}, len(v))
		for i, val := range v {
			processed, err := e.processValue(val, runtime)
			if err != nil {
				return nil, err
			}
			result[i] = processed
		}
		return result, nil
	default:
		// 其他类型直接返回
		return v, nil
	}
}
