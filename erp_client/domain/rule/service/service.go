package service

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"voderpltvv/erp_client/domain/engine/common"
	"voderpltvv/erp_client/domain/rule/engine"
	"voderpltvv/erp_client/domain/rule/engine/adapter"
	"voderpltvv/erp_client/domain/rule/model"
	"voderpltvv/erp_client/domain/rule/repository"
)

// Service 规则服务接口
type Service interface {
	// Evaluate 评估规则组
	Evaluate(ctx context.Context, ruleGroupId string, ruleCtx *model.RuleContext) (*model.RuleResult, error)

	// LoadRuleGroup 加载规则组
	LoadRuleGroup(ctx context.Context, ruleGroupID string) (*model.RuleGroup, error)

	// Apply 应用单个规则
	Apply(ctx context.Context, ruleID string, ruleCtx *model.RuleContext) (*model.RuleResult, error)

	// Combine 组合多个规则
	Combine(ctx context.Context, ruleIDs []string, ruleCtx *model.RuleContext) (*model.RuleResult, error)

	// CRUD 操作
	Create(ctx context.Context, rule *model.Rule) error
	Update(ctx context.Context, rule *model.Rule) error
	Delete(ctx context.Context, ruleID string) error
	Get(ctx context.Context, ruleID string) (*model.Rule, error)
	Query(ctx context.Context, condition map[string]interface{}) ([]*model.Rule, error)
}

// service Rule服务实现
type service struct {
	repo   repository.Repository
	engine common.IEngine
	loader common.IEngineLoader
}

// NewService 创建Rule服务
func NewService(repo repository.Repository) Service {
	// 创建基础规则引擎
	baseEngine := engine.NewRuleEngine()

	// 创建适配器
	engineAdapter := adapter.NewRuleEngineAdapter(baseEngine)
	loaderAdapter := adapter.NewRuleLoaderAdapter(baseEngine)

	svc := &service{
		repo:   repo,
		engine: engineAdapter,
		loader: loaderAdapter,
	}

	// 初始化时加载所有规则组
	ctx := context.Background()
	if err := svc.loadAllRuleGroups(ctx); err != nil {
		fmt.Printf("[NewService] 加载规则组失败: %v\n", err)
		// 不要 panic，继续运行，让服务可以启动
		// 后续可以通过手动触发重新加载规则
	} else {
		fmt.Println("[NewService] 成功加载所有规则组")
	}

	return svc
}

// loadAllRuleGroups 加载所有规则组
func (s *service) loadAllRuleGroups(ctx context.Context) error {
	// 获取规则组目录
	ruleDir := "erp_client/config/rules"
	files, err := os.ReadDir(ruleDir)
	if err != nil {
		fmt.Printf("[loadAllRuleGroups] 读取规则目录失败: %v\n", err)
		return fmt.Errorf("读取规则目录失败: %w", err)
	}

	loadedCount := 0
	// 加载每个规则文件
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".yaml") {
			content, err := os.ReadFile(ruleDir + "/" + file.Name())
			if err != nil {
				fmt.Printf("[loadAllRuleGroups] 读取规则文件[%s]失败: %v\n", file.Name(), err)
				continue
			}

			if err := s.loader.Load(content); err != nil {
				fmt.Printf("[loadAllRuleGroups] 加载规则文件[%s]失败: %v\n", file.Name(), err)
				continue
			} else {
				fmt.Printf("[loadAllRuleGroups] 成功加载规则文件: %s\n", file.Name())
				loadedCount++
			}
		}
	}

	if loadedCount == 0 {
		return fmt.Errorf("没有成功加载任何规则文件")
	}

	fmt.Printf("[loadAllRuleGroups] 成功加载 %d 个规则文件\n", loadedCount)
	return nil
}

// Evaluate 评估规则组
func (s *service) Evaluate(ctx context.Context, ruleGroupID string, ruleCtx *model.RuleContext) (*model.RuleResult, error) {
	// 1. 检查规则组是否存在
	if _, err := s.LoadRuleGroup(ctx, ruleGroupID); err != nil {
		return nil, fmt.Errorf("加载规则组失败: %w", err)
	}

	// 2. 转换上下文为参数映射
	params := make(map[string]interface{})
	if ruleCtx != nil {
		// 只获取 input 命名空间的数据
		if inputData, err := ruleCtx.GetValue("input"); err == nil {
			if inputMap, ok := inputData.(map[string]interface{}); ok {
				params = inputMap
			}
		}
	}

	// 3. 执行规则引擎
	result, err := s.engine.Execute(ctx, ruleGroupID, params)
	if err != nil {
		return nil, err
	}

	// 4. 转换结果
	ruleResult := &model.RuleResult{
		RuleID:      ruleGroupID,
		Matched:     true,
		Success:     true,
		Effect:      model.EffectAllow,
		ExecuteTime: time.Now(),
		Data:        result,
		Context:     make(map[string]interface{}),
	}

	return ruleResult, nil
}

// LoadRuleGroup 加载规则组
func (s *service) LoadRuleGroup(ctx context.Context, ruleGroupID string) (*model.RuleGroup, error) {
	group, err := s.repo.GetGroup(ctx, ruleGroupID)
	if err != nil {
		return nil, fmt.Errorf("获取规则组失败: %w", err)
	}

	// 加载规则组中的规则
	rules, err := s.repo.FindRulesByGroup(ctx, ruleGroupID)
	if err != nil {
		return nil, fmt.Errorf("获取规则组规则失败: %w", err)
	}

	group.Rules = rules
	return group, nil
}

// Apply 应用单个规则
func (s *service) Apply(ctx context.Context, ruleID string, ruleCtx *model.RuleContext) (*model.RuleResult, error) {
	// 1. 检查规则是否存在
	if _, err := s.repo.GetRule(ctx, ruleID); err != nil {
		return nil, fmt.Errorf("获取规则失败: %w", err)
	}

	// 2. 获取输入参数
	params := make(map[string]interface{})
	if ruleCtx != nil {
		if inputData, err := ruleCtx.GetValue("input"); err == nil {
			if inputMap, ok := inputData.(map[string]interface{}); ok {
				params = inputMap
			}
		}
	}

	// 3. 执行规则
	result, err := s.engine.Execute(ctx, ruleID, params)
	if err != nil {
		return nil, err
	}

	// 4. 构建结果
	return &model.RuleResult{
		RuleID:      ruleID,
		Matched:     true,
		Success:     true,
		Effect:      model.EffectAllow,
		ExecuteTime: time.Now(),
		Data:        result,
		Context:     make(map[string]interface{}),
	}, nil
}

// Combine 组合多个规则
func (s *service) Combine(ctx context.Context, ruleIDs []string, ruleCtx *model.RuleContext) (*model.RuleResult, error) {
	if len(ruleIDs) == 0 {
		return nil, fmt.Errorf("规则ID列表为空")
	}

	// 1. 获取输入参数
	params := make(map[string]interface{})
	if ruleCtx != nil {
		if inputData, err := ruleCtx.GetValue("input"); err == nil {
			if inputMap, ok := inputData.(map[string]interface{}); ok {
				params = inputMap
			}
		}
	}

	// 2. 依次执行每个规则
	combinedResult := make(map[string]interface{})
	for _, ruleID := range ruleIDs {
		result, err := s.engine.Execute(ctx, ruleID, params)
		if err != nil {
			return nil, fmt.Errorf("执行规则 %s 失败: %w", ruleID, err)
		}

		// 合并结果
		for k, v := range result {
			combinedResult[k] = v
		}
	}

	// 3. 构建最终结果
	return &model.RuleResult{
		RuleID:      ruleIDs[0], // 使用第一个规则ID作为组合规则ID
		Matched:     true,
		Success:     true,
		Effect:      model.EffectAllow,
		ExecuteTime: time.Now(),
		Data:        combinedResult,
		Context:     make(map[string]interface{}),
	}, nil
}

// Create 创建规则
func (s *service) Create(ctx context.Context, rule *model.Rule) error {
	// 设置创建时间和更新时间
	now := time.Now()
	rule.CreateTime = now
	rule.UpdateTime = now

	return s.repo.SaveRule(ctx, rule)
}

// Update 更新规则
func (s *service) Update(ctx context.Context, rule *model.Rule) error {
	// 检查规则是否存在
	existingRule, err := s.repo.GetRule(ctx, rule.ID)
	if err != nil {
		return fmt.Errorf("规则不存在: %w", err)
	}

	// 保留创建时间,更新更新时间
	rule.CreateTime = existingRule.CreateTime
	rule.UpdateTime = time.Now()

	return s.repo.SaveRule(ctx, rule)
}

// Delete 删除规则
func (s *service) Delete(ctx context.Context, ruleID string) error {
	return s.repo.DeleteRule(ctx, ruleID)
}

// Get 获取规则
func (s *service) Get(ctx context.Context, ruleID string) (*model.Rule, error) {
	return s.repo.GetRule(ctx, ruleID)
}

// Query 查询规则
func (s *service) Query(ctx context.Context, condition map[string]interface{}) ([]*model.Rule, error) {
	return s.repo.FindRulesByCondition(ctx, condition)
}
