package repository

import (
	"context"

	"voderpltvv/erp_client/domain/rule/model"
)

// Repository 规则仓储接口
type Repository interface {
	// GetGroup 获取规则组
	GetGroup(ctx context.Context, groupID string) (*model.RuleGroup, error)

	// SaveGroup 保存规则组
	SaveGroup(ctx context.Context, group *model.RuleGroup) error

	// DeleteGroup 删除规则组
	DeleteGroup(ctx context.Context, groupID string) error

	// GetRule 获取规则
	GetRule(ctx context.Context, ruleID string) (*model.Rule, error)

	// SaveRule 保存规则
	SaveRule(ctx context.Context, rule *model.Rule) error

	// DeleteRule 删除规则
	DeleteRule(ctx context.Context, ruleID string) error

	// FindRulesByGroup 查找规则组中的规则
	FindRulesByGroup(ctx context.Context, groupID string) ([]*model.Rule, error)

	// FindRulesByCondition 根据条件查找规则
	FindRulesByCondition(ctx context.Context, condition map[string]interface{}) ([]*model.Rule, error)
}
