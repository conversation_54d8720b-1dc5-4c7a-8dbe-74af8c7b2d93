package parser

import (
	"fmt"
	"time"

	"gopkg.in/yaml.v3"

	"voderpltvv/erp_client/domain/rule/model"
)

// YAMLRuleParser YAML规则解析器
type YAMLRuleParser struct{}

// NewYAMLRuleParser 创建YAML规则解析器
func NewYAMLRuleParser() *YAMLRuleParser {
	return &YAMLRuleParser{}
}

// ParseRuleGroup 解析规则组配置
func (p *YAMLRuleParser) ParseRuleGroup(data []byte) (*model.RuleGroup, error) {
	var rawConfig struct {
		RuleGroup struct {
			ID       string `yaml:"id"`
			Name     string `yaml:"name"`
			Priority int    `yaml:"priority"`
			Rules    []struct {
				ID        string                   `yaml:"id"`
				Name      string                   `yaml:"name"`
				Priority  int                      `yaml:"priority"`
				Condition map[string]interface{}   `yaml:"condition"`
				Actions   []map[string]interface{} `yaml:"actions"`
				ValidFrom string                   `yaml:"valid_from,omitempty"`
				ValidTo   string                   `yaml:"valid_to,omitempty"`
			} `yaml:"rules"`
			Metadata map[string]interface{} `yaml:"metadata"`
		} `yaml:"rule_group"`
	}

	if err := yaml.Unmarshal(data, &rawConfig); err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %v", err)
	}

	group := &model.RuleGroup{
		ID:       rawConfig.RuleGroup.ID,
		Name:     rawConfig.RuleGroup.Name,
		Priority: rawConfig.RuleGroup.Priority,
		Rules:    make([]*model.Rule, 0, len(rawConfig.RuleGroup.Rules)),
		Metadata: rawConfig.RuleGroup.Metadata,
	}

	for _, rawRule := range rawConfig.RuleGroup.Rules {
		// 设置默认的时间范围
		validFrom := time.Time{}                                    // 零值表示无限过去
		validTo := time.Date(9999, 12, 31, 23, 59, 59, 0, time.UTC) // 远未来表示无限未来

		// 如果指定了有效期开始时间,则解析
		if rawRule.ValidFrom != "" {
			var err error
			validFrom, err = time.Parse(time.RFC3339, rawRule.ValidFrom)
			if err != nil {
				return nil, fmt.Errorf("invalid valid_from time for rule %s: %v", rawRule.ID, err)
			}
		}

		// 如果指定了有效期结束时间,则解析
		if rawRule.ValidTo != "" {
			var err error
			validTo, err = time.Parse(time.RFC3339, rawRule.ValidTo)
			if err != nil {
				return nil, fmt.Errorf("invalid valid_to time for rule %s: %v", rawRule.ID, err)
			}
		}

		// 将条件转换为字符串
		conditionBytes, err := yaml.Marshal(rawRule.Condition)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal condition for rule %s: %v", rawRule.ID, err)
		}

		// 将动作转换为字符串
		actionBytes, err := yaml.Marshal(rawRule.Actions)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal actions for rule %s: %v", rawRule.ID, err)
		}

		rule := &model.Rule{
			ID:         rawRule.ID,
			GroupID:    group.ID,
			Name:       rawRule.Name,
			Priority:   rawRule.Priority,
			Condition:  string(conditionBytes),
			Action:     string(actionBytes),
			ValidFrom:  validFrom,
			ValidTo:    validTo,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		}

		group.Rules = append(group.Rules, rule)
	}

	group.CreateTime = time.Now()
	group.UpdateTime = time.Now()

	return group, nil
}

// ParseCondition 解析条件
func (p *YAMLRuleParser) ParseCondition(data []byte, condition *model.Condition) error {
	var rawCondition map[string]interface{}
	if err := yaml.Unmarshal(data, &rawCondition); err != nil {
		return fmt.Errorf("解析条件YAML失败: %v", err)
	}

	// 解析操作符
	operator, ok := rawCondition["operator"].(string)
	if !ok {
		return fmt.Errorf("缺少operator字段")
	}
	condition.Operator = operator

	// 解析左操作数
	left, ok := rawCondition["left"]
	if !ok {
		return fmt.Errorf("缺少left字段")
	}
	condition.Left = left

	// 对于exists操作符，right字段是可选的
	if operator != "exists" {
		// 解析右操作数
		right, ok := rawCondition["right"]
		if !ok {
			return fmt.Errorf("缺少right字段")
		}
		condition.Right = right
	} else {
		condition.Right = nil // exists操作符不需要右值
	}

	return nil
}

// ParseAction 解析动作
func (p *YAMLRuleParser) ParseAction(data []byte, action *model.Action) error {
	// 首先尝试解析为动作数组
	var actionArray []map[string]interface{}
	if err := yaml.Unmarshal(data, &actionArray); err == nil {
		// 如果是数组，取第一个动作（目前只支持单个动作）
		if len(actionArray) > 0 {
			rawAction := actionArray[0]
			return p.parseActionObject(rawAction, action)
		}
		return fmt.Errorf("动作数组为空")
	}

	// 如果不是数组，尝试解析为单个动作对象
	var rawAction map[string]interface{}
	if err := yaml.Unmarshal(data, &rawAction); err != nil {
		return fmt.Errorf("解析动作YAML失败: %v", err)
	}

	return p.parseActionObject(rawAction, action)
}

// parseActionObject 解析单个动作对象
func (p *YAMLRuleParser) parseActionObject(rawAction map[string]interface{}, action *model.Action) error {
	// 解析类型
	actionType, ok := rawAction["type"].(string)
	if !ok {
		return fmt.Errorf("缺少type字段")
	}
	action.Type = actionType

	// 解析参数
	params, ok := rawAction["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("缺少params字段")
	}
	action.Parameters = params

	return nil
}
