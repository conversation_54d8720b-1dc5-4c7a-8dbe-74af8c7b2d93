package executor

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"voderpltvv/erp_client/domain/rule/model"
)

// RuleExecutor 规则执行器
type RuleExecutor struct {
	operators map[string]OperatorFunc
	actions   map[string]ActionFunc
}

// OperatorFunc 操作符函数
type OperatorFunc func(left, right interface{}) (bool, error)

// ActionFunc 动作函数
type ActionFunc func(ctx *model.RuleContext, params map[string]interface{}) error

// NewRuleExecutor 创建规则执行器
func NewRuleExecutor() *RuleExecutor {
	executor := &RuleExecutor{
		operators: make(map[string]OperatorFunc),
		actions:   make(map[string]ActionFunc),
	}
	executor.registerDefaultOperators()
	executor.registerDefaultActions()
	return executor
}

// RegisterOperator 注册操作符
func (e *RuleExecutor) RegisterOperator(name string, op OperatorFunc) {
	e.operators[name] = op
}

// RegisterAction 注册动作
func (e *RuleExecutor) RegisterAction(name string, action ActionFunc) {
	e.actions[name] = action
}

// CompareValues 比较值
func (e *RuleExecutor) CompareValues(operator string, left, right interface{}) (bool, error) {
	if op, exists := e.operators[operator]; exists {
		return op(left, right)
	}
	return false, fmt.Errorf("不支持的操作符: %s", operator)
}

// ExecuteAction 执行动作
func (e *RuleExecutor) ExecuteAction(action *model.Action, ctx *model.RuleContext, result *model.RuleResult) error {
	if actionFunc, exists := e.actions[action.Type]; exists {
		if err := actionFunc(ctx, action.Parameters); err != nil {
			return fmt.Errorf("执行动作失败: %v", err)
		}
		result.Success = true
		return nil
	}
	return fmt.Errorf("不支持的动作类型: %s", action.Type)
}

// GetOperator 获取操作符函数
func (e *RuleExecutor) GetOperator(name string) (OperatorFunc, bool) {
	op, exists := e.operators[name]
	return op, exists
}

// GetAction 获取动作函数
func (e *RuleExecutor) GetAction(name string) (ActionFunc, bool) {
	action, exists := e.actions[name]
	return action, exists
}

// registerDefaultOperators 注册默认操作符
func (e *RuleExecutor) registerDefaultOperators() {
	// 基础比较操作符
	e.operators["eq"] = func(left, right interface{}) (bool, error) {
		return reflect.DeepEqual(left, right), nil
	}
	e.operators["ne"] = func(left, right interface{}) (bool, error) {
		return !reflect.DeepEqual(left, right), nil
	}

	// 存在性检查操作符
	e.operators["exists"] = func(left, right interface{}) (bool, error) {
		// 检查左值是否为nil
		if left == nil {
			return false, nil
		}
		// 如果是指针，检查是否为nil
		if reflect.ValueOf(left).Kind() == reflect.Ptr {
			return !reflect.ValueOf(left).IsNil(), nil
		}
		// 如果是空接口，检查是否为nil
		if reflect.ValueOf(left).Kind() == reflect.Interface {
			return !reflect.ValueOf(left).IsNil(), nil
		}
		// 如果是map或slice，检查是否为空
		v := reflect.ValueOf(left)
		if v.Kind() == reflect.Map || v.Kind() == reflect.Slice {
			return v.Len() > 0, nil
		}
		// 其他类型，只要不为nil就认为存在
		return true, nil
	}

	// 数值比较操作符
	e.operators["gt"] = func(left, right interface{}) (bool, error) {
		leftFloat, ok1 := left.(float64)
		rightFloat, ok2 := right.(float64)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("值不能进行顺序比较")
		}
		return leftFloat > rightFloat, nil
	}
	e.operators["lt"] = func(left, right interface{}) (bool, error) {
		leftFloat, ok1 := left.(float64)
		rightFloat, ok2 := right.(float64)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("值不能进行顺序比较")
		}
		return leftFloat < rightFloat, nil
	}
	e.operators["ge"] = func(left, right interface{}) (bool, error) {
		leftFloat, ok1 := left.(float64)
		rightFloat, ok2 := right.(float64)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("值不能进行顺序比较")
		}
		return leftFloat >= rightFloat, nil
	}
	e.operators["le"] = func(left, right interface{}) (bool, error) {
		leftFloat, ok1 := left.(float64)
		rightFloat, ok2 := right.(float64)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("值不能进行顺序比较")
		}
		return leftFloat <= rightFloat, nil
	}

	// 字符串操作符
	e.operators["contains"] = func(left, right interface{}) (bool, error) {
		leftStr, ok1 := left.(string)
		rightStr, ok2 := right.(string)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("contains 操作符只支持字符串")
		}
		return strings.Contains(leftStr, rightStr), nil
	}
	e.operators["startsWith"] = func(left, right interface{}) (bool, error) {
		leftStr, ok1 := left.(string)
		rightStr, ok2 := right.(string)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("startsWith 操作符只支持字符串")
		}
		return strings.HasPrefix(leftStr, rightStr), nil
	}
	e.operators["endsWith"] = func(left, right interface{}) (bool, error) {
		leftStr, ok1 := left.(string)
		rightStr, ok2 := right.(string)
		if !ok1 || !ok2 {
			return false, fmt.Errorf("endsWith 操作符只支持字符串")
		}
		return strings.HasSuffix(leftStr, rightStr), nil
	}

	// 时间操作符
	e.operators["time_between"] = func(left, right interface{}) (bool, error) {
		timeStr, ok := left.(string)
		if !ok {
			return false, fmt.Errorf("time_between 操作符的左值必须是时间字符串")
		}

		config, ok := right.(map[string]interface{})
		if !ok {
			return false, fmt.Errorf("time_between 操作符的右值必须是时间范围配置")
		}

		format, ok := config["format"].(string)
		if !ok {
			return false, fmt.Errorf("time_between 配置缺少 format")
		}

		ranges, ok := config["ranges"].([]interface{})
		if !ok {
			return false, fmt.Errorf("time_between 配置缺少 ranges")
		}

		currentTime, err := time.Parse(format, timeStr)
		if err != nil {
			return false, fmt.Errorf("解析当前时间失败: %v", err)
		}

		for _, r := range ranges {
			rangeMap, ok := r.(map[string]interface{})
			if !ok {
				continue
			}

			startStr, ok := rangeMap["start"].(string)
			if !ok {
				continue
			}

			endStr, ok := rangeMap["end"].(string)
			if !ok {
				continue
			}

			startTime, err := time.Parse(format, startStr)
			if err != nil {
				continue
			}

			endTime, err := time.Parse(format, endStr)
			if err != nil {
				continue
			}

			current := currentTime.Hour()*60 + currentTime.Minute()
			start := startTime.Hour()*60 + startTime.Minute()
			end := endTime.Hour()*60 + endTime.Minute()

			if current >= start && current <= end {
				return true, nil
			}
		}

		return false, nil
	}

	// 日期操作符
	e.operators["date_in"] = func(left, right interface{}) (bool, error) {
		leftStr, ok := left.(string)
		if !ok {
			return false, fmt.Errorf("date_in 操作符的左值必须是字符串")
		}

		rightSlice, ok := right.([]interface{})
		if !ok {
			return false, fmt.Errorf("date_in 操作符的右值必须是字符串数组")
		}

		for _, date := range rightSlice {
			if dateStr, ok := date.(string); ok {
				if leftStr == dateStr {
					return true, nil
				}
			}
		}
		return false, nil
	}

	// 字段相等操作符
	e.operators["field_equals"] = func(left, right interface{}) (bool, error) {
		return reflect.DeepEqual(left, right), nil
	}
}

// registerDefaultActions 注册默认动作
func (e *RuleExecutor) registerDefaultActions() {
	// 设置字段值
	e.actions["set_field"] = func(ctx *model.RuleContext, params map[string]interface{}) error {
		target, ok := params["target"].(string)
		if !ok {
			return fmt.Errorf("缺少target参数")
		}

		value, exists := params["value"]
		if !exists {
			return fmt.Errorf("缺少value参数")
		}

		if err := ctx.SetValue(target, value); err != nil {
			return fmt.Errorf("设置字段值失败: %v", err)
		}
		return nil
	}

	// 更新对象
	e.actions["update_object"] = func(ctx *model.RuleContext, params map[string]interface{}) error {
		target, ok := params["target"].(string)
		if !ok {
			return fmt.Errorf("缺少target参数")
		}

		updates, ok := params["updates"].(map[string]interface{})
		if !ok {
			return fmt.Errorf("缺少updates参数")
		}

		// 获取目标对象
		targetObj, err := ctx.GetValue(target)
		if err != nil {
			return fmt.Errorf("获取目标对象失败: %v", err)
		}

		if targetObj == nil {
			targetObj = make(map[string]interface{})
		}

		// 如果目标对象是map，直接更新
		if targetMap, ok := targetObj.(map[string]interface{}); ok {
			for k, v := range updates {
				targetMap[k] = v
			}
			if err := ctx.SetValue(target, targetMap); err != nil {
				return fmt.Errorf("更新目标对象失败: %v", err)
			}
			return nil
		}

		// 如果目标对象是数组，对每个元素应用更新
		if targetSlice, ok := targetObj.([]interface{}); ok {
			for _, item := range targetSlice {
				if itemMap, ok := item.(map[string]interface{}); ok {
					for k, v := range updates {
						itemMap[k] = v
					}
				}
			}
			if err := ctx.SetValue(target, targetSlice); err != nil {
				return fmt.Errorf("更新目标数组失败: %v", err)
			}
			return nil
		}

		// 如果目标对象是结构体，使用反射更新
		value := reflect.ValueOf(targetObj)
		if value.Kind() == reflect.Ptr {
			value = value.Elem()
		}

		if value.Kind() != reflect.Struct {
			return fmt.Errorf("目标对象必须是map、数组或结构体")
		}

		// 创建新的map来存储更新后的值
		updatedObj := make(map[string]interface{})

		// 复制原有字段
		t := value.Type()
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			updatedObj[field.Name] = value.Field(i).Interface()
		}

		// 应用更新
		for field, update := range updates {
			updatedObj[field] = update
		}

		// 更新上下文
		if err := ctx.SetValue(target, updatedObj); err != nil {
			return fmt.Errorf("更新上下文失败: %v", err)
		}

		return nil
	}

	// 计算动作
	e.actions["calculate"] = func(ctx *model.RuleContext, params map[string]interface{}) error {
		target, ok := params["target"].(string)
		if !ok {
			return fmt.Errorf("缺少target参数")
		}

		expression, ok := params["expression"].(string)
		if !ok {
			return fmt.Errorf("缺少expression参数")
		}

		// 解析表达式
		value, err := e.evaluateExpression(expression, ctx)
		if err != nil {
			return fmt.Errorf("计算表达式失败: %v", err)
		}

		// 设置计算结果
		if err := ctx.SetValue(target, value); err != nil {
			return fmt.Errorf("设置计算结果失败: %v", err)
		}

		return nil
	}
}

// toFloat64 将值转换为float64
func toFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

// evaluateExpression 评估表达式
func (e *RuleExecutor) evaluateExpression(expression string, ctx *model.RuleContext) (interface{}, error) {
	// 支持 sum 函数
	if strings.HasPrefix(expression, "sum(") && strings.HasSuffix(expression, ")") {
		// 提取数组路径
		arrayPath := strings.TrimSuffix(strings.TrimPrefix(expression, "sum("), ")")

		// 处理条件过滤
		var filterCondition string
		if strings.Contains(arrayPath, "[?") && strings.Contains(arrayPath, "]") {
			parts := strings.Split(arrayPath, "[?")
			arrayPath = parts[0]
			filterCondition = strings.TrimSuffix(parts[1], "]")
		}

		// 获取数组值
		array, err := ctx.GetValue(arrayPath)
		if err != nil {
			return nil, fmt.Errorf("获取数组失败: %v", err)
		}

		// 计算总和
		var sum float64
		arrayValue := reflect.ValueOf(array)
		if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
			return nil, fmt.Errorf("表达式必须作用于数组: %s", arrayPath)
		}

		for i := 0; i < arrayValue.Len(); i++ {
			item := arrayValue.Index(i).Interface()

			// 应用过滤条件
			if filterCondition != "" {
				match, err := e.evaluateFilterCondition(filterCondition, item)
				if err != nil {
					return nil, err
				}
				if !match {
					continue
				}
			}

			// 获取数值并累加
			itemValue, err := toFloat64(item)
			if err != nil {
				return nil, fmt.Errorf("无法转换为数值: %v", err)
			}
			sum += itemValue
		}

		return sum, nil
	}

	return nil, fmt.Errorf("不支持的表达式: %s", expression)
}

// evaluateFilterCondition 评估过滤条件
func (e *RuleExecutor) evaluateFilterCondition(condition string, item interface{}) (bool, error) {
	// 解析条件，例如 "type=='ROOMPLAN'"
	parts := strings.Split(condition, "==")
	if len(parts) != 2 {
		return false, fmt.Errorf("不支持的过滤条件格式: %s", condition)
	}

	field := strings.TrimSpace(parts[0])
	expectedValue := strings.Trim(strings.TrimSpace(parts[1]), "'")

	// 获取字段值
	itemMap, ok := item.(map[string]interface{})
	if !ok {
		return false, fmt.Errorf("过滤项必须是对象: %v", item)
	}

	actualValue, exists := itemMap[field]
	if !exists {
		return false, nil
	}

	// 比较值
	return fmt.Sprintf("%v", actualValue) == expectedValue, nil
}

// ConvertValueByType 根据类型转换值
func (e *RuleExecutor) ConvertValueByType(value interface{}, voType string, ruleCtx *model.RuleContext) (interface{}, error) {
	// 创建类型转换器
	converter := model.NewTypeConverter(ruleCtx)

	switch voType {
	case "string":
		return converter.Convert(value, model.TypeString)
	case "number":
		return converter.Convert(value, model.TypeFloat)
	case "boolean":
		return converter.Convert(value, model.TypeBool)
	case "object":
		return e.convertToObject(value)
	case "objectArray":
		return e.convertToObjectArray(value)
	default:
		return value, nil
	}
}

// convertToObject 转换为对象
func (e *RuleExecutor) convertToObject(value interface{}) (map[string]interface{}, error) {
	switch v := value.(type) {
	case map[string]interface{}:
		return v, nil
	case map[interface{}]interface{}:
		// 转换key为string类型
		result := make(map[string]interface{})
		for key, val := range v {
			if strKey, ok := key.(string); ok {
				result[strKey] = val
			}
		}
		return result, nil
	default:
		// 尝试使用反射将结构体转换为map
		if reflect.TypeOf(value).Kind() == reflect.Struct {
			result := make(map[string]interface{})
			val := reflect.ValueOf(value)
			typ := val.Type()
			for i := 0; i < val.NumField(); i++ {
				result[typ.Field(i).Name] = val.Field(i).Interface()
			}
			return result, nil
		}
		return nil, fmt.Errorf("无法转换为对象类型: %T", value)
	}
}

// convertToObjectArray 转换为对象数组
func (e *RuleExecutor) convertToObjectArray(value interface{}) ([]interface{}, error) {
	val := reflect.ValueOf(value)
	if val.Kind() != reflect.Slice && val.Kind() != reflect.Array {
		return nil, fmt.Errorf("无法转换为数组类型: %T", value)
	}

	result := make([]interface{}, val.Len())
	for i := 0; i < val.Len(); i++ {
		item := val.Index(i).Interface()
		// 尝试将每个元素转换为对象
		obj, err := e.convertToObject(item)
		if err != nil {
			return nil, fmt.Errorf("转换数组元素失败: %v", err)
		}
		result[i] = obj
	}
	return result, nil
}

// PrepareValues 预处理值
func (e *RuleExecutor) PrepareValues(left, right interface{}, ruleCtx *model.RuleContext) (interface{}, interface{}, error) {
	// 获取类型转换器
	converter := model.NewTypeConverter(ruleCtx)

	// 尝试将值转换为相同类型
	var leftValue, rightValue interface{}
	var err error

	switch {
	case e.isNumber(left) || e.isNumber(right):
		// 转换为 float64 进行比较
		leftValue, err = converter.Convert(left, "float64")
		if err != nil {
			return nil, nil, fmt.Errorf("转换左值失败: %v", err)
		}
		rightValue, err = converter.Convert(right, "float64")
		if err != nil {
			return nil, nil, fmt.Errorf("转换右值失败: %v", err)
		}
	case e.isBool(left) || e.isBool(right):
		// 转换为布尔值进行比较
		leftValue, err = converter.Convert(left, "bool")
		if err != nil {
			return nil, nil, fmt.Errorf("转换左值失败: %v", err)
		}
		rightValue, err = converter.Convert(right, "bool")
		if err != nil {
			return nil, nil, fmt.Errorf("转换右值失败: %v", err)
		}
	case e.isString(left) || e.isString(right):
		// 转换为字符串进行比较
		leftValue, err = converter.Convert(left, "string")
		if err != nil {
			return nil, nil, fmt.Errorf("转换左值失败: %v", err)
		}
		rightValue, err = converter.Convert(right, "string")
		if err != nil {
			return nil, nil, fmt.Errorf("转换右值失败: %v", err)
		}
	default:
		leftValue = left
		rightValue = right
	}

	return leftValue, rightValue, nil
}

// isNumber 检查值是否为数字类型
func (e *RuleExecutor) isNumber(value interface{}) bool {
	switch value.(type) {
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return true
	}
	return false
}

// isString 检查值是否为字符串类型
func (e *RuleExecutor) isString(value interface{}) bool {
	_, ok := value.(string)
	return ok
}

// isBool 检查值是否为布尔类型
func (e *RuleExecutor) isBool(value interface{}) bool {
	_, ok := value.(bool)
	return ok
}
