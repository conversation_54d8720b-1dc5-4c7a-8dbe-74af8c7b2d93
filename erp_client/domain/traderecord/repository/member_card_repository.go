package repository

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardRepository 会员充值账单仓储接口
type MemberCardRepository interface {
	// FindAllMemberCard 查询所有会员充值账单
	FindAllMemberCard(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberCard, error)
	// ConvertToMemberCardVO 转换为会员充值账单VO
	ConvertToMemberCardVO(ctx context.Context, memberCard po.MemberCard) vo.MemberCardVO
	// ConvertToMemberCard 转换为会员充值账单PO
	ConvertToMemberCard(ctx context.Context, memberCardVO vo.MemberCardVO) po.MemberCard
	// GetMaxMemberCardId 获取最大会员ID
	GetMaxMemberCardId(ctx context.Context) (int, error)
	// SaveMemberCard 保存会员
	SaveMemberCard(ctx context.Context, memberCard po.MemberCard, reqDto req.V3OpenCardReqDto) (po.MemberCard, error)
	// FindById 根据ID查询会员
	FindById(ctx context.Context, id string) (po.MemberCard, error)
	// FindsByVenueId 根据场馆ID查询所有会员
	FindsByVenueId(ctx context.Context, venueId string) ([]po.MemberCard, error)
	// FindsByIdsAndCtime 根据ID查询所有会员
	FindsByIdsAndCtime(ctx context.Context, ids []string, startTime int64, endTime int64) ([]po.MemberCard, error)
}
