package repository

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
)

// PayRepository 支付仓储接口
type PayRepository interface {
	// FindOrdersBySessionId 根据场次ID查询订单
	FindOrdersBySessionId(ctx context.Context, venueId, sessionId string) (*[]po.Order, error)

	// FindOrderRoomPlansBySessionId 根据场次ID查询订单房间计划
	FindOrderRoomPlansBySessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderRoomPlan, error)

	// FindOrderProductsBySessionId 根据场次ID查询订单商品
	FindOrderProductsBySessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderProduct, error)

	// SavePayBill 保存支付账单
	SavePayBill(ctx context.Context, payBill *po.PayBill) error

	// SaveOrderAndPays 保存订单支付关系
	SaveOrderAndPays(ctx context.Context, orderAndPays *[]po.OrderAndPay) error

	// UpdateOrders 更新订单
	UpdateOrders(ctx context.Context, orders *[]po.Order) error

	// UpdateOrderRoomPlans 更新订单房间计划
	UpdateOrderRoomPlans(ctx context.Context, orderRoomPlans *[]po.OrderRoomPlan) error

	// UpdateOrderProducts 更新订单商品
	UpdateOrderProducts(ctx context.Context, orderProducts *[]po.OrderProduct) error

	// SaveBatchTxForPay 批量保存支付数据（事务）
	SaveBatchTxForPay(ctx context.Context, payBill *po.PayBill,
		orderAndPays *[]po.OrderAndPay, orders *[]po.Order,
		orderRoomPlans *[]po.OrderRoomPlan, orderProducts *[]po.OrderProduct) error

	// FindPayBillById 根据ID查询支付账单
	FindPayBillById(ctx context.Context, payId string) (*po.PayBill, error)

	// QueryPaidOrderVOsBySessionId 根据场次ID查询已支付订单
	QueryPaidOrderVOsBySessionId(ctx context.Context, venueId, sessionId string) (int64, error)

	// CalculateManyFee 计算多个费用
	CalculateManyFee(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// CalculateManyFeeForAdditionalOrder 计算多个费用-新增订单
	CalculateManyFeeForAdditionalOrder(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// CalculateManyFeeForPay 计算多个费用-支付-后付
	CalculateManyFeeForPay(ctx context.Context, totalFeeThis int64, opsInDB []vo.OrderProductVO, omsInDB []vo.OrderRoomPlanVO, orderVOs []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// CalculateManyFeeForTransferRoom 计算多个费用-支付-后付
	CalculateManyFeeForTransferRoom(ctx context.Context, orderVOMarkDelete []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// GetOrdersInfoByOrderNos 根据订单号查询订单信息
	GetOrdersInfoByOrderNos(ctx context.Context, orderNos []string, venueId string, sessionId string) ([]vo.OrderVO, error)

	// SaveOrderPayInfoCallbackByPayId 保存支付信息回调
	SaveOrderPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.OrderPayCallbackVO) error

	// SaveOrderPayInfoCallbackByBillIdForFree 保存支付信息回调
	SaveOrderPayInfoCallbackByBillIdForFree(ctx context.Context, billId string) error

	// V3RefundByCash 按现金退款
	V3RefundByCash(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// V3RefundByBack 按原路返回退款
	V3RefundByBack(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// RefundAllOrderBySessionId
	RefundAllOrderBySessionId(ctx context.Context, sessionId string, venueId string) (vo.OrderPOInfoUnionVO, error)

	// GetOrderPOInfoBySessionId 查询session下的所有订单相关PO数据
	GetOrderPOInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO

	// GetPayInfoBySessionId 查询session下的所有支付相关PO数据
	GetPayInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO

	// GetPayInfoBillBackBySessionId 查询session下的所有支付相关PO数据
	GetPayInfoBillBackBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO

	// BuildOrderVOInfoMergedVOs 构造mergevo
	BuildOrderVOInfoMergedVOs(ctx context.Context, oisVO vo.OrderPOInfoUnionVO) []vo.OrderVOInfoMergedVO

	// UpdateSessionInfoUnpaid 更新session信息-未支付
	UpdateSessionInfoUnpaid(ctx context.Context, sessionId string, venueId string) error

	// BuildPayBillVOInfoBackVO 构建backvo,计算每个bill最终通过哪种方式退款多少钱
	BuildPayBillVOInfoBackVO(ctx context.Context, payBills []po.PayBill, payRecords []po.PayRecord, orderAndPays []po.OrderAndPay) []vo.PayBillVOInfoBackVO

	// SavePayBillBack 保存还原账单
	SavePayBillBack(ctx context.Context, payBillPOInfoBackVOs []vo.PayBillPOInfoBackVO) error

	// V3BillBack 还原账单
	V3BillBack(ctx context.Context, reqDto req.V3BillBackReqDto, newPayBillVOInfoBackVOs []vo.PayBillPOInfoBackVO) error

	// V3TransformPayGate 转换支付网关
	V3TransformPayGate(ctx context.Context, reqDto *req.V3QueryOrderPayTransformReqDto, toPayBill *po.PayBill) ([]vo.PayResultVO, error)

	// V3CallPayCallback 乐刷支付回调
	V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error

	// V3CallRefundCallback 乐刷退款回调
	V3CallRefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error

	// TransferLeshuaRefund 乐刷退款
	TransferLeshuaRefund(ctx context.Context, payRecordVO vo.PayRecordVO) error

	// StartLeshuaTimer 启动乐刷支付定时器
	StartLeshuaTimer(ctx context.Context, key string) error

	// SavePayBillInfoBillBackVOs 保存还原账单
	SavePayBillInfoBillBackVOs(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO, reqDto req.V3BillBackReqDto) error

	// SendNATSMessageForRoomStatusChanged 发送NATS消息
	SendNATSMessageForRoomStatusChanged(ctx context.Context, venueId string) error

	// V3RefundByCashRoomPlan 按现金退款
	V3RefundByCashRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// V3RefundByBackRoomPlan 按原路返回退款
	V3RefundByBackRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)
	
	// GetOrderInfoBatchBySessionId 获取session下的所有订单相关PO数据
	GetOrderInfoBatchBySessionId(ctx context.Context, sessionId string, venueId string, roomId string, employeeId string) (vo.ModeOrderInfoBaseSessionPO, error)

	// DoAndSaveCancelOrderOpenInfo 取消开台
	DoAndSaveCancelOrderOpenInfo(ctx context.Context, venueId string, modeOrderInfoBaseSessionPO vo.ModeOrderInfoBaseSessionPO) error
}
