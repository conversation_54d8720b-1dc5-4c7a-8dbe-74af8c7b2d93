package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// OrderRepository 订单仓储接口
type OrderRepository interface {
	// CreateOrdersWithTransaction 在事务中创建订单相关数据
	// 包括：场次信息、订单信息、订单商品信息、订单房间计划、更新房间状态
	CreateOrdersWithTransaction(ctx context.Context, session *po.Session,
		orders *[]po.Order, orderProducts *[]po.OrderProduct,
		orderPricePlan *po.OrderPricePlan, orderRoomPlans *[]po.OrderRoomPlan,
		room *po.Room) error
	// FindAllOrder 查询所有订单
	FindAllOrder(ctx context.Context, venueId, sessionId string) (*[]po.Order, error)
	// GetOrderByOrderNO 查询订单
	GetOrderByOrderNO(ctx context.Context, venueId, orderNO string) (po.Order, error)
	// ConvertToOrderVO 转换为订单VO
	ConvertToOrderVO(ctx context.Context, order po.Order) vo.OrderVO
	// ConvertToOrderPO 转换为订单PO
	ConvertToOrderPO(ctx context.Context, orderVO vo.OrderVO) po.Order

	// 保存 开台-后付
	SaveOrderOpenInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error)
	
	// 保存 开台-立结
	SaveOrderOpenPayInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room, payBill po.PayBill, orderAndPays []po.OrderAndPay, payRecords []po.PayRecord) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error)
	
	// 保存 续台-后付
	SaveOrderOpenContinueInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error)
	
	// 保存 续台-立结
	SaveOrderOpenContinuePayInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room, payBill po.PayBill, orderAndPays []po.OrderAndPay, payRecords []po.PayRecord) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error)
	
	// 保存 点单-后付
	SaveOrderAdditionalInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toAddOrder po.Order, toUpdateOrderProducts []po.OrderProduct) error

	// 保存 点单-立结
	SaveOrderAdditionalPayInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toAddOrder po.Order, toUpdateOrderProducts []po.OrderProduct, toAddOrderAndPays []po.OrderAndPay, toAddPayRecords []po.PayRecord, toAddPayBill po.PayBill) error

	// 保存 转台-后付
	SaveTransferRoomInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toUpdateOrders []po.Order, toAddOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan) (po.Session, []po.Room, error)

	// 保存 转台-立结
	SaveTransferRoomPayInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toUpdateOrders []po.Order, toAddOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan, toAddPayBill *po.PayBill, toAddPayRecords []po.PayRecord, toAddOrderAndPays []po.OrderAndPay) (po.Session, []po.Room, error)

	// 保存 赠送时长
	SaveOrderInfoGiftTime(ctx context.Context, toAddOrder []po.Order, toAddOrderRoomPlans []po.OrderRoomPlan, toUpdateSession po.Session, toAddPayBill []po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddEmployeeGiftRecords []po.EmployeeGiftRecord) error

	// 保存 赠送商品
	SaveOrderInfoGiftProduct(ctx context.Context, toAddOrder []po.Order, toAddOrderProducts []po.OrderProduct, toAddPayBill []po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddEmployeeGiftRecords []po.EmployeeGiftRecord) error

	// 保存 结束时长消费
	SaveInfoEndTimeConsume(ctx context.Context, toUpdateOrderRoomPlans []po.OrderRoomPlan, toAddOrderRoomPlans []po.OrderRoomPlan, toUpdateSessions []po.Session, toUpdateRooms []po.Room) (po.Session, []po.Room, error)

	// 保存 重开
	SaveInfoOrderReopen(ctx context.Context, toUpdateRooms []po.Room, toUpdateSessions []po.Session) ([]po.Room, error)

	// SaveOrderPayInfoPre 保存订单支付信息预处理
	SaveOrderPayInfoPre(ctx context.Context, toUpdateSession po.Session, toUpdateModifyOrderProducts []po.OrderProduct, toUpdateModifyOrderRoomPlans []po.OrderRoomPlan, toAddPayBill po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddPayRecords []po.PayRecord) error

	// IsMatchAllUnpaidOrder 检查是否存在未支付的订单
	IsMatchAllUnpaidOrder(ctx context.Context, orderNos []string, venueId string, sessionId string) (unpaidOrderNos []string, err error)

	// 保存订单信息换台
	SaveOrderInfoSwapRoom(ctx context.Context, newOrderPOInfoUnionVOA vo.OrderPOInfoUnionVO, newOrderPOInfoUnionVOB vo.OrderPOInfoUnionVO, newSessionA po.Session, newRoomA po.Room, newSessionB po.Session, newRoomB po.Room) error

	// 保存订单信息并房
	SaveOrderInfoMergeRoom(ctx context.Context, newOrderPOInfoUnionVOA vo.OrderPOInfoUnionVO, toUpdateRooms []po.Room, toUpdateSessions []po.Session) error

	// 查询所有订单
	FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Order, error)

	// 根据订单号查询订单
	FindOrdersByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.Order, error)

	// 查询最后一个房间操作
	FindLastRoomOperation(ctx context.Context, venueId string, roomIds []string) ([]po.RoomOperation, error)

	// 记录最后一个房间操作
	RecordLastRoomOperation(ctx context.Context, venueId, roomId, sessionId, employeeId, opType, info string) error

	// 根据时间范围查询订单
	FindsByTimeRange(ctx context.Context, venueId string, startTime, endTime int64) ([]po.Order, error)

	// 发送开卡短信
	SendSmsMemberCardOpen(ctx context.Context, venue *po.Venue, memberCard *po.MemberCard) error

	// 发送会员卡充值短信
	SendSmsMemberCardRecharge(ctx context.Context, venue *po.Venue, memberCard *po.MemberCard, currentTotalAmount int64, currentPrincipalAmount int64) error

	// 发送会员卡消费短信
	SendSmsMemberCardConsume(ctx context.Context, venue *po.Venue, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error

	// 发送会员卡退款短信
	SendSmsMemberCardRefund(ctx context.Context, venueId string, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error

	// 同步产品库存
	SyncProductStock(ctx context.Context, venue *po.Venue, orderDirection string, orderProducts []po.OrderProduct) error
}
