package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// PayRecordRepository 支付记录仓储接口
type PayRecordRepository interface {
	// FindAllPayRecord 查询所有支付记录
	FindAllPayRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayRecord, error)
	// ConvertToPayRecordVO 转换为支付记录VO
	ConvertToPayRecordVO(ctx context.Context, payRecord po.PayRecord) vo.PayRecordVO
	// ConvertToPayRecord 转换为支付记录PO
	ConvertToPayRecord(ctx context.Context, payRecordVO vo.PayRecordVO) po.PayRecord
	// FindsBySessionId 查询所有支付记录
	FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayRecord, error)
	// FindsByBillIds 查询所有支付记录
	FindsByBillIds(ctx context.Context, venueId string, billIds []string) ([]po.PayRecord, error)
	// FindByPayId 查询支付记录
	FindByPayId(ctx context.Context, payId string) (*po.PayRecord, error)
}
