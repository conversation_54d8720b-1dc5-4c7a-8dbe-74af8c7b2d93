package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/model"
	"voderpltvv/erp_managent/service/po"
)

// MemberRechargeRecordRepository 会员充值记录仓储接口
type MemberRechargeRecordRepository interface {
	// FindAllMemberRechargeRecord 查询所有会员充值记录
	FindAllMemberRechargeRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberRechargeRecord, error)
	// ConvertToMemberRechargeRecordVO 转换为会员充值记录VO
	ConvertToMemberRechargeRecordVO(ctx context.Context, memberRechargeRecord po.MemberRechargeRecord) vo.MemberRechargeRecordVO
	// ConvertToMemberRechargeRecord 转换为会员充值记录PO
	ConvertToMemberRechargeRecord(ctx context.Context, memberRechargeRecordVO vo.MemberRechargeRecordVO) po.MemberRechargeRecord
	// FindByPayId 根据支付ID查询会员充值记录
	FindByPayId(ctx context.Context, payId string) (po.MemberRechargeRecord, error)
	// SaveMemberRechargeRecordPayInfoCallbackByPayId 保存会员充值记录支付信息回调
	SaveMemberRechargeRecordPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.MemberPayCallbackVO) error
	// V3CallPayCallback 调用支付回调
	V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error
}
