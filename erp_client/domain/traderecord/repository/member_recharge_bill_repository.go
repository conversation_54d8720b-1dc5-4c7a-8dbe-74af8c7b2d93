package repository

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	newModel "voderpltvv/model"
)

// MemberRechargeBillRepository 会员充值账单仓储接口
type MemberRechargeBillRepository interface {
	// FindAllMemberRechargeBill 查询所有会员充值账单
	FindAllMemberRechargeBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberRechargeBill, error)
	// ConvertToMemberRechargeBillVO 转换为会员充值账单VO
	ConvertToMemberRechargeBillVO(ctx context.Context, memberRechargeBill po.MemberRechargeBill) vo.MemberRechargeBillVO
	// ConvertToMemberRechargeBill 转换为会员充值账单PO
	ConvertToMemberRechargeBill(ctx context.Context, memberRechargeBillVO vo.MemberRechargeBillVO) po.MemberRechargeBill
	// V3MemberRechage 会员充值
	V3MemberRechage(ctx context.Context, reqDto req.V3QueryMemberRechargeReqDto) (req.MemberRechargeContext, error)
	// V3MemberPayCallback 会员充值回调
	V3MemberPayCallback(ctx context.Context, reqDto newModel.LeshuaPayCallbackModel) error
	// V3MemberRefundCallback 会员退款回调
	V3MemberRefundCallback(ctx context.Context, reqDto newModel.LeshuaRefundCallbackModel) error

	// FindByBillId 根据ID查询会员充值账单
	FindByBillId(ctx context.Context, venueId, billId string) (po.MemberRechargeBill, error)
	// V3RPCMemberCardQuery 会员信息查询
	V3RPCMemberCardQuery(ctx context.Context, reqDto req.V3QueryMemberCardQueryReqDto) (vo.MemberCardVO, error)
	// V3RPCMemberCardVaildBalance 会员卡余额验证
	V3RPCMemberCardVaildBalance(ctx context.Context, reqDto req.V3QueryMemberCardQueryBalanceReqDto) (vo.MemberCardVO, error)
	// V3RPCMemberCardPay 会员卡消费
	V3RPCMemberCardPay(ctx context.Context, reqDto req.V3RPCPayMoneyReqDto) (req.V3RPCPayMoneyRespDto, error)
}
