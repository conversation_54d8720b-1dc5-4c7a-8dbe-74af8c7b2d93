package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// OrderAndPayRepository 订单和支付仓储接口
type OrderAndPayRepository interface {
	// FindAllByBillIds 查询所有支付账单
	FindAllByBillIds(ctx context.Context, billIds []string) (*[]po.OrderAndPay, error)
	// ConvertToOrderAndPayVO 转换为订单和支付VO
	ConvertToOrderAndPayVO(ctx context.Context, orderAndPay po.OrderAndPay) vo.OrderAndPayVO
	// ConvertToOrderAndPay 转换为订单和支付PO
	ConvertToOrderAndPay(ctx context.Context, orderAndPayVO vo.OrderAndPayVO) po.OrderAndPay
	// FindAllBySessionId 查询所有支付账单
	FindAllBySessionId(ctx context.Context, sessionId string) ([]po.OrderAndPay, error)
	// FindsByOrderNos 查询订单和支付
	FindsByOrderNos(ctx context.Context, orderNos []string) ([]po.OrderAndPay, error)
}
