package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// PayBillRepository 支付账单仓储接口
type PayBillRepository interface {
	// FindAllPayBill 查询所有支付账单
	FindAllPayBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayBill, error)
	// ConvertToPayBillVO 转换为支付账单VO
	ConvertToPayBillVO(ctx context.Context, payBill po.PayBill) vo.PayBillVO
	// ConvertToPayBill 转换为支付账单PO
	ConvertToPayBill(ctx context.Context, payBillVO vo.PayBillVO) po.PayBill
	// FindAllBySessionId 查询所有支付账单
	FindAllBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error)
	// FindsBySessionId 查询所有支付账单
	FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error)
	// FindsByTimeRange 查询所有支付账单
	FindsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.PayBill, error)
	// FindByBillId 查询支付账单
	FindByBillId(ctx context.Context, billId, venueId, sessionId string) (po.PayBill, error)
	// FindsBySessionIds 查询所有支付账单
	FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.PayBill, error)
	// FindModelBasePayBillVOsByTimeRange 查询支付账单VOs
	FindModelBasePayBillVOsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) (vo.ModelBasePayBillVO, error)
}
