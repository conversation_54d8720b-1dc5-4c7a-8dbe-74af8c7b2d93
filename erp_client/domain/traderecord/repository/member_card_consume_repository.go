package repository

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardConsumeRepository 会员卡消费记录仓储接口
type MemberCardConsumeRepository interface {
	// CreateMemberCardConsume 创建会员卡消费记录
	CreateMemberCardConsume(ctx context.Context, memberCardConsume *po.MemberCardConsume) error

	// ConvertToMemberCardConsumeVO 转换为会员卡消费记录VO
	ConvertToMemberCardConsumeVO(ctx context.Context, memberCardConsume po.MemberCardConsume) vo.MemberCardConsumeVO

	// ConvertToMemberCardConsume 转换为会员卡消费记录PO
	ConvertToMemberCardConsume(ctx context.Context, memberCardConsumeVO vo.MemberCardConsumeVO) po.MemberCardConsume

	// FindMemberCardConsumesRechargeByTimeRange 根据时间范围查询会员卡消费记录
	FindMemberCardConsumesRechargeByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.MemberCardConsume, error)
}
