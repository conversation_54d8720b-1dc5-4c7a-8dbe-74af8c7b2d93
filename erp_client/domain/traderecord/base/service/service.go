package service

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/base/model"
)

// TradeRecordService 交易记录服务接口
type TradeRecordService interface {
	// 创建交易记录
	Create(ctx context.Context, record model.TradeRecord) error
	// 修改交易记录
	Modify(ctx context.Context, record model.TradeRecord) error
	// 关闭交易记录
	Close(ctx context.Context, id string) error
	// 计算交易记录
	Calculate(ctx context.Context, id string) (float64, error)
	// 添加支付信息
	AddPayment(ctx context.Context, id string, payment interface{}) error
	// 添加交易项目
	AddItems(ctx context.Context, id string, items []interface{}) error
	// 获取交易状态
	GetStatus(ctx context.Context, id string) (string, error)
	// 获取交易记录
	Get(ctx context.Context, id string) (model.TradeRecord, error)
	// 查询交易记录
	Query(ctx context.Context, condition map[string]interface{}) ([]model.TradeRecord, error)
}
