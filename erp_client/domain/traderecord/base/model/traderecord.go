package model

import (
	"time"

	"voderpltvv/erp_client/domain/common/model"
)

// TradeRecord 交易记录接口
type TradeRecord interface {
	model.Entity
	GetType() string
	GetStatus() string
	GetAmount() float64
	GetCreateTime() time.Time
	GetUpdateTime() time.Time
}

// BaseTradeRecord 基础交易记录结构
type BaseTradeRecord struct {
	model.BaseModel
	Type       string    `json:"type"`
	Status     string    `json:"status"`
	Amount     float64   `json:"amount"`
	CreateTime time.Time `json:"create_time"`
	UpdateTime time.Time `json:"update_time"`
}

func (t *BaseTradeRecord) GetType() string {
	return t.Type
}

func (t *BaseTradeRecord) GetStatus() string {
	return t.Status
}

func (t *BaseTradeRecord) GetAmount() float64 {
	return t.Amount
}

func (t *BaseTradeRecord) GetCreateTime() time.Time {
	return t.CreateTime
}

func (t *BaseTradeRecord) GetUpdateTime() time.Time {
	return t.UpdateTime
}
