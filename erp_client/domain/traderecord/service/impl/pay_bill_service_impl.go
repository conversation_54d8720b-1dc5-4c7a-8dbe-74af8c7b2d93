package impl

import (
	"context"
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
)

// PayBillServiceImpl 支付账单服务实现
type PayBillServiceImpl struct {
	payBillRepo repository.PayBillRepository
}

// NewPayBillService 创建支付账单服务实例
func NewPayBillService(payBillRepo repository.PayBillRepository) *PayBillServiceImpl {
	return &PayBillServiceImpl{
		payBillRepo: payBillRepo,
	}
}

// FindAllPayBill 查询所有支付账单
func (s *PayBillServiceImpl) FindAllPayBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayBill, error) {
	return s.payBillRepo.FindAllPayBill(ctx, venueId, sessionId, statusList)
}

// ConvertToPayBillVO 转换为支付账单VO
func (s *PayBillServiceImpl) ConvertToPayBillVO(ctx context.Context, payBill po.PayBill) vo.PayBillVO {
	return s.payBillRepo.ConvertToPayBillVO(ctx, payBill)
}

// ConvertToPayBill 转换为支付账单PO
func (s *PayBillServiceImpl) ConvertToPayBill(ctx context.Context, payBillVO vo.PayBillVO) po.PayBill {
	return s.payBillRepo.ConvertToPayBill(ctx, payBillVO)
}

// FindAllBySessionId 查询所有支付账单
func (s *PayBillServiceImpl) FindAllBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error) {
	return s.payBillRepo.FindAllBySessionId(ctx, venueId, sessionId)
}

// CheckHasUnpaidPayBill 检查是否有正在支付的账单
func (s *PayBillServiceImpl) CheckHasUnpaidPayBill(ctx context.Context, venueId, sessionId string) error {
	payBills, err := s.FindAllBySessionId(ctx, venueId, sessionId)
	if err != nil {
		return err
	}
	for _, payBill := range payBills {
		if *payBill.Status != _const.V2_PAY_BILL_STATUS_PAID {
			// return fmt.Errorf("存在正在支付的账单，请重试")
		}
	}
	return nil
}

// TrimIdToAddPayBill 去除ID
func (s *PayBillServiceImpl) TrimIdToAddPayBill(ctx context.Context, payBill po.PayBill) po.PayBill {
	newPayBill := util.DeepClone(payBill)
	newPayBill.Id = nil
	newPayBill.Ctime = nil
	newPayBill.Utime = nil
	newPayBill.State = nil
	newPayBill.Version = nil
	return newPayBill
}

// FindsBySessionId 查询所有支付账单
func (s *PayBillServiceImpl) FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error) {
	return s.payBillRepo.FindsBySessionId(ctx, venueId, sessionId)
}

// FindsByTimeRange 查询所有支付账单
func (s *PayBillServiceImpl) FindsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.PayBill, error) {
	return s.payBillRepo.FindsByTimeRange(ctx, venueId, startTime, endTime)
}

// FindByBillId 查询支付账单
func (s *PayBillServiceImpl) FindByBillId(ctx context.Context, billId, venueId, sessionId string) (po.PayBill, error) {
	return s.payBillRepo.FindByBillId(ctx, billId, venueId, sessionId)
}

// FindsBySessionIds 查询所有支付账单
func (s *PayBillServiceImpl) FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.PayBill, error) {
	return s.payBillRepo.FindsBySessionIds(ctx, venueId, sessionIds)
}

// CalcBusinessOverviewForVenue 计算营业概览
func (s *PayBillServiceImpl) CalcBusinessOverviewForVenue(ctx context.Context, venueId string, orders []po.Order, payBills []po.PayBill, orderAndPays []po.OrderAndPay, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan) vo.VenueBusinessReportVOBusinessOverview {
	// 以paybill为基准
	orderNoTotalFee := make(map[string]int64)
	// 原价 * 数量
	for _, orderRoomPlan := range orderRoomPlans {
		orderNo := orderRoomPlan.OrderNo
		orderNoTotalFee[*orderNo] += *orderRoomPlan.PayAmount
	}
	for _, orderProduct := range orderProducts {
		orderNo := orderProduct.OrderNo
		orderNoTotalFee[*orderNo] += *orderProduct.PayAmount
	}
	roomFee := int64(0)
	productFee := int64(0)
	for _, order := range orders {
		multipart := int64(1)
		if *order.Direction == _const.V2_ORDER_DIRECTION_REFUND {
			multipart = -1
		}
		thisFee := orderNoTotalFee[*order.OrderNo]
		if *order.Type == _const.V2_ORDER_TYPE_PRODUCT {
			productFee += thisFee * multipart
		} else {
			roomFee += thisFee * multipart
		}
	}

	return vo.VenueBusinessReportVOBusinessOverview{
		TotalFee:        roomFee + productFee,
		TotalFeeProduct: productFee,
		TotalFeeRoom:    roomFee,
	}
}

// FindModelBasePayBillVOsByTimeRange 查询支付账单VOs
func (s *PayBillServiceImpl) FindModelBasePayBillVOsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) (vo.ModelBasePayBillVO, error) {
	return s.payBillRepo.FindModelBasePayBillVOsByTimeRange(ctx, venueId, startTime, endTime)
}

// ConvertSrcVODataToPayBillVOs 转换为支付账单VOs
func (s *PayBillServiceImpl) ConvertSrcVODataToPayBillVOs(ctx context.Context, modelBasePayBillVO vo.ModelBasePayBillVO) []vo.PayBillVO {
	payBillVOs := make([]vo.PayBillVO, 0)
	orderNoToOrderProductVOsMap := make(map[string][]vo.OrderProductVO)
	for _, opTmp := range modelBasePayBillVO.OrderProductVOs {
		orderNoToOrderProductVOsMap[opTmp.OrderNo] = append(orderNoToOrderProductVOsMap[opTmp.OrderNo], opTmp)
	}

	// 填充订单VOs的商品和房费计划
	newOrders := []vo.OrderVO{}
	for _, orderTmp := range modelBasePayBillVO.OrderVOs {
		orderTmp.OrderProductVOs = orderNoToOrderProductVOsMap[orderTmp.OrderNo]
		newOrders = append(newOrders, orderTmp)
	}
	orderNoToOrderVOMap := make(map[string]vo.OrderVO)
	for _, orderVO := range newOrders {
		orderNoToOrderVOMap[orderVO.OrderNo] = orderVO
	}

	billIdToOrderAndPaysVOMap := make(map[string][]vo.OrderAndPayVO)
	for _, orderAndPayVO := range modelBasePayBillVO.OrderAndPayVOs {
		billIdToOrderAndPaysVOMap[orderAndPayVO.BillId] = append(billIdToOrderAndPaysVOMap[orderAndPayVO.BillId], orderAndPayVO)
	}

	for _, payBillVOTmp := range modelBasePayBillVO.PayBillVOs {
		// paybillvo.orderandpayvos
		payBillVOTmp.OrderAndPayVOs = billIdToOrderAndPaysVOMap[payBillVOTmp.BillId]
		for _, orderAndPayVO := range payBillVOTmp.OrderAndPayVOs {
			// paybillvo.orders
			orderVO, ok := orderNoToOrderVOMap[orderAndPayVO.OrderNo]
			if ok {
				payBillVOTmp.OrderVOs = append(payBillVOTmp.OrderVOs, orderVO)
			}
		}
		payBillVOs = append(payBillVOs, payBillVOTmp)
	}
	return payBillVOs
}

// MergeOrderProductSalesVOs 合并订单产品销售VOs
func (s *PayBillServiceImpl) MergeOrderProductSalesVOs(ctx context.Context, payBillVOs []vo.PayBillVO) []vo.OrderProductSalesVO {
	// 取出所有订单VO
	orderVOs := make([]vo.OrderVO, 0)
	for _, payBillVO := range payBillVOs {
		orderVOs = append(orderVOs, payBillVO.OrderVOs...)
	}
	// 取出所有orderproductvo, 并设置ordervo
	orderProductVOs := make([]vo.OrderProductVO, 0)
	for _, orderVOTmp := range orderVOs {
		orderVOTmp2 := util.DeepClone(orderVOTmp)
		orderVOTmp2.OrderProductVOs = nil
		for _, orderProductVOTmp := range orderVOTmp.OrderProductVOs {
			orderProductVOTmp.OrderVO = orderVOTmp2
			orderProductVOs = append(orderProductVOs, orderProductVOTmp)
		}
	}
	// 合并orderproductvo
	mergeOrderProductVOs := make([]vo.OrderProductVO, 0)
	nomalOrderProductVOs := make([]vo.OrderProductVO, 0)
	pidToOrderProductVOs := make(map[string][]vo.OrderProductVO)
	for _, opVOTmp := range orderProductVOs {
		orderVOTmp := opVOTmp.OrderVO
		if orderVOTmp.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			nomalOrderProductVOs = append(nomalOrderProductVOs, opVOTmp)
		} else {
			if opVOTmp.PId != "" {
				pidToOrderProductVOs[opVOTmp.PId] = append(pidToOrderProductVOs[opVOTmp.PId], opVOTmp)
			} else {
				logrus.Errorf("orderproductvo.pid is empty, orderproductvo: %+v", opVOTmp)
			}
		}
	}
	for _, opVOTmp := range nomalOrderProductVOs {
		refundOPs, ok := pidToOrderProductVOs[opVOTmp.Id]
		if ok {
			// 存在退款订单，合并
			for _, refundOPVOTmp := range refundOPs {
				opVOTmp.Quantity -= refundOPVOTmp.Quantity
				opVOTmp.PayAmount -= refundOPVOTmp.PayAmount
			}
		}
		if opVOTmp.Quantity > 0 {
			mergeOrderProductVOs = append(mergeOrderProductVOs, opVOTmp)
		}
	}

	orderProductSalesVOs := make([]vo.OrderProductSalesVO, 0)
	for _, opVOTmp := range mergeOrderProductVOs {
		orderProductSalesVOs = append(orderProductSalesVOs, vo.OrderProductSalesVO{
			VenueId:      opVOTmp.VenueId,
			EmployeeId:   opVOTmp.EmployeeId,
			ProductId:    opVOTmp.ProductId,
			ProductName:  opVOTmp.ProductName,
			CategoryId:   opVOTmp.CategoryId,
			CategoryName: opVOTmp.CategoryName,
			ShouldFee:    opVOTmp.OriginalPrice * opVOTmp.Quantity,
			Quantity:     opVOTmp.Quantity,
			TotalFee:     opVOTmp.PayAmount,
			IsGift:       opVOTmp.IsGift,
		})
	}
	groupOrderProductSalesVOs := make(map[string][]vo.OrderProductSalesVO)
	for _, opVOTmp := range orderProductSalesVOs {
		key := fmt.Sprintf("%s_%s", opVOTmp.ProductId, opVOTmp.CategoryId)
		groupOrderProductSalesVOs[key] = append(groupOrderProductSalesVOs[key], opVOTmp)
	}
	retVOs := make([]vo.OrderProductSalesVO, 0)
	for _, opVOTmps := range groupOrderProductSalesVOs {
		firstOPVOTmp := opVOTmps[0]
		retVO := vo.OrderProductSalesVO{
			VenueId:      firstOPVOTmp.VenueId,
			EmployeeId:   firstOPVOTmp.EmployeeId,
			ProductId:    firstOPVOTmp.ProductId,
			ProductName:  firstOPVOTmp.ProductName,
			CategoryId:   firstOPVOTmp.CategoryId,
			CategoryName: firstOPVOTmp.CategoryName,
			IsGift:       firstOPVOTmp.IsGift,
		}
		for _, opVOTmp := range opVOTmps {
			retVO.ShouldFee += opVOTmp.ShouldFee
			retVO.Quantity += opVOTmp.Quantity
			retVO.TotalFee += opVOTmp.TotalFee
		}
		retVOs = append(retVOs, retVO)

	}
	return retVOs
}
