package impl

import (
	"context"
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/util"
)

// CalculateTotalPaymentForPayDrop 计算最终支付总金额
// 场景：后付支付，订单中包括未支付的订单（包括正常下单的订单和退款订单），需要做合并处理 + 回放数据
// 将未支付的退款订单合并，然后再回放
func (s *PayServiceImpl) CalculateTotalPaymentForPayDrop(ctx context.Context, session vo.SessionVO, orderVOs []vo.OrderVO, payBill vo.PayBillVO) (int64, []vo.OrderProductVO, []vo.OrderRoomPlanVO) {
	// 将包含退款的未付的订单合并，记录买n退m
	newOrderVos := make([]vo.OrderVO, 0)
	nomalOpOrderVOs, refundOpOrderVOs := s.MergeOrderOP(ctx, orderVOs)
	omOrderVos := s.MergeOrderOM(ctx, orderVOs)
	newOrderVos = append(newOrderVos, nomalOpOrderVOs...)
	newOrderVos = append(newOrderVos, omOrderVos...)
	totalAmount, op, om, _, _ := s.CalculateTotalPayment(ctx, session, newOrderVos, payBill)
	// 回写 tmpBuyCount 和 tmpRefundCount 将op的买n退m回写
	newOp := s.PlaybackOrderOP(ctx, op, nomalOpOrderVOs, refundOpOrderVOs)
	newOm := s.PlaybackOrderOM(ctx, om)
	return totalAmount, newOp, newOm
}

func (s *PayServiceImpl) MergeOrderOP(ctx context.Context, orderVOs []vo.OrderVO) (nomalOpOrderVOs []vo.OrderVO, refundOpOrderVOs []vo.OrderVO) {
	nomalOpOrderVOs = make([]vo.OrderVO, 0)
	refundOpOrderVOs = make([]vo.OrderVO, 0)
	filterOrderVos := make([]vo.OrderVO, 0)
	refundOrderProducts := make([]vo.OrderProductVO, 0)
	// 1. 筛选出商品订单和退款订单
	for _, v := range orderVOs {
		if v.Status == _const.V2_ORDER_STATUS_PAID {
			panic("不应存在已支付的订单")
		}
		if v.Type != _const.V2_ORDER_TYPE_PRODUCT {
			continue
		}
		if v.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			filterOrderVos = append(filterOrderVos, v)
		} else {
			refundOpOrderVOs = append(refundOpOrderVOs, v)
			refundOrderProducts = append(refundOrderProducts, v.OrderProductVOs...)
		}
	}
	// 2. 计算合并的订单 均为未付的订单
	for _, v := range filterOrderVos {
		newOrderProducts := make([]vo.OrderProductVO, 0)
		for _, opv := range v.OrderProductVOs {
			refundCounts := s.FindOPRefundCount(refundOrderProducts, opv.Id)
			if refundCounts > 0 { // 设置退款临时标记
				opv.TmpCalcFlag = true
			}
			opv.Quantity -= refundCounts
			newOrderProducts = append(newOrderProducts, opv)
		}
		v.OrderProductVOs = newOrderProducts
		nomalOpOrderVOs = append(nomalOpOrderVOs, v)
	}
	return
}

func (s *PayServiceImpl) FindOPRefundCount(ops []vo.OrderProductVO, pid string) int64 {
	count := int64(0)
	for _, v := range ops {
		// 过滤非本商品的退
		if pid != v.PId {
			continue
		}
		count += v.Quantity
	}
	return count
}

func (s *PayServiceImpl) MergeOrderOM(ctx context.Context, orderVOs []vo.OrderVO) []vo.OrderVO {
	// 暂时不考虑房费退款的情况
	filterOrderVos := make([]vo.OrderVO, 0)
	refundOrderProducts := make([]vo.OrderRoomPlanVO, 0)
	// 1. 筛选出商品订单和退款订单
	for _, v := range orderVOs {
		if v.Status == _const.V2_ORDER_STATUS_PAID {
			panic("不应存在已支付的订单")
		}
		if v.Type != _const.V2_ORDER_TYPE_ROOMPLAN {
			continue
		}
		if v.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			filterOrderVos = append(filterOrderVos, v)
		} else {
			refundOrderProducts = append(refundOrderProducts, v.OrderRoomPlanVOs...)
		}
	}
	return filterOrderVos
}

func (s *PayServiceImpl) PlaybackOrderOP(ctx context.Context, opsCalc []vo.OrderProductVO, nomalOpOrderVOs []vo.OrderVO, refundOpOrderVOs []vo.OrderVO) []vo.OrderProductVO {
	rtOps := make([]vo.OrderProductVO, 0)
	// 找到正常订单存在退款的商品
	for _, v := range nomalOpOrderVOs {
		orderProducts := v.OrderProductVOs
		for _, opv := range orderProducts {
			var findedV vo.OrderProductVO
			for _, cv := range opsCalc {
				if cv.Id == opv.Id {
					findedV = cv
				}
			}
			if opv.TmpCalcFlag {
				calcOpvs := s.FindPlaybackOps(ctx, findedV, opv, refundOpOrderVOs)
				rtOps = append(rtOps, calcOpvs...)
			} else {
				rtOps = append(rtOps, findedV)
			}
		}
	}
	return rtOps
}

func (s *PayServiceImpl) FindPlaybackOps(ctx context.Context, opCalc vo.OrderProductVO, opInnomalOpOrderVOs vo.OrderProductVO, refundOpOrderVOs []vo.OrderVO) (rtOps []vo.OrderProductVO) {
	// opCalc 计算后的商品信息 payAmount Quantity
	// opInnomalOpOrderVOs 记录了 原始订单的总数量
	// refundOpOrderVOs 记录了 原始订单对应的退款订单信息
	rtOps = make([]vo.OrderProductVO, 0)
	opInnomalOpOrderVOs.PayAmount = opCalc.PayAmount / opCalc.Quantity * opInnomalOpOrderVOs.Quantity
	rtOps = append(rtOps, opInnomalOpOrderVOs)
	for _, v := range refundOpOrderVOs {
		for _, pv := range v.OrderProductVOs {
			if pv.PId == opCalc.Id {
				pv.PayAmount = opCalc.PayAmount / opCalc.Quantity * pv.Quantity
				rtOps = append(rtOps, pv)
			}
		}
	}
	return
}

func (s *PayServiceImpl) PlaybackOrderOM(ctx context.Context, oms []vo.OrderRoomPlanVO) []vo.OrderRoomPlanVO {
	return oms
}

// CalculateTotalPaymentForPay 计算最终支付总金额
// 场景：后付支付，订单中包括未支付的订单（包括正常下单的订单和退款订单），需要做合并处理 + 回放数据
// 将未支付的退款订单合并，然后再回放
func (s *PayServiceImpl) CalculateTotalPaymentForPay(ctx context.Context, session vo.SessionVO, orderVOs []vo.OrderVO, payBill vo.PayBillVO) (int64, []vo.OrderProductVO, []vo.OrderRoomPlanVO, int64, int64) {
	orderGroupUnpaidVOs := s.BuildOrderGroupUnpaidVO(ctx, orderVOs)
	newOrderVos := s.BuildNewOrderVOs(ctx, orderGroupUnpaidVOs)
	totalAmount, ops, oms, retProductDiscountFee, retRoomDiscountFee := s.CalculateTotalPayment(ctx, session, newOrderVos, payBill)
	newOps, newOms := s.BuildNewOpsOms(ctx, orderGroupUnpaidVOs, ops, oms)
	return totalAmount, newOps, newOms, retProductDiscountFee, retRoomDiscountFee
}

// BuildOrderGroupUnpaidVO 构建订单组
// 用入参orderVOs 以支付订单为根据 orderVOs立的orderNO是唯一的，退款订单通过POrderNO指向原订单的OrderNO
// 将 OrderGroupUnpaidVO 的 OrderVO，OrderProductVOsNormal，OrderRoomPlanVOsNomal，OrderVOsRefund，OrderProductVOsRefund，OrderRoomPlanVOsRefund填充，
// 并继续将 OrderProductVOsMerged，OrderRoomPlanVOsMerged计算出来
func (s *PayServiceImpl) BuildOrderGroupUnpaidVO(ctx context.Context, orderVOs []vo.OrderVO) []vo.OrderGroupUnpaidVO {
	orderGroupUnpaidVOs := make([]vo.OrderGroupUnpaidVO, 0)
	// 处理支付订单
	for _, orderVOTmp := range orderVOs {
		if orderVOTmp.Status == _const.V2_ORDER_STATUS_PAID {
			panic("不应存在已支付的订单")
		}
		// 跳过退款订单
		if orderVOTmp.Direction != _const.V2_ORDER_DIRECTION_NORMAL {
			continue
		}
		// 跳过取消订单 转台时产生
		if orderVOTmp.MarkType == _const.V2_ORDER_MARK_TYPE_CANCEL {
			continue
		}
		orderGroupUnpaidVO := vo.OrderGroupUnpaidVO{
			OrderVO:               orderVOTmp,
			OrderProductVOsNormal: orderVOTmp.OrderProductVOs,
			OrderRoomPlanVOsNomal: orderVOTmp.OrderRoomPlanVOs,
		}
		// 再次遍历，寻找退款订单
		for _, orderVOTmpInner := range orderVOs {
			if orderVOTmpInner.Status == _const.V2_ORDER_STATUS_PAID {
				panic("不应存在已支付的订单")
			}
			if orderVOTmpInner.Direction != _const.V2_ORDER_DIRECTION_REFUND {
				// 跳过正常订单
				continue
			}
			// 匹配退款订单，添加到订单组中
			if orderVOTmpInner.POrderNo == orderVOTmp.OrderNo {
				orderGroupUnpaidVO.OrderVOsRefund = append(orderGroupUnpaidVO.OrderVOsRefund, orderVOTmpInner)
				orderGroupUnpaidVO.OrderProductVOsRefund = append(orderGroupUnpaidVO.OrderProductVOsRefund, orderVOTmpInner.OrderProductVOs...)
				orderGroupUnpaidVO.OrderRoomPlanVOsRefund = append(orderGroupUnpaidVO.OrderRoomPlanVOsRefund, orderVOTmpInner.OrderRoomPlanVOs...)
			}
		}
		orderGroupUnpaidVOs = append(orderGroupUnpaidVOs, orderGroupUnpaidVO)
	}
	// 合并商品和房费
	for idx, groupTmp := range orderGroupUnpaidVOs {
		// 合并商品
		opsNomal := groupTmp.OrderProductVOsNormal
		opsRefund := groupTmp.OrderProductVOsRefund
		opsMerged := make([]vo.OrderProductVO, 0)
		for _, opNomalTmp := range opsNomal {
			opMerged := util.DeepClone(opNomalTmp)
			// 查找退款商品 遍历所有退款商品
			for _, opRefundTmp := range opsRefund {
				if opMerged.Id == opRefundTmp.PId {
					opMerged.Quantity -= opRefundTmp.Quantity
				}
			}
			// 未全退的参与计算
			if opMerged.Quantity > 0 {
				opsMerged = append(opsMerged, opMerged)
			}
		}
		// 合并房费
		omsNomal := groupTmp.OrderRoomPlanVOsNomal
		omsRefund := groupTmp.OrderRoomPlanVOsRefund
		omsMerged := make([]vo.OrderRoomPlanVO, 0)
		for _, omNomalTmp := range omsNomal {
			omMerged := util.DeepClone(omNomalTmp)
			// 查找退款房费 遍历所有退款房费
			hasRefund := false
			for _, omRefundTmp := range omsRefund {
				if omMerged.Id == omRefundTmp.PId {
					hasRefund = true
				}
			}
			// 未全退的参与计算
			if !hasRefund {
				omsMerged = append(omsMerged, omMerged)
			}
		}
		orderGroupUnpaidVOs[idx].OrderProductVOsMerged = opsMerged
		orderGroupUnpaidVOs[idx].OrderRoomPlanVOsMerged = omsMerged
	}
	return orderGroupUnpaidVOs
}

// BuildNewOrderVOs 构建新的订单组
// 将 OrderGroupUnpaidVO 的 OrderVO，OrderProductVOsMerged，OrderRoomPlanVOsMerged 合并成新的OrderVOs返回
func (s *PayServiceImpl) BuildNewOrderVOs(ctx context.Context, orderGroupUnpaidVOs []vo.OrderGroupUnpaidVO) (newOrderVos []vo.OrderVO) {
	newOrderVos = make([]vo.OrderVO, 0)
	for _, groupTmp := range orderGroupUnpaidVOs {
		orderVO := groupTmp.OrderVO
		orderVO.OrderProductVOs = groupTmp.OrderProductVOsMerged
		orderVO.OrderRoomPlanVOs = groupTmp.OrderRoomPlanVOsMerged
		newOrderVos = append(newOrderVos, orderVO)
	}
	return
}

// BuildNewOpsOms
// 计算新的商品和房费，通过 ops和oms 等比回放
// 并将新的商品和房费返回，回复买n退m的关系和新的价格
func (s *PayServiceImpl) BuildNewOpsOms(ctx context.Context, orderGroupUnpaidVOs []vo.OrderGroupUnpaidVO, ops []vo.OrderProductVO, oms []vo.OrderRoomPlanVO) (newOps []vo.OrderProductVO, newOms []vo.OrderRoomPlanVO) {
	// 回放商品
	newOps = make([]vo.OrderProductVO, 0)
	// 遍历所有ops
	for _, opTmp := range ops {
		// 查找原订单
		for _, groupTmp := range orderGroupUnpaidVOs {
			// 找到原订单
			if opTmp.OrderNo == groupTmp.OrderVO.OrderNo {
				// 查找原商品信息-支付商品
				for _, opVOsNormalTmp := range groupTmp.OrderProductVOsNormal {
					if opVOsNormalTmp.Id == opTmp.Id {
						// 计算新的商品单价 - 修复精度问题：先乘后除
						newOp := util.DeepClone(opVOsNormalTmp)
						if opTmp.Quantity > 0 {
							newOp.PayAmount = int64(float64(opTmp.PayAmount) * float64(opVOsNormalTmp.Quantity) / float64(opTmp.Quantity))
						} else {
							newOp.PayAmount = 0
						}
						newOps = append(newOps, newOp)
					}
				}
				// 查找原商品信息-退款商品
				for _, opVOsRefundTmp := range groupTmp.OrderProductVOsRefund {
					if opVOsRefundTmp.PId == opTmp.Id {
						// 计算新的商品单价 - 修复精度问题：先乘后除
						newOp := util.DeepClone(opVOsRefundTmp)
						if opTmp.Quantity > 0 {
							newOp.PayAmount = int64(float64(opTmp.PayAmount) * float64(opVOsRefundTmp.Quantity) / float64(opTmp.Quantity))
						} else {
							newOp.PayAmount = 0
						}
						newOps = append(newOps, newOp)
					}
				}
			}
		}
	}
	// 回放房费
	newOms = make([]vo.OrderRoomPlanVO, 0)
	// 遍历所有oms
	for _, omTmp := range oms {
		// 查找原订单
		for _, groupTmp := range orderGroupUnpaidVOs {
			// 找到原订单
			if omTmp.OrderNo == groupTmp.OrderVO.OrderNo {
				// 查找原商品信息-支付商品
				for _, omVOsNormalTmp := range groupTmp.OrderRoomPlanVOsNomal {
					if omVOsNormalTmp.Id == omTmp.Id {
						// 计算新的商品单价
						newOm := util.DeepClone(omVOsNormalTmp)
						newOm.PayAmount = omTmp.PayAmount
						newOms = append(newOms, newOm)
					}
				}
				// 查找原商品信息-退款商品
				for _, omVOsRefundTmp := range groupTmp.OrderRoomPlanVOsRefund {
					if omVOsRefundTmp.PId == omTmp.Id {
						// 计算新的商品单价
						newOm := util.DeepClone(omVOsRefundTmp)
						newOm.PayAmount = omTmp.PayAmount
						newOms = append(newOms, newOm)
					}
				}
			}
		}
	}
	return
}

// 计算商品单价，并标记打折状态
func (s *PayServiceImpl) calcProductUnitPrice(product *vo.OrderProductVO, memberDiscountType int64) int64 {
	basePrice := product.OriginalPrice

	// 1. 特殊情况处理
	if product.IsFree || product.IsGift || product.IsFreeDrinking || product.IsMultiProductGift {
		return 0
	}

	// 2. 会员价处理
	if product.MemberDiscountable && memberDiscountType == _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE {
		if product.MemberPrice > 0 {
			basePrice = product.MemberPrice
		}
	}

	// 3. 会员折扣处理
	if product.MemberDiscountable && memberDiscountType == _const.PRODUCT_MEMBER_DISCOUNT_TYPE_DISCOUNT {
		if product.MemberDiscount > 0 {
			basePrice = basePrice * product.MemberDiscount / 100
		}
	}

	// 4. 商品折扣处理 - 标记打折状态
	if product.ProductDiscountable && product.OrderProductDiscount > 0 {
		basePrice = basePrice * product.OrderProductDiscount / 100
		product.IsDiscounted = true // 标记为已打折
	}

	return basePrice
}

// 计算房费单价
func (s *PayServiceImpl) calcRoomUnitPrice(plan *vo.OrderRoomPlanVO, memberDiscountType int64) int64 {
	basePrice := plan.OriginalPayAmount

	// 0. 特殊情况处理
	if plan.IsGift || plan.IsFree {
		return 0
	}

	// 1. 会员价处理
	if plan.MemberDiscountable && memberDiscountType == _const.ROOM_MEMBER_DISCOUNT_TYPE_PRICE {
		if plan.MemberPrice > 0 {
			basePrice = plan.MemberPrice
		}
	}

	// 2. 会员折扣处理
	if plan.MemberDiscountable && memberDiscountType == _const.ROOM_MEMBER_DISCOUNT_TYPE_DISCOUNT {
		if plan.MemberDiscount > 0 {
			basePrice = basePrice * plan.MemberDiscount / 100
			plan.PayRoomDiscount = plan.MemberDiscount // 记录会员折扣到PayRoomDiscount字段
		}
	}

	return basePrice
}

// 计算房费支付金额
func (s *PayServiceImpl) calcRoomPayment(plans []vo.OrderRoomPlanVO, order vo.OrderVO) int64 {
	if plans == nil {
		return 0
	}
	var total int64 = 0
	// 使用索引遍历以便修改原始数据
	for i := range plans {
		roomFee := s.calcRoomUnitPrice(&plans[i], order.ConfigRoomMemberDiscountType)
		plans[i].PayAmount = roomFee // 设置计算后的支付金额
		total += roomFee
	}
	return total
}

// 计算场次已支付订单金额
func (s *PayServiceImpl) calcSessionPaidAmount(ctx context.Context, venueId, sessionId string) int64 {
	if venueId == "" || sessionId == "" {
		return 0
	}
	// 查询场次所有已支付订单
	paidAmount, err := s.QueryPaidOrderVOsBySessionId(ctx, venueId, sessionId)
	if err != nil {
		return 0
	}
	return paidAmount
}

// CalculateTotalPayment 计算最终支付总金额
func (s *PayServiceImpl) CalculateTotalPayment(ctx context.Context, session vo.SessionVO, orders []vo.OrderVO, payBill vo.PayBillVO) (int64, []vo.OrderProductVO, []vo.OrderRoomPlanVO, int64, int64) {
	// 计算实际最低消费金额 = max(0, 原始最低消费 - 已支付订单金额)
	paidAmount := s.calcSessionPaidAmount(ctx, session.VenueId, session.SessionId)
	minConsume := max(0, session.MinConsume-paidAmount)

	// 0. 计算商品单价并标记打折状态
	var allProducts []vo.OrderProductVO
	for _, order := range orders {
		products := order.OrderProductVOs
		for i := range products {
			products[i].UnitPrice = s.calcProductUnitPrice(&products[i], order.ConfigProductMemberDiscountType)
		}
		allProducts = append(allProducts, products...)
	}

	// 1. 提取未打折的商品列表
	var undiscountedProducts []vo.OrderProductVO
	var discountedProducts []vo.OrderProductVO
	var discountedAmount int64 = 0
	for _, p := range allProducts {
		if !p.IsDiscounted {
			undiscountedProducts = append(undiscountedProducts, p)
		} else {
			discountedProducts = append(discountedProducts, p)
			discountedAmount += p.UnitPrice * p.Quantity
		}
	}

	// 2. 针对未打折商品计算折后价格
	// 将allProducts中的商品，按照是否可打折，分成两个列表
	var undiscountedAmount int64 = 0
	var nonDiscountableAmount int64 = 0
	for _, p := range undiscountedProducts {
		productAmount := p.UnitPrice * p.Quantity
		if p.ProductDiscountable {
			undiscountedAmount += productAmount
		} else {
			nonDiscountableAmount += productAmount
		}
	}
	retProductDiscountFee := payBill.ProductDiscountFee
	retRoomDiscountFee := payBill.RoomDiscountFee
	if payBill.ProductDiscount > 0 {
		if payBill.DiscountType == _const.DISCOUNT_TYPE_ALL {
			// 只对可打折商品进行折扣计算
			undiscountedAmount = undiscountedAmount * payBill.ProductDiscount / 100
			retProductDiscountFee = undiscountedAmount

			// 更新 PayProductDiscount 到原始商品列表
			for index, p := range undiscountedProducts {
				if p.ProductDiscountable && !(p.IsFree || p.IsGift || p.IsFreeDrinking || p.IsMultiProductGift) {
					undiscountedProducts[index].PayProductDiscount = payBill.ProductDiscount
					// 找到对应的原始商品并更新
					// for i := range allProducts {
					// 	// 如果商品id相同，或者套餐id相同，并且来源相同，则更新折扣
					// 	if allProducts[i].Id == p.Id && allProducts[i].PackageId == p.PackageId && allProducts[i].Src == p.Src {
					// 		allProducts[i].PayProductDiscount = payBill.ProductDiscount
					// 		// fmt.Println("更新前商品信息: id:", allProducts[i].Id, "原价:", allProducts[i].OriginalPrice, "单价:", allProducts[i].UnitPrice, "折扣:", allProducts[i].PayProductDiscount, "IsDiscounted:", allProducts[i].IsDiscounted)
					// 		// fmt.Println("更新后商品信息: id:", p.Id, "原价:", p.OriginalPrice, "单价:", p.UnitPrice, "折扣:", p.PayProductDiscount, "IsDiscounted:", p.IsDiscounted)
					// 		break
					// 	}
					// }
				}
			}

			// 不可打折商品保持原价
			undiscountedAmount += nonDiscountableAmount
		} else {
			// 仅超过低消部分打折
			totalProductAmount := undiscountedAmount + nonDiscountableAmount + discountedAmount
			if totalProductAmount > minConsume {
				overMinAmount := totalProductAmount - minConsume
				// 只对可打折金额部分计算折扣
				discountableOverMin := min(overMinAmount, undiscountedAmount)
				discountedOverMin := discountableOverMin * payBill.ProductDiscount / 100
				undiscountedAmount = undiscountedAmount - discountableOverMin + discountedOverMin + nonDiscountableAmount
				payBill.OverMinProductDiscountAmount = discountableOverMin - discountedOverMin
			} else {
				undiscountedAmount += nonDiscountableAmount
			}
		}
	} else {
		undiscountedAmount += nonDiscountableAmount
	}
	// 重新合并
	allProducts = []vo.OrderProductVO{}
	allProducts = append(allProducts, undiscountedProducts...)
	allProducts = append(allProducts, discountedProducts...)

	// 3. 未打折商品减免
	if payBill.ProductDiscountAmount > 0 {
		undiscountedAmount = max(0, undiscountedAmount-payBill.ProductDiscountAmount)
	}

	// 4. 房费折扣计算
	var roomAmount int64 = 0
	for _, order := range orders {
		roomAmount += s.calcRoomPayment(order.OrderRoomPlanVOs, order)
	}
	if payBill.RoomDiscount > 0 {
		roomAmount = roomAmount * payBill.RoomDiscount / 100
		retRoomDiscountFee = roomAmount
	}

	// 5. 房费减免
	if payBill.RoomDiscountAmount > 0 {
		roomAmount = max(0, roomAmount-payBill.RoomDiscountAmount)
	}

	// 6. 计算总金额并判断是否强制满足最低消费
	productAmount := undiscountedAmount + discountedAmount // 计算商品总金额
	fmt.Println("undiscountedAmount:", undiscountedAmount, "discountedAmount:", discountedAmount, "roomAmount:", roomAmount)

	// 判断商品金额是否满足最低消费
	if payBill.ForceMinimumCharge && productAmount <= minConsume {
		productAmount = minConsume // 商品金额强制调整为最低消费金额
	}

	// 最终总金额 = 商品金额 + 房费
	totalAmount := productAmount + roomAmount
	// for _, p := range allProducts {
	// 	fmt.Println("入参: id:", p.Id, "原价:", p.OriginalPrice, "单价:", p.UnitPrice, "折扣:", p.PayProductDiscount, "IsDiscounted:", p.IsDiscounted)
	// }
	// 7. 回写 op om 的payamount
	op := s.WriteBackOrderProductPayAmount(allProducts, payBill)
	roomPlans := []vo.OrderRoomPlanVO{}
	for _, order := range orders {
		roomPlans = append(roomPlans, order.OrderRoomPlanVOs...)
	}
	om := s.WriteBackOrderRoomPlanPayAmount(roomPlans, payBill)
	return totalAmount, op, om, retProductDiscountFee, retRoomDiscountFee
}

func max(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}

func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

// 回写 op 的payamount
// 1. 去掉金额为0的 和 if product.IsFree || product.IsGift || product.IsFreeDrinking || product.IsMultiProductGift
// 2. op 的payamount = UnitPrice * Quantity * PayProductDiscount
// 3. 订单的总优惠=payBill.ProductDiscountAmount + payBill.OverMinProductDiscountAmount
// 4. op.payamout-=均摊的优惠
func (s *PayServiceImpl) WriteBackOrderProductPayAmount(products []vo.OrderProductVO, payBill vo.PayBillVO) []vo.OrderProductVO {
	// 1. 筛选出需要计算的商品（排除金额为0和特殊商品）
	var validProducts []vo.OrderProductVO
	var resultProducts []vo.OrderProductVO = make([]vo.OrderProductVO, len(products))
	copy(resultProducts, products)

	for i, p := range resultProducts {
		if p.UnitPrice > 0 && !p.IsFree && !p.IsGift && !p.IsFreeDrinking && !p.IsMultiProductGift {
			validProducts = append(validProducts, p)
		} else {
			resultProducts[i].PayAmount = 0 // 特殊商品金额设为0
		}
	}

	// 2. 计算有效商品的总金额
	var totalAmount int64 = 0
	for _, p := range validProducts {
		amount := p.UnitPrice * p.Quantity
		if p.PayProductDiscount > 0 {
			amount = amount * p.PayProductDiscount / 100
		}
		totalAmount += amount
	}

	// 3. 计算总优惠金额
	totalDiscount := payBill.ProductDiscountAmount + payBill.OverMinProductDiscountAmount
	if totalDiscount <= 0 {
		// 如果没有优惠，直接设置 PayAmount
		for i, p := range resultProducts {
			if p.UnitPrice > 0 && !p.IsFree && !p.IsGift && !p.IsFreeDrinking && !p.IsMultiProductGift {
				amount := p.UnitPrice * p.Quantity
				if p.PayProductDiscount > 0 {
					amount = amount * p.PayProductDiscount / 100
				}
				resultProducts[i].PayAmount = amount
			}
		}
		return resultProducts
	}

	// 4. 按比例分摊优惠金额
	var totalPayAmount int64 = 0 // 用于累计实际分摊的金额
	lastValidIndex := -1         // 记录最后一个有效商品的索引

	for i, p := range resultProducts {
		if p.UnitPrice > 0 && !p.IsFree && !p.IsGift && !p.IsFreeDrinking && !p.IsMultiProductGift {
			amount := p.UnitPrice * p.Quantity
			if p.PayProductDiscount > 0 {
				amount = amount * p.PayProductDiscount / 100
			}

			// 计算该商品应分摊的优惠金额
			discount := totalDiscount * amount / totalAmount
			// 设置最终支付金额
			resultProducts[i].PayAmount = max(0, amount-discount)
			totalPayAmount += resultProducts[i].PayAmount
			lastValidIndex = i
		}
	}

	// 处理精度损失：将差额计入最后一个有效商品
	if lastValidIndex >= 0 {
		expectedAmount := totalAmount - totalDiscount
		if expectedAmount > 0 {
			diff := expectedAmount - totalPayAmount
			resultProducts[lastValidIndex].PayAmount += diff
		}
	}

	return resultProducts
}

// 回写 om 的payamount
// 1. 去掉金额为0的 和 if roomPlan.IsGift
// 2. om 的payamount = UnitPrice * PayRoomDiscount
// 3. 订单的总优惠 = 折扣优惠 + payBill.RoomDiscountAmount
// 4. om.payamout-=均摊的优惠
func (s *PayServiceImpl) WriteBackOrderRoomPlanPayAmount(plans []vo.OrderRoomPlanVO, payBill vo.PayBillVO) []vo.OrderRoomPlanVO {
	// 1. 创建返回结果并复制原始数据
	resultPlans := make([]vo.OrderRoomPlanVO, len(plans))
	copy(resultPlans, plans)

	// 2. 计算有效房费的总金额（先应用折扣）
	var totalAmount int64 = 0
	for i, p := range resultPlans {
		if p.PayAmount > 0 && !p.IsGift {
			// 应用房费折扣
			if payBill.RoomDiscount > 0 {
				resultPlans[i].PayAmount = p.PayAmount * payBill.RoomDiscount / 100
			}
			totalAmount += resultPlans[i].PayAmount
		}
	}

	// 3. 如果没有减免优惠，直接返回折扣后的金额
	if payBill.RoomDiscountAmount <= 0 {
		return resultPlans
	}

	// 4. 按比例分摊减免金额
	var totalPayAmount int64 = 0
	lastValidIndex := -1

	for i, p := range resultPlans {
		if p.PayAmount > 0 && !p.IsGift {
			// 计算该房费应分摊的减免金额
			discount := payBill.RoomDiscountAmount * resultPlans[i].PayAmount / totalAmount
			// 设置最终支付金额
			resultPlans[i].PayAmount = max(0, resultPlans[i].PayAmount-discount)
			totalPayAmount += resultPlans[i].PayAmount
			lastValidIndex = i
		} else {
			resultPlans[i].PayAmount = 0
		}
	}

	// 5. 处理精度损失
	if lastValidIndex >= 0 {
		expectedAmount := totalAmount - payBill.RoomDiscountAmount
		if expectedAmount > 0 {
			diff := expectedAmount - totalPayAmount
			resultPlans[lastValidIndex].PayAmount += diff
		}
	}

	return resultPlans
}
