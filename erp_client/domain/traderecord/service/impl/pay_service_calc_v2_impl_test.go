package impl

import (
	"context"
	"testing"
	_const "voderpltvv/const"
	"voderpltvv/erp_managent/api/vo"
)

func TestPayServiceImpl_CalculateTotalPayment(t *testing.T) {
	tests := []struct {
		name              string
		session           vo.SessionVO
		orders            []vo.OrderVO
		payBill           vo.PayBillVO
		expectedTotal     int64
		expectedProducts  []vo.OrderProductVO
		expectedRoomPlans []vo.OrderRoomPlanVO
	}{
		{
			name: "基础场景-无折扣",
			session: vo.SessionVO{
				SessionId:  "S001",
				MinConsume: 1000,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P004",
							OriginalPrice:       100,
							Quantity:            2,
							MemberDiscountable:  false,
							ProductDiscountable: true,
						},
					},
					OrderRoomPlanVOs: []vo.OrderRoomPlanVO{
						{
							Id:                 "R001",
							OriginalPayAmount:  500,
							MemberDiscountable: false,
						},
					},
				},
			},
			payBill:       vo.PayBillVO{},
			expectedTotal: 700, // (100*2 + 500)
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P004",
					OriginalPrice:       100,
					Quantity:            2,
					MemberDiscountable:  false,
					ProductDiscountable: true,
					PayAmount:           200, // 100 * 2
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{
				{
					Id:                 "R001",
					OriginalPayAmount:  500,
					MemberDiscountable: false,
					PayAmount:          500,
				},
			},
		},
		{
			name: "商品会员价+房费会员折扣场景",
			session: vo.SessionVO{
				SessionId:  "S002",
				MinConsume: 1000,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					ConfigRoomMemberDiscountType:    _const.ROOM_MEMBER_DISCOUNT_TYPE_DISCOUNT,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P002",
							OriginalPrice:       100,
							MemberPrice:         80,
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
					},
					OrderRoomPlanVOs: []vo.OrderRoomPlanVO{
						{
							Id:                 "R002",
							OriginalPayAmount:  1000,
							MemberDiscount:     90,
							MemberDiscountable: true,
						},
					},
				},
			},
			payBill:       vo.PayBillVO{},
			expectedTotal: 1060, // (80*2 + 1000*0.9)
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P002",
					OriginalPrice:       100,
					MemberPrice:         80,
					Quantity:            2,
					MemberDiscountable:  true,
					ProductDiscountable: true,
					PayAmount:           160, // 80 * 2
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{
				{
					Id:                 "R002",
					OriginalPayAmount:  1000,
					MemberDiscount:     90,
					MemberDiscountable: true,
					PayAmount:          900, // 1000 * 0.9
				},
			},
		},
		{
			name: "商品整单折扣+房费减免场景",
			session: vo.SessionVO{
				SessionId:  "S003",
				MinConsume: 0,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P003",
							OriginalPrice:       200,
							Quantity:            2,
							ProductDiscountable: true,
						},
					},
					OrderRoomPlanVOs: []vo.OrderRoomPlanVO{
						{
							Id:                "R003",
							OriginalPayAmount: 800,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount:    80,
				DiscountType:       _const.DISCOUNT_TYPE_ALL,
				RoomDiscountAmount: 200,
			},
			expectedTotal: 920, // (200*2*0.8 + (800-200))
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P003",
					OriginalPrice:       200,
					Quantity:            2,
					ProductDiscountable: true,
					PayProductDiscount:  80,
					PayAmount:           320, // 200 * 2 * 0.8
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{
				{
					Id:                "R003",
					OriginalPayAmount: 800,
					PayAmount:         600, // 800 - 200
				},
			},
		},
		{
			name: "商品会员价场景",
			session: vo.SessionVO{
				SessionId:  "S004",
				MinConsume: 1000,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P004_1",
							OriginalPrice:       100,
							MemberPrice:         80,
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill:       vo.PayBillVO{},
			expectedTotal: 160, // 80*2
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P004_1",
					OriginalPrice:       100,
					MemberPrice:         80,
					Quantity:            2,
					MemberDiscountable:  true,
					ProductDiscountable: true,
					PayAmount:           160, // 80 * 2
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "强制最低消费场景",
			session: vo.SessionVO{
				SessionId:  "S005",
				MinConsume: 1000,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P005_1",
							OriginalPrice:       100,
							Quantity:            2,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ForceMinimumCharge: true,
			},
			expectedTotal: 1000, // 强制最低消费
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P005_1",
					OriginalPrice:       100,
					Quantity:            2,
					ProductDiscountable: true,
					PayAmount:           200, // 强制最低消费
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "商品整单折扣场景",
			session: vo.SessionVO{
				SessionId:  "S006",
				MinConsume: 0,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P006_1",
							OriginalPrice:       100,
							Quantity:            2,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 80, // 8折
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 160, // (100*2) * 0.8
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P006_1",
					OriginalPrice:       100,
					Quantity:            2,
					ProductDiscountable: true,
					PayAmount:           160, // (100*2) * 0.8
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "超出低消部分折扣场景",
			session: vo.SessionVO{
				SessionId:  "S007",
				MinConsume: 150,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P007_1",
							OriginalPrice:       100,
							Quantity:            3,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 80, // 8折
				DiscountType:    _const.DISCOUNT_TYPE_PART,
			},
			expectedTotal: 270, // 150 + (300-150)*0.8
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P007_1",
					OriginalPrice:       100,
					Quantity:            3,
					ProductDiscountable: true,
					PayAmount:           270, // 150 + (300-150)*0.8
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "混合折扣场景-会员价+整单折扣",
			session: vo.SessionVO{
				SessionId:  "S008",
				MinConsume: 0,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P008_1",
							OriginalPrice:       200,
							MemberPrice:         160,
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
						{
							Id:                  "P008_2",
							OriginalPrice:       100,
							Quantity:            1,
							MemberDiscountable:  false, // 不参与会员折扣
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 90, // 9折
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 378, // (160*2 + 100) * 0.9
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P008_1",
					OriginalPrice:       200,
					MemberPrice:         160,
					Quantity:            2,
					MemberDiscountable:  true,
					ProductDiscountable: true,
					PayAmount:           288, // 160 * 2 * 0.9
				},
				{
					Id:                  "P008_2",
					OriginalPrice:       100,
					Quantity:            1,
					MemberDiscountable:  false, // 不参与会员折扣
					ProductDiscountable: true,
					PayAmount:           90, // 100 * 1 * 0.9
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "复杂场景-房费会员折扣+商品会员价+超额折扣",
			session: vo.SessionVO{
				SessionId:  "S009",
				MinConsume: 500,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					ConfigRoomMemberDiscountType:    _const.ROOM_MEMBER_DISCOUNT_TYPE_DISCOUNT,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P009_1",
							OriginalPrice:       200,
							MemberPrice:         160, // 会员价是最终价格
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
					},
					OrderRoomPlanVOs: []vo.OrderRoomPlanVO{
						{
							Id:                 "R009_1",
							OriginalPayAmount:  400,
							MemberDiscount:     90, // 9折
							MemberDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 80, // 8折，但不影响已经使用会员价的商品
				DiscountType:    _const.DISCOUNT_TYPE_PART,
			},
			expectedTotal: 680, // (160*2) + (400*0.9)
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P009_1",
					OriginalPrice:       200,
					MemberPrice:         160, // 会员价是最终价格
					Quantity:            2,
					MemberDiscountable:  true,
					ProductDiscountable: true,
					PayAmount:           320, // 160 * 2
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{
				{
					Id:                 "R009_1",
					OriginalPayAmount:  400,
					MemberDiscount:     90, // 9折
					MemberDiscountable: true,
					PayAmount:          360, // 400 * 0.9
				},
			},
		},
		{
			name: "多订单复合场景-含免费商品",
			session: vo.SessionVO{
				SessionId:  "S010",
				MinConsume: 300,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P010_1",
							OriginalPrice:       150,
							MemberPrice:         120, // 会员价是最终价格
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
						{
							Id:                  "P010_2",
							OriginalPrice:       0,
							Quantity:            1,
							Freeable:            true,
							ProductDiscountable: true,
						},
					},
				},
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P010_3",
							OriginalPrice:       80,
							Quantity:            3,
							MemberDiscountable:  false,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount:       85, // 8.5折，只对非会员价商品生效
				DiscountType:          _const.DISCOUNT_TYPE_PART,
				ProductDiscountAmount: 50,
			},
			expectedTotal: 403, // 300 + (240 + 0 + 240 - 300) * 0.85 - 50
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P010_1",
					OriginalPrice:       150,
					MemberPrice:         120, // 会员价是最终价格
					Quantity:            2,
					MemberDiscountable:  true,
					ProductDiscountable: true,
					PayAmount:           202, // 120 * 2
				},
				{
					Id:                  "P010_2",
					OriginalPrice:       0,
					Quantity:            1,
					Freeable:            true,
					ProductDiscountable: true,
					PayAmount:           0, // 免费商品
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "极限场景-多种折扣叠加",
			session: vo.SessionVO{
				SessionId:  "S011",
				MinConsume: 1000,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					ConfigRoomMemberDiscountType:    _const.ROOM_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P011_1",
							OriginalPrice:       300,
							MemberPrice:         240,
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
						{
							Id:                  "P011_2",
							OriginalPrice:       200,
							Quantity:            1,
							MemberDiscountable:  false,
							Freeable:            true, // 免费商品
							IsFree:              true, // 已免单
							ProductDiscountable: true,
						},
					},
					OrderRoomPlanVOs: []vo.OrderRoomPlanVO{
						{
							Id:                 "R011_1",
							OriginalPayAmount:  800,
							MemberDiscount:     85, // 8.5折
							MemberDiscountable: true,
						},
					},
				},
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P011_3",
							OriginalPrice:       150,
							Quantity:            2,
							MemberDiscountable:  true,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount:       90, // 9折
				DiscountType:          _const.DISCOUNT_TYPE_PART,
				ProductDiscountAmount: 100,
				ForceMinimumCharge:    false,
			},
			expectedTotal: 1480, // 800 + 0 + 480 + 300 - 100
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P011_1",
					OriginalPrice:       300,
					MemberPrice:         240,
					Quantity:            2,
					MemberDiscountable:  true,
					ProductDiscountable: true,
					PayAmount:           419, // 240 * 2 * 680/780
				},
				{
					Id:                  "P011_2",
					OriginalPrice:       200,
					Quantity:            1,
					MemberDiscountable:  false,
					Freeable:            true, // 免费商品
					ProductDiscountable: true,
					PayAmount:           0, // 免费商品
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{
				{
					Id:                 "R011_1",
					OriginalPayAmount:  800,
					MemberDiscount:     85, // 8.5折
					MemberDiscountable: true,
					PayAmount:          800, // 800
				},
			},
		},
		{
			name: "特殊商品属性场景-商品折扣+会员折扣",
			session: vo.SessionVO{
				SessionId:  "S012",
				MinConsume: 200,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P012_1",
							OriginalPrice:       100,
							Quantity:            2,
							ProductDiscountable: true,  // 可参与商品折扣
							MemberDiscountable:  false, // 不可参与会员折扣
						},
						{
							Id:                  "P012_2",
							OriginalPrice:       150,
							MemberPrice:         120,
							Quantity:            1,
							ProductDiscountable: false, // 明确设置不可参与商品折扣
							MemberDiscountable:  true,  // 可参与会员折扣
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 90, // 9折
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 300, // (100*2*0.9) +  = 180 + 120
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P012_1",
					OriginalPrice:       100,
					Quantity:            2,
					ProductDiscountable: true,  // 可参与商品折扣
					MemberDiscountable:  false, // 不可参与会员折扣
					PayAmount:           180,   // 100 * 2 * 0.9
				},
				{
					Id:                  "P012_2",
					OriginalPrice:       150,
					MemberPrice:         120,
					Quantity:            1,
					ProductDiscountable: false, // 明确设置不可参与商品折扣
					MemberDiscountable:  true,  // 可参与会员折扣
					PayAmount:           120,   // 120 * 1
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "特殊商品属性场景-赠送+免单",
			session: vo.SessionVO{
				SessionId:  "S013",
				MinConsume: 500,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P013_1",
							OriginalPrice:       200,
							Quantity:            2,
							Giftable:            true, // 可赠送
							IsGift:              true, // 已赠送
							ProductDiscountable: true,
						},
						{
							Id:                  "P013_2",
							OriginalPrice:       150,
							Quantity:            1,
							Freeable:            true, // 可免单
							IsFree:              true, // 已免单
							ProductDiscountable: true,
						},
						{
							Id:                  "P013_3",
							OriginalPrice:       100,
							Quantity:            3,
							ProductDiscountable: true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 80, // 8折
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 240, // 0(赠送) + 0(免单) + (100*3*0.8)
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P013_1",
					OriginalPrice:       200,
					Quantity:            2,
					Giftable:            true, // 可赠送
					IsGift:              true, // 已赠送
					ProductDiscountable: true,
					PayAmount:           0, // 赠送
				},
				{
					Id:                  "P013_2",
					OriginalPrice:       150,
					Quantity:            1,
					Freeable:            true, // 可免单
					IsFree:              true, // 已免单
					ProductDiscountable: true,
					PayAmount:           0, // 免单
				},
				{
					Id:                  "P013_3",
					OriginalPrice:       100,
					Quantity:            3,
					ProductDiscountable: true,
					PayAmount:           240, // 100 * 3 * 0.8
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "特殊商品属性场景-畅饮+多品赠送",
			session: vo.SessionVO{
				SessionId:  "S014",
				MinConsume: 300,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P014_1",
							OriginalPrice:       80,
							Quantity:            3,
							IsFreeDrinking:      true, // 畅饮
							ProductDiscountable: true,
							PayAmount:           0, // 畅饮只收一份
						},
						{
							Id:                  "P014_2",
							OriginalPrice:       120,
							Quantity:            2,
							IsMultiProductGift:  true, // 多品赠送
							ProductDiscountable: true,
							PayAmount:           0, // 多品赠送
						},
						{
							Id:                  "P014_3",
							OriginalPrice:       150,
							Quantity:            1,
							ProductDiscountable: true,
							MemberDiscountable:  true,
							PayAmount:           127, // 150 * 0.85
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 85, // 8.5折
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 127, // 0(畅饮只收一份) + 0(多品赠送) + (150*0.85) = 127.5 = 127
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P014_1",
					OriginalPrice:       80,
					Quantity:            3,
					IsFreeDrinking:      true, // 畅饮
					ProductDiscountable: true,
					PayAmount:           0, // 畅饮只收一份
				},
				{
					Id:                  "P014_2",
					OriginalPrice:       120,
					Quantity:            2,
					IsMultiProductGift:  true, // 多品赠送
					ProductDiscountable: true,
					PayAmount:           0, // 多品赠送
				},
				{
					Id:                  "P014_3",
					OriginalPrice:       150,
					Quantity:            1,
					ProductDiscountable: true,
					MemberDiscountable:  true,
					PayAmount:           127, // 150 * 0.85
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "复杂混合场景-多种特殊属性组合",
			session: vo.SessionVO{
				SessionId:  "S015",
				MinConsume: 1000,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P015_1",
							OriginalPrice:       200,
							MemberPrice:         160,
							Quantity:            2,
							ProductDiscountable: false,
							MemberDiscountable:  true,
						},
						{
							Id:                  "P015_2",
							OriginalPrice:       150,
							Quantity:            3,
							IsFreeDrinking:      true,
							ProductDiscountable: true,
						},
						{
							Id:                  "P015_3",
							OriginalPrice:       100,
							Quantity:            1,
							Freeable:            true,
							IsFree:              true,
							ProductDiscountable: true,
						},
					},
				},
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                  "P015_4",
							OriginalPrice:       180,
							Quantity:            2,
							ProductDiscountable: true,
							Giftable:            true,
							IsGift:              true,
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount:       90, // 9折
				DiscountType:          _const.DISCOUNT_TYPE_PART,
				ProductDiscountAmount: 50,
			},
			expectedTotal: 270, // 会员价(320) + 畅饮(0) + 免单(0) + 赠送(0) - 50
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                  "P015_1",
					OriginalPrice:       200,
					MemberPrice:         160,
					Quantity:            2,
					ProductDiscountable: false,
					MemberDiscountable:  true,
					PayAmount:           270, // 160 * 2 - 50
				},
				{
					Id:                  "P015_2",
					OriginalPrice:       150,
					Quantity:            3,
					IsFreeDrinking:      true,
					ProductDiscountable: true,
					PayAmount:           0, // 畅饮
				},
				{
					Id:                  "P015_3",
					OriginalPrice:       100,
					Quantity:            1,
					Freeable:            true,
					IsFree:              true,
					ProductDiscountable: true,
					PayAmount:           0, // 免单
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "点单商品折扣-OrderProductDiscount",
			session: vo.SessionVO{
				SessionId:  "S016",
				MinConsume: 200,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                   "P016_1",
							OriginalPrice:        100,
							Quantity:             2,
							ProductDiscountable:  true,
							OrderProductDiscount: 80, // 点单时8折
						},
						{
							Id:                   "P016_2",
							OriginalPrice:        150,
							Quantity:             1,
							ProductDiscountable:  true,
							OrderProductDiscount: 90, // 点单时9折
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 85, // 8.5折，但不影响已经打过折的商品
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 295, // (100*2*0.8) + (150*0.9) = 160 + 135 = 295
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                   "P016_1",
					OriginalPrice:        100,
					Quantity:             2,
					ProductDiscountable:  true,
					OrderProductDiscount: 80,  // 点单时8折
					PayAmount:            160, // 100 * 2 * 0.8
				},
				{
					Id:                   "P016_2",
					OriginalPrice:        150,
					Quantity:             1,
					ProductDiscountable:  true,
					OrderProductDiscount: 90,  // 点单时9折
					PayAmount:            135, // 150 * 0.9
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "混合折扣-OrderProductDiscount+ProductDiscount",
			session: vo.SessionVO{
				SessionId:  "S017",
				MinConsume: 300,
			},
			orders: []vo.OrderVO{
				{
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                   "P017_1",
							OriginalPrice:        200,
							Quantity:             2,
							ProductDiscountable:  true,
							OrderProductDiscount: 85, // 点单时8.5折
						},
						{
							Id:                  "P017_2",
							OriginalPrice:       150,
							Quantity:            2,
							ProductDiscountable: true, // 未设置OrderProductDiscount，参与PayBill折扣
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 90, // 9折，只对未打折商品生效
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 610, // (200*2*0.85) + (150*2*0.9) = 340 + 270 = 610
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                   "P017_1",
					OriginalPrice:        200,
					Quantity:             2,
					ProductDiscountable:  true,
					OrderProductDiscount: 85,  // 点单时8.5折
					PayAmount:            340, // 200 * 2 * 0.85
				},
				{
					Id:                  "P017_2",
					OriginalPrice:       150,
					Quantity:            2,
					ProductDiscountable: true, // 未设置OrderProductDiscount，参与PayBill折扣
					PayAmount:           270,  // 150 * 2 * 0.9
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
		{
			name: "复杂场景-多种折扣组合",
			session: vo.SessionVO{
				SessionId:  "S018",
				MinConsume: 500,
			},
			orders: []vo.OrderVO{
				{
					ConfigProductMemberDiscountType: _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE,
					OrderProductVOs: []vo.OrderProductVO{
						{
							Id:                   "P001",
							OriginalPrice:        200,
							MemberPrice:          160,
							Quantity:             2,
							ProductDiscountable:  true,
							MemberDiscountable:   true,
							OrderProductDiscount: 90, // 点单时9折，但因为有会员价，所以使用会员价
						},
						{
							Id:                   "P002",
							OriginalPrice:        150,
							Quantity:             2,
							ProductDiscountable:  true,
							OrderProductDiscount: 80, // 点单时8折
						},
						{
							Id:                  "P003",
							OriginalPrice:       100,
							Quantity:            3,
							ProductDiscountable: true, // 未设置OrderProductDiscount，参与PayBill折扣
						},
					},
				},
			},
			payBill: vo.PayBillVO{
				ProductDiscount: 85, // 8.5折，只对未打折商品生效
				DiscountType:    _const.DISCOUNT_TYPE_ALL,
			},
			expectedTotal: 783,
			expectedProducts: []vo.OrderProductVO{
				{
					Id:                   "P001",
					OriginalPrice:        200,
					MemberPrice:          160,
					Quantity:             2,
					ProductDiscountable:  true,
					MemberDiscountable:   true,
					OrderProductDiscount: 90,
					PayAmount:            288, // 160 * 2 * 0.9
				},
				{
					Id:                   "P002",
					OriginalPrice:        150,
					Quantity:             2,
					ProductDiscountable:  true,
					OrderProductDiscount: 80,
					PayAmount:            240, // 150 * 2 * 0.8
				},
				{
					Id:                  "P003",
					OriginalPrice:       100,
					Quantity:            3,
					ProductDiscountable: true,
					PayProductDiscount:  85,
					PayAmount:           255, // 100 * 3 * 0.85
				},
			},
			expectedRoomPlans: []vo.OrderRoomPlanVO{},
		},
	}

	service := &PayServiceImpl{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			total, resultProducts, resultRoomPlans, _, _ := service.CalculateTotalPayment(ctx, tt.session, tt.orders, tt.payBill)

			// 打印测试场景信息
			t.Logf("\n=== 测试场景: %s ===", tt.name)
			t.Logf("总金额: %d (期望: %d)", total, tt.expectedTotal)

			// 验证总金额
			if total != tt.expectedTotal {
				t.Errorf("总金额计算错误: 期望 %d, 实际 %d", tt.expectedTotal, total)
			}

			// 验证商品金额
			t.Log("\n商品计算结果:")
			t.Log("ID\t原价\t数量\t折扣\t实际金额\t期望金额")
			t.Log("----------------------------------------------------")
			for i, exp := range tt.expectedProducts {
				if i >= len(resultProducts) {
					t.Errorf("商品数量不匹配")
					continue
				}
				result := resultProducts[i]
				t.Logf("%s\t%d\t%d\t%d\t%d\t%d",
					result.Id,
					result.OriginalPrice,
					result.Quantity,
					result.PayProductDiscount,
					result.PayAmount,
					exp.PayAmount,
				)
				if result.PayAmount != exp.PayAmount {
					t.Errorf("商品[%s]金额计算错误: 期望 %d, 实际 %d",
						result.Id, exp.PayAmount, result.PayAmount)
				}
			}

			// 验证房费金额
			t.Log("\n房费计算结果:")
			t.Log("ID\t原价\t折扣\t实际金额\t期望金额")
			t.Log("----------------------------------------------------")
			for i, exp := range tt.expectedRoomPlans {
				if i >= len(resultRoomPlans) {
					t.Errorf("房费数量不匹配")
					continue
				}
				result := resultRoomPlans[i]
				t.Logf("%s\t%d\t%d\t%d\t%d",
					result.Id,
					result.OriginalPayAmount,
					result.PayRoomDiscount,
					result.PayAmount,
					exp.PayAmount,
				)
				if result.PayAmount != exp.PayAmount {
					t.Errorf("房费[%s]金额计算错误: 期望 %d, 实际 %d",
						result.Id, exp.PayAmount, result.PayAmount)
				}
			}
			t.Log("\n----------------------------------------------------")
		})
	}
}
