package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// OrderAndPayServiceImpl 订单和支付服务实现
type OrderAndPayServiceImpl struct {
	orderAndPayRepo repository.OrderAndPayRepository
}

// NewPayBillService 创建支付账单服务实例
func NewOrderAndPayService(orderAndPayRepo repository.OrderAndPayRepository) *OrderAndPayServiceImpl {
	return &OrderAndPayServiceImpl{
		orderAndPayRepo: orderAndPayRepo,
	}
}

// FindAllByBillIds 查询所有支付账单
func (s *OrderAndPayServiceImpl) FindAllByBillIds(ctx context.Context, billIds []string) (*[]po.OrderAndPay, error) {
	return s.orderAndPayRepo.FindAllByBillIds(ctx, billIds)
}

// ConvertToOrderAndPayVO 转换为订单和支付VO
func (s *OrderAndPayServiceImpl) ConvertToOrderAndPayVO(ctx context.Context, orderAndPay po.OrderAndPay) vo.OrderAndPayVO {
	return s.orderAndPayRepo.ConvertToOrderAndPayVO(ctx, orderAndPay)
}

// ConvertToOrderAndPay 转换为订单和支付PO
func (s *OrderAndPayServiceImpl) ConvertToOrderAndPay(ctx context.Context, orderAndPayVO vo.OrderAndPayVO) po.OrderAndPay {
	return s.orderAndPayRepo.ConvertToOrderAndPay(ctx, orderAndPayVO)
}

// FindAllBySessionId 查询所有支付账单
func (s *OrderAndPayServiceImpl) FindAllBySessionId(ctx context.Context, sessionId string) ([]po.OrderAndPay, error) {
	return s.orderAndPayRepo.FindAllBySessionId(ctx, sessionId)
}

// FindsByOrderNos 查询订单和支付
func (s *OrderAndPayServiceImpl) FindsByOrderNos(ctx context.Context, orderNos []string) ([]po.OrderAndPay, error) {
	return s.orderAndPayRepo.FindsByOrderNos(ctx, orderNos)
}
