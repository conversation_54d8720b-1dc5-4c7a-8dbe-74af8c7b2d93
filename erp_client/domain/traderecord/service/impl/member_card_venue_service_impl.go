package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardVenueServiceImpl 会员卡门店关联服务实现
type MemberCardVenueServiceImpl struct {
	memberCardVenueRepo repository.MemberCardVenueRepository
}

// NewPayBillService 创建支付账单服务实例
func NewMemberCardVenueService(memberCardVenueRepo repository.MemberCardVenueRepository) *MemberCardVenueServiceImpl {
	return &MemberCardVenueServiceImpl{
		memberCardVenueRepo: memberCardVenueRepo,
	}
}

// FindAllMemberCardVenue 查询所有会员卡门店关联
func (s *MemberCardVenueServiceImpl) FindAllMemberCardVenue(ctx context.Context, venueId string) (*[]po.MemberCardVenue, error) {
	return s.memberCardVenueRepo.FindAllMemberCardVenue(ctx, venueId)
}

// ConvertToMemberCardVenueVO 转换为会员卡门店关联VO
func (s *MemberCardVenueServiceImpl) ConvertToMemberCardVenueVO(ctx context.Context, memberCardVenue po.MemberCardVenue) vo.MemberCardVenueVO {
	return s.memberCardVenueRepo.ConvertToMemberCardVenueVO(ctx, memberCardVenue)
}

// ConvertToMemberCardVenue 转换为会员卡门店关联PO
func (s *MemberCardVenueServiceImpl) ConvertToMemberCardVenue(ctx context.Context, memberCardVenueVO vo.MemberCardVenueVO) po.MemberCardVenue {
	return s.memberCardVenueRepo.ConvertToMemberCardVenue(ctx, memberCardVenueVO)
}

// FindByMemberCardId 根据会员卡ID查询会员卡门店关联
func (s *MemberCardVenueServiceImpl) FindByMemberCardId(ctx context.Context, memberCardId string) ([]po.MemberCardVenue, error) {
	return s.memberCardVenueRepo.FindByMemberCardId(ctx, memberCardId)
}

// FindAllByVenueId 根据场馆ID查询所有会员卡门店关联
func (s *MemberCardVenueServiceImpl) FindAllByVenueId(ctx context.Context, venueId string) ([]po.MemberCardVenue, error) {
	return s.memberCardVenueRepo.FindAllByVenueId(ctx, venueId)
}
