package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberTransferMoneyServiceImpl 会员转账记录服务实现
type MemberTransferMoneyServiceImpl struct {
	memberTransferMoneyRepo repository.MemberTransferMoneyRepository
}

// NewMemberTransferMoneyService 创建会员转账记录服务实例
func NewMemberTransferMoneyService(memberTransferMoneyRepo repository.MemberTransferMoneyRepository) *MemberTransferMoneyServiceImpl {
	return &MemberTransferMoneyServiceImpl{
		memberTransferMoneyRepo: memberTransferMoneyRepo,
	}
}

// CreateMemberTransferMoney 创建会员转账记录
func (s *MemberTransferMoneyServiceImpl) CreateMemberTransferMoney(ctx context.Context, memberTransferMoney *po.MemberTransferMoney) error {
	return s.memberTransferMoneyRepo.CreateMemberTransferMoney(ctx, memberTransferMoney)
}

// ConvertToMemberTransferMoneyVO 转换为会员转账记录VO
func (s *MemberTransferMoneyServiceImpl) ConvertToMemberTransferMoneyVO(ctx context.Context, memberTransferMoney po.MemberTransferMoney) vo.MemberTransferMoneyVO {
	return s.memberTransferMoneyRepo.ConvertToMemberTransferMoneyVO(ctx, memberTransferMoney)
}

// ConvertToMemberTransferMoney 转换为会员转账记录PO
func (s *MemberTransferMoneyServiceImpl) ConvertToMemberTransferMoney(ctx context.Context, memberTransferMoneyVO vo.MemberTransferMoneyVO) po.MemberTransferMoney {
	return s.memberTransferMoneyRepo.ConvertToMemberTransferMoney(ctx, memberTransferMoneyVO)
}
