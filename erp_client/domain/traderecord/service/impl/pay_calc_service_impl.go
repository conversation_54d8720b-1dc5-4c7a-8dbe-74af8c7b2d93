package impl

import (
	"context"
	"fmt"
	_const "voderpltvv/const"
	tradeRepo "voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
)

// PayCalcServiceImpl 支付计算服务实现
type PayCalcServiceImpl struct {
	payRepo tradeRepo.PayRepository
}

// NewPayService 创建支付服务实例
func NewPayCalcService(payRepo tradeRepo.PayRepository) *PayCalcServiceImpl {
	return &PayCalcServiceImpl{
		payRepo: payRepo,
	}
}

// 计算商品单价，并标记打折状态
func (s *PayCalcServiceImpl) _calcProductUnitPrice(product *vo.OrderProductVO, memberDiscountType int64) int64 {
	basePrice := product.OriginalPrice

	// 1. 特殊情况处理
	if product.IsFree || product.IsGift || product.IsFreeDrinking || product.IsMultiProductGift {
		return 0
	}

	// 2. 会员价处理
	if product.MemberDiscountable && memberDiscountType == _const.PRODUCT_MEMBER_DISCOUNT_TYPE_PRICE {
		if product.MemberPrice > 0 {
			basePrice = product.MemberPrice
		}
	}

	// 3. 会员折扣处理
	if product.MemberDiscountable && memberDiscountType == _const.PRODUCT_MEMBER_DISCOUNT_TYPE_DISCOUNT {
		if product.MemberDiscount > 0 {
			basePrice = basePrice * product.MemberDiscount / 100
		}
	}

	// 4. 商品折扣处理 - 标记打折状态
	if product.ProductDiscountable && product.OrderProductDiscount > 0 {
		basePrice = basePrice * product.OrderProductDiscount / 100
		product.IsDiscounted = true // 标记为已打折
	}

	return basePrice
}

// 计算房费单价
func (s *PayCalcServiceImpl) _calcRoomUnitPrice(plan *vo.OrderRoomPlanVO, memberDiscountType int64) int64 {
	basePrice := plan.OriginalPayAmount

	// 0. 特殊情况处理
	if plan.IsGift || plan.IsFree {
		return 0
	}

	// 1. 会员价处理
	if plan.MemberDiscountable && memberDiscountType == _const.ROOM_MEMBER_DISCOUNT_TYPE_PRICE {
		if plan.MemberPrice > 0 {
			basePrice = plan.MemberPrice
		}
	}

	// 2. 会员折扣处理
	if plan.MemberDiscountable && memberDiscountType == _const.ROOM_MEMBER_DISCOUNT_TYPE_DISCOUNT {
		if plan.MemberDiscount > 0 {
			basePrice = basePrice * plan.MemberDiscount / 100
			plan.PayRoomDiscount = plan.MemberDiscount // 记录会员折扣到PayRoomDiscount字段
		}
	}

	return basePrice
}

// 计算房费支付金额
func (s *PayCalcServiceImpl) _calcRoomPayment(plans []vo.OrderRoomPlanVO, order vo.OrderVO) int64 {
	if plans == nil {
		return 0
	}
	var total int64 = 0
	// 使用索引遍历以便修改原始数据
	for i := range plans {
		roomFee := s._calcRoomUnitPrice(&plans[i], order.ConfigRoomMemberDiscountType)
		plans[i].PayAmount = roomFee // 设置计算后的支付金额
		total += roomFee
	}
	return total
}

// 计算场次已支付订单金额
func (s *PayCalcServiceImpl) _calcPaidAmount(ctx context.Context, venueId, sessionId string) int64 {
	if venueId == "" || sessionId == "" {
		return 0
	}
	// 查询场次所有已支付订单
	paidAmount, err := s.payRepo.QueryPaidOrderVOsBySessionId(ctx, venueId, sessionId)
	if err != nil {
		return 0
	}
	return paidAmount
}

// CalculateProductRoomPayment 计算商品和房费
func (s *PayCalcServiceImpl) CalculateProductRoomPayment(ctx context.Context, session vo.SessionVO, orders []vo.OrderVO, payBill vo.PayBillVO) (int64, []vo.OrderProductVO, []vo.OrderRoomPlanVO) {
	// 计算实际最低消费金额 = max(0, 原始最低消费 - 已支付订单金额)
	paidAmount := s._calcPaidAmount(ctx, session.VenueId, session.SessionId)
	minConsume := max(0, session.MinConsume-paidAmount)

	// 0. 计算商品单价并标记打折状态
	var allProducts []vo.OrderProductVO
	for _, order := range orders {
		products := order.OrderProductVOs
		for i := range products {
			products[i].UnitPrice = s._calcProductUnitPrice(&products[i], order.ConfigProductMemberDiscountType)
		}
		allProducts = append(allProducts, products...)
	}

	// 1. 提取未打折的商品列表
	var undiscountedProducts []vo.OrderProductVO
	var discountedAmount int64 = 0
	for _, p := range allProducts {
		if !p.IsDiscounted {
			undiscountedProducts = append(undiscountedProducts, p)
		} else {
			discountedAmount += p.UnitPrice * p.Quantity
		}
	}

	// 2. 针对未打折商品计算折后价格
	var undiscountedAmount int64 = 0
	var nonDiscountableAmount int64 = 0
	for _, p := range undiscountedProducts {
		productAmount := p.UnitPrice * p.Quantity
		if p.ProductDiscountable {
			undiscountedAmount += productAmount
		} else {
			nonDiscountableAmount += productAmount
		}
	}

	if payBill.ProductDiscount > 0 {
		if payBill.DiscountType == _const.DISCOUNT_TYPE_ALL {
			// 只对可打折商品进行折扣计算
			undiscountedAmount = undiscountedAmount * payBill.ProductDiscount / 100

			// 更新 PayProductDiscount 到原始商品列表
			for _, p := range undiscountedProducts {
				if p.ProductDiscountable && !(p.IsFree || p.IsGift || p.IsFreeDrinking || p.IsMultiProductGift) {
					// 找到对应的原始商品并更新
					for i := range allProducts {
						// 如果商品id相同，或者套餐id相同，并且来源相同，则更新折扣
						if allProducts[i].ProductId == p.ProductId && allProducts[i].PackageId == p.PackageId && allProducts[i].Src == p.Src {
							allProducts[i].PayProductDiscount = payBill.ProductDiscount
							// fmt.Println("更新前商品信息: id:", allProducts[i].Id, "原价:", allProducts[i].OriginalPrice, "单价:", allProducts[i].UnitPrice, "折扣:", allProducts[i].PayProductDiscount, "IsDiscounted:", allProducts[i].IsDiscounted)
							// fmt.Println("更新后商品信息: id:", p.Id, "原价:", p.OriginalPrice, "单价:", p.UnitPrice, "折扣:", p.PayProductDiscount, "IsDiscounted:", p.IsDiscounted)
							break
						}
					}
				}
			}

			// 不可打折商品保持原价
			undiscountedAmount += nonDiscountableAmount
		} else {
			// 仅超过低消部分打折
			totalProductAmount := undiscountedAmount + nonDiscountableAmount + discountedAmount
			if totalProductAmount > minConsume {
				overMinAmount := totalProductAmount - minConsume
				// 只对可打折金额部分计算折扣
				discountableOverMin := min(overMinAmount, undiscountedAmount)
				discountedOverMin := discountableOverMin * payBill.ProductDiscount / 100
				undiscountedAmount = undiscountedAmount - discountableOverMin + discountedOverMin + nonDiscountableAmount
				payBill.OverMinProductDiscountAmount = discountableOverMin - discountedOverMin
			} else {
				undiscountedAmount += nonDiscountableAmount
			}
		}
	} else {
		undiscountedAmount += nonDiscountableAmount
	}

	// 3. 未打折商品减免
	if payBill.ProductDiscountAmount > 0 {
		undiscountedAmount = max(0, undiscountedAmount-payBill.ProductDiscountAmount)
	}

	// 4. 房费折扣计算
	var roomAmount int64 = 0
	for _, order := range orders {
		roomAmount += s._calcRoomPayment(order.OrderRoomPlanVOs, order)
	}
	if payBill.RoomDiscount > 0 {
		roomAmount = roomAmount * payBill.RoomDiscount / 100
	}

	// 5. 房费减免
	if payBill.RoomDiscountAmount > 0 {
		roomAmount = max(0, roomAmount-payBill.RoomDiscountAmount)
	}

	// 6. 计算总金额并判断是否强制满足最低消费
	productAmount := undiscountedAmount + discountedAmount // 计算商品总金额
	fmt.Println("undiscountedAmount:", undiscountedAmount, "discountedAmount:", discountedAmount, "roomAmount:", roomAmount)

	// 判断商品金额是否满足最低消费
	if payBill.ForceMinimumCharge && productAmount <= minConsume {
		productAmount = minConsume // 商品金额强制调整为最低消费金额
	}

	// 最终总金额 = 商品金额 + 房费
	totalAmount := productAmount + roomAmount
	// for _, p := range allProducts {
	// 	fmt.Println("入参: id:", p.Id, "原价:", p.OriginalPrice, "单价:", p.UnitPrice, "折扣:", p.PayProductDiscount, "IsDiscounted:", p.IsDiscounted)
	// }
	// 7. 回写 op om 的payamount
	op := s.ReCalclateOrderProductPayAmount(allProducts, payBill)
	roomPlans := []vo.OrderRoomPlanVO{}
	for _, order := range orders {
		roomPlans = append(roomPlans, order.OrderRoomPlanVOs...)
	}
	om := s.ReCalculateOrderRoomPlanPayAmount(roomPlans, payBill)
	return totalAmount, op, om
}

// 回写 op 的payamount
// 1. 去掉金额为0的 和 if product.IsFree || product.IsGift || product.IsFreeDrinking || product.IsMultiProductGift
// 2. op 的payamount = UnitPrice * Quantity * PayProductDiscount
// 3. 订单的总优惠=payBill.ProductDiscountAmount + payBill.OverMinProductDiscountAmount
// 4. op.payamout-=均摊的优惠
func (s *PayCalcServiceImpl) ReCalclateOrderProductPayAmount(products []vo.OrderProductVO, payBill vo.PayBillVO) []vo.OrderProductVO {
	// 1. 筛选出需要计算的商品（排除金额为0和特殊商品）
	var validProducts []vo.OrderProductVO
	var resultProducts []vo.OrderProductVO = make([]vo.OrderProductVO, len(products))
	copy(resultProducts, products)

	for i, p := range resultProducts {
		if p.UnitPrice > 0 && !p.IsFree && !p.IsGift && !p.IsFreeDrinking && !p.IsMultiProductGift {
			validProducts = append(validProducts, p)
		} else {
			resultProducts[i].PayAmount = 0 // 特殊商品金额设为0
		}
	}

	// 2. 计算有效商品的总金额
	var totalAmount int64 = 0
	for _, p := range validProducts {
		amount := p.UnitPrice * p.Quantity
		if p.PayProductDiscount > 0 {
			amount = amount * p.PayProductDiscount / 100
		}
		totalAmount += amount
	}

	// 3. 计算总优惠金额
	totalDiscount := payBill.ProductDiscountAmount + payBill.OverMinProductDiscountAmount
	if totalDiscount <= 0 {
		// 如果没有优惠，直接设置 PayAmount
		for i, p := range resultProducts {
			if p.UnitPrice > 0 && !p.IsFree && !p.IsGift && !p.IsFreeDrinking && !p.IsMultiProductGift {
				amount := p.UnitPrice * p.Quantity
				if p.PayProductDiscount > 0 {
					amount = amount * p.PayProductDiscount / 100
				}
				resultProducts[i].PayAmount = amount
			}
		}
		return resultProducts
	}

	// 4. 按比例分摊优惠金额
	var totalPayAmount int64 = 0 // 用于累计实际分摊的金额
	lastValidIndex := -1         // 记录最后一个有效商品的索引

	for i, p := range resultProducts {
		if p.UnitPrice > 0 && !p.IsFree && !p.IsGift && !p.IsFreeDrinking && !p.IsMultiProductGift {
			amount := p.UnitPrice * p.Quantity
			if p.PayProductDiscount > 0 {
				amount = amount * p.PayProductDiscount / 100
			}

			// 计算该商品应分摊的优惠金额
			discount := totalDiscount * amount / totalAmount
			// 设置最终支付金额
			resultProducts[i].PayAmount = max(0, amount-discount)
			totalPayAmount += resultProducts[i].PayAmount
			lastValidIndex = i
		}
	}

	// 处理精度损失：将差额计入最后一个有效商品
	if lastValidIndex >= 0 {
		expectedAmount := totalAmount - totalDiscount
		if expectedAmount > 0 {
			diff := expectedAmount - totalPayAmount
			resultProducts[lastValidIndex].PayAmount += diff
		}
	}

	return resultProducts
}

// 回写 om 的payamount
// 1. 去掉金额为0的 和 if roomPlan.IsGift
// 2. om 的payamount = UnitPrice * PayRoomDiscount
// 3. 订单的总优惠 = 折扣优惠 + payBill.RoomDiscountAmount
// 4. om.payamout-=均摊的优惠
func (s *PayCalcServiceImpl) ReCalculateOrderRoomPlanPayAmount(plans []vo.OrderRoomPlanVO, payBill vo.PayBillVO) []vo.OrderRoomPlanVO {
	// 1. 创建返回结果并复制原始数据
	resultPlans := make([]vo.OrderRoomPlanVO, len(plans))
	copy(resultPlans, plans)

	// 2. 计算有效房费的总金额（先应用折扣）
	var totalAmount int64 = 0
	for i, p := range resultPlans {
		if p.PayAmount > 0 && !p.IsGift {
			// 应用房费折扣
			if payBill.RoomDiscount > 0 {
				resultPlans[i].PayAmount = p.PayAmount * payBill.RoomDiscount / 100
			}
			totalAmount += resultPlans[i].PayAmount
		}
	}

	// 3. 如果没有减免优惠，直接返回折扣后的金额
	if payBill.RoomDiscountAmount <= 0 {
		return resultPlans
	}

	// 4. 按比例分摊减免金额
	var totalPayAmount int64 = 0
	lastValidIndex := -1

	for i, p := range resultPlans {
		if p.PayAmount > 0 && !p.IsGift {
			// 计算该房费应分摊的减免金额
			discount := payBill.RoomDiscountAmount * resultPlans[i].PayAmount / totalAmount
			// 设置最终支付金额
			resultPlans[i].PayAmount = max(0, resultPlans[i].PayAmount-discount)
			totalPayAmount += resultPlans[i].PayAmount
			lastValidIndex = i
		} else {
			resultPlans[i].PayAmount = 0
		}
	}

	// 5. 处理精度损失
	if lastValidIndex >= 0 {
		expectedAmount := totalAmount - payBill.RoomDiscountAmount
		if expectedAmount > 0 {
			diff := expectedAmount - totalPayAmount
			resultPlans[lastValidIndex].PayAmount += diff
		}
	}

	return resultPlans
}
