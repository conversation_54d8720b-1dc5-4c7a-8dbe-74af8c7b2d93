package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
)

// MemberRechargeRecordServiceImpl 会员充值记录服务实现
type MemberRechargeRecordServiceImpl struct {
	memberRechargeRecordRepo repository.MemberRechargeRecordRepository
}

// NewPayBillService 创建支付账单服务实例
func NewMemberRechargeRecordService(memberRechargeRecordRepo repository.MemberRechargeRecordRepository) *MemberRechargeRecordServiceImpl {
	return &MemberRechargeRecordServiceImpl{
		memberRechargeRecordRepo: memberRechargeRecordRepo,
	}
}

// FindAllMemberRechargeRecord 查询所有会员充值记录
func (s *MemberRechargeRecordServiceImpl) FindAllMemberRechargeRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberRechargeRecord, error) {
	return s.memberRechargeRecordRepo.FindAllMemberRechargeRecord(ctx, venueId, sessionId, statusList)
}

// ConvertToPayBillVO 转换为支付账单VO
func (s *MemberRechargeRecordServiceImpl) ConvertToMemberRechargeRecordVO(ctx context.Context, memberRechargeRecord po.MemberRechargeRecord) vo.MemberRechargeRecordVO {
	return s.memberRechargeRecordRepo.ConvertToMemberRechargeRecordVO(ctx, memberRechargeRecord)
}

// ConvertToPayBill 转换为支付账单PO
func (s *MemberRechargeRecordServiceImpl) ConvertToMemberRechargeRecord(ctx context.Context, memberRechargeRecordVO vo.MemberRechargeRecordVO) po.MemberRechargeRecord {
	return s.memberRechargeRecordRepo.ConvertToMemberRechargeRecord(ctx, memberRechargeRecordVO)
}

// FindByPayId 根据支付ID查询会员充值账单
func (s *MemberRechargeRecordServiceImpl) FindByPayId(ctx context.Context, payId string) (po.MemberRechargeRecord, error) {
	return s.memberRechargeRecordRepo.FindByPayId(ctx, payId)
}

// SaveMemberRechargeRecordPayInfoCallbackByPayId 保存会员充值记录支付信息回调
func (s *MemberRechargeRecordServiceImpl) SaveMemberRechargeRecordPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.MemberPayCallbackVO) error {
	return s.memberRechargeRecordRepo.SaveMemberRechargeRecordPayInfoCallbackByPayId(ctx, callbackVO)
}

// V3CallPayCallback 调用支付回调
func (s *MemberRechargeRecordServiceImpl) V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error {
	return s.memberRechargeRecordRepo.V3CallPayCallback(ctx, reqDto)
}
