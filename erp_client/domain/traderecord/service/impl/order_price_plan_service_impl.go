package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// OrderPricePlanServiceImpl 订单价格方案服务实现
type OrderPricePlanServiceImpl struct {
	orderPricePlanRepo repository.OrderPricePlanRepository
}

// NewOrderPricePlanService 创建订单价格方案服务实例
func NewOrderPricePlanService(orderPricePlanRepo repository.OrderPricePlanRepository) *OrderPricePlanServiceImpl {
	return &OrderPricePlanServiceImpl{
		orderPricePlanRepo: orderPricePlanRepo,
	}
}

// FindAllOrderPricePlan 查询所有订单价格方案
func (s *OrderPricePlanServiceImpl) FindAllOrderPricePlan(ctx context.Context, venueId, sessionId string) (*[]po.OrderPricePlan, error) {
	return s.orderPricePlanRepo.FindAllOrderPricePlan(ctx, venueId, sessionId)
}

// FindOrderPricePlansByVenueIDAndSessionId 根据场所ID和场次ID查询订单价格计划
func (s *OrderPricePlanServiceImpl) FindOrderPricePlansByVenueIDAndSessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderPricePlan, error) {
	return s.orderPricePlanRepo.FindOrderPricePlansByVenueIDAndSessionId(ctx, venueId, sessionId)
}

// FindOrderPricePlansByVenueIDAndSessionIds 根据场所ID和场次ID查询订单价格计划
func (s *OrderPricePlanServiceImpl) FindOrderPricePlansByVenueIDAndSessionIds(ctx context.Context, venueId string, sessionIds []string) (*[]po.OrderPricePlan, error) {
	return s.orderPricePlanRepo.FindOrderPricePlansByVenueIDAndSessionIds(ctx, venueId, sessionIds)
}

// ConvertToPricePlanVO 转换为价格方案VO
func (s *OrderPricePlanServiceImpl) ConvertToPricePlanVO(ctx context.Context, orderPricePlan po.OrderPricePlan) vo.OrderPricePlanVO {
	return s.orderPricePlanRepo.ConvertToPricePlanVO(ctx, orderPricePlan)
}

// ConvertToPricePlan 转换为价格方案PO
func (s *OrderPricePlanServiceImpl) ConvertToPricePlan(ctx context.Context, orderPricePlanVO vo.OrderPricePlanVO) po.OrderPricePlan {
	return s.orderPricePlanRepo.ConvertToPricePlan(ctx, orderPricePlanVO)
}
