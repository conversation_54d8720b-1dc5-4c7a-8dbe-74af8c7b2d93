package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// PayRecordServiceImpl 支付记录服务实现
type PayRecordServiceImpl struct {
	payRecordRepo repository.PayRecordRepository
}

// NewPayRecordService 创建支付记录服务实例
func NewPayRecordService(payRecordRepo repository.PayRecordRepository) *PayRecordServiceImpl {
	return &PayRecordServiceImpl{
		payRecordRepo: payRecordRepo,
	}
}

// FindAllPayRecord 查询所有支付记录
func (s *PayRecordServiceImpl) FindAllPayRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayRecord, error) {
	return s.payRecordRepo.FindAllPayRecord(ctx, venueId, sessionId, statusList)
}

// ConvertToPayRecordVO 转换为支付记录VO
func (s *PayRecordServiceImpl) ConvertToPayRecordVO(ctx context.Context, payRecord po.PayRecord) vo.PayRecordVO {
	return s.payRecordRepo.ConvertToPayRecordVO(ctx, payRecord)
}

// ConvertToPayRecord 转换为支付记录PO
func (s *PayRecordServiceImpl) ConvertToPayRecord(ctx context.Context, payRecordVO vo.PayRecordVO) po.PayRecord {
	return s.payRecordRepo.ConvertToPayRecord(ctx, payRecordVO)
}

// FindsBySessionId 查询所有支付记录
func (s *PayRecordServiceImpl) FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayRecord, error) {
	return s.payRecordRepo.FindsBySessionId(ctx, venueId, sessionId)
}

// FindsByBillIds 查询所有支付记录
func (s *PayRecordServiceImpl) FindsByBillIds(ctx context.Context, venueId string, billIds []string) ([]po.PayRecord, error) {
	return s.payRecordRepo.FindsByBillIds(ctx, venueId, billIds)
}

// FindByPayId 查询支付记录
func (s *PayRecordServiceImpl) FindByPayId(ctx context.Context, payId string) (*po.PayRecord, error) {
	return s.payRecordRepo.FindByPayId(ctx, payId)
}
