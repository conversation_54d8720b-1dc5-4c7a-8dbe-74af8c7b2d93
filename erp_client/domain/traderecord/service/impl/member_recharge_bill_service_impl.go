package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	newModel "voderpltvv/model"
)

// MemberRechargeBillServiceImpl 会员充值账单服务实现
type MemberRechargeBillServiceImpl struct {
	memberRechargeBillRepo repository.MemberRechargeBillRepository
}

// NewPayBillService 创建支付账单服务实例
func NewMemberRechargeBillService(memberRechargeBillRepo repository.MemberRechargeBillRepository) *MemberRechargeBillServiceImpl {
	return &MemberRechargeBillServiceImpl{
		memberRechargeBillRepo: memberRechargeBillRepo,
	}
}

// FindAllMemberRechargeBill 查询所有会员充值账单
func (s *MemberRechargeBillServiceImpl) FindAllMemberRechargeBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberRechargeBill, error) {
	return s.memberRechargeBillRepo.FindAllMemberRechargeBill(ctx, venueId, sessionId, statusList)
}

// ConvertToPayBillVO 转换为支付账单VO
func (s *MemberRechargeBillServiceImpl) ConvertToMemberRechargeBillVO(ctx context.Context, memberRechargeBill po.MemberRechargeBill) vo.MemberRechargeBillVO {
	return s.memberRechargeBillRepo.ConvertToMemberRechargeBillVO(ctx, memberRechargeBill)
}

// ConvertToPayBill 转换为支付账单PO
func (s *MemberRechargeBillServiceImpl) ConvertToMemberRechargeBill(ctx context.Context, memberRechargeBillVO vo.MemberRechargeBillVO) po.MemberRechargeBill {
	return s.memberRechargeBillRepo.ConvertToMemberRechargeBill(ctx, memberRechargeBillVO)
}

// V3MemberRechage 会员充值
func (s *MemberRechargeBillServiceImpl) V3MemberRechage(ctx context.Context, reqDto req.V3QueryMemberRechargeReqDto) (req.MemberRechargeContext, error) {
	return s.memberRechargeBillRepo.V3MemberRechage(ctx, reqDto)
}

// V3MemberPayCallback 会员充值回调
func (s *MemberRechargeBillServiceImpl) V3MemberPayCallback(ctx context.Context, reqDto newModel.LeshuaPayCallbackModel) error {
	return s.memberRechargeBillRepo.V3MemberPayCallback(ctx, reqDto)
}

// V3MemberRefundCallback 会员退款回调
func (s *MemberRechargeBillServiceImpl) V3MemberRefundCallback(ctx context.Context, reqDto newModel.LeshuaRefundCallbackModel) error {
	return s.memberRechargeBillRepo.V3MemberRefundCallback(ctx, reqDto)
}

// FindByID 根据ID查询会员充值账单
func (s *MemberRechargeBillServiceImpl) FindByBillId(ctx context.Context, venueId, billId string) (po.MemberRechargeBill, error) {
	return s.memberRechargeBillRepo.FindByBillId(ctx, venueId, billId)
}

// V3RPCMemberCardQuery 会员信息查询
func (s *MemberRechargeBillServiceImpl) V3RPCMemberCardQuery(ctx context.Context, reqDto req.V3QueryMemberCardQueryReqDto) (vo.MemberCardVO, error) {
	return s.memberRechargeBillRepo.V3RPCMemberCardQuery(ctx, reqDto)
}

// V3RPCMemberCardVaildBalance 会员卡余额验证
func (s *MemberRechargeBillServiceImpl) V3RPCMemberCardVaildBalance(ctx context.Context, reqDto req.V3QueryMemberCardQueryBalanceReqDto) (vo.MemberCardVO, error) {
	return s.memberRechargeBillRepo.V3RPCMemberCardVaildBalance(ctx, reqDto)
}

// V3RPCMemberCardPay 会员卡消费
func (s *MemberRechargeBillServiceImpl) V3RPCMemberCardPay(ctx context.Context, reqDto req.V3RPCPayMoneyReqDto) (req.V3RPCPayMoneyRespDto, error) {
	return s.memberRechargeBillRepo.V3RPCMemberCardPay(ctx, reqDto)
}
