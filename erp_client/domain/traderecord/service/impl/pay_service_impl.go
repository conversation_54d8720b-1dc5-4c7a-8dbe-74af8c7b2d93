package impl

import (
	"context"
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_client/domain/traderecord/repository"
	orderAndPayRepo "voderpltvv/erp_client/domain/traderecord/repository"
	payBillRepo "voderpltvv/erp_client/domain/traderecord/repository"
	payRecordRepo "voderpltvv/erp_client/domain/traderecord/repository"
	orderAndPayService "voderpltvv/erp_client/domain/traderecord/service"
	payBillService "voderpltvv/erp_client/domain/traderecord/service"
	payRecordService "voderpltvv/erp_client/domain/traderecord/service"
	orderRoomPlanRepo "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/repository"
	orderProductRepo "voderpltvv/erp_client/domain/valueobject/business/orderproduct/repository"
	roomRepo "voderpltvv/erp_client/domain/valueobject/business/room/repository"
	"voderpltvv/erp_client/domain/valueobject/business/session/service"
	venueRepo "voderpltvv/erp_client/domain/valueobject/business/venue/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	managentVo "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"
)

// PayServiceImpl 支付服务实现
type PayServiceImpl struct {
	payRepo            repository.PayRepository
	sessionService     service.SessionService
	payRecordService   payRecordService.PayRecordService
	payBillService     payBillService.PayBillService
	orderAndPayService orderAndPayService.OrderAndPayService
	orderService       payRecordService.OrderService
	orderRoomPlanRepo  orderRoomPlanRepo.Repository
	orderProductRepo   orderProductRepo.Repository
	venueRepo          venueRepo.Repository
	roomRepo           roomRepo.Repository
	orderAndPayRepo    orderAndPayRepo.OrderAndPayRepository
	payRecordRepo      payRecordRepo.PayRecordRepository
	payBillRepo        payBillRepo.PayBillRepository
}

// NewPayService 创建支付服务实例
func NewPayService(payRepo repository.PayRepository, sessionService service.SessionService, payRecordService payRecordService.PayRecordService, payBillService payBillService.PayBillService, orderAndPayService orderAndPayService.OrderAndPayService, orderService payRecordService.OrderService, orderRoomPlanRepo orderRoomPlanRepo.Repository, orderProductRepo orderProductRepo.Repository, venueRepo venueRepo.Repository, roomRepo roomRepo.Repository, orderAndPayRepo orderAndPayRepo.OrderAndPayRepository, payRecordRepo payRecordRepo.PayRecordRepository, payBillRepo payBillRepo.PayBillRepository) *PayServiceImpl {
	return &PayServiceImpl{
		payRepo:            payRepo,
		sessionService:     sessionService,
		payRecordService:   payRecordService,
		payBillService:     payBillService,
		orderAndPayService: orderAndPayService,
		orderService:       orderService,
		orderRoomPlanRepo:  orderRoomPlanRepo,
		orderProductRepo:   orderProductRepo,
		venueRepo:          venueRepo,
		roomRepo:           roomRepo,
		orderAndPayRepo:    orderAndPayRepo,
		payRecordRepo:      payRecordRepo,
		payBillRepo:        payBillRepo,
	}
}

// GetPayOrderInfos 获取需支付订单信息
func (s *PayServiceImpl) GetPayOrderInfos(ctx context.Context, reqDto req.QueryOrderPayReqDto) (*[]vo.OrderVO, *[]po.Order, error) {
	// 1. 查询订单
	orders, err := s.payRepo.FindOrdersBySessionId(ctx, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return nil, nil, fmt.Errorf("find orders error: %v", err)
	}

	// 2. 转换为VO
	orderVOs := make([]vo.OrderVO, 0)
	for _, order := range *orders {
		if order.Status == nil {
			continue
		}
		if *order.Status == _const.ORDER_STATUS_PAYING {
			return nil, nil, fmt.Errorf("订单支付中，请稍后再试")
		} else if *order.Status == _const.ORDER_STATUS_REFUNDING {
			return nil, nil, fmt.Errorf("订单退款中，请稍后再试")
		} else if *order.Status == _const.ORDER_STATUS_UNPAID || *order.Status == _const.ORDER_STATUS_REFUNDED {
			orderVO := vo.OrderVO{
				OrderNo: *order.OrderNo,
				Status:  *order.Status,
			}
			orderVOs = append(orderVOs, orderVO)
		}
	}

	return &orderVOs, orders, nil
}

// GetPayOrderInfos 获取需支付订单信息
func (s *PayServiceImpl) GetPayOrderInfos_Bak(ctx context.Context, reqDto *req.QueryOrderPayReqDto) (*[]vo.OrderVO, *[]po.Order, error) {
	if reqDto == nil {
		return nil, nil, fmt.Errorf("reqDto is nil")
	}

	// 1. 查询订单
	orders, err := s.payRepo.FindOrdersBySessionId(ctx, *reqDto.VenueId, *reqDto.SessionId)
	if err != nil {
		return nil, nil, fmt.Errorf("find orders error: %v", err)
	}

	// 2. 转换为VO
	orderVOs := make([]vo.OrderVO, 0)
	for _, order := range *orders {
		if order.Status == nil {
			continue
		}
		if *order.Status == _const.ORDER_STATUS_PAYING {
			return nil, nil, fmt.Errorf("订单支付中，请稍后再试")
		} else if *order.Status == _const.ORDER_STATUS_REFUNDING {
			return nil, nil, fmt.Errorf("订单退款中，请稍后再试")
		} else if *order.Status == _const.ORDER_STATUS_UNPAID || *order.Status == _const.ORDER_STATUS_REFUNDED {
			orderVO := vo.OrderVO{
				OrderNo: *order.OrderNo,
				Status:  *order.Status,
			}
			orderVOs = append(orderVOs, orderVO)
		}
	}

	return &orderVOs, orders, nil
}

// GetPayOrderInfosWithRoomAndProductBySessionId 获取需支付订单的房间和商品信息
func (s *PayServiceImpl) GetPayOrderInfosWithRoomAndProductBySessionId(ctx context.Context, venueId string, sessionId string, orderVOs []vo.OrderVO) (*[]po.OrderRoomPlan, *[]po.OrderProduct) {
	// 1. 获取订单号列表
	orderNos := make([]string, 0)
	for _, v := range orderVOs {
		orderNos = append(orderNos, v.OrderNo)
	}

	// 2. 查询订单房间计划和商品
	orderRoomPlans, _ := s.payRepo.FindOrderRoomPlansBySessionId(ctx, venueId, sessionId)
	orderProducts, _ := s.payRepo.FindOrderProductsBySessionId(ctx, venueId, sessionId)

	return orderRoomPlans, orderProducts
}

// ValidatePayDiscountIsValid 验证支付折扣是否合法
func (s *PayServiceImpl) ValidatePayDiscountIsValid(ctx context.Context, reqDto *req.QueryOrderPayReqDto, orderRoomPlans *[]po.OrderRoomPlan, orderProducts *[]po.OrderProduct) error {
	if reqDto == nil {
		return fmt.Errorf("reqDto is nil")
	}

	// // 1. 验证房费折扣
	// if reqDto.DiscountRoomRate != nil && *reqDto.DiscountRoomRate > 0 {
	// 	for _, roomPlan := range *orderRoomPlans {
	// 		if roomPlan.DiscountRate != nil && *roomPlan.DiscountRate > 0 {
	// 			return fmt.Errorf("房费不能重复打折")
	// 		}
	// 	}
	// }

	// // 2. 验证商品折扣
	// if reqDto.DiscountProductRate != nil && *reqDto.DiscountProductRate > 0 {
	// 	for _, product := range *orderProducts {
	// 		if product.DiscountRate != nil && *product.DiscountRate > 0 {
	// 			return fmt.Errorf("商品不能重复打折")
	// 		}
	// 	}
	// }

	// 3. 验证免单金额
	if reqDto.FreeAmount != nil && *reqDto.FreeAmount > 0 {
		if *reqDto.FreeAmount > *reqDto.PayAmount {
			return fmt.Errorf("免单金额不能大于支付金额")
		}
		if reqDto.DiscountRoomRate != nil && *reqDto.DiscountRoomRate > 0 {
			return fmt.Errorf("免单和房费折扣不能同时使用")
		}
		if reqDto.DiscountProductRate != nil && *reqDto.DiscountProductRate > 0 {
			return fmt.Errorf("免单和商品折扣不能同时使用")
		}
	}

	return nil
}

// Calc2UpdateOrdersAndRoomPlansAndProducts 计算更新订单、房间计划和商品
func (s *PayServiceImpl) Calc2UpdateOrdersAndRoomPlansAndProducts(ctx context.Context, reqDto *req.QueryOrderPayReqDto,
	toPayOrderVOs *[]vo.OrderVO, toPayOrders *[]po.Order,
	orderRoomPlans *[]po.OrderRoomPlan, orderProducts *[]po.OrderProduct) (*[]po.Order, *[]po.OrderRoomPlan, *[]po.OrderProduct, int64, int64, error) {

	totalDiscountRoomAmount := int64(0)
	totalDiscountProductAmount := int64(0)

	// 1. 计算房费折扣
	toUpdateOrderRoomPlans := &[]po.OrderRoomPlan{}
	if reqDto.DiscountRoomRate != nil && *reqDto.DiscountRoomRate > 0 {
		for _, roomPlan := range *orderRoomPlans {
			discountAmount := *roomPlan.PayAmount * *reqDto.DiscountRoomRate / 100
			payAmountAfterDiscount := *roomPlan.PayAmount - discountAmount

			updatedRoomPlan := po.OrderRoomPlan{
				Id:        roomPlan.Id,
				OrderNo:   roomPlan.OrderNo,
				PayAmount: &payAmountAfterDiscount,
			}
			*toUpdateOrderRoomPlans = append(*toUpdateOrderRoomPlans, updatedRoomPlan)
			totalDiscountRoomAmount += discountAmount
		}
	}

	// 2. 计算商品折扣
	toUpdateOrderProducts := &[]po.OrderProduct{}
	if reqDto.DiscountProductRate != nil && *reqDto.DiscountProductRate > 0 {
		for _, product := range *orderProducts {
			discountAmount := *product.PayAmount * *reqDto.DiscountProductRate / 100
			payAmountAfterDiscount := *product.PayAmount - discountAmount

			updatedProduct := po.OrderProduct{
				Id:        product.Id,
				OrderNo:   product.OrderNo,
				PayAmount: &payAmountAfterDiscount,
			}
			*toUpdateOrderProducts = append(*toUpdateOrderProducts, updatedProduct)
			totalDiscountProductAmount += discountAmount
		}
	}

	// 3. 更新订单金额
	toUpdateOrders := &[]po.Order{}
	for _, order := range *toPayOrders {
		var newPayAmount int64
		for _, roomPlan := range *toUpdateOrderRoomPlans {
			if *roomPlan.OrderNo == *order.OrderNo {
				newPayAmount = *roomPlan.PayAmount
				break
			}
		}
		for _, product := range *toUpdateOrderProducts {
			if *product.OrderNo == *order.OrderNo {
				newPayAmount = *product.PayAmount
				break
			}
		}
		if newPayAmount > 0 {
			updatedOrder := po.Order{
				Id:      order.Id,
				OrderNo: order.OrderNo,
			}
			*toUpdateOrders = append(*toUpdateOrders, updatedOrder)
		}
	}

	return toUpdateOrders, toUpdateOrderRoomPlans, toUpdateOrderProducts, totalDiscountRoomAmount, totalDiscountProductAmount, nil
}

// BuildPayDataForPay 构建支付数据
func (s *PayServiceImpl) BuildPayDataForPay(ctx context.Context, reqDto req.QueryOrderPayReqDto,
	toUpdateOrders []po.Order, session po.Session,
	totalDiscountRoomAmount int64, totalDiscountProductAmount int64) (*po.PayBill, *[]po.OrderAndPay, error) {

	// 1. 生成支付ID
	billId := util.GetBillId(*reqDto.VenueId)

	// 2. 构建支付账单
	toAddPayBill := po.PayBill{
		VenueId:      reqDto.VenueId,
		RoomId:       session.RoomId,
		SessionId:    reqDto.SessionId,
		BillId:       &billId,
		TotalFee:     reqDto.PayAmount,
		CreditAmount: reqDto.CreditAmount,
		ChangeAmount: reqDto.ChangeAmount,
		Status:       util.GetItPtr(_const.PAY_STATUS_UNPAID),
		EmployeeId:   reqDto.EmployeeId,
	}

	// 3. 构建订单支付关系
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	for _, order := range toUpdateOrders {
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   order.OrderNo,
			SessionId: reqDto.SessionId,
			BillId:    &billId,
		})
	}

	return &toAddPayBill, &toAddOrderAndPays, nil
}

// BuildPayDataForPay 构建支付数据
func (s *PayServiceImpl) BuildPayDataForPay_Bak(ctx context.Context, reqDto *req.QueryOrderPayReqDto,
	toUpdateOrders *[]po.Order, session *po.Session,
	totalDiscountRoomAmount int64, totalDiscountProductAmount int64) (*po.PayBill, *[]po.OrderAndPay, error) {

	// 1. 生成支付ID
	billId := util.GetBillId(*reqDto.VenueId)

	// 2. 构建支付账单
	toAddPayBill := po.PayBill{
		VenueId:      reqDto.VenueId,
		RoomId:       session.RoomId,
		SessionId:    reqDto.SessionId,
		BillId:       &billId,
		TotalFee:     reqDto.PayAmount,
		CreditAmount: reqDto.CreditAmount,
		ChangeAmount: reqDto.ChangeAmount,
		Status:       util.GetItPtr(_const.PAY_STATUS_UNPAID),
		EmployeeId:   reqDto.EmployeeId,
	}

	// 3. 构建订单支付关系
	toAddOrderAndPays := make([]po.OrderAndPay, 0)
	for _, order := range *toUpdateOrders {
		toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
			OrderNo:   order.OrderNo,
			SessionId: reqDto.SessionId,
			BillId:    &billId,
		})
	}

	return &toAddPayBill, &toAddOrderAndPays, nil
}

// SaveBatchTxForPay 批量保存支付数据
func (s *PayServiceImpl) SaveBatchTxForPay(ctx context.Context, toPayBill *po.PayBill,
	toAddOrderAndPays *[]po.OrderAndPay, toUpdateOrders *[]po.Order,
	toUpdateOrderRoomPlans *[]po.OrderRoomPlan, toUpdateOrderProducts *[]po.OrderProduct) error {
	return s.payRepo.SaveBatchTxForPay(ctx, toPayBill, toAddOrderAndPays, toUpdateOrders, toUpdateOrderRoomPlans, toUpdateOrderProducts)
}

// TransformPayGate 转换支付网关
func (s *PayServiceImpl) TransformPayGate(ctx context.Context, reqDto *req.QueryOrderPayReqDto, toPayBill *po.PayBill) (*vo.PayResultVO, error) {
	return &vo.PayResultVO{
		PayId:   *toPayBill.BillId,
		ErrMsg:  nil,
		ErrCode: nil,
	}, nil
}

// V3TransformPayGate 转换支付网关
func (s *PayServiceImpl) V3TransformPayGate(ctx context.Context, reqDto *req.V3QueryOrderPayTransformReqDto, toPayBill *po.PayBill) ([]vo.PayResultVO, error) {
	return s.payRepo.V3TransformPayGate(ctx, reqDto, toPayBill)
}

// AfterPayCallbackCoUpdateInfoByPayId 支付回调后更新信息
func (s *PayServiceImpl) AfterPayCallbackCoUpdateInfoByPayId(ctx context.Context, payId string) error {
	// 1. 查询支付账单
	payBill, err := s.payRepo.FindPayBillById(ctx, payId)
	if err != nil {
		return fmt.Errorf("find pay bill error: %v", err)
	}
	if payBill == nil {
		return fmt.Errorf("pay bill not found")
	}

	// 2. 更新支付状态
	*payBill.Status = _const.PAY_STATUS_PAID
	return s.payRepo.SavePayBill(ctx, payBill)
}

// QueryPaidOrderVOsBySessionId 查询已支付订单
func (s *PayServiceImpl) QueryPaidOrderVOsBySessionId(ctx context.Context, venueId, sessionId string) (int64, error) {
	hasPaidAmount, err := s.payRepo.QueryPaidOrderVOsBySessionId(ctx, venueId, sessionId)
	if err != nil {
		return 0, fmt.Errorf("find orders error: %v", err)
	}
	return hasPaidAmount, nil
}

// CalculateManyFee 计算多个费用
func (s *PayServiceImpl) CalculateManyFee(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	return s.payRepo.CalculateManyFee(ctx, totalFeeThis, opsNotInDB, omsNotInDB, sessionId, venueId, lastMinimumCharge)
}

// CalculateManyFeeForAdditionalOrder 计算多个费用-新增订单
func (s *PayServiceImpl) CalculateManyFeeForAdditionalOrder(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	return s.payRepo.CalculateManyFeeForAdditionalOrder(ctx, totalFeeThis, opsNotInDB, omsNotInDB, sessionId, venueId, lastMinimumCharge)
}

// CalculateManyFeeForPay 计算多个费用-支付-后付
func (s *PayServiceImpl) CalculateManyFeeForPay(ctx context.Context, totalFeeThis int64, opsInDB []vo.OrderProductVO, omsInDB []vo.OrderRoomPlanVO, orderVOs []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	return s.payRepo.CalculateManyFeeForPay(ctx, totalFeeThis, opsInDB, omsInDB, orderVOs, sessionId, venueId, lastMinimumCharge)
}

// CalculateManyFeeForTransferRoom 计算多个费用-支付-后付
func (s *PayServiceImpl) CalculateManyFeeForTransferRoom(ctx context.Context, orderVOMarkDelete []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error) {
	return s.payRepo.CalculateManyFeeForTransferRoom(ctx, orderVOMarkDelete, sessionId, venueId, lastMinimumCharge)
}

// GetOrdersInfoByOrderNos 获取订单信息
func (s *PayServiceImpl) GetOrdersInfoByOrderNos(ctx context.Context, orderNos []string, venueId string, sessionId string) ([]vo.OrderVO, error) {
	return s.payRepo.GetOrdersInfoByOrderNos(ctx, orderNos, venueId, sessionId)
}

// SaveOrderPayInfoCallbackByPayId 保存支付信息回调
func (s *PayServiceImpl) SaveOrderPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.OrderPayCallbackVO) error {
	return s.payRepo.SaveOrderPayInfoCallbackByPayId(ctx, callbackVO)
}

// SaveOrderPayInfoCallbackByBillIdForFree 保存支付信息回调
func (s *PayServiceImpl) SaveOrderPayInfoCallbackByBillIdForFree(ctx context.Context, billId string) error {
	return s.payRepo.SaveOrderPayInfoCallbackByBillIdForFree(ctx, billId)
}

// V3RefundByCash 按现金退款
func (s *PayServiceImpl) V3RefundByCash(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []managentVo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	return s.payRepo.V3RefundByCash(ctx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
}

// V3RefundByCash 按原路返回退款
func (s *PayServiceImpl) V3RefundByBack(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []managentVo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	return s.payRepo.V3RefundByBack(ctx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
}

// RefundAllOrderBySessionId
func (s *PayServiceImpl) RefundAllOrderBySessionId(ctx context.Context, sessionId string, venueId string) (managentVo.OrderPOInfoUnionVO, error) {
	return s.payRepo.RefundAllOrderBySessionId(ctx, sessionId, venueId)
}

// GetOrderPOInfoBySessionId 查询session下的所有订单相关PO数据
func (s *PayServiceImpl) GetOrderPOInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	return s.payRepo.GetOrderPOInfoBySessionId(ctx, sessionId, venueId)
}

// GetPayInfoBySessionId 查询session下的所有支付相关PO数据
func (s *PayServiceImpl) GetPayInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	return s.payRepo.GetPayInfoBySessionId(ctx, sessionId, venueId)
}

// GetPayInfoBillBackBySessionId 查询session下的所有支付相关PO数据
func (s *PayServiceImpl) GetPayInfoBillBackBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO {
	return s.payRepo.GetPayInfoBillBackBySessionId(ctx, sessionId, venueId)
}

// GetNewSwapInfo 获取新换房信息
func (s *PayServiceImpl) GetNewSwapInfo(ctx context.Context, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, session po.Session, room po.Room, sessionIdB string, roomIdB string) (vo.OrderPOInfoUnionVO, po.Session, po.Room) {
	// 1. 创建新的PO集合
	newRoom := po.Room{
		Id:        room.Id,
		SessionId: &sessionIdB,
	}

	newSession := po.Session{
		Id:     session.Id,
		RoomId: &roomIdB,
	}
	newOrders := make([]po.Order, 0)
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	newPayBills := make([]po.PayBill, 0)
	newPayRecords := make([]po.PayRecord, 0)

	for _, order := range orderPOInfoUnionVO.Orders {
		newOrders = append(newOrders, po.Order{
			Id:     order.Id,
			RoomId: &roomIdB,
		})
	}
	for _, product := range orderPOInfoUnionVO.OrderProducts {
		newOrderProducts = append(newOrderProducts, po.OrderProduct{
			Id:     product.Id,
			RoomId: &roomIdB,
		})
	}
	for _, roomPlan := range orderPOInfoUnionVO.OrderRoomPlans {
		newOrderRoomPlans = append(newOrderRoomPlans, po.OrderRoomPlan{
			Id:     roomPlan.Id,
			RoomId: &roomIdB,
		})
	}
	for _, payBill := range orderPOInfoUnionVO.PayBills {
		newPayBills = append(newPayBills, po.PayBill{
			Id:     payBill.Id,
			RoomId: &roomIdB,
		})
	}
	for _, payRecord := range orderPOInfoUnionVO.PayRecords {
		newPayRecords = append(newPayRecords, po.PayRecord{
			Id:     payRecord.Id,
			RoomId: &roomIdB,
		})
	}

	return vo.OrderPOInfoUnionVO{
		Orders:         newOrders,
		OrderRoomPlans: newOrderRoomPlans,
		OrderProducts:  newOrderProducts,
		PayBills:       newPayBills,
		PayRecords:     newPayRecords,
	}, newSession, newRoom
}

// GetNewMergeInfo 保存订单信息并房
func (s *PayServiceImpl) GetNewMergeInfo(ctx context.Context, newOrderPOInfoUnionVOA managentVo.OrderPOInfoUnionVO, sessionIdB string) managentVo.OrderPOInfoUnionVO {
	newOrders := make([]po.Order, 0)
	newOrderProducts := make([]po.OrderProduct, 0)
	newOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	newOrderAndPays := make([]po.OrderAndPay, 0)
	newPayBills := make([]po.PayBill, 0)
	newPayRecords := make([]po.PayRecord, 0)

	for _, order := range newOrderPOInfoUnionVOA.Orders {
		newOrders = append(newOrders, po.Order{
			Id:        order.Id,
			SessionId: &sessionIdB,
		})
	}
	for _, orderProduct := range newOrderPOInfoUnionVOA.OrderProducts {
		newOrderProducts = append(newOrderProducts, po.OrderProduct{
			Id:        orderProduct.Id,
			SessionId: &sessionIdB,
		})
	}
	for _, orderRoomPlan := range newOrderPOInfoUnionVOA.OrderRoomPlans {
		newOrderRoomPlans = append(newOrderRoomPlans, po.OrderRoomPlan{
			Id:        orderRoomPlan.Id,
			SessionId: &sessionIdB,
		})
	}
	for _, orderAndPay := range newOrderPOInfoUnionVOA.OrderAndPays {
		newOrderAndPays = append(newOrderAndPays, po.OrderAndPay{
			Id:        orderAndPay.Id,
			SessionId: &sessionIdB,
		})
	}
	for _, payBill := range newOrderPOInfoUnionVOA.PayBills {
		newPayBills = append(newPayBills, po.PayBill{
			Id:        payBill.Id,
			SessionId: &sessionIdB,
		})
	}
	for _, payRecord := range newOrderPOInfoUnionVOA.PayRecords {
		newPayRecords = append(newPayRecords, po.PayRecord{
			Id:        payRecord.Id,
			SessionId: &sessionIdB,
		})
	}
	return managentVo.OrderPOInfoUnionVO{
		Orders:         newOrders,
		OrderProducts:  newOrderProducts,
		OrderRoomPlans: newOrderRoomPlans,
		OrderAndPays:   newOrderAndPays,
		PayBills:       newPayBills,
		PayRecords:     newPayRecords,
	}
}

// BuildOrderVOInfoMergedVOs 构造mergevo
func (s *PayServiceImpl) BuildOrderVOInfoMergedVOs(ctx context.Context, oisVO vo.OrderPOInfoUnionVO) []vo.OrderVOInfoMergedVO {
	return s.payRepo.BuildOrderVOInfoMergedVOs(ctx, oisVO)
}

// 获取修改roomid的对象
func (s *PayServiceImpl) GetModifyRoomIdOrderInfo(ctx context.Context, oisVO vo.OrderPOInfoUnionVO, newRoomId string) vo.OrderPOInfoUnionVO {
	// 创建新的PO集合
	newOrders := make([]po.Order, 0)
	newRoomPlans := make([]po.OrderRoomPlan, 0)
	newProducts := make([]po.OrderProduct, 0)
	newOrderAndPays := make([]po.OrderAndPay, 0)
	newPayBills := make([]po.PayBill, 0)
	newPayRecords := make([]po.PayRecord, 0)

	// 更新订单的RoomId
	for _, o := range oisVO.Orders {
		newOrders = append(newOrders, po.Order{
			Id:     o.Id,
			RoomId: &newRoomId, // 使用指针赋值
		})
	}

	// 更新房费计划的RoomId
	for _, rp := range oisVO.OrderRoomPlans {
		newRoomPlans = append(newRoomPlans, po.OrderRoomPlan{
			Id:     rp.Id,
			RoomId: &newRoomId, // 使用指针赋值
		})
	}

	// 更新商品记录的RoomId
	for _, p := range oisVO.OrderProducts {
		newProducts = append(newProducts, po.OrderProduct{
			Id:     p.Id,
			RoomId: &newRoomId, // 使用指针赋值
		})
	}

	// 更新支付账单的RoomId
	for _, pb := range oisVO.PayBills {
		newPayBills = append(newPayBills, po.PayBill{
			Id:     pb.Id,
			RoomId: &newRoomId, // 使用指针赋值
		})
	}

	// 更新支付记录的RoomId
	for _, pr := range oisVO.PayRecords {
		newPayRecords = append(newPayRecords, po.PayRecord{
			Id:     pr.Id,
			RoomId: &newRoomId, // 使用指针赋值
		})
	}

	return vo.OrderPOInfoUnionVO{
		Orders:         newOrders,
		OrderRoomPlans: newRoomPlans,
		OrderProducts:  newProducts,
		OrderAndPays:   newOrderAndPays,
		PayBills:       newPayBills,
		PayRecords:     newPayRecords,
	}
}

// UpdateSessionInfoUnpaid 更新session信息-未支付
func (s *PayServiceImpl) UpdateSessionInfoUnpaid(ctx context.Context, sessionId string, venueId string) error {
	return s.payRepo.UpdateSessionInfoUnpaid(ctx, sessionId, venueId)
}

// BuildPayBillVOInfoBackVO 构建backvo,计算每个bill最终通过哪种方式退款多少钱
func (s *PayServiceImpl) BuildPayBillVOInfoBackVO(ctx context.Context, payBills []po.PayBill, payRecords []po.PayRecord, orderAndPays []po.OrderAndPay) []vo.PayBillVOInfoBackVO {
	return s.payRepo.BuildPayBillVOInfoBackVO(ctx, payBills, payRecords, orderAndPays)
}

// BuildNewPayBillVOInfoBackVO 构建新还原账单
func (s *PayServiceImpl) BuildNewPayBillVOInfoBackVO(ctx context.Context, venueId string, sessionId string, payBillVOInfoBackVOs []vo.PayBillVOInfoBackVO) []vo.PayBillPOInfoBackVO {
	rtPOs := make([]vo.PayBillPOInfoBackVO, 0)

	// 构建新的orderAndPays
	for _, payBillVOInfoBackVO := range payBillVOInfoBackVOs {
		payBillVOTmp := payBillVOInfoBackVO.PayBillVO
		orderAndPayVOsTmp := payBillVOInfoBackVO.NormalOrderAndPayVOs
		mergedPayRecordVOsTmp := payBillVOInfoBackVO.MergedPayRecordVOs
		newOrderAndPays := make([]po.OrderAndPay, 0)
		billId := util.GetBillId(venueId)
		for _, orderAndPayVO := range orderAndPayVOsTmp {
			newOrderAndPays = append(newOrderAndPays, po.OrderAndPay{
				OrderNo:   &orderAndPayVO.OrderNo,
				BillId:    &billId,
				SessionId: &sessionId,
			})
		}
		billTotalFee := int64(0)
		now := int64(util.TimeNowUnix())
		newPayRecords := make([]po.PayRecord, 0)
		for _, mergedPayRecordVO := range mergedPayRecordVOsTmp {
			payRecordTmp := s.payRecordService.ConvertToPayRecord(ctx, mergedPayRecordVO)
			payId := util.GetPayId(venueId)
			totalFee := *payRecordTmp.TotalFee - mergedPayRecordVO.RefundAmount
			if totalFee <= 0 {
				continue
			}
			billTotalFee += totalFee
			newPayRecords = append(newPayRecords, po.PayRecord{
				VenueId:    payRecordTmp.VenueId,
				RoomId:     payRecordTmp.RoomId,
				EmployeeId: payRecordTmp.EmployeeId,
				SessionId:  payRecordTmp.SessionId,
				BillId:     &billId,
				PayId:      &payId,
				PayPid:     payRecordTmp.PayId,
				TotalFee:   &totalFee,
				Status:     util.Ptr(string(_const.V2_PAY_BILL_STATUS_WAIT)),
				PayType:    payRecordTmp.PayType,
				FinishTime: &now,
				BillDate:   &now,
			})
		}
		newPayBill := po.PayBill{
			VenueId:    &payBillVOTmp.VenueId,
			RoomId:     &payBillVOTmp.RoomId,
			EmployeeId: &payBillVOTmp.EmployeeId,
			SessionId:  &payBillVOTmp.SessionId,
			BillId:     &billId,
			BillPid:    &payBillVOTmp.BillId,
			TotalFee:   &billTotalFee,
			Direction:  util.Ptr(string(_const.V2_PAY_BILL_DIRECTION_REFUND)),
			Status:     util.Ptr(string(_const.V2_PAY_BILL_STATUS_WAIT)),
			FinishTime: &now,
			BillDate:   &now,
		}
		rtPOs = append(rtPOs, vo.PayBillPOInfoBackVO{
			PayBillPO:      newPayBill,
			PayRecordPOs:   newPayRecords,
			OrderAndPayPOs: newOrderAndPays,
		})
	}

	return rtPOs
}

// SavePayBillBack 保存还原账单
func (s *PayServiceImpl) SavePayBillBack(ctx context.Context, payBillPOInfoBackVOs []vo.PayBillPOInfoBackVO) error {
	return s.payRepo.SavePayBillBack(ctx, payBillPOInfoBackVOs)
}

// V3BillBack 还原账单
func (s *PayServiceImpl) V3BillBack(ctx context.Context, reqDto req.V3BillBackReqDto, newPayBillVOInfoBackVOs []vo.PayBillPOInfoBackVO) error {
	return s.payRepo.V3BillBack(ctx, reqDto, newPayBillVOInfoBackVOs)
}

// BuildPayBillVOsWithRecord 构建订单扩展费用VO
func (s *PayServiceImpl) BuildPayBillVOsWithRecord(ctx context.Context, payBills []po.PayBill, payRecords []po.PayRecord) []vo.PayBillVO {
	payBillVOs := make([]vo.PayBillVO, 0)
	for _, payBill := range payBills {
		payBillVO := s.payBillService.ConvertToPayBillVO(ctx, payBill)
		for _, payRecord := range payRecords {
			if *payRecord.BillId == *payBill.BillId {
				payBillVO.PayRecordVOs = append(payBillVO.PayRecordVOs, s.payRecordService.ConvertToPayRecordVO(ctx, payRecord))
			}
		}
		payBillVOs = append(payBillVOs, payBillVO)
	}
	return payBillVOs
}

// V3CallPayCallback 乐刷支付回调
func (s *PayServiceImpl) V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error {
	return s.payRepo.V3CallPayCallback(ctx, reqDto)
}

// V3CallRefundCallback 乐刷退款回调
func (s *PayServiceImpl) V3CallRefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error {
	return s.payRepo.V3CallRefundCallback(ctx, reqDto)
}

// TransferLeshuaRefund 乐刷退款
func (s *PayServiceImpl) TransferLeshuaRefund(ctx context.Context, payRecordVO vo.PayRecordVO) error {
	return s.payRepo.TransferLeshuaRefund(ctx, payRecordVO)
}

// StartLeshuaTimer 启动乐刷支付定时器
func (s *PayServiceImpl) StartLeshuaTimer(ctx context.Context, key string) error {
	return s.payRepo.StartLeshuaTimer(ctx, key)
}

// ParseToPayBillInfoSrcVOs 转换为 []PayBillInfoSrcVO
// 将 OrderPOInfoUnionVO 转换为 PayBillInfoSrcVO 列表。
// PayBillInfoSrcVO 包含一个 PayBillVO 及其关联的 PayRecordVOs, OrderAndPayVOs, OrderVOs, OrderRoomPlanVOs, OrderProductVOs。
// 关联基于 PayBill.BillId 和 OrderAndPay.OrderNo。
func (s *PayServiceImpl) ParseToPayBillInfoSrcVOs(ctx context.Context, payInfoPOs vo.OrderPOInfoUnionVO, billIds []string) []vo.PayBillInfoSrcVO {
	payBillInfoSrcVOs := make([]vo.PayBillInfoSrcVO, 0, len(payInfoPOs.PayBills)) // Pre-allocate slice capacity

	// --- 创建各种映射以提高查找效率 ---
	payRecordsMap := make(map[string][]po.PayRecord)
	for _, record := range payInfoPOs.PayRecords {
		if record.BillId != nil {
			billId := *record.BillId
			payRecordsMap[billId] = append(payRecordsMap[billId], record)
		}
	}

	orderAndPaysMap := make(map[string][]po.OrderAndPay)
	for _, oap := range payInfoPOs.OrderAndPays {
		if oap.BillId != nil {
			billId := *oap.BillId
			orderAndPaysMap[billId] = append(orderAndPaysMap[billId], oap)
		}
	}

	ordersMap := make(map[string]po.Order)
	for _, order := range payInfoPOs.Orders {
		if order.OrderNo != nil {
			ordersMap[*order.OrderNo] = order
		}
	}

	orderRoomPlansMap := make(map[string][]po.OrderRoomPlan)
	for _, rp := range payInfoPOs.OrderRoomPlans {
		if rp.OrderNo != nil {
			orderNo := *rp.OrderNo
			orderRoomPlansMap[orderNo] = append(orderRoomPlansMap[orderNo], rp)
		}
	}

	orderProductsMap := make(map[string][]po.OrderProduct)
	for _, p := range payInfoPOs.OrderProducts {
		if p.OrderNo != nil {
			orderNo := *p.OrderNo
			orderProductsMap[orderNo] = append(orderProductsMap[orderNo], p)
		}
	}
	// --- 映射创建结束 ---

	// 遍历 PayBills，构建 PayBillInfoSrcVO
	for _, payBillPO := range payInfoPOs.PayBills {
		if payBillPO.BillId == nil {
			continue // Skip if BillId is nil
		}
		billId := *payBillPO.BillId
		if len(billIds) > 0 && !util.InList(billId, billIds) { // Check billIds only if it's not empty
			continue
		}

		// 转换 PayBill PO 到 VO
		payBillVO := s.payBillService.ConvertToPayBillVO(ctx, payBillPO)

		// 查找并转换关联的 PayRecords
		relatedPayRecordPOs := payRecordsMap[billId]
		payRecordVOs := make([]vo.PayRecordVO, 0, len(relatedPayRecordPOs))
		for _, recordPO := range relatedPayRecordPOs {
			payRecordVOs = append(payRecordVOs, s.payRecordService.ConvertToPayRecordVO(ctx, recordPO))
		}

		// 查找关联的 OrderAndPays 并收集去重后的 OrderNo
		relatedOrderAndPayPOs := orderAndPaysMap[billId]
		orderAndPayVOs := make([]vo.OrderAndPayVO, 0, len(relatedOrderAndPayPOs))
		relatedOrderNos := make([]string, 0, len(relatedOrderAndPayPOs))
		for _, oapPO := range relatedOrderAndPayPOs {
			orderAndPayVOs = append(orderAndPayVOs, s.orderAndPayService.ConvertToOrderAndPayVO(ctx, oapPO))
			if oapPO.OrderNo != nil {
				util.AddListElement(&relatedOrderNos, *oapPO.OrderNo)
			}
		}

		// --- 查找并转换关联的 Orders, OrderRoomPlans, OrderProducts ---
		orderVOs := make([]vo.OrderVO, 0, len(relatedOrderNos))
		orderRoomPlanVOs := make([]vo.OrderRoomPlanVO, 0) // Initialize slice for room plans
		orderProductVOs := make([]vo.OrderProductVO, 0)   // Initialize slice for products

		for _, orderNo := range relatedOrderNos {
			// Orders
			if orderPO, found := ordersMap[orderNo]; found {
				// 假设 s.orderService 提供了 ConvertToOrderVO 方法
				orderVOs = append(orderVOs, s.orderService.ConvertToOrderVO(ctx, orderPO))
			}

			// OrderRoomPlans
			if roomPlanPOs, found := orderRoomPlansMap[orderNo]; found {
				for _, rpPO := range roomPlanPOs {
					// 假设 s.orderService (或其他服务) 提供了 ConvertToOrderRoomPlanVO 方法
					orderRoomPlanVOs = append(orderRoomPlanVOs, s.orderRoomPlanRepo.ConvertToOrderRoomPlanVO(ctx, rpPO))
				}
			}

			// OrderProducts
			if productPOs, found := orderProductsMap[orderNo]; found {
				for _, pPO := range productPOs {
					// 假设 s.orderService (或其他服务) 提供了 ConvertToOrderProductVO 方法
					orderProductVOs = append(orderProductVOs, s.orderProductRepo.ConvertToOrderProductVO(ctx, pPO))
				}
			}
		}
		// --- 转换结束 ---

		// 组装 PayBillInfoSrcVO
		payBillInfoSrcVO := vo.PayBillInfoSrcVO{
			PayBillVO:        payBillVO,
			PayRecordVOs:     payRecordVOs,
			OrderAndPayVOs:   orderAndPayVOs,
			OrderVOs:         orderVOs,         // 填充 OrderVOs
			OrderRoomPlanVOs: orderRoomPlanVOs, // 填充 OrderRoomPlanVOs
			OrderProductVOs:  orderProductVOs,  // 填充 OrderProductVOs
		}
		payBillInfoSrcVOs = append(payBillInfoSrcVOs, payBillInfoSrcVO)
	}

	return payBillInfoSrcVOs
}

// ParseToPayBillInfoNomalVOs 将 []PayBillInfoSrcVO 转换为 []PayBillInfoNomalVO
// 这个方法将退款类型的 PayBillInfoSrcVO 归类到对应的原始支付 PayBillInfoSrcVO 下，
// 形成 PayBillInfoNomalVO 结构。
func (s *PayServiceImpl) ParseToPayBillInfoNomalVOs(ctx context.Context, payBillInfoSrcVOs []vo.PayBillInfoSrcVO) []vo.PayBillInfoNomalVO {
	// 1. 分离正常支付单和退款单
	normalBills := make([]vo.PayBillInfoSrcVO, 0)
	refundBillsMap := make(map[string][]vo.PayBillInfoSrcVO) // Key: BillPid (Original BillId), Value: Refund Bill Src VOs

	for _, srcVO := range payBillInfoSrcVOs {
		if srcVO.PayBillVO.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND { // 假设 "refund" 是退款方向常量
			if srcVO.PayBillVO.BillPid != "" {
				billPid := srcVO.PayBillVO.BillPid
				refundBillsMap[billPid] = append(refundBillsMap[billPid], srcVO)
			}
			// 可以考虑日志记录 BillPid 为空的退款单
		} else {
			normalBills = append(normalBills, srcVO)
		}
	}

	// 2. 构建 PayBillInfoNomalVO 列表
	payBillInfoNomalVOs := make([]vo.PayBillInfoNomalVO, 0, len(normalBills)) // Pre-allocate capacity

	for _, normalBill := range normalBills {
		nomalVO := vo.PayBillInfoNomalVO{
			// 复制正常支付单的基础信息
			PayBillVO:        normalBill.PayBillVO,
			PayRecordVOs:     normalBill.PayRecordVOs,
			OrderAndPayVOs:   normalBill.OrderAndPayVOs,
			OrderVOs:         normalBill.OrderVOs, // <-- 添加此行，复制 OrderVOs
			OrderRoomPlanVOs: normalBill.OrderRoomPlanVOs,
			OrderProductVOs:  normalBill.OrderProductVOs,
		}

		// 查找并附加关联的退款单信息
		if relatedRefunds, found := refundBillsMap[normalBill.PayBillVO.BillId]; found {
			nomalVO.PayBillRefundVOs = relatedRefunds
		} else {
			nomalVO.PayBillRefundVOs = make([]vo.PayBillInfoSrcVO, 0) // 确保字段不为 nil
		}

		payBillInfoNomalVOs = append(payBillInfoNomalVOs, nomalVO)
	}

	return payBillInfoNomalVOs
}

// BuildPayBillInfoBillBackVOs 转换为 []PayBillInfoBillBackVO
func (s *PayServiceImpl) BuildPayBillInfoBillBackVOs(ctx context.Context, payBillInfoNomalVOs []vo.PayBillInfoNomalVO) []vo.PayBillInfoBillBackVO {
	// 1. payBillInfoNomalVOs 转换为 PayBillInfoBillBackVO
	payBillInfoBillBackVOs := s._convertToPayBillInfoBillBackVOs(ctx, payBillInfoNomalVOs)

	// 2. 根据 PayBillInfoNomalVO.PayBillRefundVOs 计算出 TmpMergedPayBillVO、TmpMergedPayRecords
	s._calculateMergedPayInfo(ctx, payBillInfoBillBackVOs)

	// 3. 根据 TmpMergedPayBillVO、TmpMergedPayRecords 计算出 NewPayBillVO、NewPayRecords
	s._createNewPayInfoForBack(ctx, payBillInfoBillBackVOs)

	// 4. 根据 NewPayBillVO、NewPayRecords 计算出 UpdateOrders
	s._createUpdateOrders(ctx, payBillInfoBillBackVOs)

	return payBillInfoBillBackVOs
}

// _convertToPayBillInfoBillBackVOs 转换为 PayBillInfoBillBackVO
func (s *PayServiceImpl) _convertToPayBillInfoBillBackVOs(ctx context.Context, payBillInfoNomalVOs []vo.PayBillInfoNomalVO) []vo.PayBillInfoBillBackVO {
	payBillInfoBillBackVOs := make([]vo.PayBillInfoBillBackVO, 0, len(payBillInfoNomalVOs))
	for _, payBillInfoNomalVO := range payBillInfoNomalVOs {
		payBillInfoBillBackVO := vo.PayBillInfoBillBackVO{
			PayBillInfoNomalVO: payBillInfoNomalVO,
		}
		payBillInfoBillBackVOs = append(payBillInfoBillBackVOs, payBillInfoBillBackVO)
	}
	return payBillInfoBillBackVOs
}

// _calculateMergedPayInfo 计算出 TmpMergedPayBillVO、TmpMergedPayRecords
// 此方法会直接修改传入的 payBillInfoBillBackVOs 切片中的元素
func (s *PayServiceImpl) _calculateMergedPayInfo(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO) {
	for i := range payBillInfoBillBackVOs {
		billBackVO := &payBillInfoBillBackVOs[i] // 使用指针直接修改切片元素
		nomalVO := billBackVO.PayBillInfoNomalVO // 获取原始支付信息

		// --- 计算 TmpMergedPayBillVO ---
		// 1. 基于原始 PayBillVO 创建
		tmpMergedPayBillVO := nomalVO.PayBillVO // 创建副本，避免修改原始 nomalVO 中的数据
		tmpMergedPayBillVO.RefundAmount = 0     // 初始化退款金额 (假设存在 RefundAmount 字段)

		// 2. 累加所有退款单的总金额
		totalBillRefundAmount := int64(0)
		for _, refundSrcVO := range nomalVO.PayBillRefundVOs {
			totalBillRefundAmount += refundSrcVO.PayBillVO.TotalFee // 退款单的 TotalFee 是负值还是正值需要明确，这里假设是正值代表退款额
		}
		// 3. 赋值给 TmpMergedPayBillVO 的 RefundAmount 字段
		tmpMergedPayBillVO.RefundAmount = totalBillRefundAmount // 假设 vo.PayBillVO 有 RefundAmount int64 字段

		// --- 计算 TmpMergedPayRecords ---
		// 1. 深度拷贝原始 PayRecordVOs
		tmpMergedPayRecords := make([]vo.PayRecordVO, len(nomalVO.PayRecordVOs))
		for k, record := range nomalVO.PayRecordVOs {
			// 初始化退款金额 (假设存在 RefundAmount 字段)
			record.RefundAmount = 0         // 假设 vo.PayRecordVO 有 RefundAmount int64 字段
			tmpMergedPayRecords[k] = record // 复制记录
		}

		// 2. 计算每个原始 PayRecord 被退款的总额
		refundsPerPayId := make(map[string]int64) // Key: 原始 PayRecord.PayId, Value: 累计退款额
		for _, refundSrcVO := range nomalVO.PayBillRefundVOs {
			for _, refundRecord := range refundSrcVO.PayRecordVOs {
				if refundRecord.PayPid != "" { // PayPid 关联到原始支付记录的 PayId
					// 假设退款记录的 TotalFee 是正值代表退款额
					refundsPerPayId[refundRecord.PayPid] += refundRecord.TotalFee
				}
			}
		}

		// 3. 将计算出的退款额赋值给对应的 TmpMergedPayRecordVO
		for k := range tmpMergedPayRecords {
			record := &tmpMergedPayRecords[k] // 使用指针修改切片内元素
			if refundAmount, found := refundsPerPayId[record.PayId]; found {
				record.RefundAmount = refundAmount // 假设 vo.PayRecordVO 有 RefundAmount int64 字段
			}
		}

		// --- 更新 BillBackVO ---
		billBackVO.TmpMergedPayBillVO = tmpMergedPayBillVO
		billBackVO.TmpMergedPayRecords = tmpMergedPayRecords
	}
}

// _createNewPayInfoForBack 计算出 NewPayBill、NewPayRecords、UpdateOrders
// 此方法会直接修改传入的 payBillInfoBillBackVOs 切片中的元素
func (s *PayServiceImpl) _createNewPayInfoForBack(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO) {
	now := int64(util.TimeNowUnix()) // 获取当前时间戳

	for i := range payBillInfoBillBackVOs {
		billBackVO := &payBillInfoBillBackVOs[i] // 使用指针直接修改切片元素
		tmpMergedPayBillVO := billBackVO.TmpMergedPayBillVO
		tmpMergedPayRecords := billBackVO.TmpMergedPayRecords

		// --- 初始化新 PO 列表和总金额 ---
		newPayRecordPOs := make([]po.PayRecord, 0, len(tmpMergedPayRecords))
		newBillTotalFee := int64(0)

		// --- 生成新的 BillId ---
		// 需要 VenueId 来生成 BillId，从 tmpMergedPayBillVO 获取
		if tmpMergedPayBillVO.VenueId == "" {
			// 处理 VenueId 为空的情况，可能需要打日志或返回错误，这里暂时跳过
			fmt.Printf("警告: VenueId 为空，无法为 BillId %s 生成新的还原账单ID\n", tmpMergedPayBillVO.BillId)
			continue
		}
		newBillId := util.GetBillId(tmpMergedPayBillVO.VenueId)
		hasLeshuaPay := false
		// --- 遍历合并后的支付记录，生成新的 PayRecord PO ---
		for _, mergedRecordVO := range tmpMergedPayRecords {
			// 计算净额 (原始支付 - 退款)
			// 再次强调：假设 TotalFee 是原始正金额, RefundAmount 是退款正金额
			netAmount := mergedRecordVO.TotalFee - mergedRecordVO.RefundAmount

			if netAmount <= 0 {
				// 如果净额小于等于0，说明此支付方式已完全退款或超额退款，无需生成新记录
				continue
			}
			if mergedRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
			}
			// 如果净额大于0，生成新的 PayRecord PO
			newPayId := util.GetPayId(tmpMergedPayBillVO.VenueId)                        // 为新记录生成新的 PayId
			payRecordTmpPO := s.payRecordService.ConvertToPayRecord(ctx, mergedRecordVO) // 复用转换逻辑获取基础信息
			status := _const.V2_PAY_RECORD_STATUS_SUCCESS
			if mergedRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				status = _const.V2_PAY_RECORD_STATUS_WAIT
			}
			newRecordPO := po.PayRecord{
				VenueId:                 &tmpMergedPayBillVO.VenueId,
				RoomId:                  &tmpMergedPayBillVO.RoomId,
				EmployeeId:              &tmpMergedPayBillVO.EmployeeId, // 使用 Bill 的 EmployeeId
				SessionId:               &tmpMergedPayBillVO.SessionId,
				MemberCardId:            &mergedRecordVO.MemberCardId,
				BillId:                  &newBillId,                     // 关联到新的 BillId
				PayId:                   &newPayId,                      // 使用新的 PayId
				PayPid:                  util.Ptr(mergedRecordVO.PayId), // 指向原始支付记录的 PayId
				TotalFee:                &netAmount,                     // 金额为净额
				PrincipalAmount:         &mergedRecordVO.PrincipalAmount,
				MemberRoomBonusAmount:   &mergedRecordVO.MemberRoomBonusAmount,
				MemberGoodsBonusAmount:  &mergedRecordVO.MemberGoodsBonusAmount,
				MemberCommonBonusAmount: &mergedRecordVO.MemberCommonBonusAmount,
				Status:                  &status,                // 状态设为等待处理
				PayType:                 payRecordTmpPO.PayType, // 保持原始支付类型
				FinishTime:              &now,                   // 完成时间为当前
				BillDate:                &now,                   // 账单日期为当前
				Info:                    util.Ptr(fmt.Sprintf("账单还原自 BillId: %s", tmpMergedPayBillVO.BillId)),
			}

			// 处理会员卡退款
			if mergedRecordVO.PayType == _const.PAY_TYPE_MEMBER_CARD {
				// 计算各类金额的净额
				if mergedRecordVO.PrincipalAmount > 0 {
					principalNetAmount := mergedRecordVO.PrincipalAmount - mergedRecordVO.RefundAmount
					if principalNetAmount > 0 {
						newRecordPO.PrincipalAmount = &principalNetAmount
					}
				}
				if mergedRecordVO.MemberRoomBonusAmount > 0 {
					roomBonusNetAmount := mergedRecordVO.MemberRoomBonusAmount - mergedRecordVO.RefundAmount
					if roomBonusNetAmount > 0 {
						newRecordPO.MemberRoomBonusAmount = &roomBonusNetAmount
					}
				}
				if mergedRecordVO.MemberGoodsBonusAmount > 0 {
					goodsBonusNetAmount := mergedRecordVO.MemberGoodsBonusAmount - mergedRecordVO.RefundAmount
					if goodsBonusNetAmount > 0 {
						newRecordPO.MemberGoodsBonusAmount = &goodsBonusNetAmount
					}
				}
				if mergedRecordVO.MemberCommonBonusAmount > 0 {
					commonBonusNetAmount := mergedRecordVO.MemberCommonBonusAmount - mergedRecordVO.RefundAmount
					if commonBonusNetAmount > 0 {
						newRecordPO.MemberCommonBonusAmount = &commonBonusNetAmount
					}
				}
			}

			newPayRecordPOs = append(newPayRecordPOs, newRecordPO)
			newBillTotalFee += netAmount // 累加新账单总金额
		}

		payBillStatus := _const.V2_PAY_BILL_STATUS_PAID
		if hasLeshuaPay {
			payBillStatus = _const.V2_PAY_BILL_STATUS_WAIT
		}
		refundAmount := tmpMergedPayBillVO.TotalFee - tmpMergedPayBillVO.RefundAmount
		// --- 生成新的 PayBill PO ---
		newPayBillPO := po.PayBill{
			VenueId:      &tmpMergedPayBillVO.VenueId,
			RoomId:       &tmpMergedPayBillVO.RoomId,
			EmployeeId:   &tmpMergedPayBillVO.EmployeeId,
			SessionId:    &tmpMergedPayBillVO.SessionId,
			MemberCardId: &tmpMergedPayBillVO.MemberCardId,
			BillId:       &newBillId,                                            // 使用新的 BillId
			BillPid:      util.Ptr(tmpMergedPayBillVO.BillId),                   // 指向原始账单的 BillId
			TotalFee:     &refundAmount,                                         // 新账单的总金额
			Direction:    util.Ptr(string(_const.V2_PAY_BILL_DIRECTION_REFUND)), // 方向设为 REFUND (表示还原操作生成的)
			Status:       &payBillStatus,                                        // 状态设为等待处理
			IsBack:       util.Ptr(true),                                        // 是否是还原账单
			FinishTime:   &now,                                                  // 完成时间为当前
			BillDate:     &now,                                                  // 账单日期为当前
			// 其他字段如 OriginalFee, ShouldFee, ZeroFee, CreditAmount, ChangeAmount 等
			// 在还原账单场景下通常为0或根据具体业务逻辑设置
			OriginalFee:  &refundAmount, // 示例：设为0
			ShouldFee:    &refundAmount, // 应付 = 实付 (因为是还原)
			ZeroFee:      util.Ptr(int64(0)),
			CreditAmount: util.Ptr(int64(0)),
			ChangeAmount: util.Ptr(int64(0)),
			// Discount 相关字段通常也为0
			ProductDiscount:       util.Ptr(int64(0)),
			RoomDiscount:          util.Ptr(int64(0)),
			ProductDiscountAmount: util.Ptr(int64(0)),
			RoomDiscountAmount:    util.Ptr(int64(0)),
			// Info 字段可以添加说明
			Info: util.Ptr(fmt.Sprintf("账单还原自 BillId: %s", tmpMergedPayBillVO.BillId)),
			// IsBack 字段可能需要处理，但 PayBill PO 定义中没有，可能在 PayBillVO 中
		}

		// --- 更新 BillBackVO ---
		billBackVO.NewPayBill = newPayBillPO
		billBackVO.NewPayRecords = newPayRecordPOs
	}
}

// _createUpdateOrders 计算出 UpdateOrders
// 此方法会直接修改传入的 payBillInfoBillBackVOs 切片中的元素
// 它基于 PayBillInfoNomalVO 中的 OrderVOs 来创建需要更新状态为"未付"的 Order PO 列表。
func (s *PayServiceImpl) _createUpdateOrders(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO) {
	unpaidStatus := _const.V2_ORDER_STATUS_UNPAID // 获取"未付"状态常量字符串

	for i := range payBillInfoBillBackVOs {
		billBackVO := &payBillInfoBillBackVOs[i]           // 使用指针直接修改切片元素
		orderVOs := billBackVO.PayBillInfoNomalVO.OrderVOs // 获取关联的 OrderVOs
		// 获取原始支付账单
		parentPayBillVO := billBackVO.PayBillInfoNomalVO.PayBillVO
		billBackVO.UpdatePayBillParent = po.PayBill{
			Id:     &parentPayBillVO.Id,
			IsBack: util.Ptr(true),
		}

		// 获取原支付账单的退款账单
		refundPayBillVOs := billBackVO.PayBillInfoNomalVO.PayBillRefundVOs
		for _, refundPayBillVO := range refundPayBillVOs {
			billBackVO.UpdatePayBillsRefund = append(billBackVO.UpdatePayBillsRefund, po.PayBill{
				Id:     &refundPayBillVO.PayBillVO.Id,
				IsBack: util.Ptr(true),
			})
		}

		// 初始化用于存储待更新 Order PO 的切片
		updateOrderPOs := make([]po.Order, 0, len(orderVOs))

		// 遍历 OrderVOs，创建对应的 Order PO 并设置状态为未付
		for _, orderVO := range orderVOs {
			if orderVO.Id == "" {
				// 如果 OrderVO 没有 ID，跳过，可能需要记录日志
				fmt.Printf("警告: OrderVO OrderNo %s 没有有效的 Id，无法生成更新记录\n", orderVO.OrderNo)
				continue
			}
			id := orderVO.Id
			updateOrderPO := po.Order{
				Id:     &id,           // 填充 Id 字段
				Status: &unpaidStatus, // 设置 Status 为"未付"
			}
			updateOrderPOs = append(updateOrderPOs, updateOrderPO)
		}
		// 对应的退款订单也要更新为未付
		refundOrderVOs := make([]vo.OrderVO, 0)
		for _, refundPayBillVO := range refundPayBillVOs {
			refundOrderVOs = append(refundOrderVOs, refundPayBillVO.OrderVOs...)
		}
		for _, refundOrderVO := range refundOrderVOs {
			if refundOrderVO.Id == "" {
				// 如果 OrderVO 没有 ID，跳过，可能需要记录日志
				fmt.Printf("警告: OrderVO OrderNo %s 没有有效的 Id，无法生成更新记录\n", refundOrderVO.OrderNo)
				continue
			}
			updateOrderPO := po.Order{
				Id:     &refundOrderVO.Id, // 填充 Id 字段
				Status: &unpaidStatus,     // 设置 Status 为"未付"
			}
			updateOrderPOs = append(updateOrderPOs, updateOrderPO)
		}

		// 如果原支付账单有折扣，则需要重建商品和房费
		if util.Between(parentPayBillVO.ProductDiscount, 1, 99) || util.Between(parentPayBillVO.RoomDiscount, 1, 99) || parentPayBillVO.ProductDiscountAmount > 0 || parentPayBillVO.RoomDiscountAmount > 0 || parentPayBillVO.IsFree {
			s._rebuildOPOM(ctx, billBackVO)
		}

		// 将生成的待更新 Order PO 列表赋值给 UpdateOrders 字段
		billBackVO.UpdateOrders = updateOrderPOs
	}
}

func (s *PayServiceImpl) _rebuildOPOM(ctx context.Context, billBackVO *vo.PayBillInfoBillBackVO) {
	allOrderVOs := make([]vo.OrderVO, 0)

	// 获取正常订单信息
	nomalOrderVOs := make([]vo.OrderVO, 0)
	for _, orderVOTmp := range billBackVO.PayBillInfoNomalVO.OrderVOs {
		newOrderVO := orderVOTmp
		newOrderVO.Status = _const.V2_ORDER_STATUS_UNPAID
		nomalOrderVOs = append(nomalOrderVOs, newOrderVO)
	}
	nomalOrderProductVOs := billBackVO.PayBillInfoNomalVO.OrderProductVOs
	nomalOrderRoomPlanVOs := billBackVO.PayBillInfoNomalVO.OrderRoomPlanVOs

	normalOrderProductMap := make(map[string][]vo.OrderProductVO)
	for _, nomalOrderProductVO := range nomalOrderProductVOs {
		newOrderProductVO := nomalOrderProductVO
		// 账单还原-移除免单特殊标记，才能重新计算金额
		newOrderProductVO.IsFree = false
		newOrderProductVO.PayProductDiscount = 100
		newOrderProductVO.PayProductDiscountAmount = 0
		normalOrderProductMap[nomalOrderProductVO.OrderNo] = append(normalOrderProductMap[nomalOrderProductVO.OrderNo], newOrderProductVO)
	}
	normalOrderRoomPlanMap := make(map[string][]vo.OrderRoomPlanVO)
	for _, nomalOrderRoomPlanVO := range nomalOrderRoomPlanVOs {
		newOrderRoomPlanVO := nomalOrderRoomPlanVO
		newOrderRoomPlanVO.IsFree = false
		newOrderRoomPlanVO.PayRoomDiscount = 100
		newOrderRoomPlanVO.PayRoomDiscountAmount = 0
		normalOrderRoomPlanMap[nomalOrderRoomPlanVO.OrderNo] = append(normalOrderRoomPlanMap[nomalOrderRoomPlanVO.OrderNo], newOrderRoomPlanVO)
	}

	for _, nomalOrderVO := range nomalOrderVOs {
		newOrderVO := nomalOrderVO
		if opVOsTmp, ok := normalOrderProductMap[nomalOrderVO.OrderNo]; ok {
			newOrderVO.OrderProductVOs = opVOsTmp
		}
		if omVOsTmp, ok := normalOrderRoomPlanMap[nomalOrderVO.OrderNo]; ok {
			newOrderVO.OrderRoomPlanVOs = omVOsTmp
		}
		allOrderVOs = append(allOrderVOs, newOrderVO)
	}

	// 获取退款订单信息
	refundPayBillRefundVOs := billBackVO.PayBillInfoNomalVO.PayBillRefundVOs
	refundOrderVOs := make([]vo.OrderVO, 0)
	refundOrderProductVOs := make([]vo.OrderProductVO, 0)
	refundOrderRoomPlanVOs := make([]vo.OrderRoomPlanVO, 0)
	for _, refundPayBillRefundVO := range refundPayBillRefundVOs {
		refundOrderVOs = make([]vo.OrderVO, 0)
		for _, orderVOTmp := range refundPayBillRefundVO.OrderVOs {
			newOrderVO := orderVOTmp
			newOrderVO.Status = _const.V2_ORDER_STATUS_UNPAID
			refundOrderVOs = append(refundOrderVOs, newOrderVO)
		}
		refundOrderProductVOs = append(refundOrderProductVOs, refundPayBillRefundVO.OrderProductVOs...)
		refundOrderRoomPlanVOs = append(refundOrderRoomPlanVOs, refundPayBillRefundVO.OrderRoomPlanVOs...)
	}

	refundOrderProductMap := make(map[string][]vo.OrderProductVO)
	for _, refundOrderProductVO := range refundOrderProductVOs {
		newOrderProductVO := refundOrderProductVO
		// 重置免单/赠送等标记，以便重新计算金额
		newOrderProductVO.IsFree = false
		newOrderProductVO.PayProductDiscount = 100
		newOrderProductVO.PayProductDiscountAmount = 0
		refundOrderProductMap[refundOrderProductVO.OrderNo] = append(refundOrderProductMap[refundOrderProductVO.OrderNo], newOrderProductVO)
	}
	refundOrderRoomPlanMap := make(map[string][]vo.OrderRoomPlanVO)
	for _, refundOrderRoomPlanVO := range refundOrderRoomPlanVOs {
		newOrderRoomPlanVO := refundOrderRoomPlanVO
		newOrderRoomPlanVO.IsFree = false
		newOrderRoomPlanVO.PayRoomDiscount = 100
		newOrderRoomPlanVO.PayRoomDiscountAmount = 0
		refundOrderRoomPlanMap[refundOrderRoomPlanVO.OrderNo] = append(refundOrderRoomPlanMap[refundOrderRoomPlanVO.OrderNo], newOrderRoomPlanVO)
	}
	for _, refundOrderVO := range refundOrderVOs {
		newOrderVO := refundOrderVO
		if opVOsTmp, ok := refundOrderProductMap[refundOrderVO.OrderNo]; ok {
			newOrderVO.OrderProductVOs = opVOsTmp
		}
		if omVOsTmp, ok := refundOrderRoomPlanMap[refundOrderVO.OrderNo]; ok {
			newOrderVO.OrderRoomPlanVOs = omVOsTmp
		}
		allOrderVOs = append(allOrderVOs, newOrderVO)
	}

	_, newOps, newOms, _, _ := s.CalculateTotalPaymentForPay(ctx, vo.SessionVO{}, allOrderVOs, vo.PayBillVO{})
	ops := make([]po.OrderProduct, 0)
	for _, opVO := range newOps {
		ops = append(ops, po.OrderProduct{
			Id:                       &opVO.Id,
			PayAmount:                &opVO.PayAmount,
			PayProductDiscount:       util.Ptr(int64(100)),
			PayProductDiscountAmount: new(int64),
			IsFree:                   util.GetItPtr(false),
		})
	}
	oms := make([]po.OrderRoomPlan, 0)
	for _, omVO := range newOms {
		oms = append(oms, po.OrderRoomPlan{
			Id:                    &omVO.Id,
			PayAmount:             &omVO.PayAmount,
			PayRoomDiscount:       util.Ptr(int64(100)),
			PayRoomDiscountAmount: new(int64),
			IsFree:                util.GetItPtr(false),
		})
	}
	billBackVO.UpdateOrderProducts = ops
	billBackVO.UpdateOrderRoomPlans = oms
}

func (s *PayServiceImpl) SavePayBillInfoBillBackVOs(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO, reqDto req.V3BillBackReqDto) error {
	return s.payRepo.SavePayBillInfoBillBackVOs(ctx, payBillInfoBillBackVOs, reqDto)
}

func (s *PayServiceImpl) CalcOrderHasMultiPayWay(ctx context.Context, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) []vo.OrderVO {
	orderVOs := []vo.OrderVO{}
	orderAndPayVOs := orderPOInfoUnionVO.OrderAndPays
	payRecords := orderPOInfoUnionVO.PayRecords
	billIdPayTypeMap := make(map[string]int)
	for _, payRecord := range payRecords {
		billIdPayTypeMap[*payRecord.BillId] += 1
	}
	orderNoToPayTypeCountMap := make(map[string]int)
	for _, orderAndPay := range orderAndPayVOs {
		orderNoToPayTypeCountMap[*orderAndPay.OrderNo] = billIdPayTypeMap[*orderAndPay.BillId]
	}
	for _, order := range orderPOInfoUnionVO.Orders {
		orderVO := s.orderService.ConvertToOrderVO(ctx, order)
		orderVO.HasMultiPayWay = orderNoToPayTypeCountMap[*order.OrderNo] > 1
		orderVOs = append(orderVOs, orderVO)
	}
	return orderVOs
}

func (s *PayServiceImpl) SendNATSMessageForRoomStatusChanged(ctx context.Context, venueId string, forceSend bool, toAddPayRecords []po.PayRecord) error {
	// 如果强制发送，则直接发送NATS消息
	if forceSend {
		return s.payRepo.SendNATSMessageForRoomStatusChanged(ctx, venueId)
	}
	// 如果不强制发送，则判断是否存在乐刷支付
	hasAsynchronousPay := false
	for _, payRecord := range toAddPayRecords {
		if util.InList(*payRecord.PayType, []string{_const.PAY_TYPE_LESHUA_BSHOWQR, _const.PAY_TYPE_LESHUA_SIMPLE}) {
			hasAsynchronousPay = true
			break
		}
	}
	if hasAsynchronousPay {
		// 如果存在乐刷支付，则不发送NATS消息，依赖回调通知
		return nil
	}
	// 如果不存在乐刷支付，则发送NATS消息
	return s.payRepo.SendNATSMessageForRoomStatusChanged(ctx, venueId)
}

// V3RefundByCashRoomPlan 按现金退款
func (s *PayServiceImpl) V3RefundByCashRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	return s.payRepo.V3RefundByCashRoomPlan(ctx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
}

// V3RefundByBackRoomPlan 按原路返回退款
func (s *PayServiceImpl) V3RefundByBackRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error) {
	return s.payRepo.V3RefundByBackRoomPlan(ctx, reqDto, orderInfoGroups, session, room, totalRefundAmount, orderPOInfoUnionVO)
}

// GetOrderInfoBatchBySessionId 获取session下的所有订单相关PO数据
func (s *PayServiceImpl) GetOrderInfoBatchBySessionId(ctx context.Context, sessionId string, venueId string, roomId string, employeeId string) (vo.ModeOrderInfoBaseSessionPO, error) {
	return s.payRepo.GetOrderInfoBatchBySessionId(ctx, sessionId, venueId, roomId, employeeId)
}

// ConvertToModeOrderInfoBaseSessionBO 转换为ModeOrderInfoBaseSessionBO
func (s *PayServiceImpl) ConvertToModeOrderInfoBaseSessionBO(ctx context.Context, orderInfoPO vo.ModeOrderInfoBaseSessionPO) vo.ModeOrderInfoBaseSessionBO {
	modeOrderInfoBaseSessionBO := vo.ModeOrderInfoBaseSessionBO{
		VenueId:   *orderInfoPO.Venue.Id,
		RoomId:    *orderInfoPO.Room.Id,
		SessionId: *orderInfoPO.Session.Id,
		VenueVO:   s.venueRepo.ConvertToVenueVO(ctx, orderInfoPO.Venue),
		RoomVO:    s.roomRepo.ConvertToRoomVO(ctx, orderInfoPO.Room),
		SessionVO: s.sessionService.ConvertToSessionVO(ctx, orderInfoPO.Session),

		ModeOrderInfoBaseSessionPO: orderInfoPO,
	}
	// 订单，将 op om 填充到 order中
	orderNOToOrderProductVOsMap := make(map[string][]vo.OrderProductVO)
	orderNOToOrderRoomPlanVOsMap := make(map[string][]vo.OrderRoomPlanVO)
	for _, orderProduct := range orderInfoPO.OrderProducts {
		orderNOToOrderProductVOsMap[*orderProduct.OrderNo] = append(orderNOToOrderProductVOsMap[*orderProduct.OrderNo], s.orderProductRepo.ConvertToOrderProductVO(ctx, orderProduct))
	}
	for _, orderRoomPlan := range orderInfoPO.OrderRoomPlans {
		orderNOToOrderRoomPlanVOsMap[*orderRoomPlan.OrderNo] = append(orderNOToOrderRoomPlanVOsMap[*orderRoomPlan.OrderNo], s.orderRoomPlanRepo.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	allOrderVOs := make([]vo.OrderVO, 0)
	for _, order := range orderInfoPO.Orders {
		orderVO := s.orderService.ConvertToOrderVO(ctx, order)
		if opVOsTmp, ok := orderNOToOrderProductVOsMap[*order.OrderNo]; ok {
			orderVO.OrderProductVOs = opVOsTmp
		}
		if omVOsTmp, ok := orderNOToOrderRoomPlanVOsMap[*order.OrderNo]; ok {
			orderVO.OrderRoomPlanVOs = omVOsTmp
		}
		allOrderVOs = append(allOrderVOs, orderVO)
	}
	orderNoToOrderVOsMap := make(map[string]vo.OrderVO)
	for _, orderVO := range allOrderVOs {
		orderNoToOrderVOsMap[orderVO.OrderNo] = orderVO
	}

	// 账单 payrecords orderandpay 填充到 paybill中
	billIdToOrderAndPayVOsMap := make(map[string][]vo.OrderAndPayVO)
	for _, orderAndPay := range orderInfoPO.OrderAndPays {
		billIdToOrderAndPayVOsMap[*orderAndPay.BillId] = append(billIdToOrderAndPayVOsMap[*orderAndPay.BillId], s.orderAndPayRepo.ConvertToOrderAndPayVO(ctx, orderAndPay))
	}
	billIdToPayRecordVOsMap := make(map[string][]vo.PayRecordVO)
	for _, payRecord := range orderInfoPO.PayRecords {
		billIdToPayRecordVOsMap[*payRecord.BillId] = append(billIdToPayRecordVOsMap[*payRecord.BillId], s.payRecordRepo.ConvertToPayRecordVO(ctx, payRecord))
	}
	allPayBillVOs := make([]vo.PayBillVO, 0)
	orderNoPaid := []string{}
	for _, payBill := range orderInfoPO.PayBills {
		payBillVO := s.payBillRepo.ConvertToPayBillVO(ctx, payBill)
		if orderAndPayVOsTmp, ok := billIdToOrderAndPayVOsMap[*payBill.BillId]; ok {
			for _, orderAndPayVO := range orderAndPayVOsTmp {
				orderVO := orderNoToOrderVOsMap[orderAndPayVO.OrderNo]
				orderNoPaid = append(orderNoPaid, orderAndPayVO.OrderNo)
				payBillVO.OrderVOs = append(payBillVO.OrderVOs, orderVO)
			}
			payBillVO.OrderAndPayVOs = orderAndPayVOsTmp
		}
		if payRecordVOsTmp, ok := billIdToPayRecordVOsMap[*payBill.BillId]; ok {
			payBillVO.PayRecordVOs = payRecordVOsTmp
		}
		allPayBillVOs = append(allPayBillVOs, payBillVO)
	}
	OrderVOsUnPaid := make([]vo.OrderVO, 0)
	for _, orderVO := range orderNoToOrderVOsMap {
		if !util.InList(orderVO.OrderNo, orderNoPaid) {
			OrderVOsUnPaid = append(OrderVOsUnPaid, orderVO)
		}
	}

	modeOrderInfoBaseSessionBO.OrderVOsUnpaid = OrderVOsUnPaid
	modeOrderInfoBaseSessionBO.PayBillVOs = allPayBillVOs
	return modeOrderInfoBaseSessionBO
}

func (s *PayServiceImpl) DoCancelOrderOpenInfo(ctx context.Context, reqDto req.V3CancelOrderOpenReqDto, modeOrderInfoBaseSessionBO vo.ModeOrderInfoBaseSessionBO) (bool, error) {
	venueId := modeOrderInfoBaseSessionBO.VenueId
	modeOrderInfoBaseSessionPO := vo.ModeOrderInfoBaseSessionPO{
		Venue:            s.venueRepo.ConvertToVenue(ctx, modeOrderInfoBaseSessionBO.VenueVO),
		Room:             s.roomRepo.ConvertToRoom(ctx, modeOrderInfoBaseSessionBO.RoomVO),
		Session:          s.sessionService.ConvertToSession(ctx, modeOrderInfoBaseSessionBO.SessionVO),
		Orders:           make([]po.Order, 0),
		OrderProducts:    make([]po.OrderProduct, 0),
		OrderRoomPlans:   make([]po.OrderRoomPlan, 0),
		OrderAndPays:     make([]po.OrderAndPay, 0),
		PayRecords:       make([]po.PayRecord, 0),
		PayBills:         make([]po.PayBill, 0),
		ToUpdateOrders:   make([]po.Order, 0),
		ToUpdatePayBills: make([]po.PayBill, 0),
	}

	// 处理未付的订单
	toUpdateOrdersTmp, newOrders, newOps, newOms := s.GenRefundOrderPOs(ctx, venueId, modeOrderInfoBaseSessionBO.OrderVOsUnpaid)
	modeOrderInfoBaseSessionPO.Orders = append(modeOrderInfoBaseSessionPO.Orders, newOrders...)
	modeOrderInfoBaseSessionPO.OrderProducts = append(modeOrderInfoBaseSessionPO.OrderProducts, newOps...)
	modeOrderInfoBaseSessionPO.OrderRoomPlans = append(modeOrderInfoBaseSessionPO.OrderRoomPlans, newOms...)
	modeOrderInfoBaseSessionPO.ToUpdateOrders = append(modeOrderInfoBaseSessionPO.ToUpdateOrders, toUpdateOrdersTmp...)

	// 处理退款订单和账单
	paidNewOrders, paidNewOps, paidNewOms, paidNewPayBills, paidNewPayRecords, paidNewOrderAndPays, toUpdatePayBills := s.DealPaidOrder(ctx, venueId, modeOrderInfoBaseSessionBO.PayBillVOs)
	modeOrderInfoBaseSessionPO.Orders = append(modeOrderInfoBaseSessionPO.Orders, paidNewOrders...)
	modeOrderInfoBaseSessionPO.OrderProducts = append(modeOrderInfoBaseSessionPO.OrderProducts, paidNewOps...)
	modeOrderInfoBaseSessionPO.OrderRoomPlans = append(modeOrderInfoBaseSessionPO.OrderRoomPlans, paidNewOms...)
	modeOrderInfoBaseSessionPO.PayBills = append(modeOrderInfoBaseSessionPO.PayBills, paidNewPayBills...)
	modeOrderInfoBaseSessionPO.PayRecords = append(modeOrderInfoBaseSessionPO.PayRecords, paidNewPayRecords...)
	modeOrderInfoBaseSessionPO.OrderAndPays = append(modeOrderInfoBaseSessionPO.OrderAndPays, paidNewOrderAndPays...)
	modeOrderInfoBaseSessionPO.ToUpdatePayBills = append(modeOrderInfoBaseSessionPO.ToUpdatePayBills, toUpdatePayBills...)

	// 添加原因
	tmpPayBills := make([]po.PayBill, 0)
	for _, payBill := range modeOrderInfoBaseSessionPO.PayBills {
		payBill.CancelEmployeeId = reqDto.CancelEmployeeId
		payBill.CancelEmployeeName = reqDto.CancelEmployeeName
		payBill.CancelReason = reqDto.CancelReason
		tmpPayBills = append(tmpPayBills, payBill)
	}
	modeOrderInfoBaseSessionPO.PayBills = tmpPayBills

	toUpdateSessions := po.Session{
		Id:                 modeOrderInfoBaseSessionPO.Session.Id,
		CancelEmployeeId:   reqDto.CancelEmployeeId,
		CancelEmployeeName: reqDto.CancelEmployeeName,
		CancelReason:       reqDto.CancelReason,
		IsTimeConsume:      util.Ptr(false),
	}
	modeOrderInfoBaseSessionPO.ToUpdateSessions = append(modeOrderInfoBaseSessionPO.ToUpdateSessions, toUpdateSessions)

	hasLeshuaPay := false
	for _, payRecordVO := range paidNewPayRecords {
		if *payRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			hasLeshuaPay = true
			break
		}
	}
	// 处理退款信息及发起退款
	err := s.payRepo.DoAndSaveCancelOrderOpenInfo(ctx, venueId, modeOrderInfoBaseSessionPO)
	if err != nil {
		return false, err
	}
	return hasLeshuaPay, nil
}

// DealUnpaidOrder 处理未付的订单
func (s *PayServiceImpl) GenRefundOrderPOs(ctx context.Context, venueId string, orderVOs []vo.OrderVO) (toUpdateOrders []po.Order, newOrders []po.Order, newOps []po.OrderProduct, newOms []po.OrderRoomPlan) {
	toUpdateOrders = make([]po.Order, 0)
	newOrders = make([]po.Order, 0)
	newOps = make([]po.OrderProduct, 0)
	newOms = make([]po.OrderRoomPlan, 0)
	// 构建支付-退款树形结构
	treeOrderVOs := s.BuildTreeOrderVOs(ctx, orderVOs)
	// 合并退款订单
	mergedTreeOrderVOs := s.MergeOrderVOs(ctx, treeOrderVOs)
	// 生成退款订单
	for _, orderVOTmp := range mergedTreeOrderVOs {
		newOrderNO := util.GetOrderNo(venueId)

		if orderVOTmp.Type == _const.V2_ORDER_TYPE_PRODUCT {
			// 生成退款商品
			for _, orderProduct := range orderVOTmp.OrderProductVOs {
				newOrderProduct := s.orderProductRepo.ConvertToOrderProduct(ctx, orderProduct)
				newOrderProduct.Id = nil
				newOrderProduct.PId = &orderProduct.Id
				newOrderProduct.OrderNo = &newOrderNO
				newOrderProduct.Ctime = nil
				newOrderProduct.Utime = nil
				newOrderProduct.State = nil
				newOrderProduct.Version = nil
				newOps = append(newOps, newOrderProduct)
			}
		} else if orderVOTmp.Type == _const.V2_ORDER_TYPE_ROOMPLAN {
			// 生成退款房费 房费要退整个订单退掉了，所以房费要退整个订单
			for _, orderRoomPlan := range orderVOTmp.OrderRoomPlanVOs {
				newOrderRoomPlan := s.orderRoomPlanRepo.ConvertToOrderRoomPlan(ctx, orderRoomPlan)
				newOrderRoomPlan.Id = nil
				newOrderRoomPlan.PId = &orderRoomPlan.Id
				newOrderRoomPlan.OrderNo = &newOrderNO
				newOrderRoomPlan.Ctime = nil
				newOrderRoomPlan.Utime = nil
				newOrderRoomPlan.State = nil
				newOrderRoomPlan.Version = nil
				newOms = append(newOms, newOrderRoomPlan)
			}
		}

		// 生成退款订单
		newOrder := s.orderService.ConvertToOrderPO(ctx, orderVOTmp)
		newOrder.Id = nil
		newOrder.OrderNo = &newOrderNO
		newOrder.POrderNo = &orderVOTmp.OrderNo
		newOrder.Direction = util.Ptr(string(_const.V2_ORDER_DIRECTION_REFUND))
		newOrder.Status = util.Ptr(string(_const.V2_ORDER_STATUS_PAID))
		newOrder.MarkType = util.Ptr(string(_const.V2_ORDER_MARK_TYPE_CANCEL))
		newOrder.RefundTag = util.Ptr(string(_const.V2_ORDER_REFUND_TAG_UNPAID_REFUND))
		newOrder.Ctime = nil
		newOrder.Utime = nil
		newOrder.State = nil
		newOrder.Version = nil
		toUpdateOrders = append(toUpdateOrders, po.Order{
			Id:        &orderVOTmp.Id,
			Status:    util.Ptr(string(_const.V2_ORDER_STATUS_PAID)),
			RefundTag: util.Ptr(string(_const.V2_ORDER_REFUND_TAG_UNPAID_REFUND)),
		})
		newOrders = append(newOrders, newOrder)
	}
	return toUpdateOrders, newOrders, newOps, newOms
}

// BuildTreeOrderVOs 构建order支付-退款树形结构
func (s *PayServiceImpl) BuildTreeOrderVOs(ctx context.Context, orderVOs []vo.OrderVO) []vo.OrderVO {
	normalOrderVOs := make([]vo.OrderVO, 0)
	refundOrderNoToOrderVOsMap := make(map[string][]vo.OrderVO)
	for _, orderVO := range orderVOs {
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			normalOrderVOs = append(normalOrderVOs, orderVO)
		} else {
			refundOrderNoToOrderVOsMap[orderVO.POrderNo] = append(refundOrderNoToOrderVOsMap[orderVO.POrderNo], orderVO)
		}
	}
	for index, orderVO := range normalOrderVOs {
		orderVO.RefundOrders = refundOrderNoToOrderVOsMap[orderVO.OrderNo]
		normalOrderVOs[index] = orderVO
	}
	return normalOrderVOs
}

// MergeOrderVOs 合并退款订单 payamount 和 quantity 需要合并,其他保持不变
func (s *PayServiceImpl) MergeOrderVOs(ctx context.Context, treeOrderVOs []vo.OrderVO) []vo.OrderVO {
	rOrderVOs := make([]vo.OrderVO, 0)
	refundOpIdToOrderProductVOsMap := make(map[string][]vo.OrderProductVO)
	for _, orderVO := range treeOrderVOs {
		if orderVO.Type == _const.V2_ORDER_TYPE_PRODUCT {
			for _, orderProduct := range orderVO.OrderProductVOs {
				refundOpIdToOrderProductVOsMap[orderProduct.PId] = append(refundOpIdToOrderProductVOsMap[orderProduct.PId], orderProduct)
			}
		}
	}
	for _, srcOrderVO := range treeOrderVOs {
		newOrderVO := util.DeepCopy(srcOrderVO)
		newOrderProductVOs := make([]vo.OrderProductVO, 0)
		newOrderRoomPlanVOs := make([]vo.OrderRoomPlanVO, 0)
		if srcOrderVO.Type == _const.V2_ORDER_TYPE_PRODUCT {
			// 商品
			for _, orderProduct := range srcOrderVO.OrderProductVOs {
				srcQuantity := orderProduct.Quantity
				hasRefundQuantity := int64(0)
				hasRefundPayAmount := int64(0)
				if opVOsTmp, ok := refundOpIdToOrderProductVOsMap[orderProduct.Id]; ok {
					for _, opVO := range opVOsTmp {
						hasRefundQuantity += opVO.Quantity
						hasRefundPayAmount += opVO.PayAmount
					}
				}
				if hasRefundQuantity == srcQuantity {
					// 跳过已经完全退款的商品
					continue
				}
				newOrderProductVO := util.DeepCopy(orderProduct)
				newOrderProductVO.Quantity = srcQuantity - hasRefundQuantity
				newOrderProductVO.PayAmount = orderProduct.PayAmount - hasRefundPayAmount
				newOrderProductVOs = append(newOrderProductVOs, newOrderProductVO)
			}
		} else if srcOrderVO.Type == _const.V2_ORDER_TYPE_ROOMPLAN {
			// 房费 是按订单整体退的，所以房费要退整个订单
			if len(srcOrderVO.RefundOrders) > 0 {
				continue
			}
			newOrderRoomPlanVOs = append(newOrderRoomPlanVOs, srcOrderVO.OrderRoomPlanVOs...)
		}
		// 完全退款的订单，则跳过
		if len(newOrderProductVOs) > 0 || len(newOrderRoomPlanVOs) > 0 {
			newOrderVO.OrderProductVOs = newOrderProductVOs
			newOrderVO.OrderRoomPlanVOs = newOrderRoomPlanVOs
			rOrderVOs = append(rOrderVOs, newOrderVO)
		}
	}
	return rOrderVOs
}

func (s *PayServiceImpl) DealPaidOrder(ctx context.Context, venueId string, payBillVOs []vo.PayBillVO) (newOrders []po.Order, newOps []po.OrderProduct, newOms []po.OrderRoomPlan, newPayBills []po.PayBill, newPayRecords []po.PayRecord, newOrderAndPays []po.OrderAndPay, toUpdatePayBills []po.PayBill) {
	newOrders = make([]po.Order, 0)
	newOps = make([]po.OrderProduct, 0)
	newOms = make([]po.OrderRoomPlan, 0)
	newPayBills = make([]po.PayBill, 0)
	newPayRecords = make([]po.PayRecord, 0)
	newOrderAndPays = make([]po.OrderAndPay, 0)
	toUpdatePayBills = make([]po.PayBill, 0)

	calcPayBillVOs := make([]vo.PayBillVO, 0)
	for _, payBillVO := range payBillVOs {
		// 如果账单是退款账单，则跳过
		if payBillVO.IsBack {
			continue
		}
		// 如果账单是赠品，则跳过
		if payBillVO.IsGift {
			continue
		}
		// 如果账单是免费账单，则跳过
		if payBillVO.IsFree {
			continue
		}
		// 如果账单是取消账单，则跳过
		if payBillVO.IsCancel {
			continue
		}
		calcPayBillVOs = append(calcPayBillVOs, payBillVO)
	}
	// 构建支付-退款树形结构
	treePayBillVOs := s.BuildTreePayBillVOs(ctx, calcPayBillVOs)
	// 合并退款账单
	mergedTreePayBillVOs := s.MergePayBillVOs(ctx, treePayBillVOs)

	// 生成退款账单
	for _, payBillVO := range mergedTreePayBillVOs {
		newPayBillId := util.GetBillId(venueId)
		newPayBill := s.payBillRepo.ConvertToPayBill(ctx, payBillVO)
		newPayBill.Id = nil
		newPayBill.BillId = &newPayBillId
		newPayBill.BillPid = &payBillVO.BillId
		newPayBill.Direction = util.Ptr(string(_const.V2_PAY_BILL_DIRECTION_REFUND))
		newPayBill.IsCancel = util.Ptr(true)
		newPayBill.Ctime = nil
		newPayBill.Utime = nil
		newPayBill.State = nil
		newPayBill.Version = nil
		toUpdatePayBills = append(toUpdatePayBills, po.PayBill{
			Id:       &payBillVO.Id,
			IsCancel: newPayBill.IsCancel,
		})

		// 生成退款订单
		orderVOs := payBillVO.OrderVOs
		for _, payBillVORefund := range payBillVO.RefundPayBillVOs {
			orderVOs = append(orderVOs, payBillVORefund.OrderVOs...)
		}
		_, newOrdersTmp, newOpsTmp, newOmsTmp := s.GenRefundOrderPOs(ctx, venueId, orderVOs)
		newOrders = append(newOrders, newOrdersTmp...)
		newOps = append(newOps, newOpsTmp...)
		newOms = append(newOms, newOmsTmp...)

		for _, order := range newOrdersTmp {
			orderNo := *order.OrderNo
			newOrderAndPays = append(newOrderAndPays, po.OrderAndPay{
				OrderNo:   &orderNo,
				SessionId: &payBillVO.SessionId,
				BillId:    &newPayBillId,
			})
		}
		// 生成退款记录
		mergePayRecords := s.MergePayRecordVOs(ctx, payBillVO)
		newPayRecordsTmp := s.GenRefundPayRecordPOs(ctx, venueId, newPayBillId, mergePayRecords)
		hasLeshuaPay := false
		for _, payRecord := range newPayRecordsTmp {
			if *payRecord.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
				hasLeshuaPay = true
			}
		}
		if hasLeshuaPay {
			newPayBill.Status = util.Ptr(string(_const.V2_PAY_BILL_STATUS_WAIT))
		}

		newPayBills = append(newPayBills, newPayBill)
		newPayRecords = append(newPayRecords, newPayRecordsTmp...)
	}
	return newOrders, newOps, newOms, newPayBills, newPayRecords, newOrderAndPays, toUpdatePayBills
}

// BuildTreePayBillVOs 构建paybill支付-退款树形结构
func (s *PayServiceImpl) BuildTreePayBillVOs(ctx context.Context, payBillVOs []vo.PayBillVO) []vo.PayBillVO {
	normalPayBillVOs := make([]vo.PayBillVO, 0)
	refundBillIdToPayBillVOsMap := make(map[string][]vo.PayBillVO)
	for _, payBillVO := range payBillVOs {
		if payBillVO.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL {
			normalPayBillVOs = append(normalPayBillVOs, payBillVO)
		} else {
			refundBillIdToPayBillVOsMap[payBillVO.BillId] = append(refundBillIdToPayBillVOsMap[payBillVO.BillId], payBillVO)
		}
	}
	for index, payBillVO := range normalPayBillVOs {
		payBillVO.RefundPayBillVOs = refundBillIdToPayBillVOsMap[payBillVO.BillId]
		normalPayBillVOs[index] = payBillVO
	}
	return normalPayBillVOs
}

// MergeOrderVOs 合并退款订单 payamount 和 quantity 需要合并,其他保持不变
func (s *PayServiceImpl) MergePayBillVOs(ctx context.Context, treePayBillVOs []vo.PayBillVO) []vo.PayBillVO {
	rPayBillVOs := make([]vo.PayBillVO, 0)
	refundBillIdToPayBillVOsMap := make(map[string][]vo.PayBillVO)
	for _, payBillVO := range treePayBillVOs {
		if payBillVO.Direction == _const.V2_PAY_BILL_DIRECTION_NORMAL {
			refundBillIdToPayBillVOsMap[payBillVO.BillPid] = append(refundBillIdToPayBillVOsMap[payBillVO.BillPid], payBillVO)
		}
	}
	for _, srcPayBillVO := range treePayBillVOs {
		newPayBillVO := util.DeepCopy(srcPayBillVO)
		refundPayBillVOs := refundBillIdToPayBillVOsMap[srcPayBillVO.BillId]
		if len(refundPayBillVOs) > 0 {
			for _, refundPayBillVO := range refundPayBillVOs {
				newPayBillVO.ShouldFee = refundPayBillVO.ShouldFee
				newPayBillVO.TotalFee -= refundPayBillVO.TotalFee
			}
		}
		// 完全退款的账单，则跳过
		if newPayBillVO.TotalFee <= 0 {
			continue
		}
		rPayBillVOs = append(rPayBillVOs, newPayBillVO)
	}
	return rPayBillVOs
}

func (s *PayServiceImpl) MergePayRecordVOs(ctx context.Context, payBillVO vo.PayBillVO) []vo.PayRecordVO {
	rPayRecordVOs := make([]vo.PayRecordVO, 0)
	normalPayRecordVOs := payBillVO.PayRecordVOs
	refundPayRecordVOs := make([]vo.PayRecordVO, 0)
	for _, refundPayBillVO := range payBillVO.RefundPayBillVOs {
		refundPayRecordVOs = append(refundPayRecordVOs, refundPayBillVO.PayRecordVOs...)
	}
	for _, payRecordVO := range normalPayRecordVOs {
		payType := payRecordVO.PayType
		for _, refundPayRecordVO := range refundPayRecordVOs {
			if refundPayRecordVO.PayType == payType {
				payRecordVO.TotalFee -= refundPayRecordVO.TotalFee
				payRecordVO.PrincipalAmount -= refundPayRecordVO.PrincipalAmount
				payRecordVO.MemberRoomBonusAmount -= refundPayRecordVO.MemberRoomBonusAmount
				payRecordVO.MemberGoodsBonusAmount -= refundPayRecordVO.MemberGoodsBonusAmount
				payRecordVO.MemberCommonBonusAmount -= refundPayRecordVO.MemberCommonBonusAmount
			}
		}
		// 如果退款后，金额大于0，则保留，否则认为该方式已经完全退款
		if payRecordVO.TotalFee > 0 {
			rPayRecordVOs = append(rPayRecordVOs, payRecordVO)
		}
	}
	return rPayRecordVOs
}

func (s *PayServiceImpl) GenRefundPayRecordPOs(ctx context.Context, venueId string, newPayBillId string, payRecordVOs []vo.PayRecordVO) []po.PayRecord {
	rPayRecords := make([]po.PayRecord, 0)
	for _, payRecordVO := range payRecordVOs {
		newPayRecordId := util.GetPayId(venueId)
		payRecordPO := s.payRecordRepo.ConvertToPayRecord(ctx, payRecordVO)
		payRecordPO.Id = nil
		payRecordPO.BillId = &newPayBillId
		payRecordPO.PayId = &newPayRecordId
		payRecordPO.PayPid = &payRecordVO.PayId
		payRecordPO.Ctime = nil
		payRecordPO.Utime = nil
		payRecordPO.State = nil
		payRecordPO.Version = nil
		if payRecordVO.PayType == _const.PAY_TYPE_LESHUA_BSHOWQR {
			payRecordPO.Status = util.Ptr(string(_const.V2_PAY_RECORD_STATUS_WAIT))
		}
		rPayRecords = append(rPayRecords, payRecordPO)
	}
	return rPayRecords
}
