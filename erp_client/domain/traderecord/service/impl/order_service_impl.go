package impl

import (
	"context"
	"encoding/json"
	"fmt"
	_const "voderpltvv/const"
	"voderpltvv/erp_client/api/vo"
	nsOrderroomplan "voderpltvv/erp_client/domain/valueobject/business/order_roomplan/service"
	nsOrderproduct "voderpltvv/erp_client/domain/valueobject/business/orderproduct/service"
	"voderpltvv/erp_client/domain/valueobject/business/price_plan/service"

	"voderpltvv/erp_client/domain/traderecord/repository"
	productRepoPkg "voderpltvv/erp_client/domain/valueobject/business/product/repository"
	productPackageRepoPkg "voderpltvv/erp_client/domain/valueobject/business/product_package/repository"
	productPackageTypeRepoPkg "voderpltvv/erp_client/domain/valueobject/business/product_package_type/repository"
	productTypeRepoPkg "voderpltvv/erp_client/domain/valueobject/business/product_type/repository"
	roomRepoPkg "voderpltvv/erp_client/domain/valueobject/business/room/repository"
	sessionRepoPkg "voderpltvv/erp_client/domain/valueobject/business/session/repository"
	venueRepoPkg "voderpltvv/erp_client/domain/valueobject/business/venue/repository"
	venuePayTypeSettingRepoPkg "voderpltvv/erp_client/domain/valueobject/business/venue_pay_type_setting/repository"
	managentVo "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
)

// OrderServiceImpl 订单服务实现
type OrderServiceImpl struct {
	orderRepo               repository.OrderRepository
	orderPricePlanRepo      repository.OrderPricePlanRepository
	payBillRepo             repository.PayBillRepository
	pricePlanService        service.Service
	orderProductService     nsOrderproduct.Service
	orderRoomPlanService    nsOrderroomplan.Service
	orderAndPayRepo         repository.OrderAndPayRepository
	productRepo             productRepoPkg.Repository
	productTypeRepo         productTypeRepoPkg.Repository
	productPackageRepo      productPackageRepoPkg.Repository
	productPackageTypeRepo  productPackageTypeRepoPkg.Repository
	payRecordRepo           repository.PayRecordRepository
	sessionRepo             sessionRepoPkg.Repository
	roomRepo                roomRepoPkg.Repository
	memberCardVenueRepo     repository.MemberCardVenueRepository
	memberCardRepo          repository.MemberCardRepository
	memberCardOperationRepo repository.MemberCardOperationRepository
	venueRepo               venueRepoPkg.Repository
	venuePayTypeSettingRepo venuePayTypeSettingRepoPkg.Repository
	memberCardConsumeRepo   repository.MemberCardConsumeRepository
}

// NewOrderService 创建订单服务实例
func NewOrderService(
	orderRepo repository.OrderRepository,
	orderPricePlanRepo repository.OrderPricePlanRepository,
	payBillRepo repository.PayBillRepository,
	pricePlanService service.Service,
	orderProductService nsOrderproduct.Service,
	orderRoomPlanService nsOrderroomplan.Service,
	orderAndPayRepo repository.OrderAndPayRepository,
	productRepo productRepoPkg.Repository,
	productTypeRepo productTypeRepoPkg.Repository,
	productPackageRepo productPackageRepoPkg.Repository,
	productPackageTypeRepo productPackageTypeRepoPkg.Repository,
	payRecordRepo repository.PayRecordRepository,
	sessionRepo sessionRepoPkg.Repository,
	roomRepo roomRepoPkg.Repository,
	memberCardVenueRepo repository.MemberCardVenueRepository,
	memberCardRepo repository.MemberCardRepository,
	memberCardOperationRepo repository.MemberCardOperationRepository,
	venueRepo venueRepoPkg.Repository,
	venuePayTypeSettingRepo venuePayTypeSettingRepoPkg.Repository,
	memberCardConsumeRepo repository.MemberCardConsumeRepository,
) *OrderServiceImpl {
	return &OrderServiceImpl{
		orderRepo:               orderRepo,
		orderPricePlanRepo:      orderPricePlanRepo,
		payBillRepo:             payBillRepo,
		pricePlanService:        pricePlanService,
		orderProductService:     orderProductService,
		orderRoomPlanService:    orderRoomPlanService,
		orderAndPayRepo:         orderAndPayRepo,
		productRepo:             productRepo,
		productTypeRepo:         productTypeRepo,
		productPackageRepo:      productPackageRepo,
		productPackageTypeRepo:  productPackageTypeRepo,
		payRecordRepo:           payRecordRepo,
		sessionRepo:             sessionRepo,
		roomRepo:                roomRepo,
		memberCardVenueRepo:     memberCardVenueRepo,
		memberCardRepo:          memberCardRepo,
		memberCardOperationRepo: memberCardOperationRepo,
		venueRepo:               venueRepo,
		venuePayTypeSettingRepo: venuePayTypeSettingRepo,
		memberCardConsumeRepo:   memberCardConsumeRepo,
	}
}

// FindAllOrder 查询所有订单
func (s *OrderServiceImpl) FindAllOrder(ctx context.Context, venueId, sessionId string) (*[]po.Order, error) {
	return s.orderRepo.FindAllOrder(ctx, venueId, sessionId)
}

// GetOrderByOrderNO 查询订单
func (s *OrderServiceImpl) GetOrderByOrderNO(ctx context.Context, venueId, orderNO string) (po.Order, error) {
	return s.orderRepo.GetOrderByOrderNO(ctx, venueId, orderNO)
}

// GenerateOrderInfo 生成订单基础信息
func (s *OrderServiceImpl) GenerateOrderInfo(ctx context.Context, venueId string) (*vo.OrderInfoVO, error) {
	if venueId == "" {
		return nil, fmt.Errorf("venueId is empty")
	}

	// 4. 生成订单号和场次ID
	orderNo := util.GetOrderNo(venueId)
	sessionId := util.GetSessionId(venueId)

	return &vo.OrderInfoVO{
		OrderNo:   orderNo,
		SessionId: sessionId,
	}, nil
}

// PrepareOrders 准备订单数据
func (s *OrderServiceImpl) PrepareOrders(ctx context.Context, reqDto *vo.OrderOpenReqVO,
	orderInfo *vo.OrderInfoVO) (*vo.OrderPrepareResultVO, error) {

	if reqDto == nil || orderInfo == nil {
		return nil, fmt.Errorf("reqDto or orderInfo is nil")
	}

	toAddAllOrders := make([]po.Order, 0)

	// 1. 创建Order对象-主开台订单
	toAddOrder4RoomPlan := &po.Order{
		VenueId:       &reqDto.VenueId,
		RoomId:        &reqDto.RoomId,
		SessionId:     &orderInfo.SessionId,
		OrderNo:       &orderInfo.OrderNo,
		EmployeeId:    &reqDto.EmployeeId,
		MinimumCharge: &reqDto.MinimumCharge,
		Status:        util.GetItPtr(_const.ORDER_STATUS_UNPAID),
		Mark:          util.GetItPtr(_const.ORDER_MARK_OPENING),
		Type:          util.GetItPtr(_const.ORDER_TYPE_ROOMPLAN),
	}
	toAddAllOrders = append(toAddAllOrders, *toAddOrder4RoomPlan)

	// 2. 处理OrderPricePlan
	var pricePlan *po.PricePlan
	var pricePlanName, consumptionMode string
	var lastMinimumCharge int64
	if reqDto.PricePlanId != "" {
		// var err error
		// pricePlan, err = s.pricePlanService.FindPricePlanById(ctx, reqDto.PricePlanId)
		// if err != nil {
		// 	return nil, fmt.Errorf("failed to find price plan: %v", err)
		// }
		if pricePlan != nil {
			pricePlanName = *pricePlan.Name
			consumptionMode = *pricePlan.ConsumptionMode
			lastMinimumCharge = *pricePlan.MinimumCharge
		}
	}
	// 优先取全局设置的低消，其次取主套餐的低消
	if reqDto.MinimumCharge > 0 {
		lastMinimumCharge = reqDto.MinimumCharge
	}

	toAddOrderPricePlan := &po.OrderPricePlan{
		VenueId:            &reqDto.VenueId,
		RoomId:             &reqDto.RoomId,
		SessionId:          &orderInfo.SessionId,
		OrderNo:            &orderInfo.OrderNo,
		PricePlanId:        &reqDto.PricePlanId,
		PricePlanName:      &pricePlanName,
		ConsumptionMode:    &consumptionMode,
		SelectedAreaId:     &reqDto.SelectedAreaId,
		SelectedRoomTypeId: &reqDto.SelectedRoomTypeId,
		BuyMinute:          &reqDto.BuyMinute,
		TimeChargeType:     &reqDto.TimeChargeType,
		TimeChargeMode:     &reqDto.TimeChargeMode,
		MinimumCharge:      &lastMinimumCharge,
		StartTime:          &reqDto.StartTime,
		EndTime:            &reqDto.EndTime,
	}

	// 3. 构造OrderRoomPlans
	toAddOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, orderRoomPlanVO := range reqDto.OrderRoomPlanVOS {
		orderRoomPlan := po.OrderRoomPlan{
			VenueId:       &reqDto.VenueId,
			RoomId:        &reqDto.RoomId,
			SessionId:     &orderInfo.SessionId,
			OrderNo:       &orderInfo.OrderNo,
			RoomName:      &orderRoomPlanVO.RoomName,
			PricePlanId:   &orderRoomPlanVO.PricePlanId,
			PricePlanName: &orderRoomPlanVO.PricePlanName,
			StartTime:     &orderRoomPlanVO.StartTime,
			EndTime:       &orderRoomPlanVO.EndTime,
			Duration:      &orderRoomPlanVO.Duration,
			PayAmount:     &orderRoomPlanVO.PayAmount,
		}
		toAddOrderRoomPlans = append(toAddOrderRoomPlans, orderRoomPlan)
	}

	// 4. 构造OrderProduct
	toAddOrderProducts := make([]po.OrderProduct, 0)

	// 4.1 商品信息-套餐内
	inOrderProductSumFee := int64(0)
	// inOrderProductSumOriginalAmount := int64(0)
	subInOrderNo := util.GetOrderNo(reqDto.VenueId)

	for _, orderProductVO := range reqDto.InOrderProductInfos {
		orderProduct := po.OrderProduct{
			VenueId:       &reqDto.VenueId,
			RoomId:        &reqDto.RoomId,
			SessionId:     &orderInfo.SessionId,
			OrderNo:       &subInOrderNo,
			ProductId:     &orderProductVO.ProductId,
			ProductName:   &orderProductVO.ProductName,
			Flavors:       &orderProductVO.Flavors,
			Unit:          &orderProductVO.Unit,
			Quantity:      &orderProductVO.Quantity,
			OriginalPrice: &orderProductVO.OriginalPrice,
			PayAmount:     &orderProductVO.PayAmount,
			Mark:          &orderProductVO.Mark,
			Src:           util.GetItPtr("in"),
		}
		toAddOrderProducts = append(toAddOrderProducts, orderProduct)
		inOrderProductSumFee += *orderProduct.PayAmount
	}

	if len(reqDto.InOrderProductInfos) > 0 {
		toAddOrder4InProduct := &po.Order{
			VenueId:       &reqDto.VenueId,
			RoomId:        &reqDto.RoomId,
			SessionId:     &orderInfo.SessionId,
			OrderNo:       &subInOrderNo,
			EmployeeId:    &reqDto.EmployeeId,
			MinimumCharge: &reqDto.MinimumCharge,
			Status:        util.GetItPtr(_const.ORDER_STATUS_UNPAID),
			Mark:          util.GetItPtr(_const.ORDER_MARK_OPENING),
			Type:          util.GetItPtr(_const.ORDER_TYPE_PRODUCT),
		}
		toAddAllOrders = append(toAddAllOrders, *toAddOrder4InProduct)
	}

	// 4.2 商品信息-套餐外
	outOrderProductSumFee := int64(0)
	subOrderNo := util.GetOrderNo(reqDto.VenueId)

	for _, orderProductVO := range reqDto.OutOrderProductInfos {
		orderProduct := po.OrderProduct{
			VenueId:       &reqDto.VenueId,
			RoomId:        &reqDto.RoomId,
			SessionId:     &orderInfo.SessionId,
			OrderNo:       &subOrderNo,
			ProductId:     &orderProductVO.ProductId,
			ProductName:   &orderProductVO.ProductName,
			Flavors:       &orderProductVO.Flavors,
			Unit:          &orderProductVO.Unit,
			Quantity:      &orderProductVO.Quantity,
			OriginalPrice: &orderProductVO.OriginalPrice,
			PayAmount:     &orderProductVO.PayAmount,
			Mark:          &orderProductVO.Mark,
			Src:           util.GetItPtr("out"),
		}
		toAddOrderProducts = append(toAddOrderProducts, orderProduct)
		outOrderProductSumFee += *orderProduct.PayAmount
	}

	if len(reqDto.OutOrderProductInfos) > 0 {
		subOrder := po.Order{
			VenueId:       &reqDto.VenueId,
			RoomId:        &reqDto.RoomId,
			SessionId:     &orderInfo.SessionId,
			OrderNo:       &subOrderNo,
			EmployeeId:    &reqDto.EmployeeId,
			MinimumCharge: &reqDto.MinimumCharge,
			Status:        util.GetItPtr(_const.ORDER_STATUS_UNPAID),
			Mark:          util.GetItPtr(_const.ORDER_MARK_OPENING),
			Type:          util.GetItPtr(_const.ORDER_TYPE_PRODUCT),
		}
		toAddAllOrders = append(toAddAllOrders, subOrder)
	}

	// 5. 计算费用
	totalAmount, unpaidAmount, supermarketAmount, roomAmount, lastMinimumChargeInSession := s.CalculateOrderFee(ctx, reqDto.VenueId, "", &toAddAllOrders)

	// 6. 创建Session
	toAddSession := &po.Session{
		VenueId:            &reqDto.VenueId,
		RoomId:             &reqDto.RoomId,
		SessionId:          &orderInfo.SessionId,
		StartTime:          &reqDto.StartTime,
		MinConsume:         &lastMinimumChargeInSession,
		Status:             util.GetItPtr(_const.ORDER_STATUS_UNPAID),
		RoomFee:            &roomAmount,
		SupermarketFee:     &supermarketAmount,
		TotalFee:           &totalAmount,
		UnpaidAmount:       &unpaidAmount,
		IsOpenTableSettled: &reqDto.IsOpenTableSettled,
		Info:               util.GetItPtr(_const.ORDER_MARK_OPENING),
	}

	if reqDto.EndTime > 0 {
		toAddSession.EndTime = &reqDto.EndTime
		// duration := reqDto.EndTime - reqDto.StartTime
		// toAddSession.Duration = &duration
	}

	// 7. 更新房间状态
	toUpdateRoom := &po.Room{
		Id:        &reqDto.RoomId,
		SessionId: &orderInfo.SessionId,
		Status:    util.GetItPtr(_const.ROOM_STATUS_IN_USE),
	}

	// 转换为RoomVO
	roomVO := &vo.RoomVO{
		Id:        *toUpdateRoom.Id,
		SessionId: *toUpdateRoom.SessionId,
		Status:    *toUpdateRoom.Status,
	}

	return &vo.OrderPrepareResultVO{
		Session:        toAddSession,
		Orders:         &toAddAllOrders,
		OrderProducts:  &toAddOrderProducts,
		OrderPricePlan: toAddOrderPricePlan,
		OrderRoomPlans: &toAddOrderRoomPlans,
		Room:           roomVO,
	}, nil
}

// CreateOrdersWithTransaction 在事务中创建订单相关数据
func (s *OrderServiceImpl) CreateOrdersWithTransaction(ctx context.Context, session *po.Session,
	orders *[]po.Order, orderProducts *[]po.OrderProduct,
	orderPricePlan *po.OrderPricePlan, orderRoomPlans *[]po.OrderRoomPlan,
	room *po.Room) error {

	if session == nil || orders == nil || orderProducts == nil ||
		orderPricePlan == nil || orderRoomPlans == nil || room == nil {
		return fmt.Errorf("invalid parameters: some parameters are nil")
	}

	// 调用仓储层的事务方法
	return s.orderRepo.CreateOrdersWithTransaction(ctx, session, orders,
		orderProducts, orderPricePlan, orderRoomPlans, room)
}

// CalculateOrderFee 计算订单费用
func (s *OrderServiceImpl) CalculateOrderFee(ctx context.Context, venueId string, sessionId string, toAddOrders *[]po.Order) (totalAmount, unpaidAmount, supermarketAmount, roomAmount int64, lastMinimumCharge int64) {
	payBills := &[]po.PayBill{}
	orders := &[]po.Order{}
	orderPricePlans := &[]po.OrderPricePlan{}

	if sessionId != "" {
		var err error
		orderPricePlans, err = s.orderPricePlanRepo.FindAllOrderPricePlan(ctx, venueId, sessionId)
		if err != nil {
			return 0, 0, 0, 0, 0
		}

		payBills, err = s.payBillRepo.FindAllPayBill(ctx, venueId, sessionId, []string{_const.PAY_STATUS_PAID, _const.PAY_STATUS_REFUND})
		if err != nil {
			return 0, 0, 0, 0, 0
		}

		orders, err = s.orderRepo.FindAllOrder(ctx, venueId, sessionId)
		if err != nil {
			return 0, 0, 0, 0, 0
		}
	}

	if toAddOrders != nil {
		*orders = append(*orders, *toAddOrders...)
	}

	// 1. 计算最低消费
	lastMinimumCharge = int64(0)
	for _, pricePlan := range *orderPricePlans {
		if pricePlan.MinimumCharge != nil {
			lastMinimumCharge += *pricePlan.MinimumCharge
		}
	}

	// // 2. 计算总房费、总商品费
	// for _, order := range *orders {
	// 	// 计算总房费
	// 	if order.Type != nil && *order.Type == _const.ORDER_TYPE_ROOMPLAN {
	// 		if order.Status != nil && *order.Status == _const.ORDER_STATUS_REFUNDED {
	// 			roomAmount -= *order.PayAmount
	// 		} else {
	// 			roomAmount += *order.PayAmount
	// 		}
	// 	} else {
	// 		// 计算总商品费
	// 		if order.Status != nil && *order.Status == _const.ORDER_STATUS_REFUNDED {
	// 			supermarketAmount -= *order.PayAmount
	// 		} else {
	// 			supermarketAmount += *order.PayAmount
	// 		}
	// 	}
	// }

	// 3. 计算总金额
	if supermarketAmount > lastMinimumCharge {
		totalAmount = roomAmount + supermarketAmount
	} else {
		totalAmount = roomAmount + lastMinimumCharge
	}

	// 4. 计算已支付金额
	var paidAmount int64
	if payBills != nil {
		for _, bill := range *payBills {
			if *bill.Status == _const.PAY_STATUS_PAID {
				paidAmount += *bill.TotalFee
			} else if *bill.Status == _const.PAY_STATUS_REFUND {
				paidAmount -= *bill.TotalFee
			}
		}
	}

	// 5. 计算未支付金额
	unpaidAmount = totalAmount - paidAmount

	return totalAmount, unpaidAmount, supermarketAmount, roomAmount, lastMinimumCharge
}

// ConvertToOrderVO 转换为订单VO
func (s *OrderServiceImpl) ConvertToOrderVO(ctx context.Context, order po.Order) managentVo.OrderVO {
	return s.orderRepo.ConvertToOrderVO(ctx, order)
}

// ConvertToOrderPO 转换为订单PO
func (s *OrderServiceImpl) ConvertToOrderPO(ctx context.Context, orderVO managentVo.OrderVO) po.Order {
	return s.orderRepo.ConvertToOrderPO(ctx, orderVO)
}

// TrimIdToAddOrderPO 转换为订单PO
func (s *OrderServiceImpl) TrimIdToAddOrderPO(ctx context.Context, orderPO po.Order) po.Order {
	newOrderPO := util.DeepCopy(orderPO)
	newOrderPO.Id = nil
	newOrderPO.Ctime = nil
	newOrderPO.Utime = nil
	newOrderPO.State = nil
	newOrderPO.Version = nil
	return newOrderPO
}

// 保存 开台-后付
func (s *OrderServiceImpl) SaveOrderOpenInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	return s.orderRepo.SaveOrderOpenInfo(ctx, orders, orderProducts, orderRoomPlans, session, room)
}

// 保存 开台-立结
func (s *OrderServiceImpl) SaveOrderOpenPayInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room, payBill po.PayBill, orderAndPays []po.OrderAndPay, payRecords []po.PayRecord) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	return s.orderRepo.SaveOrderOpenPayInfo(ctx, orders, orderProducts, orderRoomPlans, session, room, payBill, orderAndPays, payRecords)
}

// 保存 续台-后付
func (s *OrderServiceImpl) SaveOrderOpenContinueInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	return s.orderRepo.SaveOrderOpenContinueInfo(ctx, orders, orderProducts, orderRoomPlans, session, room)
}

// 保存 续台-立结
func (s *OrderServiceImpl) SaveOrderOpenContinuePayInfo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan, session po.Session, room po.Room, payBill po.PayBill, orderAndPays []po.OrderAndPay, payRecords []po.PayRecord) (po.Session, []po.Order, []po.OrderProduct, []po.OrderRoomPlan, error) {
	return s.orderRepo.SaveOrderOpenContinuePayInfo(ctx, orders, orderProducts, orderRoomPlans, session, room, payBill, orderAndPays, payRecords)
}

// 保存 点单-后付
func (s *OrderServiceImpl) SaveOrderAdditionalInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toAddOrder po.Order, toUpdateOrderProducts []po.OrderProduct) error {
	return s.orderRepo.SaveOrderAdditionalInfo(ctx, toUpdateRooms, toUpdateSession, toAddOrder, toUpdateOrderProducts)
}

// 保存 点单-立结
func (s *OrderServiceImpl) SaveOrderAdditionalPayInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toAddOrder po.Order, toUpdateOrderProducts []po.OrderProduct, toAddOrderAndPays []po.OrderAndPay, toAddPayRecords []po.PayRecord, toAddPayBill po.PayBill) error {
	return s.orderRepo.SaveOrderAdditionalPayInfo(ctx, toUpdateRooms, toUpdateSession, toAddOrder, toUpdateOrderProducts, toAddOrderAndPays, toAddPayRecords, toAddPayBill)
}

// 保存 转台-后付
func (s *OrderServiceImpl) SaveTransferRoomInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toUpdateOrders []po.Order, toAddOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan) (po.Session, []po.Room, error) {
	return s.orderRepo.SaveTransferRoomInfo(ctx, toUpdateRooms, toUpdateSession, toUpdateOrders, toAddOrders, newOrderProducts, newOrderRoomPlans)
}

// 保存 转台-后付
func (s *OrderServiceImpl) SaveTransferRoomPayInfo(ctx context.Context, toUpdateRooms []po.Room, toUpdateSession po.Session, toUpdateOrders []po.Order, toAddOrders []po.Order, newOrderProducts []po.OrderProduct, newOrderRoomPlans []po.OrderRoomPlan, toAddPayBill *po.PayBill, toAddPayRecords []po.PayRecord, toAddOrderAndPays []po.OrderAndPay) (po.Session, []po.Room, error) {
	return s.orderRepo.SaveTransferRoomPayInfo(ctx, toUpdateRooms, toUpdateSession, toUpdateOrders, toAddOrders, newOrderProducts, newOrderRoomPlans, toAddPayBill, toAddPayRecords, toAddOrderAndPays)
}

// 保存 赠送时长
func (s *OrderServiceImpl) SaveOrderInfoGiftTime(ctx context.Context, toAddOrder []po.Order, toAddOrderRoomPlans []po.OrderRoomPlan, toUpdateSession po.Session, toAddPayBill []po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddEmployeeGiftRecords []po.EmployeeGiftRecord) error {
	return s.orderRepo.SaveOrderInfoGiftTime(ctx, toAddOrder, toAddOrderRoomPlans, toUpdateSession, toAddPayBill, toAddOrderAndPays, toAddEmployeeGiftRecords)
}

// 保存 赠送商品
func (s *OrderServiceImpl) SaveOrderInfoGiftProduct(ctx context.Context, toAddOrder []po.Order, toAddOrderProducts []po.OrderProduct, toAddPayBill []po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddEmployeeGiftRecords []po.EmployeeGiftRecord) error {
	return s.orderRepo.SaveOrderInfoGiftProduct(ctx, toAddOrder, toAddOrderProducts, toAddPayBill, toAddOrderAndPays, toAddEmployeeGiftRecords)
}

// 保存 重开
func (s *OrderServiceImpl) SaveInfoOrderReopen(ctx context.Context, toUpdateRooms []po.Room, toUpdateSessions []po.Session) ([]po.Room, error) {
	return s.orderRepo.SaveInfoOrderReopen(ctx, toUpdateRooms, toUpdateSessions)
}

// 保存 结束时长消费
func (s *OrderServiceImpl) SaveInfoEndTimeConsume(ctx context.Context, toUpdateOrderRoomPlans []po.OrderRoomPlan, toAddOrderRoomPlans []po.OrderRoomPlan, toUpdateSessions []po.Session, toUpdateRooms []po.Room) (po.Session, []po.Room, error) {
	return s.orderRepo.SaveInfoEndTimeConsume(ctx, toUpdateOrderRoomPlans, toAddOrderRoomPlans, toUpdateSessions, toUpdateRooms)
}

// SaveOrderPayInfoPre 保存订单支付信息预处理
func (s *OrderServiceImpl) SaveOrderPayInfoPre(ctx context.Context, toUpdateSession po.Session, toUpdateModifyOrderProducts []po.OrderProduct, toUpdateModifyOrderRoomPlans []po.OrderRoomPlan, toAddPayBill po.PayBill, toAddOrderAndPays []po.OrderAndPay, toAddPayRecords []po.PayRecord) error {
	return s.orderRepo.SaveOrderPayInfoPre(ctx, toUpdateSession, toUpdateModifyOrderProducts, toUpdateModifyOrderRoomPlans, toAddPayBill, toAddOrderAndPays, toAddPayRecords)
}

// IsMatchAllUnpaidOrder 检查是否存在未支付的订单
func (s *OrderServiceImpl) IsMatchAllUnpaidOrder(ctx context.Context, orderNos []string, venueId string, sessionId string) (unpaidOrderNos []string, err error) {
	return s.orderRepo.IsMatchAllUnpaidOrder(ctx, orderNos, venueId, sessionId)
}

// BuildOrderVo 构建订单VO
func (s *OrderServiceImpl) BuildOrderVo(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan) []managentVo.OrderVO {
	newOrderVOs := make([]managentVo.OrderVO, 0)
	for _, order := range orders {
		newOrderVO := s.ConvertToOrderVO(ctx, order)
		ops := make([]managentVo.OrderProductVO, 0)
		for _, orderProduct := range orderProducts {
			if *orderProduct.OrderNo == *order.OrderNo {
				ops = append(ops, s.orderProductService.ConvertToOrderProductVO(ctx, orderProduct))
			}
		}
		newOrderVO.OrderProductVOs = ops
		oms := make([]managentVo.OrderRoomPlanVO, 0)
		for _, orderRoomPlan := range orderRoomPlans {
			if *orderRoomPlan.OrderNo == *order.OrderNo {
				oms = append(oms, s.orderRoomPlanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
			}
		}
		newOrderVO.OrderRoomPlanVOs = oms
		newOrderVOs = append(newOrderVOs, newOrderVO)
	}
	return newOrderVOs
}

// GetAvailableQuantity 获取订单商品可退数量
func (s *OrderServiceImpl) GetAvailableQuantity(ctx context.Context, orderProducts []po.OrderProduct, orders []po.Order) map[string]int {
	orderProductId2QuantityMap := make(map[string]int)
	orderNo2OrderMap := make(map[string]po.Order)
	for _, order := range orders {
		orderNo2OrderMap[*order.OrderNo] = order
	}
	for _, orderProduct := range orderProducts {
		orderNo := *orderProduct.OrderNo
		order := orderNo2OrderMap[orderNo]
		if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL { // 正常订单
			orderProductId2QuantityMap[*orderProduct.Id] += int(*orderProduct.Quantity)
		} else { // 退款订单
			orderProductId2QuantityMap[*orderProduct.PId] -= int(*orderProduct.Quantity)
		}
	}
	return orderProductId2QuantityMap
}

// BuildRefundOrderAndOrderProduct 构建退款订单和订单商品
func (s *OrderServiceImpl) BuildRefundOrderAndOrderProduct(ctx context.Context, orderPOInfoUnionVO managentVo.OrderPOInfoUnionVO, orderProductVosReq []managentVo.OrderProductVO) []managentVo.RefundOrderInfoGroupVO {
	orderInfoGroups := s.BuildOrderInfoGroupVOs(ctx, orderPOInfoUnionVO, orderProductVosReq)
	return s.GenRefundOrderAndOrderProduct(ctx, orderInfoGroups)
}

// 根据订单信息组构建退款订单和订单商品
func (s *OrderServiceImpl) GenRefundOrderAndOrderProduct(ctx context.Context, orderInfoGroups []managentVo.OrderInfoGroupVO) []managentVo.RefundOrderInfoGroupVO {
	newRefundOrderInfoGroups := make([]managentVo.RefundOrderInfoGroupVO, 0)
	for _, orderInfoGroup := range orderInfoGroups {
		newRefundOrderProducts := make([]po.OrderProduct, 0)
		// 待退款商品  只有Quantity字段是退款数量，其他为父订单信息
		toRefundOrderProducts := orderInfoGroup.OrderProductsToRefund
		// 已经退款订单商品
		orderProductsRefunded := orderInfoGroup.OrderProductsRefund

		// 支付商品Map opid: op
		opId2OrderProductMap := make(map[string]po.OrderProduct)
		for _, orderProduct := range orderInfoGroup.OrderProductsNormal {
			opId2OrderProductMap[*orderProduct.Id] = orderProduct
		}

		// 生成新的订单号
		orderNoNew := util.GetOrderNo(*orderInfoGroup.Order.VenueId)
		for _, toRefundOrderProductMix := range toRefundOrderProducts {
			// toRefundOrderProductMix: 只有Quantity字段是退款数量，其他为父订单信息
			pid := *toRefundOrderProductMix.Id
			orderProductParent := opId2OrderProductMap[pid]
			// 新的退款商品clone原商品
			orderProductNew := util.DeepCopy(toRefundOrderProductMix)
			// 更新退款商品信息
			orderProductNew.Id = nil
			orderProductNew.PId = &pid
			orderProductNew.OrderNo = &orderNoNew
			orderProductNew.OrderProductDiscount = nil
			orderProductNew.MemberDiscount = nil
			orderProductNew.PayProductDiscount = nil
			orderProductNew.Ctime = nil
			orderProductNew.Utime = nil
			orderProductNew.Version = nil
			// 计算订单商品支付金额
			orderProductNew.PayAmount = s.CalcOrderProductPayAmount(ctx, orderProductsRefunded, toRefundOrderProductMix, orderProductParent)

			newRefundOrderProducts = append(newRefundOrderProducts, orderProductNew)
		}

		// 生成退款订单
		newRefundOrder := util.DeepCopy(orderInfoGroup.Order)
		newRefundOrder.Id = nil
		newRefundOrder.OrderNo = &orderNoNew
		newRefundOrder.POrderNo = orderInfoGroup.Order.OrderNo
		newRefundOrder.Direction = util.GetItPtr(_const.V2_ORDER_DIRECTION_REFUND)
		newRefundOrder.Status = util.GetItPtr(_const.ORDER_STATUS_UNPAID)
		newRefundOrder.Ctime = nil
		newRefundOrder.Utime = nil
		newRefundOrder.Version = nil

		newRefundOrderInfoGroups = append(newRefundOrderInfoGroups, managentVo.RefundOrderInfoGroupVO{
			Order:                     newRefundOrder,
			POrder:                    orderInfoGroup.Order,
			POrdersRefund:             orderInfoGroup.OrdersRefund,
			POrderNomalOrderProducts:  orderInfoGroup.OrderProductsNormal,
			POrderRefundOrderProducts: orderProductsRefunded,
			POrderRefundPayBills:      orderInfoGroup.RefundPayBills,
			POrderNormalPayRecords:    orderInfoGroup.NormalPayRecords,
			POrderRefundPayRecords:    orderInfoGroup.RefundPayRecords,
			POrderMergedPayRecords:    orderInfoGroup.MergedPayRecords,
			POrderPayBill:             orderInfoGroup.PayBill,
			OrderProductsRefund:       newRefundOrderProducts,
		})
	}
	return newRefundOrderInfoGroups
}

// CalcOrderProductPayAmount 计算订单商品支付金额
func (s *OrderServiceImpl) CalcOrderProductPayAmount(ctx context.Context, orderProductsRefund []po.OrderProduct, toRefundOrderProductMix po.OrderProduct, orderProductParent po.OrderProduct) *int64 {
	totalQuantity := *orderProductParent.Quantity
	totalAmount := *orderProductParent.PayAmount
	refundNum := *toRefundOrderProductMix.Quantity

	hasRefundQuantity := int64(0)
	hasRefundAmount := int64(0)
	for _, orderProductRefund := range orderProductsRefund {
		if *orderProductRefund.PId == *toRefundOrderProductMix.Id {
			hasRefundQuantity += *orderProductRefund.Quantity
			hasRefundAmount += *orderProductRefund.PayAmount
		}
	}

	canRefundQuantity := totalQuantity - hasRefundQuantity
	canRefundAmount := totalAmount - hasRefundAmount

	var payAmount int64
	if canRefundQuantity-refundNum > 0 { // 退完商品之后还有剩余可退的，payAmount = 单价*数量
		payAmount = totalAmount / totalQuantity * refundNum
	} else { // 退完商品之后没有剩余可退的，payAmount = 剩余可退款金额
		payAmount = canRefundAmount
	}
	return &payAmount
}

// BuildOrderInfoGroupVOs 获取订单信息组
func (s *OrderServiceImpl) BuildOrderInfoGroupVOs(ctx context.Context, orderPOInfoUnionVO managentVo.OrderPOInfoUnionVO, orderProductVosReq []managentVo.OrderProductVO) []managentVo.OrderInfoGroupVO {
	ordersAll, orderProductsAll := orderPOInfoUnionVO.Orders, orderPOInfoUnionVO.OrderProducts

	// billId: payBill
	billIdToPayBillMap := make(map[string]po.PayBill)
	for _, payBill := range orderPOInfoUnionVO.PayBills {
		billIdToPayBillMap[*payBill.BillId] = payBill
	}

	orderInfoGroups := make([]managentVo.OrderInfoGroupVO, 0)
	// 支付/退款 订单
	ordersNormal := make([]po.Order, 0)
	ordersRefund := make([]po.Order, 0)
	// 订单商品 orderProductId: orderProduct
	id2OrderProductAllMap := make(map[string]po.OrderProduct)
	for _, orderProduct := range orderProductsAll {
		id2OrderProductAllMap[*orderProduct.Id] = orderProduct
	}
	// 填充支付订单和退款订单
	for _, order := range ordersAll {
		if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			ordersNormal = append(ordersNormal, order)
		} else {
			ordersRefund = append(ordersRefund, order)
		}
	}

	// 按支付订单填充 商品组，退款商品组，待退款商品组, 退款订单组
	for _, orderNomalTmp := range ordersNormal {
		orderProductsNormal := make([]po.OrderProduct, 0)
		orderProductsRefund := make([]po.OrderProduct, 0)
		// 设置支付订单对应的 商品组 OrderProductsNormal
		for _, orderProduct := range orderProductsAll {
			if *orderProduct.OrderNo == *orderNomalTmp.OrderNo {
				orderProductsNormal = append(orderProductsNormal, orderProduct)
			}
		}
		// 设置 退款订单对应的商品组 OrderProductsRefund
		for _, orderRefundTmp := range ordersRefund {
			// 匹配父订单
			if *orderRefundTmp.POrderNo == *orderNomalTmp.OrderNo {
				// 查找退款订单对应的商品组
				for _, orderProductTmp := range orderProductsAll {
					// 匹配对应退款订单的商品组
					if *orderProductTmp.OrderNo == *orderRefundTmp.OrderNo {
						orderProductsRefund = append(orderProductsRefund, orderProductTmp)
					}
				}
			}
		}
		// 设置 待退款商品组 OrderProductsToRefund
		orderProductsToRefund := make([]po.OrderProduct, 0)
		for _, orderProductVOReq := range orderProductVosReq {
			findedOrderProductParent := id2OrderProductAllMap[orderProductVOReq.Id]
			// 匹配父订单
			if *findedOrderProductParent.OrderNo == *orderNomalTmp.OrderNo {
				refundNum := orderProductVOReq.Quantity
				findedOrderProductParent.Quantity = &refundNum
				orderProductsToRefund = append(orderProductsToRefund, findedOrderProductParent)
			}
		}
		// 设置订单的退款订单组 OrdersRefund
		ordersSubRefund := make([]po.Order, 0)
		for _, orderRefund := range ordersRefund {
			if *orderRefund.POrderNo == *orderNomalTmp.OrderNo {
				ordersSubRefund = append(ordersSubRefund, orderRefund)
			}
		}
		// 过滤本次 不需要退款的订单组
		if len(orderProductsToRefund) <= 0 {
			continue
		}
		// 设置paybill 通过中间表orderAndPay寻找billid
		payBill := po.PayBill{}
		for _, orderAndPay := range orderPOInfoUnionVO.OrderAndPays {
			// 匹配 orderAndPay
			if *orderAndPay.OrderNo == *orderNomalTmp.OrderNo {
				billId := *orderAndPay.BillId
				payBillTmp, exits := billIdToPayBillMap[billId]
				// 匹配paybill 已支付
				if exits && *payBillTmp.Status == _const.V2_PAY_BILL_STATUS_PAID {
					payBill = payBillTmp
				}
			}
		}
		refundPayBills := make([]po.PayBill, 0)
		normalPayRecords := make([]po.PayRecord, 0)
		refundPayRecords := make([]po.PayRecord, 0)
		mergedPayRecords := make([]po.PayRecord, 0)
		if payBill.Id != nil { // 存在支付信息

			// 匹配退款收款单 RefundPayBills
			for _, pbTmp := range orderPOInfoUnionVO.PayBills {
				if *pbTmp.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND && *pbTmp.BillPid == *payBill.BillId {
					refundPayBills = append(refundPayBills, pbTmp)
				}
			}

			// 匹配收款单对应的支付记录 NormalPayRecordVOs
			for _, recordTmp := range orderPOInfoUnionVO.PayRecords {
				if *recordTmp.BillId == *payBill.BillId {
					normalPayRecords = append(normalPayRecords, recordTmp)
				}
			}

			// 匹配退款收款单对应的支付记录 RefundPayRecordVOs 通过退款收款单的billId查找
			for _, refundPayBillTmp := range refundPayBills {
				for _, recordTmp := range orderPOInfoUnionVO.PayRecords {
					if *recordTmp.BillId == *refundPayBillTmp.BillId {
						refundPayRecords = append(refundPayRecords, recordTmp)
					}
				}
			}

			// 考虑退款的时候可以强制按现金退款，需要考虑
			for _, normalPayRecordVOTmp := range normalPayRecords {
				mergedPayRecordVO := util.DeepCopy(normalPayRecordVOTmp)
				// 查找退款支付记录
				for _, refundPayRecordVOTmp := range refundPayRecords {
					if *refundPayRecordVOTmp.PayPid == *normalPayRecordVOTmp.PayId {
						*mergedPayRecordVO.TotalFee -= *refundPayRecordVOTmp.TotalFee
					}
				}
				mergedPayRecords = append(mergedPayRecords, mergedPayRecordVO)
			}

		}

		// 填充订单信息组
		orderInfoGroup := managentVo.OrderInfoGroupVO{
			Order:                 util.DeepCopy(orderNomalTmp),
			OrdersRefund:          ordersSubRefund,
			OrderProductsNormal:   orderProductsNormal,
			OrderProductsRefund:   orderProductsRefund,
			OrderProductsToRefund: orderProductsToRefund,
			PayBill:               payBill,
			RefundPayBills:        refundPayBills,
			NormalPayRecords:      normalPayRecords,
			RefundPayRecords:      refundPayRecords,
			MergedPayRecords:      mergedPayRecords,
		}
		orderInfoGroups = append(orderInfoGroups, orderInfoGroup)
	}

	return orderInfoGroups
}

func (s *OrderServiceImpl) BuildNewOpOmToUpdate(ctx context.Context, newOps []managentVo.OrderProductVO, newOms []managentVo.OrderRoomPlanVO) ([]po.OrderProduct, []po.OrderRoomPlan) {
	toUpdateModifyOrderProducts := make([]po.OrderProduct, 0)
	toUpdateModifyOrderRoomPlans := make([]po.OrderRoomPlan, 0)
	for _, op := range newOps {
		tmpOp := util.DeepCopy(op)
		po := po.OrderProduct{
			Id:                   &tmpOp.Id,
			PayAmount:            &tmpOp.PayAmount,
			OrderProductDiscount: &tmpOp.OrderProductDiscount,
			MemberDiscount:       &tmpOp.MemberDiscount,
			PayProductDiscount:   &tmpOp.PayProductDiscount,
		}
		toUpdateModifyOrderProducts = append(toUpdateModifyOrderProducts, po)
	}
	for _, om := range newOms {
		tmpOm := util.DeepCopy(om)
		po := po.OrderRoomPlan{
			Id:                    &tmpOm.Id,
			PayAmount:             &tmpOm.PayAmount,
			PayRoomDiscount:       &tmpOm.PayRoomDiscount,
			PayRoomDiscountAmount: &tmpOm.PayRoomDiscountAmount,
		}
		toUpdateModifyOrderRoomPlans = append(toUpdateModifyOrderRoomPlans, po)
	}
	return toUpdateModifyOrderProducts, toUpdateModifyOrderRoomPlans
}

// SaveOrderInfoSwapRoom 保存订单信息换台
func (s *OrderServiceImpl) SaveOrderInfoSwapRoom(ctx context.Context, newOrderPOInfoUnionVOA managentVo.OrderPOInfoUnionVO, newOrderPOInfoUnionVOB managentVo.OrderPOInfoUnionVO, newSessionA po.Session, newRoomA po.Room, newSessionB po.Session, newRoomB po.Room) error {
	return s.orderRepo.SaveOrderInfoSwapRoom(ctx, newOrderPOInfoUnionVOA, newOrderPOInfoUnionVOB, newSessionA, newRoomA, newSessionB, newRoomB)
}

// SaveOrderInfoMergeRoom 保存订单信息并房
func (s *OrderServiceImpl) SaveOrderInfoMergeRoom(ctx context.Context, newOrderPOInfoUnionVOA managentVo.OrderPOInfoUnionVO, toUpdateRooms []po.Room, toUpdateSessions []po.Session) error {
	return s.orderRepo.SaveOrderInfoMergeRoom(ctx, newOrderPOInfoUnionVOA, toUpdateRooms, toUpdateSessions)
}

// 获取房间锁房标签
func (s *OrderServiceImpl) GetLockRoomTag(ctx context.Context, tag *string) (string, error) {
	tagSrcs := []string{}
	// 1. 如果tag为空，则设置为锁房标签
	if tag == nil || *tag == "" {
		tagSrcs = append(tagSrcs, _const.ROOM_TAG_LOCKED)
		tagJson, err := json.Marshal(tagSrcs)
		if err != nil {
			return "", err
		}
		return string(tagJson), nil
	}
	// 2. 如果tag不为空，则解析tag
	err := json.Unmarshal([]byte(*tag), &tagSrcs)
	if err != nil {
		return "", err
	}
	// 3. 如果tagSrcs中包含锁房标签，则返回tag
	if util.InList(_const.ROOM_TAG_LOCKED, tagSrcs) {
		return *tag, nil
	}
	// 4. 如果tagSrcs中不包含锁房标签，则添加锁房标签
	tagSrcs = append(tagSrcs, _const.ROOM_TAG_LOCKED)
	tagJson, err := json.Marshal(tagSrcs)
	if err != nil {
		return "", err
	}
	return string(tagJson), nil
}

// 获取移除锁房标签后的标签
func (s *OrderServiceImpl) GetRemoveLockRoomTag(ctx context.Context, tag *string) (string, error) {
	// 1. 如果tag为空，则设置为空
	if tag == nil || *tag == "" {
		return "", nil
	}
	tagSrcs := []string{}
	// 2. 如果tag不为空，则解析tag
	err := json.Unmarshal([]byte(*tag), &tagSrcs)
	if err != nil {
		return "", err
	}
	// 3. 如果tagSrcs中不包含锁房标签，则添加锁房标签
	newSrcs := []string{}
	for _, src := range tagSrcs {
		if src != _const.ROOM_TAG_LOCKED {
			newSrcs = append(newSrcs, src)
		}
	}
	tagJson, err := json.Marshal(newSrcs)
	if err != nil {
		return "", err
	}
	return string(tagJson), nil
}

// FindsBySessionIds 查询所有订单
func (s *OrderServiceImpl) FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Order, error) {
	return s.orderRepo.FindsBySessionIds(ctx, venueId, sessionIds)
}

// BuildOrderVOsWithRoomAndProduct 构建订单VO
func (s *OrderServiceImpl) BuildOrderVOsWithRoomAndProduct(ctx context.Context, orders []po.Order, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan) []managentVo.OrderExtFeeVO {
	orderExtFeeVOs := make([]managentVo.OrderExtFeeVO, len(orders))
	for i, order := range orders {
		orderExtFeeVOs[i] = managentVo.OrderExtFeeVO{
			OrderVO: s.ConvertToOrderVO(ctx, order),
		}
		totalAmount := int64(0)
		if *order.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			for _, orderProduct := range orderProducts {
				if *orderProduct.OrderNo == *order.OrderNo {
					orderExtFeeVOs[i].OrderProductVOs = append(orderExtFeeVOs[i].OrderProductVOs, s.orderProductService.ConvertToOrderProductVO(ctx, orderProduct))
					totalAmount += *orderProduct.PayAmount
				}
			}
		} else {
			for _, orderRoomPlan := range orderRoomPlans {
				if *orderRoomPlan.OrderNo == *order.OrderNo {
					orderExtFeeVOs[i].OrderRoomPlanVOs = append(orderExtFeeVOs[i].OrderRoomPlanVOs, s.orderRoomPlanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
					totalAmount += *orderRoomPlan.PayAmount
				}
			}
		}
		orderExtFeeVOs[i].TotalFee = totalAmount
	}
	return orderExtFeeVOs
}

// FindOrdersByOrderNos 根据订单号查询订单
func (s *OrderServiceImpl) FindOrdersByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.Order, error) {
	return s.orderRepo.FindOrdersByOrderNos(ctx, venueId, orderNos)
}

// FindLastRoomOperation 查询最后一个房间操作
func (s *OrderServiceImpl) FindLastRoomOperation(ctx context.Context, venueId string, roomIds []string) ([]po.RoomOperation, error) {
	return s.orderRepo.FindLastRoomOperation(ctx, venueId, roomIds)
}

// RecordLastRoomOperation 记录最后一个房间操作
func (s *OrderServiceImpl) RecordLastRoomOperation(ctx context.Context, venueId, roomId, sessionId, employeeId, opType, info string) error {
	return s.orderRepo.RecordLastRoomOperation(ctx, venueId, roomId, sessionId, employeeId, opType, info)
}

// FindsByTimeRange 根据时间范围查询订单
func (s *OrderServiceImpl) FindsByTimeRange(ctx context.Context, venueId string, startTime, endTime int64) ([]po.Order, error) {
	return s.orderRepo.FindsByTimeRange(ctx, venueId, startTime, endTime)
}

func (s *OrderServiceImpl) GetProductAndPackageInfo(ctx context.Context, venueId string, productIdsReq []string, packageIdsReq []string) ([]managentVo.ProductVO, []managentVo.ProductTypeVO, []managentVo.ProductPackageVO, []managentVo.ProductPackageTypeVO, map[string]managentVo.ProductTypeVO, map[string]managentVo.ProductPackageTypeVO, error) {
	productVOs := []managentVo.ProductVO{}
	productTypeVOs := []managentVo.ProductTypeVO{}
	productPackageVOs := []managentVo.ProductPackageVO{}
	productPackageTypeVOs := []managentVo.ProductPackageTypeVO{}
	productIds := []string{}
	packageIds := []string{}
	for _, productId := range productIdsReq {
		if productId != "" {
			util.AddListElement(&productIds, productId)
		}
	}
	for _, packageId := range packageIdsReq {
		if packageId != "" {
			util.AddListElement(&packageIds, packageId)
		}
	}
	productTypeIdToProductTypeVO := make(map[string]managentVo.ProductTypeVO)
	productPackageTypeIdToProductPackageTypeVO := make(map[string]managentVo.ProductPackageTypeVO)

	productIdToProductTypeVO := make(map[string]managentVo.ProductTypeVO)
	productIdToProductPackageTypeVO := make(map[string]managentVo.ProductPackageTypeVO)
	if len(productIds) > 0 {
		products, err := s.productRepo.FindProductsByIds(ctx, venueId, productIds)
		if err != nil {
			logrus.Errorf("GetProductAndPackageInfo FindByID product error: %v", err)
		} else {
			categoryIds := []string{}
			for _, product := range products {
				productVOs = append(productVOs, s.productRepo.ConvertToProductVO(ctx, product))
				util.AddListElement(&categoryIds, *product.Category)
			}
			productTypes, err := s.productTypeRepo.FindProductTypesByIds(ctx, venueId, categoryIds)
			if err != nil {
				logrus.Errorf("GetProductAndPackageInfo FindByID productType error: %v", err)
			} else {
				for _, productType := range productTypes {
					voTmp := s.productTypeRepo.ConvertToProductTypeVO(ctx, productType)
					productTypeVOs = append(productTypeVOs, voTmp)
					productTypeIdToProductTypeVO[*productType.Id] = voTmp
				}
			}
		}
	}
	if len(packageIds) > 0 {
		productPackages, err := s.productPackageRepo.FindProductPackagesByIds(ctx, venueId, packageIds)
		if err != nil {
			logrus.Errorf("GetProductAndPackageInfo FindByID productPackage error: %v", err)
		} else {
			categoryIds := []string{}
			for _, productPackage := range productPackages {
				productPackageVOs = append(productPackageVOs, s.productPackageRepo.ConvertToProductPackageVO(ctx, productPackage))
				util.AddListElement(&categoryIds, *productPackage.Category)
			}
			productPackageTypes, err := s.productPackageTypeRepo.FindProductPackageTypesByIds(ctx, venueId, categoryIds)
			if err != nil {
				logrus.Errorf("GetProductAndPackageInfo FindByID productPackageType error: %v", err)
			} else {
				for _, productPackageType := range productPackageTypes {
					voTmp := s.productPackageTypeRepo.ConvertToProductPackageTypeVO(ctx, productPackageType)
					productPackageTypeVOs = append(productPackageTypeVOs, voTmp)
					productPackageTypeIdToProductPackageTypeVO[*productPackageType.Id] = voTmp
				}
			}
		}
	}
	for _, productVO := range productVOs {
		typeVO := productTypeIdToProductTypeVO[productVO.Category]
		productIdToProductTypeVO[productVO.Id] = typeVO
	}
	for _, productPackageVO := range productPackageVOs {
		typeVO := productPackageTypeIdToProductPackageTypeVO[productPackageVO.Category]
		productIdToProductPackageTypeVO[productPackageVO.Id] = typeVO
	}
	return productVOs, productTypeVOs, productPackageVOs, productPackageTypeVOs, productIdToProductTypeVO, productIdToProductPackageTypeVO, nil
}

func (s *OrderServiceImpl) IsPackage(ctx context.Context, productId, packageId, packageInfo string) (bool, []managentVo.ProductPackageInfoVO) {
	if productId == "" && packageId != "" && packageInfo != "" {
		productPackageInfoVOs := []managentVo.ProductPackageInfoVO{}
		err := json.Unmarshal([]byte(packageInfo), &productPackageInfoVOs)
		if err != nil {
			logrus.Errorf("IsPackage json.Unmarshal error: %v", err)
			return false, nil
		}
		return true, productPackageInfoVOs
	}
	return false, nil
}

func (s *OrderServiceImpl) GetCategoryInfo(ctx context.Context, orderProductVO managentVo.OrderProductVO, productIdToProductTypeVO map[string]managentVo.ProductTypeVO, productIdToProductPackageTypeVO map[string]managentVo.ProductPackageTypeVO) (string, string) {
	categoryId := orderProductVO.CategoryId
	categoryName := orderProductVO.CategoryName
	if categoryId == "" || categoryName == "" {
		isPackage, _ := s.IsPackage(ctx, orderProductVO.ProductId, orderProductVO.PackageId, orderProductVO.PackageProductInfo)
		if isPackage {
			categoryId = productIdToProductPackageTypeVO[orderProductVO.PackageId].Id
			categoryName = productIdToProductPackageTypeVO[orderProductVO.PackageId].Name
		} else {
			categoryId = productIdToProductTypeVO[orderProductVO.ProductId].Id
			categoryName = productIdToProductTypeVO[orderProductVO.ProductId].Name
		}
	}
	return categoryId, categoryName
}

func (s *OrderServiceImpl) GetOrderTree(ctx context.Context, orders []po.Order) ([]managentVo.OrderVO, map[string]managentVo.OrderVO) {
	orderNoToOrderMap := map[string]managentVo.OrderVO{}
	normalOrderVOs := []managentVo.OrderVO{}
	orderNOToRefundOrderVOs := make(map[string][]managentVo.OrderVO)
	for _, order := range orders {
		orderVO := s.ConvertToOrderVO(ctx, order)
		orderNoToOrderMap[orderVO.OrderNo] = orderVO
		if orderVO.Direction == _const.V2_ORDER_DIRECTION_NORMAL {
			normalOrderVOs = append(normalOrderVOs, orderVO)
		} else {
			orderNOToRefundOrderVOs[orderVO.POrderNo] = append(orderNOToRefundOrderVOs[orderVO.POrderNo], orderVO)
		}
	}
	for index, normalOrderVO := range normalOrderVOs {
		normalOrderVOs[index].RefundOrders = orderNOToRefundOrderVOs[normalOrderVO.OrderNo]
	}
	return normalOrderVOs, orderNoToOrderMap
}

// BuildRefundOrderAndOrderProduct 构建退款订单和订单商品
func (s *OrderServiceImpl) BuildRefundOrderAndOrderRoomPlan(ctx context.Context, orderPOInfoUnionVO managentVo.OrderPOInfoUnionVO, treeOrderVOs []managentVo.OrderVO, orderNosReq []string) []managentVo.RefundOrderInfoGroupVO {
	newRefundOrderInfoGroups := make([]managentVo.RefundOrderInfoGroupVO, 0)
	treeOrderNoToOrderMap := make(map[string]managentVo.OrderVO)
	for _, orderVO := range treeOrderVOs {
		treeOrderNoToOrderMap[orderVO.OrderNo] = orderVO
	}
	orderNOToOrderRoomPlanMap := make(map[string][]po.OrderRoomPlan)
	for _, orderRoomPlan := range orderPOInfoUnionVO.OrderRoomPlans {
		orderNOToOrderRoomPlanMap[*orderRoomPlan.OrderNo] = append(orderNOToOrderRoomPlanMap[*orderRoomPlan.OrderNo], orderRoomPlan)
	}

	// billId: payBill
	// billPid: payBills
	billIdToPayBillMap := make(map[string]po.PayBill)
	billPidToPayBillsMap := make(map[string][]po.PayBill)
	for _, payBill := range orderPOInfoUnionVO.PayBills {
		billIdToPayBillMap[*payBill.BillId] = payBill
		if payBill.Direction != nil && *payBill.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			billPidToPayBillsMap[*payBill.BillPid] = append(billPidToPayBillsMap[*payBill.BillPid], payBill)
		}
	}

	// orderNo: payBill
	// orderNo: refundPayBills
	orderNoToPayBillMap := make(map[string]po.PayBill)
	orderNoToRefundPayBillsMap := make(map[string][]po.PayBill)
	for _, orderAndPay := range orderPOInfoUnionVO.OrderAndPays {
		billId := *orderAndPay.BillId
		orderNo := *orderAndPay.OrderNo
		orderNoToPayBillMap[orderNo] = billIdToPayBillMap[billId]
		orderNoToRefundPayBillsMap[orderNo] = billPidToPayBillsMap[billId]
	}

	// billId: payRecords
	billIdToPayRecordsMap := make(map[string][]po.PayRecord)
	for _, payRecord := range orderPOInfoUnionVO.PayRecords {
		billIdToPayRecordsMap[*payRecord.BillId] = append(billIdToPayRecordsMap[*payRecord.BillId], payRecord)
	}
	// payPid: payRecords
	payPidToPayRecordsMap := make(map[string][]po.PayRecord)
	for _, payRecord := range orderPOInfoUnionVO.PayRecords {
		if payRecord.PayPid != nil && *payRecord.PayPid != "" {
			payPidToPayRecordsMap[*payRecord.PayPid] = append(payPidToPayRecordsMap[*payRecord.PayPid], payRecord)
		}
	}

	for _, orderNO := range orderNosReq {
		// 父订单
		treeOrderVO := treeOrderNoToOrderMap[orderNO]
		treeOrderPO := s.ConvertToOrderPO(ctx, treeOrderVO)
		// 新生成的退款订单
		newOrderNo := util.GetOrderNo(treeOrderVO.VenueId)
		newOrderPO := s.ConvertToOrderPO(ctx, treeOrderVO)
		newOrderPO.Id = nil
		newOrderPO.OrderNo = &newOrderNo
		newOrderPO.POrderNo = &orderNO
		newOrderPO.Direction = util.Ptr(_const.V2_ORDER_DIRECTION_REFUND)
		newOrderPO.Status = util.Ptr(_const.V2_ORDER_STATUS_UNPAID)
		newOrderPO.Ctime = nil
		newOrderPO.Utime = nil
		newOrderPO.State = nil
		newOrderPO.Version = nil

		// 父订单的房费订单
		parentOrderRoomPlans := orderNOToOrderRoomPlanMap[orderNO]
		// 新生成的退款订单的房费订单
		newRefundOrderRoomPlans := []po.OrderRoomPlan{}
		for _, parentOrderRoomPlan := range parentOrderRoomPlans {
			pid := *parentOrderRoomPlan.Id
			parentOrderRoomPlan.Id = nil
			parentOrderRoomPlan.OrderNo = &newOrderNo
			parentOrderRoomPlan.PId = &pid
			parentOrderRoomPlan.Ctime = nil
			parentOrderRoomPlan.Utime = nil
			parentOrderRoomPlan.State = nil
			parentOrderRoomPlan.Version = nil
			newRefundOrderRoomPlans = append(newRefundOrderRoomPlans, parentOrderRoomPlan)
		}

		// 父订单的收款单
		pOrderPayBill, payBillExists := orderNoToPayBillMap[orderNO]
		pOrderRefundPayBills := orderNoToRefundPayBillsMap[orderNO]

		pOrderNormalPayRecords := []po.PayRecord{}
		pOrderRefundPayRecords := []po.PayRecord{}
		pMergedPayRecords := []po.PayRecord{}
		if payBillExists {
			// 父订单的支付记录
			pOrderNormalPayRecords = billIdToPayRecordsMap[*pOrderPayBill.BillId]
			for _, payRecord := range pOrderNormalPayRecords {
				payPidToPayRecords := payPidToPayRecordsMap[*payRecord.PayId]
				if len(payPidToPayRecords) > 0 {
					pOrderRefundPayRecords = append(pOrderRefundPayRecords, payPidToPayRecords...)
					mergedPayRecord := util.DeepClone(payRecord)
					for _, payPidToPayRecord := range payPidToPayRecords {
						*mergedPayRecord.TotalFee -= *payPidToPayRecord.TotalFee
					}
					if *mergedPayRecord.TotalFee > 0 {
						pMergedPayRecords = append(pMergedPayRecords, mergedPayRecord)
					}
				}
			}
		}

		newRefundOrderInfoGroup := managentVo.RefundOrderInfoGroupVO{
			Order:                  newOrderPO,
			POrder:                 treeOrderPO,
			POrdersRefund:          []po.Order{}, // assert: 房费订单付订单的退款订单一定为空
			POrderPayBill:          pOrderPayBill,
			POrderRefundPayBills:   pOrderRefundPayBills,
			POrderNormalPayRecords: pOrderNormalPayRecords,
			POrderRefundPayRecords: pOrderRefundPayRecords,
			POrderMergedPayRecords: pMergedPayRecords,

			POrderNormalOrderRoomPlans: parentOrderRoomPlans,
			OrderRoomPlansRefund:       newRefundOrderRoomPlans,
		}
		newRefundOrderInfoGroups = append(newRefundOrderInfoGroups, newRefundOrderInfoGroup)
	}

	return newRefundOrderInfoGroups
}

func (s *OrderServiceImpl) GetModelBasePayBillBO(ctx context.Context, venueId, employeeId string, startTime, endTime int64) (managentVo.ModelBasePayBillBO, error) {

	// 1. 账单数据
	payBillsTmp, err := s.payBillRepo.FindsByTimeRange(ctx, venueId, startTime, endTime)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}
	payBills := []po.PayBill{}
	billIds := []string{}
	for _, payBill := range payBillsTmp {
		// 跳过还原账单
		if payBill.IsBack != nil && *payBill.IsBack {
			continue
		}
		payBills = append(payBills, payBill)
		util.AddListElement(&billIds, *payBill.BillId)
	}
	payRecords, err := s.payRecordRepo.FindsByBillIds(ctx, venueId, billIds)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}
	orderAndPays, err := s.orderAndPayRepo.FindAllByBillIds(ctx, billIds)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}

	// 2. 订单数据
	orderNos := make([]string, 0)
	for _, orderAndPay := range *orderAndPays {
		util.AddListElement(&orderNos, *orderAndPay.OrderNo)
	}
	orders, err := s.orderRepo.FindOrdersByOrderNos(ctx, venueId, orderNos)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}
	orderProducts, err := s.orderProductService.FindsByOrderNos(ctx, venueId, orderNos)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}
	orderRoomPlans, err := s.orderRoomPlanService.FindsByOrderNos(ctx, venueId, orderNos)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}

	// 3. 场次数据
	sessions, err := s.sessionRepo.FindSessionsByTimeRange(ctx, venueId, startTime, endTime)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}

	// 4. 房间数据
	rooms, err := s.roomRepo.FindRoomsByCtime(ctx, venueId, startTime, endTime)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}

	// 5. 会员概览
	memberCardVenues, err := s.memberCardVenueRepo.FindAllByVenueId(ctx, venueId)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}
	memberCardIds := make([]string, 0)
	for _, memberCardVenue := range memberCardVenues {
		util.AddListElement(&memberCardIds, *memberCardVenue.MemberCardId)
	}
	memberCards, err := s.memberCardRepo.FindsByIdsAndCtime(ctx, memberCardIds, startTime, endTime)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}

	memberCardRecharges, err := s.memberCardOperationRepo.FindMemberCardOperationsByTimeRange(ctx, venueId, startTime, endTime, _const.V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}
	memberCardConsumes, err := s.memberCardConsumeRepo.FindMemberCardConsumesRechargeByTimeRange(ctx, venueId, startTime, endTime)
	if err != nil {
		return managentVo.ModelBasePayBillBO{}, err
	}

	modelBasePayBillBO := managentVo.ModelBasePayBillBO{
		VenueId:      venueId,
		EmployeeId:   employeeId,
		StartTimeReq: startTime,
		EndTimeReq:   endTime,

		PayBills:       payBills,
		PayRecords:     payRecords,
		OrderAndPays:   *orderAndPays,
		Orders:         orders,
		OrderProducts:  orderProducts,
		OrderRoomPlans: orderRoomPlans,

		Sessions: sessions,
		Rooms:    rooms,

		MemberCardVenues:    memberCardVenues,
		MemberCards:         memberCards,
		MemberCardRecharges: memberCardRecharges,
		MemberCardConsumes:  memberCardConsumes,
	}
	return modelBasePayBillBO, nil
}

// 转换为营业概览VO
func (s *OrderServiceImpl) ConvertToVenueBusinessReportVO(ctx context.Context, modelBasePayBillBO managentVo.ModelBasePayBillBO) (managentVo.VenueBusinessReportV2VO, error) {

	// 1. 营业数据处理
	businessOverview := s.calcVenueBusinessReport(ctx, modelBasePayBillBO)

	// 2. 会员数据处理
	memberOverview := s.calcMemberOverview(ctx, modelBasePayBillBO)

	// 3. 场次数据处理
	sessions := modelBasePayBillBO.Sessions
	sessionOverview := managentVo.VenueBusinessReportV2VO_SessionOverview{
		SessionCount: len(sessions),
		RoomCount:    len(modelBasePayBillBO.Rooms),
	}
	venueVO := s.venueRepo.ConvertToVenueVO(ctx, *modelBasePayBillBO.Venue)
	for _, session := range sessions {
		if util.IsInTimeRange(*session.Ctime, venueVO.EarlyStartHours, venueVO.EarlyEndHours) {
			sessionOverview.EarlySessionCount++
		} else if util.IsInTimeRange(*session.Ctime, venueVO.NoonStartHours, venueVO.NoonEndHours) {
			sessionOverview.NoonSessionCount++
		} else if util.IsInTimeRange(*session.Ctime, venueVO.LateStartHours, venueVO.LateEndHours) {
			sessionOverview.LateSessionCount++
		}
	}
	businessOverview.NetRechargeFee = memberOverview.MemberRechargePrincipalBalance

	venueBusinessReportVO := managentVo.VenueBusinessReportV2VO{
		IncomeType: 0,
		VenueId:    modelBasePayBillBO.VenueId,
		VenueName:  venueVO.Name,
		StartHour:  venueVO.StartHours,
		EndHour:    venueVO.EndHours,
		StartTime:  modelBasePayBillBO.StartTimeReq,
		EndTime:    modelBasePayBillBO.EndTimeReq,

		BusinessOverview: businessOverview,
		MemberOverview:   memberOverview,
		SessionOverview:  sessionOverview,
	}
	return venueBusinessReportVO, nil
}

func (s *OrderServiceImpl) calcMemberOverview(ctx context.Context, modelBasePayBillBO managentVo.ModelBasePayBillBO) managentVo.VenueBusinessReportV2VO_MemberOverview {
	startTime := modelBasePayBillBO.StartTimeReq
	endTime := modelBasePayBillBO.EndTimeReq

	memberCards := modelBasePayBillBO.MemberCards
	// 新增会员数
	todayMemberCards := make([]po.MemberCard, 0)
	for _, memberCard := range memberCards {
		if *memberCard.Ctime > startTime && *memberCard.Ctime < endTime {
			todayMemberCards = append(todayMemberCards, memberCard)
		}
	}

	memberOverview := managentVo.VenueBusinessReportV2VO_MemberOverview{
		TotalMemberCount: int64(len(memberCards)),
		NewMemberCount:   int64(len(todayMemberCards)),
	}

	// 充值金额
	for _, MemberCardRecharge := range modelBasePayBillBO.MemberCardRecharges {
		memberOverview.MemberRechargePrincipalBalance += *MemberCardRecharge.PrincipalAmount
		memberOverview.MemberRechargeRoomBonusBalance += *MemberCardRecharge.MemberRoomBonusAmount
		memberOverview.MemberRechargeGoodsBonusBalance += *MemberCardRecharge.MemberGoodsBonusAmount
		memberOverview.MemberRechargeCommonBonusBalance += *MemberCardRecharge.MemberCommonBonusAmount
	}

	// 会员消费
	memberCardConsumes := modelBasePayBillBO.MemberCardConsumes
	for _, memberCardConsume := range memberCardConsumes {
		memberOverview.MemberPayPrincipalBalance += *memberCardConsume.PayRecordPrincipalAmount
		memberOverview.MemberPayRoomBonusBalance += *memberCardConsume.PayRecordRoomBonusAmount
		memberOverview.MemberPayGoodsBonusBalance += *memberCardConsume.PayRecordGoodsBonusAmount
		memberOverview.MemberPayCommonBonusBalance += *memberCardConsume.PayRecordCommonBonusAmount
	}

	return memberOverview
}

func (s *OrderServiceImpl) calcVenueBusinessReport(ctx context.Context, modelBasePayBillBO managentVo.ModelBasePayBillBO) managentVo.VenueBusinessReportV2VO_BusinessOverview {
	businessOverview := managentVo.VenueBusinessReportV2VO_BusinessOverview{}
	newPayBillVOs := s.buildNewPayBills(ctx, modelBasePayBillBO)

	payTypeFeeMap := make(map[string]int64)
	discountFee := int64(0)
	giftFee := int64(0)
	for index, payBillVO := range newPayBillVOs {
		// 账单还原跳过
		if payBillVO.IsBack {
			continue
		}
		billMultiplier := int64(1)
		if payBillVO.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			billMultiplier = -1
		}
		// 赠送金额-账单
		if payBillVO.IsGift {
			giftFee += payBillVO.ShouldFee * billMultiplier
		}
		// 优惠金额-账单
		discountFee += (payBillVO.ShouldFee - payBillVO.TotalFee) * billMultiplier
		// 计算出 总收入 房费 商品费
		orderVOs := payBillVO.OrderVOs
		roomFeeTotal := int64(0)
		productFeeTotal := int64(0)
		for _, orderVO := range orderVOs {
			orderMultiplier := int64(1)
			if orderVO.Direction == _const.V2_ORDER_DIRECTION_REFUND {
				orderMultiplier = -1
			}
			orderType := orderVO.Type
			if orderType == _const.V2_ORDER_TYPE_PRODUCT {
				for _, orderProductVO := range orderVO.OrderProductVOs {
					// 套内赠送商品
					if orderProductVO.IsGift {
						giftFee += orderProductVO.OriginalPrice * orderProductVO.Quantity * orderMultiplier
					}
					productFeeTotal += orderProductVO.PayAmount
				}
			} else if orderType == _const.V2_ORDER_TYPE_ROOMPLAN {
				for _, orderRoomPlanVO := range orderVO.OrderRoomPlanVOs {
					roomFeeTotal += orderRoomPlanVO.PayAmount
				}
			}
		}
		payBillVO.TmpRoomFee = roomFeeTotal
		payBillVO.TmpProductFee = productFeeTotal
		payBillVO.TmpTotalFee = payBillVO.TotalFee

		// 计算净收入占比
		netPayTypes := s.getNetPayTypes(ctx, modelBasePayBillBO.VenueId)
		netPayTotal := int64(0)
		for _, payRecordVO := range payBillVO.PayRecordVOs {
			payTypeFeeMap[payRecordVO.PayType] += payRecordVO.TotalFee
			if util.InList(payRecordVO.PayType, netPayTypes) {
				netPayTotal += payRecordVO.TotalFee
			}
		}

		per := float64(netPayTotal) / float64(payBillVO.TmpTotalFee)
		payBillVO.TmpNetTotalFee = int64(float64(payBillVO.TmpTotalFee) * per)
		payBillVO.TmpNetRoomFee = int64(float64(payBillVO.TmpRoomFee) * per)
		payBillVO.TmpNetProductFee = int64(float64(payBillVO.TmpProductFee) * per)

		newPayBillVOs[index] = payBillVO
	}

	for _, payBillVO := range newPayBillVOs {
		billMultiplier := int64(1)
		if payBillVO.Direction == _const.V2_PAY_BILL_DIRECTION_REFUND {
			billMultiplier = -1
		}
		businessOverview.TotalFee += payBillVO.TmpTotalFee * billMultiplier
		businessOverview.TotalFeeProduct += payBillVO.TmpProductFee * billMultiplier
		businessOverview.TotalFeeRoom += payBillVO.TmpRoomFee * billMultiplier
		businessOverview.NetTotalFee += payBillVO.TmpNetTotalFee * billMultiplier
		businessOverview.NetTotalFeeProduct += payBillVO.TmpNetProductFee * billMultiplier
		businessOverview.NetTotalFeeRoom += payBillVO.TmpNetRoomFee * billMultiplier
	}
	businessOverview.PayTypeFee = payTypeFeeMap
	businessOverview.DiscountFee = discountFee
	businessOverview.GiftFee = giftFee
	return businessOverview
}

func (s *OrderServiceImpl) getNetPayTypes(ctx context.Context, venueId string) []string {
	venuePayTypeSetting, err := s.venuePayTypeSettingRepo.FindVenuePayTypeSettingByVenueId(ctx, venueId)
	if err != nil {
		logrus.Println("FindVenuePayTypeSettingByVenueId error: ", err)
		return []string{}
	}
	payTypeInfo := venuePayTypeSetting.TypeInfo
	cfgs := []_const.PayTypeConfig{}
	err = json.Unmarshal([]byte(*payTypeInfo), &cfgs)
	if err != nil {
		logrus.Println("Unmarshal error: ", err)
	}
	netPayTypes := []string{}
	for _, payType := range cfgs {
		if payType.IsNetProfit {
			netPayTypes = append(netPayTypes, payType.PayType)
		}
	}
	return netPayTypes
}

func (s *OrderServiceImpl) buildNewPayBills(ctx context.Context, modelBasePayBillBO managentVo.ModelBasePayBillBO) []managentVo.PayBillVO {
	payBills := modelBasePayBillBO.PayBills
	payRecords := modelBasePayBillBO.PayRecords
	orderAndPays := modelBasePayBillBO.OrderAndPays
	orders := modelBasePayBillBO.Orders
	orderProducts := modelBasePayBillBO.OrderProducts
	orderRoomPlans := modelBasePayBillBO.OrderRoomPlans

	// 构造orderVO
	orderNoToOrderProductVOMap := make(map[string][]managentVo.OrderProductVO)
	for _, orderProduct := range orderProducts {
		orderNoToOrderProductVOMap[*orderProduct.OrderNo] = append(orderNoToOrderProductVOMap[*orderProduct.OrderNo], s.orderProductService.ConvertToOrderProductVO(ctx, orderProduct))
	}
	orderNoToOrderRoomPlanVOMap := make(map[string][]managentVo.OrderRoomPlanVO)
	for _, orderRoomPlan := range orderRoomPlans {
		orderNoToOrderRoomPlanVOMap[*orderRoomPlan.OrderNo] = append(orderNoToOrderRoomPlanVOMap[*orderRoomPlan.OrderNo], s.orderRoomPlanService.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan))
	}
	orderNoToOrderVOMap := make(map[string]managentVo.OrderVO)
	for _, order := range orders {
		orderVO := s.orderRepo.ConvertToOrderVO(ctx, order)
		orderVO.OrderProductVOs = orderNoToOrderProductVOMap[orderVO.OrderNo]
		orderVO.OrderRoomPlanVOs = orderNoToOrderRoomPlanVOMap[orderVO.OrderNo]
		orderNoToOrderVOMap[*order.OrderNo] = orderVO
	}

	// 构造payBillVO
	billIdToPayRecordVOMap := make(map[string][]managentVo.PayRecordVO)
	for _, payRecord := range payRecords {
		billIdToPayRecordVOMap[*payRecord.BillId] = append(billIdToPayRecordVOMap[*payRecord.BillId], s.payRecordRepo.ConvertToPayRecordVO(ctx, payRecord))
	}
	billIdToOrderAndPayVOMap := make(map[string][]managentVo.OrderAndPayVO)
	for _, orderAndPay := range orderAndPays {
		billIdToOrderAndPayVOMap[*orderAndPay.BillId] = append(billIdToOrderAndPayVOMap[*orderAndPay.BillId], s.orderAndPayRepo.ConvertToOrderAndPayVO(ctx, orderAndPay))
	}

	newPayBillVOs := []managentVo.PayBillVO{}
	for _, payBill := range payBills {
		payBillVO := s.payBillRepo.ConvertToPayBillVO(ctx, payBill)
		payBillVO.PayRecordVOs = billIdToPayRecordVOMap[*payBill.BillId]
		orderAndPaysVO := billIdToOrderAndPayVOMap[*payBill.BillId]
		payBillVO.OrderAndPayVOs = orderAndPaysVO
		newOrderVOs := []managentVo.OrderVO{}
		for _, orderAndPayVO := range orderAndPaysVO {
			orderVO := orderNoToOrderVOMap[orderAndPayVO.OrderNo]
			newOrderVOs = append(newOrderVOs, orderVO)
		}
		payBillVO.OrderVOs = newOrderVOs
		newPayBillVOs = append(newPayBillVOs, payBillVO)
	}
	return newPayBillVOs
}

func (s *OrderServiceImpl) SendSmsMemberCardOpen(ctx context.Context, venue *po.Venue, memberCard *po.MemberCard) error {
	return s.orderRepo.SendSmsMemberCardOpen(ctx, venue, memberCard)
}

func (s *OrderServiceImpl) SendSmsMemberCardRecharge(ctx context.Context, venue *po.Venue, memberCard *po.MemberCard, currentTotalAmount int64, currentPrincipalAmount int64) error {
	return s.orderRepo.SendSmsMemberCardRecharge(ctx, venue, memberCard, currentTotalAmount, currentPrincipalAmount)
}

func (s *OrderServiceImpl) SendSmsMemberCardConsume(ctx context.Context, venue *po.Venue, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error {
	return s.orderRepo.SendSmsMemberCardConsume(ctx, venue, memberCardId, currentPrincipalAmount, currentRoomBonusAmount, currentGoodsBonusAmount, currentCommonBonusAmount)
}

func (s *OrderServiceImpl) SendSmsMemberCardRefund(ctx context.Context, venueId string, memberCardId string, currentPrincipalAmount int64, currentRoomBonusAmount int64, currentGoodsBonusAmount int64, currentCommonBonusAmount int64) error {
	return s.orderRepo.SendSmsMemberCardRefund(ctx, venueId, memberCardId, currentPrincipalAmount, currentRoomBonusAmount, currentGoodsBonusAmount, currentCommonBonusAmount)
}

func (s *OrderServiceImpl) SyncProductStock(ctx context.Context, venue *po.Venue, orderDirection string, orderProducts []po.OrderProduct) error {
	return s.orderRepo.SyncProductStock(ctx, venue, orderDirection, orderProducts)
}