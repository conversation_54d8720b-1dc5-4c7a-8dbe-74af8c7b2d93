package impl

import (
	"context"
	_const "voderpltvv/const"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
)

// MemberCardConsumeServiceImpl 会员卡消费记录服务实现
type MemberCardConsumeServiceImpl struct {
	memberCardConsumeRepo  repository.MemberCardConsumeRepository
	memberRechargeBillRepo repository.MemberRechargeBillRepository
	payBillRepo            repository.PayBillRepository
}

// NewMemberCardConsumeService 创建会员卡消费记录服务实例
func NewMemberCardConsumeService(memberCardConsumeRepo repository.MemberCardConsumeRepository, memberRechargeBillRepo repository.MemberRechargeBillRepository, payBillRepo repository.PayBillRepository) *MemberCardConsumeServiceImpl {
	return &MemberCardConsumeServiceImpl{
		memberCardConsumeRepo:  memberCardConsumeRepo,
		memberRechargeBillRepo: memberRechargeBillRepo,
		payBillRepo:            payBillRepo,
	}
}

// CreateMemberCardConsume 创建会员卡消费记录
func (s *MemberCardConsumeServiceImpl) CreateMemberCardConsume(ctx context.Context, memberCardConsume *po.MemberCardConsume) error {
	return s.memberCardConsumeRepo.CreateMemberCardConsume(ctx, memberCardConsume)
}

// ConvertToMemberCardConsumeVO 转换为会员卡消费记录VO
func (s *MemberCardConsumeServiceImpl) ConvertToMemberCardConsumeVO(ctx context.Context, memberCardConsume po.MemberCardConsume) vo.MemberCardConsumeVO {
	return s.memberCardConsumeRepo.ConvertToMemberCardConsumeVO(ctx, memberCardConsume)
}

// ConvertToMemberCardConsume 转换为会员卡消费记录PO
func (s *MemberCardConsumeServiceImpl) ConvertToMemberCardConsume(ctx context.Context, memberCardConsumeVO vo.MemberCardConsumeVO) po.MemberCardConsume {
	return s.memberCardConsumeRepo.ConvertToMemberCardConsume(ctx, memberCardConsumeVO)
}

func (s *MemberCardConsumeServiceImpl) RecordMemberCardConsume(ctx context.Context, session po.Session, venue po.Venue, room po.Room, employee po.Employee, toAddPayRecords []po.PayRecord, toAddPayBill po.PayBill) {
	defer func() {
		if err := recover(); err != nil {
			logrus.Printf("RecordMemberCardConsume panic: %v", err)
		}
	}()
	var memberCardPayRecord po.PayRecord
	hasMemberCardPay := false
	thisPrincipalAmount := int64(0)
	thisRoomBonusAmount := int64(0)
	thisGoodsBonusAmount := int64(0)
	thisCommonBonusAmount := int64(0)
	for _, payRecord := range toAddPayRecords {
		if *payRecord.PayType == _const.PAY_TYPE_MEMBER_CARD {
			memberCardPayRecord = payRecord
			hasMemberCardPay = true
			thisPrincipalAmount += *payRecord.PrincipalAmount
			thisRoomBonusAmount += *payRecord.MemberRoomBonusAmount
			thisGoodsBonusAmount += *payRecord.MemberGoodsBonusAmount
			thisCommonBonusAmount += *payRecord.MemberCommonBonusAmount
			break
		}
	}
	if !hasMemberCardPay {
		logrus.Printf("没有会员卡支付记录, 无需记录会员卡消费记录")
		return
	}
	logrus.Printf("有会员卡支付记录, 记录会员卡消费记录")
	memberCardId := *memberCardPayRecord.MemberCardId
	memberCardNewTmp, err := s.memberRechargeBillRepo.V3RPCMemberCardQuery(ctx, req.V3QueryMemberCardQueryReqDto{
		MemberCardId: &memberCardId,
	})
	if err != nil {
		logrus.Printf("查询会员卡信息失败: %v", err)
		return
	}
	var memberCardRecord po.PayRecord
	for _, payRecord := range toAddPayRecords {
		if *payRecord.PayType == _const.PAY_TYPE_MEMBER_CARD {
			memberCardRecord = payRecord
			break
		}
	}
	billPayTime := util.TimeNowUnixInt64()
	payBillPO, err := s.payBillRepo.FindByBillId(ctx, *toAddPayBill.BillId, *toAddPayBill.VenueId, *toAddPayBill.SessionId)
	if err != nil {
		logrus.Printf("查询支付单失败: %v", err)
	} else if payBillPO.FinishTime != nil {
		billPayTime = *payBillPO.FinishTime
	}
	balance := memberCardNewTmp.PrincipalBalance + memberCardNewTmp.CommonBonusBalance + memberCardNewTmp.GoodsBonusBalance + memberCardNewTmp.RoomBonusBalance
	err = s.CreateMemberCardConsume(ctx, &po.MemberCardConsume{
		VenueId:                            venue.Id,
		VenueName:                          venue.Name,
		RoomId:                             room.Id,
		RoomName:                           room.Name,
		EmployeeId:                         employee.Id,
		EmployeeName:                       employee.Name,
		EmployeePhone:                      employee.Phone,
		MemberId:                           &memberCardId,
		MemberCardId:                       &memberCardId,
		MemberCardNumber:                   &memberCardNewTmp.CardNumber,
		MemberCardBalance:                  &balance,
		MemberCardBalancePrincipalAmount:   &memberCardNewTmp.PrincipalBalance,
		MemberCardBalanceRoomBonusAmount:   &memberCardNewTmp.RoomBonusBalance,
		MemberCardBalanceGoodsBonusAmount:  &memberCardNewTmp.GoodsBonusBalance,
		MemberCardBalanceCommonBonusAmount: &memberCardNewTmp.CommonBonusBalance,
		BizType:                            util.Ptr(_const.V2_MEMBER_RECHARGE_BILL_BIZ_TYPE_CONSUME),
		SessionId:                          session.Id,
		BillId:                             toAddPayBill.BillId,
		PayTime:                            &billPayTime,
		OriginalFee:                        toAddPayBill.OriginalFee,
		ShouldFee:                          toAddPayBill.ShouldFee,
		TotalFee:                           toAddPayBill.TotalFee,
		ZeroFee:                            toAddPayBill.ZeroFee,
		Direction:                          toAddPayBill.Direction,
		PayId:                              memberCardRecord.PayId,
		PayPid:                             memberCardRecord.PayPid,
		PayRecordTotalAmout:                memberCardRecord.TotalFee,
		PayRecordPrincipalAmount:           &thisPrincipalAmount,
		PayRecordRoomBonusAmount:           &thisRoomBonusAmount,
		PayRecordGoodsBonusAmount:          &thisGoodsBonusAmount,
		PayRecordCommonBonusAmount:         &thisCommonBonusAmount,
	})
	if err != nil {
		logrus.Printf("保存会员卡消费记录失败: %v", err)
		return
	}
}
