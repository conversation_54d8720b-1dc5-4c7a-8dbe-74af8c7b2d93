package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardServiceImpl 会员充值账单服务实现
type MemberCardServiceImpl struct {
	memberCardRepo repository.MemberCardRepository
}

// NewPayBillService 创建支付账单服务实例
func NewMemberCardService(memberCardRepo repository.MemberCardRepository) *MemberCardServiceImpl {
	return &MemberCardServiceImpl{
		memberCardRepo: memberCardRepo,
	}
}

// FindAllMemberRechargeBill 查询所有会员充值账单
func (s *MemberCardServiceImpl) FindAllMemberCard(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.MemberCard, error) {
	return s.memberCardRepo.FindAllMemberCard(ctx, venueId, sessionId, statusList)
}

// ConvertToPayBillVO 转换为支付账单VO
func (s *MemberCardServiceImpl) ConvertToMemberCardVO(ctx context.Context, memberCard po.MemberCard) vo.MemberCardVO {
	return s.memberCardRepo.ConvertToMemberCardVO(ctx, memberCard)
}

// ConvertToPayBill 转换为支付账单PO
func (s *MemberCardServiceImpl) ConvertToMemberCard(ctx context.Context, memberCardVO vo.MemberCardVO) po.MemberCard {
	return s.memberCardRepo.ConvertToMemberCard(ctx, memberCardVO)
}

// GetMaxMemberCardId 获取最大会员ID
func (s *MemberCardServiceImpl) GetMaxMemberCardId(ctx context.Context) (int, error) {
	return s.memberCardRepo.GetMaxMemberCardId(ctx)
}

// SaveMemberCard 保存会员
func (s *MemberCardServiceImpl) SaveMemberCard(ctx context.Context, memberCard po.MemberCard, reqDto req.V3OpenCardReqDto) (po.MemberCard, error) {
	return s.memberCardRepo.SaveMemberCard(ctx, memberCard, reqDto)
}

// FindById 根据ID查询会员
func (s *MemberCardServiceImpl) FindById(ctx context.Context, id string) (po.MemberCard, error) {
	return s.memberCardRepo.FindById(ctx, id)
}

// FindsByVenueId 查询所有会员
func (s *MemberCardServiceImpl) FindsByVenueId(ctx context.Context, venueId string) ([]po.MemberCard, error) {
	return s.memberCardRepo.FindsByVenueId(ctx, venueId)
}

// FindsByIdsAndCtime 查询所有会员
func (s *MemberCardServiceImpl) FindsByIdsAndCtime(ctx context.Context, ids []string, startTime int64, endTime int64) ([]po.MemberCard, error) {
	return s.memberCardRepo.FindsByIdsAndCtime(ctx, ids, startTime, endTime)
}
