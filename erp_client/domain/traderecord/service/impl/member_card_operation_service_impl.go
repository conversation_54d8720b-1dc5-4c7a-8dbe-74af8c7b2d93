package impl

import (
	"context"
	_const "voderpltvv/const"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"

	"github.com/sirupsen/logrus"
)

// MemberCardOperationServiceImpl 会员卡操作记录服务实现
type MemberCardOperationServiceImpl struct {
	memberCardOperationRepo repository.MemberCardOperationRepository
}

// NewMemberCardOperationService 创建会员卡操作记录服务实例
func NewMemberCardOperationService(memberCardOperationRepo repository.MemberCardOperationRepository) *MemberCardOperationServiceImpl {
	return &MemberCardOperationServiceImpl{
		memberCardOperationRepo: memberCardOperationRepo,
	}
}

// CreateMemberCardOperation 创建会员卡操作记录
func (s *MemberCardOperationServiceImpl) CreateMemberCardOperation(ctx context.Context, memberCardOperation *po.MemberCardOperation) error {
	return s.memberCardOperationRepo.CreateMemberCardOperation(ctx, memberCardOperation)
}

// ConvertToMemberCardOperationVO 转换为会员卡操作记录VO
func (s *MemberCardOperationServiceImpl) ConvertToMemberCardOperationVO(ctx context.Context, memberCardOperation po.MemberCardOperation) vo.MemberCardOperationVO {
	return s.memberCardOperationRepo.ConvertToMemberCardOperationVO(ctx, memberCardOperation)
}

// ConvertToMemberCardOperation 转换为会员卡操作记录PO
func (s *MemberCardOperationServiceImpl) ConvertToMemberCardOperation(ctx context.Context, memberCardOperationVO vo.MemberCardOperationVO) po.MemberCardOperation {
	return s.memberCardOperationRepo.ConvertToMemberCardOperation(ctx, memberCardOperationVO)
}

// RecordMemberCardOperation 记录会员卡操作记录
func (s *MemberCardOperationServiceImpl) RecordMemberCardOperation(ctx context.Context, memberCardOperation po.MemberCardOperation, memberCard po.MemberCard) {
	defer func() {
		if err := recover(); err != nil {
			logrus.Printf("RecordMemberCardOperation panic: %v", err)
		}
	}()
	err := s.memberCardOperationRepo.RecordMemberCardOperation(ctx, memberCardOperation, memberCard)
	if err != nil {
		logrus.Printf("RecordMemberCardOperation error: %v", err)
	}
}

// FindsRechargeByTimeRange 根据时间范围查询会员卡操作记录
func (s *MemberCardOperationServiceImpl) FindsRechargeByTimeRange(ctx context.Context, venueId string, timeStart int64, timeEnd int64) ([]po.MemberCardOperation, error) {
	return s.memberCardOperationRepo.FindMemberCardOperationsByTimeRange(ctx, venueId, timeStart, timeEnd, _const.V2_MEMBER_CARD_OPERATION_TYPE_RECHARGE)
}

// UpdateRechargeBanlance 更新充值余额
func (s *MemberCardOperationServiceImpl) UpdateRechargeBanlance(ctx context.Context, memberCardId string, payId string) error {
	return s.memberCardOperationRepo.UpdateRechargeBanlance(ctx, memberCardId, payId)
}