package impl

import (
	"context"
	"voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_managent/service/po"
)

// MemberServiceImpl 会员服务实现
type MemberServiceImpl struct {
	memberRepo repository.MemberRepository
}

// NewPayBillService 创建支付账单服务实例
func NewMemberService(memberRepo repository.MemberRepository) *MemberServiceImpl {
	return &MemberServiceImpl{
		memberRepo: memberRepo,
	}
}

// FindAllMemberRechargeBill 查询所有会员充值账单
func (s *MemberServiceImpl) FindAllMember(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.Member, error) {
	return s.memberRepo.FindAllMember(ctx, venueId, sessionId, statusList)
}
