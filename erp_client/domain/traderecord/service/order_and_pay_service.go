package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// OrderAndPayService 订单和支付服务接口
type OrderAndPayService interface {
	// FindAllByBillIds 查询所有支付账单
	FindAllByBillIds(ctx context.Context, billIds []string) (*[]po.OrderAndPay, error)
	// ConvertToOrderAndPayVO 转换为订单和支付VO
	ConvertToOrderAndPayVO(ctx context.Context, orderAndPay po.OrderAndPay) vo.OrderAndPayVO
	// ConvertToOrderAndPay 转换为订单和支付PO
	ConvertToOrderAndPay(ctx context.Context, orderAndPayVO vo.OrderAndPayVO) po.OrderAndPay
	// FindsByOrderNos 查询订单和支付
	FindsByOrderNos(ctx context.Context, orderNos []string) ([]po.OrderAndPay, error)
}
