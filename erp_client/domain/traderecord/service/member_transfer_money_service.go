package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberTransferMoneyService 会员转账记录服务接口
type MemberTransferMoneyService interface {
	// CreateMemberTransferMoney 创建会员转账记录
	CreateMemberTransferMoney(ctx context.Context, memberTransferMoney *po.MemberTransferMoney) error

	// ConvertToMemberTransferMoneyVO 转换为会员转账记录VO
	ConvertToMemberTransferMoneyVO(ctx context.Context, memberTransferMoney po.MemberTransferMoney) vo.MemberTransferMoneyVO

	// ConvertToMemberTransferMoney 转换为会员转账记录PO
	ConvertToMemberTransferMoney(ctx context.Context, memberTransferMoneyVO vo.MemberTransferMoneyVO) po.MemberTransferMoney
}
