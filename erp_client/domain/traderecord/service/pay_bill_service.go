package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// PayBillService 支付账单服务接口
type PayBillService interface {
	// FindAllPayBill 查询所有支付账单
	FindAllPayBill(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayBill, error)
	// ConvertToPayBillVO 转换为支付账单VO
	ConvertToPayBillVO(ctx context.Context, payBill po.PayBill) vo.PayBillVO
	// ConvertToPayBill 转换为支付账单PO
	ConvertToPayBill(ctx context.Context, payBillVO vo.PayBillVO) po.PayBill
	// FindAllBySessionId 查询所有支付账单
	FindAllBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error)
	// CheckHasUnpaidPayBill 检查是否有正在支付的账单
	CheckHasUnpaidPayBill(ctx context.Context, venueId, sessionId string) error
	// TrimIdToAddPayBill 去除ID
	TrimIdToAddPayBill(ctx context.Context, payBill po.PayBill) po.PayBill
	// FindsBySessionId 查询所有支付账单
	FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayBill, error)
	// FindsByTimeRange 查询所有支付账单
	FindsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.PayBill, error)
	// FindByBillId 查询支付账单
	FindByBillId(ctx context.Context, billId, venueId, sessionId string) (po.PayBill, error)
	// FindsBySessionIds 查询所有支付账单
	FindsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.PayBill, error)
	// CalcBusinessOverviewForVenue 计算营业概览
	CalcBusinessOverviewForVenue(ctx context.Context, venueId string, orders []po.Order, payBills []po.PayBill, orderAndPays []po.OrderAndPay, orderProducts []po.OrderProduct, orderRoomPlans []po.OrderRoomPlan) vo.VenueBusinessReportVOBusinessOverview

	// FindModelBasePayBillVOsByTimeRange 查询支付账单VOs
	FindModelBasePayBillVOsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) (vo.ModelBasePayBillVO, error)
	// ConvertSrcVODataToPayBillVOs 转换为支付账单VOs
	ConvertSrcVODataToPayBillVOs(ctx context.Context, modelBasePayBillVO vo.ModelBasePayBillVO) []vo.PayBillVO

	// MergeOrderProductSalesVOs 合并订单产品销售VOs
	MergeOrderProductSalesVOs(ctx context.Context, payBillVOs []vo.PayBillVO) []vo.OrderProductSalesVO
}
