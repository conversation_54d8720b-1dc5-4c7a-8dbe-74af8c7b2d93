package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardConsumeService 会员卡消费记录服务接口
type MemberCardConsumeService interface {
	// CreateMemberCardConsume 创建会员卡消费记录
	CreateMemberCardConsume(ctx context.Context, memberCardConsume *po.MemberCardConsume) error

	// ConvertToMemberCardConsumeVO 转换为会员卡消费记录VO
	ConvertToMemberCardConsumeVO(ctx context.Context, memberCardConsume po.MemberCardConsume) vo.MemberCardConsumeVO

	// ConvertToMemberCardConsume 转换为会员卡消费记录PO
	ConvertToMemberCardConsume(ctx context.Context, memberCardConsumeVO vo.MemberCardConsumeVO) po.MemberCardConsume

	// RecordMemberCardConsume 记录会员卡消费记录
	RecordMemberCardConsume(ctx context.Context, session po.Session, venue po.Venue, room po.Room, employee po.Employee, toAddPayRecords []po.PayRecord, toAddPayBill po.PayBill)
}
