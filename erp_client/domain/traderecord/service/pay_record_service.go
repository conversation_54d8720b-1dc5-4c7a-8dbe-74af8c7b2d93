package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// PayRecordService 支付记录服务接口
type PayRecordService interface {
	// FindAllPayRecord 查询所有支付记录
	FindAllPayRecord(ctx context.Context, venueId, sessionId string, statusList []string) (*[]po.PayRecord, error)
	// ConvertToPayRecordVO 转换为支付记录VO
	ConvertToPayRecordVO(ctx context.Context, payRecord po.PayRecord) vo.PayRecordVO
	// ConvertToPayRecord 转换为支付记录PO
	ConvertToPayRecord(ctx context.Context, payRecordVO vo.PayRecordVO) po.PayRecord
	// FindsBySessionId 查询所有支付记录
	FindsBySessionId(ctx context.Context, venueId, sessionId string) ([]po.PayRecord, error)
	// FindsByBillIds 查询所有支付记录
	FindsByBillIds(ctx context.Context, venueId string, billIds []string) ([]po.PayRecord, error)
	// FindPayRecordByPayId 查询支付记录
	FindByPayId(ctx context.Context, payId string) (*po.PayRecord, error)
}
