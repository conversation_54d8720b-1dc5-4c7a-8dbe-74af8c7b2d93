package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardVenueService 会员卡门店关联服务接口
type MemberCardVenueService interface {
	// FindAllMemberCardVenue 查询所有会员卡门店关联
	FindAllMemberCardVenue(ctx context.Context, venueId string) (*[]po.MemberCardVenue, error)
	// FindByMemberCardId 根据会员卡ID查询会员卡门店关联
	FindByMemberCardId(ctx context.Context, memberCardId string) ([]po.MemberCardVenue, error)
	// ConvertToMemberCardVenueVO 转换为会员卡门店关联VO
	ConvertToMemberCardVenueVO(ctx context.Context, memberCardVenue po.MemberCardVenue) vo.MemberCardVenueVO
	// ConvertToMemberCardVenue 转换为会员卡门店关联PO
	ConvertToMemberCardVenue(ctx context.Context, memberCardVenueVO vo.MemberCardVenueVO) po.MemberCardVenue
	// FindAllByVenueId 根据场馆ID查询所有会员卡门店关联
	FindAllByVenueId(ctx context.Context, venueId string) ([]po.MemberCardVenue, error)
}
