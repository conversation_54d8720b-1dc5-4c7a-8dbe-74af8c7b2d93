package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// MemberCardOperationService 会员卡操作记录服务接口
type MemberCardOperationService interface {
	// CreateMemberCardOperation 创建会员卡操作记录
	CreateMemberCardOperation(ctx context.Context, memberCardOperation *po.MemberCardOperation) error

	// ConvertToMemberCardOperationVO 转换为会员卡操作记录VO
	ConvertToMemberCardOperationVO(ctx context.Context, memberCardOperation po.MemberCardOperation) vo.MemberCardOperationVO

	// ConvertToMemberCardOperation 转换为会员卡操作记录PO
	ConvertToMemberCardOperation(ctx context.Context, memberCardOperationVO vo.MemberCardOperationVO) po.MemberCardOperation

	// RecordMemberCardOperation 记录会员卡操作记录
	RecordMemberCardOperation(ctx context.Context, memberCardOperation po.MemberCardOperation, memberCard po.MemberCard)

	// FindsRechargeByTimeRange 根据时间范围查询会员卡操作记录
	FindsRechargeByTimeRange(ctx context.Context, venueId string, timeStart int64, timeEnd int64) ([]po.MemberCardOperation, error)

	// UpdateRechargeBanlance 更新充值余额
	UpdateRechargeBanlance(ctx context.Context, memberCardId string, payId string) error
}
