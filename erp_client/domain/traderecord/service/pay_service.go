package service

import (
	"context"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
)

// PayService 支付服务接口
type PayService interface {
	// GetPayOrderInfos 获取需支付订单信息
	GetPayOrderInfos(ctx context.Context, reqDto req.QueryOrderPayReqDto) (*[]vo.OrderVO, *[]po.Order, error)
	GetPayOrderInfos_Bak(ctx context.Context, reqDto *req.QueryOrderPayReqDto) (*[]vo.OrderVO, *[]po.Order, error)

	// GetPayOrderInfosWithRoomAndProductBySessionId 获取需支付订单的房间和商品信息
	GetPayOrderInfosWithRoomAndProductBySessionId(ctx context.Context, venueId string, sessionId string, orderVOs []vo.OrderVO) (*[]po.OrderRoomPlan, *[]po.OrderProduct)

	// ValidatePayDiscountIsValid 验证支付折扣是否合法
	ValidatePayDiscountIsValid(ctx context.Context, reqDto *req.QueryOrderPayReqDto, orderRoomPlans *[]po.OrderRoomPlan, orderProducts *[]po.OrderProduct) error

	// Calc2UpdateOrdersAndRoomPlansAndProducts 计算更新订单、房间计划和商品
	Calc2UpdateOrdersAndRoomPlansAndProducts(ctx context.Context, reqDto *req.QueryOrderPayReqDto,
		toPayOrderVOs *[]vo.OrderVO, toPayOrders *[]po.Order,
		orderRoomPlans *[]po.OrderRoomPlan, orderProducts *[]po.OrderProduct) (*[]po.Order, *[]po.OrderRoomPlan, *[]po.OrderProduct, int64, int64, error)

	// BuildPayDataForPay 构建支付数据
	BuildPayDataForPay(ctx context.Context, reqDto req.QueryOrderPayReqDto,
		toUpdateOrders []po.Order, session po.Session,
		totalDiscountRoomAmount int64, totalDiscountProductAmount int64) (*po.PayBill, *[]po.OrderAndPay, error)
	BuildPayDataForPay_Bak(ctx context.Context, reqDto *req.QueryOrderPayReqDto,
		toUpdateOrders *[]po.Order, session *po.Session,
		totalDiscountRoomAmount int64, totalDiscountProductAmount int64) (*po.PayBill, *[]po.OrderAndPay, error)

	// SaveBatchTxForPay 批量保存支付数据
	SaveBatchTxForPay(ctx context.Context, toPayBill *po.PayBill,
		toAddOrderAndPays *[]po.OrderAndPay, toUpdateOrders *[]po.Order,
		toUpdateOrderRoomPlans *[]po.OrderRoomPlan, toUpdateOrderProducts *[]po.OrderProduct) error

	// TransformPayGate 转换支付网关
	TransformPayGate(ctx context.Context, reqDto *req.QueryOrderPayReqDto, toPayBill *po.PayBill) (*vo.PayResultVO, error)

	// V3TransformPayGate 转换支付网关
	V3TransformPayGate(ctx context.Context, reqDto *req.V3QueryOrderPayTransformReqDto, toPayBill *po.PayBill) ([]vo.PayResultVO, error)

	// AfterPayCallbackCoUpdateInfoByPayId 支付回调后更新信息
	AfterPayCallbackCoUpdateInfoByPayId(ctx context.Context, payId string) error

	// CalculateTotalPayment 计算总支付金额
	CalculateTotalPayment(ctx context.Context, session vo.SessionVO, orders []vo.OrderVO, payBill vo.PayBillVO) (int64, []vo.OrderProductVO, []vo.OrderRoomPlanVO, int64, int64)

	// CalculateTotalPaymentForPay 计算总支付金额-支付-后付
	CalculateTotalPaymentForPay(ctx context.Context, session vo.SessionVO, orders []vo.OrderVO, payBill vo.PayBillVO) (int64, []vo.OrderProductVO, []vo.OrderRoomPlanVO, int64, int64)

	// QueryPaidOrderVOsBySessionId 查询已支付订单
	QueryPaidOrderVOsBySessionId(ctx context.Context, venueId string, sessionId string) (int64, error)

	// CalculateManyFee 计算多个费用
	CalculateManyFee(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// CalculateManyFeeForAdditionalOrder 计算多个费用-新增订单
	CalculateManyFeeForAdditionalOrder(ctx context.Context, totalFeeThis int64, opsNotInDB []vo.OrderProductVO, omsNotInDB []vo.OrderRoomPlanVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, UnpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// CalculateManyFeeForPay 计算多个费用-支付-后付
	CalculateManyFeeForPay(ctx context.Context, totalFeeThis int64, opsInDB []vo.OrderProductVO, omsInDB []vo.OrderRoomPlanVO, orderVOs []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// CalculateManyFeeForTransferRoom 计算多个费用-支付-后付
	CalculateManyFeeForTransferRoom(ctx context.Context, orderVOMarkDelete []vo.OrderVO, sessionId string, venueId string, lastMinimumCharge int64) (totalRoomFee int64, totalSupermarketFee int64, unpaidAmount int64, paidAmount int64, totalFee int64, err error)

	// GetOrdersInfoByOrderNos 获取订单信息
	GetOrdersInfoByOrderNos(ctx context.Context, orderNos []string, venueId string, sessionId string) ([]vo.OrderVO, error)

	// SaveOrderPayInfoCallbackByPayId 保存支付信息回调
	SaveOrderPayInfoCallbackByPayId(ctx context.Context, callbackVO vo.OrderPayCallbackVO) error

	// SaveOrderPayInfoCallbackByBillIdForFree 保存支付信息回调
	SaveOrderPayInfoCallbackByBillIdForFree(ctx context.Context, billId string) error

	// V3RefundByCash 按现金退款
	V3RefundByCash(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// V3RefundByBack 按原路返回退款
	V3RefundByBack(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// RefundAllOrderBySessionId
	RefundAllOrderBySessionId(ctx context.Context, sessionId string, venueId string) (vo.OrderPOInfoUnionVO, error)

	// GetOrderPOInfoBySessionId 查询session下的所有订单相关PO数据
	GetOrderPOInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO

	// GetPayInfoBySessionId 查询session下的所有支付相关PO数据
	GetPayInfoBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO

	// GetPayInfoBillBackBySessionId 查询session下的所有支付相关PO数据
	GetPayInfoBillBackBySessionId(ctx context.Context, sessionId string, venueId string) vo.OrderPOInfoUnionVO

	// GetNewSwapInfo 获取新换房信息
	GetNewSwapInfo(ctx context.Context, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, session po.Session, room po.Room, sessionIdB string, roomIdB string) (vo.OrderPOInfoUnionVO, po.Session, po.Room)

	// GetNewMergeInfo 获取新并房信息
	GetNewMergeInfo(ctx context.Context, orderPOInfoUnionVO vo.OrderPOInfoUnionVO, sessionIdB string) vo.OrderPOInfoUnionVO

	// BuildOrderVOInfoMergedVOs 构造mergevo
	BuildOrderVOInfoMergedVOs(ctx context.Context, oisVO vo.OrderPOInfoUnionVO) []vo.OrderVOInfoMergedVO

	// 获取修改roomid的对象
	GetModifyRoomIdOrderInfo(ctx context.Context, oisVO vo.OrderPOInfoUnionVO, newRoomId string) vo.OrderPOInfoUnionVO

	// UpdateSessionInfoUnpaid 更新session信息-未支付
	UpdateSessionInfoUnpaid(ctx context.Context, sessionId string, venueId string) error

	// BuildPayBillVOInfoBackVO 构建backvo,计算每个bill最终通过哪种方式退款多少钱
	BuildPayBillVOInfoBackVO(ctx context.Context, payBills []po.PayBill, payRecords []po.PayRecord, orderAndPays []po.OrderAndPay) []vo.PayBillVOInfoBackVO

	// BuildNewPayBillVOInfoBackVO 构建新还原账单
	BuildNewPayBillVOInfoBackVO(ctx context.Context, venueId string, sessionId string, payBillVOInfoBackVOs []vo.PayBillVOInfoBackVO) []vo.PayBillPOInfoBackVO

	// SavePayBillBack 保存还原账单
	SavePayBillBack(ctx context.Context, payBillPOInfoBackVOs []vo.PayBillPOInfoBackVO) error

	// V3BillBack 还原账单
	V3BillBack(ctx context.Context, reqDto req.V3BillBackReqDto, newPayBillVOInfoBackVOs []vo.PayBillPOInfoBackVO) error

	// BuildPayBillVOsWithRecord 构建订单扩展费用VO
	BuildPayBillVOsWithRecord(ctx context.Context, payBills []po.PayBill, payRecords []po.PayRecord) []vo.PayBillVO

	// V3CallPayCallback 乐刷支付回调
	V3CallPayCallback(ctx context.Context, reqDto model.LeshuaPayCallbackModel) error

	// V3CallRefundCallback 乐刷退款回调
	V3CallRefundCallback(ctx context.Context, reqDto model.LeshuaRefundCallbackModel) error

	// TransferLeshuaRefund 乐刷退款
	TransferLeshuaRefund(ctx context.Context, payRecordVO vo.PayRecordVO) error

	// StartLeshuaTimer 启动乐刷支付定时器
	StartLeshuaTimer(ctx context.Context, key string) error

	// ParseToPayBillInfoSrcVOs 转换为 []PayBillInfoSrcVO
	ParseToPayBillInfoSrcVOs(ctx context.Context, payInfoPOs vo.OrderPOInfoUnionVO, billIds []string) []vo.PayBillInfoSrcVO

	// ParseToPayBillInfoNomalVOs 转换为 []PayBillInfoNomalVO
	ParseToPayBillInfoNomalVOs(ctx context.Context, payBillInfoSrcVOs []vo.PayBillInfoSrcVO) []vo.PayBillInfoNomalVO

	// ParseToPayBillInfoBillBackVOs 转换为 []PayBillInfoBillBackVO
	BuildPayBillInfoBillBackVOs(ctx context.Context, payBillInfoNomalVOs []vo.PayBillInfoNomalVO) []vo.PayBillInfoBillBackVO

	// SavePayBillInfoBillBackVOs 保存还原账单
	SavePayBillInfoBillBackVOs(ctx context.Context, payBillInfoBillBackVOs []vo.PayBillInfoBillBackVO, reqDto req.V3BillBackReqDto) error

	// CalcOrderHasMultiPayWay 计算订单是否存在多种支付方式
	CalcOrderHasMultiPayWay(ctx context.Context, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) []vo.OrderVO

	// SendNATSMessageForRoomStatusChanged 发送NATS消息
	SendNATSMessageForRoomStatusChanged(ctx context.Context, venueId string, forceSend bool, toAddPayRecords []po.PayRecord) error


	// V3RefundByCashRoomPlan 按现金退款
	V3RefundByCashRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// V3RefundByBackRoomPlan 按原路返回退款
	V3RefundByBackRoomPlan(ctx context.Context, reqDto req.V3QueryOrderRoomFeeRefundReqDto, orderInfoGroups []vo.RefundOrderInfoGroupVO, session po.Session, room po.Room, totalRefundAmount int64, orderPOInfoUnionVO vo.OrderPOInfoUnionVO) ([]vo.OrderRefundInfoVO, error)

	// GetOrderInfoBatchBySessionId 获取session下的所有订单相关PO数据
	GetOrderInfoBatchBySessionId(ctx context.Context, sessionId string, venueId string, roomId string, employeeId string) (vo.ModeOrderInfoBaseSessionPO, error)

	// ConvertToModeOrderInfoBaseSessionBO 转换为ModeOrderInfoBaseSessionBO
	ConvertToModeOrderInfoBaseSessionBO(ctx context.Context, orderInfoPO vo.ModeOrderInfoBaseSessionPO) vo.ModeOrderInfoBaseSessionBO

	// DoCancelOrderOpenInfo 构建取消开台信息
	DoCancelOrderOpenInfo(ctx context.Context, reqDto req.V3CancelOrderOpenReqDto, modeOrderInfoBaseSessionBO vo.ModeOrderInfoBaseSessionBO) (bool, error)
}
