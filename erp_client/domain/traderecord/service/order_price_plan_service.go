package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// OrderPricePlanService 订单价格方案服务接口
type OrderPricePlanService interface {
	// FindAllOrderPricePlan 查询所有订单价格方案
	FindAllOrderPricePlan(ctx context.Context, venueId, sessionId string) (*[]po.OrderPricePlan, error)
	// FindOrderPricePlansByVenueIDAndSessionId 根据场所ID和场次ID查询订单价格计划
	FindOrderPricePlansByVenueIDAndSessionId(ctx context.Context, venueId, sessionId string) (*[]po.OrderPricePlan, error)
	// FindOrderPricePlansByVenueIDAndSessionIds 根据场所ID和场次ID查询订单价格计划
	FindOrderPricePlansByVenueIDAndSessionIds(ctx context.Context, venueId string, sessionIds []string) (*[]po.OrderPricePlan, error)

	// ConvertToPricePlanVO 转换为价格方案VO
	ConvertToPricePlanVO(ctx context.Context, orderPricePlan po.OrderPricePlan) vo.OrderPricePlanVO
	// ConvertToPricePlanPO 转换为价格方案PO
	ConvertToPricePlan(ctx context.Context, orderPricePlanVO vo.OrderPricePlanVO) po.OrderPricePlan
}
