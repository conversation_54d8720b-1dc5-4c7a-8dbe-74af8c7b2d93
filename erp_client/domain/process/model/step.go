package model

import (
	"context"
)

// Step 流程步骤接口
type Step interface {
	// GetID 获取步骤ID
	GetID() string

	// GetName 获取步骤名称
	GetName() string

	// GetDescription 获取步骤描述
	GetDescription() string

	// Execute 执行步骤
	Execute(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error)
}

// BaseStep 基础步骤实现
type BaseStep struct {
	id          string
	name        string
	description string
}

// NewBaseStep 创建基础步骤
func NewBaseStep(id string, name string, description string) *BaseStep {
	return &BaseStep{
		id:          id,
		name:        name,
		description: description,
	}
}

// GetID 获取步骤ID
func (s *BaseStep) GetID() string {
	return s.id
}

// GetName 获取步骤名称
func (s *BaseStep) GetName() string {
	return s.name
}

// GetDescription 获取步骤描述
func (s *BaseStep) GetDescription() string {
	return s.description
}
