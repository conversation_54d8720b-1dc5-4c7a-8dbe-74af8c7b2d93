package model

import (
	"context"
	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// ProcessContext 流程上下文接口
type ProcessContext interface {
	// GetProcessID 获取流程ID
	GetProcessID() string

	// GetStore 获取变量存储
	GetStore() variable.Store
}

// Engine 流程引擎接口
type Engine interface {
	// LoadProcess 加载流程定义
	LoadProcess(processContent []byte) error

	// Execute 执行流程
	Execute(ctx context.Context, processID string, params map[string]interface{}) (map[string]interface{}, error)

	// RegisterService 注册服务
	RegisterService(name string, service interface{})

	// RegisterProcess 注册流程
	RegisterProcess(process Flow) error

	// UnregisterProcess 注销流程
	UnregisterProcess(processID string) error

	// GetProcess 获取流程
	GetProcess(processID string) (Flow, error)

	// RegisterStep 注册步骤
	RegisterStep(processID string, step Step) error

	// UnregisterStep 注销步骤
	UnregisterStep(processID string, stepID string) error

	// GetStep 获取步骤
	GetStep(processID string, stepID string) (Step, error)

	// ExecuteStep 执行步骤
	ExecuteStep(ctx context.Context, processID string, stepID string, params map[string]interface{}) (map[string]interface{}, error)
}
