package model

// ProcessStatus 流程状态
type ProcessStatus int

const (
	// ProcessStatusInit 初始状态
	ProcessStatusInit ProcessStatus = iota
	// ProcessStatusRunning 运行中
	ProcessStatusRunning
	// ProcessStatusSuccess 成功
	ProcessStatusSuccess
	// ProcessStatusFailed 失败
	ProcessStatusFailed
	// ProcessStatusCanceled 已取消
	ProcessStatusCanceled
)

// String 返回状态的字符串表示
func (s ProcessStatus) String() string {
	switch s {
	case ProcessStatusInit:
		return "初始化"
	case ProcessStatusRunning:
		return "运行中"
	case ProcessStatusSuccess:
		return "成功"
	case ProcessStatusFailed:
		return "失败"
	case ProcessStatusCanceled:
		return "已取消"
	default:
		return "未知状态"
	}
}
