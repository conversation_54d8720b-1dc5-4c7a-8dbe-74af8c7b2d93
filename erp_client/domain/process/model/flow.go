package model

import (
	"context"
)

// Flow 流程接口
type Flow interface {
	// GetID 获取流程ID
	GetID() string

	// GetName 获取流程名称
	GetName() string

	// GetDescription 获取流程描述
	GetDescription() string

	// GetSteps 获取所有步骤
	GetSteps() []Step

	// AddStep 添加步骤
	AddStep(step Step) Flow

	// Execute 执行流程
	Execute(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error)
}

// BaseFlow 基础流程实现
type BaseFlow struct {
	id          string
	name        string
	description string
	steps       []Step
}

// NewBaseFlow 创建基础流程
func NewBaseFlow(id string, name string, description string) *BaseFlow {
	return &BaseFlow{
		id:          id,
		name:        name,
		description: description,
		steps:       make([]Step, 0),
	}
}

// GetID 获取流程ID
func (f *BaseFlow) GetID() string {
	return f.id
}

// GetName 获取流程名称
func (f *BaseFlow) GetName() string {
	return f.name
}

// GetDescription 获取流程描述
func (f *BaseFlow) GetDescription() string {
	return f.description
}

// GetSteps 获取所有步骤
func (f *BaseFlow) GetSteps() []Step {
	return f.steps
}

// AddStep 添加步骤
func (f *BaseFlow) AddStep(step Step) Flow {
	f.steps = append(f.steps, step)
	return f
}

// Execute 执行流程
func (f *BaseFlow) Execute(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 执行每个步骤
	for _, step := range f.steps {
		stepResult, err := step.Execute(ctx, params)
		if err != nil {
			return nil, err
		}

		// 合并步骤结果
		for k, v := range stepResult {
			result[k] = v
		}

		// 更新参数，供下一个步骤使用
		for k, v := range stepResult {
			params[k] = v
		}
	}

	return result, nil
}
