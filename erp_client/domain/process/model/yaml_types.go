package model

import (
	"fmt"
)

// Field 字段定义
type Field struct {
	Name     string `yaml:"name"`
	Required bool   `yaml:"required"`
}

// YAMLField YAML字段定义
type YAMLField struct {
	Name     string      `yaml:"name"`
	Required bool        `yaml:"required"`
	Fields   []YAMLField `yaml:"fields,omitempty"`
	IsArray  bool        `yaml:"is_array,omitempty"`
}

// ToField 将 YAMLField 转换为 Field
func (y *YAMLField) ToField() Field {
	return Field{
		Name:     y.Name,
		Required: y.Required,
	}
}

// ProcessError 流程错误
type ProcessError struct {
	HttpCode int                    `json:"-"`              // HTTP状态码
	Code     string                 `json:"code"`           // 业务错误码
	Message  string                 `json:"message"`        // 错误消息
	Data     map[string]interface{} `json:"data,omitempty"` // 错误详情
}

func (e *ProcessError) Error() string {
	return fmt.Sprintf("[%d] %s: %s", e.HttpCode, e.Code, e.Message)
}

// ValidateService 验证服务接口
type ValidateService interface {
	ValidateRules(rules []ValidationRule, data map[string]interface{}) error
}

// ValidationRule 验证规则
type ValidationRule struct {
	Type         string                 `json:"type"`          // 规则类型
	Field        string                 `json:"field"`         // 字段名
	FieldName    string                 `json:"field_name"`    // 字段显示名
	ErrorMessage string                 `json:"error_message"` // 错误消息
	ErrorCode    string                 `json:"error_code"`    // 错误码
	Params       map[string]interface{} `json:"params"`        // 规则参数
}

// YAMLStep YAML步骤定义
type YAMLStep struct {
	ID          string                 `yaml:"id"`
	Name        string                 `yaml:"name"`
	Description string                 `yaml:"description,omitempty"`
	Condition   string                 `yaml:"condition,omitempty"`
	Action      map[string]interface{} `yaml:"action"`
}

// Definition YAML定义
type Definition struct {
	ID          string `yaml:"id"`
	Name        string `yaml:"name"`
	Description string `yaml:"description"`
	Output      []struct {
		Name string `yaml:"name"`
	} `yaml:"output"`
}
