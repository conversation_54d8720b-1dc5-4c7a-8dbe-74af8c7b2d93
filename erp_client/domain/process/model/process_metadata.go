package model

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// ProcessMetadata 流程元数据
type ProcessMetadata struct {
	Input   *MetadataInputDef   `yaml:"input"`   // 输入定义
	Output  *MetadataOutputDef  `yaml:"output"`  // 输出定义
	Context *MetadataContextDef `yaml:"context"` // 上下文定义
	System  *MetadataSystemDef  `yaml:"system"`  // 系统定义
}

// MetadataInputDef 输入定义
type MetadataInputDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 输入变量定义
}

// MetadataOutputDef 输出定义
type MetadataOutputDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 输出变量定义
}

// MetadataContextDef 上下文定义
type MetadataContextDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 上下文变量定义
}

// MetadataSystemDef 系统变量定义
type MetadataSystemDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 系统变量定义
}

// NewProcessMetadata 创建流程元数据
func NewProcessMetadata() *ProcessMetadata {
	return &ProcessMetadata{
		Input: &MetadataInputDef{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		Output: &MetadataOutputDef{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		Context: &MetadataContextDef{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		System: &MetadataSystemDef{
			Variables: make([]*variable.VariableDefinition, 0),
		},
	}
}

// Validate 验证元数据
func (m *ProcessMetadata) Validate() error {
	// 输入变量验证
	if m.Input != nil {
		for _, v := range m.Input.Variables {
			if v.Name == "" {
				return fmt.Errorf("输入变量名称不能为空")
			}
		}
	}

	// 输出变量验证
	if m.Output != nil {
		for _, v := range m.Output.Variables {
			if v.Name == "" {
				return fmt.Errorf("输出变量名称不能为空")
			}
		}
	}

	// 上下文变量验证
	if m.Context != nil {
		for _, v := range m.Context.Variables {
			if v.Name == "" {
				return fmt.Errorf("上下文变量名称不能为空")
			}
		}
	}

	// 系统变量验证
	if m.System != nil {
		for _, v := range m.System.Variables {
			if v.Name == "" {
				return fmt.Errorf("系统变量名称不能为空")
			}
		}
	}

	return nil
}

// GetInputVariables 获取输入变量
func (m *ProcessMetadata) GetInputVariables() []*variable.VariableDefinition {
	if m.Input == nil {
		return nil
	}
	return m.Input.Variables
}

// GetOutputVariables 获取输出变量
func (m *ProcessMetadata) GetOutputVariables() []*variable.VariableDefinition {
	if m.Output == nil {
		return nil
	}
	return m.Output.Variables
}

// GetContextVariables 获取上下文变量
func (m *ProcessMetadata) GetContextVariables() []*variable.VariableDefinition {
	if m.Context == nil {
		return nil
	}
	return m.Context.Variables
}

// GetSystemVariables 获取系统变量
func (m *ProcessMetadata) GetSystemVariables() []*variable.VariableDefinition {
	if m.System == nil {
		return nil
	}
	return m.System.Variables
}

// AddInputVariable 添加输入变量
func (m *ProcessMetadata) AddInputVariable(v *variable.VariableDefinition) {
	if m.Input == nil {
		m.Input = &MetadataInputDef{
			Variables: make([]*variable.VariableDefinition, 0),
		}
	}
	m.Input.Variables = append(m.Input.Variables, v)
}

// AddOutputVariable 添加输出变量
func (m *ProcessMetadata) AddOutputVariable(v *variable.VariableDefinition) {
	if m.Output == nil {
		m.Output = &MetadataOutputDef{
			Variables: make([]*variable.VariableDefinition, 0),
		}
	}
	m.Output.Variables = append(m.Output.Variables, v)
}

// AddContextVariable 添加上下文变量
func (m *ProcessMetadata) AddContextVariable(v *variable.VariableDefinition) {
	if m.Context == nil {
		m.Context = &MetadataContextDef{
			Variables: make([]*variable.VariableDefinition, 0),
		}
	}
	m.Context.Variables = append(m.Context.Variables, v)
}

// AddSystemVariable 添加系统变量
func (m *ProcessMetadata) AddSystemVariable(v *variable.VariableDefinition) {
	if m.System == nil {
		m.System = &MetadataSystemDef{
			Variables: make([]*variable.VariableDefinition, 0),
		}
	}
	m.System.Variables = append(m.System.Variables, v)
}
