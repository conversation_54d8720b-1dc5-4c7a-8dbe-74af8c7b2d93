package model

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// ProcessDefinition 流程定义
type ProcessDefinition struct {
	ID          string           `yaml:"id"`          // 流程ID
	Name        string           `yaml:"name"`        // 流程名称
	Description string           `yaml:"description"` // 流程描述
	Metadata    *ProcessMetadata `yaml:"metadata"`    // 流程元数据
	Steps       []ProcessStep    `yaml:"steps"`       // 流程步骤
}

// InputDef 输入定义
type InputDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 输入变量定义
}

// OutputDef 输出定义
type OutputDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 输出变量定义
}

// ContextDef 上下文定义
type ContextDef struct {
	Variables []*variable.VariableDefinition `yaml:"variables"` // 上下文变量定义
}

// NewProcessDefinition 创建流程定义
func NewProcessDefinition() *ProcessDefinition {
	return &ProcessDefinition{
		Metadata: NewProcessMetadata(),
		Steps:    make([]ProcessStep, 0),
	}
}

// Validate 验证流程定义
func (p *ProcessDefinition) Validate() error {
	if p.ID == "" {
		return fmt.Errorf("流程ID不能为空")
	}
	if p.Name == "" {
		return fmt.Errorf("流程名称不能为空")
	}
	if len(p.Steps) == 0 {
		return fmt.Errorf("流程步骤不能为空")
	}

	// 验证元数据
	if p.Metadata != nil {
		if err := p.Metadata.Validate(); err != nil {
			return fmt.Errorf("元数据验证失败: %w", err)
		}
	}

	// 验证步骤
	for _, step := range p.Steps {
		if err := step.Validate(); err != nil {
			return fmt.Errorf("步骤[%s]验证失败: %w", step.ID, err)
		}
	}

	return nil
}

// GetID 获取流程ID
func (p *ProcessDefinition) GetID() string {
	return p.ID
}

// GetName 获取流程名称
func (p *ProcessDefinition) GetName() string {
	return p.Name
}

// GetDescription 获取流程描述
func (p *ProcessDefinition) GetDescription() string {
	return p.Description
}

// GetSteps 获取流程步骤
func (p *ProcessDefinition) GetSteps() []ProcessStep {
	return p.Steps
}

// AddStep 添加流程步骤
func (p *ProcessDefinition) AddStep(step ProcessStep) {
	p.Steps = append(p.Steps, step)
}

// AddInputVariable 添加输入变量
func (p *ProcessDefinition) AddInputVariable(variable *variable.VariableDefinition) {
	if p.Metadata == nil {
		p.Metadata = NewProcessMetadata()
	}
	p.Metadata.AddInputVariable(variable)
}

// AddOutputVariable 添加输出变量
func (p *ProcessDefinition) AddOutputVariable(variable *variable.VariableDefinition) {
	if p.Metadata == nil {
		p.Metadata = NewProcessMetadata()
	}
	p.Metadata.AddOutputVariable(variable)
}

// AddContextVariable 添加上下文变量
func (p *ProcessDefinition) AddContextVariable(variable *variable.VariableDefinition) {
	if p.Metadata == nil {
		p.Metadata = NewProcessMetadata()
	}
	p.Metadata.AddContextVariable(variable)
}

// GetInputVariables 获取输入变量
func (p *ProcessDefinition) GetInputVariables() []*variable.VariableDefinition {
	if p.Metadata == nil {
		return nil
	}
	return p.Metadata.GetInputVariables()
}

// GetOutputVariables 获取输出变量
func (p *ProcessDefinition) GetOutputVariables() []*variable.VariableDefinition {
	if p.Metadata == nil {
		return nil
	}
	return p.Metadata.GetOutputVariables()
}

// GetContextVariables 获取上下文变量
func (p *ProcessDefinition) GetContextVariables() []*variable.VariableDefinition {
	if p.Metadata == nil {
		return nil
	}
	return p.Metadata.GetContextVariables()
}
