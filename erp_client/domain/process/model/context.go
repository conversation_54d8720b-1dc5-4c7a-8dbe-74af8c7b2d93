package model

import (
	"sync"
	"time"
	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// ExtendedProcessContext 扩展的流程上下文接口
type ExtendedProcessContext interface {
	ProcessContext
	// GetValue 获取值
	GetValue(key string) interface{}
	// SetValue 设置值
	SetValue(key string, value interface{})
	// HasValue 是否存在值
	HasValue(key string) bool
	// GetStatus 获取状态
	GetStatus() ProcessStatus
	// SetStatus 设置状态
	SetStatus(status ProcessStatus)
	// GetStartTime 获取开始时间
	GetStartTime() time.Time
	// GetEndTime 获取结束时间
	GetEndTime() time.Time
	// GetDuration 获取执行时长
	GetDuration() time.Duration
}

// extendedProcessContext 扩展的流程上下文实现
type extendedProcessContext struct {
	sync.RWMutex
	values    map[string]interface{}
	processID string
	status    ProcessStatus
	startTime time.Time
	endTime   time.Time
}

// NewExtendedProcessContext 创建扩展的流程上下文
func NewExtendedProcessContext(processID string) ExtendedProcessContext {
	return &extendedProcessContext{
		values:    make(map[string]interface{}),
		processID: processID,
		status:    ProcessStatusInit,
		startTime: time.Now(),
	}
}

// GetValue 获取值
func (c *extendedProcessContext) GetValue(key string) interface{} {
	c.RLock()
	defer c.RUnlock()
	return c.values[key]
}

// SetValue 设置值
func (c *extendedProcessContext) SetValue(key string, value interface{}) {
	c.Lock()
	defer c.Unlock()
	c.values[key] = value
}

// HasValue 是否存在值
func (c *extendedProcessContext) HasValue(key string) bool {
	c.RLock()
	defer c.RUnlock()
	_, exists := c.values[key]
	return exists
}

// GetProcessID 获取流程ID
func (c *extendedProcessContext) GetProcessID() string {
	return c.processID
}

// GetStatus 获取状态
func (c *extendedProcessContext) GetStatus() ProcessStatus {
	return c.status
}

// SetStatus 设置状态
func (c *extendedProcessContext) SetStatus(status ProcessStatus) {
	c.status = status
	if status == ProcessStatusSuccess || status == ProcessStatusFailed || status == ProcessStatusCanceled {
		c.endTime = time.Now()
	}
}

// GetStartTime 获取开始时间
func (c *extendedProcessContext) GetStartTime() time.Time {
	return c.startTime
}

// GetEndTime 获取结束时间
func (c *extendedProcessContext) GetEndTime() time.Time {
	return c.endTime
}

// GetDuration 获取执行时长
func (c *extendedProcessContext) GetDuration() time.Duration {
	if c.endTime.IsZero() {
		return time.Since(c.startTime)
	}
	return c.endTime.Sub(c.startTime)
}

// GetStore 获取变量存储（实现 ProcessContext 接口）
func (c *extendedProcessContext) GetStore() variable.Store {
	return nil // 这个实现不使用 Store
}
