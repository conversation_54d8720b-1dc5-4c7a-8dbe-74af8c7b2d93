package model

import "fmt"

// ProcessStep 流程步骤
type ProcessStep struct {
	ID          string                 `yaml:"id"`
	Name        string                 `yaml:"name"`
	Description string                 `yaml:"description,omitempty"`
	Condition   string                 `yaml:"condition,omitempty"`
	Action      map[string]interface{} `yaml:"action"`
}

// ActionType 动作类型
type ActionType string

const (
	// ActionTypeService 服务调用
	ActionTypeService ActionType = "service"
	// ActionTypeSetField 设置字段
	ActionTypeSetField ActionType = "set_field"
	// ActionTypeError 错误
	ActionTypeError ActionType = "error"
	// ActionTypeTransform 转换
	ActionTypeTransform ActionType = "transform"
	// ActionTypeValidate 验证
	ActionTypeValidate ActionType = "validate"
)

// ErrorHandlerType 错误处理器类型
type ErrorHandlerType string

const (
	// ErrorHandlerTypeEarlyReturn 提前返回
	ErrorHandlerTypeEarlyReturn ErrorHandlerType = "early_return"
	// ErrorHandlerTypeContinue 继续执行
	ErrorHandlerTypeContinue ErrorHandlerType = "continue"
)

// ErrorHandler 错误处理器
type ErrorHandler struct {
	Type         ErrorHandlerType  `yaml:"type"`
	ErrorMessage string            `yaml:"error_message"`
	ErrorCode    string            `yaml:"error_code,omitempty"`
	Target       string            `yaml:"target,omitempty"`
	Mapping      map[string]string `yaml:"mapping,omitempty"`
}

// OutputConfig 输出配置
type OutputConfig struct {
	Target       string            `yaml:"target,omitempty"`
	Mapping      map[string]string `yaml:"mapping,omitempty"`
	ErrorHandler *ErrorHandler     `yaml:"error_handler,omitempty"`
}

// GetID 获取步骤ID
func (s *ProcessStep) GetID() string {
	return s.ID
}

// GetName 获取步骤名称
func (s *ProcessStep) GetName() string {
	return s.Name
}

// GetDescription 获取步骤描述
func (s *ProcessStep) GetDescription() string {
	return s.Description
}

// GetCondition 获取步骤条件
func (s *ProcessStep) GetCondition() string {
	return s.Condition
}

// GetAction 获取步骤动作
func (s *ProcessStep) GetAction() map[string]interface{} {
	return s.Action
}

// Validate 验证步骤
func (s *ProcessStep) Validate() error {
	if s.ID == "" {
		return fmt.Errorf("步骤ID不能为空")
	}
	if s.Name == "" {
		return fmt.Errorf("步骤名称不能为空")
	}
	if s.Action == nil {
		return fmt.Errorf("步骤动作不能为空")
	}
	return nil
}
