package model

import (
	"voderpltvv/erp_client/application/framework/runtime/variable"
)

// processContext 流程上下文实现
type processContext struct {
	processID string
	store     variable.Store
}

// NewProcessContext 创建新的流程上下文
func NewProcessContext(processID string) ProcessContext {
	store := variable.NewStore()

	return &processContext{
		processID: processID,
		store:     store,
	}
}

// GetProcessID 获取流程ID
func (c *processContext) GetProcessID() string {
	return c.processID
}

// GetStore 获取变量存储
func (c *processContext) GetStore() variable.Store {
	return c.store
}
