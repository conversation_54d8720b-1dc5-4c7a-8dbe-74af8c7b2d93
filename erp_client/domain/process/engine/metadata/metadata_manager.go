package metadata

import (
	"voderpltvv/erp_client/application/framework/metadata"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	yamltypes "voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/process/model"
	log "voderpltvv/util"
)

// MetadataManager 元数据管理器
type MetadataManager struct {
	converter metadata.MetadataConverter
}

// NewMetadataManager 创建元数据管理器
func NewMetadataManager() *MetadataManager {
	return &MetadataManager{
		converter: metadata.NewBaseMetadataConverter(),
	}
}

// ConvertMetadata 转换元数据
func (m *MetadataManager) ConvertMetadata(metadata *yamltypes.Metadata) *variable.Metadata {
	log.Debug("MetadataManager.ConvertMetadata", "开始转换元数据")
	result := m.converter.ConvertToRuntimeMetadata(metadata)
	log.Debug("MetadataManager.ConvertMetadata", "元数据转换完成")
	return result
}

// ConvertToYAMLMetadata 转换为YAML元数据
func (m *MetadataManager) ConvertToYAMLMetadata(metadata *variable.Metadata) *yamltypes.Metadata {
	log.Debug("MetadataManager.ConvertToYAMLMetadata", "开始转换元数据")
	result := m.converter.ConvertToYAMLMetadata(metadata)
	log.Debug("MetadataManager.ConvertToYAMLMetadata", "元数据转换完成")
	return result
}

// ConvertMetadata 转换元数据类型
func (m *MetadataManager) ConvertMetadataType(metadata *yamltypes.Metadata) *variable.Metadata {
	log.Debug("MetadataManager.ConvertMetadata", "开始转换元数据")

	runtimeMetadata := &variable.Metadata{
		Input: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		Output: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		Context: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
		System: &variable.VariableSection{
			Variables: make([]*variable.VariableDefinition, 0),
		},
	}

	// 转换输入元数据
	if metadata.Input != nil {
		for _, v := range metadata.Input.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       m.convertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.Input.Variables = append(runtimeMetadata.Input.Variables, def)
		}
	}

	// 转换输出元数据
	if metadata.Output != nil {
		for _, v := range metadata.Output.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       m.convertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.Output.Variables = append(runtimeMetadata.Output.Variables, def)
		}
	}

	// 转换上下文元数据
	if metadata.Context != nil {
		for _, v := range metadata.Context.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       m.convertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.Context.Variables = append(runtimeMetadata.Context.Variables, def)
		}
	}

	// 转换系统元数据
	if metadata.System != nil {
		for _, v := range metadata.System.Variables {
			def := &variable.VariableDefinition{
				Name:         v.Name,
				Required:     v.Required,
				Fields:       m.convertFields(v.ToModelFields()),
				DefaultValue: v.DefaultValue,
				Expression:   v.Expression,
				IsArray:      v.IsArray,
			}
			runtimeMetadata.System.Variables = append(runtimeMetadata.System.Variables, def)
		}
	}

	log.Debug("MetadataManager.ConvertMetadata", "元数据转换完成")
	return runtimeMetadata
}

// convertFields 转换字段定义
func (m *MetadataManager) convertFields(fields []model.Field) []*variable.FieldDefinition {
	if fields == nil {
		return nil
	}
	result := make([]*variable.FieldDefinition, 0, len(fields))
	for _, f := range fields {
		field := &variable.FieldDefinition{
			Name:     f.Name,
			Required: f.Required,
		}
		result = append(result, field)
	}
	return result
}
