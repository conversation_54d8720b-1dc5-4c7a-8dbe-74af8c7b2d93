package condition

import (
	"fmt"
	log "voderpltvv/util"
)

// ContextManager 上下文管理接口
type ContextManager interface {
	EvalExpression(expression string) (interface{}, error)
}

// ConditionEvaluator 条件评估器
type ConditionEvaluator struct {
	context ContextManager
}

// NewConditionEvaluator 创建条件评估器
func NewConditionEvaluator(context ContextManager) *ConditionEvaluator {
	return &ConditionEvaluator{
		context: context,
	}
}

// EvaluateCondition 评估条件表达式
func (e *ConditionEvaluator) EvaluateCondition(condition string) (bool, error) {
	if condition == "" {
		return true, nil // 空条件默认为真
	}

	log.Debug("ConditionEvaluator.EvaluateCondition", "开始评估条件: %s", condition)

	// 评估条件表达式
	result, err := e.context.EvalExpression(condition)
	if err != nil {
		log.Error("ConditionEvaluator.EvaluateCondition", "条件评估失败: %v", err)
		return false, fmt.Errorf("条件评估失败: %w", err)
	}

	// 转换结果为布尔值
	matched, ok := result.(bool)
	if !ok {
		log.Error("ConditionEvaluator.EvaluateCondition", "条件结果类型错误: %T", result)
		return false, fmt.Errorf("条件结果必须是布尔类型，实际类型: %T", result)
	}

	log.Debug("ConditionEvaluator.EvaluateCondition", "条件评估结果: %v", matched)
	return matched, nil
}
