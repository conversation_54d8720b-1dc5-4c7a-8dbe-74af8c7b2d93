package validator

import (
	"fmt"
	yamlparser "voderpltvv/erp_client/application/framework/yaml/parser"
	"voderpltvv/erp_client/application/framework/yaml/types"
	log "voderpltvv/util"
)

// ProcessValidator 流程验证器
type ProcessValidator struct {
}

// NewProcessValidator 创建流程验证器
func NewProcessValidator() *ProcessValidator {
	return &ProcessValidator{}
}

// ValidateProcess 验证流程定义
func (v *ProcessValidator) ValidateProcess(process *yamlparser.ProcessContent) error {
	log.Info("ProcessValidator.ValidateProcess", "开始验证流程定义")

	if process == nil {
		return fmt.Errorf("流程定义为空")
	}

	if process.Definition == nil {
		return fmt.Errorf("流程定义为空")
	}

	if process.Definition.ID == "" {
		return fmt.Errorf("流程ID为空")
	}

	if len(process.Steps) == 0 {
		return fmt.Errorf("流程步骤为空")
	}

	// 验证每个步骤
	for _, step := range process.Steps {
		if err := v.ValidateStep(&step); err != nil {
			return fmt.Errorf("步骤[%s]验证失败: %w", step.ID, err)
		}
	}

	log.Info("ProcessValidator.ValidateProcess", "流程定义验证完成")
	return nil
}

// ValidateStep 验证步骤定义
func (v *ProcessValidator) ValidateStep(step *types.Step) error {
	log.Info("ProcessValidator.ValidateStep", "开始验证步骤定义")

	if step == nil {
		return fmt.Errorf("步骤定义为空")
	}

	if step.ID == "" {
		return fmt.Errorf("步骤ID为空")
	}

	if step.Name == "" {
		return fmt.Errorf("步骤名称为空")
	}

	if step.Action == nil {
		return fmt.Errorf("步骤动作为空")
	}

	// 验证动作类型
	actionType, ok := step.Action["type"].(string)
	if !ok {
		return fmt.Errorf("动作类型必须是字符串")
	}

	// 根据动作类型进行特定验证
	switch types.ActionType(actionType) {
	case types.ActionTypeService:
		return v.validateServiceAction(step.Action)
	case types.ActionTypeSetField:
		return v.validateSetFieldAction(step.Action)
	case types.ActionTypeError:
		return v.validateErrorAction(step.Action)
	case types.ActionTypeTransform:
		return v.validateTransformAction(step.Action)
	case types.ActionTypeValidate:
		return v.validateValidateAction(step.Action)
	default:
		return fmt.Errorf("不支持的动作类型: %s", actionType)
	}
}

// validateServiceAction 验证服务调用动作
func (v *ProcessValidator) validateServiceAction(action map[string]interface{}) error {
	required := []string{"service", "method"}
	for _, field := range required {
		if _, ok := action[field].(string); !ok {
			return fmt.Errorf("服务动作缺少必需字段: %s", field)
		}
	}
	return nil
}

// validateSetFieldAction 验证字段设置动作
func (v *ProcessValidator) validateSetFieldAction(action map[string]interface{}) error {
	if _, ok := action["target"].(string); !ok {
		return fmt.Errorf("字段设置动作缺少必需字段: target")
	}
	if _, exists := action["value"]; !exists {
		return fmt.Errorf("字段设置动作缺少必需字段: value")
	}
	return nil
}

// validateErrorAction 验证错误处理动作
func (v *ProcessValidator) validateErrorAction(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("错误动作必须包含params字段")
	}

	required := []string{"code", "message"}
	for _, field := range required {
		if _, ok := params[field].(string); !ok {
			return fmt.Errorf("错误动作缺少必需字段: %s", field)
		}
	}
	return nil
}

// validateTransformAction 验证数据转换动作
func (v *ProcessValidator) validateTransformAction(action map[string]interface{}) error {
	params, ok := action["params"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("转换动作必须包含params字段")
	}

	required := []string{"source", "target", "mapping"}
	for _, field := range required {
		if _, exists := params[field]; !exists {
			return fmt.Errorf("转换动作缺少必需字段: %s", field)
		}
	}

	if _, ok := params["mapping"].(map[string]interface{}); !ok {
		return fmt.Errorf("转换动作的mapping必须是对象类型")
	}

	return nil
}

// validateValidateAction 验证验证动作
func (v *ProcessValidator) validateValidateAction(action map[string]interface{}) error {
	rules, ok := action["rules"].([]interface{})
	if !ok {
		return fmt.Errorf("验证动作缺少rules字段")
	}

	for i, r := range rules {
		rule, ok := r.(map[string]interface{})
		if !ok {
			return fmt.Errorf("规则[%d]格式错误", i)
		}

		required := []string{"type", "field", "field_name", "error_message", "error_code"}
		for _, field := range required {
			if _, ok := rule[field].(string); !ok {
				return fmt.Errorf("规则[%d]缺少必需字段: %s", i, field)
			}
		}
	}

	return nil
}
