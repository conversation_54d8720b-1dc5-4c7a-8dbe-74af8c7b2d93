package error

import (
	"fmt"
	"strconv"
	"voderpltvv/erp_client/domain/process/model"
	log "voderpltvv/util"
)

// ContextManager 上下文管理接口
type ContextManager interface {
	SetValue(path string, value interface{}) error
}

// ProcessError 流程错误
type ProcessError struct {
	HttpCode int                    `json:"-"`              // HTTP状态码
	Code     string                 `json:"code"`           // 业务错误码
	Message  string                 `json:"message"`        // 错误消息
	Data     map[string]interface{} `json:"data,omitempty"` // 错误详情
}

func (e *ProcessError) Error() string {
	return fmt.Sprintf("[%d] %s: %s", e.HttpCode, e.Code, e.Message)
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	context ContextManager
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(context ContextManager) *ErrorHandler {
	return &ErrorHandler{
		context: context,
	}
}

// HandleError 处理错误
func (h *ErrorHandler) HandleError(err error, errorHandler *model.ErrorHandler) error {
	if errorHandler == nil {
		// 如果没有错误处理器，直接返回错误
		return err
	}

	log.Debug("ErrorHandler.HandleError", "开始处理错误: %v", err)
	log.Debug("ErrorHandler.HandleError", "错误处理配置: %+v", errorHandler)

	switch errorHandler.Type {
	case model.ErrorHandlerTypeEarlyReturn:
		// 创建一个新的ProcessError
		httpCode := 400 // 默认使用400 Bad Request
		// 如果配置了http_code，使用配置的值
		if errorHandler.Mapping != nil {
			if codeStr, ok := errorHandler.Mapping["http_code"]; ok {
				if code, err := strconv.Atoi(codeStr); err == nil {
					httpCode = code
				}
			}
		}

		processError := &ProcessError{
			HttpCode: httpCode,
			Code:     errorHandler.ErrorCode,
			Message:  errorHandler.ErrorMessage,
			Data: map[string]interface{}{
				"original_error": err.Error(),
			},
		}
		log.Debug("ErrorHandler.HandleError", "提前返回错误: %+v", processError)
		return processError

	case model.ErrorHandlerTypeContinue:
		// 记录错误但继续执行
		log.Debug("ErrorHandler.HandleError", "错误已记录但继续执行: %v", err)
		// 如果配置了错误映射目标，则保存错误信息
		if errorHandler.Target != "" && len(errorHandler.Mapping) > 0 {
			errorData := map[string]interface{}{
				"error_message": err.Error(),
				"error_code":    errorHandler.ErrorCode,
			}
			if err := h.context.SetValue(errorHandler.Target, errorData); err != nil {
				return fmt.Errorf("保存错误信息失败: %w", err)
			}
		}
		return nil

	default:
		return fmt.Errorf("不支持的错误处理类型: %s", errorHandler.Type)
	}
}

// CreateProcessError 创建流程错误
func (h *ErrorHandler) CreateProcessError(code string, message string, data map[string]interface{}) error {
	return &ProcessError{
		HttpCode: 400, // 默认使用400 Bad Request
		Code:     code,
		Message:  message,
		Data:     data,
	}
}

// IsProcessError 判断是否为流程错误
func (h *ErrorHandler) IsProcessError(err error) bool {
	_, ok := err.(*ProcessError)
	return ok
}

// GetProcessError 获取流程错误
func (h *ErrorHandler) GetProcessError(err error) *ProcessError {
	if processError, ok := err.(*ProcessError); ok {
		return processError
	}
	return nil
}
