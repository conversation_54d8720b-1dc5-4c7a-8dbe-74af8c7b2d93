package engine

import (
	"voderpltvv/erp_client/domain/process/model"
)

// BaseProcess 基础流程实现
type BaseProcess struct {
	id          string
	name        string
	description string
	status      model.ProcessStatus
}

// NewBaseProcess 创建基础流程
func NewBaseProcess(id string, name string, description string) *BaseProcess {
	return &BaseProcess{
		id:          id,
		name:        name,
		description: description,
		status:      model.ProcessStatusInit,
	}
}

// GetID 获取流程ID
func (p *BaseProcess) GetID() string {
	return p.id
}

// GetName 获取流程名称
func (p *BaseProcess) GetName() string {
	return p.name
}

// GetDescription 获取流程描述
func (p *BaseProcess) GetDescription() string {
	return p.description
}

// GetStatus 获取流程状态
func (p *BaseProcess) GetStatus() model.ProcessStatus {
	return p.status
}

// SetStatus 设置流程状态
func (p *BaseProcess) SetStatus(status model.ProcessStatus) {
	p.status = status
}
