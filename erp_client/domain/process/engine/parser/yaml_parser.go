package parser

import (
	"fmt"
	yamlparser "voderpltvv/erp_client/application/framework/yaml/parser"
	log "voderpltvv/util"
)

// YAMLParser YAML解析器
type YAMLParser struct {
	content []byte
}

// NewYAMLParser 创建YAML解析器
func NewYAMLParser(content []byte) *YAMLParser {
	return &YAMLParser{
		content: content,
	}
}

// ParseProcess 解析流程定义
func (p *YAMLParser) ParseProcess() (*yamlparser.ProcessContent, error) {
	log.Info("YAMLParser.ParseProcess", "开始解析流程定义")

	parser := yamlparser.NewProcessParser(p.content)
	proc, err := parser.ParseContent()
	if err != nil {
		log.Error("YAMLParser.ParseProcess", "解析流程定义失败: %v", err)
		return nil, fmt.Errorf("解析流程定义失败: %w", err)
	}

	// 验证流程ID
	if proc.Definition == nil || proc.Definition.ID == "" {
		log.Error("YAMLParser.ParseProcess", "流程定义缺少ID")
		return nil, fmt.Errorf("流程定义缺少ID")
	}

	log.Info("YAMLParser.ParseProcess", "流程定义解析完成")
	return proc, nil
}
