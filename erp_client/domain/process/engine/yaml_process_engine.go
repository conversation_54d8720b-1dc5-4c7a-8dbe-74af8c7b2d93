package engine

import (
	"context"
	"fmt"
	"reflect"

	"gopkg.in/yaml.v3"

	"voderpltvv/erp_client/application/framework/runtime/variable"
	yamltypes "voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/process/model"
	rulemodel "voderpltvv/erp_client/domain/rule/model"
	log "voderpltvv/util"
)

// YAMLProcessEngine YAML流程引擎
type YAMLProcessEngine struct {
	processDefMap map[string]*yamltypes.YAMLProcessContent
	services      map[string]interface{}
}

// NewYAMLProcessEngine 创建YAML流程引擎
func NewYAMLProcessEngine() *YAMLProcessEngine {
	return &YAMLProcessEngine{
		processDefMap: make(map[string]*yamltypes.YAMLProcessContent),
		services:      make(map[string]interface{}),
	}
}

// LoadProcessFromYAML 从YAML加载流程定义
func (e *YAMLProcessEngine) LoadProcessFromYAML(yamlContent []byte) error {
	var proc yamltypes.YAMLProcessContent
	if err := yaml.Unmarshal(yamlContent, &proc); err != nil {
		return fmt.Errorf("解析YAML失败: %w", err)
	}

	if proc.Definition == nil || proc.Definition.ID == "" {
		return fmt.Errorf("流程ID不能为空")
	}

	e.processDefMap[proc.Definition.ID] = &proc
	return nil
}

// RegisterService 注册服务
func (e *YAMLProcessEngine) RegisterService(name string, service interface{}) {
	e.services[name] = service
}

// Execute 执行流程
func (e *YAMLProcessEngine) Execute(ctx context.Context, processID string, params map[string]interface{}) (map[string]interface{}, error) {
	log.Info("YAMLProcessEngine.Execute", "开始执行流程: %s", processID)
	log.Debug("YAMLProcessEngine.Execute", "输入参数: %+v", params)

	proc, exists := e.processDefMap[processID]
	if !exists {
		log.Error("YAMLProcessEngine.Execute", "流程未定义: %s", processID)
		return nil, fmt.Errorf("流程未定义: %s", processID)
	}

	// 创建流程上下文
	processCtx := model.NewProcessContext(processID)

	// 设置输入参数
	log.Info("YAMLProcessEngine.Execute", "设置输入参数")
	if err := e.setInputParams(processCtx, params); err != nil {
		log.Error("YAMLProcessEngine.Execute", "设置输入参数失败: %v", err)
		return nil, fmt.Errorf("设置输入参数失败: %w", err)
	}

	// 执行步骤
	log.Info("YAMLProcessEngine.Execute", "开始执行步骤")
	for _, step := range proc.Steps {
		log.Info("YAMLProcessEngine.Execute", "执行步骤: %s (%s)", step.ID, step.Name)
		if err := e.executeStep(ctx, step, processCtx); err != nil {
			log.Error("YAMLProcessEngine.Execute", "执行步骤失败: %v", err)
			return nil, fmt.Errorf("执行步骤[%s]失败: %w", step.ID, err)
		}
		log.Info("YAMLProcessEngine.Execute", "步骤执行完成: %s", step.ID)
	}

	// 收集输出
	log.Info("YAMLProcessEngine.Execute", "收集输出数据")
	return e.getOutputParams(processCtx, proc)
}

// setInputParams 设置输入参数
func (e *YAMLProcessEngine) setInputParams(ctx model.ProcessContext, params map[string]interface{}) error {
	for name, value := range params {
		if err := ctx.GetStore().SetValue(name, value); err != nil {
			return fmt.Errorf("设置输入参数[%s]失败: %w", name, err)
		}
	}
	return nil
}

// getOutputParams 获取输出参数
func (e *YAMLProcessEngine) getOutputParams(ctx model.ProcessContext, proc *yamltypes.YAMLProcessContent) (map[string]interface{}, error) {
	output := make(map[string]interface{})
	for _, outDef := range proc.Definition.Output {
		value, err := ctx.GetStore().GetValue(outDef.Name)
		if err == nil && value != nil {
			output[outDef.Name] = value
		}
	}
	return output, nil
}

// executeStep 执行步骤
func (e *YAMLProcessEngine) executeStep(ctx context.Context, step yamltypes.YAMLStep, processCtx model.ProcessContext) error {
	log.Info("YAMLProcessEngine.executeStep", "开始执行步骤: %s", step.ID)

	// 检查条件
	if step.Condition != "" {
		log.Debug("YAMLProcessEngine.executeStep", "计算条件表达式: %s", step.Condition)
		matched, err := e.evaluateCondition(step.Condition, processCtx)
		if err != nil {
			log.Error("YAMLProcessEngine.executeStep", "条件评估失败: %v", err)
			return fmt.Errorf("条件评估失败: %w", err)
		}

		log.Debug("YAMLProcessEngine.executeStep", "条件表达式结果: %v", matched)
		if !matched {
			log.Info("YAMLProcessEngine.executeStep", "条件不满足，跳过步骤")
			return nil
		}
		log.Info("YAMLProcessEngine.executeStep", "条件满足，继续执行")
	}

	// 执行动作
	actionType := step.Action["type"].(string)
	log.Info("YAMLProcessEngine.executeStep", "开始执行动作: %s", actionType)

	var err error
	switch actionType {
	case "service":
		err = e.executeServiceAction(ctx, step.Action, processCtx)
	case "set_field":
		err = e.executeSetFieldAction(step.Action, processCtx)
	case "error":
		err = e.executeErrorAction(step.Action)
	case "transform":
		err = e.executeTransformAction(step.Action, processCtx)
	case "validate":
		err = e.executeValidateAction(step.Action, processCtx)
	default:
		err = fmt.Errorf("不支持的动作类型: %s", actionType)
	}

	if err != nil {
		log.Error("YAMLProcessEngine.executeStep", "执行动作失败: %v", err)
		return err
	}

	log.Info("YAMLProcessEngine.executeStep", "动作执行完成")
	log.Info("YAMLProcessEngine.executeStep", "步骤执行完成: %s", step.ID)
	return nil
}

// evaluateCondition 评估条件
func (e *YAMLProcessEngine) evaluateCondition(condition string, ctx model.ProcessContext) (bool, error) {
	result, err := e.evaluateExpression(condition, ctx)
	if err != nil {
		return false, err
	}
	return result.(bool), nil
}

// evaluateExpression 评估表达式
func (e *YAMLProcessEngine) evaluateExpression(expr string, ctx model.ProcessContext) (interface{}, error) {
	// 简单的变量替换
	if expr[0] == '$' {
		value, err := ctx.GetStore().GetValue(expr[1:])
		if err != nil {
			return nil, err
		}
		return value, nil
	}
	return expr, nil
}

// executeServiceAction 执行服务动作
func (e *YAMLProcessEngine) executeServiceAction(ctx context.Context, action map[string]interface{}, processCtx model.ProcessContext) error {
	serviceName := action["service"].(string)
	methodName := action["method"].(string)

	service, exists := e.services[serviceName]
	if !exists {
		return fmt.Errorf("服务未注册: %s", serviceName)
	}

	method := reflect.ValueOf(service).MethodByName(methodName)
	if !method.IsValid() {
		return fmt.Errorf("方法不存在: %s.%s", serviceName, methodName)
	}

	// 准备参数
	var params []interface{}
	if paramsConfig, ok := action["params"].([]interface{}); ok {
		params = paramsConfig
	}

	// 特殊处理规则服务
	if serviceName == "ruleService" {
		return e.executeRuleServiceAction(ctx, action, processCtx, method)
	}

	// 执行服务调用
	args := e.prepareParameters(params, processCtx)
	results := method.Call(args)

	// 处理返回值
	if len(results) > 0 {
		if err := e.handleServiceOutput(action, results, processCtx); err != nil {
			return err
		}
	}

	return nil
}

// executeRuleServiceAction 执行规则服务动作
func (e *YAMLProcessEngine) executeRuleServiceAction(ctx context.Context, action map[string]interface{}, processCtx model.ProcessContext, method reflect.Value) error {
	if params, ok := action["params"].([]interface{}); ok {
		// 处理 ruleGroupId
		var ruleGroupId string
		var inputData map[string]interface{}

		for _, param := range params {
			if paramMap, ok := param.(map[string]interface{}); ok {
				if paramMap["name"] == "ruleGroupId" {
					ruleGroupId = paramMap["value"].(string)
				} else if paramMap["name"] == "input" {
					// 处理 input 参数中的表达式
					inputValue := paramMap["value"].(map[string]interface{})
					processedData, err := e.processNestedExpressions(inputValue, processCtx)
					if err != nil {
						return fmt.Errorf("处理规则输入参数失败: %w", err)
					}
					inputData = processedData.(map[string]interface{})
				}
			}
		}

		// 创建 RuleContext
		ruleCtx := rulemodel.NewRuleContext()
		// 设置输入数据
		if err := ruleCtx.SetValue("input", inputData); err != nil {
			return fmt.Errorf("设置规则上下文输入参数失败: %w", err)
		}

		// 准备参数
		values := make([]reflect.Value, 0)
		values = append(values, reflect.ValueOf(ruleGroupId))
		values = append(values, reflect.ValueOf(ruleCtx))

		// 执行规则服务调用
		results := method.Call(values)

		// 处理返回值
		if len(results) > 0 {
			if err := e.handleServiceOutput(action, results, processCtx); err != nil {
				return err
			}
		}
	}

	return nil
}

// processNestedExpressions 处理嵌套表达式
func (e *YAMLProcessEngine) processNestedExpressions(data interface{}, ctx model.ProcessContext) (interface{}, error) {
	switch v := data.(type) {
	case string:
		if v[0] == '$' {
			return e.evaluateExpression(v, ctx)
		}
		return v, nil
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			processed, err := e.processNestedExpressions(value, ctx)
			if err != nil {
				return nil, err
			}
			result[key] = processed
		}
		return result, nil
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, value := range v {
			processed, err := e.processNestedExpressions(value, ctx)
			if err != nil {
				return nil, err
			}
			result[i] = processed
		}
		return result, nil
	default:
		return v, nil
	}
}

// prepareParameters 准备参数
func (e *YAMLProcessEngine) prepareParameters(params []interface{}, ctx model.ProcessContext) []reflect.Value {
	values := make([]reflect.Value, 0)
	for _, param := range params {
		switch v := param.(type) {
		case string:
			if v[0] == '$' {
				value, err := ctx.GetStore().GetValue(v[1:])
				if err == nil {
					values = append(values, reflect.ValueOf(value))
				} else {
					values = append(values, reflect.ValueOf(v))
				}
			} else {
				values = append(values, reflect.ValueOf(v))
			}
		default:
			values = append(values, reflect.ValueOf(v))
		}
	}
	return values
}

// handleServiceOutput 处理服务输出
func (e *YAMLProcessEngine) handleServiceOutput(action map[string]interface{}, results []reflect.Value, ctx model.ProcessContext) error {
	if output, ok := action["output"].(map[string]interface{}); ok {
		target := output["target"].(string)
		mappings := output["mapping"].(map[string]interface{})

		// 将服务结果转换为 map[string]interface{}
		resultMap := make(map[string]interface{})
		if len(results) > 0 && !results[0].IsNil() {
			val := results[0]
			if val.Kind() == reflect.Ptr {
				val = val.Elem()
			}
			if val.Kind() == reflect.Struct {
				for i := 0; i < val.NumField(); i++ {
					field := val.Type().Field(i)
					fieldValue := val.Field(i)
					if fieldValue.Kind() == reflect.Ptr && !fieldValue.IsNil() {
						resultMap[field.Name] = fieldValue.Elem().Interface()
					} else if fieldValue.Kind() != reflect.Ptr {
						resultMap[field.Name] = fieldValue.Interface()
					}
				}
			}
		}

		// 创建临时运行时
		tempRuntime := variable.NewTemporaryRuntime()
		if err := tempRuntime.Set("result", resultMap); err != nil {
			return fmt.Errorf("设置临时结果失败: %w", err)
		}

		// 执行映射
		stringMappings := make(map[string]string)
		for field, expr := range mappings {
			stringMappings[field] = expr.(string)
		}

		if err := tempRuntime.Map(target, stringMappings); err != nil {
			return fmt.Errorf("执行映射失败: %w", err)
		}

		// 获取映射结果并设置到原始运行时
		result, err := tempRuntime.Get(target)
		if err != nil {
			return fmt.Errorf("获取映射结果失败: %w", err)
		}

		if result == nil {
			result = make(map[string]interface{})
		}

		return ctx.GetStore().SetValue(target, result)
	}
	return nil
}

// executeSetFieldAction 执行设置字段动作
func (e *YAMLProcessEngine) executeSetFieldAction(action map[string]interface{}, ctx model.ProcessContext) error {
	target := action["target"].(string)
	value := action["value"].(string)

	result, err := e.evaluateExpression(value, ctx)
	if err != nil {
		return fmt.Errorf("计算表达式失败: %w", err)
	}

	return ctx.GetStore().SetValue(target, result)
}

// executeErrorAction 执行错误动作
func (e *YAMLProcessEngine) executeErrorAction(action map[string]interface{}) error {
	params := action["params"].(map[string]interface{})
	code := params["code"].(string)
	message := params["message"].(string)

	return &model.ProcessError{
		Code:    code,
		Message: message,
		Data:    params,
	}
}

// executeTransformAction 执行转换动作
func (e *YAMLProcessEngine) executeTransformAction(action map[string]interface{}, ctx model.ProcessContext) error {
	params := action["params"].(map[string]interface{})
	source := params["source"].(string)
	target := params["target"].(string)
	mappings := params["mapping"].(map[string]interface{})

	// 转换映射
	stringMappings := make(map[string]string)
	for field, expr := range mappings {
		stringMappings[field] = expr.(string)
	}

	// 获取源数据
	sourceData, err := e.evaluateExpression(source, ctx)
	if err != nil {
		return fmt.Errorf("获取源数据失败: %w", err)
	}

	// 设置目标数据
	if err := ctx.GetStore().SetValue(target, sourceData); err != nil {
		return fmt.Errorf("设置目标数据失败: %w", err)
	}

	return nil
}

// executeValidateAction 执行验证动作
func (e *YAMLProcessEngine) executeValidateAction(action map[string]interface{}, ctx model.ProcessContext) error {
	// 获取验证规则
	rules, ok := action["rules"].([]interface{})
	if !ok {
		return fmt.Errorf("验证动作缺少rules字段")
	}

	// 获取验证服务
	service, exists := e.services["validateService"]
	if !exists || service == nil {
		return fmt.Errorf("验证服务未注册")
	}

	validateService, ok := service.(model.ValidateService)
	if !ok {
		return fmt.Errorf("验证服务类型错误")
	}

	// 转换规则格式
	validationRules := make([]model.ValidationRule, 0, len(rules))
	for _, r := range rules {
		ruleMap, ok := r.(map[string]interface{})
		if !ok {
			return fmt.Errorf("规则格式错误")
		}

		// 构建验证规则
		rule := model.ValidationRule{
			Type:         ruleMap["type"].(string),
			Field:        ruleMap["field"].(string),
			FieldName:    ruleMap["field_name"].(string),
			ErrorMessage: ruleMap["error_message"].(string),
			ErrorCode:    ruleMap["error_code"].(string),
		}

		// 处理可选的params
		if params, exists := ruleMap["params"].(map[string]interface{}); exists {
			rule.Params = params
		}

		validationRules = append(validationRules, rule)
	}

	// 使用runtime直接获取要验证的字段值
	data := make(map[string]interface{})
	for _, rule := range validationRules {
		value, err := e.evaluateExpression(rule.Field, ctx)
		if err != nil {
			// 如果字段不存在，设置为nil，让验证规则决定是否允许
			data[rule.Field] = nil
			continue
		}
		data[rule.Field] = value
	}

	// 执行验证
	if err := validateService.ValidateRules(validationRules, data); err != nil {
		// 处理验证错误
		if errorHandler, ok := action["error_handler"].(map[string]interface{}); ok {
			handlerType := errorHandler["type"].(string)
			errorMessage := errorHandler["error_message"].(string)
			errorCode := errorHandler["error_code"].(string)

			if handlerType == "early_return" {
				return &model.ProcessError{
					Code:    errorCode,
					Message: errorMessage,
					Data: map[string]interface{}{
						"original_error": err.Error(),
					},
				}
			}
		}
		return err
	}

	return nil
}
