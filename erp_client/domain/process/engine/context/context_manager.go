package context

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	log "voderpltvv/util"
)

// ContextManager 上下文管理器
type ContextManager struct {
	runtime variable.IRuntime
}

// NewContextManager 创建上下文管理器
func NewContextManager(runtime variable.IRuntime) *ContextManager {
	return &ContextManager{
		runtime: runtime,
	}
}

// LoadMetadata 加载元数据到Runtime
func (m *ContextManager) LoadMetadata(metadata *variable.Metadata) error {
	log.Info("ContextManager.LoadMetadata", "开始加载元数据")
	if err := m.runtime.LoadMetadata(metadata); err != nil {
		log.Error("ContextManager.LoadMetadata", "加载元数据失败: %v", err)
		return fmt.Errorf("加载元数据失败: %w", err)
	}
	return nil
}

// SetInputParams 设置输入参数
func (m *ContextManager) SetInputParams(params map[string]interface{}) error {
	log.Info("ContextManager.SetInputParams", "设置输入参数")
	if err := m.runtime.Set("input", params); err != nil {
		log.Error("ContextManager.SetInputParams", "设置输入参数失败: %v", err)
		return fmt.Errorf("设置输入参数失败: %w", err)
	}
	return nil
}

// GetOutputParams 获取输出参数
func (m *ContextManager) GetOutputParams() (map[string]interface{}, error) {
	log.Info("ContextManager.GetOutputParams", "获取输出参数")
	output, err := m.runtime.Get("output")
	if err != nil {
		log.Error("ContextManager.GetOutputParams", "获取输出参数失败: %v", err)
		return nil, err
	}

	result, ok := output.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("输出数据类型错误")
	}

	return result, nil
}

// EvalExpression 计算表达式
func (m *ContextManager) EvalExpression(expression string) (interface{}, error) {
	log.Debug("ContextManager.EvalExpression", "计算表达式: %s", expression)
	result, err := m.runtime.Eval(expression)
	if err != nil {
		log.Error("ContextManager.EvalExpression", "计算表达式失败: %v", err)
		return nil, fmt.Errorf("计算表达式失败: %w", err)
	}
	return result, nil
}

// SetValue 设置值
func (m *ContextManager) SetValue(path string, value interface{}) error {
	log.Debug("ContextManager.SetValue", "设置值, 路径: %s", path)
	if err := m.runtime.Set(path, value); err != nil {
		log.Error("ContextManager.SetValue", "设置值失败: %v", err)
		return fmt.Errorf("设置值失败: %w", err)
	}
	return nil
}

// GetValue 获取值
func (m *ContextManager) GetValue(path string) (interface{}, error) {
	log.Debug("ContextManager.GetValue", "获取值, 路径: %s", path)
	value, err := m.runtime.Get(path)
	if err != nil {
		log.Error("ContextManager.GetValue", "获取值失败: %v", err)
		return nil, fmt.Errorf("获取值失败: %w", err)
	}
	return value, nil
}

// CreateNewRuntime 创建新的运行时
func (m *ContextManager) CreateNewRuntime() {
	store := variable.NewStore()
	m.runtime = variable.NewRuntime(store)
}
