package context

import (
	"voderpltvv/erp_client/domain/engine/common"
)

// RuntimeAdapter 适配 ContextManager 到 IRuntime 接口
type RuntimeAdapter struct {
	ctx *ContextManager
}

// NewRuntimeAdapter 创建运行时适配器
func NewRuntimeAdapter(ctx *ContextManager) common.IRuntime {
	return &RuntimeAdapter{
		ctx: ctx,
	}
}

// Get 实现 IRuntime 接口
func (r *RuntimeAdapter) Get(path string) (interface{}, error) {
	return r.ctx.GetValue(path)
}

// Set 实现 IRuntime 接口
func (r *RuntimeAdapter) Set(path string, value interface{}) error {
	return r.ctx.SetValue(path, value)
}

// GetNamespace 实现 IRuntime 接口
func (r *RuntimeAdapter) GetNamespace(namespace string) (map[string]interface{}, error) {
	value, err := r.ctx.GetValue(namespace)
	if err != nil {
		return nil, err
	}
	if m, ok := value.(map[string]interface{}); ok {
		return m, nil
	}
	return make(map[string]interface{}), nil
}

// EvalExpression 实现 IRuntime 接口
func (r *RuntimeAdapter) EvalExpression(expression string) (interface{}, error) {
	return r.ctx.EvalExpression(expression)
}
