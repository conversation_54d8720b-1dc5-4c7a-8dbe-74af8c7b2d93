package engine

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"

	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/validate"
	yamlparser "voderpltvv/erp_client/application/framework/yaml/parser"
	yamltypes "voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common"
	"voderpltvv/erp_client/domain/process/engine/action"
	"voderpltvv/erp_client/domain/process/engine/condition"
	ctxmanager "voderpltvv/erp_client/domain/process/engine/context"
	errorhandler "voderpltvv/erp_client/domain/process/engine/error"
	"voderpltvv/erp_client/domain/process/engine/executor"
	"voderpltvv/erp_client/domain/process/engine/metadata"
	"voderpltvv/erp_client/domain/process/engine/parser"
	"voderpltvv/erp_client/domain/process/engine/validator"
	"voderpltvv/erp_client/domain/process/model"
	rulemodel "voderpltvv/erp_client/domain/rule/model"
	log "voderpltvv/util"
)

// Engine YAML流程引擎
type Engine struct {
	processDefMap map[string]*yamlparser.ProcessContent
	services      map[string]interface{}
	processes     sync.Map // 流程集合，key为processID，value为Flow
	steps         sync.Map // 步骤集合，key为processID:stepID，value为Step

	// 组件
	parser      *parser.YAMLParser
	validator   *validator.ProcessValidator
	executor    *executor.StepExecutor
	context     *ctxmanager.ContextManager
	action      *action.ActionHandler
	evaluator   *condition.ConditionEvaluator
	varManager  *common.VariableManager
	errHandler  *errorhandler.ErrorHandler
	metaManager *metadata.MetadataManager
}

// NewEngine 创建YAML流程引擎
func NewEngine() model.Engine {
	store := variable.NewStore()
	runtime := variable.NewRuntime(store)
	ctx := ctxmanager.NewContextManager(runtime)
	adapter := ctxmanager.NewRuntimeAdapter(ctx)

	return &Engine{
		processDefMap: make(map[string]*yamlparser.ProcessContent),
		services:      make(map[string]interface{}),
		validator:     validator.NewProcessValidator(),
		executor:      executor.NewStepExecutor(),
		context:       ctx,
		action:        action.NewActionHandler(make(map[string]interface{}), ctx),
		evaluator:     condition.NewConditionEvaluator(ctx),
		varManager:    common.NewVariableManager(adapter),
		errHandler:    errorhandler.NewErrorHandler(ctx),
		metaManager:   metadata.NewMetadataManager(),
	}
}

// ProcessError 流程错误
type ProcessError struct {
	HttpCode int                    `json:"-"`              // HTTP状态码
	Code     string                 `json:"code"`           // 业务错误码
	Message  string                 `json:"message"`        // 错误消息
	Data     map[string]interface{} `json:"data,omitempty"` // 错误详情
}

func (e *ProcessError) Error() string {
	return fmt.Sprintf("[%d] %s: %s", e.HttpCode, e.Code, e.Message)
}

// LoadProcess 从YAML加载流程定义
func (e *Engine) LoadProcess(processContent []byte) error {
	log.Info("Engine.LoadProcess", "开始加载流程定义")

	// 使用解析器
	e.parser = parser.NewYAMLParser(processContent)
	proc, err := e.parser.ParseProcess()
	if err != nil {
		log.Error("Engine.LoadProcess", "解析流程定义失败: %v", err)
		return fmt.Errorf("解析流程定义失败: %w", err)
	}

	// 使用验证器验证流程定义
	if err := e.validator.ValidateProcess(proc); err != nil {
		log.Error("Engine.LoadProcess", "验证流程定义失败: %v", err)
		return fmt.Errorf("验证流程定义失败: %w", err)
	}

	processID := proc.Definition.ID
	log.Info("Engine.LoadProcess", "流程ID: %s", processID)

	// 加载元数据到Runtime
	if proc.Metadata != nil {
		log.Info("Engine.LoadProcess", "加载元数据")
		if err := e.context.LoadMetadata(proc.Metadata); err != nil {
			log.Error("Engine.LoadProcess", "加载元数据失败: %v", err)
			return fmt.Errorf("加载元数据失败: %w", err)
		}
	}

	e.processDefMap[processID] = proc
	log.Info("Engine.LoadProcess", "流程定义加载完成")
	return nil
}

// RegisterService 注册服务
func (e *Engine) RegisterService(name string, service interface{}) {
	e.services[name] = service
	e.action = action.NewActionHandler(e.services, e.context)
}

// RegisterProcess 注册流程
func (e *Engine) RegisterProcess(process model.Flow) error {
	if process == nil {
		return fmt.Errorf("process is nil")
	}
	e.processes.Store(process.GetID(), process)
	return nil
}

// UnregisterProcess 注销流程
func (e *Engine) UnregisterProcess(processID string) error {
	e.processes.Delete(processID)
	// 删除流程下的所有步骤
	e.steps.Range(func(key, value interface{}) bool {
		if stepKey, ok := key.(string); ok {
			if processIDFromKey := getProcessIDFromKey(stepKey); processIDFromKey == processID {
				e.steps.Delete(key)
			}
		}
		return true
	})
	return nil
}

// GetProcess 获取流程
func (e *Engine) GetProcess(processID string) (model.Flow, error) {
	process, ok := e.processes.Load(processID)
	if !ok {
		return nil, fmt.Errorf("process not found: %s", processID)
	}
	return process.(model.Flow), nil
}

// RegisterStep 注册步骤
func (e *Engine) RegisterStep(processID string, step model.Step) error {
	if step == nil {
		return fmt.Errorf("step is nil")
	}
	e.steps.Store(getStepKey(processID, step.GetID()), step)
	return nil
}

// UnregisterStep 注销步骤
func (e *Engine) UnregisterStep(processID string, stepID string) error {
	e.steps.Delete(getStepKey(processID, stepID))
	return nil
}

// GetStep 获取步骤
func (e *Engine) GetStep(processID string, stepID string) (model.Step, error) {
	step, ok := e.steps.Load(getStepKey(processID, stepID))
	if !ok {
		return nil, fmt.Errorf("step not found: %s", stepID)
	}
	return step.(model.Step), nil
}

// getStepKey 获取步骤键
func getStepKey(processID string, stepID string) string {
	return fmt.Sprintf("%s:%s", processID, stepID)
}

// getProcessIDFromKey 从键中获取流程ID
func getProcessIDFromKey(key string) string {
	parts := strings.Split(key, ":")
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

// ExecuteStep 执行步骤
func (e *Engine) ExecuteStep(ctx context.Context, processID string, stepID string, params map[string]interface{}) (map[string]interface{}, error) {
	step, err := e.GetStep(processID, stepID)
	if err != nil {
		return nil, err
	}

	// 使用新的执行器
	return e.executor.ExecuteStep(ctx, step, params)
}

// Execute 执行流程
func (e *Engine) Execute(ctx context.Context, processID string, params map[string]interface{}) (map[string]interface{}, error) {
	log.Info("Engine.Execute", "开始执行流程: %s", processID)
	log.Debug("Engine.Execute", "输入参数: %+v", params)

	proc, exists := e.processDefMap[processID]
	if !exists {
		log.Error("Engine.Execute", "流程未定义: %s", processID)
		return nil, fmt.Errorf("流程未定义: %s", processID)
	}

	// 创建新的运行时
	e.context.CreateNewRuntime()

	// 加载元数据到Runtime
	log.Info("Engine.Execute", "解析元数据")
	metadata, err := proc.Parser.ParseMetadata()
	if err != nil {
		log.Error("Engine.Execute", "解析元数据失败: %v", err)
		return nil, fmt.Errorf("解析元数据失败: %w", err)
	}

	// 转换元数据类型
	runtimeMetadata := e.convertMetadata(metadata)

	log.Info("Engine.Execute", "加载元数据到Runtime")
	if err := e.context.LoadMetadata(runtimeMetadata); err != nil {
		log.Error("Engine.Execute", "加载元数据失败: %v", err)
		return nil, fmt.Errorf("加载元数据失败: %w", err)
	}

	// 设置输入参数
	log.Info("Engine.Execute", "设置输入参数")
	if err := e.context.SetInputParams(params); err != nil {
		log.Error("Engine.Execute", "设置输入参数失败: %v", err)
		return nil, fmt.Errorf("设置输入参数失败: %w", err)
	}

	// 执行步骤
	log.Info("Engine.Execute", "开始执行步骤")
	for _, step := range proc.Steps {
		log.Info("Engine.Execute", "执行步骤: %s (%s)", step.ID, step.Name)
		// 转换步骤类型
		processStep := model.ProcessStep{
			ID:        step.ID,
			Name:      step.Name,
			Condition: step.Condition,
			Action:    step.Action,
		}
		if err := e.executeStep(ctx, processStep); err != nil {
			log.Error("Engine.Execute", "执行步骤失败: %v", err)
			return nil, fmt.Errorf("执行步骤[%s]失败: %w", step.ID, err)
		}
		log.Info("Engine.Execute", "步骤执行完成: %s", step.ID)
	}

	// 收集输出
	log.Info("Engine.Execute", "收集输出数据")
	return e.context.GetOutputParams()
}

// executeStep 执行步骤
func (e *Engine) executeStep(ctx context.Context, step model.ProcessStep) error {
	log.Info("Engine.executeStep", "开始执行步骤: %s", step.ID)

	// 检查条件
	if step.Condition != "" {
		log.Debug("Engine.executeStep", "计算条件表达式: %s", step.Condition)

		// 获取当前环境变量状态
		if env, err := e.context.GetValue("input"); err == nil {
			log.Debug("Engine.executeStep", "当前输入变量状态: %+v", env)
		}

		matched, err := e.evaluator.EvaluateCondition(step.Condition)
		if err != nil {
			log.Error("Engine.executeStep", "条件评估失败: %v", err)
			return fmt.Errorf("条件评估失败: %w", err)
		}

		log.Debug("Engine.executeStep", "条件表达式结果: %v", matched)

		if !matched {
			log.Info("Engine.executeStep", "条件不满足，跳过步骤")
			return nil // 条件不满足，跳过步骤
		}
		log.Info("Engine.executeStep", "条件满足，继续执行")
	}

	actionType := model.ActionType(step.Action["type"].(string))
	log.Info("Engine.executeStep", "开始执行动作: %s", actionType)

	// 使用 ActionHandler 处理动作
	if err := e.action.HandleAction(ctx, actionType, step.Action); err != nil {
		log.Error("Engine.executeStep", "执行动作失败: %v", err)
		return err
	}

	log.Info("Engine.executeStep", "动作执行完成")
	log.Info("Engine.executeStep", "步骤执行完成: %s", step.ID)
	return nil
}

// convertFields 转换字段定义
func (e *Engine) convertFields(fields []model.Field) []*variable.FieldDefinition {
	if fields == nil {
		return nil
	}
	result := make([]*variable.FieldDefinition, 0, len(fields))
	for _, f := range fields {
		field := &variable.FieldDefinition{
			Name:     f.Name,
			Required: f.Required,
		}
		result = append(result, field)
	}
	return result
}

// handleServiceError 处理服务错误
func (e *Engine) handleServiceError(err error, errorHandler *model.ErrorHandler) error {
	return e.errHandler.HandleError(err, errorHandler)
}

// executeServiceAction 执行服务调用动作
func (e *Engine) executeServiceAction(ctx context.Context, action map[string]interface{}) error {
	serviceName := action["service"].(string)
	methodName := action["method"].(string)

	log.Info("Engine.executeServiceAction", "服务调用: %s.%s", serviceName, methodName)

	service, exists := e.services[serviceName]
	if !exists {
		return fmt.Errorf("服务未注册: %s", serviceName)
	}

	// 获取方法
	method := reflect.ValueOf(service).MethodByName(methodName)
	if !method.IsValid() {
		return fmt.Errorf("方法不存在: %s.%s", serviceName, methodName)
	}

	// 准备参数列表
	var values []reflect.Value

	// 如果第一个参数是 context.Context，添加上下文参数
	if method.Type().NumIn() > 0 && method.Type().In(0).Implements(reflect.TypeOf((*context.Context)(nil)).Elem()) {
		values = append(values, reflect.ValueOf(ctx))
	}

	// 特殊处理 ruleService 的参数
	if serviceName == "ruleService" {
		if params, ok := action["params"].([]interface{}); ok {
			// 处理 ruleGroupId
			var ruleGroupId string
			var inputData map[string]interface{}

			for _, param := range params {
				if paramMap, ok := param.(map[string]interface{}); ok {
					if paramMap["name"] == "ruleGroupId" {
						ruleGroupId = paramMap["value"].(string)
					} else if paramMap["name"] == "input" {
						// 处理 input 参数中的表达式
						inputValue := paramMap["value"].(map[string]interface{})
						processedData, err := e.varManager.ProcessNestedExpressions(inputValue)
						if err != nil {
							return fmt.Errorf("处理规则输入参数失败: %w", err)
						}
						inputData = processedData.(map[string]interface{})
					}
				}
			}

			// 创建 RuleContext
			ruleCtx := rulemodel.NewRuleContext()
			// 设置输入数据
			if err := ruleCtx.SetValue("input", inputData); err != nil {
				return fmt.Errorf("设置规则上下文输入参数失败: %w", err)
			}

			// 添加 ruleGroupId 参数
			values = append(values, reflect.ValueOf(ruleGroupId))
			// 添加 RuleContext 参数
			values = append(values, reflect.ValueOf(ruleCtx))
		}
	} else {
		// 处理其他服务的参数
		if params, ok := action["params"].([]interface{}); ok {
			for _, param := range params {
				var paramValue interface{}
				var err error

				switch v := param.(type) {
				case string:
					// 尝试计算表达式
					paramValue, _ = e.context.EvalExpression(v)
					// 处理指针类型的参数
					if reflect.ValueOf(paramValue).Kind() == reflect.Ptr {
						paramValue = reflect.ValueOf(paramValue).Elem().Interface()
					}
					log.Debug("Engine.executeServiceAction", "参数 %s 解析结果: %v", v, paramValue)
				case map[string]interface{}:
					// 处理复杂参数
					paramValue = v
				default:
					// 其他类型直接使用
					paramValue = v
				}

				// 检查参数值是否为空
				if paramValue == nil {
					log.Debug("Engine.executeServiceAction", "警告: 参数值为空: %v", param)
				}

				// 转换为方法参数类型
				paramType := method.Type().In(len(values))
				convertedValue, err := e.varManager.GetConverter().ConvertToType(paramValue, paramType)
				if err != nil {
					log.Error("Engine.executeServiceAction", "参数类型转换失败: %v", err)
					return fmt.Errorf("参数类型转换失败: %w", err)
				}

				values = append(values, convertedValue)
			}
		}
	}

	// 调用方法
	results := method.Call(values)

	// 获取服务调用结果和错误
	var serviceResult interface{}
	var serviceError error

	if len(results) > 0 {
		lastResult := results[len(results)-1]
		if lastResult.Type().Implements(reflect.TypeOf((*error)(nil)).Elem()) {
			if !lastResult.IsNil() {
				serviceError = lastResult.Interface().(error)
				log.Debug("Engine.executeServiceAction", "服务调用返回错误: %v", serviceError)
			}
			// 如果有错误返回值，使用倒数第二个作为结果
			if len(results) > 1 {
				serviceResult = results[len(results)-2].Interface()
			}
		} else {
			// 否则使用第一个返回值作为结果
			serviceResult = results[0].Interface()
		}
	}

	// 处理输出
	if output, ok := action["output"]; ok {
		if serviceError != nil {
			// 其他错误则返回
			return e.handleServiceError(serviceError, nil)
		}

		// 处理输出配置
		switch outputConfig := output.(type) {
		case []interface{}:
			// 处理数组类型的输出配置
			if len(outputConfig) > 0 {
				if outputMap, ok := outputConfig[0].(map[string]interface{}); ok {
					target := outputMap["target"].(string)
					mappings := outputMap["mapping"].(map[string]interface{})

					// 如果有 foreach，使用数组映射
					if foreach, ok := mappings["foreach"].(string); ok {
						fields := mappings["fields"].(map[string]interface{})
						fieldMappings := make(map[string]string)
						for field, expr := range fields {
							fieldMappings[field] = expr.(string)
						}

						// 创建一个新的临时运行时用于执行映射
						tempRuntime := variable.NewTemporaryRuntime()
						if err := tempRuntime.Set("result", serviceResult); err != nil {
							return fmt.Errorf("设置临时结果失败: %w", err)
						}

						err := tempRuntime.MapArray(target, foreach, fieldMappings)
						if err != nil {
							return err
						}

						// 获取映射结果并设置到原始 runtime
						result, err := tempRuntime.Get(target)
						if err != nil {
							return fmt.Errorf("获取映射结果失败: %w", err)
						}
						return e.context.SetValue(target, result)
					}

					// 否则使用普通映射
					stringMappings := make(map[string]string)
					for field, expr := range mappings {
						stringMappings[field] = expr.(string)
					}

					// 将服务结果转换为 map[string]interface{}
					resultMap := make(map[string]interface{})
					if serviceResult != nil {
						val := reflect.ValueOf(serviceResult)
						if val.Kind() == reflect.Ptr {
							val = val.Elem()
						}
						if val.Kind() == reflect.Struct {
							for i := 0; i < val.NumField(); i++ {
								field := val.Type().Field(i)
								fieldValue := val.Field(i)
								// 处理指针类型的字段
								if fieldValue.Kind() == reflect.Ptr && !fieldValue.IsNil() {
									resultMap[field.Name] = fieldValue.Elem().Interface()
								} else if fieldValue.Kind() != reflect.Ptr {
									resultMap[field.Name] = fieldValue.Interface()
								}
							}
						}
					}

					// 创建一个新的临时运行时用于执行映射
					tempRuntime := variable.NewTemporaryRuntime()
					if err := tempRuntime.Set("result", resultMap); err != nil {
						fmt.Printf("[Engine.executeServiceAction] 设置临时结果失败: %v\n", err)
						return fmt.Errorf("设置临时结果失败: %w", err)
					}

					fmt.Printf("[Engine.executeServiceAction] 开始映射输出，目标: %s, 映射: %+v\n", target, stringMappings)
					fmt.Printf("[Engine.executeServiceAction] 转换后的服务结果: %+v\n", resultMap)

					// 执行映射
					if err := tempRuntime.Map(target, stringMappings); err != nil {
						fmt.Printf("[Engine.executeServiceAction] 执行映射失败: %v\n", err)
						return fmt.Errorf("执行映射失败: %w", err)
					}

					// 获取映射结果并设置到原始 runtime
					result, err := tempRuntime.Get(target)
					if err != nil {
						fmt.Printf("[Engine.executeServiceAction] 获取映射结果失败: %v\n", err)
						return fmt.Errorf("获取映射结果失败: %w", err)
					}

					fmt.Printf("[Engine.executeServiceAction] 映射结果: %+v\n", result)
					fmt.Printf("[Engine.executeServiceAction] 设置目标路径: %s\n", target)

					if result == nil {
						fmt.Printf("[Engine.executeServiceAction] 警告: 映射结果为空\n")
						result = make(map[string]interface{})
					}

					err = e.context.SetValue(target, result)
					if err != nil {
						fmt.Printf("[Engine.executeServiceAction] 设置结果到运行时失败: %v\n", err)
						return fmt.Errorf("设置结果到运行时失败: %w", err)
					}
				}
			}
		case map[string]interface{}:
			// 原有的 map 类型处理逻辑
			target := outputConfig["target"].(string)
			mappings := outputConfig["mapping"].(map[string]interface{})

			// 如果有 foreach，使用数组映射
			if foreach, ok := mappings["foreach"].(string); ok {
				fields := mappings["fields"].(map[string]interface{})
				fieldMappings := make(map[string]string)
				for field, expr := range fields {
					fieldMappings[field] = expr.(string)
				}

				// 创建一个新的临时运行时用于执行映射
				tempRuntime := variable.NewTemporaryRuntime()
				if err := tempRuntime.Set("result", serviceResult); err != nil {
					return fmt.Errorf("设置临时结果失败: %w", err)
				}

				err := tempRuntime.MapArray(target, foreach, fieldMappings)
				if err != nil {
					return err
				}

				// 获取映射结果并设置到原始 runtime
				result, err := tempRuntime.Get(target)
				if err != nil {
					return fmt.Errorf("获取映射结果失败: %w", err)
				}
				return e.context.SetValue(target, result)
			}

			// 否则使用普通映射
			stringMappings := make(map[string]string)
			for field, expr := range mappings {
				stringMappings[field] = expr.(string)
			}

			// 将服务结果转换为 map[string]interface{}
			resultMap := make(map[string]interface{})
			if serviceResult != nil {
				val := reflect.ValueOf(serviceResult)
				if val.Kind() == reflect.Ptr {
					val = val.Elem()
				}
				if val.Kind() == reflect.Struct {
					for i := 0; i < val.NumField(); i++ {
						field := val.Type().Field(i)
						fieldValue := val.Field(i)
						// 处理指针类型的字段
						if fieldValue.Kind() == reflect.Ptr && !fieldValue.IsNil() {
							resultMap[field.Name] = fieldValue.Elem().Interface()
						} else if fieldValue.Kind() != reflect.Ptr {
							resultMap[field.Name] = fieldValue.Interface()
						}
					}
				}
			}

			// 创建一个新的临时运行时用于执行映射
			tempRuntime := variable.NewTemporaryRuntime()
			if err := tempRuntime.Set("result", resultMap); err != nil {
				fmt.Printf("[Engine.executeServiceAction] 设置临时结果失败: %v\n", err)
				return fmt.Errorf("设置临时结果失败: %w", err)
			}

			fmt.Printf("[Engine.executeServiceAction] 开始映射输出，目标: %s, 映射: %+v\n", target, stringMappings)
			fmt.Printf("[Engine.executeServiceAction] 转换后的服务结果: %+v\n", resultMap)

			// 执行映射
			if err := tempRuntime.Map(target, stringMappings); err != nil {
				fmt.Printf("[Engine.executeServiceAction] 执行映射失败: %v\n", err)
				return fmt.Errorf("执行映射失败: %w", err)
			}

			// 获取映射结果并设置到原始 runtime
			result, err := tempRuntime.Get(target)
			if err != nil {
				fmt.Printf("[Engine.executeServiceAction] 获取映射结果失败: %v\n", err)
				return fmt.Errorf("获取映射结果失败: %w", err)
			}

			fmt.Printf("[Engine.executeServiceAction] 映射结果: %+v\n", result)
			fmt.Printf("[Engine.executeServiceAction] 设置目标路径: %s\n", target)

			if result == nil {
				fmt.Printf("[Engine.executeServiceAction] 警告: 映射结果为空\n")
				result = make(map[string]interface{})
			}

			err = e.context.SetValue(target, result)
			if err != nil {
				fmt.Printf("[Engine.executeServiceAction] 设置结果到运行时失败: %v\n", err)
				return fmt.Errorf("设置结果到运行时失败: %w", err)
			}
		}
	}

	// 如果没有输出处理但有错误，返回错误
	if serviceError != nil {
		return e.handleServiceError(serviceError, nil)
	}

	fmt.Printf("[Engine.executeServiceAction] 服务动作执行完成（无输出处理）\n")
	return nil
}

// executeSetFieldAction 执行设置字段动作
func (e *Engine) executeSetFieldAction(action map[string]interface{}) error {
	target := action["target"].(string)
	value := action["value"].(string)

	// 计算值表达式
	result, err := e.context.EvalExpression(value)
	if err != nil {
		return fmt.Errorf("计算表达式失败: %w", err)
	}

	// 设置字段值
	return e.context.SetValue(target, result)
}

// executeErrorAction 执行错误动作
func (e *Engine) executeErrorAction(action map[string]interface{}) error {
	params := action["params"].(map[string]interface{})
	code := params["code"].(string)
	message := params["message"].(string)

	return &ProcessError{
		Code:    code,
		Message: message,
		Data:    params,
	}
}

// executeTransformAction 执行转换动作
func (e *Engine) executeTransformAction(action map[string]interface{}) error {
	params := action["params"].(map[string]interface{})
	source := params["source"].(string)
	target := params["target"].(string)
	mappings := params["mapping"].(map[string]interface{})

	// 转换映射
	stringMappings := make(map[string]string)
	for field, expr := range mappings {
		stringMappings[field] = expr.(string)
	}

	// 获取源数据
	sourceData, err := e.context.EvalExpression(source)
	if err != nil {
		return fmt.Errorf("获取源数据失败: %w", err)
	}

	// 设置目标数据
	if err := e.context.SetValue(target, sourceData); err != nil {
		return fmt.Errorf("设置目标数据失败: %w", err)
	}

	return nil
}

// executeValidateAction 执行验证动作
func (e *Engine) executeValidateAction(action map[string]interface{}) error {
	fmt.Printf("[Engine.executeValidateAction] 开始执行验证动作\n")

	// 获取验证规则
	rules, ok := action["rules"].([]interface{})
	if !ok {
		return fmt.Errorf("验证动作缺少rules字段")
	}

	// 获取验证服务
	service, exists := e.services["validateService"]
	if !exists || service == nil {
		return fmt.Errorf("验证服务未注册")
	}

	validateService, ok := service.(validate.ValidateService)
	if !ok {
		return fmt.Errorf("验证服务类型错误")
	}

	if validateService == nil {
		return fmt.Errorf("验证服务为空")
	}

	// 转换规则格式
	validationRules := make([]validate.ValidationRule, 0, len(rules))
	for _, r := range rules {
		ruleMap, ok := r.(map[string]interface{})
		if !ok {
			return fmt.Errorf("规则格式错误")
		}

		// 构建验证规则
		rule := validate.ValidationRule{
			Type:         ruleMap["type"].(string),
			Field:        ruleMap["field"].(string),
			FieldName:    ruleMap["field_name"].(string),
			ErrorMessage: ruleMap["error_message"].(string),
			ErrorCode:    ruleMap["error_code"].(string),
		}

		// 处理可选的params
		if params, exists := ruleMap["params"].(map[string]interface{}); exists {
			rule.Params = params
		}

		validationRules = append(validationRules, rule)
	}

	// 使用runtime直接获取要验证的字段值
	data := make(map[string]interface{})
	for _, rule := range validationRules {
		value, err := e.context.EvalExpression(rule.Field)
		if err != nil {
			// 如果字段不存在，设置为nil，让验证规则决定是否允许
			data[rule.Field] = nil
			continue
		}
		data[rule.Field] = value
	}

	// 执行验证
	if err := validateService.ValidateRules(validationRules, data); err != nil {
		// 处理验证错误
		if errorHandler, ok := action["error_handler"].(map[string]interface{}); ok {
			handlerType := errorHandler["type"].(string)
			errorMessage := errorHandler["error_message"].(string)
			errorCode := errorHandler["error_code"].(string)
			httpCode := 400 // 默认使用400 Bad Request

			// 如果配置了http_code，使用配置的值
			if code, ok := errorHandler["http_code"].(int); ok {
				httpCode = code
			}

			if handlerType == "early_return" {
				return &ProcessError{
					HttpCode: httpCode,
					Code:     errorCode,
					Message:  errorMessage,
					Data: map[string]interface{}{
						"original_error": err.Error(),
					},
				}
			}
		}
		return err
	}

	return nil
}

// convertMetadata 转换元数据类型
func (e *Engine) convertMetadata(metadata *yamltypes.Metadata) *variable.Metadata {
	return e.metaManager.ConvertMetadata(metadata)
}
