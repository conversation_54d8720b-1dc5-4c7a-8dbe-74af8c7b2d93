package executor

import (
	"context"
	"fmt"
	"voderpltvv/erp_client/domain/process/model"
	log "voderpltvv/util"
)

// StepExecutor 步骤执行器
type StepExecutor struct {
	services map[string]interface{}
}

// NewStepExecutor 创建步骤执行器
func NewStepExecutor() *StepExecutor {
	return &StepExecutor{
		services: make(map[string]interface{}),
	}
}

// RegisterService 注册服务
func (e *StepExecutor) RegisterService(name string, service interface{}) {
	e.services[name] = service
}

// ExecuteStep 执行步骤
func (e *StepExecutor) ExecuteStep(ctx context.Context, step model.Step, params map[string]interface{}) (map[string]interface{}, error) {
	log.Info("StepExecutor.ExecuteStep", "开始执行步骤: %s", step.GetID())
	log.Debug("StepExecutor.ExecuteStep", "输入参数: %+v", params)

	if step == nil {
		return nil, fmt.Errorf("步骤为空")
	}

	// 执行步骤逻辑
	result, err := step.Execute(ctx, params)
	if err != nil {
		return nil, err
	}

	log.Info("StepExecutor.ExecuteStep", "步骤执行完成: %s", step.GetID())
	return result, nil
}
