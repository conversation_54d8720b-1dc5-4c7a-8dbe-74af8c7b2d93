package engine

import "sync"

// ProcessContext 流程上下文
type ProcessContext struct {
	values sync.Map
	err    error
}

// NewProcessContext 创建流程上下文
func NewProcessContext() *ProcessContext {
	return &ProcessContext{}
}

// GetValue 获取值
func (c *ProcessContext) GetValue(key string) interface{} {
	if value, ok := c.values.Load(key); ok {
		return value
	}
	return nil
}

// SetValue 设置值
func (c *ProcessContext) SetValue(key string, value interface{}) {
	c.values.Store(key, value)
}

// DeleteValue 删除值
func (c *ProcessContext) DeleteValue(key string) {
	c.values.Delete(key)
}

// SetError 设置错误
func (c *ProcessContext) SetError(err error) {
	c.err = err
}

// GetError 获取错误
func (c *ProcessContext) GetError() error {
	return c.err
}

// HasError 是否有错误
func (c *ProcessContext) HasError() bool {
	return c.err != nil
}
