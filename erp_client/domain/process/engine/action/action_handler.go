package action

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/application/framework/validate"
	"voderpltvv/erp_client/domain/process/model"
	rulemodel "voderpltvv/erp_client/domain/rule/model"
	log "voderpltvv/util"
)

// ActionHandler 动作处理器
type ActionHandler struct {
	services map[string]interface{}
	context  ContextManager
}

// ContextManager 上下文管理接口
type ContextManager interface {
	EvalExpression(expression string) (interface{}, error)
	SetValue(path string, value interface{}) error
	GetValue(path string) (interface{}, error)
}

// NewActionHandler 创建动作处理器
func NewActionHandler(services map[string]interface{}, context ContextManager) *ActionHandler {
	return &ActionHandler{
		services: services,
		context:  context,
	}
}

// ProcessError 流程错误
type ProcessError struct {
	HttpCode int                    `json:"-"`              // HTTP状态码
	Code     string                 `json:"code"`           // 业务错误码
	Message  string                 `json:"message"`        // 错误消息
	Data     map[string]interface{} `json:"data,omitempty"` // 错误详情
}

func (e *ProcessError) Error() string {
	return fmt.Sprintf("[%d] %s: %s", e.HttpCode, e.Code, e.Message)
}

// HandleAction 处理动作
func (h *ActionHandler) HandleAction(ctx context.Context, actionType model.ActionType, action map[string]interface{}) error {
	log.Info("ActionHandler.HandleAction", "开始处理动作: %s", actionType)

	var err error
	switch actionType {
	case model.ActionTypeService:
		err = h.handleServiceAction(ctx, action)
	case model.ActionTypeSetField:
		err = h.handleSetFieldAction(action)
	case model.ActionTypeError:
		err = h.handleErrorAction(action)
	case model.ActionTypeTransform:
		err = h.handleTransformAction(action)
	case model.ActionTypeValidate:
		err = h.handleValidateAction(action)
	default:
		err = fmt.Errorf("不支持的动作类型: %s", actionType)
	}

	if err != nil {
		log.Error("ActionHandler.HandleAction", "处理动作失败: %v", err)
		return err
	}

	log.Info("ActionHandler.HandleAction", "动作处理完成")
	return nil
}

// handleServiceAction 处理服务调用动作
func (h *ActionHandler) handleServiceAction(ctx context.Context, action map[string]interface{}) error {
	serviceName := action["service"].(string)
	methodName := action["method"].(string)

	log.Info("ActionHandler.handleServiceAction", "服务调用: %s.%s", serviceName, methodName)

	service, exists := h.services[serviceName]
	if !exists {
		return fmt.Errorf("服务未注册: %s", serviceName)
	}

	// 获取方法
	method := reflect.ValueOf(service).MethodByName(methodName)
	if !method.IsValid() {
		return fmt.Errorf("方法不存在: %s.%s", serviceName, methodName)
	}

	// 准备参数列表
	var values []reflect.Value

	// 如果第一个参数是 context.Context，添加上下文参数
	if method.Type().NumIn() > 0 && method.Type().In(0).Implements(reflect.TypeOf((*context.Context)(nil)).Elem()) {
		values = append(values, reflect.ValueOf(ctx))
	}

	// 特殊处理 ruleService 的参数
	if serviceName == "ruleService" {
		if params, ok := action["params"].([]interface{}); ok {
			// 处理 ruleGroupId
			var ruleGroupId string
			var inputData map[string]interface{}

			for _, param := range params {
				if paramMap, ok := param.(map[string]interface{}); ok {
					if paramMap["name"] == "ruleGroupId" {
						ruleGroupId = paramMap["value"].(string)
					} else if paramMap["name"] == "input" {
						// 处理 input 参数中的表达式
						inputValue := paramMap["value"].(map[string]interface{})
						processedData, err := h.processNestedExpressions(inputValue)
						if err != nil {
							return fmt.Errorf("处理规则输入参数失败: %w", err)
						}
						inputData = processedData.(map[string]interface{})
					}
				}
			}

			// 创建 RuleContext
			ruleCtx := rulemodel.NewRuleContext()
			// 设置输入数据
			if err := ruleCtx.SetValue("input", inputData); err != nil {
				return fmt.Errorf("设置规则上下文输入参数失败: %w", err)
			}

			// 添加 ruleGroupId 参数
			values = append(values, reflect.ValueOf(ruleGroupId))
			// 添加 RuleContext 参数
			values = append(values, reflect.ValueOf(ruleCtx))
		}
	} else {
		// 处理其他服务的参数
		if params, ok := action["params"].([]interface{}); ok {
			for _, param := range params {
				var paramValue interface{}
				var err error

				switch v := param.(type) {
				case string:
					// 尝试计算表达式
					paramValue, _ = h.context.EvalExpression(v)
					// 处理指针类型的参数
					if reflect.ValueOf(paramValue).Kind() == reflect.Ptr {
						paramValue = reflect.ValueOf(paramValue).Elem().Interface()
					}
					log.Debug("ActionHandler.handleServiceAction", "参数 %s 解析结果: %v", v, paramValue)
				case map[string]interface{}:
					// 处理复杂参数
					paramValue = v
				default:
					// 其他类型直接使用
					paramValue = v
				}

				// 检查参数值是否为空
				if paramValue == nil {
					log.Debug("ActionHandler.handleServiceAction", "警告: 参数值为空: %v", param)
				}

				// 转换为方法参数类型
				paramType := method.Type().In(len(values))
				convertedValue, err := h.convertToMethodParamType(paramValue, paramType)
				if err != nil {
					log.Error("ActionHandler.handleServiceAction", "参数类型转换失败: %v", err)
					return fmt.Errorf("参数类型转换失败: %w", err)
				}

				values = append(values, convertedValue)
			}
		}
	}

	// 调用方法
	results := method.Call(values)

	// 获取服务调用结果和错误
	var serviceResult interface{}
	var serviceError error

	if len(results) > 0 {
		lastResult := results[len(results)-1]
		if lastResult.Type().Implements(reflect.TypeOf((*error)(nil)).Elem()) {
			if !lastResult.IsNil() {
				serviceError = lastResult.Interface().(error)
				log.Debug("ActionHandler.handleServiceAction", "服务调用返回错误: %v", serviceError)
			}
			// 如果有错误返回值，使用倒数第二个作为结果
			if len(results) > 1 {
				serviceResult = results[len(results)-2].Interface()
			}
		} else {
			// 否则使用第一个返回值作为结果
			serviceResult = results[0].Interface()
		}
	}

	// 处理输出
	if output, ok := action["output"]; ok {
		if serviceError != nil {
			// 其他错误则返回
			return h.handleServiceError(serviceError, nil)
		}

		// 处理输出配置
		switch outputConfig := output.(type) {
		case []interface{}:
			// 处理数组类型的输出配置
			if len(outputConfig) > 0 {
				if outputMap, ok := outputConfig[0].(map[string]interface{}); ok {
					target := outputMap["target"].(string)
					mappings := outputMap["mapping"].(map[string]interface{})

					// 如果有 foreach，使用数组映射
					if foreach, ok := mappings["foreach"].(string); ok {
						fields := mappings["fields"].(map[string]interface{})
						fieldMappings := make(map[string]string)
						for field, expr := range fields {
							fieldMappings[field] = expr.(string)
						}

						// 创建一个新的临时运行时用于执行映射
						tempRuntime := variable.NewTemporaryRuntime()
						if err := tempRuntime.Set("result", serviceResult); err != nil {
							return fmt.Errorf("设置临时结果失败: %w", err)
						}

						err := tempRuntime.MapArray(target, foreach, fieldMappings)
						if err != nil {
							return err
						}

						// 获取映射结果并设置到原始 runtime
						result, err := tempRuntime.Get(target)
						if err != nil {
							return fmt.Errorf("获取映射结果失败: %w", err)
						}
						return h.context.SetValue(target, result)
					}

					// 否则使用普通映射
					stringMappings := make(map[string]string)
					for field, expr := range mappings {
						stringMappings[field] = expr.(string)
					}

					// 将服务结果转换为 map[string]interface{}
					resultMap := make(map[string]interface{})
					if serviceResult != nil {
						val := reflect.ValueOf(serviceResult)
						if val.Kind() == reflect.Ptr {
							val = val.Elem()
						}
						if val.Kind() == reflect.Struct {
							for i := 0; i < val.NumField(); i++ {
								field := val.Type().Field(i)
								fieldValue := val.Field(i)
								// 处理指针类型的字段
								if fieldValue.Kind() == reflect.Ptr && !fieldValue.IsNil() {
									resultMap[field.Name] = fieldValue.Elem().Interface()
								} else if fieldValue.Kind() != reflect.Ptr {
									resultMap[field.Name] = fieldValue.Interface()
								}
							}
						}
					}

					// 创建一个新的临时运行时用于执行映射
					tempRuntime := variable.NewTemporaryRuntime()
					if err := tempRuntime.Set("result", resultMap); err != nil {
						fmt.Printf("[ActionHandler.handleServiceAction] 设置临时结果失败: %v\n", err)
						return fmt.Errorf("设置临时结果失败: %w", err)
					}

					fmt.Printf("[ActionHandler.handleServiceAction] 开始映射输出，目标: %s, 映射: %+v\n", target, stringMappings)
					fmt.Printf("[ActionHandler.handleServiceAction] 转换后的服务结果: %+v\n", resultMap)

					// 执行映射
					if err := tempRuntime.Map(target, stringMappings); err != nil {
						fmt.Printf("[ActionHandler.handleServiceAction] 执行映射失败: %v\n", err)
						return fmt.Errorf("执行映射失败: %w", err)
					}

					// 获取映射结果并设置到原始 runtime
					result, err := tempRuntime.Get(target)
					if err != nil {
						fmt.Printf("[ActionHandler.handleServiceAction] 获取映射结果失败: %v\n", err)
						return fmt.Errorf("获取映射结果失败: %w", err)
					}

					fmt.Printf("[ActionHandler.handleServiceAction] 映射结果: %+v\n", result)
					fmt.Printf("[ActionHandler.handleServiceAction] 设置目标路径: %s\n", target)

					if result == nil {
						fmt.Printf("[ActionHandler.handleServiceAction] 警告: 映射结果为空\n")
						result = make(map[string]interface{})
					}

					err = h.context.SetValue(target, result)
					if err != nil {
						fmt.Printf("[ActionHandler.handleServiceAction] 设置结果到运行时失败: %v\n", err)
						return fmt.Errorf("设置结果到运行时失败: %w", err)
					}
				}
			}
		case map[string]interface{}:
			// 原有的 map 类型处理逻辑
			target := outputConfig["target"].(string)
			mappings := outputConfig["mapping"].(map[string]interface{})

			// 如果有 foreach，使用数组映射
			if foreach, ok := mappings["foreach"].(string); ok {
				fields := mappings["fields"].(map[string]interface{})
				fieldMappings := make(map[string]string)
				for field, expr := range fields {
					fieldMappings[field] = expr.(string)
				}

				// 创建一个新的临时运行时用于执行映射
				tempRuntime := variable.NewTemporaryRuntime()
				if err := tempRuntime.Set("result", serviceResult); err != nil {
					return fmt.Errorf("设置临时结果失败: %w", err)
				}

				err := tempRuntime.MapArray(target, foreach, fieldMappings)
				if err != nil {
					return err
				}

				// 获取映射结果并设置到原始 runtime
				result, err := tempRuntime.Get(target)
				if err != nil {
					return fmt.Errorf("获取映射结果失败: %w", err)
				}
				return h.context.SetValue(target, result)
			}

			// 否则使用普通映射
			stringMappings := make(map[string]string)
			for field, expr := range mappings {
				stringMappings[field] = expr.(string)
			}

			// 将服务结果转换为 map[string]interface{}
			resultMap := make(map[string]interface{})
			if serviceResult != nil {
				val := reflect.ValueOf(serviceResult)
				if val.Kind() == reflect.Ptr {
					val = val.Elem()
				}
				if val.Kind() == reflect.Struct {
					for i := 0; i < val.NumField(); i++ {
						field := val.Type().Field(i)
						fieldValue := val.Field(i)
						// 处理指针类型的字段
						if fieldValue.Kind() == reflect.Ptr && !fieldValue.IsNil() {
							resultMap[field.Name] = fieldValue.Elem().Interface()
						} else if fieldValue.Kind() != reflect.Ptr {
							resultMap[field.Name] = fieldValue.Interface()
						}
					}
				}
			}

			// 创建一个新的临时运行时用于执行映射
			tempRuntime := variable.NewTemporaryRuntime()
			if err := tempRuntime.Set("result", resultMap); err != nil {
				fmt.Printf("[ActionHandler.handleServiceAction] 设置临时结果失败: %v\n", err)
				return fmt.Errorf("设置临时结果失败: %w", err)
			}

			fmt.Printf("[ActionHandler.handleServiceAction] 开始映射输出，目标: %s, 映射: %+v\n", target, stringMappings)
			fmt.Printf("[ActionHandler.handleServiceAction] 转换后的服务结果: %+v\n", resultMap)

			// 执行映射
			if err := tempRuntime.Map(target, stringMappings); err != nil {
				fmt.Printf("[ActionHandler.handleServiceAction] 执行映射失败: %v\n", err)
				return fmt.Errorf("执行映射失败: %w", err)
			}

			// 获取映射结果并设置到原始 runtime
			result, err := tempRuntime.Get(target)
			if err != nil {
				fmt.Printf("[ActionHandler.handleServiceAction] 获取映射结果失败: %v\n", err)
				return fmt.Errorf("获取映射结果失败: %w", err)
			}

			fmt.Printf("[ActionHandler.handleServiceAction] 映射结果: %+v\n", result)
			fmt.Printf("[ActionHandler.handleServiceAction] 设置目标路径: %s\n", target)

			if result == nil {
				fmt.Printf("[ActionHandler.handleServiceAction] 警告: 映射结果为空\n")
				result = make(map[string]interface{})
			}

			err = h.context.SetValue(target, result)
			if err != nil {
				fmt.Printf("[ActionHandler.handleServiceAction] 设置结果到运行时失败: %v\n", err)
				return fmt.Errorf("设置结果到运行时失败: %w", err)
			}
		}
	}

	// 如果没有输出处理但有错误，返回错误
	if serviceError != nil {
		return h.handleServiceError(serviceError, nil)
	}

	fmt.Printf("[ActionHandler.handleServiceAction] 服务动作执行完成（无输出处理）\n")
	return nil
}

// handleSetFieldAction 处理设置字段动作
func (h *ActionHandler) handleSetFieldAction(action map[string]interface{}) error {
	target := action["target"].(string)
	value := action["value"].(string)

	// 计算值表达式
	result, err := h.context.EvalExpression(value)
	if err != nil {
		return fmt.Errorf("计算表达式失败: %w", err)
	}

	// 设置字段值
	return h.context.SetValue(target, result)
}

// handleErrorAction 处理错误动作
func (h *ActionHandler) handleErrorAction(action map[string]interface{}) error {
	params := action["params"].(map[string]interface{})
	code := params["code"].(string)
	message := params["message"].(string)

	return &ProcessError{
		Code:    code,
		Message: message,
		Data:    params,
	}
}

// handleTransformAction 处理转换动作
func (h *ActionHandler) handleTransformAction(action map[string]interface{}) error {
	params := action["params"].(map[string]interface{})
	source := params["source"].(string)
	target := params["target"].(string)
	mappings := params["mapping"].(map[string]interface{})

	// 转换映射
	stringMappings := make(map[string]string)
	for field, expr := range mappings {
		stringMappings[field] = expr.(string)
	}

	// 获取源数据
	sourceData, err := h.context.EvalExpression(source)
	if err != nil {
		return fmt.Errorf("获取源数据失败: %w", err)
	}

	// 设置目标数据
	if err := h.context.SetValue(target, sourceData); err != nil {
		return fmt.Errorf("设置目标数据失败: %w", err)
	}

	return nil
}

// handleValidateAction 处理验证动作
func (h *ActionHandler) handleValidateAction(action map[string]interface{}) error {
	fmt.Printf("[ActionHandler.handleValidateAction] 开始执行验证动作\n")

	// 获取验证规则
	rules, ok := action["rules"].([]interface{})
	if !ok {
		return fmt.Errorf("验证动作缺少rules字段")
	}

	// 获取验证服务
	service, exists := h.services["validateService"]
	if !exists || service == nil {
		return fmt.Errorf("验证服务未注册")
	}

	validateService, ok := service.(validate.ValidateService)
	if !ok {
		return fmt.Errorf("验证服务类型错误")
	}

	if validateService == nil {
		return fmt.Errorf("验证服务为空")
	}

	// 转换规则格式
	validationRules := make([]validate.ValidationRule, 0, len(rules))
	for _, r := range rules {
		ruleMap, ok := r.(map[string]interface{})
		if !ok {
			return fmt.Errorf("规则格式错误")
		}

		// 构建验证规则
		rule := validate.ValidationRule{
			Type:         ruleMap["type"].(string),
			Field:        ruleMap["field"].(string),
			FieldName:    ruleMap["field_name"].(string),
			ErrorMessage: ruleMap["error_message"].(string),
			ErrorCode:    ruleMap["error_code"].(string),
		}

		// 处理可选的params
		if params, exists := ruleMap["params"].(map[string]interface{}); exists {
			rule.Params = params
		}

		validationRules = append(validationRules, rule)
	}

	// 使用runtime直接获取要验证的字段值
	data := make(map[string]interface{})
	for _, rule := range validationRules {
		value, err := h.context.EvalExpression(rule.Field)
		if err != nil {
			// 如果字段不存在，设置为nil，让验证规则决定是否允许
			data[rule.Field] = nil
			continue
		}
		data[rule.Field] = value
	}

	// 执行验证
	if err := validateService.ValidateRules(validationRules, data); err != nil {
		// 处理验证错误
		if errorHandler, ok := action["error_handler"].(map[string]interface{}); ok {
			handlerType := errorHandler["type"].(string)
			errorMessage := errorHandler["error_message"].(string)
			errorCode := errorHandler["error_code"].(string)
			httpCode := 400 // 默认使用400 Bad Request

			// 如果配置了http_code，使用配置的值
			if code, ok := errorHandler["http_code"].(int); ok {
				httpCode = code
			}

			if handlerType == "early_return" {
				return &ProcessError{
					HttpCode: httpCode,
					Code:     errorCode,
					Message:  errorMessage,
					Data: map[string]interface{}{
						"original_error": err.Error(),
					},
				}
			}
		}
		return err
	}

	return nil
}

// handleServiceError 处理服务错误
func (h *ActionHandler) handleServiceError(err error, errorHandler *model.ErrorHandler) error {
	if errorHandler == nil {
		// 如果没有错误处理器，直接返回错误
		return err
	}

	log.Debug("ActionHandler.handleServiceError", "开始处理错误: %v", err)
	log.Debug("ActionHandler.handleServiceError", "错误处理配置: %+v", errorHandler)

	switch errorHandler.Type {
	case model.ErrorHandlerTypeEarlyReturn:
		// 创建一个新的ProcessError
		processError := &ProcessError{
			Code:    errorHandler.ErrorCode,
			Message: errorHandler.ErrorMessage,
			Data: map[string]interface{}{
				"original_error": err.Error(),
			},
		}
		log.Debug("ActionHandler.handleServiceError", "提前返回错误: %+v", processError)
		return processError

	case model.ErrorHandlerTypeContinue:
		// 记录错误但继续执行
		log.Debug("ActionHandler.handleServiceError", "错误已记录但继续执行: %v", err)
		// 如果配置了错误映射目标，则保存错误信息
		if errorHandler.Target != "" && len(errorHandler.Mapping) > 0 {
			errorData := map[string]interface{}{
				"error_message": err.Error(),
				"error_code":    errorHandler.ErrorCode,
			}
			if err := h.context.SetValue(errorHandler.Target, errorData); err != nil {
				return fmt.Errorf("保存错误信息失败: %w", err)
			}
		}
		return nil

	default:
		return fmt.Errorf("不支持的错误处理类型: %s", errorHandler.Type)
	}
}

// convertToMethodParamType 转换参数类型
func (h *ActionHandler) convertToMethodParamType(value interface{}, paramType reflect.Type) (reflect.Value, error) {
	if value == nil {
		return reflect.Zero(paramType), nil
	}

	valueType := reflect.TypeOf(value)
	if valueType.AssignableTo(paramType) {
		return reflect.ValueOf(value), nil
	}

	switch paramType.Kind() {
	case reflect.String:
		return reflect.ValueOf(fmt.Sprintf("%v", value)), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		switch v := value.(type) {
		case float64:
			return reflect.ValueOf(int64(v)).Convert(paramType), nil
		case string:
			if i, err := strconv.ParseInt(v, 10, 64); err == nil {
				return reflect.ValueOf(i).Convert(paramType), nil
			}
		}
	case reflect.Float32, reflect.Float64:
		switch v := value.(type) {
		case float64:
			return reflect.ValueOf(v).Convert(paramType), nil
		case string:
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				return reflect.ValueOf(f).Convert(paramType), nil
			}
		}
	case reflect.Bool:
		switch v := value.(type) {
		case bool:
			return reflect.ValueOf(v), nil
		case string:
			if b, err := strconv.ParseBool(v); err == nil {
				return reflect.ValueOf(b), nil
			}
		}
	case reflect.Struct:
		// 尝试转换为目标结构体
		if valueMap, ok := value.(map[string]interface{}); ok {
			newStruct := reflect.New(paramType).Elem()
			for i := 0; i < paramType.NumField(); i++ {
				field := paramType.Field(i)
				if v, ok := valueMap[field.Name]; ok {
					if converted, err := h.convertToMethodParamType(v, field.Type); err == nil {
						newStruct.Field(i).Set(converted)
					}
				}
			}
			return newStruct, nil
		}
	}

	return reflect.Value{}, fmt.Errorf("无法转换类型: %v -> %v", reflect.TypeOf(value), paramType)
}

// processNestedExpressions 处理嵌套的map结构中的表达式
func (h *ActionHandler) processNestedExpressions(data interface{}) (interface{}, error) {
	if data == nil {
		return nil, nil
	}

	switch v := data.(type) {
	case string:
		// 如果是字符串，尝试计算表达式
		result, err := h.context.EvalExpression(v)
		if err != nil {
			return nil, fmt.Errorf("计算表达式失败 [%s]: %w", v, err)
		}
		return result, nil
	case map[string]interface{}:
		// 如果是map，递归处理每个值
		result := make(map[string]interface{})
		for key, value := range v {
			processed, err := h.processNestedExpressions(value)
			if err != nil {
				return nil, fmt.Errorf("处理嵌套map失败 [%s]: %w", key, err)
			}
			result[key] = processed
		}
		return result, nil
	case []interface{}:
		// 如果是数组，递归处理每个元素
		result := make([]interface{}, len(v))
		for i, item := range v {
			processed, err := h.processNestedExpressions(item)
			if err != nil {
				return nil, fmt.Errorf("处理数组元素失败 [%d]: %w", i, err)
			}
			result[i] = processed
		}
		return result, nil
	default:
		// 其他类型直接返回
		return v, nil
	}
}
