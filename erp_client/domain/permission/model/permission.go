package model

import (
	"voderpltvv/erp_client/domain/common/model"
)

// Permission 权限接口
type Permission interface {
	model.Entity
	GetResource() string
	GetAction() string
	GetSubject() string
}

// BasePermission 基础权限结构
type BasePermission struct {
	model.BaseModel
	Resource string `json:"resource"`
	Action   string `json:"action"`
	Subject  string `json:"subject"`
}

func (p *BasePermission) GetResource() string {
	return p.Resource
}

func (p *BasePermission) GetAction() string {
	return p.Action
}

func (p *BasePermission) GetSubject() string {
	return p.Subject
}
