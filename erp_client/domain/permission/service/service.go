package service

import (
	"context"
	"voderpltvv/erp_client/domain/permission/model"
)

// PermissionService 权限服务接口
type PermissionService interface {
	// 权限检查
	Check(ctx context.Context, subject string, resource string, action string) (bool, error)
	// 权限授予
	Grant(ctx context.Context, permission model.Permission) error
	// 权限回收
	Revoke(ctx context.Context, permission model.Permission) error
	// 创建权限
	Create(ctx context.Context, permission model.Permission) error
	// 更新权限
	Update(ctx context.Context, permission model.Permission) error
	// 删除权限
	Delete(ctx context.Context, id string) error
	// 获取权限
	Get(ctx context.Context, id string) (model.Permission, error)
	// 查询权限
	Query(ctx context.Context, condition map[string]interface{}) ([]model.Permission, error)
}
