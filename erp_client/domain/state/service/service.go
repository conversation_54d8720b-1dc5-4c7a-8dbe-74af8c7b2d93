package service

import (
	"context"
	"voderpltvv/erp_client/domain/state/model"
)

// StateService 状态服务接口
type StateService interface {
	// 获取状态
	Get(ctx context.Context, id string) (model.State, error)
	// 状态转换
	Transition(ctx context.Context, id string, newState model.State) error
	// 状态验证
	Validate(ctx context.Context, state model.State) error
	// 创建状态
	Create(ctx context.Context, state model.State) error
	// 更新状态
	Update(ctx context.Context, state model.State) error
	// 删除状态
	Delete(ctx context.Context, id string) error
	// 查询状态
	Query(ctx context.Context, condition map[string]interface{}) ([]model.State, error)
}
