package model

import (
	"voderpltvv/erp_client/domain/common/model"
)

// State 状态接口
type State interface {
	model.Entity
	GetName() string
	GetType() string
	GetValue() interface{}
}

// BaseState 基础状态结构
type BaseState struct {
	model.BaseModel
	Name  string      `json:"name"`
	Type  string      `json:"type"`
	Value interface{} `json:"value"`
}

func (s *BaseState) GetName() string {
	return s.Name
}

func (s *BaseState) GetType() string {
	return s.Type
}

func (s *BaseState) GetValue() interface{} {
	return s.Value
}
