package common

import (
	"voderpltvv/erp_client/application/framework/yaml/types"
)

// IParser 解析器基础接口
type IParser interface {
	// Parse 解析内容
	Parse(content []byte) error
	// ParseMetadata 解析元数据
	ParseMetadata() (*types.Metadata, error)
}

// IParserConfig 解析器配置接口
type IParserConfig interface {
	// GetEncoding 获取编码
	GetEncoding() string
	// GetFormat 获取格式
	GetFormat() string
}

// IParserValidator 解析器验证接口
type IParserValidator interface {
	// ValidateContent 验证内容格式
	ValidateContent(content []byte) error
	// ValidateSchema 验证内容结构
	ValidateSchema(content []byte) error
}
