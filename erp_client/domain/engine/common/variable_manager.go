package common

import (
	"fmt"
	"reflect"
	"voderpltvv/erp_client/application/framework/yaml/types"
	"voderpltvv/erp_client/domain/engine/common/metadata"
	"voderpltvv/erp_client/domain/engine/common/runtime"
	"voderpltvv/erp_client/domain/engine/common/utils"
	"voderpltvv/erp_client/domain/engine/interfaces"
)

// VariableManager 变量管理器，专注于变量处理
type VariableManager struct {
	parser    *utils.ExpressionParser
	converter *utils.TypeConverter
	runtime   interfaces.IRuntime
	metadata  interfaces.IMetadata
	factory   *runtime.RuntimeFactory
}

// NewVariableManager 创建变量管理器
func NewVariableManager(rt interfaces.IRuntime) *VariableManager {
	manager := &VariableManager{
		runtime:   rt,
		metadata:  metadata.NewMetadataManager(),
		converter: utils.NewTypeConverter(),
		factory:   runtime.NewRuntimeFactory(),
	}
	manager.parser = utils.NewExpressionParser(manager, nil)
	return manager
}

// Get 实现 IRuntime 接口
func (m *VariableManager) Get(path string) (interface{}, error) {
	return m.GetVariable(path)
}

// Set 实现 IRuntime 接口
func (m *VariableManager) Set(path string, value interface{}) error {
	return m.SetVariable(path, value)
}

// GetNamespace 实现 IRuntime 接口
func (m *VariableManager) GetNamespace(namespace string) (map[string]interface{}, error) {
	return m.runtime.GetNamespace(namespace)
}

// EvalExpression 实现 IRuntime 接口
func (m *VariableManager) EvalExpression(expression string) (interface{}, error) {
	return m.parser.Parse(expression)
}

// GetVariable 获取变量值
func (m *VariableManager) GetVariable(name string) (interface{}, error) {
	return m.runtime.Get(name)
}

// SetVariable 设置变量值
func (m *VariableManager) SetVariable(name string, value interface{}) error {
	return m.runtime.Set(name, value)
}

// ProcessNestedExpressions 处理嵌套表达式
func (m *VariableManager) ProcessNestedExpressions(data interface{}) (interface{}, error) {
	if data == nil {
		return nil, nil
	}

	// 创建临时运行时用于表达式处理
	tempRuntime := m.factory.CreateTemporaryRuntime()
	defer m.factory.ReleaseTemporaryRuntime(tempRuntime)

	switch v := data.(type) {
	case string:
		return tempRuntime.EvalExpression(v)
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			processed, err := m.ProcessNestedExpressions(value)
			if err != nil {
				return nil, fmt.Errorf("处理嵌套map失败 [%s]: %w", key, err)
			}
			result[key] = processed
		}
		return result, nil
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, item := range v {
			processed, err := m.ProcessNestedExpressions(item)
			if err != nil {
				return nil, fmt.Errorf("处理数组元素失败 [%d]: %w", i, err)
			}
			result[i] = processed
		}
		return result, nil
	default:
		return v, nil
	}
}

// LoadMetadata 加载元数据到运行时
func (m *VariableManager) LoadMetadata(meta *types.Metadata) error {
	if meta == nil {
		return nil
	}

	// 加载各个区域的变量
	sections := map[string]*types.VariableSection{
		"input":   meta.Input,
		"output":  meta.Output,
		"context": meta.Context,
		"system":  meta.System,
	}

	for namespace, section := range sections {
		if err := m.loadVariableSection(namespace, section); err != nil {
			return fmt.Errorf("加载%s变量失败: %w", namespace, err)
		}
	}

	// 将元数据存储到元数据管理器
	return m.metadata.Set("metadata", meta)
}

// loadVariableSection 加载变量区域
func (m *VariableManager) loadVariableSection(namespace string, section *types.VariableSection) error {
	if section == nil || len(section.Variables) == 0 {
		return nil
	}

	for _, v := range section.Variables {
		path := fmt.Sprintf("%s.%s", namespace, v.Name)
		if v.DefaultValue != "" {
			if err := m.runtime.Set(path, v.DefaultValue); err != nil {
				return fmt.Errorf("设置默认值失败 [%s]: %w", path, err)
			}
		}
	}

	return nil
}

// ValidateMetadata 验证元数据
func (m *VariableManager) ValidateMetadata() error {
	meta := m.GetMetadata()
	if meta == nil {
		return nil
	}

	sections := map[string]*types.VariableSection{
		"input":   meta.Input,
		"output":  meta.Output,
		"context": meta.Context,
		"system":  meta.System,
	}

	for namespace, section := range sections {
		if err := m.validateVariableSection(namespace, section); err != nil {
			return fmt.Errorf("验证%s变量失败: %w", namespace, err)
		}
	}

	return nil
}

// validateVariableSection 验证变量区域
func (m *VariableManager) validateVariableSection(namespace string, section *types.VariableSection) error {
	if section == nil || len(section.Variables) == 0 {
		return nil
	}

	for _, v := range section.Variables {
		path := fmt.Sprintf("%s.%s", namespace, v.Name)

		// 检查必需字段
		if v.Required {
			value, err := m.runtime.Get(path)
			if err != nil || value == nil {
				return fmt.Errorf("必需字段未设置 [%s]", path)
			}
		}

		// 验证字段类型
		if value, err := m.runtime.Get(path); err == nil && value != nil {
			if err := m.validateType(value, v.Type); err != nil {
				return fmt.Errorf("字段类型错误 [%s]: %w", path, err)
			}
		}
	}

	return nil
}

// validateType 验证值类型
func (m *VariableManager) validateType(value interface{}, expectedType string) error {
	if value == nil {
		return nil
	}

	actualType := reflect.TypeOf(value)
	switch expectedType {
	case "string":
		if actualType.Kind() != reflect.String {
			return fmt.Errorf("expected string, got %v", actualType)
		}
	case "number":
		switch actualType.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
			reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
			reflect.Float32, reflect.Float64:
			// 这些类型都是合法的数字类型
		default:
			return fmt.Errorf("expected number, got %v", actualType)
		}
	case "boolean":
		if actualType.Kind() != reflect.Bool {
			return fmt.Errorf("expected boolean, got %v", actualType)
		}
	case "array":
		if actualType.Kind() != reflect.Slice && actualType.Kind() != reflect.Array {
			return fmt.Errorf("expected array, got %v", actualType)
		}
	case "object":
		if actualType.Kind() != reflect.Map && actualType.Kind() != reflect.Struct {
			return fmt.Errorf("expected object, got %v", actualType)
		}
	default:
		return fmt.Errorf("unsupported type: %s", expectedType)
	}
	return nil
}

// GetMetadata 获取元数据
func (m *VariableManager) GetMetadata() *types.Metadata {
	if value, err := m.metadata.Get("metadata"); err == nil && value != nil {
		if meta, ok := value.(*types.Metadata); ok {
			return meta
		}
	}
	return nil
}

// GetRuntime 获取运行时
func (m *VariableManager) GetRuntime() interfaces.IRuntime {
	return m.runtime
}

// GetConverter 获取类型转换器
func (m *VariableManager) GetConverter() *utils.TypeConverter {
	return m.converter
}

// GetParser 获取表达式解析器
func (m *VariableManager) GetParser() *utils.ExpressionParser {
	return m.parser
}

// GetFactory 获取运行时工厂
func (m *VariableManager) GetFactory() *runtime.RuntimeFactory {
	return m.factory
}
