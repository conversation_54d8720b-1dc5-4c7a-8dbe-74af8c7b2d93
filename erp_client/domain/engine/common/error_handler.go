package common

import (
	"fmt"
	"strings"
)

// ErrorCode 错误码类型
type ErrorCode int

const (
	// ErrNone 无错误
	ErrNone ErrorCode = 0
	// ErrInvalidInput 无效输入
	ErrInvalidInput ErrorCode = 1000
	// ErrInvalidConfig 无效配置
	ErrInvalidConfig ErrorCode = 1001
	// ErrInvalidMetadata 无效元数据
	ErrInvalidMetadata ErrorCode = 1002
	// ErrInvalidVariable 无效变量
	ErrInvalidVariable ErrorCode = 1003
	// ErrInvalidExpression 无效表达式
	ErrInvalidExpression ErrorCode = 1004
	// ErrInvalidAction 无效动作
	ErrInvalidAction ErrorCode = 1005
	// ErrInvalidCondition 无效条件
	ErrInvalidCondition ErrorCode = 1006
	// ErrExecutionFailed 执行失败
	ErrExecutionFailed ErrorCode = 2000
	// ErrTimeout 超时
	ErrTimeout ErrorCode = 2001
	// ErrInternalError 内部错误
	ErrInternalError ErrorCode = 9999
)

// BaseError 基础错误类型
type BaseError struct {
	Code    ErrorCode
	Message string
	Cause   error
}

// Error 实现error接口
func (e *BaseError) Error() string {
	var sb strings.Builder
	sb.WriteString(fmt.Sprintf("[%d] %s", e.Code, e.Message))
	if e.Cause != nil {
		sb.WriteString(fmt.Sprintf(": %v", e.Cause))
	}
	return sb.String()
}

// Unwrap 实现errors.Unwrap接口
func (e *BaseError) Unwrap() error {
	return e.Cause
}

// BaseErrorHandler 错误处理器基础实现
type BaseErrorHandler struct {
	lastError error
}

// NewBaseErrorHandler 创建基础错误处理器
func NewBaseErrorHandler() *BaseErrorHandler {
	return &BaseErrorHandler{}
}

// HandleError 处理错误
func (h *BaseErrorHandler) HandleError(err error) error {
	if err == nil {
		return nil
	}
	h.lastError = err
	return err
}

// WrapError 包装错误
func (h *BaseErrorHandler) WrapError(code ErrorCode, message string, cause error) error {
	err := &BaseError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
	return h.HandleError(err)
}

// GetLastError 获取最后一个错误
func (h *BaseErrorHandler) GetLastError() error {
	return h.lastError
}

// ClearError 清除错误
func (h *BaseErrorHandler) ClearError() {
	h.lastError = nil
}

// IsError 检查是否是指定类型的错误
func (h *BaseErrorHandler) IsError(err error, code ErrorCode) bool {
	if err == nil {
		return false
	}
	if baseErr, ok := err.(*BaseError); ok {
		return baseErr.Code == code
	}
	return false
}

// NewError 创建新的错误
func NewError(code ErrorCode, message string) error {
	return &BaseError{
		Code:    code,
		Message: message,
	}
}

// NewErrorf 创建新的格式化错误
func NewErrorf(code ErrorCode, format string, args ...interface{}) error {
	return &BaseError{
		Code:    code,
		Message: fmt.Sprintf(format, args...),
	}
}

// WrapErrorf 包装格式化错误
func WrapErrorf(code ErrorCode, cause error, format string, args ...interface{}) error {
	return &BaseError{
		Code:    code,
		Message: fmt.Sprintf(format, args...),
		Cause:   cause,
	}
}
