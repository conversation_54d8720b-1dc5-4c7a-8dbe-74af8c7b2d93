package context

import (
	fwcontext "voderpltvv/erp_client/application/framework/runtime/context"
	fwexpr "voderpltvv/erp_client/application/framework/runtime/expression"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/domain/engine/common/runtime/expression"
)

// RuntimeContext 运行时上下文
type RuntimeContext struct {
	variableManager  variable.Manager
	expressionEngine fwexpr.Engine
}

// NewRuntimeContext 创建运行时上下文
func NewRuntimeContext(variableManager variable.Manager) fwcontext.RuntimeContext {
	return &RuntimeContext{
		variableManager:  variableManager,
		expressionEngine: expression.NewExprEngine(),
	}
}

// GetVariableManager 获取变量管理器
func (c *RuntimeContext) GetVariableManager() variable.Manager {
	return c.variableManager
}

// GetExpressionEngine 获取表达式引擎
func (c *RuntimeContext) GetExpressionEngine() fwexpr.Engine {
	return c.expressionEngine
}

// Clone 克隆上下文
func (c *RuntimeContext) Clone() fwcontext.RuntimeContext {
	return &RuntimeContext{
		variableManager:  c.variableManager,
		expressionEngine: c.expressionEngine,
	}
}
