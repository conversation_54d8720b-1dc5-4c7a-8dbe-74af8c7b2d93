package expression

import (
	"fmt"
	"reflect"
	fwexpr "voderpltvv/erp_client/application/framework/runtime/expression"

	"github.com/expr-lang/expr"
)

// ExprEngine 基于 expr-lang/expr 的表达式引擎实现
type ExprEngine struct{}

// NewExprEngine 创建表达式引擎
func NewExprEngine() fwexpr.Engine {
	return &ExprEngine{}
}

// Evaluate 评估表达式
func (e *ExprEngine) Evaluate(expression string, context map[string]interface{}) (interface{}, error) {
	program, err := expr.Compile(expression, expr.Env(context))
	if err != nil {
		return nil, fmt.Errorf("编译表达式失败: %w", err)
	}

	result, err := expr.Run(program, context)
	if err != nil {
		return nil, fmt.Errorf("执行表达式失败: %w", err)
	}

	return result, nil
}

// ValidateExpression 验证表达式语法
func (e *ExprEngine) ValidateExpression(expression string) error {
	_, err := expr.Compile(expression)
	if err != nil {
		return fmt.Errorf("表达式语法错误: %w", err)
	}
	return nil
}

// GetExpressionType 获取表达式返回类型
func (e *ExprEngine) GetExpressionType(expression string) (string, error) {
	// 尝试执行表达式获取结果类型
	program, err := expr.Compile(expression)
	if err != nil {
		return "", fmt.Errorf("编译表达式失败: %w", err)
	}

	result, err := expr.Run(program, nil)
	if err != nil {
		return "", fmt.Errorf("执行表达式失败: %w", err)
	}

	if result == nil {
		return string(fwexpr.TypeNull), nil
	}

	t := reflect.TypeOf(result)
	switch t.Kind() {
	case reflect.Bool:
		return string(fwexpr.TypeBoolean), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
		reflect.Float32, reflect.Float64:
		return string(fwexpr.TypeNumber), nil
	case reflect.String:
		return string(fwexpr.TypeString), nil
	case reflect.Slice, reflect.Array:
		return string(fwexpr.TypeArray), nil
	case reflect.Map, reflect.Struct:
		return string(fwexpr.TypeObject), nil
	default:
		return "", fmt.Errorf("未知表达式类型: %v", t.Kind())
	}
}
