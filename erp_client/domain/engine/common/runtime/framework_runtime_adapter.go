package runtime

import (
	"fmt"
	"voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/domain/engine/interfaces"
)

// FrameworkRuntimeAdapter 适配 framework 的 Runtime 到 IRuntime 接口
type FrameworkRuntimeAdapter struct {
	runtime *variable.Runtime
}

// NewFrameworkRuntimeAdapter 创建 framework Runtime 适配器
func NewFrameworkRuntimeAdapter(store variable.Store) interfaces.IRuntime {
	return &FrameworkRuntimeAdapter{
		runtime: variable.NewRuntime(store),
	}
}

// NewTemporaryFrameworkRuntime 创建临时 framework Runtime 适配器
func NewTemporaryFrameworkRuntime() interfaces.IRuntime {
	return &FrameworkRuntimeAdapter{
		runtime: variable.NewTemporaryRuntime(),
	}
}

// Get 实现 IRuntime 接口
func (a *FrameworkRuntimeAdapter) Get(path string) (interface{}, error) {
	return a.runtime.Get(path)
}

// Set 实现 IRuntime 接口
func (a *FrameworkRuntimeAdapter) Set(path string, value interface{}) error {
	return a.runtime.Set(path, value)
}

// GetNamespace 实现 IRuntime 接口
func (a *FrameworkRuntimeAdapter) GetNamespace(namespace string) (map[string]interface{}, error) {
	value, err := a.runtime.Get(namespace)
	if err != nil {
		return nil, err
	}
	if value == nil {
		return make(map[string]interface{}), nil
	}
	if m, ok := value.(map[string]interface{}); ok {
		return m, nil
	}
	return nil, fmt.Errorf("namespace %s is not a map", namespace)
}

// EvalExpression 实现 IRuntime 接口
func (a *FrameworkRuntimeAdapter) EvalExpression(expression string) (interface{}, error) {
	return a.runtime.Eval(expression)
}

// GetRuntime 获取底层的 framework Runtime
func (a *FrameworkRuntimeAdapter) GetRuntime() *variable.Runtime {
	return a.runtime
}
