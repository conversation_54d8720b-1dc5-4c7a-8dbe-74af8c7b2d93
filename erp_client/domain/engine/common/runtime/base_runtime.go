package runtime

import (
	"fmt"
	"voderpltvv/erp_client/domain/engine/interfaces"
)

// BaseRuntime 基础运行时实现
type BaseRuntime struct {
	variables map[string]interface{}
}

// NewBaseRuntime 创建基础运行时
func NewBaseRuntime() interfaces.IRuntime {
	return &BaseRuntime{
		variables: make(map[string]interface{}),
	}
}

// Get 获取变量值
func (r *BaseRuntime) Get(path string) (interface{}, error) {
	if value, ok := r.variables[path]; ok {
		return value, nil
	}
	// 在解析阶段,变量解析直接返回变量引用格式
	return fmt.Sprintf("${%s}", path), nil
}

// Set 设置变量值
func (r *BaseRuntime) Set(path string, value interface{}) error {
	r.variables[path] = value
	return nil
}

// GetNamespace 获取命名空间下的所有变量
func (r *BaseRuntime) GetNamespace(namespace string) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	for k, v := range r.variables {
		if k == namespace {
			if m, ok := v.(map[string]interface{}); ok {
				return m, nil
			}
		}
	}
	return result, nil
}

// EvalExpression 计算表达式
func (r *BaseRuntime) EvalExpression(expression string) (interface{}, error) {
	// 在解析阶段直接返回表达式本身
	return expression, nil
}

// Clear 清空所有变量
func (r *BaseRuntime) Clear() {
	r.variables = make(map[string]interface{})
}
