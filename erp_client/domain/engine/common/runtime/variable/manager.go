package variable

import (
	"fmt"
	"strings"
	"sync"
)

// Manager 变量管理器
type Manager struct {
	store    Store
	accessor Accessor
	mutex    sync.RWMutex
	metadata *Metadata
}

// NewManager 创建新的变量管理器
func NewManager() *Manager {
	store := NewStore()
	accessor := NewAccessor(store)
	return &Manager{
		store:    store,
		accessor: accessor,
	}
}

// Get 获取变量值
func (m *Manager) Get(path string) (interface{}, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.accessor.GetValue(m.store.GetData(), path)
}

// Set 设置变量值
func (m *Manager) Set(path string, value interface{}) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	parts := splitPath(path)
	if len(parts) < 2 {
		return NewError(ErrInvalidPath, "invalid path format", path)
	}

	ns := Namespace(parts[0])
	data, err := m.store.GetNamespaceData(ns)
	if err != nil {
		data = make(map[string]interface{})
	}

	if err := m.accessor.SetValue(data, strings.Join(parts[1:], "."), value); err != nil {
		return err
	}

	return m.store.SetNamespaceData(ns, data)
}

// GetNamespace 获取命名空间数据
func (m *Manager) GetNamespace(ns Namespace) (map[string]interface{}, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.store.GetNamespaceData(ns)
}

// SetNamespace 设置命名空间数据
func (m *Manager) SetNamespace(ns Namespace, data map[string]interface{}) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	return m.store.SetNamespaceData(ns, data)
}

// LoadMetadata 加载元数据
func (m *Manager) LoadMetadata(metadata *Metadata) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.metadata = metadata
	return nil
}

// GetMetadata 获取元数据
func (m *Manager) GetMetadata() (*Metadata, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if m.metadata == nil {
		return nil, NewError(ErrPathNotFound, "metadata not found", "")
	}

	return m.metadata, nil
}

// Map 映射变量
func (m *Manager) Map(target string, mappings map[string]string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for targetPath, sourcePath := range mappings {
		value, err := m.accessor.GetValue(m.store.GetData(), sourcePath)
		if err != nil {
			return fmt.Errorf("failed to get source value from %s: %w", sourcePath, err)
		}

		if err := m.Set(fmt.Sprintf("%s.%s", target, targetPath), value); err != nil {
			return fmt.Errorf("failed to set target value at %s: %w", targetPath, err)
		}
	}

	return nil
}

// MapArray 映射数组
func (m *Manager) MapArray(target string, source string, mappings map[string]string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sourceValue, err := m.accessor.GetValue(m.store.GetData(), source)
	if err != nil {
		return fmt.Errorf("failed to get source array from %s: %w", source, err)
	}

	sourceArray, ok := sourceValue.([]interface{})
	if !ok {
		return NewError(ErrTypeMismatch, fmt.Sprintf("source %s is not an array", source), source)
	}

	targetArray := make([]interface{}, len(sourceArray))
	for i, item := range sourceArray {
		targetItem := make(map[string]interface{})
		for targetPath, sourcePath := range mappings {
			value, err := m.accessor.GetFieldValue(item, sourcePath)
			if err != nil {
				return fmt.Errorf("failed to get field value from item %d at %s: %w", i, sourcePath, err)
			}
			targetItem[targetPath] = value
		}
		targetArray[i] = targetItem
	}

	return m.Set(target, targetArray)
}

// EvalExpression 计算表达式
func (m *Manager) EvalExpression(expression string) (interface{}, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return m.accessor.EvaluateExpression(expression)
}

// splitPath 分割路径
func splitPath(path string) []string {
	return strings.Split(path, ".")
}
