package runtime

import (
	"sync"
	"voderpltvv/erp_client/domain/engine/interfaces"
)

// TemporaryRuntime 临时运行时，用于临时变量处理
type TemporaryRuntime struct {
	variables map[string]interface{}
}

// NewTemporaryRuntime 创建临时运行时
func NewTemporaryRuntime() *TemporaryRuntime {
	return &TemporaryRuntime{
		variables: make(map[string]interface{}),
	}
}

// Get 获取变量值
func (r *TemporaryRuntime) Get(path string) (interface{}, error) {
	if value, ok := r.variables[path]; ok {
		return value, nil
	}
	return nil, nil
}

// Set 设置变量值
func (r *TemporaryRuntime) Set(path string, value interface{}) error {
	r.variables[path] = value
	return nil
}

// GetNamespace 获取命名空间下的所有变量
func (r *TemporaryRuntime) GetNamespace(namespace string) (map[string]interface{}, error) {
	return r.variables, nil
}

// EvalExpression 计算表达式
func (r *TemporaryRuntime) EvalExpression(expression string) (interface{}, error) {
	return expression, nil
}

// Clear 清空所有变量
func (r *TemporaryRuntime) Clear() {
	r.variables = make(map[string]interface{})
}

// TemporaryRuntimePool 临时运行时对象池
type TemporaryRuntimePool struct {
	pool sync.Pool
}

// NewTemporaryRuntimePool 创建临时运行时对象池
func NewTemporaryRuntimePool() *TemporaryRuntimePool {
	return &TemporaryRuntimePool{
		pool: sync.Pool{
			New: func() interface{} {
				return NewTemporaryRuntime()
			},
		},
	}
}

// Get 获取临时运行时
func (p *TemporaryRuntimePool) Get() interfaces.IRuntime {
	return p.pool.Get().(*TemporaryRuntime)
}

// Put 归还临时运行时
func (p *TemporaryRuntimePool) Put(runtime interfaces.IRuntime) {
	if tr, ok := runtime.(*TemporaryRuntime); ok {
		tr.Clear()
		p.pool.Put(tr)
	}
}
