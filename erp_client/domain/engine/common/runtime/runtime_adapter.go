package runtime

import (
	"voderpltvv/erp_client/domain/engine/interfaces"
)

// RuntimeAdapter 运行时适配器基类
type RuntimeAdapter struct {
	runtime interfaces.IRuntime
}

// NewRuntimeAdapter 创建运行时适配器
func NewRuntimeAdapter(runtime interfaces.IRuntime) interfaces.IRuntime {
	return &RuntimeAdapter{
		runtime: runtime,
	}
}

// Get 实现 IRuntime 接口
func (r *RuntimeAdapter) Get(path string) (interface{}, error) {
	return r.runtime.Get(path)
}

// Set 实现 IRuntime 接口
func (r *RuntimeAdapter) Set(path string, value interface{}) error {
	return r.runtime.Set(path, value)
}

// GetNamespace 实现 IRuntime 接口
func (r *RuntimeAdapter) GetNamespace(namespace string) (map[string]interface{}, error) {
	return r.runtime.GetNamespace(namespace)
}

// EvalExpression 实现 IRuntime 接口
func (r *RuntimeAdapter) EvalExpression(expression string) (interface{}, error) {
	return r.runtime.EvalExpression(expression)
}

// GetRuntime 获取被适配的运行时
func (r *RuntimeAdapter) GetRuntime() interfaces.IRuntime {
	return r.runtime
}
