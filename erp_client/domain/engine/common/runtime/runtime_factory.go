package runtime

import (
	"sync"
	fwcontext "voderpltvv/erp_client/application/framework/runtime/context"
	fwvar "voderpltvv/erp_client/application/framework/runtime/variable"
	"voderpltvv/erp_client/domain/engine/common/runtime/context"
	"voderpltvv/erp_client/domain/engine/interfaces"
)

var (
	defaultPool     *TemporaryRuntimePool
	defaultPoolOnce sync.Once
)

// RuntimeFactory 运行时工厂
type RuntimeFactory struct {
	pool *TemporaryRuntimePool
}

// NewRuntimeFactory 创建运行时工厂
func NewRuntimeFactory() *RuntimeFactory {
	return &RuntimeFactory{
		pool: getDefaultPool(),
	}
}

// getDefaultPool 获取默认的临时运行时池
func getDefaultPool() *TemporaryRuntimePool {
	defaultPoolOnce.Do(func() {
		defaultPool = NewTemporaryRuntimePool()
	})
	return defaultPool
}

// CreateTemporaryRuntime 创建临时运行时
func (f *RuntimeFactory) CreateTemporaryRuntime() interfaces.IRuntime {
	return NewTemporaryFrameworkRuntime()
}

// ReleaseTemporaryRuntime 释放临时运行时
func (f *RuntimeFactory) ReleaseTemporaryRuntime(runtime interfaces.IRuntime) {
	if tr, ok := runtime.(*TemporaryRuntime); ok {
		tr.Clear()
		f.pool.Put(tr)
	}
}

// CreateBaseRuntime 创建基础运行时
func (f *RuntimeFactory) CreateBaseRuntime() interfaces.IRuntime {
	return NewFrameworkRuntimeAdapter(nil)
}

// CreateRuntimeAdapter 创建运行时适配器
func (f *RuntimeFactory) CreateRuntimeAdapter(runtime interfaces.IRuntime) interfaces.IRuntime {
	return NewRuntimeAdapter(runtime)
}

// CreateFrameworkRuntime 创建 framework 运行时
func (f *RuntimeFactory) CreateFrameworkRuntime(store fwvar.Store) interfaces.IRuntime {
	return NewFrameworkRuntimeAdapter(store)
}

// CreateRuntimeContext 创建运行时上下文
func (f *RuntimeFactory) CreateRuntimeContext(variableManager fwvar.Manager) fwcontext.RuntimeContext {
	return context.NewRuntimeContext(variableManager)
}
