package common

import (
	"fmt"
	"voderpltvv/erp_client/domain/engine/common/utils"
	"voderpltvv/erp_client/domain/engine/interfaces"

	"gopkg.in/yaml.v3"
)

// BaseYAMLParser YAML解析器基类
type BaseYAMLParser struct {
	content []byte
	parser  *utils.ExpressionParser
	runtime interfaces.IRuntime
}

// NewBaseYAMLParser 创建基础YAML解析器
func NewBaseYAMLParser(content []byte) *BaseYAMLParser {
	p := &BaseYAMLParser{
		content: content,
		runtime: NewBaseRuntime(),
	}
	// 创建表达式解析器,使用 BaseRuntime 作为运行时环境
	p.parser = utils.NewExpressionParser(p.runtime, nil)
	return p
}

// ParseYAML 解析YAML内容到指定结构
func (p *BaseYAMLParser) ParseYAML(out interface{}) error {
	if err := yaml.Unmarshal(p.content, out); err != nil {
		return fmt.Errorf("解析YAML失败: %w", err)
	}
	return nil
}

// ProcessValue 处理值中的表达式
func (p *BaseYAMLParser) ProcessValue(value interface{}) (interface{}, error) {
	switch v := value.(type) {
	case string:
		// 使用表达式解析器解析字符串值
		return p.parser.Parse(v)
	case map[string]interface{}:
		// 递归处理map中的值
		for key, val := range v {
			processed, err := p.ProcessValue(val)
			if err != nil {
				return nil, err
			}
			v[key] = processed
		}
		return v, nil
	case []interface{}:
		// 递归处理数组中的值
		for i, val := range v {
			processed, err := p.ProcessValue(val)
			if err != nil {
				return nil, err
			}
			v[i] = processed
		}
		return v, nil
	default:
		// 其他类型直接返回
		return v, nil
	}
}

// ProcessMap 处理map中的表达式
func (p *BaseYAMLParser) ProcessMap(data map[string]interface{}) error {
	for key, value := range data {
		processed, err := p.ProcessValue(value)
		if err != nil {
			return fmt.Errorf("处理字段[%s]失败: %w", key, err)
		}
		data[key] = processed
	}
	return nil
}

// ProcessSlice 处理数组中的表达式
func (p *BaseYAMLParser) ProcessSlice(data []interface{}) error {
	for i, value := range data {
		processed, err := p.ProcessValue(value)
		if err != nil {
			return fmt.Errorf("处理元素[%d]失败: %w", i, err)
		}
		data[i] = processed
	}
	return nil
}
