package common

import (
	"context"
)

// IExecutor 执行器基础接口
type IExecutor interface {
	// Execute 执行
	Execute(ctx context.Context, params map[string]interface{}) (map[string]interface{}, error)
}

// IActionExecutor 动作执行器接口
type IActionExecutor interface {
	// ExecuteAction 执行动作
	ExecuteAction(ctx context.Context, actionType string, action map[string]interface{}) error
	// RegisterAction 注册动作处理器
	RegisterAction(name string, handler interface{}) error
}

// IConditionExecutor 条件执行器接口
type IConditionExecutor interface {
	// EvaluateCondition 评估条件
	EvaluateCondition(condition string) (bool, error)
	// EvaluateConditions 评估多个条件
	EvaluateConditions(conditions []string) (bool, error)
}

// IExecutorContext 执行器上下文接口
type IExecutorContext interface {
	// GetRuntime 获取运行时
	GetRuntime() interface{}
	// GetVariables 获取变量
	GetVariables() map[string]interface{}
	// SetVariable 设置变量
	SetVariable(name string, value interface{}) error
}
