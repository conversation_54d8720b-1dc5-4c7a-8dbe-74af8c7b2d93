package metadata

import (
	"fmt"
	"sync"
)

// MetadataManager 元数据管理器
type MetadataManager struct {
	metadata map[string]interface{}
	mu       sync.RWMutex
}

// NewMetadataManager 创建元数据管理器
func NewMetadataManager() *MetadataManager {
	return &MetadataManager{
		metadata: make(map[string]interface{}),
	}
}

// Get 获取元数据
func (m *MetadataManager) Get(path string) (interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if value, ok := m.metadata[path]; ok {
		return value, nil
	}
	return nil, fmt.Errorf("metadata not found: %s", path)
}

// Set 设置元数据
func (m *MetadataManager) Set(path string, value interface{}) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.metadata[path] = value
	return nil
}

// GetNamespace 获取指定命名空间下的所有元数据
func (m *MetadataManager) GetNamespace(namespace string) (map[string]interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]interface{})
	for k, v := range m.metadata {
		if k == namespace {
			if m, ok := v.(map[string]interface{}); ok {
				return m, nil
			}
		}
	}
	return result, nil
}

// Clear 清空所有元数据
func (m *MetadataManager) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.metadata = make(map[string]interface{})
}

// Delete 删除指定路径的元数据
func (m *MetadataManager) Delete(path string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.metadata, path)
	return nil
}

// Exists 检查元数据是否存在
func (m *MetadataManager) Exists(path string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	_, ok := m.metadata[path]
	return ok
}

// GetAll 获取所有元数据
func (m *MetadataManager) GetAll() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]interface{})
	for k, v := range m.metadata {
		result[k] = v
	}
	return result
}
