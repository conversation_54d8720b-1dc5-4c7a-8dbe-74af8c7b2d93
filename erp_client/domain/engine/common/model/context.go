package model

// BaseContext 基础上下文模型
type BaseContext struct {
	// Variables 变量表
	Variables map[string]interface{}
	// Parent 父上下文
	Parent *BaseContext
	// Error 错误信息
	Error error
}

// NewBaseContext 创建基础上下文
func NewBaseContext(parent *BaseContext) *BaseContext {
	return &BaseContext{
		Variables: make(map[string]interface{}),
		Parent:    parent,
	}
}

// GetValue 获取变量值
func (c *BaseContext) GetValue(key string) interface{} {
	if value, ok := c.Variables[key]; ok {
		return value
	}
	if c.Parent != nil {
		return c.Parent.GetValue(key)
	}
	return nil
}

// SetValue 设置变量值
func (c *BaseContext) SetValue(key string, value interface{}) {
	c.Variables[key] = value
}

// DeleteValue 删除变量
func (c *BaseContext) DeleteValue(key string) {
	delete(c.Variables, key)
}

// SetError 设置错误
func (c *BaseContext) SetError(err error) {
	c.Error = err
}

// GetError 获取错误
func (c *BaseContext) GetError() error {
	return c.Error
}

// HasError 是否有错误
func (c *BaseContext) HasError() bool {
	return c.Error != nil
}

// Clone 克隆上下文
func (c *BaseContext) Clone() *BaseContext {
	clone := NewBaseContext(c.Parent)
	for k, v := range c.Variables {
		clone.Variables[k] = v
	}
	clone.Error = c.Error
	return clone
}
