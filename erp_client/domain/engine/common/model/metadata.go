package model

// BaseMetadata 基础元数据模型
type BaseMetadata struct {
	// InputVariables 输入变量定义
	InputVariables []string `json:"inputVariables,omitempty" yaml:"inputVariables,omitempty"`
	// OutputVariables 输出变量定义
	OutputVariables []string `json:"outputVariables,omitempty" yaml:"outputVariables,omitempty"`
	// Properties 属性
	Properties map[string]interface{} `json:"properties,omitempty" yaml:"properties,omitempty"`
}

// GetInputVariables 获取输入变量定义
func (m *BaseMetadata) GetInputVariables() []string {
	return m.InputVariables
}

// GetOutputVariables 获取输出变量定义
func (m *BaseMetadata) GetOutputVariables() []string {
	return m.OutputVariables
}

// GetProperty 获取属性
func (m *BaseMetadata) GetProperty(key string) interface{} {
	if m.Properties == nil {
		return nil
	}
	return m.Properties[key]
}

// SetProperty 设置属性
func (m *BaseMetadata) SetProperty(key string, value interface{}) {
	if m.Properties == nil {
		m.Properties = make(map[string]interface{})
	}
	m.Properties[key] = value
}

// HasProperty 检查属性是否存在
func (m *BaseMetadata) HasProperty(key string) bool {
	if m.Properties == nil {
		return false
	}
	_, ok := m.Properties[key]
	return ok
}
