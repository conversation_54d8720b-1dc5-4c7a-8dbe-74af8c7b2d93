package model

// BaseDefinition 基础定义模型
type BaseDefinition struct {
	// ID 定义ID
	ID string `json:"id" yaml:"id"`
	// Name 定义名称
	Name string `json:"name" yaml:"name"`
	// Description 描述
	Description string `json:"description,omitempty" yaml:"description,omitempty"`
	// Version 版本
	Version string `json:"version,omitempty" yaml:"version,omitempty"`
	// Metadata 元数据
	Metadata *BaseMetadata `json:"metadata,omitempty" yaml:"metadata,omitempty"`
}

// GetID 获取定义ID
func (d *BaseDefinition) GetID() string {
	return d.ID
}

// GetName 获取定义名称
func (d *BaseDefinition) GetName() string {
	return d.Name
}

// GetMetadata 获取元数据
func (d *BaseDefinition) GetMetadata() *BaseMetadata {
	return d.Metadata
}

// SetMetadata 设置元数据
func (d *BaseDefinition) SetMetadata(metadata *BaseMetadata) {
	d.Metadata = metadata
}
