package model

// VariableType 变量类型
type VariableType string

const (
	// VariableTypeString 字符串类型
	VariableTypeString VariableType = "string"
	// VariableTypeNumber 数字类型
	VariableTypeNumber VariableType = "number"
	// VariableTypeBoolean 布尔类型
	VariableTypeBoolean VariableType = "boolean"
	// VariableTypeObject 对象类型
	VariableTypeObject VariableType = "object"
	// VariableTypeArray 数组类型
	VariableTypeArray VariableType = "array"
)

// Variable 变量定义
type Variable struct {
	// Name 变量名称
	Name string `json:"name" yaml:"name"`
	// Type 变量类型
	Type VariableType `json:"type" yaml:"type"`
	// Value 变量值
	Value interface{} `json:"value,omitempty" yaml:"value,omitempty"`
	// Required 是否必须
	Required bool `json:"required,omitempty" yaml:"required,omitempty"`
	// Description 描述
	Description string `json:"description,omitempty" yaml:"description,omitempty"`
}

// GetName 获取变量名称
func (v *Variable) GetName() string {
	return v.Name
}

// GetType 获取变量类型
func (v *Variable) GetType() VariableType {
	return v.Type
}

// GetValue 获取变量值
func (v *Variable) GetValue() interface{} {
	return v.Value
}

// SetValue 设置变量值
func (v *Variable) SetValue(value interface{}) {
	v.Value = value
}

// IsRequired 是否必须
func (v *Variable) IsRequired() bool {
	return v.Required
}
