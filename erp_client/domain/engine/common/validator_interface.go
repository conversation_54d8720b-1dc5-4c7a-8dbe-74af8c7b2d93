package common

// IValidator 验证器基础接口
type IValidator interface {
	// Validate 验证
	Validate() error
}

// IMetadataValidator 元数据验证器接口
type IMetadataValidator interface {
	// ValidateMetadata 验证元数据
	ValidateMetadata() error
	// ValidateInputVariables 验证输入变量
	ValidateInputVariables(params map[string]interface{}) error
	// ValidateOutputVariables 验证输出变量
	ValidateOutputVariables(params map[string]interface{}) error
}

// IStructureValidator 结构验证器接口
type IStructureValidator interface {
	// ValidateStructure 验证结构
	ValidateStructure() error
	// ValidateReferences 验证引用
	ValidateReferences() error
}

// IContentValidator 内容验证器接口
type IContentValidator interface {
	// ValidateContent 验证内容
	ValidateContent() error
	// ValidateFormat 验证格式
	ValidateFormat() error
	// ValidateSchema 验证模式
	ValidateSchema() error
}
