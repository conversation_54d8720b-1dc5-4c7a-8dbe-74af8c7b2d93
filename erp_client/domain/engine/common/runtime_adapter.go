package common

import (
	"fmt"
	"strings"
)

// BaseRuntimeAdapter 基础运行时适配器
type BaseRuntimeAdapter struct {
	variables map[string]interface{}
	resolver  *VariableManager
}

// NewBaseRuntimeAdapter 创建基础运行时适配器
func NewBaseRuntimeAdapter() *BaseRuntimeAdapter {
	adapter := &BaseRuntimeAdapter{
		variables: make(map[string]interface{}),
	}
	adapter.resolver = NewVariableManager(adapter)
	return adapter
}

// Get 获取变量值
func (r *BaseRuntimeAdapter) Get(path string) (interface{}, error) {
	if path == "" {
		return nil, fmt.Errorf("path cannot be empty")
	}

	parts := strings.Split(path, ".")
	current := r.variables

	for i, part := range parts[:len(parts)-1] {
		if v, ok := current[part]; ok {
			if m, ok := v.(map[string]interface{}); ok {
				current = m
			} else {
				return nil, fmt.E<PERSON><PERSON>("invalid path at %s", strings.Join(parts[:i+1], "."))
			}
		} else {
			return nil, fmt.Errorf("path not found at %s", strings.Join(parts[:i+1], "."))
		}
	}

	if value, ok := current[parts[len(parts)-1]]; ok {
		return value, nil
	}
	return nil, fmt.Errorf("variable not found: %s", path)
}

// Set 设置变量值
func (r *BaseRuntimeAdapter) Set(path string, value interface{}) error {
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	parts := strings.Split(path, ".")
	current := r.variables

	for _, part := range parts[:len(parts)-1] {
		if v, ok := current[part]; ok {
			if m, ok := v.(map[string]interface{}); ok {
				current = m
			} else {
				m = make(map[string]interface{})
				current[part] = m
				current = m
			}
		} else {
			m := make(map[string]interface{})
			current[part] = m
			current = m
		}
	}

	current[parts[len(parts)-1]] = value
	return nil
}

// GetNamespace 获取命名空间下的所有变量
func (r *BaseRuntimeAdapter) GetNamespace(namespace string) (map[string]interface{}, error) {
	if namespace == "" {
		return nil, fmt.Errorf("namespace cannot be empty")
	}

	value, err := r.Get(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get namespace %s: %w", namespace, err)
	}

	if m, ok := value.(map[string]interface{}); ok {
		return m, nil
	}
	return nil, fmt.Errorf("namespace %s is not a map", namespace)
}

// EvalExpression 执行表达式
func (r *BaseRuntimeAdapter) EvalExpression(expression string) (interface{}, error) {
	if expression == "" {
		return nil, fmt.Errorf("expression cannot be empty")
	}
	return r.resolver.GetParser().Parse(expression)
}
