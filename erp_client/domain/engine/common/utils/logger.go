package utils

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	// DEBUG 调试级别
	DEBUG LogLevel = iota
	// INFO 信息级别
	INFO
	// WARN 警告级别
	WARN
	// ERROR 错误级别
	ERROR
)

// Logger 日志记录器
type Logger struct {
	level     LogLevel
	logger    *log.Logger
	mu        sync.Mutex
	fields    map[string]interface{}
	callDepth int
}

// LoggerOption 日志记录器选项
type LoggerOption func(*Logger)

// WithLevel 设置日志级别
func WithLevel(level LogLevel) LoggerOption {
	return func(l *Logger) {
		l.level = level
	}
}

// WithFields 设置日志字段
func WithFields(fields map[string]interface{}) LoggerOption {
	return func(l *Logger) {
		for k, v := range fields {
			l.fields[k] = v
		}
	}
}

// WithCallDepth 设置调用深度
func WithCallDepth(depth int) LoggerOption {
	return func(l *Logger) {
		l.callDepth = depth
	}
}

// NewLogger 创建日志记录器
func NewLogger(options ...LoggerOption) *Logger {
	l := &Logger{
		level:     INFO,
		logger:    log.New(os.Stdout, "", log.LstdFlags),
		fields:    make(map[string]interface{}),
		callDepth: 2,
	}

	for _, option := range options {
		option(l)
	}

	return l
}

// getCallerInfo 获取调用者信息
func (l *Logger) getCallerInfo() string {
	_, file, line, ok := runtime.Caller(l.callDepth)
	if !ok {
		return "???"
	}
	return fmt.Sprintf("%s:%d", filepath.Base(file), line)
}

// formatMessage 格式化日志消息
func (l *Logger) formatMessage(level LogLevel, msg string) string {
	var levelStr string
	switch level {
	case DEBUG:
		levelStr = "DEBUG"
	case INFO:
		levelStr = "INFO"
	case WARN:
		levelStr = "WARN"
	case ERROR:
		levelStr = "ERROR"
	}

	caller := l.getCallerInfo()
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	var fields []string
	for k, v := range l.fields {
		fields = append(fields, fmt.Sprintf("%s=%v", k, v))
	}
	fieldsStr := ""
	if len(fields) > 0 {
		fieldsStr = " " + strings.Join(fields, " ")
	}

	return fmt.Sprintf("%s [%s] %s%s - %s", timestamp, levelStr, caller, fieldsStr, msg)
}

// log 记录日志
func (l *Logger) log(level LogLevel, msg string) {
	if level < l.level {
		return
	}

	l.mu.Lock()
	defer l.mu.Unlock()

	l.logger.Print(l.formatMessage(level, msg))
}

// Debug 记录调试日志
func (l *Logger) Debug(msg string) {
	l.log(DEBUG, msg)
}

// Info 记录信息日志
func (l *Logger) Info(msg string) {
	l.log(INFO, msg)
}

// Warn 记录警告日志
func (l *Logger) Warn(msg string) {
	l.log(WARN, msg)
}

// Error 记录错误日志
func (l *Logger) Error(msg string) {
	l.log(ERROR, msg)
}

// WithField 添加日志字段
func (l *Logger) WithField(key string, value interface{}) *Logger {
	newLogger := &Logger{
		level:     l.level,
		logger:    l.logger,
		fields:    make(map[string]interface{}),
		callDepth: l.callDepth,
	}

	for k, v := range l.fields {
		newLogger.fields[k] = v
	}
	newLogger.fields[key] = value

	return newLogger
}

// WithFields 添加多个日志字段
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	newLogger := &Logger{
		level:     l.level,
		logger:    l.logger,
		fields:    make(map[string]interface{}),
		callDepth: l.callDepth,
	}

	for k, v := range l.fields {
		newLogger.fields[k] = v
	}
	for k, v := range fields {
		newLogger.fields[k] = v
	}

	return newLogger
}
