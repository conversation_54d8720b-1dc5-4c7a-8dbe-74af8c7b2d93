package utils

import (
	"fmt"
	"regexp"
	"strings"

	"voderpltvv/erp_client/domain/engine/interfaces"
)

var (
	// 变量引用正则表达式
	variablePattern = regexp.MustCompile(`\${([^}]+)}`)
	// 函数调用正则表达式
	functionPattern = regexp.MustCompile(`(\w+)\((.*)\)`)
)

// ExpressionParser 表达式解析器
type ExpressionParser struct {
	// 运行时环境
	runtime interfaces.IRuntime
	// 函数注册表
	functionRegistry FunctionRegistry
}

// FunctionRegistry 函数注册表接口
type FunctionRegistry interface {
	GetFunction(name string) (Function, bool)
}

// Function 函数接口
type Function func(args ...interface{}) (interface{}, error)

// NewExpressionParser 创建表达式解析器
func NewExpressionParser(runtime interfaces.IRuntime, functionRegistry FunctionRegistry) *ExpressionParser {
	return &ExpressionParser{
		runtime:          runtime,
		functionRegistry: functionRegistry,
	}
}

// Parse 解析表达式
func (p *ExpressionParser) Parse(expr string) (interface{}, error) {
	if expr == "" {
		return nil, nil
	}

	// 解析变量引用
	if variablePattern.MatchString(expr) {
		return p.parseVariable(expr)
	}

	// 解析函数调用
	if functionPattern.MatchString(expr) {
		return p.parseFunction(expr)
	}

	// 返回字面量
	return expr, nil
}

// parseVariable 解析变量引用
func (p *ExpressionParser) parseVariable(expr string) (interface{}, error) {
	matches := variablePattern.FindStringSubmatch(expr)
	if len(matches) < 2 {
		return nil, fmt.Errorf("invalid variable reference: %s", expr)
	}

	varName := strings.TrimSpace(matches[1])
	if p.runtime == nil {
		return nil, fmt.Errorf("runtime not set")
	}

	return p.runtime.Get(varName)
}

// parseFunction 解析函数调用
func (p *ExpressionParser) parseFunction(expr string) (interface{}, error) {
	matches := functionPattern.FindStringSubmatch(expr)
	if len(matches) < 3 {
		return nil, fmt.Errorf("invalid function call: %s", expr)
	}

	funcName := matches[1]
	argsStr := matches[2]

	if p.functionRegistry == nil {
		return nil, fmt.Errorf("function registry not set")
	}

	fn, ok := p.functionRegistry.GetFunction(funcName)
	if !ok {
		return nil, fmt.Errorf("function not found: %s", funcName)
	}

	// 解析参数
	var args []interface{}
	if argsStr != "" {
		for _, arg := range strings.Split(argsStr, ",") {
			arg = strings.TrimSpace(arg)
			// 递归解析参数
			parsedArg, err := p.Parse(arg)
			if err != nil {
				return nil, fmt.Errorf("failed to parse argument %s: %v", arg, err)
			}
			args = append(args, parsedArg)
		}
	}

	return fn(args...)
}
