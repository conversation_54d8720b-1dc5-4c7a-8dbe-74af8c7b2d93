package utils

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// TypeConverter 类型转换器
type TypeConverter struct{}

// NewTypeConverter 创建类型转换器
func NewTypeConverter() *TypeConverter {
	return &TypeConverter{}
}

// ToString 转换为字符串
func (c *TypeConverter) ToString(value interface{}) (string, error) {
	if value == nil {
		return "", nil
	}

	switch v := value.(type) {
	case string:
		return v, nil
	case []byte:
		return string(v), nil
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v), nil
	case float32, float64:
		return fmt.Sprintf("%g", v), nil
	case bool:
		return strconv.FormatBool(v), nil
	default:
		return fmt.Sprintf("%v", v), nil
	}
}

// ToInt 转换为整数
func (c *TypeConverter) ToInt(value interface{}) (int64, error) {
	if value == nil {
		return 0, nil
	}

	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int8:
		return int64(v), nil
	case int16:
		return int64(v), nil
	case int32:
		return int64(v), nil
	case int64:
		return v, nil
	case uint:
		return int64(v), nil
	case uint8:
		return int64(v), nil
	case uint16:
		return int64(v), nil
	case uint32:
		return int64(v), nil
	case uint64:
		return int64(v), nil
	case float32:
		return int64(v), nil
	case float64:
		return int64(v), nil
	case string:
		return strconv.ParseInt(strings.TrimSpace(v), 10, 64)
	case bool:
		if v {
			return 1, nil
		}
		return 0, nil
	default:
		return 0, fmt.Errorf("cannot convert %v to int", value)
	}
}

// ToFloat 转换为浮点数
func (c *TypeConverter) ToFloat(value interface{}) (float64, error) {
	if value == nil {
		return 0, nil
	}

	switch v := value.(type) {
	case float32:
		return float64(v), nil
	case float64:
		return v, nil
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		i, err := c.ToInt(v)
		if err != nil {
			return 0, err
		}
		return float64(i), nil
	case string:
		return strconv.ParseFloat(strings.TrimSpace(v), 64)
	case bool:
		if v {
			return 1, nil
		}
		return 0, nil
	default:
		return 0, fmt.Errorf("cannot convert %v to float", value)
	}
}

// ToBool 转换为布尔值
func (c *TypeConverter) ToBool(value interface{}) (bool, error) {
	if value == nil {
		return false, nil
	}

	switch v := value.(type) {
	case bool:
		return v, nil
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		i, err := c.ToInt(v)
		if err != nil {
			return false, err
		}
		return i != 0, nil
	case float32, float64:
		f, err := c.ToFloat(v)
		if err != nil {
			return false, err
		}
		return f != 0, nil
	case string:
		return strconv.ParseBool(strings.TrimSpace(v))
	default:
		return false, fmt.Errorf("cannot convert %v to bool", value)
	}
}

// ToSlice 转换为切片
func (c *TypeConverter) ToSlice(value interface{}) ([]interface{}, error) {
	if value == nil {
		return nil, nil
	}

	v := reflect.ValueOf(value)
	if v.Kind() != reflect.Slice && v.Kind() != reflect.Array {
		return nil, fmt.Errorf("cannot convert %v to slice", value)
	}

	result := make([]interface{}, v.Len())
	for i := 0; i < v.Len(); i++ {
		result[i] = v.Index(i).Interface()
	}
	return result, nil
}

// ToMap 转换为映射
func (c *TypeConverter) ToMap(value interface{}) (map[string]interface{}, error) {
	if value == nil {
		return nil, nil
	}

	v := reflect.ValueOf(value)
	if v.Kind() != reflect.Map {
		return nil, fmt.Errorf("cannot convert %v to map", value)
	}

	result := make(map[string]interface{})
	for _, key := range v.MapKeys() {
		k, err := c.ToString(key.Interface())
		if err != nil {
			return nil, fmt.Errorf("failed to convert map key: %v", err)
		}
		result[k] = v.MapIndex(key).Interface()
	}
	return result, nil
}

// ConvertToType 转换值到指定类型
func (c *TypeConverter) ConvertToType(value interface{}, targetType reflect.Type) (reflect.Value, error) {
	if value == nil {
		return reflect.Zero(targetType), nil
	}

	switch targetType.Kind() {
	case reflect.String:
		str, err := c.ToString(value)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(str), nil

	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		i, err := c.ToInt(value)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(i).Convert(targetType), nil

	case reflect.Float32, reflect.Float64:
		f, err := c.ToFloat(value)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(f).Convert(targetType), nil

	case reflect.Bool:
		b, err := c.ToBool(value)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(b), nil

	case reflect.Slice, reflect.Array:
		s, err := c.ToSlice(value)
		if err != nil {
			return reflect.Value{}, err
		}
		sliceValue := reflect.MakeSlice(targetType, len(s), len(s))
		for i, item := range s {
			converted, err := c.ConvertToType(item, targetType.Elem())
			if err != nil {
				return reflect.Value{}, err
			}
			sliceValue.Index(i).Set(converted)
		}
		return sliceValue, nil

	case reflect.Map:
		m, err := c.ToMap(value)
		if err != nil {
			return reflect.Value{}, err
		}
		mapValue := reflect.MakeMap(targetType)
		for k, v := range m {
			convertedKey, err := c.ConvertToType(k, targetType.Key())
			if err != nil {
				return reflect.Value{}, err
			}
			convertedValue, err := c.ConvertToType(v, targetType.Elem())
			if err != nil {
				return reflect.Value{}, err
			}
			mapValue.SetMapIndex(convertedKey, convertedValue)
		}
		return mapValue, nil

	case reflect.Struct:
		// 尝试转换为目标结构体
		if valueMap, ok := value.(map[string]interface{}); ok {
			newStruct := reflect.New(targetType).Elem()
			for i := 0; i < targetType.NumField(); i++ {
				field := targetType.Field(i)
				if v, ok := valueMap[field.Name]; ok {
					if converted, err := c.ConvertToType(v, field.Type); err == nil {
						newStruct.Field(i).Set(converted)
					}
				}
			}
			return newStruct, nil
		}
	}

	return reflect.Value{}, fmt.Errorf("无法转换类型: %v -> %v", reflect.TypeOf(value), targetType)
}
