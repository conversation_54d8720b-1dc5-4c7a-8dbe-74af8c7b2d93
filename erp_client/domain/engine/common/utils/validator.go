package utils

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
)

// Validator 验证器
type Validator struct {
	converter *TypeConverter
}

// NewValidator 创建验证器
func NewValidator() *Validator {
	return &Validator{
		converter: NewTypeConverter(),
	}
}

// ValidateRequired 验证必填
func (v *Validator) ValidateRequired(value interface{}, fieldName string) error {
	if value == nil {
		return fmt.Errorf("%s is required", fieldName)
	}

	rv := reflect.ValueOf(value)
	switch rv.Kind() {
	case reflect.String:
		if strings.TrimSpace(rv.String()) == "" {
			return fmt.Errorf("%s is required", fieldName)
		}
	case reflect.Slice, reflect.Array, reflect.Map:
		if rv.Len() == 0 {
			return fmt.Errorf("%s is required", fieldName)
		}
	}

	return nil
}

// ValidateType 验证类型
func (v *Validator) ValidateType(value interface{}, expectedType string, fieldName string) error {
	if value == nil {
		return nil
	}

	switch expectedType {
	case "string":
		if _, err := v.converter.ToString(value); err != nil {
			return fmt.Errorf("%s must be a string", fieldName)
		}
	case "int":
		if _, err := v.converter.ToInt(value); err != nil {
			return fmt.Errorf("%s must be an integer", fieldName)
		}
	case "float":
		if _, err := v.converter.ToFloat(value); err != nil {
			return fmt.Errorf("%s must be a float", fieldName)
		}
	case "bool":
		if _, err := v.converter.ToBool(value); err != nil {
			return fmt.Errorf("%s must be a boolean", fieldName)
		}
	case "slice":
		if _, err := v.converter.ToSlice(value); err != nil {
			return fmt.Errorf("%s must be a slice", fieldName)
		}
	case "map":
		if _, err := v.converter.ToMap(value); err != nil {
			return fmt.Errorf("%s must be a map", fieldName)
		}
	default:
		return fmt.Errorf("unsupported type: %s", expectedType)
	}

	return nil
}

// ValidateLength 验证长度
func (v *Validator) ValidateLength(value interface{}, min, max int, fieldName string) error {
	if value == nil {
		return nil
	}

	rv := reflect.ValueOf(value)
	var length int

	switch rv.Kind() {
	case reflect.String:
		length = len([]rune(rv.String()))
	case reflect.Slice, reflect.Array, reflect.Map:
		length = rv.Len()
	default:
		return fmt.Errorf("cannot validate length of %s", fieldName)
	}

	if min > 0 && length < min {
		return fmt.Errorf("%s length must be at least %d", fieldName, min)
	}
	if max > 0 && length > max {
		return fmt.Errorf("%s length must be at most %d", fieldName, max)
	}

	return nil
}

// ValidateRegex 验证正则表达式
func (v *Validator) ValidateRegex(value interface{}, pattern string, fieldName string) error {
	if value == nil {
		return nil
	}

	str, err := v.converter.ToString(value)
	if err != nil {
		return fmt.Errorf("cannot validate regex for %s", fieldName)
	}

	matched, err := regexp.MatchString(pattern, str)
	if err != nil {
		return fmt.Errorf("invalid regex pattern for %s: %v", fieldName, err)
	}
	if !matched {
		return fmt.Errorf("%s does not match pattern: %s", fieldName, pattern)
	}

	return nil
}

// ValidateRange 验证数值范围
func (v *Validator) ValidateRange(value interface{}, min, max float64, fieldName string) error {
	if value == nil {
		return nil
	}

	num, err := v.converter.ToFloat(value)
	if err != nil {
		return fmt.Errorf("cannot validate range for %s", fieldName)
	}

	if min != 0 && num < min {
		return fmt.Errorf("%s must be at least %g", fieldName, min)
	}
	if max != 0 && num > max {
		return fmt.Errorf("%s must be at most %g", fieldName, max)
	}

	return nil
}

// ValidateEnum 验证枚举值
func (v *Validator) ValidateEnum(value interface{}, enumValues []interface{}, fieldName string) error {
	if value == nil {
		return nil
	}

	for _, enum := range enumValues {
		if reflect.DeepEqual(value, enum) {
			return nil
		}
	}

	return fmt.Errorf("%s must be one of: %v", fieldName, enumValues)
}
