package common

import (
	"context"
)

// IEngine 引擎基础接口
type IEngine interface {
	// Execute 执行引擎
	Execute(ctx context.Context, id string, params map[string]interface{}) (map[string]interface{}, error)
}

// IEngineContext 引擎上下文接口
type IEngineContext interface {
	// GetValue 获取值
	GetValue(key string) interface{}
	// SetValue 设置值
	SetValue(key string, value interface{})
	// DeleteValue 删除值
	DeleteValue(key string)
	// SetError 设置错误
	SetError(err error)
	// GetError 获取错误
	GetError() error
	// HasError 是否有错误
	HasError() bool
}

// IEngineMetadata 引擎元数据接口
type IEngineMetadata interface {
	// GetInputVariables 获取输入变量定义
	GetInputVariables() []string
	// GetOutputVariables 获取输出变量定义
	GetOutputVariables() []string
	// ValidateInput 验证输入参数
	ValidateInput(params map[string]interface{}) error
}

// IEngineDefinition 引擎定义接口
type IEngineDefinition interface {
	// GetID 获取定义ID
	GetID() string
	// GetName 获取定义名称
	GetName() string
	// GetMetadata 获取元数据
	GetMetadata() IEngineMetadata
}

// IEngineLoader 引擎加载器接口
type IEngineLoader interface {
	// Load 加载定义
	Load(content []byte) error
}

// IEngineRegistry 引擎注册表接口
type IEngineRegistry interface {
	// Register 注册定义
	Register(def IEngineDefinition) error
	// Unregister 注销定义
	Unregister(id string) error
	// Get 获取定义
	Get(id string) (IEngineDefinition, error)
}
