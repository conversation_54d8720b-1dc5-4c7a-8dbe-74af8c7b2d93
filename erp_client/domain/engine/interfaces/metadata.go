package interfaces

// IMetadata 元数据管理接口
type IMetadata interface {
	// Get 获取元数据
	Get(path string) (interface{}, error)

	// Set 设置元数据
	Set(path string, value interface{}) error

	// GetNamespace 获取指定命名空间下的所有元数据
	GetNamespace(namespace string) (map[string]interface{}, error)

	// Clear 清空所有元数据
	Clear()

	// Delete 删除指定路径的元数据
	Delete(path string) error

	// Exists 检查元数据是否存在
	Exists(path string) bool

	// GetAll 获取所有元数据
	GetAll() map[string]interface{}
}
