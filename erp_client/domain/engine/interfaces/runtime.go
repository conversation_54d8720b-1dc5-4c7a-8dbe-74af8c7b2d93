package interfaces

// IRuntime 统一的运行时接口
type IRuntime interface {
	// Get 获取变量值
	// path: 变量路径，支持点号分隔的多级路径
	// 返回: 变量值和可能的错误
	Get(path string) (interface{}, error)

	// Set 设置变量值
	// path: 变量路径，支持点号分隔的多级路径
	// value: 要设置的值
	// 返回: 可能的错误
	Set(path string, value interface{}) error

	// GetNamespace 获取指定命名空间下的所有变量
	// namespace: 命名空间
	// 返回: 命名空间下的变量映射和可能的错误
	GetNamespace(namespace string) (map[string]interface{}, error)

	// EvalExpression 计算表达式
	// expression: 要计算的表达式
	// 返回: 表达式的计算结果和可能的错误
	EvalExpression(expression string) (interface{}, error)
}
