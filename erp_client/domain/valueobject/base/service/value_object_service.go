package service

import (
	"context"
)

// ValueObjectService 值对象服务接口
type ValueObjectService[T any] interface {
	// Create 创建值对象
	Create(ctx context.Context, vo T) error
	// Update 更新值对象
	Update(ctx context.Context, vo T) error
	// Delete 删除值对象
	Delete(ctx context.Context, id string) error
	// Validate 验证值对象
	Validate(ctx context.Context, vo T) error
	// FindByCondition 根据条件查询值对象
	FindByCondition(ctx context.Context, condition map[string]interface{}) ([]T, error)
}
