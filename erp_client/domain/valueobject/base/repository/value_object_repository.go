package repository

import (
	"context"
)

// ValueObjectRepository 基础值对象仓储接口
type ValueObjectRepository[T any] interface {
	// Create 创建值对象
	Create(ctx context.Context, po *T) error
	// Update 更新值对象
	Update(ctx context.Context, po *T) error
	// Delete 删除值对象
	Delete(ctx context.Context, id string) error
	// FindByID 根据ID查询值对象
	FindByID(ctx context.Context, id string) (*T, error)
	// FindByCondition 根据条件查询值对象
	FindByCondition(ctx context.Context, condition map[string]interface{}) (*[]T, error)
}
