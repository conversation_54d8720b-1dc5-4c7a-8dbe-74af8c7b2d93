package model

import (
	"errors"
)

// ValueObject 值对象接口
type ValueObject interface {
	// GetID 获取ID
	GetID() string
	// GetCtime 获取创建时间
	GetCtime() int64
	// GetUtime 获取更新时间
	GetUtime() int64
	// GetState 获取状态
	GetState() int
	// GetVersion 获取版本
	GetVersion() int
	// Validate 验证
	Validate() error
	// Clone 克隆
	Clone() ValueObject
}

// 基础错误定义
var (
	// ErrInvalidValue 无效的值
	ErrInvalidValue = errors.New("invalid value")
	// ErrEmptyID ID为空
	ErrEmptyID = errors.New("empty id")
	// ErrEmptyField 必填字段为空
	ErrEmptyField = errors.New("required field is empty")
	// ErrInvalidFormat 格式错误
	ErrInvalidFormat = errors.New("invalid format")
	// ErrOutOfRange 值超出范围
	ErrOutOfRange = errors.New("value out of range")
	// ErrNotFound 未找到对象
	ErrNotFound = errors.New("object not found")
	// ErrDuplicate 对象重复
	ErrDuplicate = errors.New("object already exists")
)
