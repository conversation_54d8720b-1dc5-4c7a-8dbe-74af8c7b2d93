package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 场次仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.RoomOperation]

	// ConvertToRoomOperationVO 转换为房间类型VO
	ConvertToRoomOperationVO(ctx context.Context, roomOperation po.RoomOperation) vo.RoomOperationVO

	// ConvertToRoomOperationPO 转换为房间类型PO
	ConvertToRoomOperation(ctx context.Context, roomOperationVO vo.RoomOperationVO) po.RoomOperation

}
