package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomOperationService 场次服务接口
type RoomOperationService interface {
	baseService.ValueObjectService[po.RoomOperation]

	// FindByID 根据ID查询场次
	FindByID(ctx context.Context, id string) (*po.RoomOperation, error)

	// ConvertToRoomOperationVO 转换为房间类型VO
	ConvertToRoomOperationVO(ctx context.Context, roomOperation po.RoomOperation) vo.RoomOperationVO

	// ConvertToRoomOperationPO 转换为房间类型PO
	ConvertToRoomOperation(ctx context.Context, roomOperationVO vo.RoomOperationVO) po.RoomOperation

}
