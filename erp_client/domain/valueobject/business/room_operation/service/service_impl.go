package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/room_operation/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomOperationServiceImpl 包厢服务实现
type RoomOperationServiceImpl struct {
	repo repository.Repository
}

// NewService 创建场次服务
func NewService(repo repository.Repository) RoomOperationService {
	return 	&RoomOperationServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *RoomOperationServiceImpl) Create(ctx context.Context, roomOperation po.RoomOperation) error {
	return nil
}

// Update 更新值对象
func (s *RoomOperationServiceImpl) Update(ctx context.Context, roomOperation po.RoomOperation) error {
	return nil
}

// Delete 删除值对象
func (s *RoomOperationServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *RoomOperationServiceImpl) Validate(ctx context.Context, roomOperation po.RoomOperation) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *RoomOperationServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.RoomOperation, error) {
	return nil, nil
}

// FindByVenueID 根据场所ID查询房间类型列表
func (s *RoomOperationServiceImpl) FindByID(ctx context.Context, id string) (*po.RoomOperation, error) {
	return s.repo.FindByID(ctx, id)
}

// ConvertToRoomOperationVO 转换为房间类型VO
func (s *RoomOperationServiceImpl) ConvertToRoomOperationVO(ctx context.Context, roomOperation po.RoomOperation) vo.RoomOperationVO {
	return s.repo.ConvertToRoomOperationVO(ctx, roomOperation)
}

// ConvertToRoomOperationPO 转换为房间类型PO
func (s *RoomOperationServiceImpl) ConvertToRoomOperation(ctx context.Context, roomOperationVO vo.RoomOperationVO) po.RoomOperation {
	return s.repo.ConvertToRoomOperation(ctx, roomOperationVO)
}
