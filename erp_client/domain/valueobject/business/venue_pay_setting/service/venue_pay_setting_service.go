package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// VenuePaySettingService 门店支付设置服务接口
type VenuePaySettingService interface {
	baseService.ValueObjectService[po.VenuePaySetting]

	// FindByID 根据ID查询门店支付设置
	FindByID(ctx context.Context, id string) (*po.VenuePaySetting, error)

	// FindAll 查询所有门店支付设置
	FindAll(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, error)

	// FindAllWithPagination 分页查询所有门店支付设置
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, int64, error)

	// FindVenuePaySettingsByIds 根据ID列表查询门店支付设置
	FindVenuePaySettingsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error)

	// ConvertToVenuePaySettingVO 转换为门店支付设置VO
	ConvertToVenuePaySettingVO(ctx context.Context, venuePaySetting po.VenuePaySetting) vo.VenuePaySettingVO

	// ConvertToVenuePaySetting 转换为门店支付设置PO
	ConvertToVenuePaySetting(ctx context.Context, venuePaySettingVO vo.VenuePaySettingVO) po.VenuePaySetting

	// FindsByIds 根据ID列表查询门店支付设置
	FindsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error)

	// FindVenuePaySettingByVenueId 根据门店ID查询支付设置
	FindVenuePaySettingByVenueId(ctx context.Context, venueId string) (po.VenuePaySetting, error)

}
