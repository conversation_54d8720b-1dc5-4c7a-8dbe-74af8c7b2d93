package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/venue_pay_setting/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// VenuePaySettingServiceImpl 门店支付设置服务实现
type VenuePaySettingServiceImpl struct {
	repo repository.Repository
}

// NewService 创建门店支付设置服务
func NewService(repo repository.Repository) VenuePaySettingService {
	return &VenuePaySettingServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *VenuePaySettingServiceImpl) Create(ctx context.Context, venuePaySetting po.VenuePaySetting) error {
	return nil
}

// Update 更新值对象
func (s *VenuePaySettingServiceImpl) Update(ctx context.Context, venuePaySetting po.VenuePaySetting) error {
	return nil
}

// Delete 删除值对象
func (s *VenuePaySettingServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *VenuePaySettingServiceImpl) Validate(ctx context.Context, venuePaySetting po.VenuePaySetting) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *VenuePaySettingServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.VenuePaySetting, error) {
	return nil, nil
}

// FindByID 根据ID查询门店支付设置
func (s *VenuePaySettingServiceImpl) FindByID(ctx context.Context, id string) (*po.VenuePaySetting, error) {
	return s.repo.FindByID(ctx, id)
}

// FindAll 查询所有门店支付设置
func (s *VenuePaySettingServiceImpl) FindAll(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, error) {
	return s.repo.FindAll(ctx, reqDto)
}

// FindAllWithPagination 分页查询所有门店支付设置
func (s *VenuePaySettingServiceImpl) FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, int64, error) {
	return s.repo.FindAllWithPagination(ctx, reqDto)
}

// FindVenuePaySettingsByIds 根据ID列表查询门店支付设置
func (s *VenuePaySettingServiceImpl) FindVenuePaySettingsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error) {
	return s.repo.FindVenuePaySettingsByIds(ctx, venueId, venuePaySettingIds)
}

// ConvertToVenuePaySettingVO 转换为门店支付设置VO
func (s *VenuePaySettingServiceImpl) ConvertToVenuePaySettingVO(ctx context.Context, venuePaySetting po.VenuePaySetting) vo.VenuePaySettingVO {
	return s.repo.ConvertToVenuePaySettingVO(ctx, venuePaySetting)
}

// ConvertToVenuePaySetting 转换为门店支付设置PO
func (s *VenuePaySettingServiceImpl) ConvertToVenuePaySetting(ctx context.Context, venuePaySettingVO vo.VenuePaySettingVO) po.VenuePaySetting {
	return s.repo.ConvertToVenuePaySetting(ctx, venuePaySettingVO)
}

// FindsByIds 根据ID列表查询门店支付设置
func (s *VenuePaySettingServiceImpl) FindsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error) {
	return s.repo.FindsByIds(ctx, venueId, venuePaySettingIds)
}

// FindVenuePaySettingByVenueId 根据门店ID查询支付设置
func (s *VenuePaySettingServiceImpl) FindVenuePaySettingByVenueId(ctx context.Context, venueId string) (po.VenuePaySetting, error) {
	return s.repo.FindVenuePaySettingByVenueId(ctx, venueId)
}