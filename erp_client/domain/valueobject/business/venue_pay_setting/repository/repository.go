package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository VenuePaySetting仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.VenuePaySetting]

	// FindAll 查询所有门店支付设置
	FindAll(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, error)

	// FindAllWithPagination 分页查询所有门店支付设置
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePaySettingReqDto) (*[]po.VenuePaySetting, int64, error)

	// FindVenuePaySettingsByIds 根据ID列表查询门店支付设置
	FindVenuePaySettingsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error)

	// ConvertToVenuePaySettingVO 转换为门店支付设置VO
	ConvertToVenuePaySettingVO(ctx context.Context, venuePaySetting po.VenuePaySetting) vo.VenuePaySettingVO

	// ConvertToVenuePaySetting 转换为门店支付设置PO
	ConvertToVenuePaySetting(ctx context.Context, venuePaySettingVO vo.VenuePaySettingVO) po.VenuePaySetting

	// FindsByIds 根据ID列表查询门店支付设置
	FindsByIds(ctx context.Context, venueId string, venuePaySettingIds []string) ([]po.VenuePaySetting, error)

	// FindVenuePaySettingByVenueId 根据门店ID查询支付设置
	FindVenuePaySettingByVenueId(ctx context.Context, venueId string) (po.VenuePaySetting, error)
}
