package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"
	"voderpltvv/erp_client/domain/valueobject/business/holiday/model"
	"voderpltvv/erp_managent/service/po"
)

// Repository 节假日仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.Holiday]

	// FindByVenueID 根据场所ID查询节假日列表
	FindByVenueID(ctx context.Context, venueID string) ([]model.Holiday, error)
	// FindByDate 根据日期查询节假日列表
	FindByDate(ctx context.Context, date string) ([]model.Holiday, error)
	// FindByVenueIDAndDate 根据场所ID和日期查询节假日列表
	FindByVenueIDAndDate(ctx context.Context, venueID string, date string) ([]model.Holiday, error)
}
