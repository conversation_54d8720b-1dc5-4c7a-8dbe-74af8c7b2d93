package service

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/holiday/model"
	"voderpltvv/erp_client/domain/valueobject/business/holiday/repository"
	"voderpltvv/erp_managent/service/po"
)

// serviceImpl 节假日值对象服务实现
type serviceImpl struct {
	repo repository.Repository
}

// NewService 创建节假日值对象服务
func NewService(repo repository.Repository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *serviceImpl) Create(ctx context.Context, holiday po.Holiday) error {
	vo := model.NewHolidayFromPO(&holiday)
	return s.repo.Create(ctx, vo.GetPO())
}

// Update 更新值对象
func (s *serviceImpl) Update(ctx context.Context, holiday po.Holiday) error {
	vo := model.NewHolidayFromPO(&holiday)
	return s.repo.Update(ctx, vo.GetPO())
}

// Delete 删除值对象
func (s *serviceImpl) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// Validate 验证值对象
func (s *serviceImpl) Validate(ctx context.Context, holiday po.Holiday) error {
	vo := model.NewHolidayFromPO(&holiday)
	return vo.Validate()
}

// FindByCondition 根据条件查询值对象
func (s *serviceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.Holiday, error) {
	pos, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	if pos == nil {
		return []po.Holiday{}, nil
	}
	result := make([]po.Holiday, len(*pos))
	for i, p := range *pos {
		result[i] = p
	}
	return result, nil
}

// GetHoliday 获取节假日信息
func (s *serviceImpl) GetHoliday(ctx context.Context, id string) (po.Holiday, error) {
	if id == "" {
		return po.Holiday{}, nil
	}
	holiday, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return po.Holiday{}, err
	}
	if holiday == nil {
		return po.Holiday{}, nil
	}
	return *holiday, nil
}

// FindByVenueID 根据场所ID查询节假日列表
func (s *serviceImpl) FindByVenueID(ctx context.Context, venueID string) ([]po.Holiday, error) {
	if venueID == "" {
		return []po.Holiday{}, nil
	}
	holidays, err := s.repo.FindByVenueID(ctx, venueID)
	if err != nil {
		return nil, err
	}
	result := make([]po.Holiday, len(holidays))
	for i, holiday := range holidays {
		result[i] = *holiday.GetPO()
	}
	return result, nil
}

// FindByDate 根据日期查询节假日列表
func (s *serviceImpl) FindByDate(ctx context.Context, date string) ([]po.Holiday, error) {
	if date == "" {
		return []po.Holiday{}, nil
	}
	holidays, err := s.repo.FindByDate(ctx, date)
	if err != nil {
		return nil, err
	}
	result := make([]po.Holiday, len(holidays))
	for i, holiday := range holidays {
		result[i] = *holiday.GetPO()
	}
	return result, nil
}

// FindByVenueIDAndDate 根据场所ID和日期查询节假日列表
func (s *serviceImpl) FindByVenueIDAndDate(ctx context.Context, venueID string, date string) ([]po.Holiday, error) {
	if venueID == "" || date == "" {
		return []po.Holiday{}, nil
	}
	holidays, err := s.repo.FindByVenueIDAndDate(ctx, venueID, date)
	if err != nil {
		return nil, err
	}
	result := make([]po.Holiday, len(holidays))
	for i, holiday := range holidays {
		result[i] = *holiday.GetPO()
	}
	return result, nil
}

// FindByDateRange 根据日期范围查询节假日列表
func (s *serviceImpl) FindByDateRange(ctx context.Context, startDate string, endDate string) ([]po.Holiday, error) {
	if startDate == "" || endDate == "" {
		return nil, nil
	}
	pos, err := s.repo.FindByCondition(ctx, map[string]interface{}{
		"date": map[string]interface{}{
			"$gte": startDate,
			"$lte": endDate,
		},
	})
	if err != nil {
		return nil, err
	}
	return *pos, nil
}
