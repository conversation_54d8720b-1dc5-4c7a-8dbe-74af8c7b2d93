package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"
	"voderpltvv/erp_managent/service/po"
)

// Service 节假日服务接口
type Service interface {
	baseService.ValueObjectService[po.Holiday]

	// GetHoliday 获取节假日信息
	GetHoliday(ctx context.Context, id string) (po.Holiday, error)
	// FindByVenueID 根据场所ID查询节假日列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.Holiday, error)
	// FindByDate 根据日期查询节假日
	FindByDate(ctx context.Context, date string) ([]po.Holiday, error)
	// FindByVenueIDAndDate 根据场所ID和日期查询节假日列表
	FindByVenueIDAndDate(ctx context.Context, venueID string, date string) ([]po.Holiday, error)
}
