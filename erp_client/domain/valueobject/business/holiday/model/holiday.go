package model

import (
	"context"
	"errors"
	baseModel "voderpltvv/erp_client/domain/valueobject/base/model"
	"voderpltvv/erp_managent/service/po"
)

// Holiday 节假日值对象接口
type Holiday interface {
	baseModel.ValueObject

	// GetID 获取ID
	GetID() string
	// GetName 获取名称
	GetName() string
	// GetDate 获取日期
	GetDate() string
	// GetType 获取类型
	GetType() string
	// GetVenueID 获取场所ID
	GetVenueID() string
	// GetPO 获取PO对象
	GetPO() *po.Holiday
}

// holidayImpl 节假日值对象实现
type holidayImpl struct {
	po *po.Holiday
}

// NewHolidayFromPO 从PO创建节假日值对象
func NewHolidayFromPO(po *po.Holiday) Holiday {
	return &holidayImpl{
		po: po,
	}
}

// GetID 获取ID
func (h *holidayImpl) GetID() string {
	return *h.po.Id
}

// GetName 获取名称
func (h *holidayImpl) GetName() string {
	return *h.po.Name
}

// GetDate 获取日期
func (h *holidayImpl) GetDate() string {
	return *h.po.Date
}

// GetType 获取类型
func (h *holidayImpl) GetType() string {
	return *h.po.Type
}

// GetVenueID 获取场所ID
func (h *holidayImpl) GetVenueID() string {
	return *h.po.VenueId
}

// GetPO 获取PO对象
func (h *holidayImpl) GetPO() *po.Holiday {
	return h.po
}

// GetCtime 获取创建时间
func (h *holidayImpl) GetCtime() int64 {
	return *h.po.Ctime
}

// GetUtime 获取更新时间
func (h *holidayImpl) GetUtime() int64 {
	return *h.po.Utime
}

// GetState 获取状态
func (h *holidayImpl) GetState() int {
	return *h.po.State
}

// GetVersion 获取版本
func (h *holidayImpl) GetVersion() int {
	return *h.po.Version
}

// Validate 验证
func (h *holidayImpl) Validate() error {
	if h.po.Id == nil {
		return errors.New("id is required")
	}
	if h.po.VenueId == nil {
		return errors.New("venueId is required")
	}
	if h.po.Name == nil {
		return errors.New("name is required")
	}
	if h.po.Date == nil {
		return errors.New("date is required")
	}
	if h.po.Type == nil {
		return errors.New("type is required")
	}
	return nil
}

// Clone 克隆
func (h *holidayImpl) Clone() baseModel.ValueObject {
	return NewHolidayFromPO(h.po)
}

// BeforeCreate 创建前钩子
func (h *holidayImpl) BeforeCreate(ctx context.Context) error {
	return nil
}

// BeforeUpdate 更新前钩子
func (h *holidayImpl) BeforeUpdate(ctx context.Context) error {
	return nil
}

// Equals 值对象比较
func (h *holidayImpl) Equals(other baseModel.ValueObject) bool {
	if other == nil {
		return false
	}
	if otherHoliday, ok := other.(Holiday); ok {
		return h.GetID() == otherHoliday.GetID() &&
			h.GetVenueID() == otherHoliday.GetVenueID() &&
			h.GetName() == otherHoliday.GetName() &&
			h.GetDate() == otherHoliday.GetDate() &&
			h.GetType() == otherHoliday.GetType()
	}
	return false
}
