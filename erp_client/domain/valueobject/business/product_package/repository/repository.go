package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository ProductPackage仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.ProductPackage]

	// FindAll 查询所有产品套餐
	FindAll(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, error)

	// FindAllWithPagination 分页查询所有产品套餐
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, int64, error)

	// FindProductPackagesByIds 根据ID列表查询产品套餐
	FindProductPackagesByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error)

	// ConvertToProductPackageVO 转换为产品套餐VO
	ConvertToProductPackageVO(ctx context.Context, productPackage po.ProductPackage) vo.ProductPackageVO

	// ConvertToProductPackage 转换为产品套餐PO
	ConvertToProductPackage(ctx context.Context, productPackageVO vo.ProductPackageVO) po.ProductPackage

	// FindsByIds 根据ID列表查询产品套餐
	FindsByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error)
}
