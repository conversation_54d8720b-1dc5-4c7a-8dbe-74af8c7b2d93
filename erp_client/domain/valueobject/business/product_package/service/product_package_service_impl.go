package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/product_package/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductPackageServiceImpl 产品套餐服务实现
type ProductPackageServiceImpl struct {
	repo repository.Repository
}

// NewService 创建产品套餐服务
func NewService(repo repository.Repository) ProductPackageService {
	return &ProductPackageServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *ProductPackageServiceImpl) Create(ctx context.Context, productPackage po.ProductPackage) error {
	return nil
}

// Update 更新值对象
func (s *ProductPackageServiceImpl) Update(ctx context.Context, productPackage po.ProductPackage) error {
	return nil
}

// Delete 删除值对象
func (s *ProductPackageServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *ProductPackageServiceImpl) Validate(ctx context.Context, productPackage po.ProductPackage) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *ProductPackageServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.ProductPackage, error) {
	return nil, nil
}

// FindByID 根据ID查询产品套餐
func (s *ProductPackageServiceImpl) FindByID(ctx context.Context, id string) (*po.ProductPackage, error) {
	return s.repo.FindByID(ctx, id)
}

// FindAll 查询所有产品套餐
func (s *ProductPackageServiceImpl) FindAll(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, error) {
	return s.repo.FindAll(ctx, reqDto)
}

// FindAllWithPagination 分页查询所有产品套餐
func (s *ProductPackageServiceImpl) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, int64, error) {
	return s.repo.FindAllWithPagination(ctx, reqDto)
}

// FindProductPackagesByIds 根据ID列表查询产品套餐
func (s *ProductPackageServiceImpl) FindProductPackagesByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error) {
	return s.repo.FindProductPackagesByIds(ctx, venueId, productPackageIds)
}

// ConvertToProductPackageVO 转换为产品套餐VO
func (s *ProductPackageServiceImpl) ConvertToProductPackageVO(ctx context.Context, productPackage po.ProductPackage) vo.ProductPackageVO {
	return s.repo.ConvertToProductPackageVO(ctx, productPackage)
}

// ConvertToProductPackage 转换为产品套餐PO
func (s *ProductPackageServiceImpl) ConvertToProductPackage(ctx context.Context, productPackageVO vo.ProductPackageVO) po.ProductPackage {
	return s.repo.ConvertToProductPackage(ctx, productPackageVO)
}

// FindsByIds 根据ID列表查询产品套餐
func (s *ProductPackageServiceImpl) FindsByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error) {
	return s.repo.FindsByIds(ctx, venueId, productPackageIds)
}
