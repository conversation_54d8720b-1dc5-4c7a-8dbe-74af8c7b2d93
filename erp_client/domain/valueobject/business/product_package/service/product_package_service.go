package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductPackageService 产品套餐服务接口
type ProductPackageService interface {
	baseService.ValueObjectService[po.ProductPackage]

	// FindByID 根据ID查询产品套餐
	FindByID(ctx context.Context, id string) (*po.ProductPackage, error)

	// FindAll 查询所有产品套餐
	FindAll(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, error)

	// FindAllWithPagination 分页查询所有产品套餐
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageReqDto) (*[]po.ProductPackage, int64, error)

	// FindProductPackagesByIds 根据ID列表查询产品套餐
	FindProductPackagesByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error)

	// ConvertToProductPackageVO 转换为产品套餐VO
	ConvertToProductPackageVO(ctx context.Context, productPackage po.ProductPackage) vo.ProductPackageVO

	// ConvertToProductPackage 转换为产品套餐PO
	ConvertToProductPackage(ctx context.Context, productPackageVO vo.ProductPackageVO) po.ProductPackage

	// FindsByIds 根据ID列表查询产品套餐
	FindsByIds(ctx context.Context, venueId string, productPackageIds []string) ([]po.ProductPackage, error)
}
