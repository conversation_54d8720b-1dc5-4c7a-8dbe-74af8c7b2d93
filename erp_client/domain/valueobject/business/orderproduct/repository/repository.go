package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 房间类型仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.OrderProduct]

	// GetOrderProduct 获取订单房间计划
	GetOrderProduct(ctx context.Context, id string) (po.OrderProduct, error)

	// ConvertToOrderProductVO 转换为订单房间计划VO
	ConvertToOrderProductVO(ctx context.Context, orderProduct po.OrderProduct) vo.OrderProductVO

	// ConvertToOrderProduct 转换为订单房间计划PO
	ConvertToOrderProduct(ctx context.Context, orderProductVO vo.OrderProductVO) po.OrderProduct

	// FindAllBySessionId 根据sessionId查询订单商品
	FindAllBySessionId(ctx context.Context, sessionId string, venueId string) (*[]po.OrderProduct, error)

	// FindAllByIds 根据ids查询订单商品
	FindAllByIds(ctx context.Context, sessionId string, venueId string, ids []string) ([]po.OrderProduct, error)

	// FindsByOrderNos 根据orderNos查询订单商品
	FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderProduct, error)

	// FindOrderProductsTimeRange 查询订单商品时间范围
	FindOrderProductsTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.OrderProduct, error)

	// FindProductsByIds 查询商品信息
	FindProductsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error)

	// FindCategorysByIds 查询分类信息
	FindCategorysByIds(ctx context.Context, venueId string, categoryIds []string) ([]po.ProductType, error)

	// FindOrderProductsGiftBySessionIds 查询订单商品时间范围
	FindOrderProductsGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderProduct, error)
}
