package service

import (
	"context"
	"encoding/json"
	orderRepo "voderpltvv/erp_client/domain/traderecord/repository"
	"voderpltvv/erp_client/domain/valueobject/business/orderproduct/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/sirupsen/logrus"
)

// serviceImpl 订单房间计划值对象服务实现
type serviceImpl struct {
	repo      repository.Repository
	orderRepo orderRepo.OrderRepository
}

// NewService 创建订单房间计划值对象服务
func NewService(repo repository.Repository, orderRepo orderRepo.OrderRepository) Service {
	return &serviceImpl{
		repo:      repo,
		orderRepo: orderRepo,
	}
}

// Create 创建值对象
func (s *serviceImpl) Create(ctx context.Context, po po.OrderProduct) error {
	return s.repo.Create(ctx, &po)
}

// Update 更新值对象
func (s *serviceImpl) Update(ctx context.Context, po po.OrderProduct) error {
	return s.repo.Update(ctx, &po)
}

// Delete 删除值对象
func (s *serviceImpl) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// Validate 验证值对象
func (s *serviceImpl) Validate(ctx context.Context, po po.OrderProduct) error {
	return nil
}

// FindByID 根据ID查询值对象
func (s *serviceImpl) FindByID(ctx context.Context, id string) (*po.OrderProduct, error) {
	return s.repo.FindByID(ctx, id)
}

// FindByCondition 根据条件查询值对象
func (s *serviceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.OrderProduct, error) {
	pos, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return *pos, nil
}

// GetOrderProduct 获取订单房间计划
func (s *serviceImpl) GetOrderProduct(ctx context.Context, id string) (po.OrderProduct, error) {
	return s.repo.GetOrderProduct(ctx, id)
}

// ConvertToOrderProductVO 转换为订单房间计划VO
func (s *serviceImpl) ConvertToOrderProductVO(ctx context.Context, orderProduct po.OrderProduct) vo.OrderProductVO {
	return s.repo.ConvertToOrderProductVO(ctx, orderProduct)
}

// ConvertToOrderProduct 转换为订单房间计划PO
func (s *serviceImpl) ConvertToOrderProduct(ctx context.Context, orderProductVO vo.OrderProductVO) po.OrderProduct {
	return s.repo.ConvertToOrderProduct(ctx, orderProductVO)
}

// TrimIdToAddOrderProduct 转换为订单房间计划PO
func (s *serviceImpl) TrimIdToAddOrderProduct(ctx context.Context, orderProduct po.OrderProduct) po.OrderProduct {
	newOrderProduct := util.DeepClone(orderProduct)
	newOrderProduct.Id = nil
	newOrderProduct.Ctime = nil
	newOrderProduct.Utime = nil
	newOrderProduct.State = nil
	newOrderProduct.Version = nil
	return newOrderProduct
}

// FindAllBySessionId 根据sessionId查询订单商品
func (s *serviceImpl) FindAllBySessionId(ctx context.Context, sessionId string, venueId string) (*[]po.OrderProduct, error) {
	return s.repo.FindAllBySessionId(ctx, sessionId, venueId)
}

// FindAllByIds 根据ids查询订单商品
func (s *serviceImpl) FindAllByIds(ctx context.Context, sessionId string, venueId string, ids []string) ([]po.OrderProduct, error) {
	return s.repo.FindAllByIds(ctx, sessionId, venueId, ids)
}

// FindsByOrderNos 查询所有订单商品
func (s *serviceImpl) FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderProduct, error) {
	return s.repo.FindsByOrderNos(ctx, venueId, orderNos)
}

// FindOrderProductsTimeRange 查询订单商品时间范围
func (s *serviceImpl) FindOrderProductsTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.OrderProduct, error) {
	return s.repo.FindOrderProductsTimeRange(ctx, venueId, startTime, endTime)
}

// ReBuildOrderProductVOs 重构订单商品VO
func (s *serviceImpl) ReBuildOrderProductVOs(ctx context.Context, orderProducts []po.OrderProduct, orderVOs []po.Order) []vo.OrderProductVO {
	orderNoMap := map[string]po.Order{}
	for _, order := range orderVOs {
		orderNoMap[*order.OrderNo] = order
	}
	// 如果订单商品VO的订单类型为套餐，则需要查询套餐内的商品
	newOrderProductVOs := []vo.OrderProductVO{}
	for _, orderProduct := range orderProducts {
		orderProductVO := s.ConvertToOrderProductVO(ctx, orderProduct)
		order, ok := orderNoMap[orderProductVO.OrderNo]
		if ok {
			orderProductVO.OrderVO = s.orderRepo.ConvertToOrderVO(ctx, order)
		}
		if orderProductVO.ProductId == "" && orderProductVO.PackageId != "" && orderProductVO.PackageProductInfo != "" {
			// 套餐
			unpackageOrderProductVOs := s.UnPackageOrderProductVOs(ctx, orderProductVO, order)
			newOrderProductVOs = append(newOrderProductVOs, unpackageOrderProductVOs...)
		} else {
			newOrderProductVOs = append(newOrderProductVOs, orderProductVO)
		}
	}

	return newOrderProductVOs
}

// UnPackageOrderProductVOs 解包订单商品VO
func (s *serviceImpl) UnPackageOrderProductVOs(ctx context.Context, orderProductVO vo.OrderProductVO, order po.Order) []vo.OrderProductVO {
	newOrderProductVOs := []vo.OrderProductVO{}
	// packageProductInfo: [{"id":"ce907bd8579d4895bf2434f85bf39ab8","count":2},{"id":"bc9fe858b2af4d609863c3fb71e31429","count":1}]
	productProductInfos := []struct {
		Id            string `json:"id"`
		OriginalPrice int64  `json:"price"`
		Count         int64  `json:"count"`
	}{}
	err := json.Unmarshal([]byte(orderProductVO.PackageProductInfo), &productProductInfos)
	if err != nil {
		logrus.Errorf("UnPackageOrderProductVOs error: %v", err)
		return newOrderProductVOs
	}
	// 套餐数量
	packageCount := orderProductVO.Quantity
	// 套餐总金额-支付金额
	packageTotalFee := orderProductVO.PayAmount
	// 套餐总金额-原价
	originalTotalFee := int64(0)
	for _, productProductInfo := range productProductInfos {
		originalTotalFee += productProductInfo.OriginalPrice * productProductInfo.Count * packageCount
		newOrderProductVO := vo.OrderProductVO{}
		newOrderProductVO.OrderVO = s.orderRepo.ConvertToOrderVO(ctx, order)
		newOrderProductVO.ProductId = productProductInfo.Id
		newOrderProductVO.Quantity = productProductInfo.Count * packageCount
		newOrderProductVO.OriginalPrice = productProductInfo.OriginalPrice
		newOrderProductVOs = append(newOrderProductVOs, newOrderProductVO)
	}
	// 原价为0，则不进行计算
	if originalTotalFee == 0 {
		return []vo.OrderProductVO{}
	}
	per := float64(packageTotalFee) / float64(originalTotalFee)
	tmpLeiji := int64(0)
	for idx, newOrderProductVO := range newOrderProductVOs {
		newPayAmount := int64(float64(newOrderProductVO.OriginalPrice*newOrderProductVO.Quantity) * per)
		newOrderProductVO.PayAmount = newPayAmount
		if idx == len(newOrderProductVOs)-1 { // 有精度损失，放在最后一个
			newOrderProductVO.PayAmount = packageTotalFee - tmpLeiji
		} else {
			tmpLeiji += newPayAmount
		}
		newOrderProductVOs[idx] = newOrderProductVO
	}
	return newOrderProductVOs
}

// FindProductsByIds 查询商品信息
func (s *serviceImpl) FindProductsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error) {
	return s.repo.FindProductsByIds(ctx, venueId, productIds)
}

// FindCategorysByIds 查询分类信息
func (s *serviceImpl) FindCategorysByIds(ctx context.Context, venueId string, categoryIds []string) ([]po.ProductType, error) {
	return s.repo.FindCategorysByIds(ctx, venueId, categoryIds)
}

// FindOrderProductsGiftBySessionIds 查询订单商品时间范围
func (s *serviceImpl) FindOrderProductsGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderProduct, error) {
	return s.repo.FindOrderProductsGiftBySessionIds(ctx, venueId, sessionIds)
}
