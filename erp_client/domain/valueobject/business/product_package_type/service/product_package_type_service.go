package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductPackageTypeService 产品套餐类型服务接口
type ProductPackageTypeService interface {
	baseService.ValueObjectService[po.ProductPackageType]

	// FindByID 根据ID查询产品套餐类型
	FindByID(ctx context.Context, id string) (*po.ProductPackageType, error)

	// FindAll 查询所有产品套餐类型
	FindAll(ctx context.Context, reqDto *req.QueryProductPackageTypeReqDto) (*[]po.ProductPackageType, error)

	// FindAllWithPagination 分页查询所有产品套餐类型
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageTypeReqDto) (*[]po.ProductPackageType, int64, error)

	// FindProductPackageTypesByIds 根据ID列表查询产品套餐类型
	FindProductPackageTypesByIds(ctx context.Context, venueId string, productPackageTypeIds []string) ([]po.ProductPackageType, error)

	// ConvertToProductPackageTypeVO 转换为产品套餐类型VO
	ConvertToProductPackageTypeVO(ctx context.Context, productPackageType po.ProductPackageType) vo.ProductPackageTypeVO

	// ConvertToProductPackageType 转换为产品套餐类型PO
	ConvertToProductPackageType(ctx context.Context, productPackageTypeVO vo.ProductPackageTypeVO) po.ProductPackageType

	// FindsByIds 根据ID列表查询产品套餐类型
	FindsByIds(ctx context.Context, venueId string, productPackageTypeIds []string) ([]po.ProductPackageType, error)
}
