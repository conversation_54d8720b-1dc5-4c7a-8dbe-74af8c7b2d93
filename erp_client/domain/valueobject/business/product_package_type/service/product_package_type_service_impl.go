package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/product_package_type/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductPackageTypeServiceImpl 产品套餐类型服务实现
type ProductPackageTypeServiceImpl struct {
	repo repository.Repository
}

// NewService 创建产品套餐类型服务
func NewService(repo repository.Repository) ProductPackageTypeService {
	return &ProductPackageTypeServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *ProductPackageTypeServiceImpl) Create(ctx context.Context, productPackageType po.ProductPackageType) error {
	return nil
}

// Update 更新值对象
func (s *ProductPackageTypeServiceImpl) Update(ctx context.Context, productPackageType po.ProductPackageType) error {
	return nil
}

// Delete 删除值对象
func (s *ProductPackageTypeServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *ProductPackageTypeServiceImpl) Validate(ctx context.Context, productPackageType po.ProductPackageType) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *ProductPackageTypeServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.ProductPackageType, error) {
	return nil, nil
}

// FindByID 根据ID查询产品套餐类型
func (s *ProductPackageTypeServiceImpl) FindByID(ctx context.Context, id string) (*po.ProductPackageType, error) {
	return s.repo.FindByID(ctx, id)
}

// FindAll 查询所有产品套餐类型
func (s *ProductPackageTypeServiceImpl) FindAll(ctx context.Context, reqDto *req.QueryProductPackageTypeReqDto) (*[]po.ProductPackageType, error) {
	return s.repo.FindAll(ctx, reqDto)
}

// FindAllWithPagination 分页查询所有产品套餐类型
func (s *ProductPackageTypeServiceImpl) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductPackageTypeReqDto) (*[]po.ProductPackageType, int64, error) {
	return s.repo.FindAllWithPagination(ctx, reqDto)
}

// FindProductPackageTypesByIds 根据ID列表查询产品套餐类型
func (s *ProductPackageTypeServiceImpl) FindProductPackageTypesByIds(ctx context.Context, venueId string, productPackageTypeIds []string) ([]po.ProductPackageType, error) {
	return s.repo.FindProductPackageTypesByIds(ctx, venueId, productPackageTypeIds)
}

// ConvertToProductPackageTypeVO 转换为产品套餐类型VO
func (s *ProductPackageTypeServiceImpl) ConvertToProductPackageTypeVO(ctx context.Context, productPackageType po.ProductPackageType) vo.ProductPackageTypeVO {
	return s.repo.ConvertToProductPackageTypeVO(ctx, productPackageType)
}

// ConvertToProductPackageType 转换为产品套餐类型PO
func (s *ProductPackageTypeServiceImpl) ConvertToProductPackageType(ctx context.Context, productPackageTypeVO vo.ProductPackageTypeVO) po.ProductPackageType {
	return s.repo.ConvertToProductPackageType(ctx, productPackageTypeVO)
}

// FindsByIds 根据ID列表查询产品套餐类型
func (s *ProductPackageTypeServiceImpl) FindsByIds(ctx context.Context, venueId string, productPackageTypeIds []string) ([]po.ProductPackageType, error) {
	return s.repo.FindsByIds(ctx, venueId, productPackageTypeIds)
}
