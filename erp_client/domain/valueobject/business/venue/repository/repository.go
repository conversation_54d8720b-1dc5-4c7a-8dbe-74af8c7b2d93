package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository Venue仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.Venue]

	// FindByVenueID 根据场所ID查询场所列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.Venue, error)

	// ConvertToVenueVO 转换为场所VO
	ConvertToVenueVO(ctx context.Context, venue po.Venue) vo.VenueVO

	// ConvertToVenuePO 转换为场所PO
	ConvertToVenue(ctx context.Context, venueVO vo.VenueVO) po.Venue
}
