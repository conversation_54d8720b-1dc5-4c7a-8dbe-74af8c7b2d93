package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// VenueService 场所服务接口
type VenueService interface {
	baseService.ValueObjectService[po.Venue]

	// FindByID 根据场所ID查询场所
	FindByID(ctx context.Context, venueID string) (*po.Venue, error)

	// FindByVenueID 根据场所ID查询场所列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.Venue, error)

	// ConvertToVenueVO 转换为场所场所
	ConvertToVenueVO(ctx context.Context, venue po.Venue) vo.VenueVO

	// ConvertToVenuePO 转换为场所PO
	ConvertToVenue(ctx context.Context, venueVO vo.VenueVO) po.Venue
}
