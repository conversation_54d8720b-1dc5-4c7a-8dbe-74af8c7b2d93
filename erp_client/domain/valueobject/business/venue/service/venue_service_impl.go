package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/venue/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// VenueServiceImpl 场所服务实现
type VenueServiceImpl struct {
	repo repository.Repository
}

// NewService 创建场所服务
func NewService(repo repository.Repository) VenueService {
	return &VenueServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *VenueServiceImpl) Create(ctx context.Context, venue po.Venue) error {
	return nil
}

// Update 更新值对象
func (s *VenueServiceImpl) Update(ctx context.Context, venue po.Venue) error {
	return nil
}

// Delete 删除值对象
func (s *VenueServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *VenueServiceImpl) Validate(ctx context.Context, venue po.Venue) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *VenueServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.Venue, error) {
	return nil, nil
}

// FindByID 根据场所ID查询场所
func (s *VenueServiceImpl) FindByID(ctx context.Context, venueID string) (*po.Venue, error) {
	return s.repo.FindByID(ctx, venueID)
}

// FindByVenueID 根据场所ID查询场所列表
func (s *VenueServiceImpl) FindByVenueID(ctx context.Context, venueID string) ([]po.Venue, error) {
	return s.repo.FindByVenueID(ctx, venueID)
}

// ConvertToVenueVO 转换为场所VO
func (s *VenueServiceImpl) ConvertToVenueVO(ctx context.Context, venue po.Venue) vo.VenueVO {
	return s.repo.ConvertToVenueVO(ctx, venue)
}

// ConvertToVenuePO 转换为场所PO
func (s *VenueServiceImpl) ConvertToVenue(ctx context.Context, venueVO vo.VenueVO) po.Venue {
	return s.repo.ConvertToVenue(ctx, venueVO)
}
