package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 房间类型仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.OrderRoomPlan]

	// GetOrderRoomPlan 获取订单房间计划
	GetOrderRoomPlan(ctx context.Context, id string) (po.OrderRoomPlan, error)

	// ConvertToOrderRoomPlanVO 转换为订单房间计划VO
	ConvertToOrderRoomPlanVO(ctx context.Context, orderRoomPlan po.OrderRoomPlan) vo.OrderRoomPlanVO

	// ConvertToOrderRoomPlan 转换为订单房间计划PO
	ConvertToOrderRoomPlan(ctx context.Context, orderRoomPlanVO vo.OrderRoomPlanVO) po.OrderRoomPlan

	GetOrderRoomPlansBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error) 

	GetOrderRoomPlanById(ctx context.Context, venueId string, orderRoomPlanId string) (po.OrderRoomPlan, error)

	// FindsByOrderNos 根据orderNos查询订单房间计划
	FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderRoomPlan, error)

	FindOrderRoomPlansGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error)
}
