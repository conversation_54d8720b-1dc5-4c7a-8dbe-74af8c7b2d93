package service

import (
	"context"
	"sort"
	"voderpltvv/erp_client/domain/valueobject/business/order_roomplan/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"
)

// serviceImpl 订单房间计划值对象服务实现
type serviceImpl struct {
	repo repository.Repository
}

// NewService 创建订单房间计划值对象服务
func NewService(repo repository.Repository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *serviceImpl) Create(ctx context.Context, po po.OrderRoomPlan) error {
	return s.repo.Create(ctx, &po)
}

// Update 更新值对象
func (s *serviceImpl) Update(ctx context.Context, po po.OrderRoomPlan) error {
	return s.repo.Update(ctx, &po)
}

// Delete 删除值对象
func (s *serviceImpl) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// Validate 验证值对象
func (s *serviceImpl) Validate(ctx context.Context, po po.OrderRoomPlan) error {
	return nil
}

// FindByID 根据ID查询值对象
func (s *serviceImpl) FindByID(ctx context.Context, id string) (*po.OrderRoomPlan, error) {
	return s.repo.FindByID(ctx, id)
}

// FindByCondition 根据条件查询值对象
func (s *serviceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.OrderRoomPlan, error) {
	pos, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return *pos, nil
}

// GetOrderRoomPlan 获取订单房间计划
func (s *serviceImpl) GetOrderRoomPlan(ctx context.Context, id string) (po.OrderRoomPlan, error) {
	return s.repo.GetOrderRoomPlan(ctx, id)
}

// ConvertToOrderRoomPlanVO 转换为订单房间计划VO
func (s *serviceImpl) ConvertToOrderRoomPlanVO(ctx context.Context, orderRoomPlan po.OrderRoomPlan) vo.OrderRoomPlanVO {
	return s.repo.ConvertToOrderRoomPlanVO(ctx, orderRoomPlan)
}

// ConvertToOrderRoomPlan 转换为订单房间计划PO
func (s *serviceImpl) ConvertToOrderRoomPlan(ctx context.Context, orderRoomPlanVO vo.OrderRoomPlanVO) po.OrderRoomPlan {
	return s.repo.ConvertToOrderRoomPlan(ctx, orderRoomPlanVO)
}

// TrimIdToAddOrderRoomPlan 转换为订单房间计划PO
func (s *serviceImpl) TrimIdToAddOrderRoomPlan(ctx context.Context, orderRoomPlan po.OrderRoomPlan) po.OrderRoomPlan {
	newOrderRoomPlan := util.DeepClone(orderRoomPlan)
	newOrderRoomPlan.Id = nil
	newOrderRoomPlan.Ctime = nil
	newOrderRoomPlan.Utime = nil
	newOrderRoomPlan.State = nil
	newOrderRoomPlan.Version = nil
	return newOrderRoomPlan
}

func (s *serviceImpl) GetOrderRoomPlanStartEndTime(ctx context.Context, orderRoomPlans []po.OrderRoomPlan) (start, end, duration int64) {
	if len(orderRoomPlans) == 0 {
		return 0, 0, 0
	}

	// 按StartTime从大到小排序
	sort.Slice(orderRoomPlans, func(i, j int) bool {
		if orderRoomPlans[i].StartTime == nil {
			orderRoomPlans[i].StartTime = new(int64)
		}
		if orderRoomPlans[j].StartTime == nil {
			orderRoomPlans[j].StartTime = new(int64)
		}
		return *orderRoomPlans[i].StartTime > *orderRoomPlans[j].StartTime
	})

	// 初始化start和end为第一个元素的时间
	start = *orderRoomPlans[0].StartTime
	endPtr := orderRoomPlans[len(orderRoomPlans)-1].EndTime
	if endPtr == nil {
		end = 0
	} else {
		end = *endPtr
	}
	duration = end - start
	if end == 0 {
		duration = 0
	}
	return
}

func (s *serviceImpl) GetOrderRoomPlansBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error) {
	return s.repo.GetOrderRoomPlansBySessionIds(ctx, venueId, sessionIds)
}

func (s *serviceImpl) GetOrderRoomPlanById(ctx context.Context, venueId string, orderRoomPlanId string) (po.OrderRoomPlan, error) {
	return s.repo.GetOrderRoomPlanById(ctx, venueId, orderRoomPlanId)
}

func (s *serviceImpl) TrimIdToAddOrderRoomPlanPO(ctx context.Context, orderRoomPlan po.OrderRoomPlan) po.OrderRoomPlan {
	newOrderRoomPlan := util.DeepClone(orderRoomPlan)
	newOrderRoomPlan.Id = nil
	newOrderRoomPlan.Ctime = nil
	newOrderRoomPlan.Utime = nil
	newOrderRoomPlan.State = nil
	newOrderRoomPlan.Version = nil
	return newOrderRoomPlan
}

// FindsByOrderNos 根据orderNos查询订单房间计划
func (s *serviceImpl) FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderRoomPlan, error) {
	return s.repo.FindsByOrderNos(ctx, venueId, orderNos)
}

func (s *serviceImpl) FindOrderRoomPlansGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error) {
	return s.repo.FindOrderRoomPlansGiftBySessionIds(ctx, venueId, sessionIds)
}
