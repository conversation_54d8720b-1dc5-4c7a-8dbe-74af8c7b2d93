package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Service 房间类型服务接口
type Service interface {
	baseService.ValueObjectService[po.OrderRoomPlan]

	
	// GetOrderRoomPlan 获取订单房间计划信息
	GetOrderRoomPlan(ctx context.Context, id string) (po.OrderRoomPlan, error)
	
	// FindByID 根据ID查询订单房间计划
	FindByID(ctx context.Context, id string) (*po.OrderRoomPlan, error)

	// ConvertToOrderRoomPlanVO 转换为订单房间计划VO
	ConvertToOrderRoomPlanVO(ctx context.Context, orderRoomPlan po.OrderRoomPlan) vo.OrderRoomPlanVO

	// ConvertToOrderRoomPlan 转换为订单房间计划PO
	ConvertToOrderRoomPlan(ctx context.Context, orderRoomPlanVO vo.OrderRoomPlanVO) po.OrderRoomPlan

	// TrimIdToAddOrderRoomPlan 转换为订单房间计划PO
	TrimIdToAddOrderRoomPlan(ctx context.Context, orderRoomPlan po.OrderRoomPlan) po.OrderRoomPlan

	GetOrderRoomPlanStartEndTime(ctx context.Context, orderRoomPlanVOs []po.OrderRoomPlan) (start, end, duration int64)

	GetOrderRoomPlansBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error)

	GetOrderRoomPlanById(ctx context.Context, venueId string, orderRoomPlanId string) (po.OrderRoomPlan, error)

	TrimIdToAddOrderRoomPlanPO(ctx context.Context, orderRoomPlan po.OrderRoomPlan) po.OrderRoomPlan

	// FindsByOrderNos 根据orderNos查询订单房间计划
	FindsByOrderNos(ctx context.Context, venueId string, orderNos []string) ([]po.OrderRoomPlan, error)

	FindOrderRoomPlansGiftBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.OrderRoomPlan, error)
}
