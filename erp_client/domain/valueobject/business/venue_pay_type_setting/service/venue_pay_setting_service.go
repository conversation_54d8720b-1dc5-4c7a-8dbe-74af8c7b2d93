package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// VenuePayTypeSettingService 门店支付设置服务接口
type VenuePayTypeSettingService interface {
	baseService.ValueObjectService[po.VenuePayTypeSetting]

	// FindByID 根据ID查询门店支付设置
	FindByID(ctx context.Context, id string) (*po.VenuePayTypeSetting, error)

	// FindAll 查询所有门店支付设置
	FindAll(ctx context.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (*[]po.VenuePayTypeSetting, error)

	// FindAllWithPagination 分页查询所有门店支付设置
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (*[]po.VenuePayTypeSetting, int64, error)

	// FindVenuePayTypeSettingsByIds 根据ID列表查询门店支付设置
	FindVenuePayTypeSettingsByIds(ctx context.Context, venueId string, venuePayTypeSettingIds []string) ([]po.VenuePayTypeSetting, error)

	// ConvertToVenuePayTypeSettingVO 转换为门店支付设置VO
	ConvertToVenuePayTypeSettingVO(ctx context.Context, venuePayTypeSetting po.VenuePayTypeSetting) vo.VenuePayTypeSettingVO

	// ConvertToVenuePayTypeSetting 转换为门店支付设置PO
	ConvertToVenuePayTypeSetting(ctx context.Context, venuePayTypeSettingVO vo.VenuePayTypeSettingVO) po.VenuePayTypeSetting

	// FindsByIds 根据ID列表查询门店支付设置
	FindsByIds(ctx context.Context, venueId string, venuePayTypeSettingIds []string) ([]po.VenuePayTypeSetting, error)

	// FindVenuePayTypeSettingByVenueId 根据门店ID查询支付设置
	FindVenuePayTypeSettingByVenueId(ctx context.Context, venueId string) (*po.VenuePayTypeSetting, error)

}
