package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/venue_pay_type_setting/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// VenuePaySettingServiceImpl 门店支付设置服务实现
type VenuePayTypeSettingServiceImpl struct {
	repo repository.Repository
}

// NewService 创建门店支付设置服务
func NewService(repo repository.Repository) VenuePayTypeSettingService {
	return &VenuePayTypeSettingServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *VenuePayTypeSettingServiceImpl) Create(ctx context.Context, venuePaySetting po.VenuePayTypeSetting) error {
	return nil
}

// Update 更新值对象
func (s *VenuePayTypeSettingServiceImpl) Update(ctx context.Context, venuePaySetting po.VenuePayTypeSetting) error {
	return nil
}

// Delete 删除值对象
func (s *VenuePayTypeSettingServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *VenuePayTypeSettingServiceImpl) Validate(ctx context.Context, venuePayTypeSetting po.VenuePayTypeSetting) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *VenuePayTypeSettingServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.VenuePayTypeSetting, error) {
	return nil, nil
}

// FindByID 根据ID查询门店支付设置
func (s *VenuePayTypeSettingServiceImpl) FindByID(ctx context.Context, id string) (*po.VenuePayTypeSetting, error) {
	return s.repo.FindByID(ctx, id)
}

// FindAll 查询所有门店支付设置
func (s *VenuePayTypeSettingServiceImpl) FindAll(ctx context.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (*[]po.VenuePayTypeSetting, error) {
	return s.repo.FindAll(ctx, reqDto)
}

// FindAllWithPagination 分页查询所有门店支付设置
func (s *VenuePayTypeSettingServiceImpl) FindAllWithPagination(ctx context.Context, reqDto *req.QueryVenuePayTypeSettingReqDto) (*[]po.VenuePayTypeSetting, int64, error) {
	return s.repo.FindAllWithPagination(ctx, reqDto)
}

// FindVenuePayTypeSettingsByIds 根据ID列表查询门店支付设置
func (s *VenuePayTypeSettingServiceImpl) FindVenuePayTypeSettingsByIds(ctx context.Context, venueId string, venuePayTypeSettingIds []string) ([]po.VenuePayTypeSetting, error) {
	return s.repo.FindVenuePayTypeSettingsByIds(ctx, venueId, venuePayTypeSettingIds)
}

// ConvertToVenuePayTypeSettingVO 转换为门店支付设置VO
func (s *VenuePayTypeSettingServiceImpl) ConvertToVenuePayTypeSettingVO(ctx context.Context, venuePayTypeSetting po.VenuePayTypeSetting) vo.VenuePayTypeSettingVO {
	return s.repo.ConvertToVenuePayTypeSettingVO(ctx, venuePayTypeSetting)
}

// ConvertToVenuePayTypeSetting 转换为门店支付设置PO
func (s *VenuePayTypeSettingServiceImpl) ConvertToVenuePayTypeSetting(ctx context.Context, venuePayTypeSettingVO vo.VenuePayTypeSettingVO) po.VenuePayTypeSetting {
	return s.repo.ConvertToVenuePayTypeSetting(ctx, venuePayTypeSettingVO)
}

// FindsByIds 根据ID列表查询门店支付设置
func (s *VenuePayTypeSettingServiceImpl) FindsByIds(ctx context.Context, venueId string, venuePayTypeSettingIds []string) ([]po.VenuePayTypeSetting, error) {
	return s.repo.FindsByIds(ctx, venueId, venuePayTypeSettingIds)
}

// FindVenuePayTypeSettingByVenueId 根据门店ID查询支付设置
func (s *VenuePayTypeSettingServiceImpl) FindVenuePayTypeSettingByVenueId(ctx context.Context, venueId string) (*po.VenuePayTypeSetting, error) {
	return s.repo.FindVenuePayTypeSettingByVenueId(ctx, venueId)
}