package service

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/room_type/model"
	"voderpltvv/erp_client/domain/valueobject/business/room_type/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// serviceImpl 房间类型值对象服务实现
type serviceImpl struct {
	repo repository.Repository
}

// NewService 创建房间类型值对象服务
func NewService(repo repository.Repository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *serviceImpl) Create(ctx context.Context, roomType po.RoomType) error {
	vo := model.NewRoomTypeFromPO(&roomType)
	return s.repo.Create(ctx, vo.GetPO())
}

// Update 更新值对象
func (s *serviceImpl) Update(ctx context.Context, roomType po.RoomType) error {
	vo := model.NewRoomTypeFromPO(&roomType)
	return s.repo.Update(ctx, vo.GetPO())
}

// Delete 删除值对象
func (s *serviceImpl) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// Validate 验证值对象
func (s *serviceImpl) Validate(ctx context.Context, roomType po.RoomType) error {
	vo := model.NewRoomTypeFromPO(&roomType)
	return vo.Validate()
}

// FindByCondition 根据条件查询值对象
func (s *serviceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.RoomType, error) {
	pos, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	if pos == nil {
		return []po.RoomType{}, nil
	}
	result := make([]po.RoomType, len(*pos))
	for i, p := range *pos {
		result[i] = p
	}
	return result, nil
}

// GetRoomType 获取房间类型信息
func (s *serviceImpl) GetRoomType(ctx context.Context, id string) (po.RoomType, error) {
	if id == "" {
		return po.RoomType{}, nil
	}
	roomType, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return po.RoomType{}, err
	}
	if roomType == nil {
		return po.RoomType{}, nil
	}
	return *roomType, nil
}

// FindByVenueID 根据场所ID查询房间类型列表
func (s *serviceImpl) FindByVenueID(ctx context.Context, venueID string) ([]po.RoomType, error) {
	if venueID == "" {
		return []po.RoomType{}, nil
	}
	pos, err := s.repo.FindByVenueID(ctx, venueID)
	if err != nil {
		return nil, err
	}
	result := make([]po.RoomType, len(pos))
	for i, p := range pos {
		result[i] = p
	}
	return result, nil
}

func (s *serviceImpl) FindByID(ctx context.Context, id string) (*po.RoomType, error) {
	return s.repo.FindByID(ctx, id)
}

// ConvertToRoomTypeVO 转换为房间类型VO
func (s *serviceImpl) ConvertToRoomTypeVO(ctx context.Context, roomType *po.RoomType) vo.RoomTypeVO {
	return s.repo.ConvertToRoomTypeVO(ctx, roomType)
}

// ConvertToRoomTypePO 转换为房间类型PO
func (s *serviceImpl) ConvertToRoomTypePO(ctx context.Context, roomTypeVO *vo.RoomTypeVO) po.RoomType {
	return s.repo.ConvertToRoomTypePO(ctx, roomTypeVO)
}

// FindRoomTypesByRoomTypeIds 根据房间类型ID查询房间类型列表
func (s *serviceImpl) FindRoomTypesByRoomTypeIds(ctx context.Context, venueId string, roomTypeIds []string) ([]po.RoomType, error) {
	return s.repo.FindRoomTypesByRoomTypeIds(ctx, venueId, roomTypeIds)
}
