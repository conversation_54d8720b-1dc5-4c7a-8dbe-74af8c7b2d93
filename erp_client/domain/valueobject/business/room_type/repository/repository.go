package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 房间类型仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.RoomType]

	// FindByVenueID 根据场所ID查询房间类型列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.RoomType, error)

	// FindByID 根据ID查询房间类型
	FindByID(ctx context.Context, id string) (*po.RoomType, error)

	// ConvertToRoomTypeVO 转换为房间类型VO
	ConvertToRoomTypeVO(ctx context.Context, roomType *po.RoomType) vo.RoomTypeVO

	// ConvertToRoomTypePO 转换为房间类型PO
	ConvertToRoomTypePO(ctx context.Context, roomTypeVO *vo.RoomTypeVO) po.RoomType

	// FindRoomTypesByRoomTypeIds 根据房间类型ID查询房间类型列表
	FindRoomTypesByRoomTypeIds(ctx context.Context, venueId string, roomTypeIds []string) ([]po.RoomType, error)
}
