package model

import (
	"context"
	"errors"
	"voderpltvv/erp_client/domain/valueobject/base/model"
	"voderpltvv/erp_managent/service/po"
)

// RoomType 房间类型值对象接口
type RoomType interface {
	model.ValueObject

	// GetVenueID 获取场所ID
	GetVenueID() string
	// GetName 获取名称
	GetName() string
	// GetConsumptionMode 获取消费模式
	GetConsumptionMode() string
	// GetHighConsumptionAlert 获取高消费警报金额
	GetHighConsumptionAlert() int64
	// GetPO 获取PO对象
	GetPO() *po.RoomType
}

// NewRoomTypeFromPO 从PO创建房间类型值对象
func NewRoomTypeFromPO(po *po.RoomType) RoomType {
	return &roomTypeImpl{
		po: po,
	}
}

// roomTypeImpl 房间类型值对象实现
type roomTypeImpl struct {
	po *po.RoomType
}

// GetID 获取ID
func (r *roomTypeImpl) GetID() string {
	return *r.po.Id
}

// GetVenueID 获取场所ID
func (r *roomTypeImpl) GetVenueID() string {
	return *r.po.VenueId
}

// GetName 获取名称
func (r *roomTypeImpl) GetName() string {
	return *r.po.Name
}

// GetConsumptionMode 获取消费模式
func (r *roomTypeImpl) GetConsumptionMode() string {
	return *r.po.ConsumptionMode
}

// GetHighConsumptionAlert 获取高消费警报金额
func (r *roomTypeImpl) GetHighConsumptionAlert() int64 {
	return *r.po.HighConsumptionAlert
}

// GetPO 获取PO对象
func (r *roomTypeImpl) GetPO() *po.RoomType {
	return r.po
}

// Validate 验证值对象
func (r *roomTypeImpl) Validate() error {
	if r.po.Id == nil {
		return errors.New("id is required")
	}
	if r.po.VenueId == nil {
		return errors.New("venueId is required")
	}
	if r.po.Name == nil {
		return errors.New("name is required")
	}
	if r.po.ConsumptionMode == nil {
		return errors.New("consumptionMode is required")
	}
	return nil
}

// Equals 值对象比较
func (r *roomTypeImpl) Equals(other model.ValueObject) bool {
	if other == nil {
		return false
	}
	if otherRoomType, ok := other.(RoomType); ok {
		return r.GetID() == otherRoomType.GetID() &&
			r.GetVenueID() == otherRoomType.GetVenueID() &&
			r.GetName() == otherRoomType.GetName() &&
			r.GetConsumptionMode() == otherRoomType.GetConsumptionMode() &&
			r.GetHighConsumptionAlert() == otherRoomType.GetHighConsumptionAlert()
	}
	return false
}

// BeforeCreate 创建前钩子
func (r *roomTypeImpl) BeforeCreate(ctx context.Context) error {
	return nil
}

// BeforeUpdate 更新前钩子
func (r *roomTypeImpl) BeforeUpdate(ctx context.Context) error {
	return nil
}

// GetCtime 获取创建时间
func (r *roomTypeImpl) GetCtime() int64 {
	return *r.po.Ctime
}

// GetUtime 获取更新时间
func (r *roomTypeImpl) GetUtime() int64 {
	return *r.po.Utime
}

// GetState 获取状态
func (r *roomTypeImpl) GetState() int {
	return *r.po.State
}

// GetVersion 获取版本
func (r *roomTypeImpl) GetVersion() int {
	return *r.po.Version
}

// Clone 克隆值对象
func (r *roomTypeImpl) Clone() model.ValueObject {
	return NewRoomTypeFromPO(r.po)
}
