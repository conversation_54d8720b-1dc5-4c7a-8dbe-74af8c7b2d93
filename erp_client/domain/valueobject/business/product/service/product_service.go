package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductService 产品服务接口
type ProductService interface {
	baseService.ValueObjectService[po.Product]

	// FindByID 根据ID查询产品
	FindByID(ctx context.Context, id string) (*po.Product, error)

	// FindAll 查询所有产品
	FindAll(ctx context.Context, reqDto *req.QueryProductReqDto) (*[]po.Product, error)

	// FindAllWithPagination 分页查询所有产品
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductReqDto) (*[]po.Product, int64, error)

	// FindProductsByIds 根据ID列表查询产品
	FindProductsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error)

	// ConvertToProductVO 转换为产品VO
	ConvertToProductVO(ctx context.Context, product po.Product) vo.ProductVO

	// ConvertToProduct 转换为产品PO
	ConvertToProduct(ctx context.Context, productVO vo.ProductVO) po.Product

	// FindsByIds 根据ID列表查询产品
	FindsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error)
}
