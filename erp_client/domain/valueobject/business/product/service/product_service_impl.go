package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/product/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductServiceImpl 产品服务实现
type ProductServiceImpl struct {
	repo repository.Repository
}

// NewService 创建产品服务
func NewService(repo repository.Repository) ProductService {
	return &ProductServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *ProductServiceImpl) Create(ctx context.Context, product po.Product) error {
	return nil
}

// Update 更新值对象
func (s *ProductServiceImpl) Update(ctx context.Context, product po.Product) error {
	return nil
}

// Delete 删除值对象
func (s *ProductServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *ProductServiceImpl) Validate(ctx context.Context, product po.Product) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *ProductServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.Product, error) {
	return nil, nil
}

// FindByID 根据ID查询产品
func (s *ProductServiceImpl) FindByID(ctx context.Context, id string) (*po.Product, error) {
	return s.repo.FindByID(ctx, id)
}

// FindAll 查询所有产品
func (s *ProductServiceImpl) FindAll(ctx context.Context, reqDto *req.QueryProductReqDto) (*[]po.Product, error) {
	return s.repo.FindAll(ctx, reqDto)
}

// FindAllWithPagination 分页查询所有产品
func (s *ProductServiceImpl) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductReqDto) (*[]po.Product, int64, error) {
	return s.repo.FindAllWithPagination(ctx, reqDto)
}

// FindProductsByIds 根据ID列表查询产品
func (s *ProductServiceImpl) FindProductsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error) {
	return s.repo.FindProductsByIds(ctx, venueId, productIds)
}

// ConvertToProductVO 转换为产品VO
func (s *ProductServiceImpl) ConvertToProductVO(ctx context.Context, product po.Product) vo.ProductVO {
	return s.repo.ConvertToProductVO(ctx, product)
}

// ConvertToProduct 转换为产品PO
func (s *ProductServiceImpl) ConvertToProduct(ctx context.Context, productVO vo.ProductVO) po.Product {
	return s.repo.ConvertToProduct(ctx, productVO)
}

// FindsByIds 根据ID列表查询产品
func (s *ProductServiceImpl) FindsByIds(ctx context.Context, venueId string, productIds []string) ([]po.Product, error) {
	return s.repo.FindsByIds(ctx, venueId, productIds)
}