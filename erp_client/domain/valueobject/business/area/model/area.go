package model

import (
	"context"
	"errors"
	"voderpltvv/erp_client/domain/valueobject/base/model"
	"voderpltvv/erp_managent/service/po"
)

// Area 区域值对象
type Area interface {
	model.ValueObject

	// GetVenueID 获取门店ID
	GetVenueID() string
	// GetName 获取区域名称
	GetName() string
	// GetCapacity 获取容量
	GetCapacity() int
	// GetDescription 获取描述
	GetDescription() string
	// IsDisplayed 是否显示
	IsDisplayed() bool
}

// NewArea 创建区域值对象
func NewArea(
	po *po.Area,
) Area {
	return &areaImpl{
		po: po,
	}
}

// areaImpl 区域值对象实现
type areaImpl struct {
	po *po.Area
}

// GetID 获取ID
func (a *areaImpl) GetID() string {
	return *a.po.Id
}

// GetVenueID 获取门店ID
func (a *areaImpl) GetVenueID() string {
	return *a.po.VenueId
}

// GetName 获取区域名称
func (a *areaImpl) GetName() string {
	return *a.po.Name
}

// GetCapacity 获取容量
func (a *areaImpl) GetCapacity() int {
	return *a.po.Capacity
}

// GetDescription 获取描述
func (a *areaImpl) GetDescription() string {
	return *a.po.Description
}

// IsDisplayed 是否显示
func (a *areaImpl) IsDisplayed() bool {
	return *a.po.IsDisplayed
}

// Validate 验证值对象
func (a *areaImpl) Validate() error {
	if a.po.Id == nil {
		return errors.New("id is required")
	}
	if a.po.VenueId == nil {
		return errors.New("venueId is required")
	}
	if a.po.Name == nil {
		return errors.New("name is required")
	}
	if a.po.Capacity == nil {
		return errors.New("capacity is required")
	}
	return nil
}

// Equals 值对象比较
func (a *areaImpl) Equals(other model.ValueObject) bool {
	if other == nil {
		return false
	}
	if otherArea, ok := other.(Area); ok {
		return a.GetID() == otherArea.GetID() &&
			a.GetVenueID() == otherArea.GetVenueID() &&
			a.GetName() == otherArea.GetName() &&
			a.GetCapacity() == otherArea.GetCapacity() &&
			a.GetDescription() == otherArea.GetDescription() &&
			a.IsDisplayed() == otherArea.IsDisplayed()
	}
	return false
}

// BeforeCreate 创建前钩子
func (a *areaImpl) BeforeCreate(ctx context.Context) error {
	return nil
}

// BeforeUpdate 更新前钩子
func (a *areaImpl) BeforeUpdate(ctx context.Context) error {
	return nil
}

// GetCtime 获取创建时间
func (a *areaImpl) GetCtime() int64 {
	return *a.po.Ctime
}

// GetUtime 获取更新时间
func (a *areaImpl) GetUtime() int64 {
	return *a.po.Utime
}

// GetState 获取状态
func (a *areaImpl) GetState() int {
	return *a.po.State
}

// GetVersion 获取版本
func (a *areaImpl) GetVersion() int {
	return *a.po.Version
}

// Clone 克隆值对象
func (a *areaImpl) Clone() model.ValueObject {
	return NewArea(
		a.po,
	)
}
