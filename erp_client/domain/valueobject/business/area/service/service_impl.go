package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_client/domain/valueobject/business/area/repository"
	"voderpltvv/erp_managent/service/po"
)

// serviceImpl 区域服务实现
type serviceImpl struct {
	repo repository.Repository
}

// NewService 创建区域服务实例
func NewService(repo repository.Repository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// GetArea 获取区域信息
func (s *serviceImpl) GetArea(ctx context.Context, id string) (po.Area, error) {
	area, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return po.Area{}, err
	}
	return *area, nil
}

// FindByVenueID 根据场馆ID查询区域列表
func (s *serviceImpl) FindByVenueID(ctx context.Context, venueID string) ([]po.Area, error) {
	return s.repo.FindByVenueID(ctx, venueID)
}

func (s *serviceImpl) ConvertToAreaVO(ctx context.Context, area *po.Area) vo.AreaVO {
	return s.repo.ConvertToAreaVO(ctx, area)
}

func (s *serviceImpl) ConvertAreaVOToPO(ctx context.Context, areaVO *vo.AreaVO) po.Area {
	return s.repo.ConvertAreaVOToPO(ctx, areaVO)
}

// FindAreasByAreaIds 根据区域ID查询区域列表
func (s *serviceImpl) FindAreasByAreaIds(ctx context.Context, venueId string, areaIds []string) ([]po.Area, error) {
	return s.repo.FindAreasByAreaIds(ctx, venueId, areaIds)
}
