package service

import (
	"context"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Service 区域服务接口
type Service interface {
	// GetArea 获取区域信息
	GetArea(ctx context.Context, id string) (po.Area, error)

	// FindByVenueID 根据场馆ID查询区域列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.Area, error)

	// ConvertToAreaVO 转换区域VO
	ConvertToAreaVO(ctx context.Context, area *po.Area) vo.AreaVO

	// ConvertAreaVOToPO 转换区域PO
	ConvertAreaVOToPO(ctx context.Context, areaVO *vo.AreaVO) po.Area

	// FindAreasByAreaIds 根据区域ID查询区域列表
	FindAreasByAreaIds(ctx context.Context, venueId string, areaIds []string) ([]po.Area, error)
}
