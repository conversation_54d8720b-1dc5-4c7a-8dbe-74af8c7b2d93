package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 区域仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.Area]

	// FindByVenueID 根据场馆ID查询区域列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.Area, error)
	ConvertToAreaVO(ctx context.Context, area *po.Area) vo.AreaVO
	ConvertAreaVOToPO(ctx context.Context, areaVO *vo.AreaVO) po.Area

	// FindAreasByAreaIds 根据区域ID查询区域列表
	FindAreasByAreaIds(ctx context.Context, venueId string, areaIds []string) ([]po.Area, error)
}
