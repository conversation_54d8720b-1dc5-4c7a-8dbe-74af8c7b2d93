package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 房间故障仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.RoomFault]

	// ConvertToRoomFaultVO 转换为房间故障VO
	ConvertToRoomFaultVO(ctx context.Context, roomFault po.RoomFault) vo.RoomFaultVO

	// ConvertToRoomFault 转换为房间故障PO
	ConvertToRoomFault(ctx context.Context, roomFaultVO vo.RoomFaultVO) po.RoomFault

	// FindRoomFaultsByRoomId 根据房间ID查询房间故障
	FindRoomFaultsByRoomId(ctx context.Context, venueId, roomId string) ([]po.RoomFault, error)
}
