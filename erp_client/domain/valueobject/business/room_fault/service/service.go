package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomFaultService 房间故障服务接口
type RoomFaultService interface {
	baseService.ValueObjectService[po.RoomFault]

	// FindByID 根据ID查询房间故障
	FindByID(ctx context.Context, id string) (*po.RoomFault, error)

	// ConvertToRoomFaultVO 转换为房间故障VO
	ConvertToRoomFaultVO(ctx context.Context, roomFault po.RoomFault) vo.RoomFaultVO

	// ConvertToRoomFault 转换为房间故障PO
	ConvertToRoomFault(ctx context.Context, roomFaultVO vo.RoomFaultVO) po.RoomFault

	FindRoomFaultsByRoomId(ctx context.Context, venueId, roomId string) ([]po.RoomFault, error)
}
