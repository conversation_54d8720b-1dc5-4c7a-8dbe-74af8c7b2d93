package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/room_fault/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomFaultServiceImpl 包厢服务实现
type RoomFaultServiceImpl struct {
	repo repository.Repository
}

// NewService 创建场次服务
func NewService(repo repository.Repository) RoomFaultService {
	return &RoomFaultServiceImpl{
		repo: repo,
	}
}

// FindRoomFaultsByRoomId implements RoomFaultService.
func (s *RoomFaultServiceImpl) FindRoomFaultsByRoomId(ctx context.Context, venueId, roomId string) ([]po.RoomFault, error) {
	return s.repo.FindRoomFaultsByRoomId(ctx, venueId, roomId)
}

// Create 创建值对象
func (s *RoomFaultServiceImpl) Create(ctx context.Context, roomFault po.RoomFault) error {
	return nil
}

// Update 更新值对象
func (s *RoomFaultServiceImpl) Update(ctx context.Context, roomFault po.RoomFault) error {
	return nil
}

// Delete 删除值对象
func (s *RoomFaultServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *RoomFaultServiceImpl) Validate(ctx context.Context, roomFault po.RoomFault) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *RoomFaultServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.RoomFault, error) {
	return nil, nil
}

// FindByVenueID 根据场所ID查询房间类型列表
func (s *RoomFaultServiceImpl) FindByID(ctx context.Context, id string) (*po.RoomFault, error) {
	return s.repo.FindByID(ctx, id)
}

// ConvertToRoomOperationVO 转换为房间类型VO
func (s *RoomFaultServiceImpl) ConvertToRoomFaultVO(ctx context.Context, roomFault po.RoomFault) vo.RoomFaultVO {
	return s.repo.ConvertToRoomFaultVO(ctx, roomFault)
}

// ConvertToRoomOperationPO 转换为房间类型PO
func (s *RoomFaultServiceImpl) ConvertToRoomFault(ctx context.Context, roomFaultVO vo.RoomFaultVO) po.RoomFault {
	return s.repo.ConvertToRoomFault(ctx, roomFaultVO)
}
