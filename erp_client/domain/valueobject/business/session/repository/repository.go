package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository Session仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.Session]

	// FindByVenueID 根据场所ID查询房间类型列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.Session, error)

	// ConvertToSessionVO 转换为房间类型VO
	ConvertToSessionVO(ctx context.Context, session po.Session) vo.SessionVO

	// ConvertToSessionPO 转换为房间类型PO
	ConvertToSession(ctx context.Context, sessionVO vo.SessionVO) po.Session

	// FindBySessionId 根据sessionId查询场次
	FindBySessionId(ctx context.Context, sessionId string, venueId string) (po.Session, error)

	// FindSessionsBySessionIds 根据sessionId查询场次列表
	FindSessionsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Session, error)

	// FindSessionLatestByRoomId 根据roomId查询最新场次
	FindSessionLatestByRoomId(ctx context.Context, venueId string, roomId string) (po.Session, error)

	// FindSessionsByTimeRange 根据时间范围查询场次列表
	FindSessionsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Session, error)
}
