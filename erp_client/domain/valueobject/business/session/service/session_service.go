package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// SessionService 场次服务接口
type SessionService interface {
	baseService.ValueObjectService[po.Session]

	// FindByID 根据ID查询场次
	FindByID(ctx context.Context, id string) (*po.Session, error)

	// FindBySessionId 根据sessionId查询场次
	FindBySessionId(ctx context.Context, sessionId string, venueId string) (po.Session, error)

	// ConvertToSessionVO 转换为房间类型VO
	ConvertToSessionVO(ctx context.Context, session po.Session) vo.SessionVO

	// ConvertToSessionPO 转换为房间类型PO
	ConvertToSession(ctx context.Context, sessionVO vo.SessionVO) po.Session

	// FindSessionsBySessionIds 根据sessionId查询场次列表
	FindSessionsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Session, error)

	// FindSessionLatestByRoomId 根据roomId查询最新场次
	FindSessionLatestByRoomId(ctx context.Context, venueId string, roomId string) (po.Session, error)

	// FindSessionsByTimeRange 根据时间范围查询场次列表
	FindSessionsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Session, error)
}
