package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/session/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// SessionServiceImpl 场次服务实现
type SessionServiceImpl struct {
	repo repository.Repository
}

// NewService 创建场次服务
func NewService(repo repository.Repository) SessionService {
	return &SessionServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *SessionServiceImpl) Create(ctx context.Context, roomType po.Session) error {
	return nil
}

// Update 更新值对象
func (s *SessionServiceImpl) Update(ctx context.Context, roomType po.Session) error {
	return nil
}

// Delete 删除值对象
func (s *SessionServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *SessionServiceImpl) Validate(ctx context.Context, roomType po.Session) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *SessionServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.Session, error) {
	return nil, nil
}

// FindByVenueID 根据场所ID查询房间类型列表
func (s *SessionServiceImpl) FindByID(ctx context.Context, venueID string) (*po.Session, error) {
	return s.repo.FindByID(ctx, venueID)
}

// ConvertToSessionVO 转换为房间类型VO
func (s *SessionServiceImpl) ConvertToSessionVO(ctx context.Context, session po.Session) vo.SessionVO {
	return s.repo.ConvertToSessionVO(ctx, session)
}

// ConvertToSessionPO 转换为房间类型PO
func (s *SessionServiceImpl) ConvertToSession(ctx context.Context, sessionVO vo.SessionVO) po.Session {
	return s.repo.ConvertToSession(ctx, sessionVO)
}

// FindBySessionId 根据sessionId查询场次
func (s *SessionServiceImpl) FindBySessionId(ctx context.Context, sessionId string, venueId string) (po.Session, error) {
	return s.repo.FindBySessionId(ctx, sessionId, venueId)
}

// FindSessionsBySessionIds 根据sessionId查询场次列表
func (s *SessionServiceImpl) FindSessionsBySessionIds(ctx context.Context, venueId string, sessionIds []string) ([]po.Session, error) {
	return s.repo.FindSessionsBySessionIds(ctx, venueId, sessionIds)
}

// FindSessionLatestByRoomId 根据roomId查询最新场次
func (s *SessionServiceImpl) FindSessionLatestByRoomId(ctx context.Context, venueId string, roomId string) (po.Session, error) {
	return s.repo.FindSessionLatestByRoomId(ctx, venueId, roomId)
}

// FindSessionsByTimeRange 根据时间范围查询场次列表
func (s *SessionServiceImpl) FindSessionsByTimeRange(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Session, error) {
	return s.repo.FindSessionsByTimeRange(ctx, venueId, startTime, endTime)
}
