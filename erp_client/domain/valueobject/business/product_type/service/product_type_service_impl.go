package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/product_type/repository"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductTypeServiceImpl 产品类型服务实现
type ProductTypeServiceImpl struct {
	repo repository.Repository
}

// NewService 创建产品类型服务
func NewService(repo repository.Repository) ProductTypeService {
	return &ProductTypeServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *ProductTypeServiceImpl) Create(ctx context.Context, productType po.ProductType) error {
	return nil
}

// Update 更新值对象
func (s *ProductTypeServiceImpl) Update(ctx context.Context, productType po.ProductType) error {
	return nil
}

// Delete 删除值对象
func (s *ProductTypeServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *ProductTypeServiceImpl) Validate(ctx context.Context, productType po.ProductType) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *ProductTypeServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.ProductType, error) {
	return nil, nil
}

// FindByID 根据ID查询产品类型
func (s *ProductTypeServiceImpl) FindByID(ctx context.Context, id string) (*po.ProductType, error) {
	return s.repo.FindByID(ctx, id)
}

// FindAll 查询所有产品类型
func (s *ProductTypeServiceImpl) FindAll(ctx context.Context, reqDto *req.QueryProductTypeReqDto) (*[]po.ProductType, error) {
	return s.repo.FindAll(ctx, reqDto)
}

// FindAllWithPagination 分页查询所有产品类型
func (s *ProductTypeServiceImpl) FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductTypeReqDto) (*[]po.ProductType, int64, error) {
	return s.repo.FindAllWithPagination(ctx, reqDto)
}

// FindProductTypesByIds 根据ID列表查询产品类型
func (s *ProductTypeServiceImpl) FindProductTypesByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error) {
	return s.repo.FindProductTypesByIds(ctx, venueId, productTypeIds)
}

// ConvertToProductTypeVO 转换为产品类型VO
func (s *ProductTypeServiceImpl) ConvertToProductTypeVO(ctx context.Context, productType po.ProductType) vo.ProductTypeVO {
	return s.repo.ConvertToProductTypeVO(ctx, productType)
}

// ConvertToProductType 转换为产品类型PO
func (s *ProductTypeServiceImpl) ConvertToProductType(ctx context.Context, productTypeVO vo.ProductTypeVO) po.ProductType {
	return s.repo.ConvertToProductType(ctx, productTypeVO)
}

// FindsByIds 根据ID列表查询产品类型
func (s *ProductTypeServiceImpl) FindsByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error) {
	return s.repo.FindsByIds(ctx, venueId, productTypeIds)
}
