package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// ProductTypeService 产品类型服务接口
type ProductTypeService interface {
	baseService.ValueObjectService[po.ProductType]

	// FindByID 根据ID查询产品类型
	FindByID(ctx context.Context, id string) (*po.ProductType, error)

	// FindAll 查询所有产品类型
	FindAll(ctx context.Context, reqDto *req.QueryProductTypeReqDto) (*[]po.ProductType, error)

	// FindAllWithPagination 分页查询所有产品类型
	FindAllWithPagination(ctx context.Context, reqDto *req.QueryProductTypeReqDto) (*[]po.ProductType, int64, error)

	// FindProductTypesByIds 根据ID列表查询产品类型
	FindProductTypesByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error)

	// ConvertToProductTypeVO 转换为产品类型VO
	ConvertToProductTypeVO(ctx context.Context, productType po.ProductType) vo.ProductTypeVO

	// ConvertToProductType 转换为产品类型PO
	ConvertToProductType(ctx context.Context, productTypeVO vo.ProductTypeVO) po.ProductType

	// FindsByIds 根据ID列表查询产品类型
	FindsByIds(ctx context.Context, venueId string, productTypeIds []string) ([]po.ProductType, error)
}
