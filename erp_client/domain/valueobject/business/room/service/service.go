package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomService 包厢服务接口
type Service interface {
	baseService.ValueObjectService[po.Room]

	// GetRoom 获取包厢信息
	GetRoom(ctx context.Context, id string) (po.Room, error)
	// FindByVenueID 根据场所ID查询包厢
	FindByVenueID(ctx context.Context, venueID string) ([]po.Room, error)
	// FindByID 根据包厢ID查询包厢
	FindByID(ctx context.Context, venueID string) (*po.Room, error)

	// IsRoomLocked 检查包厢是否锁定
	IsRoomLocked(ctx context.Context, room po.Room) bool
	
	// GetAttachRooms 获取联房的其他房间信息
	GetAttachRooms(ctx context.Context, room po.Room) ([]po.Room, error)

	// ConvertToRoomVO 转换为包厢场所
	ConvertToRoomVO(ctx context.Context, venue po.Room) vo.RoomVO

	// ConvertToRoomPO 转换为包厢PO
	ConvertToRoom(ctx context.Context, roomVO vo.RoomVO) po.Room

	// FindRoomsBySessionId 根据sessionId查询包厢
	FindRoomsBySessionId(ctx context.Context, sessionId string, venueId string) ([]po.Room, error)

	// FindRoomsByVenueId 根据场所ID查询包厢
	FindRoomsByVenueId(ctx context.Context, venueId string, isDeleted bool, isDisplay bool) ([]po.Room, error)
	
	// CloseRoom 关房
	CloseRoom(ctx context.Context, rooms []po.Room, toUpdateSessions []po.Session) (error)

	// CleanRoomFinish 关房
	CleanRoomFinish(ctx context.Context, rooms []po.Room) (error)

	// LockRoom 锁房
	LockRoom(ctx context.Context, rooms []po.Room) (error)

	// UnlockRoom 解锁房
	UnlockRoom(ctx context.Context, rooms []po.Room) (error)

	// FindRoomsByCtime 根据创建时间查询包厢
	FindRoomsByCtime(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Room, error)
}
