package service

import (
	"context"

	"voderpltvv/erp_managent/service/po"
)

// RoomService 房间服务接口
type RoomService interface {
	// 已实现的方法
	FindRoomById(ctx context.Context, id string) (*po.Room, error)

	// 新增的方法
	UpdateRoomStatus(ctx context.Context, room *po.Room) error
	ValidateRoomStatus(ctx context.Context, roomId string, expectedStatus []string) error

	// 定时器相关方法
	SetRoomTimer(ctx context.Context, venueId, roomId, sessionId string, endTime int64) error
	CancelRoomTimer(ctx context.Context, roomId string) error
	UpdateRoomTimer(ctx context.Context, roomId string) error
}
