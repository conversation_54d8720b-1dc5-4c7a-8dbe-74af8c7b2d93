package service

import (
	"context"
	"voderpltvv/erp_client/domain/valueobject/business/room/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// roomServiceImpl 房间领域服务实现
type roomServiceImpl struct {
	repo repository.Repository
}

// NewRoomService 创建房间领域服务
func NewService(repo repository.Repository) Service {
	return &roomServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *roomServiceImpl) Create(ctx context.Context, room po.Room) error {
	return nil
}

// Update 更新值对象
func (s *roomServiceImpl) Update(ctx context.Context, room po.Room) error {
	return nil
}

// Delete 删除值对象
func (s *roomServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *roomServiceImpl) Validate(ctx context.Context, room po.Room) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *roomServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.Room, error) {
	return nil, nil
}

// FindByID 根据场所ID查询场所
func (s *roomServiceImpl) FindByID(ctx context.Context, venueID string) (*po.Room, error) {
	return s.repo.FindByID(ctx, venueID)
}

// FindByVenueID 根据场所ID查询场所列表
func (s *roomServiceImpl) FindByVenueID(ctx context.Context, venueID string) ([]po.Room, error) {
	return s.repo.FindByVenueID(ctx, venueID)
}

// ConvertToRoomVO 转换为场所VO
func (s *roomServiceImpl) ConvertToRoomVO(ctx context.Context, room po.Room) vo.RoomVO {
	return s.repo.ConvertToRoomVO(ctx, room)
}

// ConvertToRoomPO 转换为场所PO
func (s *roomServiceImpl) ConvertToRoom(ctx context.Context, roomVO vo.RoomVO) po.Room {
	return s.repo.ConvertToRoom(ctx, roomVO)
}

// GetRoom 获取包厢信息
func (s *roomServiceImpl) GetRoom(ctx context.Context, id string) (po.Room, error) {
	room, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return po.Room{}, err
	}
	return *room, nil
}

// IsRoomLocked 检查包厢是否锁定
func (s *roomServiceImpl) IsRoomLocked(ctx context.Context, room po.Room) bool {
	return s.repo.IsRoomLocked(ctx, room)
}

// GetAttachRooms 获取联房的其他房间信息
func (s *roomServiceImpl) GetAttachRooms(ctx context.Context, room po.Room) ([]po.Room, error) {
	return s.repo.GetAttachRooms(ctx, room)
}

// FindRoomsBySessionId 根据sessionId查询包厢
func (s *roomServiceImpl) FindRoomsBySessionId(ctx context.Context, sessionId string, venueId string) ([]po.Room, error) {
	return s.repo.FindRoomsBySessionId(ctx, sessionId, venueId)
}

// FindRoomsByVenueId 根据场所ID查询包厢
func (s *roomServiceImpl) FindRoomsByVenueId(ctx context.Context, venueId string, isDeleted bool, isDisplay bool) ([]po.Room, error) {
	return s.repo.FindRoomsByVenueId(ctx, venueId, isDeleted, isDisplay)
}

// CloseRoom 关房
func (s *roomServiceImpl) CloseRoom(ctx context.Context, rooms []po.Room, toUpdateSessions []po.Session) error {
	return s.repo.CloseRoom(ctx, rooms, toUpdateSessions)
}

// CleanRoomFinish 关房
func (s *roomServiceImpl) CleanRoomFinish(ctx context.Context, rooms []po.Room) error {
	return s.repo.CleanRoomFinish(ctx, rooms)
}

// LockRoom 锁房
func (s *roomServiceImpl) LockRoom(ctx context.Context, rooms []po.Room) error {
	return s.repo.LockRoom(ctx, rooms)
}

// UnlockRoom 解锁房
func (s *roomServiceImpl) UnlockRoom(ctx context.Context, rooms []po.Room) error {
	return s.repo.UnlockRoom(ctx, rooms)
}

// FindRoomsByCtime 根据创建时间查询包厢
func (s *roomServiceImpl) FindRoomsByCtime(ctx context.Context, venueId string, startTime int64, endTime int64) ([]po.Room, error) {
	return s.repo.FindRoomsByCtime(ctx, venueId, startTime, endTime)
}
