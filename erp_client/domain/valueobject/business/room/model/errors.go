package model

import (
	"fmt"
	baseModel "voderpltvv/erp_client/domain/valueobject/base/model"
)

// Room相关错误定义
var (
	// ErrInvalidRoomID 无效的房间ID
	ErrInvalidRoomID = fmt.Errorf("%w: invalid room id", baseModel.ErrInvalidValue)
	// ErrInvalidVenueID 无效的场所ID
	ErrInvalidVenueID = fmt.Errorf("%w: invalid venue id", baseModel.ErrInvalidValue)
	// ErrInvalidAreaID 无效的区域ID
	ErrInvalidAreaID = fmt.Errorf("%w: invalid area id", baseModel.ErrInvalidValue)
	// ErrInvalidTypeID 无效的类型ID
	ErrInvalidTypeID = fmt.Errorf("%w: invalid type id", baseModel.ErrInvalidValue)
	// ErrInvalidRoomName 无效的房间名称
	ErrInvalidRoomName = fmt.Errorf("%w: invalid room name", baseModel.ErrEmptyField)
	// ErrInvalidCapacity 无效的容量
	ErrInvalidCapacity = fmt.Errorf("%w: invalid capacity", baseModel.ErrOutOfRange)
)
