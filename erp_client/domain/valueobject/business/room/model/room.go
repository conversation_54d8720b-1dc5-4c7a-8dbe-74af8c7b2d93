package model

import (
	"voderpltvv/erp_client/domain/valueobject/base/model"
	"voderpltvv/erp_managent/service/po"
)

// Room 包厢价值对象接口
type Room interface {
	model.ValueObject
	GetVenueID() string
	GetAreaID() string
	GetTypeID() string
	GetName() string
	IsDisplayed() bool
	GetStatus() string
	GetTag() string
}

// NewRoom 创建包厢
func NewRoom(
	po *po.Room,
) Room {
	return &roomImpl{
		po: po,
	}
}

// roomImpl 包厢价值对象实现
type roomImpl struct {
	po *po.Room
}

// GetID 获取ID
func (r *roomImpl) GetID() string {
	return *r.po.Id
}

// GetVenueID 获取所属场所ID
func (r *roomImpl) GetVenueID() string {
	return *r.po.VenueId
}

// GetAreaID 获取所属区域ID
func (r *roomImpl) GetAreaID() string {
	return *r.po.AreaId
}

// GetTypeID 获取包厢类型ID
func (r *roomImpl) GetTypeID() string {
	return *r.po.TypeId
}

// GetName 获取包厢名称
func (r *roomImpl) GetName() string {
	return *r.po.Name
}

// IsDisplayed 是否显示
func (r *roomImpl) IsDisplayed() bool {
	return *r.po.IsDisplayed
}

// GetStatus 获取状态
func (r *roomImpl) GetStatus() string {
	return *r.po.Status
}

// GetTags 获取标签
func (r *roomImpl) GetTag() string {
	return *r.po.Tag
}

// Validate 验证
func (r *roomImpl) Validate() error {
	if r.po.Id == nil {
		return ErrInvalidRoomID
	}
	if r.po.VenueId == nil {
		return ErrInvalidVenueID
	}
	if r.po.AreaId == nil {
		return ErrInvalidAreaID
	}
	if r.po.TypeId == nil {
		return ErrInvalidTypeID
	}
	if r.po.Name == nil {
		return ErrInvalidRoomName
	}
	return nil
}

// GetCtime 获取创建时间
func (r *roomImpl) GetCtime() int64 {
	return *r.po.Ctime
}

// GetUtime 获取更新时间
func (r *roomImpl) GetUtime() int64 {
	return *r.po.Utime
}

// GetState 获取状态
func (r *roomImpl) GetState() int {
	return *r.po.State
}

// GetVersion 获取版本
func (r *roomImpl) GetVersion() int {
	return *r.po.Version
}

// Clone 克隆
func (r *roomImpl) Clone() model.ValueObject {
	return NewRoom(
		r.po,
	)
}
