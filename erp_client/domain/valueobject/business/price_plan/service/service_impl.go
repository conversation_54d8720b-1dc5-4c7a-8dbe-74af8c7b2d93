package service

import (
	"context"
	"encoding/json"
	"voderpltvv/erp_client/domain/valueobject/business/price_plan/model"
	"voderpltvv/erp_client/domain/valueobject/business/price_plan/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// serviceImpl 价格方案值对象服务实现
type serviceImpl struct {
	repo repository.Repository
}

// NewService 创建价格方案值对象服务
func NewService(repo repository.Repository) Service {
	return &serviceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *serviceImpl) Create(ctx context.Context, pricePlan po.PricePlan) error {
	vo := model.NewPricePlanFromPO(&pricePlan)
	return s.repo.Create(ctx, vo.GetPO())
}

// Update 更新值对象
func (s *serviceImpl) Update(ctx context.Context, pricePlan po.PricePlan) error {
	vo := model.NewPricePlanFromPO(&pricePlan)
	return s.repo.Update(ctx, vo.GetPO())
}

// Delete 删除值对象
func (s *serviceImpl) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// Validate 验证值对象
func (s *serviceImpl) Validate(ctx context.Context, pricePlan po.PricePlan) error {
	vo := model.NewPricePlanFromPO(&pricePlan)
	return vo.Validate()
}

// FindByCondition 根据条件查询值对象
func (s *serviceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.PricePlan, error) {
	pos, err := s.repo.FindByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	if pos == nil {
		return []po.PricePlan{}, nil
	}
	result := make([]po.PricePlan, len(*pos))
	for i, p := range *pos {
		result[i] = p
	}
	return result, nil
}

// GetPricePlan 获取价格方案信息
func (s *serviceImpl) GetPricePlan(ctx context.Context, id string) (po.PricePlan, error) {
	if id == "" {
		return po.PricePlan{}, nil
	}
	pricePlan, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return po.PricePlan{}, err
	}
	if pricePlan == nil {
		return po.PricePlan{}, nil
	}
	return *pricePlan, nil
}

// FindByVenueID 根据场所ID查询价格方案列表
func (s *serviceImpl) FindByVenueID(ctx context.Context, venueID string) ([]po.PricePlan, error) {
	if venueID == "" {
		return []po.PricePlan{}, nil
	}
	pricePlans, err := s.repo.FindByVenueID(ctx, venueID)
	if err != nil {
		return nil, err
	}
	result := make([]po.PricePlan, len(pricePlans))
	for i, pricePlan := range pricePlans {
		result[i] = *pricePlan.GetPO()
	}
	return result, nil
}

// FindByRoomType 根据房间类型查询价格方案列表
func (s *serviceImpl) FindByRoomType(ctx context.Context, roomType string) ([]po.PricePlan, error) {
	if roomType == "" {
		return []po.PricePlan{}, nil
	}
	pricePlans, err := s.repo.FindByRoomType(ctx, roomType)
	if err != nil {
		return nil, err
	}
	result := make([]po.PricePlan, len(pricePlans))
	for i, pricePlan := range pricePlans {
		result[i] = *pricePlan.GetPO()
	}
	return result, nil
}

// FindByVenueIDAndRoomType 根据场所ID和房间类型查询价格方案列表
func (s *serviceImpl) FindByVenueIDAndRoomType(ctx context.Context, venueID string, roomType string) ([]po.PricePlan, error) {
	if venueID == "" || roomType == "" {
		return []po.PricePlan{}, nil
	}
	pricePlans, err := s.repo.FindByVenueIDAndRoomType(ctx, venueID, roomType)
	if err != nil {
		return nil, err
	}
	result := make([]po.PricePlan, len(pricePlans))
	for i, pricePlan := range pricePlans {
		result[i] = *pricePlan.GetPO()
	}
	return result, nil
}

// GetBaseRoomFee 获取基础房费
func (s *serviceImpl) GetBaseRoomFee(ctx context.Context, pricePlan po.PricePlan) (int64, error) {
	if pricePlan.BaseRoomFee == nil {
		return 0, nil
	}
	return *pricePlan.BaseRoomFee, nil
}

// GetBirthdayPrice 获取生日价格
func (s *serviceImpl) GetBirthdayPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error) {
	if pricePlan.BirthdayFee == nil {
		return 0, nil
	}
	return *pricePlan.BirthdayFee, nil
}

// GetHolidayPrice 获取节假日价格
func (s *serviceImpl) GetHolidayPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error) {
	if pricePlan.HolidayPrices == nil {
		return 0, nil
	}
	// 解析节假日价格
	var prices map[string]int64
	if err := json.Unmarshal([]byte(*pricePlan.HolidayPrices), &prices); err != nil {
		return 0, err
	}
	// 返回默认节假日价格
	if defaultPrice, ok := prices["default"]; ok {
		return defaultPrice, nil
	}
	return 0, nil
}

// GetWeekendPrice 获取周末价格
func (s *serviceImpl) GetWeekendPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error) {
	// 周末价格可能包含在节假日价格中
	if pricePlan.HolidayPrices == nil {
		return 0, nil
	}
	// 解析节假日价格
	var prices map[string]int64
	if err := json.Unmarshal([]byte(*pricePlan.HolidayPrices), &prices); err != nil {
		return 0, err
	}
	// 返回周末价格
	if weekendPrice, ok := prices["weekend"]; ok {
		return weekendPrice, nil
	}
	return 0, nil
}

// GetOtherPrice 获取其他价格
func (s *serviceImpl) GetOtherPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error) {
	// 其他价格可能包含在节假日价格中
	if pricePlan.HolidayPrices == nil {
		return 0, nil
	}
	// 解析节假日价格
	var prices map[string]int64
	if err := json.Unmarshal([]byte(*pricePlan.HolidayPrices), &prices); err != nil {
		return 0, err
	}
	// 返回其他价格
	if otherPrice, ok := prices["other"]; ok {
		return otherPrice, nil
	}
	return 0, nil
}

// FindPricePlansByRoomType 根据房间类型查询价格方案列表
func (s *serviceImpl) FindPricePlansByRoomType(ctx context.Context, roomType string, venueID string) ([]po.PricePlan, error) {
	if roomType == "" || venueID == "" {
		return []po.PricePlan{}, nil
	}
	pricePlans, err := s.repo.FindPricePlansByRoomType(ctx, roomType, venueID)
	if err != nil {
		return nil, err
	}
	return pricePlans, nil
}

// ConvertToPricePlanVO 转换为价格方案VO
func (s *serviceImpl) ConvertToPricePlanVO(ctx context.Context, pricePlan *po.PricePlan) vo.PricePlanVO {
	return s.repo.ConvertToPricePlanVO(ctx, pricePlan)
}

// ConvertToPricePlan 转换为价格方案
func (s *serviceImpl) ConvertToPricePlan(ctx context.Context, pricePlanVO *vo.PricePlanVO) po.PricePlan {
	return s.repo.ConvertToPricePlan(ctx, pricePlanVO)
}

// FillProductAndPackageProductsVOsForPricePlan 填充产品及套餐产品VO
func (s *serviceImpl) FillProductAndPackageProductsVOsForPricePlan(ctx context.Context, venueID string, pricePlanVO *vo.PricePlanVO) {
	s.repo.FillProductAndPackageProductsVOsForPricePlan(ctx, venueID, pricePlanVO)
}

// FindByVenueIDAndSessionId 根据场所ID和场次ID查询价格方案列表
func (s *serviceImpl) FindByVenueIDAndSessionId(ctx context.Context, venueID string, sessionID string) ([]po.PricePlan, error) {
	return s.repo.FindByVenueIDAndSessionId(ctx, venueID, sessionID)
}
