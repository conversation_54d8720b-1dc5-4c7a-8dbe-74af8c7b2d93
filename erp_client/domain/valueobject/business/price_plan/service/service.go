package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Service 价格方案服务接口
type Service interface {
	baseService.ValueObjectService[po.PricePlan]

	// GetPricePlan 获取价格方案信息
	GetPricePlan(ctx context.Context, id string) (po.PricePlan, error)
	// FindByVenueID 根据场所ID查询价格方案列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.PricePlan, error)
	// FindByRoomType 根据房间类型查询价格方案列表
	FindByRoomType(ctx context.Context, roomType string) ([]po.PricePlan, error)
	// FindByVenueIDAndRoomType 根据场所ID和房间类型查询价格方案列表
	FindByVenueIDAndRoomType(ctx context.Context, venueID string, roomType string) ([]po.PricePlan, error)

	// GetBaseRoomFee 获取基础房费
	GetBaseRoomFee(ctx context.Context, pricePlan po.PricePlan) (int64, error)
	// GetBirthdayPrice 获取生日价格
	GetBirthdayPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error)
	// GetHolidayPrice 获取节假日价格
	GetHolidayPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error)
	// GetWeekendPrice 获取周末价格
	GetWeekendPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error)
	// GetOtherPrice 获取其他价格
	GetOtherPrice(ctx context.Context, pricePlan po.PricePlan) (int64, error)

	// FindPricePlansByRoomType 根据房间类型查询价格方案列表
	FindPricePlansByRoomType(ctx context.Context, roomType string, venueID string) ([]po.PricePlan, error)

	// ConvertToPricePlanVO 转换为价格方案VO
	ConvertToPricePlanVO(ctx context.Context, pricePlan *po.PricePlan) vo.PricePlanVO
	// ConvertToPricePlan 转换为价格方案
	ConvertToPricePlan(ctx context.Context, pricePlanVO *vo.PricePlanVO) po.PricePlan

	// FillProductAndPackageProductsVOsForPricePlan 填充产品及套餐产品VO
	FillProductAndPackageProductsVOsForPricePlan(ctx context.Context, venueID string, pricePlanVO *vo.PricePlanVO)

	// FindByVenueIDAndSessionId 根据场所ID和场次ID查询价格方案列表
	FindByVenueIDAndSessionId(ctx context.Context, venueID string, sessionID string) ([]po.PricePlan, error)
}
