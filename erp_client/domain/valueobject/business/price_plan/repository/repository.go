package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"
	"voderpltvv/erp_client/domain/valueobject/business/price_plan/model"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository 价格方案仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.PricePlan]

	// FindByVenueID 根据场所ID查询价格方案列表
	FindByVenueID(ctx context.Context, venueID string) ([]model.PricePlan, error)
	// FindByRoomType 根据房间类型查询价格方案列表
	FindByRoomType(ctx context.Context, roomType string) ([]model.PricePlan, error)
	// FindByVenueIDAndRoomType 根据场所ID和房间类型查询价格方案列表
	FindByVenueIDAndRoomType(ctx context.Context, venueID string, roomType string) ([]model.PricePlan, error)

	// FindPricePlansByRoomType 根据房间类型查询价格方案列表
	FindPricePlansByRoomType(ctx context.Context, roomType string, venueID string) ([]po.PricePlan, error)

	// ConvertToPricePlanVO 转换为价格方案VO
	ConvertToPricePlanVO(ctx context.Context, pricePlan *po.PricePlan) vo.PricePlanVO
	// ConvertToPricePlan 转换为价格方案
	ConvertToPricePlan(ctx context.Context, pricePlanVO *vo.PricePlanVO) po.PricePlan

	// FillProductAndPackageProductsVOsForPricePlan 填充产品及套餐产品VO
	FillProductAndPackageProductsVOsForPricePlan(ctx context.Context, venueID string, pricePlanVO *vo.PricePlanVO)
	// GetPricePlan 获取价格方案
	GetPricePlan(ctx context.Context, id string) (po.PricePlan, error)
	// FindByVenueIDAndSessionId 根据场所ID和场次ID查询价格方案列表
	FindByVenueIDAndSessionId(ctx context.Context, venueID string, sessionID string) ([]po.PricePlan, error)
}
