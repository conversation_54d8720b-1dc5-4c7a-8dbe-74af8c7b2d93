package model

import (
	"errors"
	baseModel "voderpltvv/erp_client/domain/valueobject/base/model"
	"voderpltvv/erp_managent/service/po"
)

// PricePlan 价格方案值对象接口
type PricePlan interface {
	baseModel.ValueObject
	GetPO() *po.PricePlan
	GetBaseRoomFee() int64
	GetBirthdayFee() int64
	SetBaseRoomFee(fee int64)
	SetBirthdayFee(fee int64)
	GetGroupBuyFee() int64
	GetActivityFee() int64
	SetGroupBuyFee(fee int64)
	SetActivityFee(fee int64)
	HasMinimumCharge() bool
	GetMinimumCharge() int64
	SetMinimumCharge(fee int64)
	GetMinimumConsumption() int64
	SetMinimumConsumption(fee int64)
	GetMaxDeductibleAmount() int64
	SetMaxDeductibleAmount(fee int64)
}

// pricePlanImpl 价格方案值对象实现
type pricePlanImpl struct {
	po *po.PricePlan
}

// NewPricePlanFromPO 从PO创建价格方案值对象
func NewPricePlanFromPO(po *po.PricePlan) PricePlan {
	return &pricePlanImpl{
		po: po,
	}
}

// GetID 获取ID
func (p *pricePlanImpl) GetID() string {
	return *p.po.Id
}

// GetPO 获取PO对象
func (p *pricePlanImpl) GetPO() *po.PricePlan {
	return p.po
}

// GetCtime 获取创建时间
func (p *pricePlanImpl) GetCtime() int64 {
	return *p.po.Ctime
}

// GetUtime 获取更新时间
func (p *pricePlanImpl) GetUtime() int64 {
	return *p.po.Utime
}

// GetState 获取状态
func (p *pricePlanImpl) GetState() int {
	return *p.po.State
}

// GetVersion 获取版本
func (p *pricePlanImpl) GetVersion() int {
	return *p.po.Version
}

// Validate 验证
func (p *pricePlanImpl) Validate() error {
	if p.po.Id == nil {
		return errors.New("id is required")
	}
	if p.po.VenueId == nil {
		return errors.New("venueId is required")
	}
	if p.po.Name == nil {
		return errors.New("name is required")
	}
	if p.po.RoomType == nil {
		return errors.New("roomType is required")
	}
	if p.po.ConsumptionMode == nil {
		return errors.New("consumptionMode is required")
	}
	return nil
}

// GetBaseRoomFee 获取基础房费
func (p *pricePlanImpl) GetBaseRoomFee() int64 {
	return *p.po.BaseRoomFee
}

// GetBirthdayPrice 获取生日价格
func (p *pricePlanImpl) GetBirthdayFee() int64 {
	return *p.po.BirthdayFee
}

// GetActivityFee 获取活动价格
func (p *pricePlanImpl) GetActivityFee() int64 {
	return *p.po.ActivityFee
}

// GetGroupBuyFee 获取团购价格
func (p *pricePlanImpl) GetGroupBuyFee() int64 {
	return *p.po.GroupBuyFee
}

// GetMaxDeductibleAmount 获取最高可抵扣金额
func (p *pricePlanImpl) GetMaxDeductibleAmount() int64 {
	return *p.po.MaxDeductibleAmount
}

// GetMinimumCharge 获取最低消费金额
func (p *pricePlanImpl) GetMinimumCharge() int64 {
	return *p.po.MinimumCharge
}

// GetMinimumConsumption 获取最低消费金额
func (p *pricePlanImpl) GetMinimumConsumption() int64 {
	return *p.po.MinimumConsumption
}

// HasMinimumCharge 是否支持最低消费
func (p *pricePlanImpl) HasMinimumCharge() bool {
	return *p.po.HasMinimumCharge
}

// SetActivityFee 设置活动价格
func (p *pricePlanImpl) SetActivityFee(fee int64) {
	p.po.ActivityFee = &fee
}

// SetBaseRoomFee 设置基础房费
func (p *pricePlanImpl) SetBaseRoomFee(fee int64) {
	p.po.BaseRoomFee = &fee
}

// SetBirthdayFee 设置生日价格
func (p *pricePlanImpl) SetBirthdayFee(fee int64) {
	p.po.BirthdayFee = &fee
}

// SetGroupBuyFee 设置团购价格
func (p *pricePlanImpl) SetGroupBuyFee(fee int64) {
	p.po.GroupBuyFee = &fee
}

// SetMaxDeductibleAmount 设置最高可抵扣金额
func (p *pricePlanImpl) SetMaxDeductibleAmount(fee int64) {
	p.po.MaxDeductibleAmount = &fee
}

// SetMinimumCharge 设置最低消费金额
func (p *pricePlanImpl) SetMinimumCharge(fee int64) {
	p.po.MinimumCharge = &fee
}

// SetMinimumConsumption 设置最低消费金额
func (p *pricePlanImpl) SetMinimumConsumption(fee int64) {
	p.po.MinimumConsumption = &fee
}

// Clone 克隆
func (p *pricePlanImpl) Clone() baseModel.ValueObject {
	return NewPricePlanFromPO(p.po)
}

// Equals 值对象比较
func (p *pricePlanImpl) Equals(other baseModel.ValueObject) bool {
	if other == nil {
		return false
	}
	if otherPricePlan, ok := other.(PricePlan); ok {
		return p.Equals(otherPricePlan)
	}
	return false
}
