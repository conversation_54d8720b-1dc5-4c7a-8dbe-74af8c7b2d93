package service

import (
	"context"
	baseService "voderpltvv/erp_client/domain/valueobject/base/service"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomThemeService 场所服务接口
type RoomThemeService interface {
	baseService.ValueObjectService[po.RoomTheme]

	// FindByID 根据场所ID查询场所
	FindByID(ctx context.Context, venueID string) (*po.RoomTheme, error)

	// ConvertToRoomThemeVO 转换为场所场所
	ConvertToRoomThemeVO(ctx context.Context, roomTheme po.RoomTheme) vo.RoomThemeVO

	// ConvertToRoomTheme 转换为场所PO
	ConvertToRoomTheme(ctx context.Context, roomThemeVO vo.RoomThemeVO) po.RoomTheme

	// FindRoomThemesByRoomIds 根据房间ID查询场所列表
	FindRoomThemesByRoomIds(ctx context.Context, venueId string, roomIds []string) ([]po.RoomTheme, error)
}
