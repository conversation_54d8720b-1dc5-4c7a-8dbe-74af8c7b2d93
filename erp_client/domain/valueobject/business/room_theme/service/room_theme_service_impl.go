package service

import (
	"context"

	"voderpltvv/erp_client/domain/valueobject/business/room_theme/repository"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// RoomThemeServiceImpl 场所服务实现
type RoomThemeServiceImpl struct {
	repo repository.Repository
}

// NewService 创建场所服务
func NewService(repo repository.Repository) RoomThemeService {
	return &RoomThemeServiceImpl{
		repo: repo,
	}
}

// Create 创建值对象
func (s *RoomThemeServiceImpl) Create(ctx context.Context, roomTheme po.RoomTheme) error {
	return nil
}

// Update 更新值对象
func (s *RoomThemeServiceImpl) Update(ctx context.Context, roomTheme po.RoomTheme) error {
	return nil
}

// Delete 删除值对象
func (s *RoomThemeServiceImpl) Delete(ctx context.Context, id string) error {
	return nil
}

// Validate 验证值对象
func (s *RoomThemeServiceImpl) Validate(ctx context.Context, roomTheme po.RoomTheme) error {
	return nil
}

// FindByCondition 根据条件查询值对象
func (s *RoomThemeServiceImpl) FindByCondition(ctx context.Context, condition map[string]interface{}) ([]po.RoomTheme, error) {
	return nil, nil
}

// FindByID 根据场所ID查询场所
func (s *RoomThemeServiceImpl) FindByID(ctx context.Context, venueID string) (*po.RoomTheme, error) {
	return s.repo.FindByID(ctx, venueID)
}

// ConvertToRoomThemeVO 转换为场所VO
func (s *RoomThemeServiceImpl) ConvertToRoomThemeVO(ctx context.Context, roomTheme po.RoomTheme) vo.RoomThemeVO {
	return s.repo.ConvertToRoomThemeVO(ctx, roomTheme)
}

// ConvertToRoomTheme 转换为场所PO
func (s *RoomThemeServiceImpl) ConvertToRoomTheme(ctx context.Context, roomThemeVO vo.RoomThemeVO) po.RoomTheme {
	return s.repo.ConvertToRoomTheme(ctx, roomThemeVO)
}

// FindRoomThemesByRoomIds 根据房间ID查询场所列表
func (s *RoomThemeServiceImpl) FindRoomThemesByRoomIds(ctx context.Context, venueId string, roomIds []string) ([]po.RoomTheme, error) {
	return s.repo.FindRoomThemesByRoomIds(ctx, venueId, roomIds)
}
