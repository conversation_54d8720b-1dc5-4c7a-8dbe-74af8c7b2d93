package repository

import (
	"context"
	baseRepository "voderpltvv/erp_client/domain/valueobject/base/repository"

	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
)

// Repository Venue仓储接口
type Repository interface {
	baseRepository.ValueObjectRepository[po.RoomTheme]

	// FindByVenueID 根据场所ID查询场所列表
	FindByVenueID(ctx context.Context, venueID string) ([]po.RoomTheme, error)

	// ConvertToRoomThemeVO 转换为场所VO
	ConvertToRoomThemeVO(ctx context.Context, roomTheme po.RoomTheme) vo.RoomThemeVO

	// ConvertToRoomTheme 转换为场所PO
	ConvertToRoomTheme(ctx context.Context, roomThemeVO vo.RoomThemeVO) po.RoomTheme

	// FindRoomThemesByRoomIds 根据房间ID查询场所列表
	FindRoomThemesByRoomIds(ctx context.Context, venueId string, roomIds []string) ([]po.RoomTheme, error)
}
