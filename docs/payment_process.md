# 支付流程开发文档

## 概述
支付流程是处理订单支付的核心业务逻辑，包括参数验证、折扣计算、支付处理等多个环节。

## 主要流程

### 1. 请求参数验证
- **入参绑定**：将HTTP请求体绑定到`QueryOrderPayReqDto`结构体
- **基础参数验证**：
  - 支付类型(PayType)验证
  - 支付金额(PayAmount)验证
  - 场地ID(VenueId)验证
  - 房间ID(RoomId)验证
  - 会话ID(SessionId)验证
  - 员工ID(EmployeeId)验证

### 2. 折扣检查
检查订单是否包含以下折扣类型：
- 房费打折(DiscountRoomRate)
- 商品打折(DiscountProductRate)
- 房费减免(ReduceRoomAmount)
- 商品减免(ReduceProductAmount)
- 免单金额(FreeAmount)

### 3. 订单信息获取
```go
toPayOrderVOs, toPayOrders, err := payService.GetPayOrderInfos(ctx, &reqDto)
```
- 查询会话相关的所有订单
- 过滤出需要支付的订单
- 获取订单关联的支付信息

### 4. 订单金额计算
处理订单金额的计算，包括：
- **房费计算**
  - 房费打折计算
  - 房费减免计算
- **商品金额计算**
  - 商品打折计算
  - 商品减免计算
- **最终金额确认**
  - 验证计算后的金额与支付金额是否匹配

### 5. 支付数据处理
```go
toAddPayBill, toAddOrderAndPays := payService.BuildPayDataForPay(...)
```
- 生成支付单号
- 创建支付记录
- 关联订单和支付信息

### 6. 数据持久化
```go
err = payService.SaveBatchTxForPay(...)
```
- 保存支付单据
- 更新订单状态
- 更新房间和商品信息

### 7. 支付网关处理
```go
payResult, err := payService.TransformPayGate(...)
```
- 调用支付网关
- 处理支付结果

### 8. 支付后处理
- 对于记账支付类型：
  ```go
  err := payService.AfterPayCallbackCoUpdateInfoByPayId(...)
  ```
  - 更新支付状态
  - 重新计算会话费用

## 数据结构

### 主要请求参数 (QueryOrderPayReqDto)
```go
type QueryOrderPayReqDto struct {
    VenueId              *string  // 场地ID
    RoomId               *string  // 房间ID
    SessionId            *string  // 会话ID
    PayAmount            *int64   // 支付金额
    PayType             *string  // 支付类型
    DiscountRoomRate    *int64   // 房费打折率
    DiscountProductRate *int64   // 商品打折率
    ReduceRoomAmount    *int64   // 房费减免
    ReduceProductAmount *int64   // 商品减免
    FreeAmount          *int64   // 免单金额
    EmployeeId          *string  // 员工ID
}
```

### 支付单据 (PayBill)
```go
type PayBill struct {
    PayId                 *string
    TotalFee             *int64
    DiscountRoomAmount    *int64
    DiscountProductAmount *int64
    Status               *string
    PayType              *string
    FinishTime           *int64
}
```

## 错误处理
支付流程中的主要错误处理：
1. 参数验证错误
2. 订单状态错误
3. 金额计算错误
4. 支付网关错误
5. 数据保存错误

## 注意事项

### 1. 并发处理
- 确保订单状态的原子性
- 防止重复支付
- 处理并发支付请求

### 2. 数据一致性
- 保证订单金额计算准确
- 确保支付记录与订单状态一致
- 维护交易记录的完整性

### 3. 安全考虑
- 验证支付请求的合法性
- 保护敏感支付信息
- 记录关键操作日志

### 4. 性能优化
- 优化数据库查询
- 合理使用事务
- 异步处理非关键流程

## 测试建议

### 1. 单元测试
- 测试各类折扣计算
- 验证金额计算准确性
- 检查状态转换逻辑

### 2. 集成测试
- 测试完整支付流程
- 验证数据一致性
- 测试并发场景

### 3. 压力测试
- 测试高并发支付
- 验证系统稳定性
- 检查性能瓶颈 