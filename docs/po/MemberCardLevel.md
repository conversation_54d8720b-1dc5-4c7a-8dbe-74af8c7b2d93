# MemberCardLevel 会员卡等级实体

## 基础字段
- id: ID
- venue_id: 门店ID
- name: 卡名称
- level: 等级
- logo: logo图片地址或base64字符串
- card_type: 卡类型
- default_usage_scope: 默认使用范围
- distribution_channels: 分发渠道
- state: 状态
- version: 版本号
- ctime: 创建时间戳
- utime: 更新时间戳

## 储值相关
- is_support_balance: 是否支持储值(0-不支持,1-支持)
- balance_usage_scope: 本金使用范围



## 线上领卡
- is_support_online_card: 是否支持线上领卡(0-不支持,1-支持)

## 消费时段设置
- card_consumption_time_type: 卡消费时段类型(WEEK-星期、DATE-日期)
- card_consumption_time_week_day: 星期几，JSON数组存储数字

```json
[1, 2, 3]  // 1-7分别代表周一到周日
```

- card_consumption_time_date: 日期，JSON数组存储时间戳

```json
[1704067200, 1704153600]  // 时间戳示例
```

- card_consumption_time_start_date: 开始日期时间戳
- card_consumption_time_end_date: 结束日期时间戳
- card_consumption_time_start_time: 时段范围开始时间，格式 HH:mm:ss
- card_consumption_time_end_time: 时段范围结束时间，格式 HH:mm:ss

## 商品结算配置
- product_settlement_config: 商品结账配置

```json
[
  {
    "productId": "xxx",
    "settlementTypes": ["PRINCIPAL", "GIFT"] // PRINCIPAL-本金, GIFT-赠金
  }
]
```

## 有效期设置
- validity_period: 有效期类型(PERMANENT-永久, RELATIVE-相对有效期, FIXED-固定有效期)
- validity_period_value: 相对有效期时长值
- validity_period_unit: 相对有效期单位(YEAR-年, MONTH-月, DAY-日)
- validity_start_date: 固定有效期开始时间戳
- validity_end_date: 固定有效期结束时间戳
- renewal_period_value: 续期周期值
- renewal_period_unit: 续期周期单位(YEAR-年, MONTH-月, DAY-日)

## 礼遇设置
- birthday_benefits: 生日礼遇
- registration_upgrade_benefits: 开卡/升级礼遇(格式同birthday_benefits)
- monthly_vouchers: 每月礼遇(格式同birthday_benefits)

```json
[
  {
    "type": "SING_VOUCHER",    // SING_VOUCHER-欢唱券, CASH_VOUCHER-代金券
    "value": 60,               // 欢唱券时表示分钟数，代金券时表示金额(分)
    "limit": 1,                // 每人限领数量
    "validDays": 30,           // 有效期天数
    "usageScope": "ROOM_FEE_ONLY"  // ROOM_FEE_ONLY-仅房费可用, ROOM_OR_PRODUCT-房费或商品可用
  }
]
```

## 费用设置
- is_need_card_issue_fee: 是否需要制卡费(0-不需要,1-需要)
- card_issue_fee: 电子卡办卡费用(分)
- card_replacement_fee: 电子卡补卡费用(分)
- card_renewal_fee: 续卡费用(分)
- minimum_recharge_amount: 最低充值金额(分)
- minimum_consumption_amount: 最低消费金额(分)
- minimum_balance_for_discount: 享受折扣的最低余额(分)

## 折扣设置
- room_discount: 房间折扣
- beverage_discount: 商品、酒水折扣
- service_fee_discount: 服务费折扣

## 积分规则
- consumption_points_base: ���费积分基数(如100元)
- consumption_points_per_base: 消费consumption_points_base获得积分数
- consumption_points_rule: 消费积分规则

```json
{
  "baseAmount": 100,           // 基准金额(分)
  "pointsPerBase": 1,         // 每满基准金额获得积分数
  "productTypes": ["ROOM", "PRODUCT"],  // 可获得积分的消费类型: ROOM-房费, PRODUCT-商品
  "excludeProductIds": ["xxx"]  // 不参与积分的商品ID列表
}
```

- recharge_points_rule: 充值积分规则

```json
[
  {
    "minAmount": 10000,        // 最小充值金额(分)
    "maxAmount": 50000,        // 最大充值金额(分)
    "points": 100             // 获得积分数
  }
]
```

## 升降级条件
- upgrade_conditions: 升级条件

```json
{
  "consumptionAmount": 1000000,  // 累计消费金额(分)
  "consumptionPoints": 1000      // 年累计积分
}
```

- downgrade_conditions: 降级条件

```json
{
  "consumptionPoints": 500       // 年等级累计积分
}
```

## 房间限制
- daily_room_limit: 每日房间限制(0表示不限制)
- consumption_time_slots: 消费时间段

```json
[
  {
    "startTime": "10:00:00",  // HH:mm:ss 格式
    "endTime": "22:00:00",    // HH:mm:ss 格式
    "weekDays": [1, 2]        // 1-7分别代表周一到周日
  }
]
```

## 支付限制
- payment_restrictions: 支付限制

```json
{
  "allowedMethods": ["WECHAT", "ALIPAY", "BALANCE"],  // 允许的支付方式
  "minAmount": 10000,  // 单次最小支付金额(分)
  "maxAmount": 100000  // 单次最大支付金额(分)
}
```
