原始需求：
```markdown:
现在我们联调买钟方案，按minute购买的情况，debug并修复代码
设置了一下三个方案：
roomFee代表[hourMinuteStart,hourMinuteEnd]之间的总费用
设置测试用的开始时间和结束时间
pricePlanVO1: hourMinuteStart = "15:00", hourMinuteEnd = "17:10", roomFee = 100
pricePlanVO2: hourMinuteStart = "17:10", hourMinuteEnd = "19:20", roomFee = 200
pricePlanVO3: hourMinuteStart = "19:20", hourMinuteEnd = "21:30", roomFee = 300
设置开始时间为CurrentTime：1729494600
// 2024-10-21 15:10:00 : 1729494600
// 2024-10-21 15:00:00 : 1729494000 
得出的正确输出:
段: 2024-10-21 15:10:00 ~ 2024-10-21 17:10:00, RoomFee: 100, DurationMinutes: 120, InVODurationMinutes: 130, DurationRoomFee: 92.3076923077
段: 2024-10-21 17:10:00 ~ 2024-10-21 19:20:00, RoomFee: 200, DurationMinutes: 130, InVODurationMinutes: 130, DurationRoomFee: 200
段: 2024-10-21 19:20:00 ~ 2024-10-21 20:10:00, RoomFee: 300, DurationMinutes: 50, InVODurationMinutes: 130, DurationRoomFee: 115.3846153846
先描述你的理解
```
```markdown:service/impl/OrderValidateService_1byminite.md
# 按分钟购买房间计费方案测试文档

## 1. 测试场景设置

### 1.1 价格方案配置
设置三个连续的价格时段方案：
```
pricePlanVO1: 
- 时间段: 15:00 - 17:10
- 时段时长: 130分钟
- 总费用(roomFee): 100元

pricePlanVO2:
- 时间段: 17:10 - 19:20
- 时段时长: 130分钟
- 总费用(roomFee): 200元

pricePlanVO3:
- 时间段: 19:20 - 21:30
- 时段时长: 130分钟
- 总费用(roomFee): 300元
```

### 1.2 测试订单时间
- 开始时间(CurrentTime): 1729494600 (2024-10-21 15:10:00)
- 参考时间点: 1729494000 (2024-10-21 15:00:00)
- 结束时间: 2024-10-21 20:10:00

## 2. 预期计费结果

### 2.1 第一时段费用
```
时间范围: 2024-10-21 15:10:00 ~ 2024-10-21 17:10:00
实际使用时长(DurationMinutes): 120分钟
时段总时长(InVODurationMinutes): 130分钟
费用计算: 100 * (120/130) = 92.3076923077元
```

### 2.2 第二时段费用
```
时间范围: 2024-10-21 17:10:00 ~ 2024-10-21 19:20:00
实际使用时长(DurationMinutes): 130分钟
时段总时长(InVODurationMinutes): 130分钟
费用计算: 200 * (130/130) = 200元
```

### 2.3 第三时段费用
```
时间范围: 2024-10-21 19:20:00 ~ 2024-10-21 20:10:00
实际使用时长(DurationMinutes): 50分钟
时段总时长(InVODurationMinutes): 130分钟
费用计算: 300 * (50/130) = 115.3846153846元
```

## 3. 计费规则说明
1. 按分钟精确计费，不进行向上取整
2. 部分使用的时段按照实际使用时长比例计费
3. 每个时段独立计算费用后求和
4. 时段交界处精确切分计费
5. 总费用为各时段费用之和

## 4. 技术实现要点
1. 时间戳与日期时间的准确转换
2. 小数计算精度控制
3. 时间段边界处理
4. 比例计算准确性
5. 跨时段订单的拆分处理

## 5. 验证重点
1. 跨时段计费的准确性
2. 不完整时段的按比例计费准确性
3. 时间边界处理的准确性
4. 总费用计算的准确性
```