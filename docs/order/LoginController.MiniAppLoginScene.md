# MiniAppLoginScene 函数逻辑流程

## 功能概述
此函数处理公众号消息中的关注码跳转逻辑，返回相关的登录场景信息。主要用于小程序扫码登录场景。

## 详细流程

1. **参数验证**
   - 解析请求体到 `QueryMiniAppLoginReqDto` 结构
   - 验证 `sceneStr`（场景字符串）不能为空

2. **查询授权信息**
   - 通过 `sceneStr` 查询 `VenueShouyinGrantGzhQR` 表
   - 如果查不到记录，返回"关注码不存在"错误
   - 获取关联的 `venueId`（场馆ID）

3. **查询场馆信息**
   - 如果有 `venueId`，则查询场馆表获取相关信息
   - 从场馆信息中提取老板电话号码（phone字段）

4. **处理 Token 相关信息**
   - 从请求头获取 token
   - 如果 token 存在：
     - 尝试解析 token 获取 unionid
     - 如果成功获取 unionid：
       - 检查 token 是否过期
       - 已过期：生成新的 token
       - 未过期：使用原 token
     - 如果无法从 token 获取 unionid：
       - 标记需要调用微信登录接口
   - 如果 token 不存在：
     - 标记需要调用微信登录接口

5. **微信登录处理**
   - 如果需要调用微信登录接口：
     - 验证小程序 code 参数不为空
     - 调用微信小程序登录接口获取 unionid
     - 使用获取到的 unionid 生成新的 token

6. **查询员工信息**
   - 如果获取到 unionid：
     - 查询员工表获取对应的手机号

7. **返回结果**
   返回一个包含以下信息的 `QueryMiniAppLoginSceneVO` 对象：
   - Unionid：用户的 unionid
   - PhoneEmployee：员工手机号
   - VenueId：场馆ID
   - PhoneBoss：老板手机号
   - Token：新的或原有的 token

## 错误处理
- 参数绑定错误：返回服务器忙（500）
- sceneStr 为空：返回参数错误（400）
- 关注码不存在：返回未找到（404）
- 服务器忙：返回服务器忙（500）
- code 参数缺失（当需要通过小程序登录时）：返回参数错误（400）

## 主要依赖
- 场馆授权二维码服务 (venueShouyinGrantGzhQRService)
- 场馆服务 (venueService)
- 员工服务 (employeeService)
- Token 工具类 (util)
- 微信小程序接口

## 流程图
```mermaid
flowchart TD
    Start([开始]) --> ParseReq[解析请求参数]
    ParseReq --> ValidateScene{验证sceneStr}
    
    ValidateScene -->|为空| RetErr1[返回400:参数错误]
    ValidateScene -->|不为空| QueryQR[查询授权二维码]
    
    QueryQR --> CheckQR{是否存在}
    CheckQR -->|不存在| RetErr2[返回404:关注码不存在]
    CheckQR -->|存在| GetVenueInfo[获取场馆信息]
    
    GetVenueInfo --> GetBossPhone[获取老板电话]
    GetBossPhone --> ProcessToken[处理Token]
    
    ProcessToken --> CheckToken{检查Token}
    CheckToken -->|不存在| NeedWxLogin[标记需要微信登录]
    CheckToken -->|存在| ParseToken[解析Token获取unionid]
    
    ParseToken --> ValidateUnionid{unionid是否有效}
    ValidateUnionid -->|无效| NeedWxLogin
    ValidateUnionid -->|有效| CheckExpired{Token是否过期}
    
    CheckExpired -->|已过期| GenNewToken1[生成新Token]
    CheckExpired -->|未过期| UseOldToken[使用原Token]
    
    NeedWxLogin --> ValidateCode{验证code}
    ValidateCode -->|为空| RetErr3[返回400:code不能为空]
    ValidateCode -->|不为空| CallWxAPI[调用微信登录接口]
    
    CallWxAPI --> GetUnionid[获取unionid]
    GetUnionid --> GenNewToken2[生成新Token]
    
    GenNewToken1 --> QueryEmployee[查询员工信息]
    GenNewToken2 --> QueryEmployee
    UseOldToken --> QueryEmployee
    
    QueryEmployee --> PrepareResult[准备返回数据]
    PrepareResult --> ReturnVO[返回LoginSceneVO]
    
    ReturnVO --> End([结束])
    RetErr1 --> End
    RetErr2 --> End
    RetErr3 --> End

    subgraph ReturnVO内容
        direction TB
        VO1[Unionid] 
        VO2[PhoneEmployee]
        VO3[VenueId]
        VO4[PhoneBoss]
        VO5[Token]
    end
```

</rewritten_file>