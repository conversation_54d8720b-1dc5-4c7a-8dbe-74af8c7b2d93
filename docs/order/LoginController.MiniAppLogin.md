```markdown:controller/LoginController.MiniAppLogin.md
# MiniAppLogin

小程序扫码登录接口

## 接口信息

- **路径**: `/api/miniapp/login`
- **方法**: POST
- **标签**: 登录

## 请求参数

### Body参数 (req.QueryMiniAppLoginReqDto)

| 参数名 | 类型   | 必填 | 描述        |
|--------|--------|------|-------------|
| code   | string | 是   | 小程序登录code |

## 处理流程

1. 验证请求参数，确保code不为空
2. 调用微信小程序登录接口获取unionid
   - 使用appid、secret和code请求微信接口
   - 获取用户unionid信息
3. 生成新的token
4. 根据unionid查询员工信息
5. 查询关联的门店信息
6. 组装返回数据

## 流程图

```mermaid
flowchart TD
    A[开始] --> B[验证请求参数]
    B --> C{code是否为空?}
    C -->|是| D[返回参数错误]
    C -->|否| E[调用微信小程序登录接口]
    E --> F{接口调用是否成功?}
    F -->|否| G[返回服务器错误]
    F -->|是| H[获取unionid]
    H --> I[生成新token]
    I --> J[查询员工信息]
    J --> K[查询关联门店信息]
    K --> L[组装返回数据]
    L --> M[返回成功结果]
    D --> N[结束]
    G --> N
    M --> N
```

## 返回结果

### 成功响应 (200)

返回Result对象，包含vo.LoginMinAppResultVO数据：

```json
{
  "unionid": "string",     // 用户unionid
  "token": "string",       // 新生成的token
  "employeeVOs": [         // 员工信息列表
    {
      // 员工详细信息
    }
  ],
  "venueVOs": [           // 门店信息列表
    {
      // 门店详细信息
    }
  ]
}
```

### 错误响应 (500)

返回Result对象，包含错误信息：

- code参数为空
- 服务器处理异常

## 注意事项

1. 需要配置正确的小程序appid和secret
2. 需要处理微信接口调用可能的异常情况
3. token生成失败需要合理处理
```
