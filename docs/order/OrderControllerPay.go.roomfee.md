房费减免

```go
    *reqDto.ReduceRoomAmount
        if len(toUpdateOrderProducts) == 0 {
			for _, v := range *orderProducts {
				toUpdateOrderProducts = append(toUpdateOrderProducts, po.OrderProduct{
					Id:           v.Id,
					OrderNo:      v.OrderNo,
					ReduceAmount: reqDto.ReduceProductAmount,
					PayPrice:     util.GetItPtr(*v.PayPrice - *reqDto.ReduceProductAmount),
					TotalAmount:  util.GetItPtr(*v.PayPrice*int64(*v.Quantity) - *reqDto.ReduceProductAmount),
				})
			}
		} else {
			for _, v := range toUpdateOrderProducts {
				orderNo2TotalAmount[*v.OrderNo] -= (*v.PayPrice - *reqDto.ReduceProductAmount)
			}
		}
```
减免总金额：*reqDto.ReduceRoomAmount
按商品总金额等比分配到每个商品组上,余数余到最后一个商品组上
单价直接用减免后的金额除以数量
单价、金额计算过程中产生小数部分直接舍去

例如
减免21元
总金额: 65.97+23.31=89.28
3 20   65.97 
7  3.33   23.31

