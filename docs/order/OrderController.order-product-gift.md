
# OrderProductGift 方法文档

## 功能描述
赠送商品功能，用于处理包房赠送商品的业务逻辑。

## API 信息
- **路径**: `/api/order/product-gift`
- **方法**: POST
- **标签**: 订单

## 请求参数
请求体需要包含`AddOrderAdditionalReqDto`对象，主要字段包括：
- `RoomId`: 房间ID（必填）
- `OrderProductVOs`: 赠送商品列表
  - `ProductId`: 商品ID
  - `ProductName`: 商品名称
  - `Flavors`: 口味
  - `Quantity`: 数量
  - `Unit`: 单位
  - `PayPrice`: 支付价格
  - `Mark`: 备注
  - `Price`: 价格
  - `TotalAmount`: 总金额

## 处理流程
1. 验证请求参数
   - 检查请求体格式是否正确
   - 验证RoomId是否存在

2. 检查房间状态
   - 查询房间信息
   - 验证房间是否存在
   - 检查SessionId是否存在

3. 检查场次状态
   - 查询场次信息
   - 验证场次是否存在

4. 创建订单商品
   - 生成订单号
   - 创建订单商品记录
   - 计算总金额

5. 创建订单
   - 设置订单基本信息
   - 标记为已支付状态
   - 设置订单类型为赠送商品

6. 保存数据
   - 调用OrderService保存订单和商品信息

## 返回结果
- 成功：返回空对象
- 失败：返回错误信息

## 错误码
- `GeneralCodes.ServerIsBuzy.Code`: 服务器忙
- `GeneralCodes.ParamError.Code`: 参数错误
- `GeneralCodes.InternalError.Code`: 内部错误

## 示例
### 请求示例
```json
{
  "roomId": "room123",
  "orderProductVOs": [
    {
      "productId": "prod001",
      "productName": "可乐",
      "quantity": 2,
      "unit": "瓶",
      "price": 1000,
      "totalAmount": 2000
    }
  ]
}
```

### 成功响应示例
```json
{
  "code": 200,
  "data": null,
  "message": "success"
}
```

### 失败响应示例
```json
{
  "code": 500,
  "data": null,
  "message": "Failed to create additional order"
}
```

## 注意事项
1. 赠送商品订单会直接标记为已支付状态
2. 赠送商品不会影响房间的状态
3. 需要确保房间和场次状态正常才能进行赠送操作