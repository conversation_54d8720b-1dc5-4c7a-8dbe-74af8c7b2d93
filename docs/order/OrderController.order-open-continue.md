
# OrderOpenContinue 续台接口

## 接口说明
该接口用于处理包厢续台操作，允许用户延长已开台包厢的使用时间。

## 请求参数
请求体为 JSON 格式，包含以下字段：
- `sessionId`: 场次ID，必填
- `roomId`: 包厢ID，必填
- `venueId`: 场所ID，必填
- `startTime`: 开始时间（Unix时间戳），必填
- `endTime`: 结束时间（Unix时间戳），必填
- `currentTime`: 当前时间（Unix时间戳），选填，默认为服务器时间
- `totalAmount`: 总金额，必填
- `minimumCharge`: 最低消费金额，选填
- `pricePlanId`: 价格方案ID，选填
- `selectedAreaId`: 选择的区域ID，选填
- `selectedRoomTypeId`: 选择的包厢类型ID，选填
- `buyMinute`: 购买分钟数，选填
- `timeChargeType`: 计时类型，选填
- `timeChargeMode`: 计时模式，选填

## 处理流程
1. 验证请求参数
   - 验证 sessionId 不为空
   - 验证 roomId 不为空
   - 检查场次信息是否存在
   - 检查包厢状态

2. 生成订单号
   - 使用 util.GetOpeningOrderNo 生成新的订单号

3. 时间校验
   - 验证开始时间是否合法
   - 验证是否超时（30分钟限制）
   - 设置当前时间

4. 创建主订单信息
   - 设置场次ID、场所ID、包厢ID等基本信息
   - 设置订单状态为未支付
   - 设置时间和金额信息

5. 处理价格方案
   - 获取价格方案信息
   - 设置消费模式和最低消费等信息

6. 创建包厢计划信息
   - 记录包厢使用时间段
   - 记录价格方案信息
   - 计算费用信息

7. 更新包厢状态
   - 更新包厢的结束时间

8. 保存数据
   - 调用 orderService.OrderTimeGift 保存所有相关数据

## 返回结果
- 成功：返回 success 状态
- 失败：返回错误码和错误信息

## 错误码
- `ParamError`: 参数错误
- `InternalError`: 内部错误
- `ServerIsBuzy`: 服务器繁忙

## 注意事项
1. 续台操作会生成新的订单
2. 需要确保原场次存在且状态正确
3. 时间验证有30分钟的限制
4. 包厢状态检查可能被注释（需要根据实际情况决定是否启用）