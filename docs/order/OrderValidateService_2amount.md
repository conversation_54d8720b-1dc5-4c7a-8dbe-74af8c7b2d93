原始需求：
```markdown:
现在我们联调买钟方案，按amount购买的情况，debug并修复代码
设置了一下三个方案：
roomFee代表[hourMinuteStart,hourMinuteEnd]之间的总费用
设置测试用的开始时间和结束时间
pricePlanVO1: hourMinuteStart = "15:00", hourMinuteEnd = "17:10", roomFee = 100
pricePlanVO2: hourMinuteStart = "17:10", hourMinuteEnd = "19:20", roomFee = 200
pricePlanVO3: hourMinuteStart = "19:20", hourMinuteEnd = "21:30", roomFee = 300
设置开始时间为CurrentTime：1729494000
// 2024-10-21 15:10:00 : 1729494600
// 2024-10-21 15:00:00 : 1729494000 
amount: 408
得出的正确输出:
段: 2024-10-21 15:10:00 ~ 2024-10-21 17:10:00, RoomFee: 100, DurationMinutes: 120, InVODurationMinutes: 130, DurationRoomFee: 92.3076923076923
段: 2024-10-21 17:10:00 ~ 2024-10-21 19:20:00, RoomFee: 200, DurationMinutes: 130, InVODurationMinutes: 130, DurationRoomFee: 200
段: 2024-10-21 19:20:00 ~ 2024-10-21 20:10:00, RoomFee: 300, DurationMinutes: 50, InVODurationMinutes: 130, DurationRoomFee: 115.38461538461539
先描述你的理解
```

让我分析一下按金额购买的场景：

### 1. 测试场景理解
- 购买方式：按金额购买 (timeChargeMode = "amount")
- 购买金额：408元
- 开始时间：2024-10-21 15:00:00 (1729494000)
- 预期结束时间：2024-10-21 20:10:00

### 2. 价格方案设置
与按分钟购买相同的三个时段：
```
pricePlanVO1: 15:00-17:10, roomFee=100
pricePlanVO2: 17:10-19:20, roomFee=200
pricePlanVO3: 19:20-21:30, roomFee=300
```

### 3. 计费逻辑分析
按金额购买时，需要：
1. 从开始时间依次使用各时段的费率
2. 计算每个时段可以购买的时长，直到消耗完总金额
3. 每个时段的时长计算：
   - 可用时长 = min(剩余金额/时段费率 * 时段总时长, 时段剩余时长)

### 4. 死循环问题分析
死循环可能的原因：
1. 剩余金额计算错误，导致永远无法归零
2. 时间段切换逻辑有误
3. 金额分配计算精度问题

### 5. 建议修复方向
1. 确保每次循环都能消耗金额
2. 添加最大循环次数保护
3. 处理金额计算的精度问题
4. 确保时间段正确切换
