# OrderOpen 开台接口

## 接口说明
该接口用于处理开台操作，包括创建订单、房间状态更新等功能。

## 请求参数
### AddOrderOpenReqDto
| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roomId | string | 是 | 房间ID |
| venueId | string | 是 | 门店id |
| startTime | int64 | 是 | 开始时间(时间戳) |
| endTime | int64 | 否 | 结束时间(时间戳) |
| currentTime | int64 | 否 | 当前时间(时间戳) |
| totalAmount | int64 | 否 | 总金额(分) |
| minimumCharge | int64 | 否 | 最低消费(分) |
| pricePlanId | string | 否 | 价格方案ID |
| selectedAreaId | string | 否 | 选择的区域ID |
| selectedRoomTypeId | string | 否 | 选择的房间类型ID |
| buyMinute | int64 | 否 | 购买时长(分钟) |
| timeChargeType | string | 否 | 计时类型 |
| timeChargeMode | string | 否 | 计费模式 |
| orderRoomPlanVOS | []OrderRoomPlanVO | 否 | 房间计划列表 |
| inOrderProductInfos | []OrderProductVO | 否 | 套餐内商品信息 |
| bookingId | string | 否 | 预订ID |

## 处理流程
1. 验证输入参数
   - 检查roomId是否为空
   - 检查房间状态是否为空闲

2. 生成订单号和场次ID
   - 根据venueId生成订单号
   - 根据venueId生成场次ID

3. 检查时间
   - 验证开始时间是否合法
   - 验证是否超时(30分钟)
   - 设置当前时间

4. 创建主开台订单
   - 设置订单基本信息
   - 设置订单状态为未支付

5. 处理价格方案
   - 查询价格方案信息
   - 创建订单价格方案记录

6. 处理房间计划
   - 创建房间计划记录
   - 计算总房间费用

7. 处理商品信息
   - 处理套餐内商品
   - 计算总商品费用

8. 更新订单和房间状态
   - 保存所有相关数据到数据库
   - 如果有预订信息，更新预订状态

## 返回结果
### 成功
返回场次信息(SessionVO)

### 失败
返回错误码和错误信息

## 错误码
| 错误码 | 说明 |
|--------|------|
| ParamError | 参数错误 |
| InternalError | 内部错误 |
| ServerIsBuzy | 服务器繁忙 |
