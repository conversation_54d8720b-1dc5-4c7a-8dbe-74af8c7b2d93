房费减免

```go
    *reqDto.ReduceRoomAmount
        for _, v := range *orderRoomPlans {
			toUpdateOrderRoomPlan := po.OrderRoomPlan{
				Id:           v.Id,
				OrderNo:      v.OrderNo,
				DiscountRate: reqDto.DiscountRoomRate,
				PayFee:       util.GetItPtr((*v.PayFee) * (*reqDto.DiscountRoomRate) / 100),
			}
			// 对应订单的房费减去打折的差额
			orderNo2TotalAmount[*v.OrderNo] -= (*v.PayFee - *toUpdateOrderRoomPlan.PayFee)
			toUpdateOrderRoomPlans = append(toUpdateOrderRoomPlans, toUpdateOrderRoomPlan)
		}
```
将*reqDto.ReduceRoomAmount
先按总金额


减免21元
总金额: 65.97+23.31=89.28


3 21.99  65.97
7 3.33   23.31
