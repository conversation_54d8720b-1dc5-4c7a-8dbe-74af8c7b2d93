实现 CalculateOrderFee 方法
入参有：logCtx *gin.Context, 
	session *po.Session, 
	orderMain *po.Order, 
	additionalOrders *[]po.Order, 
	orderProducts *[]po.OrderProduct, 
	orderPricePlans *[]po.OrderPricePlan, 
	orderRoomPlans *[]po.OrderRoomPlan, 
	room *po.Room,
	payBills *[]po.PayBill

计算出 totalAmount 和 unpaidAmount, supermarketAmount, roomAmount

计算过程：
	1. 将 orderMain 和 additionalOrders（套餐外点单订单）合并并按 orderPricePlans的(startTime, endTime) 进行分组
	2. 遍历 orderPricePlans
		2.1. 计算在 遍历的orderPricePlan 时间段内的费用 totalAmountInner = roomFee + max(supermarketFee, minimumCharge)
			2.1.1. roomFee = 遍历的orderPricePlan 时间段内的的 sum(orderRoomPlans.PayFee)
			2.1.2. supermarketFee = 遍历的orderPricePlan 时间段内的 orderMain 和 additionalOrders（套餐外点单订单）合并的 sum(orderProducts.PayPrice)
			2.1.3. minimumCharge = 遍历的orderPricePlan 的 minimumCharge
		2.2. 将 totalAmountInner 累加到 totalAmount
		2.3. 将 supermarketFee 累加到 supermarketAmount
		2.4. 将 roomFee 累加到 roomAmount
	3. 遍历 payBills
		3.1. 将 payBills 的 TotalFee 累加为已支付的金额
	4. 返回 totalAmount 和 unpaidAmount = totalAmount - 已支付的金额, supermarketAmount, roomAmount


 @Session.go @Order.go @OrderProduct.go @OrderPricePlan.go @OrderRoomPlan.go @Room.go @PayBill.go  @OrderFeeCalcService.md 中文


我来帮你分析实现 CalculateOrderFee 方法的思路。

我来帮你优化一下需求文档的描述，使其更清晰和结构化。

### CalculateOrderFee 方法实现文档

#### 方法签名
```go
func CalculateOrderFee(
    logCtx *gin.Context,
    session *po.Session,
    orderMain *po.Order,
    additionalOrders *[]po.Order,
    orderProducts *[]po.OrderProduct,
    orderPricePlans *[]po.OrderPricePlan,
    orderRoomPlans *[]po.OrderRoomPlan,
    room *po.Room,
    payBills *[]po.PayBill,
) (totalAmount, unpaidAmount, supermarketAmount, roomAmount int64, err error)
```

#### 功能描述
计算订单的总金额、未支付金额、超市商品金额和房间费用。

#### 计算流程
1. **参数验证**
   - 验证必要参数的有效性

2. **分时段费用计算**
   - 遍历每个 orderPricePlan，计算该时段内：
     ```
     时段总费用(totalAmountInner) = roomFee + max(supermarketFee, minimumCharge)
     其中：
     - roomFee = sum(orderRoomPlans.PayFee)  // 该时段内的房费总和
     - supermarketFee = sum(orderProducts.PayPrice)  // 该时段内商品费用总和（根据商品的Ctime判断是否在时段内）
     - minimumCharge = orderPricePlan.MinimumCharge  // 该时段的最低消费
     ```

3. **总费用汇总**
   - totalAmount = sum(各时段 totalAmountInner)
   - supermarketAmount = sum(各时段 supermarketFee)
   - roomAmount = sum(各时段 roomFee)
   - 已支付金额 = sum(payBills.TotalFee where status == SUCCESS)
   - unpaidAmount = totalAmount - 已支付金额

#### 返回值
- totalAmount：订单总金额
- unpaidAmount：未支付金额
- supermarketAmount：超市商品总金额
- roomAmount：房间费用
- err：错误信息

#### 注意事项
1. 商品费用计算时使用 orderProducts.Ctime 判断是否在时间段内
2. 确保金额计算的准确性，避免精度损失
3. 处理可能的空值情况
4. 考虑并发安全性

需要我继续提供具体的代码实现吗？