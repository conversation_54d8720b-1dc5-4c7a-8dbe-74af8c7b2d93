# ListOrderSessions

分页查询订单场次列表

## 接口信息

- 路由: `/api/order/list`
- 方法: POST
- 请求体: `req.QueryOrderSessionReqDto`
- 响应: `Result[vo.PageVO[[]vo.SessionVO]]`

## 功能说明

该接口用于分页查询订单场次列表，并返回带有房间信息和价格方案的场次数据。

## 请求参数

QueryOrderSessionReqDto:
- PageNum: 页码
- PageSize: 每页大小
- 其他查询条件...

## 响应数据

PageVO[[]vo.SessionVO]:
- PageNum: 当前页码
- PageSize: 每页大小
- Total: 总记录数
- Data: 场次列表
  - SessionVO: 场次信息
  - RoomVO: 关联的房间信息
  - OrderPricePlanVO: 关联的价格方案信息

## 处理流程

1. 绑定并验证请求参数
2. 调用 sessionService 进行分页查询
3. 构造分页响应对象
4. 遍历场次列表:
   - 查询关联的价格方案信息
   - 查询关联的房间信息
5. 转换并组装最终响应数据

## 错误码

- ParamError: 参数错误
- ServerIsBuzy: 服务器繁忙 