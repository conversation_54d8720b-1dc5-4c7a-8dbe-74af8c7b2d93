我会帮你将 AdditionalOrder 方法的文档写入到 `controller/OrderController.additional-order.md` 文件中。以下是中文文档内容：

```markdown:controller/OrderController.additional-order.md
# 点单接口

## 接口说明
该接口用于在已开台的房间中追加商品订单。

## 请求方式
- POST
- 路径: /api/order/additional-order

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roomId | string | 是 | 房间ID |
| venueId | string | 是 | 门店id |
| orderProductVOs | array | 是 | 商品列表 |

### orderProductVOs 商品对象结构
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productId | string | 是 | 商品ID |
| productName | string | 是 | 商品名称 |
| flavors | string | 否 | 口味选择 |
| quantity | number | 是 | 数量 |
| unit | string | 是 | 单位 |
| payPrice | number | 是 | 实付单价 |
| price | number | 是 | 原始单价 |
| totalAmount | number | 是 | 总金额 |
| mark | string | 否 | 备注 |

## 业务流程
1. 验证房间状态，确保房间存在且已开台
2. 检查场次状态，确保场次存在
3. 创建订单商品记录
4. 创建追加订单记录
5. 更新场次费用
   - 如果存在低消要求，需要判断超市费用是否满足低消
   - 更新场次的超市费用和总费用
6. 保存所有数据到数据库

## 响应结果
### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

### 错误响应
```json
{
    "code": 500,
    "message": "错误信息",
    "data": null
}
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 500 | 服务器内部错误 |
| 400 | 参数错误 |

## 注意事项
1. 追加订单会影响场次的总费用和超市费用
2. 如果场次设置了低消，需要考虑低消的影响
3. 追加订单的状态默认为未支付
4. 订单标记(Mark)为"additional"，表示这是一个追加订单
```