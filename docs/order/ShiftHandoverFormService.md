## SaveHandOverGroupByDay

### 功能描述
按天统计并保存交接班数据，计算各类收入和支付方式的汇总金额。根据传入的开始时间和结束时间，将数据按天进行分组统计，每天生成一条 ShiftHandoverForm 记录。

### 参数说明
- `ctx` (*gin.Context): Gin上下文
- `startTime` (int64): 开始时间戳，用于确定统计起始日期
- `endTime` (int64): 结束时间戳，用于确定统计结束日期
- `venueId` (string): 场馆ID
- `payBills` (*[]po.PayBill): 支付账单列表

### 返回值
- `err` (error): 错误信息

### 业务逻辑
1. 时间处理：
   - 根据 startTime 和 endTime 计算需要统计的天数
   - 将 payBills 按照支付时间分配到对应的日期组中
   - 每个日期组的数据将生成一条 ShiftHandoverForm 记录

2. 每天统计营业数据（记录到 ShiftHandoverForm）：
   - 营业应收（BusinessReceivable）：原始费用
   - 营业实收（BusinessActual）：实际支付总额
   - 营业净收（BusinessNet）：暂与营业应收相同
   - 商家优惠（MerchantDiscount）：房费折扣+商品折扣+房费减免+商品减免

3. 每天统计支付方式（记录到 ShiftHandoverForm）：
   - 微信支付（WechatPayAmount）
   - 支付宝支付（AlipayPayAmount）
   - 现金支付（CashPayAmount）
   - 会员卡支付（MemberCardPrincipalAmount）
   - 银行卡支付（BankCardPayAmount）

4. 每天统计其他项（记录到 ShiftHandoverForm）：
   - 冲账应收/实收（WriteOffReceivable/WriteOffActual）
   - 会员卡支付（MemberCardPay）
   - 挂账金额（CreditAmount）
   - 客户批次数（CustomerBatches）：基于会话ID统计

### 数据存储
- 每天的统计数据生成一条 ShiftHandoverForm 记录
- 一个 ShiftHandoverForm 记录包含该天的所有统计数据
- 所有 ShiftHandoverForm 记录最终批量保存到数据库

### 注意事项
- 退款订单的金额会以负数计入相应统计项
- 所有金额计算都通过 `getCorrectFee` 方法处理，确保退款金额正确计入
- 每天的统计数据会单独保存一条记录
- 跨天的订单会根据支付时间归属到对应的统计日期中