# Domain Subject 领域主体设计规范

## 1. 领域层分层设计

### 1.1 整体分层结构
```
domain/subject/
├── base/                 # 基础抽象层
│   ├── model/           # 基础模型定义
│   ├── repository/      # 基础仓储接口
│   └── service/         # 基础服务接口
└── business/            # 业务实现层
    └── {业务领域}/      # 具体业务领域实现
        ├── model/       # 业务领域模型
        ├── repository/  # 业务仓储接口
        └── service/     # 业务领域服务
```

### 1.2 标准调用链路

#### a) 理论调用链路
```mermaid
sequenceDiagram
    participant App as ApplicationService
    participant DS as DomainService
    participant R as Repository
    participant M as Model
    
    App->>+DS: 业务操作请求
    DS->>+R: 获取领域对象
    R-->>-DS: 领域对象实例
    
    DS->>+M: 验证领域对象
    M-->>-DS: 验证结果
    
    alt 验证通过
        DS->>+M: 执行业务规则
        M-->>-DS: 执行结果
        
        alt 规则满足
            DS->>+R: 持久化更新
            R-->>-DS: 更新结果
            DS-->>App: 成功响应
        else 规则不满足
            DS-->>App: 业务错误
        end
    else 验证失败
        DS-->>App: 验证错误
    end
```

#### b) 实际调用链路
```mermaid
sequenceDiagram
    participant App as ApplicationService
    participant DS as DomainService
    participant R as Repository Interface
    participant P as Proxy
    participant DB as Database Service
    
    App->>+DS: GetArea(id)
    DS->>+R: FindByID(id)
    R->>+P: FindByID(id)
    
    Note over P: 转换上下文和参数
    
    P->>+DB: FindAreaById(ginCtx, id)
    DB-->>-P: Area PO
    
    Note over P: 转换返回结果
    
    P-->>-R: Area Domain Model
    R-->>-DS: Area Domain Model
    DS-->>-App: Area Domain Model
```

#### c) 实际代码示例

1. 应用层调用
```go
// RoomOpenViewProcess 房间开台视图流程
func (p *RoomOpenViewProcess) fetchAreaInfo(ctx context.Context, processCtx *engineProcess.ProcessContext, reqDto dto.QueryRoomStageReqDto) error {
    // 通过领域服务调用
    area, err := p.areaService.GetArea(ctx, *reqDto.AreaId)
    if err != nil {
        return fmt.Errorf("获取区域信息失败: %w", err)
    }
    // ... 处理返回结果
}
```

2. 领域服务层
```go
// AreaService 区域服务实现
type AreaService struct {
    repository Repository  // 依赖仓储接口
}

func (s *AreaService) GetArea(ctx context.Context, id string) (Area, error) {
    // 通过仓储接口查询
    return s.repository.FindByID(ctx, id)
}
```

3. 代理层（基础设施适配）
```go
// AreaProxy 代理实现
type AreaProxy struct {
    *BaseSubjectProxy
    legacyService *impl.AreaService  // 实际的基础设施服务
}

func (p *AreaProxy) FindByID(ctx context.Context, id string) (*po.Area, error) {
    // 转换上下文
    ginCtx, err := p.ConvertContext(ctx)
    if err != nil {
        return nil, err
    }
    // 调用实际的数据库服务
    return p.legacyService.FindAreaById(ginCtx, id)
}
```

4. 依赖注入实现
```go
// 在容器中注册仓储和服务
func (c *Container) initDomainServices() {
    // 1. 初始化仓储实现
    areaRepo := proxy.NewAreaProxy()
    
    // 2. 注入仓储到领域服务
    c.areaService = areaService.NewService(areaRepo)
    
    // 3. 注入领域服务到应用服务
    c.roomApplicationService = service.NewRoomApplicationService(
        c.areaService,
        // ... 其他依赖
    )
}
```

5. 调用链路说明
- 应用层：负责业务流程编排，通过领域服务访问数据
- 领域服务：封装领域逻辑，依赖仓储接口
- 仓储接口：定义数据访问契约，不依赖具体实现
- 代理层：实现仓储接口，适配基础设施服务
- 基础设施：提供实际的数据访问能力

6. 设计优势
- 清晰的职责分离
- 领域逻辑的纯粹性
- 基础设施的可替换性
- 依赖关系的清晰控制
- 便于单元测试（可Mock任何层级）

### 1.3 分层职责定义

#### 1.3.1 领域模型层(Model)
- 定义领域实体和值对象
- 封装业务属性和行为
- 实现业务规则和约束
- 保证对象不可变性

#### 1.3.2 仓储层(Repository)
- 提供数据访问抽象
- 实现持久化操作
- 维护数据一致性
- 处理并发控制

#### 1.3.3 领域服务层(Service)
- 编排领域操作
- 协调多个领域对象
- 实现复杂业务规则
- 处理事务边界

## 2. 领域层标准设计

### 2.1 领域模型设计规范

#### 2.1.1 基础模型接口
```go
// Subject 基础主体接口
type Subject interface {
    GetID() string
    GetType() string
    GetState() int
    GetVersion() int
    Validate() error
    Clone() Subject
}
```

#### 2.1.2 业务模型接口
```go
// BusinessSubject 业务主体接口
type BusinessSubject interface {
    Subject
    // 扩展业务属性方法
    GetBusinessAttributes() map[string]interface{}
    // 扩展业务行为方法
    ExecuteBusinessAction(action string, params map[string]interface{}) error
}
```

### 2.2 仓储层设计规范

#### 2.2.1 基础仓储接口
```go
// Repository 基础仓储接口
type Repository[T Subject] interface {
    FindByID(ctx context.Context, id string) (T, error)
    FindByCondition(ctx context.Context, condition map[string]interface{}) ([]T, error)
    Save(ctx context.Context, subject T) error
    Delete(ctx context.Context, id string) error
}
```

#### 2.2.2 仓储实现原理

##### a) 依赖倒置原则(DIP)的体现
1. 接口定义与实现分离
```
domain/subject/                    # 领域层
├── base/
│   └── repository/
│       └── repository.go         # 定义仓储接口(抽象)
└── business/
    └── venue/
        └── repository/
            └── repository.go     # 定义业务仓储接口(抽象)

infrastructure/                    # 基础设施层
└── persistence/
    └── mysql/
        ├── base_repository.go    # 实现基础仓储接口
        └── venue_repository.go   # 实现业务仓储接口
```

2. 依赖关系反转
```go
// 1. 领域层定义接口（高层模块）
type Repository[T Subject] interface {
    FindByID(ctx context.Context, id string) (T, error)
    // ... 其他方法
}

// 2. 基础设施层实现接口（低层模块）
type MySQLRepository[T Subject] struct {
    db *gorm.DB
}

// 3. 依赖注入方式使用
type VenueService struct {
    repository Repository[Venue]  // 依赖抽象接口，而不是具体实现
}

func NewVenueService(repo Repository[Venue]) *VenueService {
    return &VenueService{repository: repo}
}
```

3. 依赖倒置的优势
- 领域层关注业务逻辑，不关注数据存储
- 领域层不依赖于具体的数据库实现
- 可以轻松替换不同的存储实现（MySQL/MongoDB/Redis）
- 便于进行单元测试（可以使用Mock实现）
- 符合"面向接口编程"原则

##### b) 基础设施支持
仓储接口能够正常工作，依赖于基础设施层提供的具体实现：

```mermaid
sequenceDiagram
    participant S as Service
    participant R as Repository Interface
    participant CR as CachedRepository
    participant MR as MySQLRepository
    participant C as Cache
    participant DB as Database

    S->>+R: FindByID(id)
    R->>+CR: FindByID(id)
    CR->>+C: Get(cacheKey)
    
    alt Cache Hit
        C-->>-CR: Cached Data
        CR-->>R: Domain Object
    else Cache Miss
        C-->>CR: Cache Miss
        CR->>+MR: FindByID(id)
        MR->>+DB: Query
        DB-->>-MR: Raw Data
        MR-->>CR: Domain Object
        CR->>C: Set(cacheKey, data)
        CR-->>-R: Domain Object
    end
    
    R-->>-S: Domain Object
```

1. 持久化实现
```go
// MySQLRepository MySQL仓储实现
type MySQLRepository[T Subject] struct {
    db *gorm.DB
    // 其他依赖...
}

// 实现Repository接口方法
func (r *MySQLRepository[T]) FindByID(ctx context.Context, id string) (T, error) {
    var entity T
    // 实际的数据库操作
    result := r.db.WithContext(ctx).First(&entity, "id = ?", id)
    return entity, result.Error
}
```

2. 事务管理
```go
// 事务管理示例
func (r *MySQLRepository[T]) WithTransaction(tx *gorm.DB) Repository[T] {
    return &MySQLRepository[T]{
        db: tx,
    }
}
```

3. 缓存支持
```go
// CachedRepository 带缓存的仓储实现
type CachedRepository[T Subject] struct {
    repository Repository[T]  // 实际的仓储实现
    cache     cache.Cache    // 缓存实现
}
```

##### c) 仓储注册机制
通过依赖注入容器，将基础设施层的具体实现注入到领域层：

```mermaid
sequenceDiagram
    participant C as Container
    participant VS as VenueService
    participant VR as VenueRepository
    participant CR as CachedRepository
    participant MR as MySQLRepository
    participant DB as Database
    participant Cache as Cache

    Note over C: 初始化阶段
    
    C->>+DB: 创建数据库连接
    C->>+Cache: 创建缓存客户端
    
    C->>+MR: 创建MySQL仓储
    Note over MR: 注入数据库连接
    
    C->>+CR: 创建缓存仓储
    Note over CR: 注入MySQL仓储和缓存
    
    C->>+VR: 注册仓储接口实现
    Note over VR: 绑定缓存仓储实现
    
    C->>+VS: 创建领域服务
    Note over VS: 注入仓储接口
    
    Note over C: 完成依赖注入
```

```go
// 仓储注册示例
func RegisterRepositories(container *dig.Container) error {
    return container.Provide(func(
        db *gorm.DB,
        cache cache.Cache,
    ) Repository[Venue] {
        return &CachedRepository[Venue]{
            repository: &MySQLRepository[Venue]{db: db},
            cache:     cache,
        }
    })
}
```

#### 2.2.3 业务仓储接口

业务仓储接口在基础仓储接口的基础上，提供更具体的业务查询能力：

```mermaid
sequenceDiagram
    participant AS as ApplicationService
    participant DS as DomainService
    participant BR as BusinessRepository
    participant BM as BusinessModel
    participant CR as CachedRepository
    participant DB as Database

    AS->>+DS: 业务操作请求
    DS->>+BR: FindByBusinessType(type)
    
    BR->>+CR: 执行业务查询
    CR->>+DB: SELECT * FROM table WHERE business_type = ?
    DB-->>-CR: Raw Data
    
    CR->>CR: 转换为领域模型
    CR-->>-BR: Business Models
    
    BR->>+BM: 业务规则验证
    BM-->>-BR: 验证结果
    
    BR-->>-DS: 业务对象列表
    DS-->>-AS: 处理结果
```

##### a) 设计原则
1. 继承基础仓储接口的通用能力
2. 扩展特定业务场景的查询方法
3. 方法名应体现业务语义
4. 参数和返回值应该使用领域模型

##### b) 接口示例
```go
// VenueRepository 场馆仓储接口
type VenueRepository interface {
    Repository[Venue]  // 继承基础仓储接口

    // 业务查询方法
    FindByName(ctx context.Context, name string) ([]Venue, error)
    FindByCity(ctx context.Context, cityCode string) ([]Venue, error)
    FindByBusinessType(ctx context.Context, businessType int) ([]Venue, error)
    FindByOperationStatus(ctx context.Context, status int) ([]Venue, error)
    
    // 复杂业务查询
    FindAvailableVenues(ctx context.Context, params VenueSearchParams) ([]Venue, error)
}

// AreaRepository 区域仓储接口
type AreaRepository interface {
    Repository[Area]  // 继承基础仓储接口

    // 业务查询方法
    FindByVenueID(ctx context.Context, venueID string) ([]Area, error)
    FindByFloor(ctx context.Context, venueID string, floor int) ([]Area, error)
    FindByType(ctx context.Context, areaType int) ([]Area, error)
}
```

##### c) 实现示例
```go
// VenueRepositoryImpl 场馆仓储实现
type VenueRepositoryImpl struct {
    *MySQLRepository[Venue]
}

// 实现业务查询方法
func (r *VenueRepositoryImpl) FindByName(ctx context.Context, name string) ([]Venue, error) {
    var venues []Venue
    result := r.db.WithContext(ctx).
        Where("name LIKE ?", "%"+name+"%").
        Find(&venues)
    return venues, result.Error
}

func (r *VenueRepositoryImpl) FindAvailableVenues(ctx context.Context, params VenueSearchParams) ([]Venue, error) {
    var venues []Venue
    query := r.db.WithContext(ctx)
    
    // 构建复杂业务查询
    if params.CityCode != "" {
        query = query.Where("city_code = ?", params.CityCode)
    }
    if params.BusinessType > 0 {
        query = query.Where("business_type = ?", params.BusinessType)
    }
    if params.MinCapacity > 0 {
        query = query.Where("capacity >= ?", params.MinCapacity)
    }
    // ... 其他业务条件

    result := query.Find(&venues)
    return venues, result.Error
}
```

##### d) 设计优势
1. 分层清晰
   - 基础仓储：提供通用CRUD能力
   - 业务仓储：提供特定业务查询能力

2. 业务语义明确
   - 方法名反映业务概念
   - 参数和返回值符合领域模型

3. 查询能力分级
   - 简单查询：直接使用基础仓储方法
   - 业务查询：使用特定业务方法
   - 复杂查询：组合多个条件的业务方法

4. 可维护性好
   - 业务逻辑内聚
   - 查询逻辑复用
   - 接口语义清晰

