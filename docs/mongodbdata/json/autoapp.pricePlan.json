[{"_id": "66f0d7f2562cc84e8d2a1717", "_t": "PricePlan", "name": "房费抵饮料果盘", "roomType": "[\"66dfa2f2155ffb38a019c9ba\",\"66e2a80a3a98f63e93793ac9\",\"66deac8f07429f2e064f609a\"]", "consumptionMode": "timeChargeDiscount", "hasMinimumCharge": false, "isEnabled": true, "supportsPoints": false, "timeType": "week", "weeks": "[2,3,4]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "isAreaSpecified": false, "isExcessIncluded": false, "discountMode": "roomChargeForGoods", "discountDuration": 200, "giftDuration": 0, "roomChargeGoods": "[\"饮料\",\"果盘\"]", "maxDeductibleAmount": {"$numberDecimal": "20"}, "minimumConsumption": {"$numberDecimal": "0"}, "ctime": {"$numberLong": "0"}, "utime": {"$numberLong": "1728881678658"}, "state": 0, "version": 0}, {"_id": "66f0db66562cc84e8d2a1719", "_t": "PricePlan", "name": "test3", "roomType": "[\"66dfa2f2155ffb38a019c9ba\",\"66e2a80a3a98f63e93793ac9\"]", "consumptionMode": "timeChargeDiscount", "hasMinimumCharge": false, "isEnabled": true, "supportsPoints": false, "timeType": "week", "weeks": "[3,4]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "isAreaSpecified": false, "isExcessIncluded": false, "discountMode": "goodsForRoomCharge", "discountDuration": 200, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberDecimal": "0"}, "minimumConsumption": {"$numberDecimal": "30"}, "ctime": {"$numberLong": "0"}, "utime": {"$numberLong": "0"}, "state": 0, "version": 0}, {"_id": "66f0e800562cc84e8d2a171a", "_t": "PricePlan", "name": "工作日包夜", "roomType": "[\"66deac8f07429f2e064f609a\",\"66dfa2f2155ffb38a019c9ba\",\"66e2a80a3a98f63e93793ac9\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "buyout", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":400,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":500,\"minimumCharge\":600}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":500,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":600,\"minimumCharge\":600}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":550,\"minimumCharge\":550}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[1,2,3,4]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "22:00", "hourMinuteEnd": "06:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "duration": 240, "isAreaSpecified": true, "selectedAreas": "[\"test\",\"东区\",\"西区\"]", "baseRoomFee": {"$numberLong": "300"}, "advanceDisableDuration": 10, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "0"}, "groupBuyFee": {"$numberLong": "0"}, "activityFee": {"$numberLong": "0"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "\"\"", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "statisticsCategory": "", "planPic": "", "ctime": {"$numberLong": "0"}, "utime": {"$numberLong": "1729736671413"}, "state": 0, "version": 0}, {"_id": "66f0e8c8562cc84e8d2a171b", "_t": "PricePlan", "name": "工作日小包", "roomType": "[\"66dfa2f2155ffb38a019c9ba\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "buyout", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":400,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":500,\"minimumCharge\":600}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":500,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":600,\"minimumCharge\":600}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":550,\"minimumCharge\":550}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[1,2,3,4]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "duration": 300, "isAreaSpecified": true, "selectedAreas": "[\"东区\",\"test\"]", "baseRoomFee": {"$numberLong": "350"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "0"}, "groupBuyFee": {"$numberLong": "0"}, "activityFee": {"$numberLong": "0"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "\"\"", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "statisticsCategory": "", "planPic": "", "ctime": {"$numberLong": "0"}, "utime": {"$numberLong": "1729741172002"}, "state": 0, "version": 0}, {"_id": "670ca506aec75a21bd096e84", "_t": "PricePlan", "name": "10月全天", "roomType": "[\"66deac8f07429f2e064f609a\",\"66dfa2f2155ffb38a019c9ba\",\"66e2a80a3a98f63e93793ac9\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "buyout", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":400,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":500,\"minimumCharge\":600}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":500,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":600,\"minimumCharge\":600}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":550,\"minimumCharge\":550}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "date", "weeks": "[]", "dayStart": "2024-10-01", "dayEnd": "2024-10-31", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "duration": 300, "isAreaSpecified": true, "selectedAreas": "[\"东区\",\"西区\"]", "baseRoomFee": {"$numberLong": "400"}, "advanceDisableDuration": 20, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "0"}, "groupBuyFee": {"$numberLong": "0"}, "activityFee": {"$numberLong": "0"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "\"\"", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "statisticsCategory": "", "planPic": "", "ctime": {"$numberLong": "**********"}, "utime": {"$numberLong": "1729741183306"}, "state": 0, "version": 0}, {"_id": "670ca557aec75a21bd096e85", "_t": "PricePlan", "name": "周五", "roomType": "[\"66deac8f07429f2e064f609a\",\"66dfa2f2155ffb38a019c9ba\",\"66e2a80a3a98f63e93793ac9\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "buyout", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":400,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":500,\"minimumCharge\":600}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":500,\"minimumCharge\":500},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":600,\"minimumCharge\":600}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":550,\"minimumCharge\":550}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[5]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "duration": 300, "isAreaSpecified": true, "selectedAreas": "[\"东区\",\"西区\"]", "baseRoomFee": {"$numberLong": "500"}, "advanceDisableDuration": 20, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "0"}, "groupBuyFee": {"$numberLong": "0"}, "activityFee": {"$numberLong": "0"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "\"\"", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "statisticsCategory": "", "planPic": "", "ctime": {"$numberLong": "**********"}, "utime": {"$numberLong": "1729741194738"}, "state": 0, "version": 0}, {"_id": "670cb577aec75a21bd096e87", "_t": "PricePlan", "name": "周末大包", "roomType": "[\"66deac8f07429f2e064f609a\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "buyout", "hasMinimumCharge": false, "minimumCharge": {"$numberDecimal": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberDecimal": "0"}, "memberDiscount": {"$numberDecimal": "0"}, "areaPrices": "{}", "areaMemberPrices": "{}", "holidayPrices": "{}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[6,7]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "planProducts": "\"\"", "duration": 300, "isAreaSpecified": true, "selectedAreas": "[\"东区\",\"西区\"]", "baseRoomFee": {"$numberDecimal": "800"}, "advanceDisableDuration": 20, "isExcessIncluded": false, "birthdayFee": {"$numberDecimal": "0"}, "groupBuyFee": {"$numberDecimal": "0"}, "activityFee": {"$numberDecimal": "0"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "\"\"", "maxDeductibleAmount": {"$numberDecimal": "0"}, "minimumConsumption": {"$numberDecimal": "0"}, "statisticsCategory": "", "planPic": "", "ctime": {"$numberLong": "**********"}, "utime": {"$numberLong": "1728886275494"}, "state": 0, "version": 0}, {"_id": "670fa2fa482f6338b37eb2bc", "_t": "PricePlan", "name": "", "roomType": "[\"66dfa2f2155ffb38a019c9ba\",\"66e2a80a3a98f63e93793ac9\",\"66f632b66c5ad902293cce51\",\"66deac8f07429f2e064f609a\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "timeCharge", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":200,\"birthdayFee\":180,\"activityFee\":180,\"groupBuyFee\":180},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":220,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":300,\"birthdayFee\":260,\"activityFee\":260,\"groupBuyFee\":260},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":260,\"birthdayFee\":230,\"activityFee\":230,\"groupBuyFee\":230}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":230,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[1,2,3,4]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "17:00", "hourMinuteEnd": "22:30", "isAreaSpecified": false, "selectedAreas": "[]", "baseRoomFee": {"$numberLong": "200"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "180"}, "groupBuyFee": {"$numberLong": "180"}, "activityFee": {"$numberLong": "180"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "ctime": {"$numberLong": "1729078010"}, "utime": {"$numberLong": "1729741204835"}, "state": 0, "version": 0, "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]"}, {"_id": "671084fc482f6338b37eb2bd", "_t": "PricePlan", "name": "", "roomType": "[\"66dfa2f2155ffb38a019c9ba\",\"66deac8f07429f2e064f609a\",\"66e2a80a3a98f63e93793ac9\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "timeCharge", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":200,\"birthdayFee\":180,\"activityFee\":180,\"groupBuyFee\":180},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":220,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":300,\"birthdayFee\":260,\"activityFee\":260,\"groupBuyFee\":260},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":260,\"birthdayFee\":230,\"activityFee\":230,\"groupBuyFee\":230}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":230,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}", "isEnabled": true, "supportsPoints": false, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[1,2,3,4]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "22:30", "hourMinuteEnd": "06:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "isAreaSpecified": true, "selectedAreas": "[\"东区\",\"西区\"]", "baseRoomFee": {"$numberLong": "100"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "80"}, "groupBuyFee": {"$numberLong": "80"}, "activityFee": {"$numberLong": "80"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "ctime": {"$numberLong": "1729135868"}, "utime": {"$numberLong": "1729742661368"}, "state": 0, "version": 0}, {"_id": "67108539482f6338b37eb2be", "_t": "PricePlan", "name": "", "roomType": "[\"66dfa2f2155ffb38a019c9ba\",\"66deac8f07429f2e064f609a\",\"66e2a80a3a98f63e93793ac9\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "timeCharge", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":200,\"birthdayFee\":180,\"activityFee\":180,\"groupBuyFee\":180},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":220,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":300,\"birthdayFee\":260,\"activityFee\":260,\"groupBuyFee\":260},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":260,\"birthdayFee\":230,\"activityFee\":230,\"groupBuyFee\":230}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":230,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}", "isEnabled": true, "supportsPoints": false, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[1,3,2,4,5]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "10:00", "hourMinuteEnd": "15:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "isAreaSpecified": false, "selectedAreas": "[]", "baseRoomFee": {"$numberLong": "120"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "100"}, "groupBuyFee": {"$numberLong": "100"}, "activityFee": {"$numberLong": "100"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "ctime": {"$numberLong": "1729135929"}, "utime": {"$numberLong": "1729742675171"}, "state": 0, "version": 0}, {"_id": "671085da482f6338b37eb2bf", "_t": "PricePlan", "name": "", "roomType": "[\"66e2a80a3a98f63e93793ac9\",\"66dfa2f2155ffb38a019c9ba\",\"66deac8f07429f2e064f609a\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "timeCharge", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":200,\"birthdayFee\":180,\"activityFee\":180,\"groupBuyFee\":180},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":220,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":300,\"birthdayFee\":260,\"activityFee\":260,\"groupBuyFee\":260},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":260,\"birthdayFee\":230,\"activityFee\":230,\"groupBuyFee\":230}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":230,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[5,6,7]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "17:00", "hourMinuteEnd": "06:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "isAreaSpecified": false, "selectedAreas": "[]", "baseRoomFee": {"$numberLong": "220"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "200"}, "groupBuyFee": {"$numberLong": "200"}, "activityFee": {"$numberLong": "200"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "ctime": {"$numberLong": "1729136090"}, "utime": {"$numberLong": "1729742691786"}, "state": 0, "version": 0}, {"_id": "67108647482f6338b37eb2c0", "_t": "PricePlan", "name": "", "roomType": "[\"66e2a80a3a98f63e93793ac9\",\"66deac8f07429f2e064f609a\",\"66dfa2f2155ffb38a019c9ba\",\"66f632b66c5ad902293cce51\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "timeCharge", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":200,\"birthdayFee\":180,\"activityFee\":180,\"groupBuyFee\":180},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":220,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":300,\"birthdayFee\":260,\"activityFee\":260,\"groupBuyFee\":260},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":260,\"birthdayFee\":230,\"activityFee\":230,\"groupBuyFee\":230}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":230,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "week", "weeks": "[6,7]", "dayStart": "", "dayEnd": "", "hourMinuteStart": "10:00", "hourMinuteEnd": "15:00", "planProducts": "[{\"billType\":\"paid\",\"optionType\":\"\",\"products\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"type\":\"standard\",\"count\":1}]},{\"billType\":\"paid\",\"optionType\":\"by_plan\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"type\":\"standard\",\"count\":2}],\"optionCount\":2},{\"billType\":\"free\",\"optionType\":\"\",\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":3}]},{\"billType\":\"free\",\"optionType\":\"by_count\",\"products\":[{\"id\":\"6705dad81d0bfc61e962b632\",\"type\":\"standard\",\"count\":1},{\"id\":\"6705ddb3fcd9ac2de9108528\",\"type\":\"standard\",\"count\":1}],\"optionCount\":2}]", "isAreaSpecified": false, "selectedAreas": "[]", "baseRoomFee": {"$numberLong": "180"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "160"}, "groupBuyFee": {"$numberLong": "160"}, "activityFee": {"$numberLong": "160"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "ctime": {"$numberLong": "1729136199"}, "utime": {"$numberLong": "1729742703089"}, "state": 0, "version": 0}, {"_id": "671086c7482f6338b37eb2c1", "_t": "PricePlan", "name": "", "roomType": "[\"66e2a80a3a98f63e93793ac9\"]", "distributionChannel": "[\"收银机\",\"线上自助\",\"线上预订\",\"移动点单\"]", "consumptionMode": "timeCharge", "hasMinimumCharge": false, "minimumCharge": {"$numberLong": "0"}, "exampleProducts": "", "optionalProducts": "", "freeProducts": "", "memberPrice": {"$numberLong": "0"}, "memberDiscount": {"$numberLong": "0"}, "areaPrices": "[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":200,\"birthdayFee\":180,\"activityFee\":180,\"groupBuyFee\":180},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":220,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}]", "areaMemberPrices": "{}", "holidayPrices": "{\"areaPrices\":[{\"id\":\"66e145d5155ffb38a019c9bb\",\"baseRoomFee\":300,\"birthdayFee\":260,\"activityFee\":260,\"groupBuyFee\":260},{\"id\":\"670ca275aec75a21bd096e7a\",\"baseRoomFee\":260,\"birthdayFee\":230,\"activityFee\":230,\"groupBuyFee\":230}],\"validHolidayIDs\":[\"670f6e521faea863cb8a9530\",\"670f6f347381c06c93d1b312\"],\"baseRoomFee\":230,\"birthdayFee\":200,\"activityFee\":200,\"groupBuyFee\":200}", "isEnabled": true, "supportsPoints": true, "buyGiftPlan": "{}", "timeType": "date", "weeks": "[]", "dayStart": "2024-10-15", "dayEnd": "2024-10-30", "hourMinuteStart": "06:00", "hourMinuteEnd": "06:00", "planProducts": "{\"standardProducts\":[{\"id\":\"6705dd031d0bfc61e962b633\",\"count\":2},{\"id\":\"670d3f3acb63880601d780c6\",\"count\":1}],\"optionalProducts\":{\"type\":\"ByPlan\",\"count\":2,\"products\":[{\"id\":\"66f90073b9510b08c2124252\",\"count\":1},{\"id\":\"6705dd031d0bfc61e962b633\",\"count\":2}]},\"freeProducts\":[{\"id\":\"66f90073b9510b08c2124252\",\"count\":1},{\"id\":\"6705dad81d0bfc61e962b632\",\"count\":3}],\"optionalFreeProducts\":{\"type\":\"ByCount\",\"count\":2,\"productIds\":[\"6705dad81d0bfc61e962b632\",\"6705ddb3fcd9ac2de9108528\"]}}", "isAreaSpecified": false, "selectedAreas": "[]", "baseRoomFee": {"$numberLong": "80"}, "advanceDisableDuration": 0, "isExcessIncluded": false, "birthdayFee": {"$numberLong": "60"}, "groupBuyFee": {"$numberLong": "60"}, "activityFee": {"$numberLong": "60"}, "discountMode": "", "discountDuration": 0, "giftDuration": 0, "roomChargeGoods": "[]", "maxDeductibleAmount": {"$numberLong": "0"}, "minimumConsumption": {"$numberLong": "0"}, "ctime": {"$numberLong": "1729136327"}, "utime": {"$numberLong": "1729136327"}, "state": 0, "version": 0}]