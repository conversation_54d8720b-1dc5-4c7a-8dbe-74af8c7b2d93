[{"_id": "670632c26e923d3b20387ebf", "_t": "MemberCardTemplate", "name": "黄金会员卡", "cardFee": 0, "replacementFee": 0, "renewalFee": 0, "minimumRechargeAmount": 0, "roomDiscount": 0, "productDiscount": 0, "consumptionPointsRatio": 0, "rechargePointsRatio": 0, "ctime": {"$numberLong": "0"}, "utime": {"$numberLong": "0"}, "state": 0, "version": 0}, {"_id": "67063301d1e0612bad22f542", "_t": "MemberCardTemplate", "name": "黄金会员卡", "logo": "https://example.com/gold-card-logo.png", "balanceUsageScope": ["客房消费", "餐饮消费", "SPA服务"], "distributionChannels": ["线下门店", "官方网站", "移动应用"], "validityPeriod": "365天", "cardFee": 100, "replacementFee": 50, "renewalFee": 80, "minimumRechargeAmount": 500, "roomDiscount": 0.8500000238418579, "productDiscount": 0.8999999761581421, "consumptionPointsRatio": 0.009999999776482582, "rechargePointsRatio": 0.004999999888241291, "upgradeConditions": "年消费满10000元", "downgradeConditions": "连续两年年消费低于5000元", "birthdayPerks": ["生日当天房费5折", "生日蛋糕一个"], "signupUpgradePerks": ["免费升级房型一次", "欢迎饮品"], "monthlyCoupons": ["50元代金券", "SPA护理9折券"], "consumptionPeriods": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"], "paymentRestrictions": ["不可使用信用卡支付年费"], "ctime": {"$numberLong": "0"}, "utime": {"$numberLong": "0"}, "state": 0, "version": 0}]