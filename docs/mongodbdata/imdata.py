import json
import mysql.connector
from datetime import datetime

basepath="/Users/<USER>/sbin/app/work/erp-lt-vv"

def connect_mysql():
    return mysql.connector.connect(
        host="**************",
        user="root",
        password="ktvsky5166",
        database="autoapp"
    )

def serialize_list(data):
    if isinstance(data, list):
        return json.dumps(data,ensure_ascii=False)
    return data

def import_employees():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.employee.json', 'r', encoding='utf-8') as file:
        employees = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO employee (
        id, name, employee_number, phone, password, type,
        permissions, employee_group, permission_role, marketing_role,
        employee_card_id, wechat_binding, can_manage_groups,
        sales_performance, can_view_commission_performance,
        has_mobile_ordering_permission, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s
    )
    """

    try:
        for emp in employees:
            values = (
                emp['_id'],
                emp['name'],
                emp['employeeNumber'],
                emp['phone'],
                emp['password'],
                emp['type'],
                serialize_list(emp['permissions']),
                emp['employeeGroup'],
                emp['permissionRole'],
                emp['marketingRole'],
                emp['employeeCardId'],
                emp['wechatBinding'],
                emp['canManageGroups'],
                emp['salesPerformance'],
                emp['canViewCommissionPerformance'],
                int(emp['ctime']['$numberLong']) // 1000,  # Convert to seconds
                int(emp['utime']['$numberLong']) // 1000,  # Convert to seconds
                emp['state'],
                emp['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(employees)} employees")

    except Exception as e:
        print(f"Error importing data: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_flavors():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.flavor.json', 'r', encoding='utf-8') as file:
        flavors = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO flavor (
        id, venue_id, name, sort_num, description,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for flavor in flavors:
            values = (
                flavor['_id'],
                None,  # venue_id is not in JSON but exists in schema
                flavor['name'],
                flavor['sortNum'],
                flavor['description'],
                int(flavor['ctime']['$numberLong']) // 1000,  # Convert to seconds
                int(flavor['utime']['$numberLong']) // 1000,  # Convert to seconds
                flavor['state'],
                flavor['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(flavors)} flavors")

    except Exception as e:
        print(f"Error importing flavors: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_gift_groups():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.giftGroup.json', 'r', encoding='utf-8') as file:
        gift_groups = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO gift_group (
        id, name, products, is_displayed,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for group in gift_groups:
            # Note: is_displayed is not in the JSON, setting default to True since these are active records
            values = (
                group['_id'],
                group['name'],
                group['products'],  # Already a JSON string in the source data
                True,  # Default value for is_displayed
                int(group['ctime']['$numberLong']) // 1000,  # Convert to seconds
                int(group['utime']['$numberLong']) // 1000,  # Convert to seconds
                group['state'],
                group['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(gift_groups)} gift groups")

    except Exception as e:
        print(f"Error importing gift groups: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_holidays():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.holiday.json', 'r', encoding='utf-8') as file:
        holidays = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO holiday (
        id, name, date, type, venue_id,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for holiday in holidays:
            values = (
                holiday['_id'],
                holiday['name'],
                holiday['date'],
                holiday['type'],
                holiday['venueId'],
                int(holiday['ctime']['$numberLong']) // 1000,  # Convert to seconds
                int(holiday['utime']['$numberLong']) // 1000,  # Convert to seconds
                holiday['state'],
                holiday['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(holidays)} holidays")

    except Exception as e:
        print(f"Error importing holidays: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_member_card_templates():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.memberCardTemplate.json', 'r', encoding='utf-8') as file:
        templates = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO member_card_template (
        id, name, logo, balance_usage_scope, distribution_channels,
        validity_period, card_fee, replacement_fee, renewal_fee,
        minimum_recharge_amount, room_discount, product_discount,
        consumption_points_ratio, recharge_points_ratio,
        upgrade_conditions, downgrade_conditions, birthday_perks,
        signup_upgrade_perks, monthly_coupons, consumption_periods,
        payment_restrictions, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for template in templates:
            values = (
                template['_id'],
                template['name'],
                template.get('logo', ''),  # Using get() with default for optional fields
                serialize_list(template.get('balanceUsageScope', [])),
                serialize_list(template.get('distributionChannels', [])),
                template.get('validityPeriod', ''),
                template.get('cardFee', 0),
                template.get('replacementFee', 0),
                template.get('renewalFee', 0),
                template.get('minimumRechargeAmount', 0),
                float(template.get('roomDiscount', 0)),
                float(template.get('productDiscount', 0)),
                float(template.get('consumptionPointsRatio', 0)),
                float(template.get('rechargePointsRatio', 0)),
                template.get('upgradeConditions', ''),
                template.get('downgradeConditions', ''),
                serialize_list(template.get('birthdayPerks', [])),
                serialize_list(template.get('signupUpgradePerks', [])),
                serialize_list(template.get('monthlyCoupons', [])),
                serialize_list(template.get('consumptionPeriods', [])),
                serialize_list(template.get('paymentRestrictions', [])),
                int(template['ctime']['$numberLong']) // 1000,  # Convert to seconds
                int(template['utime']['$numberLong']) // 1000,  # Convert to seconds
                template['state'],
                template['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(templates)} member card templates")

    except Exception as e:
        print(f"Error importing member card templates: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_orders():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.order.json', 'r', encoding='utf-8') as file:
        orders = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO `order` (
        id, session_id, venue_id, room_id, order_no,
        employee_id, member_id, start_time, end_time,
        total_amount, minimum_charge, tag, status,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for order in orders:
            values = (
                order['_id'],
                order['sessionId'],
                order['venueId'],
                order['roomId'],
                order['orderNo'],
                order['employeeId'],
                order['memberId'],
                int(order['startTime']['$numberLong']),  # Already in seconds
                int(order['endTime']['$numberLong']),    # Already in seconds
                int(order['totalAmount']['$numberLong']),
                int(order['minimumCharge']['$numberLong']),
                order['tag'],
                order['status'],
                int(order['ctime']['$numberLong']),      # Already in seconds
                int(order['utime']['$numberLong']),      # Already in seconds
                order['state'],
                order['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(orders)} orders")

    except Exception as e:
        print(f"Error importing orders: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_order_price_plans():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.orderPricePlan.json', 'r', encoding='utf-8') as file:
        price_plans = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO order_price_plan (
        id, session_id, order_no, price_plan_id, price_plan_name,
        consumption_mode, selected_area_id, selected_room_type_id,
        buy_minute, time_charge_type, time_charge_mode, minimum_charge,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for plan in price_plans:
            values = (
                plan['_id'],
                plan['sessionId'],
                plan['orderNo'],
                '',  # price_plan_id (not in JSON)
                plan.get('pricePlanName', ''),
                plan.get('consumptionMode', ''),
                plan.get('selectedAreaId', ''),
                plan.get('selectedRoomTypeId', ''),
                plan.get('buyMinute', 0),
                '',  # time_charge_type (not in JSON)
                '',  # time_charge_mode (not in JSON)
                int(plan['minimumCharge']['$numberLong']),
                int(plan['ctime']['$numberLong']),  # Already in seconds
                int(plan['utime']['$numberLong']),  # Already in seconds
                plan['state'],
                plan['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(price_plans)} order price plans")

    except Exception as e:
        print(f"Error importing order price plans: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_order_products():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.orderProduct.json', 'r', encoding='utf-8') as file:
        products = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO order_product (
        id, session_id, order_no, product_id, product_name,
        flavors, quantity, unit, pay_price, mark,
        pay_status, price, total_amount, in_package_tag,
        src, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for product in products:
            values = (
                product['_id'],
                product['sessionId'],
                product['orderNo'],
                product.get('productId', ''),  # Some products might not have productId
                product['productName'],
                '',  # flavors (not in JSON)
                product['quantity'],
                product['unit'],
                int(product['payPrice']['$numberLong']),
                '',  # mark (not in JSON)
                '',  # pay_status (not in JSON)
                int(product['price']['$numberLong']),
                int(product['totalAmount']['$numberLong']),
                '',  # in_package_tag (not in JSON)
                product.get('src', ''),
                int(product['ctime']['$numberLong']),  # Already in seconds
                int(product['utime']['$numberLong']),  # Already in seconds
                product['state'],
                product['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(products)} order products")

    except Exception as e:
        print(f"Error importing order products: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_order_room_plans():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.orderRoomPlan.json', 'r', encoding='utf-8') as file:
        room_plans = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO order_room_plan (
        id, session_id, order_no, room_id, room_name,
        price_plan_id, price_plan_name, start_time, end_time,
        duration, pay_fee, pay_status, price_plan_type,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for plan in room_plans:
            values = (
                plan['_id'],
                plan['sessionId'],
                plan['orderNo'],
                plan['roomId'],
                plan['roomName'],
                plan.get('pricePlanId', ''),  # Some plans might not have pricePlanId
                plan['pricePlanName'],
                int(plan['startTime']['$numberLong']),  # Already in seconds
                int(plan['endTime']['$numberLong']),    # Already in seconds
                plan['duration'],
                int(plan['payFee']['$numberLong']),
                plan['payStatus'],
                '',  # price_plan_type (not in JSON)
                int(plan['ctime']['$numberLong']),      # Already in seconds
                int(plan['utime']['$numberLong']),      # Already in seconds
                plan['state'],
                plan['version']
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(room_plans)} order room plans")

    except Exception as e:
        print(f"Error importing order room plans: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_price_plans():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.pricePlan.json', 'r', encoding='utf-8') as file:
        plans = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO price_plan (
        id, venue_id, name, room_type, distribution_channel,
        consumption_mode, has_minimum_charge, minimum_charge,
        example_products, optional_products, free_products,
        member_price, member_discount, area_prices,
        area_member_prices, holiday_prices, is_enabled,
        supports_points, consumption_time_slots, buy_gift_plan,
        time_type, weeks, day_start, day_end,
        hour_minute_start, hour_minute_end, plan_products,
        duration, is_area_specified, selected_areas,
        base_room_fee, advance_disable_duration, is_excess_included,
        birthday_fee, group_buy_fee, activity_fee,
        discount_mode, discount_duration, gift_duration,
        room_charge_goods, max_deductible_amount, minimum_consumption,
        statistics_category, plan_pic, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for plan in plans:
            values = (
                plan['_id'],
                '',  # venue_id (not in JSON)
                plan.get('name', ''),
                plan.get('roomType', '[]'),
                plan.get('distributionChannel', '[]'),
                plan.get('consumptionMode', ''),
                plan.get('hasMinimumCharge', False),
                int(plan.get('minimumCharge', {}).get('$numberLong', '0')),
                plan.get('exampleProducts', ''),
                plan.get('optionalProducts', ''),
                plan.get('freeProducts', ''),
                int(plan.get('memberPrice', {}).get('$numberLong', '0')),
                int(plan.get('memberDiscount', {}).get('$numberLong', '0')),
                plan.get('areaPrices', '[]'),
                plan.get('areaMemberPrices', '{}'),
                plan.get('holidayPrices', '{}'),
                plan.get('isEnabled', False),
                plan.get('supportsPoints', False),
                '',  # consumption_time_slots (not in JSON)
                plan.get('buyGiftPlan', '{}'),
                plan.get('timeType', ''),
                plan.get('weeks', '[]'),
                plan.get('dayStart', ''),
                plan.get('dayEnd', ''),
                plan.get('hourMinuteStart', ''),
                plan.get('hourMinuteEnd', ''),
                plan.get('planProducts', '[]'),
                plan.get('duration', 0),
                plan.get('isAreaSpecified', False),
                plan.get('selectedAreas', '[]'),
                int(plan.get('baseRoomFee', {}).get('$numberLong', '0')),
                plan.get('advanceDisableDuration', 0),
                plan.get('isExcessIncluded', False),
                int(plan.get('birthdayFee', {}).get('$numberLong', '0')),
                int(plan.get('groupBuyFee', {}).get('$numberLong', '0')),
                int(plan.get('activityFee', {}).get('$numberLong', '0')),
                plan.get('discountMode', ''),
                plan.get('discountDuration', 0),
                plan.get('giftDuration', 0),
                plan.get('roomChargeGoods', '[]'),
                int(plan.get('maxDeductibleAmount', {}).get('$numberLong', '0')),
                int(plan.get('minimumConsumption', {}).get('$numberLong', '0')),
                '',  # statistics_category (not in JSON)
                '',  # plan_pic (not in JSON)
                int(plan['ctime']['$numberLong']),
                int(plan['utime']['$numberLong']),
                plan.get('state', 0),
                plan.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(plans)} price plans")

    except Exception as e:
        print(f"Error importing price plans: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_products():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.product.json', 'r', encoding='utf-8') as file:
        products = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product (
        id, name, type, current_price, pay_price,
        barcode, area_prices, time_slot_prices, distribution_channels,
        minimum_sale_quantity, is_real_price_product, category,
        discounts, allow_repeat_buy, recommend_combos,
        member_card_limits, flavors, ingredients,
        is_displayed, allow_staff_gift, count_to_min_charge,
        count_to_performance, is_promotion, is_sold_out,
        allow_wine_storage, calculate_inventory, is_area_specified,
        selected_areas, is_room_type_specified, selected_room_types,
        start_time, end_time, description, image,
        low_stock_threshold, delivery_timeout, supports_external_delivery,
        external_delivery_price, unit, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s
    )
    """

    def get_number_value(field):
        if not isinstance(field, dict):
            return 0
        if '$numberLong' in field:
            return int(field['$numberLong'])
        if '$numberDecimal' in field:
            return int(float(field['$numberDecimal']))
        return 0

    try:
        for product in products:
            values = (
                product['_id'],
                product['name'],
                product['type'],
                get_number_value(product.get('currentPrice', 0)),
                get_number_value(product.get('payPrice', 0)),
                product.get('barcode', ''),
                product.get('areaPrices', '[]'),
                product.get('timeSlotPrices', '[]'),
                product.get('distributionChannels', '[]'),
                product.get('minimumSaleQuantity', 1),
                product.get('isRealPriceProduct', False),
                product.get('category', ''),
                product.get('discounts', '[]'),
                product.get('allowRepeatBuy', True),
                product.get('recommendCombos', '[]'),
                product.get('memberCardLimits', '[]'),
                product.get('flavors', '[]'),
                product.get('ingredients', '[]'),
                product.get('isDisplayed', True),
                product.get('allowStaffGift', True),
                product.get('countToMinCharge', True),
                product.get('countToPerformance', True),
                product.get('isPromotion', True),
                product.get('isSoldOut', False),
                product.get('allowWineStorage', False),
                product.get('calculateInventory', False),
                product.get('isAreaSpecified', False),
                product.get('selectedAreas', '[]'),
                product.get('isRoomTypeSpecified', False),
                product.get('selectedRoomTypes', '[]'),
                product.get('startTime', '00:00'),
                product.get('endTime', '00:00'),
                product.get('description', ''),
                product.get('image', ''),
                product.get('lowStockThreshold', 0),
                product.get('deliveryTimeout', 0),
                product.get('supportsExternalDelivery', False),
                get_number_value(product.get('externalDeliveryPrice', 0)),
                product.get('unit', ''),
                get_number_value(product.get('ctime', 0)),
                get_number_value(product.get('utime', 0)),
                product.get('state', 0),
                product.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(products)} products")

    except Exception as e:
        print(f"Error importing products: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_product_display_categories():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.productDisplayCategory.json', 'r', encoding='utf-8') as file:
        categories = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product_display_category (
        id, name, product_types, is_display_second,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for category in categories:
            values = (
                category['_id'],
                category['name'],
                category['productTypes'],
                category['isDisplaySecond'],
                int(category['ctime']['$numberLong']),
                int(category['utime']['$numberLong']),
                category.get('state', 0),
                category.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(categories)} product display categories")

    except Exception as e:
        print(f"Error importing product display categories: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_product_multiple_buy_free():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.productMultipleBuyFree.json', 'r', encoding='utf-8') as file:
        policies = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product_multiple_buy_free (
        id, name, buy_products, buy_count,
        example_gift_product, gift_policy, time_slots,
        is_can_repeat_buy, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for policy in policies:
            values = (
                policy['_id'],
                policy['name'],
                policy['buyProducts'],
                policy['buyCount'],
                policy['exampleGiftProduct'],
                policy['giftPolicy'],
                policy['timeSlots'],
                policy['isCanRepeatBuy'],
                int(policy['ctime']['$numberLong']),
                int(policy['utime']['$numberLong']),
                policy.get('state', 0),
                policy.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(policies)} product multiple buy free policies")

    except Exception as e:
        print(f"Error importing product multiple buy free policies: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_product_packages():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.productPackage.json', 'r', encoding='utf-8') as file:
        packages = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product_package (
        id, name, category, series, current_price,
        supports_discount, area_prices, time_slot_prices,
        free_drink_mode, package_products, optional_groups,
        member_card_payment_limit, order_quantity_limit,
        barcode, image, is_on_shelf, shelf_time_slots,
        staff_gift, count_in_minimum_consumption,
        calculate_performance, is_promoted,
        deployment_room_types, deployment_areas,
        deployment_channels, available_after_minimum_consumption,
        consumption_gift_coupon, description,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s
    )
    """

    try:
        for package in packages:
            values = (
                package['_id'],
                package['name'],
                package['category'],
                '',  # series (not in JSON)
                int(package['currentPrice']['$numberLong']),
                package['supportsDiscount'],
                package['areaPrices'],
                package['timeSlotPrices'],
                package['freeDrinkMode'],
                package['packageProducts'],
                package['optionalGroups'],
                package.get('memberCardPaymentLimit', ''),
                package.get('orderQuantityLimit', 0),
                package.get('barcode', ''),
                package.get('image', ''),
                package['isOnShelf'],
                package['shelfTimeSlots'],
                package['staffGift'],
                package['countInMinimumConsumption'],
                package['calculatePerformance'],
                package['isPromoted'],
                package.get('deploymentRoomTypes', ''),
                package.get('deploymentAreas', ''),
                package.get('deploymentChannels', '[]'),
                package['availableAfterMinimumConsumption'],
                package.get('consumptionGiftCoupon', ''),
                package.get('description', ''),
                int(package['ctime']['$numberLong']),
                int(package['utime']['$numberLong']),
                package.get('state', 0),
                package.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(packages)} product packages")

    except Exception as e:
        print(f"Error importing product packages: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_product_package_types():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.productPackageType.json', 'r', encoding='utf-8') as file:
        types = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product_package_type (
        id, name, distribution_channels, is_displayed,
        support_points, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for pkg_type in types:
            values = (
                pkg_type['_id'],
                pkg_type['name'],
                pkg_type['distributionChannels'],
                pkg_type['isDisplayed'],
                pkg_type['supportPoints'],
                int(pkg_type['ctime']['$numberLong']),
                int(pkg_type['utime']['$numberLong']),
                pkg_type.get('state', 0),
                pkg_type.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(types)} product package types")

    except Exception as e:
        print(f"Error importing product package types: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_product_time_slots():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.productTimeSlot.json', 'r', encoding='utf-8') as file:
        time_slots = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product_time_slot (
        id, name, venue_id, type, days,
        timerange, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for slot in time_slots:
            values = (
                slot['_id'],
                slot['name'],
                '',  # venue_id (not in JSON)
                slot['type'],
                slot['days'],
                slot['timerange'],
                int(int(slot['ctime']['$numberLong'])/1000) if len(str(int(slot['ctime']['$numberLong']))) == 13 else int(slot['ctime']['$numberLong']),
                int(int(slot['utime']['$numberLong'])/1000) if len(str(int(slot['utime']['$numberLong']))) == 13 else int(slot['utime']['$numberLong']),
                slot.get('state', 0),
                slot.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(time_slots)} product time slots")

    except Exception as e:
        print(f"Error importing product time slots: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_product_types():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.productType.json', 'r', encoding='utf-8') as file:
        types = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO product_type (
        id, venue_id, name, distribution_channels,
        is_displayed, delivery_timeout, custom_storage_config,
        supports_points, is_included_in_drink_analysis,
        is_kitchen_monitoring, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for prod_type in types:
            values = (
                prod_type['_id'],
                '',  # venue_id (not in JSON)
                prod_type['name'],
                prod_type['distributionChannels'],
                prod_type['isDisplayed'],
                prod_type['deliveryTimeout'],
                prod_type['customStorageConfig'],
                prod_type['supportsPoints'],
                prod_type['isIncludedInDrinkAnalysis'],
                prod_type['isKitchenMonitoring'],
                int(prod_type['ctime']['$numberLong']),
                int(prod_type['utime']['$numberLong']),
                prod_type.get('state', 0),
                prod_type.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(types)} product types")

    except Exception as e:
        print(f"Error importing product types: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_rooms():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.room.json', 'r', encoding='utf-8') as file:
        rooms = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO room (
        id, venue_id, session_id, name, type_id,
        area_id, theme_id, price_plan_id, high_consumption_alert,
        status, open_time, close_time, consumption_mode,
        interior_photo, is_displayed, qr_code, color,
        display_items, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s
    )
    """

    try:
        for room in rooms:
            # Handle closeTime and openTime which might be 0 or missing
            close_time = room.get('closeTime', 0)
            if isinstance(close_time, dict):
                close_time = int(close_time.get('$numberLong', 0))
            
            open_time = room.get('openTime', 0)
            if isinstance(open_time, dict):
                open_time = int(open_time.get('$numberLong', 0))

            values = (
                room['_id'],
                '',  # venue_id (not in JSON)
                room.get('sessionId', ''),
                room['name'],
                room['typeId'],
                room['areaId'],
                room['themeId'],
                room.get('pricePlanId', ''),
                room.get('highConsumptionAlert', 0),
                room.get('status', ''),
                open_time,
                close_time,
                room.get('consumptionMode', ''),
                room.get('interiorPhoto', ''),
                room['isDisplayed'],
                room.get('qrCode', ''),
                room.get('color', ''),
                '',  # display_items (not in JSON)
                            int(int(room['ctime']['$numberLong'])/1000) if len(str(int(room['ctime']['$numberLong']))) == 13 else int(room['ctime']['$numberLong']),
                            int(int(room['utime']['$numberLong'])/1000) if len(str(int(room['utime']['$numberLong']))) == 13 else int(room['utime']['$numberLong']),
                room.get('state', 0),
                room.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(rooms)} rooms")

    except Exception as e:
        print(f"Error importing rooms: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_room_themes():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.roomTheme.json', 'r', encoding='utf-8') as file:
        themes = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO room_theme (
        id, venue_id, name, is_displayed, description,
        image_url, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for theme in themes:
            values = (
                theme['_id'],
                '',  # venue_id (not in JSON)
                theme['name'],
                theme['isDisplayed'],
                theme.get('description', ''),
                '',  # image_url (not in JSON)
                int(theme['ctime']['$numberLong']),
                int(theme['utime']['$numberLong']),
                theme.get('state', 0),
                theme.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(themes)} room themes")

    except Exception as e:
        print(f"Error importing room themes: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_room_types():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.roomType.json', 'r', encoding='utf-8') as file:
        types = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO room_type (
        id, venue_id, name, consumption_mode,
        high_consumption_alert, time_charge_base_plan,
        photo, remark, distribution_channel,
        is_displayed, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for room_type in types:
            # Handle highConsumptionAlert which might be a dict or missing
            high_consumption = room_type.get('highConsumptionAlert', {'$numberLong': '0'})
            if isinstance(high_consumption, dict):
                high_consumption = int(high_consumption.get('$numberLong', '0'))

            values = (
                room_type['_id'],
                '',  # venue_id (not in JSON)
                room_type['name'],
                room_type.get('consumptionMode', ''),
                high_consumption,
                room_type.get('timeChargeBasePlan', ''),
                room_type.get('photo', ''),
                room_type.get('remark', ''),
                room_type.get('distributionChannel', ''),
                room_type['isDisplayed'],
                int(room_type['ctime']['$numberLong']),
                int(room_type['utime']['$numberLong']),
                room_type.get('state', 0),
                room_type.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(types)} room types")

    except Exception as e:
        print(f"Error importing room types: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_sessions():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.session.json', 'r', encoding='utf-8') as file:
        sessions = json.load(file)
    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO session (
        id, session_id, venue_id, room_id, start_time,
        end_time, duration, status, order_source, customer_source,
        customer_tag, agent_person, duty_person, rank_number,
        min_consume, room_fee, supermarket_fee, total_fee,
        pre_pay_balance, is_open_table_settled, ctime, utime,
        state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
        %s, %s, %s, %s
    )
    """

    try:
        for session in sessions:
            values = (
                session['_id'],
                session['sessionId'],
                session['venueId'],
                session['roomId'],
                int(session['startTime']['$numberLong']),
                int(session['endTime']['$numberLong']),
                int(session['duration']['$numberLong']),
                session['status'],
                '',  # order_source (not in JSON)
                '',  # customer_source (not in JSON)
                '',  # customer_tag (not in JSON)
                '',  # agent_person (not in JSON)
                '',  # duty_person (not in JSON)
                '',  # rank_number (not in JSON)
                int(session['minConsume']['$numberLong']),
                int(session['roomFee']['$numberLong']),
                int(session['supermarketFee']['$numberLong']),
                int(session['totalFee']['$numberLong']),
                int(session['prePayBalance']['$numberLong']),
                session['isOpenTableSettled'],
                int(session['ctime']['$numberLong']),
                int(session['utime']['$numberLong']),
                session.get('state', 0),
                session.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(sessions)} sessions")

    except Exception as e:
        print(f"Error importing sessions: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_statistics_categories():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.statisticsCategory.json', 'r', encoding='utf-8') as file:
        categories = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO statistics_category (
        id, name, venue_id, price_plan_ids,
        ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for category in categories:
            values = (
                category['_id'],
                category['name'],
                category['venueId'],
                category['pricePlanIds'],
                int(int(category['ctime']['$numberLong'])/1000) if len(str(int(category['ctime']['$numberLong']))) == 13 else int(category['ctime']['$numberLong']),
                int(int(category['utime']['$numberLong'])/1000) if len(str(int(category['utime']['$numberLong']))) == 13 else int(category['utime']['$numberLong']),
                category.get('state', 0),
                category.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(categories)} statistics categories")

    except Exception as e:
        print(f"Error importing statistics categories: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_themes():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.theme.json', 'r', encoding='utf-8') as file:
        themes = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO theme (
        id, name, is_displayed, ctime,
        utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for theme in themes:
            values = (
                theme['_id'],
                theme['name'],
                theme['isDisplayed'],
                int(theme['ctime']['$numberLong']),
                int(theme['utime']['$numberLong']),
                theme.get('state', 0),
                theme.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(themes)} themes")

    except Exception as e:
        print(f"Error importing themes: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def import_wine_storage_settings():
    # Read JSON file
    with open(basepath+'/docs/mongodbdata/json/autoapp.wineStorageSetting.json', 'r', encoding='utf-8') as file:
        settings = json.load(file)

    conn = connect_mysql()
    cursor = conn.cursor()

    insert_query = """
    INSERT INTO wine_storage_setting (
        id, venue_id, storage_days, renewal_times, renewal_days,
        customer_notification_days, merchant_notification_days,
        merchant_expiration_reminder, agent_expiration_reminder,
        auto_confiscate, overdue_withdrawal_limit, auto_confiscate_days,
        confiscate_warehouse, ctime, utime, state, version
    ) VALUES (
        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
    )
    """

    try:
        for setting in settings:
            values = (
                setting['_id'],
                '',  # venue_id (not in JSON)
                setting['storageDays'],
                setting['renewalTimes'],
                setting['renewalDays'],
                setting['customerNotificationDays'],
                setting['merchantNotificationDays'],
                setting.get('merchantExpirationReminder', ''),
                setting.get('agentExpirationReminder', ''),
                setting['autoConfiscate'],
                setting['overdueWithdrawalLimit'],
                setting.get('autoConfiscateDays', 0),
                '',  # confiscate_warehouse (not in JSON)
                int(setting['ctime']['$numberLong']),
                int(setting['utime']['$numberLong']),
                setting.get('state', 0),
                setting.get('version', 0)
            )
            cursor.execute(insert_query, values)

        conn.commit()
        print(f"Successfully imported {len(settings)} wine storage settings")

    except Exception as e:
        print(f"Error importing wine storage settings: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    # import_employees()
    # import_flavors()
    # import_gift_groups()
    # import_holidays()
    # import_member_card_templates()
    # import_orders()
    # import_order_price_plans()
    # import_order_products()
    # import_order_room_plans()
    # import_price_plans()
    # import_products()
    # import_product_display_categories()
    # import_product_multiple_buy_free()
    # import_product_packages()
    # import_product_package_types()
    # import_product_time_slots()
    # import_product_types()
    # import_rooms()
    # import_room_themes()
    # import_room_types()
    # import_sessions()
    # import_statistics_categories()
    # import_themes()
    import_wine_storage_settings()
