# Pay 函数流程文档

## 文件路径
`erp_managent/controller/OrderControllerPay.go`

## 函数概述
`Pay` 函数用于处理支付订单的请求。它接收支付请求的参数，验证参数的有效性，计算支付金额，并最终发起支付。

## 函数流程

1. **参数绑定**:
   - 使用 `ctx.ShouldBindJSON(&reqDto)` 将请求体绑定到 `reqDto` 结构体中。
   - 如果绑定失败，返回服务器繁忙的错误信息。

2. **验证入参**:
   - 调用 `orderValidator.ValidatePayBase(ctx, &reqDto)` 验证请求参数的有效性。
   - 如果验证失败，返回相应的错误信息。

3. **检查折扣**:
   - 调用 `orderValidator.ValidatePayHasDiscount(&reqDto)` 检查是否有折扣。

4. **获取支付订单信息**:
   - 调用 `payService.GetPayOrderInfos(ctx, &reqDto)` 获取需要支付的订单信息。
   - 如果获取失败，返回相应的错误信息。

5. **获取最终更新的订单信息**:
   - 调用 `payService.GetFinalUpdateOrderInfo(ctx, hasDiscount, &reqDto, &toPayOrderVOsSrc, &toPayOrdersSrc, *session.UnpaidAmount)` 获取需要更新的订单、房间和商品信息。
   - 如果获取失败，返回相应的错误信息。

6. **组装支付数据**:
   - 调用 `payService.BuildPayDataForPay(ctx, &reqDto, toPayFinalUpdateOrders, session, totalDiscountRoomAmount, totalDiscountProductAmount)` 组装支付数据。

7. **保存支付信息**:
   - 调用 `payService.SaveBatchTxForPay(ctx, toAddPayBill, toAddOrderAndPays, toPayFinalUpdateOrders, toUpdateorderRoomPlans, toUpdateOrderProducts)` 保存支付信息。
   - 如果保存失败，返回相应的错误信息。

8. **发起支付**:
   - 调用 `payService.TransformPayGate(ctx, &reqDto, toAddPayBill)` 发起支付请求。
   - 如果发起支付失败，返回相应的错误信息。

9. **支付后处理**:
   - 如果支付类型为记账支付，调用 `payService.AfterPayCallbackCoUpdateInfoByPayId(ctx, *toAddPayBill.PayId)` 更新支付信息。
   - 如果有需要，调用 `payService.ReCalcSessionFees(ctx, *session.Id, *reqDto.SessionId, *reqDto.VenueId)` 重新计算费用。

10. **返回成功结果**:
    - 返回支付结果。

## 注意事项
- 确保所有的请求参数都经过严格的验证。
- 处理支付时要考虑到各种可能的错误情况，并返回相应的错误信息。
- 在发起支付后，确保进行必要的后处理，以更新相关的订单和费用信息。
