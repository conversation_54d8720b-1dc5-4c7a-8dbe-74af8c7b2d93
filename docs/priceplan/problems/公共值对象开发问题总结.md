# 包厢价格方案 - 公共值对象开发问题总结

本文档记录了在开发包厢价格方案公共值对象过程中遇到的问题和解决方案。

## 1. 导入路径问题

**问题描述：**

在编写单元测试时，遇到了导入路径问题，无法导入 `erp_managent/domain/roomprice/model/common` 包。

```
could not import erp_managent/domain/roomprice/model/common (no required module provides package "erp_managent/domain/roomprice/model/common")
```

**解决方案：**

创建 `go.mod` 文件，定义模块名称和依赖关系。

```bash
cd erp_managent
go mod init erp_managent
go mod tidy
```

## 2. 时间格式验证问题

**问题描述：**

在 `TimeConfig` 值对象的单元测试中，发现 `isValidHourMinute` 函数没有正确验证时间格式，导致测试失败。

```
--- FAIL: TestWeekdayTimeConfig_IsValid (0.00s)
    TimeConfig_test.go:50: 
                Error Trace:    /Users/<USER>/projects/ai/erp/server/erp-lt-vv/erp_managent/domain/roomprice/model/common/test/TimeConfig_test.go:50
                Error:          Should be false
                Test:           TestWeekdayTimeConfig_IsValid
--- FAIL: TestDateTimeConfig_IsValid (0.00s)
    TimeConfig_test.go:71: 
                Error Trace:    /Users/<USER>/projects/ai/erp/server/erp-lt-vv/erp_managent/domain/roomprice/model/common/test/TimeConfig_test.go:71
                Error:          Should be false
                Test:           TestDateTimeConfig_IsValid
```

**解决方案：**

修改 `isValidHourMinute` 函数，增加对时间格式的严格验证，确保小时和分钟都是两位数。

```go
// isValidHourMinute 验证时间格式是否有效 (HH:mm)
func isValidHourMinute(hourMinute string) bool {
    if hourMinute == "" {
        return false
    }

    parts := strings.Split(hourMinute, ":")
    if len(parts) != 2 {
        return false
    }

    // 确保小时和分钟都是两位数
    if len(parts[0]) != 2 || len(parts[1]) != 2 {
        return false
    }

    hour, err1 := strconv.Atoi(parts[0])
    minute, err2 := strconv.Atoi(parts[1])

    if err1 != nil || err2 != nil {
        return false
    }

    return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59
}
```

## 3. 值对象的不可变性设计

**问题描述：**

在设计值对象时，需要考虑值对象的不可变性，确保值对象一旦创建就不能被修改，以避免副作用。

**解决方案：**

1. 对于 `Money` 值对象，所有操作都返回新的 `Money` 实例，而不是修改原有实例。

```go
// Add 金额相加
func (m Money) Add(other Money) Money {
    return Money{amount: m.amount + other.amount}
}
```

2. 对于 `TimeConfig` 值对象，使用链式调用方法返回修改后的新实例。

```go
// WithWeekdayConfig 设置星期配置
func (tc *TimeConfig) WithWeekdayConfig(weekdayConfig *WeekdayTimeConfig) *TimeConfig {
    tc.WeekdayConfig = weekdayConfig
    return tc
}
```

3. 对于其他配置类值对象，由于需要支持添加和删除操作，采用了可变设计，但在使用时应注意避免共享实例。

## 4. 序列化与反序列化设计

**问题描述：**

值对象需要与 PO 对象进行转换，涉及序列化和反序列化操作，需要确保数据的一致性和完整性。

**解决方案：**

1. 为每个配置类值对象提供 `FromPO` 和 `ToPOString` 方法，处理 JSON 序列化和反序列化。

```go
// FromPO 从PO对象创建RoomTypeConfig值对象
func (rtc *RoomTypeConfig) FromPO(roomTypeStr string) error {
    if roomTypeStr == "" {
        return nil
    }

    err := json.Unmarshal([]byte(roomTypeStr), &rtc.RoomTypes)
    if err != nil {
        return fmt.Errorf("解析房间类型配置失败: %w", err)
    }

    return nil
}

// ToPOString 将RoomTypeConfig值对象转换为PO字段字符串
func (rtc *RoomTypeConfig) ToPOString() (string, error) {
    if rtc.IsEmpty() {
        return "", nil
    }

    bytes, err := json.Marshal(rtc.RoomTypes)
    if err != nil {
        return "", fmt.Errorf("序列化房间类型配置失败: %w", err)
    }

    return string(bytes), nil
}
```

2. 对于 `TimeConfig` 值对象，由于其结构较为特殊，提供了 `FromPO` 和 `ToPOFields` 方法，处理多个字段的转换。

```go
// FromPO 从PO对象创建TimeConfig值对象
func FromPO(timeType string, weeks string, dayStart, dayEnd, hourMinuteStart, hourMinuteEnd string) (*TimeConfig, error) {
    // ...
}

// ToPOFields 将TimeConfig值对象转换为PO字段
func (tc *TimeConfig) ToPOFields() (timeType string, weeks string, dayStart, dayEnd, hourMinuteStart, hourMinuteEnd string) {
    // ...
}
```

## 5. 值对象的验证逻辑

**问题描述：**

值对象需要进行有效性验证，确保数据的正确性和完整性。

**解决方案：**

为每个值对象提供 `IsValid` 方法，验证值对象的有效性。

```go
// IsValid 验证TimeConfig配置是否完整和有效
func (tc *TimeConfig) IsValid() bool {
    if tc.TimeType != "weekday" && tc.TimeType != "date" {
        return false
    }

    if tc.TimeType == "weekday" {
        return tc.WeekdayConfig != nil && tc.WeekdayConfig.IsValid()
    }

    if tc.TimeType == "date" {
        return tc.DateConfig != nil && tc.DateConfig.IsValid()
    }

    return false
}
```

## 6. 值对象的测试策略

**问题描述：**

值对象的测试需要覆盖各种场景，确保功能的正确性和稳定性。

**解决方案：**

1. 为每个值对象编写单元测试，覆盖创建、验证、操作等各个方面。
2. 使用表驱动测试方法，测试多种输入和预期输出。
3. 测试边界条件和异常情况，确保代码的健壮性。
4. 使用 `github.com/stretchr/testify/assert` 包简化测试断言。

```go
func TestTimeConfig_Contains(t *testing.T) {
    // 测试星期配置
    weekdayConfig := common.NewWeekdayTimeConfig([]int{1, 2, 3}, "08:00", "18:00")
    timeConfig := common.NewTimeConfig("weekday").WithWeekdayConfig(weekdayConfig)

    // 周一 (星期1) 10:00，应该在范围内
    monday := time.Date(2023, 1, 2, 10, 0, 0, 0, time.Local) // 2023-01-02 是周一
    assert.True(t, timeConfig.Contains(monday))

    // 周四 (星期4) 10:00，不在范围内
    thursday := time.Date(2023, 1, 5, 10, 0, 0, 0, time.Local) // 2023-01-05 是周四
    assert.False(t, timeConfig.Contains(thursday))

    // 周一 (星期1) 20:00，不在时间范围内
    mondayEvening := time.Date(2023, 1, 2, 20, 0, 0, 0, time.Local)
    assert.False(t, timeConfig.Contains(mondayEvening))
}
``` 