
# 包厢价格方案 DDD 设计


本文档旨在描述针对包厢价格方案的领域驱动设计 (DDD) 方案，并结合现有的 PO (Persistent Object) 定义，提供代码目录结构、开发建议、以及 PriceConfig 和 TimeConfig 相关的值对象定义和数据转换指导。

## 1. DDD 目录结构 (erp_managent 根目录下)

```
erp_managent/
├── application/          # 应用层 (用例和应用服务)
│   └── roomprice/       # 房价相关的应用服务
│       ├── BuyoutPricePlanService.go
│       ├── TimePricePlanService.go
│       └── TimeDiscountPlanService.go
├── domain/             # 领域层 (核心业务逻辑)
│   └── roomprice/      # 房价领域 (限界上下文/模块)
│       ├── model/      # 领域模型
│       │   ├── buyoutpriceplan/
│       │   │   ├── BuyoutPricePlan.go         # 买断价格方案聚合根
│       │   │   ├── BuyoutPriceConfig.go     # 买断价格配置 (值对象)
│       │   │   └── AreaPriceConfig.go        # 买断区域价格配置 (值对象)
│       │   ├── timepriceplan/
│       │   │   ├── TimePricePlan.go        # 计时价格方案聚合根
│       │   │   ├── TimePriceConfig.go     # 计时价格配置 (值对象)
│       │   │   └── AreaTimePriceConfig.go   # 计时区域价格配置 (值对象)
│       │   └── timediscountplan/
│       │       ├── TimeDiscountPlan.go       # 计时优惠方案聚合根
│       │       ├── TimeDiscountConfig.go    # 计时优惠配置 (值对象)
│       │       └── DiscountStrategy.go        # 优惠策略 (接口+实现)
│       │   └── common/                    # 公共值对象
│       │       ├── RoomTypeConfig.go
│       │       ├── ChannelConfig.go
│       │       ├── AreaConfig.go
│       │       ├── TimeConfig.go         # TimeConfig 值对象 (包含星期和日期配置)
│       │       ├── ProductSelection.go
│       │       ├── Money.go                 # 金额 (值对象)
│       │       └── PriceConfig.go           # 价格配置相关的通用 Value Object 定义 (PriceConfigList, PriceSettingItem, HolidayPrice, AreaPrice)
│       ├── repository/ # 仓储接口
│       │   ├── BuyoutPricePlanRepository.go
│       │   ├── TimePricePlanRepository.go
│       │   └── TimeDiscountPlanRepository.go
│       └── service/    # 领域服务 (聚合根内部 + 跨聚合根)
│           ├── BuyoutPricePlanDomainService.go
│           ├── TimePricePlanDomainService.go
│           └── TimeDiscountPlanDomainService.go
│           └── RoomPriceService.go          # 跨价格类型的领域服务(可选)
├── infra/              # 基础设施层
│   └── repository/     # 基础设施仓储实现 (适配 erp_managent/service)
│       └── roomprice/
│           ├── BuyoutPricePlanRepositoryImpl.go  # 基于 erp_managent/service 的实现
│           ├── TimePricePlanRepositoryImpl.go
│           └── TimeDiscountPlanRepositoryImpl.go
└── service/           # EXISTING erp_managent/service 目录 (基础设施层 - 数据访问等)
    ├── application/   #  (现有, 视为基础设施层一部分)
    ├── dal/           #  (现有, 数据访问层 - 基础设施)
    ├── domain/        #  (现有, 可逐步废弃或重构)
    ├── impl/          #  (现有, 实现 - 基础设施)
    ├── infra/         #  (现有, 基础设施)
    ├── po/            #  (现有 POs - 基础设施)
    │   └── PricePlan.go    #  PricePlan PO 定义
    ├── testcase/      #  (现有 测试用例 - 可移动或重构)
    └── transfer/      #  (现有 DTOs/Transfer Objects - 可重构或用于应用层 API)
```

**说明:**

*   此目录结构在 `erp_managent` 根目录下构建 DDD 分层架构，**保留了现有的 `erp_managent/service` 目录**。
*   **`erp_managent/service` 目录被视为基础设施层的一个组成部分**，包含了以前的数据库操作、数据访问代码等。
*   **`infra/repository` 目录** 在新的 DDD 结构中，用于存放仓储接口的 **具体实现**。这些实现类将 **适配** `erp_managent/service` 目录下的代码，例如调用 `erp_managent/service/dal` 或 `erp_managent/service/po` 进行数据操作。
*   `po/` 目录被移到 `erp_managent` 根目录下，用于存放 PO 对象定义。

## 2. 包厢价格方案 DDD 开发建议

*   **关注领域模型：**  核心在于 `domain` 目录下的领域模型。围绕 `BuyoutPricePlan`, `TimePricePlan`, `TimeDiscountPlan` 三个聚合根进行业务逻辑建模。
*   **值对象优先：**  充分利用值对象封装数据和行为，例如 `Money`, `TimeConfig`, `PriceConfigList` 等，提高代码的可读性和可维护性。
*   **聚合根职责明确：**  确保每个聚合根都维护自身的一致性，业务操作通过聚合根的方法进行，避免直接操作聚合根内部的实体和值对象。
*   **应用服务编排用例：**  `application` 目录下的应用服务负责接收外部请求，编排领域服务和仓储，实现具体的业务用例。
*   **基础设施层解耦与适配：**  `infra/repository` 目录负责仓储接口的具体实现，需要 **适配** 现有的 `erp_managent/service` 目录下的数据访问代码。领域层不应直接依赖 `erp_managent/service`，通过仓储接口实现解耦。
*   **测试驱动开发 (TDD)：**  为领域模型、应用服务编写单元测试和集成测试，确保代码质量。

## 3. PriceConfig 相关的值对象定义 (Go 代码)

```go
package model

// PriceConfigList  价格配置列表 (对应JSON的最外层数组)
type PriceConfigList []PriceSettingItem

// PriceSettingItem  价格配置项 (对应JSON数组中的每个对象)
type PriceSettingItem struct {
	Name         string          `json:"name"`         // 价格类型名称，例如 "基础", "活动", "基础会员"
	Type         string          `json:"type"`         // 价格类型标识，例如 "baseRoom", "activity", "baseMember"
	Price        int64           `json:"price"`        // 默认价格 (非节假日，非区域价格)
	HolidayPrice []HolidayPrice  `json:"holidayPrice"` // 节假日价格配置
	AreaPrice    []AreaPrice     `json:"areaPrice"`    // 区域价格配置
}

// HolidayPrice  节假日价格配置 (对应JSON中的 holidayPrice 数组元素)
type HolidayPrice struct {
	HolidayId string      `json:"holidayId"`   // 节假日ID
	Price     int64       `json:"price"`     // 节假日价格
	AreaPrice []AreaPrice `json:"areaPrice"` // 节假日下的区域价格配置
}

// AreaPrice  区域价格配置 (对应JSON中的 areaPrice 数组元素)
type AreaPrice struct {
	AreaId string `json:"areaId"` // 区域ID
	Price  int64  `json:"price"`  // 区域价格
}
```

**PriceConfig 核心特点: 平铺结构与动态类型支持**

**价格类型平铺展开:** `PriceConfigList` 采用 **平铺展开** 的结构来管理价格配置。这意味着，所有价格类型（例如 "基础价格"、"会员价格"、"活动价格" 等）都被放置在同一层级的列表中进行配置。 这种平铺结构 **避免了深层嵌套**，使得价格配置更加 **扁平化** 和 **易于理解**。

**支持动态价格类型:** `PriceConfigList` 的设计支持 **动态的价格类型扩展**。 你可以根据业务需求，**灵活地添加新的价格类型**，例如：

*   **不同等级会员卡价格:**  可以根据用户配置的会员卡等级，动态添加 "黄金会员价格"、"铂金会员价格" 等类型，并分别配置价格。
*   **特殊活动价格:** 可以为特定活动（例如 "周年庆活动"、"节日促销活动"）添加活动专属的价格类型。
*   **渠道专属价格:**  可以为不同销售渠道（例如 "线上渠道价格"、"线下渠道价格"）配置不同的价格。

**要添加新的价格类型，只需在 `PriceConfigList` 中添加新的 `PriceSettingItem` 即可**，并定义其 `Name` 和 `Type` 属性。  **代码无需修改，具有良好的扩展性**，可以灵活适应未来业务变化带来的新的价格类型需求。 这种动态类型支持，使得价格配置系统更加强大和灵活。

## 4. TimeConfig 值对象定义 (Go 代码)

```go
package model

import "time"

// TimeConfig 时间配置 值对象
type TimeConfig struct {
	TimeType        string        `json:"timeType"`        // 时间类型: "weekday", "date"
	WeekdayConfig   *WeekdayTimeConfig `json:"weekdayConfig,omitempty"` // 星期配置，TimeType为 "weekday" 时使用
	DateConfig      *DateTimeConfig    `json:"dateConfig,omitempty"`    // 日期配置，TimeType为 "date" 时使用
}

// WeekdayTimeConfig 星期时间配置
type WeekdayTimeConfig struct {
	Weeks         []int `json:"weeks"`         // 星期的日子选择数组(1~7)
	HourMinuteStart string        `json:"hourMinuteStart"` // 开始时间 (HH:mm 格式字符串)
	HourMinuteEnd   string        `json:"hourMinuteEnd"`   // 结束时间 (HH:mm 格式字符串)
}


// DateTimeConfig 日期时间配置
type DateTimeConfig struct {
	DayStart        string `json:"dayStart"`        // 开始日期 (YYYY-MM-DD 格式字符串)
	DayEnd          string `json:"dayEnd"`          // 结束日期 (YYYY-MM-DD 格式字符串)
	HourMinuteStart string `json:"hourMinuteStart"` // 开始时间 (HH:mm 格式字符串)
	HourMinuteEnd   string `json:"hourMinuteEnd"`   // 结束时间 (HH:mm 格式字符串)
}
```

**TimeConfig 值对象说明:**

*   **`TimeConfig`**: 根 Value Object，包含 `TimeType` 字段，用于区分时间类型（`weekday` 或 `date`），并根据 `TimeType` 决定使用 `WeekdayTimeConfig` 还是 `DateTimeConfig`。
    *   **`TimeType`**: 字符串类型，枚举值 `"weekday"` 和 `"date"`。
    *   **`WeekdayConfig`**: `*WeekdayTimeConfig` 类型指针，当 `TimeType` 为 `"weekday"` 时使用。
    *   **`DateConfig`**: `*DateTimeConfig` 类型指针，当 `TimeType` 为 `"date"` 时使用。
    *   **`IsValid()`**:  验证 `TimeConfig` 配置是否完整和有效。

*   **`WeekdayTimeConfig`**: 星期时间配置 Value Object，用于配置基于星期的重复性时间范围。
    *   **`Weeks`**: `[]time.Weekday` 类型，表示星期几的数组。
    *   **`HourMinuteStart`**: 字符串类型，"HH:mm" 格式，表示每天的开始时间。
    *   **`HourMinuteEnd`**: 字符串类型，"HH:mm" 格式，表示每天的结束时间。

*   **`DateTimeConfig`**: 日期时间配置 Value Object，用于配置特定日期范围和每天的时间范围。
    *   **`DayStart`**: 字符串类型，"YYYY-MM-DD" 格式，表示开始日期。
    *   **`DayEnd`**: 字符串类型，"YYYY-MM-DD" 格式，表示结束日期。
    *   **`HourMinuteStart`**: 字符串类型，"HH:mm" 格式，表示每天的开始时间。
    *   **`HourMinuteEnd`**: 字符串类型，"HH:mm" 格式，表示每天的结束时间。


**使用 `TimeConfig` Value Object:**

在 `BuyoutPricePlan`, `TimePricePlan`, `TimeDiscountPlan` 聚合根中，使用 `common.TimeConfig` 类型替换原先分散的时间配置字段：

```go
// ... in BuyoutPricePlan, TimePricePlan, TimeDiscountPlan model ...

type BuyoutPricePlan struct {
    // ...
    timeConfig        common.TimeConfig // 使用 TimeConfig 值对象
    // ...
}

type TimePricePlan struct {
    // ...
    timeConfig       common.TimeConfig // 使用 TimeConfig 值对象
    // ...
}

type TimeDiscountPlan struct {
    // ...
    timeConfig       common.TimeConfig // 使用 TimeConfig 值对象
    // ...
}
```

在前端或应用层设置时间配置时，需要根据用户选择的时间类型，构建相应的 `TimeConfig` 值对象，并设置 `TimeType`, `WeekdayConfig`, `DateConfig` 属性。 在领域模型中，通过调用 `timeConfig.Contains(checkTime)` 方法来判断时间是否在配置范围内。

通过 `TimeConfig` 值对象，我们将时间配置的逻辑封装起来，使得领域模型更清晰，更易于维护和测试。

## 5. PriceConfig 与 PO 的序列化与反序列化

**序列化 (DDD PriceConfig -> PO PriceConfig 字符串):**

在基础设施层 (例如仓储实现) 将 DDD 领域模型中的 `PriceConfigList` 值对象保存到数据库时，需要将其序列化为 JSON 字符串，并赋值给 PO 的 `PriceConfig` 字段。

```go
import "encoding/json"

func (repo *PricePlanRepositoryImpl) Save(plan *domain.BuyoutPricePlan) error {
    // ...
    priceConfigBytes, err := json.Marshal(plan.PriceConfig()) // 假设 BuyoutPricePlan 有 PriceConfig() 方法返回 PriceConfigList
    if err != nil {
        return err
    }
    po := &po.PricePlan{
        // ... 其他字段映射 ...
        PriceConfig: string(priceConfigBytes), // 将 JSON 字符串赋值给 PO 的 PriceConfig 字段
    }
    // ... 使用 ORM 或原生 SQL 保存 PO 到数据库 ...
    return nil
}
```

**反序列化 (PO PriceConfig 字符串 -> DDD PriceConfig):**

从数据库读取 PO 时，需要将 PO 的 `PriceConfig` 字符串字段反序列化为 DDD 领域模型所需的 `PriceConfigList` 值对象。

```go
import "encoding/json"

func (repo *PricePlanRepositoryImpl) FindByID(id string) (*domain.BuyoutPricePlan, error) {
    po := &po.PricePlan{}
    // ... 使用 ORM 或原生 SQL 从数据库加载 PO ...

    priceConfigList := model.PriceConfigList{}
    if po.PriceConfig != nil { // 判空处理
        err := json.Unmarshal([]byte(*po.PriceConfig), &priceConfigList)
        if err != nil {
            // 错误处理，例如日志记录，返回错误等
            return nil, fmt.Errorf("反序列化 PriceConfig 失败: %w", err)
        }
    }

    buyoutPlan := &domain.BuyoutPricePlan{
        // ... 其他字段映射 ...
        PriceConfig: priceConfigList, // 将反序列化后的 PriceConfigList 赋值给领域模型
    }
    return buyoutPlan, nil
}
```

**关键点:**

*   使用 `encoding/json` 包的 `json.Marshal` 进行序列化，`json.Unmarshal` 进行反序列化。
*   在仓储实现层进行序列化和反序列化操作，将基础设施层的技术细节与领域层隔离。
*   对 `po.PriceConfig` 为 `nil` 的情况进行判空处理，避免反序列化空指针错误。
*   进行错误处理，例如反序列化失败时，应该记录日志并返回错误，以便排查问题。

## 6. 三种价格方案描述和 PO 字段废弃说明（废弃字段暂时标记为废弃，但不删除）

### 6.1 买断价格方案 (BuyoutPricePlan)

**描述:**

买断价格方案是指用户购买后，在一定时长内可以独占使用包厢的定价模式。

**核心特点:**

*   **时长计费：**  按照购买的时长（例如 2 小时、半天、一天）进行收费。
*   **独占使用：**  用户在购买的时长内可以独占使用包厢，不受时间限制。
*   **适用场景：**  例如会议包厢、长时间聚会等场景。

**PO 中可废弃的字段 (在 DDD 架构下，由 PriceConfigList 管理):**

*   `BaseRoomFee`
*   `MemberPrice`
*   `MemberDiscount`
*   `AreaPrices`
*   `AreaMemberPrices`
*   `HolidayPrices`
*   `BirthdayFee`
*   `GroupBuyFee`
*   `ActivityFee`

这些价格相关的字段将由 `PriceConfigList` 值对象统一管理，以更灵活和可扩展的方式配置价格。

### 6.2 买钟价格方案 (TimePricePlan)

**描述:**

买钟价格方案是指按照用户实际使用包厢的时间长度进行收费的定价模式，通常按小时或分钟计费。

**核心特点:**

*   **时长计费：** 按照实际使用时长（例如每小时多少钱，每分钟多少钱）进行收费。
*   **按需付费：**  用户使用多久付多久，灵活性高。
*   **适用场景：**  例如 KTV 包厢、普通娱乐包厢等场景。

**PO 中可废弃的字段 (在 DDD 架构下，由 PriceConfigList 管理):**

*   `BaseRoomFee`
*   `MemberPrice`
*   `MemberDiscount`
*   `AreaPrices`
*   `AreaMemberPrices`
*   `HolidayPrices`
*   `BirthdayFee`
*   `GroupBuyFee`
*   `ActivityFee`

同样地，这些价格相关的字段将由 `PriceConfigList` 值对象统一管理。


**总结:**

通过以上 DDD 设计，可以更好地组织和管理包厢价格方案的业务逻辑，提高代码的可维护性和可扩展性。使用 `PriceConfigList` 值对象统一管理价格配置，可以更灵活地支持各种复杂的价格场景。 废弃 PO 中冗余的价格相关字段，使得 PO 更专注于数据持久化，领域模型更专注于业务逻辑。
