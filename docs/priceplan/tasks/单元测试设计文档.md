# 包厢价格方案单元测试设计文档

本文档旨在描述包厢价格方案DDD改造中的单元测试设计，涵盖领域模型和应用服务的单元测试策略、方法和最佳实践。

## 1. 单元测试概述

单元测试是软件开发过程中的重要环节，用于验证代码的最小可测试单元（函数、方法、类等）的功能是否符合预期。在DDD中，单元测试主要关注领域模型和应用服务的业务逻辑正确性。

**单元测试的目标：**

- 验证业务逻辑的正确性
- 提高代码质量和可维护性
- 支持快速迭代和重构
- 尽早发现和修复Bug

**单元测试的特点：**

- **快速**：单元测试应该快速执行，以便频繁运行
- **独立**：单元测试应该独立于其他测试，不依赖外部环境
- **可重复**：单元测试每次运行结果应该一致
- **可验证**：单元测试应该能够明确验证代码行为是否符合预期

## 2. 领域模型单元测试

领域模型的单元测试主要关注聚合根、实体和值对象中封装的业务逻辑。

### 2.1 测试对象

- **聚合根 (Aggregate Roots)**：测试聚合根的核心业务逻辑、状态变化和不变性约束。
- **实体 (Entities)**：测试实体的业务逻辑和状态变化。
- **值对象 (Value Objects)**：测试值对象的创建、相等性、不变性和值对象的行为。
- **领域服务 (Domain Services)**：虽然领域服务通常处理跨聚合根的逻辑，但部分简单的领域服务也可以进行单元测试，重点测试其业务逻辑。

### 2.2 测试策略

- **关注行为而非状态**：更多关注方法调用的结果和副作用，而不是内部状态的细节。
- **测试业务规则和约束**：验证领域模型是否正确执行了业务规则和约束条件。
- **使用 Mock 隔离依赖**：领域模型可能依赖于其他领域对象或基础设施组件，在单元测试中应使用 Mock 对象来模拟这些依赖，以便隔离测试目标。
- **测试边界条件和异常情况**：除了正常流程，还需要测试边界条件和异常情况，例如无效参数、状态错误等。

### 2.3 测试示例

#### 2.3.1 聚合根单元测试示例 (BuyoutPricePlan)

假设 `BuyoutPricePlan` 聚合根有 `Enable()` 和 `Disable()` 方法，以及一个 `IsValidTime()` 方法用于检查时间是否有效。

```go
func TestBuyoutPricePlan_Enable(t *testing.T) {
    plan := NewBuyoutPricePlan("plan-001", "venue-001", "买断方案", /* ... 其他参数 ... */)
    assert.False(t, plan.IsEnabled(), "初始状态应为禁用")

    plan.Enable()
    assert.True(t, plan.IsEnabled(), "启用后状态应为启用")
}

func TestBuyoutPricePlan_Disable(t *testing.T) {
    plan := NewBuyoutPricePlan("plan-001", "venue-001", "买断方案", /* ... 其他参数 ... */)
    plan.Enable() // 先启用
    assert.True(t, plan.IsEnabled(), "启用后状态应为启用")

    plan.Disable()
    assert.False(t, plan.IsEnabled(), "禁用后状态应为禁用")
}

func TestBuyoutPricePlan_IsValidTime(t *testing.T) {
    timeConfig := common.TimeConfig{ /* ... 定义有效时间范围 ... */}
    plan := NewBuyoutPricePlan("plan-001", "venue-001", "买断方案", /* ..., timeConfig, ... */)

    validTime := time.Now() // 假设在 timeConfig 定义的有效时间内
    invalidTime := time.Now().Add(time.Hour * 24 * 365) // 假设不在有效时间内

    assert.True(t, plan.IsValidTime(validTime), "有效时间内应返回 true")
    assert.False(t, plan.IsValidTime(invalidTime), "无效时间内应返回 false")
}
```

#### 2.3.2 值对象单元测试示例 (Money)

假设 `Money` 值对象需要测试其创建和相等性。

```go
func TestMoney_Create(t *testing.T) {
    money := common.NewMoney(10000) // 100元
    assert.Equal(t, int64(10000), money.Amount(), "金额应为 10000 分")
    assert.Equal(t, "CNY", money.Currency(), "货币单位应为 CNY")
}

func TestMoney_Equals(t *testing.T) {
    moneyA := common.NewMoney(10000)
    moneyB := common.NewMoney(10000)
    moneyC := common.NewMoney(20000)

    assert.True(t, moneyA.Equals(moneyB), "金额相等的 Money 对象应相等")
    assert.False(t, moneyA.Equals(moneyC), "金额不等的 Money 对象应不相等")
}
```

## 3. 应用服务单元测试

应用服务的单元测试主要关注应用服务的编排逻辑和与领域服务的交互。

### 3.1 测试对象

- **应用服务 (Application Services)**：测试应用服务的用例编排、参数验证、事务处理和 DTO 转换等逻辑。

### 3.2 测试策略

- **Mock 领域服务和仓储**：应用服务通常依赖于领域服务和仓储接口，在单元测试中应使用 Mock 对象模拟这些依赖，以便隔离测试目标。
- **测试用例编排**：验证应用服务是否正确地调用了领域服务和仓储接口，以完成业务用例。
- **测试输入验证**：验证应用服务是否正确地验证了输入参数，并处理了无效输入。
- **测试 DTO 转换**：验证应用服务是否正确地进行了 DTO 和领域对象之间的转换。
- **测试错误处理**：验证应用服务是否正确地处理了领域服务和仓储接口返回的错误。

### 3.3 测试示例

#### 3.3.1 应用服务单元测试示例 (BuyoutPricePlanService)

假设 `BuyoutPricePlanService` 的 `CreateBuyoutPricePlan` 方法需要测试参数验证和仓储调用。

```go
func TestBuyoutPricePlanService_CreateBuyoutPricePlan_ValidationFailed(t *testing.T) {
    mockRepo := new(mocks.MockBuyoutPricePlanRepository) // 使用 Mock 仓储
    service := NewBuyoutPricePlanService(mockRepo)

    input := &CreateBuyoutPricePlanInput{
        VenueID: "", // 缺少 VenueID，导致验证失败
        Name:    "买断方案",
        // ... 其他必要参数
    }

    output, err := service.CreateBuyoutPricePlan(context.Background(), input)

    assert.Error(t, err, "参数验证失败时应返回错误")
    assert.Nil(t, output, "参数验证失败时不应返回 Output")
    mockRepo.AssertNotCalled(t, "Save", mock.Anything, mock.Anything) // 验证仓储未被调用
}

func TestBuyoutPricePlanService_CreateBuyoutPricePlan_Success(t *testing.T) {
    mockRepo := new(mocks.MockBuyoutPricePlanRepository) // 使用 Mock 仓储
    service := NewBuyoutPricePlanService(mockRepo)

    input := &CreateBuyoutPricePlanInput{
        ID:      "plan-001",
        VenueID: "venue-001",
        Name:    "买断方案",
        RoomTypeConfig: &common.RoomTypeConfig{ /* ... */ },
        TimeConfig:     &common.TimeConfig{ /* ... */ },
        Duration:       120,
        // ... 其他必要参数
    }

    // 预设 Mock 仓储的行为：Save 方法不返回错误
    mockRepo.On("Save", mock.Anything, mock.Anything).Return(nil).Once()

    output, err := service.CreateBuyoutPricePlan(context.Background(), input)

    assert.NoError(t, err, "创建成功时不应返回错误")
    assert.NotNil(t, output, "创建成功时应返回 Output")
    assert.Equal(t, input.ID, output.ID, "Output ID 应与 Input ID 一致")
    mockRepo.AssertCalled(t, "Save", mock.Anything, mock.Anything) // 验证仓储被调用
}
```

## 4. 单元测试工具和库

- **Go 内置 `testing` 包**：Go 语言自带的测试框架，用于编写和运行测试用例。
- **`github.com/stretchr/testify/assert`**：断言库，提供丰富的断言函数，使测试代码更简洁易读。
- **`github.com/golang/mock/gomock` 或 `github.com/stretchr/testify/mock`**：Mock 框架，用于创建 Mock 对象，隔离测试依赖。

## 5. 单元测试最佳实践

- **编写快速、独立的测试**：确保测试执行速度快，避免测试之间的相互影响。
- **测试代码与业务代码同步编写**：推荐采用 TDD（测试驱动开发）或 BDD（行为驱动开发）模式，先编写测试用例，再编写业务代码。
- **保持测试用例简洁易懂**：测试用例应该清晰地表达测试意图，易于理解和维护。
- **使用有意义的测试名称**：测试名称应该清晰地描述被测功能和测试场景。
- **追求高代码覆盖率**：尽量覆盖代码的各种分支和场景，提高测试覆盖率，但不要盲目追求 100% 覆盖率。
- **持续集成**：将单元测试集成到持续集成流程中，每次代码提交都自动运行单元测试，及时发现问题。

## 6. 总结

本文档描述了包厢价格方案DDD改造中的单元测试设计，包括领域模型和应用服务的单元测试策略、方法和最佳实践。通过编写高质量的单元测试，可以有效地提高代码质量，降低Bug率，并为系统的长期维护和演进奠定坚实的基础。

在实际开发过程中，应根据具体情况选择合适的测试策略和方法，并不断完善和优化单元测试，确保其能够有效地保障代码质量。 