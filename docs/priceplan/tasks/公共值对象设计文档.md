# 包厢价格方案 - 公共值对象设计文档

本文档描述了包厢价格方案领域模型中的公共值对象设计思路和使用方法。

## 1. TimeConfig 时间配置值对象

`TimeConfig` 值对象用于封装时间配置，包括星期配置和日期配置。它支持两种时间类型：基于星期的重复性时间范围和基于日期的特定时间范围。

### 设计思路

- 使用 `TimeType` 字段区分时间类型（`weekday` 或 `date`）
- 根据 `TimeType` 决定使用 `WeekdayTimeConfig` 还是 `DateTimeConfig`
- 提供 `Contains` 方法判断给定时间是否在配置的时间范围内
- 提供与 PO 对象的转换方法，方便数据持久化

### 使用方法

```go
// 创建星期配置
weekdayConfig := common.NewWeekdayTimeConfig([]int{1, 2, 3}, "08:00", "18:00")
timeConfig := common.NewTimeConfig("weekday").WithWeekdayConfig(weekdayConfig)

// 创建日期配置
dateConfig := common.NewDateTimeConfig("2023-01-01", "2023-12-31", "08:00", "18:00")
timeConfig = common.NewTimeConfig("date").WithDateConfig(dateConfig)

// 判断时间是否在范围内
now := time.Now()
if timeConfig.Contains(now) {
    // 在时间范围内
}

// 从 PO 对象创建
timeConfig, err := common.FromPO("weekday", "1,2,3", "", "", "08:00", "18:00")

// 转换为 PO 字段
timeType, weeks, dayStart, dayEnd, hourMinuteStart, hourMinuteEnd := timeConfig.ToPOFields()
```

## 2. PriceConfig 价格配置值对象

`PriceConfig` 值对象用于封装价格配置，支持基础价格、节假日价格、区域价格等多种价格类型。

### 设计思路

- 使用 `PriceConfigList` 表示价格配置列表，包含多个价格配置项
- 每个价格配置项 `PriceSettingItem` 包含名称、类型、默认价格、节假日价格和区域价格
- 支持节假日价格和区域价格的嵌套，例如节假日下的区域价格
- 提供灵活的价格查询方法，根据节假日ID和区域ID获取对应的价格

### 使用方法

```go
// 创建价格配置列表
priceConfigList := common.NewPriceConfigList()

// 添加基础价格配置项
baseItem := common.NewPriceSettingItem("基础", "baseRoom", 10000)
baseItem.AddAreaPrice("area1", 12000)
holidayPrice := baseItem.AddHolidayPrice("holiday1", 15000)
holidayPrice.AddAreaPrice("area1", 18000)
priceConfigList.AddPriceSettingItem(baseItem)

// 添加会员价格配置项
memberItem := common.NewPriceSettingItem("会员", "member", 8000)
priceConfigList.AddPriceSettingItem(memberItem)

// 获取价格
price := priceConfigList.GetPrice("baseRoom", "holiday1", "area1") // 获取基础节假日区域价格
```

## 3. Money 金额值对象

`Money` 值对象用于封装金额相关的操作，确保金额计算的准确性和一致性。

### 设计思路

- 使用 `int64` 类型存储金额，单位为分，避免浮点数计算误差
- 提供元和分之间的转换方法
- 提供各种金额计算方法，如加、减、乘、除等
- 提供金额比较方法，如相等、大于、小于等

### 使用方法

```go
// 创建金额
money1 := common.NewMoney(10000) // 100元
money2 := common.NewMoneyFromYuan(100.5) // 100.5元

// 金额计算
sum := money1.Add(money2)
diff := money1.Subtract(money2)
product := money1.Multiply(3)
quotient := money1.Divide(4)
discount := money1.MultiplyByFloat(0.8) // 打八折

// 金额比较
if money1.GreaterThan(money2) {
    // money1 大于 money2
}

// 获取金额
amountInCents := money1.Amount() // 获取分
amountInYuan := money1.Yuan() // 获取元
```

## 4. RoomTypeConfig 房间类型配置值对象

`RoomTypeConfig` 值对象用于封装房间类型配置，管理包厢价格方案适用的房间类型。

### 设计思路

- 使用 `RoomTypes` 列表存储多个房间类型
- 每个房间类型包含 ID 和名称
- 提供添加、移除、查询房间类型的方法
- 提供与 PO 对象的转换方法，方便数据持久化

### 使用方法

```go
// 创建房间类型配置
roomTypeConfig := common.NewRoomTypeConfig()
roomTypeConfig.AddRoomType("room1", "大包")
roomTypeConfig.AddRoomType("room2", "中包")

// 判断是否包含指定房间类型
if roomTypeConfig.ContainsRoomType("room1") {
    // 包含房间类型 room1
}

// 获取房间类型名称
name := roomTypeConfig.GetRoomTypeName("room1") // "大包"

// 从 PO 对象创建
roomTypeConfig.FromPO(roomTypeStr)

// 转换为 PO 字段
roomTypeStr, err := roomTypeConfig.ToPOString()
```

## 5. ChannelConfig 分销渠道配置值对象

`ChannelConfig` 值对象用于封装分销渠道配置，管理包厢价格方案适用的分销渠道。

### 设计思路

- 使用 `Channels` 列表存储多个分销渠道
- 每个分销渠道包含 ID 和名称
- 提供添加、移除、查询分销渠道的方法
- 提供与 PO 对象的转换方法，方便数据持久化

### 使用方法

```go
// 创建分销渠道配置
channelConfig := common.NewChannelConfig()
channelConfig.AddChannel("channel1", "官网")
channelConfig.AddChannel("channel2", "微信小程序")

// 判断是否包含指定分销渠道
if channelConfig.ContainsChannel("channel1") {
    // 包含分销渠道 channel1
}

// 获取分销渠道名称
name := channelConfig.GetChannelName("channel1") // "官网"

// 从 PO 对象创建
channelConfig.FromPO(channelStr)

// 转换为 PO 字段
channelStr, err := channelConfig.ToPOString()
```

## 6. AreaConfig 区域配置值对象

`AreaConfig` 值对象用于封装区域配置，管理包厢价格方案适用的区域。

### 设计思路

- 使用 `Areas` 列表存储多个区域
- 每个区域包含 ID 和名称
- 提供添加、移除、查询区域的方法
- 提供与 PO 对象的转换方法，方便数据持久化

### 使用方法

```go
// 创建区域配置
areaConfig := common.NewAreaConfig()
areaConfig.AddArea("area1", "一楼")
areaConfig.AddArea("area2", "二楼")

// 判断是否包含指定区域
if areaConfig.ContainsArea("area1") {
    // 包含区域 area1
}

// 获取区域名称
name := areaConfig.GetAreaName("area1") // "一楼"

// 从 PO 对象创建
areaConfig.FromPO(areaStr)

// 转换为 PO 字段
areaStr, err := areaConfig.ToPOString()
```

## 7. ProductSelection 产品选择配置值对象

`ProductSelection` 值对象用于封装产品选择配置，管理包厢价格方案包含的产品。

### 设计思路

- 使用 `Products` 列表存储多个产品
- 每个产品包含 ID、名称、分类、价格和数量
- 提供添加、移除、查询产品的方法
- 提供计算总价格的方法
- 提供与 PO 对象的转换方法，方便数据持久化

### 使用方法

```go
// 创建产品选择配置
productSelection := common.NewProductSelection()
productSelection.AddProduct("product1", "可乐", "饮料", 500, 2)
productSelection.AddProduct("product2", "薯片", "零食", 800, 1)

// 判断是否包含指定产品
if productSelection.ContainsProduct("product1") {
    // 包含产品 product1
}

// 获取产品
product := productSelection.GetProduct("product1")

// 更新产品数量
productSelection.UpdateProductQuantity("product1", 3)

// 获取总价格
totalPrice := productSelection.GetTotalPrice()

// 根据分类获取产品
drinks := productSelection.GetProductsByCategory("饮料")

// 获取所有分类
categories := productSelection.GetCategories()

// 从 PO 对象创建
productSelection.FromPO(productStr)

// 转换为 PO 字段
productStr, err := productSelection.ToPOString()
``` 