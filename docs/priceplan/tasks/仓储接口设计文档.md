# 包厢价格方案仓储接口设计文档

## 1. 概述

本文档描述了包厢价格方案DDD改造中的仓储接口设计。仓储接口是领域模型与持久化层之间的桥梁，它定义了领域模型如何被存储和检索的契约，而不关心具体的存储实现细节。在DDD中，仓储接口属于领域层，而其实现则属于基础设施层。

本次设计包含三个核心仓储接口：
1. `BuyoutPricePlanRepository`：买断价格方案仓储接口
2. `TimePricePlanRepository`：计时价格方案仓储接口
3. `TimeDiscountPlanRepository`：计时优惠方案仓储接口

## 2. 设计原则

仓储接口设计遵循以下原则：

1. **面向领域模型**：仓储接口的方法参数和返回值应该是领域模型，而非数据传输对象(DTO)或持久化对象(PO)。
2. **单一职责**：每个仓储接口只负责一个聚合根的持久化操作。
3. **接口隔离**：仓储接口应该只包含必要的方法，避免过度设计。
4. **依赖倒置**：领域层定义仓储接口，基础设施层实现这些接口，从而实现领域层对基础设施层的控制反转。
5. **上下文传递**：所有方法都接受`context.Context`参数，用于传递请求上下文、超时控制等。

## 3. 仓储接口设计

### 3.1 买断价格方案仓储接口

```go
package repository

import (
    "context"
    "erp_managent/domain/roomprice/model/buyoutpriceplan"
)

// BuyoutPricePlanRepository 买断价格方案仓储接口
type BuyoutPricePlanRepository interface {
    // Save 保存买断价格方案
    Save(ctx context.Context, plan *buyoutpriceplan.BuyoutPricePlan) error

    // FindByID 根据ID查找买断价格方案
    FindByID(ctx context.Context, id string) (*buyoutpriceplan.BuyoutPricePlan, error)

    // FindAll 查询所有买断价格方案
    FindAll(ctx context.Context) ([]*buyoutpriceplan.BuyoutPricePlan, error)

    // FindByVenueID 根据门店ID查询买断价格方案
    FindByVenueID(ctx context.Context, venueID string) ([]*buyoutpriceplan.BuyoutPricePlan, error)

    // FindByRoomTypeID 根据房间类型ID查询买断价格方案
    FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*buyoutpriceplan.BuyoutPricePlan, error)

    // FindEnabled 查询所有启用的买断价格方案
    FindEnabled(ctx context.Context) ([]*buyoutpriceplan.BuyoutPricePlan, error)

    // FindEnabledByVenueID 根据门店ID查询启用的买断价格方案
    FindEnabledByVenueID(ctx context.Context, venueID string) ([]*buyoutpriceplan.BuyoutPricePlan, error)

    // Delete 删除买断价格方案
    Delete(ctx context.Context, id string) error
}
```

### 3.2 计时价格方案仓储接口

```go
package repository

import (
    "context"
    "erp_managent/domain/roomprice/model/timepriceplan"
)

// TimePricePlanRepository 计时价格方案仓储接口
type TimePricePlanRepository interface {
    // Save 保存计时价格方案
    Save(ctx context.Context, plan *timepriceplan.TimePricePlan) error

    // FindByID 根据ID查找计时价格方案
    FindByID(ctx context.Context, id string) (*timepriceplan.TimePricePlan, error)

    // FindAll 查询所有计时价格方案
    FindAll(ctx context.Context) ([]*timepriceplan.TimePricePlan, error)

    // FindByVenueID 根据门店ID查询计时价格方案
    FindByVenueID(ctx context.Context, venueID string) ([]*timepriceplan.TimePricePlan, error)

    // FindByRoomTypeID 根据房间类型ID查询计时价格方案
    FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*timepriceplan.TimePricePlan, error)

    // FindEnabled 查询所有启用的计时价格方案
    FindEnabled(ctx context.Context) ([]*timepriceplan.TimePricePlan, error)

    // FindEnabledByVenueID 根据门店ID查询启用的计时价格方案
    FindEnabledByVenueID(ctx context.Context, venueID string) ([]*timepriceplan.TimePricePlan, error)

    // Delete 删除计时价格方案
    Delete(ctx context.Context, id string) error
}
```

### 3.3 计时优惠方案仓储接口

```go
package repository

import (
    "context"
    "erp_managent/domain/roomprice/model/timediscountplan"
)

// TimeDiscountPlanRepository 计时优惠方案仓储接口
type TimeDiscountPlanRepository interface {
    // Save 保存计时优惠方案
    Save(ctx context.Context, plan *timediscountplan.TimeDiscountPlan) error

    // FindByID 根据ID查找计时优惠方案
    FindByID(ctx context.Context, id string) (*timediscountplan.TimeDiscountPlan, error)

    // FindAll 查询所有计时优惠方案
    FindAll(ctx context.Context) ([]*timediscountplan.TimeDiscountPlan, error)

    // FindByVenueID 根据门店ID查询计时优惠方案
    FindByVenueID(ctx context.Context, venueID string) ([]*timediscountplan.TimeDiscountPlan, error)

    // FindByRoomTypeID 根据房间类型ID查询计时优惠方案
    FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*timediscountplan.TimeDiscountPlan, error)

    // FindEnabled 查询所有启用的计时优惠方案
    FindEnabled(ctx context.Context) ([]*timediscountplan.TimeDiscountPlan, error)

    // FindEnabledByVenueID 根据门店ID查询启用的计时优惠方案
    FindEnabledByVenueID(ctx context.Context, venueID string) ([]*timediscountplan.TimeDiscountPlan, error)

    // Delete 删除计时优惠方案
    Delete(ctx context.Context, id string) error
}
```

## 4. 方法说明

所有仓储接口都提供了以下核心方法：

### 4.1 Save 方法

```go
Save(ctx context.Context, plan *XXXPricePlan) error
```

- **功能**：保存价格方案到持久化存储。如果方案已存在（根据ID判断），则更新；否则创建新方案。
- **参数**：
  - `ctx context.Context`：上下文，用于传递请求上下文、超时控制等。
  - `plan *XXXPricePlan`：要保存的价格方案聚合根。
- **返回值**：
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.2 FindByID 方法

```go
FindByID(ctx context.Context, id string) (*XXXPricePlan, error)
```

- **功能**：根据ID查找价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
  - `id string`：价格方案的唯一标识符。
- **返回值**：
  - `*XXXPricePlan`：找到的价格方案聚合根，如果未找到则返回nil。
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.3 FindAll 方法

```go
FindAll(ctx context.Context) ([]*XXXPricePlan, error)
```

- **功能**：查询所有价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
- **返回值**：
  - `[]*XXXPricePlan`：价格方案聚合根的切片，如果没有方案则返回空切片。
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.4 FindByVenueID 方法

```go
FindByVenueID(ctx context.Context, venueID string) ([]*XXXPricePlan, error)
```

- **功能**：根据门店ID查询价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
  - `venueID string`：门店的唯一标识符。
- **返回值**：
  - `[]*XXXPricePlan`：价格方案聚合根的切片，如果没有方案则返回空切片。
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.5 FindByRoomTypeID 方法

```go
FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*XXXPricePlan, error)
```

- **功能**：根据房间类型ID查询价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
  - `roomTypeID string`：房间类型的唯一标识符。
- **返回值**：
  - `[]*XXXPricePlan`：价格方案聚合根的切片，如果没有方案则返回空切片。
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.6 FindEnabled 方法

```go
FindEnabled(ctx context.Context) ([]*XXXPricePlan, error)
```

- **功能**：查询所有启用的价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
- **返回值**：
  - `[]*XXXPricePlan`：价格方案聚合根的切片，如果没有方案则返回空切片。
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.7 FindEnabledByVenueID 方法

```go
FindEnabledByVenueID(ctx context.Context, venueID string) ([]*XXXPricePlan, error)
```

- **功能**：根据门店ID查询启用的价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
  - `venueID string`：门店的唯一标识符。
- **返回值**：
  - `[]*XXXPricePlan`：价格方案聚合根的切片，如果没有方案则返回空切片。
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

### 4.8 Delete 方法

```go
Delete(ctx context.Context, id string) error
```

- **功能**：删除价格方案。
- **参数**：
  - `ctx context.Context`：上下文。
  - `id string`：价格方案的唯一标识符。
- **返回值**：
  - `error`：操作过程中发生的错误，如果操作成功则返回nil。

## 5. 使用示例

### 5.1 应用服务中使用仓储接口

```go
package application

import (
    "context"
    "erp_managent/domain/roomprice/model/buyoutpriceplan"
    "erp_managent/domain/roomprice/repository"
)

type BuyoutPricePlanService struct {
    repo repository.BuyoutPricePlanRepository
}

func NewBuyoutPricePlanService(repo repository.BuyoutPricePlanRepository) *BuyoutPricePlanService {
    return &BuyoutPricePlanService{
        repo: repo,
    }
}

func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // 创建领域模型
    plan, err := buyoutpriceplan.NewBuyoutPricePlan(
        input.ID,
        input.VenueID,
        input.Name,
        input.RoomTypeConfig,
        input.TimeConfig,
        input.Duration,
        input.AdvanceDisableDuration,
        input.IsExcessIncluded,
        input.HasMinimumCharge,
        input.MinimumCharge,
    )
    if err != nil {
        return nil, err
    }

    // 设置其他属性
    // ...

    // 保存到仓储
    err = s.repo.Save(ctx, plan)
    if err != nil {
        return nil, err
    }

    // 转换为输出DTO
    output := &BuyoutPricePlanOutput{
        ID:      plan.ID(),
        VenueID: plan.VenueID(),
        Name:    plan.Name(),
        // ...其他字段
    }

    return output, nil
}

// 其他方法...
```

### 5.2 仓储实现示例（基础设施层）

```go
package infra

import (
    "context"
    "erp_managent/domain/roomprice/model/buyoutpriceplan"
    "erp_managent/domain/roomprice/repository"
    "erp_managent/service"
    "erp_managent/service/po"
)

type BuyoutPricePlanRepositoryImpl struct {
    pricePlanService service.PricePlanService
}

func NewBuyoutPricePlanRepositoryImpl(pricePlanService service.PricePlanService) repository.BuyoutPricePlanRepository {
    return &BuyoutPricePlanRepositoryImpl{
        pricePlanService: pricePlanService,
    }
}

func (r *BuyoutPricePlanRepositoryImpl) Save(ctx context.Context, plan *buyoutpriceplan.BuyoutPricePlan) error {
    // 将领域模型转换为PO
    pricePlanPO := &po.PricePlan{
        ID:      plan.ID(),
        VenueID: plan.VenueID(),
        Name:    plan.Name(),
        // ...其他字段转换
    }

    // 调用基础服务保存PO
    return r.pricePlanService.SavePricePlan(ctx, pricePlanPO)
}

// 其他方法实现...
```

## 6. 仓储接口与聚合根的关系

在DDD中，仓储接口与聚合根有着密切的关系：

1. **一对一关系**：每个聚合根对应一个仓储接口，仓储接口只负责一个聚合根的持久化操作。
2. **聚合边界**：仓储接口的方法参数和返回值是聚合根，这强化了聚合的边界，确保聚合作为一个整体被持久化和检索。
3. **领域完整性**：仓储接口的设计应该确保领域模型的完整性，例如，保存方法应该保存整个聚合，而不是聚合的一部分。

## 7. 总结

本文档描述了包厢价格方案DDD改造中的仓储接口设计。通过定义清晰的仓储接口，我们实现了领域层与基础设施层的解耦，使得领域模型可以专注于业务逻辑，而不必关心持久化细节。同时，仓储接口的设计也为后续的基础设施层实现提供了明确的契约。

在实际应用中，仓储接口的实现将在基础设施层完成，通过适配现有的服务层代码，将领域模型与持久化对象进行转换，从而实现领域模型的持久化和检索。 