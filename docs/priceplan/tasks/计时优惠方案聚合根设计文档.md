# 计时优惠方案聚合根设计文档

## 1. 概述

计时优惠方案（TimeDiscountPlan）是包厢价格方案DDD改造中的一个核心聚合根，它代表了一种特定的包厢消费优惠模式：在计时收费的基础上，根据不同的优惠策略提供额外的优惠。计时优惠方案包含了丰富的业务规则和配置，如时间范围、优惠策略、适用房型、优惠所需时长等。

## 2. 领域模型设计

### 2.1 核心属性

计时优惠方案聚合根包含以下核心属性：

- **基本信息**
  - `id`: 唯一标识符
  - `venueID`: 门店ID
  - `name`: 方案名称
  - `roomTypeConfig`: 包厢类型配置（值对象）
  - `timeConfig`: 时间配置（值对象）
  - `isEnabled`: 是否启用
  - `channelConfig`: 投放渠道配置（值对象）
  - `areaConfig`: 投放区域配置（值对象）
  - `discountStrategy`: 优惠策略（值对象）
  - `requiredDuration`: 优惠所需时长（分钟）
  - `requiredConsumption`: 优惠所需消费金额（值对象）
  - `giftPlan`: 消费赠券
  - `supportsPoints`: 是否支持积分

- **元数据**
  - `createTime`: 创建时间
  - `updateTime`: 修改时间
  - `state`: 状态
  - `version`: 版本号

### 2.2 值对象依赖

计时优惠方案聚合根依赖以下值对象：

- `RoomTypeConfig`: 包厢类型配置，定义了方案适用的包厢类型
- `TimeConfig`: 时间配置，定义了方案的有效时间范围
- `ChannelConfig`: 投放渠道配置，定义了方案的销售渠道
- `AreaConfig`: 投放区域配置，定义了方案的适用区域
- `Money`: 金额值对象，用于表示优惠所需消费金额
- `DiscountStrategy`: 优惠策略接口，定义了不同类型的优惠策略

## 3. 优惠策略设计

### 3.1 策略模式与多态

计时优惠方案采用策略模式来实现不同类型的优惠策略，具有以下特点：

1. **策略接口**：定义了所有优惠策略必须实现的方法，包括：
   - `Type()`: 返回策略类型
   - `CalculateDiscount()`: 计算优惠金额
   - `Validate()`: 验证策略配置是否有效
   - `ToJSON()`: 将策略转换为JSON字符串

2. **具体策略实现**：提供了四种具体的优惠策略实现：
   - `DurationDiscountStrategy`: 赠送时长策略
   - `TimeSlotDiscountStrategy`: 赠送时段策略
   - `RoomChargeToGoodsDiscountStrategy`: 房费抵商品策略
   - `GoodsToRoomChargeDiscountStrategy`: 商品抵房费策略

3. **工厂方法**：提供了从JSON字符串创建优惠策略的工厂方法，根据策略类型创建对应的策略实例。

### 3.2 优惠策略详解

#### 3.2.1 赠送时长策略（DurationDiscountStrategy）

赠送时长策略是指在满足一定条件（如购买一定时长的包厢）后，赠送额外的使用时长。

- **核心属性**：
  - `GiftDuration`: 赠送时长（分钟）

- **优惠计算逻辑**：
  - 该策略不直接返回优惠金额，而是通过赠送时长来提供优惠
  - 在应用层，需要根据赠送的时长计算实际的优惠金额

#### 3.2.2 赠送时段策略（TimeSlotDiscountStrategy）

赠送时段策略是指在特定的时间段内提供免费使用包厢的优惠。

- **核心属性**：
  - 无额外属性，复用TimeConfig来定义赠送的时间段

- **优惠计算逻辑**：
  - 该策略不直接返回优惠金额，而是通过赠送特定时段来提供优惠
  - 在应用层，需要根据赠送的时段计算实际的优惠金额

#### 3.2.3 房费抵商品策略（RoomChargeToGoodsDiscountStrategy）

房费抵商品策略是指将部分房费用于抵扣指定类别的商品消费。

- **核心属性**：
  - `DeductibleGoods`: 可抵商品类别
  - `MaxDeductibleAmount`: 累计最高可抵扣金额

- **优惠计算逻辑**：
  - 该策略返回的优惠金额为最高可抵扣金额
  - 在应用层，需要根据实际消费的商品类别和金额计算实际的抵扣金额

#### 3.2.4 商品抵房费策略（GoodsToRoomChargeDiscountStrategy）

商品抵房费策略是指将部分商品消费用于抵扣房费。

- **核心属性**：
  - 无额外属性

- **优惠计算逻辑**：
  - 该策略不直接返回优惠金额，因为具体抵扣金额需要在应用层根据实际消费计算
  - 在应用层，需要根据实际消费的商品金额计算可抵扣的房费金额

### 3.3 策略配置示例

```json
// 赠送时长策略
{
  "giftDuration": 60
}

// 房费抵商品策略
{
  "deductibleGoods": ["food", "drink"],
  "maxDeductibleAmount": 5000
}
```

## 4. 核心业务方法

计时优惠方案聚合根提供以下核心业务方法：

### 4.1 构造与基本操作

- `NewTimeDiscountPlan`: 创建一个新的计时优惠方案，并进行必要的参数校验
- `Enable/Disable`: 启用/禁用方案
- `UpdateTimeConfig`: 更新时间配置
- `UpdateDiscountStrategy`: 更新优惠策略
- `SetRequiredDuration`: 设置优惠所需时长
- `SetRequiredConsumption`: 设置优惠所需消费金额

### 4.2 业务逻辑方法

- `IsValid`: 检查方案在指定时间是否有效（检查是否启用、是否在时间范围内等）
- `CalculateDiscount`: 根据优惠策略、时间、使用时长等信息计算优惠金额

### 4.3 序列化与反序列化

- `ToJSON`: 将计时优惠方案转换为JSON字符串
- `FromJSON`: 从JSON字符串创建计时优惠方案

## 5. 使用示例

### 5.1 创建计时优惠方案

```go
// 创建必要的值对象
roomTypeConfig := common.NewRoomTypeConfig()
roomTypeConfig.AddRoomType("room1", "大包")

timeConfig := common.NewTimeConfig("weekday")
weekdayConfig := common.NewWeekdayTimeConfig([]int{1, 2, 3, 4, 5}, "08:00", "23:00")
timeConfig.WithWeekdayConfig(weekdayConfig)

// 创建优惠策略
strategy, err := timediscountplan.NewDurationDiscountStrategy(60) // 赠送60分钟
if err != nil {
    // 处理错误
}

// 创建计时优惠方案
plan, err := timediscountplan.NewTimeDiscountPlan(
    "plan1",
    "venue1",
    "测试优惠方案",
    roomTypeConfig,
    timeConfig,
    strategy,
    120, // 需要至少买2小时
    common.NewMoney(0),
)
if err != nil {
    // 处理错误
}

// 启用方案
plan.Enable()
```

### 5.2 计算优惠金额

```go
// 计算优惠金额
ctx := context.Background()
timePricePlan := ... // 获取计时价格方案
duration := 120 // 使用时长2小时
discount, err := plan.CalculateDiscount(ctx, timePricePlan, duration)
if err != nil {
    // 处理错误
}

fmt.Printf("优惠金额: %.2f元\n", discount.Yuan())
```

## 6. 仓储接口

计时优惠方案仓储接口定义了与持久化相关的操作：

```go
type TimeDiscountPlanRepository interface {
    // Save 保存计时优惠方案
    Save(ctx context.Context, plan *timediscountplan.TimeDiscountPlan) error
    
    // FindByID 根据ID查找计时优惠方案
    FindByID(ctx context.Context, id string) (*timediscountplan.TimeDiscountPlan, error)
    
    // FindAll 查询所有计时优惠方案
    FindAll(ctx context.Context) ([]*timediscountplan.TimeDiscountPlan, error)
    
    // FindByVenueID 根据门店ID查询计时优惠方案
    FindByVenueID(ctx context.Context, venueID string) ([]*timediscountplan.TimeDiscountPlan, error)
    
    // FindByRoomTypeID 根据房间类型ID查询计时优惠方案
    FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*timediscountplan.TimeDiscountPlan, error)
    
    // FindEnabled 查询所有启用的计时优惠方案
    FindEnabled(ctx context.Context) ([]*timediscountplan.TimeDiscountPlan, error)
    
    // FindEnabledByVenueID 根据门店ID查询启用的计时优惠方案
    FindEnabledByVenueID(ctx context.Context, venueID string) ([]*timediscountplan.TimeDiscountPlan, error)
    
    // Delete 删除计时优惠方案
    Delete(ctx context.Context, id string) error
}
```

## 7. 与计时价格方案的关系

计时优惠方案与计时价格方案是紧密关联的两个聚合根：

1. **计时价格方案**：定义了基础的计时收费规则，包括最小计费时长、计费单位、价格配置等。
2. **计时优惠方案**：在计时价格方案的基础上，提供额外的优惠策略，如赠送时长、房费抵商品等。

在实际应用中，计时优惠方案通常需要与计时价格方案配合使用，例如：

- 在计算最终价格时，先使用计时价格方案计算基础价格，然后应用计时优惠方案计算优惠金额。
- 在赠送时长策略中，需要根据计时价格方案的价格配置计算赠送时长对应的优惠金额。

## 8. 总结

计时优惠方案聚合根是包厢价格方案DDD改造中的核心领域模型之一，它通过丰富的配置和业务规则，实现了计时模式下的各种优惠策略。其中，优惠策略采用策略模式实现，使得系统能够灵活应对各种优惠类型的需求，同时保持了良好的可扩展性。计时优惠方案与计时价格方案紧密配合，共同构成了完整的计时收费与优惠体系。 