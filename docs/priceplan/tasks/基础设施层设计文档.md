 # 包厢价格方案 - 基础设施层设计文档

## 1. 概述

本文档描述了包厢价格方案 DDD 改造中基础设施层的设计和实现。基础设施层是 DDD 分层架构中的最外层，负责与外部系统（如数据库、消息队列、第三方服务等）进行交互，并为领域层提供技术支持。

在包厢价格方案的 DDD 改造中，基础设施层主要包括仓储接口的实现，用于将领域模型持久化到数据库，以及从数据库中恢复领域模型。

## 2. 基础设施层组件

### 2.1 仓储实现

基础设施层实现了领域层定义的仓储接口，主要包括以下三个仓储实现：

1. **BuyoutPricePlanRepositoryImpl**：买断价格方案仓储实现
2. **TimePricePlanRepositoryImpl**：计时价格方案仓储实现
3. **TimeDiscountPlanRepositoryImpl**：计时优惠方案仓储实现

这些仓储实现负责将领域模型（聚合根）转换为持久化对象（PO），并通过现有的 `erp_managent/service/impl/PricePlanService` 进行数据库操作。

### 2.2 适配现有服务

为了与现有系统兼容，基础设施层采用了适配器模式，通过适配 `erp_managent/service` 目录下的代码来实现对数据库的访问。具体来说，仓储实现类持有一个 `PricePlanService` 实例，并通过该实例进行数据库操作。

```go
type BuyoutPricePlanRepositoryImpl struct {
    PricePlanService *impl.PricePlanService
}
```

### 2.3 数据转换

仓储实现类负责在领域模型和持久化对象之间进行转换。这包括：

1. **toPO 方法**：将领域模型（聚合根）转换为持久化对象（PO）
2. **toDomainModel 方法**：将持久化对象（PO）转换为领域模型（聚合根）

这些转换方法处理了领域模型和持久化对象之间的字段映射，以及复杂字段（如 JSON 字符串）的序列化和反序列化。

## 3. 仓储实现详解

### 3.1 BuyoutPricePlanRepositoryImpl

`BuyoutPricePlanRepositoryImpl` 实现了 `BuyoutPricePlanRepository` 接口，提供了买断价格方案的持久化和查询功能。

#### 3.1.1 主要方法

- **Save**：保存买断价格方案，根据 ID 是否为空决定是创建新记录还是更新已有记录
- **FindByID**：根据 ID 查找买断价格方案
- **FindAll**：查找所有买断价格方案
- **Delete**：删除买断价格方案
- **FindByRoomTypeID**：根据房型 ID 查找买断价格方案
- **FindByVenueID**：根据场馆 ID 查找买断价格方案
- **FindEnabled**：查找所有启用的买断价格方案
- **FindEnabledByVenueID**：根据场馆 ID 查找启用的买断价格方案

#### 3.1.2 数据转换

`BuyoutPricePlanRepositoryImpl` 提供了两个数据转换方法：

- **toPO**：将 `BuyoutPricePlan` 聚合根转换为 `PricePlan` PO 对象
- **toDomainModel**：将 `PricePlan` PO 对象转换为 `BuyoutPricePlan` 聚合根

这些方法处理了字段映射和复杂字段的序列化/反序列化，如 `PriceConfigList`、`RoomTypeConfig`、`TimeConfig` 等。

### 3.2 TimePricePlanRepositoryImpl

`TimePricePlanRepositoryImpl` 实现了 `TimePricePlanRepository` 接口，提供了计时价格方案的持久化和查询功能。

#### 3.2.1 主要方法

与 `BuyoutPricePlanRepositoryImpl` 类似，`TimePricePlanRepositoryImpl` 提供了一系列方法用于计时价格方案的持久化和查询。

#### 3.2.2 数据转换

`TimePricePlanRepositoryImpl` 提供了两个数据转换方法：

- **toPO**：将 `TimePricePlan` 聚合根转换为 `PricePlan` PO 对象
- **toDomainModel**：将 `PricePlan` PO 对象转换为 `TimePricePlan` 聚合根

### 3.3 TimeDiscountPlanRepositoryImpl

`TimeDiscountPlanRepositoryImpl` 实现了 `TimeDiscountPlanRepository` 接口，提供了计时优惠方案的持久化和查询功能。

#### 3.3.1 主要方法

与其他仓储实现类似，`TimeDiscountPlanRepositoryImpl` 提供了一系列方法用于计时优惠方案的持久化和查询。

#### 3.3.2 数据转换

`TimeDiscountPlanRepositoryImpl` 提供了两个数据转换方法：

- **toPO**：将 `TimeDiscountPlan` 聚合根转换为 `PricePlan` PO 对象
- **toDomainModel**：将 `PricePlan` PO 对象转换为 `TimeDiscountPlan` 聚合根

## 4. 错误处理

基础设施层的仓储实现类对可能出现的错误进行了处理，包括：

1. **数据库操作错误**：如连接错误、查询错误等
2. **数据转换错误**：如 JSON 序列化/反序列化错误
3. **参数校验错误**：如必填字段为空等

错误处理采用了 Go 语言的标准错误处理方式，通过返回 error 类型来表示错误。同时，为了提供更详细的错误信息，使用了 `fmt.Errorf` 函数包装原始错误，添加上下文信息。

## 5. 测试

基础设施层的仓储实现类编写了单元测试，用于验证其功能的正确性。测试用例包括：

1. **Save_Create**：测试创建新记录
2. **Save_Update**：测试更新已有记录
3. **FindByID_Success**：测试根据 ID 查找记录成功
4. **FindByID_NotFound**：测试根据 ID 查找记录未找到
5. **Delete**：测试删除记录
6. **FindByRoomTypeID**：测试根据房型 ID 查找记录
7. **FindByVenueID**：测试根据场馆 ID 查找记录
8. **FindEnabled**：测试查找所有启用的记录
9. **FindEnabledByVenueID**：测试根据场馆 ID 查找启用的记录

由于测试环境的限制，这些测试用例使用了 mock 对象来模拟 `PricePlanService` 的行为，避免了对实际数据库的依赖。

## 6. 总结

基础设施层的设计和实现遵循了 DDD 的原则，通过适配现有系统，实现了领域层定义的仓储接口。这种设计使得领域层可以专注于业务逻辑，而不必关心数据持久化的细节。同时，通过数据转换，实现了领域模型和持久化对象之间的映射，保证了数据的一致性和完整性。