# 包厢价格方案领域服务设计文档

## 1. 概述

本文档描述了包厢价格方案中领域服务的设计思路、职责划分和使用场景。领域服务是DDD中处理跨聚合根业务逻辑的关键组件，它包含那些无法自然归属于任何单一聚合根的业务逻辑。

在包厢价格方案DDD设计中，领域服务主要负责处理需要跨多个聚合根协同的复杂业务逻辑，例如价格计算、价格比较、方案匹配等业务场景。

## 2. 领域服务清单

在当前的包厢价格方案设计中，主要有以下领域服务：

1. **PriceCalculationService**：价格计算服务，用于处理跨聚合根的复杂价格计算逻辑

## 3. 价格计算服务（PriceCalculationService）详细设计

### 3.1 职责

`PriceCalculationService` 主要负责：

1. 处理需要考虑买断方案、计时方案和计时优惠方案组合的复杂价格计算
2. 提供基于不同使用场景（如特定时间段、不同会员类型、节假日等）的价格比较功能
3. 根据实际业务需求，找出最优的价格方案组合

### 3.2 接口定义

```go
package service

import (
	"context"
	"time"

	"voderpltvv/erp_managent/domain/roomprice/model/common"
)

// PriceCalculationRequest 价格计算请求
type PriceCalculationRequest struct {
	VenueID          string    // 门店ID
	RoomTypeID       string    // 包厢类型ID
	StartTime        time.Time // 开始时间
	EndTime          time.Time // 结束时间
	MemberType       string    // 会员类型
	HolidayID        string    // 节假日ID
	AreaID           string    // 区域ID
	ChannelID        string    // 渠道ID
	EstimatedSpending int64     // 预计消费金额（用于计算优惠）
}

// PriceCalculationResult 价格计算结果
type PriceCalculationResult struct {
	PlanType       string       // 方案类型："buyout"或"time"
	PlanID         string       // 方案ID
	DiscountPlanID string       // 优惠方案ID（如果有）
	TotalPrice     common.Money // 总价
	OriginalPrice  common.Money // 原价（未优惠前）
	DiscountAmount common.Money // 优惠金额
	Description    string       // 价格描述
}

// PriceCalculationService 价格计算服务接口
type PriceCalculationService interface {
	// CalculateBestPrice 计算最优价格
	// 根据请求参数，计算并返回最优价格方案
	CalculateBestPrice(ctx context.Context, request *PriceCalculationRequest) (*PriceCalculationResult, error)

	// ComparePrices 比较不同方案的价格
	// 返回所有可用方案的价格计算结果，按价格从低到高排序
	ComparePrices(ctx context.Context, request *PriceCalculationRequest) ([]*PriceCalculationResult, error)

	// CalculateBuyoutPrice 计算买断方案价格
	CalculateBuyoutPrice(ctx context.Context, request *PriceCalculationRequest) (*PriceCalculationResult, error)

	// CalculateTimePrice 计算计时方案价格
	CalculateTimePrice(ctx context.Context, request *PriceCalculationRequest) (*PriceCalculationResult, error)

	// CalculateTimePriceWithDiscount 计算计时方案价格（含优惠）
	CalculateTimePriceWithDiscount(ctx context.Context, request *PriceCalculationRequest) (*PriceCalculationResult, error)
}
```

### 3.3 实现逻辑

价格计算服务的核心算法包括：

1. **最优价格计算**：
   - 分别计算买断方案和计时方案（含优惠）的价格
   - 比较各方案价格，选择价格最低的方案
   - 对于计时方案，还需要考虑各种优惠方案的组合

2. **买断价格计算**：
   - 查找符合条件的买断方案（根据时间、包厢类型、区域等）
   - 调用买断方案聚合根的 `CalculatePrice` 方法计算价格
   - 考虑会员折扣、节假日加价等因素

3. **计时价格计算**：
   - 计算总使用时长
   - 查找符合条件的计时方案
   - 根据计时方案的计费规则（最小计费时长、计费单位等）计算基础价格
   - 应用会员折扣、节假日加价等价格调整

4. **优惠计算**：
   - 查找适用的优惠方案
   - 根据优惠策略（赠送时长、赠送时段、房费抵商品、商品抵房费）计算优惠金额
   - 将优惠金额应用到计时方案价格上

### 3.4 使用场景

价格计算服务主要应用于以下业务场景：

1. **预订场景**：用户在线预订包厢时，系统需要计算并显示不同方案的价格供用户选择
2. **前台收银**：店内前台为用户办理入住手续时，需要计算并显示最优价格方案
3. **实时变更**：用户在使用中变更包厢或延长使用时间时，需要重新计算价格
4. **账单结算**：用户结账时，系统需要根据实际使用情况计算最终价格

### 3.5 与聚合根的协作

价格计算服务需要与三个核心聚合根密切协作：

1. **BuyoutPricePlan**：调用其 `CalculatePrice` 方法计算买断价格
2. **TimePricePlan**：调用其 `CalculatePrice` 方法计算计时价格
3. **TimeDiscountPlan**：调用其 `CalculateDiscount` 方法计算优惠金额

同时，价格计算服务还需要通过相应的仓储接口查询符合条件的价格方案，这些仓储包括：

1. **BuyoutPricePlanRepository**
2. **TimePricePlanRepository**
3. **TimeDiscountPlanRepository**

## 4. 扩展性考虑

价格计算服务设计中考虑了以下扩展性因素：

1. **新的价格影响因素**：系统可以轻松添加新的价格影响因素，如新的会员等级、特殊活动价格等
2. **新的优惠策略**：可以通过实现 `DiscountStrategy` 接口来添加新的优惠策略
3. **新的计费规则**：价格计算逻辑设计为可配置的，可以适应不同的计费规则变化

## 5. 性能优化

价格计算服务涉及复杂的计算和多次数据库查询，为了优化性能，采取了以下措施：

1. **缓存机制**：对频繁使用的价格方案信息进行缓存
2. **批量查询**：一次性查询所有可能需要的价格方案，减少数据库交互次数
3. **并行计算**：对于互不影响的价格计算（如买断价格和计时价格），采用并行计算提高效率

## 6. 总结

价格计算服务是包厢价格方案DDD设计中的核心领域服务，它封装了复杂的跨聚合根业务逻辑，使得价格相关的计算逻辑更加清晰、可维护和可扩展。通过良好的接口设计和内部实现，价格计算服务可以满足各种复杂的价格计算需求，为上层应用提供强大的价格计算能力。 