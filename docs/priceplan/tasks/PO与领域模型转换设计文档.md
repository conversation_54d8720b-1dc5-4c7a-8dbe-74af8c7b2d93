 # 包厢价格方案 - PO与领域模型转换设计文档

## 1. 概述

本文档描述了包厢价格方案 DDD 改造中 PO（持久化对象）与领域模型之间的转换设计和实现。在 DDD 架构中，领域模型是业务逻辑的核心，而 PO 是数据持久化的载体。两者之间的转换是基础设施层的重要职责，确保了领域模型可以被正确地持久化和恢复。

## 2. 转换原则

PO 与领域模型之间的转换遵循以下原则：

1. **完整性**：确保领域模型的所有必要信息都能被持久化，并且能够从持久化数据中完全恢复。
2. **一致性**：确保转换过程中不会丢失或篡改数据，保持数据的一致性。
3. **解耦**：领域模型不应该依赖于持久化细节，转换逻辑应该封装在基础设施层中。
4. **可测试性**：转换逻辑应该是可测试的，以确保其正确性。

## 3. 转换方向

PO 与领域模型之间的转换有两个方向：

1. **领域模型 -> PO**：将领域模型转换为 PO，用于持久化到数据库。
2. **PO -> 领域模型**：将 PO 转换为领域模型，用于从数据库中恢复领域模型。

## 4. 转换实现

### 4.1 领域模型 -> PO

领域模型到 PO 的转换由仓储实现类的 `toPO` 方法完成。以 `BuyoutPricePlanRepositoryImpl` 为例：

```go
func (r *BuyoutPricePlanRepositoryImpl) toPO(plan *buyoutpriceplan.BuyoutPricePlan) (*po.PricePlan, error) {
    // 创建 PO 对象
    pricePlanPO := &po.PricePlan{}
    
    // 设置基本字段
    pricePlanPO.Id = strPtr(plan.ID())
    pricePlanPO.VenueId = strPtr(plan.VenueID())
    pricePlanPO.Name = strPtr(plan.Name())
    pricePlanPO.ConsumptionMode = strPtr("buyout") // 买断模式
    
    // 设置其他字段
    // ...
    
    // 处理复杂字段（如 JSON 序列化）
    roomTypeJSON, err := json.Marshal(plan.RoomTypeConfig().RoomTypes())
    if err != nil {
        return nil, fmt.Errorf("序列化包厢类型配置失败: %w", err)
    }
    pricePlanPO.RoomType = strPtr(string(roomTypeJSON))
    
    // 处理价格配置
    priceConfigJSON, err := json.Marshal(plan.PriceConfigList())
    if err != nil {
        return nil, fmt.Errorf("序列化价格配置失败: %w", err)
    }
    pricePlanPO.PriceConfig = strPtr(string(priceConfigJSON))
    
    return pricePlanPO, nil
}
```

### 4.2 PO -> 领域模型

PO 到领域模型的转换由仓储实现类的 `toDomainModel` 方法完成。以 `BuyoutPricePlanRepositoryImpl` 为例：

```go
func (r *BuyoutPricePlanRepositoryImpl) toDomainModel(pricePlanPO *po.PricePlan) (*buyoutpriceplan.BuyoutPricePlan, error) {
    // 处理基本字段
    id := *pricePlanPO.Id
    venueID := *pricePlanPO.VenueId
    name := *pricePlanPO.Name
    
    // 处理复杂字段（如 JSON 反序列化）
    var roomTypes []string
    err := json.Unmarshal([]byte(*pricePlanPO.RoomType), &roomTypes)
    if err != nil {
        return nil, fmt.Errorf("反序列化包厢类型配置失败: %w", err)
    }
    roomTypeConfig := common.NewRoomTypeConfig(roomTypes)
    
    // 处理时间配置
    timeConfig := common.NewTimeConfig(*pricePlanPO.TimeType)
    if *pricePlanPO.TimeType == "weekday" {
        // 处理星期时间配置
        // ...
    } else if *pricePlanPO.TimeType == "date" {
        // 处理日期时间配置
        // ...
    }
    
    // 处理价格配置
    var priceConfigList common.PriceConfigList
    if pricePlanPO.PriceConfig != nil {
        err := json.Unmarshal([]byte(*pricePlanPO.PriceConfig), &priceConfigList)
        if err != nil {
            return nil, fmt.Errorf("反序列化价格配置失败: %w", err)
        }
    }
    
    // 创建领域模型
    plan, err := buyoutpriceplan.NewBuyoutPricePlan(
        id,
        venueID,
        name,
        roomTypeConfig,
        timeConfig,
        *pricePlanPO.Duration,
    )
    if err != nil {
        return nil, fmt.Errorf("创建买断价格方案失败: %w", err)
    }
    
    // 设置其他字段
    // ...
    
    // 设置价格配置
    plan.UpdatePriceConfig(priceConfigList)
    
    return plan, nil
}
```

## 5. 复杂字段的处理

在 PO 与领域模型之间的转换中，有一些复杂字段需要特殊处理，主要包括：

### 5.1 JSON 序列化/反序列化

一些复杂的领域模型字段，如 `RoomTypeConfig`、`ChannelConfig`、`AreaConfig`、`PriceConfigList` 等，在 PO 中以 JSON 字符串的形式存储。这些字段的转换涉及 JSON 的序列化和反序列化。

```go
// 领域模型 -> PO（序列化）
roomTypeJSON, err := json.Marshal(plan.RoomTypeConfig().RoomTypes())
if err != nil {
    return nil, fmt.Errorf("序列化包厢类型配置失败: %w", err)
}
pricePlanPO.RoomType = strPtr(string(roomTypeJSON))

// PO -> 领域模型（反序列化）
var roomTypes []string
err := json.Unmarshal([]byte(*pricePlanPO.RoomType), &roomTypes)
if err != nil {
    return nil, fmt.Errorf("反序列化包厢类型配置失败: %w", err)
}
roomTypeConfig := common.NewRoomTypeConfig(roomTypes)
```

### 5.2 时间配置的处理

时间配置（`TimeConfig`）是一个复杂的值对象，包含星期配置（`WeekdayTimeConfig`）或日期配置（`DateTimeConfig`）。在 PO 中，时间配置被拆分为多个字段存储，如 `TimeType`、`Weeks`、`HourMinuteStart`、`HourMinuteEnd` 等。

```go
// 领域模型 -> PO
timeConfig := plan.TimeConfig()
pricePlanPO.TimeType = strPtr(timeConfig.TimeType)
if timeConfig.TimeType == "weekday" {
    weekdayConfig := timeConfig.WeekdayConfig
    weeks := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(weekdayConfig.Weeks)), ","), "[]")
    pricePlanPO.Weeks = strPtr(weeks)
    pricePlanPO.HourMinuteStart = strPtr(weekdayConfig.HourMinuteStart)
    pricePlanPO.HourMinuteEnd = strPtr(weekdayConfig.HourMinuteEnd)
} else if timeConfig.TimeType == "date" {
    dateConfig := timeConfig.DateConfig
    pricePlanPO.DayStart = strPtr(dateConfig.DayStart)
    pricePlanPO.DayEnd = strPtr(dateConfig.DayEnd)
    pricePlanPO.HourMinuteStart = strPtr(dateConfig.HourMinuteStart)
    pricePlanPO.HourMinuteEnd = strPtr(dateConfig.HourMinuteEnd)
}

// PO -> 领域模型
timeConfig := common.NewTimeConfig(*pricePlanPO.TimeType)
if *pricePlanPO.TimeType == "weekday" {
    // 解析星期数组
    var weeks []int
    if pricePlanPO.Weeks != nil {
        weekStrs := strings.Split(*pricePlanPO.Weeks, ",")
        for _, weekStr := range weekStrs {
            week, err := strconv.Atoi(weekStr)
            if err != nil {
                return nil, fmt.Errorf("解析星期配置失败: %w", err)
            }
            weeks = append(weeks, week)
        }
    }
    
    weekdayConfig := common.NewWeekdayTimeConfig(
        weeks,
        *pricePlanPO.HourMinuteStart,
        *pricePlanPO.HourMinuteEnd,
    )
    timeConfig.WeekdayConfig = weekdayConfig
} else if *pricePlanPO.TimeType == "date" {
    dateConfig := common.NewDateTimeConfig(
        *pricePlanPO.DayStart,
        *pricePlanPO.DayEnd,
        *pricePlanPO.HourMinuteStart,
        *pricePlanPO.HourMinuteEnd,
    )
    timeConfig.DateConfig = dateConfig
}
```

### 5.3 优惠策略的处理

计时优惠方案（`TimeDiscountPlan`）中的优惠策略（`DiscountStrategy`）是一个接口类型，有多种实现。在 PO 中，优惠策略以 JSON 字符串的形式存储在 `DiscountStrategy` 字段中，同时使用 `DiscountMode` 字段标识具体的策略类型。

```go
// 领域模型 -> PO
discountStrategy := plan.DiscountStrategy()
discountMode := discountStrategy.Type()
pricePlanPO.DiscountMode = strPtr(discountMode)

discountStrategyJSON, err := discountStrategy.ToJSON()
if err != nil {
    return nil, fmt.Errorf("序列化优惠策略失败: %w", err)
}
pricePlanPO.DiscountStrategy = strPtr(discountStrategyJSON)

// PO -> 领域模型
var discountStrategy timediscountplan.DiscountStrategy
if pricePlanPO.DiscountMode != nil && pricePlanPO.DiscountStrategy != nil {
    switch *pricePlanPO.DiscountMode {
    case "durationDiscount":
        var strategy timediscountplan.DurationDiscountStrategy
        err := json.Unmarshal([]byte(*pricePlanPO.DiscountStrategy), &strategy)
        if err != nil {
            return nil, fmt.Errorf("反序列化赠送时长优惠策略失败: %w", err)
        }
        discountStrategy = &strategy
    case "timeSlotDiscount":
        var strategy timediscountplan.TimeSlotDiscountStrategy
        err := json.Unmarshal([]byte(*pricePlanPO.DiscountStrategy), &strategy)
        if err != nil {
            return nil, fmt.Errorf("反序列化赠送时段优惠策略失败: %w", err)
        }
        discountStrategy = &strategy
    // ... 其他策略类型
    }
}
```

## 6. 辅助函数

为了简化 PO 与领域模型之间的转换，仓储实现类提供了一些辅助函数，如：

```go
// 字符串指针转换
func strPtr(s string) *string {
    return &s
}

// 整数指针转换
func intPtr(i int) *int {
    return &i
}

// 布尔值指针转换
func boolPtr(b bool) *bool {
    return &b
}

// 时间戳指针转换
func int64Ptr(i int64) *int64 {
    return &i
}
```

这些辅助函数简化了基本类型与指针类型之间的转换，提高了代码的可读性和可维护性。

## 7. 错误处理

在 PO 与领域模型之间的转换过程中，可能会遇到各种错误，如 JSON 序列化/反序列化错误、字段解析错误等。为了提供更详细的错误信息，转换方法使用了 `fmt.Errorf` 函数包装原始错误，添加上下文信息。

```go
roomTypeJSON, err := json.Marshal(plan.RoomTypeConfig().RoomTypes())
if err != nil {
    return nil, fmt.Errorf("序列化包厢类型配置失败: %w", err)
}
```

这种错误处理方式使得调用者可以更容易地定位和解决问题。

## 8. 测试

为了确保 PO 与领域模型之间的转换的正确性，编写了单元测试来验证转换逻辑。测试用例包括：

1. **正常情况**：验证在正常情况下，领域模型和 PO 之间可以正确地相互转换。
2. **边界情况**：验证在边界情况下（如空字段、最大/最小值等），转换逻辑是否仍然正确。
3. **错误情况**：验证在错误情况下（如 JSON 格式错误），转换逻辑是否能够正确地处理错误。

## 9. 总结

PO 与领域模型之间的转换是基础设施层的重要职责，确保了领域模型可以被正确地持久化和恢复。通过精心设计的转换逻辑，我们实现了领域模型和持久化细节的解耦，使得领域模型可以专注于业务逻辑，而不必关心数据持久化的细节。同时，通过完善的错误处理和测试，我们确保了转换逻辑的正确性和可靠性。