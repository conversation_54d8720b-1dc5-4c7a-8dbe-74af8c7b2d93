# 计时价格方案聚合根设计文档

## 1. 概述

计时价格方案（TimePricePlan）是包厢价格方案DDD改造中的一个核心聚合根，它代表了一种特定的包厢消费模式：客户按照时间长短计费，通常以小时或半小时为计费单位。计时价格方案包含了丰富的业务规则和配置，如时间范围、价格配置、适用房型、最小计费时长、计费单位等。

## 2. 领域模型设计

### 2.1 核心属性

计时价格方案聚合根包含以下核心属性：

- **基本信息**
  - `id`: 唯一标识符
  - `venueID`: 门店ID
  - `name`: 方案名称
  - `roomTypeConfig`: 包厢类型配置（值对象）
  - `timeConfig`: 时间配置（值对象）
  - `isEnabled`: 是否启用
  - `channelConfig`: 投放渠道配置（值对象）
  - `areaConfig`: 投放区域配置（值对象）
  - `productSelection`: 方案内商品（值对象）
  - `giftPlan`: 消费赠券

- **计时特有配置**
  - `minimumDuration`: 最小计费时长（分钟）
  - `billingUnit`: 计费单位（分钟）

- **其他配置**
  - `supportsPoints`: 是否支持积分

- **价格配置（核心）**
  - `priceConfigList`: 价格配置列表（值对象）

- **元数据**
  - `createTime`: 创建时间
  - `updateTime`: 修改时间
  - `state`: 状态
  - `version`: 版本号

### 2.2 值对象依赖

计时价格方案聚合根依赖以下值对象：

- `RoomTypeConfig`: 包厢类型配置，定义了方案适用的包厢类型
- `TimeConfig`: 时间配置，定义了方案的有效时间范围
- `ChannelConfig`: 投放渠道配置，定义了方案的销售渠道
- `AreaConfig`: 投放区域配置，定义了方案的适用区域
- `ProductSelection`: 方案内商品，定义了方案包含的商品
- `PriceConfigList`: 价格配置列表，定义了方案的各种价格类型和价格

## 3. 价格配置设计

### 3.1 平铺结构与动态类型支持

与买断价格方案类似，计时价格方案也使用`PriceConfigList`作为核心价格配置组件，采用平铺结构来管理各种价格类型，具有以下特点：

1. **平铺结构**：所有价格类型（如基础价格、会员价格、活动价格等）都被放置在同一层级的列表中进行配置，避免了深层嵌套，使得价格配置更加扁平化和易于理解。

2. **动态类型支持**：支持动态的价格类型扩展，可以根据业务需求灵活地添加新的价格类型。

3. **价格查询逻辑**：
   - 首先根据价格类型（如"baseRoom"、"member"等）查找对应的价格配置项
   - 然后根据节假日ID和区域ID查找具体的价格
   - 如果找不到指定的价格类型，则尝试使用基础价格类型（"baseRoom"）
   - 如果仍然找不到有效价格，则返回错误

### 3.2 计时价格计算逻辑

计时价格方案的价格计算逻辑与买断价格方案有所不同，主要体现在以下几点：

1. **最小计费时长**：如果实际使用时长小于最小计费时长，则按最小计费时长计费。

2. **计费单位向上取整**：根据计费单位对实际使用时长进行向上取整。例如，如果计费单位为30分钟，实际使用70分钟，则按3个计费单位（90分钟）计费。

3. **单位价格与总价**：`PriceConfigList`中配置的价格是每个计费单位的价格，计算总价时需要乘以计费单位数量。

### 3.3 价格配置示例

```json
[
  {
    "name": "基础",
    "type": "baseRoom",
    "price": 5000,  // 每30分钟5000分（50元）
    "holidayPrice": [
      {
        "holidayId": "holiday1",
        "price": 7500,  // 节假日每30分钟7500分（75元）
        "areaPrice": [
          {
            "areaId": "area1",
            "price": 9000  // 节假日特定区域每30分钟9000分（90元）
          }
        ]
      }
    ],
    "areaPrice": [
      {
        "areaId": "area1",
        "price": 6000  // 特定区域每30分钟6000分（60元）
      }
    ]
  },
  {
    "name": "会员",
    "type": "member",
    "price": 4000,  // 会员每30分钟4000分（40元）
    "holidayPrice": [
      {
        "holidayId": "holiday1",
        "price": 6000,
        "areaPrice": [
          {
            "areaId": "area1",
            "price": 7000
          }
        ]
      }
    ],
    "areaPrice": [
      {
        "areaId": "area1",
        "price": 4500
      }
    ]
  }
]
```

## 4. 核心业务方法

计时价格方案聚合根提供以下核心业务方法：

### 4.1 构造与基本操作

- `NewTimePricePlan`: 创建一个新的计时价格方案，并进行必要的参数校验
- `Enable/Disable`: 启用/禁用方案
- `UpdateTimeConfig`: 更新时间配置
- `UpdatePriceConfig`: 更新价格配置
- `SetMinimumDuration`: 设置最小计费时长
- `SetBillingUnit`: 设置计费单位

### 4.2 业务逻辑方法

- `IsValid`: 检查方案在指定时间是否有效（检查是否启用、是否在时间范围内等）
- `GetPriceSetting`: 根据价格类型获取价格配置项
- `CalculatePrice`: 根据价格类型、时间、区域、节假日、使用时长等信息计算价格

### 4.3 序列化与反序列化

- `ToJSON`: 将计时价格方案转换为JSON字符串
- `FromJSON`: 从JSON字符串创建计时价格方案

## 5. 使用示例

### 5.1 创建计时价格方案

```go
// 创建必要的值对象
roomTypeConfig := common.NewRoomTypeConfig()
roomTypeConfig.AddRoomType("room1", "大包")

timeConfig := common.NewTimeConfig("weekday")
weekdayConfig := common.NewWeekdayTimeConfig([]int{1, 2, 3, 4, 5}, "08:00", "23:00")
timeConfig.WithWeekdayConfig(weekdayConfig)

// 创建计时方案
plan, err := timepriceplan.NewTimePricePlan(
    "plan1",
    "venue1",
    "测试计时方案",
    roomTypeConfig,
    timeConfig,
    60,  // 最小计费时长1小时
    30,  // 计费单位30分钟
)
if err != nil {
    // 处理错误
}

// 设置价格配置
priceConfigList := common.NewPriceConfigList()

// 添加基础价格（每30分钟的价格）
baseItem := common.NewPriceSettingItem("基础", "baseRoom", 5000)
baseItem.AddAreaPrice("area1", 6000)
holidayPrice := baseItem.AddHolidayPrice("holiday1", 7500)
holidayPrice.AddAreaPrice("area1", 9000)
priceConfigList.AddPriceSettingItem(baseItem)

// 添加会员价格
memberItem := common.NewPriceSettingItem("会员", "member", 4000)
priceConfigList.AddPriceSettingItem(memberItem)

err = plan.UpdatePriceConfig(priceConfigList)
if err != nil {
    // 处理错误
}

// 启用方案
plan.Enable()
```

### 5.2 计算价格

```go
// 计算价格
ctx := context.Background()
checkTime := time.Now()
duration := 90  // 使用时长90分钟
price, err := plan.CalculatePrice(ctx, checkTime, duration, "area1", "holiday1", "baseRoom")
if err != nil {
    // 处理错误
}

fmt.Printf("价格: %.2f元\n", price.Yuan())
```

## 6. 仓储接口

计时价格方案仓储接口定义了与持久化相关的操作：

```go
type TimePricePlanRepository interface {
    // Save 保存计时价格方案
    Save(ctx context.Context, plan *timepriceplan.TimePricePlan) error
    
    // FindByID 根据ID查找计时价格方案
    FindByID(ctx context.Context, id string) (*timepriceplan.TimePricePlan, error)
    
    // FindAll 查询所有计时价格方案
    FindAll(ctx context.Context) ([]*timepriceplan.TimePricePlan, error)
    
    // FindByVenueID 根据门店ID查询计时价格方案
    FindByVenueID(ctx context.Context, venueID string) ([]*timepriceplan.TimePricePlan, error)
    
    // FindByRoomTypeID 根据房间类型ID查询计时价格方案
    FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*timepriceplan.TimePricePlan, error)
    
    // FindEnabled 查询所有启用的计时价格方案
    FindEnabled(ctx context.Context) ([]*timepriceplan.TimePricePlan, error)
    
    // FindEnabledByVenueID 根据门店ID查询启用的计时价格方案
    FindEnabledByVenueID(ctx context.Context, venueID string) ([]*timepriceplan.TimePricePlan, error)
    
    // Delete 删除计时价格方案
    Delete(ctx context.Context, id string) error
}
```

## 7. 与买断价格方案的区别

计时价格方案与买断价格方案的主要区别在于：

1. **计费模式**：
   - 买断价格方案：固定价格，不随时间变化
   - 计时价格方案：按时间长短计费，价格随使用时长变化

2. **特有配置**：
   - 买断价格方案：`duration`（买断持续时长）、`advanceDisableDuration`（提前禁用时长）、`isExcessIncluded`（多余部分是否计入房费）、`hasMinimumCharge`（是否有最低消费）、`minimumCharge`（最低消费金额）
   - 计时价格方案：`minimumDuration`（最小计费时长）、`billingUnit`（计费单位）

3. **价格计算逻辑**：
   - 买断价格方案：直接获取配置的价格
   - 计时价格方案：根据使用时长、最小计费时长和计费单位计算总价

## 8. 总结

计时价格方案聚合根是包厢价格方案DDD改造中的核心领域模型之一，它通过丰富的配置和业务规则，实现了计时模式下的包厢价格管理。其中，价格配置采用平铺结构和动态类型支持，使得系统能够灵活应对各种价格类型的需求，同时保持了良好的可扩展性。计时价格方案的核心特点是按时间长短计费，通过最小计费时长和计费单位的配置，实现了灵活的计费策略。 