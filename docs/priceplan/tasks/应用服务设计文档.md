# 包厢价格方案应用服务设计文档

## 1. 概述

本文档详细描述了包厢价格方案DDD改造中应用服务（Application Services）的设计思路、接口定义和交互流程。在DDD架构中，应用服务是协调领域模型完成用户请求的关键组件，它位于用户界面层（或API层）和领域层之间，负责编排领域对象完成特定用例。

包厢价格方案系统中包含三个核心应用服务：
1. **BuyoutPricePlanService**：买断价格方案应用服务
2. **TimePricePlanService**：计时价格方案应用服务
3. **TimeDiscountPlanService**：计时优惠方案应用服务

## 2. 应用服务职责

应用服务在DDD架构中扮演以下角色：

1. **用例实现**：实现系统用例，将用户意图转化为对领域对象的操作
2. **编排协调**：编排领域对象（聚合根、领域服务等）完成业务流程
3. **事务管理**：负责事务边界的控制
4. **权限校验**：执行基本的权限验证
5. **数据转换**：在DTO（数据传输对象）和领域对象之间进行转换
6. **不包含业务逻辑**：应用服务不应包含业务逻辑，业务逻辑应在领域层实现

## 3. 买断价格方案应用服务（BuyoutPricePlanService）

### 3.1 职责

BuyoutPricePlanService负责处理与买断价格方案相关的所有用例，包括：

1. 创建买断价格方案
2. 获取买断价格方案详情
3. 获取买断价格方案列表
4. 更新买断价格方案
5. 启用/禁用买断价格方案
6. 删除买断价格方案
7. 计算买断价格

### 3.2 接口定义

```go
// BuyoutPricePlanService 买断价格方案应用服务
type BuyoutPricePlanService struct {
    repo repository.BuyoutPricePlanRepository
}

// NewBuyoutPricePlanService 创建买断价格方案应用服务
func NewBuyoutPricePlanService(repo repository.BuyoutPricePlanRepository) *BuyoutPricePlanService {
    return &BuyoutPricePlanService{
        repo: repo,
    }
}

// CreateBuyoutPricePlan 创建买断价格方案
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error)

// GetBuyoutPricePlan 获取买断价格方案
func (s *BuyoutPricePlanService) GetBuyoutPricePlan(ctx context.Context, id string) (*BuyoutPricePlanOutput, error)

// GetBuyoutPricePlans 获取所有买断价格方案
func (s *BuyoutPricePlanService) GetBuyoutPricePlans(ctx context.Context) ([]*BuyoutPricePlanOutput, error)

// GetBuyoutPricePlansByVenueID 根据门店ID获取买断价格方案
func (s *BuyoutPricePlanService) GetBuyoutPricePlansByVenueID(ctx context.Context, venueID string) ([]*BuyoutPricePlanOutput, error)

// GetEnabledBuyoutPricePlans 获取启用的买断价格方案
func (s *BuyoutPricePlanService) GetEnabledBuyoutPricePlans(ctx context.Context) ([]*BuyoutPricePlanOutput, error)

// GetEnabledBuyoutPricePlansByVenueID 根据门店ID获取启用的买断价格方案
func (s *BuyoutPricePlanService) GetEnabledBuyoutPricePlansByVenueID(ctx context.Context, venueID string) ([]*BuyoutPricePlanOutput, error)

// UpdateBuyoutPricePlan 更新买断价格方案
func (s *BuyoutPricePlanService) UpdateBuyoutPricePlan(ctx context.Context, id string, input *UpdateBuyoutPricePlanInput) error

// DeleteBuyoutPricePlan 删除买断价格方案
func (s *BuyoutPricePlanService) DeleteBuyoutPricePlan(ctx context.Context, id string) error

// EnableBuyoutPricePlan 启用买断价格方案
func (s *BuyoutPricePlanService) EnableBuyoutPricePlan(ctx context.Context, id string) error

// DisableBuyoutPricePlan 禁用买断价格方案
func (s *BuyoutPricePlanService) DisableBuyoutPricePlan(ctx context.Context, id string) error

// CalculatePrice 计算买断价格
func (s *BuyoutPricePlanService) CalculatePrice(ctx context.Context, input *CalculatePriceInput) (*CalculatePriceOutput, error)
```

### 3.3 输入/输出数据结构（DTO）

#### 3.3.1 创建买断价格方案输入DTO

```go
// CreateBuyoutPricePlanInput 创建买断价格方案输入DTO
type CreateBuyoutPricePlanInput struct {
    ID                     string                   // 唯一标识符
    VenueID                string                   // 门店ID
    Name                   string                   // 方案名称
    RoomTypeConfig         *common.RoomTypeConfig   // 包厢类型配置
    TimeConfig             *common.TimeConfig       // 时间配置
    ChannelConfig          *common.ChannelConfig    // 投放渠道配置
    AreaConfig             *common.AreaConfig       // 投放区域配置
    ProductSelection       *common.ProductSelection // 方案内商品
    GiftPlan               string                   // 消费赠券
    Duration               int                      // 买断持续时长（分钟）
    AdvanceDisableDuration int                      // 提前禁用时长（分钟）
    IsExcessIncluded       bool                     // 多余部分是否计入房费
    HasMinimumCharge       bool                     // 是否有最低消费
    MinimumCharge          int64                    // 最低消费金额
    StatisticsCategory     string                   // 统计分类
    PlanPic                string                   // 方案图片
    SupportsPoints         bool                     // 是否支持积分
    PriceConfigList        []PriceSettingItemDTO    // 价格配置列表
}
```

#### 3.3.2 买断价格方案输出DTO

```go
// BuyoutPricePlanOutput 买断价格方案输出DTO
type BuyoutPricePlanOutput struct {
    ID                     string                   // 唯一标识符
    VenueID                string                   // 门店ID
    Name                   string                   // 方案名称
    RoomTypeConfig         *common.RoomTypeConfig   // 包厢类型配置
    TimeConfig             *common.TimeConfig       // 时间配置
    IsEnabled              bool                     // 是否启用
    ChannelConfig          *common.ChannelConfig    // 投放渠道配置
    AreaConfig             *common.AreaConfig       // 投放区域配置
    ProductSelection       *common.ProductSelection // 方案内商品
    GiftPlan               string                   // 消费赠券
    Duration               int                      // 买断持续时长（分钟）
    AdvanceDisableDuration int                      // 提前禁用时长（分钟）
    IsExcessIncluded       bool                     // 多余部分是否计入房费
    HasMinimumCharge       bool                     // 是否有最低消费
    MinimumCharge          int64                    // 最低消费金额
    StatisticsCategory     string                   // 统计分类
    PlanPic                string                   // 方案图片
    SupportsPoints         bool                     // 是否支持积分
    PriceConfigList        []PriceSettingItemDTO    // 价格配置列表
    CreateTime             int64                    // 创建时间
    UpdateTime             int64                    // 修改时间
}
```

### 3.4 实现流程

以创建买断价格方案为例，应用服务的实现流程如下：

```go
// CreateBuyoutPricePlan 创建买断价格方案
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // 参数校验
    if input.ID == "" {
        return nil, errors.New("ID不能为空")
    }
    if input.VenueID == "" {
        return nil, errors.New("门店ID不能为空")
    }
    if input.Name == "" {
        return nil, errors.New("方案名称不能为空")
    }
    // 更多参数校验...

    // 转换价格配置
    priceConfigList, err := convertToPriceConfigList(input.PriceConfigList)
    if err != nil {
        return nil, err
    }

    // 创建领域对象
    plan, err := buyoutpriceplan.NewBuyoutPricePlan(
        input.ID,
        input.VenueID,
        input.Name,
        input.RoomTypeConfig,
        input.TimeConfig,
        input.Duration,
    )
    if err != nil {
        return nil, err
    }

    // 设置可选属性
    if input.ChannelConfig != nil {
        plan.SetChannelConfig(input.ChannelConfig)
    }
    if input.AreaConfig != nil {
        plan.SetAreaConfig(input.AreaConfig)
    }
    // 设置更多属性...

    // 设置价格配置
    err = plan.UpdatePriceConfig(priceConfigList)
    if err != nil {
        return nil, err
    }

    // 保存到仓储
    err = s.repo.Save(ctx, plan)
    if err != nil {
        return nil, err
    }

    // 转换为输出DTO
    output := convertToOutput(plan)

    return output, nil
}
```

## 4. 计时价格方案应用服务（TimePricePlanService）

### 4.1 职责

TimePricePlanService负责处理与计时价格方案相关的所有用例，包括：

1. 创建计时价格方案
2. 获取计时价格方案详情
3. 获取计时价格方案列表
4. 更新计时价格方案
5. 启用/禁用计时价格方案
6. 删除计时价格方案
7. 计算计时价格

### 4.2 接口定义

```go
// TimePricePlanService 计时价格方案应用服务
type TimePricePlanService struct {
    repo repository.TimePricePlanRepository
}

// NewTimePricePlanService 创建计时价格方案应用服务
func NewTimePricePlanService(repo repository.TimePricePlanRepository) *TimePricePlanService {
    return &TimePricePlanService{
        repo: repo,
    }
}

// CreateTimePricePlan 创建计时价格方案
func (s *TimePricePlanService) CreateTimePricePlan(ctx context.Context, input *CreateTimePricePlanInput) (*TimePricePlanOutput, error)

// GetTimePricePlan 获取计时价格方案
func (s *TimePricePlanService) GetTimePricePlan(ctx context.Context, id string) (*TimePricePlanOutput, error)

// GetTimePricePlans 获取所有计时价格方案
func (s *TimePricePlanService) GetTimePricePlans(ctx context.Context) ([]*TimePricePlanOutput, error)

// GetTimePricePlansByVenueID 根据门店ID获取计时价格方案
func (s *TimePricePlanService) GetTimePricePlansByVenueID(ctx context.Context, venueID string) ([]*TimePricePlanOutput, error)

// GetEnabledTimePricePlans 获取启用的计时价格方案
func (s *TimePricePlanService) GetEnabledTimePricePlans(ctx context.Context) ([]*TimePricePlanOutput, error)

// GetEnabledTimePricePlansByVenueID 根据门店ID获取启用的计时价格方案
func (s *TimePricePlanService) GetEnabledTimePricePlansByVenueID(ctx context.Context, venueID string) ([]*TimePricePlanOutput, error)

// UpdateTimePricePlan 更新计时价格方案
func (s *TimePricePlanService) UpdateTimePricePlan(ctx context.Context, id string, input *UpdateTimePricePlanInput) error

// DeleteTimePricePlan 删除计时价格方案
func (s *TimePricePlanService) DeleteTimePricePlan(ctx context.Context, id string) error

// EnableTimePricePlan 启用计时价格方案
func (s *TimePricePlanService) EnableTimePricePlan(ctx context.Context, id string) error

// DisableTimePricePlan 禁用计时价格方案
func (s *TimePricePlanService) DisableTimePricePlan(ctx context.Context, id string) error

// CalculatePrice 计算计时价格
func (s *TimePricePlanService) CalculatePrice(ctx context.Context, input *CalculatePriceInput) (*CalculatePriceOutput, error)
```

## 5. 计时优惠方案应用服务（TimeDiscountPlanService）

### 5.1 职责

TimeDiscountPlanService负责处理与计时优惠方案相关的所有用例，包括：

1. 创建计时优惠方案
2. 获取计时优惠方案详情
3. 获取计时优惠方案列表
4. 更新计时优惠方案
5. 启用/禁用计时优惠方案
6. 删除计时优惠方案
7. 计算优惠金额

### 5.2 接口定义

```go
// TimeDiscountPlanService 计时优惠方案应用服务
type TimeDiscountPlanService struct {
    repo              repository.TimeDiscountPlanRepository
    timePricePlanRepo repository.TimePricePlanRepository
}

// NewTimeDiscountPlanService 创建计时优惠方案应用服务
func NewTimeDiscountPlanService(repo repository.TimeDiscountPlanRepository, timePricePlanRepo repository.TimePricePlanRepository) *TimeDiscountPlanService {
    return &TimeDiscountPlanService{
        repo:              repo,
        timePricePlanRepo: timePricePlanRepo,
    }
}

// CreateTimeDiscountPlan 创建计时优惠方案
func (s *TimeDiscountPlanService) CreateTimeDiscountPlan(ctx context.Context, input *CreateTimeDiscountPlanInput) (*TimeDiscountPlanOutput, error)

// GetTimeDiscountPlan 获取计时优惠方案
func (s *TimeDiscountPlanService) GetTimeDiscountPlan(ctx context.Context, id string) (*TimeDiscountPlanOutput, error)

// GetTimeDiscountPlans 获取所有计时优惠方案
func (s *TimeDiscountPlanService) GetTimeDiscountPlans(ctx context.Context) ([]*TimeDiscountPlanOutput, error)

// GetTimeDiscountPlansByVenueID 根据门店ID获取计时优惠方案
func (s *TimeDiscountPlanService) GetTimeDiscountPlansByVenueID(ctx context.Context, venueID string) ([]*TimeDiscountPlanOutput, error)

// GetEnabledTimeDiscountPlans 获取启用的计时优惠方案
func (s *TimeDiscountPlanService) GetEnabledTimeDiscountPlans(ctx context.Context) ([]*TimeDiscountPlanOutput, error)

// GetEnabledTimeDiscountPlansByVenueID 根据门店ID获取启用的计时优惠方案
func (s *TimeDiscountPlanService) GetEnabledTimeDiscountPlansByVenueID(ctx context.Context, venueID string) ([]*TimeDiscountPlanOutput, error)

// UpdateTimeDiscountPlan 更新计时优惠方案
func (s *TimeDiscountPlanService) UpdateTimeDiscountPlan(ctx context.Context, id string, input *UpdateTimeDiscountPlanInput) error

// DeleteTimeDiscountPlan 删除计时优惠方案
func (s *TimeDiscountPlanService) DeleteTimeDiscountPlan(ctx context.Context, id string) error

// EnableTimeDiscountPlan 启用计时优惠方案
func (s *TimeDiscountPlanService) EnableTimeDiscountPlan(ctx context.Context, id string) error

// DisableTimeDiscountPlan 禁用计时优惠方案
func (s *TimeDiscountPlanService) DisableTimeDiscountPlan(ctx context.Context, id string) error

// CalculateDiscount 计算优惠金额
func (s *TimeDiscountPlanService) CalculateDiscount(ctx context.Context, input *CalculateDiscountInput) (*CalculateDiscountOutput, error)
```

## 6. 应用服务与其他层的交互

### 6.1 与表示层/API层的交互

应用服务直接被表示层/API层调用，例如：

```go
// RoomPriceController API控制器
func (c *RoomPriceController) CreateBuyoutPricePlan(ctx *gin.Context) {
    // 解析请求参数
    var input roomprice.CreateBuyoutPricePlanInput
    if err := ctx.ShouldBindJSON(&input); err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 调用应用服务
    service := roomprice.NewBuyoutPricePlanService(c.buyoutPricePlanRepo)
    output, err := service.CreateBuyoutPricePlan(ctx, &input)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    // 返回响应
    ctx.JSON(http.StatusOK, output)
}
```

### 6.2 与领域层的交互

应用服务调用领域模型（如聚合根、领域服务）和仓储接口，例如：

```go
// UpdateBuyoutPricePlan 更新买断价格方案
func (s *BuyoutPricePlanService) UpdateBuyoutPricePlan(ctx context.Context, id string, input *UpdateBuyoutPricePlanInput) error {
    // 参数校验
    if id == "" {
        return errors.New("ID不能为空")
    }

    // 从仓储获取领域对象
    plan, err := s.repo.FindByID(ctx, id)
    if err != nil {
        return err
    }
    if plan == nil {
        return errors.New("买断价格方案不存在")
    }

    // 更新领域对象的属性
    if input.Name != "" {
        if err := plan.SetName(input.Name); err != nil {
            return err
        }
    }

    if input.RoomTypeConfig != nil {
        if err := plan.SetRoomTypeConfig(input.RoomTypeConfig); err != nil {
            return err
        }
    }

    // 更新更多属性...

    // 保存更新后的领域对象
    return s.repo.Save(ctx, plan)
}
```

## 7. 事务管理

应用服务负责事务边界的控制，例如：

```go
// CreateBuyoutPricePlan 创建买断价格方案（带事务）
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    var output *BuyoutPricePlanOutput

    // 开启事务
    err := s.txManager.WithTransaction(ctx, func(txCtx context.Context) error {
        // 创建领域对象
        plan, err := buyoutpriceplan.NewBuyoutPricePlan(
            input.ID,
            input.VenueID,
            input.Name,
            input.RoomTypeConfig,
            input.TimeConfig,
            input.Duration,
        )
        if err != nil {
            return err
        }

        // 设置属性...

        // 保存到仓储
        if err := s.repo.Save(txCtx, plan); err != nil {
            return err
        }

        // 转换为输出DTO
        output = convertToOutput(plan)
        return nil
    })

    if err != nil {
        return nil, err
    }

    return output, nil
}
```

## 8. 性能优化

1. **批量操作**：对于需要处理大量数据的场景，应用服务应该提供批量操作接口，减少网络往返和数据库操作次数
2. **异步处理**：对于耗时长的操作，应用服务可以提供异步处理方式，返回任务ID供客户端后续查询进度
3. **缓存策略**：应用服务可以实现基本的缓存策略，减少对领域模型和数据库的频繁访问

## 9. 总结

应用服务是领域驱动设计中连接用户界面层和领域层的关键组件，它负责编排领域对象完成用户用例，但不包含业务逻辑。通过合理设计应用服务，我们可以实现以下目标：

1. **关注点分离**：应用服务关注用例流程和协调，领域模型关注业务逻辑
2. **边界清晰**：明确定义应用服务的职责，避免职责混淆
3. **可测试性**：应用服务易于测试，可以通过模拟依赖进行单元测试
4. **可扩展性**：新的用例可以通过添加新的应用服务方法实现

在包厢价格方案DDD改造中，我们设计了三个核心应用服务，它们共同构成了系统的应用层，为上层提供了清晰的API，同时协调领域层完成各种业务用例。 