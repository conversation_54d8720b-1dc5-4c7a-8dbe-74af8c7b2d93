# 买断价格方案聚合根设计文档

## 1. 概述

买断价格方案（BuyoutPricePlan）是包厢价格方案DDD改造中的一个核心聚合根，它代表了一种特定的包厢消费模式：客户可以在一定时间内（如2小时、3小时）以固定价格使用包厢，不按时间计费。买断价格方案包含了丰富的业务规则和配置，如时间范围、价格配置、适用房型等。

## 2. 领域模型设计

### 2.1 核心属性

买断价格方案聚合根包含以下核心属性：

- **基本信息**
  - `id`: 唯一标识符
  - `venueID`: 门店ID
  - `name`: 方案名称
  - `roomTypeConfig`: 包厢类型配置（值对象）
  - `timeConfig`: 时间配置（值对象）
  - `isEnabled`: 是否启用
  - `channelConfig`: 投放渠道配置（值对象）
  - `areaConfig`: 投放区域配置（值对象）
  - `productSelection`: 方案内商品（值对象）
  - `giftPlan`: 消费赠券

- **买断特有配置**
  - `duration`: 买断持续时长（分钟）
  - `advanceDisableDuration`: 提前禁用时长（分钟）
  - `isExcessIncluded`: 多余部分是否计入房费

- **最低消费配置**
  - `hasMinimumCharge`: 是否有最低消费
  - `minimumCharge`: 最低消费金额（值对象）

- **其他配置**
  - `statisticsCategory`: 统计分类
  - `planPic`: 方案图片
  - `supportsPoints`: 是否支持积分

- **价格配置（核心）**
  - `priceConfigList`: 价格配置列表（值对象）

- **元数据**
  - `createTime`: 创建时间
  - `updateTime`: 修改时间
  - `state`: 状态
  - `version`: 版本号

### 2.2 值对象依赖

买断价格方案聚合根依赖以下值对象：

- `RoomTypeConfig`: 包厢类型配置，定义了方案适用的包厢类型
- `TimeConfig`: 时间配置，定义了方案的有效时间范围
- `ChannelConfig`: 投放渠道配置，定义了方案的销售渠道
- `AreaConfig`: 投放区域配置，定义了方案的适用区域
- `ProductSelection`: 方案内商品，定义了方案包含的商品
- `Money`: 金额值对象，用于表示最低消费金额
- `PriceConfigList`: 价格配置列表，定义了方案的各种价格类型和价格

## 3. 价格配置设计

### 3.1 平铺结构与动态类型支持

`PriceConfigList` 是买断价格方案中最核心的价格配置组件，它采用平铺结构来管理各种价格类型，具有以下特点：

1. **平铺结构**：所有价格类型（如基础价格、会员价格、活动价格等）都被放置在同一层级的列表中进行配置，避免了深层嵌套，使得价格配置更加扁平化和易于理解。

2. **动态类型支持**：支持动态的价格类型扩展，可以根据业务需求灵活地添加新的价格类型，例如：
   - 不同等级会员卡价格：可以根据用户配置的会员卡等级，动态添加"黄金会员价格"、"铂金会员价格"等类型
   - 特殊活动价格：可以为特定活动添加活动专属的价格类型
   - 渠道专属价格：可以为不同销售渠道配置不同的价格

3. **价格查询逻辑**：
   - 首先根据价格类型（如"baseRoom"、"member"等）查找对应的价格配置项
   - 然后根据节假日ID和区域ID查找具体的价格
   - 如果找不到指定的价格类型，则尝试使用基础价格类型（"baseRoom"）
   - 如果仍然找不到有效价格，则返回错误

### 3.2 价格配置示例

```json
[
  {
    "name": "基础",
    "type": "baseRoom",
    "price": 10000,
    "holidayPrice": [
      {
        "holidayId": "holiday1",
        "price": 15000,
        "areaPrice": [
          {
            "areaId": "area1",
            "price": 18000
          }
        ]
      }
    ],
    "areaPrice": [
      {
        "areaId": "area1",
        "price": 12000
      }
    ]
  },
  {
    "name": "会员",
    "type": "member",
    "price": 8000,
    "holidayPrice": [
      {
        "holidayId": "holiday1",
        "price": 12000,
        "areaPrice": [
          {
            "areaId": "area1",
            "price": 14000
          }
        ]
      }
    ],
    "areaPrice": [
      {
        "areaId": "area1",
        "price": 9000
      }
    ]
  }
]
```

## 4. 核心业务方法

买断价格方案聚合根提供以下核心业务方法：

### 4.1 构造与基本操作

- `NewBuyoutPricePlan`: 创建一个新的买断价格方案，并进行必要的参数校验
- `Enable/Disable`: 启用/禁用方案
- `UpdateTimeConfig`: 更新时间配置
- `UpdatePriceConfig`: 更新价格配置
- `SetMinimumCharge`: 设置最低消费

### 4.2 业务逻辑方法

- `IsValid`: 检查方案在指定时间是否有效（检查是否启用、是否在时间范围内等）
- `GetPriceSetting`: 根据价格类型获取价格配置项
- `CalculatePrice`: 根据价格类型、时间、区域、节假日等信息计算价格

### 4.3 序列化与反序列化

- `ToJSON`: 将买断价格方案转换为JSON字符串
- `FromJSON`: 从JSON字符串创建买断价格方案

## 5. 使用示例

### 5.1 创建买断价格方案

```go
// 创建必要的值对象
roomTypeConfig := common.NewRoomTypeConfig()
roomTypeConfig.AddRoomType("room1", "大包")

timeConfig := common.NewTimeConfig("weekday")
weekdayConfig := common.NewWeekdayTimeConfig([]int{1, 2, 3, 4, 5}, "08:00", "23:00")
timeConfig.WithWeekdayConfig(weekdayConfig)

// 创建买断方案
plan, err := buyoutpriceplan.NewBuyoutPricePlan(
    "plan1",
    "venue1",
    "测试买断方案",
    roomTypeConfig,
    timeConfig,
    120, // 2小时
)
if err != nil {
    // 处理错误
}

// 设置价格配置
priceConfigList := common.NewPriceConfigList()

// 添加基础价格
baseItem := common.NewPriceSettingItem("基础", "baseRoom", 10000)
baseItem.AddAreaPrice("area1", 12000)
holidayPrice := baseItem.AddHolidayPrice("holiday1", 15000)
holidayPrice.AddAreaPrice("area1", 18000)
priceConfigList.AddPriceSettingItem(baseItem)

// 添加会员价格
memberItem := common.NewPriceSettingItem("会员", "member", 8000)
priceConfigList.AddPriceSettingItem(memberItem)

err = plan.UpdatePriceConfig(priceConfigList)
if err != nil {
    // 处理错误
}

// 启用方案
plan.Enable()
```

### 5.2 计算价格

```go
// 计算价格
ctx := context.Background()
checkTime := time.Now()
price, err := plan.CalculatePrice(ctx, checkTime, "area1", "holiday1", "baseRoom")
if err != nil {
    // 处理错误
}

fmt.Printf("价格: %.2f元\n", price.Yuan())
```

## 6. 仓储接口

买断价格方案仓储接口定义了与持久化相关的操作：

```go
type BuyoutPricePlanRepository interface {
    // Save 保存买断价格方案
    Save(ctx context.Context, plan *buyoutpriceplan.BuyoutPricePlan) error
    
    // FindByID 根据ID查找买断价格方案
    FindByID(ctx context.Context, id string) (*buyoutpriceplan.BuyoutPricePlan, error)
    
    // FindAll 查询所有买断价格方案
    FindAll(ctx context.Context) ([]*buyoutpriceplan.BuyoutPricePlan, error)
    
    // FindByVenueID 根据门店ID查询买断价格方案
    FindByVenueID(ctx context.Context, venueID string) ([]*buyoutpriceplan.BuyoutPricePlan, error)
    
    // FindByRoomTypeID 根据房间类型ID查询买断价格方案
    FindByRoomTypeID(ctx context.Context, roomTypeID string) ([]*buyoutpriceplan.BuyoutPricePlan, error)
    
    // FindEnabled 查询所有启用的买断价格方案
    FindEnabled(ctx context.Context) ([]*buyoutpriceplan.BuyoutPricePlan, error)
    
    // FindEnabledByVenueID 根据门店ID查询启用的买断价格方案
    FindEnabledByVenueID(ctx context.Context, venueID string) ([]*buyoutpriceplan.BuyoutPricePlan, error)
    
    // Delete 删除买断价格方案
    Delete(ctx context.Context, id string) error
}
```

## 7. 总结

买断价格方案聚合根是包厢价格方案DDD改造中的核心领域模型之一，它通过丰富的配置和业务规则，实现了买断模式下的包厢价格管理。其中，价格配置采用平铺结构和动态类型支持，使得系统能够灵活应对各种价格类型的需求，同时保持了良好的可扩展性。 