# 包厢价格方案应用服务类关系图

## 1. 应用服务整体结构

```
                  +------------------------+
                  |      IAppService       |
                  +------------------------+
                  | - repo: IRepository    |
                  +------------------------+
                         ^
                         |
          +--------------|-------------+
          |              |             |
+-------------------+  +-------------------+  +------------------------+
| BuyoutPricePlan   |  | TimePricePlan     |  | TimeDiscountPlan       |
| Service           |  | Service           |  | Service                |
+-------------------+  +-------------------+  +------------------------+
| - repo: IBuyout   |  | - repo: ITime     |  | - repo: ITimeDiscount  |
|   PricePlanRepo   |  |   PricePlanRepo   |  |   PlanRepo             |
+-------------------+  +-------------------+  | - timePricePlanRepo:   |
| + CreateBuyout    |  | + CreateTime      |  |   ITimePricePlanRepo   |
|   PricePlan()     |  |   PricePlan()     |  +------------------------+
| + GetBuyoutPrice  |  | + GetTimePricePlan|  | + CreateTimeDiscount   |
|   Plan()          |  |   ()              |  |   Plan()               |
| + UpdateBuyout    |  | + UpdateTime      |  | + GetTimeDiscountPlan()|
|   PricePlan()     |  |   PricePlan()     |  | + UpdateTimeDiscount   |
| + DeleteBuyout    |  | + DeleteTime      |  |   Plan()               |
|   PricePlan()     |  |   PricePlan()     |  | + DeleteTimeDiscount   |
| + EnableBuyout    |  | + EnableTime      |  |   Plan()               |
|   PricePlan()     |  |   PricePlan()     |  | + EnableTimeDiscount   |
| + DisableBuyout   |  | + DisableTime     |  |   Plan()               |
|   PricePlan()     |  |   PricePlan()     |  | + DisableTimeDiscount  |
| + CalculatePrice()|  | + CalculatePrice()|  |   Plan()               |
+-------------------+  +-------------------+  | + CalculateDiscount()  |
                                              +------------------------+
```

## 2. 应用服务与仓储接口关系

```
+----------------------+  使用  +-------------------------+
| BuyoutPricePlan      |------->| IBuyoutPricePlanRepo    |
| Service              |        +-------------------------+
+----------------------+        | + FindByID()            |
                                | + FindByVenueID()       |
                                | + Save()                |
                                | + Delete()              |
                                | + FindByRoomTypeID()    |
                                +-------------------------+
                                         ^
                                         |
                                         | 实现
                                         |
                                +---------------------------+
                                | BuyoutPricePlanRepository |
                                +---------------------------+
                                | (数据库实现)               |
                                +---------------------------+


+----------------------+  使用  +-------------------------+
| TimePricePlan        |------->| ITimePricePlanRepo      |
| Service              |        +-------------------------+
+----------------------+        | + FindByID()            |
                                | + FindByVenueID()       |
                                | + Save()                |
                                | + Delete()              |
                                | + FindByRoomTypeID()    |
                                +-------------------------+
                                         ^
                                         |
                                         | 实现
                                         |
                                +---------------------------+
                                | TimePricePlanRepository   |
                                +---------------------------+
                                | (数据库实现)               |
                                +---------------------------+


+----------------------+  使用  +-------------------------+
| TimeDiscountPlan     |------->| ITimeDiscountPlanRepo   |
| Service              |        +-------------------------+
+----------------------+        | + FindByID()            |
           |                    | + FindByVenueID()       |
           |                    | + Save()                |
           |                    | + Delete()              |
           |                    | + FindByTimePricePlanID()|
           |                    +-------------------------+
           |                              ^
           |                              |
           |                              | 实现
           |                              |
           |                     +---------------------------+
           |                     | TimeDiscountPlanRepository|
           |                     +---------------------------+
           |                     | (数据库实现)               |
           |                     +---------------------------+
           |
           |  使用
           v
+-------------------------+
| ITimePricePlanRepo      |
+-------------------------+
```

## 3. DTO对象结构

### 3.1 买断价格方案DTOs

```
+-------------------------------+
| CreateBuyoutPricePlanInput    |
+-------------------------------+
| + ID: string                  |
| + VenueID: string             |
| + Name: string                |
| + RoomTypeConfig: []string    |
| + TimeConfig: TimeConfigDTO   |
| + PriceConfig: []PriceConfig  |
| + State: int                  |
+-------------------------------+

+-------------------------------+
| BuyoutPricePlanOutput         |
+-------------------------------+
| + ID: string                  |
| + VenueID: string             |
| + Name: string                |
| + RoomTypeConfig: []string    |
| + TimeConfig: TimeConfigDTO   |
| + PriceConfig: []PriceConfig  |
| + State: int                  |
| + CreateTime: int64           |
| + UpdateTime: int64           |
+-------------------------------+

+-------------------------+
| CalculatePriceInput     |
+-------------------------+
| + PlanID: string        |
| + RoomTypeID: string    |
| + Date: string          |
| + PriceType: string     |
| + MemberType: string    |
| + AreaID: string        |
+-------------------------+

+-------------------------+
| CalculatePriceOutput    |
+-------------------------+
| + Price: int64          |
| + Currency: string      |
| + Formula: string       |
+-------------------------+
```

### 3.2 计时价格方案DTOs

```
+-------------------------------+
| CreateTimePricePlanInput      |
+-------------------------------+
| + ID: string                  |
| + VenueID: string             |
| + Name: string                |
| + RoomTypeConfig: []string    |
| + TimeConfig: TimeConfigDTO   |
| + TimeUnitType: string        |
| + TimeUnitValue: int          |
| + PriceConfig: []PriceConfig  |
| + State: int                  |
+-------------------------------+

+-------------------------------+
| TimePricePlanOutput           |
+-------------------------------+
| + ID: string                  |
| + VenueID: string             |
| + Name: string                |
| + RoomTypeConfig: []string    |
| + TimeConfig: TimeConfigDTO   |
| + TimeUnitType: string        |
| + TimeUnitValue: int          |
| + PriceConfig: []PriceConfig  |
| + State: int                  |
| + CreateTime: int64           |
| + UpdateTime: int64           |
+-------------------------------+
```

### 3.3 计时优惠方案DTOs

```
+----------------------------------+
| CreateTimeDiscountPlanInput      |
+----------------------------------+
| + ID: string                     |
| + VenueID: string                |
| + Name: string                   |
| + RoomTypeConfig: []string       |
| + TimeConfig: TimeConfigDTO      |
| + DiscountStrategyType: string   |
| + DiscountStrategyData: JSON     |
| + RequiredDuration: int          |
| + RequiredConsumption: int64     |
| + State: int                     |
+----------------------------------+

+----------------------------------+
| TimeDiscountPlanOutput           |
+----------------------------------+
| + ID: string                     |
| + VenueID: string                |
| + Name: string                   |
| + RoomTypeConfig: []string       |
| + TimeConfig: TimeConfigDTO      |
| + DiscountStrategyType: string   |
| + DiscountStrategyData: JSON     |
| + RequiredDuration: int          |
| + RequiredConsumption: int64     |
| + State: int                     |
| + CreateTime: int64              |
| + UpdateTime: int64              |
+----------------------------------+

+----------------------------+
| CalculateDiscountInput     |
+----------------------------+
| + DiscountPlanID: string   |
| + TimePricePlanID: string  |
| + Duration: int            |
| + Consumption: int64       |
+----------------------------+

+----------------------------+
| CalculateDiscountOutput    |
+----------------------------+
| + DiscountAmount: int64    |
| + DiscountType: string     |
| + Formula: string          |
+----------------------------+
```

### 3.4 共享DTOs

```
+----------------------+
| TimeConfigDTO        |
+----------------------+
| + StartTime: string  |
| + EndTime: string    |
| + DaysOfWeek: []int  |
| + Holidays: []string |
+----------------------+

+----------------------+
| PriceSettingItemDTO  |
+----------------------+
| + Name: string       |
| + Type: string       |
| + Price: int64       |
| + AreaPrice: []      |
| + HolidayPrice: []   |
+----------------------+

+----------------------+
| AreaPriceDTO         |
+----------------------+
| + AreaId: string     |
| + Price: int64       |
+----------------------+

+----------------------+
| HolidayPriceDTO      |
+----------------------+
| + HolidayId: string  |
| + Price: int64       |
| + AreaPrice: []      |
+----------------------+
```

## 4. 应用服务依赖关系

应用服务之间的依赖关系表现在方法调用和服务注入上：

1. **TimeDiscountPlanService 依赖于 TimePricePlanService**：
   - TimeDiscountPlanService 在计算优惠时需要获取对应的计时价格方案
   - 这种依赖通过构造函数注入 ITimePricePlanRepo 实现

2. **应用服务与领域服务的依赖**：
   - 应用服务主要负责协调和转换，而复杂业务逻辑由领域服务实现
   - 例如价格计算逻辑委托给 PriceCalculationService 领域服务

```
+----------------------+          +------------------------+
| BuyoutPricePlan      |  依赖于   | PriceCalculation       |
| Service              |--------->| Service                |
+----------------------+          | (领域服务)              |
                                  +------------------------+
                                          ^
                                          |
+----------------------+          依赖于  |
| TimePricePlan        |----------------+
| Service              |
+----------------------+
        ^
        |
        | 依赖于
        |
+----------------------+
| TimeDiscountPlan     |
| Service              |
+----------------------+
```

## 5. 应用服务事务边界

应用服务定义了事务边界，每个公共方法代表一个业务用例，并在单一事务中完成：

```
+-------------------+
| 应用服务方法        |
+-------------------+
|                   |
| 开始事务           |
|                   |
| +---------------+ |
| | 执行业务逻辑    | |
| +---------------+ |
|                   |
| 提交/回滚事务      |
|                   |
+-------------------+
```

具体实现上，事务管理通常由基础设施层提供，例如：

```go
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // 开始事务
    tx, err := s.txManager.Begin()
    if err != nil {
        return nil, err
    }
    
    // 使用defer确保事务结束
    defer func() {
        if p := recover(); p != nil {
            tx.Rollback()
            panic(p) // 重新抛出panic
        }
    }()
    
    // 业务逻辑
    // ...
    
    // 保存领域对象
    err = s.repo.Save(ctx, plan)
    if err != nil {
        tx.Rollback()
        return nil, err
    }
    
    // 提交事务
    if err := tx.Commit(); err != nil {
        return nil, err
    }
    
    // 返回DTO
    return convertToOutput(plan), nil
}
```

## 6. 应用服务与界面层的交互

应用服务通过DTO与界面层交互，一般流程如下：

```
+----------------+       +-------------------+       +-------------------+
| 控制器/界面层    |       | 应用服务           |       | 领域层            |
+----------------+       +-------------------+       +-------------------+
        |                        |                           |
        | 1. 发送DTO请求          |                           |
        |----------------------->|                           |
        |                        |                           |
        |                        | 2. 转换为领域对象           |
        |                        |-------------------------->|
        |                        |                           |
        |                        | 3. 执行领域逻辑            |
        |                        |<--------------------------|
        |                        |                           |
        |                        | 4. 保存领域对象            |
        |                        |-------------------------->|
        |                        |                           |
        | 5. 返回DTO响应          |                           |
        |<-----------------------|                           |
        |                        |                           |
```

界面层实现示例：

```go
// REST API控制器
func (c *RoomPriceController) CreateBuyoutPricePlan(w http.ResponseWriter, r *http.Request) {
    // 解析请求
    var req CreateBuyoutPricePlanRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    
    // 转换为应用服务输入DTO
    input := &roomprice.CreateBuyoutPricePlanInput{
        ID:            req.ID,
        VenueID:       req.VenueID,
        Name:          req.Name,
        RoomTypeConfig: req.RoomTypeConfig,
        TimeConfig:    convertToTimeConfigDTO(req.TimeConfig),
        PriceConfig:   convertToPriceConfigDTO(req.PriceConfig),
        State:         req.State,
    }
    
    // 调用应用服务
    output, err := c.buyoutPricePlanService.CreateBuyoutPricePlan(r.Context(), input)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    // 返回响应
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(output)
}
``` 