# 应用服务开发规范

## 1. 概述

本规范文档旨在指导包厢价格方案DDD项目中应用服务（Application Service）的开发，确保代码质量、可维护性和一致性。应用服务作为连接领域层和表示层的桥梁，其设计和实现对整个系统架构具有重要影响。

## 2. 命名规范

### 2.1 服务命名

- 应用服务类名应**以Service结尾**，并采用驼峰命名法
- 命名应体现管理的核心聚合根或用例，如`BuyoutPricePlanService`、`TimePricePlanService`

```go
// 推荐
type BuyoutPricePlanService struct {
    // ...
}

// 不推荐
type BuyoutPricePlanManager struct { // 不以Service结尾
    // ...
}
```

### 2.2 方法命名

- 应用服务方法名应**采用动词+名词形式**，清晰表明操作意图
- 常见操作前缀：`Create`、`Get`、`Update`、`Delete`、`Enable`、`Disable`、`Calculate`

```go
// 推荐
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // ...
}

// 不推荐
func (s *BuyoutPricePlanService) BuyoutPricePlanCreation(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // ... (名词+动词，不符合规范)
}
```

### 2.3 DTO命名

- 输入DTO应**以Input结尾**
- 输出DTO应**以Output结尾**
- DTO命名应包含操作和实体名称

```go
// 推荐
type CreateBuyoutPricePlanInput struct {
    // ...
}

type BuyoutPricePlanOutput struct {
    // ...
}

// 不推荐
type BuyoutPricePlanCreationParams struct { // 不以Input结尾
    // ...
}

type BuyoutPricePlanDTO struct { // 不以Output结尾
    // ...
}
```

## 3. 结构规范

### 3.1 服务结构

- 应用服务应**通过构造函数创建**，并注入所需依赖
- 依赖项应定义为接口类型，便于测试和替换实现

```go
// 推荐
type BuyoutPricePlanService struct {
    repo repository.IBuyoutPricePlanRepository
    // 其他依赖...
}

func NewBuyoutPricePlanService(repo repository.IBuyoutPricePlanRepository) *BuyoutPricePlanService {
    return &BuyoutPricePlanService{
        repo: repo,
    }
}

// 不推荐
type BuyoutPricePlanService struct {
    repo *repository.BuyoutPricePlanRepository // 直接依赖具体实现，不利于测试
    // ...
}
```

### 3.2 方法结构

- 公共方法应**添加注释**，说明功能、参数和返回值
- 方法实现应遵循"验证-执行-返回"的流程
- 复杂逻辑应提取为私有方法

```go
// CreateBuyoutPricePlan 创建买断价格方案
// 参数:
//   - ctx: 上下文
//   - input: 创建买断价格方案的输入参数
// 返回:
//   - 创建的买断价格方案
//   - 错误信息
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // 1. 验证
    if err := s.validateCreateInput(input); err != nil {
        return nil, err
    }
    
    // 2. 执行业务逻辑
    plan, err := s.createPlan(ctx, input)
    if err != nil {
        return nil, err
    }
    
    // 3. 返回结果
    return s.convertToOutput(plan), nil
}

// 提取的私有方法
func (s *BuyoutPricePlanService) validateCreateInput(input *CreateBuyoutPricePlanInput) error {
    // 验证逻辑...
}
```

### 3.3 DTO结构

- DTO应只包含**基本类型和内嵌DTO**，不包含领域对象
- 按照字段的重要性和相关性组织字段顺序
- 为字段添加合适的JSON标签和验证标签

```go
type CreateBuyoutPricePlanInput struct {
    ID            string          `json:"id"`
    VenueID       string          `json:"venueId" validate:"required"`
    Name          string          `json:"name" validate:"required,max=50"`
    RoomTypeConfig []string        `json:"roomTypeConfig" validate:"required,min=1"`
    TimeConfig    TimeConfigDTO   `json:"timeConfig" validate:"required"`
    PriceConfig   []PriceConfigDTO `json:"priceConfig" validate:"required,min=1"`
    State         int             `json:"state" validate:"oneof=0 1"`
}
```

## 4. 错误处理规范

### 4.1 错误定义

- 应用服务特有的错误应定义为**包级常量或变量**
- 错误信息应清晰、具体，并采用中文表述（面向终端用户）
- 可使用`errors.New`、`fmt.Errorf`或自定义错误类型

```go
// 错误常量定义
var (
    ErrBuyoutPricePlanNotFound = errors.New("买断价格方案不存在")
    ErrInvalidTimeConfig       = errors.New("无效的时间配置")
    ErrDuplicateBuyoutPricePlan = errors.New("买断价格方案已存在")
)

// 或使用自定义错误类型
type RoomPriceError struct {
    Code    string
    Message string
}

func (e RoomPriceError) Error() string {
    return e.Message
}

func NewRoomPriceError(code string, message string) RoomPriceError {
    return RoomPriceError{Code: code, Message: message}
}
```

### 4.2 错误返回

- 方法应**尽早返回错误**，避免深层嵌套
- 应保留原始错误信息，包装时添加上下文信息
- 可使用`fmt.Errorf`和`%w`包装错误

```go
// 推荐
func (s *BuyoutPricePlanService) GetBuyoutPricePlan(ctx context.Context, id string) (*BuyoutPricePlanOutput, error) {
    if id == "" {
        return nil, errors.New("ID不能为空")
    }
    
    plan, err := s.repo.FindByID(ctx, id)
    if err != nil {
        return nil, fmt.Errorf("查询买断价格方案失败: %w", err)
    }
    
    if plan == nil {
        return nil, ErrBuyoutPricePlanNotFound
    }
    
    return s.convertToOutput(plan), nil
}

// 不推荐
func (s *BuyoutPricePlanService) GetBuyoutPricePlan(ctx context.Context, id string) (*BuyoutPricePlanOutput, error) {
    var output *BuyoutPricePlanOutput
    
    if id != "" {
        plan, err := s.repo.FindByID(ctx, id)
        if err == nil {
            if plan != nil {
                output = s.convertToOutput(plan)
            } else {
                err = ErrBuyoutPricePlanNotFound
            }
        }
        
        if err != nil {
            return nil, err // 丢失上下文信息
        }
    } else {
        return nil, errors.New("ID不能为空")
    }
    
    return output, nil
}
```

### 4.3 错误处理

- 应用服务应处理可恢复的错误，重新抛出不可恢复的错误
- 在处理事务时，确保错误导致事务回滚
- 使用defer处理资源释放和panic恢复

```go
func (s *BuyoutPricePlanService) UpdateBuyoutPricePlan(ctx context.Context, input *UpdateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    tx, err := s.txManager.Begin()
    if err != nil {
        return nil, fmt.Errorf("开始事务失败: %w", err)
    }
    
    // 使用defer确保事务结束
    defer func() {
        if p := recover(); p != nil {
            tx.Rollback()
            panic(p) // 重新抛出panic
        }
    }()
    
    // 业务逻辑
    plan, err := s.repo.FindByID(ctx, input.ID)
    if err != nil {
        tx.Rollback()
        return nil, fmt.Errorf("查询买断价格方案失败: %w", err)
    }
    
    if plan == nil {
        tx.Rollback()
        return nil, ErrBuyoutPricePlanNotFound
    }
    
    // 更新逻辑...
    
    // 保存
    if err := s.repo.Save(ctx, plan); err != nil {
        tx.Rollback()
        return nil, fmt.Errorf("保存买断价格方案失败: %w", err)
    }
    
    // 提交事务
    if err := tx.Commit(); err != nil {
        return nil, fmt.Errorf("提交事务失败: %w", err)
    }
    
    return s.convertToOutput(plan), nil
}
```

## 5. 日志记录规范

### 5.1 日志级别使用

- **TRACE**: 仅用于开发调试，记录方法进入/退出等极细粒度信息
- **DEBUG**: 记录调试信息，如方法参数、中间结果等
- **INFO**: 记录业务事件，如创建、更新、删除操作的成功
- **WARN**: 记录可恢复的异常情况，如重试成功的操作
- **ERROR**: 记录错误信息，需人工介入处理的问题
- **FATAL**: 记录导致应用崩溃的严重错误，极少使用

### 5.2 日志内容规范

- 日志应包含**上下文信息**、**操作类型**和**关键参数**
- 敏感信息（如密码、令牌）应脱敏或不记录
- 结构化日志应使用统一的字段名

```go
// 推荐
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    logger := log.WithContext(ctx).With(
        "operation", "CreateBuyoutPricePlan",
        "planId", input.ID,
        "venueId", input.VenueID,
    )
    
    logger.Info("开始创建买断价格方案")
    
    // 业务逻辑...
    
    if err != nil {
        logger.Error("创建买断价格方案失败", "error", err)
        return nil, err
    }
    
    logger.Info("创建买断价格方案成功")
    return output, nil
}

// 不推荐
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    log.Info("CreateBuyoutPricePlan开始") // 缺少上下文信息
    
    // 业务逻辑...
    
    if err != nil {
        log.Error(err.Error()) // 缺少操作类型和上下文
        return nil, err
    }
    
    log.Info("CreateBuyoutPricePlan成功") // 缺少关键参数
    return output, nil
}
```

### 5.3 日志位置

- 在应用服务**方法开始**处记录INFO级别的进入日志
- 在**错误发生**处记录ERROR级别日志
- 在**方法结束**处记录INFO级别的成功日志
- 在关键业务节点记录DEBUG级别的详细信息

## 6. 领域转换规范

### 6.1 DTO与领域对象转换

- 转换逻辑应**封装在应用服务的私有方法**中
- 复杂转换应使用专门的转换器类
- 确保字段映射完整，不丢失信息

```go
// 领域对象到DTO转换
func (s *BuyoutPricePlanService) convertToOutput(plan *buyoutpriceplan.BuyoutPricePlan) *BuyoutPricePlanOutput {
    return &BuyoutPricePlanOutput{
        ID:            plan.GetID(),
        VenueID:       plan.GetVenueID(),
        Name:          plan.GetName(),
        RoomTypeConfig: plan.GetRoomTypeConfig(),
        TimeConfig:    convertTimeConfigToDTO(plan.GetTimeConfig()),
        PriceConfig:   convertPriceConfigToDTO(plan.GetPriceConfig()),
        State:         plan.GetState(),
        CreateTime:    plan.GetCreateTime(),
        UpdateTime:    plan.GetUpdateTime(),
    }
}

// DTO到领域对象转换
func (s *BuyoutPricePlanService) createPlanFromInput(input *CreateBuyoutPricePlanInput) (*buyoutpriceplan.BuyoutPricePlan, error) {
    timeConfig, err := convertDTOToTimeConfig(input.TimeConfig)
    if err != nil {
        return nil, err
    }
    
    priceConfig, err := convertDTOToPriceConfig(input.PriceConfig)
    if err != nil {
        return nil, err
    }
    
    plan, err := buyoutpriceplan.NewBuyoutPricePlan(
        input.ID,
        input.VenueID,
        input.Name,
        input.RoomTypeConfig,
        timeConfig,
        priceConfig,
    )
    
    if err != nil {
        return nil, err
    }
    
    if input.State == 1 {
        plan.Enable()
    }
    
    return plan, nil
}
```

### 6.2 值对象转换

- 对于复杂值对象，应**提供专门的转换函数**
- 确保值对象的不变性约束在转换过程中得到保障
- 处理为空的情况

```go
// TimeConfigDTO到TimeConfig转换
func convertDTOToTimeConfig(dto TimeConfigDTO) (*common.TimeConfig, error) {
    startTime, err := time.Parse("15:04", dto.StartTime)
    if err != nil {
        return nil, fmt.Errorf("无效的开始时间格式: %w", err)
    }
    
    endTime, err := time.Parse("15:04", dto.EndTime)
    if err != nil {
        return nil, fmt.Errorf("无效的结束时间格式: %w", err)
    }
    
    config := common.NewTimeConfig(
        startTime.Hour(), startTime.Minute(),
        endTime.Hour(), endTime.Minute(),
        dto.DaysOfWeek,
    )
    
    for _, holidayID := range dto.Holidays {
        config.AddHoliday(holidayID)
    }
    
    return config, nil
}

// TimeConfig到TimeConfigDTO转换
func convertTimeConfigToDTO(config *common.TimeConfig) TimeConfigDTO {
    startTime := fmt.Sprintf("%02d:%02d", config.GetStartHour(), config.GetStartMinute())
    endTime := fmt.Sprintf("%02d:%02d", config.GetEndHour(), config.GetEndMinute())
    
    return TimeConfigDTO{
        StartTime:  startTime,
        EndTime:    endTime,
        DaysOfWeek: config.GetDaysOfWeek(),
        Holidays:   config.GetHolidays(),
    }
}
```

## 7. 单元测试规范

### 7.1 测试文件组织

- 测试文件应与被测服务**放在同一包**中，命名为`xxx_test.go`
- 测试用例应按方法分组，使用合理的命名表明测试意图
- 使用表格驱动测试处理多种输入场景

```go
// roomprice/BuyoutPricePlanService_test.go
package roomprice

import (
    "testing"
    // ...
)

func TestBuyoutPricePlanService_CreateBuyoutPricePlan(t *testing.T) {
    // 创建测试用例...
}

func TestBuyoutPricePlanService_GetBuyoutPricePlan(t *testing.T) {
    // 获取测试用例...
}

func TestBuyoutPricePlanService_CalculatePrice(t *testing.T) {
    tests := []struct {
        name          string
        input         *CalculatePriceInput
        expectedPrice int64
        expectedError bool
    }{
        {
            name: "基础价格计算",
            input: &CalculatePriceInput{
                PlanID:     "plan1",
                RoomTypeID: "room1",
                Date:       "2023-05-01",
                PriceType:  "baseRoom",
            },
            expectedPrice: 10000,
            expectedError: false,
        },
        // 更多测试用例...
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试代码...
        })
    }
}
```

### 7.2 依赖模拟

- 使用mock框架（如testify/mock）模拟依赖
- 为每个测试场景配置适当的行为期望
- 验证关键依赖的调用

```go
// 创建模拟对象
type MockBuyoutPricePlanRepository struct {
    mock.Mock
}

func (m *MockBuyoutPricePlanRepository) FindByID(ctx context.Context, id string) (*buyoutpriceplan.BuyoutPricePlan, error) {
    args := m.Called(ctx, id)
    if args.Get(0) == nil {
        return nil, args.Error(1)
    }
    return args.Get(0).(*buyoutpriceplan.BuyoutPricePlan), args.Error(1)
}

// 测试中使用模拟对象
func TestBuyoutPricePlanService_GetBuyoutPricePlan(t *testing.T) {
    // 创建模拟仓储
    mockRepo := new(MockBuyoutPricePlanRepository)
    
    // 创建测试对象
    plan := createTestPlan("plan1", "venue1")
    
    // 设置期望
    mockRepo.On("FindByID", mock.Anything, "plan1").Return(plan, nil)
    mockRepo.On("FindByID", mock.Anything, "nonexistent").Return(nil, nil)
    mockRepo.On("FindByID", mock.Anything, "error").Return(nil, errors.New("数据库错误"))
    
    // 创建服务
    service := NewBuyoutPricePlanService(mockRepo)
    
    // 测试正常情况
    output, err := service.GetBuyoutPricePlan(context.Background(), "plan1")
    assert.NoError(t, err)
    assert.NotNil(t, output)
    assert.Equal(t, "plan1", output.ID)
    
    // 测试不存在情况
    output, err = service.GetBuyoutPricePlan(context.Background(), "nonexistent")
    assert.Error(t, err)
    assert.Nil(t, output)
    assert.Equal(t, ErrBuyoutPricePlanNotFound, err)
    
    // 测试错误情况
    output, err = service.GetBuyoutPricePlan(context.Background(), "error")
    assert.Error(t, err)
    assert.Nil(t, output)
    assert.Contains(t, err.Error(), "数据库错误")
    
    // 验证期望被满足
    mockRepo.AssertExpectations(t)
}
```

### 7.3 测试覆盖率要求

- 应用服务应达到**至少80%的代码覆盖率**
- 关键业务逻辑和边界条件应达到100%覆盖
- 使用`go test -cover`命令检查覆盖率

## 8. 性能优化规范

### 8.1 数据库操作优化

- 避免N+1查询问题，合理使用批量查询
- 在一个方法中避免重复查询同一对象
- 使用分页处理大量数据

```go
// 推荐
func (s *BuyoutPricePlanService) GetBuyoutPricePlansByVenue(ctx context.Context, venueID string, page, size int) ([]*BuyoutPricePlanOutput, int, error) {
    // 一次查询获取总数
    total, err := s.repo.CountByVenueID(ctx, venueID)
    if err != nil {
        return nil, 0, err
    }
    
    // 一次查询获取列表
    plans, err := s.repo.FindByVenueID(ctx, venueID, page, size)
    if err != nil {
        return nil, 0, err
    }
    
    // 批量转换
    outputs := make([]*BuyoutPricePlanOutput, 0, len(plans))
    for _, plan := range plans {
        outputs = append(outputs, s.convertToOutput(plan))
    }
    
    return outputs, total, nil
}

// 不推荐
func (s *BuyoutPricePlanService) GetBuyoutPricePlansByVenue(ctx context.Context, venueID string) ([]*BuyoutPricePlanOutput, error) {
    // 获取ID列表
    ids, err := s.repo.FindIDsByVenueID(ctx, venueID)
    if err != nil {
        return nil, err
    }
    
    // 逐个查询详情（N+1问题）
    outputs := make([]*BuyoutPricePlanOutput, 0, len(ids))
    for _, id := range ids {
        output, err := s.GetBuyoutPricePlan(ctx, id) // 每个ID都额外查询一次
        if err != nil {
            return nil, err
        }
        outputs = append(outputs, output)
    }
    
    return outputs, nil
}
```

### 8.2 内存使用优化

- 预先分配适当大小的切片，避免频繁扩容
- 及时释放不再使用的大对象
- 避免不必要的对象复制

```go
// 推荐
func (s *BuyoutPricePlanService) convertOutputs(plans []*buyoutpriceplan.BuyoutPricePlan) []*BuyoutPricePlanOutput {
    // 预分配适当大小的切片
    outputs := make([]*BuyoutPricePlanOutput, 0, len(plans))
    
    for _, plan := range plans {
        outputs = append(outputs, s.convertToOutput(plan))
    }
    
    return outputs
}

// 不推荐
func (s *BuyoutPricePlanService) convertOutputs(plans []*buyoutpriceplan.BuyoutPricePlan) []*BuyoutPricePlanOutput {
    // 不预分配，导致多次扩容
    var outputs []*BuyoutPricePlanOutput
    
    for _, plan := range plans {
        outputs = append(outputs, s.convertToOutput(plan))
    }
    
    return outputs
}
```

### 8.3 并发优化

- 适当使用goroutine处理独立任务
- 使用适当的并发控制机制（如WaitGroup、Channel）
- 注意共享资源的并发安全

```go
func (s *BuyoutPricePlanService) BatchProcessPlans(ctx context.Context, planIDs []string) (map[string]bool, error) {
    results := make(map[string]bool)
    var mu sync.Mutex // 保护map的并发访问
    
    // 创建工作池
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, 5) // 最多5个并发
    
    for _, id := range planIDs {
        wg.Add(1)
        go func(id string) {
            defer wg.Done()
            
            // 控制并发数
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            // 处理单个计划
            success, err := s.processPlan(ctx, id)
            
            // 安全地更新结果
            mu.Lock()
            results[id] = err == nil && success
            mu.Unlock()
        }(id)
    }
    
    wg.Wait()
    return results, nil
}
```

## 9. 安全规范

### 9.1 输入验证

- 在应用服务方法开始处**验证所有输入参数**
- 使用验证框架（如validator）进行声明式验证
- 对特殊字段进行自定义验证

```go
func (s *BuyoutPricePlanService) CreateBuyoutPricePlan(ctx context.Context, input *CreateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // 基本验证
    if input == nil {
        return nil, errors.New("输入不能为空")
    }
    
    // 使用验证框架
    validate := validator.New()
    if err := validate.Struct(input); err != nil {
        return nil, fmt.Errorf("验证失败: %w", err)
    }
    
    // 自定义验证
    if err := s.validateTimeConfig(input.TimeConfig); err != nil {
        return nil, err
    }
    
    // 业务逻辑...
}

func (s *BuyoutPricePlanService) validateTimeConfig(config TimeConfigDTO) error {
    // 验证开始时间早于结束时间
    startTime, _ := time.Parse("15:04", config.StartTime)
    endTime, _ := time.Parse("15:04", config.EndTime)
    
    if !startTime.Before(endTime) {
        return errors.New("开始时间必须早于结束时间")
    }
    
    return nil
}
```

### 9.2 授权验证

- 应用服务应**检查操作权限**，确保用户有权限执行请求的操作
- 从上下文获取用户信息进行权限验证
- 使用统一的授权服务进行权限检查

```go
func (s *BuyoutPricePlanService) UpdateBuyoutPricePlan(ctx context.Context, input *UpdateBuyoutPricePlanInput) (*BuyoutPricePlanOutput, error) {
    // 从上下文获取用户信息
    userID, err := auth.GetUserIDFromContext(ctx)
    if err != nil {
        return nil, errors.New("未授权的访问")
    }
    
    // 验证用户权限
    hasPermission, err := s.authService.HasPermission(ctx, userID, "buyout_price_plan:update", input.VenueID)
    if err != nil {
        return nil, fmt.Errorf("权限验证失败: %w", err)
    }
    
    if !hasPermission {
        return nil, errors.New("没有更新买断价格方案的权限")
    }
    
    // 业务逻辑...
}
```

### 9.3 敏感数据处理

- 不在日志中记录敏感信息
- 敏感数据传输时应加密
- 使用安全的比较方法验证敏感值

```go
func (s *UserService) ValidateToken(ctx context.Context, input *ValidateTokenInput) (*ValidateTokenOutput, error) {
    // 安全比较，防止时序攻击
    if subtle.ConstantTimeCompare([]byte(input.Token), []byte(expectedToken)) != 1 {
        return nil, errors.New("无效的令牌")
    }
    
    // 在日志中脱敏敏感信息
    logger := log.WithContext(ctx).With(
        "operation", "ValidateToken",
        "tokenLength", len(input.Token),
        // 不记录完整token: "token", input.Token,
    )
    
    // 业务逻辑...
}
```

## 10. 总结

应用服务开发规范确保了代码质量、可维护性和一致性。开发人员应遵循以下核心原则：

1. **命名明确**：使用一致、清晰的命名约定
2. **职责单一**：应用服务专注于用例协调，不包含业务逻辑
3. **错误处理**：提供清晰的错误信息，适当包装和记录错误
4. **日志完整**：记录关键操作和错误，包含足够上下文
5. **安全优先**：验证所有输入，检查操作权限，保护敏感数据
6. **测试覆盖**：编写全面的单元测试，确保代码质量
7. **性能意识**：优化数据库操作，合理使用内存和并发

遵循这些规范，将帮助我们构建高质量、可维护的应用服务层，更好地支持领域驱动设计的实践。 