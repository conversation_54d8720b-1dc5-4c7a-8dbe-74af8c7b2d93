通过java的model生成go的项目文件
go项目项目框架
 - gin
 - mysql
 - redis
 - swagger
 - sentry

需要生成的文件:
 - /api/vo/XXXVO.go
 - /api/req/AddXXXReqDto.go
 - /api/req/UpdateXXXReqDto.go
 - /api/req/DeleteXXXReqDto.go
 - /api/req/QueryXXXReqDto.go
 - /service/po/XXX.go
 - /service/transfer/XXXTransfer.go
 - /service/impl/XXXService.go
 - /controller/XXXController.go
 - /router/XXXRoute.go

流程：
    1. 我提供java的model类
    2. 我提供按java的model类生成的go代码

这个是正确的例子：
1. 这是model类代码：类名：Booking
```java
package run.mone.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import run.mone.bo.MongoBo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Id;

import java.util.Date;
/**
 * 预订
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class Booking implements MongoBo {

    @Id
    private String id;
    // 预订ID
    private String bookingId;
    // 预订时间
    private Date bookingTime;
    // 状态
    private String status;

    private long ctime;

    private long utime;

    private int state;

    private int version;
}
```

2. 这是生成的go代码：
 - po:
```go
package po

// 假设这是你的基础模型包

// Booking 预订实体
type Booking struct {
	Id          *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                                 // ID
	BookingId   *string `gorm:"column:booking_id;type:varchar(64);default:''" json:"bookingId"` // 预订ID
	BookingTime *int64  `gorm:"column:booking_time;type:int;default:0" json:"bookingTime"`      // 预订时间
	Status      *string `gorm:"column:status;type:varchar(64);default:''" json:"status"`        // 状态
	Ctime       *int64  `gorm:"column:ctime;type:int;default:0" json:"ctime"`                   // 创建时间戳
	Utime       *int64  `gorm:"column:utime;type:int;default:0" json:"utime"`                   // 更新时间戳
	State       *int    `gorm:"column:state;type:int;default:0" json:"state"`                   // 状态值
	Version     *int    `gorm:"column:version;type:int;default:0" json:"version"`               // 版本号
}

// TableName 设置表名
func (Booking) TableName() string {
	return "booking"
}


func (b Booking) GetId() string {
	return *b.Id
}


```

 - req_add:
```go
package req

// AddBookingReqDto 创建预订请求DTO
type AddBookingReqDto struct {
	BookingId   *string `json:"bookingId"`   // 预订ID
	BookingTime *int64  `json:"bookingTime"` // 预订时间
	Status      *string `json:"status"`      // 状态
}

```

 - req_update:
```go
package req

type UpdateBookingReqDto struct {
	Id          *string `json:"id"`          // 区域ID
	BookingId   *string `json:"bookingId"`   // 预订ID
	BookingTime *int64  `json:"bookingTime"` // 预订时间
	Status      *string `json:"status"`      // 状态
}

```

 - req_delete:
```go
package req

type DeleteBookingReqDto struct {
	Id *string `json:"id"` // 区域ID
}


```

 - req_query:
```go
package req

type QueryBookingReqDto struct {
	Id          *string `json:"id"`          // 区域ID
	BookingId   *string `json:"bookingId"`   // 预订ID
	BookingTime *int64  `json:"bookingTime"` // 预订时间
	Status      *string `json:"status"`      // 状态
	PageNum     *int    `json:"pageNum"`     // 页码
	PageSize    *int    `json:"pageSize"`    // 每页记录数
}

```

 - vo:
```go
package vo

// BookingVO 预订信息值对象
type BookingVO struct {
	Id          string `json:"id"`                  // ID
	BookingId   string `json:"bookingId"`     // 预订ID
	BookingTime int64  `json:"bookingTime"` // 预订时间
	Status      string `json:"status"`           // 状态
	Ctime       int64  `json:"ctime"`             // 创建时间戳
	Utime       int64  `json:"utime"`             // 更新时间戳
	State       int    `json:"state"`             // 状态值
	Version     int    `json:"version"`         // 版本号
}

```

 - transfer:
```go
package transfer

import (
	"voderpltvv/api/vo"
	"voderpltvv/service/po"

	"github.com/jinzhu/copier"
)

type BookingTransfer struct {
}

func (transfer *BookingTransfer) PoToVo(po po.Booking) vo.BookingVO {
	vo := vo.BookingVO{}
	copier.Copy(&vo, &po)
	return vo
}

func (transfer *BookingTransfer) VoToPo(vo vo.BookingVO) po.Booking {
	po := po.Booking{}
	copier.Copy(&po, &vo)
	return po
}

```

 - service:
```go
package impl

import (
	"voderpltvv/api/req"
	"voderpltvv/model"
	"voderpltvv/service/po"
	"voderpltvv/service/transfer"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BookingService struct {
}

var (
	bookingService = BookingService{}
	bookingTransfer = transfer.BookingTransfer{}
)

func (service *BookingService) CreateBooking(logCtx *gin.Context, booking *po.Booking) error {
	return Save(booking)
}

func (service *BookingService) CreateBookingWithTx(logCtx *gin.Context, booking *po.Booking, tx *gorm.DB) error {
	return SaveWithTx(booking, tx)
}

func (service *BookingService) UpdateBooking(logCtx *gin.Context, booking *po.Booking) error {
	return Update(booking)
}

func (service *BookingService) UpdateBookingPartial(logCtx *gin.Context, booking *po.Booking) error {
	return UpdateNotNull(booking)
}

func (service *BookingService) UpdateBookingPartialWithTx(logCtx *gin.Context, booking *po.Booking, tx *gorm.DB) error {
	return UpdateNotNullWithTx(booking, tx)
}

func (service *BookingService) DeleteBooking(logCtx *gin.Context, id string) error {
	return Delete(po.Booking{Id: &id})
}

func (service *BookingService) FindBookingById(logCtx *gin.Context, id string) (booking *po.Booking, err error) {
	booking = &po.Booking{}
	err = model.DBMaster.Self.Where("id=?", id).First(booking).Error
	return
}

func (service *BookingService) FindAllBooking(logCtx *gin.Context, reqDto *req.QueryBookingReqDto) (list *[]po.Booking, err error) {
	db := model.DBSlave.Self.Model(&po.Booking{})
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	db = db.Order("ctime desc")
	list = &[]po.Booking{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		return
	}
	return
}

func (service *BookingService) FindAllBookingWithPagination(logCtx *gin.Context, reqDto *req.QueryBookingReqDto) (list *[]po.Booking, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.Booking{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.Booking{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}


```

 - controller:
```go
package controller

import (
	"voderpltvv/api/req"
	"voderpltvv/api/vo"
	"voderpltvv/service/impl"
	"voderpltvv/service/po"
	"voderpltvv/service/transfer"

	"github.com/gin-gonic/gin"
)

type BookingController struct{}

var (
	bookingService  = impl.BookingService{}
	bookingTransfer = transfer.BookingTransfer{}
)

// @Summary 添加预订
// @Description 添加预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.AddBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.BookingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/add [post]
func (controller *BookingController) AddBooking(ctx *gin.Context) {
	reqDto := req.AddBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	booking := po.Booking{}
	if reqDto.BookingId != nil {
		booking.BookingId = reqDto.BookingId
	}
	if reqDto.BookingTime != nil {
		booking.BookingTime = reqDto.BookingTime
	}
	if reqDto.Status != nil {
		booking.Status = reqDto.Status
	}
	err = bookingService.CreateBooking(ctx, &booking)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, bookingTransfer.PoToVo(booking))
}

// @Summary 更新预订
// @Description 更新预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.UpdateBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.BookingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/update [post]
func (controller *BookingController) UpdateBooking(ctx *gin.Context) {
	reqDto := req.UpdateBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "id不能为空")
		return
	}
	booking, err := bookingService.FindBookingById(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	if reqDto.BookingId != nil {
		booking.BookingId = reqDto.BookingId
	}
	if reqDto.BookingTime != nil {
		booking.BookingTime = reqDto.BookingTime
	}
	if reqDto.Status != nil {
		booking.Status = reqDto.Status
	}
	err = bookingService.UpdateBooking(ctx, booking)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, bookingTransfer.PoToVo(*booking))
}

// @Summary 删除预订
// @Description 删除预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.DeleteBookingReqDto true "请求体"
// @Success 200 {object} Result[any] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/delete [post]
func (controller *BookingController) DeleteBooking(ctx *gin.Context) {
	reqDto := req.DeleteBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	err = bookingService.DeleteBooking(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	Result_success[any](ctx, nil)
}

// @Summary 查询预订
// @Description 查询预订
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.QueryBookingReqDto true "请求体"
// @Success 200 {object} Result[[]vo.BookingVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/query [post]
func (controller *BookingController) QueryBookings(ctx *gin.Context) {
	reqDto := req.QueryBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	list, err := bookingService.FindAllBooking(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	newList := []vo.BookingVO{}
	for _, v := range *list {
		newList = append(newList, bookingTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &newList)
}

// @Summary 查询预订列表
// @Description 查询预订列表
// @Tags 预订
// @Accept json
// @Produce json
// @Param body body req.QueryBookingReqDto true "请求体"
// @Success 200 {object} Result[vo.PageVO[[]vo.BookingVO]] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/booking/list [post]
func (a *BookingController) ListBookings(ctx *gin.Context) {
	reqDto := req.QueryBookingReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
		return
	}
	list, totalCount, err := bookingService.FindAllBookingWithPagination(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}
	page := vo.PageVO[[]vo.BookingVO]{}
	page.PageNum = *reqDto.PageNum
	page.PageSize = *reqDto.PageSize
	page.Total = totalCount
	page.Data = []vo.BookingVO{}
	for _, v := range *list {
		page.Data = append(page.Data, bookingTransfer.PoToVo(v))
	}
	Result_success[any](ctx, &page)
}


```

 - router:
```go

package router

import (
	"voderpltvv/controller"

	"github.com/gin-gonic/gin"
)

type BookingRoute struct {
}

func (s *BookingRoute) InitBookingRouter(g *gin.Engine) {

	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	bookingController := controller.BookingController{}
	route := g.Group("")
	{
		route.POST("/api/booking/add", bookingController.AddBooking)    //add
		route.POST("/api/booking/update", bookingController.UpdateBooking) //update
		route.POST("/api/booking/delete", bookingController.DeleteBooking) //delete
		route.POST("/api/booking/query", bookingController.QueryBookings)     //query
		route.POST("/api/booking/list", bookingController.ListBookings)     //list
	}
}


```

这是需要生成的model类的java代码：
```java

package run.mone.model.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import run.mone.bo.MongoBo;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Id;

/**
 * VenueAndMember 门店和会员关联
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class VenueAndMember implements MongoBo {

    @Id
    private String id;
    // 门店ID
    private String venueId;
	// 会员ID
	private String memberId;
	// 备注
    private String remark;
	// 创建时间
    private long ctime;
    // 更新时间
    private long utime;
    // 状态
    private int state;
    // 版本
    private int version;
}



```
这是我的项目名称：voderpltvv
输出给我的：
1. 其他字段省略，需要更新所有字段   其他字段设置 ...  这些要给出具体的实现，不要偷懒
2. 你自己来判断，如果输出太长，可以分成两部分或三部分输出给我
3. 中文回复我的问题
4. 需要生成的代码数组json格式：
```json
[
	{
		"vo": "",
		"req_add": "",
		"req_update": "",
		"req_delete": "",
		"req_query": "",
		"po": "",
		"transfer": "",
		"service": "",
		"controller": "",
		"router": ""
	}
]
```