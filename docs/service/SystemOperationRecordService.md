# 系统操作记录服务文档

## 服务概述
提供统一的系统操作日志记录能力，适用于所有需要记录关键操作的业务场景。记录信息包括操作人、操作类型、操作对象、操作前后状态等。

## 核心功能
1. 记录任意类型的操作日志
2. 自动序列化复杂数据结构为JSON
3. 支持扩展信息记录
4. 毫秒级操作时间记录

## 调用示例
```go
// 在业务服务中调用
params := impl.RecordParams{
    OldValue:  oldProduct, // 操作前状态（自动序列化为JSON）
    NewValue:  updatedProduct, // 操作后状态（自动序列化为JSON）
    ExtraInfo: map[string]interface{}{"ip": "***********"}, // 扩展信息（自动序列化为JSON）
	ClientID  string      // 业务端标识
}

err := systemRecordService.RecordOperation(
    ctx,
    "user_123",             // 操作人ID
    impl.ClientMiniProgram,   // 使用常量代替硬编码
    "app.venueauth.audit",  // 操作类型（层级.服务名称.操作）
    3,                      // 目标类型（3=商品）
    "product_456",      
    params,
)
```

## 字段定义说明
| 参数        | 类型   | 示例值               | 说明                                                                 |
|-----------|--------|---------------------|--------------------------------------------------------------------|
| operatorID | string | "user_123"           | 操作主体标识（用户ID/管理员ID/系统标识）                                       |
| opType     | string  | "app.venueauth.audit" | 操作类型（使用服务名称标识，格式：层级.服务名称.操作，如："app.venue.create"、"app.venueauth.audit"） |
| targetType | string | "venue", "order"    | 操作对象类型（建议使用业务实体英文名称，如："venue"-门店，"order"-订单）          |
| targetID   | string | "product_456"         | 操作对象的业务ID                                                         |
| clientID   | string | "mini-program"        | 客户端标识（web/ios/android/h5等，建议使用预定义常量）                             |

## 最佳实践
1. 为每个业务模块定义专属的操作类型常量
2. 在关键业务操作前后调用本服务
3. 敏感数据操作建议记录OldValue和NewValue
4. 通过ExtraInfo记录附加上下文（如IP地址、设备信息等）
5. 业务端标识建议使用统一枚举值：
```go
// 在 OperationConst.go 中定义的客户端标识常量
const (
    ClientMiniProgram = "mini-program"  // 小程序端
    ClientCashier     = "cashier"       // 收银端
    ClientBusinessSaaS= "business-saas" // 商务授权SaaS
    ClientAdmin       = "admin"         // 管理后台
    ClientSystem      = "system"        // 系统自动操作
)
```
6. 目标类型命名规范建议：
   - 使用英文小写单数形式
   - 保持全局唯一性
   - 示例：
   ```go
   const (
       TargetVenue  = "venue"   // 门店
       TargetOrder  = "order"   // 订单
       TargetProduct= "product" // 商品
   )
   ``` 

## 更新文档

### 最佳实践更新
```go
// 正确调用示例（使用预定义常量）
err := systemRecordService.RecordOperation(
    ctx,
    "user_123",
    ClientAdmin,  // 使用常量代替硬编码
    "app.venue.update",
    "venue",
    "venue_789",
    params,
)

// 错误示例（直接使用字符串）
err := systemRecordService.RecordOperation(
    ctx,
    "user_123",
    "Admin",  // 错误：大小写不一致
    // ...其他参数...
)
```

### 字段说明更新
| 参数     | 类型   | 合法值示例                 | 说明                     |
|----------|--------|-------------------------|--------------------------|
| clientID | string | ClientMiniProgram 等常量 | 必须使用预定义的客户端标识常量 |