# 异常支付订单 API文档

## 1. 基本信息

### 1.1 模块说明
- 模块名称: 异常支付订单管理
- 标签分组: 异常支付订单
- 业务归属: 支付服务
- 负责人: [待补充]

### 1.2 调用方
- [x] 收银台
- [ ] SAAS管理后台
- [ ] 财务分析后台

## 2. 数据结构

### 2.1 数据库表
```sql
CREATE TABLE `abnormal_payment_order` (
  `id` varchar(64) NOT NULL COMMENT '唯一id',
  `order_number` varchar(64) DEFAULT '' COMMENT '订单号',
  `room_number` varchar(64) DEFAULT '' COMMENT '房间号',
  `order_status` varchar(64) DEFAULT '' COMMENT '订单状态',
  `ctime` int DEFAULT 0 COMMENT '创建时间戳',
  `utime` int DEFAULT 0 COMMENT '更新时间戳',
  `state` int DEFAULT 0 COMMENT '状态值',
  `version` int DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常支付订单表';
```

### 2.2 字段说明
| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | varchar(64) | 是 | 唯一id | "abc123" |
| order_number | varchar(64) | 否 | 订单号 | "ORD2024001" |
| room_number | varchar(64) | 否 | 房间号 | "R101" |
| order_status | varchar(64) | 否 | 订单状态 | "PENDING" |
| ctime | int | 否 | 创建时间戳 | 1648888888 |
| utime | int | 否 | 更新时间戳 | 1648889999 |
| state | int | 否 | 状态值 | 0 |
| version | int | 否 | 版本号 | 1 |

## 3. API接口

### 3.1 添加异常支付订单
- 接口路径: `POST /api/abnormal-payment-order/add`
- 请求参数:
```json
{
    "orderNumber": "string",
    "roomNumber": "string",
    "orderStatus": "string"
}
```
- 响应结果:
```json
{
    "code": 200,
    "data": {
        "id": "string",
        "orderNumber": "string",
        "roomNumber": "string",
        "orderStatus": "string"
    }
}
```

### 3.2 更新异常支付订单
- 接口路径: `POST /api/abnormal-payment-order/update`
- 请求参数:
```json
{
    "id": "string",
    "orderNumber": "string",
    "roomNumber": "string", 
    "orderStatus": "string"
}
```

### 3.3 删除异常支付订单
- 接口路径: `POST /api/abnormal-payment-order/delete`
- 请求参数:
```json
{
    "id": "string"
}
```

### 3.4 查询异常支付订单
- 接口路径: `POST /api/abnormal-payment-order/query`
- 请求参数:
```json
{
    "id": "string"
}
```

### 3.5 分页查询异常支付订单
- 接口路径: `POST /api/abnormal-payment-order/list`
- 请求参数:
```json
{
    "id": "string",
    "pageNum": 1,
    "pageSize": 10
}
```

## 4. 数据流转

### 4.1 数据处理流程
1. 创建订单
   - 参数校验
   - 保存数据
   - 返回订单信息

2. 查询订单
   - 支持单个查询
   - 支持列表查询
   - 支持分页查询
   - 按创建时间倒序排序

### 4.2 数据库操作
- 主库: 写操作(新增/更新/删除)
- 从库: 读操作(查询)

## 5. 注意事项

### 5.1 错误码
| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| ServerIsBuzy | 服务器繁忙 | 请稍后重试 |
| ParamError | 参数错误 | 检查请求参数 |

### 5.2 依赖服务
- 数据库主从服务 