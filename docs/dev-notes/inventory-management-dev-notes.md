# 库存管理开发方案说明文档

## 重要说明 ⚠️

**架构设计澄清**：
- 本系统采用 **DDD（领域驱动设计）** 架构
- **当前阶段**：主要开发入库功能，但架构设计已考虑后续出库功能扩展
- **PO表设计理念**：`inventory_record` 和 `inventory_record_item` 表采用 **混合存储** 设计
  - 同一张表存储入库、出库、盘点等所有类型的库存变动记录
  - 通过 `type` 字段区分记录类型（INBOUND/OUTBOUND/INBOUND_REVERSE/OUTBOUND_REVERSE/ADJUSTMENT）
  - **领域模型层面**：抽象为不同的业务对象（InboundRecord、OutboundRecord、AdjustmentRecord）
  - **Repository层**：负责将领域模型转换为统一的数据存储格式
- **设计优势**：
  - 数据一致性：所有库存变动在同一表中，便于事务管理和一致性保证
  - 业务语义清晰：领域层区分不同业务操作，代码可读性强
  - 扩展性强：新增库存操作类型只需扩展领域模型，无需修改表结构
  - 查询效率高：库存计算和历史追溯可在单表中完成

---

## 1. 需求分析

### 1.1 业务背景
KTV ERP系统需要实现入库记录功能，用于管理商品的入库、出库、库存查询等操作。前端界面将展示商品列表，包含商品信息、库存数量、入库单价、入库小计等信息，并支持数量选择和库存状态展示。

### 1.2 功能需求
- **入库记录管理**：创建、查询、修改入库记录
- **库存查询**：实时查询商品当前库存数量
- **出库管理**：支持未来的出库功能扩展
- **数据修改**：支持已确认入库单的修改（通过冲销重开实现）
- **库存追溯**：完整的库存变动历史记录

### 1.3 非功能需求
- **性能要求**：前端库存查询响应时间 < 100ms
- **数据一致性**：库存数据必须准确无误
- **可扩展性**：支持多门店、多仓库场景
- **可审计性**：所有库存变动都有完整的审计轨迹

## 2. 技术方案

### 2.1 整体架构
采用 **DDD（领域驱动设计）** 架构模式，结合 **流水表 + 快照表** 的混合数据模型。

### 2.2 核心设计原则
1. **数据源分离**：
   - `InventoryRecord` + `InventoryRecordItem`：处理非销售业务的库存变动（入库、出库、盘点等）
   - `Order` + `OrderProduct`：处理销售消费的库存变动（现有表，无需修改）
   - `ProductStock`：库存快照表，提供高性能查询

2. **异步更新机制**：
   - 主业务操作只写入流水表，保证核心业务性能
   - 异步任务更新快照表，确保查询性能
   - 定期校准任务保证数据最终一致性

3. **冲销机制**：
   - 禁止直接修改已确认的库存记录
   - 通过创建反向记录（红字冲销）+ 新正确记录（蓝字重开）实现修改

## 3. 数据模型

### 3.1 核心表结构

#### 3.1.1 inventory_record（库存记录主表）
```sql
CREATE TABLE inventory_record (
    id VARCHAR(64) PRIMARY KEY,
    venue_id VARCHAR(64) NOT NULL COMMENT '门店ID',
    warehouse VARCHAR(64) DEFAULT '' COMMENT '仓库名称',
    type VARCHAR(64) DEFAULT '' COMMENT '记录类型：INBOUND/OUTBOUND/INBOUND_REVERSE/OUTBOUND_REVERSE/ADJUSTMENT',
    handler VARCHAR(64) DEFAULT '' COMMENT '操作人',
    time INT DEFAULT 0 COMMENT '操作时间',
    record_number VARCHAR(64) DEFAULT '' COMMENT '关联单号',
    original_record_id VARCHAR(64) DEFAULT NULL COMMENT '原始单据ID（用于冲销）',
    total_amount DECIMAL(10,2) COMMENT '总金额',
    remark VARCHAR(255) COMMENT '备注',
    ctime INT DEFAULT 0 COMMENT '创建时间',
    utime INT DEFAULT 0 COMMENT '更新时间',
    state INT DEFAULT 0 COMMENT '状态',
    version INT DEFAULT 0 COMMENT '版本'
);
```

#### 3.1.2 inventory_record_item（库存记录明细表）
```sql
CREATE TABLE inventory_record_item (
    id VARCHAR(64) PRIMARY KEY,
    inventory_record_id VARCHAR(64) NOT NULL COMMENT '关联主记录ID',
    product_id VARCHAR(64) NOT NULL COMMENT '商品ID',
    product_name VARCHAR(255) COMMENT '商品名称（冗余）',
    unit VARCHAR(64) COMMENT '单位（冗余）',
    quantity INT COMMENT '数量（入库为正，出库为负）',
    unit_price DECIMAL(10,2) COMMENT '单价',
    subtotal DECIMAL(10,2) COMMENT '小计',
    ctime INT DEFAULT 0 COMMENT '创建时间',
    utime INT DEFAULT 0 COMMENT '更新时间',
    state INT DEFAULT 0 COMMENT '状态',
    version INT DEFAULT 0 COMMENT '版本'
);
```

#### 3.1.3 product_stock（商品库存快照表）
```sql
CREATE TABLE product_stock (
    id VARCHAR(64) PRIMARY KEY,
    product_id VARCHAR(64) NOT NULL COMMENT '商品ID',
    venue_id VARCHAR(64) NOT NULL COMMENT '门店ID',
    warehouse VARCHAR(64) NOT NULL COMMENT '仓库名称',
    stock INT COMMENT '当前库存数量',
    ctime INT DEFAULT 0 COMMENT '创建时间',
    utime INT DEFAULT 0 COMMENT '更新时间',
    version INT DEFAULT 0 COMMENT '版本'
);
```

### 3.2 库存计算公式
```
当前库存 = (所有 inventory_record_item 的入库总和) 
         - (所有 inventory_record_item 的出库总和) 
         - (所有已支付 order_product 的销售总和)
```

### 3.3 索引设计
```sql
-- inventory_record_item 表索引
CREATE INDEX idx_inventory_record_item_record_id ON inventory_record_item(inventory_record_id);
CREATE INDEX idx_inventory_record_item_product_id ON inventory_record_item(product_id);

-- product_stock 表索引
CREATE UNIQUE INDEX idx_product_stock_unique ON product_stock(product_id, venue_id, warehouse);
CREATE INDEX idx_product_stock_venue ON product_stock(venue_id);

-- inventory_record 表索引
CREATE INDEX idx_inventory_record_venue ON inventory_record(venue_id);
CREATE INDEX idx_inventory_record_type ON inventory_record(type);
CREATE INDEX idx_inventory_record_time ON inventory_record(time);
```

## 4. DDD架构设计

### 4.1 分层结构
```
├── Domain Layer（领域层）
│   ├── inventory/
│   │   ├── model/          # 领域实体
│   │   ├── repository/     # 仓储接口
│   │   └── service/        # 领域服务
├── Application Layer（应用层）
│   └── inventory/          # 应用服务
├── Infrastructure Layer（基础设施层）
│   └── repository/         # 仓储实现
└── Interface Layer（接口层）
    ├── controller/         # 控制器
    └── router/            # 路由
```

### 4.2 核心组件

#### 4.2.1 领域实体（Domain Entities）
- `InventoryRecord`：库存记录基础聚合根
- `InboundRecord`：入库记录领域模型（继承自InventoryRecord）
- `OutboundRecord`：出库记录领域模型（继承自InventoryRecord）
- `AdjustmentRecord`：盘点调整记录领域模型（继承自InventoryRecord）
- `InventoryRecordItem`：库存记录明细实体
- `ProductStock`：商品库存实体

**设计说明**：
虽然底层使用同一张 `inventory_record` 表存储所有类型的库存变动，但在领域模型层面，我们将不同的业务操作抽象为独立的领域对象。这样做的好处是：
1. **业务语义清晰**：入库、出库、盘点在业务上有不同的含义和规则
2. **扩展性强**：可以为不同类型的操作添加特定的业务逻辑和验证
3. **维护简单**：开发人员可以专注于特定的业务场景
4. **数据一致**：Repository层负责将领域模型转换为统一的数据存储格式

#### 4.2.2 仓储接口（Repository Interface）
```go
type InventoryRepository interface {
    // 通用库存记录操作
    Save(record *InventoryRecord, items []InventoryRecordItem) error
    FindById(id string) (*InventoryRecord, error)
    
    // 入库记录操作
    SaveInboundRecord(record *InboundRecord, items []InventoryRecordItem) error
    FindInboundRecords(venueId string) ([]*InboundRecord, error)
    FindInboundRecordById(id string) (*InboundRecord, error)
    
    // 出库记录操作
    SaveOutboundRecord(record *OutboundRecord, items []InventoryRecordItem) error
    FindOutboundRecords(venueId string) ([]*OutboundRecord, error)
    FindOutboundRecordById(id string) (*OutboundRecord, error)
    
    // 商品库存操作
    GetProductStock(productId, venueId, warehouse string) (*ProductStock, error)
    UpdateProductStock(stock *ProductStock) error
    BatchUpdateProductStock(stocks []ProductStock) error
    
    // 库存计算
    CalculateStockFromRecords(productId, venueId string) (int, error)
}
```

#### 4.2.3 领域服务（Domain Service）
```go
type InventoryDomainService interface {
    // 创建入库记录
    CreateInboundRecord(record *InventoryRecord, items []InventoryRecordItem) error
    
    // 冲销记录
    ReverseRecord(originalRecordId string, reason string) error
    
    // 校验库存
    ValidateStock(productId, venueId string, requiredQuantity int) error
}
```

#### 4.2.4 应用服务（Application Service）
```go
// 入库应用服务
type InboundAppService interface {
    // 创建入库单
    CreateInboundRecord(req CreateInboundRecordReq) (*InboundRecordVO, error)
    
    // 修改入库单（冲销重开）
    ModifyInboundRecord(recordId string, req ModifyInboundRecordReq) (*InboundRecordVO, error)
    
    // 查询入库记录
    QueryInboundRecords(venueId string) ([]*InboundRecordVO, error)
    GetInboundRecordById(id string) (*InboundRecordVO, error)
}

// 出库应用服务
type OutboundAppService interface {
    // 创建出库单
    CreateOutboundRecord(req CreateOutboundRecordReq) (*OutboundRecordVO, error)
    
    // 修改出库单（冲销重开）
    ModifyOutboundRecord(recordId string, req ModifyOutboundRecordReq) (*OutboundRecordVO, error)
    
    // 查询出库记录
    QueryOutboundRecords(venueId string) ([]*OutboundRecordVO, error)
    GetOutboundRecordById(id string) (*OutboundRecordVO, error)
}

// 库存查询应用服务
type StockQueryAppService interface {
    // 查询库存
    GetProductStock(productId, venueId string) (*ProductStockVO, error)
    BatchGetProductStock(productIds []string, venueId string) ([]*ProductStockVO, error)
    
    // 库存校准
    ReconcileStock(productId, venueId string) error
    ReconcileAllStock(venueId string) error
}
```

## 5. 数据同步机制

### 5.1 异步更新流程
1. **主业务操作**：在数据库事务中写入 `inventory_record` 和 `inventory_record_item`
2. **触发异步任务**：事务提交后，发送消息到队列或触发goroutine
3. **更新快照表**：异步任务根据变动更新 `product_stock` 表
4. **错误处理**：异步任务失败时记录日志，等待重试或定期校准修复

### 5.2 定期校准机制
- **执行频率**：每日早上6点执行
- **校准范围**：全量或增量校准
- **校准逻辑**：
  ```sql
  UPDATE product_stock s SET stock = (
      SELECT COALESCE(SUM(iri.quantity), 0)
      FROM inventory_record_item iri
      WHERE iri.product_id = s.product_id
  ) - (
      SELECT COALESCE(SUM(op.quantity), 0)
      FROM order_product op
      JOIN `order` o ON op.order_no = o.order_no
      WHERE op.product_id = s.product_id
      AND o.status IN ('PAID', 'COMPLETED')
  )
  WHERE s.product_id = ?;
  ```

### 5.3 消费出库集成
在现有订单支付成功的事务中，增加异步任务触发：
```go
// 在 OrderApplicationServiceImpl 的支付方法中
func (s *OrderApplicationServiceImpl) V3AdditionalOrderPay(ctx context.Context, reqDto req.V3AddOrderAdditionalPayReqDto) {
    // ... 现有支付逻辑 ...
    
    // 事务提交后，触发库存更新
    go func() {
        s.inventoryService.UpdateStockFromOrder(orderProducts)
    }()
}
```

## 6. API设计

### 6.1 入库管理API
```
POST   /api/inventory/inbound          # 创建入库单
GET    /api/inventory/inbound/:id      # 查询入库单详情
GET    /api/inventory/inbound          # 查询入库单列表
PUT    /api/inventory/inbound/:id      # 修改入库单（冲销重开）
```

### 6.2 出库管理API
```
POST   /api/inventory/outbound         # 创建出库单
GET    /api/inventory/outbound/:id     # 查询出库单详情
GET    /api/inventory/outbound         # 查询出库单列表
PUT    /api/inventory/outbound/:id     # 修改出库单（冲销重开）
```

### 6.3 库存查询API
```
GET    /api/inventory/stock/:productId # 查询商品库存
GET    /api/inventory/stock            # 批量查询库存
POST   /api/inventory/reconcile        # 手动库存校准
```

### 6.4 请求响应示例
#### 创建入库单请求
```json
{
    "venueId": "venue_001",
    "warehouse": "主仓库",
    "handler": "张三",
    "recordNumber": "RK202312010001",
    "remark": "供应商A入库",
    "items": [
        {
            "productId": "product_001",
            "quantity": 100,
            "unitPrice": 25.50
        }
    ]
}
```

#### 查询库存响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "productId": "product_001",
        "productName": "轩尼诗VSOP700ml",
        "venueId": "venue_001",
        "warehouse": "主仓库",
        "stock": 88,
        "unit": "瓶",
        "updateTime": 1703123456
    }
}
```

## 7. 实现计划

### 7.1 第一阶段：基础数据层
- [ ] 创建PO实体类
- [ ] 创建数据库迁移脚本
- [ ] 实现Repository接口和实现类
- [ ] 编写单元测试

### 7.2 第二阶段：领域服务层
- [ ] 实现领域服务
- [ ] 实现应用服务
- [ ] 编写业务逻辑测试

### 7.3 第三阶段：接口层
- [ ] 创建Controller和路由
- [ ] 实现API接口
- [ ] 编写API测试

### 7.4 第四阶段：集成和优化
- [ ] 集成现有订单系统
- [ ] 实现异步更新机制
- [ ] 实现定期校准任务
- [ ] 性能优化和压力测试

## 8. 测试策略

### 8.1 单元测试
- Repository层测试：数据库操作正确性
- Service层测试：业务逻辑正确性
- 边界条件测试：异常情况处理

### 8.2 集成测试
- API接口测试：完整请求响应流程
- 数据一致性测试：流水表与快照表一致性
- 并发测试：高并发场景下的数据正确性

### 8.3 性能测试
- 库存查询性能：单次查询 < 100ms
- 批量操作性能：1000条记录处理 < 5s
- 并发性能：100并发下的系统稳定性

## 9. 监控和运维

### 9.1 监控指标
- API响应时间和成功率
- 异步任务执行状态
- 数据一致性检查结果
- 数据库连接池状态

### 9.2 告警机制
- 异步任务失败告警
- 数据不一致告警
- API响应时间超阈值告警

### 9.3 日志记录
- 所有库存变动操作日志
- 异步任务执行日志
- 错误和异常日志

## 10. 风险评估

### 10.1 技术风险
- **数据一致性风险**：异步更新可能导致短暂不一致
  - 缓解措施：定期校准 + 监控告警
- **性能风险**：大量数据时查询性能下降
  - 缓解措施：合理索引 + 分页查询

### 10.2 业务风险
- **库存准确性风险**：计算错误导致库存不准
  - 缓解措施：完整测试 + 人工复核机制
- **操作风险**：误操作导致数据错误
  - 缓解措施：权限控制 + 操作日志

## 11. 后续扩展

### 11.1 功能扩展
- 多仓库管理
- 库存预警
- 库存报表
- 移动端支持

### 11.2 技术扩展
- 读写分离
- 缓存优化
- 消息队列集成
- 微服务拆分

---

**文档版本**：v1.0  
**创建时间**：2024-01-XX  
**更新时间**：2024-01-XX  
**负责人**：开发团队 