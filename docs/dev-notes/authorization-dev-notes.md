# 门店授权功能开发笔记

## 1. 开发状态

### 1.1 已完成
- [x] 基础门店管理功能
- [x] 门店创建接口
- [x] 数据库基础表结构
- [x] 数据库结构更新
  - [x] venue表增加audit_status字段
  - [x] 创建venue_auth_code表
  - [x] 创建venue_audit_log表
- [x] 生成数据库查询代码（gen）
  - [x] 添加VenueAuthCode到basicModels
  - [x] 添加VenueAuditRecord到basicModels
  - [x] 生成DAL层代码
- [x] 领域服务实现
  - [x] VenueAuthService
    - [x] 定义接口
    - [x] 实现试用授权生成
    - [x] 实现正式授权管理
    - [x] 实现授权状态查询
  - [x] VenueAuditService
    - [x] 定义接口
    - [x] 实现审核日志记录
    - [x] 实现审核历史查询
- [x] 应用层服务实现
  - [x] VenueAuthAppService
    - [x] 定义接口
    - [x] 实现门店创建流程
    - [x] 实现授权激活流程
    - [x] 实现审核流程
- [x] 控制器层实现
  - [x] VenueAuthController
    - [x] 添加授权相关接口
    - [x] 添加审核相关接口
    - [x] 实现参数校验
    - [x] 实现错误处理
- [x] 对象转换实现
  - [x] VenueAuthTransfer
    - [x] PO/VO转换
    - [x] 数据格式化
    - [x] 状态计算逻辑
- [x] 路由层实现
  - [x] 添加VenueAuthRoute
  - [x] 配置授权相关路由
    - [x] 授权激活接口
    - [x] 授权状态查询接口
    - [x] 审核流程接口
    - [x] 生成授权码接口
  - [x] 实现路由分组
  - [x] 添加接口权限控制

### 1.2 进行中（第三阶段）
1.  **接口测试**
    - [ ]  VenueAuthService测试
    - [ ]  VenueAuditService测试
    - [ ]  VenueAuthAppService测试
    - [ ]  VenueAuthController测试

2. 性能优化
   - [ ] 添加缓存层
   - [ ] 优化查询性能
   - [ ] 添加索引

### 1.3 待开发
1. 商务后台功能
   - [ ] 审核列表和操作
   - [ ] 授权码管理
   - [ ] 授权历史查询
    
2. 小程序功能
   - [ ] 授权状态展示
   - [ ] 授权码激活
   - [ ] 授权到期提醒

3. 监控告警
   - [ ] 授权到期监控
   - [ ] 异常操作监控
   - [ ] 系统性能监控

## 2. 主要更新点
1. 完成控制器层实现
2. 完成对象转换层实现
3. 完善应用层服务实现
4. 更新开发状态和进度
5. 添加单元测试说明

## 3. 后续计划
1. 完成单元测试编写
2. 开始性能优化工作
3. 规划商务后台功能开发
4. 准备小程序端集成

## 4. 实现细节

### 4.1 数据模型说明
1. PO对象字段类型
   - 所有字段均使用指针类型
   - ID字段命名为Id而不是ID
   - 时间字段使用*time.Time
   - 状态字段使用*int

2. VO对象设计
   - 使用值类型而不是指针
   - 只有可能为空的字段使用指针
   - JSON标签使用snake_case

### 4.2 业务规则实现
1. 授权码生成
   - 使用UUID作为主键
   - 使用12位随机字符串作为授权码
   - 试用授权固定30天
   - 正式授权支持自定义天数

2. 授权状态管理
   - 未使用(0)
   - 已使用(1)
   - 已过期(2)

3. 审核状态管理
   - 待审核(0)
   - 已通过(1)
   - 已拒绝(2)

### 4.3 注意事项
1. 错误处理
   - 使用errors.Wrap包装错误
   - 添加具体的错误描述
   - 区分业务错误和系统错误

2. 时间处理
   - 统一使用time.Now()获取当前时间
   - 使用AddDate处理日期计算
   - 注意指针类型的时间比较

3. 事务处理
   - 在应用层处理事务
   - 确保状态变更的原子性
   - 处理好并发情况

## 5. 技术要点

### 5.1 工程目录结构
```
erp_managent/                # 项目根目录
├── api/                     # API层：请求响应定义
│   ├── req/                # 请求DTO定义
│   │   ├── errors.go       # 错误定义
│   │   └── VenueAuthReqDto.go  # 授权相关请求DTO
│   └── vo/                 # 值对象定义
│       └── VenueAuthStatusVO.go  # 授权状态VO
├── controller/             # 控制器层
│   └── VenueAuthController.go  # 授权相关控制器
├── router/                 # 路由层
│   └── router.go          # 路由配置
└── service/               # 服务层
    ├── application/       # 应用服务层
    │   └── VenueAuthAppService.go  # 授权应用服务
    ├── impl/             # 领域服务实现
    │   ├── VenueAuthService.go    # 授权服务
    │   └── VenueAuditService.go   # 审核服务
    ├── dal/              # 数据访问层（自动生成）
    │   ├── venue.gen.go
    │   ├── venue_auth_code.gen.go
    │   └── venue_audit_log.gen.go
    ├── po/               # 持久化对象
    │   ├── venue.go
    │   ├── venue_auth_code.go
    │   └── venue_audit_log.go
    └── transfer/         # 对象转换层
        └── venue_transfer.go
```

### 5.2 分层职责

1. API层
   - req：请求数据传输对象
     - 定义请求参数结构
     - 实现参数校验
     - 统一错误处理
   - vo：值对象
     - 定义响应数据结构
     - 处理数据展示格式

2. Controller层
   - 处理HTTP请求
   - 参数绑定和校验
   - 调用应用服务
   - 处理响应和错误
   - Swagger文档注解

3. Router层
   - URL路由配置
   - 中间件配置
   - 权限控制
   - 路由分组

4. Service层
   - application：应用服务
     - 业务流程编排
     - 事务处理
     - 服务组合
   - impl：领域服务
     - 核心业务逻辑
     - 业务规则维护
     - 状态管理
   - dal：数据访问
     - CRUD操作
     - 自定义查询
     - 读写分离
   - po：持久化对象
     - 数据库映射
     - 字段定义
     - 基础验证
   - transfer：对象转换
     - PO/VO转换
     - 数据格式化

### 5.3 开发规范

1. 命名规范
   - 文件名使用驼峰命名
   - 包名使用小写
   - 结构体名使用驼峰命名
   - 方法名使用驼峰命名
   - 常量使用全大写

2. 目录规范
   - 按功能模块划分目录
   - 保持目录结构清晰
   - 相关文件放在一起
   - 遵循DDD分层架构

3. 代码组织
   - 单一职责原则
   - 接口隔离原则
   - 依赖倒置原则
   - 关注点分离

4. Controller层规范
   - 统一使用Result[T]作为响应结构
   - VO对象定义在api/vo目录下，每个VO一个独立文件
   - 错误码使用GeneralCodes中定义的标准错误码
   - 参数校验错误返回ParamError
   - 业务错误返回ServerIsBuzy
   - Swagger注解完整规范
   - 泛型响应格式示例：
     ```go
     // 成功响应空数据
     Result_success[any](ctx, nil)
     
     // 成功响应具体数据
     Result_success[vo.VenueAuthStatusVO](ctx, status)
     
     // 参数错误
     Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误信息")
     
     // 业务错误
     Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
     ```

## 6. 授权相关代码文件

### 6.1 文件列表
- **api/req/**
  - `ActivateAuthReqDto.go`: 定义激活授权请求 DTO
  - `AuditVenueReqDto.go`: 定义审核门店请求 DTO
  - `GenerateAuthReqDto.go`: 定义生成授权码请求 DTO
- **api/vo/**
  - `VenueAuthStatusVO.go`: 定义门店授权状态 VO
- **controller/**
  - `VenueAuthController.go`: 门店授权控制器
- **service/application/**
  - `VenueAuthAppService.go`: 门店授权应用服务接口和实现
- **service/impl/**
  - `VenueAuthService.go`: 门店授权领域服务接口和实现
  - `VenueAuditService.go`: 门店审核领域服务接口和实现
- **service/dal/**
  - `venue.gen.go`: 门店表数据访问层代码
  - `venue_auth_code.gen.go`: 授权码表数据访问层代码
  - `venue_audit_log.gen.go`: 审核日志表数据访问层代码
- **service/po/**
  - `venue.go`: 门店表持久化对象
  - `VenueAuthCode.go`: 授权码表持久化对象
  - `VenueAuditRecord.go`: 审核日志表持久化对象
- **service/transfer/**
  - `VenueAuthTransfer.go`: 授权相关 PO 和 VO 转换

## 7. 单元测试

### 7.1 目录结构
```
erp_managent/
├── test/
│   └── scripts/
│       └── auth_test.py
```