# 商务账号功能开发笔记

## 1. 开发状态

### 1.1 已完成
- [x] 需求文档编写
  - [x] 数据表设计
  - [x] 业务规则定义
  - [x] 状态说明表
  - [x] 安全要求规范
- [x] 代码框架搭建
  - [x] 分层架构定义
  - [x] 核心对象定义（PO/DTO/VO）
  - [x] 接口规范设计
  - [x] 登录流程时序图
- [x] 基础设施准备
  - [x] 数据库表创建（PO）
  - [x] 实现DTO、VO
- [x] 基础认证功能
  - [x] 状态流转逻辑
  - [x] 密码加密存储
  - [x] 登录失败锁定
- [x] Token认证机制
- [x] 登录安全增强
- [x] 登录接口开发

### 1.2 进行中（第三阶段）
- [ ] 路由系统集成
  - [ ] 商务认证路由组注册
  - [ ] 权限中间件接入路由
  - [ ] 路由分组与版本控制
  - [ ] 接口文档注解补充

### 1.3 待开发
- [ ] 权限控制系统
  - [ ] API访问控制中间件
  - [ ] 权限等级路由过滤
  - [ ] 管理后台权限集成
- [ ] 日志审计系统
  - [ ] 登录日志记录
  - [ ] 操作日志跟踪
  - [ ] 异常行为监控
- [ ] 系统优化
  - [ ] 高频接口缓存
  - [ ] Token黑名单压缩存储
  - [ ] 密码强度实时校验

## 2. 主要更新点
1. 完成核心认证流程闭环
2. 实现全链路Token验证机制
3. 密码策略实际生效（8位+大小写+数字）
4. 登录失败锁定策略投入生产环境

## 3. 后续计划
1. 开发权限管理控制台
2. 实现操作日志可视化
3. 集成分布式链路追踪
4. 优化Token失效策略

## 4. 实现细节

### 4.1 新增完成功能
1. **完整登录流程**
   - 实现手机号+密码登录
   - 生成带权限等级的Token
   - 返回标准化响应VO

2. **增强安全机制**
   - 5次失败锁定5分钟
   - BCrypt密码加密存储
   - Token自动失效策略

### 4.2 下一步实现要点
5. **路由配置规范**：
   - 按功能模块划分路由组
   - 统一接口版本前缀（/api/v1/business）
   - 集成Swagger接口文档
   - 应用速率限制中间件

## 5. 注意事项
1. 高频接口需添加速率限制

## 6. 待解决问题
1. 权限等级是否需要动态配置？
2. 操作日志的存储周期策略？
3. Token失效名单的清理机制优化？
4. 路由版本控制策略如何设计？
5. 敏感接口路由是否需要特殊隔离？
6. 如何统一管理路由中间件？

## 7. 技术要点

### 7.1 工程目录结构
```
erp_managent/
├── api/
│   ├── req/              # 请求DTO
│   │   └── BusinessLoginReqDto.go
│   └── vo/               # 响应VO
│       └── BusinessLoginVO.go
├── controller/           # 控制器
│   └── BusinessAuthController.go
└── service/
    ├── application/      # 应用服务
    │   └── BusinessAuthAppService.go
    ├── impl/             # 领域服务
    │   └── BusinessAuthService.go
    └── po/               # 持久化对象
        └── BusinessStaff.go
```

### 7.2 核心接口清单
1. 认证接口（新增参数校验）
   - POST /api/business/auth/login（手机号11位校验）
   - POST /api/business/auth/logout

2. 管理接口（待开发）
   - GET /api/business/staffs
   - PUT /api/business/staffs/{id}/status
   - PATCH /api/business/staffs/{id}/permission

## 8. 开发规范

### 8.1 接口实现要求
1. 响应格式
```go
// 成功响应
{
  "code": 200,
  "data": {...},
  "msg": "success"
}

// 错误响应
{
  "code": 400,
  "data": null,
  "msg": "无效的手机号格式"
}
```

2. 错误码规范
   - 400: 客户端请求错误
   - 401: 未授权访问
   - 403: 权限不足
   - 500: 服务端内部错误

### 8.2 测试要求
1. 必须覆盖场景
   - 正常登录流程
   - 账号禁用状态登录
   - 权限等级切换测试
   - 登录失败锁定机制
   - Token过期续期

## 9. 待解决问题
1. 权限等级是否需要细分更多级别
2. 审核流程是否需要工作流引擎支持
3. 登录失败锁定策略的灵活性配置 