# 库存管理功能开发任务清单 (修订版 v2.0)

## 概述

本任务清单旨在指导KTV ERP系统库存管理功能的开发。鉴于现有代码已包含 `InventoryRecord` 的基础CRUD功能，本清单将侧重于**理解现有实现**、**引入DDD核心概念（如领域实体和更明确的服务职责）**、**实现文档中定义的"流水表 + 快照表"机制**、**完善库存计算和冲销逻辑**，以及**与订单系统的集成**。

**重要更新**：根据DDD架构设计原则，将开发任务按照架构分层进行重新组织，每个阶段专注于特定的架构层次，便于代码review和git提交管理。

## 阶段零：理解现有实现 ✅

**目标：** 全面了解现有 `InventoryRecord` 相关代码的功能、结构和数据流。

### 任务列表

*   **[✅] 任务 0.1：分析PO实体类**
    *   **描述：** 详细阅读 `erp-lt-vv/erp_managent/service/po/InventoryRecord.go`, `InventoryRecordItem.go`, `ProductStock.go`，理解其字段含义、与数据库表的映射关系。
    *   **依赖：** 无
    *   **完成状态：** 已完成 - 三个PO实体已存在，字段定义基本符合设计文档
*   **[✅] 任务 0.2：分析Service层实现**
    *   **描述：** 详细阅读 `erp-lt-vv/erp_managent/service/impl/InventoryRecordService.go`，理解其提供的CRUD方法，以及数据访问方式。
    *   **依赖：** 任务 0.1
    *   **完成状态：** 已完成 - InventoryRecordService提供基础CRUD，但缺少InventoryRecordItem的Service
*   **[✅] 任务 0.3：分析Controller和路由**
    *   **描述：** 详细阅读 `erp-lt-vv/erp_managent/controller/InventoryRecordController.go` 和 `erp-lt-vv/erp_managent/router/InventoryRecordRoute.go`，理解API接口的定义、请求/响应结构和与Service层的交互。
    *   **依赖：** 任务 0.2
    *   **完成状态：** 已完成 - Controller和Route已存在，提供基础API接口（已删除，不符合DDD设计）
*   **[✅] 任务 0.4：确认数据库表结构**
    *   **描述：** 检查实际数据库中 `inventory_record`, `inventory_record_item`, `product_stock` 表的结构，确保与PO定义一致，并确认索引情况。
    *   **依赖：** 任务 0.1
    *   **完成状态：** 已完成 - 通过PO实体定义确认表结构符合设计文档
*   **[✅] 任务 0.5：评估现有代码与DDD设计文档的差异**
    *   **描述：** 对比现有 `InventoryRecordService` 的职责与文档中 `InventoryRepository`, `InventoryDomainService`, `InboundAppService`, `StockQueryAppService` 的定义，识别需要重构或新增的接口和实现。
    *   **依赖：** 任务 0.1, 0.2, 0.3
    *   **完成状态：** 已完成 - 分析结果如下：
        - **缺少DDD分层架构**：现有只有简单的Service层，缺少Repository、Domain Service、Application Service分层
        - **字段不一致**：现有DTO/VO使用InboundNumber，已修正为RecordNumber
        - **缺少InventoryRecordItem服务**：只有PO定义，没有对应的Service实现
        - **缺少库存计算逻辑**：没有实现"流水表+快照表"机制
        - **缺少冲销逻辑**：没有实现冲销重开机制
        - **API设计不完整**：现有API只是简单CRUD，缺少业务语义（已删除废弃代码）

### 依赖分析与排序

1.  **任务 0.1：分析PO实体类** (无依赖，基础) ✅
2.  **任务 0.2：分析Service层实现** (依赖 任务 0.1) ✅
3.  **任务 0.3：分析Controller和路由** (依赖 任务 0.2) ✅
4.  **任务 0.4：确认数据库表结构** (依赖 任务 0.1) ✅
5.  **任务 0.5：评估现有代码与DDD设计文档的差异** (依赖 任务 0.1, 0.2, 0.3) ✅

## 阶段一：基础设施层重构与InventoryRecordItem服务

**目标：** 修正现有代码问题，创建缺失的InventoryRecordItem服务，为DDD架构奠定基础。

### 任务列表

*   **[✅] 任务 1.1：修正PO实体字段不一致问题**
    *   **描述：** 修正DTO/VO中的字段命名，确保与PO实体一致。删除废弃的Controller、Router、Transfer代码。
    *   **依赖：** 任务 0.5
    *   **完成状态：** 已完成 - 修正了DTO/VO字段名称，删除了废弃代码
*   **[✅] 任务 1.2：创建InventoryRecordItem服务**
    *   **描述：** 创建 `InventoryRecordItemService` 提供CRUD功能，确保主子表操作的一致性。
    *   **依赖：** 任务 1.1
    *   **完成状态：** 已完成 - 创建了InventoryRecordItemService，提供完整的CRUD功能和事务支持

### 依赖分析与排序

1.  **任务 1.1：修正PO实体字段不一致问题** (依赖 任务 0.5) ✅
2.  **任务 1.2：创建InventoryRecordItem服务** (依赖 任务 1.1) ✅

**Git提交建议：** 
- 提交1: "fix: 修正库存管理DTO/VO字段命名不一致问题，删除废弃代码"
- 提交2: "feat: 新增InventoryRecordItem服务，支持主子表事务操作"

## 阶段二：DDD领域模型层实现

**目标：** 实现DDD架构的领域模型层，包括领域实体、值对象、领域服务接口和仓储接口定义。

### 任务列表

*   **[✅] 任务 2.1：创建领域实体**
    *   **描述：** 创建领域实体 `InboundRecord`、`OutboundRecord`、`AdjustmentRecord`、`ProductStock`，以及相关的值对象。
    *   **依赖：** 任务 1.2
    *   **完成状态：** 已完成 - 创建了InboundRecord、ProductStock、DomainError等领域实体
*   **[✅] 任务 2.2：定义仓储接口**
    *   **描述：** 根据DDD设计，定义 `InventoryRepository` 接口，包含入库、出库、库存查询等操作的接口定义。
    *   **依赖：** 任务 2.1
    *   **完成状态：** 已完成 - 创建了InventoryRepository接口定义
*   **[✅] 任务 2.3：定义领域服务接口**
    *   **描述：** 定义 `InventoryDomainService` 接口，包含创建入库记录、冲销记录、库存校验等核心领域逻辑接口。
    *   **依赖：** 任务 2.2
    *   **完成状态：** 已完成 - 定义了库存领域服务接口

### 依赖分析与排序

1.  **任务 2.1：创建领域实体** (依赖 任务 1.2) ✅
2.  **任务 2.2：定义仓储接口** (依赖 任务 2.1) ✅
3.  **任务 2.3：定义领域服务接口** (依赖 任务 2.2) ✅

**Git提交建议：** 
- 提交1: "feat: 实现DDD领域实体层 - 入库记录、商品库存等核心实体"
- 提交2: "feat: 定义DDD仓储接口 - 库存管理数据访问抽象"
- 提交3: "feat: 定义DDD领域服务接口 - 核心业务逻辑抽象"

## 阶段三：基础设施层Repository实现

**目标：** 实现DDD架构的基础设施层，包括仓储接口的具体实现和领域服务的实现。

### 任务列表

*   **[✅] 任务 3.1：实现Repository具体类**
    *   **描述：** 实现 `InventoryRepositoryImpl`，提供数据持久化功能，包括领域模型与数据库PO的转换。
    *   **依赖：** 任务 2.2
    *   **完成状态：** 已完成 - 创建了InventoryRepositoryImpl实现
*   **[✅] 任务 3.2：实现领域服务**
    *   **描述：** 实现 `InventoryDomainServiceImpl`，包含创建入库记录、冲销记录、库存校验等核心领域逻辑。
    *   **依赖：** 任务 3.1, 任务 2.3
    *   **完成状态：** 已完成 - 实现了库存领域服务，包含核心业务逻辑
*   **[ ] 任务 3.3：实现"冲销重开"逻辑**
    *   **描述：** 在领域服务中实现已确认入库单的修改通过创建反向记录（红字冲销）和新正确记录（蓝字重开）的方式。
    *   **依赖：** 任务 3.2
*   **[ ] 任务 3.4：实现库存计算逻辑**
    *   **描述：** 实现基于流水表的库存计算逻辑，支持实时库存查询和历史库存追溯。
    *   **依赖：** 任务 3.2

### 依赖分析与排序

1.  **任务 3.1：实现Repository具体类** (依赖 任务 2.2) ✅
2.  **任务 3.2：实现领域服务** (依赖 任务 3.1, 2.3) ✅
3.  **任务 3.3：实现"冲销重开"逻辑** (依赖 任务 3.2)
4.  **任务 3.4：实现库存计算逻辑** (依赖 任务 3.2)

**Git提交建议：** 
- 提交1: "feat: 实现DDD仓储具体类 - 库存数据持久化与领域模型转换"
- 提交2: "feat: 实现DDD领域服务 - 核心库存业务逻辑"
- 提交3: "feat: 实现冲销重开机制 - 支持已确认单据的安全修改"
- 提交4: "feat: 实现库存计算逻辑 - 基于流水表的实时库存查询"

## 阶段四：应用服务层实现

**目标：** 实现DDD架构的应用服务层，协调领域服务完成完整的业务流程。

### 任务列表

*   **[ ] 任务 4.1：定义应用服务接口**
    *   **描述：** 定义 `InboundAppService`、`OutboundAppService`、`StockQueryAppService` 接口，明确应用服务的职责边界。
    *   **依赖：** 任务 3.2
*   **[ ] 任务 4.2：实现入库应用服务**
    *   **描述：** 实现 `InboundAppServiceImpl`，协调领域服务完成入库业务流程，包括数据验证、业务规则执行、事务管理。
    *   **依赖：** 任务 4.1, 任务 3.3, 任务 3.4
*   **[已删除] 任务 4.3：实现出库应用服务**
    *   **描述：** 出库功能暂不实现，后续根据需求开发。
    *   **状态：** 已删除相关代码和接口
*   **[ ] 任务 4.4：实现库存查询应用服务**
    *   **描述：** 实现 `StockQueryAppServiceImpl`，提供库存查询、批量查询、库存校准等功能。
    *   **依赖：** 任务 4.1, 任务 3.4
*   **[ ] 任务 4.5：实现异步更新机制**
    *   **描述：** 实现主业务操作写入流水表后，通过goroutine异步更新 `product_stock` 快照表的机制。
    *   **依赖：** 任务 4.2, 任务 4.4

### 依赖分析与排序

1.  **任务 4.1：定义应用服务接口** (依赖 任务 3.2)
2.  **任务 4.2：实现入库应用服务** (依赖 任务 4.1, 3.3, 3.4)
3.  **任务 4.4：实现库存查询应用服务** (依赖 任务 4.1, 3.4)
4.  **任务 4.5：实现异步更新机制** (依赖 任务 4.2, 4.4)

**Git提交建议：** 
- 提交1: "feat: 定义DDD应用服务接口 - 入库、库存查询服务抽象，分离DTO/VO"
- 提交2: "feat: 实现入库应用服务 - 完整的入库业务流程编排"
- 提交3: "feat: 实现库存查询应用服务 - 库存查询与校准功能"
- 提交4: "feat: 实现异步更新机制 - 流水表到快照表的异步同步"

## 阶段五：接口层与API实现

**目标：** 基于DDD架构，实现符合业务语义的API接口层。

### 任务列表

*   **[ ] 任务 5.1：实现入库记录Controller**
    *   **描述：** 创建 `InboundRecordController`，提供入库记录的创建、查询、修改（冲销重开）等API接口。
    *   **依赖：** 任务 4.2
*   **[ ] 任务 5.2：实现出库记录Controller**
    *   **描述：** 创建 `OutboundRecordController`，提供出库记录的创建、查询、修改等API接口（为后续功能扩展准备）。
    *   **依赖：** 任务 4.3
*   **[ ] 任务 5.3：实现库存查询Controller**
    *   **描述：** 创建 `StockQueryController`，提供商品库存查询、批量查询、库存校准等API接口。
    *   **依赖：** 任务 4.4
*   **[ ] 任务 5.4：实现路由配置**
    *   **描述：** 创建符合RESTful风格的路由配置，映射到对应的Controller方法。
    *   **依赖：** 任务 5.1, 5.2, 5.3
*   **[ ] 任务 5.5：实现定期校准任务**
    *   **描述：** 实现每日执行的库存校准任务，确保流水表和快照表数据最终一致性。
    *   **依赖：** 任务 4.5

### 依赖分析与排序

1.  **任务 5.1：实现入库记录Controller** (依赖 任务 4.2)
2.  **任务 5.2：实现出库记录Controller** (依赖 任务 4.3)
3.  **任务 5.3：实现库存查询Controller** (依赖 任务 4.4)
4.  **任务 5.4：实现路由配置** (依赖 任务 5.1, 5.2, 5.3)
5.  **任务 5.5：实现定期校准任务** (依赖 任务 4.5)

**Git提交建议：** 
- 提交1: "feat: 实现入库记录Controller - 入库管理API接口"
- 提交2: "feat: 实现出库记录Controller - 出库管理API接口"
- 提交3: "feat: 实现库存查询Controller - 库存查询API接口"
- 提交4: "feat: 配置库存管理路由 - RESTful API路由映射"
- 提交5: "feat: 实现定期库存校准任务 - 数据一致性保障"

## 阶段六：系统集成与优化

**目标：** 与现有订单系统集成，完成系统整体功能。

### 任务列表

*   **[ ] 任务 6.1：集成现有订单系统（消费出库）**
    *   **描述：** 在现有订单支付成功的事务中，增加触发库存更新的异步任务。
    *   **依赖：** 任务 4.5
*   **[ ] 任务 6.2：编写单元测试**
    *   **描述：** 为Repository、领域服务和应用服务编写全面的单元测试，覆盖核心业务逻辑和边界条件。
    *   **依赖：** 任务 4.5
*   **[ ] 任务 6.3：编写集成测试**
    *   **描述：** 为API接口、异步更新机制和定期校准任务编写集成测试，验证端到端的数据流和一致性。
    *   **依赖：** 任务 5.4, 5.5
*   **[ ] 任务 6.4：性能测试与优化**
    *   **描述：** 对库存查询和入库操作进行性能测试，确保满足响应时间要求。根据测试结果进行代码优化。
    *   **依赖：** 任务 5.4
*   **[ ] 任务 6.5：监控与告警**
    *   **描述：** 配置库存数据一致性、异步任务执行状态和API性能的监控指标和告警机制。
    *   **依赖：** 任务 6.1, 6.2, 6.3

### 依赖分析与排序

1.  **任务 6.1：集成现有订单系统（消费出库）** (依赖 任务 4.5)
2.  **任务 6.2：编写单元测试** (依赖 任务 4.5)
3.  **任务 6.3：编写集成测试** (依赖 任务 5.4, 5.5)
4.  **任务 6.4：性能测试与优化** (依赖 任务 5.4)
5.  **任务 6.5：监控与告警** (依赖 任务 6.1, 6.2, 6.3)

**Git提交建议：** 
- 提交1: "feat: 集成订单系统库存更新 - 消费出库自动触发"
- 提交2: "test: 添加库存管理单元测试 - 核心业务逻辑测试覆盖"
- 提交3: "test: 添加库存管理集成测试 - 端到端功能验证"
- 提交4: "perf: 库存管理性能优化 - 查询响应时间优化"
- 提交5: "feat: 添加库存管理监控告警 - 系统稳定性保障"

## 总结

### 架构分层对应关系
- **阶段一**：基础设施层重构与补充
- **阶段二**：DDD领域模型层（Domain Layer）
- **阶段三**：基础设施层Repository实现（Infrastructure Layer）
- **阶段四**：应用服务层（Application Layer）
- **阶段五**：接口层（Interface Layer）
- **阶段六**：系统集成与测试优化

### 开发建议
1. **严格按阶段进行**：每个阶段完成后进行代码review和测试，确保质量
2. **及时提交代码**：每个任务完成后及时提交，便于版本管理和问题追溯
3. **编写文档**：关键接口和业务逻辑需要编写清晰的文档说明
4. **持续测试**：开发过程中持续编写和执行测试，确保代码质量

### 预期收益
- **代码结构清晰**：DDD分层架构使代码职责明确，易于维护
- **业务语义明确**：领域模型直接反映业务概念，便于理解和扩展
- **测试覆盖完整**：分层测试策略确保系统稳定性
- **性能可控**：异步更新和快照表机制保证查询性能