# KTV ERP系统 Wiki

欢迎来到KTV ERP系统的技术文档中心！本wiki提供了系统的完整技术文档，帮助开发团队理解系统架构、业务逻辑和开发规范。

## 🚀 架构亮点

### 双架构并存设计
- **erp_managent**：老架构，采用渐进式DDD改造，部分模块已完全改造为DDD架构
- **erp_client**：全新DDD架构，基于**领域驱动设计+规则引擎+流程引擎**的现代化架构

### 老架构改造亮点
- 🔄 **渐进式改造**：保持系统稳定的前提下，逐步引入DDD设计
- 🔗 **适配器模式**：新DDD架构与传统Service层无缝集成
- 📊 **分模块改造**：价格方案、打印、升级等模块已完全DDD化
- 🛡️ **兼容性保证**：新老架构并存，确保业务连续性

### 新架构核心特性
- 🎯 **规则引擎驱动**：业务规则配置化，支持YAML格式的规则定义和热更新
- 🔄 **流程引擎编排**：业务流程标准化，复杂业务逻辑通过流程配置实现
- 🏗️ **DDD领域设计**：六大核心领域（Rule、Process、ValueObject、Subject、State、Permission）清晰划分
- 📊 **事件驱动架构**：领域间通过事件通信，保持松耦合和高扩展性

### API设计特色
- 📡 **统一POST接口**：所有API接口统一使用POST方法，简化客户端调用逻辑
- 🔒 **统一响应封装**：使用泛型Result[T]和HttpResult[T]统一封装返回值，确保响应格式一致性
- 🎫 **JWT认证**：基于JWT Token的身份认证和权限控制机制
- 📝 **完整文档**：Swagger自动生成的完整API文档，支持在线调试
- 🔍 **请求追踪**：每个请求包含唯一RequestID，支持分布式链路追踪

## 📋 目录导航

### 🏗️ [项目概述](./01-project-overview/README.md)
- [项目介绍](./01-project-overview/project-introduction.md)
- [技术栈](./01-project-overview/tech-stack.md)
- [系统架构](./01-project-overview/system-architecture.md)
- [目录结构](./01-project-overview/directory-structure.md)

### 💼 [核心业务模块](./02-business-modules/README.md)
- [订单管理](./02-business-modules/order-management.md)
- [会员管理](./02-business-modules/member-management.md)
- [房间管理](./02-business-modules/room-management.md)
- [商品管理](./02-business-modules/product-management.md)
- [支付管理](./02-business-modules/payment-management.md)
- [员工管理](./02-business-modules/employee-management.md)

### 🏛️ [技术架构](./03-technical-architecture/README.md)
- [分层架构](./03-technical-architecture/layered-architecture.md)
- [DDD设计](./03-technical-architecture/ddd-design.md)
- [规则引擎](./03-technical-architecture/rule-engine.md)
- [数据库设计](./03-technical-architecture/database-design.md)
- [API设计](./03-technical-architecture/api-design.md)

### 🛠️ [开发指南](./04-development-guide/README.md)
- [环境搭建](./04-development-guide/environment-setup.md)
- [代码规范](./04-development-guide/coding-standards.md)
- [开发流程](./04-development-guide/development-process.md)
- [测试指南](./04-development-guide/testing-guide.md)
- [部署指南](./04-development-guide/deployment-guide.md)

### 📚 [API文档](./05-api-documentation/README.md)
- [接口规范](./05-api-documentation/api-standards.md)
- [认证授权](./05-api-documentation/authentication.md)
- [错误处理](./05-api-documentation/error-handling.md)
- [接口列表](./05-api-documentation/api-list.md)

### 🔧 [运维指南](./06-operations/README.md)
- [系统监控](./06-operations/monitoring.md)
- [日志管理](./06-operations/logging.md)
- [性能优化](./06-operations/performance.md)
- [故障排查](./06-operations/troubleshooting.md)

### 📖 [最佳实践](./07-best-practices/README.md)
- [设计模式](./07-best-practices/design-patterns.md)
- [性能优化](./07-best-practices/performance-optimization.md)
- [安全规范](./07-best-practices/security-guidelines.md)
- [代码重构](./07-best-practices/refactoring.md)

### 📝 [更新日志](./08-changelog/README.md)
- [版本历史](./08-changelog/version-history.md)
- [功能更新](./08-changelog/feature-updates.md)
- [Bug修复](./08-changelog/bug-fixes.md)

## 🚀 快速开始

### 新手开发者学习路径
如果你是新加入的开发者，建议按以下顺序阅读文档：

1. **了解项目背景**：先阅读[项目介绍](./01-project-overview/project-introduction.md)了解业务背景
2. **理解架构设计**：重点学习[系统架构](./01-project-overview/system-architecture.md)，理解新老架构的区别
3. **搭建开发环境**：按照[环境搭建](./04-development-guide/environment-setup.md)指南配置开发环境
4. **熟悉代码组织**：学习[目录结构](./01-project-overview/directory-structure.md)，重点关注erp_client新架构
5. **深入技术架构**：学习[规则引擎](./03-technical-architecture/rule-engine.md)和[流程引擎](./03-technical-architecture/process-engine.md)
6. **理解DDD设计**：深入学习[DDD设计](./03-technical-architecture/ddd-design.md)的领域划分和实现

### 架构师/技术负责人学习路径
1. **DDD战略设计**：首先学习[DDD战略设计](./03-technical-architecture/ddd-strategic-design.md)，理解整体领域划分
2. **架构对比分析**：重点关注[目录结构](./01-project-overview/directory-structure.md)中的新老架构对比
3. **技术架构深入**：详细了解[技术架构](./03-technical-architecture/README.md)的设计理念
4. **DDD实践**：研究[DDD设计](./03-technical-architecture/ddd-design.md)和[老架构DDD改造](./03-technical-architecture/legacy-ddd-transformation.md)
5. **规则引擎设计**：深入理解[规则引擎](./03-technical-architecture/rule-engine.md)的实现机制
6. **流程引擎设计**：学习[流程引擎](./03-technical-architecture/process-engine.md)的编排能力

## 📞 联系我们

如果你在使用过程中遇到问题或有改进建议，请：

- 提交Issue到项目仓库
- 联系技术负责人
- 参与技术讨论会议

## 📄 文档维护

本文档由开发团队共同维护，每个模块都有对应的负责人：

- **项目架构**：架构师负责
- **业务模块**：各模块开发负责人
- **开发指南**：技术经理负责
- **API文档**：前后端开发共同维护

---

*最后更新时间：2025-01-06*
*文档版本：v1.0*
