# 项目概述

本章节提供KTV ERP系统的整体概述，包括项目背景、技术选型、系统架构等核心信息。

## 📑 章节内容

### [项目介绍](./project-introduction.md)
- 项目背景与目标
- 核心功能特性
- 业务价值与优势
- 项目团队组织

### [技术栈](./tech-stack.md)
- 后端技术栈
- 前端技术栈
- 数据库与中间件
- 开发工具链

### [系统架构](./system-architecture.md)
- 整体架构设计
- 分层架构模式
- 领域驱动设计(DDD)
- 微服务架构考虑

### [目录结构](./directory-structure.md)
- 项目目录组织
- 代码分层结构
- 模块划分说明
- 文件命名规范

## 🎯 学习路径

对于不同角色的读者，建议的学习路径：

### 新入职开发者
1. 项目介绍 → 了解业务背景
2. 技术栈 → 熟悉技术选型
3. 目录结构 → 理解代码组织
4. 系统架构 → 掌握设计理念

### 架构师/技术负责人
1. 系统架构 → 深入理解设计思路
2. 技术栈 → 评估技术选型合理性
3. 项目介绍 → 了解业务需求
4. 目录结构 → 审查代码组织

### 产品经理/业务人员
1. 项目介绍 → 理解产品定位
2. 系统架构 → 了解技术实现方式
3. 技术栈 → 基础技术概念

## 📋 关键概念

在阅读本章节时，需要重点理解以下概念：

- **领域驱动设计(DDD)**：以业务领域为核心的软件设计方法
- **分层架构**：将系统按职责分为不同层次
- **规则引擎**：将业务规则配置化的技术方案
- **事件驱动**：基于事件的异步处理模式

## 🔗 相关链接

- [技术架构详细设计](../03-technical-architecture/README.md)
- [开发环境搭建](../04-development-guide/environment-setup.md)
- [API接口文档](../05-api-documentation/README.md)

---

*本章节负责人：架构师*
*最后更新：2025-01-06*
