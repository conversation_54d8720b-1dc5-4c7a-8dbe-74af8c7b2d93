# 目录结构

本文档详细介绍KTV ERP系统的项目目录结构，帮助开发者快速理解代码组织方式和各模块的职责。

## 🏗️ 整体结构

### 项目根目录
```
erp-lt-vv/
├── api/                    # API层：接口定义和数据传输对象
├── cmd/                    # 命令行工具和应用入口
├── conf/                   # 配置文件
├── const/                  # 常量定义
├── controller/             # 控制器层：HTTP请求处理
├── db/                     # 数据库相关
├── docs/                   # 项目文档
├── erp_client/            # 客户端模块（DDD架构）
├── erp_managent/          # 管理端模块
├── gen/                   # 代码生成相关
├── internal/              # 内部包
├── log/                   # 日志文件
├── middleware/            # 中间件
├── model/                 # 基础设施层：数据库、缓存等
├── natsdk/               # NATS消息队列SDK
├── notes/                # 开发笔记
├── paysdk/               # 支付SDK
├── router/               # 路由层：请求路由配置
├── rule/                 # 规则引擎
├── script/               # 脚本工具
├── service/              # 服务层：业务逻辑实现
├── terminal/             # 终端相关
├── test/                 # 测试代码
├── tpl/                  # 模板文件
├── tstest/               # TypeScript测试
├── util/                 # 工具包
├── 产品文档/              # 产品相关文档
├── 文档/                  # 技术文档
├── 需求开发/              # 需求开发文档
├── go.mod                # Go模块定义
├── go.sum                # Go模块校验
├── main.go               # 应用程序入口
├── Makefile              # 构建脚本
└── README.md             # 项目说明
```

## 📁 核心目录详解

### API层 (api/)
```
api/
├── req/                   # 请求对象（DTO）
│   ├── AddOrderReqDto.go
│   ├── QueryOrderReqDto.go
│   └── ...
└── vo/                    # 响应对象（VO）
    ├── OrderVO.go
    ├── MemberVO.go
    └── ...
```

**职责**：
- 定义API接口的请求和响应数据结构
- 实现数据传输对象（DTO）和值对象（VO）
- 提供数据验证和序列化功能

### 控制器层 (controller/)
```
controller/
├── OrderController.go      # 订单控制器
├── MemberController.go     # 会员控制器
├── ProductController.go    # 商品控制器
├── PaymentController.go    # 支付控制器
├── check/                  # 健康检查
│   ├── check.go
│   ├── ping.go
│   └── version.go
└── validator/              # 参数验证器
    └── OrderValidator.go
```

**职责**：
- 处理HTTP请求和响应
- 参数验证和格式转换
- 调用服务层处理业务逻辑
- 异常处理和错误响应

### 服务层 (service/)
```
service/
├── impl/                   # 服务实现
│   ├── OrderService.go
│   ├── MemberService.go
│   ├── ProductService.go
│   └── PaymentService.go
├── po/                     # 持久化对象
│   ├── Order.go
│   ├── Member.go
│   ├── Product.go
│   └── PayBill.go
├── transfer/               # 对象转换器
│   ├── OrderTransfer.go
│   ├── MemberTransfer.go
│   └── ...
└── dal/                    # 数据访问层
    ├── gen.go              # GORM生成代码
    ├── member.gen.go
    └── ...
```

**职责**：
- 实现核心业务逻辑
- 数据库操作和事务管理
- 对象转换和数据映射
- 业务规则验证

### 路由层 (router/)
```
router/
├── OrderRoute.go           # 订单路由
├── MemberRoute.go          # 会员路由
├── ProductRoute.go         # 商品路由
├── LoginRouter.go          # 登录路由
├── middleware/             # 中间件
│   ├── auth_token.go       # 认证中间件
│   ├── cros.go            # 跨域中间件
│   ├── logging.go         # 日志中间件
│   └── exception.go       # 异常处理中间件
└── router.go              # 路由配置
```

**职责**：
- 定义API路由规则
- 配置中间件链
- 权限控制和认证
- 请求日志和监控

### 基础设施层 (model/)
```
model/
├── db.go                   # 数据库连接
├── redis.go               # Redis缓存
├── httpclient.go          # HTTP客户端
├── bigcache.go            # 内存缓存
├── timer.go               # 定时器
├── sentry.go              # 错误监控
└── ssh_tunnel.go          # SSH隧道
```

**职责**：
- 数据库连接和配置
- 缓存管理和操作
- 外部服务集成
- 基础设施组件封装

## 🎯 新架构模块详解

### ERP客户端新架构 (erp_client/)
```
erp_client/
├── api/                           # API层：接口定义和控制器实现
│   ├── controller/                # 控制器实现
│   ├── dto/                      # 数据传输对象
│   ├── router/                   # 路由配置
│   └── vo/                       # 视图对象
├── application/                   # 应用层：业务流程编排和应用服务
│   ├── business/                 # 业务应用服务
│   └── framework/                # 应用框架组件
│       ├── runtime/              # 运行时组件
│       ├── variable/             # 变量管理
│       └── yaml/                 # YAML配置处理
├── config/                       # 配置管理
│   ├── config.go                 # 配置定义
│   ├── container.go              # 依赖注入容器
│   ├── processes/                # 流程配置文件
│   ├── rules/                    # 规则配置文件
│   └── validation/               # 验证配置
├── domain/                       # 领域层：核心业务逻辑和领域模型
│   ├── common/                   # 通用领域组件
│   ├── configuration/            # 配置领域
│   ├── engine/                   # 引擎领域
│   ├── permission/               # 权限领域
│   ├── process/                  # 流程领域
│   │   ├── engine/               # 流程引擎实现
│   │   ├── model/                # 流程模型
│   │   └── service/              # 流程领域服务
│   ├── rule/                     # 规则领域
│   │   ├── engine/               # 规则引擎实现
│   │   ├── executor/             # 规则执行器
│   │   ├── model/                # 规则模型
│   │   └── service/              # 规则领域服务
│   ├── state/                    # 状态领域
│   ├── subject/                  # 主体领域
│   ├── traderecord/             # 交易记录领域
│   └── valueobject/             # 值对象领域
├── infrastructure/               # 基础设施层：数据持久化和外部服务
│   ├── common/                   # 通用基础设施
│   ├── event/                    # 事件处理
│   ├── messaging/                # 消息传递
│   ├── persistence/              # 数据持久化
│   └── proxy/                    # 基础设施代理
├── docs/                         # 新架构文档
│   ├── context_specification.md  # 上下文规范
│   ├── engine_refactoring_plan.md # 引擎重构计划
│   ├── process_migration_plan.md  # 流程迁移计划
│   ├── variable_guide.md         # 变量使用指南
│   └── yaml_config_guide.md      # YAML配置指南
├── notes/                        # 开发笔记
│   ├── calc_refactor_guide.md    # 计算重构指南
│   ├── domain_services_guide.md  # 领域服务指南
│   ├── order_*.md               # 订单相关开发指南
│   ├── 会员*.md                  # 会员相关开发指南
│   └── 支付*.md                  # 支付相关开发指南
├── main_client.go                # 新架构应用入口
└── 开发指南.md                   # 新架构开发指南
```

**新架构设计理念**：
- **领域驱动设计(DDD)**：六大核心领域清晰划分，每个领域内聚性强
- **规则引擎驱动**：业务规则配置化，支持YAML格式的规则定义
- **流程引擎编排**：业务流程标准化，支持复杂业务流程的编排
- **事件驱动架构**：领域间通过事件通信，保持松耦合
- **分层架构**：API、应用、领域、基础设施四层清晰分离

### 管理端老架构 (erp_managent/) - 渐进式DDD改造
```
erp_managent/
├── api/                           # API接口定义
│   ├── req/                       # 请求DTO
│   ├── vo/                        # 响应VO
│   └── constant/                  # API常量
├── application/                   # 应用层（部分业务已改造）
│   ├── area/                      # 区域应用服务
│   ├── print/                     # 打印应用服务（完全DDD改造）
│   ├── product/                   # 商品应用服务
│   ├── productout/                # 出品单应用服务
│   ├── roomprice/                 # 价格方案应用服务（完全DDD改造）
│   └── upgrade/                   # 升级应用服务（完全DDD改造）
├── controller/                    # 控制器层（传统MVC + 部分DDD）
│   ├── OrderControllerMainDDD.go  # 订单控制器DDD版本
│   ├── AppUpgradeController.go    # 升级控制器（DDD改造）
│   ├── PrintRecordController.go   # 打印控制器（DDD改造）
│   └── ...                       # 其他传统控制器
├── domain/                        # 领域层（部分业务完全改造）
│   ├── print/                     # 打印领域（完全DDD）
│   │   ├── model/                 # 领域模型
│   │   ├── repository/            # 仓储接口
│   │   └── service/               # 领域服务
│   ├── roomprice/                 # 价格方案领域（完全DDD）
│   │   ├── model/                 # 聚合根和值对象
│   │   ├── repository/            # 仓储接口
│   │   └── service/               # 领域服务
│   ├── room/                      # 房间领域（部分改造）
│   ├── upgrade/                   # 升级领域（完全DDD）
│   └── ...                       # 其他领域
├── infra/                         # 基础设施层（DDD改造模块）
│   ├── repository/                # 仓储实现
│   │   ├── print/                 # 打印仓储实现
│   │   └── roomprice/             # 价格方案仓储实现
│   └── query/                     # 查询服务
├── service/                       # 传统服务层（保留兼容）
│   ├── application/               # 早期应用服务
│   ├── impl/                      # 业务服务实现
│   ├── po/                        # 持久化对象
│   ├── dal/                       # 数据访问层（GORM生成）
│   └── transfer/                  # 对象转换器
└── router/                        # 路由配置
```

**老架构改造特点**：
- **渐进式改造**：保留传统架构，逐步引入DDD设计
- **双模式并存**：传统Service层与DDD应用层并存
- **适配器模式**：DDD仓储实现适配传统Service层
- **分模块改造**：不同业务模块改造程度不同

### DDD改造模块详解

#### 1. 完全DDD改造模块

##### 价格方案模块 (roomprice)
```
erp_managent/domain/roomprice/
├── model/                         # 领域模型
│   ├── buyoutpriceplan/           # 买断价格方案聚合根
│   │   └── BuyoutPricePlan.go     # 买断价格方案聚合根
│   ├── timepriceplan/             # 计时价格方案聚合根
│   │   └── TimePricePlan.go       # 计时价格方案聚合根
│   ├── timediscountplan/          # 时段优惠方案聚合根
│   │   └── TimeDiscountPlan.go    # 时段优惠方案聚合根
│   ├── common/                    # 通用值对象
│   │   ├── Money.go               # 金额值对象
│   │   ├── PriceConfig.go         # 价格配置值对象
│   │   └── RoomTypeConfig.go      # 房间类型配置值对象
│   └── RoomTypeTimePrice.go       # 房间类型基准价格实体
├── repository/                    # 仓储接口
│   ├── BuyoutPricePlanRepository.go
│   ├── TimePricePlanRepository.go
│   ├── TimeDiscountPlanRepository.go
│   └── RoomTypeTimePriceRepository.go
└── service/                       # 领域服务
    └── PricePlanDomainService.go  # 价格方案领域服务

erp_managent/infra/repository/roomprice/
├── BuyoutPricePlanRepositoryImpl.go      # 买断价格方案仓储实现
├── TimePricePlanRepositoryImpl.go        # 计时价格方案仓储实现
├── TimeDiscountPlanRepositoryImpl.go     # 时段优惠方案仓储实现
└── RoomTypeTimePriceRepositoryImpl.go    # 房间类型基准价格仓储实现

erp_managent/application/roomprice/
├── PricePlanAppService.go         # 价格方案应用服务
├── BuyoutPricePlanAppService.go   # 买断价格方案应用服务
└── TimePricePlanAppService.go     # 计时价格方案应用服务
```

**特点**：
- **丰富的领域模型**：聚合根包含完整的业务逻辑和不变性约束
- **值对象设计**：Money、PriceConfig等值对象保证数据完整性
- **仓储模式**：清晰的仓储接口和实现分离
- **适配器集成**：仓储实现适配传统Service层进行数据操作

##### 打印模块 (print)
```
erp_managent/domain/print/
├── model/
│   └── productout/                # 出品单打印领域模型
│       ├── ProductOutPrintRecord.go  # 出品单打印记录聚合根
│       └── ProductOutType.go         # 出品点类型实体
├── repository/                    # 仓储接口
│   ├── ProductOutPrintRecordRepository.go
│   └── ProductOutTypeRepository.go
└── service/                       # 领域服务
    ├── OrderQueryService.go       # 订单查询领域服务
    └── ProductOutDataAssemblyService.go  # 出品单数据组装服务

erp_managent/infra/repository/print/
├── ProductOutPrintRecordRepositoryImpl.go
└── ProductOutTypeRepositoryImpl.go

erp_managent/application/print/
├── ProductOutPrintRecordAppService.go  # 出品单打印应用服务
└── PrintRecordAppService.go           # 打印记录应用服务
```

##### 升级模块 (upgrade)
```
erp_managent/domain/upgrade/
├── model/
│   └── AppUpgrade.go              # 应用升级聚合根
├── repository/
│   └── AppUpgradeRepository.go    # 升级仓储接口
└── service/
    └── AppUpgradeService.go       # 升级领域服务

erp_managent/application/upgrade/
└── AppUpgradeAppService.go        # 升级应用服务
```

#### 2. 部分DDD改造模块

##### 房间模块 (room)
```
erp_managent/domain/room/
├── model/
│   ├── Room.go                    # 房间聚合根（已改造）
│   └── RoomType.go                # 房间类型值对象（已改造）
└── repository/                    # 仓储接口（部分定义）
```

**特点**：
- **领域模型已定义**：Room和RoomType已按DDD模式设计
- **仓储接口待完善**：仓储接口和实现还未完全实现
- **应用层待改造**：仍使用传统Service层处理业务逻辑

#### 3. 传统架构模块

##### 订单模块 (order)
- **传统Controller**：OrderControllerMain.go（传统MVC模式）
- **DDD Controller**：OrderControllerMainDDD.go（DDD模式，但未完全实现）
- **Service层**：仍主要使用service/impl/OrderService.go
- **改造状态**：正在从传统模式向DDD模式迁移

## 🔄 新老架构对比

### 架构演进对比表
| 对比维度 | 老架构传统模式 | 老架构DDD改造 | 新架构(erp_client) |
|----------|---------------|---------------|-------------------|
| **设计模式** | 传统分层架构 | DDD分层架构 | DDD + 规则引擎 + 流程引擎 |
| **业务逻辑** | Service层事务脚本 | 领域模型 + 应用服务 | 领域模型 + 规则配置 + 流程编排 |
| **代码组织** | 按技术分层 | 按业务领域分层 | 按业务领域分层 |
| **领域模型** | 贫血模型（PO） | 充血模型（聚合根） | 充血模型 + 值对象 |
| **仓储模式** | 直接调用Service | 仓储接口 + 实现 | 仓储接口 + 代理 |
| **规则管理** | 硬编码在代码中 | 硬编码在领域模型中 | YAML配置化管理 |
| **流程编排** | 代码中硬编码 | 应用服务编排 | 流程引擎配置化 |
| **数据访问** | 直接GORM操作 | 适配传统Service层 | 基础设施代理 |
| **扩展性** | 修改代码扩展 | 修改领域模型扩展 | 配置文件扩展 |
| **测试性** | 依赖较多，难测试 | 领域隔离，较易测试 | 领域隔离，易测试 |
| **维护性** | 业务逻辑分散 | 业务逻辑内聚 | 业务逻辑内聚 |
| **改造成本** | - | 中等（保留兼容性） | 高（全新架构） |

### 技术栈对比
```mermaid
graph LR
    subgraph "老架构技术栈"
        OldAPI[传统API]
        OldService[Service层]
        OldRepo[Repository]
        OldDB[(数据库)]
    end

    subgraph "新架构技术栈"
        NewAPI[API层]
        NewApp[应用层]
        NewProcess[流程引擎]
        NewRule[规则引擎]
        NewDomain[领域层]
        NewInfra[基础设施层]
    end

    OldAPI --> OldService
    OldService --> OldRepo
    OldRepo --> OldDB

    NewAPI --> NewApp
    NewApp --> NewProcess
    NewProcess --> NewRule
    NewRule --> NewDomain
    NewDomain --> NewInfra
```

### 业务处理方式对比

#### 传统架构业务处理
```go
// 传统架构：价格计算（事务脚本模式）
func (s *PricePlanService) CalculatePrice(req *CalculatePriceRequest) (*PriceResult, error) {
    // 1. 参数验证
    if err := s.validateRequest(req); err != nil {
        return nil, err
    }

    // 2. 获取基础价格
    basePrice, err := s.getBasePrice(req.RoomTypeID)
    if err != nil {
        return nil, err
    }

    // 3. 计算价格（硬编码规则）
    totalAmount := basePrice
    if req.IsHoliday {
        totalAmount = totalAmount * 1.5 // 节假日1.5倍
    }
    if req.MemberLevel == "VIP" {
        totalAmount = totalAmount * 0.9 // VIP 9折
    }

    // 4. 返回结果
    return &PriceResult{
        BaseAmount:  basePrice,
        TotalAmount: totalAmount,
    }, nil
}
```

#### DDD改造架构业务处理
```go
// DDD改造架构：价格计算（领域模型 + 应用服务）
func (s *PricePlanAppService) CalculatePrice(ctx *gin.Context, req *req.CalculatePriceReqDto) (*vo.PriceResultVO, error) {
    // 1. 使用领域服务进行价格计算
    priceRequest := &model.PriceCalculationRequest{
        VenueID:     req.VenueID,
        RoomTypeID:  req.RoomTypeID,
        MemberLevel: req.MemberLevel,
        IsHoliday:   req.IsHoliday,
        Duration:    req.Duration,
    }

    // 2. 调用领域服务
    result, err := s.pricePlanDomainService.CalculatePrice(ctx, priceRequest)
    if err != nil {
        return nil, err
    }

    // 3. 转换为VO返回
    return s.pricePlanTransfer.DomainToVO(result), nil
}

// 领域服务实现
func (s *pricePlanDomainService) CalculatePrice(ctx *gin.Context, req *model.PriceCalculationRequest) (*model.PriceResult, error) {
    // 1. 获取价格方案聚合根
    pricePlan, err := s.timePricePlanRepo.FindByRoomTypeID(ctx, req.RoomTypeID)
    if err != nil {
        return nil, err
    }

    // 2. 使用聚合根的业务方法计算价格
    return pricePlan.CalculatePrice(req), nil
}

// 聚合根业务方法
func (p *TimePricePlan) CalculatePrice(req *PriceCalculationRequest) *PriceResult {
    // 领域模型内部的业务逻辑
    baseAmount := p.getBaseAmount()

    // 应用价格配置
    for _, config := range p.priceConfigList {
        if config.IsApplicable(req) {
            baseAmount = config.ApplyPrice(baseAmount)
        }
    }

    return &PriceResult{
        BaseAmount:  p.getBaseAmount(),
        TotalAmount: baseAmount,
    }
}
```

#### 新架构业务处理
```go
// 新架构：价格计算（流程引擎 + 规则引擎）
func (s *PriceAppService) CalculatePrice(ctx context.Context, req *dto.CalculatePriceRequest) (*vo.PriceResultVO, error) {
    // 1. 启动价格计算流程
    processParams := map[string]interface{}{
        "price_request": req,
        "room_info":     s.getRoomInfo(ctx, req.RoomID),
        "member_info":   s.getMemberInfo(ctx, req.MemberID),
    }

    // 2. 执行流程引擎（流程中集成规则引擎）
    result, err := s.processEngine.Execute(ctx, "price_calculation_process", processParams)
    if err != nil {
        return nil, fmt.Errorf("价格计算流程执行失败: %w", err)
    }

    // 3. 构造返回结果
    return s.buildPriceVO(result), nil
}
```

### 配置化对比

#### 老架构：硬编码规则
```go
// 价格计算逻辑硬编码在代码中
func calculatePrice(baseAmount decimal.Decimal, member *Member) decimal.Decimal {
    if member == nil {
        return baseAmount
    }

    switch member.Level {
    case "VIP":
        return baseAmount.Mul(decimal.NewFromFloat(0.9))
    case "GOLD":
        return baseAmount.Mul(decimal.NewFromFloat(0.95))
    default:
        return baseAmount
    }
}
```

#### 新架构：配置化规则
```yaml
# 价格计算规则配置文件
rule_group_id: "order_pricing_rules"
name: "订单价格计算规则"

rules:
  - name: "VIP会员折扣"
    priority: 100
    condition: "input.member_info != null && input.member_info.level == 'VIP'"
    actions:
      - type: "calculate"
        params:
          target: "output.total_amount"
          expression: "input.order_info.amount * 0.9"

  - name: "金牌会员折扣"
    priority: 90
    condition: "input.member_info != null && input.member_info.level == 'GOLD'"
    actions:
      - type: "calculate"
        params:
          target: "output.total_amount"
          expression: "input.order_info.amount * 0.95"
```

## 🔧 工具和配置

### 配置文件 (conf/)
```
conf/
├── config.yaml            # 默认配置
├── local_config.yaml      # 本地开发配置
├── online_config.yaml     # 线上配置
├── pre_config.yaml        # 预发布配置
└── windows_config.yaml    # Windows配置
```

### 脚本工具 (script/)
```
script/
├── api_docs/              # API文档生成
├── business_staff/        # 业务员工脚本
├── check/                 # 检查脚本
├── collection_api_codes.py # API代码收集
├── collection_code_tree.sh # 代码树生成
├── generate_docs.py       # 文档生成
├── merge_codes.py         # 代码合并
├── setup_swag.sh          # Swagger设置
└── swag.sh               # Swagger生成
```

### 规则引擎 (rule/)
```
rule/
├── core/                  # 核心引擎
├── groups/                # 规则组
├── rules/                 # 规则定义
├── testcase/              # 测试用例
└── validator/             # 规则验证器
```

## 📊 代码统计

### 目录大小统计
| 目录 | 文件数 | 代码行数 | 主要语言 |
|------|--------|----------|----------|
| service/ | 200+ | 50,000+ | Go |
| controller/ | 100+ | 20,000+ | Go |
| api/ | 500+ | 30,000+ | Go |
| erp_client/ | 150+ | 25,000+ | Go |
| router/ | 50+ | 5,000+ | Go |
| model/ | 20+ | 3,000+ | Go |

### 模块复杂度
- **高复杂度**：service/impl/（业务逻辑复杂）
- **中复杂度**：controller/（请求处理逻辑）
- **低复杂度**：api/（数据结构定义）

## 🎨 命名规范

### 文件命名
- **Go文件**：PascalCase，如 `OrderService.go`
- **配置文件**：snake_case，如 `local_config.yaml`
- **脚本文件**：snake_case，如 `generate_docs.py`

### 目录命名
- **模块目录**：snake_case，如 `erp_client`
- **功能目录**：小写，如 `controller`
- **工具目录**：小写，如 `script`

### 包命名
- **业务包**：简短有意义，如 `order`、`member`
- **工具包**：功能描述，如 `util`、`middleware`
- **第三方包**：保持原名，如 `paysdk`、`natsdk`

## 🔍 快速定位

### 常用文件位置
```bash
# 应用入口
main.go

# 配置文件
conf/local_config.yaml

# 订单相关
controller/OrderController.go
service/impl/OrderService.go
api/req/AddOrderReqDto.go
api/vo/OrderVO.go

# 会员相关
controller/MemberController.go
service/impl/MemberService.go

# 路由配置
router/router.go
router/OrderRoute.go

# 数据库模型
service/po/Order.go
service/po/Member.go
```

### 开发常用命令
```bash
# 启动服务
make run

# 生成代码
make dal

# 运行测试
make test

# 构建项目
make build

# 生成文档
make swagger
```

---

*文档维护者：架构师、开发团队*
*最后更新：2025-01-06*
