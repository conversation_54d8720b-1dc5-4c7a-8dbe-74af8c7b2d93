# 系统架构

## 🏗️ 架构概述

KTV ERP系统采用**双架构并存**的设计模式：
- **erp_managent**：传统分层架构，承载现有业务功能
- **erp_client**：全新DDD架构，基于领域驱动设计+规则引擎+流程引擎的现代化架构

新架构(erp_client)是系统的核心发展方向，采用领域驱动设计(DDD)和分层架构模式，结合规则引擎和流程引擎，构建了一个高内聚、低耦合、易扩展的企业级应用系统。

## 🎯 新架构设计理念

### 核心原则
- **领域驱动设计(DDD)**：以业务领域为核心，通过清晰的分层架构和领域模型，保证业务逻辑的内聚和可维护性
- **规则引擎驱动**：业务规则是核心驱动力，系统行为和业务决策由可配置的业务规则驱动，而不是硬编码在业务逻辑中
- **流程编排**：业务流程是核心执行路径，使用流程引擎将业务逻辑组织成清晰的流程，流程中的关键节点集成规则引擎
- **事件驱动**：通过事件实现领域间通信，保持模块间的松耦合

### 六大核心要素
```mermaid
graph TB
    Subject[主体 Subject] --> ValueObject[值对象 ValueObject]
    Subject --> Rule[规则 Rule]
    Subject --> Permission[权限 Permission]
    Subject --> State[状态 State]
    Subject --> Order[订单 Order]

    ValueObject --> Business[业务实体]
    Rule --> Engine[规则引擎]
    Permission --> RBAC[访问控制]
    State --> FSM[状态机]
    Order --> Transaction[交易记录]
```

### 架构约束
- **领域隔离**：领域服务之间禁止直接调用，跨领域交互必须通过事件
- **接口规范**：所有领域必须实现标准原子操作，禁止绕过接口直接访问资源
- **数据流向**：单向数据流，状态不可变，通过事件传递变更
- **事务处理**：领域内事务保证ACID，跨领域采用最终一致性

## 🏛️ 新架构分层设计

### erp_client架构分层图
```mermaid
graph TB
    subgraph "API层 - 接口定义和控制器实现"
        Controller[Controller控制器]
        DTO[DTO数据传输对象]
        Router[Router路由配置]
        VO[VO值对象]
    end

    subgraph "应用层 - 业务流程编排和应用服务"
        AppService[Application Service应用服务]
        ProcessEngine[Process Engine流程引擎]
        BusinessProcess[Business Process业务流程]
        Framework[Framework通用框架]
    end

    subgraph "领域层 - 核心业务逻辑和领域模型"
        RuleEngine[Rule Engine规则引擎]
        ValueObject[Value Object值对象]
        DomainService[Domain Service领域服务]
        ProcessModel[Process Model流程模型]
        RuleModel[Rule Model规则模型]
    end

    subgraph "基础设施层 - 数据持久化和外部服务"
        Proxy[Infrastructure Proxy基础设施代理]
        Repository[Repository仓储实现]
        Engine[Engine引擎实现]
        External[External外部服务]
    end

    Controller --> AppService
    AppService --> ProcessEngine
    ProcessEngine --> BusinessProcess
    BusinessProcess --> RuleEngine
    RuleEngine --> DomainService
    DomainService --> Proxy
    Proxy --> Repository
```

### 新架构核心工作流程
```mermaid
sequenceDiagram
    participant C as Controller
    participant AS as Application Service
    participant PE as Process Engine
    participant RE as Rule Engine
    participant DS as Domain Service
    participant P as Infrastructure Proxy

    C->>AS: 1. 接收请求，调用应用服务
    AS->>PE: 2. 启动业务流程
    PE->>RE: 3. 执行业务规则
    RE->>DS: 4. 调用领域服务
    DS->>P: 5. 通过代理访问基础设施
    P-->>DS: 6. 返回数据
    DS-->>RE: 7. 返回领域结果
    RE-->>PE: 8. 返回规则结果
    PE-->>AS: 9. 返回流程结果
    AS-->>C: 10. 返回最终结果
```

### 各层职责

#### 表现层 (API Layer)
```go
// 职责：处理HTTP请求，数据验证，响应格式化
type OrderController struct {
    orderAppService application.OrderAppService
}

func (c *OrderController) CreateOrder(ctx *gin.Context) {
    var req dto.CreateOrderRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        // 参数验证
        return
    }
    
    result, err := c.orderAppService.CreateOrder(ctx, req)
    // 响应处理
}
```

#### 应用层 (Application Layer)
```go
// 职责：业务流程编排，事务管理，领域服务协调
type OrderAppService struct {
    orderService    domain.OrderService
    paymentService  domain.PaymentService
    ruleEngine     rule.Engine
}

func (s *OrderAppService) CreateOrder(ctx context.Context, req dto.CreateOrderRequest) error {
    // 1. 业务流程编排
    // 2. 规则引擎执行
    // 3. 领域服务调用
    // 4. 事务管理
}
```

#### 领域层 (Domain Layer)
```go
// 职责：核心业务逻辑，业务规则，领域模型
type Order struct {
    ID          string
    VenueID     string
    SessionID   string
    Products    []OrderProduct
    TotalAmount decimal.Decimal
    Status      OrderStatus
}

func (o *Order) CalculateTotal() error {
    // 核心业务逻辑
}
```

#### 基础设施层 (Infrastructure Layer)
```go
// 职责：数据持久化，外部服务集成，技术实现
type OrderRepository struct {
    db *gorm.DB
}

func (r *OrderRepository) Save(order *domain.Order) error {
    // 数据持久化实现
}
```

## 🔧 规则引擎架构

### 规则引擎设计
```mermaid
graph LR
    subgraph "规则定义"
        YAML[YAML配置]
        JSON[JSON配置]
        DB[数据库配置]
    end
    
    subgraph "规则引擎核心"
        Parser[规则解析器]
        Engine[执行引擎]
        Context[执行上下文]
    end
    
    subgraph "规则执行"
        Condition[条件评估]
        Action[动作执行]
        Result[执行结果]
    end
    
    YAML --> Parser
    JSON --> Parser
    DB --> Parser
    Parser --> Engine
    Engine --> Context
    Context --> Condition
    Condition --> Action
    Action --> Result
```

### 规则引擎组件
```go
// 规则引擎核心接口
type RuleEngine interface {
    AddRule(rule Rule) error
    ExecuteRules(ctx RuleContext) error
    GetRulesByGroup(group string) []Rule
}

// 规则定义
type Rule struct {
    ID          string      `yaml:"id"`
    Name        string      `yaml:"name"`
    Priority    int         `yaml:"priority"`
    Condition   Condition   `yaml:"condition"`
    Actions     []Action    `yaml:"actions"`
    Enabled     bool        `yaml:"enabled"`
}
```

## 🔄 事件驱动架构

### 事件流转图
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API层
    participant App as 应用层
    participant Domain as 领域层
    participant Event as 事件总线
    participant Handler as 事件处理器
    
    Client->>API: 创建订单请求
    API->>App: 调用应用服务
    App->>Domain: 执行领域逻辑
    Domain->>Event: 发布订单创建事件
    Event->>Handler: 异步处理事件
    Handler-->>Event: 处理完成
    Domain-->>App: 返回结果
    App-->>API: 返回响应
    API-->>Client: 响应结果
```

### 事件处理机制
```go
// 事件定义
type OrderCreatedEvent struct {
    OrderID   string    `json:"order_id"`
    VenueID   string    `json:"venue_id"`
    Amount    decimal.Decimal `json:"amount"`
    CreatedAt time.Time `json:"created_at"`
}

// 事件处理器
type OrderEventHandler struct {
    inventoryService domain.InventoryService
    notificationService domain.NotificationService
}

func (h *OrderEventHandler) HandleOrderCreated(event OrderCreatedEvent) error {
    // 库存扣减
    // 通知发送
    // 其他业务处理
}
```

## 🗄️ 数据架构

### 数据分层设计
```mermaid
graph TB
    subgraph "数据访问层"
        DAO[数据访问对象]
        Repository[仓储实现]
        Query[查询构建器]
    end
    
    subgraph "数据存储层"
        MySQL[(MySQL主库)]
        Redis[(Redis缓存)]
        Backup[(备份数据库)]
    end
    
    subgraph "数据模型层"
        PO[持久化对象]
        VO[值对象]
        DTO[传输对象]
    end
    
    Repository --> DAO
    DAO --> MySQL
    DAO --> Redis
    MySQL --> Backup
    
    PO --> Repository
    VO --> PO
    DTO --> VO
```

### 数据一致性策略
- **强一致性**：核心业务数据（订单、支付）
- **最终一致性**：统计数据、报表数据
- **缓存一致性**：Cache-Aside模式
- **分布式事务**：本地事务 + 事件最终一致性

## 🔐 安全架构

### 安全层次图
```mermaid
graph TB
    subgraph "网络安全"
        HTTPS[HTTPS加密]
        Firewall[防火墙]
        WAF[Web应用防火墙]
    end
    
    subgraph "应用安全"
        Auth[身份认证]
        RBAC[权限控制]
        Validation[输入验证]
    end
    
    subgraph "数据安全"
        Encryption[数据加密]
        Masking[数据脱敏]
        Audit[审计日志]
    end
    
    HTTPS --> Auth
    Auth --> RBAC
    RBAC --> Encryption
```

### 认证授权流程
```go
// JWT Token认证
type AuthMiddleware struct {
    jwtSecret string
}

func (m *AuthMiddleware) Authenticate(ctx *gin.Context) {
    token := ctx.GetHeader("Authorization")
    claims, err := m.validateToken(token)
    if err != nil {
        ctx.AbortWithStatus(401)
        return
    }
    
    ctx.Set("user_id", claims.UserID)
    ctx.Set("permissions", claims.Permissions)
    ctx.Next()
}
```

## 📈 性能架构

### 性能优化策略
```mermaid
graph LR
    subgraph "缓存策略"
        L1[内存缓存]
        L2[Redis缓存]
        L3[数据库]
    end
    
    subgraph "并发控制"
        Pool[连接池]
        Goroutine[协程池]
        Limit[限流器]
    end
    
    subgraph "数据优化"
        Index[索引优化]
        Partition[分区表]
        Archive[数据归档]
    end
    
    L1 --> L2
    L2 --> L3
    Pool --> Goroutine
    Goroutine --> Limit
```

### 监控与观测
- **应用性能监控**：响应时间、吞吐量、错误率
- **基础设施监控**：CPU、内存、磁盘、网络
- **业务指标监控**：订单量、支付成功率、用户活跃度
- **日志聚合分析**：ELK Stack或类似方案

## 🚀 扩展性设计

### 水平扩展能力
- **无状态设计**：应用服务无状态，支持水平扩展
- **数据库分片**：支持读写分离和分库分表
- **缓存集群**：Redis集群模式
- **负载均衡**：多实例负载均衡

### 微服务演进路径
```mermaid
graph TB
    Current[当前单体架构] --> Phase1[模块化重构]
    Phase1 --> Phase2[服务拆分]
    Phase2 --> Phase3[微服务架构]
    Phase3 --> Phase4[云原生架构]
    
    Phase1 --> |DDD边界| Modules[明确模块边界]
    Phase2 --> |API网关| Gateway[统一入口]
    Phase3 --> |服务治理| Governance[服务发现/配置]
    Phase4 --> |容器化| K8s[Kubernetes]
```

---

*文档维护者：架构师*
*最后更新：2025-01-06*
