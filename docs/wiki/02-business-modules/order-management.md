# 订单管理

## 📖 模块概述

订单管理是KTV ERP系统的核心模块，负责处理从顾客进店到离店的完整业务流程。该模块集成了房间管理、商品管理、会员管理和支付管理，是整个系统的业务中枢。

## 🎯 核心功能

### 开台流程
- **包厢选择**：根据人数、时长、预算推荐合适包厢
- **价格计算**：基于时段、节假日、会员等级的动态定价
- **会员识别**：支持手机号、会员卡、人脸识别等方式
- **预付款处理**：支持预付费模式，提前锁定消费金额

### 点单流程
- **商品浏览**：分类展示、搜索过滤、推荐算法
- **套餐选择**：预设套餐、自定义组合、动态调整
- **特殊需求**：口味偏好、温度要求、配送时间
- **库存检查**：实时库存验证、缺货提醒、替代推荐

### 结账流程
- **费用计算**：房费、商品费、服务费的精确计算
- **优惠处理**：会员折扣、促销活动、优惠券使用
- **支付集成**：多种支付方式、分账处理、找零管理
- **发票开具**：电子发票、纸质发票、税务合规

### 退款流程
- **退款申请**：商品退款、房费退款、服务费退款
- **审批流程**：权限控制、审批记录、状态跟踪
- **资金处理**：原路退回、现金退款、余额退款
- **库存回滚**：退货入库、状态更新、成本调整

## 🔄 业务流程

### 完整业务流程图
```mermaid
sequenceDiagram
    participant C as 顾客
    participant S as 服务员
    participant Sys as 系统
    participant Pay as 支付系统
    participant Kitchen as 后厨
    
    C->>S: 到店选择包厢
    S->>Sys: 查询可用包厢
    Sys-->>S: 返回包厢列表
    S->>Sys: 创建订单(开台)
    Sys->>Sys: 计算房费
    Sys-->>S: 返回订单信息
    
    C->>S: 点选商品
    S->>Sys: 添加商品到订单
    Sys->>Sys: 检查库存
    Sys->>Kitchen: 发送出品通知
    Sys-->>S: 确认添加成功
    
    C->>S: 申请结账
    S->>Sys: 计算总费用
    Sys->>Sys: 应用优惠规则
    Sys-->>S: 返回账单详情
    S->>Pay: 发起支付
    Pay-->>S: 支付成功
    S->>Sys: 完成订单
    Sys->>Sys: 更新库存
    Sys-->>S: 订单完成
```

### 订单状态流转
```mermaid
stateDiagram-v2
    [*] --> 待开台
    待开台 --> 使用中: 开台成功
    使用中 --> 使用中: 添加商品
    使用中 --> 待结账: 申请结账
    待结账 --> 已结账: 支付成功
    待结账 --> 使用中: 取消结账
    已结账 --> 已完成: 离店确认
    已结账 --> 部分退款: 申请退款
    部分退款 --> 已完成: 退款完成
    
    使用中 --> 已取消: 取消订单
    待结账 --> 已取消: 取消订单
    已取消 --> [*]
    已完成 --> [*]
```

## 💾 数据模型

### 核心实体关系
```mermaid
erDiagram
    Order ||--o{ OrderProduct : contains
    Order ||--|| Room : occupies
    Order ||--o| Member : belongs_to
    Order ||--o{ PayBill : generates
    
    Order {
        string id PK
        string venue_id
        string session_id
        string room_id
        string member_id
        decimal room_amount
        decimal product_amount
        decimal total_amount
        decimal discount_amount
        decimal pay_amount
        string status
        datetime created_at
        datetime updated_at
    }
    
    OrderProduct {
        string id PK
        string order_id FK
        string product_id
        string product_name
        decimal unit_price
        int quantity
        decimal total_price
        string status
    }
    
    Room {
        string id PK
        string name
        string type_id
        string area_id
        string status
        decimal base_price
    }
    
    Member {
        string id PK
        string phone
        string name
        string level
        decimal balance
        int points
    }
    
    PayBill {
        string id PK
        string order_id FK
        decimal amount
        string payment_method
        string status
        datetime paid_at
    }
```

### 关键字段说明

#### Order（订单表）
- **session_id**：场次ID，用于区分同一包厢的不同使用时段
- **room_amount**：房费金额，基于时长和价格方案计算
- **product_amount**：商品金额，所有商品的总价
- **discount_amount**：优惠金额，包括会员折扣、促销优惠等
- **pay_amount**：实付金额，最终需要支付的金额

#### OrderProduct（订单商品表）
- **unit_price**：单价，下单时的商品价格
- **quantity**：数量，购买数量
- **total_price**：小计，单价×数量
- **status**：状态，待出品/已出品/已退款等

## 🧮 价格计算规则

### 计算公式
```go
// 订单总金额计算
type OrderCalculation struct {
    RoomAmount     decimal.Decimal // 房费
    ProductAmount  decimal.Decimal // 商品费
    ServiceAmount  decimal.Decimal // 服务费
    DiscountAmount decimal.Decimal // 优惠金额
    TotalAmount    decimal.Decimal // 应收金额
    PayAmount      decimal.Decimal // 实付金额
}

// 计算逻辑
func CalculateOrderAmount(order *Order) *OrderCalculation {
    calc := &OrderCalculation{}
    
    // 1. 计算房费
    calc.RoomAmount = calculateRoomAmount(order)
    
    // 2. 计算商品费
    calc.ProductAmount = calculateProductAmount(order)
    
    // 3. 计算服务费
    calc.ServiceAmount = calculateServiceAmount(order)
    
    // 4. 计算优惠金额
    calc.DiscountAmount = calculateDiscount(order, calc)
    
    // 5. 计算应收金额
    calc.TotalAmount = calc.RoomAmount + calc.ProductAmount + calc.ServiceAmount
    
    // 6. 计算实付金额
    calc.PayAmount = calc.TotalAmount - calc.DiscountAmount
    
    return calc
}
```

### 房费计算
```go
func calculateRoomAmount(order *Order) decimal.Decimal {
    // 获取价格方案
    pricePlan := getPricePlan(order.RoomID, order.CreatedAt)
    
    // 计算使用时长
    duration := calculateDuration(order.StartTime, order.EndTime)
    
    // 基础房费
    baseAmount := pricePlan.BasePrice.Mul(duration)
    
    // 时段加价
    timeAmount := calculateTimePrice(pricePlan, order.StartTime, duration)
    
    // 节假日加价
    holidayAmount := calculateHolidayPrice(pricePlan, order.CreatedAt)
    
    return baseAmount.Add(timeAmount).Add(holidayAmount)
}
```

### 优惠计算
```go
func calculateDiscount(order *Order, calc *OrderCalculation) decimal.Decimal {
    var totalDiscount decimal.Decimal
    
    // 会员折扣
    if order.MemberID != "" {
        memberDiscount := calculateMemberDiscount(order, calc)
        totalDiscount = totalDiscount.Add(memberDiscount)
    }
    
    // 促销活动
    promotionDiscount := calculatePromotionDiscount(order, calc)
    totalDiscount = totalDiscount.Add(promotionDiscount)
    
    // 优惠券
    couponDiscount := calculateCouponDiscount(order, calc)
    totalDiscount = totalDiscount.Add(couponDiscount)
    
    // 商家调整
    merchantDiscount := calculateMerchantDiscount(order, calc)
    totalDiscount = totalDiscount.Add(merchantDiscount)
    
    return totalDiscount
}
```

## 🔧 核心接口

### 订单操作接口
```go
// 创建订单（开台）
POST /api/order/create
{
    "venue_id": "venue_001",
    "room_id": "room_001",
    "member_id": "member_001", // 可选
    "guest_count": 4,
    "estimated_duration": 120, // 预计使用时长（分钟）
    "remark": "生日聚会"
}

// 添加商品
POST /api/order/add-product
{
    "order_id": "order_001",
    "products": [
        {
            "product_id": "product_001",
            "quantity": 2,
            "unit_price": 25.00,
            "remark": "少冰"
        }
    ]
}

// 计算费用
POST /api/order/calculate
{
    "order_id": "order_001",
    "end_time": "2025-01-06T20:00:00Z", // 可选，默认当前时间
    "coupons": ["coupon_001"], // 可选，使用的优惠券
    "merchant_discount": 10.00 // 可选，商家调整金额
}

// 结账支付
POST /api/order/pay
{
    "order_id": "order_001",
    "payment_method": "wechat",
    "pay_amount": 158.00,
    "remark": "现金找零5元"
}
```

### 查询接口
```go
// 查询订单详情
GET /api/order/detail?order_id=order_001

// 查询订单列表
POST /api/order/list
{
    "venue_id": "venue_001",
    "status": "using", // 可选，订单状态
    "room_id": "room_001", // 可选，包厢ID
    "member_id": "member_001", // 可选，会员ID
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "page": 1,
    "page_size": 20
}

// 查询包厢当前订单
GET /api/order/current?room_id=room_001
```

## ⚡ 性能优化

### 缓存策略
```go
// 热点数据缓存
type OrderCache struct {
    // 当前使用中的订单（按包厢ID索引）
    ActiveOrders map[string]*Order
    
    // 价格方案缓存
    PricePlans map[string]*PricePlan
    
    // 商品信息缓存
    Products map[string]*Product
}

// 缓存更新策略
func (c *OrderCache) UpdateOrder(order *Order) {
    // 更新内存缓存
    c.ActiveOrders[order.RoomID] = order
    
    // 更新Redis缓存
    redis.Set(fmt.Sprintf("order:room:%s", order.RoomID), order, 24*time.Hour)
    
    // 发送缓存更新事件
    eventBus.Publish("order.cache.updated", order)
}
```

### 数据库优化
```sql
-- 关键索引
CREATE INDEX idx_order_venue_status ON orders(venue_id, status);
CREATE INDEX idx_order_room_created ON orders(room_id, created_at);
CREATE INDEX idx_order_member_created ON orders(member_id, created_at);
CREATE INDEX idx_order_product_order ON order_products(order_id);

-- 分区表（按月分区）
ALTER TABLE orders PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at));
```

## 🚨 异常处理

### 常见异常场景
1. **库存不足**：商品库存不够，需要提示并推荐替代商品
2. **支付失败**：网络异常、余额不足等支付问题
3. **并发冲突**：多人同时操作同一订单
4. **数据不一致**：缓存与数据库数据不一致

### 异常处理策略
```go
// 统一异常处理
type OrderError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

var (
    ErrOrderNotFound     = &OrderError{"ORDER_NOT_FOUND", "订单不存在", nil}
    ErrOrderStatusInvalid = &OrderError{"ORDER_STATUS_INVALID", "订单状态无效", nil}
    ErrInsufficientStock = &OrderError{"INSUFFICIENT_STOCK", "库存不足", nil}
    ErrPaymentFailed     = &OrderError{"PAYMENT_FAILED", "支付失败", nil}
)

// 重试机制
func (s *OrderService) CreateOrderWithRetry(ctx context.Context, req *CreateOrderRequest) (*Order, error) {
    var lastErr error
    for i := 0; i < 3; i++ {
        order, err := s.CreateOrder(ctx, req)
        if err == nil {
            return order, nil
        }
        
        // 判断是否可重试
        if !isRetryableError(err) {
            return nil, err
        }
        
        lastErr = err
        time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
    }
    
    return nil, lastErr
}
```

---

*文档维护者：订单模块负责人*
*最后更新：2025-01-06*
