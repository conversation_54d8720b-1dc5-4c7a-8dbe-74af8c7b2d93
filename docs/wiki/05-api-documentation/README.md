# API文档

本章节提供KTV ERP系统的完整API文档，包括接口规范、认证授权、错误处理和具体的接口列表。

## 📋 文档概览

### API特点
- **统一POST接口**：所有API接口统一使用POST方法，简化客户端调用逻辑
- **标准化响应**：统一的JSON响应格式，包含状态码、消息、数据和请求ID
- **完善的错误处理**：详细的错误码和错误信息，支持调试追踪
- **安全认证**：基于JWT的身份认证和权限控制机制
- **版本管理**：支持API版本控制，保证向后兼容性

### 技术规范
- **协议**：HTTPS（生产环境）/ HTTP（开发环境）
- **数据格式**：JSON（UTF-8编码）
- **请求方法**：POST（统一使用POST方法）
- **认证方式**：JWT Bearer Token
- **文档生成**：Swagger自动生成，支持在线调试

### POST接口设计原因
基于KTV业务特点和技术考虑，本系统采用统一POST接口设计：

1. **业务复杂性**：KTV业务场景复杂，大部分操作都涉及数据修改和复杂参数传递
2. **参数安全性**：敏感业务数据通过请求体传输，避免在URL中暴露
3. **客户端简化**：统一的调用方式，简化前端和移动端的HTTP客户端实现
4. **网关友好**：便于API网关的统一处理、监控和限流
5. **缓存控制**：POST请求不会被浏览器和代理服务器缓存，确保数据实时性

## 📚 章节内容

### [接口规范](./api-standards.md)
定义API设计的基本规范和约定。

**主要内容**：
- URL设计规范：资源命名、路径结构
- HTTP方法使用：统一使用POST方法的原因和实现
- 请求响应格式：标准化的数据结构
- 状态码定义：HTTP状态码的使用规范

### [认证授权](./authentication.md)
详细介绍系统的认证和授权机制。

**主要内容**：
- JWT Token认证：Token生成、验证、刷新
- 权限控制：RBAC模型、权限检查
- 安全机制：Token过期、黑名单、防重放
- 登录流程：用户登录、权限获取

### [错误处理](./error-handling.md)
统一的错误处理机制和错误码定义。

**主要内容**：
- 错误响应格式：标准化的错误信息结构
- 错误码分类：业务错误、系统错误、参数错误
- 错误处理策略：异常捕获、错误日志、用户提示
- 调试信息：开发环境的详细错误信息

### [接口列表](./api-list.md)
完整的API接口清单和详细说明。

**主要内容**：
- 订单管理接口：开台、点单、结账、退款
- 会员管理接口：注册、查询、充值、消费
- 商品管理接口：商品信息、库存管理
- 系统管理接口：用户管理、权限配置

## 🔧 API基础信息

### 服务地址
```
开发环境：http://localhost:8080
测试环境：https://test-api.ktv-erp.com
生产环境：https://api.ktv-erp.com
```

### 通用请求头
```http
Content-Type: application/json
Authorization: Bearer <JWT_TOKEN>
X-Request-ID: <UNIQUE_REQUEST_ID>
X-Client-Version: <CLIENT_VERSION>
```

### 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体业务数据
  },
  "request_id": "req_123456789",
  "timestamp": "2025-01-06T10:30:00Z"
}
```

### 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      // 数据列表
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5
    }
  },
  "request_id": "req_123456789",
  "timestamp": "2025-01-06T10:30:00Z"
}
```

## 🔐 认证流程

### 登录获取Token
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123",
  "venue_id": "venue_001"
}
```

### 响应示例
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200,
    "user_info": {
      "user_id": "user_001",
      "username": "admin",
      "name": "管理员",
      "role": "admin",
      "permissions": ["order:create", "order:query", "member:manage"]
    }
  }
}
```

### 使用Token访问接口
```http
POST /api/order/create
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "venue_id": "venue_001",
  "room_id": "room_001",
  "member_id": "member_001"
}
```

## 📊 核心接口示例

### 订单管理
```http
# 创建订单（开台）
POST /api/order/create
{
  "venue_id": "venue_001",
  "room_id": "room_001",
  "member_id": "member_001",
  "guest_count": 4,
  "estimated_duration": 120
}

# 添加商品
POST /api/order/add-product
{
  "order_id": "order_001",
  "products": [
    {
      "product_id": "product_001",
      "quantity": 2,
      "unit_price": 25.00
    }
  ]
}

# 订单结账
POST /api/order/pay
{
  "order_id": "order_001",
  "payment_method": "wechat",
  "pay_amount": 158.00
}
```

### 会员管理
```http
# 会员查询
POST /api/member/query
{
  "phone": "13800138000"
}

# 会员充值
POST /api/member/recharge
{
  "member_id": "member_001",
  "amount": 500.00,
  "payment_method": "wechat"
}
```

### 商品管理
```http
# 商品列表
POST /api/product/list
{
  "venue_id": "venue_001",
  "category_id": "category_001",
  "page": 1,
  "page_size": 20
}

# 库存查询
POST /api/product/stock
{
  "venue_id": "venue_001",
  "product_ids": ["product_001", "product_002"]
}
```

## 🚨 错误码说明

### 通用错误码
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权 | 检查Token是否有效 |
| 403 | 权限不足 | 联系管理员分配权限 |
| 404 | 资源不存在 | 检查资源ID是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 业务错误码
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 10001 | 订单不存在 | 检查订单ID |
| 10002 | 订单状态无效 | 检查订单当前状态 |
| 10003 | 库存不足 | 选择其他商品或减少数量 |
| 20001 | 会员不存在 | 检查会员信息 |
| 20002 | 会员余额不足 | 提醒会员充值 |
| 30001 | 房间已被占用 | 选择其他房间 |
| 30002 | 房间状态异常 | 联系服务员处理 |

## 📖 Swagger文档

### 访问地址
```
开发环境：http://localhost:8080/swagger/index.html
测试环境：https://test-api.ktv-erp.com/swagger/index.html
```

### 文档特性
- **交互式文档**：可直接在页面测试接口
- **参数说明**：详细的参数类型和约束
- **示例数据**：请求和响应的示例
- **错误说明**：可能的错误情况和处理方式

### 生成文档
```bash
# 安装swag工具
go install github.com/swaggo/swag/cmd/swag@latest

# 生成文档
swag init -g main.go -o docs

# 更新文档
make swagger
```

## 🔧 调试工具

### Postman集合
项目提供完整的Postman集合文件，包含所有接口的示例请求。

```bash
# 导入Postman集合
# 文件位置：docs/postman/KTV-ERP-API.postman_collection.json
```

### cURL示例
```bash
# 登录获取Token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123","venue_id":"venue_001"}'

# 使用Token访问接口
curl -X POST http://localhost:8080/api/order/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <TOKEN>" \
  -d '{"venue_id":"venue_001","room_id":"room_001"}'
```

## 📈 性能指标

### 响应时间要求
- **查询接口**：< 100ms
- **创建接口**：< 200ms
- **复杂计算接口**：< 500ms
- **批量操作接口**：< 1000ms

### 并发能力
- **单接口QPS**：1000+
- **系统总QPS**：5000+
- **并发用户数**：1000+

---

*文档维护者：前后端开发团队*
*最后更新：2025-01-06*
