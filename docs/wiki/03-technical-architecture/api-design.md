# API设计

## 📖 概述

本文档详细介绍KTV ERP系统的API设计规范，包括接口设计原则、URL规范、请求响应格式、错误处理等内容。

## 🎯 设计原则

### 统一POST接口设计
基于业务特点和技术考虑，本系统所有API接口统一使用POST方法：

**设计原因**：
- **业务复杂性**：KTV业务场景复杂，大部分操作都涉及数据修改和复杂参数传递
- **参数安全性**：敏感业务数据通过请求体传输，避免URL暴露
- **客户端简化**：统一的调用方式，简化前端和移动端的HTTP客户端实现
- **网关友好**：便于API网关的统一处理和监控
- **缓存控制**：POST请求不会被浏览器和代理服务器缓存，确保数据实时性

**技术实现**：
```go
// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
route.POST("/api/employee/add", employeeController.AddEmployee)       // 添加
route.POST("/api/employee/update", employeeController.UpdateEmployee) // 更新  
route.POST("/api/employee/delete", employeeController.DeleteEmployee) // 删除
route.POST("/api/employee/query", employeeController.QueryEmployees)  // 查询
route.POST("/api/employee/list", employeeController.ListEmployees)    // 列表
```

### 一致性原则
- **统一的命名规范**：所有接口遵循相同的命名约定
- **一致的数据格式**：请求和响应使用统一的JSON格式
- **标准化的错误码**：统一的错误码体系和错误信息格式
- **规范的文档格式**：使用Swagger注解自动生成API文档

## 🌐 URL设计规范

### 基本格式
```
POST /api/{module}/{action}
POST /api/v{version}/{module}/{action}
```

### 命名规范
- **模块名称**：使用小写字母，多个单词用连字符分隔
- **操作名称**：使用动词，表示具体的业务操作
- **版本控制**：使用v1、v2、v3等版本号

### 示例
```bash
# 员工管理
POST /api/employee/add          # 添加员工
POST /api/employee/update       # 更新员工
POST /api/employee/delete       # 删除员工
POST /api/employee/query        # 查询员工
POST /api/employee/list         # 员工列表

# 订单管理（V3版本）
POST /api/v3/order/open         # 开台
POST /api/v3/order/additional-order  # 点单
POST /api/v3/order/pay          # 支付
POST /api/v3/order/refund       # 退款

# 会员管理
POST /api/member/add            # 添加会员
POST /api/member/recharge       # 会员充值
POST /api/member/query          # 查询会员
```

## 📝 请求格式

### 请求头
```http
Content-Type: application/json
Authorization: Bearer <JWT_TOKEN>
X-Request-ID: <UNIQUE_REQUEST_ID>
X-Client-Version: <CLIENT_VERSION>
```

### 请求体格式
```json
{
  "venueId": "venue_001",
  "name": "张三",
  "phone": "13800138000",
  "type": "服务员",
  "permissions": ["order_manage", "member_query"]
}
```

### 分页请求
```json
{
  "venueId": "venue_001",
  "pageNum": 1,
  "pageSize": 20,
  "orderBy": "createTime",
  "orderDirection": "desc"
}
```

## 📤 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "emp_001",
    "name": "张三",
    "phone": "13800138000",
    "createTime": 1704528000
  },
  "requestID": "req_123456789",
  "serverTime": 1704528000
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 20,
    "total": 100,
    "data": [
      {
        "id": "emp_001",
        "name": "张三"
      }
    ]
  },
  "requestID": "req_123456789",
  "serverTime": 1704528000
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "requestID": "req_123456789",
  "serverTime": 1704528000,
  "traceId": "trace_123456789"
}
```

## 🔐 认证授权

### JWT Token认证
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token结构
```json
{
  "userId": "user_001",
  "venueId": "venue_001",
  "employeeId": "emp_001",
  "permissions": ["order_manage", "member_query"],
  "exp": 1704614400
}
```

#### 3. 使用示例

**Controller层标准用法**：
```go
// 员工管理Controller示例
func (controller *EmployeeController) AddEmployee(ctx *gin.Context) {
    // 1. 参数绑定和校验
    reqDto := req.AddEmployeeReqDto{}
    if err := ctx.ShouldBindJSON(&reqDto); err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    // 2. 参数业务校验
    if reqDto.Phone == nil || *reqDto.Phone == "" {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, "手机号不能为空")
        return
    }

    // 3. 调用应用服务
    employee, err := employeeAppService.AddEmployee(ctx, &reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    // 4. 成功响应
    Result_success[any](ctx, employee)
}

// 订单管理Controller示例（erp_client）
func (c *Controller) V3Pay(ctx *gin.Context) {
    var reqDto req.V3QueryOrderPayReqDto
    if err := ctx.ShouldBindJSON(&reqDto); err != nil {
        controller.HttpResult_fail[any](ctx, controller.GeneralCodes.ParamError.Code, err.Error())
        return
    }

    orderPayResultVO, err := c.orderApplicationService.V3OrderPay(ctx, reqDto)
    if err != nil {
        controller.HttpResult_fail[any](ctx, controller.GeneralCodes.InternalError.Code, err.Error())
        return
    }

    controller.HttpResult_success(ctx, orderPayResultVO)
}
```

**带附加信息的响应示例**：
```go
// 登录失败时返回剩余尝试次数
func buildErrorResult(ctx *gin.Context, err error, code int) {
    var authErr *impl.AuthError
    if errors.As(err, &authErr) {
        attachments := map[string]string{
            "remainingAttempts": strconv.Itoa(authErr.RemainingAttempts),
            "lockedUntil":       strconv.FormatInt(authErr.LockedUntil, 10),
        }
        Result_fail[any](ctx, code, ERR_MSG[code], attachments)
    } else {
        Result_fail[any](ctx, code, ERR_MSG[code])
    }
}
```

## 📊 状态码定义

### 标准错误码（GeneralCodes）
```go
var GeneralCodes = struct {
    OK                  ErrorCode  // 0 - 成功
    InternalError       ErrorCode  // 500 - 内部错误
    ServerIsBuzy        ErrorCode  // 50013 - 服务器繁忙
    ParamError          ErrorCode  // 400 - 参数错误
    NotAuthorized       ErrorCode  // 401 - 未授权
    NotLogin            ErrorCode  // 402 - 未登录
    Forbidden           ErrorCode  // 403 - 权限不足
    NotFound            ErrorCode  // 404 - 资源不存在
    TokenExpired        ErrorCode  // 407 - Token过期
    Throttled           ErrorCode  // 418 - 请求限流
    OrderAmountNotMatch ErrorCode  // 4001 - 订单金额不匹配
    ShowDialogError     ErrorCode  // 6000 - 显示对话框错误
    AccountLocked       ErrorCode  // 4002 - 账号已锁定
    AccountNotExist     ErrorCode  // 4003 - 账号不存在
    AccountPending      ErrorCode  // 4004 - 账号待审核
    InvalidCredentials  ErrorCode  // 4005 - 用户名或密码错误
    CashierNotBound     ErrorCode  // 4100 - 收银台未绑定
    VenueNotAudited     ErrorCode  // 4101 - 门店未审核
}{
    OK:                  ErrorCode{Code: 0},
    InternalError:       ErrorCode{Code: 500},
    ServerIsBuzy:        ErrorCode{Code: 50013},
    ParamError:          ErrorCode{Code: 400},
    // ... 其他错误码定义
}
```

### 使用规范
- **参数错误**：使用 `GeneralCodes.ParamError.Code`
- **业务错误**：使用 `GeneralCodes.ServerIsBuzy.Code`
- **权限错误**：使用 `GeneralCodes.Forbidden.Code`
- **认证错误**：使用 `GeneralCodes.NotAuthorized.Code`

## 🔧 技术实现

### Controller层实现
```go
// @Summary 添加员工
// @Description 添加员工
// @Tags 员工
// @Accept json
// @Produce json
// @Param body body req.AddEmployeeReqDto true "请求体"
// @Success 200 {object} Result[vo.EmployeeVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/employee/add [post]
func (controller *EmployeeController) AddEmployee(ctx *gin.Context) {
    reqDto := req.AddEmployeeReqDto{}
    if err := ctx.ShouldBindJSON(&reqDto); err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    // 参数校验
    if reqDto.Phone == nil || *reqDto.Phone == "" {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, "手机号不能为空")
        return
    }

    // 调用应用服务
    employee, err := employeeAppService.AddEmployee(ctx, &reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    Result_success[any](ctx, employee)
}
```

### 统一响应封装

项目中使用统一的返回值封装方法，确保所有API接口的响应格式一致。

#### 1. 响应数据结构

**erp_managent 使用 Result[T any]**：
```go
type Result[T any] struct {
    Code        int               `json:"code"`        // 状态码
    Message     string            `json:"message"`     // 响应消息
    Data        T                 `json:"data,omitempty"` // 业务数据
    TraceId     string            `json:"traceId,omitempty"` // 链路追踪ID
    Attachments map[string]string `json:"attachments,omitempty"` // 附加信息
    RequestID   *string           `json:"requestID"`   // 请求ID
    ServerTime  *int              `json:"serverTime"`  // 服务器时间戳
}
```

**erp_client 使用 HttpResult[T any]**：
```go
type HttpResult[T any] struct {
    Code        int               `json:"code"`        // 状态码
    Message     string            `json:"message"`     // 响应消息
    Data        T                 `json:"data,omitempty"` // 业务数据
    TraceId     string            `json:"traceId,omitempty"` // 链路追踪ID
    Attachments map[string]string `json:"attachments,omitempty"` // 附加信息
    RequestID   *string           `json:"requestID"`   // 请求ID
    ServerTime  *int              `json:"serverTime"`  // 服务器时间戳
}
```

#### 2. 封装方法

**成功响应封装**：
```go
// erp_managent
func Result_success[T any](ctx *gin.Context, t T) {
    requestIDAny, exists := ctx.Get("X-Request-Id")
    if !exists {
        requestIDAny = ""
    }
    requestID := requestIDAny.(string)

    ctx.JSON(http.StatusOK, Result[T]{
        Code:       GeneralCodes.OK.Code,  // 0
        Message:    "success",
        Data:       t,
        RequestID:  &requestID,
        ServerTime: util.GetItPtr(util.TimeNowUnix()),
    })
}

// erp_client
func HttpResult_success[T any](ctx *gin.Context, t T) {
    // 实现逻辑相同，结构体名称不同
}
```

**失败响应封装**：
```go
// erp_managent
func Result_fail[T any](ctx *gin.Context, errcode int, errmsg string, attachments ...map[string]string) {
    requestIDAny, exists := ctx.Get("X-Request-Id")
    if !exists {
        requestIDAny = ""
    }
    requestID := requestIDAny.(string)

    res := Result[T]{
        Code:       errcode,
        Message:    errmsg,
        RequestID:  &requestID,
        ServerTime: util.GetItPtr(util.TimeNowUnix()),
    }

    if len(attachments) > 0 {
        res.Attachments = attachments[0]
    }

    ctx.JSON(http.StatusOK, res)
}

// erp_client
func HttpResult_fail[T any](ctx *gin.Context, errcode int, errmsg string, attachments ...map[string]string) {
    // 实现逻辑相同，结构体名称不同
}
```

**带附加信息的成功响应**：
```go
func Result_success_attachments[T any](ctx *gin.Context, t T, attachments map[string]string) {
    // 支持返回附加信息，如分页信息、统计信息等
}
```

## 📋 接口分类

### 核心业务接口
- **订单管理**：开台、点单、结账、退款
- **会员管理**：注册、充值、查询、消费
- **支付管理**：支付、退款、对账

### 支撑业务接口
- **房间管理**：房间状态、价格方案、预订
- **商品管理**：商品信息、库存、套餐
- **员工管理**：员工档案、权限、审核

### 系统管理接口
- **用户管理**：登录、注册、权限
- **配置管理**：系统配置、参数设置
- **监控接口**：健康检查、性能监控

## 🔍 接口监控

### 请求追踪
- **Request ID**：每个请求分配唯一ID
- **Trace ID**：分布式追踪标识
- **用户标识**：记录操作用户信息

### 性能监控
- **响应时间**：接口响应时间统计
- **成功率**：接口成功率监控
- **错误统计**：错误类型和频率分析

## 📖 文档生成

### Swagger注解
使用Swagger注解自动生成API文档：
```go
// @Summary 接口摘要
// @Description 接口详细描述
// @Tags 标签分组
// @Accept json
// @Produce json
// @Param body body RequestDTO true "请求参数"
// @Success 200 {object} ResponseVO "成功响应"
// @Failure 400 {object} ErrorResponse "错误响应"
// @Router /api/path [post]
```

### 文档访问
- **开发环境**：http://localhost:8080/swagger/index.html
- **测试环境**：https://test-api.ktv-erp.com/swagger/index.html

---

*文档维护者：架构师、后端开发团队*
*最后更新：2025-01-06*
