# 支撑子域功能详解

## 📖 概述

本文档详细介绍KTV ERP系统三个支撑子域的具体功能实现，基于代码分析总结各子域的核心能力、技术实现和业务价值。

## 🎯 支撑子域概览

### 支撑子域定义
支撑子域为核心业务提供重要支撑，具有一定的业务复杂度，但不是核心竞争力。KTV ERP系统的支撑子域包括：
1. **房间管理子域** - 资源管理和价格策略
2. **商品管理子域** - 商品信息和库存控制
3. **员工管理子域** - 人力资源和权限控制

这三个子域支撑核心业务的正常运行，需要适度投资以满足业务需求。

## 🏠 房间管理子域

### 业务价值
- **资源优化**：提升房间利用率，最大化营收
- **价格策略**：灵活的定价策略支撑营收优化
- **客户体验**：房间状态和预订管理提升服务质量

### 核心功能架构
```mermaid
graph TB
    subgraph "房间管理子域"
        RoomMgmt[房间管理]
        PriceMgmt[价格管理]
        BookingMgmt[预订管理]
        StatusMgmt[状态管理]
        
        RoomMgmt --> StatusMgmt
        RoomMgmt --> PriceMgmt
        BookingMgmt --> StatusMgmt
        PriceMgmt --> BookingMgmt
    end
```

### 详细功能实现

#### 1. 房间信息管理
**房间基础信息**
- **房间属性**：名称、房型、区域、主题、容量
- **设备配置**：点歌机、音响、灯光、空调等设备
- **服务配置**：高消费警报、特殊服务标记

**技术实现**：
```go
// 房间聚合根（erp_managent）
type Room struct {
    id         string    // 房间ID
    venueID    string    // 所属门店ID
    name       string    // 房间名称
    typeID     string    // 房间类型ID
    capacity   int       // 容纳人数
    status     int       // 房间状态
    createTime time.Time // 创建时间
    updateTime time.Time // 更新时间
}

// 状态转换业务方法
func (r *Room) ChangeStatus(newStatus int) error {
    if !r.isValidStatusTransition(r.status, newStatus) {
        return errors.New("无效的状态转换")
    }
    r.status = newStatus
    r.updateTime = time.Now()
    return nil
}
```

#### 2. 价格方案管理
**买断价格方案**
- **固定时长计费**：设定固定时长和价格
- **超时处理**：超出时长的计费规则
- **时段差异**：不同时段的价格差异

**计时价格方案**
- **按时计费**：按实际使用时间计费
- **最小计费单位**：最小计费时间单位
- **阶梯价格**：不同时长的阶梯价格

**技术实现**：
```go
// 买断价格方案聚合根
type BuyoutPricePlan struct {
    id                     string
    venueID                string
    name                   string
    duration               int  // 买断时长（分钟）
    advanceDisableDuration int  // 提前禁用时长
    isExcessIncluded       bool // 是否包含超时费用
    priceConfigList        common.PriceConfigList
}

// 价格计算核心方法
func (p *BuyoutPricePlan) CalculatePrice(req *PriceCalculationRequest) *PriceResult {
    baseAmount := p.getBaseAmount()
    
    // 应用买断特有的计算逻辑
    if p.isExcessIncluded {
        baseAmount = p.calculateWithExcess(baseAmount, req.Duration)
    }
    
    // 应用价格配置（会员价、节假日价等）
    for _, config := range p.priceConfigList {
        if config.IsApplicable(req) {
            baseAmount = config.ApplyPrice(baseAmount)
        }
    }
    
    return &PriceResult{
        BaseAmount:  p.getBaseAmount(),
        TotalAmount: baseAmount,
    }
}
```

#### 3. 房间状态管理
**状态类型**
- **空闲（IDLE）**：房间可用，等待客户
- **使用中（IN_USE）**：客户正在使用
- **清洁中（CLEANING）**：房间清洁维护
- **故障（FAULT）**：设备故障，暂停使用

**状态流转规则**
```go
// 房间状态规则引擎配置
rules:
  - id: "room_status_idle"
    condition: "input.room.status == 'IDLE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "空闲"
          color: "green"
          
  - id: "room_status_in_use"
    condition: "input.room.status == 'IN_USE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "使用中"
          color: "red"
```

#### 4. 预订管理
**预订流程**
- **预订创建**：客户预订指定时间和房型
- **预订确认**：门店确认预订，锁定房间
- **预订取消**：取消预订，释放房间资源

**技术实现**：
```go
// 预订服务接口
type BookingService interface {
    CreateBooking(ctx context.Context, req *CreateBookingRequest) (*Booking, error)
    ConfirmBooking(ctx context.Context, bookingId string) error
    CancelBooking(ctx context.Context, bookingId string) error
    ValidateBookingTime(ctx context.Context, arrivalTime int64) error
}

// 预订时间验证
func (s *BookingServiceImpl) ValidateBookingTime(ctx context.Context, arrivalTime int64) error {
    now := time.Now().Unix()
    if arrivalTime < now {
        return bookingerror.NewInvalidBookingTimeError("预订时间不能早于当前时间")
    }
    
    // 检查预订时间是否在营业时间内
    if !s.isWithinBusinessHours(arrivalTime) {
        return bookingerror.NewInvalidBookingTimeError("预订时间不在营业时间内")
    }
    
    return nil
}
```

### 技术特色
- **状态机管理**：严格的房间状态流转控制
- **规则引擎集成**：房间状态判断通过规则引擎实现
- **价格策略灵活**：支持多种价格方案和计算规则
- **预订系统完整**：完整的预订生命周期管理

## 📦 商品管理子域

### 业务价值
- **服务质量**：完善的商品管理提升服务水平
- **库存控制**：精确的库存管理降低成本
- **营销支撑**：套餐和促销活动支撑营销策略

### 核心功能架构
```mermaid
graph TB
    subgraph "商品管理子域"
        ProductInfo[商品信息]
        InventoryMgmt[库存管理]
        StorageMgmt[存酒管理]
        PackageMgmt[套餐管理]
        
        ProductInfo --> InventoryMgmt
        ProductInfo --> StorageMgmt
        ProductInfo --> PackageMgmt
        InventoryMgmt --> StorageMgmt
    end
```

### 详细功能实现

#### 1. 商品信息管理
**商品基础信息**
- **基础属性**：名称、类型、价格、条码、规格
- **分类管理**：商品类型、展示分类、销售分类
- **价格策略**：基础价格、区域价格、时段价格

**技术实现**：
```go
// 商品应用服务（erp_client）
func (s *ProductApplicationServiceImpl) AddProduct(ctx context.Context, reqDto req.AddProductReqDto) (vo.ProductVO, error) {
    // 1. 验证基础参数
    venue, err := s.validateBaseParams(ctx, reqDto.VenueId)
    if err != nil {
        return vo.ProductVO{}, err
    }

    // 2. 创建商品实体
    product := &po.Product{
        VenueId:             venue.Id,
        Name:                reqDto.Name,
        Type:                reqDto.Type,
        CurrentPrice:        reqDto.CurrentPrice,
        Price:               reqDto.Price,
        Barcode:             reqDto.Barcode,
        AreaPrices:          reqDto.AreaPrices,          // 区域价格
        TimeSlotPrices:      reqDto.TimeSlotPrices,      // 时段价格
        MinimumSaleQuantity: reqDto.MinimumSaleQuantity, // 最小销售数量
    }
    
    // 3. 保存商品信息
    return s.productService.Create(ctx, product)
}
```

#### 2. 存酒管理（KTV特色业务）
**存酒业务流程**
- **存酒登记**：客户购买酒水后选择存酒
- **存酒续期**：延长存酒有效期
- **取酒消费**：客户消费存酒
- **存酒报废**：过期或损坏的存酒处理

**技术实现**：
```go
// 存酒操作服务（erp_managent）
func (service *ProductStorageOperationService) OperateProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
    switch reqDto.OperationType {
    case _const.OperationTypeExtend: // 续存
        return service.ExtendProductStorage(ctx, storage, reqDto)
    case _const.OperationTypeCancel: // 撤销存酒
        return service.CancelProductStorage(ctx, storage, reqDto)
    case _const.OperationTypeDiscard: // 报废
        return service.DiscardProductStorage(ctx, storage, reqDto)
    case "update": // 更新存酒记录
        return service.UpdateProductStorageInfo(ctx, storage, reqDto)
    default:
        return fmt.Errorf("不支持的操作类型: %s", reqDto.OperationType)
    }
}

// 存酒续期处理
func (service *ProductStorageOperationService) ExtendProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
    // 1. 验证续期参数
    if reqDto.ExtendDays == nil || *reqDto.ExtendDays <= 0 {
        return fmt.Errorf("续期天数必须大于0")
    }
    
    // 2. 计算新的到期时间
    newExpiryDate := storage.ExpiryDate.AddDate(0, 0, *reqDto.ExtendDays)
    
    // 3. 更新存酒记录
    storage.ExpiryDate = newExpiryDate
    storage.UpdatedAt = time.Now()
    
    return service.productStorageService.UpdateProductStorage(ctx, storage)
}
```

#### 3. 套餐管理
**赠品组管理**
- **赠品组配置**：定义赠品组合和显示规则
- **买赠方案**：购买指定商品赠送其他商品
- **组合套餐**：多商品组合销售，统一定价

**技术实现**：
```go
// 赠品组管理（erp_managent）
func (controller *GiftGroupController) AddGiftGroup(ctx *gin.Context) {
    var reqDto req.AddGiftGroupReqDto
    if err := ctx.ShouldBindJSON(&reqDto); err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }

    // 创建赠品组
    giftGroup := po.GiftGroup{
        Name:        reqDto.Name,
        Products:    reqDto.Products,    // 包含的商品列表
        IsDisplayed: reqDto.IsDisplayed, // 是否在前端显示
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }
    
    err := giftGroupService.CreateGiftGroup(ctx, &giftGroup)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }
    
    Result_success(ctx, giftGroup)
}
```

#### 4. 商品查询服务
**V3QueryProducts（erp_client）**
- **流程引擎集成**：商品查询通过流程引擎编排
- **多维度筛选**：支持分类、价格、库存等筛选
- **分页排序**：支持分页查询和多种排序方式

**技术实现**：
```go
// 商品查询服务（erp_client）
func (s *ProductApplicationServiceImpl) V3QueryProducts(ctx context.Context, reqDto req.V3QueryProductReqDto) ([]vo.ProductVO, error) {
    // 通过流程引擎执行商品查询流程
    params := map[string]interface{}{
        "reqDto":    reqDto,
        "venueInfo": s.getVenueInfo(ctx, reqDto.VenueId),
        "userInfo":  s.getUserInfo(ctx),
    }
    
    result, err := s.processEngine.Execute(ctx, "product_query_process", params)
    if err != nil {
        return nil, fmt.Errorf("商品查询流程执行失败: %w", err)
    }
    
    return s.buildProductVOs(result), nil
}
```

### 技术特色
- **流程引擎集成**：商品查询通过流程引擎编排
- **存酒业务特色**：KTV行业特有的客户存酒业务
- **多维度管理**：商品信息、库存、价格、分类的统一管理
- **灵活配置**：支持区域价格、时段价格等复杂定价策略

## 👥 员工管理子域

### 业务价值
- **人效提升**：优化人力资源配置和管理效率
- **权限控制**：保证系统安全和操作规范性
- **服务质量**：员工管理直接影响服务质量

### 核心功能架构
```mermaid
graph TB
    subgraph "员工管理子域"
        EmployeeLifecycle[员工生命周期]
        PermissionMgmt[权限管理]
        ProfileMgmt[档案管理]
        RelationMgmt[关联关系]
        
        EmployeeLifecycle --> PermissionMgmt
        EmployeeLifecycle --> ProfileMgmt
        ProfileMgmt --> RelationMgmt
        PermissionMgmt --> RelationMgmt
    end
```

### 详细功能实现

#### 1. 员工生命周期管理
**员工邀请**
- **邀请码生成**：生成唯一的员工邀请二维码
- **分组信息**：获取员工分组和权限模板
- **有效期控制**：邀请码的有效期管理

**员工注册**
- **邀请验证**：验证邀请码的有效性
- **信息录入**：员工基础信息录入
- **关联创建**：创建ERP用户与员工的关联关系

**技术实现**：
```go
// 员工应用服务（erp_managent）
func (s *EmployeeAppServiceImpl) AddEmployee(ctx *gin.Context, reqDto *req.AddEmployeeReqDto) (*vo.EmployeeVO, error) {
    // 1. 验证用户身份
    currentUserId := ctx.GetString("userId")
    if currentUserId == "" {
        return nil, fmt.Errorf("请先登录")
    }

    // 2. 事务处理员工创建
    var employee *po.Employee
    err := model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
        // 检查是否已存在关联关系
        existRelations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
            ERPUserId: &currentUserId,
            VenueId:   reqDto.VenueId,
        }, tx)
        if err != nil {
            return fmt.Errorf("查询关联关系失败: %v", err)
        }
        if len(*existRelations) > 0 {
            return fmt.Errorf("您已在该门店存在员工账号")
        }

        // 创建员工和关联关系
        employee, err = s.createEmployeeWithTx(ctx, reqDto, currentUserId, tx)
        return err
    })

    if err != nil {
        return nil, err
    }

    // 3. 设置审核状态
    employee.ReviewStatus = util.GetItPtr(0) // 0-待审核
    return s.employeeTransfer.POToVO(employee), nil
}
```

#### 2. 权限管理
**权限角色定义**
- **角色模板**：预定义的权限角色模板
- **权限列表**：详细的权限项配置
- **商品类型权限**：不同角色可操作的商品类型

**权限验证**
- **操作权限验证**：在业务操作前验证员工权限
- **数据权限控制**：控制员工可访问的数据范围
- **动态权限调整**：支持动态调整员工权限

**技术实现**：
```go
// 权限角色实体（erp_managent）
type PermissionRole struct {
    Name                *string `json:"name"`                // 角色名称
    EmployeeType        *string `json:"employeeType"`        // 员工类型
    Permissions         *string `json:"permissions"`         // 权限列表（JSON）
    AllowedProductTypes *string `json:"allowedProductTypes"` // 允许的商品类型
}

// 员工权限验证
func (s *EmployeeAppServiceImpl) ReviewEmployee(ctx *gin.Context, reqDto *req.ReviewEmployeeReqDto) error {
    // 1. 获取操作员信息
    operatorEmployee, err := s.getOperatorEmployee(ctx, reqDto.VenueId)
    if err != nil {
        return fmt.Errorf("获取操作员信息失败: %v", err)
    }
    
    // 2. 权限校验：只有老板可以审核员工
    if operatorEmployee.IsBoss == nil || !*operatorEmployee.IsBoss {
        return fmt.Errorf("您没有权限审核员工")
    }
    
    // 3. 执行审核操作
    return s.processEmployeeReview(ctx, reqDto)
}
```

#### 3. 员工档案管理
**基础信息管理**
- **个人信息**：姓名、手机号、性别、员工编号
- **岗位信息**：员工类型、员工组、权限角色
- **业务信息**：销售业绩、佣金权限、管理权限

**技术实现**：
```go
// 员工实体（erp_managent）
type Employee struct {
    Id                           *string  `json:"id"`                           // 员工ID
    VenueId                      *string  `json:"venueId"`                      // 门店ID
    Name                         *string  `json:"name"`                         // 员工姓名
    EmployeeNumber               *string  `json:"employeeNumber"`               // 员工编号
    Phone                        *string  `json:"phone"`                        // 电话号码
    Gender                       *string  `json:"gender"`                       // 性别
    Type                         *string  `json:"type"`                         // 员工类型
    Permissions                  *string  `json:"permissions"`                  // 权限列表
    EmployeeGroup                *string  `json:"employeeGroup"`                // 员工组
    PermissionRole               *string  `json:"permissionRole"`               // 权限角色
    SalesPerformance             *float32 `json:"salesPerformance"`             // 销售业绩
    CanViewCommissionPerformance *bool    `json:"canViewCommissionPerformance"` // 是否可以查看佣金业绩
    ReviewStatus                 *int     `json:"reviewStatus"`                 // 审核状态
    IsBoss                       *bool    `json:"isBoss"`                       // 是否是门店老板
}
```

#### 4. 关联关系管理
**ERP用户关联**
- **一对多关系**：一个ERP用户可以在多个门店担任员工
- **关联创建**：员工注册时创建用户关联
- **关联维护**：员工删除时清理关联关系

**技术实现**：
```go
// ERP用户和员工关联实体
type ERPUserAndEmployee struct {
    ERPUserId  *string `json:"erpUserId"`  // ERP用户ID
    EmployeeId *string `json:"employeeId"` // 员工ID
    VenueId    *string `json:"venueId"`    // 门店ID
}

// 删除员工时清理关联关系
func (s *EmployeeAppServiceImpl) DeleteEmployee(ctx *gin.Context, venueId string, employeeId string) error {
    return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
        // 1. 查询并删除指定门店的关联关系
        relations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
            EmployeeId: &employeeId,
            VenueId:    &venueId,
        }, tx)
        
        if err != nil {
            return fmt.Errorf("查询关联关系失败: %v", err)
        }
        
        for _, relation := range *relations {
            if err := erpUserAndEmployeeService.DeleteERPUserAndEmployeeWithTx(ctx, *relation.Id, tx); err != nil {
                return fmt.Errorf("删除关联失败: %v", err)
            }
        }
        
        // 2. 删除员工主体
        return employeeService.DeleteEmployeeWithTx(ctx, employeeId, tx)
    })
}
```

### 技术特色
- **完整生命周期**：从邀请到删除的完整员工管理流程
- **权限控制精细**：基于角色的权限管理和验证机制
- **多门店支持**：支持员工在多个门店工作的复杂场景
- **事务安全**：关键操作采用数据库事务保证数据一致性

## 📊 支撑子域指标

### 房间管理指标
- 房间利用率：> 75%
- 价格计算准确率：> 99.9%
- 预订确认率：> 95%
- 状态流转响应时间：< 1秒

### 商品管理指标
- 库存准确率：> 98%
- 商品查询响应时间：< 1秒
- 存酒业务处理时间：< 2分钟
- 套餐配置成功率：> 99%

### 员工管理指标
- 员工注册成功率：> 98%
- 权限验证响应时间：< 500ms
- 员工审核处理时间：< 24小时
- 关联关系一致性：> 99.9%

---

*文档维护者：技术架构师、业务分析师*
*最后更新：2025-01-06*
