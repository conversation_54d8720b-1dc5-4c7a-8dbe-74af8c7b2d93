# 流程引擎

## 📖 概述

erp_client新架构的流程引擎是业务流程编排的核心组件，采用YAML配置驱动的方式定义业务流程，与规则引擎深度集成，实现复杂业务逻辑的流程化管理。流程引擎负责将业务操作组织成有序的步骤序列，在关键节点集成规则引擎进行业务决策。

## 🎯 设计理念

### 核心原则
- **流程驱动**：业务逻辑以流程的形式组织和执行
- **步骤化执行**：复杂业务分解为可管理的步骤
- **规则集成**：在流程节点集成规则引擎进行决策
- **条件分支**：支持基于条件的流程分支
- **服务调用**：支持调用领域服务和外部服务

### 架构特点
- **声明式定义**：使用YAML声明式定义流程
- **可视化流程**：流程定义清晰，易于理解和维护
- **动态执行**：支持运行时动态加载和执行流程
- **错误处理**：完善的错误处理和回滚机制
- **监控追踪**：流程执行状态监控和追踪

## 🏗️ 架构设计

### 流程引擎架构图
```mermaid
graph TB
    subgraph "流程定义层"
        YAML[YAML流程配置]
        ProcessDef[流程定义]
        StepDef[步骤定义]
    end
    
    subgraph "流程引擎核心"
        Engine[ProcessEngine流程引擎]
        Context[ProcessContext流程上下文]
        Executor[StepExecutor步骤执行器]
    end
    
    subgraph "动作执行器"
        ServiceAction[Service动作]
        RuleAction[Rule动作]
        SetFieldAction[SetField动作]
        TransformAction[Transform动作]
    end
    
    subgraph "集成组件"
        RuleEngine[规则引擎]
        DomainService[领域服务]
        ExternalService[外部服务]
    end
    
    YAML --> Engine
    ProcessDef --> Engine
    StepDef --> Engine
    
    Engine --> Context
    Engine --> Executor
    
    Executor --> ServiceAction
    Executor --> RuleAction
    Executor --> SetFieldAction
    Executor --> TransformAction
    
    ServiceAction --> DomainService
    RuleAction --> RuleEngine
    ServiceAction --> ExternalService
```

### 核心组件

#### 1. ProcessEngine (流程引擎)
```go
type ProcessEngine interface {
    // 加载流程定义
    LoadProcess(content []byte) error
    
    // 执行流程
    Execute(ctx context.Context, processID string, params map[string]interface{}) (map[string]interface{}, error)
    
    // 注册服务
    RegisterService(name string, service interface{})
}
```

#### 2. ProcessContext (流程上下文)
```go
type ProcessContext interface {
    // 设置变量
    SetVariable(name string, value interface{})
    
    // 获取变量
    GetVariable(name string) (interface{}, error)
    
    // 设置输入参数
    SetInput(params map[string]interface{})
    
    // 获取输出结果
    GetOutput() map[string]interface{}
}
```

## 📋 流程定义格式

### YAML流程配置结构
```yaml
# 流程定义
process_id: "order_create_process"
name: "订单创建流程"
description: "处理订单创建的完整业务流程"
version: "1.0.0"

# 输入参数定义
input:
  - name: "order_request"
    type: "object"
    required: true
    description: "订单创建请求"
  - name: "user_info"
    type: "object"
    required: true
    description: "用户信息"

# 输出参数定义
output:
  - name: "order_id"
    type: "string"
    description: "创建的订单ID"
  - name: "total_amount"
    type: "decimal"
    description: "订单总金额"

# 流程步骤
steps:
  # 步骤1：验证用户信息
  - id: "validate_user"
    name: "验证用户信息"
    condition: "input.user_info != null"
    action:
      type: "service"
      service: "UserService"
      method: "ValidateUser"
      params:
        user_id: "input.user_info.user_id"
      result: "temp.user_valid"
  
  # 步骤2：计算订单价格
  - id: "calculate_price"
    name: "计算订单价格"
    condition: "temp.user_valid == true"
    action:
      type: "rule"
      rule_group: "order_pricing_rules"
      input:
        order_info: "input.order_request"
        member_info: "input.user_info.member_info"
      output: "temp.pricing_result"
  
  # 步骤3：创建订单
  - id: "create_order"
    name: "创建订单"
    action:
      type: "service"
      service: "OrderService"
      method: "CreateOrder"
      params:
        order_request: "input.order_request"
        total_amount: "temp.pricing_result.total_amount"
      result: "output.order_id"
  
  # 步骤4：设置输出
  - id: "set_output"
    name: "设置输出结果"
    action:
      type: "set_field"
      target: "output"
      fields:
        total_amount: "temp.pricing_result.total_amount"
        discount_amount: "temp.pricing_result.discount_amount"
```

### 流程执行流程图
```mermaid
flowchart TD
    Start([开始]) --> LoadProcess[加载流程定义]
    LoadProcess --> InitContext[初始化流程上下文]
    InitContext --> SetInput[设置输入参数]
    SetInput --> ExecuteSteps[执行流程步骤]
    
    ExecuteSteps --> CheckCondition{检查步骤条件}
    CheckCondition -->|条件满足| ExecuteAction[执行步骤动作]
    CheckCondition -->|条件不满足| NextStep[下一步骤]
    
    ExecuteAction --> ActionType{动作类型}
    ActionType -->|service| CallService[调用服务]
    ActionType -->|rule| CallRule[调用规则引擎]
    ActionType -->|set_field| SetField[设置字段]
    ActionType -->|transform| Transform[数据转换]
    
    CallService --> NextStep
    CallRule --> NextStep
    SetField --> NextStep
    Transform --> NextStep
    
    NextStep --> HasMoreSteps{还有步骤?}
    HasMoreSteps -->|是| ExecuteSteps
    HasMoreSteps -->|否| CollectOutput[收集输出结果]
    
    CollectOutput --> End([结束])
```

## ⚙️ 动作类型

### 1. Service动作 - 调用服务
```yaml
action:
  type: "service"
  service: "OrderService"          # 服务名称
  method: "CreateOrder"            # 方法名称
  params:                          # 参数映射
    order_request: "input.order_request"
    user_id: "input.user_info.user_id"
  result: "temp.order_result"      # 结果存储位置
```

### 2. Rule动作 - 调用规则引擎
```yaml
action:
  type: "rule"
  rule_group: "order_pricing_rules" # 规则组ID
  input:                           # 规则输入
    order_info: "input.order_request"
    member_info: "input.user_info.member_info"
  output: "temp.pricing_result"    # 规则输出存储位置
```

### 3. SetField动作 - 设置字段
```yaml
action:
  type: "set_field"
  target: "output"                 # 目标对象
  fields:                          # 字段映射
    order_id: "temp.order_result.order_id"
    status: "'created'"
    created_at: "now()"
```

### 4. Transform动作 - 数据转换
```yaml
action:
  type: "transform"
  source: "temp.order_result"      # 源数据
  target: "output.order_info"      # 目标位置
  mapping:                         # 字段映射
    id: "order_id"
    amount: "total_amount"
    status: "order_status"
```

### 5. Validate动作 - 数据验证
```yaml
action:
  type: "validate"
  data: "input.order_request"      # 待验证数据
  rules:                           # 验证规则
    - field: "amount"
      rule: "required|numeric|min:0"
    - field: "user_id"
      rule: "required|string"
  result: "temp.validation_result"
```

## 🔧 服务注册与调用

### 服务注册
```go
// 注册领域服务
engine.RegisterService("OrderService", orderService)
engine.RegisterService("UserService", userService)
engine.RegisterService("PaymentService", paymentService)

// 服务接口定义
type OrderService interface {
    CreateOrder(ctx context.Context, request *CreateOrderRequest) (*CreateOrderResponse, error)
    UpdateOrder(ctx context.Context, orderID string, updates map[string]interface{}) error
    GetOrder(ctx context.Context, orderID string) (*Order, error)
}
```

### 服务调用机制
```go
// 服务调用执行器
func (e *YAMLProcessEngine) executeServiceAction(ctx context.Context, action map[string]interface{}, processCtx model.ProcessContext) error {
    serviceName := action["service"].(string)
    methodName := action["method"].(string)
    
    // 获取注册的服务
    service, exists := e.services[serviceName]
    if !exists {
        return fmt.Errorf("服务未注册: %s", serviceName)
    }
    
    // 使用反射调用服务方法
    serviceValue := reflect.ValueOf(service)
    method := serviceValue.MethodByName(methodName)
    if !method.IsValid() {
        return fmt.Errorf("方法不存在: %s.%s", serviceName, methodName)
    }
    
    // 准备参数并调用
    args := e.prepareMethodArgs(action["params"], processCtx)
    results := method.Call(args)
    
    // 处理返回结果
    return e.handleMethodResults(results, action["result"], processCtx)
}
```

## 🔄 流程执行示例

### 订单创建流程执行
```go
// 1. 加载流程定义
processContent := loadYAMLFile("order_create_process.yaml")
engine.LoadProcess(processContent)

// 2. 准备输入参数
params := map[string]interface{}{
    "order_request": map[string]interface{}{
        "product_id": "prod_001",
        "quantity":   2,
        "amount":     100.00,
    },
    "user_info": map[string]interface{}{
        "user_id": "user_001",
        "member_info": map[string]interface{}{
            "level": "VIP",
            "points": 1000,
        },
    },
}

// 3. 执行流程
result, err := engine.Execute(ctx, "order_create_process", params)
if err != nil {
    log.Error("流程执行失败: %v", err)
    return
}

// 4. 获取执行结果
orderID := result["order_id"].(string)
totalAmount := result["total_amount"].(float64)
discountAmount := result["discount_amount"].(float64)

log.Info("订单创建成功: ID=%s, 总金额=%.2f, 优惠金额=%.2f", 
    orderID, totalAmount, discountAmount)
```

### 流程执行监控
```go
// 流程执行状态监控
type ProcessMonitor struct {
    processID   string
    startTime   time.Time
    currentStep string
    stepResults map[string]interface{}
}

func (m *ProcessMonitor) OnStepStart(stepID string) {
    log.Info("步骤开始执行: %s", stepID)
    m.currentStep = stepID
}

func (m *ProcessMonitor) OnStepComplete(stepID string, result interface{}) {
    log.Info("步骤执行完成: %s", stepID)
    m.stepResults[stepID] = result
}

func (m *ProcessMonitor) OnProcessComplete(result map[string]interface{}) {
    duration := time.Since(m.startTime)
    log.Info("流程执行完成: %s, 耗时: %v", m.processID, duration)
}
```

## 🚨 错误处理

### 错误处理策略
```yaml
# 流程级错误处理
error_handling:
  strategy: "rollback"              # 错误处理策略: rollback/continue/stop
  max_retries: 3                    # 最大重试次数
  retry_delay: "1s"                 # 重试延迟

# 步骤级错误处理
steps:
  - id: "risky_step"
    name: "可能失败的步骤"
    action:
      type: "service"
      service: "ExternalService"
      method: "CallAPI"
    error_handling:
      on_error: "continue"          # 错误时继续执行
      fallback:                     # 降级处理
        type: "set_field"
        target: "temp.api_result"
        fields:
          status: "'fallback'"
          message: "'使用默认值'"
```

### 事务管理
```go
// 流程事务管理
type ProcessTransaction struct {
    steps       []TransactionStep
    rollbackOps []RollbackOperation
}

func (t *ProcessTransaction) Execute(ctx context.Context) error {
    // 执行所有步骤
    for _, step := range t.steps {
        if err := step.Execute(ctx); err != nil {
            // 执行回滚操作
            t.Rollback(ctx)
            return err
        }
    }
    
    // 提交事务
    return t.Commit(ctx)
}
```

---

*文档维护者：架构师、流程引擎开发团队*
*最后更新：2025-01-06*
