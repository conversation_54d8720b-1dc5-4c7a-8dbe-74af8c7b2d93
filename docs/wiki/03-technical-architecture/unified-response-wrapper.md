# 统一响应封装

## 📖 概述

本文档详细介绍KTV ERP系统的统一响应封装机制，包括数据结构设计、封装方法、使用规范和最佳实践。

## 🎯 设计目标

### 一致性保证
- **响应格式统一**：所有API接口使用相同的响应结构
- **错误处理标准化**：统一的错误码和错误信息格式
- **元数据完整**：包含请求追踪、时间戳等元数据

### 开发效率
- **泛型支持**：使用Go泛型提供类型安全的响应封装
- **简化调用**：提供便捷的封装方法，减少重复代码
- **自动处理**：自动处理请求ID、时间戳等通用字段

## 🏗️ 数据结构设计

### 核心结构体

#### 1. Result[T any]（erp_managent）
```go
type Result[T any] struct {
    Code        int               `json:"code"`        // 状态码
    Message     string            `json:"message"`     // 响应消息
    Data        T                 `json:"data,omitempty"` // 业务数据（泛型）
    TraceId     string            `json:"traceId,omitempty"` // 链路追踪ID
    Attachments map[string]string `json:"attachments,omitempty"` // 附加信息
    RequestID   *string           `json:"requestID"`   // 请求ID
    ServerTime  *int              `json:"serverTime"`  // 服务器时间戳
}
```

#### 2. HttpResult[T any]（erp_client）
```go
type HttpResult[T any] struct {
    Code        int               `json:"code"`        // 状态码
    Message     string            `json:"message"`     // 响应消息
    Data        T                 `json:"data,omitempty"` // 业务数据（泛型）
    TraceId     string            `json:"traceId,omitempty"` // 链路追踪ID
    Attachments map[string]string `json:"attachments,omitempty"` // 附加信息
    RequestID   *string           `json:"requestID"`   // 请求ID
    ServerTime  *int              `json:"serverTime"`  // 服务器时间戳
}
```

### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| code | int | ✅ | 业务状态码，0表示成功 |
| message | string | ✅ | 响应消息，成功时为"success" |
| data | T | ❌ | 业务数据，使用泛型支持任意类型 |
| traceId | string | ❌ | 分布式链路追踪ID |
| attachments | map[string]string | ❌ | 附加信息，如分页、统计等 |
| requestID | *string | ✅ | 请求唯一标识 |
| serverTime | *int | ✅ | 服务器时间戳（Unix时间） |

## 🔧 封装方法

### 成功响应封装

#### 1. 基础成功响应
```go
// erp_managent
func Result_success[T any](ctx *gin.Context, t T) {
    requestIDAny, exists := ctx.Get("X-Request-Id")
    if !exists {
        requestIDAny = ""
    }
    requestID := requestIDAny.(string)

    ctx.JSON(http.StatusOK, Result[T]{
        Code:       GeneralCodes.OK.Code,  // 0
        Message:    "success",
        Data:       t,
        RequestID:  &requestID,
        ServerTime: util.GetItPtr(util.TimeNowUnix()),
    })
}

// erp_client
func HttpResult_success[T any](ctx *gin.Context, t T) {
    // 实现逻辑相同，使用HttpResult结构体
}
```

#### 2. 带附加信息的成功响应
```go
func Result_success_attachments[T any](ctx *gin.Context, t T, attachments map[string]string) {
    requestIDAny, exists := ctx.Get("X-Request-Id")
    if !exists {
        requestIDAny = ""
    }
    requestID := requestIDAny.(string)

    ctx.JSON(http.StatusOK, Result[T]{
        Code:        GeneralCodes.OK.Code,
        Message:     "success",
        Data:        t,
        RequestID:   &requestID,
        Attachments: attachments,
        ServerTime:  util.GetItPtr(util.TimeNowUnix()),
    })
}
```

### 失败响应封装

#### 1. 基础失败响应
```go
func Result_fail[T any](ctx *gin.Context, errcode int, errmsg string, attachments ...map[string]string) {
    requestIDAny, exists := ctx.Get("X-Request-Id")
    if !exists {
        requestIDAny = ""
    }
    requestID := requestIDAny.(string)

    res := Result[T]{
        Code:       errcode,
        Message:    errmsg,
        RequestID:  &requestID,
        ServerTime: util.GetItPtr(util.TimeNowUnix()),
    }

    // 可选的附加信息
    if len(attachments) > 0 {
        res.Attachments = attachments[0]
    }

    ctx.JSON(http.StatusOK, res)
}
```

## 📝 使用规范

### Controller层标准用法

#### 1. 参数校验错误
```go
func (controller *EmployeeController) AddEmployee(ctx *gin.Context) {
    reqDto := req.AddEmployeeReqDto{}
    if err := ctx.ShouldBindJSON(&reqDto); err != nil {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())
        return
    }
    
    // 业务参数校验
    if reqDto.Phone == nil || *reqDto.Phone == "" {
        Result_fail[any](ctx, GeneralCodes.ParamError.Code, "手机号不能为空")
        return
    }
}
```

#### 2. 业务处理成功
```go
func (controller *EmployeeController) AddEmployee(ctx *gin.Context) {
    // ... 参数校验

    employee, err := employeeAppService.AddEmployee(ctx, &reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    // 成功响应 - 返回具体类型
    Result_success[vo.EmployeeVO](ctx, employee)
    
    // 或者使用any类型
    Result_success[any](ctx, employee)
}
```

#### 3. 空数据响应
```go
func (controller *EmployeeController) DeleteEmployee(ctx *gin.Context) {
    // ... 删除逻辑

    // 删除成功，返回空数据
    Result_success[any](ctx, nil)
}
```

#### 4. 带附加信息的响应
```go
func (controller *EmployeeController) ListEmployees(ctx *gin.Context) {
    employees, total, err := employeeAppService.ListEmployees(ctx, &reqDto)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }

    // 分页信息作为附加信息
    attachments := map[string]string{
        "total":     strconv.FormatInt(total, 10),
        "pageNum":   strconv.Itoa(reqDto.PageNum),
        "pageSize":  strconv.Itoa(reqDto.PageSize),
    }

    Result_success_attachments[any](ctx, employees, attachments)
}
```

### 错误处理最佳实践

#### 1. 标准错误码使用
```go
// 参数错误
Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误信息")

// 业务错误
Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())

// 权限错误
Result_fail[any](ctx, GeneralCodes.Forbidden.Code, "权限不足")

// 认证错误
Result_fail[any](ctx, GeneralCodes.NotAuthorized.Code, "未授权访问")
```

#### 2. 特殊错误处理
```go
func buildErrorResult(ctx *gin.Context, err error, code int) {
    var authErr *impl.AuthError
    if errors.As(err, &authErr) {
        // 登录失败时返回剩余尝试次数
        attachments := map[string]string{
            "remainingAttempts": strconv.Itoa(authErr.RemainingAttempts),
            "lockedUntil":       strconv.FormatInt(authErr.LockedUntil, 10),
        }
        Result_fail[any](ctx, code, ERR_MSG[code], attachments)
    } else {
        Result_fail[any](ctx, code, ERR_MSG[code])
    }
}
```

## 📊 响应示例

### 成功响应示例
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "emp_001",
    "name": "张三",
    "phone": "13800138000",
    "createTime": 1704528000
  },
  "requestID": "req_123456789",
  "serverTime": 1704528000
}
```

### 失败响应示例
```json
{
  "code": 400,
  "message": "手机号不能为空",
  "requestID": "req_123456789",
  "serverTime": 1704528000
}
```

### 带附加信息的响应示例
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": "emp_001",
      "name": "张三"
    }
  ],
  "attachments": {
    "total": "100",
    "pageNum": "1",
    "pageSize": "20"
  },
  "requestID": "req_123456789",
  "serverTime": 1704528000
}
```

## 🔍 技术特点

### 1. 泛型支持
- **类型安全**：编译时类型检查，避免类型错误
- **代码复用**：同一套封装方法支持不同数据类型
- **IDE友好**：完整的类型提示和自动补全

### 2. 自动化处理
- **请求ID自动提取**：从Gin Context中自动获取请求ID
- **时间戳自动生成**：自动添加服务器时间戳
- **错误码标准化**：使用预定义的错误码常量

### 3. 扩展性
- **附加信息支持**：支持返回额外的元数据信息
- **链路追踪集成**：预留TraceId字段支持分布式追踪
- **中间件友好**：与Gin中间件无缝集成

## 🎯 最佳实践

### 1. 泛型类型选择
```go
// 推荐：明确指定返回类型
Result_success[vo.EmployeeVO](ctx, employee)

// 可接受：使用any类型（当类型复杂时）
Result_success[any](ctx, complexData)

// 避免：不必要的类型转换
```

### 2. 错误信息规范
```go
// 推荐：清晰的错误信息
Result_fail[any](ctx, GeneralCodes.ParamError.Code, "手机号格式不正确")

// 避免：模糊的错误信息
Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数错误")
```

### 3. 附加信息使用
```go
// 推荐：有意义的附加信息
attachments := map[string]string{
    "total":    strconv.FormatInt(total, 10),
    "hasMore":  strconv.FormatBool(hasMore),
}

// 避免：无意义的附加信息
```

---

*文档维护者：架构师、后端开发团队*
*最后更新：2025-01-06*
