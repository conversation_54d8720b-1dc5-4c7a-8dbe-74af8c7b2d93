# DDD战略设计

## 📖 概述

本文档基于领域驱动设计(DDD)的战略设计方法，对KTV ERP系统进行完整的领域分析，确定系统的领域边界、子域划分和界限上下文，为系统架构设计提供战略指导。

## 🎯 业务领域分析

### KTV行业核心业务
KTV ERP系统服务于KTV行业的数字化经营管理，核心业务围绕**顾客消费体验**和**门店运营管理**两大主线展开：

1. **顾客消费主线**：预订 → 开台 → 点单 → 服务 → 结账 → 离店
2. **运营管理主线**：门店管理 → 员工管理 → 商品管理 → 财务管理 → 数据分析

### 业务复杂度分析
- **高复杂度**：订单处理、价格计算、支付结算、会员管理
- **中复杂度**：房间管理、商品管理、员工管理
- **低复杂度**：基础配置、系统管理、报表统计

## 🏗️ 领域分析和子域划分

### 领域（Domain）识别
**KTV ERP系统的整体领域**：KTV经营管理领域

这是一个完整的业务领域，涵盖了KTV行业从顾客消费到门店运营的全部业务活动。该领域的核心目标是通过数字化手段提升KTV的经营效率和顾客体验。

### 子域（Subdomain）划分
基于业务职责和功能内聚性，将KTV经营管理领域划分为以下9个子域：

#### 1. 订单管理子域
**业务范围**：从开台到结账的完整订单生命周期管理
**核心概念**：订单、场次、计费、开台、点单、结账
**实现系统**：erp_client（核心）+ erp_managent（查询分析）

**核心功能实现**：

##### 1.1 开台功能
- **V3OrderOpen（开台-后付）**：创建订单和场次，房间状态变更为使用中
- **V3OrderOpenPay（开台-立结）**：开台同时完成支付，适用于预付费场景
- **V3OpenContinue（续房-后付）**：在现有场次基础上延长使用时间
- **V3OpenContinuePay（续房-立结）**：续房同时完成支付

**技术实现**：
```go
// 开台核心流程
func (s *OrderApplicationServiceImpl) V3OrderOpen(ctx context.Context, reqDto req.V3AddOrderOpenReqDto) (managentVo.SessionVO, error) {
    // 1. 验证基础参数（门店、房间、员工）
    // 2. 检查房间状态（必须为空闲状态）
    // 3. 生成场次ID和订单号
    // 4. 计算房费（基于价格方案和时长）
    // 5. 更新房间状态为使用中
    // 6. 保存订单和场次信息
    // 7. 更新预订状态（如有预订）
}
```

##### 1.2 点单功能
- **V3AdditionalOrder（点单-后付）**：在现有场次中添加商品订单
- **V3AdditionalOrderPay（点单-立结）**：点单同时完成支付

**技术实现**：
```go
// 点单核心流程
func (s *OrderApplicationServiceImpl) V3AdditionalOrder(ctx context.Context, reqDto req.V3AddOrderAdditionalReqDto) (managentVo.OrderVO, error) {
    // 1. 验证场次状态（必须为进行中）
    // 2. 检查房间是否锁定
    // 3. 验证商品信息和库存
    // 4. 计算商品费用
    // 5. 更新场次总费用
    // 6. 保存商品订单
}
```

##### 1.3 支付功能
- **V3OrderPay（支付-后付）**：对未支付订单进行支付
- **V3PayCallback（支付回调）**：处理第三方支付回调
- **V3PayQuery（支付查询）**：查询支付状态

##### 1.4 退款功能
- **V3OrderRefundDo（订单退款）**：支持现金退款和原路返回两种方式
- **退款计算**：精确计算退款金额，支持部分退款
- **库存回滚**：退款时自动回滚库存

**核心能力**：
- **流程引擎集成**：开台流程通过流程引擎编排，支持复杂业务规则
- **实时计费**：基于时间和商品的实时费用计算
- **状态管理**：订单、场次、房间状态的一致性管理
- **支付集成**：支持多种支付方式（现金、微信、支付宝、乐刷等）
- **事务保证**：关键操作采用数据库事务保证数据一致性

#### 2. 会员管理子域
**业务范围**：会员全生命周期管理和客户关系维护
**核心概念**：会员、会员卡、积分、等级、营销活动
**实现系统**：erp_client（交易）+ erp_managent（管理）

**核心功能实现**：

##### 2.1 会员注册功能
- **RegisterMember（普通会员注册）**：通过手机号注册，自动生成会员卡号
- **RegisterMemberWithPhysicalCard（实体卡注册）**：通过实体卡号注册会员
- **VerifyMemberInfo（会员信息验证）**：验证会员身份和信息完整性

**技术实现**：
```go
// 会员注册核心流程
func (s *MemberRegisterApplicationServiceImpl) RegisterMember(ctx context.Context, reqDto *dto.MemberRegisterDTO) (*vo.RegisterMemberVO, error) {
    // 1. 通过流程引擎执行会员注册流程
    params := map[string]interface{}{"reqDto": reqDto}
    result, err := s.processEngine.Execute(ctx, "member_register_process", params)

    // 2. 流程包含：
    // - 验证手机号唯一性
    // - 生成会员卡号
    // - 设置会员等级和权益
    // - 保存会员信息
    // - 创建门店关联关系
}
```

##### 2.2 会员充值功能
- **V3MemberRecharge（会员充值）**：支持多种支付方式的会员卡充值
- **V3OpenCard（会员开卡）**：新会员开卡并充值
- **充值回调处理**：处理第三方支付充值回调

**技术实现**：
```go
// 会员充值核心流程
func (s *MemberRechageApplicationServiceImpl) V3MemberRechage(ctx context.Context, reqDto req.V3QueryMemberRechargeReqDto) ([]vo.MemberRechargeBillExVO, error) {
    // 1. 验证充值参数
    // 2. 验证会员卡状态
    // 3. 计算充值金额和赠金
    // 4. 创建充值账单
    // 5. 处理支付记录
    // 6. 更新会员余额（如为记账支付）
    // 7. 发起第三方支付（如需要）
}
```

##### 2.3 会员消费功能
- **会员卡消费**：在订单支付时扣减会员卡余额
- **积分管理**：消费时自动累积积分
- **等级升级**：根据消费金额自动升级会员等级

##### 2.4 会员信息管理
- **会员档案管理**：基础信息、消费记录、积分记录
- **会员卡管理**：卡状态、余额、有效期管理
- **营销活动**：生日特权、等级权益、优惠券发放

**核心能力**：
- **流程引擎集成**：会员注册通过流程引擎编排，支持复杂验证规则
- **多卡类型支持**：虚拟卡、实体卡、等级卡等多种卡类型
- **余额管理**：本金、赠金分离管理，支持不同使用规则
- **积分体系**：完整的积分获取、消费、过期管理机制
- **事务安全**：充值、消费等关键操作保证事务一致性

#### 3. 支付管理子域
**业务范围**：支付处理、账单管理和资金安全
**核心概念**：支付、账单、退款、对账、风控
**实现系统**：erp_client（核心）+ erp_managent（管理）

**核心功能实现**：

##### 3.1 支付处理功能
- **多渠道支付**：现金、微信、支付宝、银行卡、会员卡、乐刷支付
- **支付网关**：统一的支付网关接口，支持不同支付方式
- **支付回调**：处理第三方支付平台的异步回调通知

**技术实现**：
```go
// 支付处理核心流程
func (s *PayServiceImpl) TransformPayGate(ctx context.Context, reqDto *req.QueryOrderPayReqDto, payBill *po.PayBill) (*vo.PayResultVO, error) {
    // 根据支付类型选择对应的支付网关
    switch *reqDto.PayType {
    case _const.PAY_TYPE_RECORD_CASH:
        return paysdk.CashPay(ctx, reqDto)
    case _const.PAY_TYPE_RECORD_WECHAT:
        return paysdk.WechatRecordPay(ctx, reqDto)
    case _const.PAY_TYPE_LESHUA_BSHOWQR:
        return paysdk.LeshuaPay(ctx, reqDto)
    case _const.PAY_TYPE_MEMBER_CARD:
        return s.memberCardPay(ctx, reqDto)
    }
}
```

##### 3.2 账单管理功能
- **PayBill（支付账单）**：记录每次支付的详细信息
- **PayRecord（支付记录）**：记录具体的支付方式和金额
- **OrderAndPay（订单支付关联）**：关联订单和支付账单

**技术实现**：
```go
// 账单生成核心流程
func (s *PayServiceImpl) BuildPayDataForPay(ctx context.Context, reqDto req.QueryOrderPayReqDto,
    toUpdateOrders []po.Order, session po.Session) (*po.PayBill, *[]po.OrderAndPay, error) {

    // 1. 生成支付账单ID
    billId := util.GetBillId(*reqDto.VenueId)

    // 2. 构建支付账单
    toAddPayBill := po.PayBill{
        VenueId:      reqDto.VenueId,
        SessionId:    reqDto.SessionId,
        BillId:       &billId,
        TotalFee:     reqDto.PayAmount,
        Status:       util.GetItPtr(_const.PAY_STATUS_UNPAID),
    }

    // 3. 构建订单支付关联
    toAddOrderAndPays := make([]po.OrderAndPay, 0)
    for _, order := range toUpdateOrders {
        toAddOrderAndPays = append(toAddOrderAndPays, po.OrderAndPay{
            OrderNo:   order.OrderNo,
            SessionId: reqDto.SessionId,
            BillId:    &billId,
        })
    }

    return &toAddPayBill, &toAddOrderAndPays, nil
}
```

##### 3.3 退款处理功能
- **现金退款**：直接现金退款，无需第三方处理
- **原路返回**：通过原支付渠道退款
- **部分退款**：支持按商品或金额进行部分退款

**技术实现**：
```go
// 退款处理核心流程
func (s *PayServiceImpl) V3RefundByCash(ctx context.Context, reqDto req.V3QueryOrderRefundReqDto) ([]vo.OrderRefundInfoVO, error) {
    // 1. 验证退款权限和金额
    // 2. 计算退款分配（按支付记录比例分配）
    // 3. 创建退款账单和记录
    // 4. 更新订单状态
    // 5. 处理库存回滚
    // 6. 发起第三方退款（如需要）
}
```

##### 3.4 对账管理功能
- **日结对账**：每日营业结束后的资金对账
- **支付查询**：查询第三方支付状态
- **异常处理**：处理支付异常和对账差异

##### 3.5 风控管理功能
- **支付限额**：单笔和日累计支付限额控制
- **异常监控**：监控异常支付行为
- **资金安全**：支付密码、操作员权限验证

**核心能力**：
- **多渠道统一**：统一的支付接口，支持多种支付方式
- **事务一致性**：支付、退款操作保证数据一致性
- **异步处理**：支付回调异步处理，提升用户体验
- **精确计算**：支持复杂的费用计算和分摊逻辑
- **安全可靠**：完善的风控机制和异常处理

#### 4. 房间管理子域
**业务范围**：房间资源管理和价格策略制定
**核心概念**：房间、房型、价格方案、预订、设备
**实现系统**：erp_client（查询）+ erp_managent（管理）

**核心功能实现**：

##### 4.1 房间信息管理
- **房间基础信息**：房间名称、房型、区域、主题、容量等
- **房间状态管理**：空闲、使用中、故障、清洁、客人等状态
- **房间配置**：高消费警报阈值、设备配置、标签管理

**技术实现**：
```go
// 房间聚合根（erp_managent）
type Room struct {
    id         string    // ID
    venueID    string    // 所属场所ID
    name       string    // 包厢名称
    typeID     string    // 包厢类型ID
    capacity   int       // 容纳人数
    status     int       // 包厢状态（0-空闲，1-占用，2-维修中）
    createTime time.Time // 创建时间
    updateTime time.Time // 更新时间
}

// 状态转换业务方法
func (r *Room) ChangeStatus(newStatus int) error {
    if !r.isValidStatusTransition(r.status, newStatus) {
        return errors.New("无效的状态转换")
    }
    r.status = newStatus
    r.updateTime = time.Now()
    return nil
}
```

##### 4.2 价格方案管理
- **买断价格方案**：固定时长的包厢费用，支持超时计费
- **计时价格方案**：按时间计费的包厢费用，支持最小计费单位
- **价格配置**：基础价格、会员价格、节假日价格、区域价格

**技术实现**：
```go
// 买断价格方案聚合根
type BuyoutPricePlan struct {
    id              string
    venueID         string
    name            string
    roomTypeConfig  *common.RoomTypeConfig
    timeConfig      *common.BuyoutTimeConfig
    duration        int  // 买断时长（分钟）
    priceConfigList common.PriceConfigList
}

// 价格计算业务方法
func (p *BuyoutPricePlan) CalculatePrice(req *PriceCalculationRequest) *PriceResult {
    baseAmount := p.getBaseAmount()
    if p.isExcessIncluded {
        baseAmount = p.calculateWithExcess(baseAmount, req.Duration)
    }
    return &PriceResult{BaseAmount: baseAmount}
}
```

##### 4.3 预订管理
- **预订创建**：客户预订包厢，指定时间和房型
- **预订确认**：确认预订信息，更新房间状态
- **预订取消**：取消预订，释放房间资源

**技术实现**：
```go
// 预订服务接口
type BookingService interface {
    UpdateBookingStatus(ctx context.Context, bookingId string) error
    ValidateBooking(ctx context.Context, bookingId string) error
    FindBookingById(ctx context.Context, bookingId string) (*po.Booking, error)
    ValidateBookingTime(ctx context.Context, arrivalTime int64) error
}

// 预订时间验证
func (s *BookingServiceImpl) ValidateBookingTime(ctx context.Context, arrivalTime int64) error {
    now := time.Now().Unix()
    if arrivalTime < now {
        return bookingerror.NewInvalidBookingTimeError("arrival time cannot be in the past")
    }
    return nil
}
```

##### 4.4 房间状态流转
- **开台状态流转**：空闲 → 使用中
- **关台状态流转**：使用中 → 清洁中/待清洁
- **故障状态管理**：故障报修、维修完成

**技术实现**：
```go
// 房间状态规则引擎配置
rules:
  - id: "check_room_idle"
    condition: "input.room.status == 'IDLE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "空闲"

  - id: "check_room_in_use"
    condition: "input.room.status == 'IN_USE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "使用中"
```

**核心能力**：
- **状态机管理**：严格的房间状态流转控制
- **价格策略**：灵活的价格配置和计算规则
- **预订系统**：完整的预订生命周期管理
- **规则引擎集成**：房间状态判断通过规则引擎实现

#### 5. 商品管理子域
**业务范围**：商品信息管理和库存控制
**核心概念**：商品、库存、分类、套餐、供应商
**实现系统**：erp_client（查询）+ erp_managent（管理）

**核心功能实现**：

##### 5.1 商品信息管理
- **商品基础信息**：名称、类型、价格、条码、规格等
- **商品分类**：商品类型、展示分类、销售分类
- **价格管理**：当前价格、历史价格、区域价格、时段价格

**技术实现**：
```go
// 商品应用服务（erp_client）
func (s *ProductApplicationServiceImpl) AddProduct(ctx context.Context, reqDto req.AddProductReqDto) (vo.ProductVO, error) {
    // 验证基础参数
    venue, err := s.validateBaseParams(ctx, reqDto.VenueId)
    if err != nil {
        return vo.ProductVO{}, err
    }

    // 创建产品
    product := &po.Product{
        VenueId:                       venue.Id,
        Name:                          reqDto.Name,
        Type:                          reqDto.Type,
        CurrentPrice:                  reqDto.CurrentPrice,
        Price:                         reqDto.Price,
        Barcode:                       reqDto.Barcode,
        AreaPrices:                    reqDto.AreaPrices,
        TimeSlotPrices:                reqDto.TimeSlotPrices,
        MinimumSaleQuantity:           reqDto.MinimumSaleQuantity,
    }

    return s.productService.Create(ctx, product)
}
```

##### 5.2 库存管理
- **存酒管理**：客户存酒、续存、取酒、报废等操作
- **库存统计**：实时库存、库存预警、库存盘点
- **存酒订单**：存酒单创建、状态管理、明细管理

**技术实现**：
```go
// 存酒管理服务（erp_managent）
func (service *ProductStorageOperationService) OperateProductStorage(ctx *gin.Context, storage *po.ProductStorage, reqDto *req.OperateProductStorageReqDto) error {
    switch reqDto.OperationType {
    case _const.OperationTypeExtend: // 续存
        return service.ExtendProductStorage(ctx, storage, reqDto)
    case _const.OperationTypeCancel: // 撤销存酒
        return service.CancelProductStorage(ctx, storage, reqDto)
    case _const.OperationTypeDiscard: // 报废
        return service.DiscardProductStorage(ctx, storage, reqDto)
    case "update": // 更新存酒记录
        return service.UpdateProductStorageInfo(ctx, storage, reqDto)
    default:
        return fmt.Errorf("不支持的操作类型: %s", reqDto.OperationType)
    }
}
```

##### 5.3 套餐管理
- **赠品组管理**：赠品组配置、商品组合、显示控制
- **买赠方案**：购买指定商品赠送其他商品
- **组合套餐**：多商品组合销售，统一定价

**技术实现**：
```go
// 赠品组管理（erp_managent）
func (controller *GiftGroupController) AddGiftGroup(ctx *gin.Context) {
    giftGroup := po.GiftGroup{
        Name:        reqDto.Name,
        Products:    reqDto.Products,    // 包含的商品列表
        IsDisplayed: reqDto.IsDisplayed, // 是否显示
    }

    err := giftGroupService.CreateGiftGroup(ctx, &giftGroup)
    if err != nil {
        Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
        return
    }
}
```

##### 5.4 商品查询服务
- **V3QueryProducts（erp_client）**：客户端商品查询，支持分类、价格筛选
- **商品详情查询**：获取商品完整信息，包括库存、价格等
- **商品列表查询**：支持分页、排序、筛选的商品列表

**技术实现**：
```go
// 商品查询服务（erp_client）
func (s *ProductApplicationServiceImpl) V3QueryProducts(ctx context.Context, reqDto req.V3QueryProductReqDto) ([]vo.ProductVO, error) {
    // 通过流程引擎执行商品查询流程
    params := map[string]interface{}{
        "reqDto": reqDto,
        "venueInfo": s.getVenueInfo(ctx, reqDto.VenueId),
    }

    result, err := s.processEngine.Execute(ctx, "product_query_process", params)
    if err != nil {
        return nil, err
    }

    return s.buildProductVOs(result), nil
}
```

##### 5.5 商品单位和类型管理
- **商品单位管理**：计量单位、包装单位、销售单位
- **商品类型管理**：商品分类体系、类型属性配置
- **商品绑定**：商品间的关联关系管理

**核心能力**：
- **流程引擎集成**：商品查询通过流程引擎编排
- **多维度管理**：商品信息、库存、价格、分类的统一管理
- **存酒业务**：KTV特有的客户存酒业务完整实现
- **灵活配置**：支持区域价格、时段价格等复杂定价策略

#### 6. 员工管理子域
**业务范围**：人力资源管理和权限控制
**核心概念**：员工、角色、权限、绩效、排班
**实现系统**：erp_managent（核心）

**核心功能实现**：

##### 6.1 员工生命周期管理
- **员工邀请**：生成邀请二维码，获取员工分组信息
- **员工注册**：通过邀请链接注册，创建员工档案
- **员工审核**：老板审核员工申请，通过或拒绝
- **员工删除**：删除员工及相关关联关系

**技术实现**：
```go
// 员工应用服务（erp_managent）
func (s *EmployeeAppServiceImpl) AddEmployee(ctx *gin.Context, reqDto *req.AddEmployeeReqDto) (*vo.EmployeeVO, error) {
    // 1. 验证用户身份
    currentUserId := ctx.GetString("userId")
    if currentUserId == "" {
        return nil, fmt.Errorf("请先登录")
    }

    // 2. 事务创建员工和关联关系
    var employee *po.Employee
    err = model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
        // 检查是否已存在关联关系
        existRelations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
            ERPUserId: &currentUserId,
            VenueId:   reqDto.VenueId,
        }, tx)
        if err != nil {
            return fmt.Errorf("查询关联关系失败: %v", err)
        }
        if len(*existRelations) > 0 {
            return fmt.Errorf("您已在该门店存在员工账号")
        }

        // 创建员工和关联关系
        employee, err = s.createEmployeeWithTx(ctx, reqDto, currentUserId, tx)
        return err
    })

    // 3. 设置审核状态
    employee.ReviewStatus = util.GetItPtr(0) // 0-待审核
    return employee, nil
}
```

##### 6.2 权限管理
- **权限角色管理**：定义不同的权限角色，配置权限列表
- **员工权限分配**：为员工分配权限角色，控制操作权限
- **权限验证**：在业务操作中验证员工权限

**技术实现**：
```go
// 权限角色管理（erp_managent）
type PermissionRole struct {
    Name                *string `json:"name"`                // 角色名称
    EmployeeType        *string `json:"employeeType"`        // 员工类型
    Permissions         *string `json:"permissions"`         // 权限列表
    AllowedProductTypes *string `json:"allowedProductTypes"` // 允许的商品类型
}

// 员工权限验证
func (s *EmployeeAppServiceImpl) ReviewEmployee(ctx *gin.Context, reqDto *req.ReviewEmployeeReqDto) error {
    // 权限校验：只有老板可以审核员工
    operatorEmployee, err := s.getOperatorEmployee(ctx, reqDto.VenueId)
    if err != nil {
        return err
    }

    if operatorEmployee.IsBoss == nil || !*operatorEmployee.IsBoss {
        return fmt.Errorf("您没有权限审核员工")
    }

    // 执行审核操作
    return s.processReview(ctx, reqDto)
}
```

##### 6.3 员工档案管理
- **基础信息**：姓名、手机号、性别、员工编号等
- **岗位信息**：员工类型、员工组、权限角色等
- **业务信息**：销售业绩、佣金权限、管理权限等

**技术实现**：
```go
// 员工实体（erp_managent）
type Employee struct {
    Id                           *string  `json:"id"`                           // ID
    VenueId                      *string  `json:"venueId"`                      // 门店ID
    Name                         *string  `json:"name"`                         // 员工姓名
    EmployeeNumber               *string  `json:"employeeNumber"`               // 员工编号
    Phone                        *string  `json:"phone"`                        // 电话号码
    Gender                       *string  `json:"gender"`                       // 性别
    Type                         *string  `json:"type"`                         // 员工类型
    Permissions                  *string  `json:"permissions"`                  // 权限列表
    EmployeeGroup                *string  `json:"employeeGroup"`                // 员工组
    PermissionRole               *string  `json:"permissionRole"`               // 权限角色
    SalesPerformance             *float32 `json:"salesPerformance"`             // 销售业绩
    CanViewCommissionPerformance *bool    `json:"canViewCommissionPerformance"` // 是否可以查看佣金业绩
    ReviewStatus                 *int     `json:"reviewStatus"`                 // 审核状态（0-待审核 1-通过 2-拒绝）
    IsBoss                       *bool    `json:"isBoss"`                       // 是否是门店老板
}
```

##### 6.4 员工关联关系管理
- **ERP用户关联**：员工与ERP系统用户的关联关系
- **多门店支持**：同一员工可以在多个门店工作
- **关联关系维护**：创建、更新、删除关联关系

**技术实现**：
```go
// ERP用户和员工关联（erp_managent）
type ERPUserAndEmployee struct {
    ERPUserId  *string `json:"erpUserId"`  // ERP用户ID
    EmployeeId *string `json:"employeeId"` // 员工ID
    VenueId    *string `json:"venueId"`    // 门店ID
}

// 删除员工时清理关联关系
func (s *EmployeeAppServiceImpl) DeleteEmployee(ctx *gin.Context, venueId string, employeeId string) error {
    return model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
        // 删除指定门店的关联关系
        relations, err := erpUserAndEmployeeService.FindAllERPUserAndEmployee(ctx, &req.QueryERPUserAndEmployeeReqDto{
            EmployeeId: &employeeId,
            VenueId:    &venueId,
        }, tx)

        for _, relation := range *relations {
            if err := erpUserAndEmployeeService.DeleteERPUserAndEmployeeWithTx(ctx, *relation.Id, tx); err != nil {
                return fmt.Errorf("删除关联失败: %v", err)
            }
        }

        // 删除员工主体
        return employeeService.DeleteEmployeeWithTx(ctx, employeeId, tx)
    })
}
```

##### 6.5 员工查询和管理
- **员工列表查询**：支持分页、筛选的员工列表
- **员工详情查询**：获取员工完整信息
- **员工信息更新**：更新员工基础信息和权限

**核心能力**：
- **完整生命周期**：从邀请、注册、审核到删除的完整流程
- **权限控制**：基于角色的权限管理和验证机制
- **多门店支持**：支持员工在多个门店工作的复杂场景
- **事务安全**：关键操作采用数据库事务保证数据一致性

#### 7. 门店管理子域
**业务范围**：门店基础信息和配置管理
**核心概念**：门店、区域、配置、营业时间

**核心能力**：
- 门店信息：基础信息、联系方式、营业时间
- 区域管理：区域划分、容量配置
- 系统配置：业务参数、系统设置

#### 8. 系统管理子域
**业务范围**：系统运行支撑和技术服务
**核心概念**：用户、认证、日志、监控

**核心能力**：
- 用户管理：用户注册、登录、权限
- 认证授权：JWT Token、RBAC权限
- 日志管理：操作日志、审计日志
- 系统监控：性能监控、异常告警

#### 9. 报表统计子域
**业务范围**：数据分析和决策支持
**核心概念**：报表、分析、仪表板、KPI

**核心能力**：
- 财务报表：营收统计、成本分析
- 经营报表：客流分析、商品销售
- 员工报表：绩效统计、考勤分析
- 数据大屏：实时监控、趋势分析

### 子域分类（核心域、支撑域、通用域）

#### 核心域 (Core Domain)
**定义**：为企业带来核心竞争优势的业务领域，是系统的核心价值所在，直接影响业务成功。

**KTV ERP系统的核心域**：
1. **订单管理子域** - KTV业务的核心流程，直接影响顾客体验和营收
2. **会员管理子域** - 客户关系管理，提升客户粘性和复购率
3. **支付管理子域** - 资金安全和交易完整性，直接影响营收

**特征**：
- 高业务价值，高技术复杂度
- 需要重点投资和持续优化
- 体现企业核心竞争力
- 不可外包，必须自主掌控

#### 支撑域 (Supporting Domain)
**定义**：支撑核心业务运行的重要领域，具有一定的业务复杂度，但不是核心竞争力。

**KTV ERP系统的支撑域**：
4. **房间管理子域** - 资源管理和价格策略，影响运营效率
5. **商品管理子域** - 商品和库存管理，影响服务质量
6. **员工管理子域** - 人力资源管理，影响服务质量和运营效率

**特征**：
- 中等业务价值，中等技术复杂度
- 需要适度投资，满足业务需求
- 具有企业特性，但不具有通用性
- 可以考虑部分外包或使用成熟方案

#### 通用域 (Generic Domain)
**定义**：通用的基础功能，没有太多个性化诉求，可以使用现成的解决方案。

**KTV ERP系统的通用域**：
7. **门店管理子域** - 基础配置管理，支撑业务运行
8. **系统管理子域** - 系统运行支撑，技术性功能
9. **报表统计子域** - 数据分析支撑，辅助决策

**特征**：
- 低到中等业务价值，低技术复杂度
- 最小投资，使用标准解决方案
- 具有通用性，多个子域共享
- 优先考虑外包或采购成熟产品

## 🔗 界限上下文 (Bounded Context)

### 界限上下文划分原则
1. **业务边界**：按照业务职责和数据所有权划分
2. **团队边界**：考虑团队组织结构和开发能力
3. **技术边界**：考虑技术架构和部署策略
4. **数据边界**：避免数据模型的概念冲突

### 界限上下文映射

#### 1. 客户端上下文 (erp_client) - 实时交易处理
```mermaid
graph TB
    subgraph "客户端上下文 - 实时交易"
        subgraph "订单交易上下文"
            OrderCreate[订单创建]
            OrderProcess[订单处理]
            OrderCalc[订单计算]
        end

        subgraph "会员交易上下文"
            MemberRegister[会员注册]
            MemberRecharge[会员充值]
            MemberConsume[会员消费]
        end

        subgraph "支付交易上下文"
            PaymentProcess[支付处理]
            PaymentCallback[支付回调]
            PaymentRefund[支付退款]
        end

        subgraph "资源查询上下文"
            RoomQuery[房间查询]
            ProductQuery[商品查询]
            PriceQuery[价格查询]
        end
    end
```

#### 2. 管理端上下文 (erp_managent) - 配置管理分析
```mermaid
graph TB
    subgraph "管理端上下文 - 配置管理"
        subgraph "会员管理上下文"
            MemberMgmt[会员信息管理]
            MemberAnalysis[会员数据分析]
            MemberReport[会员报表]
        end

        subgraph "资源管理上下文"
            RoomMgmt[房间信息管理]
            ProductMgmt[商品信息管理]
            PriceMgmt[价格方案管理]
        end

        subgraph "订单管理上下文"
            OrderQuery[订单查询]
            OrderAnalysis[订单分析]
            OrderReport[订单报表]
        end

        subgraph "系统管理上下文"
            UserMgmt[用户管理]
            AuthMgmt[认证管理]
            LogMgmt[日志管理]
        end

        subgraph "运营管理上下文"
            EmployeeMgmt[员工管理]
            VenueMgmt[门店管理]
            ConfigMgmt[配置管理]
        end
    end
```

#### 3. 跨系统上下文关系
```mermaid
graph LR
    subgraph "erp_client"
        ClientOrder[订单交易]
        ClientMember[会员交易]
        ClientRoom[房间查询]
        ClientProduct[商品查询]
    end

    subgraph "erp_managent"
        MgmtOrder[订单管理]
        MgmtMember[会员管理]
        MgmtRoom[房间管理]
        MgmtProduct[商品管理]
    end

    subgraph "共享数据"
        SharedDB[(共享数据库)]
        EventBus[事件总线]
        API[API调用]
    end

    ClientOrder -.->|查询历史| MgmtOrder
    ClientMember -.->|同步数据| MgmtMember
    ClientRoom -.->|获取配置| MgmtRoom
    ClientProduct -.->|获取信息| MgmtProduct

    ClientOrder --> SharedDB
    MgmtOrder --> SharedDB
    ClientMember --> EventBus
    MgmtMember --> EventBus
    ClientRoom --> API
    MgmtRoom --> API
```

### 上下文间关系

#### 1. 共享内核关系 (Shared Kernel)
- **erp_client订单交易 ↔ erp_managent订单管理**：共享订单数据模型和核心业务规则
- **erp_client会员交易 ↔ erp_managent会员管理**：共享会员数据模型和基础信息

#### 2. 客户-供应商关系 (Customer-Supplier)
- **erp_client房间查询 → erp_managent房间管理**：客户端查询房间信息时依赖管理端的配置数据
- **erp_client商品查询 → erp_managent商品管理**：客户端查询商品信息时依赖管理端的商品数据
- **erp_client价格查询 → erp_managent价格管理**：客户端计算价格时依赖管理端的价格方案

#### 3. 遵循者关系 (Conformist)
- **erp_client所有上下文 → erp_managent认证管理**：客户端所有操作都需要管理端的认证服务
- **erp_client所有上下文 → erp_managent日志管理**：客户端所有操作都需要记录到管理端的日志系统

#### 4. 开放主机服务 (Open Host Service)
- **erp_managent → erp_client**：管理端为客户端提供标准化的API服务
- **erp_client → erp_managent**：客户端为管理端提供实时数据查询接口

#### 5. 发布语言 (Published Language)
- **事件格式标准化**：两个系统间的事件通信使用统一的事件格式
- **API接口标准化**：跨系统调用使用统一的API规范和数据格式

#### 6. 大泥球关系 (Big Ball of Mud)
- **数据库共享**：当前两个系统共享同一个数据库，存在数据耦合
- **代码重复**：部分业务逻辑在两个系统中都有实现，存在代码重复

## 📊 子域重要性矩阵

### 战略重要性评估

#### 子域战略重要性矩阵
```mermaid
graph LR
    subgraph "高业务价值"
        subgraph "核心域"
            A1[订单管理<br/>高复杂度/高价值]
            A2[支付管理<br/>高复杂度/高价值]
            A3[会员管理<br/>中复杂度/高价值]
        end

        subgraph "支撑域"
            B1[房间管理<br/>中复杂度/中价值]
            B2[商品管理<br/>中复杂度/中价值]
        end
    end

    subgraph "低业务价值"
        subgraph "通用域"
            C1[员工管理<br/>中复杂度/中价值]
            C2[门店管理<br/>低复杂度/低价值]
            C3[系统管理<br/>低复杂度/低价值]
            C4[报表统计<br/>低复杂度/中价值]
        end
    end

    style A1 fill:#ff6b6b
    style A2 fill:#ff6b6b
    style A3 fill:#ff6b6b
    style B1 fill:#4ecdc4
    style B2 fill:#4ecdc4
    style C1 fill:#45b7d1
    style C2 fill:#96ceb4
    style C3 fill:#96ceb4
    style C4 fill:#96ceb4
```

#### 子域战略评估表格
| 子域 | 业务价值 | 技术复杂度 | 战略分类 | 投资优先级 | 当前实现系统 | 目标归属 |
|------|----------|------------|----------|------------|------------|----------|
| **核心域** |
| 订单管理子域 | 极高 | 高 | 核心域 | 最高 | 重叠 | erp_client |
| 会员管理子域 | 极高 | 高 | 核心域 | 最高 | 重叠 | erp_client |
| 支付管理子域 | 极高 | 高 | 核心域 | 最高 | erp_client | erp_client |
| **支撑域** |
| 房间管理子域 | 中高 | 中高 | 支撑域 | 高 | 重叠 | erp_client |
| 商品管理子域 | 中高 | 中高 | 支撑域 | 高 | 重叠 | erp_client |
| 员工管理子域 | 中 | 中 | 支撑域 | 中 | erp_managent | erp_managent |
| **通用域** |
| 门店管理子域 | 低 | 低 | 通用域 | 低 | erp_managent | erp_managent |
| 系统管理子域 | 低 | 低 | 通用域 | 低 | erp_managent | erp_managent |
| 报表统计子域 | 中 | 低 | 通用域 | 低 | erp_managent | erp_managent |

#### 子域分类说明

**核心域特征分析**：
- **订单管理子域**：KTV业务的核心，从开台到结账的完整流程，直接影响营收和客户体验
- **会员管理子域**：客户关系管理核心，影响客户粘性和复购率，是差异化竞争的关键
- **支付管理子域**：资金安全和交易完整性，直接关系到企业资金流和风险控制

**支撑域特征分析**：
- **房间管理子域**：KTV特有的资源管理，价格策略制定，影响运营效率
- **商品管理子域**：服务商品的管理，影响服务质量和客户满意度
- **员工管理子域**：人力资源管理，影响服务质量，但相对标准化

**通用域特征分析**：
- **门店管理子域**：基础配置功能，可使用通用解决方案
- **系统管理子域**：技术基础功能，可采用成熟框架
- **报表统计子域**：数据分析功能，可使用BI工具

### 投资策略建议

#### 核心域 - 重点投资（70%资源）
**投资原则**：自主研发，持续优化，构建核心竞争力

- **订单管理子域**：
  - 投资重点：规则引擎、流程引擎、实时计算
  - 目标：提升订单处理效率，优化客户体验
  - 预期收益：直接提升营收和客户满意度

- **会员管理子域**：
  - 投资重点：会员画像、精准营销、积分体系
  - 目标：提升客户粘性和复购率
  - 预期收益：增加客户生命周期价值

- **支付管理子域**：
  - 投资重点：多渠道支付、风控系统、对账系统
  - 目标：保证资金安全，提升支付成功率
  - 预期收益：降低资金风险，提升收银效率

#### 支撑域 - 适度投资（20%资源）
**投资原则**：满足业务需求，适度优化，考虑成熟方案

- **房间管理子域**：
  - 投资重点：智能定价、设备集成、状态监控
  - 目标：优化资源利用率，提升运营效率
  - 策略：自研核心功能，集成第三方设备管理

- **商品管理子域**：
  - 投资重点：库存优化、套餐配置、供应链管理
  - 目标：提升服务质量，降低库存成本
  - 策略：使用成熟的库存管理框架

- **员工管理子域**：
  - 投资重点：绩效管理、排班优化、权限控制
  - 目标：提升人效，优化服务质量
  - 策略：采用标准HR解决方案，定制KTV特色功能

#### 通用域 - 最小投资（10%资源）
**投资原则**：采购成熟产品，外包实施，快速交付

- **门店管理子域**：
  - 策略：使用标准的企业配置管理系统
  - 目标：满足基本配置需求，降低维护成本

- **系统管理子域**：
  - 策略：采用成熟的技术框架和开源方案
  - 目标：保证系统稳定性，降低技术风险

- **报表统计子域**：
  - 策略：使用商业BI工具或开源报表工具
  - 目标：快速实现数据分析需求，降低开发成本

## 🚀 架构演进策略

### 当前架构状态
```mermaid
graph TB
    subgraph "系统分工"
        Legacy[erp_managent<br/>管理端系统<br/>渐进式DDD改造]
        New[erp_client<br/>客户端系统<br/>全新DDD架构]
    end

    subgraph "核心域 - 客户消费流程"
        CoreOrder[订单管理<br/>开台/点单/结账]
        CoreMember[会员管理<br/>注册/充值/积分]
        CorePayment[支付管理<br/>收银/对账/退款]
    end

    subgraph "支撑域 - 资源和运营"
        SupportRoom[房间管理<br/>状态/价格/设备]
        SupportProduct[商品管理<br/>信息/库存/套餐]
        SupportEmployee[员工管理<br/>档案/权限/绩效]
    end

    subgraph "通用域 - 基础配置"
        GenericVenue[门店管理<br/>基础信息/配置]
        GenericSystem[系统管理<br/>用户/认证/日志]
        GenericReport[报表统计<br/>财务/经营/分析]
    end

    subgraph "功能重叠区域"
        OverlapMember[会员管理]
        OverlapRoom[房间管理]
        OverlapProduct[商品管理]
        OverlapOrder[订单管理]
    end

    %% 核心域主要在erp_client
    New -.-> CoreOrder
    New -.-> CoreMember
    New -.-> CorePayment

    %% 支撑域分布在两个系统
    New -.-> SupportRoom
    New -.-> SupportProduct
    Legacy -.-> SupportEmployee

    %% 通用域主要在erp_managent
    Legacy -.-> GenericVenue
    Legacy -.-> GenericSystem
    Legacy -.-> GenericReport

    %% 重叠功能
    Legacy -.-> OverlapMember
    New -.-> OverlapMember
    Legacy -.-> OverlapRoom
    New -.-> OverlapRoom
    Legacy -.-> OverlapProduct
    New -.-> OverlapProduct
    Legacy -.-> OverlapOrder
    New -.-> OverlapOrder

    style OverlapMember fill:#ffeb3b
    style OverlapRoom fill:#ffeb3b
    style OverlapProduct fill:#ffeb3b
    style OverlapOrder fill:#ffeb3b
```

### 功能重叠分析

#### 1. 会员管理重叠
**erp_managent（管理端）**：
- 会员基础信息管理（增删改查）
- 会员列表查询和详情查看
- 会员数据统计和分析

**erp_client（客户端）**：
- 会员注册和验证
- 会员充值和消费
- 会员卡等级管理

**重叠原因**：会员管理既需要管理端的后台管理功能，也需要客户端的实时交易功能

#### 2. 房间管理重叠
**erp_managent（管理端）**：
- 房间基础信息管理（增删改查）
- 房间状态监控和管理
- 房间设备和配置管理

**erp_client（客户端）**：
- 房间状态查询（开台时）
- 房间信息获取（二维码扫描）
- 房间故障报修

**重叠原因**：房间管理既需要管理端的配置功能，也需要客户端的实时查询功能

#### 3. 商品管理重叠
**erp_managent（管理端）**：
- 商品基础信息管理（增删改查）
- 商品分类和价格管理
- 库存管理和统计

**erp_client（客户端）**：
- 商品查询（点单时）
- 商品信息展示
- 库存检查

**重叠原因**：商品管理既需要管理端的维护功能，也需要客户端的查询功能

#### 4. 订单管理重叠
**erp_managent（管理端）**：
- 订单历史查询和管理
- 订单统计和分析
- 订单退款和调整

**erp_client（客户端）**：
- 订单创建和处理（开台/点单/结账）
- 订单状态管理
- 订单实时计算

**重叠原因**：订单管理既需要管理端的查询分析功能，也需要客户端的实时处理功能

### 重叠处理策略

#### 1. 读写分离策略
- **写操作**：主要在对应的主责系统中实现
- **读操作**：通过API调用或数据同步实现跨系统访问

#### 2. 职责明确策略
- **erp_managent**：负责配置管理、数据维护、统计分析
- **erp_client**：负责实时交易、业务流程、规则执行

#### 3. 数据同步策略
- **实时同步**：关键业务数据通过事件总线实时同步
- **定时同步**：非关键数据通过定时任务同步
- **按需同步**：查询时通过API获取最新数据

### 未来演进路径

#### 阶段1：核心域完善 (3-6个月)
**目标**：完善erp_client中的核心域功能，明确职责边界
- **订单管理域**：完善规则引擎和流程引擎集成，承担所有实时订单处理
- **会员管理域**：增强营销功能和积分体系，承担所有会员交易功能
- **支付管理域**：完善多渠道支付和风控机制，承担所有支付处理

#### 阶段2：重叠功能整合 (6-12个月)
**目标**：解决功能重叠问题，明确系统边界
- **会员管理整合**：
  - erp_client负责：会员注册、充值、消费、积分管理
  - erp_managent负责：会员信息维护、数据分析、营销管理
- **房间管理整合**：
  - erp_client负责：房间状态查询、实时信息获取
  - erp_managent负责：房间配置管理、设备管理、统计分析
- **商品管理整合**：
  - erp_client负责：商品查询、库存检查、价格计算
  - erp_managent负责：商品信息维护、分类管理、库存管理
- **订单管理整合**：
  - erp_client负责：订单创建、处理、实时计算
  - erp_managent负责：订单查询、统计分析、历史管理

#### 阶段3：数据解耦和标准化 (12-18个月)
**目标**：解决数据耦合问题，实现真正的系统独立
- **数据库分离**：将共享数据库拆分为独立的数据库
- **事件驱动集成**：使用事件总线实现系统间数据同步
- **API标准化**：建立统一的跨系统API规范
- **通用域标准化**：采用标准解决方案和第三方工具

### 技术债务管理

#### 高优先级技术债务
1. **功能重叠**：会员、房间、商品、订单管理在两个系统中都有实现
2. **数据耦合**：两个系统共享同一个数据库，存在强耦合
3. **代码重复**：相同的业务逻辑在两个系统中重复实现
4. **接口不统一**：跨系统调用缺乏统一的API规范

#### 中优先级技术债务
1. **事务一致性**：跨系统的事务处理缺乏统一机制
2. **数据同步**：实时数据同步机制不完善
3. **测试覆盖**：重叠功能的测试覆盖率不足
4. **文档缺失**：系统间集成的技术文档不完善

#### 低优先级技术债务
1. **性能优化**：跨系统调用的性能优化
2. **监控完善**：跨系统的业务监控和告警
3. **安全加固**：跨系统调用的安全机制
4. **运维复杂度**：双系统部署和维护的复杂性

## 📈 成功指标

### 业务指标
- **用户体验**：订单处理时间 < 3秒
- **系统稳定性**：可用性 > 99.9%
- **业务增长**：支持10倍业务量增长

### 技术指标
- **代码质量**：测试覆盖率 > 80%
- **开发效率**：新功能开发周期缩短50%
- **维护成本**：Bug修复时间缩短60%

### 团队指标
- **学习成本**：新人上手时间 < 2周
- **协作效率**：跨团队协作成本降低40%
- **技术债务**：技术债务比例 < 20%

## 🎯 实施建议

### 组织建议
1. **设立DDD专家组**：负责领域建模和架构指导
2. **建立跨功能团队**：包含业务专家、开发人员、测试人员
3. **定期架构评审**：每季度进行架构健康度检查

### 技术建议
1. **渐进式迁移**：避免大爆炸式重构，采用渐进式迁移
2. **事件驱动集成**：使用事件总线实现界限上下文间通信
3. **自动化测试**：建立完善的自动化测试体系

### 流程建议
1. **领域建模工作坊**：定期举办领域建模工作坊
2. **代码审查机制**：强化DDD相关的代码审查
3. **知识分享**：建立DDD最佳实践知识库

---

*文档维护者：架构师、业务分析师*
*最后更新：2025-01-06*
