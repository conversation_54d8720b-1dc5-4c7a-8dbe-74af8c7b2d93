# DDD设计

## 📖 概述

erp_client新架构采用领域驱动设计(Domain-Driven Design, DDD)作为核心设计方法论，通过清晰的领域边界划分、丰富的领域模型和完善的分层架构，构建了一个高内聚、低耦合的业务系统。

## 🎯 DDD核心理念

### 设计原则
- **领域为王**：业务领域是系统设计的核心，技术服务于业务
- **统一语言**：开发团队与业务专家使用统一的业务语言
- **边界清晰**：明确的限界上下文，避免领域概念混淆
- **模型驱动**：丰富的领域模型承载核心业务逻辑
- **分层隔离**：清晰的分层架构，职责分离

### 架构约束
- **领域隔离**：不同领域之间禁止直接调用，必须通过事件通信
- **依赖倒置**：领域层不依赖基础设施层，通过接口抽象
- **聚合完整性**：聚合内部保证数据一致性，聚合间最终一致性
- **事务边界**：一个事务只能修改一个聚合根

## 🏗️ 领域架构

### erp_client领域架构图
```mermaid
graph TB
    subgraph "API层"
        Controller[Controller控制器]
        DTO[DTO数据传输对象]
        VO[VO视图对象]
    end
    
    subgraph "应用层 Application"
        AppService[应用服务]
        ProcessEngine[流程引擎]
        Framework[应用框架]
    end
    
    subgraph "领域层 Domain"
        subgraph "Rule领域"
            RuleEngine[规则引擎]
            RuleModel[规则模型]
            RuleService[规则服务]
        end
        
        subgraph "Process领域"
            ProcessModel[流程模型]
            ProcessService[流程服务]
        end
        
        subgraph "ValueObject领域"
            OrderVO[订单值对象]
            MemberVO[会员值对象]
            ProductVO[商品值对象]
        end
        
        subgraph "Subject领域"
            UserSubject[用户主体]
            SystemSubject[系统主体]
        end
        
        subgraph "State领域"
            OrderState[订单状态]
            PaymentState[支付状态]
        end
        
        subgraph "Permission领域"
            RolePermission[角色权限]
            ResourcePermission[资源权限]
        end
    end
    
    subgraph "基础设施层 Infrastructure"
        Proxy[基础设施代理]
        Repository[仓储实现]
        Event[事件处理]
        Messaging[消息传递]
    end
    
    Controller --> AppService
    AppService --> ProcessEngine
    ProcessEngine --> RuleEngine
    RuleEngine --> RuleService
    ProcessEngine --> ProcessService
    
    RuleService --> Proxy
    ProcessService --> Repository
    ValueObject --> Event
```

### 六大核心领域

#### 1. Rule领域 - 规则引擎领域
```go
// 规则聚合根
type RuleAggregate struct {
    ID          string
    GroupID     string
    Name        string
    Priority    int
    Condition   string
    Actions     []RuleAction
    ValidFrom   time.Time
    ValidTo     time.Time
    Status      RuleStatus
}

// 规则领域服务
type RuleService interface {
    // 评估规则组
    Evaluate(ctx context.Context, ruleGroupID string, ruleCtx *RuleContext) (*RuleResult, error)
    
    // 应用单个规则
    Apply(ctx context.Context, ruleID string, ruleCtx *RuleContext) (*RuleResult, error)
    
    // 加载规则组
    LoadRuleGroup(ctx context.Context, ruleGroupID string) (*RuleGroup, error)
}
```

#### 2. Process领域 - 流程引擎领域
```go
// 流程聚合根
type ProcessAggregate struct {
    ID          string
    Name        string
    Version     string
    Steps       []ProcessStep
    Status      ProcessStatus
    CreatedAt   time.Time
    UpdatedAt   time.Time
}

// 流程领域服务
type ProcessService interface {
    // 执行流程
    Execute(ctx context.Context, processID string, params map[string]interface{}) (map[string]interface{}, error)
    
    // 加载流程定义
    LoadProcess(ctx context.Context, processID string) (*ProcessDefinition, error)
    
    // 获取流程状态
    GetProcessStatus(ctx context.Context, instanceID string) (*ProcessStatus, error)
}
```

#### 3. ValueObject领域 - 值对象领域
```go
// 订单值对象
type OrderValueObject struct {
    OrderID      string
    VenueID      string
    RoomID       string
    MemberID     string
    Amount       decimal.Decimal
    Status       OrderStatus
    CreatedAt    time.Time
}

// 会员值对象
type MemberValueObject struct {
    MemberID     string
    Phone        string
    Name         string
    Level        MemberLevel
    Balance      decimal.Decimal
    Points       int
}
```

#### 4. Subject领域 - 主体领域
```go
// 用户主体
type UserSubject struct {
    UserID      string
    Username    string
    Roles       []Role
    Permissions []Permission
    Status      UserStatus
}

// 系统主体
type SystemSubject struct {
    SystemID    string
    Name        string
    Type        SystemType
    Config      map[string]interface{}
}
```

#### 5. State领域 - 状态管理领域
```go
// 状态聚合根
type StateAggregate struct {
    EntityID    string
    EntityType  string
    CurrentState string
    PreviousState string
    Transitions []StateTransition
    UpdatedAt   time.Time
}

// 状态领域服务
type StateService interface {
    // 状态转换
    Transition(ctx context.Context, entityID string, targetState string) error
    
    // 获取当前状态
    GetCurrentState(ctx context.Context, entityID string) (string, error)
    
    // 检查状态转换是否有效
    IsValidTransition(ctx context.Context, entityID string, targetState string) bool
}
```

#### 6. Permission领域 - 权限管理领域
```go
// 权限聚合根
type PermissionAggregate struct {
    ID          string
    SubjectID   string
    SubjectType string
    Resource    string
    Action      string
    Effect      PermissionEffect
    Conditions  []PermissionCondition
}

// 权限领域服务
type PermissionService interface {
    // 检查权限
    CheckPermission(ctx context.Context, subject Subject, resource string, action string) bool
    
    // 授权
    Grant(ctx context.Context, subjectID string, permissions []Permission) error
    
    // 撤销权限
    Revoke(ctx context.Context, subjectID string, permissions []Permission) error
}
```

## 🔄 领域间协作

### 事件驱动的领域协作
```mermaid
sequenceDiagram
    participant API as API层
    participant App as 应用层
    participant Rule as Rule领域
    participant Process as Process领域
    participant State as State领域
    participant Event as 事件总线
    
    API->>App: 1. 业务请求
    App->>Process: 2. 启动业务流程
    Process->>Rule: 3. 执行业务规则
    Rule->>Event: 4. 发布规则执行事件
    Event->>State: 5. 状态变更通知
    State->>Event: 6. 发布状态变更事件
    Event->>Process: 7. 状态变更反馈
    Process->>App: 8. 流程执行结果
    App->>API: 9. 返回业务结果
```

### 领域事件定义
```go
// 规则执行事件
type RuleExecutedEvent struct {
    RuleID      string
    RuleGroupID string
    Context     map[string]interface{}
    Result      map[string]interface{}
    ExecutedAt  time.Time
}

// 流程状态变更事件
type ProcessStateChangedEvent struct {
    ProcessID     string
    InstanceID    string
    PreviousState string
    CurrentState  string
    ChangedAt     time.Time
}

// 订单状态变更事件
type OrderStateChangedEvent struct {
    OrderID       string
    PreviousState string
    CurrentState  string
    ChangedBy     string
    ChangedAt     time.Time
}
```

## 🏛️ 分层架构实现

### 1. API层实现
```go
// 控制器实现
type OrderController struct {
    orderAppService application.OrderAppService
}

func (c *OrderController) CreateOrder(ctx *gin.Context) {
    var req dto.CreateOrderRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, "参数错误", err)
        return
    }
    
    result, err := c.orderAppService.CreateOrder(ctx, &req)
    if err != nil {
        response.Error(ctx, "创建订单失败", err)
        return
    }
    
    response.Success(ctx, "创建成功", result)
}
```

### 2. 应用层实现
```go
// 应用服务实现
type OrderAppService struct {
    processEngine process.Engine
    ruleEngine    rule.Engine
    eventBus      event.Bus
}

func (s *OrderAppService) CreateOrder(ctx context.Context, req *dto.CreateOrderRequest) (*vo.OrderVO, error) {
    // 1. 启动订单创建流程
    processParams := map[string]interface{}{
        "order_request": req,
        "user_info":     s.getUserInfo(ctx, req.UserID),
    }
    
    result, err := s.processEngine.Execute(ctx, "order_create_process", processParams)
    if err != nil {
        return nil, fmt.Errorf("订单创建流程执行失败: %w", err)
    }
    
    // 2. 构造返回结果
    orderVO := &vo.OrderVO{
        OrderID:        result["order_id"].(string),
        TotalAmount:    result["total_amount"].(float64),
        DiscountAmount: result["discount_amount"].(float64),
        Status:         "created",
    }
    
    // 3. 发布订单创建事件
    event := &OrderCreatedEvent{
        OrderID:   orderVO.OrderID,
        UserID:    req.UserID,
        Amount:    orderVO.TotalAmount,
        CreatedAt: time.Now(),
    }
    s.eventBus.Publish(ctx, event)
    
    return orderVO, nil
}
```

### 3. 领域层实现
```go
// 规则领域服务实现
type RuleServiceImpl struct {
    ruleEngine engine.RuleEngine
    repository rule.Repository
}

func (s *RuleServiceImpl) Evaluate(ctx context.Context, ruleGroupID string, ruleCtx *model.RuleContext) (*model.RuleResult, error) {
    // 1. 检查规则组是否存在
    if _, err := s.LoadRuleGroup(ctx, ruleGroupID); err != nil {
        return nil, fmt.Errorf("加载规则组失败: %w", err)
    }
    
    // 2. 转换上下文为参数映射
    params := s.convertContextToParams(ruleCtx)
    
    // 3. 执行规则引擎
    result, err := s.ruleEngine.Execute(ctx, ruleGroupID, params)
    if err != nil {
        return nil, err
    }
    
    // 4. 转换结果
    return s.convertResultToRuleResult(result), nil
}
```

### 4. 基础设施层实现
```go
// 基础设施代理实现
type InfrastructureProxy struct {
    orderRepo    repository.OrderRepository
    memberRepo   repository.MemberRepository
    paymentRepo  repository.PaymentRepository
    cacheManager cache.Manager
}

func (p *InfrastructureProxy) GetOrder(ctx context.Context, orderID string) (*model.Order, error) {
    // 1. 先从缓存获取
    if cached, err := p.cacheManager.Get(ctx, "order:"+orderID); err == nil {
        return cached.(*model.Order), nil
    }
    
    // 2. 从数据库获取
    order, err := p.orderRepo.GetByID(ctx, orderID)
    if err != nil {
        return nil, err
    }
    
    // 3. 写入缓存
    p.cacheManager.Set(ctx, "order:"+orderID, order, 5*time.Minute)
    
    return order, nil
}
```

## 📊 领域模型设计

### 聚合设计原则
```go
// 订单聚合根
type OrderAggregate struct {
    // 聚合根标识
    orderID string
    
    // 基本信息
    venueID   string
    roomID    string
    memberID  string
    
    // 订单项（实体）
    orderItems []OrderItem
    
    // 价格信息（值对象）
    pricing OrderPricing
    
    // 状态信息
    status    OrderStatus
    createdAt time.Time
    updatedAt time.Time
    
    // 领域事件
    events []DomainEvent
}

// 聚合根方法
func (o *OrderAggregate) AddOrderItem(item OrderItem) error {
    // 业务规则验证
    if err := o.validateOrderItem(item); err != nil {
        return err
    }
    
    // 添加订单项
    o.orderItems = append(o.orderItems, item)
    
    // 重新计算价格
    o.recalculatePricing()
    
    // 发布领域事件
    o.addEvent(&OrderItemAddedEvent{
        OrderID: o.orderID,
        Item:    item,
        AddedAt: time.Now(),
    })
    
    return nil
}
```

### 值对象设计
```go
// 价格值对象
type OrderPricing struct {
    baseAmount     decimal.Decimal
    discountAmount decimal.Decimal
    taxAmount      decimal.Decimal
    totalAmount    decimal.Decimal
}

// 值对象不可变性
func NewOrderPricing(base, discount, tax decimal.Decimal) OrderPricing {
    return OrderPricing{
        baseAmount:     base,
        discountAmount: discount,
        taxAmount:      tax,
        totalAmount:    base.Sub(discount).Add(tax),
    }
}

// 值对象相等性
func (p OrderPricing) Equals(other OrderPricing) bool {
    return p.baseAmount.Equal(other.baseAmount) &&
           p.discountAmount.Equal(other.discountAmount) &&
           p.taxAmount.Equal(other.taxAmount)
}
```

## 🔧 仓储模式

### 仓储接口定义
```go
// 订单仓储接口
type OrderRepository interface {
    // 保存聚合根
    Save(ctx context.Context, order *OrderAggregate) error
    
    // 根据ID获取聚合根
    GetByID(ctx context.Context, orderID string) (*OrderAggregate, error)
    
    // 查询订单列表
    FindByCondition(ctx context.Context, condition OrderQueryCondition) ([]*OrderAggregate, error)
    
    // 删除订单
    Delete(ctx context.Context, orderID string) error
}
```

### 仓储实现
```go
// 订单仓储实现
type OrderRepositoryImpl struct {
    db    *gorm.DB
    cache cache.Manager
}

func (r *OrderRepositoryImpl) Save(ctx context.Context, order *OrderAggregate) error {
    // 1. 开启事务
    tx := r.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    // 2. 保存聚合根
    if err := r.saveOrderEntity(tx, order); err != nil {
        tx.Rollback()
        return err
    }
    
    // 3. 保存订单项
    if err := r.saveOrderItems(tx, order); err != nil {
        tx.Rollback()
        return err
    }
    
    // 4. 提交事务
    if err := tx.Commit().Error; err != nil {
        return err
    }
    
    // 5. 更新缓存
    r.cache.Set(ctx, "order:"+order.orderID, order, 5*time.Minute)
    
    // 6. 发布领域事件
    r.publishDomainEvents(ctx, order.GetEvents())
    
    return nil
}
```

---

*文档维护者：架构师、DDD设计团队*
*最后更新：2025-01-06*
