# 规则引擎

## 📖 概述

erp_client新架构的规则引擎是系统的核心组件之一，采用YAML配置驱动的方式，将复杂的业务规则从代码中分离出来，实现业务规则的配置化管理。规则引擎与流程引擎深度集成，在业务流程的关键节点执行规则，驱动业务决策。

## 🎯 设计理念

### 核心原则
- **规则驱动**：业务行为由规则决定，而不是硬编码的逻辑
- **配置化管理**：规则以YAML格式配置，支持热更新
- **表达式计算**：支持复杂的条件表达式和计算表达式
- **变量管理**：完整的变量命名空间和作用域管理
- **动作执行**：丰富的动作类型，支持扩展

### 架构特点
- **分层设计**：规则定义、规则引擎、规则执行器分层实现
- **插件化**：支持自定义操作符和动作处理器
- **高性能**：规则缓存、表达式预编译、并行执行
- **易扩展**：标准化的接口设计，便于功能扩展

## 🏗️ 架构设计

### 规则引擎架构图
```mermaid
graph TB
    subgraph "规则定义层"
        YAML[YAML规则配置]
        RuleGroup[规则组定义]
        Metadata[元数据定义]
    end
    
    subgraph "规则引擎核心"
        Engine[RuleEngine规则引擎]
        Executor[RuleExecutor执行器]
        Runtime[Variable Runtime变量运行时]
    end
    
    subgraph "执行支撑"
        Parser[Expression Parser表达式解析器]
        Converter[Type Converter类型转换器]
        Actions[Action Handlers动作处理器]
    end
    
    subgraph "变量管理"
        Store[Variable Store变量存储]
        Namespace[Namespace命名空间]
        Context[Rule Context规则上下文]
    end
    
    YAML --> Engine
    RuleGroup --> Engine
    Metadata --> Engine
    
    Engine --> Executor
    Engine --> Runtime
    
    Executor --> Parser
    Executor --> Converter
    Executor --> Actions
    
    Runtime --> Store
    Runtime --> Namespace
    Runtime --> Context
```

### 核心组件

#### 1. RuleEngine (规则引擎)
```go
type RuleEngine interface {
    // 注册动作处理器
    RegisterAction(name string, action ActionFunc)
    
    // 执行规则组
    Execute(ctx context.Context, ruleGroupID string, params map[string]interface{}) (map[string]interface{}, error)
    
    // 执行单个规则
    ExecuteRule(ctx context.Context, rule *types.Rule, runtime variable.IRuntime) error
    
    // 加载规则组
    LoadRuleGroup(content []byte) error
}
```

#### 2. Variable Runtime (变量运行时)
```go
type IRuntime interface {
    // 设置变量值
    Set(path string, value interface{}) error
    
    // 获取变量值
    Get(path string) (interface{}, error)
    
    // 计算表达式
    Eval(expression string) (interface{}, error)
    
    // 检查变量是否存在
    Exists(path string) bool
}
```

#### 3. Rule Executor (规则执行器)
```go
type RuleExecutor struct {
    actions   map[string]ActionFunc
    parser    *utils.ExpressionParser
    converter *utils.TypeConverter
}
```

## 📋 规则定义格式

### YAML规则配置结构
```yaml
# 规则组定义
rule_group_id: "order_pricing_rules"
name: "订单价格计算规则"
description: "处理订单价格计算的业务规则"
version: "1.0.0"

# 元数据定义
metadata:
  input:
    variables:
      - name: "order_info"
        type: "object"
        description: "订单信息"
      - name: "member_info"
        type: "object"
        description: "会员信息"
  
  output:
    variables:
      - name: "total_amount"
        type: "decimal"
        description: "订单总金额"
        default_value: "0"
      - name: "discount_amount"
        type: "decimal"
        description: "优惠金额"
        default_value: "0"

# 规则列表
rules:
  - name: "会员折扣规则"
    priority: 100
    condition: "input.member_info != null && input.member_info.level == 'VIP'"
    actions:
      - type: "calculate"
        params:
          target: "output.discount_amount"
          expression: "input.order_info.amount * 0.1"
      - type: "set"
        params:
          target: "output.total_amount"
          expression: "input.order_info.amount - output.discount_amount"
  
  - name: "普通用户规则"
    priority: 50
    condition: "input.member_info == null || input.member_info.level != 'VIP'"
    actions:
      - type: "set"
        params:
          target: "output.total_amount"
          expression: "input.order_info.amount"
```

### 规则组织结构
```mermaid
graph TB
    RuleGroup[规则组 RuleGroup] --> Metadata[元数据 Metadata]
    RuleGroup --> Rules[规则列表 Rules]
    
    Metadata --> Input[输入变量定义]
    Metadata --> Output[输出变量定义]
    
    Rules --> Rule1[规则1]
    Rules --> Rule2[规则2]
    Rules --> RuleN[规则N]
    
    Rule1 --> Condition1[条件表达式]
    Rule1 --> Actions1[动作列表]
    
    Actions1 --> Action1[动作1]
    Actions1 --> Action2[动作2]
```

## ⚙️ 执行机制

### 规则执行流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Engine as 规则引擎
    participant Runtime as 变量运行时
    participant Executor as 规则执行器
    participant Actions as 动作处理器
    
    Client->>Engine: 1. 执行规则组
    Engine->>Runtime: 2. 初始化变量运行时
    Engine->>Runtime: 3. 设置输入参数
    Engine->>Runtime: 4. 初始化输出变量默认值
    
    loop 遍历规则列表
        Engine->>Executor: 5. 执行规则
        Executor->>Runtime: 6. 计算条件表达式
        Runtime-->>Executor: 7. 返回条件结果
        
        alt 条件满足
            loop 遍历动作列表
                Executor->>Actions: 8. 执行动作
                Actions->>Runtime: 9. 更新变量值
            end
        end
    end
    
    Engine->>Runtime: 10. 获取输出结果
    Runtime-->>Engine: 11. 返回输出数据
    Engine-->>Client: 12. 返回执行结果
```

### 变量命名空间
```go
// 变量命名空间定义
const (
    InputNamespace  = "input"   // 输入变量命名空间
    OutputNamespace = "output"  // 输出变量命名空间
    TempNamespace   = "temp"    // 临时变量命名空间
    ContextNamespace = "context" // 上下文变量命名空间
)

// 变量路径示例
// input.order_info.amount        - 输入订单金额
// output.total_amount            - 输出总金额
// temp.calculated_discount       - 临时计算的折扣
// context.current_time           - 上下文当前时间
```

## 🎬 动作类型

### 支持的动作类型
```yaml
# 1. set - 设置变量值
- type: "set"
  params:
    target: "output.total_amount"
    expression: "input.order_info.amount"

# 2. calculate - 计算表达式
- type: "calculate"
  params:
    target: "output.discount_amount"
    expression: "input.order_info.amount * 0.1"

# 3. set_field - 设置对象字段
- type: "set_field"
  params:
    target: "output.order_info"
    field: "status"
    value: "calculated"

# 4. call - 调用函数
- type: "call"
  params:
    function: "calculate_tax"
    args:
      - "input.order_info.amount"
      - "input.order_info.tax_rate"
    result: "output.tax_amount"

# 5. return - 提前返回
- type: "return"
  params:
    message: "规则执行完成"
```

### 动作处理器注册
```go
// 注册自定义动作处理器
engine.RegisterAction("custom_action", func(ctx context.Context, runtime variable.IRuntime, params map[string]interface{}) error {
    // 自定义动作逻辑
    target := params["target"].(string)
    value := params["value"]
    
    return runtime.Set(target, value)
})
```

## 🔧 表达式系统

### 支持的表达式类型
```go
// 1. 算术表达式
"input.amount * 0.1"
"(input.base_price + input.service_fee) * input.quantity"

// 2. 逻辑表达式
"input.member_level == 'VIP' && input.amount > 1000"
"input.age >= 18 || input.has_guardian == true"

// 3. 比较表达式
"input.amount > 500"
"input.create_time >= context.start_time"

// 4. 字符串表达式
"input.name + '_processed'"
"'Order_' + input.order_id"

// 5. 函数调用
"max(input.amount, 100)"
"round(input.amount * 0.1, 2)"
```

### 内置函数
```go
// 数学函数
max(a, b)           // 最大值
min(a, b)           // 最小值
round(value, precision) // 四舍五入
abs(value)          // 绝对值

// 字符串函数
len(str)            // 字符串长度
upper(str)          // 转大写
lower(str)          // 转小写
contains(str, substr) // 包含判断

// 日期函数
now()               // 当前时间
date_add(date, days) // 日期加法
date_diff(date1, date2) // 日期差值

// 类型转换
to_string(value)    // 转字符串
to_number(value)    // 转数字
to_bool(value)      // 转布尔值
```

---

*文档维护者：架构师、规则引擎开发团队*
*最后更新：2025-01-06*
