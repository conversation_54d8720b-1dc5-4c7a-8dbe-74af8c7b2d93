# 核心子域功能详解

## 📖 概述

本文档详细介绍KTV ERP系统三个核心子域的具体功能实现，基于代码分析总结各子域的核心能力、技术实现和业务价值。

## 🎯 核心子域概览

### 核心子域定义
根据DDD战略设计，KTV ERP系统的核心子域包括：
1. **订单管理子域** - KTV业务的核心流程
2. **会员管理子域** - 客户关系管理核心
3. **支付管理子域** - 资金安全和交易完整性

这三个子域直接影响企业的核心竞争力和业务成功，是系统投资的重点领域。

## 🛒 订单管理子域

### 业务价值
- **直接影响营收**：每个订单都直接关联到营业收入
- **客户体验核心**：从开台到结账的完整体验决定客户满意度
- **运营效率关键**：订单处理效率直接影响门店运营效率

### 核心功能架构
```mermaid
graph TB
    subgraph "订单管理子域"
        OpenService[开台服务]
        OrderService[点单服务]
        PayService[支付服务]
        RefundService[退款服务]
        
        OpenService --> SessionMgmt[场次管理]
        OrderService --> ProductMgmt[商品管理]
        PayService --> BillMgmt[账单管理]
        RefundService --> InventoryMgmt[库存管理]
    end
```

### 详细功能实现

#### 1. 开台功能群
**V3OrderOpen（开台-后付）**
- **业务场景**：客户到店后开台，后续结账时付款
- **核心流程**：
  1. 验证房间状态（必须为空闲）
  2. 生成场次ID和订单号
  3. 计算基础房费（基于价格方案）
  4. 更新房间状态为使用中
  5. 保存订单和场次信息
  6. 处理预订关联（如有）

**V3OrderOpenPay（开台-立结）**
- **业务场景**：开台时立即支付，适用于预付费或特殊场景
- **核心流程**：开台流程 + 立即发起支付

**V3OpenContinue（续房-后付）**
- **业务场景**：在现有场次基础上延长使用时间
- **核心流程**：
  1. 验证场次状态（必须为进行中）
  2. 计算续房费用
  3. 更新场次结束时间
  4. 生成续房订单

#### 2. 点单功能群
**V3AdditionalOrder（点单-后付）**
- **业务场景**：在使用过程中追加商品订单
- **核心流程**：
  1. 验证场次状态和房间锁定状态
  2. 验证商品信息和库存
  3. 计算商品费用
  4. 更新场次总费用
  5. 保存商品订单

**V3AdditionalOrderPay（点单-立结）**
- **业务场景**：点单时立即支付
- **核心流程**：点单流程 + 立即发起支付

#### 3. 支付功能群
**V3OrderPay（支付-后付）**
- **业务场景**：对未支付订单进行结账支付
- **核心流程**：
  1. 汇总场次内所有未支付订单
  2. 计算总费用（房费+商品费用+服务费）
  3. 应用优惠和折扣
  4. 生成支付账单
  5. 发起支付流程

#### 4. 退款功能群
**V3OrderRefundDo（订单退款）**
- **业务场景**：客户要求退款或商品退回
- **支持模式**：
  - 现金退款：直接现金退还
  - 原路返回：通过原支付渠道退款
- **核心流程**：
  1. 验证退款权限和金额
  2. 计算退款分配
  3. 创建退款订单和账单
  4. 处理库存回滚
  5. 发起退款支付

### 技术特色
- **流程引擎集成**：开台等关键流程通过流程引擎编排
- **实时计费**：基于时间和商品的动态费用计算
- **状态机管理**：订单、场次、房间状态的严格管理
- **事务保证**：关键操作采用数据库事务保证一致性

## 👥 会员管理子域

### 业务价值
- **客户粘性提升**：通过会员体系增强客户忠诚度
- **精准营销基础**：会员数据支撑精准营销活动
- **复购率提升**：会员权益和积分体系促进复购

### 核心功能架构
```mermaid
graph TB
    subgraph "会员管理子域"
        RegisterService[注册服务]
        RechargeService[充值服务]
        ConsumeService[消费服务]
        PointsService[积分服务]
        
        RegisterService --> MemberProfile[会员档案]
        RechargeService --> BalanceMgmt[余额管理]
        ConsumeService --> ConsumptionRecord[消费记录]
        PointsService --> LevelMgmt[等级管理]
    end
```

### 详细功能实现

#### 1. 会员注册功能群
**RegisterMember（普通会员注册）**
- **业务场景**：新客户注册成为会员
- **核心流程**：
  1. 验证手机号唯一性
  2. 生成会员卡号
  3. 设置初始等级和权益
  4. 保存会员基础信息
  5. 创建门店关联关系

**RegisterMemberWithPhysicalCard（实体卡注册）**
- **业务场景**：通过实体卡注册会员
- **核心流程**：
  1. 验证实体卡状态（未使用）
  2. 绑定实体卡号到会员
  3. 更新实体卡状态为已使用
  4. 完成会员注册流程

#### 2. 会员充值功能群
**V3MemberRecharge（会员充值）**
- **业务场景**：会员卡余额充值
- **支持方式**：现金、微信、支付宝、银行卡等
- **核心流程**：
  1. 验证会员卡状态和充值金额
  2. 计算赠金（根据充值规则）
  3. 创建充值账单和支付记录
  4. 更新会员余额（本金+赠金分离）
  5. 处理第三方支付（如需要）

**V3OpenCard（会员开卡）**
- **业务场景**：新会员开卡并首次充值
- **核心流程**：会员注册 + 首次充值

#### 3. 会员消费功能群
**会员卡消费**
- **业务场景**：使用会员卡余额支付订单
- **消费规则**：
  - 优先使用赠金
  - 赠金不足时使用本金
  - 支持部分会员卡支付

**积分管理**
- **积分获取**：消费时按比例获取积分
- **积分消费**：积分兑换商品或优惠
- **积分过期**：定期清理过期积分

#### 4. 会员信息管理
**会员档案管理**
- 基础信息：姓名、手机、生日、性别
- 消费统计：总消费金额、消费次数
- 积分记录：当前积分、历史积分变动

**会员卡管理**
- 卡状态：正常、冻结、注销
- 余额管理：本金、赠金分离
- 有效期管理：卡片有效期控制

### 技术特色
- **流程引擎集成**：会员注册通过流程引擎编排
- **多卡类型支持**：虚拟卡、实体卡、等级卡
- **余额分离管理**：本金、赠金独立管理和使用规则
- **积分体系**：完整的积分生命周期管理

## 💳 支付管理子域

### 业务价值
- **资金安全保障**：确保每笔交易的资金安全
- **交易完整性**：保证支付流程的完整性和一致性
- **多渠道支持**：支持多种支付方式满足不同客户需求

### 核心功能架构
```mermaid
graph TB
    subgraph "支付管理子域"
        PayGateway[支付网关]
        BillMgmt[账单管理]
        RefundMgmt[退款管理]
        ReconciliationMgmt[对账管理]
        
        PayGateway --> MultiChannel[多渠道支付]
        BillMgmt --> PayRecord[支付记录]
        RefundMgmt --> RefundRecord[退款记录]
        ReconciliationMgmt --> FinancialReport[财务报表]
    end
```

### 详细功能实现

#### 1. 支付处理功能群
**多渠道支付支持**
- **现金支付**：直接现金收款，无需第三方
- **微信支付**：微信扫码或刷卡支付
- **支付宝支付**：支付宝扫码或刷卡支付
- **银行卡支付**：POS机刷卡支付
- **会员卡支付**：使用会员卡余额支付
- **乐刷支付**：集成乐刷支付平台

**支付网关设计**
```go
// 统一支付网关接口
func (s *PayServiceImpl) TransformPayGate(ctx context.Context, reqDto *req.QueryOrderPayReqDto, payBill *po.PayBill) (*vo.PayResultVO, error) {
    switch *reqDto.PayType {
    case _const.PAY_TYPE_RECORD_CASH:
        return paysdk.CashPay(ctx, reqDto)
    case _const.PAY_TYPE_RECORD_WECHAT:
        return paysdk.WechatRecordPay(ctx, reqDto)
    case _const.PAY_TYPE_LESHUA_BSHOWQR:
        return paysdk.LeshuaPay(ctx, reqDto)
    case _const.PAY_TYPE_MEMBER_CARD:
        return s.memberCardPay(ctx, reqDto)
    }
}
```

#### 2. 账单管理功能群
**PayBill（支付账单）**
- **账单生成**：每次支付生成唯一账单
- **状态管理**：未支付、支付中、已支付、已退款
- **金额记录**：总金额、实付金额、找零金额

**PayRecord（支付记录）**
- **支付明细**：记录每种支付方式的具体金额
- **支付状态**：成功、失败、处理中
- **第三方信息**：第三方支付平台的交易号

**OrderAndPay（订单支付关联）**
- **关联管理**：订单与支付账单的多对多关联
- **分摊计算**：多订单支付时的金额分摊

#### 3. 退款处理功能群
**退款方式支持**
- **现金退款**：直接现金退还，适用于现金支付
- **原路返回**：通过原支付渠道退款，适用于第三方支付

**退款计算逻辑**
- **按比例分摊**：多种支付方式时按比例退款
- **部分退款**：支持按商品或金额进行部分退款
- **库存回滚**：退款时自动回滚相关库存

#### 4. 对账管理功能群
**日结对账**
- **营业日结**：每日营业结束后的资金汇总
- **支付方式统计**：各种支付方式的收款统计
- **异常订单处理**：处理支付异常的订单

**第三方对账**
- **支付查询**：主动查询第三方支付状态
- **差异处理**：处理本地记录与第三方的差异
- **补单机制**：自动补充遗漏的支付记录

### 技术特色
- **多渠道统一**：统一的支付接口适配不同支付方式
- **异步处理**：支付回调异步处理，提升响应速度
- **事务一致性**：支付、退款操作保证数据一致性
- **精确计算**：支持复杂的费用计算和分摊逻辑
- **安全可靠**：完善的风控机制和异常处理

## 🔗 子域间协作

### 订单与支付协作
- 订单生成支付账单
- 支付完成更新订单状态
- 退款时同步更新订单和库存

### 订单与会员协作
- 开台时识别会员身份
- 消费时累积会员积分
- 会员卡支付时扣减余额

### 会员与支付协作
- 会员充值通过支付流程
- 会员卡消费作为支付方式
- 支付完成更新会员余额

## 📊 核心指标

### 订单管理指标
- 开台成功率：> 99.5%
- 订单处理时间：< 3秒
- 退款处理时间：< 5分钟

### 会员管理指标
- 会员注册成功率：> 99%
- 充值成功率：> 98%
- 会员活跃度：月活跃率 > 60%

### 支付管理指标
- 支付成功率：> 99%
- 支付响应时间：< 2秒
- 对账准确率：> 99.9%

---

*文档维护者：技术架构师、业务分析师*
*最后更新：2025-01-06*
