# 环境搭建

本文档详细介绍KTV ERP系统的开发环境搭建步骤，帮助新加入的开发者快速搭建完整的开发环境。

## 🎯 环境要求

### 系统要求
- **操作系统**：macOS 10.15+、Ubuntu 18.04+、Windows 10+
- **内存**：8GB以上（推荐16GB）
- **磁盘空间**：20GB以上可用空间
- **网络**：稳定的互联网连接

### 软件版本要求
- **Go**：1.23.0+
- **MySQL**：8.0+
- **Redis**：6.0+
- **Git**：2.30+
- **Docker**：20.0+（可选）

## 🔧 基础环境安装

### 1. Go语言环境

#### macOS安装
```bash
# 使用Homebrew安装
brew install go

# 或者下载官方安装包
# https://golang.org/dl/
```

#### Ubuntu安装
```bash
# 下载Go安装包
wget https://golang.org/dl/go1.23.0.linux-amd64.tar.gz

# 解压到/usr/local
sudo tar -C /usr/local -xzf go1.23.0.linux-amd64.tar.gz

# 添加到PATH
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

#### Windows安装
1. 下载官方安装包：https://golang.org/dl/
2. 运行安装程序，按提示完成安装
3. 验证安装：打开命令提示符，运行 `go version`

#### 配置Go环境
```bash
# 设置GOPROXY（国内用户推荐）
go env -w GOPROXY=https://goproxy.cn,direct

# 设置GOSUMDB
go env -w GOSUMDB=sum.golang.google.cn

# 验证配置
go env GOPROXY
go env GOSUMDB
```

### 2. MySQL数据库

#### macOS安装
```bash
# 使用Homebrew安装
brew install mysql

# 启动MySQL服务
brew services start mysql

# 安全配置
mysql_secure_installation
```

#### Ubuntu安装
```bash
# 更新包列表
sudo apt update

# 安装MySQL
sudo apt install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### Docker安装（推荐）
```bash
# 拉取MySQL镜像
docker pull mysql:8.0

# 运行MySQL容器
docker run --name mysql-dev \
  -e MYSQL_ROOT_PASSWORD=root123 \
  -e MYSQL_DATABASE=ktv_erp \
  -e MYSQL_USER=ktv_user \
  -e MYSQL_PASSWORD=ktv_pass \
  -p 3306:3306 \
  -d mysql:8.0

# 验证安装
docker exec -it mysql-dev mysql -u root -p
```

### 3. Redis缓存

#### macOS安装
```bash
# 使用Homebrew安装
brew install redis

# 启动Redis服务
brew services start redis

# 测试连接
redis-cli ping
```

#### Ubuntu安装
```bash
# 安装Redis
sudo apt install redis-server

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 测试连接
redis-cli ping
```

#### Docker安装（推荐）
```bash
# 拉取Redis镜像
docker pull redis:6.2

# 运行Redis容器
docker run --name redis-dev \
  -p 6379:6379 \
  -d redis:6.2 redis-server --appendonly yes

# 测试连接
docker exec -it redis-dev redis-cli ping
```

## 📁 项目环境搭建

### 1. 克隆项目代码
```bash
# 克隆项目仓库
<NAME_EMAIL>:wow/erp-lt-vv.git

# 进入项目目录
cd erp-lt-vv

# 查看项目结构
tree -L 2
```

### 2. 安装项目依赖
```bash
# 下载依赖包
go mod download

# 验证依赖
go mod verify

# 整理依赖
go mod tidy
```

### 3. 配置文件设置

#### 复制配置文件
```bash
# 复制本地配置文件
cp conf/config.yaml conf/local_config.yaml

# 编辑本地配置
vim conf/local_config.yaml
```

#### 配置文件示例
```yaml
# conf/local_config.yaml
server:
  port: 8080
  mode: debug

database:
  mysql:
    host: localhost
    port: 3306
    username: ktv_user
    password: ktv_pass
    database: ktv_erp
    charset: utf8mb4
    max_idle_conns: 10
    max_open_conns: 100

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10

log:
  level: debug
  format: json
  output: stdout
```

### 4. 数据库初始化

#### 创建数据库
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE ktv_erp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'ktv_user'@'localhost' IDENTIFIED BY 'ktv_pass';

-- 授权
GRANT ALL PRIVILEGES ON ktv_erp.* TO 'ktv_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 运行数据库迁移
```bash
# 生成数据访问层代码
make dal

# 运行数据库迁移（如果有）
# go run cmd/migrate/main.go
```

### 5. 启动项目服务

#### 本地启动
```bash
# 方式1：直接运行
go run main.go -c conf/local_config.yaml

# 方式2：使用Makefile
make run

# 方式3：编译后运行
make build
./bin/voderpltvv -c conf/local_config.yaml
```

#### 验证服务启动
```bash
# 检查服务状态
curl http://localhost:8080/api/health

# 查看API文档
open http://localhost:8080/swagger/index.html
```

## 🛠️ 开发工具配置

### 1. IDE配置

#### GoLand配置
1. 打开项目：File → Open → 选择项目目录
2. 配置Go SDK：Settings → Go → GOROOT
3. 配置数据库：Database → + → MySQL
4. 配置运行配置：Run → Edit Configurations

#### VS Code配置
```json
// .vscode/settings.json
{
  "go.gopath": "",
  "go.goroot": "",
  "go.toolsGopath": "",
  "go.useLanguageServer": true,
  "go.formatTool": "goimports",
  "go.lintTool": "golangci-lint",
  "go.testFlags": ["-v"],
  "go.coverOnSave": true
}
```

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Server",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/main.go",
      "args": ["-c", "conf/local_config.yaml"],
      "env": {},
      "cwd": "${workspaceFolder}"
    }
  ]
}
```

### 2. Git配置
```bash
# 配置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 配置SSH密钥
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
cat ~/.ssh/id_rsa.pub # 复制公钥到Git服务器

# 配置Git别名
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
```

### 3. 调试工具

#### Delve调试器
```bash
# 安装Delve
go install github.com/go-delve/delve/cmd/dlv@latest

# 启动调试
dlv debug main.go -- -c conf/local_config.yaml

# 在代码中设置断点
(dlv) break main.main
(dlv) continue
```

#### 性能分析工具
```bash
# 安装pprof工具
go install github.com/google/pprof@latest

# 启动性能分析
go tool pprof http://localhost:8080/debug/pprof/profile
```

## 🐳 Docker开发环境

### 1. Docker Compose配置
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ktv-mysql-dev
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: ktv_erp
      MYSQL_USER: ktv_user
      MYSQL_PASSWORD: ktv_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6.2
    container_name: ktv-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ktv-app-dev
    ports:
      - "8080:8080"
    volumes:
      - .:/app
    depends_on:
      - mysql
      - redis
    environment:
      - CONFIG_FILE=/app/conf/docker_config.yaml

volumes:
  mysql_data:
  redis_data:
```

### 2. 启动Docker环境
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f app

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

## 🔍 常见问题解决

### 1. Go模块问题
```bash
# 清理模块缓存
go clean -modcache

# 重新下载依赖
go mod download

# 检查模块完整性
go mod verify
```

### 2. 数据库连接问题
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -tlnp | grep 3306

# 测试数据库连接
mysql -h localhost -P 3306 -u ktv_user -p
```

### 3. Redis连接问题
```bash
# 检查Redis服务状态
sudo systemctl status redis

# 测试Redis连接
redis-cli -h localhost -p 6379 ping
```

### 4. 端口冲突问题
```bash
# 查看端口占用
lsof -i :8080

# 杀死占用进程
kill -9 <PID>

# 修改配置文件端口
vim conf/local_config.yaml
```

## ✅ 环境验证清单

- [ ] Go版本 ≥ 1.23.0
- [ ] MySQL服务正常运行
- [ ] Redis服务正常运行
- [ ] 项目代码克隆成功
- [ ] 依赖包下载完成
- [ ] 配置文件设置正确
- [ ] 数据库连接成功
- [ ] 服务启动成功
- [ ] API接口可访问
- [ ] Swagger文档可查看

---

*文档维护者：开发团队*
*最后更新：2025-01-06*
