<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>KTV ERP系统 Wiki</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="KTV ERP系统技术文档中心">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
  <style>
    :root {
      --theme-color: #42b883;
      --sidebar-width: 300px;
    }
    
    .sidebar {
      background: #f8f9fa;
    }
    
    .sidebar-nav > ul > li > a {
      font-weight: 600;
      color: #2c3e50;
    }
    
    .app-name-link {
      color: #42b883 !important;
      font-weight: bold;
    }
    
    .markdown-section h1 {
      color: #2c3e50;
      border-bottom: 2px solid #42b883;
      padding-bottom: 10px;
    }
    
    .markdown-section h2 {
      color: #34495e;
      border-left: 4px solid #42b883;
      padding-left: 10px;
    }
    
    .markdown-section blockquote {
      border-left: 4px solid #42b883;
      background: #f8f9fa;
    }
    
    .markdown-section code {
      background: #f1f2f3;
      color: #e96900;
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    .markdown-section pre {
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 6px;
    }
    
    .markdown-section table {
      border-collapse: collapse;
      margin: 1em 0;
    }
    
    .markdown-section table th,
    .markdown-section table td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
    }
    
    .markdown-section table th {
      background: #f6f8fa;
      font-weight: 600;
    }
    
    .pagination-item-label {
      color: #42b883;
    }
    
    .search .input-wrap {
      border: 1px solid #42b883;
    }
    
    .search .results-panel {
      border: 1px solid #e1e4e8;
    }
  </style>
</head>
<body>
  <div id="app">加载中...</div>
  <script>
    window.$docsify = {
      name: 'KTV ERP系统 Wiki',
      repo: 'https://g.ktvsky.com/wow/erp-lt-vv',
      loadSidebar: '_sidebar.md',
      loadNavbar: false,
      subMaxLevel: 3,
      auto2top: true,
      homepage: 'README.md',
      search: {
        maxAge: 86400000,
        paths: 'auto',
        placeholder: '搜索文档...',
        noData: '没有找到相关内容',
        depth: 6,
        hideOtherSidebarContent: false
      },
      copyCode: {
        buttonText: '复制代码',
        errorText: '复制失败',
        successText: '已复制'
      },
      pagination: {
        previousText: '上一页',
        nextText: '下一页',
        crossChapter: true,
        crossChapterText: true
      },
      count: {
        countable: true,
        fontsize: '0.9em',
        color: 'rgb(90,90,90)',
        language: 'chinese'
      },
      plugins: [
        function(hook, vm) {
          hook.beforeEach(function (html) {
            var url = 'https://g.ktvsky.com/wow/erp-lt-vv/blob/main/docs/wiki/' + vm.route.file;
            var editHtml = '[:memo: 编辑此页](' + url + ')\n';
            return editHtml + html;
          });
          
          hook.doneEach(function() {
            // 添加返回顶部按钮
            var backToTop = document.createElement('div');
            backToTop.innerHTML = '↑';
            backToTop.style.cssText = `
              position: fixed;
              bottom: 20px;
              right: 20px;
              width: 40px;
              height: 40px;
              background: #42b883;
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              font-size: 20px;
              font-weight: bold;
              box-shadow: 0 2px 10px rgba(0,0,0,0.2);
              transition: all 0.3s ease;
              z-index: 1000;
            `;
            
            backToTop.onclick = function() {
              window.scrollTo({ top: 0, behavior: 'smooth' });
            };
            
            // 移除之前的按钮
            var existingButton = document.querySelector('.back-to-top');
            if (existingButton) {
              existingButton.remove();
            }
            
            backToTop.className = 'back-to-top';
            document.body.appendChild(backToTop);
            
            // 滚动时显示/隐藏按钮
            window.onscroll = function() {
              if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
                backToTop.style.opacity = '1';
              } else {
                backToTop.style.opacity = '0';
              }
            };
          });
        }
      ]
    }
  </script>
  
  <!-- Docsify v4 -->
  <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
  
  <!-- 搜索插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  
  <!-- 代码复制插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code@2"></script>
  
  <!-- 分页插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination@2/dist/docsify-pagination.min.js"></script>
  
  <!-- 字数统计插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-count@latest/dist/countable.min.js"></script>
  
  <!-- 代码高亮 -->
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-go.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-bash.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-yaml.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-json.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-sql.min.js"></script>
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-docker.min.js"></script>
  
  <!-- Mermaid 图表支持 -->
  <script src="//cdn.jsdelivr.net/npm/mermaid@8/dist/mermaid.min.js"></script>
  <script>
    mermaid.initialize({ startOnLoad: true });
  </script>
  <script src="//cdn.jsdelivr.net/npm/docsify-mermaid@latest/dist/docsify-mermaid.js"></script>
  
  <!-- 外部链接插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/external-script.min.js"></script>
  
  <!-- Zoom 图片插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/zoom-image.min.js"></script>
</body>
</html>
