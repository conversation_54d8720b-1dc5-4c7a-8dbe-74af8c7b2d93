# KTV ERP系统 Wiki 导航

## 🏠 [首页](README.md)

## 📖 项目概述
- [概述](01-project-overview/README.md)
- [项目介绍](01-project-overview/project-introduction.md)
- [技术栈](01-project-overview/tech-stack.md)
- [系统架构](01-project-overview/system-architecture.md)
- [目录结构](01-project-overview/directory-structure.md)

## 💼 核心业务模块
- [模块概述](02-business-modules/README.md)
- [订单管理](02-business-modules/order-management.md)
- [会员管理](02-business-modules/member-management.md)
- [房间管理](02-business-modules/room-management.md)
- [商品管理](02-business-modules/product-management.md)
- [支付管理](02-business-modules/payment-management.md)
- [员工管理](02-business-modules/employee-management.md)

## 🏛️ 技术架构
- [架构概述](03-technical-architecture/README.md)
- [分层架构](03-technical-architecture/layered-architecture.md)
- [DDD战略设计](03-technical-architecture/ddd-strategic-design.md)
- [核心子域功能详解](03-technical-architecture/core-subdomain-functions.md)
- [支撑子域功能详解](03-technical-architecture/supporting-subdomain-functions.md)
- [DDD设计](03-technical-architecture/ddd-design.md)
- [老架构DDD改造](03-technical-architecture/legacy-ddd-transformation.md)
- [规则引擎](03-technical-architecture/rule-engine.md)
- [流程引擎](03-technical-architecture/process-engine.md)
- [数据库设计](03-technical-architecture/database-design.md)
- [API设计](03-technical-architecture/api-design.md)
- [统一响应封装](03-technical-architecture/unified-response-wrapper.md)

## 🛠️ 开发指南
- [指南概述](04-development-guide/README.md)
- [环境搭建](04-development-guide/environment-setup.md)
- [代码规范](04-development-guide/coding-standards.md)
- [开发流程](04-development-guide/development-process.md)
- [测试指南](04-development-guide/testing-guide.md)
- [部署指南](04-development-guide/deployment-guide.md)

## 📚 API文档
- [API概述](05-api-documentation/README.md)
- [接口规范](05-api-documentation/api-standards.md)
- [认证授权](05-api-documentation/authentication.md)
- [错误处理](05-api-documentation/error-handling.md)
- [接口列表](05-api-documentation/api-list.md)

## 🔧 运维指南
- [运维概述](06-operations/README.md)
- [系统监控](06-operations/monitoring.md)
- [日志管理](06-operations/logging.md)
- [性能优化](06-operations/performance.md)
- [故障排查](06-operations/troubleshooting.md)

## 📖 最佳实践
- [实践概述](07-best-practices/README.md)
- [设计模式](07-best-practices/design-patterns.md)
- [性能优化](07-best-practices/performance-optimization.md)
- [安全规范](07-best-practices/security-guidelines.md)
- [代码重构](07-best-practices/refactoring.md)

## 📝 更新日志
- [日志概述](08-changelog/README.md)
- [版本历史](08-changelog/version-history.md)
- [功能更新](08-changelog/feature-updates.md)
- [Bug修复](08-changelog/bug-fixes.md)

---

## 🔗 快速链接

### 🚀 快速开始
- [环境搭建](04-development-guide/environment-setup.md)
- [项目介绍](01-project-overview/project-introduction.md)
- [系统架构](01-project-overview/system-architecture.md)

### 📋 开发必读
- [代码规范](04-development-guide/coding-standards.md)
- [API规范](05-api-documentation/api-standards.md)
- [目录结构](01-project-overview/directory-structure.md)

### 🔧 技术深入
- [DDD设计](03-technical-architecture/ddd-design.md)
- [规则引擎](03-technical-architecture/rule-engine.md)
- [数据库设计](03-technical-architecture/database-design.md)

### 💡 业务理解
- [订单管理](02-business-modules/order-management.md)
- [会员管理](02-business-modules/member-management.md)
- [支付管理](02-business-modules/payment-management.md)

---

*最后更新：2025-01-06*
