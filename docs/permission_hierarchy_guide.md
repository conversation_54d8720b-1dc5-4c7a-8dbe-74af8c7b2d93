# 层级权限系统使用指南

## 概述

我们的权限系统结合了基于权限范围(Scope)的分级权限控制和层级权限标识符，实现了更细粒度和更灵活的权限管理。通过层级结构的资源标识符，您可以轻松地配置不同范围的权限。

## 权限系统架构

### 1. 基础权限范围(Scope)体系
系统采用五级权限范围层级体系：
- **SYSTEM(50级)**: 系统级权限，控制系统核心功能
- **PAGE(40级)**: 页面级权限，控制页面访问和客户端
- **MODULE(30级)**: 模块级权限，控制业务模块
- **ACTION(20级)**: 操作级权限，控制具体操作
- **DATA(10级)**: 数据级权限，控制数据范围

### 2. 层级权限格式
权限资源使用三级结构：`domain:action:scope`

- **domain**: 权限域（如 system, order, product）
- **action**: 操作类型（如 login, create, view, delete）  
- **scope**: 权限范围（如 web, mobile, admin, *）

### 示例
```
system:login:web      # 只能在Web端登录
system:login:mobile   # 只能在移动端登录
system:login:*        # 可以在任何端登录（通配符权限）
order:create:web      # 只能在Web端创建订单
order:*:*             # 拥有所有订单相关权限
```

## 通配符权限

使用 `*` 作为通配符可以授予更广泛的权限：

- `system:login:*` - 可以在任何端登录（web, mobile, admin等）
- `order:*:web` - 可以在Web端执行所有订单操作
- `order:*:*` - 可以在任何端执行所有订单操作
- `*:*:*` - 超级权限（不推荐）

## API使用方法

### 1. 分配层级权限给角色

```go
// 创建权限服务
permissionService, err := service.NewPermissionService(db)
if err != nil {
    log.Fatal("Failed to create permission service:", err)
}

// 给admin角色分配系统登录的全部权限
err = permissionService.AssignHierarchicalPermissionToRole(ctx, "admin", "system", "login", "*", "access", venueId)
if err != nil {
    log.Printf("Failed to assign permission: %v", err)
}

// 给web_user角色分配只能web端登录的权限  
err = permissionService.AssignHierarchicalPermissionToRole(ctx, "web_user", "system", "login", "web", "access", venueId)
if err != nil {
    log.Printf("Failed to assign permission: %v", err)
}

// 给order_manager角色分配订单管理的全部权限
err = permissionService.AssignHierarchicalPermissionToRole(ctx, "order_manager", "order", "*", "*", "access", venueId)
if err != nil {
    log.Printf("Failed to assign permission: %v", err)
}
```

### 2. 检查权限

#### 方式一：使用便捷方法
```go
// 检查用户是否可以在web端登录
hasPermission, err := permissionService.CheckPermissionWithScope(ctx, userId, venueId, "system", "login", "web")
if err != nil {
    log.Printf("Permission check failed: %v", err)
    return false
}

// 检查用户是否可以在mobile端创建订单
hasPermission, err := permissionService.CheckPermissionWithScope(ctx, userId, venueId, "order", "create", "mobile")
if err != nil {
    log.Printf("Permission check failed: %v", err)
    return false
}
```

#### 方式二：使用传统方法
```go
// 检查用户是否有 system:login:web 权限
hasPermission, err := permissionService.CheckPermission(ctx, userId, venueId, "system:login:web", "access")
if err != nil {
    log.Printf("Permission check failed: %v", err)
    return false
}
```

### 3. 与现有权限范围系统集成

```go
// 结合权限范围检查（使用现有的testdata.go中的函数）
func CheckCompletePermission(ctx context.Context, employeeId, venueId, domain, action, scope string) (bool, error) {
    // 1. 首先检查员工角色和基础权限
    role := GetEmployeeRole(employeeId)
    if role == "" {
        return false, fmt.Errorf("employee role not found")
    }
    
    // 2. 检查角色是否有对应的权限范围级别
    maxScope := GetRoleMaxScope(role)
    if !HasScopePermission(role, strings.ToUpper(domain)) {
        return false, fmt.Errorf("role %s does not have %s scope permission", role, domain)
    }
    
    // 3. 检查层级权限
    permissionService, err := service.NewPermissionService(db)
    if err != nil {
        return false, err
    }
    
    return permissionService.CheckPermissionWithScope(ctx, employeeId, venueId, domain, action, scope)
}
```

## 实际场景示例

### 场景1: 多端登录权限控制

```go
func SetupLoginPermissions(ctx context.Context, venueId string) error {
    permissionService, err := service.NewPermissionService(db)
    if err != nil {
        return err
    }
    
    // 超级管理员可以在任何端登录
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "ADMIN", "system", "login", "*", "access", venueId)
    if err != nil {
        return fmt.Errorf("failed to assign admin permissions: %w", err)
    }

    // 普通员工只能在web端登录
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "CASHIER", "system", "login", "web", "access", venueId)
    if err != nil {
        return fmt.Errorf("failed to assign cashier permissions: %w", err)
    }

    // 移动销售员只能在mobile端登录
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "WAITER", "system", "login", "mobile", "access", venueId)
    if err != nil {
        return fmt.Errorf("failed to assign waiter permissions: %w", err)
    }
    
    return nil
}

// 在登录中间件中使用
func LoginMiddleware(permissionService *service.PermissionService) gin.HandlerFunc {
    return func(c *gin.Context) {
        employeeId := c.GetHeader("X-Employee-Id")
        venueId := c.GetHeader("X-Venue-Id")
        clientType := detectClientType(c) // web, mobile, pad
        
        hasPermission, err := permissionService.CheckPermissionWithScope(
            c.Request.Context(), employeeId, venueId, "system", "login", clientType)
        
        if err != nil || !hasPermission {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "登录权限不足",
                "client_type": clientType,
            })
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 场景2: 订单权限分级管理

```go
func SetupOrderPermissions(ctx context.Context, venueId string) error {
    permissionService, err := service.NewPermissionService(db)
    if err != nil {
        return err
    }
    
    // 订单管理员：可以在任何端执行所有订单操作
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "MANAGER", "order", "*", "*", "access", venueId)
    if err != nil {
        return err
    }

    // Web订单员：只能在web端处理订单
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "CASHIER", "order", "*", "web", "access", venueId)
    if err != nil {
        return err
    }

    // 订单查看员：只能查看订单，不能修改
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "WAITER", "order", "view", "*", "access", venueId)
    if err != nil {
        return err
    }

    // 移动端订单查看员：只能在移动端查看订单
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "WAITER", "order", "view", "mobile", "access", venueId)
    if err != nil {
        return err
    }
    
    return nil
}
```

### 场景3: 商品权限控制

```go
func SetupProductPermissions(ctx context.Context, venueId string) error {
    permissionService, err := service.NewPermissionService(db)
    if err != nil {
        return err
    }
    
    // 商品管理员：完全的商品管理权限
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "MANAGER", "product", "*", "*", "access", venueId)
    if err != nil {
        return err
    }

    // 商品录入员：只能在web端创建和编辑商品
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "CASHIER", "product", "create", "web", "access", venueId)
    if err != nil {
        return err
    }
    
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "CASHIER", "product", "edit", "web", "access", venueId)
    if err != nil {
        return err
    }

    // 移动端销售：只能在移动端查看商品
    err = permissionService.AssignHierarchicalPermissionToRole(ctx, "WAITER", "product", "view", "mobile", "access", venueId)
    if err != nil {
        return err
    }
    
    return nil
}
```

## 权限继承关系

### 层级权限匹配流程
```mermaid
graph LR
    Request["权限请求<br/>system:login:web"] --> Parse["解析层级权限<br/>domain: system<br/>action: login<br/>scope: web"]
    Parse --> Check1["检查完全匹配<br/>system:login:web"]
    Check1 --> |"匹配"| Allow1["✅ 允许访问"]
    Check1 --> |"不匹配"| Check2["检查通配符匹配<br/>system:login:*"]
    Check2 --> |"匹配"| Allow2["✅ 允许访问"]
    Check2 --> |"不匹配"| Check3["检查上级通配符<br/>system:*:*"]
    Check3 --> |"匹配"| Allow3["✅ 允许访问"]
    Check3 --> |"不匹配"| Deny["❌ 拒绝访问"]
    
    Policy1["策略: system:login:web"] --> Check1
    Policy2["策略: system:login:*"] --> Check2
    Policy3["策略: system:*:*"] --> Check3
    
    style Request fill:#3498db,stroke:#333,stroke-width:2px,color:#fff
    style Parse fill:#f39c12,stroke:#333,stroke-width:2px,color:#fff
    style Allow1 fill:#2ecc71,stroke:#333,stroke-width:2px,color:#fff
    style Allow2 fill:#2ecc71,stroke:#333,stroke-width:2px,color:#fff
    style Allow3 fill:#2ecc71,stroke:#333,stroke-width:2px,color:#fff
    style Deny fill:#e74c3c,stroke:#333,stroke-width:2px,color:#fff
    style Policy1 fill:#9b59b6,stroke:#333,stroke-width:2px,color:#fff
    style Policy2 fill:#9b59b6,stroke:#333,stroke-width:2px,color:#fff
    style Policy3 fill:#9b59b6,stroke:#333,stroke-width:2px,color:#fff
```

通配符权限具有继承性：

```
拥有 system:login:* 权限的用户可以：
✓ 在web端登录 (system:login:web)
✓ 在mobile端登录 (system:login:mobile) 
✓ 在admin端登录 (system:login:admin)
✓ 在任何新增端登录 (system:login:desktop)

拥有 order:*:web 权限的用户可以：
✓ 在web端创建订单 (order:create:web)
✓ 在web端查看订单 (order:view:web)
✓ 在web端删除订单 (order:delete:web)
✗ 在mobile端操作订单 (order:*:mobile)
```

## 与现有角色系统的集成

### 角色权限范围映射
结合现有的权限范围体系，不同角色的最大权限范围：

| 角色     | 最高权限范围 | 权限级别 | 层级权限示例                    | 客户端访问           |
|----------|-------------|----------|--------------------------------|---------------------|
| ADMIN    | SYSTEM      | 50       | system:*:*                     | PC + Pad + Mobile   |
| MANAGER  | PAGE        | 40       | system:login:*, order:*:*      | PC + Pad + Mobile   |
| CASHIER  | MODULE      | 30       | order:*:web, product:view:*    | PC + Pad + Mobile   |
| WAITER   | ACTION      | 20       | order:view:mobile              | Pad + Mobile        |
| CLEANER  | DATA        | 10       | system:login:mobile            | Mobile only         |

### 检查综合权限
```go
func CheckComprehensivePermission(ctx context.Context, employeeId, venueId, domain, action, scope string) (bool, error) {
    // 1. 检查员工角色
    role := GetEmployeeRole(employeeId)
    if role == "" {
        return false, fmt.Errorf("employee role not found")
    }
    
    // 2. 检查角色是否有对应的权限范围
    if !HasScopePermission(role, strings.ToUpper(domain)) {
        return false, fmt.Errorf("role %s does not have %s scope permission", role, domain)
    }
    
    // 3. 检查客户端权限
    clientTypeMap := map[string]string{
        "web": TestClientTypePC,
        "mobile": TestClientTypeMobile,
        "pad": TestClientTypePad,
    }
    
    if clientType, exists := clientTypeMap[scope]; exists {
        if !HasClientPermission(role, clientType) {
            return false, fmt.Errorf("role %s does not have %s client permission", role, clientType)
        }
    }
    
    // 4. 检查层级权限
    permissionService, err := service.NewPermissionService(model.DBMaster.Self)
    if err != nil {
        return false, err
    }
    
    return permissionService.CheckPermissionWithScope(ctx, employeeId, venueId, domain, action, scope)
}
```

## 最佳实践

1. **最小权限原则**: 只给用户必需的最小权限范围
2. **角色分层**: 创建不同层级的角色，避免直接给用户分配权限
3. **权限审计**: 定期检查和清理不必要的权限
4. **错误处理**: 所有权限检查都要有完整的错误处理
5. **性能优化**: 在高频调用的地方考虑权限缓存
6. **测试覆盖**: 为关键权限场景编写测试用例

## 迁移指南

如果您之前使用的是简单的权限模式，可以这样迁移：

```go
// 旧方式 - 基于角色的简单权限
if role := GetEmployeeRole(employeeId); role == "ADMIN" {
    // 允许所有操作
}

// 新方式 - 更具体的权限控制
permissionService, _ := service.NewPermissionService(db)

// 分配具体的层级权限
err := permissionService.AssignHierarchicalPermissionToRole(ctx, "ADMIN", "system", "login", "web", "access", venueId)

// 检查具体的权限
hasPermission, err := permissionService.CheckPermissionWithScope(ctx, employeeId, venueId, "system", "login", "web")

// 或者保持兼容的通配符权限
err := permissionService.AssignHierarchicalPermissionToRole(ctx, "ADMIN", "system", "login", "*", "access", venueId)
```

## 常见问题解决

### 1. 权限检查失败
```go
hasPermission, err := permissionService.CheckPermissionWithScope(ctx, userId, venueId, "system", "login", "web")
if err != nil {
    log.Printf("Permission check error: %v", err)
    // 检查数据库连接、Casbin配置等
}
if !hasPermission {
    log.Printf("Permission denied for user %s", userId)
    // 检查是否正确分配了权限
}
```

### 2. 权限分配失败
```go
err := permissionService.AssignHierarchicalPermissionToRole(ctx, roleId, domain, action, scope, operation, venueId)
if err != nil {
    log.Printf("Permission assignment failed: %v", err)
    // 检查角色是否存在、数据库连接等
}
```

### 3. 性能优化
```go
// 使用权限缓存
err := permissionService.RefreshUserPermissionCache(ctx, userId, venueId, systemType)
if err != nil {
    log.Printf("Cache refresh failed: %v", err)
}
```

## 注意事项

1. **通配符权限应谨慎使用**，避免过度授权
2. **权限检查会自动处理层级匹配**，无需额外配置
3. **系统保持向后兼容**，现有的权限检查API仍然可用
4. **建议在生产环境使用前进行充分测试**
5. **权限变更后要及时清理相关缓存**
6. **定期检查和清理无效的权限策略** 