# 动态表配置服务

## 概述

动态表配置服务是一个超轻量、零PO结构、完全由前端携带"表名+字段"驱动的动态CRUD服务。该服务允许前端通过配置化的方式对任意数据库表进行增删改查操作，无需为每个表单独编写后端代码。

## 特性

- ✅ **零PO结构**：无需为每个表定义实体类
- ✅ **动态CRUD**：支持任意表的增删改查操作
- ✅ **列缓存机制**：自动缓存表结构，提升性能
- ✅ **安全校验**：列白名单校验，防止SQL注入
- ✅ **租户隔离**：自动添加venue_id过滤
- ✅ **软删除支持**：自动检测deleted_at字段
- ✅ **复杂过滤**：支持多种操作符和$or逻辑
- ✅ **参数化查询**：所有SQL均使用参数化，确保安全
- ✅ **统一响应格式**：标准化的JSON响应

## API路由

**统一前缀**: `/vc/table/config`

| 功能       | Method | Path         | 描述           |
|------------|--------|--------------|----------------|
| 列表查询   | POST   | `/list`      | 分页查询表数据 |
| 记录详情   | POST   | `/get`       | 获取单条记录   |
| 创建记录   | POST   | `/create`    | 创建新记录     |
| 更新记录   | POST   | `/update`    | 更新现有记录   |
| 删除记录   | POST   | `/delete`    | 删除记录       |
| 批量操作   | POST   | `/batch`     | 批量删除/更新  |
| 下拉选项   | GET    | `/options`   | 获取下拉数据   |
| 页面配置   | GET    | `/pages`     | 获取页面配置   |

## 请求示例

### 1. 列表查询

```bash
POST /vc/table/config/list
Content-Type: application/json

{
  "table": "product",
  "page": 1,
  "pageSize": 20,
  "filters": [
    ["name", "like", "咖啡"],
    ["price", ">", 10],
    ["$or", [
      ["status", "=", "active"],
      ["featured", "=", true]
    ]]
  ],
  "sort": ["created_at", "desc"]
}
```

### 2. 创建记录

```bash
POST /vc/table/config/create
Content-Type: application/json

{
  "table": "product",
  "record": {
    "name": "拿铁咖啡",
    "price": 25.00,
    "category_id": 1,
    "status": "active"
  }
}
```

### 3. 更新记录

```bash
POST /vc/table/config/update
Content-Type: application/json

{
  "table": "product",
  "id": 123,
  "record": {
    "price": 28.00,
    "status": "inactive"
  }
}
```

### 4. 下拉选项

```bash
GET /vc/table/config/options?table=product_category&value=id&label=name&q=饮品
```

## Filters语法

支持以下操作符：

- `=`, `!=` - 等于、不等于
- `>`, `>=`, `<`, `<=` - 大于、大于等于、小于、小于等于
- `like` - 模糊匹配
- `in`, `not in` - 包含、不包含
- `between` - 区间查询
- `is null`, `is not null` - 空值检查

逻辑操作：
- `$or` - 或逻辑

示例：
```json
[
  ["name", "like", "咖啡"],
  ["price", "between", [10, 50]],
  ["category_id", "in", [1, 2, 3]],
  ["$or", [
    ["status", "=", "active"],
    ["featured", "=", true]
  ]]
]
```

## 响应格式

```json
{
  "code": 0,
  "message": "ok",
  "data": {
    // 具体数据
  }
}
```

错误码：
- `0` - 成功
- `1001` - 参数错误/列名非法
- `1002` - 未认证
- `1003` - 无权限
- `1004` - 表不存在
- `1005` - pages.json版本冲突
- `2001` - 校验失败
- `9000` - 服务器内部异常

## 自动字段处理

### 创建时自动添加：
- `venue_id` - 当前租户ID（如果表包含此字段）
- `created_at` - 创建时间戳
- `updated_at` - 更新时间戳

### 更新时自动添加：
- `updated_at` - 更新时间戳

### 删除时处理：
- 如果表包含 `deleted_at` 字段，执行软删除
- 否则执行硬删除

## 安全特性

1. **列白名单校验**：所有字段名都会通过INFORMATION_SCHEMA验证
2. **参数化查询**：防止SQL注入
3. **租户隔离**：自动添加venue_id过滤条件
4. **数据库权限**：建议只赋予SELECT/INSERT/UPDATE/DELETE权限

## 配置文件

### 环境变量
- `PAGES_PATH` - pages.json文件路径，默认 `./internal/pages/pages.json`

### pages.json 示例
```json
{
  "version": "1.0.0",
  "pages": {
    "product": {
      "title": "商品管理",
      "fields": [...],
      "actions": ["create", "update", "delete", "batch"],
      "filters": [...]
    }
  }
}
```

## 文件结构

```
erp_managent/
├── service/
│   └── TableConfigService.go     # 核心业务逻辑
├── controller/
│   └── TableConfigController.go  # HTTP控制器
└── router/
    └── TableConfigRoute.go       # 路由配置

internal/
└── pages/
    └── pages.json                # 页面配置文件

test/
└── table_config_test.go          # 单元测试
```

## 使用说明

1. **启动服务**：服务会自动注册到路由，无需额外配置
2. **配置pages.json**：定义前端页面结构（可选）
3. **前端集成**：按照API规范调用接口
4. **监控日志**：所有错误都会记录到日志

## 性能优化

- 列信息缓存机制，避免重复查询INFORMATION_SCHEMA
- 使用SQL_CALC_FOUND_ROWS优化分页查询
- 支持清除缓存接口：`GET /vc/table/config/clear-cache?table=表名`

## 最佳实践

1. **表设计**：建议包含 `id`, `venue_id`, `created_at`, `updated_at`, `deleted_at` 字段
2. **权限控制**：在中间件层面控制表访问权限
3. **数据校验**：在前端进行基础校验，后端进行安全校验
4. **监控告警**：监控异常错误码，特别是1004（表不存在）和9000（服务器错误）

## 扩展性

该服务设计为完全配置驱动，可以轻松扩展：
- 新增表只需前端配置，无需后端代码修改
- 支持自定义字段类型和校验规则
- 可扩展支持更多数据库类型
- 支持插件化的业务逻辑扩展 