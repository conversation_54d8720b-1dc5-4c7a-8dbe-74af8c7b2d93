# 会员存酒API接口文档

基于memberCardId实现的会员存酒查询功能，提供专门的查询接口。

## 接口列表

### 1. 查询会员存酒记录

**接口地址：** `POST /api/product-storage/member/query`

**功能说明：** 基于会员卡ID查询会员的存酒记录，支持多种查询条件、排序和分页

**请求参数：**

```json
{
  "venueId": "门店ID（必填）",
  "memberCardId": "会员卡ID",
  "memberCardNumber": "会员卡号",
  
  "productName": "商品名称（模糊查询）",
  "productType": "商品类型",
  "productId": "商品ID",
  "storageLocation": "存放位置",
  
  "storageTimeStart": "存入时间起始（时间戳）",
  "storageTimeEnd": "存入时间截止（时间戳）",
  "expireTimeStart": "到期时间起始（时间戳）",
  "expireTimeEnd": "到期时间截止（时间戳）",
  
  "onlyRemaining": "仅查询有剩余数量的记录（布尔值）",
  "onlyExpiring": "仅查询即将过期的记录（布尔值）",
  "onlyExpired": "仅查询已过期的记录（布尔值）",
  
  "sortBy": "排序字段：storageTime, expireTime, productName",
  "sortOrder": "排序方式：asc, desc（默认desc）",
  "pageNum": "页码（默认1）",
  "pageSize": "每页大小（默认20，最大100）"
}
```

**返回数据：**

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "total": "总记录数",
    "list": [
      {
        "id": "存酒记录ID",
        "orderNo": "存酒单号",
        "parentOrderNo": "父订单号",
        "productId": "商品ID",
        "productName": "商品名称",
        "productType": "商品类型",
        "productUnit": "商品单位",
        "productSpec": "商品规格",
        "quantity": "存入数量",
        "remainingQty": "剩余数量",
        "withdrawnQty": "已取数量",
        "storageLocation": "存放位置",
        "storageTime": "存入时间戳",
        "storageTimeStr": "存入时间字符串",
        "expireTime": "到期时间戳",
        "expireTimeStr": "到期时间字符串",
        "daysToExpire": "距离过期天数",
        "expiringStatus": "过期状态：normal, expiring, expired",
        "statusCode": "状态码",
        "statusName": "状态名称",
        "operatorId": "操作员ID",
        "operatorName": "操作员姓名",
        "remark": "备注"
      }
    ],
    "pageInfo": {
      "pageNum": "当前页码",
      "pageSize": "每页大小"
    },
    "memberInfo": {
      "memberCardId": "会员卡ID",
      "memberCardNumber": "会员卡号",
      "customerId": "客户ID",
      "customerName": "客户姓名",
      "phoneNumber": "电话号码"
    }
  }
}
```



## 接口特性

### 查询条件支持
- **会员标识**：支持会员卡ID或会员卡号查询
- **商品过滤**：商品名称模糊查询、商品类型精确匹配
- **时间范围**：存入时间、到期时间范围查询
- **状态过滤**：仅剩余、仅即将过期、仅已过期
- **位置查询**：指定存放位置

### 排序功能
- **存入时间排序**：按存酒时间升序/降序
- **到期时间排序**：按到期时间升序/降序
- **商品名称排序**：按商品名称字母升序/降序

### 分页机制
- 支持页码和每页大小设置
- 默认每页20条，最大100条
- 返回总记录数和分页信息

### 状态计算
- **过期状态**：自动计算normal（正常）、expiring（即将过期）、expired（已过期）
- **剩余状态**：自动计算stored（已存放）、partial（部分取用）、withdrawn（已取完）
- **天数计算**：自动计算距离过期天数



## 使用说明

1. **必填参数**：门店ID + （会员卡ID 或 会员卡号）
2. **查询优化**：建议设置合理的时间范围以提高查询性能
3. **分页建议**：大数据量时建议使用分页，避免一次性返回过多数据
4. **明细查看**：如需查看具体的存取操作明细，请使用专门的操作历史接口

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 门店ID不能为空 | 必须提供有效的门店ID |
| 400 | 会员卡ID或会员卡号至少提供一个 | 必须提供会员卡标识 |
| 500 | 查询会员存酒记录失败 | 服务器内部错误或数据库查询异常 | 