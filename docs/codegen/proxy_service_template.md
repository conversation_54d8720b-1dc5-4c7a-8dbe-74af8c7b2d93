# 代理服务生成模板

## 基本信息

```
服务对应目录: erp_client
代理服务名称：                    // 例如：会员服务代理
所属基础设施层：infrastructure/proxy
代理接口文件路径：                // 例如：infrastructure/proxy/member_proxy.go
代理实现文件路径：                // 与接口通常在同一文件
服务依赖路径：                   // 例如：erp_managent/service/impl/MemberService.go
```

## 代理描述

```
【代理概述】
// 简要描述该代理服务的核心职责，2-3句话

【设计原则】
// 代理类应对应领域概念而非数据库表
// 每个代理类应该对应一个领域概念（如会员、订单、商品等），而不是简单地映射到单一数据库表
// 代理类可能需要操作多个相关表来完成一个领域操作，这符合领域驱动设计(DDD)的原则
// 代理类作为防腐层，隔离领域模型与外部服务实现的细节
// 代理类应保持适当颗粒度，按照领域职责划分，避免单一代理承担过多职责
// 例如，会员领域可以划分为：会员基本信息代理、会员卡代理、会员支付代理、会员操作记录代理等

【服务依赖】
// 列出该代理依赖的内部服务
1. 服务名称：
   服务路径：
   依赖原因：
```

**示例（会员服务代理）：**

```
【代理概述】
会员服务代理提供对会员相关操作的领域接口，它封装了对erp_managent中会员服务实现的调用，负责数据格式转换和错误处理，是领域层与具体实现的适配器。

【设计原则】
会员服务代理对应领域概念而非数据库表
会员服务代理应该对应一个领域概念（如会员），而不是简单地映射到单一数据库表
会员服务代理可能需要操作多个相关表来完成一个领域操作，这符合领域驱动设计(DDD)的原则
会员服务代理作为防腐层，隔离领域模型与外部服务实现的细节
会员服务代理应保持适当颗粒度，按照领域职责划分，避免单一代理承担过多职责
例如，会员领域可以划分为：
- 会员基本信息代理：负责会员基础信息的CRUD操作
- 会员卡代理：负责会员卡的发放、激活、挂失等操作
- 会员支付代理：负责会员支付记录的查询和管理
- 会员操作记录代理：负责记录和查询会员相关的操作日志

【服务依赖】
1. 服务名称：MemberService
   服务路径：erp_managent/service/impl/MemberService.go
   依赖原因：提供会员信息的核心业务逻辑实现

2. 服务名称：PhysicalCardService
   服务路径：erp_managent/service/impl/PhysicalCardService.go
   依赖原因：提供会员卡物理卡操作的实现

【转换对象】
1. 会员模型（Member）：在领域模型和服务DTO之间转换
2. 会员卡信息（MemberCard）：转换会员卡相关信息
3. 查询参数（MemberQuery）：转换会员查询条件

【接口职责】
1. 提供符合领域需求的会员操作接口
2. 转发调用到实际的服务实现
3. 处理数据格式转换，屏蔽实现细节
4. 统一错误处理，转换为领域错误类型

【错误处理】
1. 服务调用错误：捕获并转换为领域错误类型
2. 参数验证错误：在调用实际服务前进行基本验证
3. 数据转换错误：处理模型转换过程中可能出现的错误
```

## 接口定义

```
【接口方法】
// 为每个方法提供名称、参数和返回值
1. 方法名：
   输入参数：
   返回值：
   功能描述：
   实现思路：

2. 方法名：
   输入参数：
   返回值：
   功能描述：
   实现思路：

3. 方法名：
   输入参数：
   返回值：
   功能描述：
   实现思路：
```

**示例（会员服务代理）：**

```
【接口方法】
1. 方法名：GetMemberById
   输入参数：ctx context.Context, id string
   返回值：(*model.Member, error)
   功能描述：根据ID获取会员信息
   实现思路：
   - 调用 MemberService.GetMemberById 获取会员DTO
   - 将DTO转换为领域模型 model.Member
   - 处理可能的错误并返回

2. 方法名：QueryMembers
   输入参数：ctx context.Context, query *model.MemberQuery
   返回值：([]*model.Member, int64, error)
   功能描述：根据条件查询会员列表
   实现思路：
   - 将领域查询条件转换为服务查询参数
   - 调用 MemberService.QueryMembers 获取结果
   - 转换返回的DTO列表为领域模型列表
   - 返回会员列表、总数和可能的错误

3. 方法名：SaveMember
   输入参数：ctx context.Context, member *model.Member
   返回值：(*model.Member, error)
   功能描述：保存会员信息（创建或更新）
   实现思路：
   - 将领域模型转换为服务DTO
   - 调用 MemberService.SaveMember 保存数据
   - 将返回的DTO转换回领域模型
   - 处理可能的错误并返回
```

## 数据转换

```
【模型转换】
// 描述领域模型和DTO之间的转换逻辑
1. 转换方向：
   来源模型：
   目标模型：
   转换字段：
   特殊处理：

2. 转换方向：
   来源模型：
   目标模型：
   转换字段：
   特殊处理：
```

**示例（会员服务代理）：**

```
【模型转换】
1. 转换方向：DTO到领域模型
   来源模型：dto.MemberDTO
   目标模型：model.Member
   转换字段：
   - Id -> Id
   - Name -> Name
   - Phone -> Phone
   - Gender -> Gender
   - BirthDate -> BirthDate
   - CardNumber -> Card.Number
   - CardLevelId -> Card.LevelId
   特殊处理：
   - 将服务层时间格式统一转换为领域模型时间格式
   - 处理可能的空值情况
   - 将扁平结构转换为嵌套结构（如Card信息）

2. 转换方向：领域模型到DTO
   来源模型：model.Member
   目标模型：dto.MemberDTO
   转换字段：
   - Id -> Id
   - Name -> Name
   - Phone -> Phone
   - Gender -> Gender
   - BirthDate -> BirthDate
   - Card.Number -> CardNumber
   - Card.LevelId -> CardLevelId
   特殊处理：
   - 确保必填字段不为空
   - 将嵌套结构扁平化（如Card信息）
   - 处理领域模型中的计算属性
```

## 代理实现

```
【依赖注入】
// 描述如何注入依赖服务
1. 依赖服务：
   注入方式：
   初始化时机：

【错误转换】
// 描述错误转换策略
1. 错误类型：
   转换规则：
   错误码映射：

【接口注册】
// 描述如何在工厂中注册该代理
1. 注册函数：
   实现方式：
```

**示例（会员服务代理）：**

```
【依赖注入】
1. 依赖服务：MemberService
   注入方式：构造函数参数注入
   初始化时机：应用启动时通过工厂函数初始化

2. 依赖服务：PhysicalCardService
   注入方式：构造函数参数注入
   初始化时机：应用启动时通过工厂函数初始化

【错误转换】
1. 错误类型：数据库错误
   转换规则：转换为领域错误类型，如 NotFoundError、ConflictError
   错误码映射：
   - SQL无结果 -> NotFoundError
   - 唯一键冲突 -> ConflictError
   - 其他数据库错误 -> InternalError

2. 错误类型：业务逻辑错误
   转换规则：保留原始错误信息，但包装为领域错误类型
   错误码映射：
   - 参数验证失败 -> InvalidArgumentError
   - 权限不足 -> ForbiddenError
   - 业务规则冲突 -> BusinessRuleViolationError

【接口注册】
1. 注册函数：RegisterMemberProxy
   实现方式：
   - 在factory.go中添加创建会员代理的方法
   - 确保正确传入所需的服务实现
   - 返回接口类型而非具体实现类型
```

## 测试设计

```
【单元测试】
// 列出需要测试的关键方法和场景
1. 测试方法：
   测试场景：
   模拟行为：
   断言内容：

2. 测试方法：
   测试场景：
   模拟行为：
   断言内容：
```

**示例（会员服务代理测试）：**

```
【单元测试】
1. 测试方法：TestGetMemberById
   测试场景：
   - 成功获取存在的会员
   - 获取不存在的会员返回错误
   - 服务调用出错的情况
   模拟行为：
   - 模拟MemberService返回有效的会员DTO
   - 模拟MemberService返回未找到错误
   - 模拟MemberService返回内部错误
   断言内容：
   - 验证返回的Member对象字段映射正确
   - 验证NotFoundError是否正确返回
   - 验证服务错误是否正确转换为领域错误

2. 测试方法：TestSaveMember
   测试场景：
   - 成功保存新会员
   - 成功更新已有会员
   - 保存会员信息不完整
   模拟行为：
   - 模拟MemberService保存并返回新会员DTO
   - 模拟MemberService更新并返回更新后的DTO
   - 模拟MemberService返回参数错误
   断言内容：
   - 验证新会员保存后ID不为空
   - 验证更新后的字段是否正确
   - 验证参数错误是否正确转换为InvalidArgumentError
```

## 代码结构示例

```go
// 代理接口
type MemberProxy interface {
    GetMemberById(ctx context.Context, id string) (*model.Member, error)
    QueryMembers(ctx context.Context, query *model.MemberQuery) ([]*model.Member, int64, error)
    SaveMember(ctx context.Context, member *model.Member) (*model.Member, error)
    // 其他方法...
}

// 代理实现
type memberProxyImpl struct {
    memberService     *impl.MemberService
    physicalCardService *impl.PhysicalCardService
}

// 构造函数
func NewMemberProxy(memberService *impl.MemberService, physicalCardService *impl.PhysicalCardService) MemberProxy {
    return &memberProxyImpl{
        memberService:     memberService,
        physicalCardService: physicalCardService,
    }
}

// 接口实现示例
func (m *memberProxyImpl) GetMemberById(ctx context.Context, id string) (*model.Member, error) {
    // 调用服务实现
    memberDTO, err := m.memberService.GetMemberById(ctx, id)
    if err != nil {
        // 错误转换
        if strings.Contains(err.Error(), "not found") {
            return nil, errors.NewNotFoundError("会员不存在", err)
        }
        return nil, errors.NewInternalError("获取会员信息失败", err)
    }
    
    // 转换DTO到领域模型
    member := &model.Member{
        Id:       memberDTO.Id,
        Name:     memberDTO.Name,
        Phone:    memberDTO.Phone,
        Gender:   memberDTO.Gender,
        BirthDate: memberDTO.BirthDate,
        // 其他字段映射...
    }
    
    return member, nil
}

// 在factory.go中注册
func RegisterMemberProxy(factory *ProxyFactory) {
    memberService := factory.GetMemberService()
    physicalCardService := factory.GetPhysicalCardService()
    factory.memberProxy = NewMemberProxy(memberService, physicalCardService)
}

func (f *ProxyFactory) GetMemberProxy() MemberProxy {
    return f.memberProxy
}
```

## 填写示例（会员服务代理）

```
代理服务名称：会员服务代理
所属基础设施层：infrastructure/proxy
代理接口文件路径：infrastructure/proxy/member_proxy.go
代理实现文件路径：infrastructure/proxy/member_proxy.go
服务依赖路径：erp_managent/service/impl/MemberService.go,erp_managent/service/impl/PhysicalCardService.go
```
