# 领域服务生成模板

## 基本信息

```
服务对应目录: erp_client
领域服务名称：                    // 例如：会员领域服务
所属业务领域：                    // 例如：member、order、room等
服务接口文件路径：                // 例如：domain/subject/business/member/service/member_domain_service.go
服务实现文件路径：                // 例如：domain/subject/business/member/service/member_domain_service_impl.go
模型文件路径：                    // 例如：domain/subject/business/member/model/member.go
仓储接口文件路径：                // 例如：domain/subject/business/member/repository/member_repository.go
```

## 领域描述

```
【领域概述】
// 简要描述该领域的核心概念和责任范围，3-5句话

【核心实体】
// 列出该领域中的核心实体
1. 
2. 
3. 

【领域规则】
// 列出该领域需要遵循的关键业务规则
1. 
2. 
3. 

【不变量】
// 列出在任何情况下都必须保持的领域不变量
1. 
2. 
3. 
```

**示例（会员领域）：**

```
【领域概述】
会员领域负责管理会员的生命周期、会员卡等级、权益和会员与场所的关联关系。它确保会员信息的完整性和一致性，并提供会员注册、查询、卡级别变更等核心功能。该领域是连接用户与业务系统的关键纽带，支持会员积分和余额管理。

【核心实体】
1. 会员（Member）：表示系统中的会员个体，包含基本信息和会员卡信息
2. 会员卡等级（MemberCardLevel）：定义会员的等级和对应的权益
3. 实体卡（PhysicalCard）：物理会员卡，与会员关联
4. 场所与会员关联（VenueAndMember）：记录会员与具体场所的关联关系

【领域规则】
1. 同一个手机号在同一场所下只能注册一个会员
2. 会员卡余额不能为负数
3. 会员卡等级变更必须记录操作历史
4. 实体卡只能绑定给一个会员
5. 会员账户变动需要记录操作日志

【不变量】
1. 会员ID一旦创建不可变更
2. 会员卡号全局唯一且不可修改
3. 会员的积分和余额操作必须保持事务一致性
```

## 接口定义

```
【接口方法】
// 为每个方法提供名称、参数和返回值
1. 方法名：
   输入参数：
   返回值：
   功能描述：
   业务规则：

2. 方法名：
   输入参数：
   返回值：
   功能描述：
   业务规则：

3. 方法名：
   输入参数：
   返回值：
   功能描述：
   业务规则：
```

**示例（会员领域服务）：**

```
【接口方法】
1. 方法名：RegisterMember
   输入参数：ctx context.Context, memberInfo *model.MemberRegisterInfo
   返回值：(*model.Member, error)
   功能描述：注册新会员，创建会员信息并关联到指定场所
   业务规则：
   - 手机号、姓名为必填信息
   - 同一手机号在同一场所下不能重复注册
   - 注册成功后生成唯一会员卡号
   - 根据配置的初始等级设置会员卡等级

2. 方法名：ValidateMemberInfo
   输入参数：ctx context.Context, memberVerifyInfo *model.MemberVerifyInfo
   返回值：(*model.VerifyResult, error)
   功能描述：验证会员信息的有效性，用于注册前检查
   业务规则：
   - 验证手机号格式是否正确
   - 检查会员是否已在指定场所存在
   - 返回验证结果和详细信息

3. 方法名：BindPhysicalCard
   输入参数：ctx context.Context, memberId string, cardNumber string
   返回值：(bool, error)
   功能描述：将实体卡绑定到指定会员
   业务规则：
   - 实体卡必须存在且状态为未绑定
   - 会员必须存在且有效
   - 绑定后更新实体卡状态为已绑定
   - 记录绑卡操作日志
```

## 领域模型

```
【聚合根】
// 列出该领域中的聚合根
1. 聚合根名称：
   核心职责：
   包含实体：
   值对象：
   领域事件：

【实体】
// 列出领域中的实体
1. 实体名称：
   属性列表：
   - 属性名：        类型：        描述：        约束：
   - 属性名：        类型：        描述：        约束：
   行为：
   - 方法名：        描述：        前置条件：    后置条件：

2. 实体名称：
   属性列表：
   - 属性名：        类型：        描述：        约束：
   - 属性名：        类型：        描述：        约束：
   行为：
   - 方法名：        描述：        前置条件：    后置条件：

【值对象】
// 列出领域中的值对象
1. 值对象名称：
   属性列表：
   - 属性名：        类型：        描述：
   - 属性名：        类型：        描述：
   不变性：

2. 值对象名称：
   属性列表：
   - 属性名：        类型：        描述：
   - 属性名：        类型：        描述：
   不变性：

【领域事件】
// 列出领域中的事件
1. 事件名称：
   属性列表：
   - 属性名：        类型：        描述：
   触发条件：
   处理者：
```

**示例（会员领域）：**

```
【聚合根】
1. 聚合根名称：Member（会员）
   核心职责：管理会员信息及其关联的卡信息和会员权益
   包含实体：会员卡操作记录（MemberCardOperation）
   值对象：会员卡信息（CardInfo）、会员等级（LevelInfo）
   领域事件：MemberRegisteredEvent、CardLevelChangedEvent

【实体】
1. 实体名称：Member（会员）
   属性列表：
   - Id：           string：       会员唯一标识：    不可修改，全局唯一
   - Name：         string：       会员姓名：        不能为空
   - Phone：        string：       手机号码：        符合手机号格式，唯一索引
   - CardNumber：   string：       会员卡号：        创建后不可修改，全局唯一
   - CardLevelId：  string：       会员卡等级ID：    必须是有效的等级ID
   - Balance：      int64：        会员卡余额：      不能为负数
   - Points：       int：          会员积分：        不能为负数
   - Gender：       string：       性别：            可选值：MALE/FEMALE/UNKNOWN
   - Birthday：     int64：        生日时间戳：      可为空
   行为：
   - ChangeCardLevel：  变更会员卡等级：  会员状态正常：  更新等级并记录变更历史
   - AddBalance：       增加余额：        金额大于0：     更新余额并记录操作历史
   - ConsumeBalance：   消费余额：        余额充足：      扣减余额并记录消费记录

2. 实体名称：PhysicalCard（实体卡）
   属性列表：
   - Id：           string：       卡唯一标识：      不可修改，全局唯一
   - CardNumber：   string：       卡号：            不可修改，全局唯一
   - State：        int：          卡状态：          1-未绑定 2-已绑定 3-已作废
   - MemberId：     string：       绑定的会员ID：    只能绑定一个会员
   行为：
   - BindToMember：     绑定到会员：      卡状态为未绑定：  更新状态为已绑定并关联会员ID
   - Invalidate：       作废卡：          卡必须存在：      更新状态为已作废

【值对象】
1. 值对象名称：CardInfo（卡信息）
   属性列表：
   - CardNumber：   string：       卡号
   - CardType：     string：       卡类型（虚拟卡/实体卡）
   - CardLevelName：string：       卡等级名称
   - IssueTime：    int64：        发卡时间戳
   不变性：一旦创建，所有字段不可修改

2. 值对象名称：MemberVerifyInfo（会员验证信息）
   属性列表：
   - Phone：        string：       手机号码
   - VenueId：      string：       场所ID
   - Name：         string：       会员姓名
   不变性：验证过程中不可修改

【领域事件】
1. 事件名称：MemberRegisteredEvent（会员注册事件）
   属性列表：
   - MemberId：     string：       会员ID
   - VenueId：      string：       场所ID
   - RegisterTime： int64：        注册时间戳
   - Operator：     string：       操作人
   触发条件：会员成功注册
   处理者：会员积分系统、营销系统、通知系统

2. 事件名称：CardLevelChangedEvent（会员卡等级变更事件）
   属性列表：
   - MemberId：     string：       会员ID
   - OldLevelId：   string：       原等级ID
   - NewLevelId：   string：       新等级ID
   - ChangeTime：   int64：        变更时间戳
   - Reason：       string：       变更原因
   触发条件：会员卡等级变更
   处理者：权益系统、通知系统
```

## 仓储接口

```
【仓储方法】
// 为每个仓储方法提供名称、参数和返回值
1. 方法名：
   输入参数：
   返回值：
   功能描述：

2. 方法名：
   输入参数：
   返回值：
   功能描述：

3. 方法名：
   输入参数：
   返回值：
   功能描述：
```

**示例（会员仓储）：**

```
【仓储方法】
1. 方法名：Save
   输入参数：ctx context.Context, member *model.Member
   返回值：(*model.Member, error)
   功能描述：保存会员信息，如果ID已存在则更新，否则创建新会员

2. 方法名：FindById
   输入参数：ctx context.Context, id string
   返回值：(*model.Member, error)
   功能描述：根据ID查询会员信息

3. 方法名：FindByPhone
   输入参数：ctx context.Context, phone string
   返回值：(*model.Member, error)
   功能描述：根据手机号查询会员信息

4. 方法名：FindByCardNumber
   输入参数：ctx context.Context, cardNumber string
   返回值：(*model.Member, error)
   功能描述：根据卡号查询会员信息

5. 方法名：Update
   输入参数：ctx context.Context, member *model.Member
   返回值：(*model.Member, error)
   功能描述：更新会员信息
```

## 领域服务实现

```
【依赖仓储】
// 列出领域服务依赖的仓储接口
1. 仓储名称：
   依赖原因：
   使用方法：

【依赖服务】
// 列出领域服务依赖的其他领域服务
1. 服务名称：
   依赖原因：
   使用方法：

【关键算法】
// 列出领域服务中的关键算法或复杂业务逻辑
1. 算法名称：
   用途：
   实现思路：
   边界条件：
```

**示例（会员领域服务）：**

```
【依赖仓储】
1. 仓储名称：MemberRepository
   依赖原因：负责会员信息的持久化和查询
   使用方法：
   - Save方法用于保存会员信息
   - FindByPhone用于查找是否存在重复会员
   - FindById用于获取会员详情

2. 仓储名称：VenueAndMemberRepository
   依赖原因：管理会员与场所的关联关系
   使用方法：
   - Save方法创建会员与场所的关联
   - FindByVenueIdAndPhone查询场所下是否存在指定手机号的会员

3. 仓储名称：PhysicalCardRepository
   依赖原因：管理实体卡信息
   使用方法：
   - FindByCardNumber查询实体卡信息
   - UpdateStatus更新实体卡状态

【依赖服务】
1. 服务名称：EventPublisher
   依赖原因：发布领域事件
   使用方法：
   - PublishEvent方法发布会员注册事件
   - PublishEvent方法发布卡等级变更事件

2. 服务名称：IdGenerator
   依赖原因：生成唯一ID和卡号
   使用方法：
   - GenerateMemberId方法生成会员ID
   - GenerateCardNumber方法生成会员卡号

【关键算法】
1. 算法名称：会员卡号生成算法
   用途：生成全局唯一的会员卡号
   实现思路：
   - 使用场所编码(4位) + 时间戳(8位) + 随机数(4位)组合
   - 通过仓储层验证卡号唯一性
   边界条件：
   - 处理并发请求时需要确保唯一性
   - 卡号生成失败时需要重试

2. 算法名称：会员余额消费算法
   用途：处理会员消费时的余额扣减逻辑
   实现思路：
   - 先检查会员余额是否充足
   - 区分本金余额和赠送余额，优先使用赠送余额
   - 在事务中同时更新余额和创建消费记录
   边界条件：
   - 余额不足时拒绝消费
   - 处理并发消费请求时需要做余额锁定
```

## 测试设计

```
【单元测试】
// 列出需要测试的关键方法和场景
1. 测试方法：
   测试场景：
   断言内容：

2. 测试方法：
   测试场景：
   断言内容：

【集成测试】
// 列出需要进行的集成测试
1. 测试场景：
   测试步骤：
   验证点：

2. 测试场景：
   测试步骤：
   验证点：
```

**示例（会员领域服务测试）：**

```
【单元测试】
1. 测试方法：TestRegisterMember
   测试场景：
   - 测试正常注册会员
   - 测试重复手机号注册
   - 测试必填信息缺失注册
   断言内容：
   - 成功场景下返回的会员对象包含正确的信息
   - 重复注册场景应返回适当错误
   - 信息缺失场景应返回适当错误

2. 测试方法：TestBindPhysicalCard
   测试场景：
   - 测试正常绑定实体卡
   - 测试绑定不存在的实体卡
   - 测试绑定已绑定的实体卡
   断言内容：
   - 成功绑定后实体卡状态应更新为已绑定
   - 绑定不存在卡应返回卡不存在错误
   - 绑定已绑定卡应返回卡已绑定错误

【集成测试】
1. 测试场景：会员完整注册流程
   测试步骤：
   - 验证会员信息有效性
   - 注册会员
   - 查询会员信息
   - 验证场所关联关系
   验证点：
   - 会员信息已正确保存
   - 会员卡号已正确生成
   - 会员与场所关联已建立
   - 会员初始积分和余额已设置

2. 测试场景：会员卡升级流程
   测试步骤：
   - 查询会员当前等级
   - 执行等级升级
   - 查询更新后的会员信息
   验证点：
   - 会员等级已正确更新
   - 升级记录已保存
   - 升级事件已发布
```

## 补充说明

```
【性能考虑】
// 任何需要特别关注的性能问题

【扩展性】
// 描述如何扩展该领域服务以适应未来需求

【安全性】
// 任何安全相关的考虑

【参考资料】
// 任何有助于理解或实现的参考资料、文档或示例
```

**示例（会员领域服务）：**

```
【性能考虑】
1. 会员查询是高频操作，应考虑使用缓存优化查询性能
2. 会员卡号索引和手机号索引应设计合理，避免慢查询
3. 大规模会员数据下，应考虑分库分表策略
4. 会员注册和更新操作应考虑并发控制，避免数据不一致

【扩展性】
1. 会员模型应设计可扩展的自定义字段，满足不同业务场景的需求
2. 会员卡等级体系应支持自定义配置，便于业务调整
3. 会员服务应支持多种会员卡类型和识别方式（如二维码、NFC等）
4. 领域事件设计应允许新增事件处理器，而不影响现有功能

【安全性】
1. 会员敏感信息（如手机号）应加密存储
2. 会员余额和积分操作应有完整的审计日志
3. 应防范会员账户被盗用的风险，关键操作应有额外验证
4. 实体卡管理应有防伪和验证机制

【参考资料】
1. 《实现领域驱动设计》 - Vaughn Vernon
2. 《领域驱动设计：软件核心复杂性应对之道》 - Eric Evans
3. 会员管理系统设计参考：https://martinfowler.com/bliki/DDD_Aggregate.html
4. 会员忠诚度计划设计最佳实践：《The Loyalty Effect》 - Frederick F. Reichheld
```

---

## 填写示例（会员领域服务）

```
领域服务名称：会员领域服务
所属业务领域：member
服务接口文件路径：domain/subject/business/member/service/member_domain_service.go
服务实现文件路径：domain/subject/business/member/service/member_domain_service_impl.go
模型文件路径：domain/subject/business/member/model/member.go
仓储接口文件路径：domain/subject/business/member/repository/member_repository.go
``` 