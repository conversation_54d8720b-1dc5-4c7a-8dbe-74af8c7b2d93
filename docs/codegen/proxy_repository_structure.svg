<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="700" xmlns="http://www.w3.org/2000/svg">
    <!-- 定义样式 -->
    <defs>
        <style>
            .title { font-family: Arial; font-size: 24px; font-weight: bold; }
            .subtitle { font-family: Arial; font-size: 18px; font-style: italic; }
            .box-title { font-family: Arial; font-size: 16px; font-weight: bold; }
            .box-text { font-family: Arial; font-size: 14px; }
            .note { font-family: Arial; font-size: 14px; font-style: italic; }
            .arrow { stroke: #666; stroke-width: 2; marker-end: url(#arrowhead); }
            .dashed { stroke-dasharray: 5,5; }
            .domain-box { fill: #E1F5FE; stroke: #0288D1; stroke-width: 2; }
            .repo-box { fill: #E8F5E9; stroke: #388E3C; stroke-width: 2; }
            .db-box { fill: #FFEBEE; stroke: #D32F2F; stroke-width: 2; }
            .principle-box { fill: #FFF8E1; stroke: #FFA000; stroke-width: 2; }
        </style>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
        </marker>
    </defs>

    <!-- 标题 -->
    <text x="400" y="40" text-anchor="middle" class="title">代理仓储结构设计原则</text>
    <text x="400" y="70" text-anchor="middle" class="subtitle">领域驱动的代理类设计与颗粒度控制</text>

    <!-- 左侧：错误的设计方式 -->
    <rect x="50" y="120" width="300" height="400" rx="10" ry="10" fill="#FFEBEE" fill-opacity="0.3" stroke="#D32F2F" stroke-width="1" stroke-dasharray="5,5" />
    <text x="200" y="150" text-anchor="middle" class="box-title" fill="#D32F2F">❌ 不推荐：颗粒度过大</text>

    <!-- 大而全的会员服务 -->
    <rect x="80" y="180" width="240" height="80" rx="5" ry="5" class="domain-box" />
    <text x="200" y="210" text-anchor="middle" class="box-title">会员领域概念 (Member)</text>
    <text x="200" y="235" text-anchor="middle" class="box-text">包含所有会员相关信息和行为</text>

    <!-- 单一大型代理 -->
    <rect x="80" y="300" width="240" height="180" rx="5" ry="5" class="repo-box" />
    <text x="200" y="330" text-anchor="middle" class="box-title">大型会员仓储代理</text>
    <text x="200" y="355" text-anchor="middle" class="box-text">会员基本信息管理</text>
    <text x="200" y="375" text-anchor="middle" class="box-text">会员卡操作</text>
    <text x="200" y="395" text-anchor="middle" class="box-text">会员支付记录</text>
    <text x="200" y="415" text-anchor="middle" class="box-text">会员操作日志</text>
    <text x="200" y="435" text-anchor="middle" class="box-text">会员积分管理</text>
    <text x="200" y="455" text-anchor="middle" class="box-text">...</text>

    <!-- 右侧：正确的设计方式 -->
    <rect x="400" y="120" width="350" height="500" rx="10" ry="10" fill="#E8F5E9" fill-opacity="0.3" stroke="#388E3C" stroke-width="1" />
    <text x="575" y="150" text-anchor="middle" class="box-title" fill="#388E3C">✅ 推荐：按职责划分颗粒度</text>

    <!-- 领域概念 - 拆分为多个子领域 -->
    <rect x="420" y="180" width="310" height="80" rx="5" ry="5" class="domain-box" />
    <text x="575" y="210" text-anchor="middle" class="box-title">会员领域 - 按职责划分</text>
    <text x="575" y="235" text-anchor="middle" class="box-text">基本信息 | 会员卡 | 支付明细 | 操作记录</text>

    <!-- 代理仓储 - 拆分为多个 -->
    <rect x="420" y="300" width="140" height="70" rx="5" ry="5" class="repo-box" />
    <text x="490" y="325" text-anchor="middle" class="box-title">会员基本信息代理</text>
    <text x="490" y="345" text-anchor="middle" class="box-text">FindById, Save...</text>

    <rect x="590" y="300" width="140" height="70" rx="5" ry="5" class="repo-box" />
    <text x="660" y="325" text-anchor="middle" class="box-title">会员卡代理</text>
    <text x="660" y="345" text-anchor="middle" class="box-text">GetCard, UpdateCard...</text>

    <rect x="420" y="390" width="140" height="70" rx="5" ry="5" class="repo-box" />
    <text x="490" y="415" text-anchor="middle" class="box-title">会员支付代理</text>
    <text x="490" y="435" text-anchor="middle" class="box-text">GetPayments, AddPayment...</text>

    <rect x="590" y="390" width="140" height="70" rx="5" ry="5" class="repo-box" />
    <text x="660" y="415" text-anchor="middle" class="box-title">会员操作记录代理</text>
    <text x="660" y="435" text-anchor="middle" class="box-text">LogOperation, GetLogs...</text>

    <!-- 数据库表 -->
    <rect x="420" y="490" width="70" height="40" rx="5" ry="5" class="db-box" />
    <text x="455" y="515" text-anchor="middle" class="box-text" style="font-size: 12px;">会员表</text>

    <rect x="500" y="490" width="70" height="40" rx="5" ry="5" class="db-box" />
    <text x="535" y="515" text-anchor="middle" class="box-text" style="font-size: 12px;">会员卡表</text>

    <rect x="580" y="490" width="70" height="40" rx="5" ry="5" class="db-box" />
    <text x="615" y="515" text-anchor="middle" class="box-text" style="font-size: 12px;">支付表</text>

    <rect x="660" y="490" width="70" height="40" rx="5" ry="5" class="db-box" />
    <text x="695" y="515" text-anchor="middle" class="box-text" style="font-size: 12px;">操作日志表</text>

    <!-- 连接箭头 -->
    <line x1="490" y1="260" x2="490" y2="300" class="arrow" />
    <line x1="660" y1="260" x2="660" y2="300" class="arrow" />
    <line x1="490" y1="370" x2="490" y2="390" class="arrow" />
    <line x1="660" y1="370" x2="660" y2="390" class="arrow" />

    <!-- 数据库连接 -->
    <line x1="455" y1="460" x2="455" y2="490" class="arrow dashed" />
    <line x1="535" y1="460" x2="535" y2="490" class="arrow dashed" />
    <line x1="615" y1="460" x2="615" y2="490" class="arrow dashed" />
    <line x1="695" y1="460" x2="695" y2="490" class="arrow dashed" />

    <!-- 设计原则框 -->
    <rect x="50" y="640" width="700" height="40" rx="5" ry="5" class="principle-box" />
    <text x="400" y="665" text-anchor="middle" class="box-text">
        设计原则：代理类应按领域职责划分，保持适当颗粒度，避免单一代理承担过多职责
    </text>
</svg> 