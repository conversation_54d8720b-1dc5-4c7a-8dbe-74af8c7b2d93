# 应用服务生成模板

## 基本信息

```
服务对应目录: erp_client
应用服务名称：                    // 例如：会员注册服务
所属业务领域：                    // 例如：member、order、room等
服务接口文件路径：                // 例如：application/business/member/service/member_application_service.go
服务实现文件路径：                // 例如：application/business/member/service/member_application_service_impl.go
服务测试文件路径：                // 例如：application/business/member/service/member_application_service_test.go
```

## 业务需求

```
【功能概述】
// 简要描述该应用服务的主要功能，3-5句话

【业务场景】
// 列出该服务支持的主要业务场景
1. 
2. 
3. 

【业务规则】
// 列出需要遵循的关键业务规则
1. 
2. 
3. 

【异常场景】
// 列出需要特别处理的异常情况
1. 
2. 
3. 
```

## 接口定义

```
【接口方法】
// 为每个方法提供名称、参数和返回值
1. 方法名：
   输入参数：
   返回值：
   功能描述：

2. 方法名：
   输入参数：
   返回值：
   功能描述：

3. 方法名：
   输入参数：
   返回值：
   功能描述：
```

## 数据结构

```
【请求DTO】
// 列出接口需要的输入DTO
1. DTO名称：
   字段列表：
   - 字段名：        类型：        描述：        是否必填：
   - 字段名：        类型：        描述：        是否必填：

2. DTO名称：
   字段列表：
   - 字段名：        类型：        描述：        是否必填：
   - 字段名：        类型：        描述：        是否必填：

【响应VO】
// 列出接口返回的值对象
1. VO名称：
   字段列表：
   - 字段名：        类型：        描述：
   - 字段名：        类型：        描述：

2. VO名称：
   字段列表：
   - 字段名：        类型：        描述：
   - 字段名：        类型：        描述：

【领域对象/PO】
// 列出已有的或需要新增的持久化对象
1. PO名称：
   字段列表：
   - 字段名：        类型：        描述：
   - 字段名：        类型：        描述：
```

## 依赖服务

```
【应用层依赖】
// 列出依赖的其他应用服务
1. 服务名称：
   依赖原因：
   调用方法：

【领域层依赖】
// 列出依赖的领域服务
1. 服务名称：
   依赖原因：
   调用方法：

【基础设施层依赖】
// 列出依赖的代理服务或外部系统
1. 代理名称：
   依赖原因：
   调用方法：
```

## 配置文件

```
【流程配置】
// 提供process.yaml的草稿或关键步骤
process:
  name: ""
  steps:
    - id: ""
      name: ""
      condition: ""
      action:
        type: ""
        service: ""
        method: ""
        params:
          # 参数列表

【规则配置】
// 提供rule.yaml的草稿或关键规则
rules:
  - id: ""
    name: ""
    priority: 
    condition: ""
    actions:
      - type: ""
        params:
          # 参数列表
```

## 控制器与路由

```
【控制器】
// 描述需要实现的控制器方法
1. 方法名：
   路径：
   HTTP方法：
   对应应用服务方法：

【路由注册】
// 描述API路由信息
基础路径：
路由列表：
1. 路径：        方法：        处理函数：
2. 路径：        方法：        处理函数：
```

## 补充说明

```
【特殊需求】
// 任何不适合上述分类的特殊需求或说明

【实现优先级】
// 各功能实现的优先级排序

【参考资料】
// 任何有助于理解或实现的参考资料、文档或示例
```

---

## 填写示例（会员注册服务）

```
应用服务名称：会员注册服务
所属业务领域：member
服务接口文件路径：application/business/member/service/member_application_service.go
服务实现文件路径：application/business/member/service/member_application_service_impl.go
```
