<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义箭头 -->
    <marker id="arrow" viewBox="0 0 10 10" refX="9" refY="5"
      markerWidth="6" markerHeight="6" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#666"/>
    </marker>
    <!-- 虚线箭头 -->
    <marker id="dashArrow" viewBox="0 0 10 10" refX="9" refY="5"
      markerWidth="6" markerHeight="6" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#999"/>
    </marker>
    <!-- 渐变背景 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4a6baf;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2a4a8f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="modelGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#d35f5f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b33f3f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="interfaceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#5fad5f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3f8d3f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="implGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#5f8dad;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3f6d8d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="repoGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ad5fad;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8d3f8d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="eventGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#d3ad5f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b38d3f;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 标题 -->
  <g>
    <rect x="250" y="20" width="400" height="50" rx="5" fill="url(#headerGradient)" />
    <text x="450" y="50" font-family="Arial" font-size="18" font-weight="bold" fill="white" text-anchor="middle">领域服务生成链路图</text>
  </g>
  
  <!-- 领域服务接口 -->
  <g>
    <rect x="340" y="120" width="220" height="90" rx="5" fill="url(#interfaceGradient)" />
    <text x="450" y="145" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">领域服务接口</text>
    <text x="450" y="170" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 定义领域操作方法</text>
    <text x="450" y="190" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 不包含实现细节</text>
  </g>
  
  <!-- 领域服务实现 -->
  <g>
    <rect x="340" y="280" width="220" height="110" rx="5" fill="url(#implGradient)" />
    <text x="450" y="305" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">领域服务实现</text>
    <text x="450" y="330" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 实现业务逻辑</text>
    <text x="450" y="350" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 依赖仓储接口</text>
    <text x="450" y="370" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 发布领域事件</text>
  </g>
  
  <!-- 领域模型 -->
  <g>
    <rect x="80" y="280" width="180" height="130" rx="5" fill="url(#modelGradient)" />
    <text x="170" y="305" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">领域模型</text>
    <text x="170" y="330" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 聚合根</text>
    <text x="170" y="350" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 实体</text>
    <text x="170" y="370" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 值对象</text>
    <text x="170" y="390" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 领域行为</text>
  </g>
  
  <!-- 仓储接口 -->
  <g>
    <rect x="340" y="460" width="220" height="90" rx="5" fill="url(#repoGradient)" />
    <text x="450" y="485" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">仓储接口</text>
    <text x="450" y="510" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 定义数据访问方法</text>
    <text x="450" y="530" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 不依赖具体数据源</text>
  </g>
  
  <!-- 仓储实现 -->
  <g>
    <rect x="340" y="610" width="220" height="70" rx="5" fill="#888" />
    <text x="450" y="635" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">仓储实现</text>
    <text x="450" y="660" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 数据持久化逻辑</text>
  </g>
  
  <!-- 领域事件 -->
  <g>
    <rect x="640" y="280" width="180" height="90" rx="5" fill="url(#eventGradient)" />
    <text x="730" y="305" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">领域事件</text>
    <text x="730" y="330" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 传递领域变更</text>
    <text x="730" y="350" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 解耦领域服务</text>
  </g>
  
  <!-- 依赖服务 -->
  <g>
    <rect x="640" y="460" width="180" height="90" rx="5" fill="#777" />
    <text x="730" y="485" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">其他领域服务</text>
    <text x="730" y="510" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 提供跨领域功能</text>
    <text x="730" y="530" font-family="Arial" font-size="12" fill="white" text-anchor="middle">- 边界明确</text>
  </g>
  
  <!-- 连接线 -->
  <!-- 接口到实现 -->
  <line x1="450" y1="210" x2="450" y2="280" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 实现到仓储接口 -->
  <line x1="450" y1="390" x2="450" y2="460" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 仓储接口到实现 -->
  <line x1="450" y1="550" x2="450" y2="610" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 模型到服务接口 -->
  <line x1="260" y1="320" x2="340" y2="165" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 模型到服务实现 -->
  <line x1="260" y1="335" x2="340" y2="335" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 模型到仓储接口 -->
  <line x1="180" y1="410" x2="180" y2="505" stroke="#666" stroke-width="2" />
  <line x1="180" y1="505" x2="340" y2="505" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 实现到事件 -->
  <line x1="560" y1="335" x2="640" y2="335" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 实现到其他服务 -->
  <line x1="560" y1="350" x2="600" y2="350" stroke="#666" stroke-width="2" />
  <line x1="600" y1="350" x2="600" y2="505" stroke="#666" stroke-width="2" />
  <line x1="600" y1="505" x2="640" y2="505" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 图例 -->
  <g>
    <rect x="80" y="120" width="180" height="110" rx="5" fill="#f9f9f9" stroke="#ccc" />
    <text x="170" y="145" font-family="Arial" font-size="14" font-weight="bold" fill="#333" text-anchor="middle">核心职责</text>
    <circle cx="100" cy="170" r="5" fill="url(#interfaceGradient)" />
    <text x="110" y="175" font-family="Arial" font-size="12" fill="#333" x="10">定义领域行为</text>
    <circle cx="100" cy="195" r="5" fill="url(#modelGradient)" />
    <text x="110" y="200" font-family="Arial" font-size="12" fill="#333" x="10">封装业务规则</text>
    <circle cx="100" cy="220" r="5" fill="url(#repoGradient)" />
    <text x="110" y="225" font-family="Arial" font-size="12" fill="#333" x="10">持久化领域对象</text>
  </g>
  
  <!-- 箭头文本 -->
  <text x="465" y="250" font-family="Arial" font-size="11" fill="#666">实现接口</text>
  <text x="465" y="430" font-family="Arial" font-size="11" fill="#666">使用仓储</text>
  <text x="465" y="585" font-family="Arial" font-size="11" fill="#666">实现持久化</text>
  <text x="280" y="270" font-family="Arial" font-size="11" fill="#666">定义结构</text>
  <text x="290" y="350" font-family="Arial" font-size="11" fill="#666">操作模型</text>
  <text x="280" y="480" font-family="Arial" font-size="11" fill="#666">存储模型</text>
  <text x="600" y="315" font-family="Arial" font-size="11" fill="#666">发布事件</text>
  <text x="570" y="430" font-family="Arial" font-size="11" fill="#666">依赖服务</text>
</svg> 