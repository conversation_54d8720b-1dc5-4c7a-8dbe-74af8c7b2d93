<?xml version="1.0" encoding="UTF-8"?>
<svg width="1500" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义箭头 -->
    <marker id="arrow" viewBox="0 0 10 10" refX="9" refY="5"
      markerWidth="6" markerHeight="6" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#666"/>
    </marker>
    <!-- 虚线箭头 -->
    <marker id="dashArrow" viewBox="0 0 10 10" refX="9" refY="5"
      markerWidth="6" markerHeight="6" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#999"/>
    </marker>
    <!-- 渐变背景 -->
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4a6baf;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2a4a8f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="appGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#5fad5f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3f8d3f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="domainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#d35f5f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b33f3f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="infraGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#5f8dad;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3f6d8d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="poGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ad5fad;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8d3f8d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="erpMgtGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#d3ad5f;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b38d3f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#8d8d8d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d6d6d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="noteGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f9f9d0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f0c0;stop-opacity:1" />
    </linearGradient>
    <!-- 半透明阴影效果 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="2" dy="2" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  
  <!-- 标题 -->
  <g>
    <rect x="500" y="20" width="500" height="50" rx="5" fill="url(#headerGradient)" filter="url(#dropShadow)" />
    <text x="750" y="50" font-family="Arial" font-size="18" font-weight="bold" fill="white" text-anchor="middle">代码结构与调用关系图</text>
  </g>
  
  <!-- 主要模块结构 -->
  <g>
    <!-- erp_client 模块 -->
    <rect x="120" y="100" width="550" height="780" rx="5" fill="#f0f0f0" stroke="#ddd" stroke-width="2" />
    <text x="395" y="130" font-family="Arial" font-size="16" font-weight="bold" fill="#333" text-anchor="middle">erp_client</text>
    
    <!-- 应用层说明 -->
    <rect x="150" y="145" width="490" height="45" rx="3" fill="url(#noteGradient)" stroke="#d4d4a0" stroke-width="1" />
    <text x="395" y="167" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">应用层：负责处理用户请求，协调各领域服务完成业务流程，</text>
    <text x="395" y="183" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">不包含核心业务逻辑</text>
    
    <!-- erp_client 内部结构 -->
    <!-- 应用层 -->
    <rect x="150" y="200" width="490" height="150" rx="5" fill="url(#appGradient)" filter="url(#dropShadow)" />
    <text x="395" y="225" font-family="Arial" font-size="14" font-weight="bold" fill="white" text-anchor="middle">application</text>
    
    <!-- 应用服务 -->
    <rect x="170" y="240" width="200" height="80" rx="3" fill="#fff" stroke="#3f8d3f" stroke-width="1" />
    <text x="270" y="265" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">应用服务接口</text>
    <text x="270" y="290" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberRegisterApplication</text>
    
    <!-- 应用服务实现 -->
    <rect x="420" y="240" width="200" height="80" rx="3" fill="#fff" stroke="#3f8d3f" stroke-width="1" />
    <text x="520" y="265" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">应用服务实现</text>
    <text x="520" y="290" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberRegisterServiceImpl</text>
    
    <!-- 应用服务说明框 -->
    <rect x="185" y="330" width="420" height="45" rx="3" fill="#f7ffef" stroke="#3f8d3f" stroke-width="1" stroke-dasharray="3,2" />
    <text x="395" y="350" font-family="Arial" font-size="10" fill="#333" text-anchor="middle">应用服务负责编排业务流程，协调领域服务，</text>
    <text x="395" y="365" font-family="Arial" font-size="10" fill="#333" text-anchor="middle">处理事务和跨领域交互</text>
    
    <!-- 领域层说明 -->
    <rect x="150" y="380" width="490" height="45" rx="3" fill="url(#noteGradient)" stroke="#d4d4a0" stroke-width="1" />
    <text x="395" y="400" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">领域层：包含核心业务规则和流程，是系统的核心，</text>
    <text x="395" y="415" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">体现业务知识和专业领域概念</text>
    
    <!-- 领域层 -->
    <rect x="150" y="435" width="490" height="250" rx="5" fill="url(#domainGradient)" filter="url(#dropShadow)" />
    <text x="395" y="460" font-family="Arial" font-size="14" font-weight="bold" fill="white" text-anchor="middle">domain</text>
    
    <!-- 领域服务接口 -->
    <rect x="170" y="475" width="200" height="80" rx="3" fill="#fff" stroke="#b33f3f" stroke-width="1" />
    <text x="270" y="500" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">领域服务接口</text>
    <text x="270" y="520" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberDomainService</text>
    <text x="270" y="535" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CardLevelDomainService</text>
    
    <!-- 领域服务实现 -->
    <rect x="420" y="475" width="200" height="80" rx="3" fill="#fff" stroke="#b33f3f" stroke-width="1" />
    <text x="520" y="500" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">领域服务实现</text>
    <text x="520" y="520" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberDomainServiceImpl</text>
    <text x="520" y="535" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CardLevelDomainServiceImpl</text>
    
    <!-- 领域模型 -->
    <rect x="170" y="570" width="200" height="80" rx="3" fill="#fff" stroke="#b33f3f" stroke-width="1" />
    <text x="270" y="595" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">领域模型</text>
    <text x="270" y="615" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">Member</text>
    <text x="270" y="630" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CardLevel</text>
    
    <!-- 仓储接口 -->
    <rect x="420" y="570" width="200" height="80" rx="3" fill="#fff" stroke="#b33f3f" stroke-width="1" />
    <text x="520" y="595" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">仓储接口</text>
    <text x="520" y="615" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberRepository</text>
    <text x="520" y="630" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CardLevelRepository</text>
    
    <!-- 基础设施层说明 -->
    <rect x="150" y="695" width="490" height="45" rx="3" fill="url(#noteGradient)" stroke="#d4d4a0" stroke-width="1" />
    <text x="395" y="715" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">基础设施层：提供技术支持，实现仓储接口，</text>
    <text x="395" y="730" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">处理与外部系统的交互，如数据持久化</text>
    
    <!-- 基础设施层 -->
    <rect x="150" y="750" width="490" height="120" rx="5" fill="url(#infraGradient)" filter="url(#dropShadow)" />
    <text x="395" y="775" font-family="Arial" font-size="14" font-weight="bold" fill="white" text-anchor="middle">infrastructure</text>
    
    <!-- Proxy实现 -->
    <rect x="170" y="790" width="450" height="70" rx="3" fill="#fff" stroke="#3f6d8d" stroke-width="1" />
    <text x="395" y="810" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">仓储代理实现</text>
    <text x="395" y="830" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberRepositoryProxy、CardLevelRepositoryProxy</text>
  </g>
  
  <!-- erp_managent 模块 -->
  <g>
    <!-- erp_managent说明 -->
    <rect x="780" y="100" width="550" height="780" rx="5" fill="#f8f8f8" stroke="#ddd" stroke-width="2" />
    <text x="1055" y="130" font-family="Arial" font-size="16" font-weight="bold" fill="#333" text-anchor="middle">erp_managent</text>
    
    <!-- 服务层说明 -->
    <rect x="810" y="145" width="490" height="45" rx="3" fill="url(#noteGradient)" stroke="#d4d4a0" stroke-width="1" />
    <text x="1055" y="167" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">服务层：提供业务处理能力，封装数据访问操作，</text>
    <text x="1055" y="183" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">作为erp_client基础设施层的调用目标</text>
    
    <!-- 服务层 -->
    <rect x="810" y="200" width="490" height="430" rx="5" fill="url(#erpMgtGradient)" filter="url(#dropShadow)" />
    <text x="1055" y="225" font-family="Arial" font-size="14" font-weight="bold" fill="white" text-anchor="middle">service</text>
    
    <!-- 服务实现 -->
    <rect x="830" y="240" width="450" height="80" rx="3" fill="#fff" stroke="#b38d3f" stroke-width="1" />
    <text x="1055" y="265" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">服务实现 (impl)</text>
    <text x="1055" y="290" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberService</text>
    <text x="1055" y="305" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">PhysicalCardService</text>
    
    <!-- 持久化对象 -->
    <rect x="830" y="330" width="450" height="260" rx="3" fill="url(#poGradient)" filter="url(#dropShadow)" />
    <text x="1055" y="355" font-family="Arial" font-size="12" font-weight="bold" fill="white" text-anchor="middle">持久化对象 (po)</text>
    
    <!-- Member PO -->
    <rect x="850" y="370" width="190" height="100" rx="3" fill="#fff" stroke="#8d3f8d" stroke-width="1" />
    <text x="945" y="395" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">Member</text>
    <text x="945" y="415" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">Id: *string</text>
    <text x="945" y="430" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">Name: *string</text>
    <text x="945" y="445" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">...</text>
    <text x="945" y="460" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">State: *int</text>
    
    <!-- PhysicalCard PO -->
    <rect x="1070" y="370" width="190" height="100" rx="3" fill="#fff" stroke="#8d3f8d" stroke-width="1" />
    <text x="1165" y="395" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">PhysicalCard</text>
    <text x="1165" y="415" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">Id: *string</text>
    <text x="1165" y="430" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">CardNumber: *string</text>
    <text x="1165" y="445" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">...</text>
    <text x="1165" y="460" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">State: *int</text>
    
    <!-- CardLevel PO -->
    <rect x="850" y="480" width="190" height="80" rx="3" fill="#fff" stroke="#8d3f8d" stroke-width="1" />
    <text x="945" y="505" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">MemberCardLevel</text>
    <text x="945" y="525" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">Id: *string</text>
    <text x="945" y="540" font-family="Arial" font-size="9" fill="#666" text-anchor="middle">...</text>
    
    <!-- DAL层说明 -->
    <rect x="810" y="645" width="490" height="45" rx="3" fill="url(#noteGradient)" stroke="#d4d4a0" stroke-width="1" />
    <text x="1055" y="665" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">数据访问层：负责与数据库交互，封装SQL操作，</text>
    <text x="1055" y="680" font-family="Arial" font-size="11" fill="#333" text-anchor="middle">提供PO对象的CRUD功能</text>
    
    <!-- DAL层 -->
    <rect x="810" y="700" width="490" height="145" rx="5" fill="url(#serviceGradient)" filter="url(#dropShadow)" />
    <text x="1055" y="725" font-family="Arial" font-size="14" font-weight="bold" fill="white" text-anchor="middle">dal - 数据访问层</text>
    
    <!-- DAO 接口和实现 -->
    <rect x="830" y="740" width="210" height="95" rx="3" fill="#fff" stroke="#666" stroke-width="1" />
    <text x="935" y="760" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">DAO 接口</text>
    <text x="935" y="780" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CardLevelDAO</text>
    <text x="935" y="795" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">PhysicalCardDAO</text>
    <text x="935" y="810" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberDAO</text>
    
    <rect x="1070" y="740" width="210" height="95" rx="3" fill="#fff" stroke="#666" stroke-width="1" />
    <text x="1175" y="760" font-family="Arial" font-size="12" fill="#333" text-anchor="middle">DAO 实现</text>
    <text x="1175" y="780" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">CardLevelDAOImpl</text>
    <text x="1175" y="795" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">PhysicalCardDAOImpl</text>
    <text x="1175" y="810" font-family="Arial" font-size="10" fill="#666" text-anchor="middle">MemberDAOImpl</text>
  </g>
  
  <!-- 流程与数据流图 -->
  <g>
    <rect x="150" y="920" width="1200" height="210" rx="5" fill="#f9f9f9" stroke="#ccc" stroke-width="2" />
    <text x="750" y="950" font-family="Arial" font-size="16" font-weight="bold" fill="#333" text-anchor="middle">核心调用流程</text>
    
    <!-- 调用流程 -->
    <!-- 第一行红框部分流程图 -->
    <rect x="200" y="970" width="1100" height="60" rx="5" fill="#fff" stroke="#f00" stroke-width="2" />
    
    <!-- 调用流程图中的元素 -->
    <rect x="230" y="985" width="170" height="30" rx="3" fill="url(#appGradient)" />
    <text x="315" y="1005" font-family="Arial" font-size="12" fill="white" text-anchor="middle">应用服务</text>
    
    <line x1="400" y1="1000" x2="450" y2="1000" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
    
    <rect x="450" y="985" width="170" height="30" rx="3" fill="url(#domainGradient)" />
    <text x="535" y="1005" font-family="Arial" font-size="12" fill="white" text-anchor="middle">领域服务</text>
    
    <line x1="620" y1="1000" x2="670" y2="1000" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
    
    <rect x="670" y="985" width="170" height="30" rx="3" fill="url(#domainGradient)" />
    <text x="755" y="1005" font-family="Arial" font-size="12" fill="white" text-anchor="middle">仓储接口</text>
    
    <line x1="840" y1="1000" x2="890" y2="1000" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
    
    <rect x="890" y="985" width="170" height="30" rx="3" fill="url(#infraGradient)" />
    <text x="975" y="1005" font-family="Arial" font-size="12" fill="white" text-anchor="middle">仓储代理</text>
    
    <line x1="1060" y1="1000" x2="1110" y2="1000" stroke="#666" stroke-width="2" marker-end="url(#arrow)" />
    
    <rect x="1110" y="985" width="170" height="30" rx="3" fill="url(#erpMgtGradient)" />
    <text x="1195" y="1005" font-family="Arial" font-size="12" fill="white" text-anchor="middle">服务实现</text>
    
    <!-- 跨模块调用说明 -->
    <rect x="240" y="1050" width="1020" height="70" rx="3" fill="#fff" stroke="#ccc" stroke-width="1" />
    <text x="750" y="1070" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">跨模块调用流程</text>
    <text x="750" y="1095" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">erp_client (Infrastructure Proxy) → erp_managent (Service Impl) → erp_managent (DAL)</text>
    <text x="750" y="1115" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">例如: MemberRepositoryProxy → MemberService → MemberDAO/CardLevelDAO</text>
  </g>
  
  <!-- 调用关系连接线 -->
  <!-- erp_client应用服务 到 领域服务 -->
  <line x1="335" y1="320" x2="335" y2="475" stroke="#666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  
  <!-- 领域服务 到 仓储接口 -->
  <line x1="450" y1="555" x2="485" y2="570" stroke="#666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  
  <!-- 仓储接口 到 代理 -->
  <line x1="440" y1="650" x2="440" y2="790" stroke="#666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  
  <!-- 关键跨模块调用 - Proxy到erp_managent服务 -->
  <line x1="620" y1="800" x2="830" y2="270" stroke="#f00" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- erp_managent服务到dal层 -->
  <line x1="1055" y1="320" x2="1055" y2="740" stroke="#666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
</svg> 