<svg xmlns="http://www.w3.org/2000/svg" width="800" height="700" viewBox="0 0 800 700">
  <!-- 背景 -->
  <rect width="800" height="700" fill="#f8f9fa" />
  
  <!-- 标题 -->
  <text x="400" y="30" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" font-weight="bold">DDD架构运行流程图</text>
  
  <!-- 客户端 -->
  <rect x="350" y="60" width="100" height="40" rx="5" ry="5" fill="#f5d76e" stroke="#000" stroke-width="1" />
  <text x="400" y="85" font-family="Arial, sans-serif" font-size="14" text-anchor="middle">客户端</text>
  
  <!-- 接口层 -->
  <rect x="150" y="140" width="500" height="80" rx="5" ry="5" fill="#d2e9ff" stroke="#000" stroke-width="1" />
  <text x="400" y="165" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">接口层 (API)</text>
  <rect x="170" y="180" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="230" y="200" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Controller</text>
  <rect x="310" y="180" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="370" y="200" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">DTO/VO</text>
  <rect x="450" y="180" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="510" y="200" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Router</text>
  
  <!-- 应用层 -->
  <rect x="150" y="240" width="500" height="120" rx="5" ry="5" fill="#bbdefb" stroke="#000" stroke-width="1" />
  <text x="400" y="265" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">应用层 (Application)</text>
  <rect x="170" y="280" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="230" y="300" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">ApplicationService</text>
  <rect x="310" y="280" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="370" y="300" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">ProcessEngine</text>
  <rect x="450" y="280" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="510" y="300" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Runtime</text>
  <rect x="170" y="320" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="230" y="340" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">ValidateService</text>
  <rect x="310" y="320" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="370" y="340" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Transaction</text>
  <rect x="450" y="320" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="510" y="340" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Event</text>
  
  <!-- 领域层 -->
  <rect x="150" y="380" width="500" height="80" rx="5" ry="5" fill="#c8e6c9" stroke="#000" stroke-width="1" />
  <text x="400" y="405" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">领域层 (Domain)</text>
  <rect x="170" y="420" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="230" y="440" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">DomainService</text>
  <rect x="310" y="420" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="370" y="440" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Entity/ValueObject</text>
  <rect x="450" y="420" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="510" y="440" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Repository</text>
  
  <!-- 基础设施层 -->
  <rect x="150" y="480" width="500" height="80" rx="5" ry="5" fill="#ffccbc" stroke="#000" stroke-width="1" />
  <text x="400" y="505" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" font-weight="bold">基础设施层 (Infrastructure)</text>
  <rect x="170" y="520" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="230" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">RepositoryImpl</text>
  <rect x="310" y="520" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="370" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Proxy</text>
  <rect x="450" y="520" width="120" height="30" rx="5" ry="5" fill="#ffffff" stroke="#000" stroke-width="1" />
  <text x="510" y="540" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">Messaging/Event</text>
  
  <!-- 外部系统 -->
  <rect x="350" y="580" width="100" height="40" rx="5" ry="5" fill="#e0e0e0" stroke="#000" stroke-width="1" />
  <text x="400" y="605" font-family="Arial, sans-serif" font-size="14" text-anchor="middle">外部系统</text>
  
  <!-- 连接线 -->
  <!-- 客户端到接口层 -->
  <line x1="400" y1="100" x2="400" y2="140" stroke="#000" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 接口层到应用层 -->
  <line x1="400" y1="220" x2="400" y2="240" stroke="#000" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 应用层到领域层 -->
  <line x1="400" y1="360" x2="400" y2="380" stroke="#000" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 领域层到基础设施层 -->
  <line x1="400" y1="460" x2="400" y2="480" stroke="#000" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 基础设施层到外部系统 -->
  <line x1="400" y1="560" x2="400" y2="580" stroke="#000" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#000" />
    </marker>
  </defs>
  
  <!-- 图例 -->
  <rect x="650" y="430" width="20" height="20" fill="#d2e9ff" stroke="#000" stroke-width="1" />
  <text x="675" y="445" font-family="Arial, sans-serif" font-size="12" text-anchor="start">接口层</text>
  
  <rect x="650" y="455" width="20" height="20" fill="#bbdefb" stroke="#000" stroke-width="1" />
  <text x="675" y="470" font-family="Arial, sans-serif" font-size="12" text-anchor="start">应用层</text>
  
  <rect x="650" y="480" width="20" height="20" fill="#c8e6c9" stroke="#000" stroke-width="1" />
  <text x="675" y="495" font-family="Arial, sans-serif" font-size="12" text-anchor="start">领域层</text>
  
  <rect x="650" y="505" width="20" height="20" fill="#ffccbc" stroke="#000" stroke-width="1" />
  <text x="675" y="520" font-family="Arial, sans-serif" font-size="12" text-anchor="start">基础设施层</text>
  
  <!-- 版权信息 -->
  <text x="400" y="670" font-family="Arial, sans-serif" font-size="12" text-anchor="middle">© DDD架构实现规范</text>
</svg>