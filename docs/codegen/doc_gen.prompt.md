我需要基于应用服务模板创建一个{服务名称}的详细文档。请按照以下要求完成：

1. 使用 docs/codegen/application_service_template.md 作为基础模板
2. 创建一个新文档，命名为 docs/codegen/{服务名称小写}_service.md
3. 填充所有必要的部分，包括：
   - 基本信息（服务名称、业务领域、文件路径）
   - 业务需求（功能概述、业务场景、业务规则、异常场景）
   - 接口定义（方法名、输入参数、返回值、功能描述）
   - 数据结构（请求DTO、响应VO、领域对象/PO）
   - 依赖服务（应用层、领域层、基础设施层）
   - 配置文件（流程配置、规则配置）
   - 控制器与路由（API接口、路由信息）
   - 补充说明（特殊需求、实现优先级、参考资料）

4. {服务名称}的具体要求：
   - 服务名称：{服务名称}
   - 业务领域：{业务领域}
   - 主要功能：{简要描述主要功能，用逗号分隔}
   - 其他关键特性：{列出2-3个关键特性}

5. 使用以下PO对象作为参考：
   {列出相关的PO对象及其简要说明}

6. 接口方法应包括：
   {列出需要实现的主要接口方法，每个方法一行}

7. 业务规则应包括：
   {列出关键业务规则，每条规则一行}

8. 流程配置应详细描述{主要业务流程}的完整流程，包括：
   {列出流程的主要步骤，每个步骤一行}

9. 规则配置应包括{列出需要的验证规则类型}

请确保文档内容完整、结构清晰，并符合DDD架构设计规范。生成的文档应该能够作为开发团队实现该服务的详细指南。