# DDD架构实现规范：应用层、领域层与基础设施层

## 1. 标准架构设计

### 1.1 分层架构
```
├── api                     // 接口层
│   ├── dto                // 数据传输对象
│   ├── vo                 // 值对象
│   ├── router            // 路由定义
│   └── controller        // 控制器
├── application            // 应用层
│   ├── framework          // 框架组件
│   │   ├── validate      // 验证组件
│   │   ├── transaction   // 事务管理
│   │   ├── yaml          // YAML配置处理
│   │   ├── runtime       // 运行时支持
│   │   ├── variable      // 变量处理
│   │   ├── service       // 服务注册
│   │   ├── adapter       // 适配器
│   │   ├── context       // 上下文管理
│   │   └── event         // 事件处理
│   └── business          // 业务应用服务
│       ├── order         // 订单业务
│       └── room          // 房间业务
├── domain                 // 领域层
│   ├── core              // 核心领域模型
│   ├── process           // 流程定义
│   ├── traderecord       // 交易记录
│   ├── rule              // 规则引擎
│   ├── valueobject       // 值对象
│   ├── subject           // 主体
│   ├── state             // 状态管理
│   ├── permission        // 权限
│   ├── common            // 公共领域
│   └── configuration     // 配置领域
└── infrastructure        // 基础设施层
    ├── persistence       // 持久化实现
    ├── proxy             // 代理服务
    ├── common            // 公共组件
    ├── messaging         // 消息处理
    └── event             // 事件处理
```

### 1.2 标准服务结构

#### 1.2.1 应用层服务
```go
// 应用服务接口与实现
// 接口定义
type ApplicationService interface {
    // 业务方法定义，如：
    BusinessOperation(ctx context.Context, reqDto dto.RequestDTO) (*vo.ResponseVO, error)
}

// 实现
type ApplicationServiceImpl struct {
    processEngine       model.Engine                    // 流程引擎
    domainServices      []interface{}                   // 领域服务
    valueServices       []interface{}                   // 值对象服务
    validateService     validateService.ValidateService // 验证服务
    // 其他依赖服务...
}

// 工厂方法
func NewApplicationService(
    domainService1 DomainService1,
    domainService2 DomainService2,
    valueService1 ValueService1,
    validateService validateService.ValidateService,
    // 其他依赖服务...
) ApplicationService {
    // 创建实例
    service := &ApplicationServiceImpl{
        domainService1: domainService1,
        domainService2: domainService2,
        valueService1: valueService1,
        validateService: validateService,
        // 初始化其他依赖...
    }
    
    // 初始化流程引擎
    engine := processEngine.NewEngine()
    service.processEngine = engine
    
    // 注册服务到流程引擎
    engine.RegisterService("domainService1", domainService1)
    engine.RegisterService("domainService2", domainService2)
    // 注册其他服务...
    
    // 加载流程定义
    // 可能从YAML文件或其他配置源加载
    
    return service
}
```

### 1.3 应用层核心组件

以下组件都属于**应用层**的核心框架组件，它们共同构成了应用层的基础设施和运行时环境：

#### 1.3.1 流程引擎相关组件
- **ProcessEngine/ProcessEngineImpl**：流程引擎接口及实现
  - 作用：负责编排和执行业务流程，是应用层的核心组件
  - 职责：加载流程定义、执行流程步骤、管理流程状态、协调各服务之间的调用

- **Runtime/RuntimeImpl**：运行时环境接口及实现
  - 作用：为流程引擎提供运行时支持，管理流程执行的上下文环境
  - 职责：加载元数据、管理变量、执行表达式求值、处理数据映射

#### 1.3.2 验证相关组件
- **ValidateService/ValidateServiceImpl**：验证服务接口及实现
  - 作用：提供统一的参数验证机制
  - 职责：验证业务参数的合法性、完整性，确保数据符合业务规则

这些组件共同工作，形成了应用层的核心框架，它们具有以下特点：

1. **通用性**：这些组件不包含特定业务逻辑，可以被不同的业务应用服务复用
2. **可配置性**：通过配置驱动，而非硬编码方式定义流程
3. **可扩展性**：支持注册新的服务和验证规则
4. **分离关注点**：将流程编排、运行时环境和验证逻辑分离

在应用服务实现中，这些组件被注入到具体的业务应用服务中，为业务应用服务提供基础能力支持。

## 2. 标准调用链路

### 2.1 基础调用链路
```
1. 接口层 (API)
   ├── 接收请求 (Controller)
   ├── 参数校验 (基础验证)
   └── DTO转换

2. 应用层 (Application)
   ├── 编排业务流程 (ProcessEngine)
   ├── 调用领域服务
   └── 事务管理 (Transaction)

3. 领域层 (Domain)
   ├── 实现业务规则
   ├── 状态转换 (State)
   └── 领域事件 (Event)

4. 基础设施层 (Infrastructure)
   ├── 持久化处理 (Persistence)
   ├── 外部服务调用 (Proxy)
   └── 通用能力支持 (Common)
```

### 2.2 通用时序图
```mermaid
sequenceDiagram
    %% 图例
    note over C: 客户端
    note over A, AI: 应用层 #blue
    note over D, DI: 领域层 #green
    note over RP, PX: 基础设施层 #orange
    note over E: 外部系统 #gray
    
    participant C as Client
    participant A as ApplicationService #blue
    participant AI as ApplicationServiceImpl #blue
    participant P as ProcessEngine #blue
    participant PI as ProcessEngineImpl #blue
    participant R as Runtime #blue
    participant RI as RuntimeImpl #blue
    participant V as ValidateService #blue
    participant VI as ValidateServiceImpl #blue
    participant D as DomainService #green
    participant DI as DomainServiceImpl #green
    participant RP as Repository #green
    participant PX as RepositoryProxy #orange
    participant E as ExternalSystem #gray
    
    C->>+AI: Request(DTO)
    AI->>+PI: Execute(processID, params)
    
    PI->>+RI: LoadMetadata(processDefinition)
    RI-->>-PI: Metadata Loaded
    
    PI->>+RI: Set("input", params)
    RI-->>-PI: Input Set
    
    PI->>+PI: For Each Step in Process
    
    PI->>+RI: Eval(step.condition)
    RI-->>-PI: Condition Result
    
    alt Service Action
        PI->>+VI: Validate(params)
        VI-->>-PI: ValidationResult
        
        PI->>+DI: ExecuteDomain()
        
        alt Repository Operation
            DI->>+RP: Repository Method Call
            
            RP->>+PX: Proxy Implementation
            
            PX->>+PX: Convert Context
            PX-->>-PX: Context Converted
            
            PX->>+PX: Build Request
            PX-->>-PX: Request Built
            
            PX->>+E: External System Call
            E-->>-PX: External System Response
            
            PX->>+PX: Convert Response to Domain Model
            PX-->>-PX: Domain Model Created
            
            PX-->>-RP: Domain Model Returned
            RP-->>-DI: Repository Result
        end
        
        DI-->>-PI: Domain Result
        
        PI->>+RI: Map(target, mappings)
        RI-->>-PI: Mapping Result
    end
    
    PI->>-PI: End For Each Step
    
    PI->>+RI: Get("output")
    RI-->>-PI: Output Data
    
    PI-->>-AI: ProcessResult
    AI-->>-C: ResponseVO
```

## 3. 架构规范要素

### 3.1 流程引擎机制
- **配置驱动设计**：
  - 流程定义通过YAML配置文件描述，而非硬编码
  - 配置文件位于`config/processes/`目录下，每个业务流程对应一个YAML文件
  - 流程引擎在启动时加载并解析这些配置文件
  
- **服务注册与依赖注入**：
  - 所有参与流程的服务都需要注册到流程引擎
  - 通过`RegisterService(name, service)`方法注册
  - 在YAML配置中通过服务名引用已注册的服务

- **统一的流程执行模型**：
  - 流程由多个有序步骤组成，每个步骤可以包含条件判断和动作执行
  - 支持条件分支、服务调用、数据映射等操作
  - 标准执行流程：加载流程定义 → 设置输入参数 → 按序执行步骤 → 返回输出结果

- **上下文管理**：
  - 流程执行期间维护三类变量空间：输入(input)、上下文(context)和输出(output)
  - Runtime组件负责变量存储、检索和表达式求值
  - 支持复杂的数据转换和映射操作

- **错误处理机制**：
  - 统一的错误传播和包装机制
  - 支持业务错误与技术错误的区分
  - 提供详细的错误信息和上下文

### 3.2 依赖注入规范
- 构造函数注入
- 接口依赖
- 服务注册

### 3.3 错误处理规范
- 统一的错误包装
- 分层的错误处理
- 错误信息标准化

### 3.4 数据传输规范
- DTO/VO转换
- 参数校验
- 结果封装

### 3.5 Proxy代理机制
- **定位与作用**：Proxy位于基础设施层，作为领域层与外部系统之间的桥梁
- **主要职责**：
  - 实现领域层定义的仓储接口
  - 适配外部系统API
  - 封装外部依赖细节
  - 处理数据转换和上下文转换
- **防腐层功能**：保护领域模型不受外部系统影响，维持领域模型的纯净性
- **实现模式**：
  - 直接代理模式：直接封装外部服务，实现领域层定义的接口
  - 工厂模式：通过工厂创建和管理不同类型的代理实例
  - 单例模式：确保全局只有一个代理实例
- **调用链路**：
  ```
  领域层(Domain) <---> 基础设施层(Infrastructure/Proxy) <---> 外部系统
  ```
- **代码示例**：
  ```go
  // 领域层定义接口与基础设施层实现
  // 领域层接口
  type Repository interface {
      FindByID(ctx context.Context, id string) (*Entity, error)
  }
  
  // 基础设施层实现
  type RepositoryProxy struct {
      externalService *ExternalService
  }
  
  func (r *RepositoryProxy) FindByID(ctx context.Context, id string) (*Entity, error) {
      // 1. 上下文转换
      specificCtx := convertContext(ctx)
      
      // 2. 构造请求
      request := buildRequest(id)
      
      // 3. 调用外部服务
      response, err := r.externalService.Call(specificCtx, request)
      if err != nil {
          return nil, err
      }
      
      // 4. 转换为领域模型
      return convertToDomainModel(response), nil
  }
  ```

### 3.6 配置文件机制

#### 3.6.1 配置文件结构
配置文件位于项目的`config`目录下，主要包含以下两类配置：

1. **流程定义配置**：位于`config/processes/`目录，采用YAML格式
   - 各业务流程分别对应独立的YAML文件（如`order_open_process.yaml`、`order_pay_process.yaml`等）
   - 定义流程的元数据、输入/输出参数、上下文变量、执行步骤等
   - 通过声明式方式描述业务流程，无需修改代码即可调整流程

2. **规则定义配置**：位于`config/rules/`目录，采用YAML格式
   - 各规则组分别对应独立的YAML文件（如`room_status_rules.yaml`、`price_calculation_rules.yaml`等）
   - 定义规则的条件、动作、优先级等
   - 实现业务规则与代码的解耦，便于调整业务规则

#### 3.6.2 配置文件加载流程
1. **应用初始化阶段**：
   - 依赖注入容器(`Container`)初始化时会创建流程引擎(`ProcessEngine`)和规则引擎(`RuleEngine`)
   - 引擎会从对应目录加载YAML配置文件

2. **流程加载**：
   ```go
   // 流程引擎加载流程定义
   func (e *Engine) LoadProcess(processContent []byte) error {
       // 解析YAML内容
       parser := yamlparser.NewProcessParser(processContent)
       proc, err := parser.ParseContent()
       
       // 获取流程ID
       processID := proc.Definition.ID
       
       // 加载元数据到Runtime
       e.runtime.LoadMetadata(proc.Metadata)
       
       // 存储流程定义
       e.processDefMap[processID] = proc
       
       return nil
   }
   ```

3. **规则加载**：
   ```go
   // 规则引擎加载规则定义
   func (e *RuleEngine) LoadRules(ruleContent []byte) error {
       // 解析YAML内容
       parser := yamlparser.NewRuleParser(ruleContent)
       rules, err := parser.ParseContent()
       
       // 注册规则到引擎
       e.ruleRegistry.RegisterRules(rules)
       
       return nil
   }
   ```

#### 3.6.3 配置文件运行机制

1. **流程执行**：
   - 应用服务调用流程引擎的Execute方法，传入流程ID和参数
   - 流程引擎根据YAML配置的步骤定义，依次执行各个步骤
   - 每个步骤可能调用领域服务、规则引擎，或执行其他操作

   ```go
   // 应用服务中流程执行示例
   func (s *OrderApplicationServiceImpl) OrderOpen(ctx context.Context, reqDto dto.OrderOpenDTO) (*vo.SessionVO, error) {
       // 调用流程引擎执行流程
       result, err := s.processEngine.Execute("order_open_process", map[string]interface{}{
           "reqDto": reqDto,
       })
       if err != nil {
           return nil, err
       }
       
       // 转换结果
       return result.(*vo.SessionVO), nil
   }
   ```

2. **规则评估**：
   - 在流程执行过程中，可能需要调用规则引擎评估业务规则
   - 规则引擎根据YAML配置的规则定义，评估条件并执行动作

   ```yaml
   # YAML流程配置中调用规则引擎的示例（order_open_process.yaml）
   - id: "validate_room_status"
     name: "验证房间状态"
     action:
       type: "service"
       service: "ruleService"
       method: "Evaluate"
       params:
         - name: "ruleGroupId"
           value: "room_status_rules"
         - name: "input"
           value:
             room:
               status: "context.room.status"
   ```

#### 3.6.4 配置文件的优势

1. **业务逻辑与代码分离**：
   - 业务流程和规则定义在YAML配置中，而不是硬编码在程序中
   - 业务调整只需修改配置文件，无需改动代码

2. **提高灵活性**：
   - 可以动态加载不同的流程和规则配置
   - 支持热更新，无需重启应用

3. **增强可维护性**：
   - 配置文件结构清晰，易于理解和维护
   - 业务逻辑可视化，降低维护成本

4. **促进团队协作**：
   - 业务分析师可以直接参与流程和规则定义
   - 开发人员与业务人员之间的沟通更加高效

### 3.7 规则引擎机制

#### 3.7.1 规则引擎定位
- **架构位置**：规则引擎属于领域层的核心组件，位于`domain/rule`包下
- **主要职责**：将业务决策规则从代码中抽离，实现业务规则的集中管理和动态配置
- **与流程引擎的关系**：规则引擎通常在流程执行过程中被调用，用于复杂业务逻辑的决策

#### 3.7.2 规则组织结构
- **规则组**：相关规则的集合，例如`room_status_rules`、`price_calculation_rules`等
- **规则定义**：每条规则包含条件(condition)和动作(actions)两部分
- **规则优先级**：通过`priority`属性定义规则执行的优先顺序

```yaml
# 规则定义示例
rules:
  - id: "check_room_idle"
    name: "检查房间空闲状态"
    priority: 100
    condition: "input.room.status == 'IDLE'"
    actions:
      - type: "set"
        params:
          target: "output.statusResult.displayStatus"
          value: "空闲"
      - type: "set"
        params:
          target: "output.statusResult.color"
          value: "#00FF00"
```

#### 3.7.3 规则引擎执行流程
1. **规则评估请求**：应用服务或领域服务通过`ruleEngine.Evaluate(ruleGroupId, input)`调用规则引擎
2. **规则匹配**：规则引擎根据输入数据评估每条规则的条件是否满足
3. **规则执行**：对于条件满足的规则，按优先级执行其对应的动作
4. **结果返回**：将执行结果返回给调用方

```go
// 规则引擎执行示例
func (s *RoomService) GetRoomStatus(ctx context.Context, roomId string) (*dto.RoomStatusDTO, error) {
    // 查询房间基本信息
    room, err := s.roomRepository.FindById(ctx, roomId)
    if err != nil {
        return nil, err
    }
    
    // 构造规则输入
    ruleInput := map[string]interface{}{
        "room": map[string]interface{}{
            "status": room.Status,
            "currentOrder": room.CurrentOrder,
        },
    }
    
    // 调用规则引擎评估
    result, err := s.ruleEngine.Evaluate("room_status_rules", ruleInput)
    if err != nil {
        return nil, err
    }
    
    // 返回规则评估结果
    return &dto.RoomStatusDTO{
        DisplayStatus: result.Data["statusResult"].(map[string]interface{})["displayStatus"].(string),
        Color: result.Data["statusResult"].(map[string]interface{})["color"].(string),
    }, nil
}
```

#### 3.7.4 规则引擎的扩展性
- **动态加载**：支持在运行时动态加载规则定义
- **规则表达式**：支持丰富的表达式语言，可处理复杂业务条件
- **动作扩展**：内置多种动作类型，如设置值、计算、调用服务等
- **自定义函数**：允许注册自定义函数，扩展规则引擎的能力

#### 3.7.5 规则引擎的应用场景
- **状态计算**：如房间状态显示逻辑
- **价格计算**：如折扣、促销规则计算
- **验证规则**：如业务操作前的条件检查
- **决策规则**：如订单处理流程中的分支决策

## 4. 扩展机制设计

### 4.1 流程定义扩展
```yaml
process:
  name: "业务流程名"
  steps:
    - service: "领域服务"
      method: "方法名"
      input: "入参映射"
      output: "出参映射"
```

### 4.2 服务扩展
```go
// 领域服务接口与实现
// 接口定义
type NewDomainService interface {
    // 方法定义
    ExecuteBusinessLogic(ctx context.Context, param ParamType) (ResultType, error)
}

// 实现
type NewDomainServiceImpl struct {
    // 依赖定义
    repository Repository
    // 其他依赖...
}

// 注册到流程引擎
engine.RegisterService("newDomainService", newDomainService)
```

### 4.3 验证规则扩展
```go
// 验证规则接口与实现
// 接口定义
type ValidationRule interface {
    Validate(interface{}) error
}

// 注册验证规则
validateService.RegisterRule("ruleName", rule)
```

### 4.4 Proxy扩展
```go
// 领域层仓储接口与基础设施层实现
// 领域层接口
type NewRepository interface {
    FindByCondition(ctx context.Context, condition Condition) ([]Entity, error)
    Save(ctx context.Context, entity Entity) error
}

// 基础设施层实现
type NewRepositoryProxy struct {
    externalService *ExternalService
}

// 工厂方法
func NewNewRepository() NewRepository {
    return &NewRepositoryProxy{
        externalService: &ExternalService{},
    }
}

// 实现接口方法
func (p *NewRepositoryProxy) FindByCondition(ctx context.Context, condition Condition) ([]Entity, error) {
    // 实现查询逻辑
}

func (p *NewRepositoryProxy) Save(ctx context.Context, entity Entity) error {
    // 实现保存逻辑
}
```

## 5. 实现案例：订单应用服务

### 5.1 服务结构
```go
// 订单应用服务实现
type OrderApplicationServiceImpl struct {
    processEngine       model.Engine                               // 流程引擎
    orderService        tradeService.OrderService                  // 订单领域服务
    bookingService      bookingService.BookingService             // 预订领域服务
    roomService         roomService.Service                       // 房间值对象服务
    ruleService         ruleService.Service                       // 规则领域服务
    ruleEngine          ruleEngine.RuleEngine                     // 规则引擎
    validateService     validateService.ValidateService           // 验证服务
    discountCalcService discountService.DiscountCalculateService  // 折扣计算服务
    orderAmountService  amountService.OrderAmountService          // 订单金额服务
    sessionService      sessionService.SessionService             // 会话值对象服务
    payService          tradeService.PayService                   // 支付服务
}
```

### 5.2 开台流程实现
```mermaid
sequenceDiagram
    %% 图例
    note over C: 客户端
    note over A, AI, P, PI, R, RI, RE: 应用层 #blue
    note over OS, OSI, SS, SSI: 领域层 #green
    note over RR, RP: 基础设施层 #orange
    note over ES: 外部系统 #gray
    
    participant C as Client
    participant A as OrderApplicationService #blue
    participant AI as OrderApplicationServiceImpl #blue
    participant P as ProcessEngine #blue
    participant PI as ProcessEngineImpl #blue
    participant R as Runtime #blue
    participant RI as RuntimeImpl #blue
    participant RE as RuleEngine #blue
    participant OS as OrderService #green
    participant OSI as OrderServiceImpl #green
    participant SS as SessionService #green
    participant SSI as SessionServiceImpl #green
    participant RR as RoomRepository #green
    participant RP as RoomRepositoryProxy #orange
    participant ES as ExternalSystem #gray
    
    C->>+AI: OrderOpen(reqDto)
    AI->>+PI: Execute("order_open_process", params)
    
    PI->>+RI: LoadMetadata(processDefinition)
    RI-->>-PI: Metadata Loaded
    
    PI->>+RI: Set("input", params)
    RI-->>-PI: Input Set
    
    PI->>+PI: For Each Step
    
    alt Fetch Room Info Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+OSI: GetRoom(roomId)
        
        OSI->>+RR: FindByID(roomId)
        
        RR->>+RP: FindByID(roomId)
        
        RP->>+RP: Convert Context (ctx -> ginCtx)
        RP-->>-RP: Context Converted
        
        RP->>+ES: FindRoomById(ginCtx, roomId)
        ES-->>-RP: Room PO
        
        RP->>+RP: Convert to Domain Model
        RP-->>-RP: Room Domain Model
        
        RP-->>-RR: Room Domain Model
        RR-->>-OSI: Room Domain Model
        
        OSI-->>-PI: Room
        
        PI->>+RI: Map("context.room", mappings)
        RI-->>-PI: Mapping Result
    end
    
    alt Validate Room Status Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+RE: Evaluate("room_status_rules", input)
        RE-->>-PI: ValidationResult
        
        PI->>+RI: Map("context.validation", mappings)
        RI-->>-PI: Mapping Result
    end
    
    alt Create Order Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+OSI: CreateOrder(params)
        
        OSI->>+RR: SaveOrder(order)
        
        RR->>+RP: SaveOrder(order)
        
        RP->>+RP: Convert Context & Data
        RP-->>-RP: Converted
        
        RP->>+ES: CreateOrder(ginCtx, orderPO)
        ES-->>-RP: Order ID
        
        RP-->>-RR: Order Result
        RR-->>-OSI: Order Result
        
        OSI-->>-PI: Order
        
        PI->>+RI: Map("context.orderInfo", mappings)
        RI-->>-PI: Mapping Result
    end
    
    alt Create Session Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+SSI: CreateSession(params)
        
        SSI->>+RR: SaveSession(session)
        
        RR->>+RP: SaveSession(session)
        
        RP->>+RP: Convert Context & Data
        RP-->>-RP: Converted
        
        RP->>+ES: CreateSession(ginCtx, sessionPO)
        ES-->>-RP: Session ID
        
        RP-->>-RR: Session Result
        RR-->>-SSI: Session Result
        
        SSI-->>-PI: Session
        
        PI->>+RI: Map("output.session", mappings)
        RI-->>-PI: Mapping Result
    end
    
    PI->>-PI: End For Each Step
    
    PI->>+RI: Get("output")
    RI-->>-PI: Output Data
    
    PI-->>-AI: ProcessResult
    AI-->>-C: SessionVO
```

### 5.3 支付流程实现
```mermaid
sequenceDiagram
    %% 图例
    note over C: 客户端
    note over A, AI, P, PI, R, RI, V, VI: 应用层 #blue
    note over AM, AMI, DC, DCI, PS, PSI: 领域层 #green
    note over OR, OP: 基础设施层 #orange
    note over ES: 外部系统 #gray
    
    participant C as Client
    participant A as OrderApplicationService #blue
    participant AI as OrderApplicationServiceImpl #blue
    participant P as ProcessEngine #blue
    participant PI as ProcessEngineImpl #blue
    participant R as Runtime #blue
    participant RI as RuntimeImpl #blue
    participant V as ValidateService #blue
    participant VI as ValidateServiceImpl #blue
    participant AM as OrderAmountService #green
    participant AMI as OrderAmountServiceImpl #green
    participant DC as DiscountCalcService #green
    participant DCI as DiscountCalcServiceImpl #green
    participant PS as PayService #green
    participant PSI as PayServiceImpl #green
    participant OR as OrderRepository #green
    participant OP as OrderRepositoryProxy #orange
    participant ES as ExternalSystem #gray
    
    C->>+AI: OrderPay(reqDto)
    AI->>+PI: Execute("order_pay_process", params)
    
    PI->>+RI: LoadMetadata(processDefinition)
    RI-->>-PI: Metadata Loaded
    
    PI->>+RI: Set("input", params)
    RI-->>-PI: Input Set
    
    PI->>+PI: For Each Step
    
    alt Validate Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+VI: Validate(params)
        VI-->>-PI: ValidationResult
        
        PI->>+RI: Map("context.validation", mappings)
        RI-->>-PI: Mapping Result
    end
    
    alt Calculate Amount Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+AMI: CalculateAmount(params)
        
        AMI->>+OR: GetOrderDetails(orderId)
        
        OR->>+OP: GetOrderDetails(orderId)
        
        OP->>+OP: Convert Context (ctx -> ginCtx)
        OP-->>-OP: Context Converted
        
        OP->>+ES: FindOrderById(ginCtx, orderId)
        ES-->>-OP: Order PO
        
        OP-->>-OR: Order Details
        OR-->>-AMI: Order Details
        
        AMI-->>-PI: Amount
        
        PI->>+RI: Map("context.amount", mappings)
        RI-->>-PI: Mapping Result
    end
    
    alt Calculate Discount Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+DCI: CalculateDiscount(params)
        DCI-->>-PI: DiscountedAmount
        
        PI->>+RI: Map("context.discountAmount", mappings)
        RI-->>-PI: Mapping Result
    end
    
    alt Process Payment Step
        PI->>+RI: Eval(step.condition)
        RI-->>-PI: true
        
        PI->>+PSI: ProcessPayment(params)
        
        PSI->>+OR: SavePayment(payment)
        
        OR->>+OP: SavePayment(payment)
        
        OP->>+OP: Convert Context & Data
        OP-->>-OP: Converted
        
        OP->>+ES: CreatePayment(ginCtx, paymentPO)
        ES-->>-OP: Payment Result
        
        OP-->>-OR: Payment Result
        OR-->>-PSI: Payment Result
        
        PSI-->>-PI: PaymentResult
        
        PI->>+RI: Map("output.payResult", mappings)
        RI-->>-PI: Mapping Result
    end
    
    PI->>-PI: End For Each Step
    
    PI->>+RI: Get("output")
    RI-->>-PI: Output Data
    
    PI-->>-AI: ProcessResult
    AI-->>-C: PayResultVO
```

### 5.4 Proxy在订单流程中的作用
```mermaid
sequenceDiagram
    %% 图例
    note over D, DI: 领域层 #green
    note over R: 领域层接口 #green
    note over P: 基础设施层实现 #orange
    note over E: 外部系统 #gray
    
    participant D as DomainService #green
    participant DI as DomainServiceImpl #green
    participant R as Repository #green
    participant P as RepositoryProxy #orange
    participant E as ExternalSystem #gray
    
    DI->>+R: 调用仓储接口
    R->>+P: 代理实现
    
    P->>+P: 1. 上下文转换(ctx -> ginCtx)
    P-->>-P: 上下文转换完成
    
    P->>+P: 2. 构造请求对象(DTO/PO)
    P-->>-P: 请求对象构造完成
    
    P->>+E: 3. 调用外部系统API
    E-->>-P: 返回外部系统数据
    
    P->>+P: 4. 错误处理与转换
    P-->>-P: 错误处理完成
    
    P->>+P: 5. 数据转换(PO -> Domain Model)
    P-->>-P: 数据转换完成
    
    P-->>-R: 返回领域模型
    R-->>-DI: 返回领域对象
```

## 6. 架构规范检查清单

### 6.1 分层合规性
- [ ] 层次职责明确
- [ ] 依赖方向正确
- [ ] 跨层调用规范

### 6.2 服务定义
- [ ] 接口完整性
- [ ] 职责单一性
- [ ] 依赖注入规范

### 6.3 流程定义
- [ ] 流程配置完整
- [ ] 服务注册正确
- [ ] 错误处理完备

### 6.4 扩展实现
- [ ] 遵循扩展点设计
- [ ] 向后兼容
- [ ] 文档完备

### 6.5 Proxy实现
- [ ] 完全实现领域层定义的接口
- [ ] 正确处理上下文转换
- [ ] 适当的错误处理和转换
- [ ] 不泄露外部系统细节到领域层

## 7. 添加新应用服务步骤

### 7.1 API层定义
1. 在`api/dto`中定义请求DTO
2. 在`api/vo`中定义响应VO
3. 在`api/controller`中定义控制器
4. 在`api/router`中注册路由

### 7.2 应用层实现
1. 在`application/business/{业务领域}/service`中定义服务接口和实现
2. 定义流程YAML配置

### 7.3 领域层支持
1. 实现或复用领域服务（接口+实现）
2. 定义领域模型和规则
3. 定义仓储接口

### 7.4 基础设施层
1. 实现持久化逻辑
2. 实现Proxy代理，连接外部系统
3. 集成外部服务

### 7.5 API-First实现方式
1. 先定义API接口和数据结构
2. 创建应用服务接口和空实现
3. 实现控制器和路由
4. 逐步完善领域逻辑和流程定义
