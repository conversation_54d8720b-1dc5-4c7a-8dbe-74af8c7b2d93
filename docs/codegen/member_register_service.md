# 应用服务生成模板

## 基本信息

```
服务对应目录: erp_client
应用服务名称：会员开通服务
所属业务领域：member
服务接口文件路径：application/business/member/service/member_register_application_service.go
服务实现文件路径：application/business/member/service/member_register_application_service_impl.go
服务测试文件路径：application/business/member/service/member_register_application_service_test.go
```

## 业务需求

```
【功能概述】
提供会员开通相关功能，包括会员信息注册、会员卡开卡、会员与门店关联等操作。支持多种开卡方式，如实体卡、虚拟卡、电子卡等，确保会员信息的完整性和唯一性，并支持会员卡等级管理。

【业务场景】
1. 用户通过线下门店办理实体会员卡
2. 用户通过小程序或APP注册成为线上会员
3. 会员卡升级或变更等级
4. 会员信息修改与更新
5. 会员卡状态管理（启用、禁用、注销等）

【业务规则】
1. 同一手机号在同一门店只能关联一个有效会员账号
2. 会员卡开通需要验证必要的个人信息（如姓名、手机号等）
3. 不同等级的会员卡具有不同的权益和折扣
4. 会员卡开通可能需要收取制卡费用，具体根据卡等级配置决定
5. 会员开卡时可能赠送初始积分或余额，具体根据卡等级配置决定
6. 会员卡有效期根据卡等级配置可能为永久、相对有效期或固定有效期

【异常场景】
1. 手机号已被注册为会员
2. 实体卡号不存在或已被使用
3. 会员信息不完整或格式错误
4. 会员卡等级不存在或已停用
5. 开卡操作人无权限执行开卡操作
```

## 接口定义

```
【接口方法】
1. 方法名：RegisterMember
   输入参数：ctx context.Context, reqDto dto.MemberRegisterDTO
   返回值：(*vo.MemberVO, error)
   功能描述：注册新会员，创建会员信息并关联会员卡

2. 方法名：RegisterMemberWithPhysicalCard
   输入参数：ctx context.Context, reqDto dto.PhysicalCardRegisterDTO
   返回值：(*vo.MemberVO, error)
   功能描述：通过实体卡注册会员，验证实体卡有效性并创建会员信息

3. 方法名：VerifyMemberInfo
   输入参数：ctx context.Context, reqDto dto.MemberVerifyDTO
   返回值：(*vo.VerifyResultVO, error)
   功能描述：验证会员信息的有效性，如手机号是否已注册等

4. 方法名：GetMemberCardLevels
   输入参数：ctx context.Context, venueId string
   返回值：(*vo.CardLevelsVO, error)
   功能描述：获取指定门店可用的会员卡等级列表
```

## 数据结构

```
【请求DTO】
1. DTO名称：MemberRegisterDTO
   字段列表：
   - venueId：        string：        门店ID：        是
   - name：           string：        会员姓名：      是
   - phone：          string：        手机号：        是
   - cardLevelId：    string：        卡等级ID：      是
   - cardType：       string：        卡类型：        是
   - gender：         string：        性别：          否
   - birthday：       int64：         生日时间戳：    否
   - operatorId：     string：        操作人ID：      是
   - operatorName：   string：        操作人姓名：    是
   - initialBalance： int64：         初始余额：      否
   - initialPoints：  int：           初始积分：      否
   - remark：         string：        备注：          否

2. DTO名称：PhysicalCardRegisterDTO
   字段列表：
   - venueId：        string：        门店ID：        是
   - name：           string：        会员姓名：      是
   - phone：          string：        手机号：        是
   - cardNumber：     string：        实体卡号：      是
   - cardLevelId：    string：        卡等级ID：      是
   - gender：         string：        性别：          否
   - birthday：       int64：         生日时间戳：    否
   - operatorId：     string：        操作人ID：      是
   - operatorName：   string：        操作人姓名：    是
   - initialBalance： int64：         初始余额：      否
   - initialPoints：  int：           初始积分：      否
   - remark：         string：        备注：          否

3. DTO名称：MemberVerifyDTO
   字段列表：
   - venueId：        string：        门店ID：        是
   - phone：          string：        手机号：        是
   - cardNumber：     string：        卡号：          否

【响应VO】
1. VO名称：MemberVO
   字段列表：
   - id：                     string：        会员ID：
   - name：                   string：        会员姓名：
   - phone：                  string：        手机号：
   - cardNumber：             string：        会员卡号：
   - cardType：               string：        卡类型：
   - cardId：                 string：        会员卡ID：
   - cardName：               string：        会员卡名称：
   - cardLevel：              int：           卡等级：
   - points：                 int：           会员积分：
   - balance：                int64：         会员余额：
   - principalBalance：       int64：         本金余额：
   - bonusBalance：           int64：         赠金余额：
   - birthday：               int64：         生日时间戳：
   - gender：                 string：        性别：
   - status：                 string：        会员状态：
   - isEnabled：              bool：          是否启用：
   - totalConsumptionTimes：  int64：         累计消费次数：
   - totalConsumptionAmount： int64：         累计消费金额：
   - validityStartDate：      int64：         有效期开始时间：
   - validityEndDate：        int64：         有效期结束时间：
   - ctime：                  int64：         创建时间：

2. VO名称：VerifyResultVO
   字段列表：
   - isValid：        bool：          是否有效：
   - message：        string：        提示信息：
   - existingMember： MemberVO：      已存在的会员信息：

3. VO名称：CardLevelsVO
   字段列表：
   - levels：         []CardLevelInfo：  卡等级列表：
   
   CardLevelInfo结构：
   - id：             string：        等级ID：
   - name：           string：        等级名称：
   - level：          int：           等级值：
   - logo：           string：        等级logo：
   - cardType：       string：        卡类型：
   - roomDiscount：   int：           房间折扣：
   - productDiscount：int：           商品折扣：
   - description：    string：        等级描述：

【领域对象/PO】
1. PO名称：Member
   字段列表：
   - Id：                     string：        会员ID：
   - Name：                   string：        会员姓名：
   - Phone：                  string：        会员手机号：
   - IsEnabled：              bool：          是否启用：
   - CardNumber：             string：        会员卡号：
   - CardType：               string：        卡类型：
   - CardId：                 string：        会员卡ID：
   - CardName：               string：        会员卡名称：
   - OperatorId：             string：        开卡操作人ID：
   - OperatorName：           string：        开卡操作人姓名：
   - Points：                 int：           会员积分：
   - Balance：                int64：         会员余额：
   - PrincipalBalance：       int64：         本金余额：
   - BonusBalance：           int64：         赠金余额：
   - Birthday：               int64：         生日：
   - Gender：                 string：        性别：
   - TotalConsumptionTimes：  int64：         累计消费次数：
   - TotalConsumptionAmount： int64：         累计消费金额：
   - Ctime：                  int64：         创建时间戳：
   - Utime：                  int64：         更新时间戳：
   - Source：                 string：        会员来源：
   - Status：                 string：        会员状态：
   - State：                  int：           状态值：
   - Version：                int：           版本号：

2. PO名称：MemberCardLevel
   字段列表：
   - Id：                   string：        ID：
   - VenueId：              string：        门店ID：
   - Name：                 string：        卡名称：
   - Level：                int：           等级：
   - Logo：                 string：        logo：
   - CardType：             string：        卡类型：
   - ValidityPeriodType：   string：        有效期类型：
   - ValidityPeriodValue：  int：           相对有效期时长值：
   - ValidityPeriodUnit：   string：        相对有效期单位：
   - ValidityStartDate：    int64：         固定有效期开始时间戳：
   - ValidityEndDate：      int64：         固定有效期结束时间戳：
   - RoomDiscount：         int：           房间折扣：
   - ProductDiscount：      int：           商品折扣：
   - State：                int：           状态：
   - Version：              int：           版本号：
   - Ctime：                int64：         创建时间戳：
   - Utime：                int64：         更新时间戳：

3. PO名称：MemberCardOperation
   字段列表：
   - Id：               string：        ID：
   - MemberId：         string：        会员ID：
   - CardNumber：       string：        会员卡号：
   - OperationType：    string：        操作类型：
   - Balance：          float64：       余额：
   - ReceivableAmount： float64：       应收金额：
   - ActualAmount：     float64：       实收金额：
   - OperatorId：       string：        操作人ID：
   - OperatorName：     string：        操作人姓名：
   - Remark：           string：        备注：
   - Status：           string：        状态值：
   - State：            int：           状态值：
   - Version：          int：           版本号：
   - Ctime：            int64：         创建时间戳：
   - Utime：            int64：         更新时间戳：

4. PO名称：VenueAndMember
   字段列表：
   - Id：       string：        ID：
   - VenueId：  string：        门店ID：
   - MemberId： string：        会员ID：
   - Remark：   string：        备注：
   - Ctime：    int64：         创建时间戳：
   - Utime：    int64：         更新时间戳：
   - State：    int：           状态值：
   - Version：  int：           版本号：

5. PO名称：PhysicalCard
   字段列表：
   - Id：         string：        唯一ID：
   - CardNumber： string：        卡号：
   - Ctime：      int64：         创建时间戳：
   - Utime：      int64：         更新时间戳：
   - State：      int：           状态值：
   - Version：    int：           版本号：
```

## 依赖服务

```
【应用层依赖】
1. 服务名称：MemberQueryApplicationService
   依赖原因：查询会员信息，验证会员是否已存在
   调用方法：GetMemberByPhone, GetMemberByCardNumber

2. 服务名称：CardLevelApplicationService
   依赖原因：获取会员卡等级信息和配置
   调用方法：GetCardLevelById, GetCardLevelsByVenue

【领域层依赖】
1. 服务名称：MemberDomainService
   依赖原因：处理会员领域核心业务逻辑
   调用方法：CreateMember, ValidateMemberInfo

2. 服务名称：CardLevelDomainService
   依赖原因：处理会员卡等级相关业务逻辑
   调用方法：GetCardLevel, ValidateCardLevel

3. 服务名称：MemberCardOperationDomainService
   依赖原因：记录会员卡操作日志
   调用方法：RecordCardOperation

【基础设施层依赖】
1. 代理名称：MemberRepositoryProxy
   依赖原因：会员信息的持久化操作
   调用方法：Save, FindByPhone, FindByCardNumber

2. 代理名称：CardLevelRepositoryProxy
   依赖原因：会员卡等级信息的查询
   调用方法：FindById, FindByVenueId

3. 代理名称：VenueAndMemberRepositoryProxy
   依赖原因：会员与门店关联关系的持久化
   调用方法：Save, FindByVenueIdAndMemberId

4. 代理名称：PhysicalCardRepositoryProxy
   依赖原因：实体卡信息的查询和更新
   调用方法：FindByCardNumber, UpdateStatus

5. 代理名称：MemberCardOperationRepositoryProxy
   依赖原因：会员卡操作记录的持久化
   调用方法：Save
```

## 配置文件

```
【流程配置】
// 提供process.yaml的草稿或关键步骤
process:
  id: "member_register_process"
  name: "会员注册流程"
  description: "处理会员注册和开卡的业务流程"
  metadata:
    input:
      - name: "reqDto"
        type: "object"
    output:
      - name: "memberVO"
        type: "object"
    context:
      - name: "memberInfo"
        type: "object"
      - name: "cardLevelInfo"
        type: "object"
      - name: "validationResult"
        type: "object"
  steps:
    - id: "validate_member_info"
      name: "验证会员信息"
      condition: "true"
      action:
        type: "service"
        service: "validateService"
        method: "Validate"
        params:
          - name: "data"
            value: "input.reqDto"
          - name: "rules"
            value: "['member_register_rules']"
      output_mapping:
        - target: "context.validationResult"
          source: "result"

    - id: "check_member_exists"
      name: "检查会员是否已存在"
      condition: "context.validationResult.isValid == true"
      action:
        type: "service"
        service: "memberDomainService"
        method: "CheckMemberExists"
        params:
          - name: "venueId"
            value: "input.reqDto.venueId"
          - name: "phone"
            value: "input.reqDto.phone"
      output_mapping:
        - target: "context.memberExists"
          source: "exists"
        - target: "context.existingMember"
          source: "member"

    - id: "validate_card_level"
      name: "验证会员卡等级"
      condition: "context.memberExists == false"
      action:
        type: "service"
        service: "cardLevelDomainService"
        method: "ValidateCardLevel"
        params:
          - name: "cardLevelId"
            value: "input.reqDto.cardLevelId"
          - name: "venueId"
            value: "input.reqDto.venueId"
      output_mapping:
        - target: "context.cardLevelInfo"
          source: "cardLevel"
        - target: "context.cardLevelValid"
          source: "isValid"

    - id: "create_member"
      name: "创建会员"
      condition: "context.cardLevelValid == true"
      action:
        type: "service"
        service: "memberDomainService"
        method: "CreateMember"
        params:
          - name: "memberInfo"
            value: "input.reqDto"
          - name: "cardLevelInfo"
            value: "context.cardLevelInfo"
      output_mapping:
        - target: "context.memberInfo"
          source: "member"

    - id: "create_venue_member_relation"
      name: "创建门店会员关联"
      condition: "context.memberInfo != null"
      action:
        type: "service"
        service: "memberDomainService"
        method: "CreateVenueMemberRelation"
        params:
          - name: "venueId"
            value: "input.reqDto.venueId"
          - name: "memberId"
            value: "context.memberInfo.id"
          - name: "remark"
            value: "input.reqDto.remark"
      output_mapping:
        - target: "context.relationCreated"
          source: "success"

    - id: "record_card_operation"
      name: "记录卡操作"
      condition: "context.relationCreated == true"
      action:
        type: "service"
        service: "memberCardOperationDomainService"
        method: "RecordCardOperation"
        params:
          - name: "memberId"
            value: "context.memberInfo.id"
          - name: "cardNumber"
            value: "context.memberInfo.cardNumber"
          - name: "operationType"
            value: "'open_card'"
          - name: "operatorId"
            value: "input.reqDto.operatorId"
          - name: "operatorName"
            value: "input.reqDto.operatorName"
          - name: "remark"
            value: "input.reqDto.remark"
      output_mapping:
        - target: "context.operationRecorded"
          source: "success"

    - id: "prepare_response"
      name: "准备响应数据"
      condition: "context.operationRecorded == true"
      action:
        type: "service"
        service: "memberDomainService"
        method: "ConvertToMemberVO"
        params:
          - name: "member"
            value: "context.memberInfo"
          - name: "cardLevel"
            value: "context.cardLevelInfo"
      output_mapping:
        - target: "output.memberVO"
          source: "memberVO"

【规则配置】
// 提供rule.yaml的草稿或关键规则
rules:
  - id: "member_register_rules"
    name: "会员注册验证规则"
    priority: 100
    condition: "true"
    actions:
      - type: "validate"
        params:
          field: "venueId"
          rules: ["required"]
          message: "门店ID不能为空"
      - type: "validate"
        params:
          field: "name"
          rules: ["required", "max:50"]
          message: "会员姓名不能为空且长度不超过50"
      - type: "validate"
        params:
          field: "phone"
          rules: ["required", "phone"]
          message: "手机号格式不正确"
      - type: "validate"
        params:
          field: "cardLevelId"
          rules: ["required"]
          message: "卡等级ID不能为空"
      - type: "validate"
        params:
          field: "cardType"
          rules: ["required", "in:physical_card,virtual_card,electronic_card"]
          message: "卡类型不正确"
      - type: "validate"
        params:
          field: "operatorId"
          rules: ["required"]
          message: "操作人ID不能为空"
      - type: "validate"
        params:
          field: "operatorName"
          rules: ["required"]
          message: "操作人姓名不能为空"

  - id: "physical_card_register_rules"
    name: "实体卡注册验证规则"
    priority: 100
    condition: "true"
    actions:
      - type: "validate"
        params:
          field: "cardNumber"
          rules: ["required"]
          message: "实体卡号不能为空"
      - type: "validate"
        params:
          field: "venueId"
          rules: ["required"]
          message: "门店ID不能为空"
      - type: "validate"
        params:
          field: "name"
          rules: ["required", "max:50"]
          message: "会员姓名不能为空且长度不超过50"
      - type: "validate"
        params:
          field: "phone"
          rules: ["required", "phone"]
          message: "手机号格式不正确"
      - type: "validate"
        params:
          field: "cardLevelId"
          rules: ["required"]
          message: "卡等级ID不能为空"
      - type: "validate"
        params:
          field: "operatorId"
          rules: ["required"]
          message: "操作人ID不能为空"
      - type: "validate"
        params:
          field: "operatorName"
          rules: ["required"]
          message: "操作人姓名不能为空"
```

## 控制器与路由

```
【控制器】
1. 方法名：RegisterMember
   路径：/api/v1/member/register
   HTTP方法：POST
   对应应用服务方法：RegisterMember

2. 方法名：RegisterMemberWithPhysicalCard
   路径：/api/v1/member/register/physical-card
   HTTP方法：POST
   对应应用服务方法：RegisterMemberWithPhysicalCard

3. 方法名：VerifyMemberInfo
   路径：/api/v1/member/verify
   HTTP方法：POST
   对应应用服务方法：VerifyMemberInfo

4. 方法名：GetMemberCardLevels
   路径：/api/v1/member/card-levels
   HTTP方法：GET
   对应应用服务方法：GetMemberCardLevels

【路由注册】
基础路径：/api/v1/member
路由列表：
1. 路径：/register        方法：POST        处理函数：RegisterMember
2. 路径：/register/physical-card  方法：POST  处理函数：RegisterMemberWithPhysicalCard
3. 路径：/verify          方法：POST        处理函数：VerifyMemberInfo
4. 路径：/card-levels     方法：GET         处理函数：GetMemberCardLevels
```

## 补充说明

```
【特殊需求】
1. 会员开卡时需要根据卡等级配置计算有效期，支持永久有效、相对有效期和固定有效期三种模式
2. 会员开卡时可能需要收取制卡费，根据卡等级配置决定
3. 会员开卡时可能赠送初始积分或余额，根据卡等级配置决定
4. 支持多种卡类型：实体卡、虚拟卡、电子卡，不同类型的卡处理逻辑有所不同
5. 需要记录会员卡操作日志，包括开卡、充值、消费等操作

【实现优先级】
1. 基本会员注册功能
2. 实体卡注册功能
3. 会员信息验证功能
4. 会员卡等级查询功能
5. 特殊需求实现

【参考资料】
1. 会员模块数据库表设计文档
2. 会员卡等级权益配置说明
3. 会员卡操作流程规范
4. DDD架构实现规范文档
```

# 会员注册服务

## 基本信息
服务名称：会员注册服务
服务路径：erp_client/application/business/member/service/member_register_application_service.go
服务测试文件路径：application/business/member/service/member_register_application_service_test.go
服务接口路径：erp_client/application/business/member/service/member_register_application_service.go
服务实现路径：erp_client/application/business/member/service/member_register_application_service_impl.go
仓储代理实现路径：erp_client/infrastructure/proxy/member_repository_proxy.go

## 业务需求
业务场景：会员注册
业务规则：
1. 会员注册需要验证手机号是否已经注册
2. 会员注册需要验证手机号格式是否正确
3. 会员注册需要验证姓名是否为空
4. 会员注册成功后，需要返回会员ID、姓名、手机号等信息
5. 支持普通会员注册和实体卡会员注册两种方式
6. 实体卡会员注册需要验证卡号是否存在，并更新卡状态

异常情况：
1. 手机号已经注册，返回错误信息
2. 手机号格式不正确，返回错误信息
3. 姓名为空，返回错误信息
4. 实体卡号不存在，返回错误信息
5. 实体卡已被使用，返回错误信息

特殊要求：
1. 需要支持流程引擎配置，实现业务流程的灵活配置
2. 需要支持验证规则配置，实现验证规则的灵活配置

## 接口定义
```go
// 会员注册应用服务接口
type MemberRegisterApplicationService interface {
	// 会员注册
	Register(ctx context.Context, dto dto.MemberRegisterDTO) (*vo.MemberRegisterVO, error)
}
```

## 数据结构
```go
// 会员注册请求数据结构
type MemberRegisterDTO struct {
	VenueId     string `json:"venueId"`     // 门店ID
	Name        string `json:"name"`        // 姓名
	Phone       string `json:"phone"`       // 手机号
	Gender      string `json:"gender"`      // 性别
	Birthday    int64  `json:"birthday"`    // 生日
	CardId      string `json:"cardId"`      // 会员卡ID
	CardNumber  string `json:"cardNumber"`  // 会员卡号
	OperatorId  string `json:"operatorId"`  // 操作人ID
	OperatorName string `json:"operatorName"` // 操作人姓名
}

// 会员注册响应数据结构
type MemberRegisterVO struct {
	Id          string `json:"id"`          // 会员ID
	Name        string `json:"name"`        // 姓名
	Phone       string `json:"phone"`       // 手机号
	CardNumber  string `json:"cardNumber"`  // 会员卡号
	CardName    string `json:"cardName"`    // 会员卡名称
}
```

## 依赖服务
```go
// 依赖的领域服务
type MemberDomainService interface {
	// 创建会员
	CreateMember(ctx context.Context, member *model.Member) (*model.Member, error)
	// 检查会员是否存在
	CheckMemberExists(ctx context.Context, venueId, phone string) (bool, error)
	// 验证会员信息
	ValidateMemberInfo(ctx context.Context, member *model.Member) error
	// 创建门店会员关联
	CreateVenueMemberRelation(ctx context.Context, venueId, memberId string) error
	// 转换为会员VO
	ConvertToMemberVO(ctx context.Context, member *model.Member) *vo.MemberRegisterVO
}

// 依赖的仓储服务
type MemberRepository interface {
	// 保存会员
	Save(ctx context.Context, member *model.Member) (*model.Member, error)
	// 根据手机号查询会员
	FindByPhone(ctx context.Context, phone string) (*model.Member, error)
}

type CardLevelRepository interface {
	// 根据ID查询会员卡等级
	FindById(ctx context.Context, id string) (*model.MemberCardLevel, error)
}

type VenueAndMemberRepository interface {
	// 保存门店会员关联
	Save(ctx context.Context, venueAndMember *model.VenueAndMember) (*model.VenueAndMember, error)
	// 根据门店ID和手机号查询会员
	FindByVenueIdAndPhone(ctx context.Context, venueId, phone string) (*model.Member, error)
}

type PhysicalCardRepository interface {
	// 根据卡号查询实体卡
	FindByCardNumber(ctx context.Context, cardNumber string) (*model.PhysicalCard, error)
	// 更新实体卡状态
	UpdateStatus(ctx context.Context, cardNumber string, state int) (bool, error)
}

// 仓储代理实现
// 代理实现需要调用erp_managent/service/impl下的服务，并将外部服务的数据结构转换为领域模型
// 代理实现需要处理外部服务的异常，并将异常转换为领域异常
// 代理实现需要处理外部服务的返回值，并将返回值转换为领域模型
// 代理实现需要注意PO对象的字段类型与领域模型的字段类型可能不同，需要进行适当的转换
```

## 配置文件
```yaml
# 会员注册流程配置
process:
  name: member-register
  description: 会员注册流程
  steps:
    - name: validate-input
      description: 验证输入参数
      type: validation
      rules:
        - field: venueId
          required: true
          message: 门店ID不能为空
        - field: name
          required: true
          message: 姓名不能为空
        - field: phone
          required: true
          message: 手机号不能为空
          pattern: "^1[3-9]\\d{9}$"
          message: 手机号格式不正确
    - name: check-member-exists
      description: 检查会员是否已存在
      type: condition
      condition: "!checkMemberExists(venueId, phone)"
      error_message: 该手机号已注册
    - name: create-member
      description: 创建会员
      type: action
      action: createMember
      input_mapping:
        venueId: venueId
        name: name
        phone: phone
        gender: gender
        birthday: birthday
        cardId: cardId
        cardNumber: cardNumber
        operatorId: operatorId
        operatorName: operatorName
      output_mapping:
        member: result
    - name: create-venue-member-relation
      description: 创建门店会员关联
      type: action
      action: createVenueMemberRelation
      input_mapping:
        venueId: venueId
        memberId: member.Id
    - name: convert-to-vo
      description: 转换为VO
      type: action
      action: convertToMemberVO
      input_mapping:
        member: member
      output_mapping:
        result: result

# 实体卡会员注册流程配置
process:
  name: physical-card-member-register
  description: 实体卡会员注册流程
  steps:
    - name: validate-input
      description: 验证输入参数
      type: validation
      rules:
        - field: venueId
          required: true
          message: 门店ID不能为空
        - field: name
          required: true
          message: 姓名不能为空
        - field: phone
          required: true
          message: 手机号不能为空
          pattern: "^1[3-9]\\d{9}$"
          message: 手机号格式不正确
        - field: cardNumber
          required: true
          message: 卡号不能为空
    - name: check-member-exists
      description: 检查会员是否已存在
      type: condition
      condition: "!checkMemberExists(venueId, phone)"
      error_message: 该手机号已注册
    - name: check-card-exists
      description: 检查卡是否存在
      type: action
      action: findPhysicalCardByCardNumber
      input_mapping:
        cardNumber: cardNumber
      output_mapping:
        card: result
    - name: validate-card
      description: 验证卡是否可用
      type: condition
      condition: "card != null && card.State == 0"
      error_message: 卡不存在或已被使用
    - name: create-member
      description: 创建会员
      type: action
      action: createMember
      input_mapping:
        venueId: venueId
        name: name
        phone: phone
        gender: gender
        birthday: birthday
        cardId: cardId
        cardNumber: cardNumber
        operatorId: operatorId
        operatorName: operatorName
      output_mapping:
        member: result
    - name: update-card-status
      description: 更新卡状态
      type: action
      action: updatePhysicalCardStatus
      input_mapping:
        cardNumber: cardNumber
        state: 1
    - name: create-venue-member-relation
      description: 创建门店会员关联
      type: action
      action: createVenueMemberRelation
      input_mapping:
        venueId: venueId
        memberId: member.Id
    - name: convert-to-vo
      description: 转换为VO
      type: action
      action: convertToMemberVO
      input_mapping:
        member: member
      output_mapping:
        result: result
```

## 控制器和路由
```go
// 控制器
type MemberRegisterController struct {
	// 依赖的应用服务
	memberRegisterService service.MemberRegisterApplicationService
}

// 注册会员
func (c *MemberRegisterController) Register(ctx *gin.Context) {
	var req dto.MemberRegisterDTO
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result, err := c.memberRegisterService.Register(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, result)
}

// 路由注册
func RegisterMemberRoutes(router *gin.RouterGroup) {
	controller := &MemberRegisterController{
		memberRegisterService: service.NewMemberRegisterApplicationService(),
	}
	router.POST("/member/register", controller.Register)
}
```

## 其他说明
1. 会员注册服务需要支持普通会员注册和实体卡会员注册两种方式
2. 实体卡会员注册需要验证卡号是否存在，并更新卡状态
3. 会员注册成功后，需要创建门店会员关联
4. 会员注册成功后，需要返回会员ID、姓名、手机号、卡号、卡名称等信息