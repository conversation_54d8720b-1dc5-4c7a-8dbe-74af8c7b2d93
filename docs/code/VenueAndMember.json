[{"po": "package po\n\n// VenueAndMember 门店和会员关联实体\ntype VenueAndMember struct {\n\tId       *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`        // ID\n\tVenueId  *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`      // 门店ID\n\tMemberId *string `gorm:\"column:member_id;type:varchar(64);default:''\" json:\"memberId\"`    // 会员ID\n\tRemark   *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`        // 备注\n\tCtime    *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime    *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState    *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion  *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (VenueAndMember) TableName() string {\n\treturn \"venue_and_member\"\n}\n\nfunc (v VenueAndMember) GetId() string {\n\treturn *v.Id\n}", "vo": "package vo\n\n// VenueAndMemberVO 门店和会员关联值对象\ntype VenueAndMemberVO struct {\n\tId       string `json:\"id\"`       // ID\n\tVenueId  string `json:\"venueId\"`  // 门店ID\n\tMemberId string `json:\"memberId\"` // 会员ID\n\tRemark   string `json:\"remark\"`   // 备注\n\tCtime    int64  `json:\"ctime\"`    // 创建时间戳\n\tUtime    int64  `json:\"utime\"`    // 更新时间戳\n\tState    int    `json:\"state\"`    // 状态值\n\tVersion  int    `json:\"version\"`  // 版本号\n}", "req_add": "package req\n\n// AddVenueAndMemberReqDto 创建门店和会员关联请求DTO\ntype AddVenueAndMemberReqDto struct {\n\tVenueId  *string `json:\"venueId\"`  // 门店ID\n\tMemberId *string `json:\"memberId\"` // 会员ID\n\tRemark   *string `json:\"remark\"`   // 备注\n}", "req_update": "package req\n\ntype UpdateVenueAndMemberReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tVenueId  *string `json:\"venueId\"`  // 门店ID\n\tMemberId *string `json:\"memberId\"` // 会员ID\n\tRemark   *string `json:\"remark\"`   // 备注\n}", "req_delete": "package req\n\ntype DeleteVenueAndMemberReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryVenueAndMemberReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tVenueId  *string `json:\"venueId\"`  // 门店ID\n\tMemberId *string `json:\"memberId\"` // 会员ID\n\tRemark   *string `json:\"remark\"`   // 备注\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VenueAndMemberTransfer struct {\n}\n\nfunc (transfer *VenueAndMemberTransfer) PoToVo(po po.VenueAndMember) vo.VenueAndMemberVO {\n\tvo := vo.VenueAndMemberVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VenueAndMemberTransfer) VoToPo(vo vo.VenueAndMemberVO) po.VenueAndMember {\n\tpo := po.VenueAndMember{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype VenueAndMemberService struct {\n}\n\nvar (\n\tvenueAndMemberService = VenueAndMemberService{}\n\tvenueAndMemberTransfer = transfer.VenueAndMemberTransfer{}\n)\n\nfunc (service *VenueAndMemberService) CreateVenueAndMember(logCtx *gin.Context, venueAndMember *po.VenueAndMember) error {\n\treturn Save(venueAndMember)\n}\n\nfunc (service *VenueAndMemberService) CreateVenueAndMemberWithTx(logCtx *gin.Context, venueAndMember *po.VenueAndMember, tx *gorm.DB) error {\n\treturn SaveWithTx(venueAndMember, tx)\n}\n\nfunc (service *VenueAndMemberService) UpdateVenueAndMember(logCtx *gin.Context, venueAndMember *po.VenueAndMember) error {\n\treturn Update(venueAndMember)\n}\n\nfunc (service *VenueAndMemberService) UpdateVenueAndMemberPartial(logCtx *gin.Context, venueAndMember *po.VenueAndMember) error {\n\treturn UpdateNotNull(venueAndMember)\n}\n\nfunc (service *VenueAndMemberService) UpdateVenueAndMemberPartialWithTx(logCtx *gin.Context, venueAndMember *po.VenueAndMember, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(venueAndMember, tx)\n}\n\nfunc (service *VenueAndMemberService) DeleteVenueAndMember(logCtx *gin.Context, id string) error {\n\treturn Delete(po.VenueAndMember{Id: &id})\n}\n\nfunc (service *VenueAndMemberService) FindVenueAndMemberById(logCtx *gin.Context, id string) (venueAndMember *po.VenueAndMember, err error) {\n\tvenueAndMember = &po.VenueAndMember{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(venueAndMember).Error\n\treturn\n}\n\nfunc (service *VenueAndMemberService) FindAllVenueAndMember(logCtx *gin.Context, reqDto *req.QueryVenueAndMemberReqDto) (list *[]po.VenueAndMember, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenueAndMember{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.VenueAndMember{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VenueAndMemberService) FindAllVenueAndMemberWithPagination(logCtx *gin.Context, reqDto *req.QueryVenueAndMemberReqDto) (list *[]po.VenueAndMember, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenueAndMember{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.VenueAndMember{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueAndMemberController struct{}\n\nvar (\n\tvenueAndMemberService  = impl.VenueAndMemberService{}\n\tvenueAndMemberTransfer = transfer.VenueAndMemberTransfer{}\n)\n\n// @Summary 添加门店会员关联\n// @Description 添加门店会员关联\n// @Tags 门店会员关联\n// @Accept json\n// @Produce json\n// @Param body body req.AddVenueAndMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueAndMemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-member/add [post]\nfunc (controller *VenueAndMemberController) AddVenueAndMember(ctx *gin.Context) {\n\treqDto := req.AddVenueAndMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvenueAndMember := po.VenueAndMember{}\n\tif reqDto.VenueId != nil {\n\t\tvenueAndMember.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.MemberId != nil {\n\t\tvenueAndMember.MemberId = reqDto.MemberId\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenueAndMember.Remark = reqDto.Remark\n\t}\n\terr = venueAndMemberService.CreateVenueAndMember(ctx, &venueAndMember)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueAndMemberTransfer.PoToVo(venueAndMember))\n}\n\n// @Summary 更新门店会员关联\n// @Description 更新门店会员关联\n// @Tags 门店会员关联\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVenueAndMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueAndMemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-member/update [post]\nfunc (controller *VenueAndMemberController) UpdateVenueAndMember(ctx *gin.Context) {\n\treqDto := req.UpdateVenueAndMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tvenueAndMember, err := venueAndMemberService.FindVenueAndMemberById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.VenueId != nil {\n\t\tvenueAndMember.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.MemberId != nil {\n\t\tvenueAndMember.MemberId = reqDto.MemberId\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenueAndMember.Remark = reqDto.Remark\n\t}\n\terr = venueAndMemberService.UpdateVenueAndMember(ctx, venueAndMember)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueAndMemberTransfer.PoToVo(*venueAndMember))\n}\n\n// @Summary 删除门店会员关联\n// @Description 删除门店会员关联\n// @Tags 门店会员关联\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVenueAndMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-member/delete [post]\nfunc (controller *VenueAndMemberController) DeleteVenueAndMember(ctx *gin.Context) {\n\treqDto := req.DeleteVenueAndMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = venueAndMemberService.DeleteVenueAndMember(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询门店会员关联\n// @Description 查询门店会员关联\n// @Tags 门店会员关联\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueAndMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VenueAndMemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-member/query [post]\nfunc (controller *VenueAndMemberController) QueryVenueAndMembers(ctx *gin.Context) {\n\treqDto := req.QueryVenueAndMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := venueAndMemberService.FindAllVenueAndMember(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.VenueAndMemberVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, venueAndMemberTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询门店会员关联列表\n// @Description 查询门店会员关联列表\n// @Tags 门店会员关联\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueAndMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.VenueAndMemberVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-member/list [post]\nfunc (a *VenueAndMemberController) ListVenueAndMembers(ctx *gin.Context) {\n\treqDto := req.QueryVenueAndMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := venueAndMemberService.FindAllVenueAndMemberWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.VenueAndMemberVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VenueAndMemberVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, venueAndMemberTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueAndMemberRoute struct {\n}\n\nfunc (s *VenueAndMemberRoute) InitVenueAndMemberRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvenueAndMemberController := controller.VenueAndMemberController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/venue-member/add\", venueAndMemberController.AddVenueAndMember)    //add\n\t\troute.POST(\"/api/venue-member/update\", venueAndMemberController.UpdateVenueAndMember) //update\n\t\troute.POST(\"/api/venue-member/delete\", venueAndMemberController.DeleteVenueAndMember) //delete\n\t\troute.POST(\"/api/venue-member/query\", venueAndMemberController.QueryVenueAndMembers)     //query\n\t\troute.POST(\"/api/venue-member/list\", venueAndMemberController.ListVenueAndMembers)     //list\n\t}\n}"}]