[{"po": "package po\n\n// VenueAndEmployee 门店和员工关联实体\ntype VenueAndEmployee struct {\n\tId         *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tVenueId    *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`    // 门店ID\n\tEmployeeId *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"` // 员工ID\n\tRemark     *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`       // 备注\n\tCtime      *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime      *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (VenueAndEmployee) TableName() string {\n\treturn \"venue_and_employee\"\n}\n\nfunc (v VenueAndEmployee) GetId() string {\n\treturn *v.Id\n}", "vo": "package vo\n\n// VenueAndEmployeeVO 门店和员工关联值对象\ntype VenueAndEmployeeVO struct {\n\tId         string `json:\"id\"`         // ID\n\tVenueId    string `json:\"venueId\"`    // 门店ID\n\tEmployeeId string `json:\"employeeId\"` // 员工ID\n\tRemark     string `json:\"remark\"`     // 备注\n\tCtime      int64  `json:\"ctime\"`      // 创建时间戳\n\tUtime      int64  `json:\"utime\"`      // 更新时间戳\n\tState      int    `json:\"state\"`      // 状态值\n\tVersion    int    `json:\"version\"`    // 版本号\n}", "req_add": "package req\n\n// AddVenueAndEmployeeReqDto 创建门店和员工关联请求DTO\ntype AddVenueAndEmployeeReqDto struct {\n\tVenueId    *string `json:\"venueId\"`    // 门店ID\n\tEmployeeId *string `json:\"employeeId\"` // 员工ID\n\tRemark     *string `json:\"remark\"`     // 备注\n}", "req_update": "package req\n\n// UpdateVenueAndEmployeeReqDto 更新门店和员工关联请求DTO\ntype UpdateVenueAndEmployeeReqDto struct {\n\tId         *string `json:\"id\"`         // ID\n\tVenueId    *string `json:\"venueId\"`    // 门店ID\n\tEmployeeId *string `json:\"employeeId\"` // 员工ID\n\tRemark     *string `json:\"remark\"`     // 备注\n}", "req_delete": "package req\n\n// DeleteVenueAndEmployeeReqDto 删除门店和员工关联请求DTO\ntype DeleteVenueAndEmployeeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryVenueAndEmployeeReqDto 查询门店和员工关联请求DTO\ntype QueryVenueAndEmployeeReqDto struct {\n\tId         *string `json:\"id\"`         // ID\n\tVenueId    *string `json:\"venueId\"`    // 门店ID\n\tEmployeeId *string `json:\"employeeId\"` // 员工ID\n\tRemark     *string `json:\"remark\"`     // 备注\n\tPageNum    *int    `json:\"pageNum\"`    // 页码\n\tPageSize   *int    `json:\"pageSize\"`   // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VenueAndEmployeeTransfer struct {\n}\n\nfunc (transfer *VenueAndEmployeeTransfer) PoToVo(po po.VenueAndEmployee) vo.VenueAndEmployeeVO {\n\tvo := vo.VenueAndEmployeeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VenueAndEmployeeTransfer) VoToPo(vo vo.VenueAndEmployeeVO) po.VenueAndEmployee {\n\tpo := po.VenueAndEmployee{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype VenueAndEmployeeService struct {\n}\n\nvar (\n\tvenueAndEmployeeService = VenueAndEmployeeService{}\n\tvenueAndEmployeeTransfer = transfer.VenueAndEmployeeTransfer{}\n)\n\nfunc (service *VenueAndEmployeeService) CreateVenueAndEmployee(logCtx *gin.Context, venueAndEmployee *po.VenueAndEmployee) error {\n\treturn Save(venueAndEmployee)\n}\n\nfunc (service *VenueAndEmployeeService) CreateVenueAndEmployeeWithTx(logCtx *gin.Context, venueAndEmployee *po.VenueAndEmployee, tx *gorm.DB) error {\n\treturn SaveWithTx(venueAndEmployee, tx)\n}\n\nfunc (service *VenueAndEmployeeService) UpdateVenueAndEmployee(logCtx *gin.Context, venueAndEmployee *po.VenueAndEmployee) error {\n\treturn Update(venueAndEmployee)\n}\n\nfunc (service *VenueAndEmployeeService) UpdateVenueAndEmployeePartial(logCtx *gin.Context, venueAndEmployee *po.VenueAndEmployee) error {\n\treturn UpdateNotNull(venueAndEmployee)\n}\n\nfunc (service *VenueAndEmployeeService) UpdateVenueAndEmployeePartialWithTx(logCtx *gin.Context, venueAndEmployee *po.VenueAndEmployee, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(venueAndEmployee, tx)\n}\n\nfunc (service *VenueAndEmployeeService) DeleteVenueAndEmployee(logCtx *gin.Context, id string) error {\n\treturn Delete(po.VenueAndEmployee{Id: &id})\n}\n\nfunc (service *VenueAndEmployeeService) FindVenueAndEmployeeById(logCtx *gin.Context, id string) (venueAndEmployee *po.VenueAndEmployee, err error) {\n\tvenueAndEmployee = &po.VenueAndEmployee{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(venueAndEmployee).Error\n\treturn\n}\n\nfunc (service *VenueAndEmployeeService) FindAllVenueAndEmployee(logCtx *gin.Context, reqDto *req.QueryVenueAndEmployeeReqDto) (list *[]po.VenueAndEmployee, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenueAndEmployee{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.VenueAndEmployee{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VenueAndEmployeeService) FindAllVenueAndEmployeeWithPagination(logCtx *gin.Context, reqDto *req.QueryVenueAndEmployeeReqDto) (list *[]po.VenueAndEmployee, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenueAndEmployee{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.VenueAndEmployee{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueAndEmployeeController struct{}\n\nvar (\n\tvenueAndEmployeeService  = impl.VenueAndEmployeeService{}\n\tvenueAndEmployeeTransfer = transfer.VenueAndEmployeeTransfer{}\n)\n\n// @Summary 添加门店和员工关联\n// @Description 添加门店和员工关联\n// @Tags 门店和员工关联\n// @Accept json\n// @Produce json\n// @Param body body req.AddVenueAndEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueAndEmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-employee/add [post]\nfunc (controller *VenueAndEmployeeController) AddVenueAndEmployee(ctx *gin.Context) {\n\treqDto := req.AddVenueAndEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvenueAndEmployee := po.VenueAndEmployee{}\n\tif reqDto.VenueId != nil {\n\t\tvenueAndEmployee.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tvenueAndEmployee.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenueAndEmployee.Remark = reqDto.Remark\n\t}\n\n\terr = venueAndEmployeeService.CreateVenueAndEmployee(ctx, &venueAndEmployee)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueAndEmployeeTransfer.PoToVo(venueAndEmployee))\n}\n\n// @Summary 更新门店和员工关联\n// @Description 更新门店和员工关联\n// @Tags 门店和员工关联\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVenueAndEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueAndEmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-employee/update [post]\nfunc (controller *VenueAndEmployeeController) UpdateVenueAndEmployee(ctx *gin.Context) {\n\treqDto := req.UpdateVenueAndEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tvenueAndEmployee, err := venueAndEmployeeService.FindVenueAndEmployeeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tvenueAndEmployee.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tvenueAndEmployee.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenueAndEmployee.Remark = reqDto.Remark\n\t}\n\n\terr = venueAndEmployeeService.UpdateVenueAndEmployee(ctx, venueAndEmployee)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueAndEmployeeTransfer.PoToVo(*venueAndEmployee))\n}\n\n// @Summary 删除门店和员工关联\n// @Description 删除门店和员工关联\n// @Tags 门店和员工关联\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVenueAndEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-employee/delete [post]\nfunc (controller *VenueAndEmployeeController) DeleteVenueAndEmployee(ctx *gin.Context) {\n\treqDto := req.DeleteVenueAndEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = venueAndEmployeeService.DeleteVenueAndEmployee(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询门店和员工关联\n// @Description 查询门店和员工关联\n// @Tags 门店和员工关联\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueAndEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VenueAndEmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-employee/query [post]\nfunc (controller *VenueAndEmployeeController) QueryVenueAndEmployees(ctx *gin.Context) {\n\treqDto := req.QueryVenueAndEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := venueAndEmployeeService.FindAllVenueAndEmployee(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.VenueAndEmployeeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, venueAndEmployeeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询门店和员工关联列表\n// @Description 查询门店和员工关联列表\n// @Tags 门店和员工关联\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueAndEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.VenueAndEmployeeVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-employee/list [post]\nfunc (controller *VenueAndEmployeeController) ListVenueAndEmployees(ctx *gin.Context) {\n\treqDto := req.QueryVenueAndEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := venueAndEmployeeService.FindAllVenueAndEmployeeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.VenueAndEmployeeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VenueAndEmployeeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, venueAndEmployeeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueAndEmployeeRoute struct {\n}\n\nfunc (s *VenueAndEmployeeRoute) InitVenueAndEmployeeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvenueAndEmployeeController := controller.VenueAndEmployeeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/venue-employee/add\", venueAndEmployeeController.AddVenueAndEmployee)       // add\n\t\troute.POST(\"/api/venue-employee/update\", venueAndEmployeeController.UpdateVenueAndEmployee)   // update\n\t\troute.POST(\"/api/venue-employee/delete\", venueAndEmployeeController.DeleteVenueAndEmployee)   // delete\n\t\troute.POST(\"/api/venue-employee/query\", venueAndEmployeeController.QueryVenueAndEmployees)    // query\n\t\troute.POST(\"/api/venue-employee/list\", venueAndEmployeeController.ListVenueAndEmployees)     // list\n\t}\n}"}]