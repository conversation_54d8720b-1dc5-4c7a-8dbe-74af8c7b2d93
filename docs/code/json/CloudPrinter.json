[{"po": "package po\n\n// CloudPrinter 云打印机实体\ntype CloudPrinter struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tName          *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                  // 云打印机名称\n\tCode          *string `gorm:\"column:code;type:varchar(64);default:''\" json:\"code\"`                  // 云打印机编码\n\tSecretKey     *string `gorm:\"column:secret_key;type:varchar(64);default:''\" json:\"secretKey\"`        // 云打印机密钥\n\tSpecification *string `gorm:\"column:specification;type:varchar(64);default:''\" json:\"specification\"` // 云打印机规格\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                         // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                         // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                         // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                     // 版本号\n}\n\n// TableName 设置表名\nfunc (CloudPrinter) TableName() string {\n\treturn \"cloud_printer\"\n}\n\nfunc (c CloudPrinter) GetId() string {\n\treturn *c.Id\n}\n", "req_add": "package req\n\n// AddCloudPrinterReqDto 创建云打印机请求DTO\ntype AddCloudPrinterReqDto struct {\n\tName          *string `json:\"name\"`          // 云打印机名称\n\tCode          *string `json:\"code\"`          // 云打印机编码\n\tSecretKey     *string `json:\"secretKey\"`     // 云打印机密钥\n\tSpecification *string `json:\"specification\"` // 云打印机规格\n}\n", "req_update": "package req\n\ntype UpdateCloudPrinterReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tName          *string `json:\"name\"`          // 云打印机名称\n\tCode          *string `json:\"code\"`          // 云打印机编码\n\tSecretKey     *string `json:\"secretKey\"`     // 云打印机密钥\n\tSpecification *string `json:\"specification\"` // 云打印机规格\n}\n", "req_delete": "package req\n\ntype DeleteCloudPrinterReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCloudPrinterReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tName          *string `json:\"name\"`          // 云打印机名称\n\tCode          *string `json:\"code\"`          // 云打印机编码\n\tSecretKey     *string `json:\"secretKey\"`     // 云打印机密钥\n\tSpecification *string `json:\"specification\"` // 云打印机规格\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "vo": "package vo\n\n// CloudPrinterVO 云打印机信息值对象\ntype CloudPrinterVO struct {\n\tId            string `json:\"id\"`            // ID\n\tName          string `json:\"name\"`          // 云打印机名称\n\tCode          string `json:\"code\"`          // 云打印机编码\n\tSecretKey     string `json:\"secretKey\"`     // 云打印机密钥\n\tSpecification string `json:\"specification\"` // 云打印机规格\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CloudPrinterTransfer struct {\n}\n\nfunc (transfer *CloudPrinterTransfer) PoToVo(po po.CloudPrinter) vo.CloudPrinterVO {\n\tvo := vo.CloudPrinterVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CloudPrinterTransfer) VoToPo(vo vo.CloudPrinterVO) po.CloudPrinter {\n\tpo := po.CloudPrinter{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CloudPrinterService struct {\n}\n\nfunc (service *CloudPrinterService) CreateCloudPrinter(logCtx *gin.Context, cloudPrinter *po.CloudPrinter) error {\n\treturn Save(cloudPrinter)\n}\n\nfunc (service *CloudPrinterService) UpdateCloudPrinter(logCtx *gin.Context, cloudPrinter *po.CloudPrinter) error {\n\treturn Update(cloudPrinter)\n}\n\nfunc (service *CloudPrinterService) DeleteCloudPrinter(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CloudPrinter{Id: &id})\n}\n\nfunc (service *CloudPrinterService) FindCloudPrinterById(logCtx *gin.Context, id string) (cloudPrinter *po.CloudPrinter, err error) {\n\tcloudPrinter = &po.CloudPrinter{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(cloudPrinter).Error\n\treturn\n}\n\nfunc (service *CloudPrinterService) FindAllCloudPrinter(logCtx *gin.Context, reqDto *req.QueryCloudPrinterReqDto) (list *[]po.CloudPrinter, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CloudPrinter{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.Code != nil && *reqDto.Code != \"\" {\n\t\tdb = db.Where(\"code=?\", *reqDto.Code)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CloudPrinter{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CloudPrinterService) FindAllCloudPrinterWithPagination(logCtx *gin.Context, reqDto *req.QueryCloudPrinterReqDto) (list *[]po.CloudPrinter, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CloudPrinter{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.Code != nil && *reqDto.Code != \"\" {\n\t\tdb = db.Where(\"code=?\", *reqDto.Code)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CloudPrinter{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CloudPrinterController struct{}\n\nvar (\n\tcloudPrinterService  = impl.CloudPrinterService{}\n\tcloudPrinterTransfer = transfer.CloudPrinterTransfer{}\n)\n\n// @Summary 添加云打印机\n// @Description 添加云打印机\n// @Tags 云打印机\n// @Accept json\n// @Produce json\n// @Param body body req.AddCloudPrinterReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CloudPrinterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cloudPrinter/add [post]\nfunc (controller *CloudPrinterController) AddCloudPrinter(ctx *gin.Context) {\n\treqDto := req.AddCloudPrinterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcloudPrinter := po.CloudPrinter{}\n\tif reqDto.Name != nil {\n\t\tcloudPrinter.Name = reqDto.Name\n\t}\n\tif reqDto.Code != nil {\n\t\tcloudPrinter.Code = reqDto.Code\n\t}\n\tif reqDto.SecretKey != nil {\n\t\tcloudPrinter.SecretKey = reqDto.SecretKey\n\t}\n\tif reqDto.Specification != nil {\n\t\tcloudPrinter.Specification = reqDto.Specification\n\t}\n\terr = cloudPrinterService.CreateCloudPrinter(ctx, &cloudPrinter)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, cloudPrinterTransfer.PoToVo(cloudPrinter))\n}\n\n// @Summary 更新云打印机\n// @Description 更新云打印机\n// @Tags 云打印机\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCloudPrinterReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CloudPrinterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cloudPrinter/update [post]\nfunc (controller *CloudPrinterController) UpdateCloudPrinter(ctx *gin.Context) {\n\treqDto := req.UpdateCloudPrinterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcloudPrinter, err := cloudPrinterService.FindCloudPrinterById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tcloudPrinter.Name = reqDto.Name\n\t}\n\tif reqDto.Code != nil {\n\t\tcloudPrinter.Code = reqDto.Code\n\t}\n\tif reqDto.SecretKey != nil {\n\t\tcloudPrinter.SecretKey = reqDto.SecretKey\n\t}\n\tif reqDto.Specification != nil {\n\t\tcloudPrinter.Specification = reqDto.Specification\n\t}\n\terr = cloudPrinterService.UpdateCloudPrinter(ctx, cloudPrinter)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, cloudPrinterTransfer.PoToVo(*cloudPrinter))\n}\n\n// @Summary 删除云打印机\n// @Description 删除云打印机\n// @Tags 云打印机\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCloudPrinterReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cloudPrinter/delete [post]\nfunc (controller *CloudPrinterController) DeleteCloudPrinter(ctx *gin.Context) {\n\treqDto := req.DeleteCloudPrinterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = cloudPrinterService.DeleteCloudPrinter(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询云打印机\n// @Description 查询云打印机\n// @Tags 云打印机\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCloudPrinterReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CloudPrinterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cloudPrinter/query [post]\nfunc (controller *CloudPrinterController) QueryCloudPrinters(ctx *gin.Context) {\n\treqDto := req.QueryCloudPrinterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := cloudPrinterService.FindAllCloudPrinter(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CloudPrinterVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, cloudPrinterTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询云打印机列表\n// @Description 查询云打印机列表\n// @Tags 云打印机\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCloudPrinterReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CloudPrinterVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cloudPrinter/list [post]\nfunc (a *CloudPrinterController) ListCloudPrinters(ctx *gin.Context) {\n\treqDto := req.QueryCloudPrinterReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := cloudPrinterService.FindAllCloudPrinterWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CloudPrinterVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CloudPrinterVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, cloudPrinterTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CloudPrinterRoute struct {\n}\n\nfunc (s *CloudPrinterRoute) InitCloudPrinterRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcloudPrinterController := controller.CloudPrinterController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/cloudPrinter/add\", cloudPrinterController.AddCloudPrinter)       //add\n\t\troute.POST(\"/api/cloudPrinter/update\", cloudPrinterController.UpdateCloudPrinter) //update\n\t\troute.POST(\"/api/cloudPrinter/delete\", cloudPrinterController.DeleteCloudPrinter) //delete\n\t\troute.POST(\"/api/cloudPrinter/query\", cloudPrinterController.QueryCloudPrinters)  //query\n\t\troute.POST(\"/api/cloudPrinter/list\", cloudPrinterController.ListCloudPrinters)   //list\n\t}\n}\n"}]