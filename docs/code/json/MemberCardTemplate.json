[{"po": "package po\n\n// MemberCardTemplate 会员卡模板实体\ntype MemberCardTemplate struct {\n\tId                    *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                  *string  `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                          // 模板名称\n\tLogo                  *string  `gorm:\"column:logo;type:varchar(255);default:''\" json:\"logo\"`                          // 卡片logo\n\tBalanceUsageScope     *string  `gorm:\"column:balance_usage_scope;type:varchar(255);default:''\" json:\"balanceUsageScope\"`     // 余额使用范围\n\tDistributionChannels  *string  `gorm:\"column:distribution_channels;type:varchar(255);default:''\" json:\"distributionChannels\"`  // 分发渠道\n\tValidityPeriod        *string  `gorm:\"column:validity_period;type:varchar(255);default:''\" json:\"validityPeriod\"`        // 有效期\n\tCardFee               *int64   `gorm:\"column:card_fee;type:bigint;default:0\" json:\"cardFee\"`                           // 办卡费用\n\tReplacementFee        *int64   `gorm:\"column:replacement_fee;type:bigint;default:0\" json:\"replacementFee\"`                // 补卡费用\n\tRenewalFee            *int64   `gorm:\"column:renewal_fee;type:bigint;default:0\" json:\"renewalFee\"`                        // 续费费用\n\tMinimumRechargeAmount *int64   `gorm:\"column:minimum_recharge_amount;type:bigint;default:0\" json:\"minimumRechargeAmount\"` // 最低充值金额\n\tRoomDiscount          *float32 `gorm:\"column:room_discount;type:float;default:0\" json:\"roomDiscount\"`                    // 房间折扣\n\tProductDiscount       *float32 `gorm:\"column:product_discount;type:float;default:0\" json:\"productDiscount\"`             // 商品折扣\n\tConsumptionPointsRatio *float32 `gorm:\"column:consumption_points_ratio;type:float;default:0\" json:\"consumptionPointsRatio\"` // 消费积分比例\n\tRechargePointsRatio    *float32 `gorm:\"column:recharge_points_ratio;type:float;default:0\" json:\"rechargePointsRatio\"`       // 充值积分比例\n\tUpgradeConditions     *string  `gorm:\"column:upgrade_conditions;type:varchar(255);default:''\" json:\"upgradeConditions\"`     // 升级条件\n\tDowngradeConditions   *string  `gorm:\"column:downgrade_conditions;type:varchar(255);default:''\" json:\"downgradeConditions\"` // 降级条件\n\tBirthdayPerks         *string  `gorm:\"column:birthday_perks;type:varchar(255);default:''\" json:\"birthdayPerks\"`           // 生日特权\n\tSignupUpgradePerks    *string  `gorm:\"column:signup_upgrade_perks;type:varchar(255);default:''\" json:\"signupUpgradePerks\"` // 注册升级特权\n\tMonthlyCoupons        *string  `gorm:\"column:monthly_coupons;type:varchar(255);default:''\" json:\"monthlyCoupons\"`         // 每月优惠券\n\tConsumptionPeriods    *string  `gorm:\"column:consumption_periods;type:varchar(255);default:''\" json:\"consumptionPeriods\"` // 消费周期\n\tPaymentRestrictions   *string  `gorm:\"column:payment_restrictions;type:varchar(255);default:''\" json:\"paymentRestrictions\"` // 支付限制\n\tCtime                 *int64   `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                                 // 创建时间\n\tUtime                 *int64   `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                                 // 更新时间\n\tState                 *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                                   // 状态\n\tVersion               *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                               // 版本\n}\n\n// TableName 设置表名\nfunc (MemberCardTemplate) TableName() string {\n\treturn \"member_card_template\"\n}\n\nfunc (m MemberCardTemplate) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MemberCardTemplateVO 会员卡模板值对象\ntype MemberCardTemplateVO struct {\n\tId                    string  `json:\"id\"`                    // ID\n\tName                  string  `json:\"name\"`                  // 模板名称\n\tLogo                  string  `json:\"logo\"`                  // 卡片logo\n\tBalanceUsageScope     string  `json:\"balanceUsageScope\"`     // 余额使用范围\n\tDistributionChannels  string  `json:\"distributionChannels\"`  // 分发渠道\n\tValidityPeriod        string  `json:\"validityPeriod\"`        // 有效期\n\tCardFee               int64   `json:\"cardFee\"`               // 办卡费用\n\tReplacementFee        int64   `json:\"replacementFee\"`        // 补卡费用\n\tRenewalFee            int64   `json:\"renewalFee\"`            // 续费费用\n\tMinimumRechargeAmount int64   `json:\"minimumRechargeAmount\"` // 最低充值金额\n\tRoomDiscount          float32 `json:\"roomDiscount\"`          // 房间折扣\n\tProductDiscount       float32 `json:\"productDiscount\"`       // 商品折扣\n\tConsumptionPointsRatio float32 `json:\"consumptionPointsRatio\"` // 消费积分比例\n\tRechargePointsRatio    float32 `json:\"rechargePointsRatio\"`    // 充值积分比例\n\tUpgradeConditions     string  `json:\"upgradeConditions\"`     // 升级条件\n\tDowngradeConditions   string  `json:\"downgradeConditions\"`   // 降级条件\n\tBirthdayPerks         string  `json:\"birthdayPerks\"`         // 生日特权\n\tSignupUpgradePerks    string  `json:\"signupUpgradePerks\"`    // 注册升级特权\n\tMonthlyCoupons        string  `json:\"monthlyCoupons\"`        // 每月优惠券\n\tConsumptionPeriods    string  `json:\"consumptionPeriods\"`    // 消费周期\n\tPaymentRestrictions   string  `json:\"paymentRestrictions\"`   // 支付限制\n\tCtime                 int64   `json:\"ctime\"`                 // 创建时间\n\tUtime                 int64   `json:\"utime\"`                 // 更新时间\n\tState                 int     `json:\"state\"`                 // 状态\n\tVersion               int     `json:\"version\"`               // 版本\n}\n", "req_add": "package req\n\n// AddMemberCardTemplateReqDto 创建会员卡模板请求DTO\ntype AddMemberCardTemplateReqDto struct {\n\tName                  *string  `json:\"name\"`                  // 模板名称\n\tLogo                  *string  `json:\"logo\"`                  // 卡片logo\n\tBalanceUsageScope     *string  `json:\"balanceUsageScope\"`     // 余额使用范围\n\tDistributionChannels  *string  `json:\"distributionChannels\"`  // 分发渠道\n\tValidityPeriod        *string  `json:\"validityPeriod\"`        // 有效期\n\tCardFee               *int64   `json:\"cardFee\"`               // 办卡费用\n\tReplacementFee        *int64   `json:\"replacementFee\"`        // 补卡费用\n\tRenewalFee            *int64   `json:\"renewalFee\"`            // 续费费用\n\tMinimumRechargeAmount *int64   `json:\"minimumRechargeAmount\"` // 最低充值金额\n\tRoomDiscount          *float32 `json:\"roomDiscount\"`          // 房间折扣\n\tProductDiscount       *float32 `json:\"productDiscount\"`       // 商品折扣\n\tConsumptionPointsRatio *float32 `json:\"consumptionPointsRatio\"` // 消费积分比例\n\tRechargePointsRatio    *float32 `json:\"rechargePointsRatio\"`    // 充值积分比例\n\tUpgradeConditions     *string  `json:\"upgradeConditions\"`     // 升级条件\n\tDowngradeConditions   *string  `json:\"downgradeConditions\"`   // 降级条件\n\tBirthdayPerks         *string  `json:\"birthdayPerks\"`         // 生日特权\n\tSignupUpgradePerks    *string  `json:\"signupUpgradePerks\"`    // 注册升级特权\n\tMonthlyCoupons        *string  `json:\"monthlyCoupons\"`        // 每月优惠券\n\tConsumptionPeriods    *string  `json:\"consumptionPeriods\"`    // 消费周期\n\tPaymentRestrictions   *string  `json:\"paymentRestrictions\"`   // 支付限制\n}\n", "req_update": "package req\n\n// UpdateMemberCardTemplateReqDto 更新会员卡模板请求DTO\ntype UpdateMemberCardTemplateReqDto struct {\n\tId                    *string  `json:\"id\"`                    // ID\n\tName                  *string  `json:\"name\"`                  // 模板名称\n\tLogo                  *string  `json:\"logo\"`                  // 卡片logo\n\tBalanceUsageScope     *string  `json:\"balanceUsageScope\"`     // 余额使用范围\n\tDistributionChannels  *string  `json:\"distributionChannels\"`  // 分发渠道\n\tValidityPeriod        *string  `json:\"validityPeriod\"`        // 有效期\n\tCardFee               *int64   `json:\"cardFee\"`               // 办卡费用\n\tReplacementFee        *int64   `json:\"replacementFee\"`        // 补卡费用\n\tRenewalFee            *int64   `json:\"renewalFee\"`            // 续费费用\n\tMinimumRechargeAmount *int64   `json:\"minimumRechargeAmount\"` // 最低充值金额\n\tRoomDiscount          *float32 `json:\"roomDiscount\"`          // 房间折扣\n\tProductDiscount       *float32 `json:\"productDiscount\"`       // 商品折扣\n\tConsumptionPointsRatio *float32 `json:\"consumptionPointsRatio\"` // 消费积分比例\n\tRechargePointsRatio    *float32 `json:\"rechargePointsRatio\"`    // 充值积分比例\n\tUpgradeConditions     *string  `json:\"upgradeConditions\"`     // 升级条件\n\tDowngradeConditions   *string  `json:\"downgradeConditions\"`   // 降级条件\n\tBirthdayPerks         *string  `json:\"birthdayPerks\"`         // 生日特权\n\tSignupUpgradePerks    *string  `json:\"signupUpgradePerks\"`    // 注册升级特权\n\tMonthlyCoupons        *string  `json:\"monthlyCoupons\"`        // 每月优惠券\n\tConsumptionPeriods    *string  `json:\"consumptionPeriods\"`    // 消费周期\n\tPaymentRestrictions   *string  `json:\"paymentRestrictions\"`   // 支付限制\n}\n", "req_delete": "package req\n\n// DeleteMemberCardTemplateReqDto 删除会员卡模板请求DTO\ntype DeleteMemberCardTemplateReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryMemberCardTemplateReqDto 查询会员卡模板请求DTO\ntype QueryMemberCardTemplateReqDto struct {\n\tId                    *string  `json:\"id\"`                    // ID\n\tName                  *string  `json:\"name\"`                  // 模板名称\n\tLogo                  *string  `json:\"logo\"`                  // 卡片logo\n\tBalanceUsageScope     *string  `json:\"balanceUsageScope\"`     // 余额使用范围\n\tDistributionChannels  *string  `json:\"distributionChannels\"`  // 分发渠道\n\tValidityPeriod        *string  `json:\"validityPeriod\"`        // 有效期\n\tCardFee               *int64   `json:\"cardFee\"`               // 办卡费用\n\tReplacementFee        *int64   `json:\"replacementFee\"`        // 补卡费用\n\tRenewalFee            *int64   `json:\"renewalFee\"`            // 续费费用\n\tMinimumRechargeAmount *int64   `json:\"minimumRechargeAmount\"` // 最低充值金额\n\tRoomDiscount          *float32 `json:\"roomDiscount\"`          // 房间折扣\n\tProductDiscount       *float32 `json:\"productDiscount\"`       // 商品折扣\n\tConsumptionPointsRatio *float32 `json:\"consumptionPointsRatio\"` // 消费积分比例\n\tRechargePointsRatio    *float32 `json:\"rechargePointsRatio\"`    // 充值积分比例\n\tUpgradeConditions     *string  `json:\"upgradeConditions\"`     // 升级条件\n\tDowngradeConditions   *string  `json:\"downgradeConditions\"`   // 降级条件\n\tBirthdayPerks         *string  `json:\"birthdayPerks\"`         // 生日特权\n\tSignupUpgradePerks    *string  `json:\"signupUpgradePerks\"`    // 注册升级特权\n\tMonthlyCoupons        *string  `json:\"monthlyCoupons\"`        // 每月优惠券\n\tConsumptionPeriods    *string  `json:\"consumptionPeriods\"`    // 消费周期\n\tPaymentRestrictions   *string  `json:\"paymentRestrictions\"`   // 支付限制\n\tPageNum               *int     `json:\"pageNum\"`               // 页码\n\tPageSize              *int     `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberCardTemplateTransfer struct {\n}\n\nfunc (transfer *MemberCardTemplateTransfer) PoToVo(po po.MemberCardTemplate) vo.MemberCardTemplateVO {\n\tvo := vo.MemberCardTemplateVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MemberCardTemplateTransfer) VoToPo(vo vo.MemberCardTemplateVO) po.MemberCardTemplate {\n\tpo := po.MemberCardTemplate{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardTemplateService struct {\n}\n\nfunc (service *MemberCardTemplateService) CreateMemberCardTemplate(logCtx *gin.Context, template *po.MemberCardTemplate) error {\n\treturn Save(template)\n}\n\nfunc (service *MemberCardTemplateService) UpdateMemberCardTemplate(logCtx *gin.Context, template *po.MemberCardTemplate) error {\n\treturn Update(template)\n}\n\nfunc (service *MemberCardTemplateService) DeleteMemberCardTemplate(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MemberCardTemplate{Id: &id})\n}\n\nfunc (service *MemberCardTemplateService) FindMemberCardTemplateById(logCtx *gin.Context, id string) (template *po.MemberCardTemplate, err error) {\n\ttemplate = &po.MemberCardTemplate{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(template).Error\n\treturn\n}\n\nfunc (service *MemberCardTemplateService) FindAllMemberCardTemplate(logCtx *gin.Context, reqDto *req.QueryMemberCardTemplateReqDto) (list *[]po.MemberCardTemplate, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberCardTemplate{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MemberCardTemplate{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MemberCardTemplateService) FindAllMemberCardTemplateWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberCardTemplateReqDto) (list *[]po.MemberCardTemplate, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberCardTemplate{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MemberCardTemplate{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardTemplateController struct{}\n\nvar (\n\tmemberCardTemplateService  = impl.MemberCardTemplateService{}\n\tmemberCardTemplateTransfer = transfer.MemberCardTemplateTransfer{}\n)\n\n// @Summary 添加会员卡模板\n// @Description 添加会员卡模板\n// @Tags 会员卡模板\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberCardTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberCardTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardTemplate/add [post]\nfunc (controller *MemberCardTemplateController) AddMemberCardTemplate(ctx *gin.Context) {\n\treqDto := req.AddMemberCardTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\ttemplate := po.MemberCardTemplate{}\n\tcopier.Copy(&template, &reqDto)\n\n\terr = memberCardTemplateService.CreateMemberCardTemplate(ctx, &template)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberCardTemplateTransfer.PoToVo(template))\n}\n\n// @Summary 更新会员卡模板\n// @Description 更新会员卡模板\n// @Tags 会员卡模板\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberCardTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberCardTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardTemplate/update [post]\nfunc (controller *MemberCardTemplateController) UpdateMemberCardTemplate(ctx *gin.Context) {\n\treqDto := req.UpdateMemberCardTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\ttemplate, err := memberCardTemplateService.FindMemberCardTemplateById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tcopier.Copy(template, &reqDto)\n\n\terr = memberCardTemplateService.UpdateMemberCardTemplate(ctx, template)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberCardTemplateTransfer.PoToVo(*template))\n}\n\n// @Summary 删除会员卡模板\n// @Description 删除会员卡模板\n// @Tags 会员卡模板\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberCardTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardTemplate/delete [post]\nfunc (controller *MemberCardTemplateController) DeleteMemberCardTemplate(ctx *gin.Context) {\n\treqDto := req.DeleteMemberCardTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = memberCardTemplateService.DeleteMemberCardTemplate(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员卡模板\n// @Description 查询会员卡模板\n// @Tags 会员卡模板\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberCardTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberCardTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardTemplate/query [post]\nfunc (controller *MemberCardTemplateController) QueryMemberCardTemplates(ctx *gin.Context) {\n\treqDto := req.QueryMemberCardTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := memberCardTemplateService.FindAllMemberCardTemplate(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MemberCardTemplateVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, memberCardTemplateTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询会员卡模板列表\n// @Description 查询会员卡模板列表\n// @Tags 会员卡模板\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberCardTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberCardTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardTemplate/list [post]\nfunc (controller *MemberCardTemplateController) ListMemberCardTemplates(ctx *gin.Context) {\n\treqDto := req.QueryMemberCardTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := memberCardTemplateService.FindAllMemberCardTemplateWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MemberCardTemplateVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MemberCardTemplateVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberCardTemplateTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardTemplateRoute struct {\n}\n\nfunc (s *MemberCardTemplateRoute) InitMemberCardTemplateRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmemberCardTemplateController := controller.MemberCardTemplateController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/memberCardTemplate/add\", memberCardTemplateController.AddMemberCardTemplate)       //add\n\t\troute.POST(\"/api/memberCardTemplate/update\", memberCardTemplateController.UpdateMemberCardTemplate)   //update\n\t\troute.POST(\"/api/memberCardTemplate/delete\", memberCardTemplateController.DeleteMemberCardTemplate)   //delete\n\t\troute.POST(\"/api/memberCardTemplate/query\", memberCardTemplateController.QueryMemberCardTemplates)    //query\n\t\troute.POST(\"/api/memberCardTemplate/list\", memberCardTemplateController.ListMemberCardTemplates)     //list\n\t}\n}\n"}]