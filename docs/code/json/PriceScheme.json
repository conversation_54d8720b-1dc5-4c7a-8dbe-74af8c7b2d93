[{"po": "package po\n\n// PriceScheme 价格方案实体\ntype PriceScheme struct {\n\tId                  *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tName                *string  `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                // 价格方案名称\n\tRoomType            *string  `gorm:\"column:room_type;type:varchar(64);default:''\" json:\"roomType\"`        // 房间类型\n\tDistributionChannel *string  `gorm:\"column:distribution_channel;type:varchar(64);default:''\" json:\"distributionChannel\"` // 分销渠道\n\tConsumptionMode     *string  `gorm:\"column:consumption_mode;type:varchar(64);default:''\" json:\"consumptionMode\"` // 消费模式\n\tHasMinimumCharge    *bool    `gorm:\"column:has_minimum_charge;type:tinyint(1);default:0\" json:\"hasMinimumCharge\"` // 是否有最低消费\n\tMinimumCharge       *int64   `gorm:\"column:minimum_charge;type:bigint;default:0\" json:\"minimumCharge\"`   // 最低消费金额\n\tDefaultItems        *string  `gorm:\"column:default_items;type:text\" json:\"defaultItems\"`               // 默认包含项目\n\tOptionalItems       *string  `gorm:\"column:optional_items;type:text\" json:\"optionalItems\"`             // 可选项目\n\tFreeItems          *string  `gorm:\"column:free_items;type:text\" json:\"freeItems\"`                     // 免费项目\n\tMemberPrice        *int64   `gorm:\"column:member_price;type:bigint;default:0\" json:\"memberPrice\"`       // 会员价格\n\tMemberDiscount     *float32 `gorm:\"column:member_discount;type:float;default:0\" json:\"memberDiscount\"` // 会员折扣\n\tAreaPrice          *int64   `gorm:\"column:area_price;type:bigint;default:0\" json:\"areaPrice\"`           // 区域价格\n\tAreaMemberPrice    *int64   `gorm:\"column:area_member_price;type:bigint;default:0\" json:\"areaMemberPrice\"` // 区域会员价格\n\tHolidayPrice       *int64   `gorm:\"column:holiday_price;type:bigint;default:0\" json:\"holidayPrice\"`     // 节假日价格\n\tIsEnabled          *bool    `gorm:\"column:is_enabled;type:tinyint(1);default:0\" json:\"isEnabled\"`       // 是否启用\n\tSupportsPoints     *bool    `gorm:\"column:supports_points;type:tinyint(1);default:0\" json:\"supportsPoints\"` // 是否支持积分\n\tConsumptionTimeSlot *string  `gorm:\"column:consumption_time_slot;type:varchar(255);default:''\" json:\"consumptionTimeSlot\"` // 消费时段\n\tBuyAndGiftScheme   *string  `gorm:\"column:buy_and_gift_scheme;type:text\" json:\"buyAndGiftScheme\"`     // 买赠方案\n\tCtime              *int64   `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime              *int64   `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                   // 更新时间\n\tState              *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态\n\tVersion            *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (PriceScheme) TableName() string {\n\treturn \"price_scheme\"\n}\n\nfunc (p PriceScheme) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PriceSchemeVO 价格方案值对象\ntype PriceSchemeVO struct {\n\tId                  string  `json:\"id\"`                  // ID\n\tName                string  `json:\"name\"`                // 价格方案名称\n\tRoomType            string  `json:\"roomType\"`            // 房间类型\n\tDistributionChannel string  `json:\"distributionChannel\"` // 分销渠道\n\tConsumptionMode     string  `json:\"consumptionMode\"`     // 消费模式\n\tHasMinimumCharge    bool    `json:\"hasMinimumCharge\"`    // 是否有最低消费\n\tMinimumCharge       int64   `json:\"minimumCharge\"`       // 最低消费金额\n\tDefaultItems        string  `json:\"defaultItems\"`        // 默认包含项目\n\tOptionalItems       string  `json:\"optionalItems\"`       // 可选项目\n\tFreeItems          string  `json:\"freeItems\"`          // 免费项目\n\tMemberPrice        int64   `json:\"memberPrice\"`        // 会员价格\n\tMemberDiscount     float32 `json:\"memberDiscount\"`     // 会员折扣\n\tAreaPrice          int64   `json:\"areaPrice\"`          // 区域价格\n\tAreaMemberPrice    int64   `json:\"areaMemberPrice\"`    // 区域会员价格\n\tHolidayPrice       int64   `json:\"holidayPrice\"`       // 节假日价格\n\tIsEnabled          bool    `json:\"isEnabled\"`          // 是否启用\n\tSupportsPoints     bool    `json:\"supportsPoints\"`     // 是否支持积分\n\tConsumptionTimeSlot string  `json:\"consumptionTimeSlot\"` // 消费时段\n\tBuyAndGiftScheme   string  `json:\"buyAndGiftScheme\"`   // 买赠方案\n\tCtime              int64   `json:\"ctime\"`              // 创建时间\n\tUtime              int64   `json:\"utime\"`              // 更新时间\n\tState              int     `json:\"state\"`              // 状态\n\tVersion            int     `json:\"version\"`            // 版本号\n}\n", "req_add": "package req\n\n// AddPriceSchemeReqDto 创建价格方案请求DTO\ntype AddPriceSchemeReqDto struct {\n\tName                *string  `json:\"name\"`                // 价格方案名称\n\tRoomType            *string  `json:\"roomType\"`            // 房间类型\n\tDistributionChannel *string  `json:\"distributionChannel\"` // 分销渠道\n\tConsumptionMode     *string  `json:\"consumptionMode\"`     // 消费模式\n\tHasMinimumCharge    *bool    `json:\"hasMinimumCharge\"`    // 是否有最低消费\n\tMinimumCharge       *int64   `json:\"minimumCharge\"`       // 最低消费金额\n\tDefaultItems        *string  `json:\"defaultItems\"`        // 默认包含项目\n\tOptionalItems       *string  `json:\"optionalItems\"`       // 可选项目\n\tFreeItems          *string  `json:\"freeItems\"`          // 免费项目\n\tMemberPrice        *int64   `json:\"memberPrice\"`        // 会员价格\n\tMemberDiscount     *float32 `json:\"memberDiscount\"`     // 会员折扣\n\tAreaPrice          *int64   `json:\"areaPrice\"`          // 区域价格\n\tAreaMemberPrice    *int64   `json:\"areaMemberPrice\"`    // 区域会员价格\n\tHolidayPrice       *int64   `json:\"holidayPrice\"`       // 节假日价格\n\tIsEnabled          *bool    `json:\"isEnabled\"`          // 是否启用\n\tSupportsPoints     *bool    `json:\"supportsPoints\"`     // 是否支持积分\n\tConsumptionTimeSlot *string  `json:\"consumptionTimeSlot\"` // 消费时段\n\tBuyAndGiftScheme   *string  `json:\"buyAndGiftScheme\"`   // 买赠方案\n}\n", "req_update": "package req\n\n// UpdatePriceSchemeReqDto 更新价格方案请求DTO\ntype UpdatePriceSchemeReqDto struct {\n\tId                  *string  `json:\"id\"`                  // ID\n\tName                *string  `json:\"name\"`                // 价格方案名称\n\tRoomType            *string  `json:\"roomType\"`            // 房间类型\n\tDistributionChannel *string  `json:\"distributionChannel\"` // 分销渠道\n\tConsumptionMode     *string  `json:\"consumptionMode\"`     // 消费模式\n\tHasMinimumCharge    *bool    `json:\"hasMinimumCharge\"`    // 是否有最低消费\n\tMinimumCharge       *int64   `json:\"minimumCharge\"`       // 最低消费金额\n\tDefaultItems        *string  `json:\"defaultItems\"`        // 默认包含项目\n\tOptionalItems       *string  `json:\"optionalItems\"`       // 可选项目\n\tFreeItems          *string  `json:\"freeItems\"`          // 免费项目\n\tMemberPrice        *int64   `json:\"memberPrice\"`        // 会员价格\n\tMemberDiscount     *float32 `json:\"memberDiscount\"`     // 会员折扣\n\tAreaPrice          *int64   `json:\"areaPrice\"`          // 区域价格\n\tAreaMemberPrice    *int64   `json:\"areaMemberPrice\"`    // 区域会员价格\n\tHolidayPrice       *int64   `json:\"holidayPrice\"`       // 节假日价格\n\tIsEnabled          *bool    `json:\"isEnabled\"`          // 是否启用\n\tSupportsPoints     *bool    `json:\"supportsPoints\"`     // 是否支持积分\n\tConsumptionTimeSlot *string  `json:\"consumptionTimeSlot\"` // 消费时段\n\tBuyAndGiftScheme   *string  `json:\"buyAndGiftScheme\"`   // 买赠方案\n}\n", "req_delete": "package req\n\n// DeletePriceSchemeReqDto 删除价格方案请求DTO\ntype DeletePriceSchemeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryPriceSchemeReqDto 查询价格方案请求DTO\ntype QueryPriceSchemeReqDto struct {\n\tId                  *string  `json:\"id\"`                  // ID\n\tName                *string  `json:\"name\"`                // 价格方案名称\n\tRoomType            *string  `json:\"roomType\"`            // 房间类型\n\tDistributionChannel *string  `json:\"distributionChannel\"` // 分销渠道\n\tConsumptionMode     *string  `json:\"consumptionMode\"`     // 消费模式\n\tHasMinimumCharge    *bool    `json:\"hasMinimumCharge\"`    // 是否有最低消费\n\tMinimumCharge       *int64   `json:\"minimumCharge\"`       // 最低消费金额\n\tMemberPrice        *int64   `json:\"memberPrice\"`        // 会员价格\n\tMemberDiscount     *float32 `json:\"memberDiscount\"`     // 会员折扣\n\tAreaPrice          *int64   `json:\"areaPrice\"`          // 区域价格\n\tAreaMemberPrice    *int64   `json:\"areaMemberPrice\"`    // 区域会员价格\n\tHolidayPrice       *int64   `json:\"holidayPrice\"`       // 节假日价格\n\tIsEnabled          *bool    `json:\"isEnabled\"`          // 是否启用\n\tSupportsPoints     *bool    `json:\"supportsPoints\"`     // 是否支持积分\n\tPageNum            *int     `json:\"pageNum\"`            // 页码\n\tPageSize           *int     `json:\"pageSize\"`           // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PriceSchemeTransfer struct {\n}\n\nfunc (transfer *PriceSchemeTransfer) PoToVo(po po.PriceScheme) vo.PriceSchemeVO {\n\tvo := vo.PriceSchemeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PriceSchemeTransfer) VoToPo(vo vo.PriceSchemeVO) po.PriceScheme {\n\tpo := po.PriceScheme{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PriceSchemeService struct {\n}\n\nfunc (service *PriceSchemeService) CreatePriceScheme(logCtx *gin.Context, priceScheme *po.PriceScheme) error {\n\treturn Save(priceScheme)\n}\n\nfunc (service *PriceSchemeService) UpdatePriceScheme(logCtx *gin.Context, priceScheme *po.PriceScheme) error {\n\treturn Update(priceScheme)\n}\n\nfunc (service *PriceSchemeService) DeletePriceScheme(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PriceScheme{Id: &id})\n}\n\nfunc (service *PriceSchemeService) FindPriceSchemeById(logCtx *gin.Context, id string) (priceScheme *po.PriceScheme, err error) {\n\tpriceScheme = &po.PriceScheme{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(priceScheme).Error\n\treturn\n}\n\nfunc (service *PriceSchemeService) FindAllPriceScheme(logCtx *gin.Context, reqDto *req.QueryPriceSchemeReqDto) (list *[]po.PriceScheme, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PriceScheme{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.RoomType != nil && *reqDto.RoomType != \"\" {\n\t\tdb = db.Where(\"room_type=?\", *reqDto.RoomType)\n\t}\n\tif reqDto.DistributionChannel != nil && *reqDto.DistributionChannel != \"\" {\n\t\tdb = db.Where(\"distribution_channel=?\", *reqDto.DistributionChannel)\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tdb = db.Where(\"has_minimum_charge=?\", *reqDto.HasMinimumCharge)\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tdb = db.Where(\"minimum_charge=?\", *reqDto.MinimumCharge)\n\t}\n\tif reqDto.MemberPrice != nil {\n\t\tdb = db.Where(\"member_price=?\", *reqDto.MemberPrice)\n\t}\n\tif reqDto.MemberDiscount != nil {\n\t\tdb = db.Where(\"member_discount=?\", *reqDto.MemberDiscount)\n\t}\n\tif reqDto.AreaPrice != nil {\n\t\tdb = db.Where(\"area_price=?\", *reqDto.AreaPrice)\n\t}\n\tif reqDto.AreaMemberPrice != nil {\n\t\tdb = db.Where(\"area_member_price=?\", *reqDto.AreaMemberPrice)\n\t}\n\tif reqDto.HolidayPrice != nil {\n\t\tdb = db.Where(\"holiday_price=?\", *reqDto.HolidayPrice)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled=?\", *reqDto.IsEnabled)\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tdb = db.Where(\"supports_points=?\", *reqDto.SupportsPoints)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PriceScheme{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PriceSchemeService) FindAllPriceSchemeWithPagination(logCtx *gin.Context, reqDto *req.QueryPriceSchemeReqDto) (list *[]po.PriceScheme, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PriceScheme{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.RoomType != nil && *reqDto.RoomType != \"\" {\n\t\tdb = db.Where(\"room_type=?\", *reqDto.RoomType)\n\t}\n\tif reqDto.DistributionChannel != nil && *reqDto.DistributionChannel != \"\" {\n\t\tdb = db.Where(\"distribution_channel=?\", *reqDto.DistributionChannel)\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tdb = db.Where(\"has_minimum_charge=?\", *reqDto.HasMinimumCharge)\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tdb = db.Where(\"minimum_charge=?\", *reqDto.MinimumCharge)\n\t}\n\tif reqDto.MemberPrice != nil {\n\t\tdb = db.Where(\"member_price=?\", *reqDto.MemberPrice)\n\t}\n\tif reqDto.MemberDiscount != nil {\n\t\tdb = db.Where(\"member_discount=?\", *reqDto.MemberDiscount)\n\t}\n\tif reqDto.AreaPrice != nil {\n\t\tdb = db.Where(\"area_price=?\", *reqDto.AreaPrice)\n\t}\n\tif reqDto.AreaMemberPrice != nil {\n\t\tdb = db.Where(\"area_member_price=?\", *reqDto.AreaMemberPrice)\n\t}\n\tif reqDto.HolidayPrice != nil {\n\t\tdb = db.Where(\"holiday_price=?\", *reqDto.HolidayPrice)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled=?\", *reqDto.IsEnabled)\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tdb = db.Where(\"supports_points=?\", *reqDto.SupportsPoints)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PriceScheme{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PriceSchemeController struct{}\n\nvar (\n\tpriceSchemeService  = impl.PriceSchemeService{}\n\tpriceSchemeTransfer = transfer.PriceSchemeTransfer{}\n)\n\n// @Summary 添加价格方案\n// @Description 添加价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.AddPriceSchemeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PriceSchemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-scheme/add [post]\nfunc (controller *PriceSchemeController) AddPriceScheme(ctx *gin.Context) {\n\treqDto := req.AddPriceSchemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpriceScheme := po.PriceScheme{}\n\tif reqDto.Name != nil {\n\t\tpriceScheme.Name = reqDto.Name\n\t}\n\tif reqDto.RoomType != nil {\n\t\tpriceScheme.RoomType = reqDto.RoomType\n\t}\n\tif reqDto.DistributionChannel != nil {\n\t\tpriceScheme.DistributionChannel = reqDto.DistributionChannel\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\tpriceScheme.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tpriceScheme.HasMinimumCharge = reqDto.HasMinimumCharge\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tpriceScheme.MinimumCharge = reqDto.MinimumCharge\n\t}\n\tif reqDto.DefaultItems != nil {\n\t\tpriceScheme.DefaultItems = reqDto.DefaultItems\n\t}\n\tif reqDto.OptionalItems != nil {\n\t\tpriceScheme.OptionalItems = reqDto.OptionalItems\n\t}\n\tif reqDto.FreeItems != nil {\n\t\tpriceScheme.FreeItems = reqDto.FreeItems\n\t}\n\tif reqDto.MemberPrice != nil {\n\t\tpriceScheme.MemberPrice = reqDto.MemberPrice\n\t}\n\tif reqDto.MemberDiscount != nil {\n\t\tpriceScheme.MemberDiscount = reqDto.MemberDiscount\n\t}\n\tif reqDto.AreaPrice != nil {\n\t\tpriceScheme.AreaPrice = reqDto.AreaPrice\n\t}\n\tif reqDto.AreaMemberPrice != nil {\n\t\tpriceScheme.AreaMemberPrice = reqDto.AreaMemberPrice\n\t}\n\tif reqDto.HolidayPrice != nil {\n\t\tpriceScheme.HolidayPrice = reqDto.HolidayPrice\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpriceScheme.IsEnabled = reqDto.IsEnabled\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tpriceScheme.SupportsPoints = reqDto.SupportsPoints\n\t}\n\tif reqDto.ConsumptionTimeSlot != nil {\n\t\tpriceScheme.ConsumptionTimeSlot = reqDto.ConsumptionTimeSlot\n\t}\n\tif reqDto.BuyAndGiftScheme != nil {\n\t\tpriceScheme.BuyAndGiftScheme = reqDto.BuyAndGiftScheme\n\t}\n\n\terr = priceSchemeService.CreatePriceScheme(ctx, &priceScheme)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, priceSchemeTransfer.PoToVo(priceScheme))\n}\n\n// @Summary 更新价格方案\n// @Description 更新价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePriceSchemeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PriceSchemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-scheme/update [post]\nfunc (controller *PriceSchemeController) UpdatePriceScheme(ctx *gin.Context) {\n\treqDto := req.UpdatePriceSchemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tpriceScheme, err := priceSchemeService.FindPriceSchemeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tpriceScheme.Name = reqDto.Name\n\t}\n\tif reqDto.RoomType != nil {\n\t\tpriceScheme.RoomType = reqDto.RoomType\n\t}\n\tif reqDto.DistributionChannel != nil {\n\t\tpriceScheme.DistributionChannel = reqDto.DistributionChannel\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\tpriceScheme.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tpriceScheme.HasMinimumCharge = reqDto.HasMinimumCharge\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tpriceScheme.MinimumCharge = reqDto.MinimumCharge\n\t}\n\tif reqDto.DefaultItems != nil {\n\t\tpriceScheme.DefaultItems = reqDto.DefaultItems\n\t}\n\tif reqDto.OptionalItems != nil {\n\t\tpriceScheme.OptionalItems = reqDto.OptionalItems\n\t}\n\tif reqDto.FreeItems != nil {\n\t\tpriceScheme.FreeItems = reqDto.FreeItems\n\t}\n\tif reqDto.MemberPrice != nil {\n\t\tpriceScheme.MemberPrice = reqDto.MemberPrice\n\t}\n\tif reqDto.MemberDiscount != nil {\n\t\tpriceScheme.MemberDiscount = reqDto.MemberDiscount\n\t}\n\tif reqDto.AreaPrice != nil {\n\t\tpriceScheme.AreaPrice = reqDto.AreaPrice\n\t}\n\tif reqDto.AreaMemberPrice != nil {\n\t\tpriceScheme.AreaMemberPrice = reqDto.AreaMemberPrice\n\t}\n\tif reqDto.HolidayPrice != nil {\n\t\tpriceScheme.HolidayPrice = reqDto.HolidayPrice\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpriceScheme.IsEnabled = reqDto.IsEnabled\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tpriceScheme.SupportsPoints = reqDto.SupportsPoints\n\t}\n\tif reqDto.ConsumptionTimeSlot != nil {\n\t\tpriceScheme.ConsumptionTimeSlot = reqDto.ConsumptionTimeSlot\n\t}\n\tif reqDto.BuyAndGiftScheme != nil {\n\t\tpriceScheme.BuyAndGiftScheme = reqDto.BuyAndGiftScheme\n\t}\n\n\terr = priceSchemeService.UpdatePriceScheme(ctx, priceScheme)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, priceSchemeTransfer.PoToVo(*priceScheme))\n}\n\n// @Summary 删除价格方案\n// @Description 删除价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePriceSchemeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-scheme/delete [post]\nfunc (controller *PriceSchemeController) DeletePriceScheme(ctx *gin.Context) {\n\treqDto := req.DeletePriceSchemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = priceSchemeService.DeletePriceScheme(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询价格方案\n// @Description 查询价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPriceSchemeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PriceSchemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-scheme/query [post]\nfunc (controller *PriceSchemeController) QueryPriceSchemes(ctx *gin.Context) {\n\treqDto := req.QueryPriceSchemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := priceSchemeService.FindAllPriceScheme(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PriceSchemeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, priceSchemeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询价格方案列表\n// @Description 查询价格方案列表\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPriceSchemeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PriceSchemeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-scheme/list [post]\nfunc (controller *PriceSchemeController) ListPriceSchemes(ctx *gin.Context) {\n\treqDto := req.QueryPriceSchemeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := priceSchemeService.FindAllPriceSchemeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PriceSchemeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PriceSchemeVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, priceSchemeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PriceSchemeRoute struct {\n}\n\nfunc (s *PriceSchemeRoute) InitPriceSchemeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tpriceSchemeController := controller.PriceSchemeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/price-scheme/add\", priceSchemeController.AddPriceScheme)       // add\n\t\troute.POST(\"/api/price-scheme/update\", priceSchemeController.UpdatePriceScheme) // update\n\t\troute.POST(\"/api/price-scheme/delete\", priceSchemeController.DeletePriceScheme) // delete\n\t\troute.POST(\"/api/price-scheme/query\", priceSchemeController.QueryPriceSchemes)  // query\n\t\troute.POST(\"/api/price-scheme/list\", priceSchemeController.ListPriceSchemes)   // list\n\t}\n}\n"}]