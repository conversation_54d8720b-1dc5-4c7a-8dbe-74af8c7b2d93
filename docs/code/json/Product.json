[{"po": "package po\n\n// Product 产品实体\ntype Product struct {\n\tId                            *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tVenueId                        *string  `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`          // 所属门店ID\n\tName                           *string  `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`               // 产品名称\n\tType                           *string  `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`               // 产品类型\n\tCurrentPrice                   *int64   `gorm:\"column:current_price;type:bigint;default:0\" json:\"currentPrice\"`     // 当前价格\n\tPayPrice                       *int64   `gorm:\"column:pay_price;type:bigint;default:0\" json:\"payPrice\"`           // 支付价格\n\tPayMark                        *string  `gorm:\"column:pay_mark;type:varchar(255);default:''\" json:\"payMark\"`        // 支付标签\n\tBarcode                        *string  `gorm:\"column:barcode;type:varchar(255);default:''\" json:\"barcode\"`        // 条形码\n\tAreaPrices                     *string  `gorm:\"column:area_prices;type:text\" json:\"areaPrices\"`                    // 不同区域价格\n\tBuyGiftPlan                    *string  `gorm:\"column:buy_gift_plan;type:text\" json:\"buyGiftPlan\"`                // 买赠方案\n\tTimeSlotPrices                 *string  `gorm:\"column:time_slot_prices;type:text\" json:\"timeSlotPrices\"`          // 时段价格\n\tDistributionChannels           *string  `gorm:\"column:distribution_channels;type:text\" json:\"distributionChannels\"` // 分销渠道\n\tMemberCardPaymentRestrictions  *string  `gorm:\"column:member_card_payment_restrictions;type:text\" json:\"memberCardPaymentRestrictions\"` // 会员卡支付限制\n\tMinimumSaleQuantity           *int     `gorm:\"column:minimum_sale_quantity;type:int;default:0\" json:\"minimumSaleQuantity\"` // 最小销售数量\n\tIsRealPriceProduct            *bool    `gorm:\"column:is_real_price_product;type:tinyint(1);default:0\" json:\"isRealPriceProduct\"` // 是否为实价产品\n\tAuxiliaryFormula              *string  `gorm:\"column:auxiliary_formula;type:text\" json:\"auxiliaryFormula\"`        // 辅助公式\n\tCategory                       *string  `gorm:\"column:category;type:varchar(255);default:''\" json:\"category\"`       // 商品分类\n\tDiscounts                      *string  `gorm:\"column:discounts;type:text\" json:\"discounts\"`                      // 支持折扣\n\tAllowRepeatBuy                *bool    `gorm:\"column:allow_repeat_buy;type:tinyint(1);default:0\" json:\"allowRepeatBuy\"` // 支持重复购买\n\tRecommendCombos               *string  `gorm:\"column:recommend_combos;type:text\" json:\"recommendCombos\"`          // 推荐搭配\n\tMemberCardLimits              *string  `gorm:\"column:member_card_limits;type:text\" json:\"memberCardLimits\"`      // 会员卡结账限制\n\tFlavors                        *string  `gorm:\"column:flavors;type:text\" json:\"flavors\"`                          // 商品口味\n\tIngredients                    *string  `gorm:\"column:ingredients;type:text\" json:\"ingredients\"`                  // 辅料配方\n\tIsDisplayed                    *bool    `gorm:\"column:is_displayed;type:tinyint(1);default:0\" json:\"isDisplayed\"` // 是否上架展示\n\tAllowStaffGift                *bool    `gorm:\"column:allow_staff_gift;type:tinyint(1);default:0\" json:\"allowStaffGift\"` // 支持员工赠送\n\tCountToMinCharge              *bool    `gorm:\"column:count_to_min_charge;type:tinyint(1);default:0\" json:\"countToMinCharge\"` // 计入低消\n\tCountToPerformance            *bool    `gorm:\"column:count_to_performance;type:tinyint(1);default:0\" json:\"countToPerformance\"` // 计算业绩\n\tIsPromotion                    *bool    `gorm:\"column:is_promotion;type:tinyint(1);default:0\" json:\"isPromotion\"` // 推广\n\tIsSoldOut                      *bool    `gorm:\"column:is_sold_out;type:tinyint(1);default:0\" json:\"isSoldOut\"` // 是否洁清\n\tAllowWineStorage              *bool    `gorm:\"column:allow_wine_storage;type:tinyint(1);default:0\" json:\"allowWineStorage\"` // 支持存酒\n\tGiftVoucher                    *string  `gorm:\"column:gift_voucher;type:text\" json:\"giftVoucher\"`                // 消费赠券\n\tCalculateInventory            *bool    `gorm:\"column:calculate_inventory;type:tinyint(1);default:0\" json:\"calculateInventory\"` // 计算库存\n\tIsAreaSpecified               *bool    `gorm:\"column:is_area_specified;type:tinyint(1);default:0\" json:\"isAreaSpecified\"` // 指定投放区域\n\tSelectedAreas                  *string  `gorm:\"column:selected_areas;type:text\" json:\"selectedAreas\"`              // 指定的投放区域\n\tIsRoomTypeSpecified           *bool    `gorm:\"column:is_room_type_specified;type:tinyint(1);default:0\" json:\"isRoomTypeSpecified\"` // 指定投放包厢类型\n\tSelectedRoomTypes             *string  `gorm:\"column:selected_room_types;type:text\" json:\"selectedRoomTypes\"`    // 指定的投放包厢类型\n\tStartTime                      *string  `gorm:\"column:start_time;type:varchar(64);default:''\" json:\"startTime\"`    // 投放开始时间\n\tEndTime                        *string  `gorm:\"column:end_time;type:varchar(64);default:''\" json:\"endTime\"`        // 投放结束时间\n\tDescription                    *string  `gorm:\"column:description;type:text\" json:\"description\"`                  // 商品介绍\n\tImage                          *string  `gorm:\"column:image;type:text\" json:\"image\"`                              // 商品图片\n\tLowStockThreshold             *int     `gorm:\"column:low_stock_threshold;type:int;default:0\" json:\"lowStockThreshold\"` // 低库存数\n\tDeliveryTimeout               *int     `gorm:\"column:delivery_timeout;type:int;default:0\" json:\"deliveryTimeout\"` // 送达超时时间\n\tSupportsExternalDelivery      *bool    `gorm:\"column:supports_external_delivery;type:tinyint(1);default:0\" json:\"supportsExternalDelivery\"` // 是否支持外送\n\tExternalDeliveryPrice         *int64   `gorm:\"column:external_delivery_price;type:bigint;default:0\" json:\"externalDeliveryPrice\"` // 外送价格\n\tUnit                           *string  `gorm:\"column:unit;type:varchar(64);default:''\" json:\"unit\"`               // 单位\n\tCtime                          *int64   `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                 // 创建时间\n\tUtime                          *int64   `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                 // 更新时间\n\tState                          *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态\n\tVersion                        *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本\n}\n\n// TableName 设置表名\nfunc (Product) TableName() string {\n\treturn \"product\"\n}\n\nfunc (p Product) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductVO 产品信息值对象\ntype ProductVO struct {\n\tId                            string  `json:\"id\"`                            // ID\n\tVenueId                        string  `json:\"venueId\"`                        // 所属门店ID\n\tName                           string  `json:\"name\"`                           // 产品名称\n\tType                           string  `json:\"type\"`                           // 产品类型\n\tCurrentPrice                   int64   `json:\"currentPrice\"`                   // 当前价格\n\tPayPrice                       int64   `json:\"payPrice\"`                       // 支付价格\n\tPayMark                        string  `json:\"payMark\"`                        // 支付标签\n\tBarcode                        string  `json:\"barcode\"`                        // 条形码\n\tAreaPrices                     string  `json:\"areaPrices\"`                     // 不同区域价格\n\tBuyGiftPlan                    string  `json:\"buyGiftPlan\"`                    // 买赠方案\n\tTimeSlotPrices                 string  `json:\"timeSlotPrices\"`                 // 时段价格\n\tDistributionChannels           string  `json:\"distributionChannels\"`           // 分销渠道\n\tMemberCardPaymentRestrictions  string  `json:\"memberCardPaymentRestrictions\"`  // 会员卡支付限制\n\tMinimumSaleQuantity           int     `json:\"minimumSaleQuantity\"`           // 最小销售数量\n\tIsRealPriceProduct            bool    `json:\"isRealPriceProduct\"`            // 是否为实价产品\n\tAuxiliaryFormula              string  `json:\"auxiliaryFormula\"`              // 辅助公式\n\tCategory                       string  `json:\"category\"`                       // 商品分类\n\tDiscounts                      string  `json:\"discounts\"`                      // 支持折扣\n\tAllowRepeatBuy                bool    `json:\"allowRepeatBuy\"`                // 支持重复购买\n\tRecommendCombos               string  `json:\"recommendCombos\"`               // 推荐搭配\n\tMemberCardLimits              string  `json:\"memberCardLimits\"`              // 会员卡结账限制\n\tFlavors                        string  `json:\"flavors\"`                        // 商品口味\n\tIngredients                    string  `json:\"ingredients\"`                    // 辅料配方\n\tIsDisplayed                    bool    `json:\"isDisplayed\"`                    // 是否上架展示\n\tAllowStaffGift                bool    `json:\"allowStaffGift\"`                // 支持员工赠送\n\tCountToMinCharge              bool    `json:\"countToMinCharge\"`              // 计入低消\n\tCountToPerformance            bool    `json:\"countToPerformance\"`            // 计算业绩\n\tIsPromotion                    bool    `json:\"isPromotion\"`                    // 推广\n\tIsSoldOut                      bool    `json:\"isSoldOut\"`                      // 是否洁清\n\tAllowWineStorage              bool    `json:\"allowWineStorage\"`              // 支持存酒\n\tGiftVoucher                    string  `json:\"giftVoucher\"`                    // 消费赠券\n\tCalculateInventory            bool    `json:\"calculateInventory\"`            // 计算库存\n\tIsAreaSpecified               bool    `json:\"isAreaSpecified\"`               // 指定投放区域\n\tSelectedAreas                  string  `json:\"selectedAreas\"`                  // 指定的投放区域\n\tIsRoomTypeSpecified           bool    `json:\"isRoomTypeSpecified\"`           // 指定投放包厢类型\n\tSelectedRoomTypes             string  `json:\"selectedRoomTypes\"`             // 指定的投放包厢类型\n\tStartTime                      string  `json:\"startTime\"`                      // 投放开始时间\n\tEndTime                        string  `json:\"endTime\"`                        // 投放结束时间\n\tDescription                    string  `json:\"description\"`                    // 商品介绍\n\tImage                          string  `json:\"image\"`                          // 商品图片\n\tLowStockThreshold             int     `json:\"lowStockThreshold\"`             // 低库存数\n\tDeliveryTimeout               int     `json:\"deliveryTimeout\"`               // 送达超时时间\n\tSupportsExternalDelivery      bool    `json:\"supportsExternalDelivery\"`      // 是否支持外送\n\tExternalDeliveryPrice         int64   `json:\"externalDeliveryPrice\"`         // 外送价格\n\tUnit                           string  `json:\"unit\"`                           // 单位\n\tCtime                          int64   `json:\"ctime\"`                          // 创建时间\n\tUtime                          int64   `json:\"utime\"`                          // 更新时间\n\tState                          int     `json:\"state\"`                          // 状态\n\tVersion                        int     `json:\"version\"`                        // 版本\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductTransfer struct {\n}\n\nfunc (transfer *ProductTransfer) PoToVo(po po.Product) vo.ProductVO {\n\tvo := vo.ProductVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductTransfer) VoToPo(vo vo.ProductVO) po.Product {\n\tpo := po.Product{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "req_add": "package req\n\n// AddProductReqDto 创建产品请求DTO\ntype AddProductReqDto struct {\n\tVenueId                        *string  `json:\"venueId\"`                        // 所属门店ID\n\tName                           *string  `json:\"name\"`                           // 产品名称\n\tType                           *string  `json:\"type\"`                           // 产品类型\n\tCurrentPrice                   *int64   `json:\"currentPrice\"`                   // 当前价格\n\tPayPrice                       *int64   `json:\"payPrice\"`                       // 支付价格\n\tPayMark                        *string  `json:\"payMark\"`                        // 支付标签\n\tBarcode                        *string  `json:\"barcode\"`                        // 条形码\n\tAreaPrices                     *string  `json:\"areaPrices\"`                     // 不同区域价格\n\tBuyGiftPlan                    *string  `json:\"buyGiftPlan\"`                    // 买赠方案\n\tTimeSlotPrices                 *string  `json:\"timeSlotPrices\"`                 // 时段价格\n\tDistributionChannels           *string  `json:\"distributionChannels\"`           // 分销渠道\n\tMemberCardPaymentRestrictions  *string  `json:\"memberCardPaymentRestrictions\"`  // 会员卡支付限制\n\tMinimumSaleQuantity           *int     `json:\"minimumSaleQuantity\"`           // 最小销售数量\n\tIsRealPriceProduct            *bool    `json:\"isRealPriceProduct\"`            // 是否为实价产品\n\tAuxiliaryFormula              *string  `json:\"auxiliaryFormula\"`              // 辅助公式\n\tCategory                       *string  `json:\"category\"`                       // 商品分类\n\tDiscounts                      *string  `json:\"discounts\"`                      // 支持折扣\n\tAllowRepeatBuy                *bool    `json:\"allowRepeatBuy\"`                // 支持重复购买\n\tRecommendCombos               *string  `json:\"recommendCombos\"`               // 推荐搭配\n\tMemberCardLimits              *string  `json:\"memberCardLimits\"`              // 会员卡结账限制\n\tFlavors                        *string  `json:\"flavors\"`                        // 商品口味\n\tIngredients                    *string  `json:\"ingredients\"`                    // 辅料配方\n\tIsDisplayed                    *bool    `json:\"isDisplayed\"`                    // 是否上架展示\n\tAllowStaffGift                *bool    `json:\"allowStaffGift\"`                // 支持员工赠送\n\tCountToMinCharge              *bool    `json:\"countToMinCharge\"`              // 计入低消\n\tCountToPerformance            *bool    `json:\"countToPerformance\"`            // 计算业绩\n\tIsPromotion                    *bool    `json:\"isPromotion\"`                    // 推广\n\tIsSoldOut                      *bool    `json:\"isSoldOut\"`                      // 是否洁清\n\tAllowWineStorage              *bool    `json:\"allowWineStorage\"`              // 支持存酒\n\tGiftVoucher                    *string  `json:\"giftVoucher\"`                    // 消费赠券\n\tCalculateInventory            *bool    `json:\"calculateInventory\"`            // 计算库存\n\tIsAreaSpecified               *bool    `json:\"isAreaSpecified\"`               // 指定投放区域\n\tSelectedAreas                  *string  `json:\"selectedAreas\"`                  // 指定的投放区域\n\tIsRoomTypeSpecified           *bool    `json:\"isRoomTypeSpecified\"`           // 指定投放包厢类型\n\tSelectedRoomTypes             *string  `json:\"selectedRoomTypes\"`             // 指定的投放包厢类型\n\tStartTime                      *string  `json:\"startTime\"`                      // 投放开始时间\n\tEndTime                        *string  `json:\"endTime\"`                        // 投放结束时间\n\tDescription                    *string  `json:\"description\"`                    // 商品介绍\n\tImage                          *string  `json:\"image\"`                          // 商品图片\n\tLowStockThreshold             *int     `json:\"lowStockThreshold\"`             // 低库存数\n\tDeliveryTimeout               *int     `json:\"deliveryTimeout\"`               // 送达超时时间\n\tSupportsExternalDelivery      *bool    `json:\"supportsExternalDelivery\"`      // 是否支持外送\n\tExternalDeliveryPrice         *int64   `json:\"externalDeliveryPrice\"`         // 外送价格\n\tUnit                           *string  `json:\"unit\"`                           // 单位\n}\n", "req_update": "package req\n\ntype UpdateProductReqDto struct {\n\tId                            *string  `json:\"id\"`                            // ID\n\tVenueId                        *string  `json:\"venueId\"`                        // 所属门店ID\n\tName                           *string  `json:\"name\"`                           // 产品名称\n\tType                           *string  `json:\"type\"`                           // 产品类型\n\tCurrentPrice                   *int64   `json:\"currentPrice\"`                   // 当前价格\n\tPayPrice                       *int64   `json:\"payPrice\"`                       // 支付价格\n\tPayMark                        *string  `json:\"payMark\"`                        // 支付标签\n\tBarcode                        *string  `json:\"barcode\"`                        // 条形码\n\tAreaPrices                     *string  `json:\"areaPrices\"`                     // 不同区域价格\n\tBuyGiftPlan                    *string  `json:\"buyGiftPlan\"`                    // 买赠方案\n\tTimeSlotPrices                 *string  `json:\"timeSlotPrices\"`                 // 时段价格\n\tDistributionChannels           *string  `json:\"distributionChannels\"`           // 分销渠道\n\tMemberCardPaymentRestrictions  *string  `json:\"memberCardPaymentRestrictions\"`  // 会员卡支付限制\n\tMinimumSaleQuantity           *int     `json:\"minimumSaleQuantity\"`           // 最小销售数量\n\tIsRealPriceProduct            *bool    `json:\"isRealPriceProduct\"`            // 是否为实价产品\n\tAuxiliaryFormula              *string  `json:\"auxiliaryFormula\"`              // 辅助公式\n\tCategory                       *string  `json:\"category\"`                       // 商品分类\n\tDiscounts                      *string  `json:\"discounts\"`                      // 支持折扣\n\tAllowRepeatBuy                *bool    `json:\"allowRepeatBuy\"`                // 支持重复购买\n\tRecommendCombos               *string  `json:\"recommendCombos\"`               // 推荐搭配\n\tMemberCardLimits              *string  `json:\"memberCardLimits\"`              // 会员卡结账限制\n\tFlavors                        *string  `json:\"flavors\"`                        // 商品口味\n\tIngredients                    *string  `json:\"ingredients\"`                    // 辅料配方\n\tIsDisplayed                    *bool    `json:\"isDisplayed\"`                    // 是否上架展示\n\tAllowStaffGift                *bool    `json:\"allowStaffGift\"`                // 支持员工赠送\n\tCountToMinCharge              *bool    `json:\"countToMinCharge\"`              // 计入低消\n\tCountToPerformance            *bool    `json:\"countToPerformance\"`            // 计算业绩\n\tIsPromotion                    *bool    `json:\"isPromotion\"`                    // 推广\n\tIsSoldOut                      *bool    `json:\"isSoldOut\"`                      // 是否洁清\n\tAllowWineStorage              *bool    `json:\"allowWineStorage\"`              // 支持存酒\n\tGiftVoucher                    *string  `json:\"giftVoucher\"`                    // 消费赠券\n\tCalculateInventory            *bool    `json:\"calculateInventory\"`            // 计算库存\n\tIsAreaSpecified               *bool    `json:\"isAreaSpecified\"`               // 指定投放区域\n\tSelectedAreas                  *string  `json:\"selectedAreas\"`                  // 指定的投放区域\n\tIsRoomTypeSpecified           *bool    `json:\"isRoomTypeSpecified\"`           // 指定投放包厢类型\n\tSelectedRoomTypes             *string  `json:\"selectedRoomTypes\"`             // 指定的投放包厢类型\n\tStartTime                      *string  `json:\"startTime\"`                      // 投放开始时间\n\tEndTime                        *string  `json:\"endTime\"`                        // 投放结束时间\n\tDescription                    *string  `json:\"description\"`                    // 商品介绍\n\tImage                          *string  `json:\"image\"`                          // 商品图片\n\tLowStockThreshold             *int     `json:\"lowStockThreshold\"`             // 低库存数\n\tDeliveryTimeout               *int     `json:\"deliveryTimeout\"`               // 送达超时时间\n\tSupportsExternalDelivery      *bool    `json:\"supportsExternalDelivery\"`      // 是否支持外送\n\tExternalDeliveryPrice         *int64   `json:\"externalDeliveryPrice\"`         // 外送价格\n\tUnit                           *string  `json:\"unit\"`                           // 单位\n}\n", "req_delete": "package req\n\ntype DeleteProductReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryProductReqDto struct {\n\tId                            *string  `json:\"id\"`                            // ID\n\tVenueId                        *string  `json:\"venueId\"`                        // 所属门店ID\n\tName                           *string  `json:\"name\"`                           // 产品名称\n\tType                           *string  `json:\"type\"`                           // 产品类型\n\tCurrentPrice                   *int64   `json:\"currentPrice\"`                   // 当前价格\n\tPayPrice                       *int64   `json:\"payPrice\"`                       // 支付价格\n\tPayMark                        *string  `json:\"payMark\"`                        // 支付标签\n\tBarcode                        *string  `json:\"barcode\"`                        // 条形码\n\tAreaPrices                     *string  `json:\"areaPrices\"`                     // 不同区域价格\n\tBuyGiftPlan                    *string  `json:\"buyGiftPlan\"`                    // 买赠方案\n\tTimeSlotPrices                 *string  `json:\"timeSlotPrices\"`                 // 时段价格\n\tDistributionChannels           *string  `json:\"distributionChannels\"`           // 分销渠道\n\tMemberCardPaymentRestrictions  *string  `json:\"memberCardPaymentRestrictions\"`  // 会员卡支付限制\n\tMinimumSaleQuantity           *int     `json:\"minimumSaleQuantity\"`           // 最小销售数量\n\tIsRealPriceProduct            *bool    `json:\"isRealPriceProduct\"`            // 是否为实价产品\n\tAuxiliaryFormula              *string  `json:\"auxiliaryFormula\"`              // 辅助公式\n\tCategory                       *string  `json:\"category\"`                       // 商品分类\n\tDiscounts                      *string  `json:\"discounts\"`                      // 支持折扣\n\tAllowRepeatBuy                *bool    `json:\"allowRepeatBuy\"`                // 支持重复购买\n\tRecommendCombos               *string  `json:\"recommendCombos\"`               // 推荐搭配\n\tMemberCardLimits              *string  `json:\"memberCardLimits\"`              // 会员卡结账限制\n\tFlavors                        *string  `json:\"flavors\"`                        // 商品口味\n\tIngredients                    *string  `json:\"ingredients\"`                    // 辅料配方\n\tIsDisplayed                    *bool    `json:\"isDisplayed\"`                    // 是否上架展示\n\tAllowStaffGift                *bool    `json:\"allowStaffGift\"`                // 支持员工赠送\n\tCountToMinCharge              *bool    `json:\"countToMinCharge\"`              // 计入低消\n\tCountToPerformance            *bool    `json:\"countToPerformance\"`            // 计算业绩\n\tIsPromotion                    *bool    `json:\"isPromotion\"`                    // 推广\n\tIsSoldOut                      *bool    `json:\"isSoldOut\"`                      // 是否洁清\n\tAllowWineStorage              *bool    `json:\"allowWineStorage\"`              // 支持存酒\n\tGiftVoucher                    *string  `json:\"giftVoucher\"`                    // 消费赠券\n\tCalculateInventory            *bool    `json:\"calculateInventory\"`            // 计算库存\n\tIsAreaSpecified               *bool    `json:\"isAreaSpecified\"`               // 指定投放区域\n\tSelectedAreas                  *string  `json:\"selectedAreas\"`                  // 指定的投放区域\n\tIsRoomTypeSpecified           *bool    `json:\"isRoomTypeSpecified\"`           // 指定投放包厢类型\n\tSelectedRoomTypes             *string  `json:\"selectedRoomTypes\"`             // 指定的投放包厢类型\n\tStartTime                      *string  `json:\"startTime\"`                      // 投放开始时间\n\tEndTime                        *string  `json:\"endTime\"`                        // 投放结束时间\n\tDescription                    *string  `json:\"description\"`                    // 商品介绍\n\tImage                          *string  `json:\"image\"`                          // 商品图片\n\tLowStockThreshold             *int     `json:\"lowStockThreshold\"`             // 低库存数\n\tDeliveryTimeout               *int     `json:\"deliveryTimeout\"`               // 送达超时时间\n\tSupportsExternalDelivery      *bool    `json:\"supportsExternalDelivery\"`      // 是否支持外送\n\tExternalDeliveryPrice         *int64   `json:\"externalDeliveryPrice\"`         // 外送价格\n\tUnit                           *string  `json:\"unit\"`                           // 单位\n\tPageNum                        *int     `json:\"pageNum\"`                        // 页码\n\tPageSize                       *int     `json:\"pageSize\"`                       // 每页记录数\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductService struct {\n}\n\nfunc (service *ProductService) CreateProduct(logCtx *gin.Context, product *po.Product) error {\n\treturn Save(product)\n}\n\nfunc (service *ProductService) UpdateProduct(logCtx *gin.Context, product *po.Product) error {\n\treturn Update(product)\n}\n\nfunc (service *ProductService) DeleteProduct(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Product{Id: &id})\n}\n\nfunc (service *ProductService) FindProductById(logCtx *gin.Context, id string) (product *po.Product, err error) {\n\tproduct = &po.Product{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(product).Error\n\treturn\n}\n\nfunc (service *ProductService) FindAllProduct(logCtx *gin.Context, reqDto *req.QueryProductReqDto) (list *[]po.Product, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Product{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.Barcode != nil && *reqDto.Barcode != \"\" {\n\t\tdb = db.Where(\"barcode=?\", *reqDto.Barcode)\n\t}\n\tif reqDto.Category != nil && *reqDto.Category != \"\" {\n\t\tdb = db.Where(\"category=?\", *reqDto.Category)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\tif reqDto.IsSoldOut != nil {\n\t\tdb = db.Where(\"is_sold_out=?\", *reqDto.IsSoldOut)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Product{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductService) FindAllProductWithPagination(logCtx *gin.Context, reqDto *req.QueryProductReqDto) (list *[]po.Product, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Product{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.Barcode != nil && *reqDto.Barcode != \"\" {\n\t\tdb = db.Where(\"barcode=?\", *reqDto.Barcode)\n\t}\n\tif reqDto.Category != nil && *reqDto.Category != \"\" {\n\t\tdb = db.Where(\"category=?\", *reqDto.Category)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\tif reqDto.IsSoldOut != nil {\n\t\tdb = db.Where(\"is_sold_out=?\", *reqDto.IsSoldOut)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Product{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductRoute struct {\n}\n\nfunc (s *ProductRoute) InitProductRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductController := controller.ProductController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/product/add\", productController.AddProduct)       // add\n\t\troute.POST(\"/api/product/update\", productController.UpdateProduct) // update\n\t\troute.POST(\"/api/product/delete\", productController.DeleteProduct) // delete\n\t\troute.POST(\"/api/product/query\", productController.QueryProducts)  // query\n\t\troute.POST(\"/api/product/list\", productController.ListProducts)    // list\n\t}\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductController struct{}\n\nvar (\n\tproductService  = impl.ProductService{}\n\tproductTransfer = transfer.ProductTransfer{}\n)\n\n// @Summary 添加产品\n// @Description 添加产品\n// @Tags 产品\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product/add [post]\nfunc (controller *ProductController) AddProduct(ctx *gin.Context) {\n\treqDto := req.AddProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproduct := po.Product{}\n\tif reqDto.VenueId != nil {\n\t\tproduct.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproduct.Name = reqDto.Name\n\t}\n\tif reqDto.Type != nil {\n\t\tproduct.Type = reqDto.Type\n\t}\n\tif reqDto.CurrentPrice != nil {\n\t\tproduct.CurrentPrice = reqDto.CurrentPrice\n\t}\n\tif reqDto.PayPrice != nil {\n\t\tproduct.PayPrice = reqDto.PayPrice\n\t}\n\tif reqDto.PayMark != nil {\n\t\tproduct.PayMark = reqDto.PayMark\n\t}\n\tif reqDto.Barcode != nil {\n\t\tproduct.Barcode = reqDto.Barcode\n\t}\n\tif reqDto.AreaPrices != nil {\n\t\tproduct.AreaPrices = reqDto.AreaPrices\n\t}\n\tif reqDto.BuyGiftPlan != nil {\n\t\tproduct.BuyGiftPlan = reqDto.BuyGiftPlan\n\t}\n\tif reqDto.TimeSlotPrices != nil {\n\t\tproduct.TimeSlotPrices = reqDto.TimeSlotPrices\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tproduct.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.MemberCardPaymentRestrictions != nil {\n\t\tproduct.MemberCardPaymentRestrictions = reqDto.MemberCardPaymentRestrictions\n\t}\n\tif reqDto.MinimumSaleQuantity != nil {\n\t\tproduct.MinimumSaleQuantity = reqDto.MinimumSaleQuantity\n\t}\n\tif reqDto.IsRealPriceProduct != nil {\n\t\tproduct.IsRealPriceProduct = reqDto.IsRealPriceProduct\n\t}\n\tif reqDto.AuxiliaryFormula != nil {\n\t\tproduct.AuxiliaryFormula = reqDto.AuxiliaryFormula\n\t}\n\tif reqDto.Category != nil {\n\t\tproduct.Category = reqDto.Category\n\t}\n\tif reqDto.Discounts != nil {\n\t\tproduct.Discounts = reqDto.Discounts\n\t}\n\tif reqDto.AllowRepeatBuy != nil {\n\t\tproduct.AllowRepeatBuy = reqDto.AllowRepeatBuy\n\t}\n\tif reqDto.RecommendCombos != nil {\n\t\tproduct.RecommendCombos = reqDto.RecommendCombos\n\t}\n\tif reqDto.MemberCardLimits != nil {\n\t\tproduct.MemberCardLimits = reqDto.MemberCardLimits\n\t}\n\tif reqDto.Flavors != nil {\n\t\tproduct.Flavors = reqDto.Flavors\n\t}\n\tif reqDto.Ingredients != nil {\n\t\tproduct.Ingredients = reqDto.Ingredients\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tproduct.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.AllowStaffGift != nil {\n\t\tproduct.AllowStaffGift = reqDto.AllowStaffGift\n\t}\n\tif reqDto.CountToMinCharge != nil {\n\t\tproduct.CountToMinCharge = reqDto.CountToMinCharge\n\t}\n\tif reqDto.CountToPerformance != nil {\n\t\tproduct.CountToPerformance = reqDto.CountToPerformance\n\t}\n\tif reqDto.IsPromotion != nil {\n\t\tproduct.IsPromotion = reqDto.IsPromotion\n\t}\n\tif reqDto.IsSoldOut != nil {\n\t\tproduct.IsSoldOut = reqDto.IsSoldOut\n\t}\n\tif reqDto.AllowWineStorage != nil {\n\t\tproduct.AllowWineStorage = reqDto.AllowWineStorage\n\t}\n\tif reqDto.GiftVoucher != nil {\n\t\tproduct.GiftVoucher = reqDto.GiftVoucher\n\t}\n\tif reqDto.CalculateInventory != nil {\n\t\tproduct.CalculateInventory = reqDto.CalculateInventory\n\t}\n\tif reqDto.IsAreaSpecified != nil {\n\t\tproduct.IsAreaSpecified = reqDto.IsAreaSpecified\n\t}\n\tif reqDto.SelectedAreas != nil {\n\t\tproduct.SelectedAreas = reqDto.SelectedAreas\n\t}\n\tif reqDto.IsRoomTypeSpecified != nil {\n\t\tproduct.IsRoomTypeSpecified = reqDto.IsRoomTypeSpecified\n\t}\n\tif reqDto.SelectedRoomTypes != nil {\n\t\tproduct.SelectedRoomTypes = reqDto.SelectedRoomTypes\n\t}\n\tif reqDto.StartTime != nil {\n\t\tproduct.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\tproduct.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.Description != nil {\n\t\tproduct.Description = reqDto.Description\n\t}\n\tif reqDto.Image != nil {\n\t\tproduct.Image = reqDto.Image\n\t}\n\tif reqDto.LowStockThreshold != nil {\n\t\tproduct.LowStockThreshold = reqDto.LowStockThreshold\n\t}\n\tif reqDto.DeliveryTimeout != nil {\n\t\tproduct.DeliveryTimeout = reqDto.DeliveryTimeout\n\t}\n\tif reqDto.SupportsExternalDelivery != nil {\n\t\tproduct.SupportsExternalDelivery = reqDto.SupportsExternalDelivery\n\t}\n\tif reqDto.ExternalDeliveryPrice != nil {\n\t\tproduct.ExternalDeliveryPrice = reqDto.ExternalDeliveryPrice\n\t}\n\tif reqDto.Unit != nil {\n\t\tproduct.Unit = reqDto.Unit\n\t}\n\n\terr = productService.CreateProduct(ctx, &product)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productTransfer.PoToVo(product))\n}\n\n// @Summary 更新产品\n// @Description 更新产品\n// @Tags 产品\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product/update [post]\nfunc (controller *ProductController) UpdateProduct(ctx *gin.Context) {\n\treqDto := req.UpdateProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproduct, err := productService.FindProductById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tproduct.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproduct.Name = reqDto.Name\n\t}\n\tif reqDto.Type != nil {\n\t\tproduct.Type = reqDto.Type\n\t}\n\tif reqDto.CurrentPrice != nil {\n\t\tproduct.CurrentPrice = reqDto.CurrentPrice\n\t}\n\tif reqDto.PayPrice != nil {\n\t\tproduct.PayPrice = reqDto.PayPrice\n\t}\n\tif reqDto.PayMark != nil {\n\t\tproduct.PayMark = reqDto.PayMark\n\t}\n\tif reqDto.Barcode != nil {\n\t\tproduct.Barcode = reqDto.Barcode\n\t}\n\tif reqDto.AreaPrices != nil {\n\t\tproduct.AreaPrices = reqDto.AreaPrices\n\t}\n\tif reqDto.BuyGiftPlan != nil {\n\t\tproduct.BuyGiftPlan = reqDto.BuyGiftPlan\n\t}\n\tif reqDto.TimeSlotPrices != nil {\n\t\tproduct.TimeSlotPrices = reqDto.TimeSlotPrices\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tproduct.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.MemberCardPaymentRestrictions != nil {\n\t\tproduct.MemberCardPaymentRestrictions = reqDto.MemberCardPaymentRestrictions\n\t}\n\tif reqDto.MinimumSaleQuantity != nil {\n\t\tproduct.MinimumSaleQuantity = reqDto.MinimumSaleQuantity\n\t}\n\tif reqDto.IsRealPriceProduct != nil {\n\t\tproduct.IsRealPriceProduct = reqDto.IsRealPriceProduct\n\t}\n\tif reqDto.AuxiliaryFormula != nil {\n\t\tproduct.AuxiliaryFormula = reqDto.AuxiliaryFormula\n\t}\n\tif reqDto.Category != nil {\n\t\tproduct.Category = reqDto.Category\n\t}\n\tif reqDto.Discounts != nil {\n\t\tproduct.Discounts = reqDto.Discounts\n\t}\n\tif reqDto.AllowRepeatBuy != nil {\n\t\tproduct.AllowRepeatBuy = reqDto.AllowRepeatBuy\n\t}\n\tif reqDto.RecommendCombos != nil {\n\t\tproduct.RecommendCombos = reqDto.RecommendCombos\n\t}\n\tif reqDto.MemberCardLimits != nil {\n\t\tproduct.MemberCardLimits = reqDto.MemberCardLimits\n\t}\n\tif reqDto.Flavors != nil {\n\t\tproduct.Flavors = reqDto.Flavors\n\t}\n\tif reqDto.Ingredients != nil {\n\t\tproduct.Ingredients = reqDto.Ingredients\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tproduct.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.AllowStaffGift != nil {\n\t\tproduct.AllowStaffGift = reqDto.AllowStaffGift\n\t}\n\tif reqDto.CountToMinCharge != nil {\n\t\tproduct.CountToMinCharge = reqDto.CountToMinCharge\n\t}\n\tif reqDto.CountToPerformance != nil {\n\t\tproduct.CountToPerformance = reqDto.CountToPerformance\n\t}\n\tif reqDto.IsPromotion != nil {\n\t\tproduct.IsPromotion = reqDto.IsPromotion\n\t}\n\tif reqDto.IsSoldOut != nil {\n\t\tproduct.IsSoldOut = reqDto.IsSoldOut\n\t}\n\tif reqDto.AllowWineStorage != nil {\n\t\tproduct.AllowWineStorage = reqDto.AllowWineStorage\n\t}\n\tif reqDto.GiftVoucher != nil {\n\t\tproduct.GiftVoucher = reqDto.GiftVoucher\n\t}\n\tif reqDto.CalculateInventory != nil {\n\t\tproduct.CalculateInventory = reqDto.CalculateInventory\n\t}\n\tif reqDto.IsAreaSpecified != nil {\n\t\tproduct.IsAreaSpecified = reqDto.IsAreaSpecified\n\t}\n\tif reqDto.SelectedAreas != nil {\n\t\tproduct.SelectedAreas = reqDto.SelectedAreas\n\t}\n\tif reqDto.IsRoomTypeSpecified != nil {\n\t\tproduct.IsRoomTypeSpecified = reqDto.IsRoomTypeSpecified\n\t}\n\tif reqDto.SelectedRoomTypes != nil {\n\t\tproduct.SelectedRoomTypes = reqDto.SelectedRoomTypes\n\t}\n\tif reqDto.StartTime != nil {\n\t\tproduct.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\tproduct.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.Description != nil {\n\t\tproduct.Description = reqDto.Description\n\t}\n\tif reqDto.Image != nil {\n\t\tproduct.Image = reqDto.Image\n\t}\n\tif reqDto.LowStockThreshold != nil {\n\t\tproduct.LowStockThreshold = reqDto.LowStockThreshold\n\t}\n\tif reqDto.DeliveryTimeout != nil {\n\t\tproduct.DeliveryTimeout = reqDto.DeliveryTimeout\n\t}\n\tif reqDto.SupportsExternalDelivery != nil {\n\t\tproduct.SupportsExternalDelivery = reqDto.SupportsExternalDelivery\n\t}\n\tif reqDto.ExternalDeliveryPrice != nil {\n\t\tproduct.ExternalDeliveryPrice = reqDto.ExternalDeliveryPrice\n\t}\n\tif reqDto.Unit != nil {\n\t\tproduct.Unit = reqDto.Unit\n\t}\n\n\terr = productService.UpdateProduct(ctx, product)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productTransfer.PoToVo(*product))\n}\n\n// @Summary 删除产品\n// @Description 删除产品\n// @Tags 产品\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product/delete [post]\nfunc (controller *ProductController) DeleteProduct(ctx *gin.Context) {\n\treqDto := req.DeleteProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = productService.DeleteProduct(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品\n// @Description 查询产品\n// @Tags 产品\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product/query [post]\nfunc (controller *ProductController) QueryProducts(ctx *gin.Context) {\n\treqDto := req.QueryProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := productService.FindAllProduct(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.ProductVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品列表\n// @Description 查询产品列表\n// @Tags 产品\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product/list [post]\nfunc (controller *ProductController) ListProducts(ctx *gin.Context) {\n\treqDto := req.QueryProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := productService.FindAllProductWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.ProductVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n"}]