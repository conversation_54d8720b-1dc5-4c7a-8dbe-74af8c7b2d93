[{"po": "package po\n\n// AuthorizationRecord 授权记录实体\ntype AuthorizationRecord struct {\n\tId                *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                                 // ID\n\tAuthorizationTime *int64  `gorm:\"column:authorization_time;type:int;default:0\" json:\"authorizationTime\"` // 授权时间\n\tAuthorizationType *string `gorm:\"column:authorization_type;type:varchar(64);default:''\" json:\"authorizationType\"` // 授权类型\n\tEmployeeId       *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"` // 操作员工ID\n\tGiftRecordId     *string `gorm:\"column:gift_record_id;type:varchar(64);default:''\" json:\"giftRecordId\"` // 赠送记录ID\n\tCtime            *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime            *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState            *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion          *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (AuthorizationRecord) TableName() string {\n\treturn \"authorization_record\"\n}\n\nfunc (a AuthorizationRecord) GetId() string {\n\treturn *a.Id\n}\n", "vo": "package vo\n\n// AuthorizationRecordVO 授权记录值对象\ntype AuthorizationRecordVO struct {\n\tId                string `json:\"id\"`                // ID\n\tAuthorizationTime int64  `json:\"authorizationTime\"` // 授权时间\n\tAuthorizationType string `json:\"authorizationType\"` // 授权类型\n\tEmployeeId       string `json:\"employeeId\"`       // 操作员工ID\n\tGiftRecordId     string `json:\"giftRecordId\"`     // 赠送记录ID\n\tCtime            int64  `json:\"ctime\"`            // 创建时间戳\n\tUtime            int64  `json:\"utime\"`            // 更新时间戳\n\tState            int    `json:\"state\"`            // 状态值\n\tVersion          int    `json:\"version\"`          // 版本号\n}\n", "req_add": "package req\n\n// AddAuthorizationRecordReqDto 创建授权记录请求DTO\ntype AddAuthorizationRecordReqDto struct {\n\tAuthorizationTime *int64  `json:\"authorizationTime\"` // 授权时间\n\tAuthorizationType *string `json:\"authorizationType\"` // 授权类型\n\tEmployeeId       *string `json:\"employeeId\"`       // 操作员工ID\n\tGiftRecordId     *string `json:\"giftRecordId\"`     // 赠送记录ID\n}\n", "req_update": "package req\n\ntype UpdateAuthorizationRecordReqDto struct {\n\tId                *string `json:\"id\"`                // ID\n\tAuthorizationTime *int64  `json:\"authorizationTime\"` // 授权时间\n\tAuthorizationType *string `json:\"authorizationType\"` // 授权类型\n\tEmployeeId       *string `json:\"employeeId\"`       // 操作员工ID\n\tGiftRecordId     *string `json:\"giftRecordId\"`     // 赠送记录ID\n}\n", "req_delete": "package req\n\ntype DeleteAuthorizationRecordReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryAuthorizationRecordReqDto struct {\n\tId                *string `json:\"id\"`                // ID\n\tAuthorizationTime *int64  `json:\"authorizationTime\"` // 授权时间\n\tAuthorizationType *string `json:\"authorizationType\"` // 授权类型\n\tEmployeeId       *string `json:\"employeeId\"`       // 操作员工ID\n\tGiftRecordId     *string `json:\"giftRecordId\"`     // 赠送记录ID\n\tPageNum          *int    `json:\"pageNum\"`          // 页码\n\tPageSize         *int    `json:\"pageSize\"`         // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype AuthorizationRecordTransfer struct {\n}\n\nfunc (transfer *AuthorizationRecordTransfer) PoToVo(po po.AuthorizationRecord) vo.AuthorizationRecordVO {\n\tvo := vo.AuthorizationRecordVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *AuthorizationRecordTransfer) VoToPo(vo vo.AuthorizationRecordVO) po.AuthorizationRecord {\n\tpo := po.AuthorizationRecord{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype AuthorizationRecordService struct {\n}\n\nfunc (service *AuthorizationRecordService) CreateAuthorizationRecord(logCtx *gin.Context, authorizationRecord *po.AuthorizationRecord) error {\n\treturn Save(authorizationRecord)\n}\n\nfunc (service *AuthorizationRecordService) UpdateAuthorizationRecord(logCtx *gin.Context, authorizationRecord *po.AuthorizationRecord) error {\n\treturn Update(authorizationRecord)\n}\n\nfunc (service *AuthorizationRecordService) DeleteAuthorizationRecord(logCtx *gin.Context, id string) error {\n\treturn Delete(po.AuthorizationRecord{Id: &id})\n}\n\nfunc (service *AuthorizationRecordService) FindAuthorizationRecordById(logCtx *gin.Context, id string) (authorizationRecord *po.AuthorizationRecord, err error) {\n\tauthorizationRecord = &po.AuthorizationRecord{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(authorizationRecord).Error\n\treturn\n}\n\nfunc (service *AuthorizationRecordService) FindAllAuthorizationRecord(logCtx *gin.Context, reqDto *req.QueryAuthorizationRecordReqDto) (list *[]po.AuthorizationRecord, err error) {\n\tdb := model.DBSlave.Self.Model(&po.AuthorizationRecord{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.AuthorizationRecord{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *AuthorizationRecordService) FindAllAuthorizationRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryAuthorizationRecordReqDto) (list *[]po.AuthorizationRecord, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.AuthorizationRecord{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.AuthorizationRecord{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype AuthorizationRecordController struct{}\n\nvar (\n\tauthorizationRecordService  = impl.AuthorizationRecordService{}\n\tauthorizationRecordTransfer = transfer.AuthorizationRecordTransfer{}\n)\n\n// @Summary 添加授权记录\n// @Description 添加授权记录\n// @Tags 授权记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddAuthorizationRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.AuthorizationRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/authorization-record/add [post]\nfunc (controller *AuthorizationRecordController) AddAuthorizationRecord(ctx *gin.Context) {\n\treqDto := req.AddAuthorizationRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tauthorizationRecord := po.AuthorizationRecord{}\n\tif reqDto.AuthorizationTime != nil {\n\t\tauthorizationRecord.AuthorizationTime = reqDto.AuthorizationTime\n\t}\n\tif reqDto.AuthorizationType != nil {\n\t\tauthorizationRecord.AuthorizationType = reqDto.AuthorizationType\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tauthorizationRecord.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.GiftRecordId != nil {\n\t\tauthorizationRecord.GiftRecordId = reqDto.GiftRecordId\n\t}\n\terr = authorizationRecordService.CreateAuthorizationRecord(ctx, &authorizationRecord)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, authorizationRecordTransfer.PoToVo(authorizationRecord))\n}\n\n// @Summary 更新授权记录\n// @Description 更新授权记录\n// @Tags 授权记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateAuthorizationRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.AuthorizationRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/authorization-record/update [post]\nfunc (controller *AuthorizationRecordController) UpdateAuthorizationRecord(ctx *gin.Context) {\n\treqDto := req.UpdateAuthorizationRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tauthorizationRecord, err := authorizationRecordService.FindAuthorizationRecordById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.AuthorizationTime != nil {\n\t\tauthorizationRecord.AuthorizationTime = reqDto.AuthorizationTime\n\t}\n\tif reqDto.AuthorizationType != nil {\n\t\tauthorizationRecord.AuthorizationType = reqDto.AuthorizationType\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tauthorizationRecord.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.GiftRecordId != nil {\n\t\tauthorizationRecord.GiftRecordId = reqDto.GiftRecordId\n\t}\n\terr = authorizationRecordService.UpdateAuthorizationRecord(ctx, authorizationRecord)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, authorizationRecordTransfer.PoToVo(*authorizationRecord))\n}\n\n// @Summary 删除授权记录\n// @Description 删除授权记录\n// @Tags 授权记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteAuthorizationRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/authorization-record/delete [post]\nfunc (controller *AuthorizationRecordController) DeleteAuthorizationRecord(ctx *gin.Context) {\n\treqDto := req.DeleteAuthorizationRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = authorizationRecordService.DeleteAuthorizationRecord(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询授权记录\n// @Description 查询授权记录\n// @Tags 授权记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryAuthorizationRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.AuthorizationRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/authorization-record/query [post]\nfunc (controller *AuthorizationRecordController) QueryAuthorizationRecords(ctx *gin.Context) {\n\treqDto := req.QueryAuthorizationRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := authorizationRecordService.FindAllAuthorizationRecord(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.AuthorizationRecordVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, authorizationRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询授权记录列表\n// @Description 查询授权记录列表\n// @Tags 授权记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryAuthorizationRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.AuthorizationRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/authorization-record/list [post]\nfunc (a *AuthorizationRecordController) ListAuthorizationRecords(ctx *gin.Context) {\n\treqDto := req.QueryAuthorizationRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := authorizationRecordService.FindAllAuthorizationRecordWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.AuthorizationRecordVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.AuthorizationRecordVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, authorizationRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype AuthorizationRecordRoute struct {\n}\n\nfunc (s *AuthorizationRecordRoute) InitAuthorizationRecordRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tauthorizationRecordController := controller.AuthorizationRecordController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/authorization-record/add\", authorizationRecordController.AddAuthorizationRecord)    //add\n\t\troute.POST(\"/api/authorization-record/update\", authorizationRecordController.UpdateAuthorizationRecord) //update\n\t\troute.POST(\"/api/authorization-record/delete\", authorizationRecordController.DeleteAuthorizationRecord) //delete\n\t\troute.POST(\"/api/authorization-record/query\", authorizationRecordController.QueryAuthorizationRecords)     //query\n\t\troute.POST(\"/api/authorization-record/list\", authorizationRecordController.ListAuthorizationRecords)     //list\n\t}\n}\n"}]