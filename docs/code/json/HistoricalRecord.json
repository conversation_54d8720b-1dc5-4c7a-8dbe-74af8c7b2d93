[{"po": "package po\n\n// HistoricalRecord 历史记录实体\ntype HistoricalRecord struct {\n\tId                *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // 唯一id\n\tOpenTables        *int    `gorm:\"column:open_tables;type:int;default:0\" json:\"openTables\"`               // 开台数量\n\tTotalOpenedTables *int    `gorm:\"column:total_opened_tables;type:int;default:0\" json:\"totalOpenedTables\"` // 总开台数量\n\tFinishedTables    *int    `gorm:\"column:finished_tables;type:int;default:0\" json:\"finishedTables\"`       // 结束台数\n\tSettlementAmount  *int64  `gorm:\"column:settlement_amount;type:bigint;default:0\" json:\"settlementAmount\"` // 结算金额\n\tCtime            *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                       // 创建时间\n\tUtime            *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                       // 更新时间\n\tState            *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态\n\tVersion          *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本\n}\n\n// TableName 设置表名\nfunc (HistoricalRecord) TableName() string {\n\treturn \"historical_record\"\n}\n\nfunc (h HistoricalRecord) GetId() string {\n\treturn *h.Id\n}\n", "vo": "package vo\n\n// HistoricalRecordVO 历史记录值对象\ntype HistoricalRecordVO struct {\n\tId                string `json:\"id\"`                // 唯一id\n\tOpenTables        int    `json:\"openTables\"`        // 开台数量\n\tTotalOpenedTables int    `json:\"totalOpenedTables\"` // 总开台数量\n\tFinishedTables    int    `json:\"finishedTables\"`    // 结束台数\n\tSettlementAmount  int64  `json:\"settlementAmount\"`  // 结算金额\n\tCtime             int64  `json:\"ctime\"`             // 创建时间\n\tUtime             int64  `json:\"utime\"`             // 更新时间\n\tState             int    `json:\"state\"`             // 状态\n\tVersion           int    `json:\"version\"`           // 版本\n}\n", "req_add": "package req\n\n// AddHistoricalRecordReqDto 创建历史记录请求DTO\ntype AddHistoricalRecordReqDto struct {\n\tOpenTables        *int   `json:\"openTables\"`        // 开台数量\n\tTotalOpenedTables *int   `json:\"totalOpenedTables\"` // 总开台数量\n\tFinishedTables    *int   `json:\"finishedTables\"`    // 结束台数\n\tSettlementAmount  *int64 `json:\"settlementAmount\"`  // 结算金额\n}\n", "req_update": "package req\n\n// UpdateHistoricalRecordReqDto 更新历史记录请求DTO\ntype UpdateHistoricalRecordReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一id\n\tOpenTables        *int    `json:\"openTables\"`        // 开台数量\n\tTotalOpenedTables *int    `json:\"totalOpenedTables\"` // 总开台数量\n\tFinishedTables    *int    `json:\"finishedTables\"`    // 结束台数\n\tSettlementAmount  *int64  `json:\"settlementAmount\"`  // 结算金额\n}\n", "req_delete": "package req\n\n// DeleteHistoricalRecordReqDto 删除历史记录请求DTO\ntype DeleteHistoricalRecordReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\n// QueryHistoricalRecordReqDto 查询历史记录请求DTO\ntype QueryHistoricalRecordReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一id\n\tOpenTables        *int    `json:\"openTables\"`        // 开台数量\n\tTotalOpenedTables *int    `json:\"totalOpenedTables\"` // 总开台数量\n\tFinishedTables    *int    `json:\"finishedTables\"`    // 结束台数\n\tSettlementAmount  *int64  `json:\"settlementAmount\"`  // 结算金额\n\tPageNum           *int    `json:\"pageNum\"`           // 页码\n\tPageSize          *int    `json:\"pageSize\"`          // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype HistoricalRecordTransfer struct {\n}\n\nfunc (transfer *HistoricalRecordTransfer) PoToVo(po po.HistoricalRecord) vo.HistoricalRecordVO {\n\tvo := vo.HistoricalRecordVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *HistoricalRecordTransfer) VoToPo(vo vo.HistoricalRecordVO) po.HistoricalRecord {\n\tpo := po.HistoricalRecord{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype HistoricalRecordService struct {\n}\n\nfunc (service *HistoricalRecordService) CreateHistoricalRecord(logCtx *gin.Context, record *po.HistoricalRecord) error {\n\treturn Save(record)\n}\n\nfunc (service *HistoricalRecordService) UpdateHistoricalRecord(logCtx *gin.Context, record *po.HistoricalRecord) error {\n\treturn Update(record)\n}\n\nfunc (service *HistoricalRecordService) DeleteHistoricalRecord(logCtx *gin.Context, id string) error {\n\treturn Delete(po.HistoricalRecord{Id: &id})\n}\n\nfunc (service *HistoricalRecordService) FindHistoricalRecordById(logCtx *gin.Context, id string) (record *po.HistoricalRecord, err error) {\n\trecord = &po.HistoricalRecord{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(record).Error\n\treturn\n}\n\nfunc (service *HistoricalRecordService) FindAllHistoricalRecord(logCtx *gin.Context, reqDto *req.QueryHistoricalRecordReqDto) (list *[]po.HistoricalRecord, err error) {\n\tdb := model.DBSlave.Self.Model(&po.HistoricalRecord{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.HistoricalRecord{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *HistoricalRecordService) FindAllHistoricalRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryHistoricalRecordReqDto) (list *[]po.HistoricalRecord, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.HistoricalRecord{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.HistoricalRecord{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype HistoricalRecordController struct{}\n\nvar (\n\thistoricalRecordService  = impl.HistoricalRecordService{}\n\thistoricalRecordTransfer = transfer.HistoricalRecordTransfer{}\n)\n\n// @Summary 添加历史记录\n// @Description 添加历史记录\n// @Tags 历史记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddHistoricalRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.HistoricalRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/historical-record/add [post]\nfunc (controller *HistoricalRecordController) AddHistoricalRecord(ctx *gin.Context) {\n\treqDto := req.AddHistoricalRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\trecord := po.HistoricalRecord{}\n\tif reqDto.OpenTables != nil {\n\t\trecord.OpenTables = reqDto.OpenTables\n\t}\n\tif reqDto.TotalOpenedTables != nil {\n\t\trecord.TotalOpenedTables = reqDto.TotalOpenedTables\n\t}\n\tif reqDto.FinishedTables != nil {\n\t\trecord.FinishedTables = reqDto.FinishedTables\n\t}\n\tif reqDto.SettlementAmount != nil {\n\t\trecord.SettlementAmount = reqDto.SettlementAmount\n\t}\n\n\terr = historicalRecordService.CreateHistoricalRecord(ctx, &record)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, historicalRecordTransfer.PoToVo(record))\n}\n\n// @Summary 更新历史记录\n// @Description 更新历史记录\n// @Tags 历史记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateHistoricalRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.HistoricalRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/historical-record/update [post]\nfunc (controller *HistoricalRecordController) UpdateHistoricalRecord(ctx *gin.Context) {\n\treqDto := req.UpdateHistoricalRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\trecord, err := historicalRecordService.FindHistoricalRecordById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.OpenTables != nil {\n\t\trecord.OpenTables = reqDto.OpenTables\n\t}\n\tif reqDto.TotalOpenedTables != nil {\n\t\trecord.TotalOpenedTables = reqDto.TotalOpenedTables\n\t}\n\tif reqDto.FinishedTables != nil {\n\t\trecord.FinishedTables = reqDto.FinishedTables\n\t}\n\tif reqDto.SettlementAmount != nil {\n\t\trecord.SettlementAmount = reqDto.SettlementAmount\n\t}\n\n\terr = historicalRecordService.UpdateHistoricalRecord(ctx, record)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, historicalRecordTransfer.PoToVo(*record))\n}\n\n// @Summary 删除历史记录\n// @Description 删除历史记录\n// @Tags 历史记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteHistoricalRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/historical-record/delete [post]\nfunc (controller *HistoricalRecordController) DeleteHistoricalRecord(ctx *gin.Context) {\n\treqDto := req.DeleteHistoricalRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = historicalRecordService.DeleteHistoricalRecord(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询历史记录\n// @Description 查询历史记录\n// @Tags 历史记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryHistoricalRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.HistoricalRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/historical-record/query [post]\nfunc (controller *HistoricalRecordController) QueryHistoricalRecords(ctx *gin.Context) {\n\treqDto := req.QueryHistoricalRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := historicalRecordService.FindAllHistoricalRecord(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.HistoricalRecordVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, historicalRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询历史记录列表\n// @Description 查询历史记录列表\n// @Tags 历史记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryHistoricalRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.HistoricalRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/historical-record/list [post]\nfunc (controller *HistoricalRecordController) ListHistoricalRecords(ctx *gin.Context) {\n\treqDto := req.QueryHistoricalRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := historicalRecordService.FindAllHistoricalRecordWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.HistoricalRecordVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.HistoricalRecordVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, historicalRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype HistoricalRecordRoute struct {\n}\n\nfunc (s *HistoricalRecordRoute) InitHistoricalRecordRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\thistoricalRecordController := controller.HistoricalRecordController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/historical-record/add\", historicalRecordController.AddHistoricalRecord)       // add\n\t\troute.POST(\"/api/historical-record/update\", historicalRecordController.UpdateHistoricalRecord) // update\n\t\troute.POST(\"/api/historical-record/delete\", historicalRecordController.DeleteHistoricalRecord) // delete\n\t\troute.POST(\"/api/historical-record/query\", historicalRecordController.QueryHistoricalRecords)   // query\n\t\troute.POST(\"/api/historical-record/list\", historicalRecordController.ListHistoricalRecords)     // list\n\t}\n}\n"}]