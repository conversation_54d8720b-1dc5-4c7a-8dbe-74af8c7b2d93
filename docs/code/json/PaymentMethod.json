[{"po": "package po\n\n// PaymentMethod 支付方式实体\ntype PaymentMethod struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tName      *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`            // 支付方式名称\n\tIsEnabled *bool   `gorm:\"column:is_enabled;type:tinyint(1);default:0\" json:\"isEnabled\"`    // 是否启用\n\tSortOrder *int    `gorm:\"column:sort_order;type:int;default:0\" json:\"sortOrder\"`          // 排序顺序\n\tType      *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`            // 支付方式类型\n\tCtime     *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`               // 创建时间\n\tUtime     *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`               // 更新时间\n\tState     *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                  // 状态\n\tVersion   *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`              // 版本号\n}\n\n// TableName 设置表名\nfunc (PaymentMethod) TableName() string {\n\treturn \"payment_method\"\n}\n\nfunc (p PaymentMethod) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PaymentMethodVO 支付方式值对象\ntype PaymentMethodVO struct {\n\tId        string `json:\"id\"`        // ID\n\tName      string `json:\"name\"`      // 支付方式名称\n\tIsEnabled bool   `json:\"isEnabled\"` // 是否启用\n\tSortOrder int    `json:\"sortOrder\"` // 排序顺序\n\tType      string `json:\"type\"`      // 支付方式类型\n\tCtime     int64  `json:\"ctime\"`     // 创建时间\n\tUtime     int64  `json:\"utime\"`     // 更新时间\n\tState     int    `json:\"state\"`     // 状态\n\tVersion   int    `json:\"version\"`   // 版本号\n}\n", "req_add": "package req\n\n// AddPaymentMethodReqDto 创建支付方式请求DTO\ntype AddPaymentMethodReqDto struct {\n\tName      *string `json:\"name\"`      // 支付方式名称\n\tIsEnabled *bool   `json:\"isEnabled\"` // 是否启用\n\tSortOrder *int    `json:\"sortOrder\"` // 排序顺序\n\tType      *string `json:\"type\"`      // 支付方式类型\n}\n", "req_update": "package req\n\n// UpdatePaymentMethodReqDto 更新支付方式请求DTO\ntype UpdatePaymentMethodReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tName      *string `json:\"name\"`      // 支付方式名称\n\tIsEnabled *bool   `json:\"isEnabled\"` // 是否启用\n\tSortOrder *int    `json:\"sortOrder\"` // 排序顺序\n\tType      *string `json:\"type\"`      // 支付方式类型\n}\n", "req_delete": "package req\n\n// DeletePaymentMethodReqDto 删除支付方式请求DTO\ntype DeletePaymentMethodReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryPaymentMethodReqDto 查询支付方式请求DTO\ntype QueryPaymentMethodReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tName      *string `json:\"name\"`      // 支付方式名称\n\tIsEnabled *bool   `json:\"isEnabled\"` // 是否启用\n\tSortOrder *int    `json:\"sortOrder\"` // 排序顺序\n\tType      *string `json:\"type\"`      // 支付方式类型\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PaymentMethodTransfer struct {\n}\n\nfunc (transfer *PaymentMethodTransfer) PoToVo(po po.PaymentMethod) vo.PaymentMethodVO {\n\tvo := vo.PaymentMethodVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PaymentMethodTransfer) VoToPo(vo vo.PaymentMethodVO) po.PaymentMethod {\n\tpo := po.PaymentMethod{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PaymentMethodService struct {\n}\n\nfunc (service *PaymentMethodService) CreatePaymentMethod(logCtx *gin.Context, paymentMethod *po.PaymentMethod) error {\n\treturn Save(paymentMethod)\n}\n\nfunc (service *PaymentMethodService) UpdatePaymentMethod(logCtx *gin.Context, paymentMethod *po.PaymentMethod) error {\n\treturn Update(paymentMethod)\n}\n\nfunc (service *PaymentMethodService) DeletePaymentMethod(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PaymentMethod{Id: &id})\n}\n\nfunc (service *PaymentMethodService) FindPaymentMethodById(logCtx *gin.Context, id string) (paymentMethod *po.PaymentMethod, err error) {\n\tpaymentMethod = &po.PaymentMethod{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(paymentMethod).Error\n\treturn\n}\n\nfunc (service *PaymentMethodService) FindAllPaymentMethod(logCtx *gin.Context, reqDto *req.QueryPaymentMethodReqDto) (list *[]po.PaymentMethod, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PaymentMethod{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled=?\", *reqDto.IsEnabled)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\n\tdb = db.Order(\"sort_order asc, ctime desc\")\n\tlist = &[]po.PaymentMethod{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PaymentMethodService) FindAllPaymentMethodWithPagination(logCtx *gin.Context, reqDto *req.QueryPaymentMethodReqDto) (list *[]po.PaymentMethod, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PaymentMethod{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled=?\", *reqDto.IsEnabled)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PaymentMethod{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"sort_order asc, ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PaymentMethodController struct{}\n\nvar (\n\tpaymentMethodService  = impl.PaymentMethodService{}\n\tpaymentMethodTransfer = transfer.PaymentMethodTransfer{}\n)\n\n// @Summary 添加支付方式\n// @Description 添加支付方式\n// @Tags 支付方式\n// @Accept json\n// @Produce json\n// @Param body body req.AddPaymentMethodReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PaymentMethodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/payment-method/add [post]\nfunc (controller *PaymentMethodController) AddPaymentMethod(ctx *gin.Context) {\n\treqDto := req.AddPaymentMethodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpaymentMethod := po.PaymentMethod{}\n\tif reqDto.Name != nil {\n\t\tpaymentMethod.Name = reqDto.Name\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpaymentMethod.IsEnabled = reqDto.IsEnabled\n\t}\n\tif reqDto.SortOrder != nil {\n\t\tpaymentMethod.SortOrder = reqDto.SortOrder\n\t}\n\tif reqDto.Type != nil {\n\t\tpaymentMethod.Type = reqDto.Type\n\t}\n\n\terr = paymentMethodService.CreatePaymentMethod(ctx, &paymentMethod)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, paymentMethodTransfer.PoToVo(paymentMethod))\n}\n\n// @Summary 更新支付方式\n// @Description 更新支付方式\n// @Tags 支付方式\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePaymentMethodReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PaymentMethodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/payment-method/update [post]\nfunc (controller *PaymentMethodController) UpdatePaymentMethod(ctx *gin.Context) {\n\treqDto := req.UpdatePaymentMethodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tpaymentMethod, err := paymentMethodService.FindPaymentMethodById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tpaymentMethod.Name = reqDto.Name\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpaymentMethod.IsEnabled = reqDto.IsEnabled\n\t}\n\tif reqDto.SortOrder != nil {\n\t\tpaymentMethod.SortOrder = reqDto.SortOrder\n\t}\n\tif reqDto.Type != nil {\n\t\tpaymentMethod.Type = reqDto.Type\n\t}\n\n\terr = paymentMethodService.UpdatePaymentMethod(ctx, paymentMethod)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, paymentMethodTransfer.PoToVo(*paymentMethod))\n}\n\n// @Summary 删除支付方式\n// @Description 删除支付方式\n// @Tags 支付方式\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePaymentMethodReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/payment-method/delete [post]\nfunc (controller *PaymentMethodController) DeletePaymentMethod(ctx *gin.Context) {\n\treqDto := req.DeletePaymentMethodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = paymentMethodService.DeletePaymentMethod(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询支付方式\n// @Description 查询支付方式\n// @Tags 支付方式\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPaymentMethodReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PaymentMethodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/payment-method/query [post]\nfunc (controller *PaymentMethodController) QueryPaymentMethods(ctx *gin.Context) {\n\treqDto := req.QueryPaymentMethodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := paymentMethodService.FindAllPaymentMethod(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.PaymentMethodVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, paymentMethodTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询支付方式列表\n// @Description 查询支付方式列表\n// @Tags 支付方式\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPaymentMethodReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PaymentMethodVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/payment-method/list [post]\nfunc (controller *PaymentMethodController) ListPaymentMethods(ctx *gin.Context) {\n\treqDto := req.QueryPaymentMethodReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := paymentMethodService.FindAllPaymentMethodWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.PaymentMethodVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PaymentMethodVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, paymentMethodTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PaymentMethodRoute struct {\n}\n\nfunc (s *PaymentMethodRoute) InitPaymentMethodRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tpaymentMethodController := controller.PaymentMethodController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/payment-method/add\", paymentMethodController.AddPaymentMethod)       // add\n\t\troute.POST(\"/api/payment-method/update\", paymentMethodController.UpdatePaymentMethod) // update\n\t\troute.POST(\"/api/payment-method/delete\", paymentMethodController.DeletePaymentMethod) // delete\n\t\troute.POST(\"/api/payment-method/query\", paymentMethodController.QueryPaymentMethods)  // query\n\t\troute.POST(\"/api/payment-method/list\", paymentMethodController.ListPaymentMethods)   // list\n\t}\n}\n"}]