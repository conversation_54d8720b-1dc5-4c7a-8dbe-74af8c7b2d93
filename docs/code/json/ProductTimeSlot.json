[{"po": "package po\n\n// ProductTimeSlot 产品时间段实体\ntype ProductTimeSlot struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tName      *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`            // 名称\n\tVenueId   *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`      // 门店ID\n\tType      *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`             // 类型\n\tDays      *string `gorm:\"column:days;type:varchar(255);default:''\" json:\"days\"`            // 星期几\n\tTimerange *string `gorm:\"column:timerange;type:varchar(255);default:''\" json:\"timerange\"`  // 时间范围\n\tCtime     *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime     *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间\n\tState     *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态\n\tVersion   *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductTimeSlot) TableName() string {\n\treturn \"product_time_slot\"\n}\n\nfunc (p ProductTimeSlot) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductTimeSlotVO 产品时间段值对象\ntype ProductTimeSlotVO struct {\n\tId        string `json:\"id\"`        // ID\n\tName      string `json:\"name\"`      // 名称\n\tVenueId   string `json:\"venueId\"`   // 门店ID\n\tType      string `json:\"type\"`      // 类型\n\tDays      string `json:\"days\"`      // 星期几\n\tTimerange string `json:\"timerange\"` // 时间范围\n\tCtime     int64  `json:\"ctime\"`     // 创建时间\n\tUtime     int64  `json:\"utime\"`     // 更新时间\n\tState     int    `json:\"state\"`     // 状态\n\tVersion   int    `json:\"version\"`   // 版本号\n}\n", "req_add": "package req\n\n// AddProductTimeSlotReqDto 创建产品时间段请求DTO\ntype AddProductTimeSlotReqDto struct {\n\tName      *string `json:\"name\"`      // 名称\n\tVenueId   *string `json:\"venueId\"`   // 门店ID\n\tType      *string `json:\"type\"`      // 类型\n\tDays      *string `json:\"days\"`      // 星期几\n\tTimerange *string `json:\"timerange\"` // 时间范围\n}\n", "req_update": "package req\n\n// UpdateProductTimeSlotReqDto 更新产品时间段请求DTO\ntype UpdateProductTimeSlotReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tName      *string `json:\"name\"`      // 名称\n\tVenueId   *string `json:\"venueId\"`   // 门店ID\n\tType      *string `json:\"type\"`      // 类型\n\tDays      *string `json:\"days\"`      // 星期几\n\tTimerange *string `json:\"timerange\"` // 时间范围\n}\n", "req_delete": "package req\n\n// DeleteProductTimeSlotReqDto 删除产品时间段请求DTO\ntype DeleteProductTimeSlotReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryProductTimeSlotReqDto 查询产品时间段请求DTO\ntype QueryProductTimeSlotReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tName      *string `json:\"name\"`      // 名称\n\tVenueId   *string `json:\"venueId\"`   // 门店ID\n\tType      *string `json:\"type\"`      // 类型\n\tDays      *string `json:\"days\"`      // 星期几\n\tTimerange *string `json:\"timerange\"` // 时间范围\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductTimeSlotTransfer struct {\n}\n\nfunc (transfer *ProductTimeSlotTransfer) PoToVo(po po.ProductTimeSlot) vo.ProductTimeSlotVO {\n\tvo := vo.ProductTimeSlotVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductTimeSlotTransfer) VoToPo(vo vo.ProductTimeSlotVO) po.ProductTimeSlot {\n\tpo := po.ProductTimeSlot{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductTimeSlotService struct {\n}\n\nfunc (service *ProductTimeSlotService) CreateProductTimeSlot(logCtx *gin.Context, productTimeSlot *po.ProductTimeSlot) error {\n\treturn Save(productTimeSlot)\n}\n\nfunc (service *ProductTimeSlotService) UpdateProductTimeSlot(logCtx *gin.Context, productTimeSlot *po.ProductTimeSlot) error {\n\treturn Update(productTimeSlot)\n}\n\nfunc (service *ProductTimeSlotService) DeleteProductTimeSlot(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductTimeSlot{Id: &id})\n}\n\nfunc (service *ProductTimeSlotService) FindProductTimeSlotById(logCtx *gin.Context, id string) (productTimeSlot *po.ProductTimeSlot, err error) {\n\tproductTimeSlot = &po.ProductTimeSlot{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productTimeSlot).Error\n\treturn\n}\n\nfunc (service *ProductTimeSlotService) FindAllProductTimeSlot(logCtx *gin.Context, reqDto *req.QueryProductTimeSlotReqDto) (list *[]po.ProductTimeSlot, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductTimeSlot{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.Days != nil && *reqDto.Days != \"\" {\n\t\tdb = db.Where(\"days=?\", *reqDto.Days)\n\t}\n\tif reqDto.Timerange != nil && *reqDto.Timerange != \"\" {\n\t\tdb = db.Where(\"timerange=?\", *reqDto.Timerange)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductTimeSlot{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductTimeSlotService) FindAllProductTimeSlotWithPagination(logCtx *gin.Context, reqDto *req.QueryProductTimeSlotReqDto) (list *[]po.ProductTimeSlot, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductTimeSlot{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.Days != nil && *reqDto.Days != \"\" {\n\t\tdb = db.Where(\"days=?\", *reqDto.Days)\n\t}\n\tif reqDto.Timerange != nil && *reqDto.Timerange != \"\" {\n\t\tdb = db.Where(\"timerange=?\", *reqDto.Timerange)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductTimeSlot{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductTimeSlotController struct{}\n\nvar (\n\tproductTimeSlotService  = impl.ProductTimeSlotService{}\n\tproductTimeSlotTransfer = transfer.ProductTimeSlotTransfer{}\n)\n\n// @Summary 添加产品时间段\n// @Description 添加产品时间段\n// @Tags 产品时间段\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductTimeSlotReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductTimeSlotVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productTimeSlot/add [post]\nfunc (controller *ProductTimeSlotController) AddProductTimeSlot(ctx *gin.Context) {\n\treqDto := req.AddProductTimeSlotReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductTimeSlot := po.ProductTimeSlot{}\n\tif reqDto.Name != nil {\n\t\tproductTimeSlot.Name = reqDto.Name\n\t}\n\tif reqDto.VenueId != nil {\n\t\tproductTimeSlot.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Type != nil {\n\t\tproductTimeSlot.Type = reqDto.Type\n\t}\n\tif reqDto.Days != nil {\n\t\tproductTimeSlot.Days = reqDto.Days\n\t}\n\tif reqDto.Timerange != nil {\n\t\tproductTimeSlot.Timerange = reqDto.Timerange\n\t}\n\n\terr = productTimeSlotService.CreateProductTimeSlot(ctx, &productTimeSlot)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productTimeSlotTransfer.PoToVo(productTimeSlot))\n}\n\n// @Summary 更新产品时间段\n// @Description 更新产品时间段\n// @Tags 产品时间段\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductTimeSlotReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductTimeSlotVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productTimeSlot/update [post]\nfunc (controller *ProductTimeSlotController) UpdateProductTimeSlot(ctx *gin.Context) {\n\treqDto := req.UpdateProductTimeSlotReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproductTimeSlot, err := productTimeSlotService.FindProductTimeSlotById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tproductTimeSlot.Name = reqDto.Name\n\t}\n\tif reqDto.VenueId != nil {\n\t\tproductTimeSlot.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Type != nil {\n\t\tproductTimeSlot.Type = reqDto.Type\n\t}\n\tif reqDto.Days != nil {\n\t\tproductTimeSlot.Days = reqDto.Days\n\t}\n\tif reqDto.Timerange != nil {\n\t\tproductTimeSlot.Timerange = reqDto.Timerange\n\t}\n\n\terr = productTimeSlotService.UpdateProductTimeSlot(ctx, productTimeSlot)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productTimeSlotTransfer.PoToVo(*productTimeSlot))\n}\n\n// @Summary 删除产品时间段\n// @Description 删除产品时间段\n// @Tags 产品时间段\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductTimeSlotReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productTimeSlot/delete [post]\nfunc (controller *ProductTimeSlotController) DeleteProductTimeSlot(ctx *gin.Context) {\n\treqDto := req.DeleteProductTimeSlotReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = productTimeSlotService.DeleteProductTimeSlot(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品时间段\n// @Description 查询产品时间段\n// @Tags 产品时间段\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductTimeSlotReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductTimeSlotVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productTimeSlot/query [post]\nfunc (controller *ProductTimeSlotController) QueryProductTimeSlots(ctx *gin.Context) {\n\treqDto := req.QueryProductTimeSlotReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := productTimeSlotService.FindAllProductTimeSlot(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.ProductTimeSlotVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productTimeSlotTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品时间段列表\n// @Description 查询产品时间段列表\n// @Tags 产品时间段\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductTimeSlotReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductTimeSlotVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productTimeSlot/list [post]\nfunc (controller *ProductTimeSlotController) ListProductTimeSlots(ctx *gin.Context) {\n\treqDto := req.QueryProductTimeSlotReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := productTimeSlotService.FindAllProductTimeSlotWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.ProductTimeSlotVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductTimeSlotVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productTimeSlotTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductTimeSlotRoute struct {\n}\n\nfunc (s *ProductTimeSlotRoute) InitProductTimeSlotRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductTimeSlotController := controller.ProductTimeSlotController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productTimeSlot/add\", productTimeSlotController.AddProductTimeSlot)       // add\n\t\troute.POST(\"/api/productTimeSlot/update\", productTimeSlotController.UpdateProductTimeSlot)   // update\n\t\troute.POST(\"/api/productTimeSlot/delete\", productTimeSlotController.DeleteProductTimeSlot)   // delete\n\t\troute.POST(\"/api/productTimeSlot/query\", productTimeSlotController.QueryProductTimeSlots)    // query\n\t\troute.POST(\"/api/productTimeSlot/list\", productTimeSlotController.ListProductTimeSlots)     // list\n\t}\n}\n"}]