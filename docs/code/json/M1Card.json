[{"po": "package po\n\n// M1Card M1卡实体\ntype M1Card struct {\n\tId       *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tCardType *string `gorm:\"column:card_type;type:varchar(64);default:''\" json:\"cardType\"` // 卡类型\n\tPassword *string `gorm:\"column:password;type:varchar(64);default:''\" json:\"password\"`   // 密码\n\tCtime    *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                 // 创建时间戳\n\tUtime    *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                 // 更新时间戳\n\tState    *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                 // 状态值\n\tVersion  *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`             // 版本号\n}\n\n// TableName 设置表名\nfunc (M1Card) TableName() string {\n\treturn \"m1_card\"\n}\n\nfunc (m M1Card) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// M1CardVO M1卡信息值对象\ntype M1CardVO struct {\n\tId       string `json:\"id\"`       // ID\n\tCardType string `json:\"cardType\"` // 卡类型\n\tPassword string `json:\"password\"` // 密码\n\tCtime    int64  `json:\"ctime\"`    // 创建时间戳\n\tUtime    int64  `json:\"utime\"`    // 更新时间戳\n\tState    int    `json:\"state\"`    // 状态值\n\tVersion  int    `json:\"version\"`  // 版本号\n}\n", "req_add": "package req\n\n// AddM1CardReqDto 创建M1卡请求DTO\ntype AddM1CardReqDto struct {\n\tCardType *string `json:\"cardType\"` // 卡类型\n\tPassword *string `json:\"password\"` // 密码\n}\n", "req_update": "package req\n\ntype UpdateM1CardReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tCardType *string `json:\"cardType\"` // 卡类型\n\tPassword *string `json:\"password\"` // 密码\n}\n", "req_delete": "package req\n\ntype DeleteM1CardReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryM1CardReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tCardType *string `json:\"cardType\"` // 卡类型\n\tPassword *string `json:\"password\"` // 密码\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype M1CardTransfer struct {\n}\n\nfunc (transfer *M1CardTransfer) PoToVo(po po.M1Card) vo.M1CardVO {\n\tvo := vo.M1CardVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *M1CardTransfer) VoToPo(vo vo.M1CardVO) po.M1Card {\n\tpo := po.M1Card{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype M1CardService struct {\n}\n\nfunc (service *M1CardService) CreateM1Card(logCtx *gin.Context, m1Card *po.M1Card) error {\n\treturn Save(m1Card)\n}\n\nfunc (service *M1CardService) UpdateM1Card(logCtx *gin.Context, m1Card *po.M1Card) error {\n\treturn Update(m1Card)\n}\n\nfunc (service *M1CardService) DeleteM1Card(logCtx *gin.Context, id string) error {\n\treturn Delete(po.M1Card{Id: &id})\n}\n\nfunc (service *M1CardService) FindM1CardById(logCtx *gin.Context, id string) (m1Card *po.M1Card, err error) {\n\tm1Card = &po.M1Card{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(m1Card).Error\n\treturn\n}\n\nfunc (service *M1CardService) FindAllM1Card(logCtx *gin.Context, reqDto *req.QueryM1CardReqDto) (list *[]po.M1Card, err error) {\n\tdb := model.DBSlave.Self.Model(&po.M1Card{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.M1Card{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *M1CardService) FindAllM1CardWithPagination(logCtx *gin.Context, reqDto *req.QueryM1CardReqDto) (list *[]po.M1Card, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.M1Card{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.M1Card{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype M1CardController struct{}\n\nvar (\n\tm1CardService  = impl.M1CardService{}\n\tm1CardTransfer = transfer.M1CardTransfer{}\n)\n\n// @Summary 添加M1卡\n// @Description 添加M1卡\n// @Tags M1卡\n// @Accept json\n// @Produce json\n// @Param body body req.AddM1CardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.M1CardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/m1card/add [post]\nfunc (controller *M1CardController) AddM1Card(ctx *gin.Context) {\n\treqDto := req.AddM1CardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tm1Card := po.M1Card{}\n\tif reqDto.CardType != nil {\n\t\tm1Card.CardType = reqDto.CardType\n\t}\n\tif reqDto.Password != nil {\n\t\tm1Card.Password = reqDto.Password\n\t}\n\terr = m1CardService.CreateM1Card(ctx, &m1Card)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, m1CardTransfer.PoToVo(m1Card))\n}\n\n// @Summary 更新M1卡\n// @Description 更新M1卡\n// @Tags M1卡\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateM1CardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.M1CardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/m1card/update [post]\nfunc (controller *M1CardController) UpdateM1Card(ctx *gin.Context) {\n\treqDto := req.UpdateM1CardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tm1Card, err := m1CardService.FindM1CardById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CardType != nil {\n\t\tm1Card.CardType = reqDto.CardType\n\t}\n\tif reqDto.Password != nil {\n\t\tm1Card.Password = reqDto.Password\n\t}\n\terr = m1CardService.UpdateM1Card(ctx, m1Card)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, m1CardTransfer.PoToVo(*m1Card))\n}\n\n// @Summary 删除M1卡\n// @Description 删除M1卡\n// @Tags M1卡\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteM1CardReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/m1card/delete [post]\nfunc (controller *M1CardController) DeleteM1Card(ctx *gin.Context) {\n\treqDto := req.DeleteM1CardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = m1CardService.DeleteM1Card(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询M1卡\n// @Description 查询M1卡\n// @Tags M1卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryM1CardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.M1CardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/m1card/query [post]\nfunc (controller *M1CardController) QueryM1Cards(ctx *gin.Context) {\n\treqDto := req.QueryM1CardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := m1CardService.FindAllM1Card(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.M1CardVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, m1CardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询M1卡列表\n// @Description 查询M1卡列表\n// @Tags M1卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryM1CardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.M1CardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/m1card/list [post]\nfunc (a *M1CardController) ListM1Cards(ctx *gin.Context) {\n\treqDto := req.QueryM1CardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := m1CardService.FindAllM1CardWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.M1CardVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.M1CardVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, m1CardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype M1CardRoute struct {\n}\n\nfunc (s *M1CardRoute) InitM1CardRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tm1CardController := controller.M1CardController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/m1card/add\", m1CardController.AddM1Card)       //add\n\t\troute.POST(\"/api/m1card/update\", m1CardController.UpdateM1Card) //update\n\t\troute.POST(\"/api/m1card/delete\", m1CardController.DeleteM1Card) //delete\n\t\troute.POST(\"/api/m1card/query\", m1CardController.QueryM1Cards)  //query\n\t\troute.POST(\"/api/m1card/list\", m1CardController.ListM1Cards)    //list\n\t}\n}\n"}]