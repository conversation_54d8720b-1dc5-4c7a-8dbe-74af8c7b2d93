[{"po": "package po\n\n// PermissionRole 权限角色实体\ntype PermissionRole struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                      // 角色名称\n\tEmployeeType        *string `gorm:\"column:employee_type;type:varchar(64);default:''\" json:\"employeeType\"`      // 员工类型\n\tModulePermissions   *string `gorm:\"column:module_permissions;type:text\" json:\"modulePermissions\"`              // 模块权限列表\n\tBusinessPermissions *string `gorm:\"column:business_permissions;type:text\" json:\"businessPermissions\"`          // 业务权限列表\n\tGiftPermissions     *string `gorm:\"column:gift_permissions;type:text\" json:\"giftPermissions\"`                  // 礼品权限列表\n\tCashierPermissions  *string `gorm:\"column:cashier_permissions;type:text\" json:\"cashierPermissions\"`            // 收银权限列表\n\tAllowedProductTypes *string `gorm:\"column:allowed_product_types;type:text\" json:\"allowedProductTypes\"`          // 允许的产品类型列表\n\tCtime              *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                             // 创建时间戳\n\tUtime              *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                             // 更新时间戳\n\tState              *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                             // 状态值\n\tVersion            *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                         // 版本号\n}\n\n// TableName 设置表名\nfunc (PermissionRole) TableName() string {\n\treturn \"permission_role\"\n}\n\nfunc (p PermissionRole) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PermissionRoleVO 权限角色值对象\ntype PermissionRoleVO struct {\n\tId                  string `json:\"id\"`                  // ID\n\tName                string `json:\"name\"`                // 角色名称\n\tEmployeeType        string `json:\"employeeType\"`        // 员工类型\n\tModulePermissions   string `json:\"modulePermissions\"`   // 模块权限列表\n\tBusinessPermissions string `json:\"businessPermissions\"` // 业务权限列表\n\tGiftPermissions     string `json:\"giftPermissions\"`     // 礼品权限列表\n\tCashierPermissions  string `json:\"cashierPermissions\"`  // 收银权限列表\n\tAllowedProductTypes string `json:\"allowedProductTypes\"` // 允许的产品类型列表\n\tCtime               int64  `json:\"ctime\"`               // 创建时间戳\n\tUtime               int64  `json:\"utime\"`               // 更新时间戳\n\tState               int    `json:\"state\"`               // 状态值\n\tVersion             int    `json:\"version\"`             // 版本号\n}\n", "req_add": "package req\n\n// AddPermissionRoleReqDto 创建权限角色请求DTO\ntype AddPermissionRoleReqDto struct {\n\tName                *string `json:\"name\"`                // 角色名称\n\tEmployeeType        *string `json:\"employeeType\"`        // 员工类型\n\tModulePermissions   *string `json:\"modulePermissions\"`   // 模块权限列表\n\tBusinessPermissions *string `json:\"businessPermissions\"` // 业务权限列表\n\tGiftPermissions     *string `json:\"giftPermissions\"`     // 礼品权限列表\n\tCashierPermissions  *string `json:\"cashierPermissions\"`  // 收银权限列表\n\tAllowedProductTypes *string `json:\"allowedProductTypes\"` // 允许的产品类型列表\n}\n", "req_update": "package req\n\n// UpdatePermissionRoleReqDto 更新权限角色请求DTO\ntype UpdatePermissionRoleReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tName                *string `json:\"name\"`                // 角色名称\n\tEmployeeType        *string `json:\"employeeType\"`        // 员工类型\n\tModulePermissions   *string `json:\"modulePermissions\"`   // 模块权限列表\n\tBusinessPermissions *string `json:\"businessPermissions\"` // 业务权限列表\n\tGiftPermissions     *string `json:\"giftPermissions\"`     // 礼品权限列表\n\tCashierPermissions  *string `json:\"cashierPermissions\"`  // 收银权限列表\n\tAllowedProductTypes *string `json:\"allowedProductTypes\"` // 允许的产品类型列表\n}\n", "req_delete": "package req\n\n// DeletePermissionRoleReqDto 删除权限角色请求DTO\ntype DeletePermissionRoleReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryPermissionRoleReqDto 查询权限角色请求DTO\ntype QueryPermissionRoleReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tName                *string `json:\"name\"`                // 角色名称\n\tEmployeeType        *string `json:\"employeeType\"`        // 员工类型\n\tModulePermissions   *string `json:\"modulePermissions\"`   // 模块权限列表\n\tBusinessPermissions *string `json:\"businessPermissions\"` // 业务权限列表\n\tGiftPermissions     *string `json:\"giftPermissions\"`     // 礼品权限列表\n\tCashierPermissions  *string `json:\"cashierPermissions\"`  // 收银权限列表\n\tAllowedProductTypes *string `json:\"allowedProductTypes\"` // 允许的产品类型列表\n\tPageNum             *int    `json:\"pageNum\"`             // 页码\n\tPageSize            *int    `json:\"pageSize\"`            // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PermissionRoleTransfer struct {\n}\n\nfunc (transfer *PermissionRoleTransfer) PoToVo(po po.PermissionRole) vo.PermissionRoleVO {\n\tvo := vo.PermissionRoleVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PermissionRoleTransfer) VoToPo(vo vo.PermissionRoleVO) po.PermissionRole {\n\tpo := po.PermissionRole{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PermissionRoleService struct {\n}\n\nfunc (service *PermissionRoleService) CreatePermissionRole(logCtx *gin.Context, permissionRole *po.PermissionRole) error {\n\treturn Save(permissionRole)\n}\n\nfunc (service *PermissionRoleService) UpdatePermissionRole(logCtx *gin.Context, permissionRole *po.PermissionRole) error {\n\treturn Update(permissionRole)\n}\n\nfunc (service *PermissionRoleService) DeletePermissionRole(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PermissionRole{Id: &id})\n}\n\nfunc (service *PermissionRoleService) FindPermissionRoleById(logCtx *gin.Context, id string) (permissionRole *po.PermissionRole, err error) {\n\tpermissionRole = &po.PermissionRole{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(permissionRole).Error\n\treturn\n}\n\nfunc (service *PermissionRoleService) FindAllPermissionRole(logCtx *gin.Context, reqDto *req.QueryPermissionRoleReqDto) (list *[]po.PermissionRole, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PermissionRole{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.EmployeeType != nil && *reqDto.EmployeeType != \"\" {\n\t\tdb = db.Where(\"employee_type=?\", *reqDto.EmployeeType)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PermissionRole{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PermissionRoleService) FindAllPermissionRoleWithPagination(logCtx *gin.Context, reqDto *req.QueryPermissionRoleReqDto) (list *[]po.PermissionRole, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PermissionRole{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.EmployeeType != nil && *reqDto.EmployeeType != \"\" {\n\t\tdb = db.Where(\"employee_type=?\", *reqDto.EmployeeType)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PermissionRole{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PermissionRoleController struct{}\n\nvar (\n\tpermissionRoleService  = impl.PermissionRoleService{}\n\tpermissionRoleTransfer = transfer.PermissionRoleTransfer{}\n)\n\n// @Summary 添加权限角色\n// @Description 添加权限角色\n// @Tags 权限角色\n// @Accept json\n// @Produce json\n// @Param body body req.AddPermissionRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PermissionRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/permission-role/add [post]\nfunc (controller *PermissionRoleController) AddPermissionRole(ctx *gin.Context) {\n\treqDto := req.AddPermissionRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpermissionRole := po.PermissionRole{}\n\tif reqDto.Name != nil {\n\t\tpermissionRole.Name = reqDto.Name\n\t}\n\tif reqDto.EmployeeType != nil {\n\t\tpermissionRole.EmployeeType = reqDto.EmployeeType\n\t}\n\tif reqDto.ModulePermissions != nil {\n\t\tpermissionRole.ModulePermissions = reqDto.ModulePermissions\n\t}\n\tif reqDto.BusinessPermissions != nil {\n\t\tpermissionRole.BusinessPermissions = reqDto.BusinessPermissions\n\t}\n\tif reqDto.GiftPermissions != nil {\n\t\tpermissionRole.GiftPermissions = reqDto.GiftPermissions\n\t}\n\tif reqDto.CashierPermissions != nil {\n\t\tpermissionRole.CashierPermissions = reqDto.CashierPermissions\n\t}\n\tif reqDto.AllowedProductTypes != nil {\n\t\tpermissionRole.AllowedProductTypes = reqDto.AllowedProductTypes\n\t}\n\n\terr = permissionRoleService.CreatePermissionRole(ctx, &permissionRole)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, permissionRoleTransfer.PoToVo(permissionRole))\n}\n\n// @Summary 更新权限角色\n// @Description 更新权限角色\n// @Tags 权限角色\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePermissionRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PermissionRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/permission-role/update [post]\nfunc (controller *PermissionRoleController) UpdatePermissionRole(ctx *gin.Context) {\n\treqDto := req.UpdatePermissionRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tpermissionRole, err := permissionRoleService.FindPermissionRoleById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tpermissionRole.Name = reqDto.Name\n\t}\n\tif reqDto.EmployeeType != nil {\n\t\tpermissionRole.EmployeeType = reqDto.EmployeeType\n\t}\n\tif reqDto.ModulePermissions != nil {\n\t\tpermissionRole.ModulePermissions = reqDto.ModulePermissions\n\t}\n\tif reqDto.BusinessPermissions != nil {\n\t\tpermissionRole.BusinessPermissions = reqDto.BusinessPermissions\n\t}\n\tif reqDto.GiftPermissions != nil {\n\t\tpermissionRole.GiftPermissions = reqDto.GiftPermissions\n\t}\n\tif reqDto.CashierPermissions != nil {\n\t\tpermissionRole.CashierPermissions = reqDto.CashierPermissions\n\t}\n\tif reqDto.AllowedProductTypes != nil {\n\t\tpermissionRole.AllowedProductTypes = reqDto.AllowedProductTypes\n\t}\n\n\terr = permissionRoleService.UpdatePermissionRole(ctx, permissionRole)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, permissionRoleTransfer.PoToVo(*permissionRole))\n}\n\n// @Summary 删除权限角色\n// @Description 删除权限角色\n// @Tags 权限角色\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePermissionRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/permission-role/delete [post]\nfunc (controller *PermissionRoleController) DeletePermissionRole(ctx *gin.Context) {\n\treqDto := req.DeletePermissionRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = permissionRoleService.DeletePermissionRole(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询权限角色\n// @Description 查询权限角色\n// @Tags 权限角色\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPermissionRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PermissionRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/permission-role/query [post]\nfunc (controller *PermissionRoleController) QueryPermissionRoles(ctx *gin.Context) {\n\treqDto := req.QueryPermissionRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := permissionRoleService.FindAllPermissionRole(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PermissionRoleVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, permissionRoleTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询权限角色列表\n// @Description 查询权限角色列表\n// @Tags 权限角色\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPermissionRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PermissionRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/permission-role/list [post]\nfunc (a *PermissionRoleController) ListPermissionRoles(ctx *gin.Context) {\n\treqDto := req.QueryPermissionRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := permissionRoleService.FindAllPermissionRoleWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PermissionRoleVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PermissionRoleVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, permissionRoleTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PermissionRoleRoute struct {\n}\n\nfunc (s *PermissionRoleRoute) InitPermissionRoleRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tpermissionRoleController := controller.PermissionRoleController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/permission-role/add\", permissionRoleController.AddPermissionRole)       //add\n\t\troute.POST(\"/api/permission-role/update\", permissionRoleController.UpdatePermissionRole) //update\n\t\troute.POST(\"/api/permission-role/delete\", permissionRoleController.DeletePermissionRole) //delete\n\t\troute.POST(\"/api/permission-role/query\", permissionRoleController.QueryPermissionRoles)  //query\n\t\troute.POST(\"/api/permission-role/list\", permissionRoleController.ListPermissionRoles)    //list\n\t}\n}\n"}]