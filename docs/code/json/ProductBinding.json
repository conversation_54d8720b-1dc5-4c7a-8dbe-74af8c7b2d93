[{"po": "package po\n\n// ProductBinding 产品绑定实体\ntype ProductBinding struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tVenueId     *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`     // ktvID\n\tName        *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`           // 产品绑定名称\n\tProductList *string `gorm:\"column:product_list;type:text\" json:\"productList\"`               // 产品列表\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n\tDescription *string `gorm:\"column:description;type:text\" json:\"description\"`               // 描述\n}\n\n// TableName 设置表名\nfunc (ProductBinding) TableName() string {\n\treturn \"product_binding\"\n}\n\nfunc (b ProductBinding) GetId() string {\n\treturn *b.Id\n}\n", "vo": "package vo\n\n// ProductBindingVO 产品绑定值对象\ntype ProductBindingVO struct {\n\tId          string `json:\"id\"`          // ID\n\tVenueId     string `json:\"venueId\"`     // ktvID\n\tName        string `json:\"name\"`        // 产品绑定名称\n\tProductList string `json:\"productList\"` // 产品列表\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n\tDescription string `json:\"description\"` // 描述\n}\n", "req_add": "package req\n\n// AddProductBindingReqDto 创建产品绑定请求DTO\ntype AddProductBindingReqDto struct {\n\tVenueId     *string `json:\"venueId\"`     // ktvID\n\tName        *string `json:\"name\"`        // 产品绑定名称\n\tProductList *string `json:\"productList\"` // 产品列表\n\tDescription *string `json:\"description\"` // 描述\n}\n", "req_update": "package req\n\ntype UpdateProductBindingReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tVenueId     *string `json:\"venueId\"`     // ktvID\n\tName        *string `json:\"name\"`        // 产品绑定名称\n\tProductList *string `json:\"productList\"` // 产品列表\n\tDescription *string `json:\"description\"` // 描述\n}\n", "req_delete": "package req\n\ntype DeleteProductBindingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryProductBindingReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tVenueId     *string `json:\"venueId\"`     // ktvID\n\tName        *string `json:\"name\"`        // 产品绑定名称\n\tProductList *string `json:\"productList\"` // 产品列表\n\tDescription *string `json:\"description\"` // 描述\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductBindingTransfer struct {\n}\n\nfunc (transfer *ProductBindingTransfer) PoToVo(po po.ProductBinding) vo.ProductBindingVO {\n\tvo := vo.ProductBindingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductBindingTransfer) VoToPo(vo vo.ProductBindingVO) po.ProductBinding {\n\tpo := po.ProductBinding{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductBindingService struct {\n}\n\nfunc (service *ProductBindingService) CreateProductBinding(logCtx *gin.Context, productBinding *po.ProductBinding) error {\n\treturn Save(productBinding)\n}\n\nfunc (service *ProductBindingService) UpdateProductBinding(logCtx *gin.Context, productBinding *po.ProductBinding) error {\n\treturn Update(productBinding)\n}\n\nfunc (service *ProductBindingService) DeleteProductBinding(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductBinding{Id: &id})\n}\n\nfunc (service *ProductBindingService) FindProductBindingById(logCtx *gin.Context, id string) (productBinding *po.ProductBinding, err error) {\n\tproductBinding = &po.ProductBinding{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productBinding).Error\n\treturn\n}\n\nfunc (service *ProductBindingService) FindAllProductBinding(logCtx *gin.Context, reqDto *req.QueryProductBindingReqDto) (list *[]po.ProductBinding, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductBinding{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductBinding{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductBindingService) FindAllProductBindingWithPagination(logCtx *gin.Context, reqDto *req.QueryProductBindingReqDto) (list *[]po.ProductBinding, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductBinding{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductBinding{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductBindingController struct{}\n\nvar (\n\tproductBindingService  = impl.ProductBindingService{}\n\tproductBindingTransfer = transfer.ProductBindingTransfer{}\n)\n\n// @Summary 添加产品绑定\n// @Description 添加产品绑定\n// @Tags 产品绑定\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductBindingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductBindingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-binding/add [post]\nfunc (controller *ProductBindingController) AddProductBinding(ctx *gin.Context) {\n\treqDto := req.AddProductBindingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductBinding := po.ProductBinding{}\n\tif reqDto.VenueId != nil {\n\t\tproductBinding.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproductBinding.Name = reqDto.Name\n\t}\n\tif reqDto.ProductList != nil {\n\t\tproductBinding.ProductList = reqDto.ProductList\n\t}\n\tif reqDto.Description != nil {\n\t\tproductBinding.Description = reqDto.Description\n\t}\n\n\terr = productBindingService.CreateProductBinding(ctx, &productBinding)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productBindingTransfer.PoToVo(productBinding))\n}\n\n// @Summary 更新产品绑定\n// @Description 更新产品绑定\n// @Tags 产品绑定\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductBindingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductBindingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-binding/update [post]\nfunc (controller *ProductBindingController) UpdateProductBinding(ctx *gin.Context) {\n\treqDto := req.UpdateProductBindingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tproductBinding, err := productBindingService.FindProductBindingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tproductBinding.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproductBinding.Name = reqDto.Name\n\t}\n\tif reqDto.ProductList != nil {\n\t\tproductBinding.ProductList = reqDto.ProductList\n\t}\n\tif reqDto.Description != nil {\n\t\tproductBinding.Description = reqDto.Description\n\t}\n\n\terr = productBindingService.UpdateProductBinding(ctx, productBinding)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productBindingTransfer.PoToVo(*productBinding))\n}\n\n// @Summary 删除产品绑定\n// @Description 删除产品绑定\n// @Tags 产品绑定\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductBindingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-binding/delete [post]\nfunc (controller *ProductBindingController) DeleteProductBinding(ctx *gin.Context) {\n\treqDto := req.DeleteProductBindingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = productBindingService.DeleteProductBinding(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品绑定\n// @Description 查询产品绑定\n// @Tags 产品绑定\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductBindingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductBindingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-binding/query [post]\nfunc (controller *ProductBindingController) QueryProductBindings(ctx *gin.Context) {\n\treqDto := req.QueryProductBindingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := productBindingService.FindAllProductBinding(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ProductBindingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productBindingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品绑定列表\n// @Description 查询产品绑定列表\n// @Tags 产品绑定\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductBindingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductBindingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-binding/list [post]\nfunc (a *ProductBindingController) ListProductBindings(ctx *gin.Context) {\n\treqDto := req.QueryProductBindingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := productBindingService.FindAllProductBindingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ProductBindingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductBindingVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productBindingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductBindingRoute struct {\n}\n\nfunc (s *ProductBindingRoute) InitProductBindingRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductBindingController := controller.ProductBindingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/product-binding/add\", productBindingController.AddProductBinding)       //add\n\t\troute.POST(\"/api/product-binding/update\", productBindingController.UpdateProductBinding) //update\n\t\troute.POST(\"/api/product-binding/delete\", productBindingController.DeleteProductBinding) //delete\n\t\troute.POST(\"/api/product-binding/query\", productBindingController.QueryProductBindings)  //query\n\t\troute.POST(\"/api/product-binding/list\", productBindingController.ListProductBindings)    //list\n\t}\n}\n"}]