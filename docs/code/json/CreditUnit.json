[{"po": "package po\n\n// CreditUnit 信用单位实体\ntype CreditUnit struct {\n\tId          *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName        *string  `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`               // 信用单位名称\n\tContactName *string  `gorm:\"column:contact_name;type:varchar(255);default:''\" json:\"contactName\"` // 联系人姓名\n\tCreditLimit *float32 `gorm:\"column:credit_limit;type:float;default:0\" json:\"creditLimit\"`         // 信用额度\n\tCtime       *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                       // 创建时间戳\n\tUtime       *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                       // 更新时间戳\n\tState       *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                       // 状态值\n\tVersion     *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                   // 版本号\n}\n\n// TableName 设置表名\nfunc (CreditUnit) TableName() string {\n\treturn \"credit_unit\"\n}\n\nfunc (c CreditUnit) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CreditUnitVO 信用单位值对象\ntype CreditUnitVO struct {\n\tId          string  `json:\"id\"`          // ID\n\tName        string  `json:\"name\"`        // 信用单位名称\n\tContactName string  `json:\"contactName\"` // 联系人姓名\n\tCreditLimit float32 `json:\"creditLimit\"` // 信用额度\n\tCtime       int64   `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64   `json:\"utime\"`       // 更新时间戳\n\tState       int     `json:\"state\"`       // 状态值\n\tVersion     int     `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddCreditUnitReqDto 创建信用单位请求DTO\ntype AddCreditUnitReqDto struct {\n\tName        *string  `json:\"name\"`        // 信用单位名称\n\tContactName *string  `json:\"contactName\"` // 联系人姓名\n\tCreditLimit *float32 `json:\"creditLimit\"` // 信用额度\n}\n", "req_update": "package req\n\ntype UpdateCreditUnitReqDto struct {\n\tId          *string  `json:\"id\"`          // ID\n\tName        *string  `json:\"name\"`        // 信用单位名称\n\tContactName *string  `json:\"contactName\"` // 联系人姓名\n\tCreditLimit *float32 `json:\"creditLimit\"` // 信用额度\n}\n", "req_delete": "package req\n\ntype DeleteCreditUnitReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCreditUnitReqDto struct {\n\tId          *string  `json:\"id\"`          // ID\n\tName        *string  `json:\"name\"`        // 信用单位名称\n\tContactName *string  `json:\"contactName\"` // 联系人姓名\n\tCreditLimit *float32 `json:\"creditLimit\"` // 信用额度\n\tPageNum     *int     `json:\"pageNum\"`     // 页码\n\tPageSize    *int     `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CreditUnitTransfer struct {\n}\n\nfunc (transfer *CreditUnitTransfer) PoToVo(po po.CreditUnit) vo.CreditUnitVO {\n\tvo := vo.CreditUnitVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CreditUnitTransfer) VoToPo(vo vo.CreditUnitVO) po.CreditUnit {\n\tpo := po.CreditUnit{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CreditUnitService struct {\n}\n\nfunc (service *CreditUnitService) CreateCreditUnit(logCtx *gin.Context, creditUnit *po.CreditUnit) error {\n\treturn Save(creditUnit)\n}\n\nfunc (service *CreditUnitService) UpdateCreditUnit(logCtx *gin.Context, creditUnit *po.CreditUnit) error {\n\treturn Update(creditUnit)\n}\n\nfunc (service *CreditUnitService) DeleteCreditUnit(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CreditUnit{Id: &id})\n}\n\nfunc (service *CreditUnitService) FindCreditUnitById(logCtx *gin.Context, id string) (creditUnit *po.CreditUnit, err error) {\n\tcreditUnit = &po.CreditUnit{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(creditUnit).Error\n\treturn\n}\n\nfunc (service *CreditUnitService) FindAllCreditUnit(logCtx *gin.Context, reqDto *req.QueryCreditUnitReqDto) (list *[]po.CreditUnit, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CreditUnit{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CreditUnit{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CreditUnitService) FindAllCreditUnitWithPagination(logCtx *gin.Context, reqDto *req.QueryCreditUnitReqDto) (list *[]po.CreditUnit, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CreditUnit{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CreditUnit{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CreditUnitController struct{}\n\nvar (\n\tcreditUnitService  = impl.CreditUnitService{}\n\tcreditUnitTransfer = transfer.CreditUnitTransfer{}\n)\n\n// @Summary 添加信用单位\n// @Description 添加信用单位\n// @Tags 信用单位\n// @Accept json\n// @Produce json\n// @Param body body req.AddCreditUnitReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CreditUnitVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditUnit/add [post]\nfunc (controller *CreditUnitController) AddCreditUnit(ctx *gin.Context) {\n\treqDto := req.AddCreditUnitReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcreditUnit := po.CreditUnit{}\n\tif reqDto.Name != nil {\n\t\tcreditUnit.Name = reqDto.Name\n\t}\n\tif reqDto.ContactName != nil {\n\t\tcreditUnit.ContactName = reqDto.ContactName\n\t}\n\tif reqDto.CreditLimit != nil {\n\t\tcreditUnit.CreditLimit = reqDto.CreditLimit\n\t}\n\n\terr = creditUnitService.CreateCreditUnit(ctx, &creditUnit)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, creditUnitTransfer.PoToVo(creditUnit))\n}\n\n// @Summary 更新信用单位\n// @Description 更新信用单位\n// @Tags 信用单位\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCreditUnitReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CreditUnitVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditUnit/update [post]\nfunc (controller *CreditUnitController) UpdateCreditUnit(ctx *gin.Context) {\n\treqDto := req.UpdateCreditUnitReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcreditUnit, err := creditUnitService.FindCreditUnitById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tcreditUnit.Name = reqDto.Name\n\t}\n\tif reqDto.ContactName != nil {\n\t\tcreditUnit.ContactName = reqDto.ContactName\n\t}\n\tif reqDto.CreditLimit != nil {\n\t\tcreditUnit.CreditLimit = reqDto.CreditLimit\n\t}\n\n\terr = creditUnitService.UpdateCreditUnit(ctx, creditUnit)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, creditUnitTransfer.PoToVo(*creditUnit))\n}\n\n// @Summary 删除信用单位\n// @Description 删除信用单位\n// @Tags 信用单位\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCreditUnitReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditUnit/delete [post]\nfunc (controller *CreditUnitController) DeleteCreditUnit(ctx *gin.Context) {\n\treqDto := req.DeleteCreditUnitReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = creditUnitService.DeleteCreditUnit(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询信用单位\n// @Description 查询信用单位\n// @Tags 信用单位\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCreditUnitReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CreditUnitVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditUnit/query [post]\nfunc (controller *CreditUnitController) QueryCreditUnits(ctx *gin.Context) {\n\treqDto := req.QueryCreditUnitReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := creditUnitService.FindAllCreditUnit(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CreditUnitVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, creditUnitTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询信用单位列表\n// @Description 查询信用单位列表\n// @Tags 信用单位\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCreditUnitReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CreditUnitVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditUnit/list [post]\nfunc (a *CreditUnitController) ListCreditUnits(ctx *gin.Context) {\n\treqDto := req.QueryCreditUnitReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := creditUnitService.FindAllCreditUnitWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CreditUnitVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CreditUnitVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, creditUnitTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CreditUnitRoute struct {\n}\n\nfunc (s *CreditUnitRoute) InitCreditUnitRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcreditUnitController := controller.CreditUnitController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/creditUnit/add\", creditUnitController.AddCreditUnit)       //add\n\t\troute.POST(\"/api/creditUnit/update\", creditUnitController.UpdateCreditUnit) //update\n\t\troute.POST(\"/api/creditUnit/delete\", creditUnitController.DeleteCreditUnit) //delete\n\t\troute.POST(\"/api/creditUnit/query\", creditUnitController.QueryCreditUnits)  //query\n\t\troute.POST(\"/api/creditUnit/list\", creditUnitController.ListCreditUnits)   //list\n\t}\n}\n"}]