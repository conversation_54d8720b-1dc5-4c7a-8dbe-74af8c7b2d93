[{"po": "package po\n\n// ProductStatisticsCategory 价格方案实体类\ntype ProductStatisticsCategory struct {\n\tId         *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tVenueId    *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`    // 所属门店ID\n\tName       *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`         // 统计分类名称\n\tProductIds *string `gorm:\"column:product_ids;type:text\" json:\"productIds\"`              // 绑定商品类型\n\tCtime      *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`               // 创建时间\n\tUtime      *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`               // 更新时间\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`               // 状态\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`           // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductStatisticsCategory) TableName() string {\n\treturn \"product_statistics_category\"\n}\n\nfunc (p ProductStatisticsCategory) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductStatisticsCategoryVO 价格方案值对象\ntype ProductStatisticsCategoryVO struct {\n\tId         string `json:\"id\"`          // ID\n\tVenueId    string `json:\"venueId\"`    // 所属门店ID\n\tName       string `json:\"name\"`        // 统计分类名称\n\tProductIds string `json:\"productIds\"`  // 绑定商品类型\n\tCtime      int64  `json:\"ctime\"`       // 创建时间\n\tUtime      int64  `json:\"utime\"`       // 更新时间\n\tState      int    `json:\"state\"`       // 状态\n\tVersion    int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddProductStatisticsCategoryReqDto 创建价格方案请求DTO\ntype AddProductStatisticsCategoryReqDto struct {\n\tVenueId    *string `json:\"venueId\"`    // 所属门店ID\n\tName       *string `json:\"name\"`        // 统计分类名称\n\tProductIds *string `json:\"productIds\"`  // 绑定商品类型\n}\n", "req_update": "package req\n\ntype UpdateProductStatisticsCategoryReqDto struct {\n\tId         *string `json:\"id\"`          // ID\n\tVenueId    *string `json:\"venueId\"`    // 所属门店ID\n\tName       *string `json:\"name\"`        // 统计分类名称\n\tProductIds *string `json:\"productIds\"`  // 绑定商品类型\n}\n", "req_delete": "package req\n\ntype DeleteProductStatisticsCategoryReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryProductStatisticsCategoryReqDto struct {\n\tId         *string `json:\"id\"`          // ID\n\tVenueId    *string `json:\"venueId\"`    // 所属门店ID\n\tName       *string `json:\"name\"`        // 统计分类名称\n\tProductIds *string `json:\"productIds\"`  // 绑定商品类型\n\tPageNum    *int    `json:\"pageNum\"`     // 页码\n\tPageSize   *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductStatisticsCategoryTransfer struct {\n}\n\nfunc (transfer *ProductStatisticsCategoryTransfer) PoToVo(po po.ProductStatisticsCategory) vo.ProductStatisticsCategoryVO {\n\tvo := vo.ProductStatisticsCategoryVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductStatisticsCategoryTransfer) VoToPo(vo vo.ProductStatisticsCategoryVO) po.ProductStatisticsCategory {\n\tpo := po.ProductStatisticsCategory{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductStatisticsCategoryService struct {\n}\n\nfunc (service *ProductStatisticsCategoryService) CreateProductStatisticsCategory(logCtx *gin.Context, productStatisticsCategory *po.ProductStatisticsCategory) error {\n\treturn Save(productStatisticsCategory)\n}\n\nfunc (service *ProductStatisticsCategoryService) UpdateProductStatisticsCategory(logCtx *gin.Context, productStatisticsCategory *po.ProductStatisticsCategory) error {\n\treturn Update(productStatisticsCategory)\n}\n\nfunc (service *ProductStatisticsCategoryService) DeleteProductStatisticsCategory(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductStatisticsCategory{Id: &id})\n}\n\nfunc (service *ProductStatisticsCategoryService) FindProductStatisticsCategoryById(logCtx *gin.Context, id string) (productStatisticsCategory *po.ProductStatisticsCategory, err error) {\n\tproductStatisticsCategory = &po.ProductStatisticsCategory{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productStatisticsCategory).Error\n\treturn\n}\n\nfunc (service *ProductStatisticsCategoryService) FindAllProductStatisticsCategory(logCtx *gin.Context, reqDto *req.QueryProductStatisticsCategoryReqDto) (list *[]po.ProductStatisticsCategory, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductStatisticsCategory{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.ProductIds != nil && *reqDto.ProductIds != \"\" {\n\t\tdb = db.Where(\"product_ids=?\", *reqDto.ProductIds)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductStatisticsCategory{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductStatisticsCategoryService) FindAllProductStatisticsCategoryWithPagination(logCtx *gin.Context, reqDto *req.QueryProductStatisticsCategoryReqDto) (list *[]po.ProductStatisticsCategory, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductStatisticsCategory{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.ProductIds != nil && *reqDto.ProductIds != \"\" {\n\t\tdb = db.Where(\"product_ids=?\", *reqDto.ProductIds)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductStatisticsCategory{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductStatisticsCategoryController struct{}\n\nvar (\n\tproductStatisticsCategoryService  = impl.ProductStatisticsCategoryService{}\n\tproductStatisticsCategoryTransfer = transfer.ProductStatisticsCategoryTransfer{}\n)\n\n// @Summary 添加价格方案\n// @Description 添加价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductStatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productStatisticsCategory/add [post]\nfunc (controller *ProductStatisticsCategoryController) AddProductStatisticsCategory(ctx *gin.Context) {\n\treqDto := req.AddProductStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductStatisticsCategory := po.ProductStatisticsCategory{}\n\tif reqDto.VenueId != nil {\n\t\tproductStatisticsCategory.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproductStatisticsCategory.Name = reqDto.Name\n\t}\n\tif reqDto.ProductIds != nil {\n\t\tproductStatisticsCategory.ProductIds = reqDto.ProductIds\n\t}\n\n\terr = productStatisticsCategoryService.CreateProductStatisticsCategory(ctx, &productStatisticsCategory)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productStatisticsCategoryTransfer.PoToVo(productStatisticsCategory))\n}\n\n// @Summary 更新价格方案\n// @Description 更新价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductStatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productStatisticsCategory/update [post]\nfunc (controller *ProductStatisticsCategoryController) UpdateProductStatisticsCategory(ctx *gin.Context) {\n\treqDto := req.UpdateProductStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tproductStatisticsCategory, err := productStatisticsCategoryService.FindProductStatisticsCategoryById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tproductStatisticsCategory.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproductStatisticsCategory.Name = reqDto.Name\n\t}\n\tif reqDto.ProductIds != nil {\n\t\tproductStatisticsCategory.ProductIds = reqDto.ProductIds\n\t}\n\n\terr = productStatisticsCategoryService.UpdateProductStatisticsCategory(ctx, productStatisticsCategory)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productStatisticsCategoryTransfer.PoToVo(*productStatisticsCategory))\n}\n\n// @Summary 删除价格方案\n// @Description 删除价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productStatisticsCategory/delete [post]\nfunc (controller *ProductStatisticsCategoryController) DeleteProductStatisticsCategory(ctx *gin.Context) {\n\treqDto := req.DeleteProductStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = productStatisticsCategoryService.DeleteProductStatisticsCategory(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询价格方案\n// @Description 查询价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductStatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productStatisticsCategory/query [post]\nfunc (controller *ProductStatisticsCategoryController) QueryProductStatisticsCategories(ctx *gin.Context) {\n\treqDto := req.QueryProductStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := productStatisticsCategoryService.FindAllProductStatisticsCategory(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ProductStatisticsCategoryVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productStatisticsCategoryTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询价格方案列表\n// @Description 查询价格方案列表\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductStatisticsCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductStatisticsCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productStatisticsCategory/list [post]\nfunc (a *ProductStatisticsCategoryController) ListProductStatisticsCategories(ctx *gin.Context) {\n\treqDto := req.QueryProductStatisticsCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := productStatisticsCategoryService.FindAllProductStatisticsCategoryWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ProductStatisticsCategoryVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductStatisticsCategoryVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productStatisticsCategoryTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductStatisticsCategoryRoute struct {\n}\n\nfunc (s *ProductStatisticsCategoryRoute) InitProductStatisticsCategoryRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductStatisticsCategoryController := controller.ProductStatisticsCategoryController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productStatisticsCategory/add\", productStatisticsCategoryController.AddProductStatisticsCategory)       //add\n\t\troute.POST(\"/api/productStatisticsCategory/update\", productStatisticsCategoryController.UpdateProductStatisticsCategory)   //update\n\t\troute.POST(\"/api/productStatisticsCategory/delete\", productStatisticsCategoryController.DeleteProductStatisticsCategory)   //delete\n\t\troute.POST(\"/api/productStatisticsCategory/query\", productStatisticsCategoryController.QueryProductStatisticsCategories)  //query\n\t\troute.POST(\"/api/productStatisticsCategory/list\", productStatisticsCategoryController.ListProductStatisticsCategories)    //list\n\t}\n}\n"}]