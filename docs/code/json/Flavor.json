[{"po": "package po\n\n// Flavor 口味实体\ntype Flavor struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tVenueId     *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`     // ktvID\n\tName        *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`            // 口味名称\n\tSortNum     *int    `gorm:\"column:sort_num;type:int;default:0\" json:\"sortNum\"`              // 排序号\n\tDescription *string `gorm:\"column:description;type:varchar(512);default:''\" json:\"description\"` // 描述\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (Flavor) TableName() string {\n\treturn \"flavor\"\n}\n\nfunc (f Flavor) GetId() string {\n\treturn *f.Id\n}\n", "vo": "package vo\n\n// FlavorVO 口味信息值对象\ntype FlavorVO struct {\n\tId          string `json:\"id\"`          // ID\n\tVenueId     string `json:\"venueId\"`     // ktvID\n\tName        string `json:\"name\"`        // 口味名称\n\tSortNum     int    `json:\"sortNum\"`     // 排序号\n\tDescription string `json:\"description\"` // 描述\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddFlavorReqDto 创建口味请求DTO\ntype AddFlavorReqDto struct {\n\tVenueId     *string `json:\"venueId\"`     // ktvID\n\tName        *string `json:\"name\"`        // 口味名称\n\tSortNum     *int    `json:\"sortNum\"`     // 排序号\n\tDescription *string `json:\"description\"` // 描述\n}\n", "req_update": "package req\n\ntype UpdateFlavorReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tVenueId     *string `json:\"venueId\"`     // ktvID\n\tName        *string `json:\"name\"`        // 口味名称\n\tSortNum     *int    `json:\"sortNum\"`     // 排序号\n\tDescription *string `json:\"description\"` // 描述\n}\n", "req_delete": "package req\n\ntype DeleteFlavorReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryFlavorReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tVenueId     *string `json:\"venueId\"`     // ktvID\n\tName        *string `json:\"name\"`        // 口味名称\n\tSortNum     *int    `json:\"sortNum\"`     // 排序号\n\tDescription *string `json:\"description\"` // 描述\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype FlavorTransfer struct {\n}\n\nfunc (transfer *FlavorTransfer) PoToVo(po po.Flavor) vo.FlavorVO {\n\tvo := vo.FlavorVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *FlavorTransfer) VoToPo(vo vo.FlavorVO) po.Flavor {\n\tpo := po.Flavor{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype FlavorService struct {\n}\n\nfunc (service *FlavorService) CreateFlavor(logCtx *gin.Context, flavor *po.Flavor) error {\n\treturn Save(flavor)\n}\n\nfunc (service *FlavorService) UpdateFlavor(logCtx *gin.Context, flavor *po.Flavor) error {\n\treturn Update(flavor)\n}\n\nfunc (service *FlavorService) DeleteFlavor(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Flavor{Id: &id})\n}\n\nfunc (service *FlavorService) FindFlavorById(logCtx *gin.Context, id string) (flavor *po.Flavor, err error) {\n\tflavor = &po.Flavor{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(flavor).Error\n\treturn\n}\n\nfunc (service *FlavorService) FindAllFlavor(logCtx *gin.Context, reqDto *req.QueryFlavorReqDto) (list *[]po.Flavor, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Flavor{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\tdb = db.Order(\"sort_num asc, ctime desc\")\n\tlist = &[]po.Flavor{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *FlavorService) FindAllFlavorWithPagination(logCtx *gin.Context, reqDto *req.QueryFlavorReqDto) (list *[]po.Flavor, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Flavor{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Flavor{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"sort_num asc, ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype FlavorController struct{}\n\nvar (\n\tflavorService  = impl.FlavorService{}\n\tflavorTransfer = transfer.FlavorTransfer{}\n)\n\n// @Summary 添加口味\n// @Description 添加口味\n// @Tags 口味\n// @Accept json\n// @Produce json\n// @Param body body req.AddFlavorReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.FlavorVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/flavor/add [post]\nfunc (controller *FlavorController) AddFlavor(ctx *gin.Context) {\n\treqDto := req.AddFlavorReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tflavor := po.Flavor{}\n\tif reqDto.VenueId != nil {\n\t\tflavor.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tflavor.Name = reqDto.Name\n\t}\n\tif reqDto.SortNum != nil {\n\t\tflavor.SortNum = reqDto.SortNum\n\t}\n\tif reqDto.Description != nil {\n\t\tflavor.Description = reqDto.Description\n\t}\n\n\terr = flavorService.CreateFlavor(ctx, &flavor)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, flavorTransfer.PoToVo(flavor))\n}\n\n// @Summary 更新口味\n// @Description 更新口味\n// @Tags 口味\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateFlavorReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.FlavorVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/flavor/update [post]\nfunc (controller *FlavorController) UpdateFlavor(ctx *gin.Context) {\n\treqDto := req.UpdateFlavorReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tflavor, err := flavorService.FindFlavorById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tflavor.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tflavor.Name = reqDto.Name\n\t}\n\tif reqDto.SortNum != nil {\n\t\tflavor.SortNum = reqDto.SortNum\n\t}\n\tif reqDto.Description != nil {\n\t\tflavor.Description = reqDto.Description\n\t}\n\n\terr = flavorService.UpdateFlavor(ctx, flavor)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, flavorTransfer.PoToVo(*flavor))\n}\n\n// @Summary 删除口味\n// @Description 删除口味\n// @Tags 口味\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteFlavorReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/flavor/delete [post]\nfunc (controller *FlavorController) DeleteFlavor(ctx *gin.Context) {\n\treqDto := req.DeleteFlavorReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = flavorService.DeleteFlavor(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询口味\n// @Description 查询口味\n// @Tags 口味\n// @Accept json\n// @Produce json\n// @Param body body req.QueryFlavorReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.FlavorVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/flavor/query [post]\nfunc (controller *FlavorController) QueryFlavors(ctx *gin.Context) {\n\treqDto := req.QueryFlavorReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := flavorService.FindAllFlavor(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.FlavorVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, flavorTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询口味列表\n// @Description 查询口味列表\n// @Tags 口味\n// @Accept json\n// @Produce json\n// @Param body body req.QueryFlavorReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.FlavorVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/flavor/list [post]\nfunc (a *FlavorController) ListFlavors(ctx *gin.Context) {\n\treqDto := req.QueryFlavorReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := flavorService.FindAllFlavorWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.FlavorVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.FlavorVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, flavorTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype FlavorRoute struct {\n}\n\nfunc (s *FlavorRoute) InitFlavorRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tflavorController := controller.FlavorController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/flavor/add\", flavorController.AddFlavor)       //add\n\t\troute.POST(\"/api/flavor/update\", flavorController.UpdateFlavor) //update\n\t\troute.POST(\"/api/flavor/delete\", flavorController.DeleteFlavor) //delete\n\t\troute.POST(\"/api/flavor/query\", flavorController.QueryFlavors)  //query\n\t\troute.POST(\"/api/flavor/list\", flavorController.ListFlavors)    //list\n\t}\n}\n"}]