[{"po": "package po\n\n// PrepaidCardType 预付卡类型实体\ntype PrepaidCardType struct {\n\tId                    *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // 唯一id\n\tName                  *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                      // 名称\n\tSaleAmount            *int64  `gorm:\"column:sale_amount;type:bigint;default:0\" json:\"saleAmount\"`               // 销售金额\n\tTotalTimes            *int    `gorm:\"column:total_times;type:int;default:0\" json:\"totalTimes\"`                  // 总次数\n\tValidityPeriod        *string `gorm:\"column:validity_period;type:varchar(64);default:''\" json:\"validityPeriod\"`  // 有效期\n\tApplicableProductTypes *string `gorm:\"column:applicable_product_types;type:varchar(255)\" json:\"applicableProductTypes\"` // 适用产品类型\n\tCtime                 *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                         // 创建时间\n\tUtime                 *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                         // 更新时间\n\tState                 *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态\n\tVersion               *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本号\n}\n\n// TableName 设置表名\nfunc (PrepaidCardType) TableName() string {\n\treturn \"prepaid_card_type\"\n}\n\nfunc (p PrepaidCardType) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PrepaidCardTypeVO 预付卡类型值对象\ntype PrepaidCardTypeVO struct {\n\tId                    string `json:\"id\"`                    // 唯一id\n\tName                  string `json:\"name\"`                  // 名称\n\tSaleAmount            int64  `json:\"saleAmount\"`            // 销售金额\n\tTotalTimes            int    `json:\"totalTimes\"`            // 总次数\n\tValidityPeriod        string `json:\"validityPeriod\"`        // 有效期\n\tApplicableProductTypes string `json:\"applicableProductTypes\"` // 适用产品类型\n\tCtime                 int64  `json:\"ctime\"`                 // 创建时间\n\tUtime                 int64  `json:\"utime\"`                 // 更新时间\n\tState                 int    `json:\"state\"`                 // 状态\n\tVersion               int    `json:\"version\"`               // 版本号\n}\n", "req_add": "package req\n\n// AddPrepaidCardTypeReqDto 创建预付卡类型请求DTO\ntype AddPrepaidCardTypeReqDto struct {\n\tName                  *string `json:\"name\"`                  // 名称\n\tSaleAmount            *int64  `json:\"saleAmount\"`            // 销售金额\n\tTotalTimes            *int    `json:\"totalTimes\"`            // 总次数\n\tValidityPeriod        *string `json:\"validityPeriod\"`        // 有效期\n\tApplicableProductTypes *string `json:\"applicableProductTypes\"` // 适用产品类型\n}\n", "req_update": "package req\n\ntype UpdatePrepaidCardTypeReqDto struct {\n\tId                    *string `json:\"id\"`                    // 唯一id\n\tName                  *string `json:\"name\"`                  // 名称\n\tSaleAmount            *int64  `json:\"saleAmount\"`            // 销售金额\n\tTotalTimes            *int    `json:\"totalTimes\"`            // 总次数\n\tValidityPeriod        *string `json:\"validityPeriod\"`        // 有效期\n\tApplicableProductTypes *string `json:\"applicableProductTypes\"` // 适用产品类型\n}\n", "req_delete": "package req\n\ntype DeletePrepaidCardTypeReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryPrepaidCardTypeReqDto struct {\n\tId                    *string `json:\"id\"`                    // 唯一id\n\tName                  *string `json:\"name\"`                  // 名称\n\tSaleAmount            *int64  `json:\"saleAmount\"`            // 销售金额\n\tTotalTimes            *int    `json:\"totalTimes\"`            // 总次数\n\tValidityPeriod        *string `json:\"validityPeriod\"`        // 有效期\n\tApplicableProductTypes *string `json:\"applicableProductTypes\"` // 适用产品类型\n\tPageNum               *int    `json:\"pageNum\"`               // 页码\n\tPageSize              *int    `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PrepaidCardTypeTransfer struct {\n}\n\nfunc (transfer *PrepaidCardTypeTransfer) PoToVo(po po.PrepaidCardType) vo.PrepaidCardTypeVO {\n\tvo := vo.PrepaidCardTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PrepaidCardTypeTransfer) VoToPo(vo vo.PrepaidCardTypeVO) po.PrepaidCardType {\n\tpo := po.PrepaidCardType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrepaidCardTypeService struct {\n}\n\nfunc (service *PrepaidCardTypeService) CreatePrepaidCardType(logCtx *gin.Context, prepaidCardType *po.PrepaidCardType) error {\n\treturn Save(prepaidCardType)\n}\n\nfunc (service *PrepaidCardTypeService) UpdatePrepaidCardType(logCtx *gin.Context, prepaidCardType *po.PrepaidCardType) error {\n\treturn Update(prepaidCardType)\n}\n\nfunc (service *PrepaidCardTypeService) DeletePrepaidCardType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PrepaidCardType{Id: &id})\n}\n\nfunc (service *PrepaidCardTypeService) FindPrepaidCardTypeById(logCtx *gin.Context, id string) (prepaidCardType *po.PrepaidCardType, err error) {\n\tprepaidCardType = &po.PrepaidCardType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(prepaidCardType).Error\n\treturn\n}\n\nfunc (service *PrepaidCardTypeService) FindAllPrepaidCardType(logCtx *gin.Context, reqDto *req.QueryPrepaidCardTypeReqDto) (list *[]po.PrepaidCardType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PrepaidCardType{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.SaleAmount != nil {\n\t\tdb = db.Where(\"sale_amount=?\", *reqDto.SaleAmount)\n\t}\n\tif reqDto.TotalTimes != nil {\n\t\tdb = db.Where(\"total_times=?\", *reqDto.TotalTimes)\n\t}\n\tif reqDto.ValidityPeriod != nil && *reqDto.ValidityPeriod != \"\" {\n\t\tdb = db.Where(\"validity_period=?\", *reqDto.ValidityPeriod)\n\t}\n\tif reqDto.ApplicableProductTypes != nil && *reqDto.ApplicableProductTypes != \"\" {\n\t\tdb = db.Where(\"applicable_product_types=?\", *reqDto.ApplicableProductTypes)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PrepaidCardType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PrepaidCardTypeService) FindAllPrepaidCardTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryPrepaidCardTypeReqDto) (list *[]po.PrepaidCardType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PrepaidCardType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.SaleAmount != nil {\n\t\tdb = db.Where(\"sale_amount=?\", *reqDto.SaleAmount)\n\t}\n\tif reqDto.TotalTimes != nil {\n\t\tdb = db.Where(\"total_times=?\", *reqDto.TotalTimes)\n\t}\n\tif reqDto.ValidityPeriod != nil && *reqDto.ValidityPeriod != \"\" {\n\t\tdb = db.Where(\"validity_period=?\", *reqDto.ValidityPeriod)\n\t}\n\tif reqDto.ApplicableProductTypes != nil && *reqDto.ApplicableProductTypes != \"\" {\n\t\tdb = db.Where(\"applicable_product_types=?\", *reqDto.ApplicableProductTypes)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PrepaidCardType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrepaidCardTypeController struct{}\n\nvar (\n\tprepaidCardTypeService  = impl.PrepaidCardTypeService{}\n\tprepaidCardTypeTransfer = transfer.PrepaidCardTypeTransfer{}\n)\n\n// @Summary 添加预付卡类型\n// @Description 添加预付卡类型\n// @Tags 预付卡类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddPrepaidCardTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PrepaidCardTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCardType/add [post]\nfunc (controller *PrepaidCardTypeController) AddPrepaidCardType(ctx *gin.Context) {\n\treqDto := req.AddPrepaidCardTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tprepaidCardType := po.PrepaidCardType{}\n\tif reqDto.Name != nil {\n\t\tprepaidCardType.Name = reqDto.Name\n\t}\n\tif reqDto.SaleAmount != nil {\n\t\tprepaidCardType.SaleAmount = reqDto.SaleAmount\n\t}\n\tif reqDto.TotalTimes != nil {\n\t\tprepaidCardType.TotalTimes = reqDto.TotalTimes\n\t}\n\tif reqDto.ValidityPeriod != nil {\n\t\tprepaidCardType.ValidityPeriod = reqDto.ValidityPeriod\n\t}\n\tif reqDto.ApplicableProductTypes != nil {\n\t\tprepaidCardType.ApplicableProductTypes = reqDto.ApplicableProductTypes\n\t}\n\n\terr = prepaidCardTypeService.CreatePrepaidCardType(ctx, &prepaidCardType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, prepaidCardTypeTransfer.PoToVo(prepaidCardType))\n}\n\n// @Summary 更新预付卡类型\n// @Description 更新预付卡类型\n// @Tags 预付卡类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePrepaidCardTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PrepaidCardTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCardType/update [post]\nfunc (controller *PrepaidCardTypeController) UpdatePrepaidCardType(ctx *gin.Context) {\n\treqDto := req.UpdatePrepaidCardTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tprepaidCardType, err := prepaidCardTypeService.FindPrepaidCardTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tprepaidCardType.Name = reqDto.Name\n\t}\n\tif reqDto.SaleAmount != nil {\n\t\tprepaidCardType.SaleAmount = reqDto.SaleAmount\n\t}\n\tif reqDto.TotalTimes != nil {\n\t\tprepaidCardType.TotalTimes = reqDto.TotalTimes\n\t}\n\tif reqDto.ValidityPeriod != nil {\n\t\tprepaidCardType.ValidityPeriod = reqDto.ValidityPeriod\n\t}\n\tif reqDto.ApplicableProductTypes != nil {\n\t\tprepaidCardType.ApplicableProductTypes = reqDto.ApplicableProductTypes\n\t}\n\n\terr = prepaidCardTypeService.UpdatePrepaidCardType(ctx, prepaidCardType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, prepaidCardTypeTransfer.PoToVo(*prepaidCardType))\n}\n\n// @Summary 删除预付卡类型\n// @Description 删除预付卡类型\n// @Tags 预付卡类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePrepaidCardTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCardType/delete [post]\nfunc (controller *PrepaidCardTypeController) DeletePrepaidCardType(ctx *gin.Context) {\n\treqDto := req.DeletePrepaidCardTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = prepaidCardTypeService.DeletePrepaidCardType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询预付卡类型\n// @Description 查询预付卡类型\n// @Tags 预付卡类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPrepaidCardTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PrepaidCardTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCardType/query [post]\nfunc (controller *PrepaidCardTypeController) QueryPrepaidCardTypes(ctx *gin.Context) {\n\treqDto := req.QueryPrepaidCardTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := prepaidCardTypeService.FindAllPrepaidCardType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PrepaidCardTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, prepaidCardTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询预付卡类型列表\n// @Description 查询预付卡类型列表\n// @Tags 预付卡类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPrepaidCardTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PrepaidCardTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCardType/list [post]\nfunc (a *PrepaidCardTypeController) ListPrepaidCardTypes(ctx *gin.Context) {\n\treqDto := req.QueryPrepaidCardTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := prepaidCardTypeService.FindAllPrepaidCardTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PrepaidCardTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PrepaidCardTypeVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, prepaidCardTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrepaidCardTypeRoute struct {\n}\n\nfunc (s *PrepaidCardTypeRoute) InitPrepaidCardTypeRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tprepaidCardTypeController := controller.PrepaidCardTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/prepaidCardType/add\", prepaidCardTypeController.AddPrepaidCardType)       //add\n\t\troute.POST(\"/api/prepaidCardType/update\", prepaidCardTypeController.UpdatePrepaidCardType)   //update\n\t\troute.POST(\"/api/prepaidCardType/delete\", prepaidCardTypeController.DeletePrepaidCardType)   //delete\n\t\troute.POST(\"/api/prepaidCardType/query\", prepaidCardTypeController.QueryPrepaidCardTypes)    //query\n\t\troute.POST(\"/api/prepaidCardType/list\", prepaidCardTypeController.ListPrepaidCardTypes)     //list\n\t}\n}\n"}]