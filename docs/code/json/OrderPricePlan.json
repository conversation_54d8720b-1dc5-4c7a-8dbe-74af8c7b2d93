[{"po": "package po\n\n// OrderPricePlan 价格方案实体类\ntype OrderPricePlan struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tSessionId           *string `gorm:\"column:session_id;type:varchar(64);default:''\" json:\"sessionId\"`           // 场次ID\n\tOrderNo             *string `gorm:\"column:order_no;type:varchar(64);default:''\" json:\"orderNo\"`               // 订单ID\n\tPricePlanId         *string `gorm:\"column:price_plan_id;type:varchar(64);default:''\" json:\"pricePlanId\"`     // 方案id\n\tPricePlanName       *string `gorm:\"column:price_plan_name;type:varchar(128);default:''\" json:\"pricePlanName\"` // 价格方案名称\n\tConsumptionMode     *string `gorm:\"column:consumption_mode;type:varchar(32);default:''\" json:\"consumptionMode\"` // 消费模式\n\tSelectedAreaId      *string `gorm:\"column:selected_area_id;type:varchar(64);default:''\" json:\"selectedAreaId\"`  // 选择的计费方式-套餐区域id\n\tSelectedRoomTypeId  *string `gorm:\"column:selected_room_type_id;type:varchar(64);default:''\" json:\"selectedRoomTypeId\"` // 选择的计费方式-房间类型id\n\tBuyMinute           *int    `gorm:\"column:buy_minute;type:int;default:0\" json:\"buyMinute\"`                   // 买钟时长\n\tTimeChargeType      *string `gorm:\"column:time_charge_type;type:varchar(32);default:''\" json:\"timeChargeType\"` // 买钟价格类型\n\tTimeChargeMode      *string `gorm:\"column:time_charge_mode;type:varchar(32);default:''\" json:\"timeChargeMode\"` // 买钟类型\n\tMinimumCharge       *int64  `gorm:\"column:minimum_charge;type:bigint;default:0\" json:\"minimumCharge\"`       // 最低消费金额\n\tCtime               *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                       // 创建时间\n\tUtime               *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                       // 更新时间\n\tState               *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态\n\tVersion             *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本\n}\n\n// TableName 设置表名\nfunc (OrderPricePlan) TableName() string {\n\treturn \"order_price_plan\"\n}\n\nfunc (o OrderPricePlan) GetId() string {\n\treturn *o.Id\n}\n", "vo": "package vo\n\n// OrderPricePlanVO 价格方案值对象\ntype OrderPricePlanVO struct {\n\tId                 string `json:\"id\"`                 // ID\n\tSessionId          string `json:\"sessionId\"`          // 场次ID\n\tOrderNo            string `json:\"orderNo\"`            // 订单ID\n\tPricePlanId        string `json:\"pricePlanId\"`        // 方案id\n\tPricePlanName      string `json:\"pricePlanName\"`      // 价格方案名称\n\tConsumptionMode    string `json:\"consumptionMode\"`    // 消费模式\n\tSelectedAreaId     string `json:\"selectedAreaId\"`     // 选择的计费方式-套餐区域id\n\tSelectedRoomTypeId string `json:\"selectedRoomTypeId\"` // 选择的计费方式-房间类型id\n\tBuyMinute          int    `json:\"buyMinute\"`          // 买钟时长\n\tTimeChargeType     string `json:\"timeChargeType\"`     // 买钟价格类型\n\tTimeChargeMode     string `json:\"timeChargeMode\"`     // 买钟类型\n\tMinimumCharge      int64  `json:\"minimumCharge\"`      // 最低消费金额\n\tCtime              int64  `json:\"ctime\"`              // 创建时间\n\tUtime              int64  `json:\"utime\"`              // 更新时间\n\tState              int    `json:\"state\"`              // 状态\n\tVersion            int    `json:\"version\"`            // 版本\n}\n", "req_add": "package req\n\n// AddOrderPricePlanReqDto 创建价格方案请求DTO\ntype AddOrderPricePlanReqDto struct {\n\tSessionId          *string `json:\"sessionId\"`          // 场次ID\n\tOrderNo            *string `json:\"orderNo\"`            // 订单ID\n\tPricePlanId        *string `json:\"pricePlanId\"`        // 方案id\n\tPricePlanName      *string `json:\"pricePlanName\"`      // 价格方案名称\n\tConsumptionMode    *string `json:\"consumptionMode\"`    // 消费模式\n\tSelectedAreaId     *string `json:\"selectedAreaId\"`     // 选择的计费方式-套餐区域id\n\tSelectedRoomTypeId *string `json:\"selectedRoomTypeId\"` // 选择的计费方式-房间类型id\n\tBuyMinute          *int    `json:\"buyMinute\"`          // 买钟时长\n\tTimeChargeType     *string `json:\"timeChargeType\"`     // 买钟价格类型\n\tTimeChargeMode     *string `json:\"timeChargeMode\"`     // 买钟类型\n\tMinimumCharge      *int64  `json:\"minimumCharge\"`      // 最低消费金额\n}\n", "req_update": "package req\n\n// UpdateOrderPricePlanReqDto 更新价格方案请求DTO\ntype UpdateOrderPricePlanReqDto struct {\n\tId                 *string `json:\"id\"`                 // ID\n\tSessionId          *string `json:\"sessionId\"`          // 场次ID\n\tOrderNo            *string `json:\"orderNo\"`            // 订单ID\n\tPricePlanId        *string `json:\"pricePlanId\"`        // 方案id\n\tPricePlanName      *string `json:\"pricePlanName\"`      // 价格方案名称\n\tConsumptionMode    *string `json:\"consumptionMode\"`    // 消费模式\n\tSelectedAreaId     *string `json:\"selectedAreaId\"`     // 选择的计费方式-套餐区域id\n\tSelectedRoomTypeId *string `json:\"selectedRoomTypeId\"` // 选择的计费方式-房间类型id\n\tBuyMinute          *int    `json:\"buyMinute\"`          // 买钟时长\n\tTimeChargeType     *string `json:\"timeChargeType\"`     // 买钟价格类型\n\tTimeChargeMode     *string `json:\"timeChargeMode\"`     // 买钟类型\n\tMinimumCharge      *int64  `json:\"minimumCharge\"`      // 最低消费金额\n}\n", "req_delete": "package req\n\n// DeleteOrderPricePlanReqDto 删除价格方案请求DTO\ntype DeleteOrderPricePlanReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryOrderPricePlanReqDto 查询价格方案请求DTO\ntype QueryOrderPricePlanReqDto struct {\n\tId                 *string `json:\"id\"`                 // ID\n\tSessionId          *string `json:\"sessionId\"`          // 场次ID\n\tOrderNo            *string `json:\"orderNo\"`            // 订单ID\n\tPricePlanId        *string `json:\"pricePlanId\"`        // 方案id\n\tPricePlanName      *string `json:\"pricePlanName\"`      // 价格方案名称\n\tConsumptionMode    *string `json:\"consumptionMode\"`    // 消费模式\n\tSelectedAreaId     *string `json:\"selectedAreaId\"`     // 选择的计费方式-套餐区域id\n\tSelectedRoomTypeId *string `json:\"selectedRoomTypeId\"` // 选择的计费方式-房间类型id\n\tTimeChargeType     *string `json:\"timeChargeType\"`     // 买钟价格类型\n\tTimeChargeMode     *string `json:\"timeChargeMode\"`     // 买钟类型\n\tPageNum            *int    `json:\"pageNum\"`            // 页码\n\tPageSize           *int    `json:\"pageSize\"`           // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype OrderPricePlanTransfer struct {\n}\n\nfunc (transfer *OrderPricePlanTransfer) PoToVo(po po.OrderPricePlan) vo.OrderPricePlanVO {\n\tvo := vo.OrderPricePlanVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *OrderPricePlanTransfer) VoToPo(vo vo.OrderPricePlanVO) po.OrderPricePlan {\n\tpo := po.OrderPricePlan{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderPricePlanService struct {\n}\n\nfunc (service *OrderPricePlanService) CreateOrderPricePlan(logCtx *gin.Context, orderPricePlan *po.OrderPricePlan) error {\n\treturn Save(orderPricePlan)\n}\n\nfunc (service *OrderPricePlanService) UpdateOrderPricePlan(logCtx *gin.Context, orderPricePlan *po.OrderPricePlan) error {\n\treturn Update(orderPricePlan)\n}\n\nfunc (service *OrderPricePlanService) DeleteOrderPricePlan(logCtx *gin.Context, id string) error {\n\treturn Delete(po.OrderPricePlan{Id: &id})\n}\n\nfunc (service *OrderPricePlanService) FindOrderPricePlanById(logCtx *gin.Context, id string) (orderPricePlan *po.OrderPricePlan, err error) {\n\torderPricePlan = &po.OrderPricePlan{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(orderPricePlan).Error\n\treturn\n}\n\nfunc (service *OrderPricePlanService) FindAllOrderPricePlan(logCtx *gin.Context, reqDto *req.QueryOrderPricePlanReqDto) (list *[]po.OrderPricePlan, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OrderPricePlan{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.SessionId != nil && *reqDto.SessionId != \"\" {\n\t\tdb = db.Where(\"session_id=?\", *reqDto.SessionId)\n\t}\n\tif reqDto.OrderNo != nil && *reqDto.OrderNo != \"\" {\n\t\tdb = db.Where(\"order_no=?\", *reqDto.OrderNo)\n\t}\n\tif reqDto.PricePlanId != nil && *reqDto.PricePlanId != \"\" {\n\t\tdb = db.Where(\"price_plan_id=?\", *reqDto.PricePlanId)\n\t}\n\tif reqDto.PricePlanName != nil && *reqDto.PricePlanName != \"\" {\n\t\tdb = db.Where(\"price_plan_name LIKE ?\", \"%\"+*reqDto.PricePlanName+\"%\")\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.SelectedAreaId != nil && *reqDto.SelectedAreaId != \"\" {\n\t\tdb = db.Where(\"selected_area_id=?\", *reqDto.SelectedAreaId)\n\t}\n\tif reqDto.SelectedRoomTypeId != nil && *reqDto.SelectedRoomTypeId != \"\" {\n\t\tdb = db.Where(\"selected_room_type_id=?\", *reqDto.SelectedRoomTypeId)\n\t}\n\tif reqDto.TimeChargeType != nil && *reqDto.TimeChargeType != \"\" {\n\t\tdb = db.Where(\"time_charge_type=?\", *reqDto.TimeChargeType)\n\t}\n\tif reqDto.TimeChargeMode != nil && *reqDto.TimeChargeMode != \"\" {\n\t\tdb = db.Where(\"time_charge_mode=?\", *reqDto.TimeChargeMode)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.OrderPricePlan{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *OrderPricePlanService) FindAllOrderPricePlanWithPagination(logCtx *gin.Context, reqDto *req.QueryOrderPricePlanReqDto) (list *[]po.OrderPricePlan, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OrderPricePlan{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.SessionId != nil && *reqDto.SessionId != \"\" {\n\t\tdb = db.Where(\"session_id=?\", *reqDto.SessionId)\n\t}\n\tif reqDto.OrderNo != nil && *reqDto.OrderNo != \"\" {\n\t\tdb = db.Where(\"order_no=?\", *reqDto.OrderNo)\n\t}\n\tif reqDto.PricePlanId != nil && *reqDto.PricePlanId != \"\" {\n\t\tdb = db.Where(\"price_plan_id=?\", *reqDto.PricePlanId)\n\t}\n\tif reqDto.PricePlanName != nil && *reqDto.PricePlanName != \"\" {\n\t\tdb = db.Where(\"price_plan_name LIKE ?\", \"%\"+*reqDto.PricePlanName+\"%\")\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.SelectedAreaId != nil && *reqDto.SelectedAreaId != \"\" {\n\t\tdb = db.Where(\"selected_area_id=?\", *reqDto.SelectedAreaId)\n\t}\n\tif reqDto.SelectedRoomTypeId != nil && *reqDto.SelectedRoomTypeId != \"\" {\n\t\tdb = db.Where(\"selected_room_type_id=?\", *reqDto.SelectedRoomTypeId)\n\t}\n\tif reqDto.TimeChargeType != nil && *reqDto.TimeChargeType != \"\" {\n\t\tdb = db.Where(\"time_charge_type=?\", *reqDto.TimeChargeType)\n\t}\n\tif reqDto.TimeChargeMode != nil && *reqDto.TimeChargeMode != \"\" {\n\t\tdb = db.Where(\"time_charge_mode=?\", *reqDto.TimeChargeMode)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.OrderPricePlan{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderPricePlanController struct{}\n\nvar (\n\torderPricePlanService  = impl.OrderPricePlanService{}\n\torderPricePlanTransfer = transfer.OrderPricePlanTransfer{}\n)\n\n// @Summary 添加价格方案\n// @Description 添加价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.AddOrderPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderPricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderPricePlan/add [post]\nfunc (controller *OrderPricePlanController) AddOrderPricePlan(ctx *gin.Context) {\n\treqDto := req.AddOrderPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\torderPricePlan := po.OrderPricePlan{}\n\tif reqDto.SessionId != nil {\n\t\torderPricePlan.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.OrderNo != nil {\n\t\torderPricePlan.OrderNo = reqDto.OrderNo\n\t}\n\tif reqDto.PricePlanId != nil {\n\t\torderPricePlan.PricePlanId = reqDto.PricePlanId\n\t}\n\tif reqDto.PricePlanName != nil {\n\t\torderPricePlan.PricePlanName = reqDto.PricePlanName\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\torderPricePlan.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.SelectedAreaId != nil {\n\t\torderPricePlan.SelectedAreaId = reqDto.SelectedAreaId\n\t}\n\tif reqDto.SelectedRoomTypeId != nil {\n\t\torderPricePlan.SelectedRoomTypeId = reqDto.SelectedRoomTypeId\n\t}\n\tif reqDto.BuyMinute != nil {\n\t\torderPricePlan.BuyMinute = reqDto.BuyMinute\n\t}\n\tif reqDto.TimeChargeType != nil {\n\t\torderPricePlan.TimeChargeType = reqDto.TimeChargeType\n\t}\n\tif reqDto.TimeChargeMode != nil {\n\t\torderPricePlan.TimeChargeMode = reqDto.TimeChargeMode\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\torderPricePlan.MinimumCharge = reqDto.MinimumCharge\n\t}\n\n\terr = orderPricePlanService.CreateOrderPricePlan(ctx, &orderPricePlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderPricePlanTransfer.PoToVo(orderPricePlan))\n}\n\n// @Summary 更新价格方案\n// @Description 更新价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateOrderPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderPricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderPricePlan/update [post]\nfunc (controller *OrderPricePlanController) UpdateOrderPricePlan(ctx *gin.Context) {\n\treqDto := req.UpdateOrderPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\torderPricePlan, err := orderPricePlanService.FindOrderPricePlanById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.SessionId != nil {\n\t\torderPricePlan.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.OrderNo != nil {\n\t\torderPricePlan.OrderNo = reqDto.OrderNo\n\t}\n\tif reqDto.PricePlanId != nil {\n\t\torderPricePlan.PricePlanId = reqDto.PricePlanId\n\t}\n\tif reqDto.PricePlanName != nil {\n\t\torderPricePlan.PricePlanName = reqDto.PricePlanName\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\torderPricePlan.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.SelectedAreaId != nil {\n\t\torderPricePlan.SelectedAreaId = reqDto.SelectedAreaId\n\t}\n\tif reqDto.SelectedRoomTypeId != nil {\n\t\torderPricePlan.SelectedRoomTypeId = reqDto.SelectedRoomTypeId\n\t}\n\tif reqDto.BuyMinute != nil {\n\t\torderPricePlan.BuyMinute = reqDto.BuyMinute\n\t}\n\tif reqDto.TimeChargeType != nil {\n\t\torderPricePlan.TimeChargeType = reqDto.TimeChargeType\n\t}\n\tif reqDto.TimeChargeMode != nil {\n\t\torderPricePlan.TimeChargeMode = reqDto.TimeChargeMode\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\torderPricePlan.MinimumCharge = reqDto.MinimumCharge\n\t}\n\n\terr = orderPricePlanService.UpdateOrderPricePlan(ctx, orderPricePlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderPricePlanTransfer.PoToVo(*orderPricePlan))\n}\n\n// @Summary 删除价格方案\n// @Description 删除价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteOrderPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderPricePlan/delete [post]\nfunc (controller *OrderPricePlanController) DeleteOrderPricePlan(ctx *gin.Context) {\n\treqDto := req.DeleteOrderPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = orderPricePlanService.DeleteOrderPricePlan(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询价格方案\n// @Description 查询价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderPricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderPricePlan/query [post]\nfunc (controller *OrderPricePlanController) QueryOrderPricePlans(ctx *gin.Context) {\n\treqDto := req.QueryOrderPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := orderPricePlanService.FindAllOrderPricePlan(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.OrderPricePlanVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, orderPricePlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询价格方案列表\n// @Description 查询价格方案列表\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderPricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderPricePlan/list [post]\nfunc (controller *OrderPricePlanController) ListOrderPricePlans(ctx *gin.Context) {\n\treqDto := req.QueryOrderPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := orderPricePlanService.FindAllOrderPricePlanWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.OrderPricePlanVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.OrderPricePlanVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, orderPricePlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderPricePlanRoute struct {\n}\n\nfunc (s *OrderPricePlanRoute) InitOrderPricePlanRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\torderPricePlanController := controller.OrderPricePlanController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/orderPricePlan/add\", orderPricePlanController.AddOrderPricePlan)       // add\n\t\troute.POST(\"/api/orderPricePlan/update\", orderPricePlanController.UpdateOrderPricePlan) // update\n\t\troute.POST(\"/api/orderPricePlan/delete\", orderPricePlanController.DeleteOrderPricePlan) // delete\n\t\troute.POST(\"/api/orderPricePlan/query\", orderPricePlanController.QueryOrderPricePlans)  // query\n\t\troute.POST(\"/api/orderPricePlan/list\", orderPricePlanController.ListOrderPricePlans)    // list\n\t}\n}\n"}]