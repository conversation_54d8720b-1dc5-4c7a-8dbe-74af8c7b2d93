[{"po": "package po\n\n// OrderRoomPlan 价格方案实体类\ntype OrderRoomPlan struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tSessionId     *string `gorm:\"column:session_id;type:varchar(64);default:''\" json:\"sessionId\"`      // 场次ID\n\tOrderNo       *string `gorm:\"column:order_no;type:varchar(64);default:''\" json:\"orderNo\"`        // 订单ID\n\tRoomId        *string `gorm:\"column:room_id;type:varchar(64);default:''\" json:\"roomId\"`         // 房间ID\n\tRoomName      *string `gorm:\"column:room_name;type:varchar(128);default:''\" json:\"roomName\"`     // 房间名称\n\tPricePlanId   *string `gorm:\"column:price_plan_id;type:varchar(64);default:''\" json:\"pricePlanId\"`  // 方案id\n\tPricePlanName *string `gorm:\"column:price_plan_name;type:varchar(128);default:''\" json:\"pricePlanName\"` // 价格方案名称\n\tStartTime     *int64  `gorm:\"column:start_time;type:int;default:0\" json:\"startTime\"`           // 开始时间\n\tEndTime       *int64  `gorm:\"column:end_time;type:int;default:0\" json:\"endTime\"`               // 结束时间\n\tDuration      *int    `gorm:\"column:duration;type:int;default:0\" json:\"duration\"`              // 买钟时长\n\tPayFee        *int64  `gorm:\"column:pay_fee;type:int;default:0\" json:\"payFee\"`                // 房费\n\tPayStatus     *string `gorm:\"column:pay_status;type:varchar(32);default:''\" json:\"payStatus\"`    // 支付状态\n\tPricePlanType *string `gorm:\"column:price_plan_type;type:varchar(32);default:''\" json:\"pricePlanType\"` // 买钟价格类型\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (OrderRoomPlan) TableName() string {\n\treturn \"order_room_plan\"\n}\n\nfunc (o OrderRoomPlan) GetId() string {\n\treturn *o.Id\n}\n", "vo": "package vo\n\n// OrderRoomPlanVO 价格方案值对象\ntype OrderRoomPlanVO struct {\n\tId            string `json:\"id\"`            // ID\n\tSessionId     string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       string `json:\"orderNo\"`       // 订单ID\n\tRoomId        string `json:\"roomId\"`        // 房间ID\n\tRoomName      string `json:\"roomName\"`      // 房间名称\n\tPricePlanId   string `json:\"pricePlanId\"`   // 方案id\n\tPricePlanName string `json:\"pricePlanName\"` // 价格方案名称\n\tStartTime     int64  `json:\"startTime\"`     // 开始时间\n\tEndTime       int64  `json:\"endTime\"`       // 结束时间\n\tDuration      int    `json:\"duration\"`      // 买钟时长\n\tPayFee        int64  `json:\"payFee\"`        // 房费\n\tPayStatus     string `json:\"payStatus\"`     // 支付状态\n\tPricePlanType string `json:\"pricePlanType\"` // 买钟价格类型\n\tCtime         int64  `json:\"ctime\"`         // 创建时间\n\tUtime         int64  `json:\"utime\"`         // 更新时间\n\tState         int    `json:\"state\"`         // 状态\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddOrderRoomPlanReqDto 创建价格方案请求DTO\ntype AddOrderRoomPlanReqDto struct {\n\tSessionId     *string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       *string `json:\"orderNo\"`       // 订单ID\n\tRoomId        *string `json:\"roomId\"`        // 房间ID\n\tRoomName      *string `json:\"roomName\"`      // 房间名称\n\tPricePlanId   *string `json:\"pricePlanId\"`   // 方案id\n\tPricePlanName *string `json:\"pricePlanName\"` // 价格方案名称\n\tStartTime     *int64  `json:\"startTime\"`     // 开始时间\n\tEndTime       *int64  `json:\"endTime\"`       // 结束时间\n\tDuration      *int    `json:\"duration\"`      // 买钟时长\n\tPayFee        *int64  `json:\"payFee\"`        // 房费\n\tPayStatus     *string `json:\"payStatus\"`     // 支付状态\n\tPricePlanType *string `json:\"pricePlanType\"` // 买钟价格类型\n}\n", "req_update": "package req\n\n// UpdateOrderRoomPlanReqDto 更新价格方案请求DTO\ntype UpdateOrderRoomPlanReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tSessionId     *string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       *string `json:\"orderNo\"`       // 订单ID\n\tRoomId        *string `json:\"roomId\"`        // 房间ID\n\tRoomName      *string `json:\"roomName\"`      // 房间名称\n\tPricePlanId   *string `json:\"pricePlanId\"`   // 方案id\n\tPricePlanName *string `json:\"pricePlanName\"` // 价格方案名称\n\tStartTime     *int64  `json:\"startTime\"`     // 开始时间\n\tEndTime       *int64  `json:\"endTime\"`       // 结束时间\n\tDuration      *int    `json:\"duration\"`      // 买钟时长\n\tPayFee        *int64  `json:\"payFee\"`        // 房费\n\tPayStatus     *string `json:\"payStatus\"`     // 支付状态\n\tPricePlanType *string `json:\"pricePlanType\"` // 买钟价格类型\n}\n", "req_delete": "package req\n\n// DeleteOrderRoomPlanReqDto 删除价格方案请求DTO\ntype DeleteOrderRoomPlanReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryOrderRoomPlanReqDto 查询价格方案请求DTO\ntype QueryOrderRoomPlanReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tSessionId     *string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       *string `json:\"orderNo\"`       // 订单ID\n\tRoomId        *string `json:\"roomId\"`        // 房间ID\n\tRoomName      *string `json:\"roomName\"`      // 房间名称\n\tPricePlanId   *string `json:\"pricePlanId\"`   // 方案id\n\tPricePlanName *string `json:\"pricePlanName\"` // 价格方案名称\n\tStartTime     *int64  `json:\"startTime\"`     // 开始时间\n\tEndTime       *int64  `json:\"endTime\"`       // 结束时间\n\tDuration      *int    `json:\"duration\"`      // 买钟时长\n\tPayFee        *int64  `json:\"payFee\"`        // 房费\n\tPayStatus     *string `json:\"payStatus\"`     // 支付状态\n\tPricePlanType *string `json:\"pricePlanType\"` // 买钟价格类型\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype OrderRoomPlanTransfer struct {\n}\n\nfunc (transfer *OrderRoomPlanTransfer) PoToVo(po po.OrderRoomPlan) vo.OrderRoomPlanVO {\n\tvo := vo.OrderRoomPlanVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *OrderRoomPlanTransfer) VoToPo(vo vo.OrderRoomPlanVO) po.OrderRoomPlan {\n\tpo := po.OrderRoomPlan{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderRoomPlanService struct {\n}\n\nfunc (service *OrderRoomPlanService) CreateOrderRoomPlan(logCtx *gin.Context, orderRoomPlan *po.OrderRoomPlan) error {\n\treturn Save(orderRoomPlan)\n}\n\nfunc (service *OrderRoomPlanService) UpdateOrderRoomPlan(logCtx *gin.Context, orderRoomPlan *po.OrderRoomPlan) error {\n\treturn Update(orderRoomPlan)\n}\n\nfunc (service *OrderRoomPlanService) DeleteOrderRoomPlan(logCtx *gin.Context, id string) error {\n\treturn Delete(po.OrderRoomPlan{Id: &id})\n}\n\nfunc (service *OrderRoomPlanService) FindOrderRoomPlanById(logCtx *gin.Context, id string) (orderRoomPlan *po.OrderRoomPlan, err error) {\n\torderRoomPlan = &po.OrderRoomPlan{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(orderRoomPlan).Error\n\treturn\n}\n\nfunc (service *OrderRoomPlanService) FindAllOrderRoomPlan(logCtx *gin.Context, reqDto *req.QueryOrderRoomPlanReqDto) (list *[]po.OrderRoomPlan, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OrderRoomPlan{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.SessionId != nil && *reqDto.SessionId != \"\" {\n\t\tdb = db.Where(\"session_id=?\", *reqDto.SessionId)\n\t}\n\tif reqDto.OrderNo != nil && *reqDto.OrderNo != \"\" {\n\t\tdb = db.Where(\"order_no=?\", *reqDto.OrderNo)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.PricePlanId != nil && *reqDto.PricePlanId != \"\" {\n\t\tdb = db.Where(\"price_plan_id=?\", *reqDto.PricePlanId)\n\t}\n\tif reqDto.StartTime != nil {\n\t\tdb = db.Where(\"start_time=?\", *reqDto.StartTime)\n\t}\n\tif reqDto.EndTime != nil {\n\t\tdb = db.Where(\"end_time=?\", *reqDto.EndTime)\n\t}\n\tif reqDto.PayStatus != nil && *reqDto.PayStatus != \"\" {\n\t\tdb = db.Where(\"pay_status=?\", *reqDto.PayStatus)\n\t}\n\tif reqDto.PricePlanType != nil && *reqDto.PricePlanType != \"\" {\n\t\tdb = db.Where(\"price_plan_type=?\", *reqDto.PricePlanType)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.OrderRoomPlan{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *OrderRoomPlanService) FindAllOrderRoomPlanWithPagination(logCtx *gin.Context, reqDto *req.QueryOrderRoomPlanReqDto) (list *[]po.OrderRoomPlan, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OrderRoomPlan{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.SessionId != nil && *reqDto.SessionId != \"\" {\n\t\tdb = db.Where(\"session_id=?\", *reqDto.SessionId)\n\t}\n\tif reqDto.OrderNo != nil && *reqDto.OrderNo != \"\" {\n\t\tdb = db.Where(\"order_no=?\", *reqDto.OrderNo)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.PricePlanId != nil && *reqDto.PricePlanId != \"\" {\n\t\tdb = db.Where(\"price_plan_id=?\", *reqDto.PricePlanId)\n\t}\n\tif reqDto.StartTime != nil {\n\t\tdb = db.Where(\"start_time=?\", *reqDto.StartTime)\n\t}\n\tif reqDto.EndTime != nil {\n\t\tdb = db.Where(\"end_time=?\", *reqDto.EndTime)\n\t}\n\tif reqDto.PayStatus != nil && *reqDto.PayStatus != \"\" {\n\t\tdb = db.Where(\"pay_status=?\", *reqDto.PayStatus)\n\t}\n\tif reqDto.PricePlanType != nil && *reqDto.PricePlanType != \"\" {\n\t\tdb = db.Where(\"price_plan_type=?\", *reqDto.PricePlanType)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.OrderRoomPlan{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderRoomPlanController struct{}\n\nvar (\n\torderRoomPlanService  = impl.OrderRoomPlanService{}\n\torderRoomPlanTransfer = transfer.OrderRoomPlanTransfer{}\n)\n\n// @Summary 添加价格方案\n// @Description 添加价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.AddOrderRoomPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderRoomPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderRoomPlan/add [post]\nfunc (controller *OrderRoomPlanController) AddOrderRoomPlan(ctx *gin.Context) {\n\treqDto := req.AddOrderRoomPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\torderRoomPlan := po.OrderRoomPlan{}\n\tif reqDto.SessionId != nil {\n\t\torderRoomPlan.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.OrderNo != nil {\n\t\torderRoomPlan.OrderNo = reqDto.OrderNo\n\t}\n\tif reqDto.RoomId != nil {\n\t\torderRoomPlan.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.RoomName != nil {\n\t\torderRoomPlan.RoomName = reqDto.RoomName\n\t}\n\tif reqDto.PricePlanId != nil {\n\t\torderRoomPlan.PricePlanId = reqDto.PricePlanId\n\t}\n\tif reqDto.PricePlanName != nil {\n\t\torderRoomPlan.PricePlanName = reqDto.PricePlanName\n\t}\n\tif reqDto.StartTime != nil {\n\t\torderRoomPlan.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\torderRoomPlan.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.Duration != nil {\n\t\torderRoomPlan.Duration = reqDto.Duration\n\t}\n\tif reqDto.PayFee != nil {\n\t\torderRoomPlan.PayFee = reqDto.PayFee\n\t}\n\tif reqDto.PayStatus != nil {\n\t\torderRoomPlan.PayStatus = reqDto.PayStatus\n\t}\n\tif reqDto.PricePlanType != nil {\n\t\torderRoomPlan.PricePlanType = reqDto.PricePlanType\n\t}\n\n\terr = orderRoomPlanService.CreateOrderRoomPlan(ctx, &orderRoomPlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderRoomPlanTransfer.PoToVo(orderRoomPlan))\n}\n\n// @Summary 更新价格方案\n// @Description 更新价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateOrderRoomPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderRoomPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderRoomPlan/update [post]\nfunc (controller *OrderRoomPlanController) UpdateOrderRoomPlan(ctx *gin.Context) {\n\treqDto := req.UpdateOrderRoomPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\torderRoomPlan, err := orderRoomPlanService.FindOrderRoomPlanById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.SessionId != nil {\n\t\torderRoomPlan.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.OrderNo != nil {\n\t\torderRoomPlan.OrderNo = reqDto.OrderNo\n\t}\n\tif reqDto.RoomId != nil {\n\t\torderRoomPlan.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.RoomName != nil {\n\t\torderRoomPlan.RoomName = reqDto.RoomName\n\t}\n\tif reqDto.PricePlanId != nil {\n\t\torderRoomPlan.PricePlanId = reqDto.PricePlanId\n\t}\n\tif reqDto.PricePlanName != nil {\n\t\torderRoomPlan.PricePlanName = reqDto.PricePlanName\n\t}\n\tif reqDto.StartTime != nil {\n\t\torderRoomPlan.StartTime = reqDto.StartTime\n\t}\n\tif reqDto.EndTime != nil {\n\t\torderRoomPlan.EndTime = reqDto.EndTime\n\t}\n\tif reqDto.Duration != nil {\n\t\torderRoomPlan.Duration = reqDto.Duration\n\t}\n\tif reqDto.PayFee != nil {\n\t\torderRoomPlan.PayFee = reqDto.PayFee\n\t}\n\tif reqDto.PayStatus != nil {\n\t\torderRoomPlan.PayStatus = reqDto.PayStatus\n\t}\n\tif reqDto.PricePlanType != nil {\n\t\torderRoomPlan.PricePlanType = reqDto.PricePlanType\n\t}\n\n\terr = orderRoomPlanService.UpdateOrderRoomPlan(ctx, orderRoomPlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderRoomPlanTransfer.PoToVo(*orderRoomPlan))\n}\n\n// @Summary 删除价格方案\n// @Description 删除价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteOrderRoomPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderRoomPlan/delete [post]\nfunc (controller *OrderRoomPlanController) DeleteOrderRoomPlan(ctx *gin.Context) {\n\treqDto := req.DeleteOrderRoomPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = orderRoomPlanService.DeleteOrderRoomPlan(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询价格方案\n// @Description 查询价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderRoomPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderRoomPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderRoomPlan/query [post]\nfunc (controller *OrderRoomPlanController) QueryOrderRoomPlans(ctx *gin.Context) {\n\treqDto := req.QueryOrderRoomPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := orderRoomPlanService.FindAllOrderRoomPlan(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.OrderRoomPlanVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, orderRoomPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询价格方案列表\n// @Description 查询价格方案列表\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderRoomPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderRoomPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderRoomPlan/list [post]\nfunc (controller *OrderRoomPlanController) ListOrderRoomPlans(ctx *gin.Context) {\n\treqDto := req.QueryOrderRoomPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := orderRoomPlanService.FindAllOrderRoomPlanWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.OrderRoomPlanVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.OrderRoomPlanVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, orderRoomPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderRoomPlanRoute struct {\n}\n\nfunc (s *OrderRoomPlanRoute) InitOrderRoomPlanRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\torderRoomPlanController := controller.OrderRoomPlanController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/orderRoomPlan/add\", orderRoomPlanController.AddOrderRoomPlan)       // add\n\t\troute.POST(\"/api/orderRoomPlan/update\", orderRoomPlanController.UpdateOrderRoomPlan) // update\n\t\troute.POST(\"/api/orderRoomPlan/delete\", orderRoomPlanController.DeleteOrderRoomPlan) // delete\n\t\troute.POST(\"/api/orderRoomPlan/query\", orderRoomPlanController.QueryOrderRoomPlans)  // query\n\t\troute.POST(\"/api/orderRoomPlan/list\", orderRoomPlanController.ListOrderRoomPlans)    // list\n\t}\n}\n"}]