[{"po": "package po\n\n// PrepaidCard 预付卡实体\ntype PrepaidCard struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一id\n\tCardNumber    *string `gorm:\"column:card_number;type:varchar(64);default:''\" json:\"cardNumber\"`    // 卡号\n\tUsedTimes     *int    `gorm:\"column:used_times;type:int;default:0\" json:\"usedTimes\"`             // 已使用次数\n\tRemainingTimes *int    `gorm:\"column:remaining_times;type:int;default:0\" json:\"remainingTimes\"`   // 剩余次数\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (PrepaidCard) TableName() string {\n\treturn \"prepaid_card\"\n}\n\nfunc (p PrepaidCard) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PrepaidCardVO 预付卡信息值对象\ntype PrepaidCardVO struct {\n\tId            string `json:\"id\"`            // 唯一id\n\tCardNumber    string `json:\"cardNumber\"`    // 卡号\n\tUsedTimes     int    `json:\"usedTimes\"`     // 已使用次数\n\tRemainingTimes int    `json:\"remainingTimes\"` // 剩余次数\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddPrepaidCardReqDto 创建预付卡请求DTO\ntype AddPrepaidCardReqDto struct {\n\tCardNumber    *string `json:\"cardNumber\"`    // 卡号\n\tUsedTimes     *int    `json:\"usedTimes\"`     // 已使用次数\n\tRemainingTimes *int    `json:\"remainingTimes\"` // 剩余次数\n}\n", "req_update": "package req\n\ntype UpdatePrepaidCardReqDto struct {\n\tId            *string `json:\"id\"`            // 唯一id\n\tCardNumber    *string `json:\"cardNumber\"`    // 卡号\n\tUsedTimes     *int    `json:\"usedTimes\"`     // 已使用次数\n\tRemainingTimes *int    `json:\"remainingTimes\"` // 剩余次数\n}\n", "req_delete": "package req\n\ntype DeletePrepaidCardReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryPrepaidCardReqDto struct {\n\tId            *string `json:\"id\"`            // 唯一id\n\tCardNumber    *string `json:\"cardNumber\"`    // 卡号\n\tUsedTimes     *int    `json:\"usedTimes\"`     // 已使用次数\n\tRemainingTimes *int    `json:\"remainingTimes\"` // 剩余次数\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PrepaidCardTransfer struct {\n}\n\nfunc (transfer *PrepaidCardTransfer) PoToVo(po po.PrepaidCard) vo.PrepaidCardVO {\n\tvo := vo.PrepaidCardVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PrepaidCardTransfer) VoToPo(vo vo.PrepaidCardVO) po.PrepaidCard {\n\tpo := po.PrepaidCard{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrepaidCardService struct {\n}\n\nfunc (service *PrepaidCardService) CreatePrepaidCard(logCtx *gin.Context, prepaidCard *po.PrepaidCard) error {\n\treturn Save(prepaidCard)\n}\n\nfunc (service *PrepaidCardService) UpdatePrepaidCard(logCtx *gin.Context, prepaidCard *po.PrepaidCard) error {\n\treturn Update(prepaidCard)\n}\n\nfunc (service *PrepaidCardService) DeletePrepaidCard(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PrepaidCard{Id: &id})\n}\n\nfunc (service *PrepaidCardService) FindPrepaidCardById(logCtx *gin.Context, id string) (prepaidCard *po.PrepaidCard, err error) {\n\tprepaidCard = &po.PrepaidCard{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(prepaidCard).Error\n\treturn\n}\n\nfunc (service *PrepaidCardService) FindAllPrepaidCard(logCtx *gin.Context, reqDto *req.QueryPrepaidCardReqDto) (list *[]po.PrepaidCard, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PrepaidCard{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\tif reqDto.UsedTimes != nil {\n\t\tdb = db.Where(\"used_times=?\", *reqDto.UsedTimes)\n\t}\n\tif reqDto.RemainingTimes != nil {\n\t\tdb = db.Where(\"remaining_times=?\", *reqDto.RemainingTimes)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PrepaidCard{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PrepaidCardService) FindAllPrepaidCardWithPagination(logCtx *gin.Context, reqDto *req.QueryPrepaidCardReqDto) (list *[]po.PrepaidCard, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PrepaidCard{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\tif reqDto.UsedTimes != nil {\n\t\tdb = db.Where(\"used_times=?\", *reqDto.UsedTimes)\n\t}\n\tif reqDto.RemainingTimes != nil {\n\t\tdb = db.Where(\"remaining_times=?\", *reqDto.RemainingTimes)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PrepaidCard{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrepaidCardController struct{}\n\nvar (\n\tprepaidCardService  = impl.PrepaidCardService{}\n\tprepaidCardTransfer = transfer.PrepaidCardTransfer{}\n)\n\n// @Summary 添加预付卡\n// @Description 添加预付卡\n// @Tags 预付卡\n// @Accept json\n// @Produce json\n// @Param body body req.AddPrepaidCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PrepaidCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCard/add [post]\nfunc (controller *PrepaidCardController) AddPrepaidCard(ctx *gin.Context) {\n\treqDto := req.AddPrepaidCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tprepaidCard := po.PrepaidCard{}\n\tif reqDto.CardNumber != nil {\n\t\tprepaidCard.CardNumber = reqDto.CardNumber\n\t}\n\tif reqDto.UsedTimes != nil {\n\t\tprepaidCard.UsedTimes = reqDto.UsedTimes\n\t}\n\tif reqDto.RemainingTimes != nil {\n\t\tprepaidCard.RemainingTimes = reqDto.RemainingTimes\n\t}\n\terr = prepaidCardService.CreatePrepaidCard(ctx, &prepaidCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, prepaidCardTransfer.PoToVo(prepaidCard))\n}\n\n// @Summary 更新预付卡\n// @Description 更新预付卡\n// @Tags 预付卡\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePrepaidCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PrepaidCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCard/update [post]\nfunc (controller *PrepaidCardController) UpdatePrepaidCard(ctx *gin.Context) {\n\treqDto := req.UpdatePrepaidCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tprepaidCard, err := prepaidCardService.FindPrepaidCardById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CardNumber != nil {\n\t\tprepaidCard.CardNumber = reqDto.CardNumber\n\t}\n\tif reqDto.UsedTimes != nil {\n\t\tprepaidCard.UsedTimes = reqDto.UsedTimes\n\t}\n\tif reqDto.RemainingTimes != nil {\n\t\tprepaidCard.RemainingTimes = reqDto.RemainingTimes\n\t}\n\terr = prepaidCardService.UpdatePrepaidCard(ctx, prepaidCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, prepaidCardTransfer.PoToVo(*prepaidCard))\n}\n\n// @Summary 删除预付卡\n// @Description 删除预付卡\n// @Tags 预付卡\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePrepaidCardReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCard/delete [post]\nfunc (controller *PrepaidCardController) DeletePrepaidCard(ctx *gin.Context) {\n\treqDto := req.DeletePrepaidCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = prepaidCardService.DeletePrepaidCard(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询预付卡\n// @Description 查询预付卡\n// @Tags 预付卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPrepaidCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PrepaidCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCard/query [post]\nfunc (controller *PrepaidCardController) QueryPrepaidCards(ctx *gin.Context) {\n\treqDto := req.QueryPrepaidCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := prepaidCardService.FindAllPrepaidCard(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PrepaidCardVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, prepaidCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询预付卡列表\n// @Description 查询预付卡列表\n// @Tags 预付卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPrepaidCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PrepaidCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/prepaidCard/list [post]\nfunc (a *PrepaidCardController) ListPrepaidCards(ctx *gin.Context) {\n\treqDto := req.QueryPrepaidCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := prepaidCardService.FindAllPrepaidCardWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PrepaidCardVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PrepaidCardVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, prepaidCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrepaidCardRoute struct {\n}\n\nfunc (s *PrepaidCardRoute) InitPrepaidCardRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tprepaidCardController := controller.PrepaidCardController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/prepaidCard/add\", prepaidCardController.AddPrepaidCard)       //add\n\t\troute.POST(\"/api/prepaidCard/update\", prepaidCardController.UpdatePrepaidCard) //update\n\t\troute.POST(\"/api/prepaidCard/delete\", prepaidCardController.DeletePrepaidCard) //delete\n\t\troute.POST(\"/api/prepaidCard/query\", prepaidCardController.QueryPrepaidCards)  //query\n\t\troute.POST(\"/api/prepaidCard/list\", prepaidCardController.ListPrepaidCards)   //list\n\t}\n}\n"}]