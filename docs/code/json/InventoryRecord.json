[{"po": "package po\n\n// InventoryRecord 库存记录实体\ntype InventoryRecord struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tVenueId       *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`         // 所属门店ID\n\tWarehouse     *string `gorm:\"column:warehouse;type:varchar(64);default:''\" json:\"warehouse\"`       // 仓库名称\n\tType          *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`               // 记录类型\n\tHandler       *string `gorm:\"column:handler;type:varchar(64);default:''\" json:\"handler\"`         // 操作人\n\tTime          *int64  `gorm:\"column:time;type:int;default:0\" json:\"time\"`                       // 操作时间\n\tInboundNumber *string `gorm:\"column:inbound_number;type:varchar(64);default:''\" json:\"inboundNumber\"` // 入库单号\n\tProducts      *string `gorm:\"column:products;type:text\" json:\"products\"`                           // 产品信息列表\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (InventoryRecord) TableName() string {\n\treturn \"inventory_record\"\n}\n\nfunc (i InventoryRecord) GetId() string {\n\treturn *i.Id\n}\n", "vo": "package vo\n\n// InventoryRecordVO 库存记录值对象\ntype InventoryRecordVO struct {\n\tId            string `json:\"id\"`            // ID\n\tVenueId       string `json:\"venueId\"`       // 所属门店ID\n\tWarehouse     string `json:\"warehouse\"`     // 仓库名称\n\tType          string `json:\"type\"`          // 记录类型\n\tHandler       string `json:\"handler\"`       // 操作人\n\tTime          int64  `json:\"time\"`          // 操作时间\n\tInboundNumber string `json:\"inboundNumber\"` // 入库单号\n\tProducts      string `json:\"products\"`      // 产品信息列表\n\tCtime         int64  `json:\"ctime\"`         // 创建时间\n\tUtime         int64  `json:\"utime\"`         // 更新时间\n\tState         int    `json:\"state\"`         // 状态\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddInventoryRecordReqDto 创建库存记录请求DTO\ntype AddInventoryRecordReqDto struct {\n\tVenueId       *string `json:\"venueId\"`       // 所属门店ID\n\tWarehouse     *string `json:\"warehouse\"`     // 仓库名称\n\tType          *string `json:\"type\"`          // 记录类型\n\tHandler       *string `json:\"handler\"`       // 操作人\n\tTime          *int64  `json:\"time\"`          // 操作时间\n\tInboundNumber *string `json:\"inboundNumber\"` // 入库单号\n\tProducts      *string `json:\"products\"`      // 产品信息列表\n}\n", "req_update": "package req\n\n// UpdateInventoryRecordReqDto 更新库存记录请求DTO\ntype UpdateInventoryRecordReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tVenueId       *string `json:\"venueId\"`       // 所属门店ID\n\tWarehouse     *string `json:\"warehouse\"`     // 仓库名称\n\tType          *string `json:\"type\"`          // 记录类型\n\tHandler       *string `json:\"handler\"`       // 操作人\n\tTime          *int64  `json:\"time\"`          // 操作时间\n\tInboundNumber *string `json:\"inboundNumber\"` // 入库单号\n\tProducts      *string `json:\"products\"`      // 产品信息列表\n}\n", "req_delete": "package req\n\ntype DeleteInventoryRecordReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryInventoryRecordReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tVenueId       *string `json:\"venueId\"`       // 所属门店ID\n\tWarehouse     *string `json:\"warehouse\"`     // 仓库名称\n\tType          *string `json:\"type\"`          // 记录类型\n\tHandler       *string `json:\"handler\"`       // 操作人\n\tTime          *int64  `json:\"time\"`          // 操作时间\n\tInboundNumber *string `json:\"inboundNumber\"` // 入库单号\n\tProducts      *string `json:\"products\"`      // 产品信息列表\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype InventoryRecordTransfer struct {\n}\n\nfunc (transfer *InventoryRecordTransfer) PoToVo(po po.InventoryRecord) vo.InventoryRecordVO {\n\tvo := vo.InventoryRecordVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *InventoryRecordTransfer) VoToPo(vo vo.InventoryRecordVO) po.InventoryRecord {\n\tpo := po.InventoryRecord{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype InventoryRecordService struct {\n}\n\nfunc (service *InventoryRecordService) CreateInventoryRecord(logCtx *gin.Context, record *po.InventoryRecord) error {\n\treturn Save(record)\n}\n\nfunc (service *InventoryRecordService) UpdateInventoryRecord(logCtx *gin.Context, record *po.InventoryRecord) error {\n\treturn Update(record)\n}\n\nfunc (service *InventoryRecordService) DeleteInventoryRecord(logCtx *gin.Context, id string) error {\n\treturn Delete(po.InventoryRecord{Id: &id})\n}\n\nfunc (service *InventoryRecordService) FindInventoryRecordById(logCtx *gin.Context, id string) (record *po.InventoryRecord, err error) {\n\trecord = &po.InventoryRecord{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(record).Error\n\treturn\n}\n\nfunc (service *InventoryRecordService) FindAllInventoryRecord(logCtx *gin.Context, reqDto *req.QueryInventoryRecordReqDto) (list *[]po.InventoryRecord, err error) {\n\tdb := model.DBSlave.Self.Model(&po.InventoryRecord{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.InventoryRecord{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *InventoryRecordService) FindAllInventoryRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryInventoryRecordReqDto) (list *[]po.InventoryRecord, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.InventoryRecord{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.InventoryRecord{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype InventoryRecordController struct{}\n\nvar (\n\tinventoryRecordService  = impl.InventoryRecordService{}\n\tinventoryRecordTransfer = transfer.InventoryRecordTransfer{}\n)\n\n// @Summary 添加库存记录\n// @Description 添加库存记录\n// @Tags 库存记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddInventoryRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.InventoryRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-record/add [post]\nfunc (controller *InventoryRecordController) AddInventoryRecord(ctx *gin.Context) {\n\treqDto := req.AddInventoryRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\trecord := po.InventoryRecord{}\n\tif reqDto.VenueId != nil {\n\t\trecord.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Warehouse != nil {\n\t\trecord.Warehouse = reqDto.Warehouse\n\t}\n\tif reqDto.Type != nil {\n\t\trecord.Type = reqDto.Type\n\t}\n\tif reqDto.Handler != nil {\n\t\trecord.Handler = reqDto.Handler\n\t}\n\tif reqDto.Time != nil {\n\t\trecord.Time = reqDto.Time\n\t}\n\tif reqDto.InboundNumber != nil {\n\t\trecord.InboundNumber = reqDto.InboundNumber\n\t}\n\tif reqDto.Products != nil {\n\t\trecord.Products = reqDto.Products\n\t}\n\n\terr = inventoryRecordService.CreateInventoryRecord(ctx, &record)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, inventoryRecordTransfer.PoToVo(record))\n}\n\n// @Summary 更新库存记录\n// @Description 更新库存记录\n// @Tags 库存记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateInventoryRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.InventoryRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-record/update [post]\nfunc (controller *InventoryRecordController) UpdateInventoryRecord(ctx *gin.Context) {\n\treqDto := req.UpdateInventoryRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\trecord, err := inventoryRecordService.FindInventoryRecordById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\trecord.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Warehouse != nil {\n\t\trecord.Warehouse = reqDto.Warehouse\n\t}\n\tif reqDto.Type != nil {\n\t\trecord.Type = reqDto.Type\n\t}\n\tif reqDto.Handler != nil {\n\t\trecord.Handler = reqDto.Handler\n\t}\n\tif reqDto.Time != nil {\n\t\trecord.Time = reqDto.Time\n\t}\n\tif reqDto.InboundNumber != nil {\n\t\trecord.InboundNumber = reqDto.InboundNumber\n\t}\n\tif reqDto.Products != nil {\n\t\trecord.Products = reqDto.Products\n\t}\n\n\terr = inventoryRecordService.UpdateInventoryRecord(ctx, record)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, inventoryRecordTransfer.PoToVo(*record))\n}\n\n// @Summary 删除库存记录\n// @Description 删除库存记录\n// @Tags 库存记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteInventoryRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-record/delete [post]\nfunc (controller *InventoryRecordController) DeleteInventoryRecord(ctx *gin.Context) {\n\treqDto := req.DeleteInventoryRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = inventoryRecordService.DeleteInventoryRecord(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询库存记录\n// @Description 查询库存记录\n// @Tags 库存记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryInventoryRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.InventoryRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-record/query [post]\nfunc (controller *InventoryRecordController) QueryInventoryRecords(ctx *gin.Context) {\n\treqDto := req.QueryInventoryRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := inventoryRecordService.FindAllInventoryRecord(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.InventoryRecordVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, inventoryRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询库存记录列表\n// @Description 查询库存记录列表\n// @Tags 库存记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryInventoryRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.InventoryRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-record/list [post]\nfunc (controller *InventoryRecordController) ListInventoryRecords(ctx *gin.Context) {\n\treqDto := req.QueryInventoryRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := inventoryRecordService.FindAllInventoryRecordWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.InventoryRecordVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.InventoryRecordVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, inventoryRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype InventoryRecordRoute struct {\n}\n\nfunc (s *InventoryRecordRoute) InitInventoryRecordRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tinventoryRecordController := controller.InventoryRecordController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/inventory-record/add\", inventoryRecordController.AddInventoryRecord)       // add\n\t\troute.POST(\"/api/inventory-record/update\", inventoryRecordController.UpdateInventoryRecord)   // update\n\t\troute.POST(\"/api/inventory-record/delete\", inventoryRecordController.DeleteInventoryRecord)   // delete\n\t\troute.POST(\"/api/inventory-record/query\", inventoryRecordController.QueryInventoryRecords)   // query\n\t\troute.POST(\"/api/inventory-record/list\", inventoryRecordController.ListInventoryRecords)     // list\n\t}\n}\n"}]