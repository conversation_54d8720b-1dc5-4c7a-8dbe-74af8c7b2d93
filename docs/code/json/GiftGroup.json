[{"po": "package po\n\n// GiftGroup 赠品组实体\ntype GiftGroup struct {\n\tId          *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName        *string  `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`           // 赠品组名称\n\tProducts    *string  `gorm:\"column:products;type:varchar(255);default:''\" json:\"products\"`     // 产品类型\n\tIsDisplayed *bool    `gorm:\"column:is_displayed;type:bool;default:false\" json:\"isDisplayed\"`   // 是否显示\n\tCtime       *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime       *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState       *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion     *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (GiftGroup) TableName() string {\n\treturn \"gift_group\"\n}\n\nfunc (g GiftGroup) GetId() string {\n\treturn *g.Id\n}\n", "vo": "package vo\n\n// GiftGroupVO 赠品组信息值对象\ntype GiftGroupVO struct {\n\tId          string  `json:\"id\"`           // ID\n\tName        string  `json:\"name\"`         // 赠品组名称\n\tProducts    string  `json:\"products\"`     // 产品类型\n\tIsDisplayed bool    `json:\"isDisplayed\"` // 是否显示\n\tCtime       int64   `json:\"ctime\"`        // 创建时间戳\n\tUtime       int64   `json:\"utime\"`        // 更新时间戳\n\tState       int     `json:\"state\"`        // 状态值\n\tVersion     int     `json:\"version\"`      // 版本号\n}\n", "req_add": "package req\n\n// AddGiftGroupReqDto 创建赠品组请求DTO\ntype AddGiftGroupReqDto struct {\n\tName        *string `json:\"name\"`         // 赠品组名称\n\tProducts    *string `json:\"products\"`     // 产品类型\n\tIsDisplayed *bool   `json:\"isDisplayed\"` // 是否显示\n}\n", "req_update": "package req\n\ntype UpdateGiftGroupReqDto struct {\n\tId          *string `json:\"id\"`           // ID\n\tName        *string `json:\"name\"`         // 赠品组名称\n\tProducts    *string `json:\"products\"`     // 产品类型\n\tIsDisplayed *bool   `json:\"isDisplayed\"` // 是否显示\n}\n", "req_delete": "package req\n\ntype DeleteGiftGroupReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryGiftGroupReqDto struct {\n\tId          *string `json:\"id\"`           // ID\n\tName        *string `json:\"name\"`         // 赠品组名称\n\tProducts    *string `json:\"products\"`     // 产品类型\n\tIsDisplayed *bool   `json:\"isDisplayed\"` // 是否显示\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype GiftGroupTransfer struct {\n}\n\nfunc (transfer *GiftGroupTransfer) PoToVo(po po.GiftGroup) vo.GiftGroupVO {\n\tvo := vo.GiftGroupVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *GiftGroupTransfer) VoToPo(vo vo.GiftGroupVO) po.GiftGroup {\n\tpo := po.GiftGroup{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype GiftGroupService struct {\n}\n\nfunc (service *GiftGroupService) CreateGiftGroup(logCtx *gin.Context, giftGroup *po.GiftGroup) error {\n\treturn Save(giftGroup)\n}\n\nfunc (service *GiftGroupService) UpdateGiftGroup(logCtx *gin.Context, giftGroup *po.GiftGroup) error {\n\treturn Update(giftGroup)\n}\n\nfunc (service *GiftGroupService) DeleteGiftGroup(logCtx *gin.Context, id string) error {\n\treturn Delete(po.GiftGroup{Id: &id})\n}\n\nfunc (service *GiftGroupService) FindGiftGroupById(logCtx *gin.Context, id string) (giftGroup *po.GiftGroup, err error) {\n\tgiftGroup = &po.GiftGroup{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(giftGroup).Error\n\treturn\n}\n\nfunc (service *GiftGroupService) FindAllGiftGroup(logCtx *gin.Context, reqDto *req.QueryGiftGroupReqDto) (list *[]po.GiftGroup, err error) {\n\tdb := model.DBSlave.Self.Model(&po.GiftGroup{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Products != nil && *reqDto.Products != \"\" {\n\t\tdb = db.Where(\"products=?\", *reqDto.Products)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.GiftGroup{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *GiftGroupService) FindAllGiftGroupWithPagination(logCtx *gin.Context, reqDto *req.QueryGiftGroupReqDto) (list *[]po.GiftGroup, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.GiftGroup{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Products != nil && *reqDto.Products != \"\" {\n\t\tdb = db.Where(\"products=?\", *reqDto.Products)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.GiftGroup{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype GiftGroupController struct{}\n\nvar (\n\tgiftGroupService  = impl.GiftGroupService{}\n\tgiftGroupTransfer = transfer.GiftGroupTransfer{}\n)\n\n// @Summary 添加赠品组\n// @Description 添加赠品组\n// @Tags 赠品组\n// @Accept json\n// @Produce json\n// @Param body body req.AddGiftGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.GiftGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-group/add [post]\nfunc (controller *GiftGroupController) AddGiftGroup(ctx *gin.Context) {\n\treqDto := req.AddGiftGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tgiftGroup := po.GiftGroup{}\n\tif reqDto.Name != nil {\n\t\tgiftGroup.Name = reqDto.Name\n\t}\n\tif reqDto.Products != nil {\n\t\tgiftGroup.Products = reqDto.Products\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tgiftGroup.IsDisplayed = reqDto.IsDisplayed\n\t}\n\terr = giftGroupService.CreateGiftGroup(ctx, &giftGroup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, giftGroupTransfer.PoToVo(giftGroup))\n}\n\n// @Summary 更新赠品组\n// @Description 更新赠品组\n// @Tags 赠品组\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateGiftGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.GiftGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-group/update [post]\nfunc (controller *GiftGroupController) UpdateGiftGroup(ctx *gin.Context) {\n\treqDto := req.UpdateGiftGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tgiftGroup, err := giftGroupService.FindGiftGroupById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tgiftGroup.Name = reqDto.Name\n\t}\n\tif reqDto.Products != nil {\n\t\tgiftGroup.Products = reqDto.Products\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tgiftGroup.IsDisplayed = reqDto.IsDisplayed\n\t}\n\terr = giftGroupService.UpdateGiftGroup(ctx, giftGroup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, giftGroupTransfer.PoToVo(*giftGroup))\n}\n\n// @Summary 删除赠品组\n// @Description 删除赠品组\n// @Tags 赠品组\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteGiftGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-group/delete [post]\nfunc (controller *GiftGroupController) DeleteGiftGroup(ctx *gin.Context) {\n\treqDto := req.DeleteGiftGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = giftGroupService.DeleteGiftGroup(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询赠品组\n// @Description 查询赠品组\n// @Tags 赠品组\n// @Accept json\n// @Produce json\n// @Param body body req.QueryGiftGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.GiftGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-group/query [post]\nfunc (controller *GiftGroupController) QueryGiftGroups(ctx *gin.Context) {\n\treqDto := req.QueryGiftGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := giftGroupService.FindAllGiftGroup(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.GiftGroupVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, giftGroupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询赠品组列表\n// @Description 查询赠品组列表\n// @Tags 赠品组\n// @Accept json\n// @Produce json\n// @Param body body req.QueryGiftGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.GiftGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-group/list [post]\nfunc (a *GiftGroupController) ListGiftGroups(ctx *gin.Context) {\n\treqDto := req.QueryGiftGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := giftGroupService.FindAllGiftGroupWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.GiftGroupVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.GiftGroupVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, giftGroupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype GiftGroupRoute struct {\n}\n\nfunc (s *GiftGroupRoute) InitGiftGroupRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tgiftGroupController := controller.GiftGroupController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/gift-group/add\", giftGroupController.AddGiftGroup)       //add\n\t\troute.POST(\"/api/gift-group/update\", giftGroupController.UpdateGiftGroup) //update\n\t\troute.POST(\"/api/gift-group/delete\", giftGroupController.DeleteGiftGroup) //delete\n\t\troute.POST(\"/api/gift-group/query\", giftGroupController.QueryGiftGroups)  //query\n\t\troute.POST(\"/api/gift-group/list\", giftGroupController.ListGiftGroups)    //list\n\t}\n}\n"}]