[{"po": "package po\n\n// Network 网络实体\ntype Network struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                     // ID\n\tConnectionStatus *string `gorm:\"column:connection_status;type:varchar(64);default:''\" json:\"connectionStatus\"` // 连接状态\n\tSubnet          *string `gorm:\"column:subnet;type:varchar(64);default:''\" json:\"subnet\"`                     // 子网\n\tCtime           *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                               // 创建时间戳\n\tUtime           *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                               // 更新时间戳\n\tState           *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                               // 状态值\n\tVersion         *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                           // 版本号\n}\n\n// TableName 设置表名\nfunc (Network) TableName() string {\n\treturn \"network\"\n}\n\nfunc (n Network) GetId() string {\n\treturn *n.Id\n}\n", "vo": "package vo\n\n// NetworkVO 网络信息值对象\ntype NetworkVO struct {\n\tId              string `json:\"id\"`              // ID\n\tConnectionStatus string `json:\"connectionStatus\"` // 连接状态\n\tSubnet          string `json:\"subnet\"`          // 子网\n\tCtime           int64  `json:\"ctime\"`           // 创建时间戳\n\tUtime           int64  `json:\"utime\"`           // 更新时间戳\n\tState           int    `json:\"state\"`           // 状态值\n\tVersion         int    `json:\"version\"`         // 版本号\n}\n", "req_add": "package req\n\n// AddNetworkReqDto 创建网络请求DTO\ntype AddNetworkReqDto struct {\n\tConnectionStatus *string `json:\"connectionStatus\"` // 连接状态\n\tSubnet          *string `json:\"subnet\"`          // 子网\n}\n", "req_update": "package req\n\ntype UpdateNetworkReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tConnectionStatus *string `json:\"connectionStatus\"` // 连接状态\n\tSubnet          *string `json:\"subnet\"`          // 子网\n}\n", "req_delete": "package req\n\ntype DeleteNetworkReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryNetworkReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tConnectionStatus *string `json:\"connectionStatus\"` // 连接状态\n\tSubnet          *string `json:\"subnet\"`          // 子网\n\tPageNum         *int    `json:\"pageNum\"`         // 页码\n\tPageSize        *int    `json:\"pageSize\"`        // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype NetworkTransfer struct {\n}\n\nfunc (transfer *NetworkTransfer) PoToVo(po po.Network) vo.NetworkVO {\n\tvo := vo.NetworkVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *NetworkTransfer) VoToPo(vo vo.NetworkVO) po.Network {\n\tpo := po.Network{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype NetworkService struct {\n}\n\nfunc (service *NetworkService) CreateNetwork(logCtx *gin.Context, network *po.Network) error {\n\treturn Save(network)\n}\n\nfunc (service *NetworkService) UpdateNetwork(logCtx *gin.Context, network *po.Network) error {\n\treturn Update(network)\n}\n\nfunc (service *NetworkService) DeleteNetwork(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Network{Id: &id})\n}\n\nfunc (service *NetworkService) FindNetworkById(logCtx *gin.Context, id string) (network *po.Network, err error) {\n\tnetwork = &po.Network{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(network).Error\n\treturn\n}\n\nfunc (service *NetworkService) FindAllNetwork(logCtx *gin.Context, reqDto *req.QueryNetworkReqDto) (list *[]po.Network, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Network{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Network{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *NetworkService) FindAllNetworkWithPagination(logCtx *gin.Context, reqDto *req.QueryNetworkReqDto) (list *[]po.Network, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Network{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Network{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype NetworkController struct{}\n\nvar (\n\tnetworkService  = impl.NetworkService{}\n\tnetworkTransfer = transfer.NetworkTransfer{}\n)\n\n// @Summary 添加网络\n// @Description 添加网络\n// @Tags 网络\n// @Accept json\n// @Produce json\n// @Param body body req.AddNetworkReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.NetworkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/network/add [post]\nfunc (controller *NetworkController) AddNetwork(ctx *gin.Context) {\n\treqDto := req.AddNetworkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnetwork := po.Network{}\n\tif reqDto.ConnectionStatus != nil {\n\t\tnetwork.ConnectionStatus = reqDto.ConnectionStatus\n\t}\n\tif reqDto.Subnet != nil {\n\t\tnetwork.Subnet = reqDto.Subnet\n\t}\n\terr = networkService.CreateNetwork(ctx, &network)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, networkTransfer.PoToVo(network))\n}\n\n// @Summary 更新网络\n// @Description 更新网络\n// @Tags 网络\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateNetworkReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.NetworkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/network/update [post]\nfunc (controller *NetworkController) UpdateNetwork(ctx *gin.Context) {\n\treqDto := req.UpdateNetworkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tnetwork, err := networkService.FindNetworkById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.ConnectionStatus != nil {\n\t\tnetwork.ConnectionStatus = reqDto.ConnectionStatus\n\t}\n\tif reqDto.Subnet != nil {\n\t\tnetwork.Subnet = reqDto.Subnet\n\t}\n\terr = networkService.UpdateNetwork(ctx, network)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, networkTransfer.PoToVo(*network))\n}\n\n// @Summary 删除网络\n// @Description 删除网络\n// @Tags 网络\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteNetworkReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/network/delete [post]\nfunc (controller *NetworkController) DeleteNetwork(ctx *gin.Context) {\n\treqDto := req.DeleteNetworkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = networkService.DeleteNetwork(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询网络\n// @Description 查询网络\n// @Tags 网络\n// @Accept json\n// @Produce json\n// @Param body body req.QueryNetworkReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.NetworkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/network/query [post]\nfunc (controller *NetworkController) QueryNetworks(ctx *gin.Context) {\n\treqDto := req.QueryNetworkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := networkService.FindAllNetwork(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.NetworkVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, networkTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询网络列表\n// @Description 查询网络列表\n// @Tags 网络\n// @Accept json\n// @Produce json\n// @Param body body req.QueryNetworkReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.NetworkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/network/list [post]\nfunc (a *NetworkController) ListNetworks(ctx *gin.Context) {\n\treqDto := req.QueryNetworkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := networkService.FindAllNetworkWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.NetworkVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.NetworkVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, networkTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype NetworkRoute struct {\n}\n\nfunc (s *NetworkRoute) InitNetworkRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tnetworkController := controller.NetworkController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/network/add\", networkController.AddNetwork)    //add\n\t\troute.POST(\"/api/network/update\", networkController.UpdateNetwork) //update\n\t\troute.POST(\"/api/network/delete\", networkController.DeleteNetwork) //delete\n\t\troute.POST(\"/api/network/query\", networkController.QueryNetworks)     //query\n\t\troute.POST(\"/api/network/list\", networkController.ListNetworks)     //list\n\t}\n}\n"}]