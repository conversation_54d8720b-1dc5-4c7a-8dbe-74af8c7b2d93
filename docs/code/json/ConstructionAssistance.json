[{"po": "package po\n\n// ConstructionAssistance 施工协助实体\ntype ConstructionAssistance struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tTechnicianPhone *string `gorm:\"column:technician_phone;type:varchar(64);default:''\" json:\"technicianPhone\"` // 技术员电话\n\tValidityPeriod  *int64  `gorm:\"column:validity_period;type:int;default:0\" json:\"validityPeriod\"`           // 有效期\n\tStoreId         *string `gorm:\"column:store_id;type:varchar(64);default:''\" json:\"storeId\"`                // 所属店铺ID\n\tCtime           *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                              // 创建时间戳\n\tUtime           *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                              // 更新时间戳\n\tState           *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                              // 状态值\n\tVersion         *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                          // 版本号\n}\n\n// TableName 设置表名\nfunc (ConstructionAssistance) TableName() string {\n\treturn \"construction_assistance\"\n}\n\nfunc (c ConstructionAssistance) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// ConstructionAssistanceVO 施工协助值对象\ntype ConstructionAssistanceVO struct {\n\tId              string `json:\"id\"`              // ID\n\tTechnicianPhone string `json:\"technicianPhone\"` // 技术员电话\n\tValidityPeriod  int64  `json:\"validityPeriod\"`  // 有效期\n\tStoreId         string `json:\"storeId\"`         // 所属店铺ID\n\tCtime           int64  `json:\"ctime\"`           // 创建时间戳\n\tUtime           int64  `json:\"utime\"`           // 更新时间戳\n\tState           int    `json:\"state\"`           // 状态值\n\tVersion         int    `json:\"version\"`         // 版本号\n}\n", "req_add": "package req\n\n// AddConstructionAssistanceReqDto 创建施工协助请求DTO\ntype AddConstructionAssistanceReqDto struct {\n\tTechnicianPhone *string `json:\"technicianPhone\"` // 技术员电话\n\tValidityPeriod  *int64  `json:\"validityPeriod\"`  // 有效期\n\tStoreId         *string `json:\"storeId\"`         // 所属店铺ID\n}\n", "req_update": "package req\n\ntype UpdateConstructionAssistanceReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tTechnicianPhone *string `json:\"technicianPhone\"` // 技术员电话\n\tValidityPeriod  *int64  `json:\"validityPeriod\"`  // 有效期\n\tStoreId         *string `json:\"storeId\"`         // 所属店铺ID\n}\n", "req_delete": "package req\n\ntype DeleteConstructionAssistanceReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryConstructionAssistanceReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tTechnicianPhone *string `json:\"technicianPhone\"` // 技术员电话\n\tValidityPeriod  *int64  `json:\"validityPeriod\"`  // 有效期\n\tStoreId         *string `json:\"storeId\"`         // 所属店铺ID\n\tPageNum         *int    `json:\"pageNum\"`         // 页码\n\tPageSize        *int    `json:\"pageSize\"`        // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ConstructionAssistanceTransfer struct {\n}\n\nfunc (transfer *ConstructionAssistanceTransfer) PoToVo(po po.ConstructionAssistance) vo.ConstructionAssistanceVO {\n\tvo := vo.ConstructionAssistanceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ConstructionAssistanceTransfer) VoToPo(vo vo.ConstructionAssistanceVO) po.ConstructionAssistance {\n\tpo := po.ConstructionAssistance{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ConstructionAssistanceService struct {\n}\n\nfunc (service *ConstructionAssistanceService) CreateConstructionAssistance(logCtx *gin.Context, constructionAssistance *po.ConstructionAssistance) error {\n\treturn Save(constructionAssistance)\n}\n\nfunc (service *ConstructionAssistanceService) UpdateConstructionAssistance(logCtx *gin.Context, constructionAssistance *po.ConstructionAssistance) error {\n\treturn Update(constructionAssistance)\n}\n\nfunc (service *ConstructionAssistanceService) DeleteConstructionAssistance(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ConstructionAssistance{Id: &id})\n}\n\nfunc (service *ConstructionAssistanceService) FindConstructionAssistanceById(logCtx *gin.Context, id string) (constructionAssistance *po.ConstructionAssistance, err error) {\n\tconstructionAssistance = &po.ConstructionAssistance{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(constructionAssistance).Error\n\treturn\n}\n\nfunc (service *ConstructionAssistanceService) FindAllConstructionAssistance(logCtx *gin.Context, reqDto *req.QueryConstructionAssistanceReqDto) (list *[]po.ConstructionAssistance, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ConstructionAssistance{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ConstructionAssistance{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ConstructionAssistanceService) FindAllConstructionAssistanceWithPagination(logCtx *gin.Context, reqDto *req.QueryConstructionAssistanceReqDto) (list *[]po.ConstructionAssistance, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ConstructionAssistance{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ConstructionAssistance{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ConstructionAssistanceController struct{}\n\nvar (\n\tconstructionAssistanceService  = impl.ConstructionAssistanceService{}\n\tconstructionAssistanceTransfer = transfer.ConstructionAssistanceTransfer{}\n)\n\n// @Summary 添加施工协助\n// @Description 添加施工协助\n// @Tags 施工协助\n// @Accept json\n// @Produce json\n// @Param body body req.AddConstructionAssistanceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ConstructionAssistanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/construction-assistance/add [post]\nfunc (controller *ConstructionAssistanceController) AddConstructionAssistance(ctx *gin.Context) {\n\treqDto := req.AddConstructionAssistanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tconstructionAssistance := po.ConstructionAssistance{}\n\tif reqDto.TechnicianPhone != nil {\n\t\tconstructionAssistance.TechnicianPhone = reqDto.TechnicianPhone\n\t}\n\tif reqDto.ValidityPeriod != nil {\n\t\tconstructionAssistance.ValidityPeriod = reqDto.ValidityPeriod\n\t}\n\tif reqDto.StoreId != nil {\n\t\tconstructionAssistance.StoreId = reqDto.StoreId\n\t}\n\terr = constructionAssistanceService.CreateConstructionAssistance(ctx, &constructionAssistance)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, constructionAssistanceTransfer.PoToVo(constructionAssistance))\n}\n\n// @Summary 更新施工协助\n// @Description 更新施工协助\n// @Tags 施工协助\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateConstructionAssistanceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ConstructionAssistanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/construction-assistance/update [post]\nfunc (controller *ConstructionAssistanceController) UpdateConstructionAssistance(ctx *gin.Context) {\n\treqDto := req.UpdateConstructionAssistanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tconstructionAssistance, err := constructionAssistanceService.FindConstructionAssistanceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.TechnicianPhone != nil {\n\t\tconstructionAssistance.TechnicianPhone = reqDto.TechnicianPhone\n\t}\n\tif reqDto.ValidityPeriod != nil {\n\t\tconstructionAssistance.ValidityPeriod = reqDto.ValidityPeriod\n\t}\n\tif reqDto.StoreId != nil {\n\t\tconstructionAssistance.StoreId = reqDto.StoreId\n\t}\n\terr = constructionAssistanceService.UpdateConstructionAssistance(ctx, constructionAssistance)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, constructionAssistanceTransfer.PoToVo(*constructionAssistance))\n}\n\n// @Summary 删除施工协助\n// @Description 删除施工协助\n// @Tags 施工协助\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteConstructionAssistanceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/construction-assistance/delete [post]\nfunc (controller *ConstructionAssistanceController) DeleteConstructionAssistance(ctx *gin.Context) {\n\treqDto := req.DeleteConstructionAssistanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = constructionAssistanceService.DeleteConstructionAssistance(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询施工协助\n// @Description 查询施工协助\n// @Tags 施工协助\n// @Accept json\n// @Produce json\n// @Param body body req.QueryConstructionAssistanceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ConstructionAssistanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/construction-assistance/query [post]\nfunc (controller *ConstructionAssistanceController) QueryConstructionAssistances(ctx *gin.Context) {\n\treqDto := req.QueryConstructionAssistanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := constructionAssistanceService.FindAllConstructionAssistance(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ConstructionAssistanceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, constructionAssistanceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询施工协助列表\n// @Description 查询施工协助列表\n// @Tags 施工协助\n// @Accept json\n// @Produce json\n// @Param body body req.QueryConstructionAssistanceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ConstructionAssistanceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/construction-assistance/list [post]\nfunc (a *ConstructionAssistanceController) ListConstructionAssistances(ctx *gin.Context) {\n\treqDto := req.QueryConstructionAssistanceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := constructionAssistanceService.FindAllConstructionAssistanceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ConstructionAssistanceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ConstructionAssistanceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, constructionAssistanceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ConstructionAssistanceRoute struct {\n}\n\nfunc (s *ConstructionAssistanceRoute) InitConstructionAssistanceRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tconstructionAssistanceController := controller.ConstructionAssistanceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/construction-assistance/add\", constructionAssistanceController.AddConstructionAssistance)    //add\n\t\troute.POST(\"/api/construction-assistance/update\", constructionAssistanceController.UpdateConstructionAssistance) //update\n\t\troute.POST(\"/api/construction-assistance/delete\", constructionAssistanceController.DeleteConstructionAssistance) //delete\n\t\troute.POST(\"/api/construction-assistance/query\", constructionAssistanceController.QueryConstructionAssistances)     //query\n\t\troute.POST(\"/api/construction-assistance/list\", constructionAssistanceController.ListConstructionAssistances)     //list\n\t}\n}\n"}]