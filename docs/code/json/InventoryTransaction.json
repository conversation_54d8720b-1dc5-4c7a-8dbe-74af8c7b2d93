[{"po": "package po\n\n// InventoryTransaction 库存交易实体\ntype InventoryTransaction struct {\n\tId                    *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                     // ID\n\tType                  *string  `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`                         // 交易类型\n\tWarehouseId           *string  `gorm:\"column:warehouse_id;type:varchar(64);default:''\" json:\"warehouseId\"`           // 仓库ID\n\tHandlerId             *string  `gorm:\"column:handler_id;type:varchar(64);default:''\" json:\"handlerId\"`               // 处理人ID\n\tTransactionTime       *int64   `gorm:\"column:transaction_time;type:int;default:0\" json:\"transactionTime\"`           // 交易时间\n\tRelatedDocumentNumber *string  `gorm:\"column:related_document_number;type:varchar(64);default:''\" json:\"relatedDocumentNumber\"` // 相关文档编号\n\tProductId             *string  `gorm:\"column:product_id;type:varchar(64);default:''\" json:\"productId\"`               // 产品ID\n\tQuantity              *int     `gorm:\"column:quantity;type:int;default:0\" json:\"quantity\"`                         // 数量\n\tCtime                 *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                             // 创建时间\n\tUtime                 *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                             // 更新时间\n\tState                 *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                             // 状态\n\tVersion               *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                         // 版本号\n}\n\n// TableName 设置表名\nfunc (InventoryTransaction) TableName() string {\n\treturn \"inventory_transaction\"\n}\n\nfunc (t InventoryTransaction) GetId() string {\n\treturn *t.Id\n}\n", "vo": "package vo\n\n// InventoryTransactionVO 库存交易值对象\ntype InventoryTransactionVO struct {\n\tId                    string  `json:\"id\"`                     // ID\n\tType                  string  `json:\"type\"`                   // 交易类型\n\tWarehouseId           string  `json:\"warehouseId\"`           // 仓库ID\n\tHandlerId             string  `json:\"handlerId\"`             // 处理人ID\n\tTransactionTime       int64   `json:\"transactionTime\"`       // 交易时间\n\tRelatedDocumentNumber string  `json:\"relatedDocumentNumber\"` // 相关文档编号\n\tProductId             string  `json:\"productId\"`             // 产品ID\n\tQuantity              int     `json:\"quantity\"`              // 数量\n\tCtime                 int64   `json:\"ctime\"`                 // 创建时间\n\tUtime                 int64   `json:\"utime\"`                 // 更新时间\n\tState                 int     `json:\"state\"`                 // 状态\n\tVersion               int     `json:\"version\"`               // 版本号\n}\n", "req_add": "package req\n\n// AddInventoryTransactionReqDto 创建库存交易请求DTO\ntype AddInventoryTransactionReqDto struct {\n\tType                  *string `json:\"type\"`                   // 交易类型\n\tWarehouseId           *string `json:\"warehouseId\"`           // 仓库ID\n\tHandlerId             *string `json:\"handlerId\"`             // 处理人ID\n\tTransactionTime       *int64  `json:\"transactionTime\"`       // 交易时间\n\tRelatedDocumentNumber *string `json:\"relatedDocumentNumber\"` // 相关文档编号\n\tProductId             *string `json:\"productId\"`             // 产品ID\n\tQuantity              *int    `json:\"quantity\"`              // 数量\n}\n", "req_update": "package req\n\n// UpdateInventoryTransactionReqDto 更新库存交易请求DTO\ntype UpdateInventoryTransactionReqDto struct {\n\tId                    *string `json:\"id\"`                     // ID\n\tType                  *string `json:\"type\"`                   // 交易类型\n\tWarehouseId           *string `json:\"warehouseId\"`           // 仓库ID\n\tHandlerId             *string `json:\"handlerId\"`             // 处理人ID\n\tTransactionTime       *int64  `json:\"transactionTime\"`       // 交易时间\n\tRelatedDocumentNumber *string `json:\"relatedDocumentNumber\"` // 相关文档编号\n\tProductId             *string `json:\"productId\"`             // 产品ID\n\tQuantity              *int    `json:\"quantity\"`              // 数量\n}\n", "req_delete": "package req\n\n// DeleteInventoryTransactionReqDto 删除库存交易请求DTO\ntype DeleteInventoryTransactionReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryInventoryTransactionReqDto 查询库存交易请求DTO\ntype QueryInventoryTransactionReqDto struct {\n\tId                    *string `json:\"id\"`                     // ID\n\tType                  *string `json:\"type\"`                   // 交易类型\n\tWarehouseId           *string `json:\"warehouseId\"`           // 仓库ID\n\tHandlerId             *string `json:\"handlerId\"`             // 处理人ID\n\tTransactionTime       *int64  `json:\"transactionTime\"`       // 交易时间\n\tRelatedDocumentNumber *string `json:\"relatedDocumentNumber\"` // 相关文档编号\n\tProductId             *string `json:\"productId\"`             // 产品ID\n\tQuantity              *int    `json:\"quantity\"`              // 数量\n\tPageNum               *int    `json:\"pageNum\"`               // 页码\n\tPageSize              *int    `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype InventoryTransactionTransfer struct {\n}\n\nfunc (transfer *InventoryTransactionTransfer) PoToVo(po po.InventoryTransaction) vo.InventoryTransactionVO {\n\tvo := vo.InventoryTransactionVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *InventoryTransactionTransfer) VoToPo(vo vo.InventoryTransactionVO) po.InventoryTransaction {\n\tpo := po.InventoryTransaction{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype InventoryTransactionService struct {\n}\n\nfunc (service *InventoryTransactionService) CreateInventoryTransaction(logCtx *gin.Context, transaction *po.InventoryTransaction) error {\n\treturn Save(transaction)\n}\n\nfunc (service *InventoryTransactionService) UpdateInventoryTransaction(logCtx *gin.Context, transaction *po.InventoryTransaction) error {\n\treturn Update(transaction)\n}\n\nfunc (service *InventoryTransactionService) DeleteInventoryTransaction(logCtx *gin.Context, id string) error {\n\treturn Delete(po.InventoryTransaction{Id: &id})\n}\n\nfunc (service *InventoryTransactionService) FindInventoryTransactionById(logCtx *gin.Context, id string) (transaction *po.InventoryTransaction, err error) {\n\ttransaction = &po.InventoryTransaction{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(transaction).Error\n\treturn\n}\n\nfunc (service *InventoryTransactionService) FindAllInventoryTransaction(logCtx *gin.Context, reqDto *req.QueryInventoryTransactionReqDto) (list *[]po.InventoryTransaction, err error) {\n\tdb := model.DBSlave.Self.Model(&po.InventoryTransaction{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.InventoryTransaction{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *InventoryTransactionService) FindAllInventoryTransactionWithPagination(logCtx *gin.Context, reqDto *req.QueryInventoryTransactionReqDto) (list *[]po.InventoryTransaction, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.InventoryTransaction{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.InventoryTransaction{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype InventoryTransactionController struct{}\n\nvar (\n\tinventoryTransactionService  = impl.InventoryTransactionService{}\n\tinventoryTransactionTransfer = transfer.InventoryTransactionTransfer{}\n)\n\n// @Summary 添加库存交易\n// @Description 添加库存交易\n// @Tags 库存交易\n// @Accept json\n// @Produce json\n// @Param body body req.AddInventoryTransactionReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.InventoryTransactionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-transaction/add [post]\nfunc (controller *InventoryTransactionController) AddInventoryTransaction(ctx *gin.Context) {\n\treqDto := req.AddInventoryTransactionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\ttransaction := po.InventoryTransaction{}\n\tif reqDto.Type != nil {\n\t\ttransaction.Type = reqDto.Type\n\t}\n\tif reqDto.WarehouseId != nil {\n\t\ttransaction.WarehouseId = reqDto.WarehouseId\n\t}\n\tif reqDto.HandlerId != nil {\n\t\ttransaction.HandlerId = reqDto.HandlerId\n\t}\n\tif reqDto.TransactionTime != nil {\n\t\ttransaction.TransactionTime = reqDto.TransactionTime\n\t}\n\tif reqDto.RelatedDocumentNumber != nil {\n\t\ttransaction.RelatedDocumentNumber = reqDto.RelatedDocumentNumber\n\t}\n\tif reqDto.ProductId != nil {\n\t\ttransaction.ProductId = reqDto.ProductId\n\t}\n\tif reqDto.Quantity != nil {\n\t\ttransaction.Quantity = reqDto.Quantity\n\t}\n\n\terr = inventoryTransactionService.CreateInventoryTransaction(ctx, &transaction)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, inventoryTransactionTransfer.PoToVo(transaction))\n}\n\n// @Summary 更新库存交易\n// @Description 更新库存交易\n// @Tags 库存交易\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateInventoryTransactionReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.InventoryTransactionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-transaction/update [post]\nfunc (controller *InventoryTransactionController) UpdateInventoryTransaction(ctx *gin.Context) {\n\treqDto := req.UpdateInventoryTransactionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\ttransaction, err := inventoryTransactionService.FindInventoryTransactionById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Type != nil {\n\t\ttransaction.Type = reqDto.Type\n\t}\n\tif reqDto.WarehouseId != nil {\n\t\ttransaction.WarehouseId = reqDto.WarehouseId\n\t}\n\tif reqDto.HandlerId != nil {\n\t\ttransaction.HandlerId = reqDto.HandlerId\n\t}\n\tif reqDto.TransactionTime != nil {\n\t\ttransaction.TransactionTime = reqDto.TransactionTime\n\t}\n\tif reqDto.RelatedDocumentNumber != nil {\n\t\ttransaction.RelatedDocumentNumber = reqDto.RelatedDocumentNumber\n\t}\n\tif reqDto.ProductId != nil {\n\t\ttransaction.ProductId = reqDto.ProductId\n\t}\n\tif reqDto.Quantity != nil {\n\t\ttransaction.Quantity = reqDto.Quantity\n\t}\n\n\terr = inventoryTransactionService.UpdateInventoryTransaction(ctx, transaction)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, inventoryTransactionTransfer.PoToVo(*transaction))\n}\n\n// @Summary 删除库存交易\n// @Description 删除库存交易\n// @Tags 库存交易\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteInventoryTransactionReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-transaction/delete [post]\nfunc (controller *InventoryTransactionController) DeleteInventoryTransaction(ctx *gin.Context) {\n\treqDto := req.DeleteInventoryTransactionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = inventoryTransactionService.DeleteInventoryTransaction(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询库存交易\n// @Description 查询库存交易\n// @Tags 库存交易\n// @Accept json\n// @Produce json\n// @Param body body req.QueryInventoryTransactionReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.InventoryTransactionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-transaction/query [post]\nfunc (controller *InventoryTransactionController) QueryInventoryTransactions(ctx *gin.Context) {\n\treqDto := req.QueryInventoryTransactionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := inventoryTransactionService.FindAllInventoryTransaction(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.InventoryTransactionVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, inventoryTransactionTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询库存交易列表\n// @Description 查询库存交易列表\n// @Tags 库存交易\n// @Accept json\n// @Produce json\n// @Param body body req.QueryInventoryTransactionReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.InventoryTransactionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/inventory-transaction/list [post]\nfunc (controller *InventoryTransactionController) ListInventoryTransactions(ctx *gin.Context) {\n\treqDto := req.QueryInventoryTransactionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := inventoryTransactionService.FindAllInventoryTransactionWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.InventoryTransactionVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.InventoryTransactionVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, inventoryTransactionTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype InventoryTransactionRoute struct {\n}\n\nfunc (s *InventoryTransactionRoute) InitInventoryTransactionRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tinventoryTransactionController := controller.InventoryTransactionController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/inventory-transaction/add\", inventoryTransactionController.AddInventoryTransaction)       // add\n\t\troute.POST(\"/api/inventory-transaction/update\", inventoryTransactionController.UpdateInventoryTransaction)   // update\n\t\troute.POST(\"/api/inventory-transaction/delete\", inventoryTransactionController.DeleteInventoryTransaction)   // delete\n\t\troute.POST(\"/api/inventory-transaction/query\", inventoryTransactionController.QueryInventoryTransactions)   // query\n\t\troute.POST(\"/api/inventory-transaction/list\", inventoryTransactionController.ListInventoryTransactions)     // list\n\t}\n}\n"}]