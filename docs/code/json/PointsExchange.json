[{"po": "package po\n\n// PointsExchange 积分兑换实体\ntype PointsExchange struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一ID\n\tRequiredPoints *int    `gorm:\"column:required_points;type:int;default:0\" json:\"requiredPoints\"` // 所需积分\n\tProductName    *string `gorm:\"column:product_name;type:varchar(255);default:''\" json:\"productName\"` // 产品名称\n\tQuantity       *int    `gorm:\"column:quantity;type:int;default:0\" json:\"quantity\"`           // 数量\n\tCtime          *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`               // 创建时间戳\n\tUtime          *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`               // 更新时间戳\n\tState          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`               // 状态值\n\tVersion        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`           // 版本号\n}\n\n// TableName 设置表名\nfunc (PointsExchange) TableName() string {\n\treturn \"points_exchange\"\n}\n\nfunc (p PointsExchange) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PointsExchangeVO 积分兑换值对象\ntype PointsExchangeVO struct {\n\tId             string `json:\"id\"`             // 唯一ID\n\tRequiredPoints int    `json:\"requiredPoints\"` // 所需积分\n\tProductName    string `json:\"productName\"`    // 产品名称\n\tQuantity       int    `json:\"quantity\"`       // 数量\n\tCtime          int64  `json:\"ctime\"`          // 创建时间戳\n\tUtime          int64  `json:\"utime\"`          // 更新时间戳\n\tState          int    `json:\"state\"`          // 状态值\n\tVersion        int    `json:\"version\"`        // 版本号\n}\n", "req_add": "package req\n\n// AddPointsExchangeReqDto 创建积分兑换请求DTO\ntype AddPointsExchangeReqDto struct {\n\tRequiredPoints *int    `json:\"requiredPoints\"` // 所需积分\n\tProductName    *string `json:\"productName\"`    // 产品名称\n\tQuantity       *int    `json:\"quantity\"`       // 数量\n}\n", "req_update": "package req\n\ntype UpdatePointsExchangeReqDto struct {\n\tId             *string `json:\"id\"`             // 唯一ID\n\tRequiredPoints *int    `json:\"requiredPoints\"` // 所需积分\n\tProductName    *string `json:\"productName\"`    // 产品名称\n\tQuantity       *int    `json:\"quantity\"`       // 数量\n}\n", "req_delete": "package req\n\ntype DeletePointsExchangeReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}\n", "req_query": "package req\n\ntype QueryPointsExchangeReqDto struct {\n\tId             *string `json:\"id\"`             // 唯一ID\n\tRequiredPoints *int    `json:\"requiredPoints\"` // 所需积分\n\tProductName    *string `json:\"productName\"`    // 产品名称\n\tQuantity       *int    `json:\"quantity\"`       // 数量\n\tPageNum        *int    `json:\"pageNum\"`        // 页码\n\tPageSize       *int    `json:\"pageSize\"`       // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PointsExchangeTransfer struct {\n}\n\nfunc (transfer *PointsExchangeTransfer) PoToVo(po po.PointsExchange) vo.PointsExchangeVO {\n\tvo := vo.PointsExchangeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PointsExchangeTransfer) VoToPo(vo vo.PointsExchangeVO) po.PointsExchange {\n\tpo := po.PointsExchange{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PointsExchangeService struct {\n}\n\nfunc (service *PointsExchangeService) CreatePointsExchange(logCtx *gin.Context, pointsExchange *po.PointsExchange) error {\n\treturn Save(pointsExchange)\n}\n\nfunc (service *PointsExchangeService) UpdatePointsExchange(logCtx *gin.Context, pointsExchange *po.PointsExchange) error {\n\treturn Update(pointsExchange)\n}\n\nfunc (service *PointsExchangeService) DeletePointsExchange(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PointsExchange{Id: &id})\n}\n\nfunc (service *PointsExchangeService) FindPointsExchangeById(logCtx *gin.Context, id string) (pointsExchange *po.PointsExchange, err error) {\n\tpointsExchange = &po.PointsExchange{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(pointsExchange).Error\n\treturn\n}\n\nfunc (service *PointsExchangeService) FindAllPointsExchange(logCtx *gin.Context, reqDto *req.QueryPointsExchangeReqDto) (list *[]po.PointsExchange, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PointsExchange{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.RequiredPoints != nil {\n\t\tdb = db.Where(\"required_points=?\", *reqDto.RequiredPoints)\n\t}\n\tif reqDto.ProductName != nil && *reqDto.ProductName != \"\" {\n\t\tdb = db.Where(\"product_name=?\", *reqDto.ProductName)\n\t}\n\tif reqDto.Quantity != nil {\n\t\tdb = db.Where(\"quantity=?\", *reqDto.Quantity)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PointsExchange{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PointsExchangeService) FindAllPointsExchangeWithPagination(logCtx *gin.Context, reqDto *req.QueryPointsExchangeReqDto) (list *[]po.PointsExchange, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PointsExchange{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.RequiredPoints != nil {\n\t\tdb = db.Where(\"required_points=?\", *reqDto.RequiredPoints)\n\t}\n\tif reqDto.ProductName != nil && *reqDto.ProductName != \"\" {\n\t\tdb = db.Where(\"product_name=?\", *reqDto.ProductName)\n\t}\n\tif reqDto.Quantity != nil {\n\t\tdb = db.Where(\"quantity=?\", *reqDto.Quantity)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PointsExchange{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PointsExchangeController struct{}\n\nvar (\n\tpointsExchangeService  = impl.PointsExchangeService{}\n\tpointsExchangeTransfer = transfer.PointsExchangeTransfer{}\n)\n\n// @Summary 添加积分兑换\n// @Description 添加积分兑换\n// @Tags 积分兑换\n// @Accept json\n// @Produce json\n// @Param body body req.AddPointsExchangeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PointsExchangeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/points-exchange/add [post]\nfunc (controller *PointsExchangeController) AddPointsExchange(ctx *gin.Context) {\n\treqDto := req.AddPointsExchangeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpointsExchange := po.PointsExchange{}\n\tif reqDto.RequiredPoints != nil {\n\t\tpointsExchange.RequiredPoints = reqDto.RequiredPoints\n\t}\n\tif reqDto.ProductName != nil {\n\t\tpointsExchange.ProductName = reqDto.ProductName\n\t}\n\tif reqDto.Quantity != nil {\n\t\tpointsExchange.Quantity = reqDto.Quantity\n\t}\n\n\terr = pointsExchangeService.CreatePointsExchange(ctx, &pointsExchange)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, pointsExchangeTransfer.PoToVo(pointsExchange))\n}\n\n// @Summary 更新积分兑换\n// @Description 更新积分兑换\n// @Tags 积分兑换\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePointsExchangeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PointsExchangeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/points-exchange/update [post]\nfunc (controller *PointsExchangeController) UpdatePointsExchange(ctx *gin.Context) {\n\treqDto := req.UpdatePointsExchangeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tpointsExchange, err := pointsExchangeService.FindPointsExchangeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.RequiredPoints != nil {\n\t\tpointsExchange.RequiredPoints = reqDto.RequiredPoints\n\t}\n\tif reqDto.ProductName != nil {\n\t\tpointsExchange.ProductName = reqDto.ProductName\n\t}\n\tif reqDto.Quantity != nil {\n\t\tpointsExchange.Quantity = reqDto.Quantity\n\t}\n\n\terr = pointsExchangeService.UpdatePointsExchange(ctx, pointsExchange)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, pointsExchangeTransfer.PoToVo(*pointsExchange))\n}\n\n// @Summary 删除积分兑换\n// @Description 删除积分兑换\n// @Tags 积分兑换\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePointsExchangeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/points-exchange/delete [post]\nfunc (controller *PointsExchangeController) DeletePointsExchange(ctx *gin.Context) {\n\treqDto := req.DeletePointsExchangeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = pointsExchangeService.DeletePointsExchange(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询积分兑换\n// @Description 查询积分兑换\n// @Tags 积分兑换\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPointsExchangeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PointsExchangeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/points-exchange/query [post]\nfunc (controller *PointsExchangeController) QueryPointsExchanges(ctx *gin.Context) {\n\treqDto := req.QueryPointsExchangeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := pointsExchangeService.FindAllPointsExchange(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.PointsExchangeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, pointsExchangeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询积分兑换列表\n// @Description 查询积分兑换列表\n// @Tags 积分兑换\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPointsExchangeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PointsExchangeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/points-exchange/list [post]\nfunc (controller *PointsExchangeController) ListPointsExchanges(ctx *gin.Context) {\n\treqDto := req.QueryPointsExchangeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := pointsExchangeService.FindAllPointsExchangeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.PointsExchangeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PointsExchangeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, pointsExchangeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PointsExchangeRoute struct {\n}\n\nfunc (s *PointsExchangeRoute) InitPointsExchangeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tpointsExchangeController := controller.PointsExchangeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/points-exchange/add\", pointsExchangeController.AddPointsExchange)       // add\n\t\troute.POST(\"/api/points-exchange/update\", pointsExchangeController.UpdatePointsExchange) // update\n\t\troute.POST(\"/api/points-exchange/delete\", pointsExchangeController.DeletePointsExchange) // delete\n\t\troute.POST(\"/api/points-exchange/query\", pointsExchangeController.QueryPointsExchanges)  // query\n\t\troute.POST(\"/api/points-exchange/list\", pointsExchangeController.ListPointsExchanges)    // list\n\t}\n}\n"}]