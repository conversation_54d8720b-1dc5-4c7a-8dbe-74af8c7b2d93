[{"po": "package po\n\n// MobileOrder 手机订单实体\ntype MobileOrder struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tOrderId    *string `gorm:\"column:order_id;type:varchar(64);default:''\" json:\"orderId\"`    // 订单ID\n\tOrderTime  *int64  `gorm:\"column:order_time;type:int;default:0\" json:\"orderTime\"`      // 下单时间\n\tStatus     *string `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`     // 状态\n\tCtime      *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`               // 创建时间戳\n\tUtime      *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`               // 更新时间戳\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`               // 状态值\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`           // 版本号\n}\n\n// TableName 设置表名\nfunc (MobileOrder) TableName() string {\n\treturn \"mobile_order\"\n}\n\nfunc (m MobileOrder) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MobileOrderVO 手机订单信息值对象\ntype MobileOrderVO struct {\n\tId        string `json:\"id\"`        // ID\n\tOrderId    string `json:\"orderId\"`    // 订单ID\n\tOrderTime  int64  `json:\"orderTime\"`  // 下单时间\n\tStatus     string `json:\"status\"`     // 状态\n\tCtime      int64  `json:\"ctime\"`      // 创建时间戳\n\tUtime      int64  `json:\"utime\"`      // 更新时间戳\n\tState      int    `json:\"state\"`      // 状态值\n\tVersion    int    `json:\"version\"`    // 版本号\n}\n", "req_add": "package req\n\n// AddMobileOrderReqDto 创建手机订单请求DTO\ntype AddMobileOrderReqDto struct {\n\tOrderId    *string `json:\"orderId\"`    // 订单ID\n\tOrderTime  *int64  `json:\"orderTime\"`  // 下单时间\n\tStatus     *string `json:\"status\"`     // 状态\n}\n", "req_update": "package req\n\ntype UpdateMobileOrderReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tOrderId    *string `json:\"orderId\"`    // 订单ID\n\tOrderTime  *int64  `json:\"orderTime\"`  // 下单时间\n\tStatus     *string `json:\"status\"`     // 状态\n}\n", "req_delete": "package req\n\ntype DeleteMobileOrderReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryMobileOrderReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tOrderId    *string `json:\"orderId\"`    // 订单ID\n\tOrderTime  *int64  `json:\"orderTime\"`  // 下单时间\n\tStatus     *string `json:\"status\"`     // 状态\n\tPageNum    *int    `json:\"pageNum\"`    // 页码\n\tPageSize   *int    `json:\"pageSize\"`   // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MobileOrderTransfer struct {\n}\n\nfunc (transfer *MobileOrderTransfer) PoToVo(po po.MobileOrder) vo.MobileOrderVO {\n\tvo := vo.MobileOrderVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MobileOrderTransfer) VoToPo(vo vo.MobileOrderVO) po.MobileOrder {\n\tpo := po.MobileOrder{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MobileOrderService struct {\n}\n\nfunc (service *MobileOrderService) CreateMobileOrder(logCtx *gin.Context, mobileOrder *po.MobileOrder) error {\n\treturn Save(mobileOrder)\n}\n\nfunc (service *MobileOrderService) UpdateMobileOrder(logCtx *gin.Context, mobileOrder *po.MobileOrder) error {\n\treturn Update(mobileOrder)\n}\n\nfunc (service *MobileOrderService) DeleteMobileOrder(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MobileOrder{Id: &id})\n}\n\nfunc (service *MobileOrderService) FindMobileOrderById(logCtx *gin.Context, id string) (mobileOrder *po.MobileOrder, err error) {\n\tmobileOrder = &po.MobileOrder{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(mobileOrder).Error\n\treturn\n}\n\nfunc (service *MobileOrderService) FindAllMobileOrder(logCtx *gin.Context, reqDto *req.QueryMobileOrderReqDto) (list *[]po.MobileOrder, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MobileOrder{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MobileOrder{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MobileOrderService) FindAllMobileOrderWithPagination(logCtx *gin.Context, reqDto *req.QueryMobileOrderReqDto) (list *[]po.MobileOrder, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MobileOrder{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MobileOrder{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MobileOrderController struct{}\n\nvar (\n\tmobileOrderService  = impl.MobileOrderService{}\n\tmobileOrderTransfer = transfer.MobileOrderTransfer{}\n)\n\n// @Summary 添加手机订单\n// @Description 添加手机订单\n// @Tags 手机订单\n// @Accept json\n// @Produce json\n// @Param body body req.AddMobileOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MobileOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/mobile-order/add [post]\nfunc (controller *MobileOrderController) AddMobileOrder(ctx *gin.Context) {\n\treqDto := req.AddMobileOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmobileOrder := po.MobileOrder{}\n\tif reqDto.OrderId != nil {\n\t\tmobileOrder.OrderId = reqDto.OrderId\n\t}\n\tif reqDto.OrderTime != nil {\n\t\tmobileOrder.OrderTime = reqDto.OrderTime\n\t}\n\tif reqDto.Status != nil {\n\t\tmobileOrder.Status = reqDto.Status\n\t}\n\terr = mobileOrderService.CreateMobileOrder(ctx, &mobileOrder)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, mobileOrderTransfer.PoToVo(mobileOrder))\n}\n\n// @Summary 更新手机订单\n// @Description 更新手机订单\n// @Tags 手机订单\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMobileOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MobileOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/mobile-order/update [post]\nfunc (controller *MobileOrderController) UpdateMobileOrder(ctx *gin.Context) {\n\treqDto := req.UpdateMobileOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmobileOrder, err := mobileOrderService.FindMobileOrderById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.OrderId != nil {\n\t\tmobileOrder.OrderId = reqDto.OrderId\n\t}\n\tif reqDto.OrderTime != nil {\n\t\tmobileOrder.OrderTime = reqDto.OrderTime\n\t}\n\tif reqDto.Status != nil {\n\t\tmobileOrder.Status = reqDto.Status\n\t}\n\terr = mobileOrderService.UpdateMobileOrder(ctx, mobileOrder)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, mobileOrderTransfer.PoToVo(*mobileOrder))\n}\n\n// @Summary 删除手机订单\n// @Description 删除手机订单\n// @Tags 手机订单\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMobileOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/mobile-order/delete [post]\nfunc (controller *MobileOrderController) DeleteMobileOrder(ctx *gin.Context) {\n\treqDto := req.DeleteMobileOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = mobileOrderService.DeleteMobileOrder(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询手机订单\n// @Description 查询手机订单\n// @Tags 手机订单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMobileOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MobileOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/mobile-order/query [post]\nfunc (controller *MobileOrderController) QueryMobileOrders(ctx *gin.Context) {\n\treqDto := req.QueryMobileOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := mobileOrderService.FindAllMobileOrder(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MobileOrderVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, mobileOrderTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询手机订单列表\n// @Description 查询手机订单列表\n// @Tags 手机订单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMobileOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MobileOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/mobile-order/list [post]\nfunc (a *MobileOrderController) ListMobileOrders(ctx *gin.Context) {\n\treqDto := req.QueryMobileOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := mobileOrderService.FindAllMobileOrderWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MobileOrderVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MobileOrderVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, mobileOrderTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MobileOrderRoute struct {\n}\n\nfunc (s *MobileOrderRoute) InitMobileOrderRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmobileOrderController := controller.MobileOrderController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/mobile-order/add\", mobileOrderController.AddMobileOrder)       //add\n\t\troute.POST(\"/api/mobile-order/update\", mobileOrderController.UpdateMobileOrder) //update\n\t\troute.POST(\"/api/mobile-order/delete\", mobileOrderController.DeleteMobileOrder) //delete\n\t\troute.POST(\"/api/mobile-order/query\", mobileOrderController.QueryMobileOrders)  //query\n\t\troute.POST(\"/api/mobile-order/list\", mobileOrderController.ListMobileOrders)    //list\n\t}\n}\n"}]