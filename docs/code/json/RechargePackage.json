[{"po": "package po\n\n// RechargePackage 充值套餐实体\ntype RechargePackage struct {\n\tId                   *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // 唯一id\n\tAmountType           *string  `gorm:\"column:amount_type;type:varchar(64);default:''\" json:\"amountType\"`           // 金额类型\n\tAmount               *int64   `gorm:\"column:amount;type:bigint;default:0\" json:\"amount\"`                         // 金额\n\tBonusAmount          *int64   `gorm:\"column:bonus_amount;type:bigint;default:0\" json:\"bonusAmount\"`             // 赠送金额\n\tDistributionChannels *string  `gorm:\"column:distribution_channels;type:varchar(64);default:''\" json:\"distributionChannels\"` // 分销渠道\n\tBonusBeverages       *string  `gorm:\"column:bonus_beverages;type:varchar(64);default:''\" json:\"bonusBeverages\"`    // 赠送酒水\n\tIntervalBonusAmount  *string  `gorm:\"column:interval_bonus_amount;type:varchar(64);default:''\" json:\"intervalBonusAmount\"` // 间隔赠送金额\n\tPercentageBonusAmount *float32 `gorm:\"column:percentage_bonus_amount;type:float;default:0\" json:\"percentageBonusAmount\"` // 百分比赠送金额\n\tRemark               *string  `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`                  // 备注\n\tCtime                *int64   `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                         // 创建时间\n\tUtime                *int64   `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                         // 更新时间\n\tState                *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态\n\tVersion              *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本\n}\n\n// TableName 设置表名\nfunc (RechargePackage) TableName() string {\n\treturn \"recharge_package\"\n}\n\nfunc (r RechargePackage) GetId() string {\n\treturn *r.Id\n}", "vo": "package vo\n\n// RechargePackageVO 充值套餐值对象\ntype RechargePackageVO struct {\n\tId                   string  `json:\"id\"`                    // 唯一id\n\tAmountType           string  `json:\"amountType\"`           // 金额类型\n\tAmount               int64   `json:\"amount\"`               // 金额\n\tBonusAmount          int64   `json:\"bonusAmount\"`          // 赠送金额\n\tDistributionChannels string  `json:\"distributionChannels\"` // 分销渠道\n\tBonusBeverages       string  `json:\"bonusBeverages\"`       // 赠送酒水\n\tIntervalBonusAmount  string  `json:\"intervalBonusAmount\"`  // 间隔赠送金额\n\tPercentageBonusAmount float32 `json:\"percentageBonusAmount\"` // 百分比赠送金额\n\tRemark               string  `json:\"remark\"`               // 备注\n\tCtime                int64   `json:\"ctime\"`                // 创建时间\n\tUtime                int64   `json:\"utime\"`                // 更新时间\n\tState                int     `json:\"state\"`                // 状态\n\tVersion              int     `json:\"version\"`              // 版本\n}", "req_add": "package req\n\n// AddRechargePackageReqDto 创建充值套餐请求DTO\ntype AddRechargePackageReqDto struct {\n\tAmountType           *string  `json:\"amountType\"`           // 金额类型\n\tAmount               *int64   `json:\"amount\"`               // 金额\n\tBonusAmount          *int64   `json:\"bonusAmount\"`          // 赠送金额\n\tDistributionChannels *string  `json:\"distributionChannels\"` // 分销渠道\n\tBonusBeverages       *string  `json:\"bonusBeverages\"`       // 赠送酒水\n\tIntervalBonusAmount  *string  `json:\"intervalBonusAmount\"`  // 间隔赠送金额\n\tPercentageBonusAmount *float32 `json:\"percentageBonusAmount\"` // 百分比赠送金额\n\tRemark               *string  `json:\"remark\"`               // 备注\n}", "req_update": "package req\n\n// UpdateRechargePackageReqDto 更新充值套餐请求DTO\ntype UpdateRechargePackageReqDto struct {\n\tId                   *string  `json:\"id\"`                    // 唯一id\n\tAmountType           *string  `json:\"amountType\"`           // 金额类型\n\tAmount               *int64   `json:\"amount\"`               // 金额\n\tBonusAmount          *int64   `json:\"bonusAmount\"`          // 赠送金额\n\tDistributionChannels *string  `json:\"distributionChannels\"` // 分销渠道\n\tBonusBeverages       *string  `json:\"bonusBeverages\"`       // 赠送酒水\n\tIntervalBonusAmount  *string  `json:\"intervalBonusAmount\"`  // 间隔赠送金额\n\tPercentageBonusAmount *float32 `json:\"percentageBonusAmount\"` // 百分比赠送金额\n\tRemark               *string  `json:\"remark\"`               // 备注\n}", "req_delete": "package req\n\n// DeleteRechargePackageReqDto 删除充值套餐请求DTO\ntype DeleteRechargePackageReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}", "req_query": "package req\n\n// QueryRechargePackageReqDto 查询充值套餐请求DTO\ntype QueryRechargePackageReqDto struct {\n\tId                   *string  `json:\"id\"`                    // 唯一id\n\tAmountType           *string  `json:\"amountType\"`           // 金额类型\n\tAmount               *int64   `json:\"amount\"`               // 金额\n\tBonusAmount          *int64   `json:\"bonusAmount\"`          // 赠送金额\n\tDistributionChannels *string  `json:\"distributionChannels\"` // 分销渠道\n\tBonusBeverages       *string  `json:\"bonusBeverages\"`       // 赠送酒水\n\tIntervalBonusAmount  *string  `json:\"intervalBonusAmount\"`  // 间隔赠送金额\n\tPercentageBonusAmount *float32 `json:\"percentageBonusAmount\"` // 百分比赠送金额\n\tRemark               *string  `json:\"remark\"`               // 备注\n\tPageNum              *int     `json:\"pageNum\"`              // 页码\n\tPageSize             *int     `json:\"pageSize\"`             // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RechargePackageTransfer struct {\n}\n\nfunc (transfer *RechargePackageTransfer) PoToVo(po po.RechargePackage) vo.RechargePackageVO {\n\tvo := vo.RechargePackageVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RechargePackageTransfer) VoToPo(vo vo.RechargePackageVO) po.RechargePackage {\n\tpo := po.RechargePackage{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RechargePackageService struct {\n}\n\nfunc (service *RechargePackageService) CreateRechargePackage(logCtx *gin.Context, rechargePackage *po.RechargePackage) error {\n\treturn Save(rechargePackage)\n}\n\nfunc (service *RechargePackageService) UpdateRechargePackage(logCtx *gin.Context, rechargePackage *po.RechargePackage) error {\n\treturn Update(rechargePackage)\n}\n\nfunc (service *RechargePackageService) DeleteRechargePackage(logCtx *gin.Context, id string) error {\n\treturn Delete(po.RechargePackage{Id: &id})\n}\n\nfunc (service *RechargePackageService) FindRechargePackageById(logCtx *gin.Context, id string) (rechargePackage *po.RechargePackage, err error) {\n\trechargePackage = &po.RechargePackage{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(rechargePackage).Error\n\treturn\n}\n\nfunc (service *RechargePackageService) FindAllRechargePackage(logCtx *gin.Context, reqDto *req.QueryRechargePackageReqDto) (list *[]po.RechargePackage, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RechargePackage{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.AmountType != nil && *reqDto.AmountType != \"\" {\n\t\tdb = db.Where(\"amount_type=?\", *reqDto.AmountType)\n\t}\n\tif reqDto.Amount != nil {\n\t\tdb = db.Where(\"amount=?\", *reqDto.Amount)\n\t}\n\tif reqDto.BonusAmount != nil {\n\t\tdb = db.Where(\"bonus_amount=?\", *reqDto.BonusAmount)\n\t}\n\tif reqDto.DistributionChannels != nil && *reqDto.DistributionChannels != \"\" {\n\t\tdb = db.Where(\"distribution_channels=?\", *reqDto.DistributionChannels)\n\t}\n\tif reqDto.BonusBeverages != nil && *reqDto.BonusBeverages != \"\" {\n\t\tdb = db.Where(\"bonus_beverages=?\", *reqDto.BonusBeverages)\n\t}\n\tif reqDto.IntervalBonusAmount != nil && *reqDto.IntervalBonusAmount != \"\" {\n\t\tdb = db.Where(\"interval_bonus_amount=?\", *reqDto.IntervalBonusAmount)\n\t}\n\tif reqDto.PercentageBonusAmount != nil {\n\t\tdb = db.Where(\"percentage_bonus_amount=?\", *reqDto.PercentageBonusAmount)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark=?\", *reqDto.Remark)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.RechargePackage{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RechargePackageService) FindAllRechargePackageWithPagination(logCtx *gin.Context, reqDto *req.QueryRechargePackageReqDto) (list *[]po.RechargePackage, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RechargePackage{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.AmountType != nil && *reqDto.AmountType != \"\" {\n\t\tdb = db.Where(\"amount_type=?\", *reqDto.AmountType)\n\t}\n\tif reqDto.Amount != nil {\n\t\tdb = db.Where(\"amount=?\", *reqDto.Amount)\n\t}\n\tif reqDto.BonusAmount != nil {\n\t\tdb = db.Where(\"bonus_amount=?\", *reqDto.BonusAmount)\n\t}\n\tif reqDto.DistributionChannels != nil && *reqDto.DistributionChannels != \"\" {\n\t\tdb = db.Where(\"distribution_channels=?\", *reqDto.DistributionChannels)\n\t}\n\tif reqDto.BonusBeverages != nil && *reqDto.BonusBeverages != \"\" {\n\t\tdb = db.Where(\"bonus_beverages=?\", *reqDto.BonusBeverages)\n\t}\n\tif reqDto.IntervalBonusAmount != nil && *reqDto.IntervalBonusAmount != \"\" {\n\t\tdb = db.Where(\"interval_bonus_amount=?\", *reqDto.IntervalBonusAmount)\n\t}\n\tif reqDto.PercentageBonusAmount != nil {\n\t\tdb = db.Where(\"percentage_bonus_amount=?\", *reqDto.PercentageBonusAmount)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark=?\", *reqDto.Remark)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.RechargePackage{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RechargePackageController struct{}\n\nvar (\n\trechargePackageService  = impl.RechargePackageService{}\n\trechargePackageTransfer = transfer.RechargePackageTransfer{}\n)\n\n// @Summary 添加充值套餐\n// @Description 添加充值套餐\n// @Tags 充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.AddRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RechargePackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/rechargePackage/add [post]\nfunc (controller *RechargePackageController) AddRechargePackage(ctx *gin.Context) {\n\treqDto := req.AddRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\trechargePackage := po.RechargePackage{}\n\tif reqDto.AmountType != nil {\n\t\trechargePackage.AmountType = reqDto.AmountType\n\t}\n\tif reqDto.Amount != nil {\n\t\trechargePackage.Amount = reqDto.Amount\n\t}\n\tif reqDto.BonusAmount != nil {\n\t\trechargePackage.BonusAmount = reqDto.BonusAmount\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\trechargePackage.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.BonusBeverages != nil {\n\t\trechargePackage.BonusBeverages = reqDto.BonusBeverages\n\t}\n\tif reqDto.IntervalBonusAmount != nil {\n\t\trechargePackage.IntervalBonusAmount = reqDto.IntervalBonusAmount\n\t}\n\tif reqDto.PercentageBonusAmount != nil {\n\t\trechargePackage.PercentageBonusAmount = reqDto.PercentageBonusAmount\n\t}\n\tif reqDto.Remark != nil {\n\t\trechargePackage.Remark = reqDto.Remark\n\t}\n\n\terr = rechargePackageService.CreateRechargePackage(ctx, &rechargePackage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, rechargePackageTransfer.PoToVo(rechargePackage))\n}\n\n// @Summary 更新充值套餐\n// @Description 更新充值套餐\n// @Tags 充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RechargePackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/rechargePackage/update [post]\nfunc (controller *RechargePackageController) UpdateRechargePackage(ctx *gin.Context) {\n\treqDto := req.UpdateRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\trechargePackage, err := rechargePackageService.FindRechargePackageById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.AmountType != nil {\n\t\trechargePackage.AmountType = reqDto.AmountType\n\t}\n\tif reqDto.Amount != nil {\n\t\trechargePackage.Amount = reqDto.Amount\n\t}\n\tif reqDto.BonusAmount != nil {\n\t\trechargePackage.BonusAmount = reqDto.BonusAmount\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\trechargePackage.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.BonusBeverages != nil {\n\t\trechargePackage.BonusBeverages = reqDto.BonusBeverages\n\t}\n\tif reqDto.IntervalBonusAmount != nil {\n\t\trechargePackage.IntervalBonusAmount = reqDto.IntervalBonusAmount\n\t}\n\tif reqDto.PercentageBonusAmount != nil {\n\t\trechargePackage.PercentageBonusAmount = reqDto.PercentageBonusAmount\n\t}\n\tif reqDto.Remark != nil {\n\t\trechargePackage.Remark = reqDto.Remark\n\t}\n\n\terr = rechargePackageService.UpdateRechargePackage(ctx, rechargePackage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, rechargePackageTransfer.PoToVo(*rechargePackage))\n}\n\n// @Summary 删除充值套餐\n// @Description 删除充值套餐\n// @Tags 充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/rechargePackage/delete [post]\nfunc (controller *RechargePackageController) DeleteRechargePackage(ctx *gin.Context) {\n\treqDto := req.DeleteRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = rechargePackageService.DeleteRechargePackage(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询充值套餐\n// @Description 查询充值套餐\n// @Tags 充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RechargePackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/rechargePackage/query [post]\nfunc (controller *RechargePackageController) QueryRechargePackages(ctx *gin.Context) {\n\treqDto := req.QueryRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := rechargePackageService.FindAllRechargePackage(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.RechargePackageVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, rechargePackageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询充值套餐列表\n// @Description 查询充值套餐列表\n// @Tags 充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RechargePackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/rechargePackage/list [post]\nfunc (controller *RechargePackageController) ListRechargePackages(ctx *gin.Context) {\n\treqDto := req.QueryRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := rechargePackageService.FindAllRechargePackageWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.RechargePackageVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RechargePackageVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, rechargePackageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RechargePackageRoute struct {\n}\n\nfunc (s *RechargePackageRoute) InitRechargePackageRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\trechargePackageController := controller.RechargePackageController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/rechargePackage/add\", rechargePackageController.AddRechargePackage)       // add\n\t\troute.POST(\"/api/rechargePackage/update\", rechargePackageController.UpdateRechargePackage) // update\n\t\troute.POST(\"/api/rechargePackage/delete\", rechargePackageController.DeleteRechargePackage) // delete\n\t\troute.POST(\"/api/rechargePackage/query\", rechargePackageController.QueryRechargePackages)  // query\n\t\troute.POST(\"/api/rechargePackage/list\", rechargePackageController.ListRechargePackages)   // list\n\t}\n}"}]