[{"po": "package po\n\n// MemberTransferMoney 会员转账记录实体\ntype MemberTransferMoney struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tVenueId             *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`                    // 门店ID\n\tTransferTime        *int64  `gorm:\"column:transfer_time;type:bigint;default:0\" json:\"transferTime\"`               // 转账时间\n\tFromMemberId        *string `gorm:\"column:from_member_id;type:varchar(64);default:''\" json:\"fromMemberId\"`         // 转出会员ID\n\tFromMemberCardNumber *string `gorm:\"column:from_member_card_number;type:varchar(64);default:''\" json:\"fromMemberCardNumber\"` // 转出会员卡号\n\tToMemberId          *string `gorm:\"column:to_member_id;type:varchar(64);default:''\" json:\"toMemberId\"`             // 转入会员ID\n\tToMemberCardNumber   *string `gorm:\"column:to_member_card_number;type:varchar(64);default:''\" json:\"toMemberCardNumber\"`   // 转入会员卡号\n\tAmount              *int64  `gorm:\"column:amount;type:bigint;default:0\" json:\"amount\"`                           // 转账金额\n\tPoints              *int64  `gorm:\"column:points;type:bigint;default:0\" json:\"points\"`                           // 转账积分\n\tStatus              *string `gorm:\"column:status;type:varchar(32);default:''\" json:\"status\"`                      // 转账状态\n\tRemark              *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`                     // 备注\n\tCtime               *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                            // 创建时间\n\tUtime               *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                            // 更新时间\n\tState               *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                               // 状态\n\tVersion             *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                           // 版本号\n}\n\n// TableName 设置表名\nfunc (MemberTransferMoney) TableName() string {\n\treturn \"member_transfer_money\"\n}\n\nfunc (m MemberTransferMoney) GetId() string {\n\treturn *m.Id\n}", "vo": "package vo\n\n// MemberTransferMoneyVO 会员转账记录值对象\ntype MemberTransferMoneyVO struct {\n\tId                  string `json:\"id\"`                    // ID\n\tVenueId             string `json:\"venueId\"`              // 门店ID\n\tTransferTime        int64  `json:\"transferTime\"`         // 转账时间\n\tFromMemberId        string `json:\"fromMemberId\"`         // 转出会员ID\n\tFromMemberCardNumber string `json:\"fromMemberCardNumber\"` // 转出会员卡号\n\tToMemberId          string `json:\"toMemberId\"`           // 转入会员ID\n\tToMemberCardNumber   string `json:\"toMemberCardNumber\"`   // 转入会员卡号\n\tAmount              int64  `json:\"amount\"`               // 转账金额\n\tPoints              int64  `json:\"points\"`               // 转账积分\n\tStatus              string `json:\"status\"`               // 转账状态\n\tRemark              string `json:\"remark\"`               // 备注\n\tCtime               int64  `json:\"ctime\"`                // 创建时间\n\tUtime               int64  `json:\"utime\"`                // 更新时间\n\tState               int    `json:\"state\"`                // 状态\n\tVersion             int    `json:\"version\"`              // 版本号\n}", "req_add": "package req\n\n// AddMemberTransferMoneyReqDto 创建会员转账记录请求DTO\ntype AddMemberTransferMoneyReqDto struct {\n\tVenueId             *string `json:\"venueId\"`              // 门店ID\n\tTransferTime        *int64  `json:\"transferTime\"`         // 转账时间\n\tFromMemberId        *string `json:\"fromMemberId\"`         // 转出会员ID\n\tFromMemberCardNumber *string `json:\"fromMemberCardNumber\"` // 转出会员卡号\n\tToMemberId          *string `json:\"toMemberId\"`           // 转入会员ID\n\tToMemberCardNumber   *string `json:\"toMemberCardNumber\"`   // 转入会员卡号\n\tAmount              *int64  `json:\"amount\"`               // 转账金额\n\tPoints              *int64  `json:\"points\"`               // 转账积分\n\tStatus              *string `json:\"status\"`               // 转账状态\n\tRemark              *string `json:\"remark\"`               // 备注\n}", "req_update": "package req\n\n// UpdateMemberTransferMoneyReqDto 更新会员转账记录请求DTO\ntype UpdateMemberTransferMoneyReqDto struct {\n\tId                  *string `json:\"id\"`                    // ID\n\tVenueId             *string `json:\"venueId\"`              // 门店ID\n\tTransferTime        *int64  `json:\"transferTime\"`         // 转账时间\n\tFromMemberId        *string `json:\"fromMemberId\"`         // 转出会员ID\n\tFromMemberCardNumber *string `json:\"fromMemberCardNumber\"` // 转出会员卡号\n\tToMemberId          *string `json:\"toMemberId\"`           // 转入会员ID\n\tToMemberCardNumber   *string `json:\"toMemberCardNumber\"`   // 转入会员卡号\n\tAmount              *int64  `json:\"amount\"`               // 转账金额\n\tPoints              *int64  `json:\"points\"`               // 转账积分\n\tStatus              *string `json:\"status\"`               // 转账状态\n\tRemark              *string `json:\"remark\"`               // 备注\n}", "req_delete": "package req\n\n// DeleteMemberTransferMoneyReqDto 删除会员转账记录请求DTO\ntype DeleteMemberTransferMoneyReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryMemberTransferMoneyReqDto 查询会员转账记录请求DTO\ntype QueryMemberTransferMoneyReqDto struct {\n\tId                  *string `json:\"id\"`                    // ID\n\tVenueId             *string `json:\"venueId\"`              // 门店ID\n\tTransferTime        *int64  `json:\"transferTime\"`         // 转账时间\n\tFromMemberId        *string `json:\"fromMemberId\"`         // 转出会员ID\n\tFromMemberCardNumber *string `json:\"fromMemberCardNumber\"` // 转出会员卡号\n\tToMemberId          *string `json:\"toMemberId\"`           // 转入会员ID\n\tToMemberCardNumber   *string `json:\"toMemberCardNumber\"`   // 转入会员卡号\n\tAmount              *int64  `json:\"amount\"`               // 转账金额\n\tPoints              *int64  `json:\"points\"`               // 转账积分\n\tStatus              *string `json:\"status\"`               // 转账状态\n\tPageNum             *int    `json:\"pageNum\"`              // 页码\n\tPageSize            *int    `json:\"pageSize\"`             // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberTransferMoneyTransfer struct {\n}\n\nfunc (transfer *MemberTransferMoneyTransfer) PoToVo(po po.MemberTransferMoney) vo.MemberTransferMoneyVO {\n\tvo := vo.MemberTransferMoneyVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MemberTransferMoneyTransfer) VoToPo(vo vo.MemberTransferMoneyVO) po.MemberTransferMoney {\n\tpo := po.MemberTransferMoney{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype MemberTransferMoneyService struct {\n}\n\nvar (\n\tmemberTransferMoneyService = MemberTransferMoneyService{}\n\tmemberTransferMoneyTransfer = transfer.MemberTransferMoneyTransfer{}\n)\n\nfunc (service *MemberTransferMoneyService) CreateMemberTransferMoney(logCtx *gin.Context, memberTransferMoney *po.MemberTransferMoney) error {\n\treturn Save(memberTransferMoney)\n}\n\nfunc (service *MemberTransferMoneyService) CreateMemberTransferMoneyWithTx(logCtx *gin.Context, memberTransferMoney *po.MemberTransferMoney, tx *gorm.DB) error {\n\treturn SaveWithTx(memberTransferMoney, tx)\n}\n\nfunc (service *MemberTransferMoneyService) UpdateMemberTransferMoney(logCtx *gin.Context, memberTransferMoney *po.MemberTransferMoney) error {\n\treturn Update(memberTransferMoney)\n}\n\nfunc (service *MemberTransferMoneyService) UpdateMemberTransferMoneyPartial(logCtx *gin.Context, memberTransferMoney *po.MemberTransferMoney) error {\n\treturn UpdateNotNull(memberTransferMoney)\n}\n\nfunc (service *MemberTransferMoneyService) UpdateMemberTransferMoneyPartialWithTx(logCtx *gin.Context, memberTransferMoney *po.MemberTransferMoney, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(memberTransferMoney, tx)\n}\n\nfunc (service *MemberTransferMoneyService) DeleteMemberTransferMoney(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MemberTransferMoney{Id: &id})\n}\n\nfunc (service *MemberTransferMoneyService) FindMemberTransferMoneyById(logCtx *gin.Context, id string) (memberTransferMoney *po.MemberTransferMoney, err error) {\n\tmemberTransferMoney = &po.MemberTransferMoney{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(memberTransferMoney).Error\n\treturn\n}\n\nfunc (service *MemberTransferMoneyService) FindAllMemberTransferMoney(logCtx *gin.Context, reqDto *req.QueryMemberTransferMoneyReqDto) (list *[]po.MemberTransferMoney, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberTransferMoney{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.FromMemberId != nil && *reqDto.FromMemberId != \"\" {\n\t\tdb = db.Where(\"from_member_id=?\", *reqDto.FromMemberId)\n\t}\n\tif reqDto.ToMemberId != nil && *reqDto.ToMemberId != \"\" {\n\t\tdb = db.Where(\"to_member_id=?\", *reqDto.ToMemberId)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MemberTransferMoney{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MemberTransferMoneyService) FindAllMemberTransferMoneyWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberTransferMoneyReqDto) (list *[]po.MemberTransferMoney, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberTransferMoney{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.FromMemberId != nil && *reqDto.FromMemberId != \"\" {\n\t\tdb = db.Where(\"from_member_id=?\", *reqDto.FromMemberId)\n\t}\n\tif reqDto.ToMemberId != nil && *reqDto.ToMemberId != \"\" {\n\t\tdb = db.Where(\"to_member_id=?\", *reqDto.ToMemberId)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MemberTransferMoney{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberTransferMoneyController struct{}\n\nvar (\n\tmemberTransferMoneyService  = impl.MemberTransferMoneyService{}\n\tmemberTransferMoneyTransfer = transfer.MemberTransferMoneyTransfer{}\n)\n\n// @Summary 添加会员转账记录\n// @Description 添加会员转账记录\n// @Tags 会员转账记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberTransferMoneyReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberTransferMoneyVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/transfer/money/add [post]\nfunc (controller *MemberTransferMoneyController) AddMemberTransferMoney(ctx *gin.Context) {\n\treqDto := req.AddMemberTransferMoneyReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmemberTransferMoney := po.MemberTransferMoney{}\n\tif reqDto.VenueId != nil {\n\t\tmemberTransferMoney.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.TransferTime != nil {\n\t\tmemberTransferMoney.TransferTime = reqDto.TransferTime\n\t}\n\tif reqDto.FromMemberId != nil {\n\t\tmemberTransferMoney.FromMemberId = reqDto.FromMemberId\n\t}\n\tif reqDto.FromMemberCardNumber != nil {\n\t\tmemberTransferMoney.FromMemberCardNumber = reqDto.FromMemberCardNumber\n\t}\n\tif reqDto.ToMemberId != nil {\n\t\tmemberTransferMoney.ToMemberId = reqDto.ToMemberId\n\t}\n\tif reqDto.ToMemberCardNumber != nil {\n\t\tmemberTransferMoney.ToMemberCardNumber = reqDto.ToMemberCardNumber\n\t}\n\tif reqDto.Amount != nil {\n\t\tmemberTransferMoney.Amount = reqDto.Amount\n\t}\n\tif reqDto.Points != nil {\n\t\tmemberTransferMoney.Points = reqDto.Points\n\t}\n\tif reqDto.Status != nil {\n\t\tmemberTransferMoney.Status = reqDto.Status\n\t}\n\tif reqDto.Remark != nil {\n\t\tmemberTransferMoney.Remark = reqDto.Remark\n\t}\n\n\terr = memberTransferMoneyService.CreateMemberTransferMoney(ctx, &memberTransferMoney)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberTransferMoneyTransfer.PoToVo(memberTransferMoney))\n}\n\n// @Summary 更新会员转账记录\n// @Description 更新会员转账记录\n// @Tags 会员转账记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberTransferMoneyReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberTransferMoneyVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/transfer/money/update [post]\nfunc (controller *MemberTransferMoneyController) UpdateMemberTransferMoney(ctx *gin.Context) {\n\treqDto := req.UpdateMemberTransferMoneyReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmemberTransferMoney, err := memberTransferMoneyService.FindMemberTransferMoneyById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tmemberTransferMoney.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.TransferTime != nil {\n\t\tmemberTransferMoney.TransferTime = reqDto.TransferTime\n\t}\n\tif reqDto.FromMemberId != nil {\n\t\tmemberTransferMoney.FromMemberId = reqDto.FromMemberId\n\t}\n\tif reqDto.FromMemberCardNumber != nil {\n\t\tmemberTransferMoney.FromMemberCardNumber = reqDto.FromMemberCardNumber\n\t}\n\tif reqDto.ToMemberId != nil {\n\t\tmemberTransferMoney.ToMemberId = reqDto.ToMemberId\n\t}\n\tif reqDto.ToMemberCardNumber != nil {\n\t\tmemberTransferMoney.ToMemberCardNumber = reqDto.ToMemberCardNumber\n\t}\n\tif reqDto.Amount != nil {\n\t\tmemberTransferMoney.Amount = reqDto.Amount\n\t}\n\tif reqDto.Points != nil {\n\t\tmemberTransferMoney.Points = reqDto.Points\n\t}\n\tif reqDto.Status != nil {\n\t\tmemberTransferMoney.Status = reqDto.Status\n\t}\n\tif reqDto.Remark != nil {\n\t\tmemberTransferMoney.Remark = reqDto.Remark\n\t}\n\n\terr = memberTransferMoneyService.UpdateMemberTransferMoney(ctx, memberTransferMoney)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberTransferMoneyTransfer.PoToVo(*memberTransferMoney))\n}\n\n// @Summary 删除会员转账记录\n// @Description 删除会员转账记录\n// @Tags 会员转账记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberTransferMoneyReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/transfer/money/delete [post]\nfunc (controller *MemberTransferMoneyController) DeleteMemberTransferMoney(ctx *gin.Context) {\n\treqDto := req.DeleteMemberTransferMoneyReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = memberTransferMoneyService.DeleteMemberTransferMoney(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员转账记录\n// @Description 查询会员转账记录\n// @Tags 会员转账记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberTransferMoneyReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberTransferMoneyVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/transfer/money/query [post]\nfunc (controller *MemberTransferMoneyController) QueryMemberTransferMoneys(ctx *gin.Context) {\n\treqDto := req.QueryMemberTransferMoneyReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := memberTransferMoneyService.FindAllMemberTransferMoney(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MemberTransferMoneyVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, memberTransferMoneyTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询会员转账记录列表\n// @Description 查询会员转账记录列表\n// @Tags 会员转账记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberTransferMoneyReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.MemberTransferMoneyVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/transfer/money/list [post]\nfunc (a *MemberTransferMoneyController) ListMemberTransferMoneys(ctx *gin.Context) {\n\treqDto := req.QueryMemberTransferMoneyReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := memberTransferMoneyService.FindAllMemberTransferMoneyWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MemberTransferMoneyVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MemberTransferMoneyVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberTransferMoneyTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberTransferMoneyRoute struct {\n}\n\nfunc (s *MemberTransferMoneyRoute) InitMemberTransferMoneyRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmemberTransferMoneyController := controller.MemberTransferMoneyController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/member/transfer/money/add\", memberTransferMoneyController.AddMemberTransferMoney)       //add\n\t\troute.POST(\"/api/member/transfer/money/update\", memberTransferMoneyController.UpdateMemberTransferMoney)   //update\n\t\troute.POST(\"/api/member/transfer/money/delete\", memberTransferMoneyController.DeleteMemberTransferMoney)   //delete\n\t\troute.POST(\"/api/member/transfer/money/query\", memberTransferMoneyController.QueryMemberTransferMoneys)    //query\n\t\troute.POST(\"/api/member/transfer/money/list\", memberTransferMoneyController.ListMemberTransferMoneys)     //list\n\t}\n}"}]