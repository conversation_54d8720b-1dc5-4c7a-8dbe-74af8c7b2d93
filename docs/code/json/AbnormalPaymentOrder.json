[{"po": "package po\n\n// AbnormalPaymentOrder 异常支付订单实体\ntype AbnormalPaymentOrder struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                  // 唯一id\n\tOrderNumber *string `gorm:\"column:order_number;type:varchar(64);default:''\" json:\"orderNumber\"` // 订单号\n\tRoomNumber  *string `gorm:\"column:room_number;type:varchar(64);default:''\" json:\"roomNumber\"`   // 房间号\n\tOrderStatus *string `gorm:\"column:order_status;type:varchar(64);default:''\" json:\"orderStatus\"` // 订单状态\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                       // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                       // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                       // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                   // 版本号\n}\n\n// TableName 设置表名\nfunc (AbnormalPaymentOrder) TableName() string {\n\treturn \"abnormal_payment_order\"\n}\n\nfunc (a AbnormalPaymentOrder) GetId() string {\n\treturn *a.Id\n}\n", "vo": "package vo\n\n// AbnormalPaymentOrderVO 异常支付订单值对象\ntype AbnormalPaymentOrderVO struct {\n\tId          string `json:\"id\" bson:\"_id\"`                      // 唯一id\n\tOrderNumber string `json:\"orderNumber\" bson:\"orderNumber\"`     // 订单号\n\tRoomNumber  string `json:\"roomNumber\" bson:\"roomNumber\"`       // 房间号\n\tOrderStatus string `json:\"orderStatus\" bson:\"orderStatus\"`     // 订单状态\n\tCtime       int64  `json:\"ctime\" bson:\"ctime\"`                 // 创建时间戳\n\tUtime       int64  `json:\"utime\" bson:\"utime\"`                 // 更新时间戳\n\tState       int    `json:\"state\" bson:\"state\"`                 // 状态值\n\tVersion     int    `json:\"version\" bson:\"version\"`             // 版本号\n}\n", "req_add": "package req\n\n// AddAbnormalPaymentOrderReqDto 创建异常支付订单请求DTO\ntype AddAbnormalPaymentOrderReqDto struct {\n\tOrderNumber *string `json:\"orderNumber\"` // 订单号\n\tRoomNumber  *string `json:\"roomNumber\"`  // 房间号\n\tOrderStatus *string `json:\"orderStatus\"` // 订单状态\n}\n", "req_update": "package req\n\ntype UpdateAbnormalPaymentOrderReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一id\n\tOrderNumber *string `json:\"orderNumber\"` // 订单号\n\tRoomNumber  *string `json:\"roomNumber\"`  // 房间号\n\tOrderStatus *string `json:\"orderStatus\"` // 订单状态\n}\n", "req_delete": "package req\n\ntype DeleteAbnormalPaymentOrderReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryAbnormalPaymentOrderReqDto struct {\n\tId          *string `json:\"id\"`          // 唯一id\n\tOrderNumber *string `json:\"orderNumber\"` // 订单号\n\tRoomNumber  *string `json:\"roomNumber\"`  // 房间号\n\tOrderStatus *string `json:\"orderStatus\"` // 订单状态\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype AbnormalPaymentOrderTransfer struct {\n}\n\nfunc (transfer *AbnormalPaymentOrderTransfer) PoToVo(po po.AbnormalPaymentOrder) vo.AbnormalPaymentOrderVO {\n\tvo := vo.AbnormalPaymentOrderVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *AbnormalPaymentOrderTransfer) VoToPo(vo vo.AbnormalPaymentOrderVO) po.AbnormalPaymentOrder {\n\tpo := po.AbnormalPaymentOrder{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype AbnormalPaymentOrderService struct {\n}\n\nfunc (service *AbnormalPaymentOrderService) CreateAbnormalPaymentOrder(logCtx *gin.Context, abnormalPaymentOrder *po.AbnormalPaymentOrder) error {\n\treturn Save(abnormalPaymentOrder)\n}\n\nfunc (service *AbnormalPaymentOrderService) UpdateAbnormalPaymentOrder(logCtx *gin.Context, abnormalPaymentOrder *po.AbnormalPaymentOrder) error {\n\treturn Update(abnormalPaymentOrder)\n}\n\nfunc (service *AbnormalPaymentOrderService) DeleteAbnormalPaymentOrder(logCtx *gin.Context, id string) error {\n\treturn Delete(po.AbnormalPaymentOrder{Id: &id})\n}\n\nfunc (service *AbnormalPaymentOrderService) FindAbnormalPaymentOrderById(logCtx *gin.Context, id string) (abnormalPaymentOrder *po.AbnormalPaymentOrder, err error) {\n\tabnormalPaymentOrder = &po.AbnormalPaymentOrder{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(abnormalPaymentOrder).Error\n\treturn\n}\n\nfunc (service *AbnormalPaymentOrderService) FindAllAbnormalPaymentOrder(logCtx *gin.Context, reqDto *req.QueryAbnormalPaymentOrderReqDto) (list *[]po.AbnormalPaymentOrder, err error) {\n\tdb := model.DBSlave.Self.Model(&po.AbnormalPaymentOrder{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.AbnormalPaymentOrder{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *AbnormalPaymentOrderService) FindAllAbnormalPaymentOrderWithPagination(logCtx *gin.Context, reqDto *req.QueryAbnormalPaymentOrderReqDto) (list *[]po.AbnormalPaymentOrder, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.AbnormalPaymentOrder{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.AbnormalPaymentOrder{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype AbnormalPaymentOrderController struct{}\n\nvar (\n\tabnormalPaymentOrderService  = impl.AbnormalPaymentOrderService{}\n\tabnormalPaymentOrderTransfer = transfer.AbnormalPaymentOrderTransfer{}\n)\n\n// @Summary 添加异常支付订单\n// @Description 添加异常支付订单\n// @Tags 异常支付订单\n// @Accept json\n// @Produce json\n// @Param body body req.AddAbnormalPaymentOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.AbnormalPaymentOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/abnormal-payment-order/add [post]\nfunc (controller *AbnormalPaymentOrderController) AddAbnormalPaymentOrder(ctx *gin.Context) {\n\treqDto := req.AddAbnormalPaymentOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tabnormalPaymentOrder := po.AbnormalPaymentOrder{}\n\tif reqDto.OrderNumber != nil {\n\t\tabnormalPaymentOrder.OrderNumber = reqDto.OrderNumber\n\t}\n\tif reqDto.RoomNumber != nil {\n\t\tabnormalPaymentOrder.RoomNumber = reqDto.RoomNumber\n\t}\n\tif reqDto.OrderStatus != nil {\n\t\tabnormalPaymentOrder.OrderStatus = reqDto.OrderStatus\n\t}\n\terr = abnormalPaymentOrderService.CreateAbnormalPaymentOrder(ctx, &abnormalPaymentOrder)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, abnormalPaymentOrderTransfer.PoToVo(abnormalPaymentOrder))\n}\n\n// @Summary 更新异常支付订单\n// @Description 更新异常支付订单\n// @Tags 异常支付订单\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateAbnormalPaymentOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.AbnormalPaymentOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/abnormal-payment-order/update [post]\nfunc (controller *AbnormalPaymentOrderController) UpdateAbnormalPaymentOrder(ctx *gin.Context) {\n\treqDto := req.UpdateAbnormalPaymentOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tabnormalPaymentOrder, err := abnormalPaymentOrderService.FindAbnormalPaymentOrderById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.OrderNumber != nil {\n\t\tabnormalPaymentOrder.OrderNumber = reqDto.OrderNumber\n\t}\n\tif reqDto.RoomNumber != nil {\n\t\tabnormalPaymentOrder.RoomNumber = reqDto.RoomNumber\n\t}\n\tif reqDto.OrderStatus != nil {\n\t\tabnormalPaymentOrder.OrderStatus = reqDto.OrderStatus\n\t}\n\terr = abnormalPaymentOrderService.UpdateAbnormalPaymentOrder(ctx, abnormalPaymentOrder)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, abnormalPaymentOrderTransfer.PoToVo(*abnormalPaymentOrder))\n}\n\n// @Summary 删除异常支付订单\n// @Description 删除异常支付订单\n// @Tags 异常支付订单\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteAbnormalPaymentOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/abnormal-payment-order/delete [post]\nfunc (controller *AbnormalPaymentOrderController) DeleteAbnormalPaymentOrder(ctx *gin.Context) {\n\treqDto := req.DeleteAbnormalPaymentOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = abnormalPaymentOrderService.DeleteAbnormalPaymentOrder(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询异常支付订单\n// @Description 查询异常支付订单\n// @Tags 异常支付订单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryAbnormalPaymentOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.AbnormalPaymentOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/abnormal-payment-order/query [post]\nfunc (controller *AbnormalPaymentOrderController) QueryAbnormalPaymentOrders(ctx *gin.Context) {\n\treqDto := req.QueryAbnormalPaymentOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := abnormalPaymentOrderService.FindAllAbnormalPaymentOrder(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.AbnormalPaymentOrderVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, abnormalPaymentOrderTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询异常支付订单列表\n// @Description 查询异常支付订单列表\n// @Tags 异常支付订单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryAbnormalPaymentOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.AbnormalPaymentOrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/abnormal-payment-order/list [post]\nfunc (a *AbnormalPaymentOrderController) ListAbnormalPaymentOrders(ctx *gin.Context) {\n\treqDto := req.QueryAbnormalPaymentOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := abnormalPaymentOrderService.FindAllAbnormalPaymentOrderWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.AbnormalPaymentOrderVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.AbnormalPaymentOrderVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, abnormalPaymentOrderTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype AbnormalPaymentOrderRoute struct {\n}\n\nfunc (s *AbnormalPaymentOrderRoute) InitAbnormalPaymentOrderRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tabnormalPaymentOrderController := controller.AbnormalPaymentOrderController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/abnormal-payment-order/add\", abnormalPaymentOrderController.AddAbnormalPaymentOrder)    //add\n\t\troute.POST(\"/api/abnormal-payment-order/update\", abnormalPaymentOrderController.UpdateAbnormalPaymentOrder) //update\n\t\troute.POST(\"/api/abnormal-payment-order/delete\", abnormalPaymentOrderController.DeleteAbnormalPaymentOrder) //delete\n\t\troute.POST(\"/api/abnormal-payment-order/query\", abnormalPaymentOrderController.QueryAbnormalPaymentOrders)     //query\n\t\troute.POST(\"/api/abnormal-payment-order/list\", abnormalPaymentOrderController.ListAbnormalPaymentOrders)     //list\n\t}\n}\n"}]