[{"po": "package po\n\n// PrintTemplate 打印模板实体\ntype PrintTemplate struct {\n\tId                      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tName                    *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                    // 打印模板名称\n\tCopies                  *int    `gorm:\"column:copies;type:int;default:0\" json:\"copies\"`                          // 打印份数\n\tPrintMode              *string `gorm:\"column:print_mode;type:varchar(64);default:''\" json:\"printMode\"`            // 打印模式\n\tPaperSize              *string `gorm:\"column:paper_size;type:varchar(64);default:''\" json:\"paperSize\"`            // 纸张大小\n\tMergeProducts          *bool   `gorm:\"column:merge_products;type:bool;default:false\" json:\"mergeProducts\"`        // 合并产品\n\tProductGroupingMethod  *string `gorm:\"column:product_grouping_method;type:varchar(64)\" json:\"productGroupingMethod\"` // 产品分组方式\n\tPrintGifts             *bool   `gorm:\"column:print_gifts;type:bool;default:false\" json:\"printGifts\"`              // 打印礼品\n\tPrintReverseBill       *bool   `gorm:\"column:print_reverse_bill;type:bool\" json:\"printReverseBill\"`              // 打印反向账单\n\tPrintZeroBill          *bool   `gorm:\"column:print_zero_bill;type:bool\" json:\"printZeroBill\"`                    // 打印零账单\n\tSelfServiceCopies      *int    `gorm:\"column:self_service_copies;type:int\" json:\"selfServiceCopies\"`            // 自助服务份数\n\tPrintWineRefund        *bool   `gorm:\"column:print_wine_refund;type:bool\" json:\"printWineRefund\"`              // 打印酒水退款\n\tWineOrderPrintSequence *string `gorm:\"column:wine_order_print_sequence;type:varchar(64)\" json:\"wineOrderPrintSequence\"` // 酒水订单打印顺序\n\tProductionPointCopies  *int    `gorm:\"column:production_point_copies;type:int\" json:\"productionPointCopies\"`    // 生产点份数\n\tProductTypeConfig      *string `gorm:\"column:product_type_config;type:varchar(255)\" json:\"productTypeConfig\"`    // 产品类型配置\n\tPrintRobotDeliveryQR   *bool   `gorm:\"column:print_robot_delivery_qr;type:bool\" json:\"printRobotDeliveryQR\"`    // 打印机器人配送二维码\n\tPrintProductTotalAmount *bool   `gorm:\"column:print_product_total_amount;type:bool\" json:\"printProductTotalAmount\"` // 打印产品总金额\n\tPrintOnlyLocalUse      *bool   `gorm:\"column:print_only_local_use;type:bool\" json:\"printOnlyLocalUse\"`          // 仅打印本地使用\n\tPrinterType            *string `gorm:\"column:printer_type;type:varchar(64)\" json:\"printerType\"`                  // 打印机类型\n\tPrintTime              *string `gorm:\"column:print_time;type:varchar(64)\" json:\"printTime\"`                      // 打印时间\n\tPrintPaymentMethodCount *bool   `gorm:\"column:print_payment_method_count;type:bool\" json:\"printPaymentMethodCount\"` // 打印支付方式计数\n\tTemplateContent        *string `gorm:\"column:template_content;type:text\" json:\"templateContent\"`                // 模板内容\n\tCtime                  *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                            // 创建时间戳\n\tUtime                  *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                            // 更新时间戳\n\tState                  *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                            // 状态值\n\tVersion                *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                        // 版本号\n}\n\n// TableName 设置表名\nfunc (PrintTemplate) TableName() string {\n\treturn \"print_template\"\n}\n\nfunc (p PrintTemplate) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PrintTemplateVO 打印模板值对象\ntype PrintTemplateVO struct {\n\tId                      string `json:\"id\"`                      // ID\n\tName                    string `json:\"name\"`                    // 打印模板名称\n\tCopies                  int    `json:\"copies\"`                  // 打印份数\n\tPrintMode              string `json:\"printMode\"`              // 打印模式\n\tPaperSize              string `json:\"paperSize\"`              // 纸张大小\n\tMergeProducts          bool   `json:\"mergeProducts\"`          // 合并产品\n\tProductGroupingMethod  string `json:\"productGroupingMethod\"`  // 产品分组方式\n\tPrintGifts             bool   `json:\"printGifts\"`             // 打印礼品\n\tPrintReverseBill       bool   `json:\"printReverseBill\"`       // 打印反向账单\n\tPrintZeroBill          bool   `json:\"printZeroBill\"`          // 打印零账单\n\tSelfServiceCopies      int    `json:\"selfServiceCopies\"`      // 自助服务份数\n\tPrintWineRefund        bool   `json:\"printWineRefund\"`        // 打印酒水退款\n\tWineOrderPrintSequence string `json:\"wineOrderPrintSequence\"` // 酒水订单打印顺序\n\tProductionPointCopies  int    `json:\"productionPointCopies\"`  // 生产点份数\n\tProductTypeConfig      string `json:\"productTypeConfig\"`      // 产品类型配置\n\tPrintRobotDeliveryQR   bool   `json:\"printRobotDeliveryQR\"`   // 打印机器人配送二维码\n\tPrintProductTotalAmount bool   `json:\"printProductTotalAmount\"` // 打印产品总金额\n\tPrintOnlyLocalUse      bool   `json:\"printOnlyLocalUse\"`      // 仅打印本地使用\n\tPrinterType            string `json:\"printerType\"`            // 打印机类型\n\tPrintTime              string `json:\"printTime\"`              // 打印时间\n\tPrintPaymentMethodCount bool   `json:\"printPaymentMethodCount\"` // 打印支付方式计数\n\tTemplateContent        string `json:\"templateContent\"`        // 模板内容\n\tCtime                  int64  `json:\"ctime\"`                  // 创建时间戳\n\tUtime                  int64  `json:\"utime\"`                  // 更新时间戳\n\tState                  int    `json:\"state\"`                  // 状态值\n\tVersion                int    `json:\"version\"`                // 版本号\n}\n", "req_add": "package req\n\n// AddPrintTemplateReqDto 创建打印模板请求DTO\ntype AddPrintTemplateReqDto struct {\n\tName                    *string `json:\"name\"`                    // 打印模板名称\n\tCopies                  *int    `json:\"copies\"`                  // 打印份数\n\tPrintMode              *string `json:\"printMode\"`              // 打印模式\n\tPaperSize              *string `json:\"paperSize\"`              // 纸张大小\n\tMergeProducts          *bool   `json:\"mergeProducts\"`          // 合并产品\n\tProductGroupingMethod  *string `json:\"productGroupingMethod\"`  // 产品分组方式\n\tPrintGifts             *bool   `json:\"printGifts\"`             // 打印礼品\n\tPrintReverseBill       *bool   `json:\"printReverseBill\"`       // 打印反向账单\n\tPrintZeroBill          *bool   `json:\"printZeroBill\"`          // 打印零账单\n\tSelfServiceCopies      *int    `json:\"selfServiceCopies\"`      // 自助服务份数\n\tPrintWineRefund        *bool   `json:\"printWineRefund\"`        // 打印酒水退款\n\tWineOrderPrintSequence *string `json:\"wineOrderPrintSequence\"` // 酒水订单打印顺序\n\tProductionPointCopies  *int    `json:\"productionPointCopies\"`  // 生产点份数\n\tProductTypeConfig      *string `json:\"productTypeConfig\"`      // 产品类型配置\n\tPrintRobotDeliveryQR   *bool   `json:\"printRobotDeliveryQR\"`   // 打印机器人配送二维码\n\tPrintProductTotalAmount *bool   `json:\"printProductTotalAmount\"` // 打印产品总金额\n\tPrintOnlyLocalUse      *bool   `json:\"printOnlyLocalUse\"`      // 仅打印本地使用\n\tPrinterType            *string `json:\"printerType\"`            // 打印机类型\n\tPrintTime              *string `json:\"printTime\"`              // 打印时间\n\tPrintPaymentMethodCount *bool   `json:\"printPaymentMethodCount\"` // 打印支付方式计数\n\tTemplateContent        *string `json:\"templateContent\"`        // 模板内容\n}\n", "req_update": "package req\n\n// UpdatePrintTemplateReqDto 更新打印模板请求DTO\ntype UpdatePrintTemplateReqDto struct {\n\tId                      *string `json:\"id\"`                      // ID\n\tName                    *string `json:\"name\"`                    // 打印模板名称\n\tCopies                  *int    `json:\"copies\"`                  // 打印份数\n\tPrintMode              *string `json:\"printMode\"`              // 打印模式\n\tPaperSize              *string `json:\"paperSize\"`              // 纸张大小\n\tMergeProducts          *bool   `json:\"mergeProducts\"`          // 合并产品\n\tProductGroupingMethod  *string `json:\"productGroupingMethod\"`  // 产品分组方式\n\tPrintGifts             *bool   `json:\"printGifts\"`             // 打印礼品\n\tPrintReverseBill       *bool   `json:\"printReverseBill\"`       // 打印反向账单\n\tPrintZeroBill          *bool   `json:\"printZeroBill\"`          // 打印零账单\n\tSelfServiceCopies      *int    `json:\"selfServiceCopies\"`      // 自助服务份数\n\tPrintWineRefund        *bool   `json:\"printWineRefund\"`        // 打印酒水退款\n\tWineOrderPrintSequence *string `json:\"wineOrderPrintSequence\"` // 酒水订单打印顺序\n\tProductionPointCopies  *int    `json:\"productionPointCopies\"`  // 生产点份数\n\tProductTypeConfig      *string `json:\"productTypeConfig\"`      // 产品类型配置\n\tPrintRobotDeliveryQR   *bool   `json:\"printRobotDeliveryQR\"`   // 打印机器人配送二维码\n\tPrintProductTotalAmount *bool   `json:\"printProductTotalAmount\"` // 打印产品总金额\n\tPrintOnlyLocalUse      *bool   `json:\"printOnlyLocalUse\"`      // 仅打印本地使用\n\tPrinterType            *string `json:\"printerType\"`            // 打印机类型\n\tPrintTime              *string `json:\"printTime\"`              // 打印时间\n\tPrintPaymentMethodCount *bool   `json:\"printPaymentMethodCount\"` // 打印支付方式计数\n\tTemplateContent        *string `json:\"templateContent\"`        // 模板内容\n}\n", "req_delete": "package req\n\ntype DeletePrintTemplateReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryPrintTemplateReqDto struct {\n\tId                      *string `json:\"id\"`                      // ID\n\tName                    *string `json:\"name\"`                    // 打印模板名称\n\tPrintMode              *string `json:\"printMode\"`              // 打印模式\n\tPaperSize              *string `json:\"paperSize\"`              // 纸张大小\n\tProductGroupingMethod  *string `json:\"productGroupingMethod\"`  // 产品分组方式\n\tPrinterType            *string `json:\"printerType\"`            // 打印机类型\n\tPageNum                *int    `json:\"pageNum\"`                // 页码\n\tPageSize               *int    `json:\"pageSize\"`               // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PrintTemplateTransfer struct {\n}\n\nfunc (transfer *PrintTemplateTransfer) PoToVo(po po.PrintTemplate) vo.PrintTemplateVO {\n\tvo := vo.PrintTemplateVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PrintTemplateTransfer) VoToPo(vo vo.PrintTemplateVO) po.PrintTemplate {\n\tpo := po.PrintTemplate{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrintTemplateService struct {\n}\n\nfunc (service *PrintTemplateService) CreatePrintTemplate(logCtx *gin.Context, printTemplate *po.PrintTemplate) error {\n\treturn Save(printTemplate)\n}\n\nfunc (service *PrintTemplateService) UpdatePrintTemplate(logCtx *gin.Context, printTemplate *po.PrintTemplate) error {\n\treturn Update(printTemplate)\n}\n\nfunc (service *PrintTemplateService) DeletePrintTemplate(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PrintTemplate{Id: &id})\n}\n\nfunc (service *PrintTemplateService) FindPrintTemplateById(logCtx *gin.Context, id string) (printTemplate *po.PrintTemplate, err error) {\n\tprintTemplate = &po.PrintTemplate{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(printTemplate).Error\n\treturn\n}\n\nfunc (service *PrintTemplateService) FindAllPrintTemplate(logCtx *gin.Context, reqDto *req.QueryPrintTemplateReqDto) (list *[]po.PrintTemplate, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PrintTemplate{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.PrintMode != nil && *reqDto.PrintMode != \"\" {\n\t\tdb = db.Where(\"print_mode=?\", *reqDto.PrintMode)\n\t}\n\tif reqDto.PaperSize != nil && *reqDto.PaperSize != \"\" {\n\t\tdb = db.Where(\"paper_size=?\", *reqDto.PaperSize)\n\t}\n\tif reqDto.ProductGroupingMethod != nil && *reqDto.ProductGroupingMethod != \"\" {\n\t\tdb = db.Where(\"product_grouping_method=?\", *reqDto.ProductGroupingMethod)\n\t}\n\tif reqDto.PrinterType != nil && *reqDto.PrinterType != \"\" {\n\t\tdb = db.Where(\"printer_type=?\", *reqDto.PrinterType)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PrintTemplate{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PrintTemplateService) FindAllPrintTemplateWithPagination(logCtx *gin.Context, reqDto *req.QueryPrintTemplateReqDto) (list *[]po.PrintTemplate, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PrintTemplate{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.PrintMode != nil && *reqDto.PrintMode != \"\" {\n\t\tdb = db.Where(\"print_mode=?\", *reqDto.PrintMode)\n\t}\n\tif reqDto.PaperSize != nil && *reqDto.PaperSize != \"\" {\n\t\tdb = db.Where(\"paper_size=?\", *reqDto.PaperSize)\n\t}\n\tif reqDto.ProductGroupingMethod != nil && *reqDto.ProductGroupingMethod != \"\" {\n\t\tdb = db.Where(\"product_grouping_method=?\", *reqDto.ProductGroupingMethod)\n\t}\n\tif reqDto.PrinterType != nil && *reqDto.PrinterType != \"\" {\n\t\tdb = db.Where(\"printer_type=?\", *reqDto.PrinterType)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PrintTemplate{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrintTemplateController struct{}\n\nvar (\n\tprintTemplateService  = impl.PrintTemplateService{}\n\tprintTemplateTransfer = transfer.PrintTemplateTransfer{}\n)\n\n// @Summary 添加打印模板\n// @Description 添加打印模板\n// @Tags 打印模板\n// @Accept json\n// @Produce json\n// @Param body body req.AddPrintTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PrintTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/print-template/add [post]\nfunc (controller *PrintTemplateController) AddPrintTemplate(ctx *gin.Context) {\n\treqDto := req.AddPrintTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tprintTemplate := po.PrintTemplate{}\n\tif reqDto.Name != nil {\n\t\tprintTemplate.Name = reqDto.Name\n\t}\n\tif reqDto.Copies != nil {\n\t\tprintTemplate.Copies = reqDto.Copies\n\t}\n\tif reqDto.PrintMode != nil {\n\t\tprintTemplate.PrintMode = reqDto.PrintMode\n\t}\n\tif reqDto.PaperSize != nil {\n\t\tprintTemplate.PaperSize = reqDto.PaperSize\n\t}\n\tif reqDto.MergeProducts != nil {\n\t\tprintTemplate.MergeProducts = reqDto.MergeProducts\n\t}\n\tif reqDto.ProductGroupingMethod != nil {\n\t\tprintTemplate.ProductGroupingMethod = reqDto.ProductGroupingMethod\n\t}\n\tif reqDto.PrintGifts != nil {\n\t\tprintTemplate.PrintGifts = reqDto.PrintGifts\n\t}\n\tif reqDto.PrintReverseBill != nil {\n\t\tprintTemplate.PrintReverseBill = reqDto.PrintReverseBill\n\t}\n\tif reqDto.PrintZeroBill != nil {\n\t\tprintTemplate.PrintZeroBill = reqDto.PrintZeroBill\n\t}\n\tif reqDto.SelfServiceCopies != nil {\n\t\tprintTemplate.SelfServiceCopies = reqDto.SelfServiceCopies\n\t}\n\tif reqDto.PrintWineRefund != nil {\n\t\tprintTemplate.PrintWineRefund = reqDto.PrintWineRefund\n\t}\n\tif reqDto.WineOrderPrintSequence != nil {\n\t\tprintTemplate.WineOrderPrintSequence = reqDto.WineOrderPrintSequence\n\t}\n\tif reqDto.ProductionPointCopies != nil {\n\t\tprintTemplate.ProductionPointCopies = reqDto.ProductionPointCopies\n\t}\n\tif reqDto.ProductTypeConfig != nil {\n\t\tprintTemplate.ProductTypeConfig = reqDto.ProductTypeConfig\n\t}\n\tif reqDto.PrintRobotDeliveryQR != nil {\n\t\tprintTemplate.PrintRobotDeliveryQR = reqDto.PrintRobotDeliveryQR\n\t}\n\tif reqDto.PrintProductTotalAmount != nil {\n\t\tprintTemplate.PrintProductTotalAmount = reqDto.PrintProductTotalAmount\n\t}\n\tif reqDto.PrintOnlyLocalUse != nil {\n\t\tprintTemplate.PrintOnlyLocalUse = reqDto.PrintOnlyLocalUse\n\t}\n\tif reqDto.PrinterType != nil {\n\t\tprintTemplate.PrinterType = reqDto.PrinterType\n\t}\n\tif reqDto.PrintTime != nil {\n\t\tprintTemplate.PrintTime = reqDto.PrintTime\n\t}\n\tif reqDto.PrintPaymentMethodCount != nil {\n\t\tprintTemplate.PrintPaymentMethodCount = reqDto.PrintPaymentMethodCount\n\t}\n\tif reqDto.TemplateContent != nil {\n\t\tprintTemplate.TemplateContent = reqDto.TemplateContent\n\t}\n\n\terr = printTemplateService.CreatePrintTemplate(ctx, &printTemplate)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, printTemplateTransfer.PoToVo(printTemplate))\n}\n\n// @Summary 更新打印模板\n// @Description 更新打印模板\n// @Tags 打印模板\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePrintTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PrintTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/print-template/update [post]\nfunc (controller *PrintTemplateController) UpdatePrintTemplate(ctx *gin.Context) {\n\treqDto := req.UpdatePrintTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tprintTemplate, err := printTemplateService.FindPrintTemplateById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tprintTemplate.Name = reqDto.Name\n\t}\n\tif reqDto.Copies != nil {\n\t\tprintTemplate.Copies = reqDto.Copies\n\t}\n\tif reqDto.PrintMode != nil {\n\t\tprintTemplate.PrintMode = reqDto.PrintMode\n\t}\n\tif reqDto.PaperSize != nil {\n\t\tprintTemplate.PaperSize = reqDto.PaperSize\n\t}\n\tif reqDto.MergeProducts != nil {\n\t\tprintTemplate.MergeProducts = reqDto.MergeProducts\n\t}\n\tif reqDto.ProductGroupingMethod != nil {\n\t\tprintTemplate.ProductGroupingMethod = reqDto.ProductGroupingMethod\n\t}\n\tif reqDto.PrintGifts != nil {\n\t\tprintTemplate.PrintGifts = reqDto.PrintGifts\n\t}\n\tif reqDto.PrintReverseBill != nil {\n\t\tprintTemplate.PrintReverseBill = reqDto.PrintReverseBill\n\t}\n\tif reqDto.PrintZeroBill != nil {\n\t\tprintTemplate.PrintZeroBill = reqDto.PrintZeroBill\n\t}\n\tif reqDto.SelfServiceCopies != nil {\n\t\tprintTemplate.SelfServiceCopies = reqDto.SelfServiceCopies\n\t}\n\tif reqDto.PrintWineRefund != nil {\n\t\tprintTemplate.PrintWineRefund = reqDto.PrintWineRefund\n\t}\n\tif reqDto.WineOrderPrintSequence != nil {\n\t\tprintTemplate.WineOrderPrintSequence = reqDto.WineOrderPrintSequence\n\t}\n\tif reqDto.ProductionPointCopies != nil {\n\t\tprintTemplate.ProductionPointCopies = reqDto.ProductionPointCopies\n\t}\n\tif reqDto.ProductTypeConfig != nil {\n\t\tprintTemplate.ProductTypeConfig = reqDto.ProductTypeConfig\n\t}\n\tif reqDto.PrintRobotDeliveryQR != nil {\n\t\tprintTemplate.PrintRobotDeliveryQR = reqDto.PrintRobotDeliveryQR\n\t}\n\tif reqDto.PrintProductTotalAmount != nil {\n\t\tprintTemplate.PrintProductTotalAmount = reqDto.PrintProductTotalAmount\n\t}\n\tif reqDto.PrintOnlyLocalUse != nil {\n\t\tprintTemplate.PrintOnlyLocalUse = reqDto.PrintOnlyLocalUse\n\t}\n\tif reqDto.PrinterType != nil {\n\t\tprintTemplate.PrinterType = reqDto.PrinterType\n\t}\n\tif reqDto.PrintTime != nil {\n\t\tprintTemplate.PrintTime = reqDto.PrintTime\n\t}\n\tif reqDto.PrintPaymentMethodCount != nil {\n\t\tprintTemplate.PrintPaymentMethodCount = reqDto.PrintPaymentMethodCount\n\t}\n\tif reqDto.TemplateContent != nil {\n\t\tprintTemplate.TemplateContent = reqDto.TemplateContent\n\t}\n\n\terr = printTemplateService.UpdatePrintTemplate(ctx, printTemplate)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, printTemplateTransfer.PoToVo(*printTemplate))\n}\n\n// @Summary 删除打印模板\n// @Description 删除打印模板\n// @Tags 打印模板\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePrintTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/print-template/delete [post]\nfunc (controller *PrintTemplateController) DeletePrintTemplate(ctx *gin.Context) {\n\treqDto := req.DeletePrintTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = printTemplateService.DeletePrintTemplate(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询打印模板\n// @Description 查询打印模板\n// @Tags 打印模板\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPrintTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PrintTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/print-template/query [post]\nfunc (controller *PrintTemplateController) QueryPrintTemplates(ctx *gin.Context) {\n\treqDto := req.QueryPrintTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := printTemplateService.FindAllPrintTemplate(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.PrintTemplateVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, printTemplateTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询打印模板列表\n// @Description 查询打印模板列表\n// @Tags 打印模板\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPrintTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PrintTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/print-template/list [post]\nfunc (controller *PrintTemplateController) ListPrintTemplates(ctx *gin.Context) {\n\treqDto := req.QueryPrintTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := printTemplateService.FindAllPrintTemplateWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.PrintTemplateVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PrintTemplateVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, printTemplateTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PrintTemplateRoute struct {\n}\n\nfunc (s *PrintTemplateRoute) InitPrintTemplateRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tprintTemplateController := controller.PrintTemplateController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/print-template/add\", printTemplateController.AddPrintTemplate)       // add\n\t\troute.POST(\"/api/print-template/update\", printTemplateController.UpdatePrintTemplate) // update\n\t\troute.POST(\"/api/print-template/delete\", printTemplateController.DeletePrintTemplate) // delete\n\t\troute.POST(\"/api/print-template/query\", printTemplateController.QueryPrintTemplates)  // query\n\t\troute.POST(\"/api/print-template/list\", printTemplateController.ListPrintTemplates)    // list\n\t}\n}\n"}]