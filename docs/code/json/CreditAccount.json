[{"po": "package po\n\n// CreditAccount 信用账户实体\ntype CreditAccount struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一id\n\tAccountName    *string `gorm:\"column:account_name;type:varchar(64);default:''\" json:\"accountName\"` // 账户名称\n\tCreditLimit    *int64  `gorm:\"column:credit_limit;type:bigint;default:0\" json:\"creditLimit\"`      // 信用额度\n\tCurrentBalance *int64  `gorm:\"column:current_balance;type:bigint;default:0\" json:\"currentBalance\"` // 当前余额\n\tCtime          *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                    // 创建时间\n\tUtime          *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                    // 更新时间\n\tState          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态\n\tVersion        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本\n}\n\n// TableName 设置表名\nfunc (CreditAccount) TableName() string {\n\treturn \"credit_account\"\n}\n\nfunc (c CreditAccount) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CreditAccountVO 信用账户值对象\ntype CreditAccountVO struct {\n\tId             string `json:\"id\"`             // 唯一id\n\tAccountName    string `json:\"accountName\"`    // 账户名称\n\tCreditLimit    int64  `json:\"creditLimit\"`    // 信用额度\n\tCurrentBalance int64  `json:\"currentBalance\"` // 当前余额\n\tCtime          int64  `json:\"ctime\"`          // 创建时间\n\tUtime          int64  `json:\"utime\"`          // 更新时间\n\tState          int    `json:\"state\"`          // 状态\n\tVersion        int    `json:\"version\"`        // 版本\n}\n", "req_add": "package req\n\n// AddCreditAccountReqDto 创建信用账户请求DTO\ntype AddCreditAccountReqDto struct {\n\tAccountName    *string `json:\"accountName\"`    // 账户名称\n\tCreditLimit    *int64  `json:\"creditLimit\"`    // 信用额度\n\tCurrentBalance *int64  `json:\"currentBalance\"` // 当前余额\n}\n", "req_update": "package req\n\ntype UpdateCreditAccountReqDto struct {\n\tId             *string `json:\"id\"`             // 唯一id\n\tAccountName    *string `json:\"accountName\"`    // 账户名称\n\tCreditLimit    *int64  `json:\"creditLimit\"`    // 信用额度\n\tCurrentBalance *int64  `json:\"currentBalance\"` // 当前余额\n}\n", "req_delete": "package req\n\ntype DeleteCreditAccountReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryCreditAccountReqDto struct {\n\tId             *string `json:\"id\"`             // 唯一id\n\tAccountName    *string `json:\"accountName\"`    // 账户名称\n\tCreditLimit    *int64  `json:\"creditLimit\"`    // 信用额度\n\tCurrentBalance *int64  `json:\"currentBalance\"` // 当前余额\n\tPageNum        *int    `json:\"pageNum\"`        // 页码\n\tPageSize       *int    `json:\"pageSize\"`       // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CreditAccountTransfer struct {\n}\n\nfunc (transfer *CreditAccountTransfer) PoToVo(po po.CreditAccount) vo.CreditAccountVO {\n\tvo := vo.CreditAccountVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CreditAccountTransfer) VoToPo(vo vo.CreditAccountVO) po.CreditAccount {\n\tpo := po.CreditAccount{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CreditAccountService struct {\n}\n\nfunc (service *CreditAccountService) CreateCreditAccount(logCtx *gin.Context, creditAccount *po.CreditAccount) error {\n\treturn Save(creditAccount)\n}\n\nfunc (service *CreditAccountService) UpdateCreditAccount(logCtx *gin.Context, creditAccount *po.CreditAccount) error {\n\treturn Update(creditAccount)\n}\n\nfunc (service *CreditAccountService) DeleteCreditAccount(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CreditAccount{Id: &id})\n}\n\nfunc (service *CreditAccountService) FindCreditAccountById(logCtx *gin.Context, id string) (creditAccount *po.CreditAccount, err error) {\n\tcreditAccount = &po.CreditAccount{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(creditAccount).Error\n\treturn\n}\n\nfunc (service *CreditAccountService) FindAllCreditAccount(logCtx *gin.Context, reqDto *req.QueryCreditAccountReqDto) (list *[]po.CreditAccount, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CreditAccount{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CreditAccount{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CreditAccountService) FindAllCreditAccountWithPagination(logCtx *gin.Context, reqDto *req.QueryCreditAccountReqDto) (list *[]po.CreditAccount, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CreditAccount{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CreditAccount{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CreditAccountController struct{}\n\nvar (\n\tcreditAccountService  = impl.CreditAccountService{}\n\tcreditAccountTransfer = transfer.CreditAccountTransfer{}\n)\n\n// @Summary 添加信用账户\n// @Description 添加信用账户\n// @Tags 信用账户\n// @Accept json\n// @Produce json\n// @Param body body req.AddCreditAccountReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CreditAccountVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditAccount/add [post]\nfunc (controller *CreditAccountController) AddCreditAccount(ctx *gin.Context) {\n\treqDto := req.AddCreditAccountReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcreditAccount := po.CreditAccount{}\n\tif reqDto.AccountName != nil {\n\t\tcreditAccount.AccountName = reqDto.AccountName\n\t}\n\tif reqDto.CreditLimit != nil {\n\t\tcreditAccount.CreditLimit = reqDto.CreditLimit\n\t}\n\tif reqDto.CurrentBalance != nil {\n\t\tcreditAccount.CurrentBalance = reqDto.CurrentBalance\n\t}\n\terr = creditAccountService.CreateCreditAccount(ctx, &creditAccount)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, creditAccountTransfer.PoToVo(creditAccount))\n}\n\n// @Summary 更新信用账户\n// @Description 更新信用账户\n// @Tags 信用账户\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCreditAccountReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CreditAccountVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditAccount/update [post]\nfunc (controller *CreditAccountController) UpdateCreditAccount(ctx *gin.Context) {\n\treqDto := req.UpdateCreditAccountReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcreditAccount, err := creditAccountService.FindCreditAccountById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.AccountName != nil {\n\t\tcreditAccount.AccountName = reqDto.AccountName\n\t}\n\tif reqDto.CreditLimit != nil {\n\t\tcreditAccount.CreditLimit = reqDto.CreditLimit\n\t}\n\tif reqDto.CurrentBalance != nil {\n\t\tcreditAccount.CurrentBalance = reqDto.CurrentBalance\n\t}\n\terr = creditAccountService.UpdateCreditAccount(ctx, creditAccount)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, creditAccountTransfer.PoToVo(*creditAccount))\n}\n\n// @Summary 删除信用账户\n// @Description 删除信用账户\n// @Tags 信用账户\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCreditAccountReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditAccount/delete [post]\nfunc (controller *CreditAccountController) DeleteCreditAccount(ctx *gin.Context) {\n\treqDto := req.DeleteCreditAccountReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = creditAccountService.DeleteCreditAccount(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询信用账户\n// @Description 查询信用账户\n// @Tags 信用账户\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCreditAccountReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CreditAccountVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditAccount/query [post]\nfunc (controller *CreditAccountController) QueryCreditAccounts(ctx *gin.Context) {\n\treqDto := req.QueryCreditAccountReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := creditAccountService.FindAllCreditAccount(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CreditAccountVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, creditAccountTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询信用账户列表\n// @Description 查询信用账户列表\n// @Tags 信用账户\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCreditAccountReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CreditAccountVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/creditAccount/list [post]\nfunc (a *CreditAccountController) ListCreditAccounts(ctx *gin.Context) {\n\treqDto := req.QueryCreditAccountReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := creditAccountService.FindAllCreditAccountWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CreditAccountVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CreditAccountVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, creditAccountTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CreditAccountRoute struct {\n}\n\nfunc (s *CreditAccountRoute) InitCreditAccountRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcreditAccountController := controller.CreditAccountController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/creditAccount/add\", creditAccountController.AddCreditAccount)       //add\n\t\troute.POST(\"/api/creditAccount/update\", creditAccountController.UpdateCreditAccount) //update\n\t\troute.POST(\"/api/creditAccount/delete\", creditAccountController.DeleteCreditAccount) //delete\n\t\troute.POST(\"/api/creditAccount/query\", creditAccountController.QueryCreditAccounts)  //query\n\t\troute.POST(\"/api/creditAccount/list\", creditAccountController.ListCreditAccounts)   //list\n\t}\n}\n"}]