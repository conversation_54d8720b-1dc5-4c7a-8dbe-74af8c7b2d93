[{"po": "package po\n\n// MarketService 市场服务实体\ntype MarketService struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tServiceName *string `gorm:\"column:service_name;type:varchar(64);default:''\" json:\"serviceName\"` // 服务名称\n\tIsEnabled   *bool   `gorm:\"column:is_enabled;type:bool;default:false\" json:\"isEnabled\"`        // 是否启用\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                      // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                      // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (MarketService) TableName() string {\n\treturn \"market_service\"\n}\n\nfunc (m MarketService) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MarketServiceVO 市场服务值对象\ntype MarketServiceVO struct {\n\tId          string `json:\"id\"`          // ID\n\tServiceName string `json:\"serviceName\"` // 服务名称\n\tIsEnabled   bool   `json:\"isEnabled\"`   // 是否启用\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddMarketServiceReqDto 创建市场服务请求DTO\ntype AddMarketServiceReqDto struct {\n\tServiceName *string `json:\"serviceName\"` // 服务名称\n\tIsEnabled   *bool   `json:\"isEnabled\"`   // 是否启用\n}\n", "req_update": "package req\n\ntype UpdateMarketServiceReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tServiceName *string `json:\"serviceName\"` // 服务名称\n\tIsEnabled   *bool   `json:\"isEnabled\"`   // 是否启用\n}\n", "req_delete": "package req\n\ntype DeleteMarketServiceReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryMarketServiceReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tServiceName *string `json:\"serviceName\"` // 服务名称\n\tIsEnabled   *bool   `json:\"isEnabled\"`   // 是否启用\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MarketServiceTransfer struct {\n}\n\nfunc (transfer *MarketServiceTransfer) PoToVo(po po.MarketService) vo.MarketServiceVO {\n\tvo := vo.MarketServiceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MarketServiceTransfer) VoToPo(vo vo.MarketServiceVO) po.MarketService {\n\tpo := po.MarketService{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketServiceService struct {\n}\n\nfunc (service *MarketServiceService) CreateMarketService(logCtx *gin.Context, marketService *po.MarketService) error {\n\treturn Save(marketService)\n}\n\nfunc (service *MarketServiceService) UpdateMarketService(logCtx *gin.Context, marketService *po.MarketService) error {\n\treturn Update(marketService)\n}\n\nfunc (service *MarketServiceService) DeleteMarketService(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MarketService{Id: &id})\n}\n\nfunc (service *MarketServiceService) FindMarketServiceById(logCtx *gin.Context, id string) (marketService *po.MarketService, err error) {\n\tmarketService = &po.MarketService{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(marketService).Error\n\treturn\n}\n\nfunc (service *MarketServiceService) FindAllMarketService(logCtx *gin.Context, reqDto *req.QueryMarketServiceReqDto) (list *[]po.MarketService, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MarketService{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MarketService{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MarketServiceService) FindAllMarketServiceWithPagination(logCtx *gin.Context, reqDto *req.QueryMarketServiceReqDto) (list *[]po.MarketService, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MarketService{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MarketService{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketServiceController struct{}\n\nvar (\n\tmarketServiceService  = impl.MarketServiceService{}\n\tmarketServiceTransfer = transfer.MarketServiceTransfer{}\n)\n\n// @Summary 添加市场服务\n// @Description 添加市场服务\n// @Tags 市场服务\n// @Accept json\n// @Produce json\n// @Param body body req.AddMarketServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MarketServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/market-service/add [post]\nfunc (controller *MarketServiceController) AddMarketService(ctx *gin.Context) {\n\treqDto := req.AddMarketServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmarketService := po.MarketService{}\n\tif reqDto.ServiceName != nil {\n\t\tmarketService.ServiceName = reqDto.ServiceName\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tmarketService.IsEnabled = reqDto.IsEnabled\n\t}\n\terr = marketServiceService.CreateMarketService(ctx, &marketService)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, marketServiceTransfer.PoToVo(marketService))\n}\n\n// @Summary 更新市场服务\n// @Description 更新市场服务\n// @Tags 市场服务\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMarketServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MarketServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/market-service/update [post]\nfunc (controller *MarketServiceController) UpdateMarketService(ctx *gin.Context) {\n\treqDto := req.UpdateMarketServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmarketService, err := marketServiceService.FindMarketServiceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.ServiceName != nil {\n\t\tmarketService.ServiceName = reqDto.ServiceName\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tmarketService.IsEnabled = reqDto.IsEnabled\n\t}\n\terr = marketServiceService.UpdateMarketService(ctx, marketService)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, marketServiceTransfer.PoToVo(*marketService))\n}\n\n// @Summary 删除市场服务\n// @Description 删除市场服务\n// @Tags 市场服务\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMarketServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/market-service/delete [post]\nfunc (controller *MarketServiceController) DeleteMarketService(ctx *gin.Context) {\n\treqDto := req.DeleteMarketServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = marketServiceService.DeleteMarketService(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询市场服务\n// @Description 查询市场服务\n// @Tags 市场服务\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMarketServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MarketServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/market-service/query [post]\nfunc (controller *MarketServiceController) QueryMarketServices(ctx *gin.Context) {\n\treqDto := req.QueryMarketServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := marketServiceService.FindAllMarketService(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MarketServiceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, marketServiceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询市场服务列表\n// @Description 查询市场服务列表\n// @Tags 市场服务\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMarketServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MarketServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/market-service/list [post]\nfunc (a *MarketServiceController) ListMarketServices(ctx *gin.Context) {\n\treqDto := req.QueryMarketServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := marketServiceService.FindAllMarketServiceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MarketServiceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MarketServiceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, marketServiceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketServiceRoute struct {\n}\n\nfunc (s *MarketServiceRoute) InitMarketServiceRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmarketServiceController := controller.MarketServiceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/market-service/add\", marketServiceController.AddMarketService)    //add\n\t\troute.POST(\"/api/market-service/update\", marketServiceController.UpdateMarketService) //update\n\t\troute.POST(\"/api/market-service/delete\", marketServiceController.DeleteMarketService) //delete\n\t\troute.POST(\"/api/market-service/query\", marketServiceController.QueryMarketServices)     //query\n\t\troute.POST(\"/api/market-service/list\", marketServiceController.ListMarketServices)     //list\n\t}\n}\n"}]