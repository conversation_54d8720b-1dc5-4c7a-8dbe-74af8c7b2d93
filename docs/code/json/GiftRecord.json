[{"po": "package po\n\n// GiftRecord 赠送记录实体\ntype GiftRecord struct {\n\tId                    *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                     // ID\n\tType                  *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`                     // 赠送类型\n\tGiftTime             *int64  `gorm:\"column:gift_time;type:int;default:0\" json:\"giftTime\"`                    // 赠送时间\n\tRequiresAuthorization *bool   `gorm:\"column:requires_authorization;type:bool;default:false\" json:\"requiresAuthorization\"` // 是否需要授权\n\tEmployeeId           *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"`         // 操作员工ID\n\tCtime                *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                           // 创建时间戳\n\tUtime                *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                           // 更新时间戳\n\tState                *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态值\n\tVersion              *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本号\n}\n\n// TableName 设置表名\nfunc (GiftRecord) TableName() string {\n\treturn \"gift_record\"\n}\n\nfunc (g GiftRecord) GetId() string {\n\treturn *g.Id\n}\n", "req_add": "package req\n\n// AddGiftRecordReqDto 创建赠送记录请求DTO\ntype AddGiftRecordReqDto struct {\n\tType                  *string `json:\"type\"`                  // 赠送类型\n\tGiftTime             *int64  `json:\"giftTime\"`             // 赠送时间\n\tRequiresAuthorization *bool   `json:\"requiresAuthorization\"` // 是否需要授权\n\tEmployeeId           *string `json:\"employeeId\"`           // 操作员工ID\n}\n", "req_update": "package req\n\ntype UpdateGiftRecordReqDto struct {\n\tId                    *string `json:\"id\"`                    // ID\n\tType                  *string `json:\"type\"`                  // 赠送类型\n\tGiftTime             *int64  `json:\"giftTime\"`             // 赠送时间\n\tRequiresAuthorization *bool   `json:\"requiresAuthorization\"` // 是否需要授权\n\tEmployeeId           *string `json:\"employeeId\"`           // 操作员工ID\n}\n", "req_delete": "package req\n\ntype DeleteGiftRecordReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryGiftRecordReqDto struct {\n\tId                    *string `json:\"id\"`                    // ID\n\tType                  *string `json:\"type\"`                  // 赠送类型\n\tGiftTime             *int64  `json:\"giftTime\"`             // 赠送时间\n\tRequiresAuthorization *bool   `json:\"requiresAuthorization\"` // 是否需要授权\n\tEmployeeId           *string `json:\"employeeId\"`           // 操作员工ID\n\tPageNum              *int    `json:\"pageNum\"`              // 页码\n\tPageSize             *int    `json:\"pageSize\"`             // 每页记录数\n}\n", "vo": "package vo\n\n// GiftRecordVO 赠送记录值对象\ntype GiftRecordVO struct {\n\tId                    string `json:\"id\"`                    // ID\n\tType                  string `json:\"type\"`                  // 赠送类型\n\tGiftTime             int64  `json:\"giftTime\"`             // 赠送时间\n\tRequiresAuthorization bool   `json:\"requiresAuthorization\"` // 是否需要授权\n\tEmployeeId           string `json:\"employeeId\"`           // 操作员工ID\n\tCtime                int64  `json:\"ctime\"`                // 创建时间戳\n\tUtime                int64  `json:\"utime\"`                // 更新时间戳\n\tState                int    `json:\"state\"`                // 状态值\n\tVersion              int    `json:\"version\"`              // 版本号\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype GiftRecordTransfer struct {\n}\n\nfunc (transfer *GiftRecordTransfer) PoToVo(po po.GiftRecord) vo.GiftRecordVO {\n\tvo := vo.GiftRecordVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *GiftRecordTransfer) VoToPo(vo vo.GiftRecordVO) po.GiftRecord {\n\tpo := po.GiftRecord{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype GiftRecordService struct {\n}\n\nfunc (service *GiftRecordService) CreateGiftRecord(logCtx *gin.Context, giftRecord *po.GiftRecord) error {\n\treturn Save(giftRecord)\n}\n\nfunc (service *GiftRecordService) UpdateGiftRecord(logCtx *gin.Context, giftRecord *po.GiftRecord) error {\n\treturn Update(giftRecord)\n}\n\nfunc (service *GiftRecordService) DeleteGiftRecord(logCtx *gin.Context, id string) error {\n\treturn Delete(po.GiftRecord{Id: &id})\n}\n\nfunc (service *GiftRecordService) FindGiftRecordById(logCtx *gin.Context, id string) (giftRecord *po.GiftRecord, err error) {\n\tgiftRecord = &po.GiftRecord{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(giftRecord).Error\n\treturn\n}\n\nfunc (service *GiftRecordService) FindAllGiftRecord(logCtx *gin.Context, reqDto *req.QueryGiftRecordReqDto) (list *[]po.GiftRecord, err error) {\n\tdb := model.DBSlave.Self.Model(&po.GiftRecord{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.GiftRecord{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *GiftRecordService) FindAllGiftRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryGiftRecordReqDto) (list *[]po.GiftRecord, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.GiftRecord{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.GiftRecord{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype GiftRecordController struct{}\n\nvar (\n\tgiftRecordService  = impl.GiftRecordService{}\n\tgiftRecordTransfer = transfer.GiftRecordTransfer{}\n)\n\n// @Summary 添加赠送记录\n// @Description 添加赠送记录\n// @Tags 赠送记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddGiftRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.GiftRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-record/add [post]\nfunc (controller *GiftRecordController) AddGiftRecord(ctx *gin.Context) {\n\treqDto := req.AddGiftRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tgiftRecord := po.GiftRecord{}\n\tif reqDto.Type != nil {\n\t\tgiftRecord.Type = reqDto.Type\n\t}\n\tif reqDto.GiftTime != nil {\n\t\tgiftRecord.GiftTime = reqDto.GiftTime\n\t}\n\tif reqDto.RequiresAuthorization != nil {\n\t\tgiftRecord.RequiresAuthorization = reqDto.RequiresAuthorization\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tgiftRecord.EmployeeId = reqDto.EmployeeId\n\t}\n\terr = giftRecordService.CreateGiftRecord(ctx, &giftRecord)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, giftRecordTransfer.PoToVo(giftRecord))\n}\n\n// @Summary 更新赠送记录\n// @Description 更新赠送记录\n// @Tags 赠送记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateGiftRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.GiftRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-record/update [post]\nfunc (controller *GiftRecordController) UpdateGiftRecord(ctx *gin.Context) {\n\treqDto := req.UpdateGiftRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tgiftRecord, err := giftRecordService.FindGiftRecordById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Type != nil {\n\t\tgiftRecord.Type = reqDto.Type\n\t}\n\tif reqDto.GiftTime != nil {\n\t\tgiftRecord.GiftTime = reqDto.GiftTime\n\t}\n\tif reqDto.RequiresAuthorization != nil {\n\t\tgiftRecord.RequiresAuthorization = reqDto.RequiresAuthorization\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\tgiftRecord.EmployeeId = reqDto.EmployeeId\n\t}\n\terr = giftRecordService.UpdateGiftRecord(ctx, giftRecord)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, giftRecordTransfer.PoToVo(*giftRecord))\n}\n\n// @Summary 删除赠送记录\n// @Description 删除赠送记录\n// @Tags 赠送记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteGiftRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-record/delete [post]\nfunc (controller *GiftRecordController) DeleteGiftRecord(ctx *gin.Context) {\n\treqDto := req.DeleteGiftRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = giftRecordService.DeleteGiftRecord(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询赠送记录\n// @Description 查询赠送记录\n// @Tags 赠送记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryGiftRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.GiftRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-record/query [post]\nfunc (controller *GiftRecordController) QueryGiftRecords(ctx *gin.Context) {\n\treqDto := req.QueryGiftRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := giftRecordService.FindAllGiftRecord(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.GiftRecordVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, giftRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询赠送记录列表\n// @Description 查询赠送记录列表\n// @Tags 赠送记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryGiftRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.GiftRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/gift-record/list [post]\nfunc (a *GiftRecordController) ListGiftRecords(ctx *gin.Context) {\n\treqDto := req.QueryGiftRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := giftRecordService.FindAllGiftRecordWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.GiftRecordVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.GiftRecordVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, giftRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype GiftRecordRoute struct {\n}\n\nfunc (s *GiftRecordRoute) InitGiftRecordRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tgiftRecordController := controller.GiftRecordController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/gift-record/add\", giftRecordController.AddGiftRecord)    //add\n\t\troute.POST(\"/api/gift-record/update\", giftRecordController.UpdateGiftRecord) //update\n\t\troute.POST(\"/api/gift-record/delete\", giftRecordController.DeleteGiftRecord) //delete\n\t\troute.POST(\"/api/gift-record/query\", giftRecordController.QueryGiftRecords)     //query\n\t\troute.POST(\"/api/gift-record/list\", giftRecordController.ListGiftRecords)     //list\n\t}\n}\n"}]