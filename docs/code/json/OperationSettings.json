[{"po": "package po\n\n// OperationSettings 运营设置实体\ntype OperationSettings struct {\n\tId                               *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tCheckoutMode                     *string  `gorm:\"column:checkout_mode;type:varchar(64)\" json:\"checkoutMode\"`                // 结账方式\n\tAllowCashierSelectMode           *bool    `gorm:\"column:allow_cashier_select_mode;type:bool\" json:\"allowCashierSelectMode\"` // 是否允许收银员选择模式\n\tSupermarketCheckoutMode          *string  `gorm:\"column:supermarket_checkout_mode;type:varchar(64)\" json:\"supermarketCheckoutMode\"` // 超市结账方式\n\tRoundingType                     *string  `gorm:\"column:rounding_type;type:varchar(64)\" json:\"roundingType\"`                // 四舍五入类型\n\tWineVerificationMethod           *string  `gorm:\"column:wine_verification_method;type:varchar(64)\" json:\"wineVerificationMethod\"` // 酒水核销方式\n\tRequireRoomForWine              *bool    `gorm:\"column:require_room_for_wine;type:bool\" json:\"requireRoomForWine\"`       // 酒水是否需要绑定房间\n\tAllowOverbuyingEstimatedClearance *bool    `gorm:\"column:allow_overbuying_estimated_clearance;type:bool\" json:\"allowOverbuyingEstimatedClearance\"` // 是否允许超额购买预估清台\n\tAutoCancelClearanceNextDay       *bool    `gorm:\"column:auto_cancel_clearance_next_day;type:bool\" json:\"autoCancelClearanceNextDay\"` // 是否自动取消次日清台\n\tEnableCrossBillingAdjustment     *bool    `gorm:\"column:enable_cross_billing_adjustment;type:bool\" json:\"enableCrossBillingAdjustment\"` // 是否启用跨账单调整\n\tAutoSwitchToTimeBilling         *bool    `gorm:\"column:auto_switch_to_time_billing;type:bool\" json:\"autoSwitchToTimeBilling\"` // 是否自动切换到时间计费\n\tEnableSplitBilling              *bool    `gorm:\"column:enable_split_billing;type:bool\" json:\"enableSplitBilling\"`       // 是否启用拆分计费\n\tSplitBillingRoomPercentage      *float32 `gorm:\"column:split_billing_room_percentage;type:float\" json:\"splitBillingRoomPercentage\"` // 拆分计费房间百分比\n\tFirstBillingDuration            *int     `gorm:\"column:first_billing_duration;type:int\" json:\"firstBillingDuration\"`     // 首次计费时长\n\tSecondBillingDuration           *int     `gorm:\"column:second_billing_duration;type:int\" json:\"secondBillingDuration\"`   // 第二次计费时长\n\tMinRenewalDuration              *int     `gorm:\"column:min_renewal_duration;type:int\" json:\"minRenewalDuration\"`       // 最小续费时长\n\tGuestDuration                   *int     `gorm:\"column:guest_duration;type:int\" json:\"guestDuration\"`               // 客人时长\n\tReopenDuration                  *int     `gorm:\"column:reopen_duration;type:int\" json:\"reopenDuration\"`             // 重新开房时长\n\tCancelOpenDuration              *int     `gorm:\"column:cancel_open_duration;type:int\" json:\"cancelOpenDuration\"`       // 取消开房时长\n\tRefundDuration                  *int     `gorm:\"column:refund_duration;type:int\" json:\"refundDuration\"`             // 退款时长\n\tRoomCleanToIdleDuration         *int     `gorm:\"column:room_clean_to_idle_duration;type:int\" json:\"roomCleanToIdleDuration\"` // 房间清洁到空闲时长\n\tReservationExpirationDuration    *int     `gorm:\"column:reservation_expiration_duration;type:int\" json:\"reservationExpirationDuration\"` // 预订到期时长\n\tDiscountMeetMinConsumption      *bool    `gorm:\"column:discount_meet_min_consumption;type:bool\" json:\"discountMeetMinConsumption\"` // 折扣是否满足最低消费\n\tDiscountOnlyExcess              *bool    `gorm:\"column:discount_only_excess;type:bool\" json:\"discountOnlyExcess\"`       // 折扣是否仅限超出部分\n\tAutoLockRoomAfterCheckout       *bool    `gorm:\"column:auto_lock_room_after_checkout;type:bool\" json:\"autoLockRoomAfterCheckout\"` // 结账后是否自动锁定房间\n\tRoomChangeRule                  *string  `gorm:\"column:room_change_rule;type:varchar(64)\" json:\"roomChangeRule\"`         // 房间变更规则\n\tMemberDiscountMethod            *string  `gorm:\"column:member_discount_method;type:varchar(64)\" json:\"memberDiscountMethod\"` // 会员折扣方式\n\tMemberCardPaymentMode           *string  `gorm:\"column:member_card_payment_mode;type:varchar(64)\" json:\"memberCardPaymentMode\"` // 会员卡支付方式\n\tMemberCardVerificationMethod    *string  `gorm:\"column:member_card_verification_method;type:varchar(64)\" json:\"memberCardVerificationMethod\"` // 会员卡验证方式\n\tInheritMemberCardMode           *string  `gorm:\"column:inherit_member_card_mode;type:varchar(64)\" json:\"inheritMemberCardMode\"` // 继承会员卡模式\n\tRoomPriceMemberDiscountMethod   *string  `gorm:\"column:room_price_member_discount_method;type:varchar(64)\" json:\"roomPriceMemberDiscountMethod\"` // 房间价格会员折扣方式\n\tProductPriceMemberDiscountMethod *string  `gorm:\"column:product_price_member_discount_method;type:varchar(64)\" json:\"productPriceMemberDiscountMethod\"` // 产品价格会员折扣方式\n\tOrderScreenHoldMode             *string  `gorm:\"column:order_screen_hold_mode;type:varchar(64)\" json:\"orderScreenHoldMode\"` // 订单屏幕保持模式\n\tAllowMinConsumptionDifference   *bool    `gorm:\"column:allow_min_consumption_difference;type:bool\" json:\"allowMinConsumptionDifference\"` // 是否允许最低消费差异\n\tMobileOrderCheckoutMode         *string  `gorm:\"column:mobile_order_checkout_mode;type:varchar(64)\" json:\"mobileOrderCheckoutMode\"` // 手机点单结账方式\n\tMobileOrderWineStorageMode      *string  `gorm:\"column:mobile_order_wine_storage_mode;type:varchar(64)\" json:\"mobileOrderWineStorageMode\"` // 手机点单酒水寄存方式\n\tMobileOrderWineRetrievalMode    *string  `gorm:\"column:mobile_order_wine_retrieval_mode;type:varchar(64)\" json:\"mobileOrderWineRetrievalMode\"` // 手机点单酒水取回方式\n\tMobileOrderOnlyStoreOrderedItems *bool    `gorm:\"column:mobile_order_only_store_ordered_items;type:bool\" json:\"mobileOrderOnlyStoreOrderedItems\"` // 手机点单是否只存储已点商品\n\tCallProcessingTimeout           *int     `gorm:\"column:call_processing_timeout;type:int\" json:\"callProcessingTimeout\"`   // 呼叫处理超时时间\n\tMobileOrderCashPaymentMode      *string  `gorm:\"column:mobile_order_cash_payment_mode;type:varchar(64)\" json:\"mobileOrderCashPaymentMode\"` // 手机点单现金支付方式\n\tCashierDataRetentionDays        *int     `gorm:\"column:cashier_data_retention_days;type:int\" json:\"cashierDataRetentionDays\"` // 收银员数据保留天数\n\tAllowCashierAddEmployee         *bool    `gorm:\"column:allow_cashier_add_employee;type:bool\" json:\"allowCashierAddEmployee\"` // 是否允许收银员添加员工\n\tRestrictAllMemberQuery          *bool    `gorm:\"column:restrict_all_member_query;type:bool\" json:\"restrictAllMemberQuery\"` // 是否限制所有会员查询\n\tEnableMultipleSalespersons      *bool    `gorm:\"column:enable_multiple_salespersons;type:bool\" json:\"enableMultipleSalespersons\"` // 是否启用多个销售员\n\tEnableReturnConfirmation        *bool    `gorm:\"column:enable_return_confirmation;type:bool\" json:\"enableReturnConfirmation\"` // 是否启用退货确认\n\tRequireRoomForOrder             *bool    `gorm:\"column:require_room_for_order;type:bool\" json:\"requireRoomForOrder\"`     // 下单是否需要房间\n\tDefaultSendReservationSMS       *bool    `gorm:\"column:default_send_reservation_sms;type:bool\" json:\"defaultSendReservationSMS\"` // 是否默认发送预订短信\n\tShowInventoryOnCashier          *bool    `gorm:\"column:show_inventory_on_cashier;type:bool\" json:\"showInventoryOnCashier\"` // 是否在收银员端显示库存\n\tCounterOrderMode                *string  `gorm:\"column:counter_order_mode;type:varchar(64)\" json:\"counterOrderMode\"`       // 柜台点单模式\n\tCounterAutoLogoutTime           *int     `gorm:\"column:counter_auto_logout_time;type:int\" json:\"counterAutoLogoutTime\"`   // 柜台自动登出时间\n\tCtime                          *int64   `gorm:\"column:ctime;type:int\" json:\"ctime\"`                               // 创建时间\n\tUtime                          *int64   `gorm:\"column:utime;type:int\" json:\"utime\"`                               // 更新时间\n\tState                          *int     `gorm:\"column:state;type:int\" json:\"state\"`                               // 状态\n\tVersion                        *int     `gorm:\"column:version;type:int\" json:\"version\"`                           // 版本\n}\n\n// TableName 设置表名\nfunc (OperationSettings) TableName() string {\n\treturn \"operation_settings\"\n}\n\nfunc (o OperationSettings) GetId() string {\n\treturn *o.Id\n}", "vo": "package vo\n\n// OperationSettingsVO 运营设置值对象\ntype OperationSettingsVO struct {\n\tId                               string  `json:\"id\"`                               // ID\n\tCheckoutMode                     string  `json:\"checkoutMode\"`                     // 结账方式\n\tAllowCashierSelectMode           bool    `json:\"allowCashierSelectMode\"`           // 是否允许收银员选择模式\n\tSupermarketCheckoutMode          string  `json:\"supermarketCheckoutMode\"`          // 超市结账方式\n\tRoundingType                     string  `json:\"roundingType\"`                     // 四舍五入类型\n\tWineVerificationMethod           string  `json:\"wineVerificationMethod\"`           // 酒水核销方式\n\tRequireRoomForWine              bool    `json:\"requireRoomForWine\"`              // 酒水是否需要绑定房间\n\tAllowOverbuyingEstimatedClearance bool    `json:\"allowOverbuyingEstimatedClearance\"` // 是否允许超额购买预估清台\n\tAutoCancelClearanceNextDay       bool    `json:\"autoCancelClearanceNextDay\"`       // 是否自动取消次日清台\n\tEnableCrossBillingAdjustment     bool    `json:\"enableCrossBillingAdjustment\"`     // 是否启用跨账单调整\n\tAutoSwitchToTimeBilling         bool    `json:\"autoSwitchToTimeBilling\"`         // 是否自动切换到时间计费\n\tEnableSplitBilling              bool    `json:\"enableSplitBilling\"`              // 是否启用拆分计费\n\tSplitBillingRoomPercentage      float32 `json:\"splitBillingRoomPercentage\"`      // 拆分计费房间百分比\n\tFirstBillingDuration            int     `json:\"firstBillingDuration\"`            // 首次计费时长\n\tSecondBillingDuration           int     `json:\"secondBillingDuration\"`           // 第二次计费时长\n\tMinRenewalDuration              int     `json:\"minRenewalDuration\"`              // 最小续费时长\n\tGuestDuration                   int     `json:\"guestDuration\"`                   // 客人时长\n\tReopenDuration                  int     `json:\"reopenDuration\"`                  // 重新开房时长\n\tCancelOpenDuration              int     `json:\"cancelOpenDuration\"`              // 取消开房时长\n\tRefundDuration                  int     `json:\"refundDuration\"`                  // 退款时长\n\tRoomCleanToIdleDuration         int     `json:\"roomCleanToIdleDuration\"`         // 房间清洁到空闲时长\n\tReservationExpirationDuration    int     `json:\"reservationExpirationDuration\"`    // 预订到期时长\n\tDiscountMeetMinConsumption      bool    `json:\"discountMeetMinConsumption\"`      // 折扣是否满足最低消费\n\tDiscountOnlyExcess              bool    `json:\"discountOnlyExcess\"`              // 折扣是否仅限超出部分\n\tAutoLockRoomAfterCheckout       bool    `json:\"autoLockRoomAfterCheckout\"`       // 结账后是否自动锁定房间\n\tRoomChangeRule                  string  `json:\"roomChangeRule\"`                  // 房间变更规则\n\tMemberDiscountMethod            string  `json:\"memberDiscountMethod\"`            // 会员折扣方式\n\tMemberCardPaymentMode           string  `json:\"memberCardPaymentMode\"`           // 会员卡支付方式\n\tMemberCardVerificationMethod    string  `json:\"memberCardVerificationMethod\"`    // 会员卡验证方式\n\tInheritMemberCardMode           string  `json:\"inheritMemberCardMode\"`           // 继承会员卡模式\n\tRoomPriceMemberDiscountMethod   string  `json:\"roomPriceMemberDiscountMethod\"`   // 房间价格会员折扣方式\n\tProductPriceMemberDiscountMethod string  `json:\"productPriceMemberDiscountMethod\"` // 产品价格会员折扣方式\n\tOrderScreenHoldMode             string  `json:\"orderScreenHoldMode\"`             // 订单屏幕保持模式\n\tAllowMinConsumptionDifference   bool    `json:\"allowMinConsumptionDifference\"`   // 是否允许最低消费差异\n\tMobileOrderCheckoutMode         string  `json:\"mobileOrderCheckoutMode\"`         // 手机点单结账方式\n\tMobileOrderWineStorageMode      string  `json:\"mobileOrderWineStorageMode\"`      // 手机点单酒水寄存方式\n\tMobileOrderWineRetrievalMode    string  `json:\"mobileOrderWineRetrievalMode\"`    // 手机点单酒水取回方式\n\tMobileOrderOnlyStoreOrderedItems bool    `json:\"mobileOrderOnlyStoreOrderedItems\"` // 手机点单是否只存储已点商品\n\tCallProcessingTimeout           int     `json:\"callProcessingTimeout\"`           // 呼叫处理超时时间\n\tMobileOrderCashPaymentMode      string  `json:\"mobileOrderCashPaymentMode\"`      // 手机点单现金支付方式\n\tCashierDataRetentionDays        int     `json:\"cashierDataRetentionDays\"`        // 收银员数据保留天数\n\tAllowCashierAddEmployee         bool    `json:\"allowCashierAddEmployee\"`         // 是否允许收银员添加员工\n\tRestrictAllMemberQuery          bool    `json:\"restrictAllMemberQuery\"`          // 是否限制所有会员查询\n\tEnableMultipleSalespersons      bool    `json:\"enableMultipleSalespersons\"`      // 是否启用多个销售员\n\tEnableReturnConfirmation        bool    `json:\"enableReturnConfirmation\"`        // 是否启用退货确认\n\tRequireRoomForOrder             bool    `json:\"requireRoomForOrder\"`             // 下单是否需要房间\n\tDefaultSendReservationSMS       bool    `json:\"defaultSendReservationSMS\"`       // 是否默认发送预订短信\n\tShowInventoryOnCashier          bool    `json:\"showInventoryOnCashier\"`          // 是否在收银员端显示库存\n\tCounterOrderMode                string  `json:\"counterOrderMode\"`                // 柜台点单模式\n\tCounterAutoLogoutTime           int     `json:\"counterAutoLogoutTime\"`           // 柜台自动登出时间\n\tCtime                          int64   `json:\"ctime\"`                          // 创建时间\n\tUtime                          int64   `json:\"utime\"`                          // 更新时间\n\tState                          int     `json:\"state\"`                          // 状态\n\tVersion                        int     `json:\"version\"`                        // 版本\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype OperationSettingsTransfer struct {\n}\n\nfunc (transfer *OperationSettingsTransfer) PoToVo(po po.OperationSettings) vo.OperationSettingsVO {\n\tvo := vo.OperationSettingsVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *OperationSettingsTransfer) VoToPo(vo vo.OperationSettingsVO) po.OperationSettings {\n\tpo := po.OperationSettings{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "req_add": "package req\n\n// AddOperationSettingsReqDto 创建运营设置请求DTO\ntype AddOperationSettingsReqDto struct {\n\tCheckoutMode                     *string  `json:\"checkoutMode\"`                     // 结账方式\n\tAllowCashierSelectMode           *bool    `json:\"allowCashierSelectMode\"`           // 是否允许收银员选择模式\n\tSupermarketCheckoutMode          *string  `json:\"supermarketCheckoutMode\"`          // 超市结账方式\n\tRoundingType                     *string  `json:\"roundingType\"`                     // 四舍五入类型\n\tWineVerificationMethod           *string  `json:\"wineVerificationMethod\"`           // 酒水核销方式\n\tRequireRoomForWine              *bool    `json:\"requireRoomForWine\"`              // 酒水是否需要绑定房间\n\tAllowOverbuyingEstimatedClearance *bool    `json:\"allowOverbuyingEstimatedClearance\"` // 是否允许超额购买预估清台\n\tAutoCancelClearanceNextDay       *bool    `json:\"autoCancelClearanceNextDay\"`       // 是否自动取消次日清台\n\tEnableCrossBillingAdjustment     *bool    `json:\"enableCrossBillingAdjustment\"`     // 是否启用跨账单调整\n\tAutoSwitchToTimeBilling         *bool    `json:\"autoSwitchToTimeBilling\"`         // 是否自动切换到时间计费\n\tEnableSplitBilling              *bool    `json:\"enableSplitBilling\"`              // 是否启用拆分计费\n\tSplitBillingRoomPercentage      *float32 `json:\"splitBillingRoomPercentage\"`      // 拆分计费房间百分比\n\tFirstBillingDuration            *int     `json:\"firstBillingDuration\"`            // 首次计费时长\n\tSecondBillingDuration           *int     `json:\"secondBillingDuration\"`           // 第二次计费时长\n\tMinRenewalDuration              *int     `json:\"minRenewalDuration\"`              // 最小续费时长\n\tGuestDuration                   *int     `json:\"guestDuration\"`                   // 客人时长\n\tReopenDuration                  *int     `json:\"reopenDuration\"`                  // 重新开房时长\n\tCancelOpenDuration              *int     `json:\"cancelOpenDuration\"`              // 取消开房时长\n\tRefundDuration                  *int     `json:\"refundDuration\"`                  // 退款时长\n\tRoomCleanToIdleDuration         *int     `json:\"roomCleanToIdleDuration\"`         // 房间清洁到空闲时长\n\tReservationExpirationDuration    *int     `json:\"reservationExpirationDuration\"`    // 预订到期时长\n\tDiscountMeetMinConsumption      *bool    `json:\"discountMeetMinConsumption\"`      // 折扣是否满足最低消费\n\tDiscountOnlyExcess              *bool    `json:\"discountOnlyExcess\"`              // 折扣是否仅限超出部分\n\tAutoLockRoomAfterCheckout       *bool    `json:\"autoLockRoomAfterCheckout\"`       // 结账后是否自动锁定房间\n\tRoomChangeRule                  *string  `json:\"roomChangeRule\"`                  // 房间变更规则\n\tMemberDiscountMethod            *string  `json:\"memberDiscountMethod\"`            // 会员折扣方式\n\tMemberCardPaymentMode           *string  `json:\"memberCardPaymentMode\"`           // 会员卡支付方式\n\tMemberCardVerificationMethod    *string  `json:\"memberCardVerificationMethod\"`    // 会员卡验证方式\n\tInheritMemberCardMode           *string  `json:\"inheritMemberCardMode\"`           // 继承会员卡模式\n\tRoomPriceMemberDiscountMethod   *string  `json:\"roomPriceMemberDiscountMethod\"`   // 房间价格会员折扣方式\n\tProductPriceMemberDiscountMethod *string  `json:\"productPriceMemberDiscountMethod\"` // 产品价格会员折扣方式\n\tOrderScreenHoldMode             *string  `json:\"orderScreenHoldMode\"`             // 订单屏幕保持模式\n\tAllowMinConsumptionDifference   *bool    `json:\"allowMinConsumptionDifference\"`   // 是否允许最低消费差异\n\tMobileOrderCheckoutMode         *string  `json:\"mobileOrderCheckoutMode\"`         // 手机点单结账方式\n\tMobileOrderWineStorageMode      *string  `json:\"mobileOrderWineStorageMode\"`      // 手机点单酒水寄存方式\n\tMobileOrderWineRetrievalMode    *string  `json:\"mobileOrderWineRetrievalMode\"`    // 手机点单酒水取回方式\n\tMobileOrderOnlyStoreOrderedItems *bool    `json:\"mobileOrderOnlyStoreOrderedItems\"` // 手机点单是否只存储已点商品\n\tCallProcessingTimeout           *int     `json:\"callProcessingTimeout\"`           // 呼叫处理超时时间\n\tMobileOrderCashPaymentMode      *string  `json:\"mobileOrderCashPaymentMode\"`      // 手机点单现金支付方式\n\tCashierDataRetentionDays        *int     `json:\"cashierDataRetentionDays\"`        // 收银员数据保留天数\n\tAllowCashierAddEmployee         *bool    `json:\"allowCashierAddEmployee\"`         // 是否允许收银员添加员工\n\tRestrictAllMemberQuery          *bool    `json:\"restrictAllMemberQuery\"`          // 是否限制所有会员查询\n\tEnableMultipleSalespersons      *bool    `json:\"enableMultipleSalespersons\"`      // 是否启用多个销售员\n\tEnableReturnConfirmation        *bool    `json:\"enableReturnConfirmation\"`        // 是否启用退货确认\n\tRequireRoomForOrder             *bool    `json:\"requireRoomForOrder\"`             // 下单是否需要房间\n\tDefaultSendReservationSMS       *bool    `json:\"defaultSendReservationSMS\"`       // 是否默认发送预订短信\n\tShowInventoryOnCashier          *bool    `json:\"showInventoryOnCashier\"`          // 是否在收银员端显示库存\n\tCounterOrderMode                *string  `json:\"counterOrderMode\"`                // 柜台点单模式\n\tCounterAutoLogoutTime           *int     `json:\"counterAutoLogoutTime\"`           // 柜台自动登出时间\n}", "req_update": "package req\n\ntype UpdateOperationSettingsReqDto struct {\n\tId                               *string  `json:\"id\"`                               // ID\n\tCheckoutMode                     *string  `json:\"checkoutMode\"`                     // 结账方式\n\tAllowCashierSelectMode           *bool    `json:\"allowCashierSelectMode\"`           // 是否允许收银员选择模式\n\tSupermarketCheckoutMode          *string  `json:\"supermarketCheckoutMode\"`          // 超市结账方式\n\tRoundingType                     *string  `json:\"roundingType\"`                     // 四舍五入类型\n\tWineVerificationMethod           *string  `json:\"wineVerificationMethod\"`           // 酒水核销方式\n\tRequireRoomForWine              *bool    `json:\"requireRoomForWine\"`              // 酒水是否需要绑定房间\n\tAllowOverbuyingEstimatedClearance *bool    `json:\"allowOverbuyingEstimatedClearance\"` // 是否允许超额购买预估清台\n\tAutoCancelClearanceNextDay       *bool    `json:\"autoCancelClearanceNextDay\"`       // 是否自动取消次日清台\n\tEnableCrossBillingAdjustment     *bool    `json:\"enableCrossBillingAdjustment\"`     // 是否启用跨账单调整\n\tAutoSwitchToTimeBilling         *bool    `json:\"autoSwitchToTimeBilling\"`         // 是否自动切换到时间计费\n\tEnableSplitBilling              *bool    `json:\"enableSplitBilling\"`              // 是否启用拆分计费\n\tSplitBillingRoomPercentage      *float32 `json:\"splitBillingRoomPercentage\"`      // 拆分计费房间百分比\n\tFirstBillingDuration            *int     `json:\"firstBillingDuration\"`            // 首次计费时长\n\tSecondBillingDuration           *int     `json:\"secondBillingDuration\"`           // 第二次计费时长\n\tMinRenewalDuration              *int     `json:\"minRenewalDuration\"`              // 最小续费时长\n\tGuestDuration                   *int     `json:\"guestDuration\"`                   // 客人时长\n\tReopenDuration                  *int     `json:\"reopenDuration\"`                  // 重新开房时长\n\tCancelOpenDuration              *int     `json:\"cancelOpenDuration\"`              // 取消开房时长\n\tRefundDuration                  *int     `json:\"refundDuration\"`                  // 退款时长\n\tRoomCleanToIdleDuration         *int     `json:\"roomCleanToIdleDuration\"`         // 房间清洁到空闲时长\n\tReservationExpirationDuration    *int     `json:\"reservationExpirationDuration\"`    // 预订到期时长\n\tDiscountMeetMinConsumption      *bool    `json:\"discountMeetMinConsumption\"`      // 折扣是否满足最低消费\n\tDiscountOnlyExcess              *bool    `json:\"discountOnlyExcess\"`              // 折扣是否仅限超出部分\n\tAutoLockRoomAfterCheckout       *bool    `json:\"autoLockRoomAfterCheckout\"`       // 结账后是否自动锁定房间\n\tRoomChangeRule                  *string  `json:\"roomChangeRule\"`                  // 房间变更规则\n\tMemberDiscountMethod            *string  `json:\"memberDiscountMethod\"`            // 会员折扣方式\n\tMemberCardPaymentMode           *string  `json:\"memberCardPaymentMode\"`           // 会员卡支付方式\n\tMemberCardVerificationMethod    *string  `json:\"memberCardVerificationMethod\"`    // 会员卡验证方式\n\tInheritMemberCardMode           *string  `json:\"inheritMemberCardMode\"`           // 继承会员卡模式\n\tRoomPriceMemberDiscountMethod   *string  `json:\"roomPriceMemberDiscountMethod\"`   // 房间价格会员折扣方式\n\tProductPriceMemberDiscountMethod *string  `json:\"productPriceMemberDiscountMethod\"` // 产品价格会员折扣方式\n\tOrderScreenHoldMode             *string  `json:\"orderScreenHoldMode\"`             // 订单屏幕保持模式\n\tAllowMinConsumptionDifference   *bool    `json:\"allowMinConsumptionDifference\"`   // 是否允许最低消费差异\n\tMobileOrderCheckoutMode         *string  `json:\"mobileOrderCheckoutMode\"`         // 手机点单结账方式\n\tMobileOrderWineStorageMode      *string  `json:\"mobileOrderWineStorageMode\"`      // 手机点单酒水寄存方式\n\tMobileOrderWineRetrievalMode    *string  `json:\"mobileOrderWineRetrievalMode\"`    // 手机点单酒水取回方式\n\tMobileOrderOnlyStoreOrderedItems *bool    `json:\"mobileOrderOnlyStoreOrderedItems\"` // 手机点单是否只存储已点商品\n\tCallProcessingTimeout           *int     `json:\"callProcessingTimeout\"`           // 呼叫处理超时时间\n\tMobileOrderCashPaymentMode      *string  `json:\"mobileOrderCashPaymentMode\"`      // 手机点单现金支付方式\n\tCashierDataRetentionDays        *int     `json:\"cashierDataRetentionDays\"`        // 收银员数据保留天数\n\tAllowCashierAddEmployee         *bool    `json:\"allowCashierAddEmployee\"`         // 是否允许收银员添加员工\n\tRestrictAllMemberQuery          *bool    `json:\"restrictAllMemberQuery\"`          // 是否限制所有会员查询\n\tEnableMultipleSalespersons      *bool    `json:\"enableMultipleSalespersons\"`      // 是否启用多个销售员\n\tEnableReturnConfirmation        *bool    `json:\"enableReturnConfirmation\"`        // 是否启用退货确认\n\tRequireRoomForOrder             *bool    `json:\"requireRoomForOrder\"`             // 下单是否需要房间\n\tDefaultSendReservationSMS       *bool    `json:\"defaultSendReservationSMS\"`       // 是否默认发送预订短信\n\tShowInventoryOnCashier          *bool    `json:\"showInventoryOnCashier\"`          // 是否在收银员端显示库存\n\tCounterOrderMode                *string  `json:\"counterOrderMode\"`                // 柜台点单模式\n\tCounterAutoLogoutTime           *int     `json:\"counterAutoLogoutTime\"`           // 柜台自动登出时间\n}", "req_delete": "package req\n\ntype DeleteOperationSettingsReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryOperationSettingsReqDto struct {\n\tId                               *string  `json:\"id\"`                               // ID\n\tCheckoutMode                     *string  `json:\"checkoutMode\"`                     // 结账方式\n\tAllowCashierSelectMode           *bool    `json:\"allowCashierSelectMode\"`           // 是否允许收银员选择模式\n\tSupermarketCheckoutMode          *string  `json:\"supermarketCheckoutMode\"`          // 超市结账方式\n\tRoundingType                     *string  `json:\"roundingType\"`                     // 四舍五入类型\n\tWineVerificationMethod           *string  `json:\"wineVerificationMethod\"`           // 酒水核销方式\n\tRequireRoomForWine              *bool    `json:\"requireRoomForWine\"`              // 酒水是否需要绑定房间\n\tAllowOverbuyingEstimatedClearance *bool    `json:\"allowOverbuyingEstimatedClearance\"` // 是否允许超额购买预估清台\n\tAutoCancelClearanceNextDay       *bool    `json:\"autoCancelClearanceNextDay\"`       // 是否自动取消次日清台\n\tEnableCrossBillingAdjustment     *bool    `json:\"enableCrossBillingAdjustment\"`     // 是否启用跨账单调整\n\tAutoSwitchToTimeBilling         *bool    `json:\"autoSwitchToTimeBilling\"`         // 是否自动切换到时间计费\n\tEnableSplitBilling              *bool    `json:\"enableSplitBilling\"`              // 是否启用拆分计费\n\tSplitBillingRoomPercentage      *float32 `json:\"splitBillingRoomPercentage\"`      // 拆分计费房间百分比\n\tFirstBillingDuration            *int     `json:\"firstBillingDuration\"`            // 首次计费时长\n\tSecondBillingDuration           *int     `json:\"secondBillingDuration\"`           // 第二次计费时长\n\tMinRenewalDuration              *int     `json:\"minRenewalDuration\"`              // 最小续费时长\n\tGuestDuration                   *int     `json:\"guestDuration\"`                   // 客人时长\n\tReopenDuration                  *int     `json:\"reopenDuration\"`                  // 重新开房时长\n\tCancelOpenDuration              *int     `json:\"cancelOpenDuration\"`              // 取消开房时长\n\tRefundDuration                  *int     `json:\"refundDuration\"`                  // 退款时长\n\tRoomCleanToIdleDuration         *int     `json:\"roomCleanToIdleDuration\"`         // 房间清洁到空闲时长\n\tReservationExpirationDuration    *int     `json:\"reservationExpirationDuration\"`    // 预订到期时长\n\tDiscountMeetMinConsumption      *bool    `json:\"discountMeetMinConsumption\"`      // 折扣是否满足最低消费\n\tDiscountOnlyExcess              *bool    `json:\"discountOnlyExcess\"`              // 折扣是否仅限超出部分\n\tAutoLockRoomAfterCheckout       *bool    `json:\"autoLockRoomAfterCheckout\"`       // 结账后是否自动锁定房间\n\tRoomChangeRule                  *string  `json:\"roomChangeRule\"`                  // 房间变更规则\n\tMemberDiscountMethod            *string  `json:\"memberDiscountMethod\"`            // 会员折扣方式\n\tMemberCardPaymentMode           *string  `json:\"memberCardPaymentMode\"`           // 会员卡支付方式\n\tMemberCardVerificationMethod    *string  `json:\"memberCardVerificationMethod\"`    // 会员卡验证方式\n\tInheritMemberCardMode           *string  `json:\"inheritMemberCardMode\"`           // 继承会员卡模式\n\tRoomPriceMemberDiscountMethod   *string  `json:\"roomPriceMemberDiscountMethod\"`   // 房间价格会员折扣方式\n\tProductPriceMemberDiscountMethod *string  `json:\"productPriceMemberDiscountMethod\"` // 产品价格会员折扣方式\n\tOrderScreenHoldMode             *string  `json:\"orderScreenHoldMode\"`             // 订单屏幕保持模式\n\tAllowMinConsumptionDifference   *bool    `json:\"allowMinConsumptionDifference\"`   // 是否允许最低消费差异\n\tMobileOrderCheckoutMode         *string  `json:\"mobileOrderCheckoutMode\"`         // 手机点单结账方式\n\tMobileOrderWineStorageMode      *string  `json:\"mobileOrderWineStorageMode\"`      // 手机点单酒水寄存方式\n\tMobileOrderWineRetrievalMode    *string  `json:\"mobileOrderWineRetrievalMode\"`    // 手机点单酒水取回方式\n\tMobileOrderOnlyStoreOrderedItems *bool    `json:\"mobileOrderOnlyStoreOrderedItems\"` // 手机点单是否只存储已点商品\n\tCallProcessingTimeout           *int     `json:\"callProcessingTimeout\"`           // 呼叫处理超时时间\n\tMobileOrderCashPaymentMode      *string  `json:\"mobileOrderCashPaymentMode\"`      // 手机点单现金支付方式\n\tCashierDataRetentionDays        *int     `json:\"cashierDataRetentionDays\"`        // 收银员数据保留天数\n\tAllowCashierAddEmployee         *bool    `json:\"allowCashierAddEmployee\"`         // 是否允许收银员添加员工\n\tRestrictAllMemberQuery          *bool    `json:\"restrictAllMemberQuery\"`          // 是否限制所有会员查询\n\tEnableMultipleSalespersons      *bool    `json:\"enableMultipleSalespersons\"`      // 是否启用多个销售员\n\tEnableReturnConfirmation        *bool    `json:\"enableReturnConfirmation\"`        // 是否启用退货确认\n\tRequireRoomForOrder             *bool    `json:\"requireRoomForOrder\"`             // 下单是否需要房间\n\tDefaultSendReservationSMS       *bool    `json:\"defaultSendReservationSMS\"`       // 是否默认发送预订短信\n\tShowInventoryOnCashier          *bool    `json:\"showInventoryOnCashier\"`          // 是否在收银员端显示库存\n\tCounterOrderMode                *string  `json:\"counterOrderMode\"`                // 柜台点单模式\n\tCounterAutoLogoutTime           *int     `json:\"counterAutoLogoutTime\"`           // 柜台自动登出时间\n\tPageNum                         *int     `json:\"pageNum\"`                         // 页码\n\tPageSize                        *int     `json:\"pageSize\"`                        // 每页记录数\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OperationSettingsService struct {\n}\n\nfunc (service *OperationSettingsService) CreateOperationSettings(logCtx *gin.Context, operationSettings *po.OperationSettings) error {\n\treturn Save(operationSettings)\n}\n\nfunc (service *OperationSettingsService) UpdateOperationSettings(logCtx *gin.Context, operationSettings *po.OperationSettings) error {\n\treturn Update(operationSettings)\n}\n\nfunc (service *OperationSettingsService) DeleteOperationSettings(logCtx *gin.Context, id string) error {\n\treturn Delete(po.OperationSettings{Id: &id})\n}\n\nfunc (service *OperationSettingsService) FindOperationSettingsById(logCtx *gin.Context, id string) (operationSettings *po.OperationSettings, err error) {\n\toperationSettings = &po.OperationSettings{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(operationSettings).Error\n\treturn\n}\n\nfunc (service *OperationSettingsService) FindAllOperationSettings(logCtx *gin.Context, reqDto *req.QueryOperationSettingsReqDto) (list *[]po.OperationSettings, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OperationSettings{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.OperationSettings{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *OperationSettingsService) FindAllOperationSettingsWithPagination(logCtx *gin.Context, reqDto *req.QueryOperationSettingsReqDto) (list *[]po.OperationSettings, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OperationSettings{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.OperationSettings{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OperationSettingsController struct{}\n\nvar (\n\toperationSettingsService  = impl.OperationSettingsService{}\n\toperationSettingsTransfer = transfer.OperationSettingsTransfer{}\n)\n\n// @Summary 添加运营设置\n// @Description 添加运营设置\n// @Tags 运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/operationSettings/add [post]\nfunc (controller *OperationSettingsController) AddOperationSettings(ctx *gin.Context) {\n\treqDto := req.AddOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\toperationSettings := po.OperationSettings{}\n\t// 设置所有字段\n\toperationSettings.CheckoutMode = reqDto.CheckoutMode\n\toperationSettings.AllowCashierSelectMode = reqDto.AllowCashierSelectMode\n\toperationSettings.SupermarketCheckoutMode = reqDto.SupermarketCheckoutMode\n\t// ... 其他字段省略，需要设置所有字段\n\n\terr = operationSettingsService.CreateOperationSettings(ctx, &operationSettings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, operationSettingsTransfer.PoToVo(operationSettings))\n}\n\n// @Summary 更新运营设置\n// @Description 更新运营设置\n// @Tags 运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/operationSettings/update [post]\nfunc (controller *OperationSettingsController) UpdateOperationSettings(ctx *gin.Context) {\n\treqDto := req.UpdateOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\toperationSettings, err := operationSettingsService.FindOperationSettingsById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\t// 更新所有字段\n\tif reqDto.CheckoutMode != nil {\n\t\toperationSettings.CheckoutMode = reqDto.CheckoutMode\n\t}\n\tif reqDto.AllowCashierSelectMode != nil {\n\t\toperationSettings.AllowCashierSelectMode = reqDto.AllowCashierSelectMode\n\t}\n\t// ... 其他字段省略，需要更新所有字段\n\n\terr = operationSettingsService.UpdateOperationSettings(ctx, operationSettings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, operationSettingsTransfer.PoToVo(*operationSettings))\n}\n\n// @Summary 删除运营设置\n// @Description 删除运营设置\n// @Tags 运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/operationSettings/delete [post]\nfunc (controller *OperationSettingsController) DeleteOperationSettings(ctx *gin.Context) {\n\treqDto := req.DeleteOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = operationSettingsService.DeleteOperationSettings(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询运营设置\n// @Description 查询运营设置\n// @Tags 运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/operationSettings/query [post]\nfunc (controller *OperationSettingsController) QueryOperationSettings(ctx *gin.Context) {\n\treqDto := req.QueryOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := operationSettingsService.FindAllOperationSettings(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.OperationSettingsVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, operationSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询运营设置列表\n// @Description 查询运营设置列表\n// @Tags 运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/operationSettings/list [post]\nfunc (controller *OperationSettingsController) ListOperationSettings(ctx *gin.Context) {\n\treqDto := req.QueryOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := operationSettingsService.FindAllOperationSettingsWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.OperationSettingsVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.OperationSettingsVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, operationSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OperationSettingsRoute struct {\n}\n\nfunc (s *OperationSettingsRoute) InitOperationSettingsRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\toperationSettingsController := controller.OperationSettingsController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/operationSettings/add\", operationSettingsController.AddOperationSettings)       //add\n\t\troute.POST(\"/api/operationSettings/update\", operationSettingsController.UpdateOperationSettings)   //update\n\t\troute.POST(\"/api/operationSettings/delete\", operationSettingsController.DeleteOperationSettings)   //delete\n\t\troute.POST(\"/api/operationSettings/query\", operationSettingsController.QueryOperationSettings)     //query\n\t\troute.POST(\"/api/operationSettings/list\", operationSettingsController.ListOperationSettings)     //list\n\t}\n}"}]