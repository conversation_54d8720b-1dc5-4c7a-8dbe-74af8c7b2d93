[{"po": "package po\n\n// MerchantService 商户服务实体\ntype MerchantService struct {\n\tId               *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tExpirationDate   *int64  `gorm:\"column:expiration_date;type:int;default:0\" json:\"expirationDate\"` // 过期日期\n\tAuthorizationCode *string `gorm:\"column:authorization_code;type:varchar(64);default:''\" json:\"authorizationCode\"` // 授权码\n\tCtime            *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime            *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState            *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion          *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (MerchantService) TableName() string {\n\treturn \"merchant_service\"\n}\n\nfunc (m MerchantService) GetId() string {\n\treturn *m.Id\n}", "vo": "package vo\n\n// MerchantServiceVO 商户服务值对象\ntype MerchantServiceVO struct {\n\tId               string `json:\"id\"`               // ID\n\tExpirationDate   int64  `json:\"expirationDate\"`   // 过期日期\n\tAuthorizationCode string `json:\"authorizationCode\"` // 授权码\n\tCtime            int64  `json:\"ctime\"`            // 创建时间戳\n\tUtime            int64  `json:\"utime\"`            // 更新时间戳\n\tState            int    `json:\"state\"`            // 状态值\n\tVersion          int    `json:\"version\"`          // 版本号\n}", "req_add": "package req\n\n// AddMerchantServiceReqDto 创建商户服务请求DTO\ntype AddMerchantServiceReqDto struct {\n\tExpirationDate   *int64  `json:\"expirationDate\"`   // 过期日期\n\tAuthorizationCode *string `json:\"authorizationCode\"` // 授权码\n}", "req_update": "package req\n\ntype UpdateMerchantServiceReqDto struct {\n\tId               *string `json:\"id\"`               // ID\n\tExpirationDate   *int64  `json:\"expirationDate\"`   // 过期日期\n\tAuthorizationCode *string `json:\"authorizationCode\"` // 授权码\n}", "req_delete": "package req\n\ntype DeleteMerchantServiceReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryMerchantServiceReqDto struct {\n\tId               *string `json:\"id\"`               // ID\n\tExpirationDate   *int64  `json:\"expirationDate\"`   // 过期日期\n\tAuthorizationCode *string `json:\"authorizationCode\"` // 授权码\n\tPageNum          *int    `json:\"pageNum\"`          // 页码\n\tPageSize         *int    `json:\"pageSize\"`         // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MerchantServiceTransfer struct {\n}\n\nfunc (transfer *MerchantServiceTransfer) PoToVo(po po.MerchantService) vo.MerchantServiceVO {\n\tvo := vo.MerchantServiceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MerchantServiceTransfer) VoToPo(vo vo.MerchantServiceVO) po.MerchantService {\n\tpo := po.MerchantService{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MerchantServiceService struct {\n}\n\nfunc (service *MerchantServiceService) CreateMerchantService(logCtx *gin.Context, merchantService *po.MerchantService) error {\n\treturn Save(merchantService)\n}\n\nfunc (service *MerchantServiceService) UpdateMerchantService(logCtx *gin.Context, merchantService *po.MerchantService) error {\n\treturn Update(merchantService)\n}\n\nfunc (service *MerchantServiceService) DeleteMerchantService(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MerchantService{Id: &id})\n}\n\nfunc (service *MerchantServiceService) FindMerchantServiceById(logCtx *gin.Context, id string) (merchantService *po.MerchantService, err error) {\n\tmerchantService = &po.MerchantService{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(merchantService).Error\n\treturn\n}\n\nfunc (service *MerchantServiceService) FindAllMerchantService(logCtx *gin.Context, reqDto *req.QueryMerchantServiceReqDto) (list *[]po.MerchantService, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MerchantService{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MerchantService{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MerchantServiceService) FindAllMerchantServiceWithPagination(logCtx *gin.Context, reqDto *req.QueryMerchantServiceReqDto) (list *[]po.MerchantService, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MerchantService{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MerchantService{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MerchantServiceController struct{}\n\nvar (\n\tmerchantServiceService  = impl.MerchantServiceService{}\n\tmerchantServiceTransfer = transfer.MerchantServiceTransfer{}\n)\n\n// @Summary 添加商户服务\n// @Description 添加商户服务\n// @Tags 商户服务\n// @Accept json\n// @Produce json\n// @Param body body req.AddMerchantServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MerchantServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/merchant-service/add [post]\nfunc (controller *MerchantServiceController) AddMerchantService(ctx *gin.Context) {\n\treqDto := req.AddMerchantServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmerchantService := po.MerchantService{}\n\tif reqDto.ExpirationDate != nil {\n\t\tmerchantService.ExpirationDate = reqDto.ExpirationDate\n\t}\n\tif reqDto.AuthorizationCode != nil {\n\t\tmerchantService.AuthorizationCode = reqDto.AuthorizationCode\n\t}\n\terr = merchantServiceService.CreateMerchantService(ctx, &merchantService)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, merchantServiceTransfer.PoToVo(merchantService))\n}\n\n// @Summary 更新商户服务\n// @Description 更新商户服务\n// @Tags 商户服务\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMerchantServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MerchantServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/merchant-service/update [post]\nfunc (controller *MerchantServiceController) UpdateMerchantService(ctx *gin.Context) {\n\treqDto := req.UpdateMerchantServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmerchantService, err := merchantServiceService.FindMerchantServiceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.ExpirationDate != nil {\n\t\tmerchantService.ExpirationDate = reqDto.ExpirationDate\n\t}\n\tif reqDto.AuthorizationCode != nil {\n\t\tmerchantService.AuthorizationCode = reqDto.AuthorizationCode\n\t}\n\terr = merchantServiceService.UpdateMerchantService(ctx, merchantService)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, merchantServiceTransfer.PoToVo(*merchantService))\n}\n\n// @Summary 删除商户服务\n// @Description 删除商户服务\n// @Tags 商户服务\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMerchantServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/merchant-service/delete [post]\nfunc (controller *MerchantServiceController) DeleteMerchantService(ctx *gin.Context) {\n\treqDto := req.DeleteMerchantServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = merchantServiceService.DeleteMerchantService(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询商户服务\n// @Description 查询商户服务\n// @Tags 商户服务\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMerchantServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MerchantServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/merchant-service/query [post]\nfunc (controller *MerchantServiceController) QueryMerchantServices(ctx *gin.Context) {\n\treqDto := req.QueryMerchantServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := merchantServiceService.FindAllMerchantService(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MerchantServiceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, merchantServiceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询商户服务列表\n// @Description 查询商户服务列表\n// @Tags 商户服务\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMerchantServiceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MerchantServiceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/merchant-service/list [post]\nfunc (a *MerchantServiceController) ListMerchantServices(ctx *gin.Context) {\n\treqDto := req.QueryMerchantServiceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := merchantServiceService.FindAllMerchantServiceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MerchantServiceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MerchantServiceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, merchantServiceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MerchantServiceRoute struct {\n}\n\nfunc (s *MerchantServiceRoute) InitMerchantServiceRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmerchantServiceController := controller.MerchantServiceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/merchant-service/add\", merchantServiceController.AddMerchantService)    //add\n\t\troute.POST(\"/api/merchant-service/update\", merchantServiceController.UpdateMerchantService) //update\n\t\troute.POST(\"/api/merchant-service/delete\", merchantServiceController.DeleteMerchantService) //delete\n\t\troute.POST(\"/api/merchant-service/query\", merchantServiceController.QueryMerchantServices)     //query\n\t\troute.POST(\"/api/merchant-service/list\", merchantServiceController.ListMerchantServices)     //list\n\t}\n}"}]