[{"po": "package po\n\n// CustomerTag 客户标签实体\ntype CustomerTag struct {\n\tId      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`  // ID\n\tName    *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"` // 客户标签名称\n\tCtime   *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`         // 创建时间戳\n\tUtime   *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`         // 更新时间戳\n\tState   *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`         // 状态值\n\tVersion *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`     // 版本号\n}\n\n// TableName 设置表名\nfunc (CustomerTag) TableName() string {\n\treturn \"customer_tag\"\n}\n\nfunc (t CustomerTag) GetId() string {\n\treturn *t.Id\n}\n", "vo": "package vo\n\n// CustomerTagVO 客户标签值对象\ntype CustomerTagVO struct {\n\tId      string `json:\"id\"`      // ID\n\tName    string `json:\"name\"`    // 客户标签名称\n\tCtime   int64  `json:\"ctime\"`   // 创建时间戳\n\tUtime   int64  `json:\"utime\"`   // 更新时间戳\n\tState   int    `json:\"state\"`   // 状态值\n\tVersion int    `json:\"version\"` // 版本号\n}\n", "req_add": "package req\n\n// AddCustomerTagReqDto 创建客户标签请求DTO\ntype AddCustomerTagReqDto struct {\n\tName *string `json:\"name\"` // 客户标签名称\n}\n", "req_update": "package req\n\ntype UpdateCustomerTagReqDto struct {\n\tId   *string `json:\"id\"`   // 标签ID\n\tName *string `json:\"name\"` // 客户标签名称\n}\n", "req_delete": "package req\n\ntype DeleteCustomerTagReqDto struct {\n\tId *string `json:\"id\"` // 标签ID\n}\n", "req_query": "package req\n\ntype QueryCustomerTagReqDto struct {\n\tId       *string `json:\"id\"`       // 标签ID\n\tName     *string `json:\"name\"`     // 客户标签名称\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CustomerTagTransfer struct {\n}\n\nfunc (transfer *CustomerTagTransfer) PoToVo(po po.CustomerTag) vo.CustomerTagVO {\n\tvo := vo.CustomerTagVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CustomerTagTransfer) VoToPo(vo vo.CustomerTagVO) po.CustomerTag {\n\tpo := po.CustomerTag{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerTagService struct {\n}\n\nfunc (service *CustomerTagService) CreateCustomerTag(logCtx *gin.Context, customerTag *po.CustomerTag) error {\n\treturn Save(customerTag)\n}\n\nfunc (service *CustomerTagService) UpdateCustomerTag(logCtx *gin.Context, customerTag *po.CustomerTag) error {\n\treturn Update(customerTag)\n}\n\nfunc (service *CustomerTagService) DeleteCustomerTag(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CustomerTag{Id: &id})\n}\n\nfunc (service *CustomerTagService) FindCustomerTagById(logCtx *gin.Context, id string) (customerTag *po.CustomerTag, err error) {\n\tcustomerTag = &po.CustomerTag{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(customerTag).Error\n\treturn\n}\n\nfunc (service *CustomerTagService) FindAllCustomerTag(logCtx *gin.Context, reqDto *req.QueryCustomerTagReqDto) (list *[]po.CustomerTag, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CustomerTag{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CustomerTag{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CustomerTagService) FindAllCustomerTagWithPagination(logCtx *gin.Context, reqDto *req.QueryCustomerTagReqDto) (list *[]po.CustomerTag, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CustomerTag{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CustomerTag{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerTagController struct{}\n\nvar (\n\tcustomerTagService  = impl.CustomerTagService{}\n\tcustomerTagTransfer = transfer.CustomerTagTransfer{}\n)\n\n// @Summary 添加客户标签\n// @Description 添加客户标签\n// @Tags 客户标签\n// @Accept json\n// @Produce json\n// @Param body body req.AddCustomerTagReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CustomerTagVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-tag/add [post]\nfunc (controller *CustomerTagController) AddCustomerTag(ctx *gin.Context) {\n\treqDto := req.AddCustomerTagReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcustomerTag := po.CustomerTag{}\n\tif reqDto.Name != nil {\n\t\tcustomerTag.Name = reqDto.Name\n\t}\n\terr = customerTagService.CreateCustomerTag(ctx, &customerTag)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, customerTagTransfer.PoToVo(customerTag))\n}\n\n// @Summary 更新客户标签\n// @Description 更新客户标签\n// @Tags 客户标签\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCustomerTagReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CustomerTagVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-tag/update [post]\nfunc (controller *CustomerTagController) UpdateCustomerTag(ctx *gin.Context) {\n\treqDto := req.UpdateCustomerTagReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcustomerTag, err := customerTagService.FindCustomerTagById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tcustomerTag.Name = reqDto.Name\n\t}\n\terr = customerTagService.UpdateCustomerTag(ctx, customerTag)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, customerTagTransfer.PoToVo(*customerTag))\n}\n\n// @Summary 删除客户标签\n// @Description 删除客户标签\n// @Tags 客户标签\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCustomerTagReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-tag/delete [post]\nfunc (controller *CustomerTagController) DeleteCustomerTag(ctx *gin.Context) {\n\treqDto := req.DeleteCustomerTagReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = customerTagService.DeleteCustomerTag(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询客户标签\n// @Description 查询客户标签\n// @Tags 客户标签\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCustomerTagReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CustomerTagVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-tag/query [post]\nfunc (controller *CustomerTagController) QueryCustomerTags(ctx *gin.Context) {\n\treqDto := req.QueryCustomerTagReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := customerTagService.FindAllCustomerTag(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CustomerTagVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, customerTagTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询客户标签列表\n// @Description 查询客户标签列表\n// @Tags 客户标签\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCustomerTagReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CustomerTagVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-tag/list [post]\nfunc (a *CustomerTagController) ListCustomerTags(ctx *gin.Context) {\n\treqDto := req.QueryCustomerTagReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := customerTagService.FindAllCustomerTagWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CustomerTagVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CustomerTagVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, customerTagTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerTagRoute struct {\n}\n\nfunc (s *CustomerTagRoute) InitCustomerTagRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcustomerTagController := controller.CustomerTagController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/customer-tag/add\", customerTagController.AddCustomerTag)       //add\n\t\troute.POST(\"/api/customer-tag/update\", customerTagController.UpdateCustomerTag) //update\n\t\troute.POST(\"/api/customer-tag/delete\", customerTagController.DeleteCustomerTag) //delete\n\t\troute.POST(\"/api/customer-tag/query\", customerTagController.QueryCustomerTags)  //query\n\t\troute.POST(\"/api/customer-tag/list\", customerTagController.ListCustomerTags)   //list\n\t}\n}\n"}]