[{"po": "package po\n\n// Coupon 优惠券实体\ntype Coupon struct {\n\tId         *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tCode       *string `gorm:\"column:code;type:varchar(64);default:''\" json:\"code\"`           // 优惠券代码\n\tValue      *int64  `gorm:\"column:value;type:int;default:0\" json:\"value\"`                   // 优惠券面值\n\tValidFrom  *int64  `gorm:\"column:valid_from;type:int;default:0\" json:\"validFrom\"`         // 有效期开始日期\n\tValidTo    *int64  `gorm:\"column:valid_to;type:int;default:0\" json:\"validTo\"`             // 有效期结束日期\n\tType       *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`           // 优惠券类型\n\tCampaignId *string `gorm:\"column:campaign_id;type:varchar(64);default:''\" json:\"campaignId\"` // 关联的营销活动ID\n\tCtime      *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime      *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (Coupon) TableName() string {\n\treturn \"coupon\"\n}\n\nfunc (c Coupon) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CouponVO 优惠券信息值对象\ntype CouponVO struct {\n\tId         string `json:\"id\"`         // ID\n\tCode       string `json:\"code\"`       // 优惠券代码\n\tValue      int64  `json:\"value\"`      // 优惠券面值\n\tValidFrom  int64  `json:\"validFrom\"`  // 有效期开始日期\n\tValidTo    int64  `json:\"validTo\"`    // 有效期结束日期\n\tType       string `json:\"type\"`       // 优惠券类型\n\tCampaignId string `json:\"campaignId\"` // 关联的营销活动ID\n\tCtime      int64  `json:\"ctime\"`      // 创建时间戳\n\tUtime      int64  `json:\"utime\"`      // 更新时间戳\n\tState      int    `json:\"state\"`      // 状态值\n\tVersion    int    `json:\"version\"`    // 版本号\n}\n", "req_add": "package req\n\n// AddCouponReqDto 创建优惠券请求DTO\ntype AddCouponReqDto struct {\n\tCode       *string `json:\"code\"`       // 优惠券代码\n\tValue      *int64  `json:\"value\"`      // 优惠券面值\n\tValidFrom  *int64  `json:\"validFrom\"`  // 有效期开始日期\n\tValidTo    *int64  `json:\"validTo\"`    // 有效期结束日期\n\tType       *string `json:\"type\"`       // 优惠券类型\n\tCampaignId *string `json:\"campaignId\"` // 关联的营销活动ID\n}\n", "req_update": "package req\n\ntype UpdateCouponReqDto struct {\n\tId         *string `json:\"id\"`         // ID\n\tCode       *string `json:\"code\"`       // 优惠券代码\n\tValue      *int64  `json:\"value\"`      // 优惠券面值\n\tValidFrom  *int64  `json:\"validFrom\"`  // 有效期开始日期\n\tValidTo    *int64  `json:\"validTo\"`    // 有效期结束日期\n\tType       *string `json:\"type\"`       // 优惠券类型\n\tCampaignId *string `json:\"campaignId\"` // 关联的营销活动ID\n}\n", "req_delete": "package req\n\ntype DeleteCouponReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCouponReqDto struct {\n\tId         *string `json:\"id\"`         // ID\n\tCode       *string `json:\"code\"`       // 优惠券代码\n\tValue      *int64  `json:\"value\"`      // 优惠券面值\n\tValidFrom  *int64  `json:\"validFrom\"`  // 有效期开始日期\n\tValidTo    *int64  `json:\"validTo\"`    // 有效期结束日期\n\tType       *string `json:\"type\"`       // 优惠券类型\n\tCampaignId *string `json:\"campaignId\"` // 关联的营销活动ID\n\tPageNum    *int    `json:\"pageNum\"`    // 页码\n\tPageSize   *int    `json:\"pageSize\"`   // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CouponTransfer struct {\n}\n\nfunc (transfer *CouponTransfer) PoToVo(po po.Coupon) vo.CouponVO {\n\tvo := vo.CouponVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CouponTransfer) VoToPo(vo vo.CouponVO) po.Coupon {\n\tpo := po.Coupon{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponService struct {\n}\n\nfunc (service *CouponService) CreateCoupon(logCtx *gin.Context, coupon *po.Coupon) error {\n\treturn Save(coupon)\n}\n\nfunc (service *CouponService) UpdateCoupon(logCtx *gin.Context, coupon *po.Coupon) error {\n\treturn Update(coupon)\n}\n\nfunc (service *CouponService) DeleteCoupon(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Coupon{Id: &id})\n}\n\nfunc (service *CouponService) FindCouponById(logCtx *gin.Context, id string) (coupon *po.Coupon, err error) {\n\tcoupon = &po.Coupon{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(coupon).Error\n\treturn\n}\n\nfunc (service *CouponService) FindAllCoupon(logCtx *gin.Context, reqDto *req.QueryCouponReqDto) (list *[]po.Coupon, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Coupon{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Coupon{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CouponService) FindAllCouponWithPagination(logCtx *gin.Context, reqDto *req.QueryCouponReqDto) (list *[]po.Coupon, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Coupon{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Coupon{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponController struct{}\n\nvar (\n\tcouponService  = impl.CouponService{}\n\tcouponTransfer = transfer.CouponTransfer{}\n)\n\n// @Summary 添加优惠券\n// @Description 添加优惠券\n// @Tags 优惠券\n// @Accept json\n// @Produce json\n// @Param body body req.AddCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/coupon/add [post]\nfunc (controller *CouponController) AddCoupon(ctx *gin.Context) {\n\treqDto := req.AddCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcoupon := po.Coupon{}\n\tif reqDto.Code != nil {\n\t\tcoupon.Code = reqDto.Code\n\t}\n\tif reqDto.Value != nil {\n\t\tcoupon.Value = reqDto.Value\n\t}\n\tif reqDto.ValidFrom != nil {\n\t\tcoupon.ValidFrom = reqDto.ValidFrom\n\t}\n\tif reqDto.ValidTo != nil {\n\t\tcoupon.ValidTo = reqDto.ValidTo\n\t}\n\tif reqDto.Type != nil {\n\t\tcoupon.Type = reqDto.Type\n\t}\n\tif reqDto.CampaignId != nil {\n\t\tcoupon.CampaignId = reqDto.CampaignId\n\t}\n\terr = couponService.CreateCoupon(ctx, &coupon)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, couponTransfer.PoToVo(coupon))\n}\n\n// @Summary 更新优惠券\n// @Description 更新优惠券\n// @Tags 优惠券\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/coupon/update [post]\nfunc (controller *CouponController) UpdateCoupon(ctx *gin.Context) {\n\treqDto := req.UpdateCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcoupon, err := couponService.FindCouponById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Code != nil {\n\t\tcoupon.Code = reqDto.Code\n\t}\n\tif reqDto.Value != nil {\n\t\tcoupon.Value = reqDto.Value\n\t}\n\tif reqDto.ValidFrom != nil {\n\t\tcoupon.ValidFrom = reqDto.ValidFrom\n\t}\n\tif reqDto.ValidTo != nil {\n\t\tcoupon.ValidTo = reqDto.ValidTo\n\t}\n\tif reqDto.Type != nil {\n\t\tcoupon.Type = reqDto.Type\n\t}\n\tif reqDto.CampaignId != nil {\n\t\tcoupon.CampaignId = reqDto.CampaignId\n\t}\n\terr = couponService.UpdateCoupon(ctx, coupon)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, couponTransfer.PoToVo(*coupon))\n}\n\n// @Summary 删除优惠券\n// @Description 删除优惠券\n// @Tags 优惠券\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/coupon/delete [post]\nfunc (controller *CouponController) DeleteCoupon(ctx *gin.Context) {\n\treqDto := req.DeleteCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = couponService.DeleteCoupon(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询优惠券\n// @Description 查询优惠券\n// @Tags 优惠券\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/coupon/query [post]\nfunc (controller *CouponController) QueryCoupons(ctx *gin.Context) {\n\treqDto := req.QueryCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := couponService.FindAllCoupon(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CouponVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, couponTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询优惠券列表\n// @Description 查询优惠券列表\n// @Tags 优惠券\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/coupon/list [post]\nfunc (a *CouponController) ListCoupons(ctx *gin.Context) {\n\treqDto := req.QueryCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := couponService.FindAllCouponWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CouponVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CouponVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, couponTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponRoute struct {\n}\n\nfunc (s *CouponRoute) InitCouponRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcouponController := controller.CouponController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/coupon/add\", couponController.AddCoupon)    //add\n\t\troute.POST(\"/api/coupon/update\", couponController.UpdateCoupon) //update\n\t\troute.POST(\"/api/coupon/delete\", couponController.DeleteCoupon) //delete\n\t\troute.POST(\"/api/coupon/query\", couponController.QueryCoupons)     //query\n\t\troute.POST(\"/api/coupon/list\", couponController.ListCoupons)     //list\n\t}\n}\n"}]