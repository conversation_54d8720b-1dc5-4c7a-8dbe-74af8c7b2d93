[{"po": "package po\n\n// MemberDay 会员日实体\ntype MemberDay struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一id\n\tDayOfWeek       *string `gorm:\"column:day_of_week;type:varchar(64);default:''\" json:\"dayOfWeek\"`    // 星期几\n\tPointsMultiplier *int    `gorm:\"column:points_multiplier;type:int;default:0\" json:\"pointsMultiplier\"` // 积分倍数\n\tCtime           *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间\n\tUtime           *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间\n\tState           *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态\n\tVersion         *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本\n}\n\n// TableName 设置表名\nfunc (MemberDay) TableName() string {\n\treturn \"member_day\"\n}\n\nfunc (m MemberDay) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MemberDayVO 会员日信息值对象\ntype MemberDayVO struct {\n\tId              string `json:\"id\"`              // 唯一id\n\tDayOfWeek       string `json:\"dayOfWeek\"`       // 星期几\n\tPointsMultiplier int    `json:\"pointsMultiplier\"` // 积分倍数\n\tCtime           int64  `json:\"ctime\"`           // 创建时间\n\tUtime           int64  `json:\"utime\"`           // 更新时间\n\tState           int    `json:\"state\"`           // 状态\n\tVersion         int    `json:\"version\"`         // 版本\n}\n", "req_add": "package req\n\n// AddMemberDayReqDto 创建会员日请求DTO\ntype AddMemberDayReqDto struct {\n\tDayOfWeek       *string `json:\"dayOfWeek\"`       // 星期几\n\tPointsMultiplier *int    `json:\"pointsMultiplier\"` // 积分倍数\n}\n", "req_update": "package req\n\ntype UpdateMemberDayReqDto struct {\n\tId              *string `json:\"id\"`              // 唯一id\n\tDayOfWeek       *string `json:\"dayOfWeek\"`       // 星期几\n\tPointsMultiplier *int    `json:\"pointsMultiplier\"` // 积分倍数\n}\n", "req_delete": "package req\n\ntype DeleteMemberDayReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryMemberDayReqDto struct {\n\tId              *string `json:\"id\"`              // 唯一id\n\tDayOfWeek       *string `json:\"dayOfWeek\"`       // 星期几\n\tPointsMultiplier *int    `json:\"pointsMultiplier\"` // 积分倍数\n\tPageNum         *int    `json:\"pageNum\"`         // 页码\n\tPageSize        *int    `json:\"pageSize\"`        // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberDayTransfer struct {\n}\n\nfunc (transfer *MemberDayTransfer) PoToVo(po po.MemberDay) vo.MemberDayVO {\n\tvo := vo.MemberDayVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MemberDayTransfer) VoToPo(vo vo.MemberDayVO) po.MemberDay {\n\tpo := po.MemberDay{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberDayService struct {\n}\n\nfunc (service *MemberDayService) CreateMemberDay(logCtx *gin.Context, memberDay *po.MemberDay) error {\n\treturn Save(memberDay)\n}\n\nfunc (service *MemberDayService) UpdateMemberDay(logCtx *gin.Context, memberDay *po.MemberDay) error {\n\treturn Update(memberDay)\n}\n\nfunc (service *MemberDayService) DeleteMemberDay(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MemberDay{Id: &id})\n}\n\nfunc (service *MemberDayService) FindMemberDayById(logCtx *gin.Context, id string) (memberDay *po.MemberDay, err error) {\n\tmemberDay = &po.MemberDay{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(memberDay).Error\n\treturn\n}\n\nfunc (service *MemberDayService) FindAllMemberDay(logCtx *gin.Context, reqDto *req.QueryMemberDayReqDto) (list *[]po.MemberDay, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberDay{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MemberDay{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MemberDayService) FindAllMemberDayWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberDayReqDto) (list *[]po.MemberDay, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberDay{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MemberDay{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberDayController struct{}\n\nvar (\n\tmemberDayService  = impl.MemberDayService{}\n\tmemberDayTransfer = transfer.MemberDayTransfer{}\n)\n\n// @Summary 添加会员日\n// @Description 添加会员日\n// @Tags 会员日\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberDayReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberDayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-day/add [post]\nfunc (controller *MemberDayController) AddMemberDay(ctx *gin.Context) {\n\treqDto := req.AddMemberDayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmemberDay := po.MemberDay{}\n\tif reqDto.DayOfWeek != nil {\n\t\tmemberDay.DayOfWeek = reqDto.DayOfWeek\n\t}\n\tif reqDto.PointsMultiplier != nil {\n\t\tmemberDay.PointsMultiplier = reqDto.PointsMultiplier\n\t}\n\terr = memberDayService.CreateMemberDay(ctx, &memberDay)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberDayTransfer.PoToVo(memberDay))\n}\n\n// @Summary 更新会员日\n// @Description 更新会员日\n// @Tags 会员日\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberDayReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberDayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-day/update [post]\nfunc (controller *MemberDayController) UpdateMemberDay(ctx *gin.Context) {\n\treqDto := req.UpdateMemberDayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmemberDay, err := memberDayService.FindMemberDayById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.DayOfWeek != nil {\n\t\tmemberDay.DayOfWeek = reqDto.DayOfWeek\n\t}\n\tif reqDto.PointsMultiplier != nil {\n\t\tmemberDay.PointsMultiplier = reqDto.PointsMultiplier\n\t}\n\terr = memberDayService.UpdateMemberDay(ctx, memberDay)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberDayTransfer.PoToVo(*memberDay))\n}\n\n// @Summary 删除会员日\n// @Description 删除会员日\n// @Tags 会员日\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberDayReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-day/delete [post]\nfunc (controller *MemberDayController) DeleteMemberDay(ctx *gin.Context) {\n\treqDto := req.DeleteMemberDayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = memberDayService.DeleteMemberDay(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员日\n// @Description 查询会员日\n// @Tags 会员日\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberDayReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberDayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-day/query [post]\nfunc (controller *MemberDayController) QueryMemberDays(ctx *gin.Context) {\n\treqDto := req.QueryMemberDayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := memberDayService.FindAllMemberDay(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MemberDayVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, memberDayTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询会员日列表\n// @Description 查询会员日列表\n// @Tags 会员日\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberDayReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberDayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-day/list [post]\nfunc (a *MemberDayController) ListMemberDays(ctx *gin.Context) {\n\treqDto := req.QueryMemberDayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := memberDayService.FindAllMemberDayWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MemberDayVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MemberDayVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberDayTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberDayRoute struct {\n}\n\nfunc (s *MemberDayRoute) InitMemberDayRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmemberDayController := controller.MemberDayController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/member-day/add\", memberDayController.AddMemberDay)    //add\n\t\troute.POST(\"/api/member-day/update\", memberDayController.UpdateMemberDay) //update\n\t\troute.POST(\"/api/member-day/delete\", memberDayController.DeleteMemberDay) //delete\n\t\troute.POST(\"/api/member-day/query\", memberDayController.QueryMemberDays)     //query\n\t\troute.POST(\"/api/member-day/list\", memberDayController.ListMemberDays)     //list\n\t}\n}\n"}]