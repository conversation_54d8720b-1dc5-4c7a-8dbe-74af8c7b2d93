[{"po": "package po\n\n// ProductDisplayCategory 商品展示分类实体\ntype ProductDisplayCategory struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tName            *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                    // 分类名称\n\tProductTypes    *string `gorm:\"column:product_types;type:varchar(255);default:''\" json:\"productTypes\"`    // 关联的商品类型\n\tIsDisplaySecond *bool   `gorm:\"column:is_display_second;type:tinyint;default:0\" json:\"isDisplaySecond\"` // 是否显示二级分类\n\tCtime          *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                           // 创建时间戳\n\tUtime          *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                           // 更新时间戳\n\tState          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态值\n\tVersion        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductDisplayCategory) TableName() string {\n\treturn \"product_display_category\"\n}\n\nfunc (p ProductDisplayCategory) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductDisplayCategoryVO 商品展示分类值对象\ntype ProductDisplayCategoryVO struct {\n\tId              string `json:\"id\"`              // ID\n\tName            string `json:\"name\"`            // 分类名称\n\tProductTypes    string `json:\"productTypes\"`    // 关联的商品类型\n\tIsDisplaySecond bool   `json:\"isDisplaySecond\"` // 是否显示二级分类\n\tCtime           int64  `json:\"ctime\"`           // 创建时间戳\n\tUtime           int64  `json:\"utime\"`           // 更新时间戳\n\tState           int    `json:\"state\"`           // 状态值\n\tVersion         int    `json:\"version\"`         // 版本号\n}\n", "req_add": "package req\n\n// AddProductDisplayCategoryReqDto 创建商品展示分类请求DTO\ntype AddProductDisplayCategoryReqDto struct {\n\tName            *string `json:\"name\"`            // 分类名称\n\tProductTypes    *string `json:\"productTypes\"`    // 关联的商品类型\n\tIsDisplaySecond *bool   `json:\"isDisplaySecond\"` // 是否显示二级分类\n}\n", "req_update": "package req\n\ntype UpdateProductDisplayCategoryReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tName            *string `json:\"name\"`            // 分类名称\n\tProductTypes    *string `json:\"productTypes\"`    // 关联的商品类型\n\tIsDisplaySecond *bool   `json:\"isDisplaySecond\"` // 是否显示二级分类\n}\n", "req_delete": "package req\n\ntype DeleteProductDisplayCategoryReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryProductDisplayCategoryReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tName            *string `json:\"name\"`            // 分类名称\n\tProductTypes    *string `json:\"productTypes\"`    // 关联的商品类型\n\tIsDisplaySecond *bool   `json:\"isDisplaySecond\"` // 是否显示二级分类\n\tPageNum         *int    `json:\"pageNum\"`         // 页码\n\tPageSize        *int    `json:\"pageSize\"`        // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductDisplayCategoryTransfer struct {\n}\n\nfunc (transfer *ProductDisplayCategoryTransfer) PoToVo(po po.ProductDisplayCategory) vo.ProductDisplayCategoryVO {\n\tvo := vo.ProductDisplayCategoryVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductDisplayCategoryTransfer) VoToPo(vo vo.ProductDisplayCategoryVO) po.ProductDisplayCategory {\n\tpo := po.ProductDisplayCategory{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductDisplayCategoryService struct {\n}\n\nfunc (service *ProductDisplayCategoryService) CreateProductDisplayCategory(logCtx *gin.Context, productDisplayCategory *po.ProductDisplayCategory) error {\n\treturn Save(productDisplayCategory)\n}\n\nfunc (service *ProductDisplayCategoryService) UpdateProductDisplayCategory(logCtx *gin.Context, productDisplayCategory *po.ProductDisplayCategory) error {\n\treturn Update(productDisplayCategory)\n}\n\nfunc (service *ProductDisplayCategoryService) DeleteProductDisplayCategory(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductDisplayCategory{Id: &id})\n}\n\nfunc (service *ProductDisplayCategoryService) FindProductDisplayCategoryById(logCtx *gin.Context, id string) (productDisplayCategory *po.ProductDisplayCategory, err error) {\n\tproductDisplayCategory = &po.ProductDisplayCategory{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productDisplayCategory).Error\n\treturn\n}\n\nfunc (service *ProductDisplayCategoryService) FindAllProductDisplayCategory(logCtx *gin.Context, reqDto *req.QueryProductDisplayCategoryReqDto) (list *[]po.ProductDisplayCategory, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductDisplayCategory{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.ProductTypes != nil && *reqDto.ProductTypes != \"\" {\n\t\tdb = db.Where(\"product_types LIKE ?\", \"%\"+*reqDto.ProductTypes+\"%\")\n\t}\n\tif reqDto.IsDisplaySecond != nil {\n\t\tdb = db.Where(\"is_display_second=?\", *reqDto.IsDisplaySecond)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductDisplayCategory{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductDisplayCategoryService) FindAllProductDisplayCategoryWithPagination(logCtx *gin.Context, reqDto *req.QueryProductDisplayCategoryReqDto) (list *[]po.ProductDisplayCategory, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductDisplayCategory{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.ProductTypes != nil && *reqDto.ProductTypes != \"\" {\n\t\tdb = db.Where(\"product_types LIKE ?\", \"%\"+*reqDto.ProductTypes+\"%\")\n\t}\n\tif reqDto.IsDisplaySecond != nil {\n\t\tdb = db.Where(\"is_display_second=?\", *reqDto.IsDisplaySecond)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductDisplayCategory{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductDisplayCategoryController struct{}\n\nvar (\n\tproductDisplayCategoryService  = impl.ProductDisplayCategoryService{}\n\tproductDisplayCategoryTransfer = transfer.ProductDisplayCategoryTransfer{}\n)\n\n// @Summary 添加商品展示分类\n// @Description 添加商品展示分类\n// @Tags 商品展示分类\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductDisplayCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductDisplayCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productDisplayCategory/add [post]\nfunc (controller *ProductDisplayCategoryController) AddProductDisplayCategory(ctx *gin.Context) {\n\treqDto := req.AddProductDisplayCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductDisplayCategory := po.ProductDisplayCategory{}\n\tif reqDto.Name != nil {\n\t\tproductDisplayCategory.Name = reqDto.Name\n\t}\n\tif reqDto.ProductTypes != nil {\n\t\tproductDisplayCategory.ProductTypes = reqDto.ProductTypes\n\t}\n\tif reqDto.IsDisplaySecond != nil {\n\t\tproductDisplayCategory.IsDisplaySecond = reqDto.IsDisplaySecond\n\t}\n\terr = productDisplayCategoryService.CreateProductDisplayCategory(ctx, &productDisplayCategory)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productDisplayCategoryTransfer.PoToVo(productDisplayCategory))\n}\n\n// @Summary 更新商品展示分类\n// @Description 更新商品展示分类\n// @Tags 商品展示分类\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductDisplayCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductDisplayCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productDisplayCategory/update [post]\nfunc (controller *ProductDisplayCategoryController) UpdateProductDisplayCategory(ctx *gin.Context) {\n\treqDto := req.UpdateProductDisplayCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tproductDisplayCategory, err := productDisplayCategoryService.FindProductDisplayCategoryById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tproductDisplayCategory.Name = reqDto.Name\n\t}\n\tif reqDto.ProductTypes != nil {\n\t\tproductDisplayCategory.ProductTypes = reqDto.ProductTypes\n\t}\n\tif reqDto.IsDisplaySecond != nil {\n\t\tproductDisplayCategory.IsDisplaySecond = reqDto.IsDisplaySecond\n\t}\n\terr = productDisplayCategoryService.UpdateProductDisplayCategory(ctx, productDisplayCategory)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productDisplayCategoryTransfer.PoToVo(*productDisplayCategory))\n}\n\n// @Summary 删除商品展示分类\n// @Description 删除商品展示分类\n// @Tags 商品展示分类\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductDisplayCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productDisplayCategory/delete [post]\nfunc (controller *ProductDisplayCategoryController) DeleteProductDisplayCategory(ctx *gin.Context) {\n\treqDto := req.DeleteProductDisplayCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = productDisplayCategoryService.DeleteProductDisplayCategory(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询商品展示分类\n// @Description 查询商品展示分类\n// @Tags 商品展示分类\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductDisplayCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductDisplayCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productDisplayCategory/query [post]\nfunc (controller *ProductDisplayCategoryController) QueryProductDisplayCategories(ctx *gin.Context) {\n\treqDto := req.QueryProductDisplayCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := productDisplayCategoryService.FindAllProductDisplayCategory(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ProductDisplayCategoryVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productDisplayCategoryTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询商品展示分类列表\n// @Description 查询商品展示分类列表\n// @Tags 商品展示分类\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductDisplayCategoryReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductDisplayCategoryVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productDisplayCategory/list [post]\nfunc (a *ProductDisplayCategoryController) ListProductDisplayCategories(ctx *gin.Context) {\n\treqDto := req.QueryProductDisplayCategoryReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := productDisplayCategoryService.FindAllProductDisplayCategoryWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ProductDisplayCategoryVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductDisplayCategoryVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productDisplayCategoryTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductDisplayCategoryRoute struct {\n}\n\nfunc (s *ProductDisplayCategoryRoute) InitProductDisplayCategoryRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductDisplayCategoryController := controller.ProductDisplayCategoryController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productDisplayCategory/add\", productDisplayCategoryController.AddProductDisplayCategory)    //add\n\t\troute.POST(\"/api/productDisplayCategory/update\", productDisplayCategoryController.UpdateProductDisplayCategory) //update\n\t\troute.POST(\"/api/productDisplayCategory/delete\", productDisplayCategoryController.DeleteProductDisplayCategory) //delete\n\t\troute.POST(\"/api/productDisplayCategory/query\", productDisplayCategoryController.QueryProductDisplayCategories)     //query\n\t\troute.POST(\"/api/productDisplayCategory/list\", productDisplayCategoryController.ListProductDisplayCategories)     //list\n\t}\n}\n"}]