[{"po": "package po\n\n// ProductPackage 产品套餐实体\ntype ProductPackage struct {\n\tId                              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tName                            *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                    // 名称\n\tCategory                        *string `gorm:\"column:category;type:varchar(64);default:''\" json:\"category\"`            // 类别\n\tSeries                         *string `gorm:\"column:series;type:varchar(64);default:''\" json:\"series\"`                // 系列\n\tCurrentPrice                    *int64  `gorm:\"column:current_price;type:bigint;default:0\" json:\"currentPrice\"`        // 当前价格\n\tSupportsDiscount               *bool   `gorm:\"column:supports_discount;type:tinyint(1);default:0\" json:\"supportsDiscount\"` // 是否支持折扣\n\tAreaPrices                     *string `gorm:\"column:area_prices;type:text\" json:\"areaPrices\"`                        // 区域价格\n\tTimeSlotPrices                 *string `gorm:\"column:time_slot_prices;type:text\" json:\"timeSlotPrices\"`            // 时段价格\n\tFreeDrinkMode                  *bool   `gorm:\"column:free_drink_mode;type:tinyint(1);default:0\" json:\"freeDrinkMode\"`   // 免费酒水模式\n\tPackageProducts                *string `gorm:\"column:package_products;type:text\" json:\"packageProducts\"`            // 套餐产品\n\tOptionalGroups                 *string `gorm:\"column:optional_groups;type:text\" json:\"optionalGroups\"`              // 可选组\n\tMemberCardPaymentLimit         *string `gorm:\"column:member_card_payment_limit;type:text\" json:\"memberCardPaymentLimit\"` // 会员卡支付限制\n\tOrderQuantityLimit             *int    `gorm:\"column:order_quantity_limit;type:int;default:0\" json:\"orderQuantityLimit\"` // 订单数量限制\n\tBarcode                        *string `gorm:\"column:barcode;type:varchar(64);default:''\" json:\"barcode\"`              // 条形码\n\tImage                          *string `gorm:\"column:image;type:varchar(255);default:''\" json:\"image\"`                // 图片\n\tIsOnShelf                      *bool   `gorm:\"column:is_on_shelf;type:tinyint(1);default:0\" json:\"isOnShelf\"`         // 是否上架\n\tShelfTimeSlots                 *string `gorm:\"column:shelf_time_slots;type:text\" json:\"shelfTimeSlots\"`            // 上架时段\n\tStaffGift                      *bool   `gorm:\"column:staff_gift;type:tinyint(1);default:0\" json:\"staffGift\"`         // 员工赠送\n\tCountInMinimumConsumption      *bool   `gorm:\"column:count_in_minimum_consumption;type:tinyint(1);default:0\" json:\"countInMinimumConsumption\"` // 是否计入最低消费\n\tCalculatePerformance           *bool   `gorm:\"column:calculate_performance;type:tinyint(1);default:0\" json:\"calculatePerformance\"` // 是否计算业绩\n\tIsPromoted                     *bool   `gorm:\"column:is_promoted;type:tinyint(1);default:0\" json:\"isPromoted\"`        // 是否促销\n\tDeploymentRoomTypes            *string `gorm:\"column:deployment_room_types;type:text\" json:\"deploymentRoomTypes\"`   // 部署包厢类型\n\tDeploymentAreas                *string `gorm:\"column:deployment_areas;type:text\" json:\"deploymentAreas\"`           // 部署区域\n\tDeploymentChannels             *string `gorm:\"column:deployment_channels;type:text\" json:\"deploymentChannels\"`      // 部署渠道\n\tAvailableAfterMinimumConsumption *bool   `gorm:\"column:available_after_minimum_consumption;type:tinyint(1);default:0\" json:\"availableAfterMinimumConsumption\"` // 最低消费后可用\n\tConsumptionGiftCoupon          *string `gorm:\"column:consumption_gift_coupon;type:text\" json:\"consumptionGiftCoupon\"` // 消费赠送优惠券\n\tDescription                    *string `gorm:\"column:description;type:text\" json:\"description\"`                    // 描述\n\tCtime                          *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime                          *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState                          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态值\n\tVersion                        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductPackage) TableName() string {\n\treturn \"product_package\"\n}\n\nfunc (p ProductPackage) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductPackageVO 产品套餐值对象\ntype ProductPackageVO struct {\n\tId                              string `json:\"id\"`                              // ID\n\tName                            string `json:\"name\"`                            // 名称\n\tCategory                        string `json:\"category\"`                        // 类别\n\tSeries                         string `json:\"series\"`                         // 系列\n\tCurrentPrice                    int64  `json:\"currentPrice\"`                    // 当前价格\n\tSupportsDiscount               bool   `json:\"supportsDiscount\"`               // 是否支持折扣\n\tAreaPrices                     string `json:\"areaPrices\"`                     // 区域价格\n\tTimeSlotPrices                 string `json:\"timeSlotPrices\"`                 // 时段价格\n\tFreeDrinkMode                  bool   `json:\"freeDrinkMode\"`                  // 免费酒水模式\n\tPackageProducts                string `json:\"packageProducts\"`                // 套餐产品\n\tOptionalGroups                 string `json:\"optionalGroups\"`                 // 可选组\n\tMemberCardPaymentLimit         string `json:\"memberCardPaymentLimit\"`         // 会员卡支付限制\n\tOrderQuantityLimit             int    `json:\"orderQuantityLimit\"`             // 订单数量限制\n\tBarcode                        string `json:\"barcode\"`                        // 条形码\n\tImage                          string `json:\"image\"`                          // 图片\n\tIsOnShelf                      bool   `json:\"isOnShelf\"`                      // 是否上架\n\tShelfTimeSlots                 string `json:\"shelfTimeSlots\"`                 // 上架时段\n\tStaffGift                      bool   `json:\"staffGift\"`                      // 员工赠送\n\tCountInMinimumConsumption      bool   `json:\"countInMinimumConsumption\"`      // 是否计入最低消费\n\tCalculatePerformance           bool   `json:\"calculatePerformance\"`           // 是否计算业绩\n\tIsPromoted                     bool   `json:\"isPromoted\"`                     // 是否促销\n\tDeploymentRoomTypes            string `json:\"deploymentRoomTypes\"`            // 部署包厢类型\n\tDeploymentAreas                string `json:\"deploymentAreas\"`                // 部署区域\n\tDeploymentChannels             string `json:\"deploymentChannels\"`             // 部署渠道\n\tAvailableAfterMinimumConsumption bool   `json:\"availableAfterMinimumConsumption\"` // 最低消费后可用\n\tConsumptionGiftCoupon          string `json:\"consumptionGiftCoupon\"`          // 消费赠送优惠券\n\tDescription                    string `json:\"description\"`                    // 描述\n\tCtime                          int64  `json:\"ctime\"`                          // 创建时间戳\n\tUtime                          int64  `json:\"utime\"`                          // 更新时间戳\n\tState                          int    `json:\"state\"`                          // 状态值\n\tVersion                        int    `json:\"version\"`                        // 版本号\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductPackageTransfer struct {\n}\n\nfunc (transfer *ProductPackageTransfer) PoToVo(po po.ProductPackage) vo.ProductPackageVO {\n\tvo := vo.ProductPackageVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductPackageTransfer) VoToPo(vo vo.ProductPackageVO) po.ProductPackage {\n\tpo := po.ProductPackage{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "req_add": "package req\n\n// AddProductPackageReqDto 创建产品套餐请求DTO\ntype AddProductPackageReqDto struct {\n\tName                            *string `json:\"name\"`                            // 名称\n\tCategory                        *string `json:\"category\"`                        // 类别\n\tSeries                         *string `json:\"series\"`                         // 系列\n\tCurrentPrice                    *int64  `json:\"currentPrice\"`                    // 当前价格\n\tSupportsDiscount               *bool   `json:\"supportsDiscount\"`               // 是否支持折扣\n\tAreaPrices                     *string `json:\"areaPrices\"`                     // 区域价格\n\tTimeSlotPrices                 *string `json:\"timeSlotPrices\"`                 // 时段价格\n\tFreeDrinkMode                  *bool   `json:\"freeDrinkMode\"`                  // 免费酒水模式\n\tPackageProducts                *string `json:\"packageProducts\"`                // 套餐产品\n\tOptionalGroups                 *string `json:\"optionalGroups\"`                 // 可选组\n\tMemberCardPaymentLimit         *string `json:\"memberCardPaymentLimit\"`         // 会员卡支付限制\n\tOrderQuantityLimit             *int    `json:\"orderQuantityLimit\"`             // 订单数量限制\n\tBarcode                        *string `json:\"barcode\"`                        // 条形码\n\tImage                          *string `json:\"image\"`                          // 图片\n\tIsOnShelf                      *bool   `json:\"isOnShelf\"`                      // 是否上架\n\tShelfTimeSlots                 *string `json:\"shelfTimeSlots\"`                 // 上架时段\n\tStaffGift                      *bool   `json:\"staffGift\"`                      // 员工赠送\n\tCountInMinimumConsumption      *bool   `json:\"countInMinimumConsumption\"`      // 是否计入最低消费\n\tCalculatePerformance           *bool   `json:\"calculatePerformance\"`           // 是否计算业绩\n\tIsPromoted                     *bool   `json:\"isPromoted\"`                     // 是否促销\n\tDeploymentRoomTypes            *string `json:\"deploymentRoomTypes\"`            // 部署包厢类型\n\tDeploymentAreas                *string `json:\"deploymentAreas\"`                // 部署区域\n\tDeploymentChannels             *string `json:\"deploymentChannels\"`             // 部署渠道\n\tAvailableAfterMinimumConsumption *bool   `json:\"availableAfterMinimumConsumption\"` // 最低消费后可用\n\tConsumptionGiftCoupon          *string `json:\"consumptionGiftCoupon\"`          // 消费赠送优惠券\n\tDescription                    *string `json:\"description\"`                    // 描述\n}\n", "req_update": "package req\n\n// UpdateProductPackageReqDto 更新产品套餐请求DTO\ntype UpdateProductPackageReqDto struct {\n\tId                              *string `json:\"id\"`                              // ID\n\tName                            *string `json:\"name\"`                            // 名称\n\tCategory                        *string `json:\"category\"`                        // 类别\n\tSeries                         *string `json:\"series\"`                         // 系列\n\tCurrentPrice                    *int64  `json:\"currentPrice\"`                    // 当前价格\n\tSupportsDiscount               *bool   `json:\"supportsDiscount\"`               // 是否支持折扣\n\tAreaPrices                     *string `json:\"areaPrices\"`                     // 区域价格\n\tTimeSlotPrices                 *string `json:\"timeSlotPrices\"`                 // 时段价格\n\tFreeDrinkMode                  *bool   `json:\"freeDrinkMode\"`                  // 免费酒水模式\n\tPackageProducts                *string `json:\"packageProducts\"`                // 套餐产品\n\tOptionalGroups                 *string `json:\"optionalGroups\"`                 // 可选组\n\tMemberCardPaymentLimit         *string `json:\"memberCardPaymentLimit\"`         // 会员卡支付限制\n\tOrderQuantityLimit             *int    `json:\"orderQuantityLimit\"`             // 订单数量限制\n\tBarcode                        *string `json:\"barcode\"`                        // 条形码\n\tImage                          *string `json:\"image\"`                          // 图片\n\tIsOnShelf                      *bool   `json:\"isOnShelf\"`                      // 是否上架\n\tShelfTimeSlots                 *string `json:\"shelfTimeSlots\"`                 // 上架时段\n\tStaffGift                      *bool   `json:\"staffGift\"`                      // 员工赠送\n\tCountInMinimumConsumption      *bool   `json:\"countInMinimumConsumption\"`      // 是否计入最低消费\n\tCalculatePerformance           *bool   `json:\"calculatePerformance\"`           // 是否计算业绩\n\tIsPromoted                     *bool   `json:\"isPromoted\"`                     // 是否促销\n\tDeploymentRoomTypes            *string `json:\"deploymentRoomTypes\"`            // 部署包厢类型\n\tDeploymentAreas                *string `json:\"deploymentAreas\"`                // 部署区域\n\tDeploymentChannels             *string `json:\"deploymentChannels\"`             // 部署渠道\n\tAvailableAfterMinimumConsumption *bool   `json:\"availableAfterMinimumConsumption\"` // 最低消费后可用\n\tConsumptionGiftCoupon          *string `json:\"consumptionGiftCoupon\"`          // 消费赠送优惠券\n\tDescription                    *string `json:\"description\"`                    // 描述\n}\n", "req_delete": "package req\n\ntype DeleteProductPackageReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryProductPackageReqDto 查询产品套餐请求DTO\ntype QueryProductPackageReqDto struct {\n\tId                              *string `json:\"id\"`                              // ID\n\tName                            *string `json:\"name\"`                            // 名称\n\tCategory                        *string `json:\"category\"`                        // 类别\n\tSeries                         *string `json:\"series\"`                         // 系列\n\tCurrentPrice                    *int64  `json:\"currentPrice\"`                    // 当前价格\n\tSupportsDiscount               *bool   `json:\"supportsDiscount\"`               // 是否支持折扣\n\tFreeDrinkMode                  *bool   `json:\"freeDrinkMode\"`                  // 免费酒水模式\n\tOrderQuantityLimit             *int    `json:\"orderQuantityLimit\"`             // 订单数量限制\n\tBarcode                        *string `json:\"barcode\"`                        // 条形码\n\tIsOnShelf                      *bool   `json:\"isOnShelf\"`                      // 是否上架\n\tStaffGift                      *bool   `json:\"staffGift\"`                      // 员工赠送\n\tCountInMinimumConsumption      *bool   `json:\"countInMinimumConsumption\"`      // 是否计入最低消费\n\tCalculatePerformance           *bool   `json:\"calculatePerformance\"`           // 是否计算业绩\n\tIsPromoted                     *bool   `json:\"isPromoted\"`                     // 是否促销\n\tAvailableAfterMinimumConsumption *bool   `json:\"availableAfterMinimumConsumption\"` // 最低消费后可用\n\tPageNum                        *int    `json:\"pageNum\"`                        // 页码\n\tPageSize                       *int    `json:\"pageSize\"`                       // 每页记录数\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductPackageService struct {\n}\n\nfunc (service *ProductPackageService) CreateProductPackage(logCtx *gin.Context, productPackage *po.ProductPackage) error {\n\treturn Save(productPackage)\n}\n\nfunc (service *ProductPackageService) UpdateProductPackage(logCtx *gin.Context, productPackage *po.ProductPackage) error {\n\treturn Update(productPackage)\n}\n\nfunc (service *ProductPackageService) DeleteProductPackage(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductPackage{Id: &id})\n}\n\nfunc (service *ProductPackageService) FindProductPackageById(logCtx *gin.Context, id string) (productPackage *po.ProductPackage, err error) {\n\tproductPackage = &po.ProductPackage{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productPackage).Error\n\treturn\n}\n\nfunc (service *ProductPackageService) FindAllProductPackage(logCtx *gin.Context, reqDto *req.QueryProductPackageReqDto) (list *[]po.ProductPackage, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductPackage{})\n\n\t// 构建查询条件\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Category != nil && *reqDto.Category != \"\" {\n\t\tdb = db.Where(\"category=?\", *reqDto.Category)\n\t}\n\tif reqDto.Series != nil && *reqDto.Series != \"\" {\n\t\tdb = db.Where(\"series=?\", *reqDto.Series)\n\t}\n\tif reqDto.CurrentPrice != nil {\n\t\tdb = db.Where(\"current_price=?\", *reqDto.CurrentPrice)\n\t}\n\tif reqDto.SupportsDiscount != nil {\n\t\tdb = db.Where(\"supports_discount=?\", *reqDto.SupportsDiscount)\n\t}\n\tif reqDto.FreeDrinkMode != nil {\n\t\tdb = db.Where(\"free_drink_mode=?\", *reqDto.FreeDrinkMode)\n\t}\n\tif reqDto.OrderQuantityLimit != nil {\n\t\tdb = db.Where(\"order_quantity_limit=?\", *reqDto.OrderQuantityLimit)\n\t}\n\tif reqDto.Barcode != nil && *reqDto.Barcode != \"\" {\n\t\tdb = db.Where(\"barcode=?\", *reqDto.Barcode)\n\t}\n\tif reqDto.IsOnShelf != nil {\n\t\tdb = db.Where(\"is_on_shelf=?\", *reqDto.IsOnShelf)\n\t}\n\tif reqDto.StaffGift != nil {\n\t\tdb = db.Where(\"staff_gift=?\", *reqDto.StaffGift)\n\t}\n\tif reqDto.CountInMinimumConsumption != nil {\n\t\tdb = db.Where(\"count_in_minimum_consumption=?\", *reqDto.CountInMinimumConsumption)\n\t}\n\tif reqDto.CalculatePerformance != nil {\n\t\tdb = db.Where(\"calculate_performance=?\", *reqDto.CalculatePerformance)\n\t}\n\tif reqDto.IsPromoted != nil {\n\t\tdb = db.Where(\"is_promoted=?\", *reqDto.IsPromoted)\n\t}\n\tif reqDto.AvailableAfterMinimumConsumption != nil {\n\t\tdb = db.Where(\"available_after_minimum_consumption=?\", *reqDto.AvailableAfterMinimumConsumption)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductPackage{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductPackageService) FindAllProductPackageWithPagination(logCtx *gin.Context, reqDto *req.QueryProductPackageReqDto) (list *[]po.ProductPackage, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductPackage{})\n\n\t// 设置默认分页参数\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\t// 构建查询条件\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Category != nil && *reqDto.Category != \"\" {\n\t\tdb = db.Where(\"category=?\", *reqDto.Category)\n\t}\n\tif reqDto.Series != nil && *reqDto.Series != \"\" {\n\t\tdb = db.Where(\"series=?\", *reqDto.Series)\n\t}\n\tif reqDto.CurrentPrice != nil {\n\t\tdb = db.Where(\"current_price=?\", *reqDto.CurrentPrice)\n\t}\n\tif reqDto.SupportsDiscount != nil {\n\t\tdb = db.Where(\"supports_discount=?\", *reqDto.SupportsDiscount)\n\t}\n\tif reqDto.FreeDrinkMode != nil {\n\t\tdb = db.Where(\"free_drink_mode=?\", *reqDto.FreeDrinkMode)\n\t}\n\tif reqDto.OrderQuantityLimit != nil {\n\t\tdb = db.Where(\"order_quantity_limit=?\", *reqDto.OrderQuantityLimit)\n\t}\n\tif reqDto.Barcode != nil && *reqDto.Barcode != \"\" {\n\t\tdb = db.Where(\"barcode=?\", *reqDto.Barcode)\n\t}\n\tif reqDto.IsOnShelf != nil {\n\t\tdb = db.Where(\"is_on_shelf=?\", *reqDto.IsOnShelf)\n\t}\n\tif reqDto.StaffGift != nil {\n\t\tdb = db.Where(\"staff_gift=?\", *reqDto.StaffGift)\n\t}\n\tif reqDto.CountInMinimumConsumption != nil {\n\t\tdb = db.Where(\"count_in_minimum_consumption=?\", *reqDto.CountInMinimumConsumption)\n\t}\n\tif reqDto.CalculatePerformance != nil {\n\t\tdb = db.Where(\"calculate_performance=?\", *reqDto.CalculatePerformance)\n\t}\n\tif reqDto.IsPromoted != nil {\n\t\tdb = db.Where(\"is_promoted=?\", *reqDto.IsPromoted)\n\t}\n\tif reqDto.AvailableAfterMinimumConsumption != nil {\n\t\tdb = db.Where(\"available_after_minimum_consumption=?\", *reqDto.AvailableAfterMinimumConsumption)\n\t}\n\n\t// 获取总记录数\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductPackage{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\n\t// 分页查询\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductPackageController struct{}\n\nvar (\n\tproductPackageService  = impl.ProductPackageService{}\n\tproductPackageTransfer = transfer.ProductPackageTransfer{}\n)\n\n// @Summary 添加产品套餐\n// @Description 添加产品套餐\n// @Tags 产品套餐\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductPackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductPackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-package/add [post]\nfunc (controller *ProductPackageController) AddProductPackage(ctx *gin.Context) {\n\treqDto := req.AddProductPackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductPackage := po.ProductPackage{}\n\tif reqDto.Name != nil {\n\t\tproductPackage.Name = reqDto.Name\n\t}\n\tif reqDto.Category != nil {\n\t\tproductPackage.Category = reqDto.Category\n\t}\n\tif reqDto.Series != nil {\n\t\tproductPackage.Series = reqDto.Series\n\t}\n\tif reqDto.CurrentPrice != nil {\n\t\tproductPackage.CurrentPrice = reqDto.CurrentPrice\n\t}\n\tif reqDto.SupportsDiscount != nil {\n\t\tproductPackage.SupportsDiscount = reqDto.SupportsDiscount\n\t}\n\tif reqDto.AreaPrices != nil {\n\t\tproductPackage.AreaPrices = reqDto.AreaPrices\n\t}\n\tif reqDto.TimeSlotPrices != nil {\n\t\tproductPackage.TimeSlotPrices = reqDto.TimeSlotPrices\n\t}\n\tif reqDto.FreeDrinkMode != nil {\n\t\tproductPackage.FreeDrinkMode = reqDto.FreeDrinkMode\n\t}\n\tif reqDto.PackageProducts != nil {\n\t\tproductPackage.PackageProducts = reqDto.PackageProducts\n\t}\n\tif reqDto.OptionalGroups != nil {\n\t\tproductPackage.OptionalGroups = reqDto.OptionalGroups\n\t}\n\tif reqDto.MemberCardPaymentLimit != nil {\n\t\tproductPackage.MemberCardPaymentLimit = reqDto.MemberCardPaymentLimit\n\t}\n\tif reqDto.OrderQuantityLimit != nil {\n\t\tproductPackage.OrderQuantityLimit = reqDto.OrderQuantityLimit\n\t}\n\tif reqDto.Barcode != nil {\n\t\tproductPackage.Barcode = reqDto.Barcode\n\t}\n\tif reqDto.Image != nil {\n\t\tproductPackage.Image = reqDto.Image\n\t}\n\tif reqDto.IsOnShelf != nil {\n\t\tproductPackage.IsOnShelf = reqDto.IsOnShelf\n\t}\n\tif reqDto.ShelfTimeSlots != nil {\n\t\tproductPackage.ShelfTimeSlots = reqDto.ShelfTimeSlots\n\t}\n\tif reqDto.StaffGift != nil {\n\t\tproductPackage.StaffGift = reqDto.StaffGift\n\t}\n\tif reqDto.CountInMinimumConsumption != nil {\n\t\tproductPackage.CountInMinimumConsumption = reqDto.CountInMinimumConsumption\n\t}\n\tif reqDto.CalculatePerformance != nil {\n\t\tproductPackage.CalculatePerformance = reqDto.CalculatePerformance\n\t}\n\tif reqDto.IsPromoted != nil {\n\t\tproductPackage.IsPromoted = reqDto.IsPromoted\n\t}\n\tif reqDto.DeploymentRoomTypes != nil {\n\t\tproductPackage.DeploymentRoomTypes = reqDto.DeploymentRoomTypes\n\t}\n\tif reqDto.DeploymentAreas != nil {\n\t\tproductPackage.DeploymentAreas = reqDto.DeploymentAreas\n\t}\n\tif reqDto.DeploymentChannels != nil {\n\t\tproductPackage.DeploymentChannels = reqDto.DeploymentChannels\n\t}\n\tif reqDto.AvailableAfterMinimumConsumption != nil {\n\t\tproductPackage.AvailableAfterMinimumConsumption = reqDto.AvailableAfterMinimumConsumption\n\t}\n\tif reqDto.ConsumptionGiftCoupon != nil {\n\t\tproductPackage.ConsumptionGiftCoupon = reqDto.ConsumptionGiftCoupon\n\t}\n\tif reqDto.Description != nil {\n\t\tproductPackage.Description = reqDto.Description\n\t}\n\n\terr = productPackageService.CreateProductPackage(ctx, &productPackage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productPackageTransfer.PoToVo(productPackage))\n}\n\n// @Summary 更新产品套餐\n// @Description 更新产品套餐\n// @Tags 产品套餐\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductPackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductPackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-package/update [post]\nfunc (controller *ProductPackageController) UpdateProductPackage(ctx *gin.Context) {\n\treqDto := req.UpdateProductPackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproductPackage, err := productPackageService.FindProductPackageById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tproductPackage.Name = reqDto.Name\n\t}\n\tif reqDto.Category != nil {\n\t\tproductPackage.Category = reqDto.Category\n\t}\n\tif reqDto.Series != nil {\n\t\tproductPackage.Series = reqDto.Series\n\t}\n\tif reqDto.CurrentPrice != nil {\n\t\tproductPackage.CurrentPrice = reqDto.CurrentPrice\n\t}\n\tif reqDto.SupportsDiscount != nil {\n\t\tproductPackage.SupportsDiscount = reqDto.SupportsDiscount\n\t}\n\tif reqDto.AreaPrices != nil {\n\t\tproductPackage.AreaPrices = reqDto.AreaPrices\n\t}\n\tif reqDto.TimeSlotPrices != nil {\n\t\tproductPackage.TimeSlotPrices = reqDto.TimeSlotPrices\n\t}\n\tif reqDto.FreeDrinkMode != nil {\n\t\tproductPackage.FreeDrinkMode = reqDto.FreeDrinkMode\n\t}\n\tif reqDto.PackageProducts != nil {\n\t\tproductPackage.PackageProducts = reqDto.PackageProducts\n\t}\n\tif reqDto.OptionalGroups != nil {\n\t\tproductPackage.OptionalGroups = reqDto.OptionalGroups\n\t}\n\tif reqDto.MemberCardPaymentLimit != nil {\n\t\tproductPackage.MemberCardPaymentLimit = reqDto.MemberCardPaymentLimit\n\t}\n\tif reqDto.OrderQuantityLimit != nil {\n\t\tproductPackage.OrderQuantityLimit = reqDto.OrderQuantityLimit\n\t}\n\tif reqDto.Barcode != nil {\n\t\tproductPackage.Barcode = reqDto.Barcode\n\t}\n\tif reqDto.Image != nil {\n\t\tproductPackage.Image = reqDto.Image\n\t}\n\tif reqDto.IsOnShelf != nil {\n\t\tproductPackage.IsOnShelf = reqDto.IsOnShelf\n\t}\n\tif reqDto.ShelfTimeSlots != nil {\n\t\tproductPackage.ShelfTimeSlots = reqDto.ShelfTimeSlots\n\t}\n\tif reqDto.StaffGift != nil {\n\t\tproductPackage.StaffGift = reqDto.StaffGift\n\t}\n\tif reqDto.CountInMinimumConsumption != nil {\n\t\tproductPackage.CountInMinimumConsumption = reqDto.CountInMinimumConsumption\n\t}\n\tif reqDto.CalculatePerformance != nil {\n\t\tproductPackage.CalculatePerformance = reqDto.CalculatePerformance\n\t}\n\tif reqDto.IsPromoted != nil {\n\t\tproductPackage.IsPromoted = reqDto.IsPromoted\n\t}\n\tif reqDto.DeploymentRoomTypes != nil {\n\t\tproductPackage.DeploymentRoomTypes = reqDto.DeploymentRoomTypes\n\t}\n\tif reqDto.DeploymentAreas != nil {\n\t\tproductPackage.DeploymentAreas = reqDto.DeploymentAreas\n\t}\n\tif reqDto.DeploymentChannels != nil {\n\t\tproductPackage.DeploymentChannels = reqDto.DeploymentChannels\n\t}\n\tif reqDto.AvailableAfterMinimumConsumption != nil {\n\t\tproductPackage.AvailableAfterMinimumConsumption = reqDto.AvailableAfterMinimumConsumption\n\t}\n\tif reqDto.ConsumptionGiftCoupon != nil {\n\t\tproductPackage.ConsumptionGiftCoupon = reqDto.ConsumptionGiftCoupon\n\t}\n\tif reqDto.Description != nil {\n\t\tproductPackage.Description = reqDto.Description\n\t}\n\n\terr = productPackageService.UpdateProductPackage(ctx, productPackage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productPackageTransfer.PoToVo(*productPackage))\n}\n\n// @Summary 删除产品套餐\n// @Description 删除产品套餐\n// @Tags 产品套餐\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductPackageReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-package/delete [post]\nfunc (controller *ProductPackageController) DeleteProductPackage(ctx *gin.Context) {\n\treqDto := req.DeleteProductPackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = productPackageService.DeleteProductPackage(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品套餐\n// @Description 查询产品套餐\n// @Tags 产品套餐\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductPackageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductPackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-package/query [post]\nfunc (controller *ProductPackageController) QueryProductPackages(ctx *gin.Context) {\n\treqDto := req.QueryProductPackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := productPackageService.FindAllProductPackage(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ProductPackageVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productPackageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品套餐列表\n// @Description 查询产品套餐列表\n// @Tags 产品套餐\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductPackageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductPackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-package/list [post]\nfunc (controller *ProductPackageController) ListProductPackages(ctx *gin.Context) {\n\treqDto := req.QueryProductPackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := productPackageService.FindAllProductPackageWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ProductPackageVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductPackageVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productPackageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductPackageRoute struct {\n}\n\nfunc (s *ProductPackageRoute) InitProductPackageRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductPackageController := controller.ProductPackageController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/product-package/add\", productPackageController.AddProductPackage)       // add\n\t\troute.POST(\"/api/product-package/update\", productPackageController.UpdateProductPackage) // update\n\t\troute.POST(\"/api/product-package/delete\", productPackageController.DeleteProductPackage) // delete\n\t\troute.POST(\"/api/product-package/query\", productPackageController.QueryProductPackages)  // query\n\t\troute.POST(\"/api/product-package/list\", productPackageController.ListProductPackages)   // list\n\t}\n}\n"}]