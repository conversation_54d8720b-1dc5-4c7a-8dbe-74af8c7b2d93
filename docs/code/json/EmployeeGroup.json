[{"po": "package po\n\n// EmployeeGroup 员工组实体\ntype EmployeeGroup struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tName          *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                  // 员工组名称\n\tSupervisor    *string `gorm:\"column:supervisor;type:varchar(64);default:''\" json:\"supervisor\"`        // 主管\n\tDirectMembers *string `gorm:\"column:direct_members;type:varchar(64);default:''\" json:\"directMembers\"` // 直属成员\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                         // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                         // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                         // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                     // 版本号\n}\n\n// TableName 设置表名\nfunc (EmployeeGroup) TableName() string {\n\treturn \"employee_group\"\n}\n\nfunc (e EmployeeGroup) GetId() string {\n\treturn *e.Id\n}\n", "vo": "package vo\n\n// EmployeeGroupVO 员工组信息值对象\ntype EmployeeGroupVO struct {\n\tId            string `json:\"id\"`            // ID\n\tName          string `json:\"name\"`          // 员工组名称\n\tSupervisor    string `json:\"supervisor\"`    // 主管\n\tDirectMembers string `json:\"directMembers\"` // 直属成员\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddEmployeeGroupReqDto 创建员工组请求DTO\ntype AddEmployeeGroupReqDto struct {\n\tName          *string `json:\"name\"`          // 员工组名称\n\tSupervisor    *string `json:\"supervisor\"`    // 主管\n\tDirectMembers *string `json:\"directMembers\"` // 直属成员\n}\n", "req_update": "package req\n\ntype UpdateEmployeeGroupReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tName          *string `json:\"name\"`          // 员工组名称\n\tSupervisor    *string `json:\"supervisor\"`    // 主管\n\tDirectMembers *string `json:\"directMembers\"` // 直属成员\n}\n", "req_delete": "package req\n\ntype DeleteEmployeeGroupReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryEmployeeGroupReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tName          *string `json:\"name\"`          // 员工组名称\n\tSupervisor    *string `json:\"supervisor\"`    // 主管\n\tDirectMembers *string `json:\"directMembers\"` // 直属成员\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype EmployeeGroupTransfer struct {\n}\n\nfunc (transfer *EmployeeGroupTransfer) PoToVo(po po.EmployeeGroup) vo.EmployeeGroupVO {\n\tvo := vo.EmployeeGroupVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *EmployeeGroupTransfer) VoToPo(vo vo.EmployeeGroupVO) po.EmployeeGroup {\n\tpo := po.EmployeeGroup{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeGroupService struct {\n}\n\nfunc (service *EmployeeGroupService) CreateEmployeeGroup(logCtx *gin.Context, employeeGroup *po.EmployeeGroup) error {\n\treturn Save(employeeGroup)\n}\n\nfunc (service *EmployeeGroupService) UpdateEmployeeGroup(logCtx *gin.Context, employeeGroup *po.EmployeeGroup) error {\n\treturn Update(employeeGroup)\n}\n\nfunc (service *EmployeeGroupService) DeleteEmployeeGroup(logCtx *gin.Context, id string) error {\n\treturn Delete(po.EmployeeGroup{Id: &id})\n}\n\nfunc (service *EmployeeGroupService) FindEmployeeGroupById(logCtx *gin.Context, id string) (employeeGroup *po.EmployeeGroup, err error) {\n\temployeeGroup = &po.EmployeeGroup{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(employeeGroup).Error\n\treturn\n}\n\nfunc (service *EmployeeGroupService) FindAllEmployeeGroup(logCtx *gin.Context, reqDto *req.QueryEmployeeGroupReqDto) (list *[]po.EmployeeGroup, err error) {\n\tdb := model.DBSlave.Self.Model(&po.EmployeeGroup{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.Supervisor != nil && *reqDto.Supervisor != \"\" {\n\t\tdb = db.Where(\"supervisor=?\", *reqDto.Supervisor)\n\t}\n\tif reqDto.DirectMembers != nil && *reqDto.DirectMembers != \"\" {\n\t\tdb = db.Where(\"direct_members=?\", *reqDto.DirectMembers)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.EmployeeGroup{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *EmployeeGroupService) FindAllEmployeeGroupWithPagination(logCtx *gin.Context, reqDto *req.QueryEmployeeGroupReqDto) (list *[]po.EmployeeGroup, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.EmployeeGroup{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.Supervisor != nil && *reqDto.Supervisor != \"\" {\n\t\tdb = db.Where(\"supervisor=?\", *reqDto.Supervisor)\n\t}\n\tif reqDto.DirectMembers != nil && *reqDto.DirectMembers != \"\" {\n\t\tdb = db.Where(\"direct_members=?\", *reqDto.DirectMembers)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.EmployeeGroup{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeGroupController struct{}\n\nvar (\n\temployeeGroupService  = impl.EmployeeGroupService{}\n\temployeeGroupTransfer = transfer.EmployeeGroupTransfer{}\n)\n\n// @Summary 添加员工组\n// @Description 添加员工组\n// @Tags 员工组\n// @Accept json\n// @Produce json\n// @Param body body req.AddEmployeeGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.EmployeeGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group/add [post]\nfunc (controller *EmployeeGroupController) AddEmployeeGroup(ctx *gin.Context) {\n\treqDto := req.AddEmployeeGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\temployeeGroup := po.EmployeeGroup{}\n\tif reqDto.Name != nil {\n\t\temployeeGroup.Name = reqDto.Name\n\t}\n\tif reqDto.Supervisor != nil {\n\t\temployeeGroup.Supervisor = reqDto.Supervisor\n\t}\n\tif reqDto.DirectMembers != nil {\n\t\temployeeGroup.DirectMembers = reqDto.DirectMembers\n\t}\n\terr = employeeGroupService.CreateEmployeeGroup(ctx, &employeeGroup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, employeeGroupTransfer.PoToVo(employeeGroup))\n}\n\n// @Summary 更新员工组\n// @Description 更新员工组\n// @Tags 员工组\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateEmployeeGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.EmployeeGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group/update [post]\nfunc (controller *EmployeeGroupController) UpdateEmployeeGroup(ctx *gin.Context) {\n\treqDto := req.UpdateEmployeeGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\temployeeGroup, err := employeeGroupService.FindEmployeeGroupById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\temployeeGroup.Name = reqDto.Name\n\t}\n\tif reqDto.Supervisor != nil {\n\t\temployeeGroup.Supervisor = reqDto.Supervisor\n\t}\n\tif reqDto.DirectMembers != nil {\n\t\temployeeGroup.DirectMembers = reqDto.DirectMembers\n\t}\n\terr = employeeGroupService.UpdateEmployeeGroup(ctx, employeeGroup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, employeeGroupTransfer.PoToVo(*employeeGroup))\n}\n\n// @Summary 删除员工组\n// @Description 删除员工组\n// @Tags 员工组\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteEmployeeGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group/delete [post]\nfunc (controller *EmployeeGroupController) DeleteEmployeeGroup(ctx *gin.Context) {\n\treqDto := req.DeleteEmployeeGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = employeeGroupService.DeleteEmployeeGroup(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询员工组\n// @Description 查询员工组\n// @Tags 员工组\n// @Accept json\n// @Produce json\n// @Param body body req.QueryEmployeeGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.EmployeeGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group/query [post]\nfunc (controller *EmployeeGroupController) QueryEmployeeGroups(ctx *gin.Context) {\n\treqDto := req.QueryEmployeeGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := employeeGroupService.FindAllEmployeeGroup(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.EmployeeGroupVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, employeeGroupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询员工组列表\n// @Description 查询员工组列表\n// @Tags 员工组\n// @Accept json\n// @Produce json\n// @Param body body req.QueryEmployeeGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.EmployeeGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group/list [post]\nfunc (a *EmployeeGroupController) ListEmployeeGroups(ctx *gin.Context) {\n\treqDto := req.QueryEmployeeGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := employeeGroupService.FindAllEmployeeGroupWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.EmployeeGroupVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.EmployeeGroupVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, employeeGroupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeGroupRoute struct {\n}\n\nfunc (s *EmployeeGroupRoute) InitEmployeeGroupRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\temployeeGroupController := controller.EmployeeGroupController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/employee-group/add\", employeeGroupController.AddEmployeeGroup)       //add\n\t\troute.POST(\"/api/employee-group/update\", employeeGroupController.UpdateEmployeeGroup) //update\n\t\troute.POST(\"/api/employee-group/delete\", employeeGroupController.DeleteEmployeeGroup) //delete\n\t\troute.POST(\"/api/employee-group/query\", employeeGroupController.QueryEmployeeGroups)  //query\n\t\troute.POST(\"/api/employee-group/list\", employeeGroupController.ListEmployeeGroups)   //list\n\t}\n}\n"}]