[{"po": "package po\n\n// CustomerGroup 客户组实体\ntype CustomerGroup struct {\n\tId                    *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                  *string  `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                      // 客户组名称\n\tCardLevels           *string  `gorm:\"column:card_levels;type:varchar(255);default:''\" json:\"cardLevels\"`         // 卡等级列表\n\tGender               *string  `gorm:\"column:gender;type:varchar(32);default:''\" json:\"gender\"`                    // 性别\n\tAgeRange             *int     `gorm:\"column:age_range;type:int;default:0\" json:\"ageRange\"`                       // 年龄范围\n\tBirthdayRange        *string  `gorm:\"column:birthday_range;type:varchar(255);default:''\" json:\"birthdayRange\"`    // 生日范围\n\tPointsRange          *int     `gorm:\"column:points_range;type:int;default:0\" json:\"pointsRange\"`                 // 积分范围\n\tConsumptionBehavior  *string  `gorm:\"column:consumption_behavior;type:varchar(255);default:''\" json:\"consumptionBehavior\"` // 消费行为\n\tTotalConsumptionTimes *int     `gorm:\"column:total_consumption_times;type:int;default:0\" json:\"totalConsumptionTimes\"` // 总消费次数\n\tTotalConsumptionAmount *float64 `gorm:\"column:total_consumption_amount;type:decimal(10,2);default:0\" json:\"totalConsumptionAmount\"` // 总消费金额\n\tCardBalance          *float64 `gorm:\"column:card_balance;type:decimal(10,2);default:0\" json:\"cardBalance\"`        // 卡余额\n\tCtime                *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                             // 创建时间\n\tUtime                *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                             // 更新时间\n\tState                *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                             // 状态\n\tVersion              *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                         // 版本号\n}\n\n// TableName 设置表名\nfunc (CustomerGroup) TableName() string {\n\treturn \"customer_group\"\n}\n\nfunc (c CustomerGroup) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CustomerGroupVO 客户组信息值对象\ntype CustomerGroupVO struct {\n\tId                    string  `json:\"id\"`                    // ID\n\tName                  string  `json:\"name\"`                  // 客户组名称\n\tCardLevels           string  `json:\"cardLevels\"`           // 卡等级列表\n\tGender               string  `json:\"gender\"`               // 性别\n\tAgeRange             int     `json:\"ageRange\"`             // 年龄范围\n\tBirthdayRange        string  `json:\"birthdayRange\"`        // 生日范围\n\tPointsRange          int     `json:\"pointsRange\"`          // 积分范围\n\tConsumptionBehavior  string  `json:\"consumptionBehavior\"`  // 消费行为\n\tTotalConsumptionTimes int     `json:\"totalConsumptionTimes\"` // 总消费次数\n\tTotalConsumptionAmount float64 `json:\"totalConsumptionAmount\"` // 总消费金额\n\tCardBalance          float64 `json:\"cardBalance\"`          // 卡余额\n\tCtime                int64   `json:\"ctime\"`                // 创建时间\n\tUtime                int64   `json:\"utime\"`                // 更新时间\n\tState                int     `json:\"state\"`                // 状态\n\tVersion              int     `json:\"version\"`              // 版本号\n}\n", "req_add": "package req\n\n// AddCustomerGroupReqDto 创建客户组请求DTO\ntype AddCustomerGroupReqDto struct {\n\tName                  *string  `json:\"name\"`                  // 客户组名称\n\tCardLevels           *string  `json:\"cardLevels\"`           // 卡等级列表\n\tGender               *string  `json:\"gender\"`               // 性别\n\tAgeRange             *int     `json:\"ageRange\"`             // 年龄范围\n\tBirthdayRange        *string  `json:\"birthdayRange\"`        // 生日范围\n\tPointsRange          *int     `json:\"pointsRange\"`          // 积分范围\n\tConsumptionBehavior  *string  `json:\"consumptionBehavior\"`  // 消费行为\n\tTotalConsumptionTimes *int     `json:\"totalConsumptionTimes\"` // 总消费次数\n\tTotalConsumptionAmount *float64 `json:\"totalConsumptionAmount\"` // 总消费金额\n\tCardBalance          *float64 `json:\"cardBalance\"`          // 卡余额\n}\n", "req_update": "package req\n\ntype UpdateCustomerGroupReqDto struct {\n\tId                    *string  `json:\"id\"`                    // ID\n\tName                  *string  `json:\"name\"`                  // 客户组名称\n\tCardLevels           *string  `json:\"cardLevels\"`           // 卡等级列表\n\tGender               *string  `json:\"gender\"`               // 性别\n\tAgeRange             *int     `json:\"ageRange\"`             // 年龄范围\n\tBirthdayRange        *string  `json:\"birthdayRange\"`        // 生日范围\n\tPointsRange          *int     `json:\"pointsRange\"`          // 积分范围\n\tConsumptionBehavior  *string  `json:\"consumptionBehavior\"`  // 消费行为\n\tTotalConsumptionTimes *int     `json:\"totalConsumptionTimes\"` // 总消费次数\n\tTotalConsumptionAmount *float64 `json:\"totalConsumptionAmount\"` // 总消费金额\n\tCardBalance          *float64 `json:\"cardBalance\"`          // 卡余额\n}\n", "req_delete": "package req\n\ntype DeleteCustomerGroupReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCustomerGroupReqDto struct {\n\tId                    *string  `json:\"id\"`                    // ID\n\tName                  *string  `json:\"name\"`                  // 客户组名称\n\tCardLevels           *string  `json:\"cardLevels\"`           // 卡等级列表\n\tGender               *string  `json:\"gender\"`               // 性别\n\tAgeRange             *int     `json:\"ageRange\"`             // 年龄范围\n\tBirthdayRange        *string  `json:\"birthdayRange\"`        // 生日范围\n\tPointsRange          *int     `json:\"pointsRange\"`          // 积分范围\n\tConsumptionBehavior  *string  `json:\"consumptionBehavior\"`  // 消费行为\n\tTotalConsumptionTimes *int     `json:\"totalConsumptionTimes\"` // 总消费次数\n\tTotalConsumptionAmount *float64 `json:\"totalConsumptionAmount\"` // 总消费金额\n\tCardBalance          *float64 `json:\"cardBalance\"`          // 卡余额\n\tPageNum              *int     `json:\"pageNum\"`              // 页码\n\tPageSize             *int     `json:\"pageSize\"`             // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CustomerGroupTransfer struct {\n}\n\nfunc (transfer *CustomerGroupTransfer) PoToVo(po po.CustomerGroup) vo.CustomerGroupVO {\n\tvo := vo.CustomerGroupVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CustomerGroupTransfer) VoToPo(vo vo.CustomerGroupVO) po.CustomerGroup {\n\tpo := po.CustomerGroup{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerGroupService struct {\n}\n\nfunc (service *CustomerGroupService) CreateCustomerGroup(logCtx *gin.Context, customerGroup *po.CustomerGroup) error {\n\treturn Save(customerGroup)\n}\n\nfunc (service *CustomerGroupService) UpdateCustomerGroup(logCtx *gin.Context, customerGroup *po.CustomerGroup) error {\n\treturn Update(customerGroup)\n}\n\nfunc (service *CustomerGroupService) DeleteCustomerGroup(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CustomerGroup{Id: &id})\n}\n\nfunc (service *CustomerGroupService) FindCustomerGroupById(logCtx *gin.Context, id string) (customerGroup *po.CustomerGroup, err error) {\n\tcustomerGroup = &po.CustomerGroup{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(customerGroup).Error\n\treturn\n}\n\nfunc (service *CustomerGroupService) FindAllCustomerGroup(logCtx *gin.Context, reqDto *req.QueryCustomerGroupReqDto) (list *[]po.CustomerGroup, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CustomerGroup{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CustomerGroup{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CustomerGroupService) FindAllCustomerGroupWithPagination(logCtx *gin.Context, reqDto *req.QueryCustomerGroupReqDto) (list *[]po.CustomerGroup, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CustomerGroup{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CustomerGroup{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerGroupController struct{}\n\nvar (\n\tcustomerGroupService  = impl.CustomerGroupService{}\n\tcustomerGroupTransfer = transfer.CustomerGroupTransfer{}\n)\n\n// @Summary 添加客户组\n// @Description 添加客户组\n// @Tags 客户组\n// @Accept json\n// @Produce json\n// @Param body body req.AddCustomerGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CustomerGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-group/add [post]\nfunc (controller *CustomerGroupController) AddCustomerGroup(ctx *gin.Context) {\n\treqDto := req.AddCustomerGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcustomerGroup := po.CustomerGroup{}\n\tif reqDto.Name != nil {\n\t\tcustomerGroup.Name = reqDto.Name\n\t}\n\t// ... 其他字段赋值 ...\n\n\terr = customerGroupService.CreateCustomerGroup(ctx, &customerGroup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, customerGroupTransfer.PoToVo(customerGroup))\n}\n\n// @Summary 更新客户组\n// @Description 更新客户组\n// @Tags 客户组\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCustomerGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CustomerGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-group/update [post]\nfunc (controller *CustomerGroupController) UpdateCustomerGroup(ctx *gin.Context) {\n\treqDto := req.UpdateCustomerGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcustomerGroup, err := customerGroupService.FindCustomerGroupById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\t// ... 更新字段 ...\n\n\terr = customerGroupService.UpdateCustomerGroup(ctx, customerGroup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, customerGroupTransfer.PoToVo(*customerGroup))\n}\n\n// @Summary 删除客户组\n// @Description 删除客户组\n// @Tags 客户组\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCustomerGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-group/delete [post]\nfunc (controller *CustomerGroupController) DeleteCustomerGroup(ctx *gin.Context) {\n\treqDto := req.DeleteCustomerGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = customerGroupService.DeleteCustomerGroup(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询客户组\n// @Description 查询客户组\n// @Tags 客户组\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCustomerGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CustomerGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-group/query [post]\nfunc (controller *CustomerGroupController) QueryCustomerGroups(ctx *gin.Context) {\n\treqDto := req.QueryCustomerGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := customerGroupService.FindAllCustomerGroup(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CustomerGroupVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, customerGroupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询客户组列表\n// @Description 查询客户组列表\n// @Tags 客户组\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCustomerGroupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CustomerGroupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-group/list [post]\nfunc (a *CustomerGroupController) ListCustomerGroups(ctx *gin.Context) {\n\treqDto := req.QueryCustomerGroupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := customerGroupService.FindAllCustomerGroupWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CustomerGroupVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CustomerGroupVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, customerGroupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerGroupRoute struct {\n}\n\nfunc (s *CustomerGroupRoute) InitCustomerGroupRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcustomerGroupController := controller.CustomerGroupController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/customer-group/add\", customerGroupController.AddCustomerGroup)    //add\n\t\troute.POST(\"/api/customer-group/update\", customerGroupController.UpdateCustomerGroup) //update\n\t\troute.POST(\"/api/customer-group/delete\", customerGroupController.DeleteCustomerGroup) //delete\n\t\troute.POST(\"/api/customer-group/query\", customerGroupController.QueryCustomerGroups)     //query\n\t\troute.POST(\"/api/customer-group/list\", customerGroupController.ListCustomerGroups)     //list\n\t}\n}\n"}]