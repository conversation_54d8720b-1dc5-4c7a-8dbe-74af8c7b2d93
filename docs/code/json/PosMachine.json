[{"po": "package po\n\n// PosMachine POS机实体\ntype PosMachine struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tIpAddress *string `gorm:\"column:ip_address;type:varchar(64);default:''\" json:\"ipAddress\"` // IP地址\n\tStatus    *string `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`       // 状态\n\tType      *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`           // 类型\n\tStoreId   *string `gorm:\"column:store_id;type:varchar(64);default:''\" json:\"storeId\"`     // 所属店铺ID\n\tCtime     *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间戳\n\tUtime     *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间戳\n\tState     *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion   *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (PosMachine) TableName() string {\n\treturn \"pos_machine\"\n}\n\nfunc (p PosMachine) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PosMachineVO POS机信息值对象\ntype PosMachineVO struct {\n\tId        string `json:\"id\"`        // ID\n\tIpAddress string `json:\"ipAddress\"` // IP地址\n\tStatus    string `json:\"status\"`    // 状态\n\tType      string `json:\"type\"`      // 类型\n\tStoreId   string `json:\"storeId\"`   // 所属店铺ID\n\tCtime     int64  `json:\"ctime\"`     // 创建时间戳\n\tUtime     int64  `json:\"utime\"`     // 更新时间戳\n\tState     int    `json:\"state\"`     // 状态值\n\tVersion   int    `json:\"version\"`   // 版本号\n}\n", "req_add": "package req\n\n// AddPosMachineReqDto 创建POS机请求DTO\ntype AddPosMachineReqDto struct {\n\tIpAddress *string `json:\"ipAddress\"` // IP地址\n\tStatus    *string `json:\"status\"`    // 状态\n\tType      *string `json:\"type\"`      // 类型\n\tStoreId   *string `json:\"storeId\"`   // 所属店铺ID\n}\n", "req_update": "package req\n\n// UpdatePosMachineReqDto 更新POS机请求DTO\ntype UpdatePosMachineReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tIpAddress *string `json:\"ipAddress\"` // IP地址\n\tStatus    *string `json:\"status\"`    // 状态\n\tType      *string `json:\"type\"`      // 类型\n\tStoreId   *string `json:\"storeId\"`   // 所属店铺ID\n}\n", "req_delete": "package req\n\n// DeletePosMachineReqDto 删除POS机请求DTO\ntype DeletePosMachineReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryPosMachineReqDto 查询POS机请求DTO\ntype QueryPosMachineReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tIpAddress *string `json:\"ipAddress\"` // IP地址\n\tStatus    *string `json:\"status\"`    // 状态\n\tType      *string `json:\"type\"`      // 类型\n\tStoreId   *string `json:\"storeId\"`   // 所属店铺ID\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PosMachineTransfer struct {\n}\n\nfunc (transfer *PosMachineTransfer) PoToVo(po po.PosMachine) vo.PosMachineVO {\n\tvo := vo.PosMachineVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PosMachineTransfer) VoToPo(vo vo.PosMachineVO) po.PosMachine {\n\tpo := po.PosMachine{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PosMachineService struct {\n}\n\nfunc (service *PosMachineService) CreatePosMachine(logCtx *gin.Context, posMachine *po.PosMachine) error {\n\treturn Save(posMachine)\n}\n\nfunc (service *PosMachineService) UpdatePosMachine(logCtx *gin.Context, posMachine *po.PosMachine) error {\n\treturn Update(posMachine)\n}\n\nfunc (service *PosMachineService) DeletePosMachine(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PosMachine{Id: &id})\n}\n\nfunc (service *PosMachineService) FindPosMachineById(logCtx *gin.Context, id string) (posMachine *po.PosMachine, err error) {\n\tposMachine = &po.PosMachine{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(posMachine).Error\n\treturn\n}\n\nfunc (service *PosMachineService) FindAllPosMachine(logCtx *gin.Context, reqDto *req.QueryPosMachineReqDto) (list *[]po.PosMachine, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PosMachine{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.StoreId != nil && *reqDto.StoreId != \"\" {\n\t\tdb = db.Where(\"store_id=?\", *reqDto.StoreId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PosMachine{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PosMachineService) FindAllPosMachineWithPagination(logCtx *gin.Context, reqDto *req.QueryPosMachineReqDto) (list *[]po.PosMachine, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PosMachine{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.IpAddress != nil && *reqDto.IpAddress != \"\" {\n\t\tdb = db.Where(\"ip_address=?\", *reqDto.IpAddress)\n\t}\n\tif reqDto.Status != nil && *reqDto.Status != \"\" {\n\t\tdb = db.Where(\"status=?\", *reqDto.Status)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.StoreId != nil && *reqDto.StoreId != \"\" {\n\t\tdb = db.Where(\"store_id=?\", *reqDto.StoreId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PosMachine{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PosMachineController struct{}\n\nvar (\n\tposMachineService  = impl.PosMachineService{}\n\tposMachineTransfer = transfer.PosMachineTransfer{}\n)\n\n// @Summary 添加POS机\n// @Description 添加POS机\n// @Tags POS机\n// @Accept json\n// @Produce json\n// @Param body body req.AddPosMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PosMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/pos-machine/add [post]\nfunc (controller *PosMachineController) AddPosMachine(ctx *gin.Context) {\n\treqDto := req.AddPosMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tposMachine := po.PosMachine{}\n\tif reqDto.IpAddress != nil {\n\t\tposMachine.IpAddress = reqDto.IpAddress\n\t}\n\tif reqDto.Status != nil {\n\t\tposMachine.Status = reqDto.Status\n\t}\n\tif reqDto.Type != nil {\n\t\tposMachine.Type = reqDto.Type\n\t}\n\tif reqDto.StoreId != nil {\n\t\tposMachine.StoreId = reqDto.StoreId\n\t}\n\n\terr = posMachineService.CreatePosMachine(ctx, &posMachine)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, posMachineTransfer.PoToVo(posMachine))\n}\n\n// @Summary 更新POS机\n// @Description 更新POS机\n// @Tags POS机\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePosMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PosMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/pos-machine/update [post]\nfunc (controller *PosMachineController) UpdatePosMachine(ctx *gin.Context) {\n\treqDto := req.UpdatePosMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tposMachine, err := posMachineService.FindPosMachineById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.IpAddress != nil {\n\t\tposMachine.IpAddress = reqDto.IpAddress\n\t}\n\tif reqDto.Status != nil {\n\t\tposMachine.Status = reqDto.Status\n\t}\n\tif reqDto.Type != nil {\n\t\tposMachine.Type = reqDto.Type\n\t}\n\tif reqDto.StoreId != nil {\n\t\tposMachine.StoreId = reqDto.StoreId\n\t}\n\n\terr = posMachineService.UpdatePosMachine(ctx, posMachine)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, posMachineTransfer.PoToVo(*posMachine))\n}\n\n// @Summary 删除POS机\n// @Description 删除POS机\n// @Tags POS机\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePosMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/pos-machine/delete [post]\nfunc (controller *PosMachineController) DeletePosMachine(ctx *gin.Context) {\n\treqDto := req.DeletePosMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = posMachineService.DeletePosMachine(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询POS机\n// @Description 查询POS机\n// @Tags POS机\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPosMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PosMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/pos-machine/query [post]\nfunc (controller *PosMachineController) QueryPosMachines(ctx *gin.Context) {\n\treqDto := req.QueryPosMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := posMachineService.FindAllPosMachine(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.PosMachineVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, posMachineTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询POS机列表\n// @Description 查询POS机列表\n// @Tags POS机\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPosMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PosMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/pos-machine/list [post]\nfunc (controller *PosMachineController) ListPosMachines(ctx *gin.Context) {\n\treqDto := req.QueryPosMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := posMachineService.FindAllPosMachineWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.PosMachineVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PosMachineVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, posMachineTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PosMachineRoute struct {\n}\n\nfunc (s *PosMachineRoute) InitPosMachineRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tposMachineController := controller.PosMachineController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/pos-machine/add\", posMachineController.AddPosMachine)       // add\n\t\troute.POST(\"/api/pos-machine/update\", posMachineController.UpdatePosMachine) // update\n\t\troute.POST(\"/api/pos-machine/delete\", posMachineController.DeletePosMachine) // delete\n\t\troute.POST(\"/api/pos-machine/query\", posMachineController.QueryPosMachines)  // query\n\t\troute.POST(\"/api/pos-machine/list\", posMachineController.ListPosMachines)   // list\n\t}\n}\n"}]