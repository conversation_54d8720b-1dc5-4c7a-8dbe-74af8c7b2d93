[{"po": "package po\n\n// Employee 员工实体\ntype Employee struct {\n\tId                         *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tName                       *string  `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                    // 员工姓名\n\tEmployeeNumber            *string  `gorm:\"column:employee_number;type:varchar(64);default:''\" json:\"employeeNumber\"` // 员工编号\n\tPhone                     *string  `gorm:\"column:phone;type:varchar(64);default:''\" json:\"phone\"`                  // 电话号码\n\tPassword                  *string  `gorm:\"column:password;type:varchar(64);default:''\" json:\"password\"`             // 密码(已加密)\n\tType                      *string  `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`                    // 员工类型\n\tModulePermissions         *string  `gorm:\"column:module_permissions;type:text\" json:\"modulePermissions\"`           // 模块权限\n\tBusinessPermissions       *string  `gorm:\"column:business_permissions;type:text\" json:\"businessPermissions\"`       // 业务权限\n\tGiftPermissions           *string  `gorm:\"column:gift_permissions;type:text\" json:\"giftPermissions\"`             // 礼品权限\n\tEmployeeGroup             *string  `gorm:\"column:employee_group;type:varchar(64)\" json:\"employeeGroup\"`           // 员工组\n\tPermissionRole            *string  `gorm:\"column:permission_role;type:varchar(64)\" json:\"permissionRole\"`         // 权限角色\n\tMarketingRole             *string  `gorm:\"column:marketing_role;type:varchar(64)\" json:\"marketingRole\"`          // 营销角色\n\tEmployeeCardId            *string  `gorm:\"column:employee_card_id;type:varchar(64)\" json:\"employeeCardId\"`        // 员工卡ID\n\tWechatBinding             *string  `gorm:\"column:wechat_binding;type:varchar(64)\" json:\"wechatBinding\"`          // 微信绑定\n\tCanManageGroups          *bool    `gorm:\"column:can_manage_groups;type:tinyint\" json:\"canManageGroups\"`         // 是否可以管理组\n\tSalesPerformance         *float32 `gorm:\"column:sales_performance;type:float\" json:\"salesPerformance\"`          // 销售业绩\n\tCanViewCommissionPerformance *bool    `gorm:\"column:can_view_commission_performance;type:tinyint\" json:\"canViewCommissionPerformance\"` // 是否可以查看佣金业绩\n\tCtime                     *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间戳\n\tUtime                     *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间戳\n\tState                     *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态值\n\tVersion                   *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (Employee) TableName() string {\n\treturn \"employee\"\n}\n\nfunc (e Employee) GetId() string {\n\treturn *e.Id\n}\n", "vo": "package vo\n\n// EmployeeVO 员工信息值对象\ntype EmployeeVO struct {\n\tId                         string  `json:\"id\"`                           // ID\n\tName                       string  `json:\"name\"`                         // 员工姓名\n\tEmployeeNumber            string  `json:\"employeeNumber\"`               // 员工编号\n\tPhone                     string  `json:\"phone\"`                        // 电话号码\n\tType                      string  `json:\"type\"`                         // 员工类型\n\tModulePermissions         string  `json:\"modulePermissions\"`            // 模块权限\n\tBusinessPermissions       string  `json:\"businessPermissions\"`          // 业务权限\n\tGiftPermissions           string  `json:\"giftPermissions\"`              // 礼品权限\n\tEmployeeGroup             string  `json:\"employeeGroup\"`                // 员工组\n\tPermissionRole            string  `json:\"permissionRole\"`               // 权限角色\n\tMarketingRole             string  `json:\"marketingRole\"`                // 营销角色\n\tEmployeeCardId            string  `json:\"employeeCardId\"`               // 员工卡ID\n\tWechatBinding             string  `json:\"wechatBinding\"`                // 微信绑定\n\tCanManageGroups          bool    `json:\"canManageGroups\"`             // 是否可以管理组\n\tSalesPerformance         float32 `json:\"salesPerformance\"`            // 销售业绩\n\tCanViewCommissionPerformance bool    `json:\"canViewCommissionPerformance\"` // 是否可以查看佣金业绩\n\tCtime                     int64   `json:\"ctime\"`                        // 创建时间戳\n\tUtime                     int64   `json:\"utime\"`                        // 更新时间戳\n\tState                     int     `json:\"state\"`                        // 状态值\n\tVersion                   int     `json:\"version\"`                      // 版本号\n}\n", "req_add": "package req\n\n// AddEmployeeReqDto 创建员工请求DTO\ntype AddEmployeeReqDto struct {\n\tName                       *string  `json:\"name\"`                         // 员工姓名\n\tEmployeeNumber            *string  `json:\"employeeNumber\"`               // 员工编号\n\tPhone                     *string  `json:\"phone\"`                        // 电话号码\n\tPassword                  *string  `json:\"password\"`                     // 密码\n\tType                      *string  `json:\"type\"`                         // 员工类型\n\tModulePermissions         *string  `json:\"modulePermissions\"`            // 模块权限\n\tBusinessPermissions       *string  `json:\"businessPermissions\"`          // 业务权限\n\tGiftPermissions           *string  `json:\"giftPermissions\"`              // 礼品权限\n\tEmployeeGroup             *string  `json:\"employeeGroup\"`                // 员工组\n\tPermissionRole            *string  `json:\"permissionRole\"`               // 权限角色\n\tMarketingRole             *string  `json:\"marketingRole\"`                // 营销角色\n\tEmployeeCardId            *string  `json:\"employeeCardId\"`               // 员工卡ID\n\tWechatBinding             *string  `json:\"wechatBinding\"`                // 微信绑定\n\tCanManageGroups          *bool    `json:\"canManageGroups\"`             // 是否可以管理组\n\tSalesPerformance         *float32 `json:\"salesPerformance\"`            // 销售业绩\n\tCanViewCommissionPerformance *bool    `json:\"canViewCommissionPerformance\"` // 是否可以查看佣金业绩\n", "req_update": "package req\n\ntype UpdateEmployeeReqDto struct {\n\tId                         *string  `json:\"id\"`                           // ID\n\tName                       *string  `json:\"name\"`                         // 员工姓名\n\tEmployeeNumber            *string  `json:\"employeeNumber\"`               // 员工编号\n\tPhone                     *string  `json:\"phone\"`                        // 电话号码\n\tPassword                  *string  `json:\"password\"`                     // 密码\n\tType                      *string  `json:\"type\"`                         // 员工类型\n\tModulePermissions         *string  `json:\"modulePermissions\"`            // 模块权限\n\tBusinessPermissions       *string  `json:\"businessPermissions\"`          // 业务权限\n\tGiftPermissions           *string  `json:\"giftPermissions\"`              // 礼品权限\n\tEmployeeGroup             *string  `json:\"employeeGroup\"`                // 员工组\n\tPermissionRole            *string  `json:\"permissionRole\"`               // 权限角色\n\tMarketingRole             *string  `json:\"marketingRole\"`                // 营销角色\n\tEmployeeCardId            *string  `json:\"employeeCardId\"`               // 员工卡ID\n\tWechatBinding             *string  `json:\"wechatBinding\"`                // 微信绑定\n\tCanManageGroups          *bool    `json:\"canManageGroups\"`             // 是否可以管理组\n\tSalesPerformance         *float32 `json:\"salesPerformance\"`            // 销售业绩\n\tCanViewCommissionPerformance *bool    `json:\"canViewCommissionPerformance\"` // 是否可以查看佣金业绩\n", "req_delete": "package req\n\ntype DeleteEmployeeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryEmployeeReqDto struct {\n\tId                         *string  `json:\"id\"`                           // ID\n\tName                       *string  `json:\"name\"`                         // 员工姓名\n\tEmployeeNumber            *string  `json:\"employeeNumber\"`               // 员工编号\n\tPhone                     *string  `json:\"phone\"`                        // 电话号码\n\tType                      *string  `json:\"type\"`                         // 员工类型\n\tEmployeeGroup             *string  `json:\"employeeGroup\"`                // 员工组\n\tPermissionRole            *string  `json:\"permissionRole\"`               // 权限角色\n\tMarketingRole             *string  `json:\"marketingRole\"`                // 营销角色\n\tEmployeeCardId            *string  `json:\"employeeCardId\"`               // 员工卡ID\n\tWechatBinding             *string  `json:\"wechatBinding\"`                // 微信绑定\n\tPageNum                   *int     `json:\"pageNum\"`                      // 页码\n\tPageSize                  *int     `json:\"pageSize\"`                     // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype EmployeeTransfer struct {\n}\n\nfunc (transfer *EmployeeTransfer) PoToVo(po po.Employee) vo.EmployeeVO {\n\tvo := vo.EmployeeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *EmployeeTransfer) VoToPo(vo vo.EmployeeVO) po.Employee {\n\tpo := po.Employee{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeService struct {\n}\n\nfunc (service *EmployeeService) CreateEmployee(logCtx *gin.Context, employee *po.Employee) error {\n\treturn Save(employee)\n}\n\nfunc (service *EmployeeService) UpdateEmployee(logCtx *gin.Context, employee *po.Employee) error {\n\treturn Update(employee)\n}\n\nfunc (service *EmployeeService) DeleteEmployee(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Employee{Id: &id})\n}\n\nfunc (service *EmployeeService) FindEmployeeById(logCtx *gin.Context, id string) (employee *po.Employee, err error) {\n\temployee = &po.Employee{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(employee).Error\n\treturn\n}\n\nfunc (service *EmployeeService) FindAllEmployee(logCtx *gin.Context, reqDto *req.QueryEmployeeReqDto) (list *[]po.Employee, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Employee{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.EmployeeNumber != nil && *reqDto.EmployeeNumber != \"\" {\n\t\tdb = db.Where(\"employee_number=?\", *reqDto.EmployeeNumber)\n\t}\n\tif reqDto.Phone != nil && *reqDto.Phone != \"\" {\n\t\tdb = db.Where(\"phone=?\", *reqDto.Phone)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Employee{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *EmployeeService) FindAllEmployeeWithPagination(logCtx *gin.Context, reqDto *req.QueryEmployeeReqDto) (list *[]po.Employee, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Employee{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.EmployeeNumber != nil && *reqDto.EmployeeNumber != \"\" {\n\t\tdb = db.Where(\"employee_number=?\", *reqDto.EmployeeNumber)\n\t}\n\tif reqDto.Phone != nil && *reqDto.Phone != \"\" {\n\t\tdb = db.Where(\"phone=?\", *reqDto.Phone)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Employee{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeController struct{}\n\nvar (\n\temployeeService  = impl.EmployeeService{}\n\temployeeTransfer = transfer.EmployeeTransfer{}\n)\n\n// @Summary 添加员工\n// @Description 添加员工\n// @Tags 员工\n// @Accept json\n// @Produce json\n// @Param body body req.AddEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.EmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee/add [post]\nfunc (controller *EmployeeController) AddEmployee(ctx *gin.Context) {\n\treqDto := req.AddEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\temployee := po.Employee{}\n\tif reqDto.Name != nil {\n\t\temployee.Name = reqDto.Name\n\t}\n\tif reqDto.EmployeeNumber != nil {\n\t\temployee.EmployeeNumber = reqDto.EmployeeNumber\n\t}\n\tif reqDto.Phone != nil {\n\t\temployee.Phone = reqDto.Phone\n\t}\n\tif reqDto.Password != nil {\n\t\temployee.Password = reqDto.Password\n\t}\n\tif reqDto.Type != nil {\n\t\temployee.Type = reqDto.Type\n\t}\n\tif reqDto.ModulePermissions != nil {\n\t\temployee.ModulePermissions = reqDto.ModulePermissions\n\t}\n\tif reqDto.BusinessPermissions != nil {\n\t\temployee.BusinessPermissions = reqDto.BusinessPermissions\n\t}\n\tif reqDto.GiftPermissions != nil {\n\t\temployee.GiftPermissions = reqDto.GiftPermissions\n\t}\n\tif reqDto.EmployeeGroup != nil {\n\t\temployee.EmployeeGroup = reqDto.EmployeeGroup\n\t}\n\tif reqDto.PermissionRole != nil {\n\t\temployee.PermissionRole = reqDto.PermissionRole\n\t}\n\tif reqDto.MarketingRole != nil {\n\t\temployee.MarketingRole = reqDto.MarketingRole\n\t}\n\tif reqDto.EmployeeCardId != nil {\n\t\temployee.EmployeeCardId = reqDto.EmployeeCardId\n\t}\n\tif reqDto.WechatBinding != nil {\n\t\temployee.WechatBinding = reqDto.WechatBinding\n\t}\n\tif reqDto.CanManageGroups != nil {\n\t\temployee.CanManageGroups = reqDto.CanManageGroups\n\t}\n\tif reqDto.SalesPerformance != nil {\n\t\temployee.SalesPerformance = reqDto.SalesPerformance\n\t}\n\tif reqDto.CanViewCommissionPerformance != nil {\n\t\temployee.CanViewCommissionPerformance = reqDto.CanViewCommissionPerformance\n\t}\n\terr = employeeService.CreateEmployee(ctx, &employee)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, employeeTransfer.PoToVo(employee))\n}\n\n// @Summary 更新员工\n// @Description 更新员工\n// @Tags 员工\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.EmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee/update [post]\nfunc (controller *EmployeeController) UpdateEmployee(ctx *gin.Context) {\n\treqDto := req.UpdateEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\temployee, err := employeeService.FindEmployeeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\temployee.Name = reqDto.Name\n\t}\n\tif reqDto.EmployeeNumber != nil {\n\t\temployee.EmployeeNumber = reqDto.EmployeeNumber\n\t}\n\tif reqDto.Phone != nil {\n\t\temployee.Phone = reqDto.Phone\n\t}\n\tif reqDto.Password != nil {\n\t\temployee.Password = reqDto.Password\n\t}\n\tif reqDto.Type != nil {\n\t\temployee.Type = reqDto.Type\n\t}\n\tif reqDto.ModulePermissions != nil {\n\t\temployee.ModulePermissions = reqDto.ModulePermissions\n\t}\n\tif reqDto.BusinessPermissions != nil {\n\t\temployee.BusinessPermissions = reqDto.BusinessPermissions\n\t}\n\tif reqDto.GiftPermissions != nil {\n\t\temployee.GiftPermissions = reqDto.GiftPermissions\n\t}\n\tif reqDto.EmployeeGroup != nil {\n\t\temployee.EmployeeGroup = reqDto.EmployeeGroup\n\t}\n\tif reqDto.PermissionRole != nil {\n\t\temployee.PermissionRole = reqDto.PermissionRole\n\t}\n\tif reqDto.MarketingRole != nil {\n\t\temployee.MarketingRole = reqDto.MarketingRole\n\t}\n\tif reqDto.EmployeeCardId != nil {\n\t\temployee.EmployeeCardId = reqDto.EmployeeCardId\n\t}\n\tif reqDto.WechatBinding != nil {\n\t\temployee.WechatBinding = reqDto.WechatBinding\n\t}\n\tif reqDto.CanManageGroups != nil {\n\t\temployee.CanManageGroups = reqDto.CanManageGroups\n\t}\n\tif reqDto.SalesPerformance != nil {\n\t\temployee.SalesPerformance = reqDto.SalesPerformance\n\t}\n\tif reqDto.CanViewCommissionPerformance != nil {\n\t\temployee.CanViewCommissionPerformance = reqDto.CanViewCommissionPerformance\n\t}\n\terr = employeeService.UpdateEmployee(ctx, employee)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, employeeTransfer.PoToVo(*employee))\n}\n\n// @Summary 删除员工\n// @Description 删除员工\n// @Tags 员工\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee/delete [post]\nfunc (controller *EmployeeController) DeleteEmployee(ctx *gin.Context) {\n\treqDto := req.DeleteEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = employeeService.DeleteEmployee(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询员工\n// @Description 查询员工\n// @Tags 员工\n// @Accept json\n// @Produce json\n// @Param body body req.QueryEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.EmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee/query [post]\nfunc (controller *EmployeeController) QueryEmployees(ctx *gin.Context) {\n\treqDto := req.QueryEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := employeeService.FindAllEmployee(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.EmployeeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, employeeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询员工列表\n// @Description 查询员工列表\n// @Tags 员工\n// @Accept json\n// @Produce json\n// @Param body body req.QueryEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.EmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee/list [post]\nfunc (controller *EmployeeController) ListEmployees(ctx *gin.Context) {\n\treqDto := req.QueryEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := employeeService.FindAllEmployeeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\tret", "router": "package route"}]