[{"po": "package po\n\n// BirthdayGreeting 生日祝福实体\ntype BirthdayGreeting struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tCustomerGroup *string `gorm:\"column:customer_group;type:varchar(64);default:''\" json:\"customerGroup\"` // 客户群组\n\tSmsTemplate   *string `gorm:\"column:sms_template;type:varchar(255);default:''\" json:\"smsTemplate\"`   // 短信模板\n\tDaysInAdvance *int    `gorm:\"column:days_in_advance;type:int;default:0\" json:\"daysInAdvance\"`       // 提前天数\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                         // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                         // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                         // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                     // 版本号\n}\n\n// TableName 设置表名\nfunc (BirthdayGreeting) TableName() string {\n\treturn \"birthday_greeting\"\n}\n\nfunc (b BirthdayGreeting) GetId() string {\n\treturn *b.Id\n}\n", "req_add": "package req\n\n// AddBirthdayGreetingReqDto 创建生日祝福请求DTO\ntype AddBirthdayGreetingReqDto struct {\n\tCustomerGroup *string `json:\"customerGroup\"` // 客户群组\n\tSmsTemplate   *string `json:\"smsTemplate\"`   // 短信模板\n\tDaysInAdvance *int    `json:\"daysInAdvance\"` // 提前天数\n}\n", "req_update": "package req\n\ntype UpdateBirthdayGreetingReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tCustomerGroup *string `json:\"customerGroup\"` // 客户群组\n\tSmsTemplate   *string `json:\"smsTemplate\"`   // 短信模板\n\tDaysInAdvance *int    `json:\"daysInAdvance\"` // 提前天数\n}\n", "req_delete": "package req\n\ntype DeleteBirthdayGreetingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryBirthdayGreetingReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tCustomerGroup *string `json:\"customerGroup\"` // 客户群组\n\tSmsTemplate   *string `json:\"smsTemplate\"`   // 短信模板\n\tDaysInAdvance *int    `json:\"daysInAdvance\"` // 提前天数\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "vo": "package vo\n\n// BirthdayGreetingVO 生日祝福值对象\ntype BirthdayGreetingVO struct {\n\tId            string `json:\"id\"`            // ID\n\tCustomerGroup string `json:\"customerGroup\"` // 客户群组\n\tSmsTemplate   string `json:\"smsTemplate\"`   // 短信模板\n\tDaysInAdvance int    `json:\"daysInAdvance\"` // 提前天数\n\tCtime         int64  `json:\"ctime\"`         // 创建时间戳\n\tUtime         int64  `json:\"utime\"`         // 更新时间戳\n\tState         int    `json:\"state\"`         // 状态值\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype BirthdayGreetingTransfer struct {\n}\n\nfunc (transfer *BirthdayGreetingTransfer) PoToVo(po po.BirthdayGreeting) vo.BirthdayGreetingVO {\n\tvo := vo.BirthdayGreetingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *BirthdayGreetingTransfer) VoToPo(vo vo.BirthdayGreetingVO) po.BirthdayGreeting {\n\tpo := po.BirthdayGreeting{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype BirthdayGreetingService struct {\n}\n\nfunc (service *BirthdayGreetingService) CreateBirthdayGreeting(logCtx *gin.Context, birthdayGreeting *po.BirthdayGreeting) error {\n\treturn Save(birthdayGreeting)\n}\n\nfunc (service *BirthdayGreetingService) UpdateBirthdayGreeting(logCtx *gin.Context, birthdayGreeting *po.BirthdayGreeting) error {\n\treturn Update(birthdayGreeting)\n}\n\nfunc (service *BirthdayGreetingService) DeleteBirthdayGreeting(logCtx *gin.Context, id string) error {\n\treturn Delete(po.BirthdayGreeting{Id: &id})\n}\n\nfunc (service *BirthdayGreetingService) FindBirthdayGreetingById(logCtx *gin.Context, id string) (birthdayGreeting *po.BirthdayGreeting, err error) {\n\tbirthdayGreeting = &po.BirthdayGreeting{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(birthdayGreeting).Error\n\treturn\n}\n\nfunc (service *BirthdayGreetingService) FindAllBirthdayGreeting(logCtx *gin.Context, reqDto *req.QueryBirthdayGreetingReqDto) (list *[]po.BirthdayGreeting, err error) {\n\tdb := model.DBSlave.Self.Model(&po.BirthdayGreeting{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.BirthdayGreeting{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *BirthdayGreetingService) FindAllBirthdayGreetingWithPagination(logCtx *gin.Context, reqDto *req.QueryBirthdayGreetingReqDto) (list *[]po.BirthdayGreeting, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.BirthdayGreeting{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.BirthdayGreeting{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype BirthdayGreetingController struct{}\n\nvar (\n\tbirthdayGreetingService  = impl.BirthdayGreetingService{}\n\tbirthdayGreetingTransfer = transfer.BirthdayGreetingTransfer{}\n)\n\n// @Summary 添加生日祝福\n// @Description 添加生日祝福\n// @Tags 生日祝福\n// @Accept json\n// @Produce json\n// @Param body body req.AddBirthdayGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.BirthdayGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/birthdayGreeting/add [post]\nfunc (controller *BirthdayGreetingController) AddBirthdayGreeting(ctx *gin.Context) {\n\treqDto := req.AddBirthdayGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tbirthdayGreeting := po.BirthdayGreeting{}\n\tif reqDto.CustomerGroup != nil {\n\t\tbirthdayGreeting.CustomerGroup = reqDto.CustomerGroup\n\t}\n\tif reqDto.SmsTemplate != nil {\n\t\tbirthdayGreeting.SmsTemplate = reqDto.SmsTemplate\n\t}\n\tif reqDto.DaysInAdvance != nil {\n\t\tbirthdayGreeting.DaysInAdvance = reqDto.DaysInAdvance\n\t}\n\terr = birthdayGreetingService.CreateBirthdayGreeting(ctx, &birthdayGreeting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, birthdayGreetingTransfer.PoToVo(birthdayGreeting))\n}\n\n// @Summary 更新生日祝福\n// @Description 更新生日祝福\n// @Tags 生日祝福\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateBirthdayGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.BirthdayGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/birthdayGreeting/update [post]\nfunc (controller *BirthdayGreetingController) UpdateBirthdayGreeting(ctx *gin.Context) {\n\treqDto := req.UpdateBirthdayGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tbirthdayGreeting, err := birthdayGreetingService.FindBirthdayGreetingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CustomerGroup != nil {\n\t\tbirthdayGreeting.CustomerGroup = reqDto.CustomerGroup\n\t}\n\tif reqDto.SmsTemplate != nil {\n\t\tbirthdayGreeting.SmsTemplate = reqDto.SmsTemplate\n\t}\n\tif reqDto.DaysInAdvance != nil {\n\t\tbirthdayGreeting.DaysInAdvance = reqDto.DaysInAdvance\n\t}\n\terr = birthdayGreetingService.UpdateBirthdayGreeting(ctx, birthdayGreeting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, birthdayGreetingTransfer.PoToVo(*birthdayGreeting))\n}\n\n// @Summary 删除生日祝福\n// @Description 删除生日祝福\n// @Tags 生日祝福\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteBirthdayGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/birthdayGreeting/delete [post]\nfunc (controller *BirthdayGreetingController) DeleteBirthdayGreeting(ctx *gin.Context) {\n\treqDto := req.DeleteBirthdayGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = birthdayGreetingService.DeleteBirthdayGreeting(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询生日祝福\n// @Description 查询生日祝福\n// @Tags 生日祝福\n// @Accept json\n// @Produce json\n// @Param body body req.QueryBirthdayGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.BirthdayGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/birthdayGreeting/query [post]\nfunc (controller *BirthdayGreetingController) QueryBirthdayGreetings(ctx *gin.Context) {\n\treqDto := req.QueryBirthdayGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := birthdayGreetingService.FindAllBirthdayGreeting(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.BirthdayGreetingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, birthdayGreetingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询生日祝福列表\n// @Description 查询生日祝福列表\n// @Tags 生日祝福\n// @Accept json\n// @Produce json\n// @Param body body req.QueryBirthdayGreetingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.BirthdayGreetingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/birthdayGreeting/list [post]\nfunc (a *BirthdayGreetingController) ListBirthdayGreetings(ctx *gin.Context) {\n\treqDto := req.QueryBirthdayGreetingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := birthdayGreetingService.FindAllBirthdayGreetingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.BirthdayGreetingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.BirthdayGreetingVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, birthdayGreetingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype BirthdayGreetingRoute struct {\n}\n\nfunc (s *BirthdayGreetingRoute) InitBirthdayGreetingRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tbirthdayGreetingController := controller.BirthdayGreetingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/birthdayGreeting/add\", birthdayGreetingController.AddBirthdayGreeting)    //add\n\t\troute.POST(\"/api/birthdayGreeting/update\", birthdayGreetingController.UpdateBirthdayGreeting) //update\n\t\troute.POST(\"/api/birthdayGreeting/delete\", birthdayGreetingController.DeleteBirthdayGreeting) //delete\n\t\troute.POST(\"/api/birthdayGreeting/query\", birthdayGreetingController.QueryBirthdayGreetings)     //query\n\t\troute.POST(\"/api/birthdayGreeting/list\", birthdayGreetingController.ListBirthdayGreetings)     //list\n\t}\n}\n"}]