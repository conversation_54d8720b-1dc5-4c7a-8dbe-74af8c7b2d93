[{"po": "package po\n\n// Order 订单实体\ntype Order struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tSessionId      *string `gorm:\"column:session_id;type:varchar(64);default:''\" json:\"sessionId\"`      // 场次ID\n\tVenueId        *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`        // 门店ID\n\tRoomId         *string `gorm:\"column:room_id;type:varchar(64);default:''\" json:\"roomId\"`         // 房间ID\n\tOrderNo        *string `gorm:\"column:order_no;type:varchar(64);default:''\" json:\"orderNo\"`        // 订单编号\n\tEmployeeId     *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"`     // 员工ID\n\tMemberId       *string `gorm:\"column:member_id;type:varchar(64);default:''\" json:\"memberId\"`       // 会员ID\n\tStartTime      *int64  `gorm:\"column:start_time;type:int;default:0\" json:\"startTime\"`           // 订单开始时间\n\tEndTime        *int64  `gorm:\"column:end_time;type:int;default:0\" json:\"endTime\"`             // 订单结束时间\n\tTotalAmount    *int64  `gorm:\"column:total_amount;type:int;default:0\" json:\"totalAmount\"`       // 订单总金额\n\tMinimumCharge  *int64  `gorm:\"column:minimum_charge;type:int;default:0\" json:\"minimumCharge\"`   // 最低消费金额\n\tTag            *string `gorm:\"column:tag;type:varchar(64);default:''\" json:\"tag\"`              // 标签\n\tStatus         *string `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`         // 订单状态\n\tCtime          *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                  // 创建时间\n\tUtime          *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                  // 更新时间\n\tState          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                  // 状态\n\tVersion        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`              // 版本号\n}\n\n// TableName 设置表名\nfunc (Order) TableName() string {\n\treturn \"order\"\n}\n\nfunc (o Order) GetId() string {\n\treturn *o.Id\n}\n", "vo": "package vo\n\n// OrderVO 订单信息值对象\ntype OrderVO struct {\n\tId             string `json:\"id\"`              // ID\n\tSessionId      string `json:\"sessionId\"`      // 场次ID\n\tVenueId        string `json:\"venueId\"`        // 门店ID\n\tRoomId         string `json:\"roomId\"`         // 房间ID\n\tOrderNo        string `json:\"orderNo\"`        // 订单编号\n\tEmployeeId     string `json:\"employeeId\"`     // 员工ID\n\tMemberId       string `json:\"memberId\"`       // 会员ID\n\tStartTime      int64  `json:\"startTime\"`      // 订单开始时间\n\tEndTime        int64  `json:\"endTime\"`        // 订单结束时间\n\tTotalAmount    int64  `json:\"totalAmount\"`    // 订单总金额\n\tMinimumCharge  int64  `json:\"minimumCharge\"`  // 最低消费金额\n\tTag            string `json:\"tag\"`            // 标签\n\tStatus         string `json:\"status\"`         // 订单状态\n\tCtime          int64  `json:\"ctime\"`          // 创建时间\n\tUtime          int64  `json:\"utime\"`          // 更新时间\n\tState          int    `json:\"state\"`          // 状态\n\tVersion        int    `json:\"version\"`        // 版本号\n}\n", "req_add": "package req\n\n// AddOrderReqDto 创建订单请求DTO\ntype AddOrderReqDto struct {\n\tSessionId      *string `json:\"sessionId\"`      // 场次ID\n\tVenueId        *string `json:\"venueId\"`        // 门店ID\n\tRoomId         *string `json:\"roomId\"`         // 房间ID\n\tOrderNo        *string `json:\"orderNo\"`        // 订单编号\n\tEmployeeId     *string `json:\"employeeId\"`     // 员工ID\n\tMemberId       *string `json:\"memberId\"`       // 会员ID\n\tStartTime      *int64  `json:\"startTime\"`      // 订单开始时间\n\tEndTime        *int64  `json:\"endTime\"`        // 订单结束时间\n\tTotalAmount    *int64  `json:\"totalAmount\"`    // 订单总金额\n\tMinimumCharge  *int64  `json:\"minimumCharge\"`  // 最低消费金额\n\tTag            *string `json:\"tag\"`            // 标签\n\tStatus         *string `json:\"status\"`         // 订单状态\n}\n", "req_update": "package req\n\n// UpdateOrderReqDto 更新订单请求DTO\ntype UpdateOrderReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tSessionId      *string `json:\"sessionId\"`      // 场次ID\n\tVenueId        *string `json:\"venueId\"`        // 门店ID\n\tRoomId         *string `json:\"roomId\"`         // 房间ID\n\tOrderNo        *string `json:\"orderNo\"`        // 订单编号\n\tEmployeeId     *string `json:\"employeeId\"`     // 员工ID\n\tMemberId       *string `json:\"memberId\"`       // 会员ID\n\tStartTime      *int64  `json:\"startTime\"`      // 订单开始时间\n\tEndTime        *int64  `json:\"endTime\"`        // 订单结束时间\n\tTotalAmount    *int64  `json:\"totalAmount\"`    // 订单总金额\n\tMinimumCharge  *int64  `json:\"minimumCharge\"`  // 最低消费金额\n\tTag            *string `json:\"tag\"`            // 标签\n\tStatus         *string `json:\"status\"`         // 订单状态\n}\n", "req_delete": "package req\n\ntype DeleteOrderReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryOrderReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tSessionId      *string `json:\"sessionId\"`      // 场次ID\n\tVenueId        *string `json:\"venueId\"`        // 门店ID\n\tRoomId         *string `json:\"roomId\"`         // 房间ID\n\tOrderNo        *string `json:\"orderNo\"`        // 订单编号\n\tEmployeeId     *string `json:\"employeeId\"`     // 员工ID\n\tMemberId       *string `json:\"memberId\"`       // 会员ID\n\tStartTime      *int64  `json:\"startTime\"`      // 订单开始时间\n\tEndTime        *int64  `json:\"endTime\"`        // 订单结束时间\n\tTotalAmount    *int64  `json:\"totalAmount\"`    // 订单总金额\n\tMinimumCharge  *int64  `json:\"minimumCharge\"`  // 最低消费金额\n\tTag            *string `json:\"tag\"`            // 标签\n\tStatus         *string `json:\"status\"`         // 订单状态\n\tPageNum        *int    `json:\"pageNum\"`        // 页码\n\tPageSize       *int    `json:\"pageSize\"`       // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype OrderTransfer struct {\n}\n\nfunc (transfer *OrderTransfer) PoToVo(po po.Order) vo.OrderVO {\n\tvo := vo.OrderVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *OrderTransfer) VoToPo(vo vo.OrderVO) po.Order {\n\tpo := po.Order{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderService struct {\n}\n\nfunc (service *OrderService) CreateOrder(logCtx *gin.Context, order *po.Order) error {\n\treturn Save(order)\n}\n\nfunc (service *OrderService) UpdateOrder(logCtx *gin.Context, order *po.Order) error {\n\treturn Update(order)\n}\n\nfunc (service *OrderService) DeleteOrder(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Order{Id: &id})\n}\n\nfunc (service *OrderService) FindOrderById(logCtx *gin.Context, id string) (order *po.Order, err error) {\n\torder = &po.Order{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(order).Error\n\treturn\n}\n\nfunc (service *OrderService) FindAllOrder(logCtx *gin.Context, reqDto *req.QueryOrderReqDto) (list *[]po.Order, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Order{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Order{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *OrderService) FindAllOrderWithPagination(logCtx *gin.Context, reqDto *req.QueryOrderReqDto) (list *[]po.Order, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Order{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Order{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderController struct{}\n\nvar (\n\torderService  = impl.OrderService{}\n\torderTransfer = transfer.OrderTransfer{}\n)\n\n// @Summary 添加订单\n// @Description 添加订单\n// @Tags 订单\n// @Accept json\n// @Produce json\n// @Param body body req.AddOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/order/add [post]\nfunc (controller *OrderController) AddOrder(ctx *gin.Context) {\n\treqDto := req.AddOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\torder := po.Order{}\n\t// 设置字段值\n\tif reqDto.SessionId != nil {\n\t\torder.SessionId = reqDto.SessionId\n\t}\n\t// ... 其他字段设置 ...\n\n\terr = orderService.CreateOrder(ctx, &order)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderTransfer.PoToVo(order))\n}\n\n// @Summary 更新订单\n// @Description 更新订单\n// @Tags 订单\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/order/update [post]\nfunc (controller *OrderController) UpdateOrder(ctx *gin.Context) {\n\treqDto := req.UpdateOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\torder, err := orderService.FindOrderById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\t// 更新字段值\n\tif reqDto.SessionId != nil {\n\t\torder.SessionId = reqDto.SessionId\n\t}\n\t// ... 其他字段更新 ...\n\n\terr = orderService.UpdateOrder(ctx, order)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderTransfer.PoToVo(*order))\n}\n\n// @Summary 删除订单\n// @Description 删除订单\n// @Tags 订单\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/order/delete [post]\nfunc (controller *OrderController) DeleteOrder(ctx *gin.Context) {\n\treqDto := req.DeleteOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = orderService.DeleteOrder(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询订单\n// @Description 查询订单\n// @Tags 订单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/order/query [post]\nfunc (controller *OrderController) QueryOrders(ctx *gin.Context) {\n\treqDto := req.QueryOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := orderService.FindAllOrder(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.OrderVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, orderTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询订单列表\n// @Description 查询订单列表\n// @Tags 订单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/order/list [post]\nfunc (a *OrderController) ListOrders(ctx *gin.Context) {\n\treqDto := req.QueryOrderReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := orderService.FindAllOrderWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.OrderVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.OrderVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, orderTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderRoute struct {\n}\n\nfunc (s *OrderRoute) InitOrderRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\torderController := controller.OrderController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/order/add\", orderController.AddOrder)       //add\n\t\troute.POST(\"/api/order/update\", orderController.UpdateOrder)   //update\n\t\troute.POST(\"/api/order/delete\", orderController.DeleteOrder)   //delete\n\t\troute.POST(\"/api/order/query\", orderController.QueryOrders)    //query\n\t\troute.POST(\"/api/order/list\", orderController.ListOrders)     //list\n\t}\n}\n"}]