[{"po": "package po\n\n// Recipe 配方实体\ntype Recipe struct {\n\tId           *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tQuantity     *float32 `gorm:\"column:quantity;type:float;default:0\" json:\"quantity\"`           // 配料数量\n\tProductId    *string  `gorm:\"column:product_id;type:varchar(64);default:''\" json:\"productId\"`   // 产品ID\n\tIngredientId *string  `gorm:\"column:ingredient_id;type:varchar(64);default:''\" json:\"ingredientId\"` // 配料ID\n\tCtime        *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间戳\n\tUtime        *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间戳\n\tState        *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态值\n\tVersion      *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (Recipe) TableName() string {\n\treturn \"recipe\"\n}\n\nfunc (r Recipe) GetId() string {\n\treturn *r.Id\n}\n", "vo": "package vo\n\n// RecipeVO 配方信息值对象\ntype RecipeVO struct {\n\tId           string  `json:\"id\"`           // ID\n\tQuantity     float32 `json:\"quantity\"`     // 配料数量\n\tProductId    string  `json:\"productId\"`    // 产品ID\n\tIngredientId string  `json:\"ingredientId\"` // 配料ID\n\tCtime        int64   `json:\"ctime\"`        // 创建时间戳\n\tUtime        int64   `json:\"utime\"`        // 更新时间戳\n\tState        int     `json:\"state\"`        // 状态值\n\tVersion      int     `json:\"version\"`      // 版本号\n}\n", "req_add": "package req\n\n// AddRecipeReqDto 创建配方请求DTO\ntype AddRecipeReqDto struct {\n\tQuantity     *float32 `json:\"quantity\"`     // 配料数量\n\tProductId    *string  `json:\"productId\"`    // 产品ID\n\tIngredientId *string  `json:\"ingredientId\"` // 配料ID\n}\n", "req_update": "package req\n\ntype UpdateRecipeReqDto struct {\n\tId           *string  `json:\"id\"`           // ID\n\tQuantity     *float32 `json:\"quantity\"`     // 配料数量\n\tProductId    *string  `json:\"productId\"`    // 产品ID\n\tIngredientId *string  `json:\"ingredientId\"` // 配料ID\n}\n", "req_delete": "package req\n\ntype DeleteRecipeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryRecipeReqDto struct {\n\tId           *string  `json:\"id\"`           // ID\n\tQuantity     *float32 `json:\"quantity\"`     // 配料数量\n\tProductId    *string  `json:\"productId\"`    // 产品ID\n\tIngredientId *string  `json:\"ingredientId\"` // 配料ID\n\tPageNum      *int     `json:\"pageNum\"`      // 页码\n\tPageSize     *int     `json:\"pageSize\"`     // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RecipeTransfer struct {\n}\n\nfunc (transfer *RecipeTransfer) PoToVo(po po.Recipe) vo.RecipeVO {\n\tvo := vo.RecipeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RecipeTransfer) VoToPo(vo vo.RecipeVO) po.Recipe {\n\tpo := po.Recipe{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RecipeService struct {\n}\n\nfunc (service *RecipeService) CreateRecipe(logCtx *gin.Context, recipe *po.Recipe) error {\n\treturn Save(recipe)\n}\n\nfunc (service *RecipeService) UpdateRecipe(logCtx *gin.Context, recipe *po.Recipe) error {\n\treturn Update(recipe)\n}\n\nfunc (service *RecipeService) DeleteRecipe(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Recipe{Id: &id})\n}\n\nfunc (service *RecipeService) FindRecipeById(logCtx *gin.Context, id string) (recipe *po.Recipe, err error) {\n\trecipe = &po.Recipe{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(recipe).Error\n\treturn\n}\n\nfunc (service *RecipeService) FindAllRecipe(logCtx *gin.Context, reqDto *req.QueryRecipeReqDto) (list *[]po.Recipe, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Recipe{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.ProductId != nil && *reqDto.ProductId != \"\" {\n\t\tdb = db.Where(\"product_id=?\", *reqDto.ProductId)\n\t}\n\tif reqDto.IngredientId != nil && *reqDto.IngredientId != \"\" {\n\t\tdb = db.Where(\"ingredient_id=?\", *reqDto.IngredientId)\n\t}\n\tif reqDto.Quantity != nil {\n\t\tdb = db.Where(\"quantity=?\", *reqDto.Quantity)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Recipe{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RecipeService) FindAllRecipeWithPagination(logCtx *gin.Context, reqDto *req.QueryRecipeReqDto) (list *[]po.Recipe, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Recipe{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.ProductId != nil && *reqDto.ProductId != \"\" {\n\t\tdb = db.Where(\"product_id=?\", *reqDto.ProductId)\n\t}\n\tif reqDto.IngredientId != nil && *reqDto.IngredientId != \"\" {\n\t\tdb = db.Where(\"ingredient_id=?\", *reqDto.IngredientId)\n\t}\n\tif reqDto.Quantity != nil {\n\t\tdb = db.Where(\"quantity=?\", *reqDto.Quantity)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Recipe{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RecipeController struct{}\n\nvar (\n\trecipeService  = impl.RecipeService{}\n\trecipeTransfer = transfer.RecipeTransfer{}\n)\n\n// @Summary 添加配方\n// @Description 添加配方\n// @Tags 配方\n// @Accept json\n// @Produce json\n// @Param body body req.AddRecipeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RecipeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/recipe/add [post]\nfunc (controller *RecipeController) AddRecipe(ctx *gin.Context) {\n\treqDto := req.AddRecipeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\trecipe := po.Recipe{}\n\tif reqDto.Quantity != nil {\n\t\trecipe.Quantity = reqDto.Quantity\n\t}\n\tif reqDto.ProductId != nil {\n\t\trecipe.ProductId = reqDto.ProductId\n\t}\n\tif reqDto.IngredientId != nil {\n\t\trecipe.IngredientId = reqDto.IngredientId\n\t}\n\n\terr = recipeService.CreateRecipe(ctx, &recipe)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, recipeTransfer.PoToVo(recipe))\n}\n\n// @Summary 更新配方\n// @Description 更新配方\n// @Tags 配方\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRecipeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RecipeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/recipe/update [post]\nfunc (controller *RecipeController) UpdateRecipe(ctx *gin.Context) {\n\treqDto := req.UpdateRecipeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\trecipe, err := recipeService.FindRecipeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Quantity != nil {\n\t\trecipe.Quantity = reqDto.Quantity\n\t}\n\tif reqDto.ProductId != nil {\n\t\trecipe.ProductId = reqDto.ProductId\n\t}\n\tif reqDto.IngredientId != nil {\n\t\trecipe.IngredientId = reqDto.IngredientId\n\t}\n\n\terr = recipeService.UpdateRecipe(ctx, recipe)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, recipeTransfer.PoToVo(*recipe))\n}\n\n// @Summary 删除配方\n// @Description 删除配方\n// @Tags 配方\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRecipeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/recipe/delete [post]\nfunc (controller *RecipeController) DeleteRecipe(ctx *gin.Context) {\n\treqDto := req.DeleteRecipeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = recipeService.DeleteRecipe(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询配方\n// @Description 查询配方\n// @Tags 配方\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRecipeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RecipeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/recipe/query [post]\nfunc (controller *RecipeController) QueryRecipes(ctx *gin.Context) {\n\treqDto := req.QueryRecipeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := recipeService.FindAllRecipe(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.RecipeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, recipeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询配方列表\n// @Description 查询配方列表\n// @Tags 配方\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRecipeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RecipeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/recipe/list [post]\nfunc (controller *RecipeController) ListRecipes(ctx *gin.Context) {\n\treqDto := req.QueryRecipeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := recipeService.FindAllRecipeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.RecipeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RecipeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, recipeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RecipeRoute struct {\n}\n\nfunc (s *RecipeRoute) InitRecipeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\trecipeController := controller.RecipeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/recipe/add\", recipeController.AddRecipe)       // add\n\t\troute.POST(\"/api/recipe/update\", recipeController.UpdateRecipe)   // update\n\t\troute.POST(\"/api/recipe/delete\", recipeController.DeleteRecipe)   // delete\n\t\troute.POST(\"/api/recipe/query\", recipeController.QueryRecipes)    // query\n\t\troute.POST(\"/api/recipe/list\", recipeController.ListRecipes)     // list\n\t}\n}\n"}]