[{"po": "package po\n\n// CommissionPlan 佣金计划实体\ntype CommissionPlan struct {\n\tId                      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                    *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                    // 佣金计划名称\n\tType                    *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`                    // 佣金类型\n\tCalculationMethod       *string `gorm:\"column:calculation_method;type:varchar(64);default:''\" json:\"calculationMethod\"` // 计算方法\n\tAmount                  *int64  `gorm:\"column:amount;type:bigint;default:0\" json:\"amount\"`                    // 佣金金额\n\tExcludedProducts       *string `gorm:\"column:excluded_products;type:text\" json:\"excludedProducts\"`           // 排除的产品列表\n\tMinimumBillAmount      *int64  `gorm:\"column:minimum_bill_amount;type:bigint;default:0\" json:\"minimumBillAmount\"` // 最低账单金额\n\tTimePeriods            *string `gorm:\"column:time_periods;type:text\" json:\"timePeriods\"`                   // 时间段列表\n\tAllowedRoomTypes       *string `gorm:\"column:allowed_room_types;type:text\" json:\"allowedRoomTypes\"`         // 允许的房间类型列表\n\tAllowedBookingQuantity *int    `gorm:\"column:allowed_booking_quantity;type:int;default:0\" json:\"allowedBookingQuantity\"` // 允许的预订数量\n\tIncludesRoomRotation   *bool   `gorm:\"column:includes_room_rotation;type:bool;default:false\" json:\"includesRoomRotation\"` // 是否包含房间轮换\n\tMaximumCommission      *int64  `gorm:\"column:maximum_commission;type:bigint;default:0\" json:\"maximumCommission\"` // 最大佣金金额\n\tTieredRechargeCommission *bool  `gorm:\"column:tiered_recharge_commission;type:bool;default:false\" json:\"tieredRechargeCommission\"` // 是否使用分层充值佣金\n\tCtime                  *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                    // 创建时间\n\tUtime                  *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                    // 更新时间\n\tState                  *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态\n\tVersion                *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本\n}\n\n// TableName 设置表名\nfunc (CommissionPlan) TableName() string {\n\treturn \"commission_plan\"\n}\n\nfunc (c CommissionPlan) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CommissionPlanVO 佣金计划值对象\ntype CommissionPlanVO struct {\n\tId                      string `json:\"id\"`                      // ID\n\tName                    string `json:\"name\"`                    // 佣金计划名称\n\tType                    string `json:\"type\"`                    // 佣金类型\n\tCalculationMethod       string `json:\"calculationMethod\"`       // 计算方法\n\tAmount                  int64  `json:\"amount\"`                  // 佣金金额\n\tExcludedProducts        string `json:\"excludedProducts\"`        // 排除的产品列表\n\tMinimumBillAmount       int64  `json:\"minimumBillAmount\"`       // 最低账单金额\n\tTimePeriods             string `json:\"timePeriods\"`             // 时间段列表\n\tAllowedRoomTypes        string `json:\"allowedRoomTypes\"`        // 允许的房间类型列表\n\tAllowedBookingQuantity  int    `json:\"allowedBookingQuantity\"`  // 允许的预订数量\n\tIncludesRoomRotation    bool   `json:\"includesRoomRotation\"`    // 是否包含房间轮换\n\tMaximumCommission       int64  `json:\"maximumCommission\"`       // 最大佣金金额\n\tTieredRechargeCommission bool   `json:\"tieredRechargeCommission\"` // 是否使用分层充值佣金\n\tCtime                   int64  `json:\"ctime\"`                   // 创建时间\n\tUtime                   int64  `json:\"utime\"`                   // 更新时间\n\tState                   int    `json:\"state\"`                   // 状态\n\tVersion                 int    `json:\"version\"`                 // 版本\n}\n", "req_add": "package req\n\n// AddCommissionPlanReqDto 创建佣金计划请求DTO\ntype AddCommissionPlanReqDto struct {\n\tName                    *string `json:\"name\"`                    // 佣金计划名称\n\tType                    *string `json:\"type\"`                    // 佣金类型\n\tCalculationMethod       *string `json:\"calculationMethod\"`       // 计算方法\n\tAmount                  *int64  `json:\"amount\"`                  // 佣金金额\n\tExcludedProducts        *string `json:\"excludedProducts\"`        // 排除的产品列表\n\tMinimumBillAmount       *int64  `json:\"minimumBillAmount\"`       // 最低账单金额\n\tTimePeriods             *string `json:\"timePeriods\"`             // 时间段列表\n\tAllowedRoomTypes        *string `json:\"allowedRoomTypes\"`        // 允许的房间类型列表\n\tAllowedBookingQuantity  *int    `json:\"allowedBookingQuantity\"`  // 允许的预订数量\n\tIncludesRoomRotation    *bool   `json:\"includesRoomRotation\"`    // 是否包含房间轮换\n\tMaximumCommission       *int64  `json:\"maximumCommission\"`       // 最大佣金金额\n\tTieredRechargeCommission *bool   `json:\"tieredRechargeCommission\"` // 是否使用分层充值佣金\n}\n", "req_update": "package req\n\n// UpdateCommissionPlanReqDto 更新佣金计划请求DTO\ntype UpdateCommissionPlanReqDto struct {\n\tId                      *string `json:\"id\"`                      // ID\n\tName                    *string `json:\"name\"`                    // 佣金计划名称\n\tType                    *string `json:\"type\"`                    // 佣金类型\n\tCalculationMethod       *string `json:\"calculationMethod\"`       // 计算方法\n\tAmount                  *int64  `json:\"amount\"`                  // 佣金金额\n\tExcludedProducts        *string `json:\"excludedProducts\"`        // 排除的产品列表\n\tMinimumBillAmount       *int64  `json:\"minimumBillAmount\"`       // 最低账单金额\n\tTimePeriods             *string `json:\"timePeriods\"`             // 时间段列表\n\tAllowedRoomTypes        *string `json:\"allowedRoomTypes\"`        // 允许的房间类型列表\n\tAllowedBookingQuantity  *int    `json:\"allowedBookingQuantity\"`  // 允许的预订数量\n\tIncludesRoomRotation    *bool   `json:\"includesRoomRotation\"`    // 是否包含房间轮换\n\tMaximumCommission       *int64  `json:\"maximumCommission\"`       // 最大佣金金额\n\tTieredRechargeCommission *bool   `json:\"tieredRechargeCommission\"` // 是否使用分层充值佣金\n}\n", "req_delete": "package req\n\ntype DeleteCommissionPlanReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCommissionPlanReqDto struct {\n\tId                      *string `json:\"id\"`                      // ID\n\tName                    *string `json:\"name\"`                    // 佣金计划名称\n\tType                    *string `json:\"type\"`                    // 佣金类型\n\tCalculationMethod       *string `json:\"calculationMethod\"`       // 计算方法\n\tPageNum                 *int    `json:\"pageNum\"`                 // 页码\n\tPageSize                *int    `json:\"pageSize\"`                // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CommissionPlanTransfer struct {\n}\n\nfunc (transfer *CommissionPlanTransfer) PoToVo(po po.CommissionPlan) vo.CommissionPlanVO {\n\tvo := vo.CommissionPlanVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CommissionPlanTransfer) VoToPo(vo vo.CommissionPlanVO) po.CommissionPlan {\n\tpo := po.CommissionPlan{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommissionPlanService struct {\n}\n\nfunc (service *CommissionPlanService) CreateCommissionPlan(logCtx *gin.Context, commissionPlan *po.CommissionPlan) error {\n\treturn Save(commissionPlan)\n}\n\nfunc (service *CommissionPlanService) UpdateCommissionPlan(logCtx *gin.Context, commissionPlan *po.CommissionPlan) error {\n\treturn Update(commissionPlan)\n}\n\nfunc (service *CommissionPlanService) DeleteCommissionPlan(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CommissionPlan{Id: &id})\n}\n\nfunc (service *CommissionPlanService) FindCommissionPlanById(logCtx *gin.Context, id string) (commissionPlan *po.CommissionPlan, err error) {\n\tcommissionPlan = &po.CommissionPlan{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(commissionPlan).Error\n\treturn\n}\n\nfunc (service *CommissionPlanService) FindAllCommissionPlan(logCtx *gin.Context, reqDto *req.QueryCommissionPlanReqDto) (list *[]po.CommissionPlan, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CommissionPlan{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.CalculationMethod != nil && *reqDto.CalculationMethod != \"\" {\n\t\tdb = db.Where(\"calculation_method=?\", *reqDto.CalculationMethod)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CommissionPlan{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CommissionPlanService) FindAllCommissionPlanWithPagination(logCtx *gin.Context, reqDto *req.QueryCommissionPlanReqDto) (list *[]po.CommissionPlan, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CommissionPlan{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.CalculationMethod != nil && *reqDto.CalculationMethod != \"\" {\n\t\tdb = db.Where(\"calculation_method=?\", *reqDto.CalculationMethod)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CommissionPlan{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommissionPlanController struct{}\n\nvar (\n\tcommissionPlanService  = impl.CommissionPlanService{}\n\tcommissionPlanTransfer = transfer.CommissionPlanTransfer{}\n)\n\n// @Summary 添加佣金计划\n// @Description 添加佣金计划\n// @Tags 佣金计划\n// @Accept json\n// @Produce json\n// @Param body body req.AddCommissionPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CommissionPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission-plan/add [post]\nfunc (controller *CommissionPlanController) AddCommissionPlan(ctx *gin.Context) {\n\treqDto := req.AddCommissionPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcommissionPlan := po.CommissionPlan{}\n\tif reqDto.Name != nil {\n\t\tcommissionPlan.Name = reqDto.Name\n\t}\n\tif reqDto.Type != nil {\n\t\tcommissionPlan.Type = reqDto.Type\n\t}\n\tif reqDto.CalculationMethod != nil {\n\t\tcommissionPlan.CalculationMethod = reqDto.CalculationMethod\n\t}\n\tif reqDto.Amount != nil {\n\t\tcommissionPlan.Amount = reqDto.Amount\n\t}\n\tif reqDto.ExcludedProducts != nil {\n\t\tcommissionPlan.ExcludedProducts = reqDto.ExcludedProducts\n\t}\n\tif reqDto.MinimumBillAmount != nil {\n\t\tcommissionPlan.MinimumBillAmount = reqDto.MinimumBillAmount\n\t}\n\tif reqDto.TimePeriods != nil {\n\t\tcommissionPlan.TimePeriods = reqDto.TimePeriods\n\t}\n\tif reqDto.AllowedRoomTypes != nil {\n\t\tcommissionPlan.AllowedRoomTypes = reqDto.AllowedRoomTypes\n\t}\n\tif reqDto.AllowedBookingQuantity != nil {\n\t\tcommissionPlan.AllowedBookingQuantity = reqDto.AllowedBookingQuantity\n\t}\n\tif reqDto.IncludesRoomRotation != nil {\n\t\tcommissionPlan.IncludesRoomRotation = reqDto.IncludesRoomRotation\n\t}\n\tif reqDto.MaximumCommission != nil {\n\t\tcommissionPlan.MaximumCommission = reqDto.MaximumCommission\n\t}\n\tif reqDto.TieredRechargeCommission != nil {\n\t\tcommissionPlan.TieredRechargeCommission = reqDto.TieredRechargeCommission\n\t}\n\n\terr = commissionPlanService.CreateCommissionPlan(ctx, &commissionPlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, commissionPlanTransfer.PoToVo(commissionPlan))\n}\n\n// @Summary 更新佣金计划\n// @Description 更新佣金计划\n// @Tags 佣金计划\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCommissionPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CommissionPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission-plan/update [post]\nfunc (controller *CommissionPlanController) UpdateCommissionPlan(ctx *gin.Context) {\n\treqDto := req.UpdateCommissionPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tcommissionPlan, err := commissionPlanService.FindCommissionPlanById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tcommissionPlan.Name = reqDto.Name\n\t}\n\tif reqDto.Type != nil {\n\t\tcommissionPlan.Type = reqDto.Type\n\t}\n\tif reqDto.CalculationMethod != nil {\n\t\tcommissionPlan.CalculationMethod = reqDto.CalculationMethod\n\t}\n\tif reqDto.Amount != nil {\n\t\tcommissionPlan.Amount = reqDto.Amount\n\t}\n\tif reqDto.ExcludedProducts != nil {\n\t\tcommissionPlan.ExcludedProducts = reqDto.ExcludedProducts\n\t}\n\tif reqDto.MinimumBillAmount != nil {\n\t\tcommissionPlan.MinimumBillAmount = reqDto.MinimumBillAmount\n\t}\n\tif reqDto.TimePeriods != nil {\n\t\tcommissionPlan.TimePeriods = reqDto.TimePeriods\n\t}\n\tif reqDto.AllowedRoomTypes != nil {\n\t\tcommissionPlan.AllowedRoomTypes = reqDto.AllowedRoomTypes\n\t}\n\tif reqDto.AllowedBookingQuantity != nil {\n\t\tcommissionPlan.AllowedBookingQuantity = reqDto.AllowedBookingQuantity\n\t}\n\tif reqDto.IncludesRoomRotation != nil {\n\t\tcommissionPlan.IncludesRoomRotation = reqDto.IncludesRoomRotation\n\t}\n\tif reqDto.MaximumCommission != nil {\n\t\tcommissionPlan.MaximumCommission = reqDto.MaximumCommission\n\t}\n\tif reqDto.TieredRechargeCommission != nil {\n\t\tcommissionPlan.TieredRechargeCommission = reqDto.TieredRechargeCommission\n\t}\n\n\terr = commissionPlanService.UpdateCommissionPlan(ctx, commissionPlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, commissionPlanTransfer.PoToVo(*commissionPlan))\n}\n\n// @Summary 删除佣金计划\n// @Description 删除佣金计划\n// @Tags 佣金计划\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCommissionPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission-plan/delete [post]\nfunc (controller *CommissionPlanController) DeleteCommissionPlan(ctx *gin.Context) {\n\treqDto := req.DeleteCommissionPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = commissionPlanService.DeleteCommissionPlan(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询佣金计划\n// @Description 查询佣金计划\n// @Tags 佣金计划\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCommissionPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CommissionPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission-plan/query [post]\nfunc (controller *CommissionPlanController) QueryCommissionPlans(ctx *gin.Context) {\n\treqDto := req.QueryCommissionPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := commissionPlanService.FindAllCommissionPlan(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.CommissionPlanVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, commissionPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询佣金计划列表\n// @Description 查询佣金计划列表\n// @Tags 佣金计划\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCommissionPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CommissionPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission-plan/list [post]\nfunc (controller *CommissionPlanController) ListCommissionPlans(ctx *gin.Context) {\n\treqDto := req.QueryCommissionPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := commissionPlanService.FindAllCommissionPlanWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.CommissionPlanVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CommissionPlanVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, commissionPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommissionPlanRoute struct {\n}\n\nfunc (s *CommissionPlanRoute) InitCommissionPlanRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcommissionPlanController := controller.CommissionPlanController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/commission-plan/add\", commissionPlanController.AddCommissionPlan)       // add\n\t\troute.POST(\"/api/commission-plan/update\", commissionPlanController.UpdateCommissionPlan)   // update\n\t\troute.POST(\"/api/commission-plan/delete\", commissionPlanController.DeleteCommissionPlan)   // delete\n\t\troute.POST(\"/api/commission-plan/query\", commissionPlanController.QueryCommissionPlans)    // query\n\t\troute.POST(\"/api/commission-plan/list\", commissionPlanController.ListCommissionPlans)     // list\n\t}\n}\n"}]