[{"po": "package po\n\n// Member 会员信息实体\ntype Member struct {\n\tId                   *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                 *string  `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                    // 会员姓名\n\tPhone                *string  `gorm:\"column:phone;type:varchar(64);default:''\" json:\"phone\"`                  // 会员手机号\n\tCardNumber           *string  `gorm:\"column:card_number;type:varchar(64);default:''\" json:\"cardNumber\"`       // 会员卡号\n\tPoints               *int     `gorm:\"column:points;type:int;default:0\" json:\"points\"`                        // 会员积分\n\tBalance              *float64 `gorm:\"column:balance;type:decimal(10,2);default:0\" json:\"balance\"`            // 会员余额\n\tLevel                *string  `gorm:\"column:level;type:varchar(64);default:''\" json:\"level\"`                  // 会员等级\n\tMemberCardTemplateId *string  `gorm:\"column:member_card_template_id;type:varchar(64);default:''\" json:\"memberCardTemplateId\"` // 所属会员卡模板ID\n\tBirthday             *int64   `gorm:\"column:birthday;type:int;default:0\" json:\"birthday\"`                    // 生日\n\tGender               *string  `gorm:\"column:gender;type:varchar(64);default:''\" json:\"gender\"`                // 性别\n\tTotalConsumptionTimes *int     `gorm:\"column:total_consumption_times;type:int;default:0\" json:\"totalConsumptionTimes\"` // 累计消费次数\n\tTotalConsumptionAmount *float64 `gorm:\"column:total_consumption_amount;type:decimal(10,2);default:0\" json:\"totalConsumptionAmount\"` // 累计消费金额\n\tCtime                *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间戳\n\tUtime                *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间戳\n\tState                *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态值\n\tVersion              *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (Member) TableName() string {\n\treturn \"member\"\n}\n\nfunc (m Member) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MemberVO 会员信息值对象\ntype MemberVO struct {\n\tId                    string  `json:\"id\"`                    // ID\n\tName                  string  `json:\"name\"`                  // 会员姓名\n\tPhone                 string  `json:\"phone\"`                 // 会员手机号\n\tCardNumber            string  `json:\"cardNumber\"`            // 会员卡号\n\tPoints                int     `json:\"points\"`                // 会员积分\n\tBalance               float64 `json:\"balance\"`               // 会员余额\n\tLevel                 string  `json:\"level\"`                 // 会员等级\n\tMemberCardTemplateId  string  `json:\"memberCardTemplateId\"`  // 所属会员卡模板ID\n\tBirthday              int64   `json:\"birthday\"`              // 生日\n\tGender                string  `json:\"gender\"`                // 性别\n\tTotalConsumptionTimes int     `json:\"totalConsumptionTimes\"` // 累计消费次数\n\tTotalConsumptionAmount float64 `json:\"totalConsumptionAmount\"` // 累计消费金额\n\tCtime                 int64   `json:\"ctime\"`                 // 创建时间戳\n\tUtime                 int64   `json:\"utime\"`                 // 更新时间戳\n\tState                 int     `json:\"state\"`                 // 状态值\n\tVersion               int     `json:\"version\"`               // 版本号\n}\n", "req_add": "package req\n\n// AddMemberReqDto 创建会员请求DTO\ntype AddMemberReqDto struct {\n\tName                  *string  `json:\"name\"`                  // 会员姓名\n\tPhone                 *string  `json:\"phone\"`                 // 会员手机号\n\tCardNumber            *string  `json:\"cardNumber\"`            // 会员卡号\n\tPoints                *int     `json:\"points\"`                // 会员积分\n\tBalance               *float64 `json:\"balance\"`               // 会员余额\n\tLevel                 *string  `json:\"level\"`                 // 会员等级\n\tMemberCardTemplateId  *string  `json:\"memberCardTemplateId\"`  // 所属会员卡模板ID\n\tBirthday              *int64   `json:\"birthday\"`              // 生日\n\tGender                *string  `json:\"gender\"`                // 性别\n\tTotalConsumptionTimes *int     `json:\"totalConsumptionTimes\"` // 累计消费次数\n\tTotalConsumptionAmount *float64 `json:\"totalConsumptionAmount\"` // 累计消费金额\n}\n", "req_update": "package req\n\n// UpdateMemberReqDto 更新会员请求DTO\ntype UpdateMemberReqDto struct {\n\tId                    *string  `json:\"id\"`                    // ID\n\tName                  *string  `json:\"name\"`                  // 会员姓名\n\tPhone                 *string  `json:\"phone\"`                 // 会员手机号\n\tCardNumber            *string  `json:\"cardNumber\"`            // 会员卡号\n\tPoints                *int     `json:\"points\"`                // 会员积分\n\tBalance               *float64 `json:\"balance\"`               // 会员余额\n\tLevel                 *string  `json:\"level\"`                 // 会员等级\n\tMemberCardTemplateId  *string  `json:\"memberCardTemplateId\"`  // 所属会员卡模板ID\n\tBirthday              *int64   `json:\"birthday\"`              // 生日\n\tGender                *string  `json:\"gender\"`                // 性别\n\tTotalConsumptionTimes *int     `json:\"totalConsumptionTimes\"` // 累计消费次数\n\tTotalConsumptionAmount *float64 `json:\"totalConsumptionAmount\"` // 累计消费金额\n}\n", "req_delete": "package req\n\ntype DeleteMemberReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryMemberReqDto struct {\n\tId                    *string  `json:\"id\"`                    // ID\n\tName                  *string  `json:\"name\"`                  // 会员姓名\n\tPhone                 *string  `json:\"phone\"`                 // 会员手机号\n\tCardNumber            *string  `json:\"cardNumber\"`            // 会员卡号\n\tLevel                 *string  `json:\"level\"`                 // 会员等级\n\tMemberCardTemplateId  *string  `json:\"memberCardTemplateId\"`  // 所属会员卡模板ID\n\tGender                *string  `json:\"gender\"`                // 性别\n\tPageNum               *int     `json:\"pageNum\"`               // 页码\n\tPageSize              *int     `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberTransfer struct {\n}\n\nfunc (transfer *MemberTransfer) PoToVo(po po.Member) vo.MemberVO {\n\tvo := vo.MemberVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MemberTransfer) VoToPo(vo vo.MemberVO) po.Member {\n\tpo := po.Member{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberService struct {\n}\n\nfunc (service *MemberService) CreateMember(logCtx *gin.Context, member *po.Member) error {\n\treturn Save(member)\n}\n\nfunc (service *MemberService) UpdateMember(logCtx *gin.Context, member *po.Member) error {\n\treturn Update(member)\n}\n\nfunc (service *MemberService) DeleteMember(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Member{Id: &id})\n}\n\nfunc (service *MemberService) FindMemberById(logCtx *gin.Context, id string) (member *po.Member, err error) {\n\tmember = &po.Member{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(member).Error\n\treturn\n}\n\nfunc (service *MemberService) FindAllMember(logCtx *gin.Context, reqDto *req.QueryMemberReqDto) (list *[]po.Member, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Member{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Phone != nil && *reqDto.Phone != \"\" {\n\t\tdb = db.Where(\"phone=?\", *reqDto.Phone)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\tif reqDto.Level != nil && *reqDto.Level != \"\" {\n\t\tdb = db.Where(\"level=?\", *reqDto.Level)\n\t}\n\tif reqDto.MemberCardTemplateId != nil && *reqDto.MemberCardTemplateId != \"\" {\n\t\tdb = db.Where(\"member_card_template_id=?\", *reqDto.MemberCardTemplateId)\n\t}\n\tif reqDto.Gender != nil && *reqDto.Gender != \"\" {\n\t\tdb = db.Where(\"gender=?\", *reqDto.Gender)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Member{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MemberService) FindAllMemberWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberReqDto) (list *[]po.Member, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Member{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Phone != nil && *reqDto.Phone != \"\" {\n\t\tdb = db.Where(\"phone=?\", *reqDto.Phone)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\tif reqDto.Level != nil && *reqDto.Level != \"\" {\n\t\tdb = db.Where(\"level=?\", *reqDto.Level)\n\t}\n\tif reqDto.MemberCardTemplateId != nil && *reqDto.MemberCardTemplateId != \"\" {\n\t\tdb = db.Where(\"member_card_template_id=?\", *reqDto.MemberCardTemplateId)\n\t}\n\tif reqDto.Gender != nil && *reqDto.Gender != \"\" {\n\t\tdb = db.Where(\"gender=?\", *reqDto.Gender)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Member{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberController struct{}\n\nvar (\n\tmemberService  = impl.MemberService{}\n\tmemberTransfer = transfer.MemberTransfer{}\n)\n\n// @Summary 添加会员\n// @Description 添加会员\n// @Tags 会员\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/add [post]\nfunc (controller *MemberController) AddMember(ctx *gin.Context) {\n\treqDto := req.AddMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmember := po.Member{}\n\tif reqDto.Name != nil {\n\t\tmember.Name = reqDto.Name\n\t}\n\tif reqDto.Phone != nil {\n\t\tmember.Phone = reqDto.Phone\n\t}\n\tif reqDto.CardNumber != nil {\n\t\tmember.CardNumber = reqDto.CardNumber\n\t}\n\tif reqDto.Points != nil {\n\t\tmember.Points = reqDto.Points\n\t}\n\tif reqDto.Balance != nil {\n\t\tmember.Balance = reqDto.Balance\n\t}\n\tif reqDto.Level != nil {\n\t\tmember.Level = reqDto.Level\n\t}\n\tif reqDto.MemberCardTemplateId != nil {\n\t\tmember.MemberCardTemplateId = reqDto.MemberCardTemplateId\n\t}\n\tif reqDto.Birthday != nil {\n\t\tmember.Birthday = reqDto.Birthday\n\t}\n\tif reqDto.Gender != nil {\n\t\tmember.Gender = reqDto.Gender\n\t}\n\tif reqDto.TotalConsumptionTimes != nil {\n\t\tmember.TotalConsumptionTimes = reqDto.TotalConsumptionTimes\n\t}\n\tif reqDto.TotalConsumptionAmount != nil {\n\t\tmember.TotalConsumptionAmount = reqDto.TotalConsumptionAmount\n\t}\n\n\terr = memberService.CreateMember(ctx, &member)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberTransfer.PoToVo(member))\n}\n\n// @Summary 更新会员\n// @Description 更新会员\n// @Tags 会员\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/update [post]\nfunc (controller *MemberController) UpdateMember(ctx *gin.Context) {\n\treqDto := req.UpdateMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmember, err := memberService.FindMemberById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tmember.Name = reqDto.Name\n\t}\n\tif reqDto.Phone != nil {\n\t\tmember.Phone = reqDto.Phone\n\t}\n\tif reqDto.CardNumber != nil {\n\t\tmember.CardNumber = reqDto.CardNumber\n\t}\n\tif reqDto.Points != nil {\n\t\tmember.Points = reqDto.Points\n\t}\n\tif reqDto.Balance != nil {\n\t\tmember.Balance = reqDto.Balance\n\t}\n\tif reqDto.Level != nil {\n\t\tmember.Level = reqDto.Level\n\t}\n\tif reqDto.MemberCardTemplateId != nil {\n\t\tmember.MemberCardTemplateId = reqDto.MemberCardTemplateId\n\t}\n\tif reqDto.Birthday != nil {\n\t\tmember.Birthday = reqDto.Birthday\n\t}\n\tif reqDto.Gender != nil {\n\t\tmember.Gender = reqDto.Gender\n\t}\n\tif reqDto.TotalConsumptionTimes != nil {\n\t\tmember.TotalConsumptionTimes = reqDto.TotalConsumptionTimes\n\t}\n\tif reqDto.TotalConsumptionAmount != nil {\n\t\tmember.TotalConsumptionAmount = reqDto.TotalConsumptionAmount\n\t}\n\n\terr = memberService.UpdateMember(ctx, member)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberTransfer.PoToVo(*member))\n}\n\n// @Summary 删除会员\n// @Description 删除会员\n// @Tags 会员\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/delete [post]\nfunc (controller *MemberController) DeleteMember(ctx *gin.Context) {\n\treqDto := req.DeleteMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = memberService.DeleteMember(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员\n// @Description 查询会员\n// @Tags 会员\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/query [post]\nfunc (controller *MemberController) QueryMembers(ctx *gin.Context) {\n\treqDto := req.QueryMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := memberService.FindAllMember(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MemberVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, memberTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询会员列表\n// @Description 查询会员列表\n// @Tags 会员\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/list [post]\nfunc (controller *MemberController) ListMembers(ctx *gin.Context) {\n\treqDto := req.QueryMemberReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := memberService.FindAllMemberWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MemberVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MemberVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberRoute struct {\n}\n\nfunc (s *MemberRoute) InitMemberRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmemberController := controller.MemberController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/member/add\", memberController.AddMember)       //add\n\t\troute.POST(\"/api/member/update\", memberController.UpdateMember) //update\n\t\troute.POST(\"/api/member/delete\", memberController.DeleteMember) //delete\n\t\troute.POST(\"/api/member/query\", memberController.QueryMembers)  //query\n\t\troute.POST(\"/api/member/list\", memberController.ListMembers)    //list\n\t}\n}\n"}]