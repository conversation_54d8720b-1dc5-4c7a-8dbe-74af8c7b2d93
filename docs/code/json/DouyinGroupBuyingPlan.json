[{"po": "package po\n\n// DouyinGroupBuyingPlan 抖音团购计划实体\ntype DouyinGroupBuyingPlan struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一id\n\tPlanId        *string `gorm:\"column:plan_id;type:varchar(64);default:''\" json:\"planId\"`          // 计划ID\n\tPlanName      *string `gorm:\"column:plan_name;type:varchar(255);default:''\" json:\"planName\"`      // 计划名称\n\tPrice         *int64  `gorm:\"column:price;type:bigint;default:0\" json:\"price\"`                    // 价格\n\tProductInfo   *string `gorm:\"column:product_info;type:text\" json:\"productInfo\"`                    // 产品信息(JSON)\n\tBindingStatus *string `gorm:\"column:binding_status;type:varchar(64);default:''\" json:\"bindingStatus\"` // 绑定状态\n\tCtime         *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                    // 创建时间\n\tUtime         *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                    // 更新时间\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本\n}\n\n// TableName 设置表名\nfunc (DouyinGroupBuyingPlan) TableName() string {\n\treturn \"douyin_group_buying_plan\"\n}\n\nfunc (d DouyinGroupBuyingPlan) GetId() string {\n\treturn *d.Id\n}", "vo": "package vo\n\n// DouyinGroupBuyingPlanVO 抖音团购计划值对象\ntype DouyinGroupBuyingPlanVO struct {\n\tId            string `json:\"id\"`            // 唯一id\n\tPlanId        string `json:\"planId\"`        // 计划ID\n\tPlanName      string `json:\"planName\"`      // 计划名称\n\tPrice         int64  `json:\"price\"`         // 价格\n\tProductInfo   string `json:\"productInfo\"`   // 产品信息(JSON)\n\tBindingStatus string `json:\"bindingStatus\"` // 绑定状态\n\tCtime         int64  `json:\"ctime\"`         // 创建时间\n\tUtime         int64  `json:\"utime\"`         // 更新时间\n\tState         int    `json:\"state\"`         // 状态\n\tVersion       int    `json:\"version\"`       // 版本\n}", "req_add": "package req\n\n// AddDouyinGroupBuyingPlanReqDto 创建抖音团购计划请求DTO\ntype AddDouyinGroupBuyingPlanReqDto struct {\n\tPlanId        *string `json:\"planId\"`        // 计划ID\n\tPlanName      *string `json:\"planName\"`      // 计划名称\n\tPrice         *int64  `json:\"price\"`         // 价格\n\tProductInfo   *string `json:\"productInfo\"`   // 产品信息(JSON)\n\tBindingStatus *string `json:\"bindingStatus\"` // 绑定状态\n}", "req_update": "package req\n\n// UpdateDouyinGroupBuyingPlanReqDto 更新抖音团购计划请求DTO\ntype UpdateDouyinGroupBuyingPlanReqDto struct {\n\tId            *string `json:\"id\"`            // 唯一id\n\tPlanId        *string `json:\"planId\"`        // 计划ID\n\tPlanName      *string `json:\"planName\"`      // 计划名称\n\tPrice         *int64  `json:\"price\"`         // 价格\n\tProductInfo   *string `json:\"productInfo\"`   // 产品信息(JSON)\n\tBindingStatus *string `json:\"bindingStatus\"` // 绑定状态\n}", "req_delete": "package req\n\n// DeleteDouyinGroupBuyingPlanReqDto 删除抖音团购计划请求DTO\ntype DeleteDouyinGroupBuyingPlanReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}", "req_query": "package req\n\n// QueryDouyinGroupBuyingPlanReqDto 查询抖音团购计划请求DTO\ntype QueryDouyinGroupBuyingPlanReqDto struct {\n\tId            *string `json:\"id\"`            // 唯一id\n\tPlanId        *string `json:\"planId\"`        // 计划ID\n\tPlanName      *string `json:\"planName\"`      // 计划名称\n\tPrice         *int64  `json:\"price\"`         // 价格\n\tBindingStatus *string `json:\"bindingStatus\"` // 绑定状态\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype DouyinGroupBuyingPlanTransfer struct {\n}\n\nfunc (transfer *DouyinGroupBuyingPlanTransfer) PoToVo(po po.DouyinGroupBuyingPlan) vo.DouyinGroupBuyingPlanVO {\n\tvo := vo.DouyinGroupBuyingPlanVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *DouyinGroupBuyingPlanTransfer) VoToPo(vo vo.DouyinGroupBuyingPlanVO) po.DouyinGroupBuyingPlan {\n\tpo := po.DouyinGroupBuyingPlan{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype DouyinGroupBuyingPlanService struct {\n}\n\nfunc (service *DouyinGroupBuyingPlanService) CreateDouyinGroupBuyingPlan(logCtx *gin.Context, plan *po.DouyinGroupBuyingPlan) error {\n\treturn Save(plan)\n}\n\nfunc (service *DouyinGroupBuyingPlanService) UpdateDouyinGroupBuyingPlan(logCtx *gin.Context, plan *po.DouyinGroupBuyingPlan) error {\n\treturn Update(plan)\n}\n\nfunc (service *DouyinGroupBuyingPlanService) DeleteDouyinGroupBuyingPlan(logCtx *gin.Context, id string) error {\n\treturn Delete(po.DouyinGroupBuyingPlan{Id: &id})\n}\n\nfunc (service *DouyinGroupBuyingPlanService) FindDouyinGroupBuyingPlanById(logCtx *gin.Context, id string) (plan *po.DouyinGroupBuyingPlan, err error) {\n\tplan = &po.DouyinGroupBuyingPlan{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(plan).Error\n\treturn\n}\n\nfunc (service *DouyinGroupBuyingPlanService) FindAllDouyinGroupBuyingPlan(logCtx *gin.Context, reqDto *req.QueryDouyinGroupBuyingPlanReqDto) (list *[]po.DouyinGroupBuyingPlan, err error) {\n\tdb := model.DBSlave.Self.Model(&po.DouyinGroupBuyingPlan{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.DouyinGroupBuyingPlan{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *DouyinGroupBuyingPlanService) FindAllDouyinGroupBuyingPlanWithPagination(logCtx *gin.Context, reqDto *req.QueryDouyinGroupBuyingPlanReqDto) (list *[]po.DouyinGroupBuyingPlan, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.DouyinGroupBuyingPlan{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.DouyinGroupBuyingPlan{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype DouyinGroupBuyingPlanController struct{}\n\nvar (\n\tdouyinGroupBuyingPlanService  = impl.DouyinGroupBuyingPlanService{}\n\tdouyinGroupBuyingPlanTransfer = transfer.DouyinGroupBuyingPlanTransfer{}\n)\n\n// @Summary 添加抖音团购计划\n// @Description 添加抖音团购计划\n// @Tags 抖音团购计划\n// @Accept json\n// @Produce json\n// @Param body body req.AddDouyinGroupBuyingPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.DouyinGroupBuyingPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/douyin-group-buying-plan/add [post]\nfunc (controller *DouyinGroupBuyingPlanController) AddDouyinGroupBuyingPlan(ctx *gin.Context) {\n\treqDto := req.AddDouyinGroupBuyingPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tplan := po.DouyinGroupBuyingPlan{}\n\tif reqDto.PlanId != nil {\n\t\tplan.PlanId = reqDto.PlanId\n\t}\n\tif reqDto.PlanName != nil {\n\t\tplan.PlanName = reqDto.PlanName\n\t}\n\tif reqDto.Price != nil {\n\t\tplan.Price = reqDto.Price\n\t}\n\tif reqDto.ProductInfo != nil {\n\t\tplan.ProductInfo = reqDto.ProductInfo\n\t}\n\tif reqDto.BindingStatus != nil {\n\t\tplan.BindingStatus = reqDto.BindingStatus\n\t}\n\n\terr = douyinGroupBuyingPlanService.CreateDouyinGroupBuyingPlan(ctx, &plan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, douyinGroupBuyingPlanTransfer.PoToVo(plan))\n}\n\n// @Summary 更新抖音团购计划\n// @Description 更新抖音团购计划\n// @Tags 抖音团购计划\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateDouyinGroupBuyingPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.DouyinGroupBuyingPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/douyin-group-buying-plan/update [post]\nfunc (controller *DouyinGroupBuyingPlanController) UpdateDouyinGroupBuyingPlan(ctx *gin.Context) {\n\treqDto := req.UpdateDouyinGroupBuyingPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tplan, err := douyinGroupBuyingPlanService.FindDouyinGroupBuyingPlanById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.PlanId != nil {\n\t\tplan.PlanId = reqDto.PlanId\n\t}\n\tif reqDto.PlanName != nil {\n\t\tplan.PlanName = reqDto.PlanName\n\t}\n\tif reqDto.Price != nil {\n\t\tplan.Price = reqDto.Price\n\t}\n\tif reqDto.ProductInfo != nil {\n\t\tplan.ProductInfo = reqDto.ProductInfo\n\t}\n\tif reqDto.BindingStatus != nil {\n\t\tplan.BindingStatus = reqDto.BindingStatus\n\t}\n\n\terr = douyinGroupBuyingPlanService.UpdateDouyinGroupBuyingPlan(ctx, plan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, douyinGroupBuyingPlanTransfer.PoToVo(*plan))\n}\n\n// @Summary 删除抖音团购计划\n// @Description 删除抖音团购计划\n// @Tags 抖音团购计划\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteDouyinGroupBuyingPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/douyin-group-buying-plan/delete [post]\nfunc (controller *DouyinGroupBuyingPlanController) DeleteDouyinGroupBuyingPlan(ctx *gin.Context) {\n\treqDto := req.DeleteDouyinGroupBuyingPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = douyinGroupBuyingPlanService.DeleteDouyinGroupBuyingPlan(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询抖音团购计划\n// @Description 查询抖音团购计划\n// @Tags 抖音团购计划\n// @Accept json\n// @Produce json\n// @Param body body req.QueryDouyinGroupBuyingPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.DouyinGroupBuyingPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/douyin-group-buying-plan/query [post]\nfunc (controller *DouyinGroupBuyingPlanController) QueryDouyinGroupBuyingPlans(ctx *gin.Context) {\n\treqDto := req.QueryDouyinGroupBuyingPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := douyinGroupBuyingPlanService.FindAllDouyinGroupBuyingPlan(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.DouyinGroupBuyingPlanVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, douyinGroupBuyingPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询抖音团购计划列表\n// @Description 查询抖音团购计划列表\n// @Tags 抖音团购计划\n// @Accept json\n// @Produce json\n// @Param body body req.QueryDouyinGroupBuyingPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.DouyinGroupBuyingPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/douyin-group-buying-plan/list [post]\nfunc (controller *DouyinGroupBuyingPlanController) ListDouyinGroupBuyingPlans(ctx *gin.Context) {\n\treqDto := req.QueryDouyinGroupBuyingPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := douyinGroupBuyingPlanService.FindAllDouyinGroupBuyingPlanWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.DouyinGroupBuyingPlanVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.DouyinGroupBuyingPlanVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, douyinGroupBuyingPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype DouyinGroupBuyingPlanRoute struct {\n}\n\nfunc (s *DouyinGroupBuyingPlanRoute) InitDouyinGroupBuyingPlanRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tdouyinGroupBuyingPlanController := controller.DouyinGroupBuyingPlanController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/douyin-group-buying-plan/add\", douyinGroupBuyingPlanController.AddDouyinGroupBuyingPlan)       // add\n\t\troute.POST(\"/api/douyin-group-buying-plan/update\", douyinGroupBuyingPlanController.UpdateDouyinGroupBuyingPlan)   // update\n\t\troute.POST(\"/api/douyin-group-buying-plan/delete\", douyinGroupBuyingPlanController.DeleteDouyinGroupBuyingPlan)   // delete\n\t\troute.POST(\"/api/douyin-group-buying-plan/query\", douyinGroupBuyingPlanController.QueryDouyinGroupBuyingPlans)    // query\n\t\troute.POST(\"/api/douyin-group-buying-plan/list\", douyinGroupBuyingPlanController.ListDouyinGroupBuyingPlans)     // list\n\t}\n}"}]