[{"po": "package po\n\n// DataCleanup 数据清理实体\ntype DataCleanup struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tDataType    *string `gorm:\"column:data_type;type:varchar(64);default:''\" json:\"dataType\"`    // 数据类型\n\tRequestTime *int64  `gorm:\"column:request_time;type:int;default:0\" json:\"requestTime\"`      // 请求时间\n\tStatus      *string `gorm:\"column:status;type:varchar(64);default:''\" json:\"status\"`        // 状态\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (DataCleanup) TableName() string {\n\treturn \"data_cleanup\"\n}\n\nfunc (d DataCleanup) GetId() string {\n\treturn *d.Id\n}\n", "vo": "package vo\n\n// DataCleanupVO 数据清理值对象\ntype DataCleanupVO struct {\n\tId          string `json:\"id\"`          // ID\n\tDataType    string `json:\"dataType\"`    // 数据类型\n\tRequestTime int64  `json:\"requestTime\"` // 请求时间\n\tStatus      string `json:\"status\"`      // 状态\n\tCtime       int64  `json:\"ctime\"`       // 创建时间\n\tUtime       int64  `json:\"utime\"`       // 更新时间\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddDataCleanupReqDto 创建数据清理请求DTO\ntype AddDataCleanupReqDto struct {\n\tDataType    *string `json:\"dataType\"`    // 数据类型\n\tRequestTime *int64  `json:\"requestTime\"` // 请求时间\n\tStatus      *string `json:\"status\"`      // 状态\n}\n", "req_update": "package req\n\ntype UpdateDataCleanupReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tDataType    *string `json:\"dataType\"`    // 数据类型\n\tRequestTime *int64  `json:\"requestTime\"` // 请求时间\n\tStatus      *string `json:\"status\"`      // 状态\n}\n", "req_delete": "package req\n\ntype DeleteDataCleanupReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryDataCleanupReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tDataType    *string `json:\"dataType\"`    // 数据类型\n\tRequestTime *int64  `json:\"requestTime\"` // 请求时间\n\tStatus      *string `json:\"status\"`      // 状态\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype DataCleanupTransfer struct {\n}\n\nfunc (transfer *DataCleanupTransfer) PoToVo(po po.DataCleanup) vo.DataCleanupVO {\n\tvo := vo.DataCleanupVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *DataCleanupTransfer) VoToPo(vo vo.DataCleanupVO) po.DataCleanup {\n\tpo := po.DataCleanup{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype DataCleanupService struct {\n}\n\nfunc (service *DataCleanupService) CreateDataCleanup(logCtx *gin.Context, dataCleanup *po.DataCleanup) error {\n\treturn Save(dataCleanup)\n}\n\nfunc (service *DataCleanupService) UpdateDataCleanup(logCtx *gin.Context, dataCleanup *po.DataCleanup) error {\n\treturn Update(dataCleanup)\n}\n\nfunc (service *DataCleanupService) DeleteDataCleanup(logCtx *gin.Context, id string) error {\n\treturn Delete(po.DataCleanup{Id: &id})\n}\n\nfunc (service *DataCleanupService) FindDataCleanupById(logCtx *gin.Context, id string) (dataCleanup *po.DataCleanup, err error) {\n\tdataCleanup = &po.DataCleanup{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(dataCleanup).Error\n\treturn\n}\n\nfunc (service *DataCleanupService) FindAllDataCleanup(logCtx *gin.Context, reqDto *req.QueryDataCleanupReqDto) (list *[]po.DataCleanup, err error) {\n\tdb := model.DBSlave.Self.Model(&po.DataCleanup{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.DataCleanup{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *DataCleanupService) FindAllDataCleanupWithPagination(logCtx *gin.Context, reqDto *req.QueryDataCleanupReqDto) (list *[]po.DataCleanup, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.DataCleanup{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.DataCleanup{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype DataCleanupController struct{}\n\nvar (\n\tdataCleanupService  = impl.DataCleanupService{}\n\tdataCleanupTransfer = transfer.DataCleanupTransfer{}\n)\n\n// @Summary 添加数据清理\n// @Description 添加数据清理\n// @Tags 数据清理\n// @Accept json\n// @Produce json\n// @Param body body req.AddDataCleanupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.DataCleanupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/dataCleanup/add [post]\nfunc (controller *DataCleanupController) AddDataCleanup(ctx *gin.Context) {\n\treqDto := req.AddDataCleanupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tdataCleanup := po.DataCleanup{}\n\tif reqDto.DataType != nil {\n\t\tdataCleanup.DataType = reqDto.DataType\n\t}\n\tif reqDto.RequestTime != nil {\n\t\tdataCleanup.RequestTime = reqDto.RequestTime\n\t}\n\tif reqDto.Status != nil {\n\t\tdataCleanup.Status = reqDto.Status\n\t}\n\terr = dataCleanupService.CreateDataCleanup(ctx, &dataCleanup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, dataCleanupTransfer.PoToVo(dataCleanup))\n}\n\n// @Summary 更新数据清理\n// @Description 更新数据清理\n// @Tags 数据清理\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateDataCleanupReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.DataCleanupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/dataCleanup/update [post]\nfunc (controller *DataCleanupController) UpdateDataCleanup(ctx *gin.Context) {\n\treqDto := req.UpdateDataCleanupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tdataCleanup, err := dataCleanupService.FindDataCleanupById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.DataType != nil {\n\t\tdataCleanup.DataType = reqDto.DataType\n\t}\n\tif reqDto.RequestTime != nil {\n\t\tdataCleanup.RequestTime = reqDto.RequestTime\n\t}\n\tif reqDto.Status != nil {\n\t\tdataCleanup.Status = reqDto.Status\n\t}\n\terr = dataCleanupService.UpdateDataCleanup(ctx, dataCleanup)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, dataCleanupTransfer.PoToVo(*dataCleanup))\n}\n\n// @Summary 删除数据清理\n// @Description 删除数据清理\n// @Tags 数据清理\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteDataCleanupReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/dataCleanup/delete [post]\nfunc (controller *DataCleanupController) DeleteDataCleanup(ctx *gin.Context) {\n\treqDto := req.DeleteDataCleanupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = dataCleanupService.DeleteDataCleanup(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询数据清理\n// @Description 查询数据清理\n// @Tags 数据清理\n// @Accept json\n// @Produce json\n// @Param body body req.QueryDataCleanupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.DataCleanupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/dataCleanup/query [post]\nfunc (controller *DataCleanupController) QueryDataCleanups(ctx *gin.Context) {\n\treqDto := req.QueryDataCleanupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := dataCleanupService.FindAllDataCleanup(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.DataCleanupVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, dataCleanupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询数据清理列表\n// @Description 查询数据清理列表\n// @Tags 数据清理\n// @Accept json\n// @Produce json\n// @Param body body req.QueryDataCleanupReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.DataCleanupVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/dataCleanup/list [post]\nfunc (a *DataCleanupController) ListDataCleanups(ctx *gin.Context) {\n\treqDto := req.QueryDataCleanupReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := dataCleanupService.FindAllDataCleanupWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.DataCleanupVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.DataCleanupVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, dataCleanupTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype DataCleanupRoute struct {\n}\n\nfunc (s *DataCleanupRoute) InitDataCleanupRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tdataCleanupController := controller.DataCleanupController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/dataCleanup/add\", dataCleanupController.AddDataCleanup)    //add\n\t\troute.POST(\"/api/dataCleanup/update\", dataCleanupController.UpdateDataCleanup) //update\n\t\troute.POST(\"/api/dataCleanup/delete\", dataCleanupController.DeleteDataCleanup) //delete\n\t\troute.POST(\"/api/dataCleanup/query\", dataCleanupController.QueryDataCleanups)     //query\n\t\troute.POST(\"/api/dataCleanup/list\", dataCleanupController.ListDataCleanups)     //list\n\t}\n}\n"}]