[{"po": "package po\n\n// ProductOutType 商品出库类型实体\ntype ProductOutType struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // 唯一id\n\tName         *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                 // 出库类型名称\n\tArea         *string `gorm:\"column:area;type:text;default:''\" json:\"area\"`                         // 适用区域，JSON格式\n\tProductTypes *string `gorm:\"column:product_types;type:text;default:''\" json:\"productTypes\"`         // 适用商品类型，JSON格式\n\tWarehouse    *string `gorm:\"column:warehouse;type:text;default:''\" json:\"warehouse\"`               // 出库仓库，JSON格式\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                         // 创建时间戳\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                         // 更新时间戳\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                         // 状态值\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                     // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductOutType) TableName() string {\n\treturn \"product_out_type\"\n}\n\nfunc (p ProductOutType) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductOutTypeVO 商品出库类型值对象\ntype ProductOutTypeVO struct {\n\tId           string `json:\"id\"`           // 唯一id\n\tName         string `json:\"name\"`         // 出库类型名称\n\tArea         string `json:\"area\"`         // 适用区域，JSON格式\n\tProductTypes string `json:\"productTypes\"` // 适用商品类型，JSON格式\n\tWarehouse    string `json:\"warehouse\"`    // 出库仓库，JSON格式\n\tCtime        int64  `json:\"ctime\"`        // 创建时间戳\n\tUtime        int64  `json:\"utime\"`        // 更新时间戳\n\tState        int    `json:\"state\"`        // 状态值\n\tVersion      int    `json:\"version\"`      // 版本号\n}\n", "req_add": "package req\n\n// AddProductOutTypeReqDto 创建商品出库类型请求DTO\ntype AddProductOutTypeReqDto struct {\n\tName         *string `json:\"name\"`         // 出库类型名称\n\tArea         *string `json:\"area\"`         // 适用区域，JSON格式\n\tProductTypes *string `json:\"productTypes\"` // 适用商品类型，JSON格式\n\tWarehouse    *string `json:\"warehouse\"`    // 出库仓库，JSON格式\n}\n", "req_update": "package req\n\ntype UpdateProductOutTypeReqDto struct {\n\tId           *string `json:\"id\"`           // 唯一id\n\tName         *string `json:\"name\"`         // 出库类型名称\n\tArea         *string `json:\"area\"`         // 适用区域，JSON格式\n\tProductTypes *string `json:\"productTypes\"` // 适用商品类型，JSON格式\n\tWarehouse    *string `json:\"warehouse\"`    // 出库仓库，JSON格式\n}\n", "req_delete": "package req\n\ntype DeleteProductOutTypeReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryProductOutTypeReqDto struct {\n\tId           *string `json:\"id\"`           // 唯一id\n\tName         *string `json:\"name\"`         // 出库类型名称\n\tArea         *string `json:\"area\"`         // 适用区域，JSON格式\n\tProductTypes *string `json:\"productTypes\"` // 适用商品类型，JSON格式\n\tWarehouse    *string `json:\"warehouse\"`    // 出库仓库，JSON格式\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductOutTypeTransfer struct {\n}\n\nfunc (transfer *ProductOutTypeTransfer) PoToVo(po po.ProductOutType) vo.ProductOutTypeVO {\n\tvo := vo.ProductOutTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductOutTypeTransfer) VoToPo(vo vo.ProductOutTypeVO) po.ProductOutType {\n\tpo := po.ProductOutType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductOutTypeService struct {\n}\n\nfunc (service *ProductOutTypeService) CreateProductOutType(logCtx *gin.Context, productOutType *po.ProductOutType) error {\n\treturn Save(productOutType)\n}\n\nfunc (service *ProductOutTypeService) UpdateProductOutType(logCtx *gin.Context, productOutType *po.ProductOutType) error {\n\treturn Update(productOutType)\n}\n\nfunc (service *ProductOutTypeService) DeleteProductOutType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductOutType{Id: &id})\n}\n\nfunc (service *ProductOutTypeService) FindProductOutTypeById(logCtx *gin.Context, id string) (productOutType *po.ProductOutType, err error) {\n\tproductOutType = &po.ProductOutType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productOutType).Error\n\treturn\n}\n\nfunc (service *ProductOutTypeService) FindAllProductOutType(logCtx *gin.Context, reqDto *req.QueryProductOutTypeReqDto) (list *[]po.ProductOutType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductOutType{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Area != nil && *reqDto.Area != \"\" {\n\t\tdb = db.Where(\"area LIKE ?\", \"%\"+*reqDto.Area+\"%\")\n\t}\n\tif reqDto.ProductTypes != nil && *reqDto.ProductTypes != \"\" {\n\t\tdb = db.Where(\"product_types LIKE ?\", \"%\"+*reqDto.ProductTypes+\"%\")\n\t}\n\tif reqDto.Warehouse != nil && *reqDto.Warehouse != \"\" {\n\t\tdb = db.Where(\"warehouse LIKE ?\", \"%\"+*reqDto.Warehouse+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductOutType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductOutTypeService) FindAllProductOutTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryProductOutTypeReqDto) (list *[]po.ProductOutType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductOutType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Area != nil && *reqDto.Area != \"\" {\n\t\tdb = db.Where(\"area LIKE ?\", \"%\"+*reqDto.Area+\"%\")\n\t}\n\tif reqDto.ProductTypes != nil && *reqDto.ProductTypes != \"\" {\n\t\tdb = db.Where(\"product_types LIKE ?\", \"%\"+*reqDto.ProductTypes+\"%\")\n\t}\n\tif reqDto.Warehouse != nil && *reqDto.Warehouse != \"\" {\n\t\tdb = db.Where(\"warehouse LIKE ?\", \"%\"+*reqDto.Warehouse+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductOutType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductOutTypeController struct{}\n\nvar (\n\tproductOutTypeService  = impl.ProductOutTypeService{}\n\tproductOutTypeTransfer = transfer.ProductOutTypeTransfer{}\n)\n\n// @Summary 添加商品出库类型\n// @Description 添加商品出库类型\n// @Tags 商品出库类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductOutTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductOutTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productOutType/add [post]\nfunc (controller *ProductOutTypeController) AddProductOutType(ctx *gin.Context) {\n\treqDto := req.AddProductOutTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductOutType := po.ProductOutType{}\n\tif reqDto.Name != nil {\n\t\tproductOutType.Name = reqDto.Name\n\t}\n\tif reqDto.Area != nil {\n\t\tproductOutType.Area = reqDto.Area\n\t}\n\tif reqDto.ProductTypes != nil {\n\t\tproductOutType.ProductTypes = reqDto.ProductTypes\n\t}\n\tif reqDto.Warehouse != nil {\n\t\tproductOutType.Warehouse = reqDto.Warehouse\n\t}\n\n\terr = productOutTypeService.CreateProductOutType(ctx, &productOutType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productOutTypeTransfer.PoToVo(productOutType))\n}\n\n// @Summary 更新商品出库类型\n// @Description 更新商品出库类型\n// @Tags 商品出库类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductOutTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductOutTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productOutType/update [post]\nfunc (controller *ProductOutTypeController) UpdateProductOutType(ctx *gin.Context) {\n\treqDto := req.UpdateProductOutTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tproductOutType, err := productOutTypeService.FindProductOutTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tproductOutType.Name = reqDto.Name\n\t}\n\tif reqDto.Area != nil {\n\t\tproductOutType.Area = reqDto.Area\n\t}\n\tif reqDto.ProductTypes != nil {\n\t\tproductOutType.ProductTypes = reqDto.ProductTypes\n\t}\n\tif reqDto.Warehouse != nil {\n\t\tproductOutType.Warehouse = reqDto.Warehouse\n\t}\n\n\terr = productOutTypeService.UpdateProductOutType(ctx, productOutType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productOutTypeTransfer.PoToVo(*productOutType))\n}\n\n// @Summary 删除商品出库类型\n// @Description 删除商品出库类型\n// @Tags 商品出库类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductOutTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productOutType/delete [post]\nfunc (controller *ProductOutTypeController) DeleteProductOutType(ctx *gin.Context) {\n\treqDto := req.DeleteProductOutTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = productOutTypeService.DeleteProductOutType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询商品出库类型\n// @Description 查询商品出库类型\n// @Tags 商品出库类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductOutTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductOutTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productOutType/query [post]\nfunc (controller *ProductOutTypeController) QueryProductOutTypes(ctx *gin.Context) {\n\treqDto := req.QueryProductOutTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := productOutTypeService.FindAllProductOutType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ProductOutTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productOutTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询商品出库类型列表\n// @Description 查询商品出库类型列表\n// @Tags 商品出库类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductOutTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductOutTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productOutType/list [post]\nfunc (a *ProductOutTypeController) ListProductOutTypes(ctx *gin.Context) {\n\treqDto := req.QueryProductOutTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := productOutTypeService.FindAllProductOutTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ProductOutTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductOutTypeVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productOutTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductOutTypeRoute struct {\n}\n\nfunc (s *ProductOutTypeRoute) InitProductOutTypeRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductOutTypeController := controller.ProductOutTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productOutType/add\", productOutTypeController.AddProductOutType)    //add\n\t\troute.POST(\"/api/productOutType/update\", productOutTypeController.UpdateProductOutType) //update\n\t\troute.POST(\"/api/productOutType/delete\", productOutTypeController.DeleteProductOutType) //delete\n\t\troute.POST(\"/api/productOutType/query\", productOutTypeController.QueryProductOutTypes)     //query\n\t\troute.POST(\"/api/productOutType/list\", productOutTypeController.ListProductOutTypes)     //list\n\t}\n}\n"}]