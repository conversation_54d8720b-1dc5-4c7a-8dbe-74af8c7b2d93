[{"po": "package po\n\n// CashierMachine 收银机实体\ntype CashierMachine struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tIpAddress   *string `gorm:\"column:ip_address;type:varchar(64);default:''\" json:\"ipAddress\"`     // IP地址\n\tVodServerIP *string `gorm:\"column:vod_server_ip;type:varchar(64);default:''\" json:\"vodServerIP\"` // 点歌服务器IP\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (CashierMachine) TableName() string {\n\treturn \"cashier_machine\"\n}\n\nfunc (c CashierMachine) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CashierMachineVO 收银机信息值对象\ntype CashierMachineVO struct {\n\tId          string `json:\"id\"`          // ID\n\tIpAddress   string `json:\"ipAddress\"`   // IP地址\n\tVodServerIP string `json:\"vodServerIP\"` // 点歌服务器IP\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddCashierMachineReqDto 创建收银机请求DTO\ntype AddCashierMachineReqDto struct {\n\tIpAddress   *string `json:\"ipAddress\"`   // IP地址\n\tVodServerIP *string `json:\"vodServerIP\"` // 点歌服务器IP\n}\n", "req_update": "package req\n\ntype UpdateCashierMachineReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tIpAddress   *string `json:\"ipAddress\"`   // IP地址\n\tVodServerIP *string `json:\"vodServerIP\"` // 点歌服务器IP\n}\n", "req_delete": "package req\n\ntype DeleteCashierMachineReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCashierMachineReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tIpAddress   *string `json:\"ipAddress\"`   // IP地址\n\tVodServerIP *string `json:\"vodServerIP\"` // 点歌服务器IP\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CashierMachineTransfer struct {\n}\n\nfunc (transfer *CashierMachineTransfer) PoToVo(po po.CashierMachine) vo.CashierMachineVO {\n\tvo := vo.CashierMachineVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CashierMachineTransfer) VoToPo(vo vo.CashierMachineVO) po.CashierMachine {\n\tpo := po.CashierMachine{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CashierMachineService struct {\n}\n\nfunc (service *CashierMachineService) CreateCashierMachine(logCtx *gin.Context, cashierMachine *po.CashierMachine) error {\n\treturn Save(cashierMachine)\n}\n\nfunc (service *CashierMachineService) UpdateCashierMachine(logCtx *gin.Context, cashierMachine *po.CashierMachine) error {\n\treturn Update(cashierMachine)\n}\n\nfunc (service *CashierMachineService) DeleteCashierMachine(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CashierMachine{Id: &id})\n}\n\nfunc (service *CashierMachineService) FindCashierMachineById(logCtx *gin.Context, id string) (cashierMachine *po.CashierMachine, err error) {\n\tcashierMachine = &po.CashierMachine{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(cashierMachine).Error\n\treturn\n}\n\nfunc (service *CashierMachineService) FindAllCashierMachine(logCtx *gin.Context, reqDto *req.QueryCashierMachineReqDto) (list *[]po.CashierMachine, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CashierMachine{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CashierMachine{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CashierMachineService) FindAllCashierMachineWithPagination(logCtx *gin.Context, reqDto *req.QueryCashierMachineReqDto) (list *[]po.CashierMachine, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CashierMachine{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CashierMachine{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CashierMachineController struct{}\n\nvar (\n\tcashierMachineService  = impl.CashierMachineService{}\n\tcashierMachineTransfer = transfer.CashierMachineTransfer{}\n)\n\n// @Summary 添加收银机\n// @Description 添加收银机\n// @Tags 收银机\n// @Accept json\n// @Produce json\n// @Param body body req.AddCashierMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CashierMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-machine/add [post]\nfunc (controller *CashierMachineController) AddCashierMachine(ctx *gin.Context) {\n\treqDto := req.AddCashierMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcashierMachine := po.CashierMachine{}\n\tif reqDto.IpAddress != nil {\n\t\tcashierMachine.IpAddress = reqDto.IpAddress\n\t}\n\tif reqDto.VodServerIP != nil {\n\t\tcashierMachine.VodServerIP = reqDto.VodServerIP\n\t}\n\terr = cashierMachineService.CreateCashierMachine(ctx, &cashierMachine)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, cashierMachineTransfer.PoToVo(cashierMachine))\n}\n\n// @Summary 更新收银机\n// @Description 更新收银机\n// @Tags 收银机\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCashierMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CashierMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-machine/update [post]\nfunc (controller *CashierMachineController) UpdateCashierMachine(ctx *gin.Context) {\n\treqDto := req.UpdateCashierMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcashierMachine, err := cashierMachineService.FindCashierMachineById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.IpAddress != nil {\n\t\tcashierMachine.IpAddress = reqDto.IpAddress\n\t}\n\tif reqDto.VodServerIP != nil {\n\t\tcashierMachine.VodServerIP = reqDto.VodServerIP\n\t}\n\terr = cashierMachineService.UpdateCashierMachine(ctx, cashierMachine)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, cashierMachineTransfer.PoToVo(*cashierMachine))\n}\n\n// @Summary 删除收银机\n// @Description 删除收银机\n// @Tags 收银机\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCashierMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-machine/delete [post]\nfunc (controller *CashierMachineController) DeleteCashierMachine(ctx *gin.Context) {\n\treqDto := req.DeleteCashierMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = cashierMachineService.DeleteCashierMachine(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询收银机\n// @Description 查询收银机\n// @Tags 收银机\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCashierMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CashierMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-machine/query [post]\nfunc (controller *CashierMachineController) QueryCashierMachines(ctx *gin.Context) {\n\treqDto := req.QueryCashierMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := cashierMachineService.FindAllCashierMachine(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CashierMachineVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, cashierMachineTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询收银机列表\n// @Description 查询收银机列表\n// @Tags 收银机\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCashierMachineReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CashierMachineVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-machine/list [post]\nfunc (a *CashierMachineController) ListCashierMachines(ctx *gin.Context) {\n\treqDto := req.QueryCashierMachineReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := cashierMachineService.FindAllCashierMachineWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CashierMachineVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CashierMachineVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, cashierMachineTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CashierMachineRoute struct {\n}\n\nfunc (s *CashierMachineRoute) InitCashierMachineRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcashierMachineController := controller.CashierMachineController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/cashier-machine/add\", cashierMachineController.AddCashierMachine)       //add\n\t\troute.POST(\"/api/cashier-machine/update\", cashierMachineController.UpdateCashierMachine)   //update\n\t\troute.POST(\"/api/cashier-machine/delete\", cashierMachineController.DeleteCashierMachine)   //delete\n\t\troute.POST(\"/api/cashier-machine/query\", cashierMachineController.QueryCashierMachines)    //query\n\t\troute.POST(\"/api/cashier-machine/list\", cashierMachineController.ListCashierMachines)      //list\n\t}\n}\n"}]