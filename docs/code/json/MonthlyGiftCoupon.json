[{"po": "package po\n\n// MonthlyGiftCoupon 月度礼品券实体\ntype MonthlyGiftCoupon struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tGiftQuantity *int    `gorm:\"column:gift_quantity;type:int;default:0\" json:\"giftQuantity\"` // 赠送数量\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                // 创建时间戳\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                // 更新时间戳\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                // 状态值\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`            // 版本号\n}\n\n// TableName 设置表名\nfunc (MonthlyGiftCoupon) TableName() string {\n\treturn \"monthly_gift_coupon\"\n}\n\nfunc (m MonthlyGiftCoupon) GetId() string {\n\treturn *m.Id\n}", "vo": "package vo\n\n// MonthlyGiftCouponVO 月度礼品券值对象\ntype MonthlyGiftCouponVO struct {\n\tId           string `json:\"id\"`           // ID\n\tGiftQuantity int    `json:\"giftQuantity\"` // 赠送数量\n\tCtime        int64  `json:\"ctime\"`        // 创建时间戳\n\tUtime        int64  `json:\"utime\"`        // 更新时间戳\n\tState        int    `json:\"state\"`        // 状态值\n\tVersion      int    `json:\"version\"`      // 版本号\n}", "req_add": "package req\n\n// AddMonthlyGiftCouponReqDto 创建月度礼品券请求DTO\ntype AddMonthlyGiftCouponReqDto struct {\n\tGiftQuantity *int `json:\"giftQuantity\"` // 赠送数量\n}", "req_update": "package req\n\ntype UpdateMonthlyGiftCouponReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tGiftQuantity *int    `json:\"giftQuantity\"` // 赠送数量\n}", "req_delete": "package req\n\ntype DeleteMonthlyGiftCouponReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryMonthlyGiftCouponReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tGiftQuantity *int    `json:\"giftQuantity\"` // 赠送数量\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MonthlyGiftCouponTransfer struct {\n}\n\nfunc (transfer *MonthlyGiftCouponTransfer) PoToVo(po po.MonthlyGiftCoupon) vo.MonthlyGiftCouponVO {\n\tvo := vo.MonthlyGiftCouponVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MonthlyGiftCouponTransfer) VoToPo(vo vo.MonthlyGiftCouponVO) po.MonthlyGiftCoupon {\n\tpo := po.MonthlyGiftCoupon{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MonthlyGiftCouponService struct {\n}\n\nfunc (service *MonthlyGiftCouponService) CreateMonthlyGiftCoupon(logCtx *gin.Context, monthlyGiftCoupon *po.MonthlyGiftCoupon) error {\n\treturn Save(monthlyGiftCoupon)\n}\n\nfunc (service *MonthlyGiftCouponService) UpdateMonthlyGiftCoupon(logCtx *gin.Context, monthlyGiftCoupon *po.MonthlyGiftCoupon) error {\n\treturn Update(monthlyGiftCoupon)\n}\n\nfunc (service *MonthlyGiftCouponService) DeleteMonthlyGiftCoupon(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MonthlyGiftCoupon{Id: &id})\n}\n\nfunc (service *MonthlyGiftCouponService) FindMonthlyGiftCouponById(logCtx *gin.Context, id string) (monthlyGiftCoupon *po.MonthlyGiftCoupon, err error) {\n\tmonthlyGiftCoupon = &po.MonthlyGiftCoupon{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(monthlyGiftCoupon).Error\n\treturn\n}\n\nfunc (service *MonthlyGiftCouponService) FindAllMonthlyGiftCoupon(logCtx *gin.Context, reqDto *req.QueryMonthlyGiftCouponReqDto) (list *[]po.MonthlyGiftCoupon, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MonthlyGiftCoupon{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MonthlyGiftCoupon{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MonthlyGiftCouponService) FindAllMonthlyGiftCouponWithPagination(logCtx *gin.Context, reqDto *req.QueryMonthlyGiftCouponReqDto) (list *[]po.MonthlyGiftCoupon, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MonthlyGiftCoupon{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MonthlyGiftCoupon{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MonthlyGiftCouponController struct{}\n\nvar (\n\tmonthlyGiftCouponService  = impl.MonthlyGiftCouponService{}\n\tmonthlyGiftCouponTransfer = transfer.MonthlyGiftCouponTransfer{}\n)\n\n// @Summary 添加月度礼品券\n// @Description 添加月度礼品券\n// @Tags 月度礼品券\n// @Accept json\n// @Produce json\n// @Param body body req.AddMonthlyGiftCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MonthlyGiftCouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/monthly-gift-coupon/add [post]\nfunc (controller *MonthlyGiftCouponController) AddMonthlyGiftCoupon(ctx *gin.Context) {\n\treqDto := req.AddMonthlyGiftCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmonthlyGiftCoupon := po.MonthlyGiftCoupon{}\n\tif reqDto.GiftQuantity != nil {\n\t\tmonthlyGiftCoupon.GiftQuantity = reqDto.GiftQuantity\n\t}\n\terr = monthlyGiftCouponService.CreateMonthlyGiftCoupon(ctx, &monthlyGiftCoupon)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, monthlyGiftCouponTransfer.PoToVo(monthlyGiftCoupon))\n}\n\n// @Summary 更新月度礼品券\n// @Description 更新月度礼品券\n// @Tags 月度礼品券\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMonthlyGiftCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MonthlyGiftCouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/monthly-gift-coupon/update [post]\nfunc (controller *MonthlyGiftCouponController) UpdateMonthlyGiftCoupon(ctx *gin.Context) {\n\treqDto := req.UpdateMonthlyGiftCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmonthlyGiftCoupon, err := monthlyGiftCouponService.FindMonthlyGiftCouponById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.GiftQuantity != nil {\n\t\tmonthlyGiftCoupon.GiftQuantity = reqDto.GiftQuantity\n\t}\n\terr = monthlyGiftCouponService.UpdateMonthlyGiftCoupon(ctx, monthlyGiftCoupon)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, monthlyGiftCouponTransfer.PoToVo(*monthlyGiftCoupon))\n}\n\n// @Summary 删除月度礼品券\n// @Description 删除月度礼品券\n// @Tags 月度礼品券\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMonthlyGiftCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/monthly-gift-coupon/delete [post]\nfunc (controller *MonthlyGiftCouponController) DeleteMonthlyGiftCoupon(ctx *gin.Context) {\n\treqDto := req.DeleteMonthlyGiftCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = monthlyGiftCouponService.DeleteMonthlyGiftCoupon(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询月度礼品券\n// @Description 查询月度礼品券\n// @Tags 月度礼品券\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMonthlyGiftCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MonthlyGiftCouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/monthly-gift-coupon/query [post]\nfunc (controller *MonthlyGiftCouponController) QueryMonthlyGiftCoupons(ctx *gin.Context) {\n\treqDto := req.QueryMonthlyGiftCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := monthlyGiftCouponService.FindAllMonthlyGiftCoupon(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MonthlyGiftCouponVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, monthlyGiftCouponTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询月度礼品券列表\n// @Description 查询月度礼品券列表\n// @Tags 月度礼品券\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMonthlyGiftCouponReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MonthlyGiftCouponVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/monthly-gift-coupon/list [post]\nfunc (a *MonthlyGiftCouponController) ListMonthlyGiftCoupons(ctx *gin.Context) {\n\treqDto := req.QueryMonthlyGiftCouponReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := monthlyGiftCouponService.FindAllMonthlyGiftCouponWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MonthlyGiftCouponVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MonthlyGiftCouponVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, monthlyGiftCouponTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MonthlyGiftCouponRoute struct {\n}\n\nfunc (s *MonthlyGiftCouponRoute) InitMonthlyGiftCouponRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmonthlyGiftCouponController := controller.MonthlyGiftCouponController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/monthly-gift-coupon/add\", monthlyGiftCouponController.AddMonthlyGiftCoupon)       //add\n\t\troute.POST(\"/api/monthly-gift-coupon/update\", monthlyGiftCouponController.UpdateMonthlyGiftCoupon)   //update\n\t\troute.POST(\"/api/monthly-gift-coupon/delete\", monthlyGiftCouponController.DeleteMonthlyGiftCoupon)   //delete\n\t\troute.POST(\"/api/monthly-gift-coupon/query\", monthlyGiftCouponController.QueryMonthlyGiftCoupons)    //query\n\t\troute.POST(\"/api/monthly-gift-coupon/list\", monthlyGiftCouponController.ListMonthlyGiftCoupons)     //list\n\t}\n}"}]