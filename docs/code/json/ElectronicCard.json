[{"po": "package po\n\n// ElectronicCard 电子卡实体\ntype ElectronicCard struct {\n\tId         *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // 唯一id\n\tCardNumber *string `gorm:\"column:card_number;type:varchar(64);default:''\" json:\"cardNumber\"` // 卡号\n\tCtime      *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间\n\tUtime      *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (ElectronicCard) TableName() string {\n\treturn \"electronic_card\"\n}\n\nfunc (e ElectronicCard) GetId() string {\n\treturn *e.Id\n}", "vo": "package vo\n\n// ElectronicCardVO 电子卡值对象\ntype ElectronicCardVO struct {\n\tId         string `json:\"id\"`         // 唯一id\n\tCardNumber string `json:\"cardNumber\"` // 卡号\n\tCtime      int64  `json:\"ctime\"`      // 创建时间\n\tUtime      int64  `json:\"utime\"`      // 更新时间\n\tState      int    `json:\"state\"`      // 状态\n\tVersion    int    `json:\"version\"`    // 版本号\n}", "req_add": "package req\n\n// AddElectronicCardReqDto 创建电子卡请求DTO\ntype AddElectronicCardReqDto struct {\n\tCardNumber *string `json:\"cardNumber\"` // 卡号\n}", "req_update": "package req\n\ntype UpdateElectronicCardReqDto struct {\n\tId         *string `json:\"id\"`         // 唯一id\n\tCardNumber *string `json:\"cardNumber\"` // 卡号\n}", "req_delete": "package req\n\ntype DeleteElectronicCardReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}", "req_query": "package req\n\ntype QueryElectronicCardReqDto struct {\n\tId         *string `json:\"id\"`         // 唯一id\n\tCardNumber *string `json:\"cardNumber\"` // 卡号\n\tPageNum    *int    `json:\"pageNum\"`    // 页码\n\tPageSize   *int    `json:\"pageSize\"`   // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ElectronicCardTransfer struct {\n}\n\nfunc (transfer *ElectronicCardTransfer) PoToVo(po po.ElectronicCard) vo.ElectronicCardVO {\n\tvo := vo.ElectronicCardVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ElectronicCardTransfer) VoToPo(vo vo.ElectronicCardVO) po.ElectronicCard {\n\tpo := po.ElectronicCard{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ElectronicCardService struct {\n}\n\nfunc (service *ElectronicCardService) CreateElectronicCard(logCtx *gin.Context, electronicCard *po.ElectronicCard) error {\n\treturn Save(electronicCard)\n}\n\nfunc (service *ElectronicCardService) UpdateElectronicCard(logCtx *gin.Context, electronicCard *po.ElectronicCard) error {\n\treturn Update(electronicCard)\n}\n\nfunc (service *ElectronicCardService) DeleteElectronicCard(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ElectronicCard{Id: &id})\n}\n\nfunc (service *ElectronicCardService) FindElectronicCardById(logCtx *gin.Context, id string) (electronicCard *po.ElectronicCard, err error) {\n\telectronicCard = &po.ElectronicCard{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(electronicCard).Error\n\treturn\n}\n\nfunc (service *ElectronicCardService) FindAllElectronicCard(logCtx *gin.Context, reqDto *req.QueryElectronicCardReqDto) (list *[]po.ElectronicCard, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ElectronicCard{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ElectronicCard{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ElectronicCardService) FindAllElectronicCardWithPagination(logCtx *gin.Context, reqDto *req.QueryElectronicCardReqDto) (list *[]po.ElectronicCard, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ElectronicCard{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ElectronicCard{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ElectronicCardController struct{}\n\nvar (\n\telectronicCardService  = impl.ElectronicCardService{}\n\telectronicCardTransfer = transfer.ElectronicCardTransfer{}\n)\n\n// @Summary 添加电子卡\n// @Description 添加电子卡\n// @Tags 电子卡\n// @Accept json\n// @Produce json\n// @Param body body req.AddElectronicCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ElectronicCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/electronic-card/add [post]\nfunc (controller *ElectronicCardController) AddElectronicCard(ctx *gin.Context) {\n\treqDto := req.AddElectronicCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\telectronicCard := po.ElectronicCard{}\n\tif reqDto.CardNumber != nil {\n\t\telectronicCard.CardNumber = reqDto.CardNumber\n\t}\n\n\terr = electronicCardService.CreateElectronicCard(ctx, &electronicCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, electronicCardTransfer.PoToVo(electronicCard))\n}\n\n// @Summary 更新电子卡\n// @Description 更新电子卡\n// @Tags 电子卡\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateElectronicCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ElectronicCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/electronic-card/update [post]\nfunc (controller *ElectronicCardController) UpdateElectronicCard(ctx *gin.Context) {\n\treqDto := req.UpdateElectronicCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\telectronicCard, err := electronicCardService.FindElectronicCardById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CardNumber != nil {\n\t\telectronicCard.CardNumber = reqDto.CardNumber\n\t}\n\n\terr = electronicCardService.UpdateElectronicCard(ctx, electronicCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, electronicCardTransfer.PoToVo(*electronicCard))\n}\n\n// @Summary 删除电子卡\n// @Description 删除电子卡\n// @Tags 电子卡\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteElectronicCardReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/electronic-card/delete [post]\nfunc (controller *ElectronicCardController) DeleteElectronicCard(ctx *gin.Context) {\n\treqDto := req.DeleteElectronicCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = electronicCardService.DeleteElectronicCard(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询电子卡\n// @Description 查询电子卡\n// @Tags 电子卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryElectronicCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ElectronicCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/electronic-card/query [post]\nfunc (controller *ElectronicCardController) QueryElectronicCards(ctx *gin.Context) {\n\treqDto := req.QueryElectronicCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := electronicCardService.FindAllElectronicCard(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ElectronicCardVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, electronicCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询电子卡列表\n// @Description 查询电子卡列表\n// @Tags 电子卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryElectronicCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ElectronicCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/electronic-card/list [post]\nfunc (a *ElectronicCardController) ListElectronicCards(ctx *gin.Context) {\n\treqDto := req.QueryElectronicCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := electronicCardService.FindAllElectronicCardWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ElectronicCardVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ElectronicCardVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, electronicCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ElectronicCardRoute struct {\n}\n\nfunc (s *ElectronicCardRoute) InitElectronicCardRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\telectronicCardController := controller.ElectronicCardController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/electronic-card/add\", electronicCardController.AddElectronicCard)       //add\n\t\troute.POST(\"/api/electronic-card/update\", electronicCardController.UpdateElectronicCard) //update\n\t\troute.POST(\"/api/electronic-card/delete\", electronicCardController.DeleteElectronicCard) //delete\n\t\troute.POST(\"/api/electronic-card/query\", electronicCardController.QueryElectronicCards)  //query\n\t\troute.POST(\"/api/electronic-card/list\", electronicCardController.ListElectronicCards)    //list\n\t}\n}"}]