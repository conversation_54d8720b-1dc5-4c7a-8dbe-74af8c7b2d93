[{"po": "package po\n\n// ConsumptionCashback 消费返现实体\ntype ConsumptionCashback struct {\n\tId              *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一id\n\tCashbackRate    *float32 `gorm:\"column:cashback_rate;type:float;default:0\" json:\"cashbackRate\"`    // 返现比例\n\tMaximumCashback *int64   `gorm:\"column:maximum_cashback;type:int;default:0\" json:\"maximumCashback\"` // 最大返现金额\n\tCtime           *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间\n\tUtime           *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间\n\tState           *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态\n\tVersion         *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本\n}\n\n// TableName 设置表名\nfunc (ConsumptionCashback) TableName() string {\n\treturn \"consumption_cashback\"\n}\n\nfunc (c ConsumptionCashback) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// ConsumptionCashbackVO 消费返现值对象\ntype ConsumptionCashbackVO struct {\n\tId              string  `json:\"id\"`              // 唯一id\n\tCashbackRate    float32 `json:\"cashbackRate\"`    // 返现比例\n\tMaximumCashback int64   `json:\"maximumCashback\"` // 最大返现金额\n\tCtime           int64   `json:\"ctime\"`           // 创建时间\n\tUtime           int64   `json:\"utime\"`           // 更新时间\n\tState           int     `json:\"state\"`           // 状态\n\tVersion         int     `json:\"version\"`         // 版本\n}\n", "req_add": "package req\n\n// AddConsumptionCashbackReqDto 创建消费返现请求DTO\ntype AddConsumptionCashbackReqDto struct {\n\tCashbackRate    *float32 `json:\"cashbackRate\"`    // 返现比例\n\tMaximumCashback *int64   `json:\"maximumCashback\"` // 最大返现金额\n}\n", "req_update": "package req\n\ntype UpdateConsumptionCashbackReqDto struct {\n\tId              *string  `json:\"id\"`              // 唯一id\n\tCashbackRate    *float32 `json:\"cashbackRate\"`    // 返现比例\n\tMaximumCashback *int64   `json:\"maximumCashback\"` // 最大返现金额\n}\n", "req_delete": "package req\n\ntype DeleteConsumptionCashbackReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryConsumptionCashbackReqDto struct {\n\tId              *string  `json:\"id\"`              // 唯一id\n\tCashbackRate    *float32 `json:\"cashbackRate\"`    // 返现比例\n\tMaximumCashback *int64   `json:\"maximumCashback\"` // 最大返现金额\n\tPageNum         *int     `json:\"pageNum\"`         // 页码\n\tPageSize        *int     `json:\"pageSize\"`        // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ConsumptionCashbackTransfer struct {\n}\n\nfunc (transfer *ConsumptionCashbackTransfer) PoToVo(po po.ConsumptionCashback) vo.ConsumptionCashbackVO {\n\tvo := vo.ConsumptionCashbackVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ConsumptionCashbackTransfer) VoToPo(vo vo.ConsumptionCashbackVO) po.ConsumptionCashback {\n\tpo := po.ConsumptionCashback{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ConsumptionCashbackService struct {\n}\n\nfunc (service *ConsumptionCashbackService) CreateConsumptionCashback(logCtx *gin.Context, consumptionCashback *po.ConsumptionCashback) error {\n\treturn Save(consumptionCashback)\n}\n\nfunc (service *ConsumptionCashbackService) UpdateConsumptionCashback(logCtx *gin.Context, consumptionCashback *po.ConsumptionCashback) error {\n\treturn Update(consumptionCashback)\n}\n\nfunc (service *ConsumptionCashbackService) DeleteConsumptionCashback(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ConsumptionCashback{Id: &id})\n}\n\nfunc (service *ConsumptionCashbackService) FindConsumptionCashbackById(logCtx *gin.Context, id string) (consumptionCashback *po.ConsumptionCashback, err error) {\n\tconsumptionCashback = &po.ConsumptionCashback{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(consumptionCashback).Error\n\treturn\n}\n\nfunc (service *ConsumptionCashbackService) FindAllConsumptionCashback(logCtx *gin.Context, reqDto *req.QueryConsumptionCashbackReqDto) (list *[]po.ConsumptionCashback, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ConsumptionCashback{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ConsumptionCashback{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ConsumptionCashbackService) FindAllConsumptionCashbackWithPagination(logCtx *gin.Context, reqDto *req.QueryConsumptionCashbackReqDto) (list *[]po.ConsumptionCashback, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ConsumptionCashback{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ConsumptionCashback{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ConsumptionCashbackController struct{}\n\nvar (\n\tconsumptionCashbackService  = impl.ConsumptionCashbackService{}\n\tconsumptionCashbackTransfer = transfer.ConsumptionCashbackTransfer{}\n)\n\n// @Summary 添加消费返现\n// @Description 添加消费返现\n// @Tags 消费返现\n// @Accept json\n// @Produce json\n// @Param body body req.AddConsumptionCashbackReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ConsumptionCashbackVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/consumptionCashback/add [post]\nfunc (controller *ConsumptionCashbackController) AddConsumptionCashback(ctx *gin.Context) {\n\treqDto := req.AddConsumptionCashbackReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tconsumptionCashback := po.ConsumptionCashback{}\n\tif reqDto.CashbackRate != nil {\n\t\tconsumptionCashback.CashbackRate = reqDto.CashbackRate\n\t}\n\tif reqDto.MaximumCashback != nil {\n\t\tconsumptionCashback.MaximumCashback = reqDto.MaximumCashback\n\t}\n\terr = consumptionCashbackService.CreateConsumptionCashback(ctx, &consumptionCashback)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, consumptionCashbackTransfer.PoToVo(consumptionCashback))\n}\n\n// @Summary 更新消费返现\n// @Description 更新消费返现\n// @Tags 消费返现\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateConsumptionCashbackReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ConsumptionCashbackVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/consumptionCashback/update [post]\nfunc (controller *ConsumptionCashbackController) UpdateConsumptionCashback(ctx *gin.Context) {\n\treqDto := req.UpdateConsumptionCashbackReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tconsumptionCashback, err := consumptionCashbackService.FindConsumptionCashbackById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CashbackRate != nil {\n\t\tconsumptionCashback.CashbackRate = reqDto.CashbackRate\n\t}\n\tif reqDto.MaximumCashback != nil {\n\t\tconsumptionCashback.MaximumCashback = reqDto.MaximumCashback\n\t}\n\terr = consumptionCashbackService.UpdateConsumptionCashback(ctx, consumptionCashback)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, consumptionCashbackTransfer.PoToVo(*consumptionCashback))\n}\n\n// @Summary 删除消费返现\n// @Description 删除消费返现\n// @Tags 消费返现\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteConsumptionCashbackReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/consumptionCashback/delete [post]\nfunc (controller *ConsumptionCashbackController) DeleteConsumptionCashback(ctx *gin.Context) {\n\treqDto := req.DeleteConsumptionCashbackReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = consumptionCashbackService.DeleteConsumptionCashback(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询消费返现\n// @Description 查询消费返现\n// @Tags 消费返现\n// @Accept json\n// @Produce json\n// @Param body body req.QueryConsumptionCashbackReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ConsumptionCashbackVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/consumptionCashback/query [post]\nfunc (controller *ConsumptionCashbackController) QueryConsumptionCashbacks(ctx *gin.Context) {\n\treqDto := req.QueryConsumptionCashbackReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := consumptionCashbackService.FindAllConsumptionCashback(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ConsumptionCashbackVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, consumptionCashbackTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询消费返现列表\n// @Description 查询消费返现列表\n// @Tags 消费返现\n// @Accept json\n// @Produce json\n// @Param body body req.QueryConsumptionCashbackReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ConsumptionCashbackVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/consumptionCashback/list [post]\nfunc (a *ConsumptionCashbackController) ListConsumptionCashbacks(ctx *gin.Context) {\n\treqDto := req.QueryConsumptionCashbackReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := consumptionCashbackService.FindAllConsumptionCashbackWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ConsumptionCashbackVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ConsumptionCashbackVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, consumptionCashbackTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ConsumptionCashbackRoute struct {\n}\n\nfunc (s *ConsumptionCashbackRoute) InitConsumptionCashbackRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tconsumptionCashbackController := controller.ConsumptionCashbackController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/consumptionCashback/add\", consumptionCashbackController.AddConsumptionCashback)    //add\n\t\troute.POST(\"/api/consumptionCashback/update\", consumptionCashbackController.UpdateConsumptionCashback) //update\n\t\troute.POST(\"/api/consumptionCashback/delete\", consumptionCashbackController.DeleteConsumptionCashback) //delete\n\t\troute.POST(\"/api/consumptionCashback/query\", consumptionCashbackController.QueryConsumptionCashbacks)     //query\n\t\troute.POST(\"/api/consumptionCashback/list\", consumptionCashbackController.ListConsumptionCashbacks)     //list\n\t}\n}\n"}]