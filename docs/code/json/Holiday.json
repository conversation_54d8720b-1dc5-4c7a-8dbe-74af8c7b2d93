[{"po": "package po\n\n// Holiday 节假日实体\ntype Holiday struct {\n\tId      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName    *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`           // 节假日名称\n\tDate    *string `gorm:\"column:date;type:varchar(64);default:''\" json:\"date\"`           // 日期\n\tType    *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`           // 节假日类型 1:节假日 2:工作日\n\tVenueId *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`     // 所属店铺ID\n\tCtime   *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                  // 创建时间戳\n\tUtime   *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                  // 更新时间戳\n\tState   *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                  // 状态值\n\tVersion *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`              // 版本号\n}\n\n// TableName 设置表名\nfunc (Holiday) TableName() string {\n\treturn \"holiday\"\n}\n\nfunc (h Holiday) GetId() string {\n\treturn *h.Id\n}\n", "vo": "package vo\n\n// HolidayVO 节假日信息值对象\ntype HolidayVO struct {\n\tId      string `json:\"id\"`      // ID\n\tName    string `json:\"name\"`    // 节假日名称\n\tDate    string `json:\"date\"`    // 日期\n\tType    string `json:\"type\"`    // 节假日类型 1:节假日 2:工作日\n\tVenueId string `json:\"venueId\"` // 所属店铺ID\n\tCtime   int64  `json:\"ctime\"`   // 创建时间戳\n\tUtime   int64  `json:\"utime\"`   // 更新时间戳\n\tState   int    `json:\"state\"`   // 状态值\n\tVersion int    `json:\"version\"` // 版本号\n}\n", "req_add": "package req\n\n// AddHolidayReqDto 创建节假日请求DTO\ntype AddHolidayReqDto struct {\n\tName    *string `json:\"name\"`    // 节假日名称\n\tDate    *string `json:\"date\"`    // 日期\n\tType    *string `json:\"type\"`    // 节假日类型 1:节假日 2:工作日\n\tVenueId *string `json:\"venueId\"` // 所属店铺ID\n}\n", "req_update": "package req\n\ntype UpdateHolidayReqDto struct {\n\tId      *string `json:\"id\"`      // ID\n\tName    *string `json:\"name\"`    // 节假日名称\n\tDate    *string `json:\"date\"`    // 日期\n\tType    *string `json:\"type\"`    // 节假日类型 1:节假日 2:工作日\n\tVenueId *string `json:\"venueId\"` // 所属店铺ID\n}\n", "req_delete": "package req\n\ntype DeleteHolidayReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryHolidayReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tName     *string `json:\"name\"`     // 节假日名称\n\tDate     *string `json:\"date\"`     // 日期\n\tType     *string `json:\"type\"`     // 节假日类型 1:节假日 2:工作日\n\tVenueId  *string `json:\"venueId\"`  // 所属店铺ID\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype HolidayTransfer struct {\n}\n\nfunc (transfer *HolidayTransfer) PoToVo(po po.Holiday) vo.HolidayVO {\n\tvo := vo.HolidayVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *HolidayTransfer) VoToPo(vo vo.HolidayVO) po.Holiday {\n\tpo := po.Holiday{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype HolidayService struct {\n}\n\nfunc (service *HolidayService) CreateHoliday(logCtx *gin.Context, holiday *po.Holiday) error {\n\treturn Save(holiday)\n}\n\nfunc (service *HolidayService) UpdateHoliday(logCtx *gin.Context, holiday *po.Holiday) error {\n\treturn Update(holiday)\n}\n\nfunc (service *HolidayService) DeleteHoliday(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Holiday{Id: &id})\n}\n\nfunc (service *HolidayService) FindHolidayById(logCtx *gin.Context, id string) (holiday *po.Holiday, err error) {\n\tholiday = &po.Holiday{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(holiday).Error\n\treturn\n}\n\nfunc (service *HolidayService) FindAllHoliday(logCtx *gin.Context, reqDto *req.QueryHolidayReqDto) (list *[]po.Holiday, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Holiday{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.Date != nil && *reqDto.Date != \"\" {\n\t\tdb = db.Where(\"date=?\", *reqDto.Date)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Holiday{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *HolidayService) FindAllHolidayWithPagination(logCtx *gin.Context, reqDto *req.QueryHolidayReqDto) (list *[]po.Holiday, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Holiday{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\tif reqDto.Date != nil && *reqDto.Date != \"\" {\n\t\tdb = db.Where(\"date=?\", *reqDto.Date)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Holiday{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype HolidayController struct{}\n\nvar (\n\tholidayService  = impl.HolidayService{}\n\tholidayTransfer = transfer.HolidayTransfer{}\n)\n\n// @Summary 添加节假日\n// @Description 添加节假日\n// @Tags 节假日\n// @Accept json\n// @Produce json\n// @Param body body req.AddHolidayReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.HolidayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/holiday/add [post]\nfunc (controller *HolidayController) AddHoliday(ctx *gin.Context) {\n\treqDto := req.AddHolidayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tholiday := po.Holiday{}\n\tif reqDto.Name != nil {\n\t\tholiday.Name = reqDto.Name\n\t}\n\tif reqDto.Date != nil {\n\t\tholiday.Date = reqDto.Date\n\t}\n\tif reqDto.Type != nil {\n\t\tholiday.Type = reqDto.Type\n\t}\n\tif reqDto.VenueId != nil {\n\t\tholiday.VenueId = reqDto.VenueId\n\t}\n\terr = holidayService.CreateHoliday(ctx, &holiday)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, holidayTransfer.PoToVo(holiday))\n}\n\n// @Summary 更新节假日\n// @Description 更新节假日\n// @Tags 节假日\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateHolidayReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.HolidayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/holiday/update [post]\nfunc (controller *HolidayController) UpdateHoliday(ctx *gin.Context) {\n\treqDto := req.UpdateHolidayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tholiday, err := holidayService.FindHolidayById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tholiday.Name = reqDto.Name\n\t}\n\tif reqDto.Date != nil {\n\t\tholiday.Date = reqDto.Date\n\t}\n\tif reqDto.Type != nil {\n\t\tholiday.Type = reqDto.Type\n\t}\n\tif reqDto.VenueId != nil {\n\t\tholiday.VenueId = reqDto.VenueId\n\t}\n\terr = holidayService.UpdateHoliday(ctx, holiday)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, holidayTransfer.PoToVo(*holiday))\n}\n\n// @Summary 删除节假日\n// @Description 删除节假日\n// @Tags 节假日\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteHolidayReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/holiday/delete [post]\nfunc (controller *HolidayController) DeleteHoliday(ctx *gin.Context) {\n\treqDto := req.DeleteHolidayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = holidayService.DeleteHoliday(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询节假日\n// @Description 查询节假日\n// @Tags 节假日\n// @Accept json\n// @Produce json\n// @Param body body req.QueryHolidayReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.HolidayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/holiday/query [post]\nfunc (controller *HolidayController) QueryHolidays(ctx *gin.Context) {\n\treqDto := req.QueryHolidayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := holidayService.FindAllHoliday(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.HolidayVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, holidayTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询节假日列表\n// @Description 查询节假日列表\n// @Tags 节假日\n// @Accept json\n// @Produce json\n// @Param body body req.QueryHolidayReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.HolidayVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/holiday/list [post]\nfunc (a *HolidayController) ListHolidays(ctx *gin.Context) {\n\treqDto := req.QueryHolidayReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := holidayService.FindAllHolidayWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.HolidayVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.HolidayVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, holidayTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype HolidayRoute struct {\n}\n\nfunc (s *HolidayRoute) InitHolidayRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tholidayController := controller.HolidayController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/holiday/add\", holidayController.AddHoliday)       //add\n\t\troute.POST(\"/api/holiday/update\", holidayController.UpdateHoliday) //update\n\t\troute.POST(\"/api/holiday/delete\", holidayController.DeleteHoliday) //delete\n\t\troute.POST(\"/api/holiday/query\", holidayController.QueryHolidays)  //query\n\t\troute.POST(\"/api/holiday/list\", holidayController.ListHolidays)    //list\n\t}\n}\n"}]