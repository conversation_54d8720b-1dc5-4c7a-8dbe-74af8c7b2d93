[{"po": "package po\n\n// MemberRechargePackage 会员充值套餐实体\ntype MemberRechargePackage struct {\n\tId                            *string   `gorm:\"column:id;type:varchar(64);primaryKey\" json:\"id\"`\n\tVenueId                       *string   `gorm:\"column:venue_id;type:varchar(64)\" json:\"venueId\"`                    // 门店ID\n\tPackageType                   *string   `gorm:\"column:package_type;type:varchar(32)\" json:\"packageType\"`            // 充值套餐类型\n\tFixedAmountLimit              *int64    `gorm:\"column:fixed_amount_limit\" json:\"fixedAmountLimit\"`                // 固定充值金额limit\n\tBonusAmount                   *int64    `gorm:\"column:bonus_amount\" json:\"bonusAmount\"`                          // 赠送金额\n\tBonusAmountType              *string   `gorm:\"column:bonus_amount_type;type:varchar(32)\" json:\"bonusAmountType\"`   // 赠送金额类型\n\tMinAmount                     *int64    `gorm:\"column:min_amount\" json:\"minAmount\"`                              // 区间金额最小\n\tMaxAmount                     *int64    `gorm:\"column:max_amount\" json:\"maxAmount\"`                              // 区间金额最大\n\tBonusAmountPercent           *int64    `gorm:\"column:bonus_amount_percent\" json:\"bonusAmountPercent\"`            // 赠送金额百分比\n\tBonusAmountCondition         *string   `gorm:\"column:bonus_amount_condition;type:varchar(255)\" json:\"bonusAmountCondition\"` // 赠送金额使用条件\n\tBonusAmountIsAvailable       *bool     `gorm:\"column:bonus_amount_is_available\" json:\"bonusAmountIsAvailable\"`    // 赠送金额当日是否可用\n\tBonusAmountIsReturnable      *bool     `gorm:\"column:bonus_amount_is_returnable\" json:\"bonusAmountIsReturnable\"` // 赠送金额是否分期返还\n\tDeliveryChannel              *string   `gorm:\"column:delivery_channel;type:varchar(64)\" json:\"deliveryChannel\"`     // 投放渠道\n\tBonusGoodsIds                *string   `gorm:\"column:bonus_goods_ids;type:text\" json:\"bonusGoodsIds\"`             // 赠送商品ID列表\n\tBonusGoodsGroupName          *string   `gorm:\"column:bonus_goods_group_name;type:varchar(255)\" json:\"bonusGoodsGroupName\"` // 赠送商品可选组名称\n\tBonusGoodsGroupProductIds    *string   `gorm:\"column:bonus_goods_group_product_ids;type:text\" json:\"bonusGoodsGroupProductIds\"` // 赠送商品组ID列表\n\tBonusGoodsGroupStrategyType  *string   `gorm:\"column:bonus_goods_group_strategy_type;type:varchar(32)\" json:\"bonusGoodsGroupStrategyType\"` // 可选组策略类型\n\tBonusGoodsGroupCount         *int      `gorm:\"column:bonus_goods_group_count\" json:\"bonusGoodsGroupCount\"`        // 可选组商品数量\n\tActivityStartTime            *int64    `gorm:\"column:activity_start_time\" json:\"activityStartTime\"`              // 活动开始时间\n\tActivityEndTime              *int64    `gorm:\"column:activity_end_time\" json:\"activityEndTime\"`                  // 活动结束时间\n\tRemark                       *string   `gorm:\"column:remark;type:varchar(255)\" json:\"remark\"`                     // 优惠说明\n\tApplicableMemberCardLevels   *string   `gorm:\"column:applicable_member_card_levels;type:text\" json:\"applicableMemberCardLevels\"` // 适用会员卡等级\n\tBonusCouponIds               *string   `gorm:\"column:bonus_coupon_ids;type:text\" json:\"bonusCouponIds\"`           // 赠送优惠券ID列表\n\tIsEnabled                    *bool     `gorm:\"column:is_enabled\" json:\"isEnabled\"`                               // 是否启用\n\tCtime                        *int64    `gorm:\"column:ctime\" json:\"ctime\"`                                       // 创建时间\n\tUtime                        *int64    `gorm:\"column:utime\" json:\"utime\"`                                       // 更新时间\n\tState                        *int      `gorm:\"column:state\" json:\"state\"`                                       // 状态\n\tVersion                      *int      `gorm:\"column:version\" json:\"version\"`                                   // 版本号\n}\n\n// TableName 设置表名\nfunc (MemberRechargePackage) TableName() string {\n\treturn \"member_recharge_package\"\n}\n\nfunc (m MemberRechargePackage) GetId() string {\n\treturn *m.Id\n}", "vo": "package vo\n\n// MemberRechargePackageVO 会员充值套餐值对象\ntype MemberRechargePackageVO struct {\n\tId                          string   `json:\"id\"`\n\tVenueId                     string   `json:\"venueId\"`                    // 门店ID\n\tPackageType                 string   `json:\"packageType\"`                // 充值套餐类型\n\tFixedAmountLimit            int64    `json:\"fixedAmountLimit\"`           // 固定充值金额limit\n\tBonusAmount                 int64    `json:\"bonusAmount\"`                // 赠送金额\n\tBonusAmountType            string   `json:\"bonusAmountType\"`            // 赠送金额类型\n\tMinAmount                   int64    `json:\"minAmount\"`                  // 区间金额最小\n\tMaxAmount                   int64    `json:\"maxAmount\"`                  // 区间金额最大\n\tBonusAmountPercent         int64    `json:\"bonusAmountPercent\"`         // 赠送金额百分比\n\tBonusAmountCondition       string   `json:\"bonusAmountCondition\"`       // 赠送金额使用条件\n\tBonusAmountIsAvailable     bool     `json:\"bonusAmountIsAvailable\"`     // 赠送金额当日是否可用\n\tBonusAmountIsReturnable    bool     `json:\"bonusAmountIsReturnable\"`    // 赠送金额是否分期返还\n\tDeliveryChannel            string   `json:\"deliveryChannel\"`            // 投放渠道\n\tBonusGoodsIds              []string `json:\"bonusGoodsIds\"`              // 赠送商品ID列表\n\tBonusGoodsGroupName        string   `json:\"bonusGoodsGroupName\"`        // 赠送商品可选组名称\n\tBonusGoodsGroupProductIds  []string `json:\"bonusGoodsGroupProductIds\"`  // 赠送商品组ID列表\n\tBonusGoodsGroupStrategyType string  `json:\"bonusGoodsGroupStrategyType\"` // 可选组策略类型\n\tBonusGoodsGroupCount       int      `json:\"bonusGoodsGroupCount\"`       // 可选组商品数量\n\tActivityStartTime          int64    `json:\"activityStartTime\"`          // 活动开始时间\n\tActivityEndTime            int64    `json:\"activityEndTime\"`            // 活动结束时间\n\tRemark                     string   `json:\"remark\"`                     // 优惠说明\n\tApplicableMemberCardLevels []string `json:\"applicableMemberCardLevels\"` // 适用会员卡等级\n\tBonusCouponIds             []string `json:\"bonusCouponIds\"`             // 赠送优惠券ID列表\n\tIsEnabled                  bool     `json:\"isEnabled\"`                  // 是否启用\n\tCtime                      int64    `json:\"ctime\"`                      // 创建时间\n\tUtime                      int64    `json:\"utime\"`                      // 更新时间\n\tState                      int      `json:\"state\"`                      // 状态\n\tVersion                    int      `json:\"version\"`                    // 版本号\n}", "transfer": "package transfer\n\nimport (\n\t\"encoding/json\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberRechargePackageTransfer struct {}\n\nfunc (t *MemberRechargePackageTransfer) PoToVo(po po.MemberRechargePackage) vo.MemberRechargePackageVO {\n\tvo := vo.MemberRechargePackageVO{}\n\tcopier.Copy(&vo, &po)\n\n\t// 处理字符串数组字段\n\tif po.BonusGoodsIds != nil {\n\t\tjson.Unmarshal([]byte(*po.BonusGoodsIds), &vo.BonusGoodsIds)\n\t}\n\tif po.BonusGoodsGroupProductIds != nil {\n\t\tjson.Unmarshal([]byte(*po.BonusGoodsGroupProductIds), &vo.BonusGoodsGroupProductIds)\n\t}\n\tif po.ApplicableMemberCardLevels != nil {\n\t\tjson.Unmarshal([]byte(*po.ApplicableMemberCardLevels), &vo.ApplicableMemberCardLevels)\n\t}\n\tif po.BonusCouponIds != nil {\n\t\tjson.Unmarshal([]byte(*po.BonusCouponIds), &vo.BonusCouponIds)\n\t}\n\n\treturn vo\n}\n\nfunc (t *MemberRechargePackageTransfer) VoToPo(vo vo.MemberRechargePackageVO) po.MemberRechargePackage {\n\tpo := po.MemberRechargePackage{}\n\tcopier.Copy(&po, &vo)\n\n\t// 处理字符串数组字段\n\tif bonusGoodsIds, err := json.Marshal(vo.BonusGoodsIds); err == nil {\n\t\tstr := string(bonusGoodsIds)\n\t\tpo.BonusGoodsIds = &str\n\t}\n\tif bonusGoodsGroupProductIds, err := json.Marshal(vo.BonusGoodsGroupProductIds); err == nil {\n\t\tstr := string(bonusGoodsGroupProductIds)\n\t\tpo.BonusGoodsGroupProductIds = &str\n\t}\n\tif applicableMemberCardLevels, err := json.Marshal(vo.ApplicableMemberCardLevels); err == nil {\n\t\tstr := string(applicableMemberCardLevels)\n\t\tpo.ApplicableMemberCardLevels = &str\n\t}\n\tif bonusCouponIds, err := json.Marshal(vo.BonusCouponIds); err == nil {\n\t\tstr := string(bonusCouponIds)\n\t\tpo.BonusCouponIds = &str\n\t}\n\n\treturn po\n}", "req_add": "package req\n\n// AddMemberRechargePackageReqDto 创建会员充值套餐请求DTO\ntype AddMemberRechargePackageReqDto struct {\n\tVenueId                     *string   `json:\"venueId\"`                    // 门店ID\n\tPackageType                 *string   `json:\"packageType\"`                // 充值套餐类型\n\tFixedAmountLimit            *int64    `json:\"fixedAmountLimit\"`           // 固定充值金额limit\n\tBonusAmount                 *int64    `json:\"bonusAmount\"`                // 赠送金额\n\tBonusAmountType            *string   `json:\"bonusAmountType\"`            // 赠送金额类型\n\tMinAmount                   *int64    `json:\"minAmount\"`                  // 区间金额最小\n\tMaxAmount                   *int64    `json:\"maxAmount\"`                  // 区间金额最大\n\tBonusAmountPercent         *int64    `json:\"bonusAmountPercent\"`         // 赠送金额百分比\n\tBonusAmountCondition       *string   `json:\"bonusAmountCondition\"`       // 赠送金额使用条件\n\tBonusAmountIsAvailable     *bool     `json:\"bonusAmountIsAvailable\"`     // 赠送金额当日是否可用\n\tBonusAmountIsReturnable    *bool     `json:\"bonusAmountIsReturnable\"`    // 赠送金额是否分期返还\n\tDeliveryChannel            *string   `json:\"deliveryChannel\"`            // 投放渠道\n\tBonusGoodsIds              []string  `json:\"bonusGoodsIds\"`              // 赠送商品ID列表\n\tBonusGoodsGroupName        *string   `json:\"bonusGoodsGroupName\"`        // 赠送商品可选组名称\n\tBonusGoodsGroupProductIds  []string  `json:\"bonusGoodsGroupProductIds\"`  // 赠送商品组ID列表\n\tBonusGoodsGroupStrategyType *string  `json:\"bonusGoodsGroupStrategyType\"` // 可选组策略类型\n\tBonusGoodsGroupCount       *int      `json:\"bonusGoodsGroupCount\"`       // 可选组商品数量\n\tActivityStartTime          *int64    `json:\"activityStartTime\"`          // 活动开始时间\n\tActivityEndTime            *int64    `json:\"activityEndTime\"`            // 活动结束时间\n\tRemark                     *string   `json:\"remark\"`                     // 优惠说明\n\tApplicableMemberCardLevels []string  `json:\"applicableMemberCardLevels\"` // 适用会员卡等级\n\tBonusCouponIds             []string  `json:\"bonusCouponIds\"`             // 赠送优惠券ID列表\n\tIsEnabled                  *bool     `json:\"isEnabled\"`                  // 是否启用\n}", "req_update": "package req\n\n// UpdateMemberRechargePackageReqDto 更新会员充值套餐请求DTO\ntype UpdateMemberRechargePackageReqDto struct {\n\tId                          *string   `json:\"id\"`                         // ID\n\tVenueId                     *string   `json:\"venueId\"`                    // 门店ID\n\tPackageType                 *string   `json:\"packageType\"`                // 充值套餐类型\n\tFixedAmountLimit            *int64    `json:\"fixedAmountLimit\"`           // 固定充值金额limit\n\tBonusAmount                 *int64    `json:\"bonusAmount\"`                // 赠送金额\n\tBonusAmountType            *string   `json:\"bonusAmountType\"`            // 赠送金额类型\n\tMinAmount                   *int64    `json:\"minAmount\"`                  // 区间金额最小\n\tMaxAmount                   *int64    `json:\"maxAmount\"`                  // 区间金额最大\n\tBonusAmountPercent         *int64    `json:\"bonusAmountPercent\"`         // 赠送金额百分比\n\tBonusAmountCondition       *string   `json:\"bonusAmountCondition\"`       // 赠送金额使用条件\n\tBonusAmountIsAvailable     *bool     `json:\"bonusAmountIsAvailable\"`     // 赠送金额当日是否可用\n\tBonusAmountIsReturnable    *bool     `json:\"bonusAmountIsReturnable\"`    // 赠送金额是否分期返还\n\tDeliveryChannel            *string   `json:\"deliveryChannel\"`            // 投放渠道\n\tBonusGoodsIds              []string  `json:\"bonusGoodsIds\"`              // 赠送商品ID列表\n\tBonusGoodsGroupName        *string   `json:\"bonusGoodsGroupName\"`        // 赠送商品可选组名称\n\tBonusGoodsGroupProductIds  []string  `json:\"bonusGoodsGroupProductIds\"`  // 赠送商品组ID列表\n\tBonusGoodsGroupStrategyType *string  `json:\"bonusGoodsGroupStrategyType\"` // 可选组策略类型\n\tBonusGoodsGroupCount       *int      `json:\"bonusGoodsGroupCount\"`       // 可选组商品数量\n\tActivityStartTime          *int64    `json:\"activityStartTime\"`          // 活动开始时间\n\tActivityEndTime            *int64    `json:\"activityEndTime\"`            // 活动结束时间\n\tRemark                     *string   `json:\"remark\"`                     // 优惠说明\n\tApplicableMemberCardLevels []string  `json:\"applicableMemberCardLevels\"` // 适用会员卡等级\n\tBonusCouponIds             []string  `json:\"bonusCouponIds\"`             // 赠送优惠券ID列表\n\tIsEnabled                  *bool     `json:\"isEnabled\"`                  // 是否启用\n}", "req_delete": "package req\n\n// DeleteMemberRechargePackageReqDto 删除会员充值套餐请求DTO\ntype DeleteMemberRechargePackageReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryMemberRechargePackageReqDto 查询会员充值套餐请求DTO\ntype QueryMemberRechargePackageReqDto struct {\n\tId                          *string   `json:\"id\"`                         // ID\n\tVenueId                     *string   `json:\"venueId\"`                    // 门店ID\n\tPackageType                 *string   `json:\"packageType\"`                // 充值套餐类型\n\tBonusAmountType            *string   `json:\"bonusAmountType\"`            // 赠送金额类型\n\tDeliveryChannel            *string   `json:\"deliveryChannel\"`            // 投放渠道\n\tBonusGoodsGroupName        *string   `json:\"bonusGoodsGroupName\"`        // 赠送商品可选组名称\n\tBonusGoodsGroupStrategyType *string  `json:\"bonusGoodsGroupStrategyType\"` // 可选组策略类型\n\tIsEnabled                  *bool     `json:\"isEnabled\"`                  // 是否启用\n\tPageNum                    *int      `json:\"pageNum\"`                    // 页码\n\tPageSize                   *int      `json:\"pageSize\"`                   // 每页记录数\n}", "service": "package impl\n\nimport (\n\t\"encoding/json\"\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype MemberRechargePackageService struct {}\n\nvar (\n\tmemberRechargePackageService  = MemberRechargePackageService{}\n\tmemberRechargePackageTransfer = transfer.MemberRechargePackageTransfer{}\n)\n\n// CreateMemberRechargePackage 创建会员充值套餐\nfunc (s *MemberRechargePackageService) CreateMemberRechargePackage(logCtx *gin.Context, pkg *po.MemberRechargePackage) error {\n\treturn Save(pkg)\n}\n\n// CreateMemberRechargePackageWithTx 在事务中创建会员充值套餐\nfunc (s *MemberRechargePackageService) CreateMemberRechargePackageWithTx(logCtx *gin.Context, pkg *po.MemberRechargePackage, tx *gorm.DB) error {\n\treturn SaveWithTx(pkg, tx)\n}\n\n// UpdateMemberRechargePackage 更新会员充值套餐\nfunc (s *MemberRechargePackageService) UpdateMemberRechargePackage(logCtx *gin.Context, pkg *po.MemberRechargePackage) error {\n\treturn Update(pkg)\n}\n\n// UpdateMemberRechargePackagePartial 部分更新会员充值套餐\nfunc (s *MemberRechargePackageService) UpdateMemberRechargePackagePartial(logCtx *gin.Context, pkg *po.MemberRechargePackage) error {\n\treturn UpdateNotNull(pkg)\n}\n\n// UpdateMemberRechargePackagePartialWithTx 在事务中部分更新会员充值套餐\nfunc (s *MemberRechargePackageService) UpdateMemberRechargePackagePartialWithTx(logCtx *gin.Context, pkg *po.MemberRechargePackage, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(pkg, tx)\n}\n\n// DeleteMemberRechargePackage 删除会员充值套餐\nfunc (s *MemberRechargePackageService) DeleteMemberRechargePackage(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MemberRechargePackage{Id: &id})\n}\n\n// FindMemberRechargePackageById 根据ID查询会员充值套餐\nfunc (s *MemberRechargePackageService) FindMemberRechargePackageById(logCtx *gin.Context, id string) (pkg *po.MemberRechargePackage, err error) {\n\tpkg = &po.MemberRechargePackage{}\n\terr = model.DBMaster.Self.Where(\"id = ?\", id).First(pkg).Error\n\treturn\n}\n\n// FindAllMemberRechargePackage 查询所有会员充值套餐\nfunc (s *MemberRechargePackageService) FindAllMemberRechargePackage(logCtx *gin.Context, reqDto *req.QueryMemberRechargePackageReqDto) (list *[]po.MemberRechargePackage, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberRechargePackage{})\n\n\t// 构建查询条件\n\tdb = s.buildQueryConditions(db, reqDto)\n\n\t// 按创建时间倒序\n\tdb = db.Order(\"ctime desc\")\n\n\tlist = &[]po.MemberRechargePackage{}\n\terr = db.Find(list).Error\n\treturn\n}\n\n// FindAllMemberRechargePackageWithPagination 分页查询会员充值套餐\nfunc (s *MemberRechargePackageService) FindAllMemberRechargePackageWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberRechargePackageReqDto) (list *[]po.MemberRechargePackage, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberRechargePackage{})\n\n\t// 设置默认分页参数\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\t// 构建查询条件\n\tdb = s.buildQueryConditions(db, reqDto)\n\n\t// 获取总数\n\terr = db.Count(&total).Error\n\tif err != nil || total <= 0 {\n\t\treturn\n\t}\n\n\t// 分页查询\n\tlist = &[]po.MemberRechargePackage{}\n\toffset := (*reqDto.PageNum - 1) * *reqDto.PageSize\n\tdb = db.Offset(int(offset)).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n\n// buildQueryConditions 构建查询条件\nfunc (s *MemberRechargePackageService) buildQueryConditions(db *gorm.DB, reqDto *req.QueryMemberRechargePackageReqDto) *gorm.DB {\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id = ?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id = ?\", *reqDto.VenueId)\n\t}\n\tif reqDto.PackageType != nil && *reqDto.PackageType != \"\" {\n\t\tdb = db.Where(\"package_type = ?\", *reqDto.PackageType)\n\t}\n\tif reqDto.BonusAmountType != nil && *reqDto.BonusAmountType != \"\" {\n\t\tdb = db.Where(\"bonus_amount_type = ?\", *reqDto.BonusAmountType)\n\t}\n\tif reqDto.DeliveryChannel != nil && *reqDto.DeliveryChannel != \"\" {\n\t\tdb = db.Where(\"delivery_channel = ?\", *reqDto.DeliveryChannel)\n\t}\n\tif reqDto.BonusGoodsGroupName != nil && *reqDto.BonusGoodsGroupName != \"\" {\n\t\tdb = db.Where(\"bonus_goods_group_name = ?\", *reqDto.BonusGoodsGroupName)\n\t}\n\tif reqDto.BonusGoodsGroupStrategyType != nil && *reqDto.BonusGoodsGroupStrategyType != \"\" {\n\t\tdb = db.Where(\"bonus_goods_group_strategy_type = ?\", *reqDto.BonusGoodsGroupStrategyType)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled = ?\", *reqDto.IsEnabled)\n\t}\n\treturn db\n}", "controller": "package controller\n\nimport (\n\t\"encoding/json\"\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberRechargePackageController struct{}\n\nvar (\n\tmemberRechargePackageService  = impl.MemberRechargePackageService{}\n\tmemberRechargePackageTransfer = transfer.MemberRechargePackageTransfer{}\n)\n\n// @Summary 添加会员充值套餐\n// @Description 添加会员充值套餐\n// @Tags 会员充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberRechargePackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/recharge/package/add [post]\nfunc (c *MemberRechargePackageController) AddMemberRechargePackage(ctx *gin.Context) {\n\treqDto := req.AddMemberRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\t// 构建PO对象\n\tpkg := po.MemberRechargePackage{}\n\tif reqDto.VenueId != nil {\n\t\tpkg.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.PackageType != nil {\n\t\tpkg.PackageType = reqDto.PackageType\n\t}\n\tif reqDto.FixedAmountLimit != nil {\n\t\tpkg.FixedAmountLimit = reqDto.FixedAmountLimit\n\t}\n\tif reqDto.BonusAmount != nil {\n\t\tpkg.BonusAmount = reqDto.BonusAmount\n\t}\n\tif reqDto.BonusAmountType != nil {\n\t\tpkg.BonusAmountType = reqDto.BonusAmountType\n\t}\n\tif reqDto.MinAmount != nil {\n\t\tpkg.MinAmount = reqDto.MinAmount\n\t}\n\tif reqDto.MaxAmount != nil {\n\t\tpkg.MaxAmount = reqDto.MaxAmount\n\t}\n\tif reqDto.BonusAmountPercent != nil {\n\t\tpkg.BonusAmountPercent = reqDto.BonusAmountPercent\n\t}\n\tif reqDto.BonusAmountCondition != nil {\n\t\tpkg.BonusAmountCondition = reqDto.BonusAmountCondition\n\t}\n\tif reqDto.BonusAmountIsAvailable != nil {\n\t\tpkg.BonusAmountIsAvailable = reqDto.BonusAmountIsAvailable\n\t}\n\tif reqDto.BonusAmountIsReturnable != nil {\n\t\tpkg.BonusAmountIsReturnable = reqDto.BonusAmountIsReturnable\n\t}\n\tif reqDto.DeliveryChannel != nil {\n\t\tpkg.DeliveryChannel = reqDto.DeliveryChannel\n\t}\n\n\t// 处理数组字段\n\tif len(reqDto.BonusGoodsIds) > 0 {\n\t\tif jsonStr, err := json.Marshal(reqDto.BonusGoodsIds); err == nil {\n\t\t\tstr := string(jsonStr)\n\t\t\tpkg.BonusGoodsIds = &str\n\t\t}\n\t}\n\tif reqDto.BonusGoodsGroupName != nil {\n\t\tpkg.BonusGoodsGroupName = reqDto.BonusGoodsGroupName\n\t}\n\tif len(reqDto.BonusGoodsGroupProductIds) > 0 {\n\t\tif jsonStr, err := json.Marshal(reqDto.BonusGoodsGroupProductIds); err == nil {\n\t\t\tstr := string(jsonStr)\n\t\t\tpkg.BonusGoodsGroupProductIds = &str\n\t\t}\n\t}\n\tif reqDto.BonusGoodsGroupStrategyType != nil {\n\t\tpkg.BonusGoodsGroupStrategyType = reqDto.BonusGoodsGroupStrategyType\n\t}\n\tif reqDto.BonusGoodsGroupCount != nil {\n\t\tpkg.BonusGoodsGroupCount = reqDto.BonusGoodsGroupCount\n\t}\n\tif reqDto.ActivityStartTime != nil {\n\t\tpkg.ActivityStartTime = reqDto.ActivityStartTime\n\t}\n\tif reqDto.ActivityEndTime != nil {\n\t\tpkg.ActivityEndTime = reqDto.ActivityEndTime\n\t}\n\tif reqDto.Remark != nil {\n\t\tpkg.Remark = reqDto.Remark\n\t}\n\tif len(reqDto.ApplicableMemberCardLevels) > 0 {\n\t\tif jsonStr, err := json.Marshal(reqDto.ApplicableMemberCardLevels); err == nil {\n\t\t\tstr := string(jsonStr)\n\t\t\tpkg.ApplicableMemberCardLevels = &str\n\t\t}\n\t}\n\tif len(reqDto.BonusCouponIds) > 0 {\n\t\tif jsonStr, err := json.Marshal(reqDto.BonusCouponIds); err == nil {\n\t\t\tstr := string(jsonStr)\n\t\t\tpkg.BonusCouponIds = &str\n\t\t}\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpkg.IsEnabled = reqDto.IsEnabled\n\t}\n\n\terr = memberRechargePackageService.CreateMemberRechargePackage(ctx, &pkg)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tResult_success[any](ctx, memberRechargePackageTransfer.PoToVo(pkg))\n}\n\n// @Summary 更新会员充值套餐\n// @Description 更新会员充值套餐\n// @Tags 会员充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberRechargePackageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/recharge/package/update [post]\nfunc (c *MemberRechargePackageController) UpdateMemberRechargePackage(ctx *gin.Context) {\n\treqDto := req.UpdateMemberRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\t// 查询现有数据\n\tpkg, err := memberRechargePackageService.FindMemberRechargePackageById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\t// 更新字段\n\t// ... [与Add方法类似的字段更新逻辑] ...\n\n\terr = memberRechargePackageService.UpdateMemberRechargePackage(ctx, pkg)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tResult_success[any](ctx, memberRechargePackageTransfer.PoToVo(*pkg))\n}\n\n// @Summary 删除会员充值套餐\n// @Description 删除会员充值套餐\n// @Tags 会员充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/recharge/package/delete [post]\nfunc (c *MemberRechargePackageController) DeleteMemberRechargePackage(ctx *gin.Context) {\n\treqDto := req.DeleteMemberRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = memberRechargePackageService.DeleteMemberRechargePackage(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员充值套餐列表\n// @Description 查询会员充值套餐列表\n// @Tags 会员充值套餐\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberRechargePackageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.MemberRechargePackageVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member/recharge/package/list [post]\nfunc (c *MemberRechargePackageController) ListMemberRechargePackages(ctx *gin.Context) {\n\treqDto := req.QueryMemberRechargePackageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, total, err := memberRechargePackageService.FindAllMemberRechargePackageWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\t// 构建分页响应\n\tpage := vo.PageVO[[]vo.MemberRechargePackageVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = total\n\tpage.Data = []vo.MemberRechargePackageVO{}\n\n\t// 转换数据\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberRechargePackageTransfer.PoToVo(v))\n\t}\n\n\tResult_success[any](ctx, &page)\n}"}]