[{"po": "package po\n\n// ProductMultipleBuyFree 商品多买多送策略实体\ntype ProductMultipleBuyFree struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // 唯一id\n\tName            *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                    // 策略名称\n\tBuyProducts     *string `gorm:\"column:buy_products;type:text\" json:\"buyProducts\"`                        // 购买商品列表\n\tBuyCount        *int    `gorm:\"column:buy_count;type:int;default:0\" json:\"buyCount\"`                    // 购买数量\n\tExampleGiftProduct *string `gorm:\"column:example_gift_product;type:text\" json:\"exampleGiftProduct\"`    // 赠送商品示例\n\tGiftPolicy      *string `gorm:\"column:gift_policy;type:text\" json:\"giftPolicy\"`                        // 赠送策略\n\tTimeSlots       *string `gorm:\"column:time_slots;type:text\" json:\"timeSlots\"`                          // 适用时间段\n\tIsCanRepeatBuy  *bool   `gorm:\"column:is_can_repeat_buy;type:tinyint(1);default:0\" json:\"isCanRepeatBuy\"` // 是否可以重复购买\n\tCtime           *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                        // 创建时间\n\tUtime           *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                        // 更新时间\n\tState           *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                          // 状态\n\tVersion         *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                      // 版本\n}\n\n// TableName 设置表名\nfunc (ProductMultipleBuyFree) TableName() string {\n\treturn \"product_multiple_buy_free\"\n}\n\nfunc (p ProductMultipleBuyFree) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductMultipleBuyFreeVO 商品多买多送策略值对象\ntype ProductMultipleBuyFreeVO struct {\n\tId                string `json:\"id\"`                // 唯一id\n\tName              string `json:\"name\"`              // 策略名称\n\tBuyProducts       string `json:\"buyProducts\"`       // 购买商品列表\n\tBuyCount          int    `json:\"buyCount\"`          // 购买数量\n\tExampleGiftProduct string `json:\"exampleGiftProduct\"` // 赠送商品示例\n\tGiftPolicy        string `json:\"giftPolicy\"`        // 赠送策略\n\tTimeSlots         string `json:\"timeSlots\"`         // 适用时间段\n\tIsCanRepeatBuy    bool   `json:\"isCanRepeatBuy\"`    // 是否可以重复购买\n\tCtime             int64  `json:\"ctime\"`             // 创建时间\n\tUtime             int64  `json:\"utime\"`             // 更新时间\n\tState             int    `json:\"state\"`             // 状态\n\tVersion           int    `json:\"version\"`           // 版本\n}\n", "req_add": "package req\n\n// AddProductMultipleBuyFreeReqDto 创建商品多买多送策略请求DTO\ntype AddProductMultipleBuyFreeReqDto struct {\n\tName              *string `json:\"name\"`              // 策略名称\n\tBuyProducts       *string `json:\"buyProducts\"`       // 购买商品列表\n\tBuyCount          *int    `json:\"buyCount\"`          // 购买数量\n\tExampleGiftProduct *string `json:\"exampleGiftProduct\"` // 赠送商品示例\n\tGiftPolicy        *string `json:\"giftPolicy\"`        // 赠送策略\n\tTimeSlots         *string `json:\"timeSlots\"`         // 适用时间段\n\tIsCanRepeatBuy    *bool   `json:\"isCanRepeatBuy\"`    // 是否可以重复购买\n}\n", "req_update": "package req\n\n// UpdateProductMultipleBuyFreeReqDto 更新商品多买多送策略请求DTO\ntype UpdateProductMultipleBuyFreeReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一id\n\tName              *string `json:\"name\"`              // 策略名称\n\tBuyProducts       *string `json:\"buyProducts\"`       // 购买商品列表\n\tBuyCount          *int    `json:\"buyCount\"`          // 购买数量\n\tExampleGiftProduct *string `json:\"exampleGiftProduct\"` // 赠送商品示例\n\tGiftPolicy        *string `json:\"giftPolicy\"`        // 赠送策略\n\tTimeSlots         *string `json:\"timeSlots\"`         // 适用时间段\n\tIsCanRepeatBuy    *bool   `json:\"isCanRepeatBuy\"`    // 是否可以重复购买\n}\n", "req_delete": "package req\n\n// DeleteProductMultipleBuyFreeReqDto 删除商品多买多送策略请求DTO\ntype DeleteProductMultipleBuyFreeReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\n// QueryProductMultipleBuyFreeReqDto 查询商品多买多送策略请求DTO\ntype QueryProductMultipleBuyFreeReqDto struct {\n\tId                *string `json:\"id\"`                // 唯一id\n\tName              *string `json:\"name\"`              // 策略名称\n\tBuyProducts       *string `json:\"buyProducts\"`       // 购买商品列表\n\tBuyCount          *int    `json:\"buyCount\"`          // 购买数量\n\tExampleGiftProduct *string `json:\"exampleGiftProduct\"` // 赠送商品示例\n\tGiftPolicy        *string `json:\"giftPolicy\"`        // 赠送策略\n\tTimeSlots         *string `json:\"timeSlots\"`         // 适用时间段\n\tIsCanRepeatBuy    *bool   `json:\"isCanRepeatBuy\"`    // 是否可以重复购买\n\tPageNum           *int    `json:\"pageNum\"`           // 页码\n\tPageSize          *int    `json:\"pageSize\"`          // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductMultipleBuyFreeTransfer struct {\n}\n\nfunc (transfer *ProductMultipleBuyFreeTransfer) PoToVo(po po.ProductMultipleBuyFree) vo.ProductMultipleBuyFreeVO {\n\tvo := vo.ProductMultipleBuyFreeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductMultipleBuyFreeTransfer) VoToPo(vo vo.ProductMultipleBuyFreeVO) po.ProductMultipleBuyFree {\n\tpo := po.ProductMultipleBuyFree{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductMultipleBuyFreeService struct {\n}\n\nfunc (service *ProductMultipleBuyFreeService) CreateProductMultipleBuyFree(logCtx *gin.Context, productMultipleBuyFree *po.ProductMultipleBuyFree) error {\n\treturn Save(productMultipleBuyFree)\n}\n\nfunc (service *ProductMultipleBuyFreeService) UpdateProductMultipleBuyFree(logCtx *gin.Context, productMultipleBuyFree *po.ProductMultipleBuyFree) error {\n\treturn Update(productMultipleBuyFree)\n}\n\nfunc (service *ProductMultipleBuyFreeService) DeleteProductMultipleBuyFree(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductMultipleBuyFree{Id: &id})\n}\n\nfunc (service *ProductMultipleBuyFreeService) FindProductMultipleBuyFreeById(logCtx *gin.Context, id string) (productMultipleBuyFree *po.ProductMultipleBuyFree, err error) {\n\tproductMultipleBuyFree = &po.ProductMultipleBuyFree{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productMultipleBuyFree).Error\n\treturn\n}\n\nfunc (service *ProductMultipleBuyFreeService) FindAllProductMultipleBuyFree(logCtx *gin.Context, reqDto *req.QueryProductMultipleBuyFreeReqDto) (list *[]po.ProductMultipleBuyFree, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductMultipleBuyFree{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.BuyCount != nil {\n\t\tdb = db.Where(\"buy_count=?\", *reqDto.BuyCount)\n\t}\n\tif reqDto.IsCanRepeatBuy != nil {\n\t\tdb = db.Where(\"is_can_repeat_buy=?\", *reqDto.IsCanRepeatBuy)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductMultipleBuyFree{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductMultipleBuyFreeService) FindAllProductMultipleBuyFreeWithPagination(logCtx *gin.Context, reqDto *req.QueryProductMultipleBuyFreeReqDto) (list *[]po.ProductMultipleBuyFree, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductMultipleBuyFree{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.BuyCount != nil {\n\t\tdb = db.Where(\"buy_count=?\", *reqDto.BuyCount)\n\t}\n\tif reqDto.IsCanRepeatBuy != nil {\n\t\tdb = db.Where(\"is_can_repeat_buy=?\", *reqDto.IsCanRepeatBuy)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductMultipleBuyFree{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductMultipleBuyFreeController struct{}\n\nvar (\n\tproductMultipleBuyFreeService  = impl.ProductMultipleBuyFreeService{}\n\tproductMultipleBuyFreeTransfer = transfer.ProductMultipleBuyFreeTransfer{}\n)\n\n// @Summary 添加商品多买多送策略\n// @Description 添加商品多买多送策略\n// @Tags 商品多买多送策略\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductMultipleBuyFreeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductMultipleBuyFreeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productMultipleBuyFree/add [post]\nfunc (controller *ProductMultipleBuyFreeController) AddProductMultipleBuyFree(ctx *gin.Context) {\n\treqDto := req.AddProductMultipleBuyFreeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductMultipleBuyFree := po.ProductMultipleBuyFree{}\n\tif reqDto.Name != nil {\n\t\tproductMultipleBuyFree.Name = reqDto.Name\n\t}\n\tif reqDto.BuyProducts != nil {\n\t\tproductMultipleBuyFree.BuyProducts = reqDto.BuyProducts\n\t}\n\tif reqDto.BuyCount != nil {\n\t\tproductMultipleBuyFree.BuyCount = reqDto.BuyCount\n\t}\n\tif reqDto.ExampleGiftProduct != nil {\n\t\tproductMultipleBuyFree.ExampleGiftProduct = reqDto.ExampleGiftProduct\n\t}\n\tif reqDto.GiftPolicy != nil {\n\t\tproductMultipleBuyFree.GiftPolicy = reqDto.GiftPolicy\n\t}\n\tif reqDto.TimeSlots != nil {\n\t\tproductMultipleBuyFree.TimeSlots = reqDto.TimeSlots\n\t}\n\tif reqDto.IsCanRepeatBuy != nil {\n\t\tproductMultipleBuyFree.IsCanRepeatBuy = reqDto.IsCanRepeatBuy\n\t}\n\n\terr = productMultipleBuyFreeService.CreateProductMultipleBuyFree(ctx, &productMultipleBuyFree)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productMultipleBuyFreeTransfer.PoToVo(productMultipleBuyFree))\n}\n\n// @Summary 更新商品多买多送策略\n// @Description 更新商品多买多送策略\n// @Tags 商品多买多送策略\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductMultipleBuyFreeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductMultipleBuyFreeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productMultipleBuyFree/update [post]\nfunc (controller *ProductMultipleBuyFreeController) UpdateProductMultipleBuyFree(ctx *gin.Context) {\n\treqDto := req.UpdateProductMultipleBuyFreeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproductMultipleBuyFree, err := productMultipleBuyFreeService.FindProductMultipleBuyFreeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tproductMultipleBuyFree.Name = reqDto.Name\n\t}\n\tif reqDto.BuyProducts != nil {\n\t\tproductMultipleBuyFree.BuyProducts = reqDto.BuyProducts\n\t}\n\tif reqDto.BuyCount != nil {\n\t\tproductMultipleBuyFree.BuyCount = reqDto.BuyCount\n\t}\n\tif reqDto.ExampleGiftProduct != nil {\n\t\tproductMultipleBuyFree.ExampleGiftProduct = reqDto.ExampleGiftProduct\n\t}\n\tif reqDto.GiftPolicy != nil {\n\t\tproductMultipleBuyFree.GiftPolicy = reqDto.GiftPolicy\n\t}\n\tif reqDto.TimeSlots != nil {\n\t\tproductMultipleBuyFree.TimeSlots = reqDto.TimeSlots\n\t}\n\tif reqDto.IsCanRepeatBuy != nil {\n\t\tproductMultipleBuyFree.IsCanRepeatBuy = reqDto.IsCanRepeatBuy\n\t}\n\n\terr = productMultipleBuyFreeService.UpdateProductMultipleBuyFree(ctx, productMultipleBuyFree)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productMultipleBuyFreeTransfer.PoToVo(*productMultipleBuyFree))\n}\n\n// @Summary 删除商品多买多送策略\n// @Description 删除商品多买多送策略\n// @Tags 商品多买多送策略\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductMultipleBuyFreeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productMultipleBuyFree/delete [post]\nfunc (controller *ProductMultipleBuyFreeController) DeleteProductMultipleBuyFree(ctx *gin.Context) {\n\treqDto := req.DeleteProductMultipleBuyFreeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = productMultipleBuyFreeService.DeleteProductMultipleBuyFree(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询商品多买多送策略\n// @Description 查询商品多买多送策略\n// @Tags 商品多买多送策略\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductMultipleBuyFreeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductMultipleBuyFreeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productMultipleBuyFree/query [post]\nfunc (controller *ProductMultipleBuyFreeController) QueryProductMultipleBuyFrees(ctx *gin.Context) {\n\treqDto := req.QueryProductMultipleBuyFreeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := productMultipleBuyFreeService.FindAllProductMultipleBuyFree(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.ProductMultipleBuyFreeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productMultipleBuyFreeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询商品多买多送策略列表\n// @Description 查询商品多买多送策略列表\n// @Tags 商品多买多送策略\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductMultipleBuyFreeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductMultipleBuyFreeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productMultipleBuyFree/list [post]\nfunc (controller *ProductMultipleBuyFreeController) ListProductMultipleBuyFrees(ctx *gin.Context) {\n\treqDto := req.QueryProductMultipleBuyFreeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := productMultipleBuyFreeService.FindAllProductMultipleBuyFreeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.ProductMultipleBuyFreeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductMultipleBuyFreeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productMultipleBuyFreeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductMultipleBuyFreeRoute struct {\n}\n\nfunc (s *ProductMultipleBuyFreeRoute) InitProductMultipleBuyFreeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductMultipleBuyFreeController := controller.ProductMultipleBuyFreeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productMultipleBuyFree/add\", productMultipleBuyFreeController.AddProductMultipleBuyFree)       // add\n\t\troute.POST(\"/api/productMultipleBuyFree/update\", productMultipleBuyFreeController.UpdateProductMultipleBuyFree)   // update\n\t\troute.POST(\"/api/productMultipleBuyFree/delete\", productMultipleBuyFreeController.DeleteProductMultipleBuyFree)   // delete\n\t\troute.POST(\"/api/productMultipleBuyFree/query\", productMultipleBuyFreeController.QueryProductMultipleBuyFrees)   // query\n\t\troute.POST(\"/api/productMultipleBuyFree/list\", productMultipleBuyFreeController.ListProductMultipleBuyFrees)     // list\n\t}\n}\n"}]