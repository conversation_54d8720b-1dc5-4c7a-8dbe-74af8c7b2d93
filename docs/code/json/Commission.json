[{"po": "package po\n\n// Commission 佣金实体\ntype Commission struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                   // ID\n\tCommissionId *string `gorm:\"column:commission_id;type:varchar(64);default:''\" json:\"commissionId\"` // 佣金ID\n\tAmount       *int64  `gorm:\"column:amount;type:bigint;default:0\" json:\"amount\"`                    // 金额\n\tDate         *int64  `gorm:\"column:date;type:int;default:0\" json:\"date\"`                          // 日期\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间戳\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间戳\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态值\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (Commission) TableName() string {\n\treturn \"commission\"\n}\n\nfunc (c Commission) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CommissionVO 佣金信息值对象\ntype CommissionVO struct {\n\tId           string `json:\"id\"`           // ID\n\tCommissionId string `json:\"commissionId\"` // 佣金ID\n\tAmount       int64  `json:\"amount\"`       // 金额\n\tDate         int64  `json:\"date\"`         // 日期\n\tCtime        int64  `json:\"ctime\"`        // 创建时间戳\n\tUtime        int64  `json:\"utime\"`        // 更新时间戳\n\tState        int    `json:\"state\"`        // 状态值\n\tVersion      int    `json:\"version\"`      // 版本号\n}\n", "req_add": "package req\n\n// AddCommissionReqDto 创建佣金请求DTO\ntype AddCommissionReqDto struct {\n\tCommissionId *string `json:\"commissionId\"` // 佣金ID\n\tAmount       *int64  `json:\"amount\"`       // 金额\n\tDate         *int64  `json:\"date\"`         // 日期\n}\n", "req_update": "package req\n\ntype UpdateCommissionReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tCommissionId *string `json:\"commissionId\"` // 佣金ID\n\tAmount       *int64  `json:\"amount\"`       // 金额\n\tDate         *int64  `json:\"date\"`         // 日期\n}\n", "req_delete": "package req\n\ntype DeleteCommissionReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCommissionReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tCommissionId *string `json:\"commissionId\"` // 佣金ID\n\tAmount       *int64  `json:\"amount\"`       // 金额\n\tDate         *int64  `json:\"date\"`         // 日期\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CommissionTransfer struct {\n}\n\nfunc (transfer *CommissionTransfer) PoToVo(po po.Commission) vo.CommissionVO {\n\tvo := vo.CommissionVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CommissionTransfer) VoToPo(vo vo.CommissionVO) po.Commission {\n\tpo := po.Commission{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommissionService struct {\n}\n\nfunc (service *CommissionService) CreateCommission(logCtx *gin.Context, commission *po.Commission) error {\n\treturn Save(commission)\n}\n\nfunc (service *CommissionService) UpdateCommission(logCtx *gin.Context, commission *po.Commission) error {\n\treturn Update(commission)\n}\n\nfunc (service *CommissionService) DeleteCommission(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Commission{Id: &id})\n}\n\nfunc (service *CommissionService) FindCommissionById(logCtx *gin.Context, id string) (commission *po.Commission, err error) {\n\tcommission = &po.Commission{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(commission).Error\n\treturn\n}\n\nfunc (service *CommissionService) FindAllCommission(logCtx *gin.Context, reqDto *req.QueryCommissionReqDto) (list *[]po.Commission, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Commission{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Commission{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CommissionService) FindAllCommissionWithPagination(logCtx *gin.Context, reqDto *req.QueryCommissionReqDto) (list *[]po.Commission, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Commission{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Commission{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommissionController struct{}\n\nvar (\n\tcommissionService  = impl.CommissionService{}\n\tcommissionTransfer = transfer.CommissionTransfer{}\n)\n\n// @Summary 添加佣金\n// @Description 添加佣金\n// @Tags 佣金\n// @Accept json\n// @Produce json\n// @Param body body req.AddCommissionReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CommissionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission/add [post]\nfunc (controller *CommissionController) AddCommission(ctx *gin.Context) {\n\treqDto := req.AddCommissionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcommission := po.Commission{}\n\tif reqDto.CommissionId != nil {\n\t\tcommission.CommissionId = reqDto.CommissionId\n\t}\n\tif reqDto.Amount != nil {\n\t\tcommission.Amount = reqDto.Amount\n\t}\n\tif reqDto.Date != nil {\n\t\tcommission.Date = reqDto.Date\n\t}\n\terr = commissionService.CreateCommission(ctx, &commission)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, commissionTransfer.PoToVo(commission))\n}\n\n// @Summary 更新佣金\n// @Description 更新佣金\n// @Tags 佣金\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCommissionReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CommissionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission/update [post]\nfunc (controller *CommissionController) UpdateCommission(ctx *gin.Context) {\n\treqDto := req.UpdateCommissionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcommission, err := commissionService.FindCommissionById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CommissionId != nil {\n\t\tcommission.CommissionId = reqDto.CommissionId\n\t}\n\tif reqDto.Amount != nil {\n\t\tcommission.Amount = reqDto.Amount\n\t}\n\tif reqDto.Date != nil {\n\t\tcommission.Date = reqDto.Date\n\t}\n\terr = commissionService.UpdateCommission(ctx, commission)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, commissionTransfer.PoToVo(*commission))\n}\n\n// @Summary 删除佣金\n// @Description 删除佣金\n// @Tags 佣金\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCommissionReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission/delete [post]\nfunc (controller *CommissionController) DeleteCommission(ctx *gin.Context) {\n\treqDto := req.DeleteCommissionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = commissionService.DeleteCommission(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询佣金\n// @Description 查询佣金\n// @Tags 佣金\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCommissionReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CommissionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission/query [post]\nfunc (controller *CommissionController) QueryCommissions(ctx *gin.Context) {\n\treqDto := req.QueryCommissionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := commissionService.FindAllCommission(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CommissionVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, commissionTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询佣金列表\n// @Description 查询佣金列表\n// @Tags 佣金\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCommissionReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CommissionVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/commission/list [post]\nfunc (a *CommissionController) ListCommissions(ctx *gin.Context) {\n\treqDto := req.QueryCommissionReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := commissionService.FindAllCommissionWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CommissionVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CommissionVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, commissionTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommissionRoute struct {\n}\n\nfunc (s *CommissionRoute) InitCommissionRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcommissionController := controller.CommissionController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/commission/add\", commissionController.AddCommission)    //add\n\t\troute.POST(\"/api/commission/update\", commissionController.UpdateCommission) //update\n\t\troute.POST(\"/api/commission/delete\", commissionController.DeleteCommission) //delete\n\t\troute.POST(\"/api/commission/query\", commissionController.QueryCommissions)     //query\n\t\troute.POST(\"/api/commission/list\", commissionController.ListCommissions)     //list\n\t}\n}\n"}]