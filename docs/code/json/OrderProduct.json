[{"po": "package po\n\n// OrderProduct 订单产品实体\ntype OrderProduct struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tSessionId     *string `gorm:\"column:session_id;type:varchar(64);default:''\" json:\"sessionId\"`      // 场次ID\n\tOrderNo       *string `gorm:\"column:order_no;type:varchar(64);default:''\" json:\"orderNo\"`        // 订单ID\n\tProductId     *string `gorm:\"column:product_id;type:varchar(64);default:''\" json:\"productId\"`      // 产品ID\n\tProductName   *string `gorm:\"column:product_name;type:varchar(255);default:''\" json:\"productName\"`  // 产品名称\n\tFlavors       *string `gorm:\"column:flavors;type:varchar(255);default:''\" json:\"flavors\"`         // 口味\n\tQuantity      *int    `gorm:\"column:quantity;type:int;default:0\" json:\"quantity\"`               // 数量\n\tUnit          *string `gorm:\"column:unit;type:varchar(64);default:''\" json:\"unit\"`              // 单位\n\tPayPrice      *int64  `gorm:\"column:pay_price;type:bigint;default:0\" json:\"payPrice\"`           // 支付价格\n\tMark          *string `gorm:\"column:mark;type:varchar(255);default:''\" json:\"mark\"`              // 产品显示备注\n\tPayStatus     *string `gorm:\"column:pay_status;type:varchar(64);default:''\" json:\"payStatus\"`      // 支付状态\n\tPrice         *int64  `gorm:\"column:price;type:bigint;default:0\" json:\"price\"`                 // 原价\n\tTotalAmount   *int64  `gorm:\"column:total_amount;type:bigint;default:0\" json:\"totalAmount\"`       // 总金额\n\tInPackageTag  *string `gorm:\"column:in_package_tag;type:varchar(64);default:''\" json:\"inPackageTag\"` // 套内商品标签\n\tSrc           *string `gorm:\"column:src;type:varchar(64);default:''\" json:\"src\"`                // 套餐来源\n\tCtime         *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                 // 创建时间\n\tUtime         *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                 // 更新时间\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (OrderProduct) TableName() string {\n\treturn \"order_product\"\n}\n\nfunc (o OrderProduct) GetId() string {\n\treturn *o.Id\n}\n", "vo": "package vo\n\n// OrderProductVO 订单产品值对象\ntype OrderProductVO struct {\n\tId            string `json:\"id\"`            // ID\n\tSessionId     string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       string `json:\"orderNo\"`       // 订单ID\n\tProductId     string `json:\"productId\"`     // 产品ID\n\tProductName   string `json:\"productName\"`   // 产品名称\n\tFlavors       string `json:\"flavors\"`       // 口味\n\tQuantity      int    `json:\"quantity\"`      // 数量\n\tUnit          string `json:\"unit\"`          // 单位\n\tPayPrice      int64  `json:\"payPrice\"`      // 支付价格\n\tMark          string `json:\"mark\"`          // 产品显示备注\n\tPayStatus     string `json:\"payStatus\"`     // 支付状态\n\tPrice         int64  `json:\"price\"`         // 原价\n\tTotalAmount   int64  `json:\"totalAmount\"`   // 总金额\n\tInPackageTag  string `json:\"inPackageTag\"`  // 套内商品标签\n\tSrc           string `json:\"src\"`           // 套餐来源\n\tCtime         int64  `json:\"ctime\"`         // 创建时间\n\tUtime         int64  `json:\"utime\"`         // 更新时间\n\tState         int    `json:\"state\"`         // 状态\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddOrderProductReqDto 创建订单产品请求DTO\ntype AddOrderProductReqDto struct {\n\tSessionId     *string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       *string `json:\"orderNo\"`       // 订单ID\n\tProductId     *string `json:\"productId\"`     // 产品ID\n\tProductName   *string `json:\"productName\"`   // 产品名称\n\tFlavors       *string `json:\"flavors\"`       // 口味\n\tQuantity      *int    `json:\"quantity\"`      // 数量\n\tUnit          *string `json:\"unit\"`          // 单位\n\tPayPrice      *int64  `json:\"payPrice\"`      // 支付价格\n\tMark          *string `json:\"mark\"`          // 产品显示备注\n\tPayStatus     *string `json:\"payStatus\"`     // 支付状态\n\tPrice         *int64  `json:\"price\"`         // 原价\n\tTotalAmount   *int64  `json:\"totalAmount\"`   // 总金额\n\tInPackageTag  *string `json:\"inPackageTag\"`  // 套内商品标签\n\tSrc           *string `json:\"src\"`           // 套餐来源\n}\n", "req_update": "package req\n\n// UpdateOrderProductReqDto 更新订单产品请求DTO\ntype UpdateOrderProductReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tSessionId     *string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       *string `json:\"orderNo\"`       // 订单ID\n\tProductId     *string `json:\"productId\"`     // 产品ID\n\tProductName   *string `json:\"productName\"`   // 产品名称\n\tFlavors       *string `json:\"flavors\"`       // 口味\n\tQuantity      *int    `json:\"quantity\"`      // 数量\n\tUnit          *string `json:\"unit\"`          // 单位\n\tPayPrice      *int64  `json:\"payPrice\"`      // 支付价格\n\tMark          *string `json:\"mark\"`          // 产品显示备注\n\tPayStatus     *string `json:\"payStatus\"`     // 支付状态\n\tPrice         *int64  `json:\"price\"`         // 原价\n\tTotalAmount   *int64  `json:\"totalAmount\"`   // 总金额\n\tInPackageTag  *string `json:\"inPackageTag\"`  // 套内商品标签\n\tSrc           *string `json:\"src\"`           // 套餐来源\n}\n", "req_delete": "package req\n\n// DeleteOrderProductReqDto 删除订单产品请求DTO\ntype DeleteOrderProductReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryOrderProductReqDto 查询订单产品请求DTO\ntype QueryOrderProductReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tSessionId     *string `json:\"sessionId\"`     // 场次ID\n\tOrderNo       *string `json:\"orderNo\"`       // 订单ID\n\tProductId     *string `json:\"productId\"`     // 产品ID\n\tProductName   *string `json:\"productName\"`   // 产品名称\n\tFlavors       *string `json:\"flavors\"`       // 口味\n\tQuantity      *int    `json:\"quantity\"`      // 数量\n\tUnit          *string `json:\"unit\"`          // 单位\n\tPayPrice      *int64  `json:\"payPrice\"`      // 支付价格\n\tMark          *string `json:\"mark\"`          // 产品显示备注\n\tPayStatus     *string `json:\"payStatus\"`     // 支付状态\n\tPrice         *int64  `json:\"price\"`         // 原价\n\tTotalAmount   *int64  `json:\"totalAmount\"`   // 总金额\n\tInPackageTag  *string `json:\"inPackageTag\"`  // 套内商品标签\n\tSrc           *string `json:\"src\"`           // 套餐来源\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype OrderProductTransfer struct {\n}\n\nfunc (transfer *OrderProductTransfer) PoToVo(po po.OrderProduct) vo.OrderProductVO {\n\tvo := vo.OrderProductVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *OrderProductTransfer) VoToPo(vo vo.OrderProductVO) po.OrderProduct {\n\tpo := po.OrderProduct{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderProductService struct {\n}\n\nfunc (service *OrderProductService) CreateOrderProduct(logCtx *gin.Context, orderProduct *po.OrderProduct) error {\n\treturn Save(orderProduct)\n}\n\nfunc (service *OrderProductService) UpdateOrderProduct(logCtx *gin.Context, orderProduct *po.OrderProduct) error {\n\treturn Update(orderProduct)\n}\n\nfunc (service *OrderProductService) DeleteOrderProduct(logCtx *gin.Context, id string) error {\n\treturn Delete(po.OrderProduct{Id: &id})\n}\n\nfunc (service *OrderProductService) FindOrderProductById(logCtx *gin.Context, id string) (orderProduct *po.OrderProduct, err error) {\n\torderProduct = &po.OrderProduct{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(orderProduct).Error\n\treturn\n}\n\nfunc (service *OrderProductService) FindAllOrderProduct(logCtx *gin.Context, reqDto *req.QueryOrderProductReqDto) (list *[]po.OrderProduct, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OrderProduct{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.OrderNo != nil && *reqDto.OrderNo != \"\" {\n\t\tdb = db.Where(\"order_no=?\", *reqDto.OrderNo)\n\t}\n\tif reqDto.ProductId != nil && *reqDto.ProductId != \"\" {\n\t\tdb = db.Where(\"product_id=?\", *reqDto.ProductId)\n\t}\n\tif reqDto.PayStatus != nil && *reqDto.PayStatus != \"\" {\n\t\tdb = db.Where(\"pay_status=?\", *reqDto.PayStatus)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.OrderProduct{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *OrderProductService) FindAllOrderProductWithPagination(logCtx *gin.Context, reqDto *req.QueryOrderProductReqDto) (list *[]po.OrderProduct, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OrderProduct{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.OrderNo != nil && *reqDto.OrderNo != \"\" {\n\t\tdb = db.Where(\"order_no=?\", *reqDto.OrderNo)\n\t}\n\tif reqDto.ProductId != nil && *reqDto.ProductId != \"\" {\n\t\tdb = db.Where(\"product_id=?\", *reqDto.ProductId)\n\t}\n\tif reqDto.PayStatus != nil && *reqDto.PayStatus != \"\" {\n\t\tdb = db.Where(\"pay_status=?\", *reqDto.PayStatus)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.OrderProduct{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderProductController struct{}\n\nvar (\n\torderProductService  = impl.OrderProductService{}\n\torderProductTransfer = transfer.OrderProductTransfer{}\n)\n\n// @Summary 添加订单产品\n// @Description 添加订单产品\n// @Tags 订单产品\n// @Accept json\n// @Produce json\n// @Param body body req.AddOrderProductReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderProduct/add [post]\nfunc (controller *OrderProductController) AddOrderProduct(ctx *gin.Context) {\n\treqDto := req.AddOrderProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\torderProduct := po.OrderProduct{}\n\tif reqDto.SessionId != nil {\n\t\torderProduct.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.OrderNo != nil {\n\t\torderProduct.OrderNo = reqDto.OrderNo\n\t}\n\tif reqDto.ProductId != nil {\n\t\torderProduct.ProductId = reqDto.ProductId\n\t}\n\tif reqDto.ProductName != nil {\n\t\torderProduct.ProductName = reqDto.ProductName\n\t}\n\tif reqDto.Flavors != nil {\n\t\torderProduct.Flavors = reqDto.Flavors\n\t}\n\tif reqDto.Quantity != nil {\n\t\torderProduct.Quantity = reqDto.Quantity\n\t}\n\tif reqDto.Unit != nil {\n\t\torderProduct.Unit = reqDto.Unit\n\t}\n\tif reqDto.PayPrice != nil {\n\t\torderProduct.PayPrice = reqDto.PayPrice\n\t}\n\tif reqDto.Mark != nil {\n\t\torderProduct.Mark = reqDto.Mark\n\t}\n\tif reqDto.PayStatus != nil {\n\t\torderProduct.PayStatus = reqDto.PayStatus\n\t}\n\tif reqDto.Price != nil {\n\t\torderProduct.Price = reqDto.Price\n\t}\n\tif reqDto.TotalAmount != nil {\n\t\torderProduct.TotalAmount = reqDto.TotalAmount\n\t}\n\tif reqDto.InPackageTag != nil {\n\t\torderProduct.InPackageTag = reqDto.InPackageTag\n\t}\n\tif reqDto.Src != nil {\n\t\torderProduct.Src = reqDto.Src\n\t}\n\n\terr = orderProductService.CreateOrderProduct(ctx, &orderProduct)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderProductTransfer.PoToVo(orderProduct))\n}\n\n// @Summary 更新订单产品\n// @Description 更新订单产品\n// @Tags 订单产品\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateOrderProductReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OrderProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderProduct/update [post]\nfunc (controller *OrderProductController) UpdateOrderProduct(ctx *gin.Context) {\n\treqDto := req.UpdateOrderProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\torderProduct, err := orderProductService.FindOrderProductById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.SessionId != nil {\n\t\torderProduct.SessionId = reqDto.SessionId\n\t}\n\tif reqDto.OrderNo != nil {\n\t\torderProduct.OrderNo = reqDto.OrderNo\n\t}\n\tif reqDto.ProductId != nil {\n\t\torderProduct.ProductId = reqDto.ProductId\n\t}\n\tif reqDto.ProductName != nil {\n\t\torderProduct.ProductName = reqDto.ProductName\n\t}\n\tif reqDto.Flavors != nil {\n\t\torderProduct.Flavors = reqDto.Flavors\n\t}\n\tif reqDto.Quantity != nil {\n\t\torderProduct.Quantity = reqDto.Quantity\n\t}\n\tif reqDto.Unit != nil {\n\t\torderProduct.Unit = reqDto.Unit\n\t}\n\tif reqDto.PayPrice != nil {\n\t\torderProduct.PayPrice = reqDto.PayPrice\n\t}\n\tif reqDto.Mark != nil {\n\t\torderProduct.Mark = reqDto.Mark\n\t}\n\tif reqDto.PayStatus != nil {\n\t\torderProduct.PayStatus = reqDto.PayStatus\n\t}\n\tif reqDto.Price != nil {\n\t\torderProduct.Price = reqDto.Price\n\t}\n\tif reqDto.TotalAmount != nil {\n\t\torderProduct.TotalAmount = reqDto.TotalAmount\n\t}\n\tif reqDto.InPackageTag != nil {\n\t\torderProduct.InPackageTag = reqDto.InPackageTag\n\t}\n\tif reqDto.Src != nil {\n\t\torderProduct.Src = reqDto.Src\n\t}\n\n\terr = orderProductService.UpdateOrderProduct(ctx, orderProduct)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, orderProductTransfer.PoToVo(*orderProduct))\n}\n\n// @Summary 删除订单产品\n// @Description 删除订单产品\n// @Tags 订单产品\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteOrderProductReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderProduct/delete [post]\nfunc (controller *OrderProductController) DeleteOrderProduct(ctx *gin.Context) {\n\treqDto := req.DeleteOrderProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = orderProductService.DeleteOrderProduct(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询订单产品\n// @Description 查询订单产品\n// @Tags 订单产品\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderProductReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderProduct/query [post]\nfunc (controller *OrderProductController) QueryOrderProducts(ctx *gin.Context) {\n\treqDto := req.QueryOrderProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := orderProductService.FindAllOrderProduct(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.OrderProductVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, orderProductTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询订单产品列表\n// @Description 查询订单产品列表\n// @Tags 订单产品\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOrderProductReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OrderProductVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/orderProduct/list [post]\nfunc (a *OrderProductController) ListOrderProducts(ctx *gin.Context) {\n\treqDto := req.QueryOrderProductReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := orderProductService.FindAllOrderProductWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.OrderProductVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.OrderProductVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, orderProductTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OrderProductRoute struct {\n}\n\nfunc (s *OrderProductRoute) InitOrderProductRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\torderProductController := controller.OrderProductController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/orderProduct/add\", orderProductController.AddOrderProduct)       //add\n\t\troute.POST(\"/api/orderProduct/update\", orderProductController.UpdateOrderProduct) //update\n\t\troute.POST(\"/api/orderProduct/delete\", orderProductController.DeleteOrderProduct) //delete\n\t\troute.POST(\"/api/orderProduct/query\", orderProductController.QueryOrderProducts)  //query\n\t\troute.POST(\"/api/orderProduct/list\", orderProductController.ListOrderProducts)   //list\n\t}\n}\n"}]