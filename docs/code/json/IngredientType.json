[{"po": "package po\n\n// IngredientType 配料类型实体\ntype IngredientType struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName        *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`               // 配料类型名称\n\tDescription *string `gorm:\"column:description;type:varchar(255);default:''\" json:\"description\"` // 配料类型描述\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                      // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                      // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (IngredientType) TableName() string {\n\treturn \"ingredient_type\"\n}\n\nfunc (i IngredientType) GetId() string {\n\treturn *i.Id\n}\n", "vo": "package vo\n\n// IngredientTypeVO 配料类型值对象\ntype IngredientTypeVO struct {\n\tId          string `json:\"id\"`          // ID\n\tName        string `json:\"name\"`        // 配料类型名称\n\tDescription string `json:\"description\"` // 配料类型描述\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddIngredientTypeReqDto 创建配料类型请求DTO\ntype AddIngredientTypeReqDto struct {\n\tName        *string `json:\"name\"`        // 配料类型名称\n\tDescription *string `json:\"description\"` // 配料类型描述\n}\n", "req_update": "package req\n\ntype UpdateIngredientTypeReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tName        *string `json:\"name\"`        // 配料类型名称\n\tDescription *string `json:\"description\"` // 配料类型描述\n}\n", "req_delete": "package req\n\ntype DeleteIngredientTypeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryIngredientTypeReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tName        *string `json:\"name\"`        // 配料类型名称\n\tDescription *string `json:\"description\"` // 配料类型描述\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype IngredientTypeTransfer struct {\n}\n\nfunc (transfer *IngredientTypeTransfer) PoToVo(po po.IngredientType) vo.IngredientTypeVO {\n\tvo := vo.IngredientTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *IngredientTypeTransfer) VoToPo(vo vo.IngredientTypeVO) po.IngredientType {\n\tpo := po.IngredientType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype IngredientTypeService struct {\n}\n\nfunc (service *IngredientTypeService) CreateIngredientType(logCtx *gin.Context, ingredientType *po.IngredientType) error {\n\treturn Save(ingredientType)\n}\n\nfunc (service *IngredientTypeService) UpdateIngredientType(logCtx *gin.Context, ingredientType *po.IngredientType) error {\n\treturn Update(ingredientType)\n}\n\nfunc (service *IngredientTypeService) DeleteIngredientType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.IngredientType{Id: &id})\n}\n\nfunc (service *IngredientTypeService) FindIngredientTypeById(logCtx *gin.Context, id string) (ingredientType *po.IngredientType, err error) {\n\tingredientType = &po.IngredientType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(ingredientType).Error\n\treturn\n}\n\nfunc (service *IngredientTypeService) FindAllIngredientType(logCtx *gin.Context, reqDto *req.QueryIngredientTypeReqDto) (list *[]po.IngredientType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.IngredientType{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Description != nil && *reqDto.Description != \"\" {\n\t\tdb = db.Where(\"description LIKE ?\", \"%\"+*reqDto.Description+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.IngredientType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *IngredientTypeService) FindAllIngredientTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryIngredientTypeReqDto) (list *[]po.IngredientType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.IngredientType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Description != nil && *reqDto.Description != \"\" {\n\t\tdb = db.Where(\"description LIKE ?\", \"%\"+*reqDto.Description+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.IngredientType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype IngredientTypeController struct{}\n\nvar (\n\tingredientTypeService  = impl.IngredientTypeService{}\n\tingredientTypeTransfer = transfer.IngredientTypeTransfer{}\n)\n\n// @Summary 添加配料类型\n// @Description 添加配料类型\n// @Tags 配料类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddIngredientTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.IngredientTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/ingredient-type/add [post]\nfunc (controller *IngredientTypeController) AddIngredientType(ctx *gin.Context) {\n\treqDto := req.AddIngredientTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tingredientType := po.IngredientType{}\n\tif reqDto.Name != nil {\n\t\tingredientType.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\tingredientType.Description = reqDto.Description\n\t}\n\n\terr = ingredientTypeService.CreateIngredientType(ctx, &ingredientType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, ingredientTypeTransfer.PoToVo(ingredientType))\n}\n\n// @Summary 更新配料类型\n// @Description 更新配料类型\n// @Tags 配料类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateIngredientTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.IngredientTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/ingredient-type/update [post]\nfunc (controller *IngredientTypeController) UpdateIngredientType(ctx *gin.Context) {\n\treqDto := req.UpdateIngredientTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tingredientType, err := ingredientTypeService.FindIngredientTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tingredientType.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\tingredientType.Description = reqDto.Description\n\t}\n\n\terr = ingredientTypeService.UpdateIngredientType(ctx, ingredientType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, ingredientTypeTransfer.PoToVo(*ingredientType))\n}\n\n// @Summary 删除配料类型\n// @Description 删除配料类型\n// @Tags 配料类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteIngredientTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/ingredient-type/delete [post]\nfunc (controller *IngredientTypeController) DeleteIngredientType(ctx *gin.Context) {\n\treqDto := req.DeleteIngredientTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = ingredientTypeService.DeleteIngredientType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询配料类型\n// @Description 查询配料类型\n// @Tags 配料类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryIngredientTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.IngredientTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/ingredient-type/query [post]\nfunc (controller *IngredientTypeController) QueryIngredientTypes(ctx *gin.Context) {\n\treqDto := req.QueryIngredientTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := ingredientTypeService.FindAllIngredientType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.IngredientTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, ingredientTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询配料类型列表\n// @Description 查询配料类型列表\n// @Tags 配料类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryIngredientTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.IngredientTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/ingredient-type/list [post]\nfunc (controller *IngredientTypeController) ListIngredientTypes(ctx *gin.Context) {\n\treqDto := req.QueryIngredientTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := ingredientTypeService.FindAllIngredientTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.IngredientTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.IngredientTypeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, ingredientTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype IngredientTypeRoute struct {\n}\n\nfunc (s *IngredientTypeRoute) InitIngredientTypeRouter(g *gin.Engine) {\n\tingredientTypeController := controller.IngredientTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/ingredient-type/add\", ingredientTypeController.AddIngredientType)       // add\n\t\troute.POST(\"/api/ingredient-type/update\", ingredientTypeController.UpdateIngredientType)   // update\n\t\troute.POST(\"/api/ingredient-type/delete\", ingredientTypeController.DeleteIngredientType)   // delete\n\t\troute.POST(\"/api/ingredient-type/query\", ingredientTypeController.QueryIngredientTypes)   // query\n\t\troute.POST(\"/api/ingredient-type/list\", ingredientTypeController.ListIngredientTypes)     // list\n\t}\n}\n"}]