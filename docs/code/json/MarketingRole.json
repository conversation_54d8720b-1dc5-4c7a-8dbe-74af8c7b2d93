[{"po": "package po\n\n// MarketingRole 营销角色实体\ntype MarketingRole struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tName        *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`               // 营销角色名称\n\tDescription *string `gorm:\"column:description;type:varchar(255);default:''\" json:\"description\"` // 角色描述\n\tPermissions *string `gorm:\"column:permissions;type:text;\" json:\"permissions\"`                    // 角色权限\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                       // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                       // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                       // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                   // 版本号\n}\n\n// TableName 设置表名\nfunc (MarketingRole) TableName() string {\n\treturn \"marketing_role\"\n}\n\nfunc (m MarketingRole) GetId() string {\n\treturn *m.Id\n}", "vo": "package vo\n\n// MarketingRoleVO 营销角色值对象\ntype MarketingRoleVO struct {\n\tId          string `json:\"id\"`          // ID\n\tName        string `json:\"name\"`        // 营销角色名称\n\tDescription string `json:\"description\"` // 角色描述\n\tPermissions string `json:\"permissions\"` // 角色权限\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}", "req_add": "package req\n\n// AddMarketingRoleReqDto 创建营销角色请求DTO\ntype AddMarketingRoleReqDto struct {\n\tName        *string `json:\"name\"`        // 营销角色名称\n\tDescription *string `json:\"description\"` // 角色描述\n\tPermissions *string `json:\"permissions\"` // 角色权限\n}", "req_update": "package req\n\ntype UpdateMarketingRoleReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tName        *string `json:\"name\"`        // 营销角色名称\n\tDescription *string `json:\"description\"` // 角色描述\n\tPermissions *string `json:\"permissions\"` // 角色权限\n}", "req_delete": "package req\n\ntype DeleteMarketingRoleReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryMarketingRoleReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tName        *string `json:\"name\"`        // 营销角色名称\n\tDescription *string `json:\"description\"` // 角色描述\n\tPermissions *string `json:\"permissions\"` // 角色权限\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MarketingRoleTransfer struct {\n}\n\nfunc (transfer *MarketingRoleTransfer) PoToVo(po po.MarketingRole) vo.MarketingRoleVO {\n\tvo := vo.MarketingRoleVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MarketingRoleTransfer) VoToPo(vo vo.MarketingRoleVO) po.MarketingRole {\n\tpo := po.MarketingRole{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketingRoleService struct {\n}\n\nfunc (service *MarketingRoleService) CreateMarketingRole(logCtx *gin.Context, marketingRole *po.MarketingRole) error {\n\treturn Save(marketingRole)\n}\n\nfunc (service *MarketingRoleService) UpdateMarketingRole(logCtx *gin.Context, marketingRole *po.MarketingRole) error {\n\treturn Update(marketingRole)\n}\n\nfunc (service *MarketingRoleService) DeleteMarketingRole(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MarketingRole{Id: &id})\n}\n\nfunc (service *MarketingRoleService) FindMarketingRoleById(logCtx *gin.Context, id string) (marketingRole *po.MarketingRole, err error) {\n\tmarketingRole = &po.MarketingRole{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(marketingRole).Error\n\treturn\n}\n\nfunc (service *MarketingRoleService) FindAllMarketingRole(logCtx *gin.Context, reqDto *req.QueryMarketingRoleReqDto) (list *[]po.MarketingRole, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MarketingRole{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MarketingRole{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MarketingRoleService) FindAllMarketingRoleWithPagination(logCtx *gin.Context, reqDto *req.QueryMarketingRoleReqDto) (list *[]po.MarketingRole, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MarketingRole{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MarketingRole{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketingRoleController struct{}\n\nvar (\n\tmarketingRoleService  = impl.MarketingRoleService{}\n\tmarketingRoleTransfer = transfer.MarketingRoleTransfer{}\n)\n\n// @Summary 添加营销角色\n// @Description 添加营销角色\n// @Tags 营销角色\n// @Accept json\n// @Produce json\n// @Param body body req.AddMarketingRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MarketingRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-role/add [post]\nfunc (controller *MarketingRoleController) AddMarketingRole(ctx *gin.Context) {\n\treqDto := req.AddMarketingRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmarketingRole := po.MarketingRole{}\n\tif reqDto.Name != nil {\n\t\tmarketingRole.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\tmarketingRole.Description = reqDto.Description\n\t}\n\tif reqDto.Permissions != nil {\n\t\tmarketingRole.Permissions = reqDto.Permissions\n\t}\n\terr = marketingRoleService.CreateMarketingRole(ctx, &marketingRole)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, marketingRoleTransfer.PoToVo(marketingRole))\n}\n\n// @Summary 更新营销角色\n// @Description 更新营销角色\n// @Tags 营销角色\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMarketingRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MarketingRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-role/update [post]\nfunc (controller *MarketingRoleController) UpdateMarketingRole(ctx *gin.Context) {\n\treqDto := req.UpdateMarketingRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmarketingRole, err := marketingRoleService.FindMarketingRoleById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tmarketingRole.Name = reqDto.Name\n\t}\n\tif reqDto.Description != nil {\n\t\tmarketingRole.Description = reqDto.Description\n\t}\n\tif reqDto.Permissions != nil {\n\t\tmarketingRole.Permissions = reqDto.Permissions\n\t}\n\terr = marketingRoleService.UpdateMarketingRole(ctx, marketingRole)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, marketingRoleTransfer.PoToVo(*marketingRole))\n}\n\n// @Summary 删除营销角色\n// @Description 删除营销角色\n// @Tags 营销角色\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMarketingRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-role/delete [post]\nfunc (controller *MarketingRoleController) DeleteMarketingRole(ctx *gin.Context) {\n\treqDto := req.DeleteMarketingRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = marketingRoleService.DeleteMarketingRole(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询营销角色\n// @Description 查询营销角色\n// @Tags 营销角色\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMarketingRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MarketingRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-role/query [post]\nfunc (controller *MarketingRoleController) QueryMarketingRoles(ctx *gin.Context) {\n\treqDto := req.QueryMarketingRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := marketingRoleService.FindAllMarketingRole(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MarketingRoleVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, marketingRoleTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询营销角色列表\n// @Description 查询营销角色列表\n// @Tags 营销角色\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMarketingRoleReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MarketingRoleVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-role/list [post]\nfunc (a *MarketingRoleController) ListMarketingRoles(ctx *gin.Context) {\n\treqDto := req.QueryMarketingRoleReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := marketingRoleService.FindAllMarketingRoleWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MarketingRoleVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MarketingRoleVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, marketingRoleTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketingRoleRoute struct {\n}\n\nfunc (s *MarketingRoleRoute) InitMarketingRoleRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmarketingRoleController := controller.MarketingRoleController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/marketing-role/add\", marketingRoleController.AddMarketingRole)    //add\n\t\troute.POST(\"/api/marketing-role/update\", marketingRoleController.UpdateMarketingRole) //update\n\t\troute.POST(\"/api/marketing-role/delete\", marketingRoleController.DeleteMarketingRole) //delete\n\t\troute.POST(\"/api/marketing-role/query\", marketingRoleController.QueryMarketingRoles)     //query\n\t\troute.POST(\"/api/marketing-role/list\", marketingRoleController.ListMarketingRoles)     //list\n\t}\n}"}]