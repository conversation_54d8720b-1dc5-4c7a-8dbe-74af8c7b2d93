[{"po": "package po\n\n// PhoneBlacklist 电话黑名单实体\ntype PhoneBlacklist struct {\n\tId          *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tPhoneNumber *string `gorm:\"column:phone_number;type:varchar(64);default:''\" json:\"phoneNumber\"` // 电话号码\n\tStoreId     *string `gorm:\"column:store_id;type:varchar(64);default:''\" json:\"storeId\"`       // 所属店铺ID\n\tCtime       *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间戳\n\tUtime       *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间戳\n\tState       *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态值\n\tVersion     *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (PhoneBlacklist) TableName() string {\n\treturn \"phone_blacklist\"\n}\n\nfunc (b PhoneBlacklist) GetId() string {\n\treturn *b.Id\n}\n", "vo": "package vo\n\n// PhoneBlacklistVO 电话黑名单值对象\ntype PhoneBlacklistVO struct {\n\tId          string `json:\"id\"`          // ID\n\tPhoneNumber string `json:\"phoneNumber\"` // 电话号码\n\tStoreId     string `json:\"storeId\"`     // 所属店铺ID\n\tCtime       int64  `json:\"ctime\"`       // 创建时间戳\n\tUtime       int64  `json:\"utime\"`       // 更新时间戳\n\tState       int    `json:\"state\"`       // 状态值\n\tVersion     int    `json:\"version\"`     // 版本号\n}\n", "req_add": "package req\n\n// AddPhoneBlacklistReqDto 创建电话黑名单请求DTO\ntype AddPhoneBlacklistReqDto struct {\n\tPhoneNumber *string `json:\"phoneNumber\"` // 电话号码\n\tStoreId     *string `json:\"storeId\"`     // 所属店铺ID\n}\n", "req_update": "package req\n\ntype UpdatePhoneBlacklistReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tPhoneNumber *string `json:\"phoneNumber\"` // 电话号码\n\tStoreId     *string `json:\"storeId\"`     // 所属店铺ID\n}\n", "req_delete": "package req\n\ntype DeletePhoneBlacklistReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryPhoneBlacklistReqDto struct {\n\tId          *string `json:\"id\"`          // ID\n\tPhoneNumber *string `json:\"phoneNumber\"` // 电话号码\n\tStoreId     *string `json:\"storeId\"`     // 所属店铺ID\n\tPageNum     *int    `json:\"pageNum\"`     // 页码\n\tPageSize    *int    `json:\"pageSize\"`    // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PhoneBlacklistTransfer struct {\n}\n\nfunc (transfer *PhoneBlacklistTransfer) PoToVo(po po.PhoneBlacklist) vo.PhoneBlacklistVO {\n\tvo := vo.PhoneBlacklistVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PhoneBlacklistTransfer) VoToPo(vo vo.PhoneBlacklistVO) po.PhoneBlacklist {\n\tpo := po.PhoneBlacklist{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PhoneBlacklistService struct {\n}\n\nfunc (service *PhoneBlacklistService) CreatePhoneBlacklist(logCtx *gin.Context, phoneBlacklist *po.PhoneBlacklist) error {\n\treturn Save(phoneBlacklist)\n}\n\nfunc (service *PhoneBlacklistService) UpdatePhoneBlacklist(logCtx *gin.Context, phoneBlacklist *po.PhoneBlacklist) error {\n\treturn Update(phoneBlacklist)\n}\n\nfunc (service *PhoneBlacklistService) DeletePhoneBlacklist(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PhoneBlacklist{Id: &id})\n}\n\nfunc (service *PhoneBlacklistService) FindPhoneBlacklistById(logCtx *gin.Context, id string) (phoneBlacklist *po.PhoneBlacklist, err error) {\n\tphoneBlacklist = &po.PhoneBlacklist{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(phoneBlacklist).Error\n\treturn\n}\n\nfunc (service *PhoneBlacklistService) FindAllPhoneBlacklist(logCtx *gin.Context, reqDto *req.QueryPhoneBlacklistReqDto) (list *[]po.PhoneBlacklist, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PhoneBlacklist{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.PhoneNumber != nil && *reqDto.PhoneNumber != \"\" {\n\t\tdb = db.Where(\"phone_number=?\", *reqDto.PhoneNumber)\n\t}\n\tif reqDto.StoreId != nil && *reqDto.StoreId != \"\" {\n\t\tdb = db.Where(\"store_id=?\", *reqDto.StoreId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PhoneBlacklist{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PhoneBlacklistService) FindAllPhoneBlacklistWithPagination(logCtx *gin.Context, reqDto *req.QueryPhoneBlacklistReqDto) (list *[]po.PhoneBlacklist, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PhoneBlacklist{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.PhoneNumber != nil && *reqDto.PhoneNumber != \"\" {\n\t\tdb = db.Where(\"phone_number=?\", *reqDto.PhoneNumber)\n\t}\n\tif reqDto.StoreId != nil && *reqDto.StoreId != \"\" {\n\t\tdb = db.Where(\"store_id=?\", *reqDto.StoreId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PhoneBlacklist{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PhoneBlacklistController struct{}\n\nvar (\n\tphoneBlacklistService  = impl.PhoneBlacklistService{}\n\tphoneBlacklistTransfer = transfer.PhoneBlacklistTransfer{}\n)\n\n// @Summary 添加电话黑名单\n// @Description 添加电话黑名单\n// @Tags 电话黑名单\n// @Accept json\n// @Produce json\n// @Param body body req.AddPhoneBlacklistReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PhoneBlacklistVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/phoneBlacklist/add [post]\nfunc (controller *PhoneBlacklistController) AddPhoneBlacklist(ctx *gin.Context) {\n\treqDto := req.AddPhoneBlacklistReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tphoneBlacklist := po.PhoneBlacklist{}\n\tif reqDto.PhoneNumber != nil {\n\t\tphoneBlacklist.PhoneNumber = reqDto.PhoneNumber\n\t}\n\tif reqDto.StoreId != nil {\n\t\tphoneBlacklist.StoreId = reqDto.StoreId\n\t}\n\terr = phoneBlacklistService.CreatePhoneBlacklist(ctx, &phoneBlacklist)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, phoneBlacklistTransfer.PoToVo(phoneBlacklist))\n}\n\n// @Summary 更新电话黑名单\n// @Description 更新电话黑名单\n// @Tags 电话黑名单\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePhoneBlacklistReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PhoneBlacklistVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/phoneBlacklist/update [post]\nfunc (controller *PhoneBlacklistController) UpdatePhoneBlacklist(ctx *gin.Context) {\n\treqDto := req.UpdatePhoneBlacklistReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tphoneBlacklist, err := phoneBlacklistService.FindPhoneBlacklistById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.PhoneNumber != nil {\n\t\tphoneBlacklist.PhoneNumber = reqDto.PhoneNumber\n\t}\n\tif reqDto.StoreId != nil {\n\t\tphoneBlacklist.StoreId = reqDto.StoreId\n\t}\n\terr = phoneBlacklistService.UpdatePhoneBlacklist(ctx, phoneBlacklist)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, phoneBlacklistTransfer.PoToVo(*phoneBlacklist))\n}\n\n// @Summary 删除电话黑名单\n// @Description 删除电话黑名单\n// @Tags 电话黑名单\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePhoneBlacklistReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/phoneBlacklist/delete [post]\nfunc (controller *PhoneBlacklistController) DeletePhoneBlacklist(ctx *gin.Context) {\n\treqDto := req.DeletePhoneBlacklistReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = phoneBlacklistService.DeletePhoneBlacklist(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询电话黑名单\n// @Description 查询电话黑名单\n// @Tags 电话黑名单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPhoneBlacklistReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PhoneBlacklistVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/phoneBlacklist/query [post]\nfunc (controller *PhoneBlacklistController) QueryPhoneBlacklists(ctx *gin.Context) {\n\treqDto := req.QueryPhoneBlacklistReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := phoneBlacklistService.FindAllPhoneBlacklist(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PhoneBlacklistVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, phoneBlacklistTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询电话黑名单列表\n// @Description 查询电话黑名单列表\n// @Tags 电话黑名单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPhoneBlacklistReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PhoneBlacklistVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/phoneBlacklist/list [post]\nfunc (a *PhoneBlacklistController) ListPhoneBlacklists(ctx *gin.Context) {\n\treqDto := req.QueryPhoneBlacklistReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := phoneBlacklistService.FindAllPhoneBlacklistWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PhoneBlacklistVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PhoneBlacklistVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, phoneBlacklistTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PhoneBlacklistRoute struct {\n}\n\nfunc (s *PhoneBlacklistRoute) InitPhoneBlacklistRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tphoneBlacklistController := controller.PhoneBlacklistController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/phoneBlacklist/add\", phoneBlacklistController.AddPhoneBlacklist)       //add\n\t\troute.POST(\"/api/phoneBlacklist/update\", phoneBlacklistController.UpdatePhoneBlacklist) //update\n\t\troute.POST(\"/api/phoneBlacklist/delete\", phoneBlacklistController.DeletePhoneBlacklist) //delete\n\t\troute.POST(\"/api/phoneBlacklist/query\", phoneBlacklistController.QueryPhoneBlacklists)  //query\n\t\troute.POST(\"/api/phoneBlacklist/list\", phoneBlacklistController.ListPhoneBlacklists)    //list\n\t}\n}\n"}]