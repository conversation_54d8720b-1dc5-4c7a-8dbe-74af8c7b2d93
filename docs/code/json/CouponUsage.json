[{"po": "package po\n\n// CouponUsage 优惠券使用记录实体\ntype CouponUsage struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tUsageTime    *int64  `gorm:\"column:usage_time;type:int;default:0\" json:\"usageTime\"`                // 使用时间\n\tUsageAmount  *int64  `gorm:\"column:usage_amount;type:int;default:0\" json:\"usageAmount\"`            // 使用金额\n\tCouponId     *string `gorm:\"column:coupon_id;type:varchar(64);default:''\" json:\"couponId\"`         // 优惠券ID\n\tMemberId     *string `gorm:\"column:member_id;type:varchar(64);default:''\" json:\"memberId\"`         // 会员ID\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间戳\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间戳\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态值\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (CouponUsage) TableName() string {\n\treturn \"coupon_usage\"\n}\n\nfunc (c CouponUsage) GetId() string {\n\treturn *c.Id\n}", "vo": "package vo\n\n// CouponUsageVO 优惠券使用记录值对象\ntype CouponUsageVO struct {\n\tId           string `json:\"id\"`           // ID\n\tUsageTime    int64  `json:\"usageTime\"`    // 使用时间\n\tUsageAmount  int64  `json:\"usageAmount\"`  // 使用金额\n\tCouponId     string `json:\"couponId\"`     // 优惠券ID\n\tMemberId     string `json:\"memberId\"`     // 会员ID\n\tCtime        int64  `json:\"ctime\"`        // 创建时间戳\n\tUtime        int64  `json:\"utime\"`        // 更新时间戳\n\tState        int    `json:\"state\"`        // 状态值\n\tVersion      int    `json:\"version\"`      // 版本号\n}", "req_add": "package req\n\n// AddCouponUsageReqDto 创建优惠券使用记录请求DTO\ntype AddCouponUsageReqDto struct {\n\tUsageTime    *int64  `json:\"usageTime\"`    // 使用时间\n\tUsageAmount  *int64  `json:\"usageAmount\"`  // 使用金额\n\tCouponId     *string `json:\"couponId\"`     // 优惠券ID\n\tMemberId     *string `json:\"memberId\"`     // 会员ID\n}", "req_update": "package req\n\ntype UpdateCouponUsageReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tUsageTime    *int64  `json:\"usageTime\"`    // 使用时间\n\tUsageAmount  *int64  `json:\"usageAmount\"`  // 使用金额\n\tCouponId     *string `json:\"couponId\"`     // 优惠券ID\n\tMemberId     *string `json:\"memberId\"`     // 会员ID\n}", "req_delete": "package req\n\ntype DeleteCouponUsageReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryCouponUsageReqDto struct {\n\tId           *string `json:\"id\"`           // ID\n\tUsageTime    *int64  `json:\"usageTime\"`    // 使用时间\n\tUsageAmount  *int64  `json:\"usageAmount\"`  // 使用金额\n\tCouponId     *string `json:\"couponId\"`     // 优惠券ID\n\tMemberId     *string `json:\"memberId\"`     // 会员ID\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CouponUsageTransfer struct {\n}\n\nfunc (transfer *CouponUsageTransfer) PoToVo(po po.CouponUsage) vo.CouponUsageVO {\n\tvo := vo.CouponUsageVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CouponUsageTransfer) VoToPo(vo vo.CouponUsageVO) po.CouponUsage {\n\tpo := po.CouponUsage{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponUsageService struct {\n}\n\nfunc (service *CouponUsageService) CreateCouponUsage(logCtx *gin.Context, couponUsage *po.CouponUsage) error {\n\treturn Save(couponUsage)\n}\n\nfunc (service *CouponUsageService) UpdateCouponUsage(logCtx *gin.Context, couponUsage *po.CouponUsage) error {\n\treturn Update(couponUsage)\n}\n\nfunc (service *CouponUsageService) DeleteCouponUsage(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CouponUsage{Id: &id})\n}\n\nfunc (service *CouponUsageService) FindCouponUsageById(logCtx *gin.Context, id string) (couponUsage *po.CouponUsage, err error) {\n\tcouponUsage = &po.CouponUsage{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(couponUsage).Error\n\treturn\n}\n\nfunc (service *CouponUsageService) FindAllCouponUsage(logCtx *gin.Context, reqDto *req.QueryCouponUsageReqDto) (list *[]po.CouponUsage, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CouponUsage{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CouponUsage{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CouponUsageService) FindAllCouponUsageWithPagination(logCtx *gin.Context, reqDto *req.QueryCouponUsageReqDto) (list *[]po.CouponUsage, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CouponUsage{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CouponUsage{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponUsageController struct{}\n\nvar (\n\tcouponUsageService  = impl.CouponUsageService{}\n\tcouponUsageTransfer = transfer.CouponUsageTransfer{}\n)\n\n// @Summary 添加优惠券使用记录\n// @Description 添加优惠券使用记录\n// @Tags 优惠券使用记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddCouponUsageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CouponUsageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponUsage/add [post]\nfunc (controller *CouponUsageController) AddCouponUsage(ctx *gin.Context) {\n\treqDto := req.AddCouponUsageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcouponUsage := po.CouponUsage{}\n\tif reqDto.UsageTime != nil {\n\t\tcouponUsage.UsageTime = reqDto.UsageTime\n\t}\n\tif reqDto.UsageAmount != nil {\n\t\tcouponUsage.UsageAmount = reqDto.UsageAmount\n\t}\n\tif reqDto.CouponId != nil {\n\t\tcouponUsage.CouponId = reqDto.CouponId\n\t}\n\tif reqDto.MemberId != nil {\n\t\tcouponUsage.MemberId = reqDto.MemberId\n\t}\n\terr = couponUsageService.CreateCouponUsage(ctx, &couponUsage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, couponUsageTransfer.PoToVo(couponUsage))\n}\n\n// @Summary 更新优惠券使用记录\n// @Description 更新优惠券使用记录\n// @Tags 优惠券使用记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCouponUsageReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CouponUsageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponUsage/update [post]\nfunc (controller *CouponUsageController) UpdateCouponUsage(ctx *gin.Context) {\n\treqDto := req.UpdateCouponUsageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcouponUsage, err := couponUsageService.FindCouponUsageById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.UsageTime != nil {\n\t\tcouponUsage.UsageTime = reqDto.UsageTime\n\t}\n\tif reqDto.UsageAmount != nil {\n\t\tcouponUsage.UsageAmount = reqDto.UsageAmount\n\t}\n\tif reqDto.CouponId != nil {\n\t\tcouponUsage.CouponId = reqDto.CouponId\n\t}\n\tif reqDto.MemberId != nil {\n\t\tcouponUsage.MemberId = reqDto.MemberId\n\t}\n\terr = couponUsageService.UpdateCouponUsage(ctx, couponUsage)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, couponUsageTransfer.PoToVo(*couponUsage))\n}\n\n// @Summary 删除优惠券使用记录\n// @Description 删除优惠券使用记录\n// @Tags 优惠券使用记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCouponUsageReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponUsage/delete [post]\nfunc (controller *CouponUsageController) DeleteCouponUsage(ctx *gin.Context) {\n\treqDto := req.DeleteCouponUsageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = couponUsageService.DeleteCouponUsage(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询优惠券使用记录\n// @Description 查询优惠券使用记录\n// @Tags 优惠券使用记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCouponUsageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CouponUsageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponUsage/query [post]\nfunc (controller *CouponUsageController) QueryCouponUsages(ctx *gin.Context) {\n\treqDto := req.QueryCouponUsageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := couponUsageService.FindAllCouponUsage(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CouponUsageVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, couponUsageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询优惠券使用记录列表\n// @Description 查询优惠券使用记录列表\n// @Tags 优惠券使用记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCouponUsageReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CouponUsageVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponUsage/list [post]\nfunc (a *CouponUsageController) ListCouponUsages(ctx *gin.Context) {\n\treqDto := req.QueryCouponUsageReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := couponUsageService.FindAllCouponUsageWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CouponUsageVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CouponUsageVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, couponUsageTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponUsageRoute struct {\n}\n\nfunc (s *CouponUsageRoute) InitCouponUsageRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcouponUsageController := controller.CouponUsageController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/couponUsage/add\", couponUsageController.AddCouponUsage)    //add\n\t\troute.POST(\"/api/couponUsage/update\", couponUsageController.UpdateCouponUsage) //update\n\t\troute.POST(\"/api/couponUsage/delete\", couponUsageController.DeleteCouponUsage) //delete\n\t\troute.POST(\"/api/couponUsage/query\", couponUsageController.QueryCouponUsages)     //query\n\t\troute.POST(\"/api/couponUsage/list\", couponUsageController.ListCouponUsages)     //list\n\t}\n}"}]