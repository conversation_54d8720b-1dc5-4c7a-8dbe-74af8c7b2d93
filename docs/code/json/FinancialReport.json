[{"po": "package po\n\n// FinancialReport 财务报表实体\ntype FinancialReport struct {\n\tId                    *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // 唯一id\n\tReportType            *string `gorm:\"column:report_type;type:varchar(64);default:''\" json:\"reportType\"`            // 报表类型\n\tReportDate            *int64  `gorm:\"column:report_date;type:int;default:0\" json:\"reportDate\"`                    // 报表日期\n\tRoomReceivable        *string `gorm:\"column:room_receivable;type:text\" json:\"roomReceivable\"`                    // 房间应收款(JSON)\n\tRoomActualReceived    *string `gorm:\"column:room_actual_received;type:text\" json:\"roomActualReceived\"`            // 房间实收款(JSON)\n\tProductReceivable     *string `gorm:\"column:product_receivable;type:text\" json:\"productReceivable\"`              // 产品应收款(JSON)\n\tProductActualReceived *string `gorm:\"column:product_actual_received;type:text\" json:\"productActualReceived\"`      // 产品实收款(JSON)\n\tProductSalesQuantity  *string `gorm:\"column:product_sales_quantity;type:text\" json:\"productSalesQuantity\"`        // 产品销售数量(JSON)\n\tProductSalesAmount    *string `gorm:\"column:product_sales_amount;type:text\" json:\"productSalesAmount\"`            // 产品销售额(JSON)\n\tGiftQuantity         *string `gorm:\"column:gift_quantity;type:text\" json:\"giftQuantity\"`                        // 赠品数量(JSON)\n\tGiftAmount           *string `gorm:\"column:gift_amount;type:text\" json:\"giftAmount\"`                            // 赠品金额(JSON)\n\tReservationIncome    *int64  `gorm:\"column:reservation_income;type:int;default:0\" json:\"reservationIncome\"`      // 预订收入\n\tReservationRefund    *int64  `gorm:\"column:reservation_refund;type:int;default:0\" json:\"reservationRefund\"`      // 预订退款\n\tRechargeIncome       *int64  `gorm:\"column:recharge_income;type:int;default:0\" json:\"rechargeIncome\"`           // 充值收入\n\tRechargeRefund       *int64  `gorm:\"column:recharge_refund;type:int;default:0\" json:\"rechargeRefund\"`           // 充值退款\n\tPointsExchangeQuantity *string `gorm:\"column:points_exchange_quantity;type:text\" json:\"pointsExchangeQuantity\"`  // 积分兑换数量(JSON)\n\tPointsExchangeAmount  *string `gorm:\"column:points_exchange_amount;type:text\" json:\"pointsExchangeAmount\"`      // 积分兑换金额(JSON)\n\tReportId             *string `gorm:\"column:report_id;type:varchar(64);default:''\" json:\"reportId\"`              // 报表ID\n\tRedemptionDetails    *string `gorm:\"column:redemption_details;type:text\" json:\"redemptionDetails\"`              // 核销明细(JSON)\n\tCtime                *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                              // 创建时间\n\tUtime                *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                              // 更新时间\n\tState                *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                              // 状态\n\tVersion              *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                          // 版本\n}\n\n// TableName 设置表名\nfunc (FinancialReport) TableName() string {\n\treturn \"financial_report\"\n}\n\nfunc (f FinancialReport) GetId() string {\n\treturn *f.Id\n}\n", "vo": "package vo\n\n// FinancialReportVO 财务报表值对象\ntype FinancialReportVO struct {\n\tId                    string `json:\"id\"`                    // 唯一id\n\tReportType            string `json:\"reportType\"`            // 报表类型\n\tReportDate            int64  `json:\"reportDate\"`            // 报表日期\n\tRoomReceivable        string `json:\"roomReceivable\"`        // 房间应收款(JSON)\n\tRoomActualReceived    string `json:\"roomActualReceived\"`    // 房间实收款(JSON)\n\tProductReceivable     string `json:\"productReceivable\"`     // 产品应收款(JSON)\n\tProductActualReceived string `json:\"productActualReceived\"` // 产品实收款(JSON)\n\tProductSalesQuantity  string `json:\"productSalesQuantity\"`  // 产品销售数量(JSON)\n\tProductSalesAmount    string `json:\"productSalesAmount\"`    // 产品销售额(JSON)\n\tGiftQuantity         string `json:\"giftQuantity\"`         // 赠品数量(JSON)\n\tGiftAmount           string `json:\"giftAmount\"`           // 赠品金额(JSON)\n\tReservationIncome    int64  `json:\"reservationIncome\"`    // 预订收入\n\tReservationRefund    int64  `json:\"reservationRefund\"`    // 预订退款\n\tRechargeIncome       int64  `json:\"rechargeIncome\"`       // 充值收入\n\tRechargeRefund       int64  `json:\"rechargeRefund\"`       // 充值退款\n\tPointsExchangeQuantity string `json:\"pointsExchangeQuantity\"` // 积分兑换数量(JSON)\n\tPointsExchangeAmount  string `json:\"pointsExchangeAmount\"`  // 积分兑换金额(JSON)\n\tReportId             string `json:\"reportId\"`             // 报表ID\n\tRedemptionDetails    string `json:\"redemptionDetails\"`    // 核销明细(JSON)\n\tCtime                int64  `json:\"ctime\"`                // 创建时间\n\tUtime                int64  `json:\"utime\"`                // 更新时间\n\tState                int    `json:\"state\"`                // 状态\n\tVersion              int    `json:\"version\"`              // 版本\n}\n", "req_add": "package req\n\n// AddFinancialReportReqDto 创建财务报表请求DTO\ntype AddFinancialReportReqDto struct {\n\tReportType            *string `json:\"reportType\"`            // 报表类型\n\tReportDate            *int64  `json:\"reportDate\"`            // 报表日期\n\tRoomReceivable        *string `json:\"roomReceivable\"`        // 房间应收款(JSON)\n\tRoomActualReceived    *string `json:\"roomActualReceived\"`    // 房间实收款(JSON)\n\tProductReceivable     *string `json:\"productReceivable\"`     // 产品应收款(JSON)\n\tProductActualReceived *string `json:\"productActualReceived\"` // 产品实收款(JSON)\n\tProductSalesQuantity  *string `json:\"productSalesQuantity\"`  // 产品销售数量(JSON)\n\tProductSalesAmount    *string `json:\"productSalesAmount\"`    // 产品销售额(JSON)\n\tGiftQuantity         *string `json:\"giftQuantity\"`         // 赠品数量(JSON)\n\tGiftAmount           *string `json:\"giftAmount\"`           // 赠品金额(JSON)\n\tReservationIncome    *int64  `json:\"reservationIncome\"`    // 预订收入\n\tReservationRefund    *int64  `json:\"reservationRefund\"`    // 预订退款\n\tRechargeIncome       *int64  `json:\"rechargeIncome\"`       // 充值收入\n\tRechargeRefund       *int64  `json:\"rechargeRefund\"`       // 充值退款\n\tPointsExchangeQuantity *string `json:\"pointsExchangeQuantity\"` // 积分兑换数量(JSON)\n\tPointsExchangeAmount  *string `json:\"pointsExchangeAmount\"`  // 积分兑换金额(JSON)\n\tReportId             *string `json:\"reportId\"`             // 报表ID\n\tRedemptionDetails    *string `json:\"redemptionDetails\"`    // 核销明细(JSON)\n}\n", "req_update": "package req\n\n// UpdateFinancialReportReqDto 更新财务报表请求DTO\ntype UpdateFinancialReportReqDto struct {\n\tId                    *string `json:\"id\"`                    // 唯一id\n\tReportType            *string `json:\"reportType\"`            // 报表类型\n\tReportDate            *int64  `json:\"reportDate\"`            // 报表日期\n\tRoomReceivable        *string `json:\"roomReceivable\"`        // 房间应收款(JSON)\n\tRoomActualReceived    *string `json:\"roomActualReceived\"`    // 房间实收款(JSON)\n\tProductReceivable     *string `json:\"productReceivable\"`     // 产品应收款(JSON)\n\tProductActualReceived *string `json:\"productActualReceived\"` // 产品实收款(JSON)\n\tProductSalesQuantity  *string `json:\"productSalesQuantity\"`  // 产品销售数量(JSON)\n\tProductSalesAmount    *string `json:\"productSalesAmount\"`    // 产品销售额(JSON)\n\tGiftQuantity         *string `json:\"giftQuantity\"`         // 赠品数量(JSON)\n\tGiftAmount           *string `json:\"giftAmount\"`           // 赠品金额(JSON)\n\tReservationIncome    *int64  `json:\"reservationIncome\"`    // 预订收入\n\tReservationRefund    *int64  `json:\"reservationRefund\"`    // 预订退款\n\tRechargeIncome       *int64  `json:\"rechargeIncome\"`       // 充值收入\n\tRechargeRefund       *int64  `json:\"rechargeRefund\"`       // 充值退款\n\tPointsExchangeQuantity *string `json:\"pointsExchangeQuantity\"` // 积分兑换数量(JSON)\n\tPointsExchangeAmount  *string `json:\"pointsExchangeAmount\"`  // 积分兑换金额(JSON)\n\tReportId             *string `json:\"reportId\"`             // 报表ID\n\tRedemptionDetails    *string `json:\"redemptionDetails\"`    // 核销明细(JSON)\n}\n", "req_delete": "package req\n\n// DeleteFinancialReportReqDto 删除财务报表请求DTO\ntype DeleteFinancialReportReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\n// QueryFinancialReportReqDto 查询财务报表请求DTO\ntype QueryFinancialReportReqDto struct {\n\tId                    *string `json:\"id\"`                    // 唯一id\n\tReportType            *string `json:\"reportType\"`            // 报表类型\n\tReportDate            *int64  `json:\"reportDate\"`            // 报表日期\n\tReportId             *string `json:\"reportId\"`             // 报表ID\n\tPageNum              *int    `json:\"pageNum\"`              // 页码\n\tPageSize             *int    `json:\"pageSize\"`             // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype FinancialReportTransfer struct {\n}\n\nfunc (transfer *FinancialReportTransfer) PoToVo(po po.FinancialReport) vo.FinancialReportVO {\n\tvo := vo.FinancialReportVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *FinancialReportTransfer) VoToPo(vo vo.FinancialReportVO) po.FinancialReport {\n\tpo := po.FinancialReport{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype FinancialReportService struct {\n}\n\nfunc (service *FinancialReportService) CreateFinancialReport(logCtx *gin.Context, financialReport *po.FinancialReport) error {\n\treturn Save(financialReport)\n}\n\nfunc (service *FinancialReportService) UpdateFinancialReport(logCtx *gin.Context, financialReport *po.FinancialReport) error {\n\treturn Update(financialReport)\n}\n\nfunc (service *FinancialReportService) DeleteFinancialReport(logCtx *gin.Context, id string) error {\n\treturn Delete(po.FinancialReport{Id: &id})\n}\n\nfunc (service *FinancialReportService) FindFinancialReportById(logCtx *gin.Context, id string) (financialReport *po.FinancialReport, err error) {\n\tfinancialReport = &po.FinancialReport{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(financialReport).Error\n\treturn\n}\n\nfunc (service *FinancialReportService) FindAllFinancialReport(logCtx *gin.Context, reqDto *req.QueryFinancialReportReqDto) (list *[]po.FinancialReport, err error) {\n\tdb := model.DBSlave.Self.Model(&po.FinancialReport{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.ReportType != nil && *reqDto.ReportType != \"\" {\n\t\tdb = db.Where(\"report_type=?\", *reqDto.ReportType)\n\t}\n\tif reqDto.ReportDate != nil {\n\t\tdb = db.Where(\"report_date=?\", *reqDto.ReportDate)\n\t}\n\tif reqDto.ReportId != nil && *reqDto.ReportId != \"\" {\n\t\tdb = db.Where(\"report_id=?\", *reqDto.ReportId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.FinancialReport{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *FinancialReportService) FindAllFinancialReportWithPagination(logCtx *gin.Context, reqDto *req.QueryFinancialReportReqDto) (list *[]po.FinancialReport, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.FinancialReport{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.ReportType != nil && *reqDto.ReportType != \"\" {\n\t\tdb = db.Where(\"report_type=?\", *reqDto.ReportType)\n\t}\n\tif reqDto.ReportDate != nil {\n\t\tdb = db.Where(\"report_date=?\", *reqDto.ReportDate)\n\t}\n\tif reqDto.ReportId != nil && *reqDto.ReportId != \"\" {\n\t\tdb = db.Where(\"report_id=?\", *reqDto.ReportId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.FinancialReport{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype FinancialReportController struct{}\n\nvar (\n\tfinancialReportService  = impl.FinancialReportService{}\n\tfinancialReportTransfer = transfer.FinancialReportTransfer{}\n)\n\n// @Summary 添加财务报表\n// @Description 添加财务报表\n// @Tags 财务报表\n// @Accept json\n// @Produce json\n// @Param body body req.AddFinancialReportReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.FinancialReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/financial-report/add [post]\nfunc (controller *FinancialReportController) AddFinancialReport(ctx *gin.Context) {\n\treqDto := req.AddFinancialReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tfinancialReport := po.FinancialReport{}\n\tif reqDto.ReportType != nil {\n\t\tfinancialReport.ReportType = reqDto.ReportType\n\t}\n\tif reqDto.ReportDate != nil {\n\t\tfinancialReport.ReportDate = reqDto.ReportDate\n\t}\n\tif reqDto.RoomReceivable != nil {\n\t\tfinancialReport.RoomReceivable = reqDto.RoomReceivable\n\t}\n\tif reqDto.RoomActualReceived != nil {\n\t\tfinancialReport.RoomActualReceived = reqDto.RoomActualReceived\n\t}\n\tif reqDto.ProductReceivable != nil {\n\t\tfinancialReport.ProductReceivable = reqDto.ProductReceivable\n\t}\n\tif reqDto.ProductActualReceived != nil {\n\t\tfinancialReport.ProductActualReceived = reqDto.ProductActualReceived\n\t}\n\tif reqDto.ProductSalesQuantity != nil {\n\t\tfinancialReport.ProductSalesQuantity = reqDto.ProductSalesQuantity\n\t}\n\tif reqDto.ProductSalesAmount != nil {\n\t\tfinancialReport.ProductSalesAmount = reqDto.ProductSalesAmount\n\t}\n\tif reqDto.GiftQuantity != nil {\n\t\tfinancialReport.GiftQuantity = reqDto.GiftQuantity\n\t}\n\tif reqDto.GiftAmount != nil {\n\t\tfinancialReport.GiftAmount = reqDto.GiftAmount\n\t}\n\tif reqDto.ReservationIncome != nil {\n\t\tfinancialReport.ReservationIncome = reqDto.ReservationIncome\n\t}\n\tif reqDto.ReservationRefund != nil {\n\t\tfinancialReport.ReservationRefund = reqDto.ReservationRefund\n\t}\n\tif reqDto.RechargeIncome != nil {\n\t\tfinancialReport.RechargeIncome = reqDto.RechargeIncome\n\t}\n\tif reqDto.RechargeRefund != nil {\n\t\tfinancialReport.RechargeRefund = reqDto.RechargeRefund\n\t}\n\tif reqDto.PointsExchangeQuantity != nil {\n\t\tfinancialReport.PointsExchangeQuantity = reqDto.PointsExchangeQuantity\n\t}\n\tif reqDto.PointsExchangeAmount != nil {\n\t\tfinancialReport.PointsExchangeAmount = reqDto.PointsExchangeAmount\n\t}\n\tif reqDto.ReportId != nil {\n\t\tfinancialReport.ReportId = reqDto.ReportId\n\t}\n\tif reqDto.RedemptionDetails != nil {\n\t\tfinancialReport.RedemptionDetails = reqDto.RedemptionDetails\n\t}\n\n\terr = financialReportService.CreateFinancialReport(ctx, &financialReport)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, financialReportTransfer.PoToVo(financialReport))\n}\n\n// @Summary 更新财务报表\n// @Description 更新财务报表\n// @Tags 财务报表\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateFinancialReportReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.FinancialReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/financial-report/update [post]\nfunc (controller *FinancialReportController) UpdateFinancialReport(ctx *gin.Context) {\n\treqDto := req.UpdateFinancialReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tfinancialReport, err := financialReportService.FindFinancialReportById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.ReportType != nil {\n\t\tfinancialReport.ReportType = reqDto.ReportType\n\t}\n\tif reqDto.ReportDate != nil {\n\t\tfinancialReport.ReportDate = reqDto.ReportDate\n\t}\n\tif reqDto.RoomReceivable != nil {\n\t\tfinancialReport.RoomReceivable = reqDto.RoomReceivable\n\t}\n\tif reqDto.RoomActualReceived != nil {\n\t\tfinancialReport.RoomActualReceived = reqDto.RoomActualReceived\n\t}\n\tif reqDto.ProductReceivable != nil {\n\t\tfinancialReport.ProductReceivable = reqDto.ProductReceivable\n\t}\n\tif reqDto.ProductActualReceived != nil {\n\t\tfinancialReport.ProductActualReceived = reqDto.ProductActualReceived\n\t}\n\tif reqDto.ProductSalesQuantity != nil {\n\t\tfinancialReport.ProductSalesQuantity = reqDto.ProductSalesQuantity\n\t}\n\tif reqDto.ProductSalesAmount != nil {\n\t\tfinancialReport.ProductSalesAmount = reqDto.ProductSalesAmount\n\t}\n\tif reqDto.GiftQuantity != nil {\n\t\tfinancialReport.GiftQuantity = reqDto.GiftQuantity\n\t}\n\tif reqDto.GiftAmount != nil {\n\t\tfinancialReport.GiftAmount = reqDto.GiftAmount\n\t}\n\tif reqDto.ReservationIncome != nil {\n\t\tfinancialReport.ReservationIncome = reqDto.ReservationIncome\n\t}\n\tif reqDto.ReservationRefund != nil {\n\t\tfinancialReport.ReservationRefund = reqDto.ReservationRefund\n\t}\n\tif reqDto.RechargeIncome != nil {\n\t\tfinancialReport.RechargeIncome = reqDto.RechargeIncome\n\t}\n\tif reqDto.RechargeRefund != nil {\n\t\tfinancialReport.RechargeRefund = reqDto.RechargeRefund\n\t}\n\tif reqDto.PointsExchangeQuantity != nil {\n\t\tfinancialReport.PointsExchangeQuantity = reqDto.PointsExchangeQuantity\n\t}\n\tif reqDto.PointsExchangeAmount != nil {\n\t\tfinancialReport.PointsExchangeAmount = reqDto.PointsExchangeAmount\n\t}\n\tif reqDto.ReportId != nil {\n\t\tfinancialReport.ReportId = reqDto.ReportId\n\t}\n\tif reqDto.RedemptionDetails != nil {\n\t\tfinancialReport.RedemptionDetails = reqDto.RedemptionDetails\n\t}\n\n\terr = financialReportService.UpdateFinancialReport(ctx, financialReport)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, financialReportTransfer.PoToVo(*financialReport))\n}\n\n// @Summary 删除财务报表\n// @Description 删除财务报表\n// @Tags 财务报表\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteFinancialReportReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/financial-report/delete [post]\nfunc (controller *FinancialReportController) DeleteFinancialReport(ctx *gin.Context) {\n\treqDto := req.DeleteFinancialReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = financialReportService.DeleteFinancialReport(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询财务x, nil)\n}\n\n// @Summary 查询财务报表\n// @Description 查询财务报表\n// @Tags 财务报表\n// @Accept json\n// @Produce json\n// @Param body body req.QueryFinancialReportReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.FinancialReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/financial-report/query [post]\nfunc (controller *FinancialReportController) QueryFinancialReports(ctx *gin.Context) {\n\treqDto := req.QueryFinancialReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := financialReportService.FindAllFinancialReport(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.FinancialReportVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, financialReportTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询财务报表列表\n// @Description 查询财务报表列表\n// @Tags 财务报表\n// @Accept json\n// @Produce json\n// @Param body body req.QueryFinancialReportReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.FinancialReportVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/financial-report/list [post]\nfunc (a *FinancialReportController) ListFinancialReports(ctx *gin.Context) {\n\treqDto := req.QueryFinancialReportReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := financialReportService.FindAllFinancialReportWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.FinancialReportVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.FinancialReportVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, financialReportTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype FinancialReportRoute struct {\n}\n\nfunc (s *FinancialReportRoute) InitFinancialReportRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tfinancialReportController := controller.FinancialReportController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/financial-report/add\", financialReportController.AddFinancialReport)    //add\n\t\troute.POST(\"/api/financial-report/update\", financialReportController.UpdateFinancialReport) //update\n\t\troute.POST(\"/api/financial-report/delete\", financialReportController.DeleteFinancialReport) //delete\n\t\troute.POST(\"/api/financial-report/query\", financialReportController.QueryFinancialReports)     //query\n\t\troute.POST(\"/api/financial-report/list\", financialReportController.ListFinancialReports)     //list\n\t}\n}\n"}]