[{"po": "package po\n\n// CashierSystem 收银系统实体\ntype CashierSystem struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tVersion1       *string `gorm:\"column:version1;type:varchar(64);default:''\" json:\"version1\"`                // 版本\n\tSystemSettings *string `gorm:\"column:system_settings;type:varchar(64);default:''\" json:\"systemSettings\"`    // 系统设置\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                              // 创建时间戳\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                              // 更新时间戳\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                              // 状态值\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                          // 版本号\n}\n\n// TableName 设置表名\nfunc (CashierSystem) TableName() string {\n\treturn \"cashier_system\"\n}\n\nfunc (c CashierSystem) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CashierSystemVO 收银系统值对象\ntype CashierSystemVO struct {\n\tId             string `json:\"id\"`             // ID\n\tVersion1       string `json:\"version1\"`       // 版本\n\tSystemSettings string `json:\"systemSettings\"` // 系统设置\n\tCtime          int64  `json:\"ctime\"`          // 创建时间戳\n\tUtime          int64  `json:\"utime\"`          // 更新时间戳\n\tState          int    `json:\"state\"`          // 状态值\n\tVersion        int    `json:\"version\"`        // 版本号\n}\n", "req_add": "package req\n\n// AddCashierSystemReqDto 创建收银系统请求DTO\ntype AddCashierSystemReqDto struct {\n\tVersion1       *string `json:\"version1\"`       // 版本\n\tSystemSettings *string `json:\"systemSettings\"` // 系统设置\n}\n", "req_update": "package req\n\ntype UpdateCashierSystemReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tVersion1       *string `json:\"version1\"`       // 版本\n\tSystemSettings *string `json:\"systemSettings\"` // 系统设置\n}\n", "req_delete": "package req\n\ntype DeleteCashierSystemReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCashierSystemReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tVersion1       *string `json:\"version1\"`       // 版本\n\tSystemSettings *string `json:\"systemSettings\"` // 系统设置\n\tPageNum        *int    `json:\"pageNum\"`        // 页码\n\tPageSize       *int    `json:\"pageSize\"`       // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CashierSystemTransfer struct {\n}\n\nfunc (transfer *CashierSystemTransfer) PoToVo(po po.CashierSystem) vo.CashierSystemVO {\n\tvo := vo.CashierSystemVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CashierSystemTransfer) VoToPo(vo vo.CashierSystemVO) po.CashierSystem {\n\tpo := po.CashierSystem{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CashierSystemService struct {\n}\n\nfunc (service *CashierSystemService) CreateCashierSystem(logCtx *gin.Context, cashierSystem *po.CashierSystem) error {\n\treturn Save(cashierSystem)\n}\n\nfunc (service *CashierSystemService) UpdateCashierSystem(logCtx *gin.Context, cashierSystem *po.CashierSystem) error {\n\treturn Update(cashierSystem)\n}\n\nfunc (service *CashierSystemService) DeleteCashierSystem(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CashierSystem{Id: &id})\n}\n\nfunc (service *CashierSystemService) FindCashierSystemById(logCtx *gin.Context, id string) (cashierSystem *po.CashierSystem, err error) {\n\tcashierSystem = &po.CashierSystem{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(cashierSystem).Error\n\treturn\n}\n\nfunc (service *CashierSystemService) FindAllCashierSystem(logCtx *gin.Context, reqDto *req.QueryCashierSystemReqDto) (list *[]po.CashierSystem, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CashierSystem{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CashierSystem{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CashierSystemService) FindAllCashierSystemWithPagination(logCtx *gin.Context, reqDto *req.QueryCashierSystemReqDto) (list *[]po.CashierSystem, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CashierSystem{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CashierSystem{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CashierSystemController struct{}\n\nvar (\n\tcashierSystemService  = impl.CashierSystemService{}\n\tcashierSystemTransfer = transfer.CashierSystemTransfer{}\n)\n\n// @Summary 添加收银系统\n// @Description 添加收银系统\n// @Tags 收银系统\n// @Accept json\n// @Produce json\n// @Param body body req.AddCashierSystemReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CashierSystemVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-system/add [post]\nfunc (controller *CashierSystemController) AddCashierSystem(ctx *gin.Context) {\n\treqDto := req.AddCashierSystemReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcashierSystem := po.CashierSystem{}\n\tif reqDto.Version1 != nil {\n\t\tcashierSystem.Version1 = reqDto.Version1\n\t}\n\tif reqDto.SystemSettings != nil {\n\t\tcashierSystem.SystemSettings = reqDto.SystemSettings\n\t}\n\terr = cashierSystemService.CreateCashierSystem(ctx, &cashierSystem)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, cashierSystemTransfer.PoToVo(cashierSystem))\n}\n\n// @Summary 更新收银系统\n// @Description 更新收银系统\n// @Tags 收银系统\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCashierSystemReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CashierSystemVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-system/update [post]\nfunc (controller *CashierSystemController) UpdateCashierSystem(ctx *gin.Context) {\n\treqDto := req.UpdateCashierSystemReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcashierSystem, err := cashierSystemService.FindCashierSystemById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Version1 != nil {\n\t\tcashierSystem.Version1 = reqDto.Version1\n\t}\n\tif reqDto.SystemSettings != nil {\n\t\tcashierSystem.SystemSettings = reqDto.SystemSettings\n\t}\n\terr = cashierSystemService.UpdateCashierSystem(ctx, cashierSystem)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, cashierSystemTransfer.PoToVo(*cashierSystem))\n}\n\n// @Summary 删除收银系统\n// @Description 删除收银系统\n// @Tags 收银系统\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCashierSystemReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-system/delete [post]\nfunc (controller *CashierSystemController) DeleteCashierSystem(ctx *gin.Context) {\n\treqDto := req.DeleteCashierSystemReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = cashierSystemService.DeleteCashierSystem(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询收银系统\n// @Description 查询收银系统\n// @Tags 收银系统\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCashierSystemReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CashierSystemVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-system/query [post]\nfunc (controller *CashierSystemController) QueryCashierSystems(ctx *gin.Context) {\n\treqDto := req.QueryCashierSystemReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := cashierSystemService.FindAllCashierSystem(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CashierSystemVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, cashierSystemTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询收银系统列表\n// @Description 查询收银系统列表\n// @Tags 收银系统\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCashierSystemReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CashierSystemVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/cashier-system/list [post]\nfunc (a *CashierSystemController) ListCashierSystems(ctx *gin.Context) {\n\treqDto := req.QueryCashierSystemReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := cashierSystemService.FindAllCashierSystemWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CashierSystemVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CashierSystemVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, cashierSystemTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CashierSystemRoute struct {\n}\n\nfunc (s *CashierSystemRoute) InitCashierSystemRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcashierSystemController := controller.CashierSystemController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/cashier-system/add\", cashierSystemController.AddCashierSystem)       //add\n\t\troute.POST(\"/api/cashier-system/update\", cashierSystemController.UpdateCashierSystem) //update\n\t\troute.POST(\"/api/cashier-system/delete\", cashierSystemController.DeleteCashierSystem) //delete\n\t\troute.POST(\"/api/cashier-system/query\", cashierSystemController.QueryCashierSystems)  //query\n\t\troute.POST(\"/api/cashier-system/list\", cashierSystemController.ListCashierSystems)    //list\n\t}\n}\n"}]