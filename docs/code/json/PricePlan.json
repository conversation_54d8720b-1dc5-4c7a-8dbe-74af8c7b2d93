[{"po": "package po\n\n// PricePlan 价格方案实体\ntype PricePlan struct {\n\tId                    *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tVenueId               *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`               // ktvid\n\tName                  *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                     // 价格方案名称\n\tRoomType              *string `gorm:\"column:room_type;type:text\" json:\"roomType\"`                              // 房间类型\n\tDistributionChannel   *string `gorm:\"column:distribution_channel;type:varchar(64);default:''\" json:\"distributionChannel\"` // 分销渠道\n\tConsumptionMode       *string `gorm:\"column:consumption_mode;type:varchar(64);default:''\" json:\"consumptionMode\"`       // 消费模式\n\tHasMinimumCharge      *bool   `gorm:\"column:has_minimum_charge;type:tinyint(1);default:0\" json:\"hasMinimumCharge\"`    // 是否有最低消费\n\tMinimumCharge         *int64  `gorm:\"column:minimum_charge;type:bigint;default:0\" json:\"minimumCharge\"`           // 最低消费金额\n\tExampleProducts       *string `gorm:\"column:example_products;type:text\" json:\"exampleProducts\"`                   // 示例产品\n\tOptionalProducts      *string `gorm:\"column:optional_products;type:text\" json:\"optionalProducts\"`                 // 可选产品\n\tFreeProducts          *string `gorm:\"column:free_products;type:text\" json:\"freeProducts\"`                       // 免费产品\n\tMemberPrice           *int64  `gorm:\"column:member_price;type:bigint;default:0\" json:\"memberPrice\"`              // 会员价格\n\tMemberDiscount        *int64  `gorm:\"column:member_discount;type:bigint;default:0\" json:\"memberDiscount\"`         // 会员折扣\n\tAreaPrices            *string `gorm:\"column:area_prices;type:text\" json:\"areaPrices\"`                           // 区域价格\n\tAreaMemberPrices      *string `gorm:\"column:area_member_prices;type:text\" json:\"areaMemberPrices\"`               // 区域会员价格\n\tHolidayPrices         *string `gorm:\"column:holiday_prices;type:text\" json:\"holidayPrices\"`                      // 节假日价格\n\tIsEnabled             *bool   `gorm:\"column:is_enabled;type:tinyint(1);default:0\" json:\"isEnabled\"`              // 是否启用\n\tSupportsPoints        *bool   `gorm:\"column:supports_points;type:tinyint(1);default:0\" json:\"supportsPoints\"`      // 是否支持积分\n\tConsumptionTimeSlots  *string `gorm:\"column:consumption_time_slots;type:text\" json:\"consumptionTimeSlots\"`       // 消费时间段\n\tBuyGiftPlan           *string `gorm:\"column:buy_gift_plan;type:text\" json:\"buyGiftPlan\"`                        // 买赠方案\n\tTimeType              *string `gorm:\"column:time_type;type:varchar(64);default:''\" json:\"timeType\"`               // 时间类型\n\tWeeks                 *string `gorm:\"column:weeks;type:text\" json:\"weeks\"`                                      // 星期\n\tDayStart              *string `gorm:\"column:day_start;type:varchar(64);default:''\" json:\"dayStart\"`               // 开始日期\n\tDayEnd                *string `gorm:\"column:day_end;type:varchar(64);default:''\" json:\"dayEnd\"`                   // 结束日期\n\tHourMinuteStart       *string `gorm:\"column:hour_minute_start;type:varchar(64);default:''\" json:\"hourMinuteStart\"`   // 开始时间\n\tHourMinuteEnd         *string `gorm:\"column:hour_minute_end;type:varchar(64);default:''\" json:\"hourMinuteEnd\"`     // 结束时间\n\tPlanProducts          *string `gorm:\"column:plan_products;type:text\" json:\"planProducts\"`                       // 方案内商品\n\tDuration              *int    `gorm:\"column:duration;type:int;default:0\" json:\"duration\"`                        // 买断持续时长\n\tIsAreaSpecified       *bool   `gorm:\"column:is_area_specified;type:tinyint(1);default:0\" json:\"isAreaSpecified\"`   // 是否指定投放区域\n\tSelectedAreas         *string `gorm:\"column:selected_areas;type:text\" json:\"selectedAreas\"`                      // 选中的投放区域\n\tBaseRoomFee           *int64  `gorm:\"column:base_room_fee;type:bigint;default:0\" json:\"baseRoomFee\"`            // 基础房费\n\tAdvanceDisableDuration *int    `gorm:\"column:advance_disable_duration;type:int;default:0\" json:\"advanceDisableDuration\"` // 提前禁用时长\n\tIsExcessIncluded      *bool   `gorm:\"column:is_excess_included;type:tinyint(1);default:0\" json:\"isExcessIncluded\"`  // 多余部分是否计入房费\n\tBirthdayFee           *int64  `gorm:\"column:birthday_fee;type:bigint;default:0\" json:\"birthdayFee\"`            // 生日价格\n\tGroupBuyFee           *int64  `gorm:\"column:group_buy_fee;type:bigint;default:0\" json:\"groupBuyFee\"`           // 团购价格\n\tActivityFee           *int64  `gorm:\"column:activity_fee;type:bigint;default:0\" json:\"activityFee\"`            // 活动价格\n\tDiscountMode          *string `gorm:\"column:discount_mode;type:varchar(64);default:''\" json:\"discountMode\"`       // 优惠模式\n\tDiscountDuration      *int    `gorm:\"column:discount_duration;type:int;default:0\" json:\"discountDuration\"`      // 买钟优惠时长\n\tGiftDuration          *int    `gorm:\"column:gift_duration;type:int;default:0\" json:\"giftDuration\"`              // 赠送时长\n\tRoomChargeGoods       *string `gorm:\"column:room_charge_goods;type:text\" json:\"roomChargeGoods\"`               // 房费可抵扣的商品分类\n\tMaxDeductibleAmount   *int64  `gorm:\"column:max_deductible_amount;type:bigint;default:0\" json:\"maxDeductibleAmount\"` // 累计最高可抵扣金额\n\tMinimumConsumption    *int64  `gorm:\"column:minimum_consumption;type:bigint;default:0\" json:\"minimumConsumption\"`  // 超市消费最低金额\n\tStatisticsCategory    *string `gorm:\"column:statistics_category;type:varchar(64);default:''\" json:\"statisticsCategory\"` // 统计分类\n\tPlanPic               *string `gorm:\"column:plan_pic;type:varchar(255);default:''\" json:\"planPic\"`               // 方案图片\n\tCtime                 *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                         // 创建时间\n\tUtime                 *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                         // 更新时间\n\tState                 *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态\n\tVersion               *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本\n}\n\n// TableName 设置表名\nfunc (PricePlan) TableName() string {\n\treturn \"price_plan\"\n}\n\nfunc (p PricePlan) GetId() string {\n\treturn *p.Id\n}", "vo": "package vo\n\n// PricePlanVO 价格方案值对象\ntype PricePlanVO struct {\n\tId                    string `json:\"id\"`                    // ID\n\tVenueId               string `json:\"venueId\"`               // ktvid\n\tName                  string `json:\"name\"`                  // 价格方案名称\n\tRoomType              string `json:\"roomType\"`              // 房间类型\n\tDistributionChannel   string `json:\"distributionChannel\"`   // 分销渠道\n\tConsumptionMode       string `json:\"consumptionMode\"`       // 消费模式\n\tHasMinimumCharge      bool   `json:\"hasMinimumCharge\"`      // 是否有最低消费\n\tMinimumCharge         int64  `json:\"minimumCharge\"`         // 最低消费金额\n\tExampleProducts       string `json:\"exampleProducts\"`       // 示例产品\n\tOptionalProducts      string `json:\"optionalProducts\"`      // 可选产品\n\tFreeProducts          string `json:\"freeProducts\"`          // 免费产品\n\tMemberPrice           int64  `json:\"memberPrice\"`           // 会员价格\n\tMemberDiscount        int64  `json:\"memberDiscount\"`        // 会员折扣\n\tAreaPrices            string `json:\"areaPrices\"`            // 区域价格\n\tAreaMemberPrices      string `json:\"areaMemberPrices\"`      // 区域会员价格\n\tHolidayPrices         string `json:\"holidayPrices\"`         // 节假日价格\n\tIsEnabled             bool   `json:\"isEnabled\"`             // 是否启用\n\tSupportsPoints        bool   `json:\"supportsPoints\"`        // 是否支持积分\n\tConsumptionTimeSlots  string `json:\"consumptionTimeSlots\"`  // 消费时间段\n\tBuyGiftPlan           string `json:\"buyGiftPlan\"`           // 买赠方案\n\tTimeType              string `json:\"timeType\"`              // 时间类型\n\tWeeks                 string `json:\"weeks\"`                 // 星期\n\tDayStart              string `json:\"dayStart\"`              // 开始日期\n\tDayEnd                string `json:\"dayEnd\"`                // 结束日期\n\tHourMinuteStart       string `json:\"hourMinuteStart\"`       // 开始时间\n\tHourMinuteEnd         string `json:\"hourMinuteEnd\"`         // 结束时间\n\tPlanProducts          string `json:\"planProducts\"`          // 方案内商品\n\tDuration              int    `json:\"duration\"`              // 买断持续时长\n\tIsAreaSpecified       bool   `json:\"isAreaSpecified\"`       // 是否指定投放区域\n\tSelectedAreas         string `json:\"selectedAreas\"`         // 选中的投放区域\n\tBaseRoomFee           int64  `json:\"baseRoomFee\"`           // 基础房费\n\tAdvanceDisableDuration int    `json:\"advanceDisableDuration\"` // 提前禁用时长\n\tIsExcessIncluded      bool   `json:\"isExcessIncluded\"`      // 多余部分是否计入房费\n\tBirthdayFee           int64  `json:\"birthdayFee\"`           // 生日价格\n\tGroupBuyFee           int64  `json:\"groupBuyFee\"`           // 团购价格\n\tActivityFee           int64  `json:\"activityFee\"`           // 活动价格\n\tDiscountMode          string `json:\"discountMode\"`          // 优惠模式\n\tDiscountDuration      int    `json:\"discountDuration\"`      // 买钟优惠时长\n\tGiftDuration          int    `json:\"giftDuration\"`          // 赠送时长\n\tRoomChargeGoods       string `json:\"roomChargeGoods\"`       // 房费可抵扣的商品分类\n\tMaxDeductibleAmount   int64  `json:\"maxDeductibleAmount\"`   // 累计最高可抵扣金额\n\tMinimumConsumption    int64  `json:\"minimumConsumption\"`    // 超市消费最低金额\n\tStatisticsCategory    string `json:\"statisticsCategory\"`    // 统计分类\n\tPlanPic               string `json:\"planPic\"`               // 方案图片\n\tCtime                 int64  `json:\"ctime\"`                 // 创建时间\n\tUtime                 int64  `json:\"utime\"`                 // 更新时间\n\tState                 int    `json:\"state\"`                 // 状态\n\tVersion               int    `json:\"version\"`               // 版本\n}", "req_add": "package req\n\n// AddPricePlanReqDto 创建价格方案请求DTO\ntype AddPricePlanReqDto struct {\n\tVenueId               *string `json:\"venueId\"`               // ktvid\n\tName                  *string `json:\"name\"`                  // 价格方案名称\n\tRoomType              *string `json:\"roomType\"`              // 房间类型\n\tDistributionChannel   *string `json:\"distributionChannel\"`   // 分销渠道\n\tConsumptionMode       *string `json:\"consumptionMode\"`       // 消费模式\n\tHasMinimumCharge      *bool   `json:\"hasMinimumCharge\"`      // 是否有最低消费\n\tMinimumCharge         *int64  `json:\"minimumCharge\"`         // 最低消费金额\n\tExampleProducts       *string `json:\"exampleProducts\"`       // 示例产品\n\tOptionalProducts      *string `json:\"optionalProducts\"`      // 可选产品\n\tFreeProducts          *string `json:\"freeProducts\"`          // 免费产品\n\tMemberPrice           *int64  `json:\"memberPrice\"`           // 会员价格\n\tMemberDiscount        *int64  `json:\"memberDiscount\"`        // 会员折扣\n\tAreaPrices            *string `json:\"areaPrices\"`            // 区域价格\n\tAreaMemberPrices      *string `json:\"areaMemberPrices\"`      // 区域会员价格\n\tHolidayPrices         *string `json:\"holidayPrices\"`         // 节假日价格\n\tIsEnabled             *bool   `json:\"isEnabled\"`             // 是否启用\n\tSupportsPoints        *bool   `json:\"supportsPoints\"`        // 是否支持积分\n\tConsumptionTimeSlots  *string `json:\"consumptionTimeSlots\"`  // 消费时间段\n\tBuyGiftPlan           *string `json:\"buyGiftPlan\"`           // 买赠方案\n\tTimeType              *string `json:\"timeType\"`              // 时间类型\n\tWeeks                 *string `json:\"weeks\"`                 // 星期\n\tDayStart              *string `json:\"dayStart\"`              // 开始日期\n\tDayEnd                *string `json:\"dayEnd\"`                // 结束日期\n\tHourMinuteStart       *string `json:\"hourMinuteStart\"`       // 开始时间\n\tHourMinuteEnd         *string `json:\"hourMinuteEnd\"`         // 结束时间\n\tPlanProducts          *string `json:\"planProducts\"`          // 方案内商品\n\tDuration              *int    `json:\"duration\"`              // 买断持续时长\n\tIsAreaSpecified       *bool   `json:\"isAreaSpecified\"`       // 是否指定投放区域\n\tSelectedAreas         *string `json:\"selectedAreas\"`         // 选中的投放区域\n\tBaseRoomFee           *int64  `json:\"baseRoomFee\"`           // 基础房费\n\tAdvanceDisableDuration *int    `json:\"advanceDisableDuration\"` // 提前禁用时长\n\tIsExcessIncluded      *bool   `json:\"isExcessIncluded\"`      // 多余部分是否计入房费\n\tBirthdayFee           *int64  `json:\"birthdayFee\"`           // 生日价格\n\tGroupBuyFee           *int64  `json:\"groupBuyFee\"`           // 团购价格\n\tActivityFee           *int64  `json:\"activityFee\"`           // 活动价格\n\tDiscountMode          *string `json:\"discountMode\"`          // 优惠模式\n\tDiscountDuration      *int    `json:\"discountDuration\"`      // 买钟优惠时长\n\tGiftDuration          *int    `json:\"giftDuration\"`          // 赠送时长\n\tRoomChargeGoods       *string `json:\"roomChargeGoods\"`       // 房费可抵扣的商品分类\n\tMaxDeductibleAmount   *int64  `json:\"maxDeductibleAmount\"`   // 累计最高可抵扣金额\n\tMinimumConsumption    *int64  `json:\"minimumConsumption\"`    // 超市消费最低金额\n\tStatisticsCategory    *string `json:\"statisticsCategory\"`    // 统计分类\n\tPlanPic               *string `json:\"planPic\"`               // 方案图片\n}", "req_update": "package req\n\n// UpdatePricePlanReqDto 更新价格方案请求DTO\ntype UpdatePricePlanReqDto struct {\n\tId                    *string `json:\"id\"`                    // ID\n\tVenueId               *string `json:\"venueId\"`               // ktvid\n\tName                  *string `json:\"name\"`                  // 价格方案名称\n\tRoomType              *string `json:\"roomType\"`              // 房间类型\n\tDistributionChannel   *string `json:\"distributionChannel\"`   // 分销渠道\n\tConsumptionMode       *string `json:\"consumptionMode\"`       // 消费模式\n\tHasMinimumCharge      *bool   `json:\"hasMinimumCharge\"`      // 是否有最低消费\n\tMinimumCharge         *int64  `json:\"minimumCharge\"`         // 最低消费金额\n\tExampleProducts       *string `json:\"exampleProducts\"`       // 示例产品\n\tOptionalProducts      *string `json:\"optionalProducts\"`      // 可选产品\n\tFreeProducts          *string `json:\"freeProducts\"`          // 免费产品\n\tMemberPrice           *int64  `json:\"memberPrice\"`           // 会员价格\n\tMemberDiscount        *int64  `json:\"memberDiscount\"`        // 会员折扣\n\tAreaPrices            *string `json:\"areaPrices\"`            // 区域价格\n\tAreaMemberPrices      *string `json:\"areaMemberPrices\"`      // 区域会员价格\n\tHolidayPrices         *string `json:\"holidayPrices\"`         // 节假日价格\n\tIsEnabled             *bool   `json:\"isEnabled\"`             // 是否启用\n\tSupportsPoints        *bool   `json:\"supportsPoints\"`        // 是否支持积分\n\tConsumptionTimeSlots  *string `json:\"consumptionTimeSlots\"`  // 消费时间段\n\tBuyGiftPlan           *string `json:\"buyGiftPlan\"`           // 买赠方案\n\tTimeType              *string `json:\"timeType\"`              // 时间类型\n\tWeeks                 *string `json:\"weeks\"`                 // 星期\n\tDayStart              *string `json:\"dayStart\"`              // 开始日期\n\tDayEnd                *string `json:\"dayEnd\"`                // 结束日期\n\tHourMinuteStart       *string `json:\"hourMinuteStart\"`       // 开始时间\n\tHourMinuteEnd         *string `json:\"hourMinuteEnd\"`         // 结束时间\n\tPlanProducts          *string `json:\"planProducts\"`          // 方案内商品\n\tDuration              *int    `json:\"duration\"`              // 买断持续时长\n\tIsAreaSpecified       *bool   `json:\"isAreaSpecified\"`       // 是否指定投放区域\n\tSelectedAreas         *string `json:\"selectedAreas\"`         // 选中的投放区域\n\tBaseRoomFee           *int64  `json:\"baseRoomFee\"`           // 基础房费\n\tAdvanceDisableDuration *int    `json:\"advanceDisableDuration\"` // 提前禁用时长\n\tIsExcessIncluded      *bool   `json:\"isExcessIncluded\"`      // 多余部分是否计入房费\n\tBirthdayFee           *int64  `json:\"birthdayFee\"`           // 生日价格\n\tGroupBuyFee           *int64  `json:\"groupBuyFee\"`           // 团购价格\n\tActivityFee           *int64  `json:\"activityFee\"`           // 活动价格\n\tDiscountMode          *string `json:\"discountMode\"`          // 优惠模式\n\tDiscountDuration      *int    `json:\"discountDuration\"`      // 买钟优惠时长\n\tGiftDuration          *int    `json:\"giftDuration\"`          // 赠送时长\n\tRoomChargeGoods       *string `json:\"roomChargeGoods\"`       // 房费可抵扣的商品分类\n\tMaxDeductibleAmount   *int64  `json:\"maxDeductibleAmount\"`   // 累计最高可抵扣金额\n\tMinimumConsumption    *int64  `json:\"minimumConsumption\"`    // 超市消费最低金额\n\tStatisticsCategory    *string `json:\"statisticsCategory\"`    // 统计分类\n\tPlanPic               *string `json:\"planPic\"`               // 方案图片\n}", "req_delete": "package req\n\ntype DeletePricePlanReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\ntype QueryPricePlanReqDto struct {\n\tId                    *string `json:\"id\"`                    // ID\n\tVenueId               *string `json:\"venueId\"`               // ktvid\n\tName                  *string `json:\"name\"`                  // 价格方案名称\n\tRoomType              *string `json:\"roomType\"`              // 房间类型\n\tDistributionChannel   *string `json:\"distributionChannel\"`   // 分销渠道\n\tConsumptionMode       *string `json:\"consumptionMode\"`       // 消费模式\n\tHasMinimumCharge      *bool   `json:\"hasMinimumCharge\"`      // 是否有最低消费\n\tMinimumCharge         *int64  `json:\"minimumCharge\"`         // 最低消费金额\n\tIsEnabled             *bool   `json:\"isEnabled\"`             // 是否启用\n\tSupportsPoints        *bool   `json:\"supportsPoints\"`        // 是否支持积分\n\tTimeType              *string `json:\"timeType\"`              // 时间类型\n\tDuration              *int    `json:\"duration\"`              // 买断持续时长\n\tIsAreaSpecified       *bool   `json:\"isAreaSpecified\"`       // 是否指定投放区域\n\tBaseRoomFee           *int64  `json:\"baseRoomFee\"`           // 基础房费\n\tAdvanceDisableDuration *int    `json:\"advanceDisableDuration\"` // 提前禁用时长\n\tIsExcessIncluded      *bool   `json:\"isExcessIncluded\"`      // 多余部分是否计入房费\n\tBirthdayFee           *int64  `json:\"birthdayFee\"`           // 生日价格\n\tGroupBuyFee           *int64  `json:\"groupBuyFee\"`           // 团购价格\n\tActivityFee           *int64  `json:\"activityFee\"`           // 活动价格\n\tDiscountMode          *string `json:\"discountMode\"`          // 优惠模式\n\tDiscountDuration      *int    `json:\"discountDuration\"`      // 买钟优惠时长\n\tGiftDuration          *int    `json:\"giftDuration\"`          // 赠送时长\n\tMaxDeductibleAmount   *int64  `json:\"maxDeductibleAmount\"`   // 累计最高可抵扣金额\n\tMinimumConsumption    *int64  `json:\"minimumConsumption\"`    // 超市消费最低金额\n\tStatisticsCategory    *string `json:\"statisticsCategory\"`    // 统计分类\n\tPageNum              *int    `json:\"pageNum\"`              // 页码\n\tPageSize             *int    `json:\"pageSize\"`             // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PricePlanTransfer struct {\n}\n\nfunc (transfer *PricePlanTransfer) PoToVo(po po.PricePlan) vo.PricePlanVO {\n\tvo := vo.PricePlanVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PricePlanTransfer) VoToPo(vo vo.PricePlanVO) po.PricePlan {\n\tpo := po.PricePlan{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PricePlanService struct {\n}\n\nfunc (service *PricePlanService) CreatePricePlan(logCtx *gin.Context, pricePlan *po.PricePlan) error {\n\treturn Save(pricePlan)\n}\n\nfunc (service *PricePlanService) UpdatePricePlan(logCtx *gin.Context, pricePlan *po.PricePlan) error {\n\treturn Update(pricePlan)\n}\n\nfunc (service *PricePlanService) DeletePricePlan(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PricePlan{Id: &id})\n}\n\nfunc (service *PricePlanService) FindPricePlanById(logCtx *gin.Context, id string) (pricePlan *po.PricePlan, err error) {\n\tpricePlan = &po.PricePlan{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(pricePlan).Error\n\treturn\n}\n\nfunc (service *PricePlanService) FindAllPricePlan(logCtx *gin.Context, reqDto *req.QueryPricePlanReqDto) (list *[]po.PricePlan, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PricePlan{})\n\n\t// 构建查询条件\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.RoomType != nil && *reqDto.RoomType != \"\" {\n\t\tdb = db.Where(\"room_type=?\", *reqDto.RoomType)\n\t}\n\tif reqDto.DistributionChannel != nil && *reqDto.DistributionChannel != \"\" {\n\t\tdb = db.Where(\"distribution_channel=?\", *reqDto.DistributionChannel)\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tdb = db.Where(\"has_minimum_charge=?\", *reqDto.HasMinimumCharge)\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tdb = db.Where(\"minimum_charge=?\", *reqDto.MinimumCharge)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled=?\", *reqDto.IsEnabled)\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tdb = db.Where(\"supports_points=?\", *reqDto.SupportsPoints)\n\t}\n\tif reqDto.TimeType != nil && *reqDto.TimeType != \"\" {\n\t\tdb = db.Where(\"time_type=?\", *reqDto.TimeType)\n\t}\n\tif reqDto.Duration != nil {\n\t\tdb = db.Where(\"duration=?\", *reqDto.Duration)\n\t}\n\tif reqDto.IsAreaSpecified != nil {\n\t\tdb = db.Where(\"is_area_specified=?\", *reqDto.IsAreaSpecified)\n\t}\n\tif reqDto.BaseRoomFee != nil {\n\t\tdb = db.Where(\"base_room_fee=?\", *reqDto.BaseRoomFee)\n\t}\n\tif reqDto.AdvanceDisableDuration != nil {\n\t\tdb = db.Where(\"advance_disable_duration=?\", *reqDto.AdvanceDisableDuration)\n\t}\n\tif reqDto.IsExcessIncluded != nil {\n\t\tdb = db.Where(\"is_excess_included=?\", *reqDto.IsExcessIncluded)\n\t}\n\tif reqDto.BirthdayFee != nil {\n\t\tdb = db.Where(\"birthday_fee=?\", *reqDto.BirthdayFee)\n\t}\n\tif reqDto.GroupBuyFee != nil {\n\t\tdb = db.Where(\"group_buy_fee=?\", *reqDto.GroupBuyFee)\n\t}\n\tif reqDto.ActivityFee != nil {\n\t\tdb = db.Where(\"activity_fee=?\", *reqDto.ActivityFee)\n\t}\n\tif reqDto.DiscountMode != nil && *reqDto.DiscountMode != \"\" {\n\t\tdb = db.Where(\"discount_mode=?\", *reqDto.DiscountMode)\n\t}\n\tif reqDto.DiscountDuration != nil {\n\t\tdb = db.Where(\"discount_duration=?\", *reqDto.DiscountDuration)\n\t}\n\tif reqDto.GiftDuration != nil {\n\t\tdb = db.Where(\"gift_duration=?\", *reqDto.GiftDuration)\n\t}\n\tif reqDto.MaxDeductibleAmount != nil {\n\t\tdb = db.Where(\"max_deductible_amount=?\", *reqDto.MaxDeductibleAmount)\n\t}\n\tif reqDto.MinimumConsumption != nil {\n\t\tdb = db.Where(\"minimum_consumption=?\", *reqDto.MinimumConsumption)\n\t}\n\tif reqDto.StatisticsCategory != nil && *reqDto.StatisticsCategory != \"\" {\n\t\tdb = db.Where(\"statistics_category=?\", *reqDto.StatisticsCategory)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PricePlan{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PricePlanService) FindAllPricePlanWithPagination(logCtx *gin.Context, reqDto *req.QueryPricePlanReqDto) (list *[]po.PricePlan, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PricePlan{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\t// 构建查询条件\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.RoomType != nil && *reqDto.RoomType != \"\" {\n\t\tdb = db.Where(\"room_type=?\", *reqDto.RoomType)\n\t}\n\tif reqDto.DistributionChannel != nil && *reqDto.DistributionChannel != \"\" {\n\t\tdb = db.Where(\"distribution_channel=?\", *reqDto.DistributionChannel)\n\t}\n\tif reqDto.ConsumptionMode != nil && *reqDto.ConsumptionMode != \"\" {\n\t\tdb = db.Where(\"consumption_mode=?\", *reqDto.ConsumptionMode)\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tdb = db.Where(\"has_minimum_charge=?\", *reqDto.HasMinimumCharge)\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tdb = db.Where(\"minimum_charge=?\", *reqDto.MinimumCharge)\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tdb = db.Where(\"is_enabled=?\", *reqDto.IsEnabled)\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tdb = db.Where(\"supports_points=?\", *reqDto.SupportsPoints)\n\t}\n\tif reqDto.TimeType != nil && *reqDto.TimeType != \"\" {\n\t\tdb = db.Where(\"time_type=?\", *reqDto.TimeType)\n\t}\n\tif reqDto.Duration != nil {\n\t\tdb = db.Where(\"duration=?\", *reqDto.Duration)\n\t}\n\tif reqDto.IsAreaSpecified != nil {\n\t\tdb = db.Where(\"is_area_specified=?\", *reqDto.IsAreaSpecified)\n\t}\n\tif reqDto.BaseRoomFee != nil {\n\t\tdb = db.Where(\"base_room_fee=?\", *reqDto.BaseRoomFee)\n\t}\n\tif reqDto.AdvanceDisableDuration != nil {\n\t\tdb = db.Where(\"advance_disable_duration=?\", *reqDto.AdvanceDisableDuration)\n\t}\n\tif reqDto.IsExcessIncluded != nil {\n\t\tdb = db.Where(\"is_excess_included=?\", *reqDto.IsExcessIncluded)\n\t}\n\tif reqDto.BirthdayFee != nil {\n\t\tdb = db.Where(\"birthday_fee=?\", *reqDto.BirthdayFee)\n\t}\n\tif reqDto.GroupBuyFee != nil {\n\t\tdb = db.Where(\"group_buy_fee=?\", *reqDto.GroupBuyFee)\n\t}\n\tif reqDto.ActivityFee != nil {\n\t\tdb = db.Where(\"activity_fee=?\", *reqDto.ActivityFee)\n\t}\n\tif reqDto.DiscountMode != nil && *reqDto.DiscountMode != \"\" {\n\t\tdb = db.Where(\"discount_mode=?\", *reqDto.DiscountMode)\n\t}\n\tif reqDto.DiscountDuration != nil {\n\t\tdb = db.Where(\"discount_duration=?\", *reqDto.DiscountDuration)\n\t}\n\tif reqDto.GiftDuration != nil {\n\t\tdb = db.Where(\"gift_duration=?\", *reqDto.GiftDuration)\n\t}\n\tif reqDto.MaxDeductibleAmount != nil {\n\t\tdb = db.Where(\"max_deductible_amount=?\", *reqDto.MaxDeductibleAmount)\n\t}\n\tif reqDto.MinimumConsumption != nil {\n\t\tdb = db.Where(\"minimum_consumption=?\", *reqDto.MinimumConsumption)\n\t}\n\tif reqDto.StatisticsCategory != nil && *reqDto.StatisticsCategory != \"\" {\n\t\tdb = db.Where(\"statistics_category=?\", *reqDto.StatisticsCategory)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PricePlan{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PricePlanController struct{}\n\nvar (\n\tpricePlanService  = impl.PricePlanService{}\n\tpricePlanTransfer = transfer.PricePlanTransfer{}\n)\n\n// @Summary 添加价格方案\n// @Description 添加价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.AddPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-plan/add [post]\nfunc (controller *PricePlanController) AddPricePlan(ctx *gin.Context) {\n\treqDto := req.AddPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpricePlan := po.PricePlan{}\n\tif reqDto.VenueId != nil {\n\t\tpricePlan.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tpricePlan.Name = reqDto.Name\n\t}\n\tif reqDto.RoomType != nil {\n\t\tpricePlan.RoomType = reqDto.RoomType\n\t}\n\tif reqDto.DistributionChannel != nil {\n\t\tpricePlan.DistributionChannel = reqDto.DistributionChannel\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\tpricePlan.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tpricePlan.HasMinimumCharge = reqDto.HasMinimumCharge\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tpricePlan.MinimumCharge = reqDto.MinimumCharge\n\t}\n\tif reqDto.ExampleProducts != nil {\n\t\tpricePlan.ExampleProducts = reqDto.ExampleProducts\n\t}\n\tif reqDto.OptionalProducts != nil {\n\t\tpricePlan.OptionalProducts = reqDto.OptionalProducts\n\t}\n\tif reqDto.FreeProducts != nil {\n\t\tpricePlan.FreeProducts = reqDto.FreeProducts\n\t}\n\tif reqDto.MemberPrice != nil {\n\t\tpricePlan.MemberPrice = reqDto.MemberPrice\n\t}\n\tif reqDto.MemberDiscount != nil {\n\t\tpricePlan.MemberDiscount = reqDto.MemberDiscount\n\t}\n\tif reqDto.AreaPrices != nil {\n\t\tpricePlan.AreaPrices = reqDto.AreaPrices\n\t}\n\tif reqDto.AreaMemberPrices != nil {\n\t\tpricePlan.AreaMemberPrices = reqDto.AreaMemberPrices\n\t}\n\tif reqDto.HolidayPrices != nil {\n\t\tpricePlan.HolidayPrices = reqDto.HolidayPrices\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpricePlan.IsEnabled = reqDto.IsEnabled\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tpricePlan.SupportsPoints = reqDto.SupportsPoints\n\t}\n\tif reqDto.ConsumptionTimeSlots != nil {\n\t\tpricePlan.ConsumptionTimeSlots = reqDto.ConsumptionTimeSlots\n\t}\n\tif reqDto.BuyGiftPlan != nil {\n\t\tpricePlan.BuyGiftPlan = reqDto.BuyGiftPlan\n\t}\n\tif reqDto.TimeType != nil {\n\t\tpricePlan.TimeType = reqDto.TimeType\n\t}\n\tif reqDto.Weeks != nil {\n\t\tpricePlan.Weeks = reqDto.Weeks\n\t}\n\tif reqDto.DayStart != nil {\n\t\tpricePlan.DayStart = reqDto.DayStart\n\t}\n\tif reqDto.DayEnd != nil {\n\t\tpricePlan.DayEnd = reqDto.DayEnd\n\t}\n\tif reqDto.HourMinuteStart != nil {\n\t\tpricePlan.HourMinuteStart = reqDto.HourMinuteStart\n\t}\n\tif reqDto.HourMinuteEnd != nil {\n\t\tpricePlan.HourMinuteEnd = reqDto.HourMinuteEnd\n\t}\n\tif reqDto.PlanProducts != nil {\n\t\tpricePlan.PlanProducts = reqDto.PlanProducts\n\t}\n\tif reqDto.Duration != nil {\n\t\tpricePlan.Duration = reqDto.Duration\n\t}\n\tif reqDto.IsAreaSpecified != nil {\n\t\tpricePlan.IsAreaSpecified = reqDto.IsAreaSpecified\n\t}\n\tif reqDto.SelectedAreas != nil {\n\t\tpricePlan.SelectedAreas = reqDto.SelectedAreas\n\t}\n\tif reqDto.BaseRoomFee != nil {\n\t\tpricePlan.BaseRoomFee = reqDto.BaseRoomFee\n\t}\n\tif reqDto.AdvanceDisableDuration != nil {\n\t\tpricePlan.AdvanceDisableDuration = reqDto.AdvanceDisableDuration\n\t}\n\tif reqDto.IsExcessIncluded != nil {\n\t\tpricePlan.IsExcessIncluded = reqDto.IsExcessIncluded\n\t}\n\tif reqDto.BirthdayFee != nil {\n\t\tpricePlan.BirthdayFee = reqDto.BirthdayFee\n\t}\n\tif reqDto.GroupBuyFee != nil {\n\t\tpricePlan.GroupBuyFee = reqDto.GroupBuyFee\n\t}\n\tif reqDto.ActivityFee != nil {\n\t\tpricePlan.ActivityFee = reqDto.ActivityFee\n\t}\n\tif reqDto.DiscountMode != nil {\n\t\tpricePlan.DiscountMode = reqDto.DiscountMode\n\t}\n\tif reqDto.DiscountDuration != nil {\n\t\tpricePlan.DiscountDuration = reqDto.DiscountDuration\n\t}\n\tif reqDto.GiftDuration != nil {\n\t\tpricePlan.GiftDuration = reqDto.GiftDuration\n\t}\n\tif reqDto.RoomChargeGoods != nil {\n\t\tpricePlan.RoomChargeGoods = reqDto.RoomChargeGoods\n\t}\n\tif reqDto.MaxDeductibleAmount != nil {\n\t\tpricePlan.MaxDeductibleAmount = reqDto.MaxDeductibleAmount\n\t}\n\tif reqDto.MinimumConsumption != nil {\n\t\tpricePlan.MinimumConsumption = reqDto.MinimumConsumption\n\t}\n\tif reqDto.StatisticsCategory != nil {\n\t\tpricePlan.StatisticsCategory = reqDto.StatisticsCategory\n\t}\n\tif reqDto.PlanPic != nil {\n\t\tpricePlan.PlanPic = reqDto.PlanPic\n\t}\n\n\terr = pricePlanService.CreatePricePlan(ctx, &pricePlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, pricePlanTransfer.PoToVo(pricePlan))\n}\n\n// @Summary 更新价格方案\n// @Description 更新价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-plan/update [post]\nfunc (controller *PricePlanController) UpdatePricePlan(ctx *gin.Context) {\n\treqDto := req.UpdatePricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tpricePlan, err := pricePlanService.FindPricePlanById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tpricePlan.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tpricePlan.Name = reqDto.Name\n\t}\n\tif reqDto.RoomType != nil {\n\t\tpricePlan.RoomType = reqDto.RoomType\n\t}\n\tif reqDto.DistributionChannel != nil {\n\t\tpricePlan.DistributionChannel = reqDto.DistributionChannel\n\t}\n\tif reqDto.ConsumptionMode != nil {\n\t\tpricePlan.ConsumptionMode = reqDto.ConsumptionMode\n\t}\n\tif reqDto.HasMinimumCharge != nil {\n\t\tpricePlan.HasMinimumCharge = reqDto.HasMinimumCharge\n\t}\n\tif reqDto.MinimumCharge != nil {\n\t\tpricePlan.MinimumCharge = reqDto.MinimumCharge\n\t}\n\tif reqDto.ExampleProducts != nil {\n\t\tpricePlan.ExampleProducts = reqDto.ExampleProducts\n\t}\n\tif reqDto.OptionalProducts != nil {\n\t\tpricePlan.OptionalProducts = reqDto.OptionalProducts\n\t}\n\tif reqDto.FreeProducts != nil {\n\t\tpricePlan.FreeProducts = reqDto.FreeProducts\n\t}\n\tif reqDto.MemberPrice != nil {\n\t\tpricePlan.MemberPrice = reqDto.MemberPrice\n\t}\n\tif reqDto.MemberDiscount != nil {\n\t\tpricePlan.MemberDiscount = reqDto.MemberDiscount\n\t}\n\tif reqDto.AreaPrices != nil {\n\t\tpricePlan.AreaPrices = reqDto.AreaPrices\n\t}\n\tif reqDto.AreaMemberPrices != nil {\n\t\tpricePlan.AreaMemberPrices = reqDto.AreaMemberPrices\n\t}\n\tif reqDto.HolidayPrices != nil {\n\t\tpricePlan.HolidayPrices = reqDto.HolidayPrices\n\t}\n\tif reqDto.IsEnabled != nil {\n\t\tpricePlan.IsEnabled = reqDto.IsEnabled\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tpricePlan.SupportsPoints = reqDto.SupportsPoints\n\t}\n\tif reqDto.ConsumptionTimeSlots != nil {\n\t\tpricePlan.ConsumptionTimeSlots = reqDto.ConsumptionTimeSlots\n\t}\n\tif reqDto.BuyGiftPlan != nil {\n\t\tpricePlan.BuyGiftPlan = reqDto.BuyGiftPlan\n\t}\n\tif reqDto.TimeType != nil {\n\t\tpricePlan.TimeType = reqDto.TimeType\n\t}\n\tif reqDto.Weeks != nil {\n\t\tpricePlan.Weeks = reqDto.Weeks\n\t}\n\tif reqDto.DayStart != nil {\n\t\tpricePlan.DayStart = reqDto.DayStart\n\t}\n\tif reqDto.DayEnd != nil {\n\t\tpricePlan.DayEnd = reqDto.DayEnd\n\t}\n\tif reqDto.HourMinuteStart != nil {\n\t\tpricePlan.HourMinuteStart = reqDto.HourMinuteStart\n\t}\n\tif reqDto.HourMinuteEnd != nil {\n\t\tpricePlan.HourMinuteEnd = reqDto.HourMinuteEnd\n\t}\n\tif reqDto.PlanProducts != nil {\n\t\tpricePlan.PlanProducts = reqDto.PlanProducts\n\t}\n\tif reqDto.Duration != nil {\n\t\tpricePlan.Duration = reqDto.Duration\n\t}\n\tif reqDto.IsAreaSpecified != nil {\n\t\tpricePlan.IsAreaSpecified = reqDto.IsAreaSpecified\n\t}\n\tif reqDto.SelectedAreas != nil {\n\t\tpricePlan.SelectedAreas = reqDto.SelectedAreas\n\t}\n\tif reqDto.BaseRoomFee != nil {\n\t\tpricePlan.BaseRoomFee = reqDto.BaseRoomFee\n\t}\n\tif reqDto.AdvanceDisableDuration != nil {\n\t\tpricePlan.AdvanceDisableDuration = reqDto.AdvanceDisableDuration\n\t}\n\tif reqDto.IsExcessIncluded != nil {\n\t\tpricePlan.IsExcessIncluded = reqDto.IsExcessIncluded\n\t}\n\tif reqDto.BirthdayFee != nil {\n\t\tpricePlan.BirthdayFee = reqDto.BirthdayFee\n\t}\n\tif reqDto.GroupBuyFee != nil {\n\t\tpricePlan.GroupBuyFee = reqDto.GroupBuyFee\n\t}\n\tif reqDto.ActivityFee != nil {\n\t\tpricePlan.ActivityFee = reqDto.ActivityFee\n\t}\n\tif reqDto.DiscountMode != nil {\n\t\tpricePlan.DiscountMode = reqDto.DiscountMode\n\t}\n\tif reqDto.DiscountDuration != nil {\n\t\tpricePlan.DiscountDuration = reqDto.DiscountDuration\n\t}\n\tif reqDto.GiftDuration != nil {\n\t\tpricePlan.GiftDuration = reqDto.GiftDuration\n\t}\n\tif reqDto.RoomChargeGoods != nil {\n\t\tpricePlan.RoomChargeGoods = reqDto.RoomChargeGoods\n\t}\n\tif reqDto.MaxDeductibleAmount != nil {\n\t\tpricePlan.MaxDeductibleAmount = reqDto.MaxDeductibleAmount\n\t}\n\tif reqDto.MinimumConsumption != nil {\n\t\tpricePlan.MinimumConsumption = reqDto.MinimumConsumption\n\t}\n\tif reqDto.StatisticsCategory != nil {\n\t\tpricePlan.StatisticsCategory = reqDto.StatisticsCategory\n\t}\n\tif reqDto.PlanPic != nil {\n\t\tpricePlan.PlanPic = reqDto.PlanPic\n\t}\n\n\terr = pricePlanService.UpdatePricePlan(ctx, pricePlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, pricePlanTransfer.PoToVo(*pricePlan))\n}\n\n// @Summary 删除价格方案\n// @Description 删除价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-plan/delete [post]\nfunc (controller *PricePlanController) DeletePricePlan(ctx *gin.Context) {\n\treqDto := req.DeletePricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = pricePlanService.DeletePricePlan(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询价格方案\n// @Description 查询价格方案\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-plan/query [post]\nfunc (controller *PricePlanController) QueryPricePlans(ctx *gin.Context) {\n\treqDto := req.QueryPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := pricePlanService.FindAllPricePlan(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PricePlanVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, pricePlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询价格方案列表\n// @Description 查询价格方案列表\n// @Tags 价格方案\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPricePlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PricePlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/price-plan/list [post]\nfunc (a *PricePlanController) ListPricePlans(ctx *gin.Context) {\n\treqDto := req.QueryPricePlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := pricePlanService.FindAllPricePlanWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PricePlanVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PricePlanVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, pricePlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PricePlanRoute struct {\n}\n\nfunc (s *PricePlanRoute) InitPricePlanRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tpricePlanController := controller.PricePlanController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/price-plan/add\", pricePlanController.AddPricePlan)    //add\n\t\troute.POST(\"/api/price-plan/update\", pricePlanController.UpdatePricePlan) //update\n\t\troute.POST(\"/api/price-plan/delete\", pricePlanController.DeletePricePlan) //delete\n\t\troute.POST(\"/api/price-plan/query\", pricePlanController.QueryPricePlans)     //query\n\t\troute.POST(\"/api/price-plan/list\", pricePlanController.ListPricePlans)     //list\n\t}\n}\n"}]