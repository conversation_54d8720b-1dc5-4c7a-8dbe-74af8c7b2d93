[{"po": "package po\n\n// CustomerSource 客户来源实体\ntype CustomerSource struct {\n\tId      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`  // ID\n\tName    *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"` // 客户来源名称\n\tCtime   *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`        // 创建时间戳\n\tUtime   *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`        // 更新时间戳\n\tState   *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`        // 状态值\n\tVersion *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`    // 版本号\n}\n\n// TableName 设置表名\nfunc (CustomerSource) TableName() string {\n\treturn \"customer_source\"\n}\n\nfunc (cs CustomerSource) GetId() string {\n\treturn *cs.Id\n}\n", "vo": "package vo\n\n// CustomerSourceVO 客户来源值对象\ntype CustomerSourceVO struct {\n\tId      string `json:\"id\"`      // ID\n\tName    string `json:\"name\"`    // 客户来源名称\n\tCtime   int64  `json:\"ctime\"`   // 创建时间戳\n\tUtime   int64  `json:\"utime\"`   // 更新时间戳\n\tState   int    `json:\"state\"`   // 状态值\n\tVersion int    `json:\"version\"` // 版本号\n}\n", "req_add": "package req\n\n// AddCustomerSourceReqDto 创建客户来源请求DTO\ntype AddCustomerSourceReqDto struct {\n\tName *string `json:\"name\"` // 客户来源名称\n}\n", "req_update": "package req\n\ntype UpdateCustomerSourceReqDto struct {\n\tId   *string `json:\"id\"`   // ID\n\tName *string `json:\"name\"` // 客户来源名称\n}\n", "req_delete": "package req\n\ntype DeleteCustomerSourceReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCustomerSourceReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tName     *string `json:\"name\"`     // 客户来源名称\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CustomerSourceTransfer struct {\n}\n\nfunc (transfer *CustomerSourceTransfer) PoToVo(po po.CustomerSource) vo.CustomerSourceVO {\n\tvo := vo.CustomerSourceVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CustomerSourceTransfer) VoToPo(vo vo.CustomerSourceVO) po.CustomerSource {\n\tpo := po.CustomerSource{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerSourceService struct {\n}\n\nfunc (service *CustomerSourceService) CreateCustomerSource(logCtx *gin.Context, customerSource *po.CustomerSource) error {\n\treturn Save(customerSource)\n}\n\nfunc (service *CustomerSourceService) UpdateCustomerSource(logCtx *gin.Context, customerSource *po.CustomerSource) error {\n\treturn Update(customerSource)\n}\n\nfunc (service *CustomerSourceService) DeleteCustomerSource(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CustomerSource{Id: &id})\n}\n\nfunc (service *CustomerSourceService) FindCustomerSourceById(logCtx *gin.Context, id string) (customerSource *po.CustomerSource, err error) {\n\tcustomerSource = &po.CustomerSource{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(customerSource).Error\n\treturn\n}\n\nfunc (service *CustomerSourceService) FindAllCustomerSource(logCtx *gin.Context, reqDto *req.QueryCustomerSourceReqDto) (list *[]po.CustomerSource, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CustomerSource{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CustomerSource{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CustomerSourceService) FindAllCustomerSourceWithPagination(logCtx *gin.Context, reqDto *req.QueryCustomerSourceReqDto) (list *[]po.CustomerSource, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CustomerSource{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name=?\", *reqDto.Name)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CustomerSource{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerSourceController struct{}\n\nvar (\n\tcustomerSourceService  = impl.CustomerSourceService{}\n\tcustomerSourceTransfer = transfer.CustomerSourceTransfer{}\n)\n\n// @Summary 添加客户来源\n// @Description 添加客户来源\n// @Tags 客户来源\n// @Accept json\n// @Produce json\n// @Param body body req.AddCustomerSourceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CustomerSourceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-source/add [post]\nfunc (controller *CustomerSourceController) AddCustomerSource(ctx *gin.Context) {\n\treqDto := req.AddCustomerSourceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcustomerSource := po.CustomerSource{}\n\tif reqDto.Name != nil {\n\t\tcustomerSource.Name = reqDto.Name\n\t}\n\terr = customerSourceService.CreateCustomerSource(ctx, &customerSource)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, customerSourceTransfer.PoToVo(customerSource))\n}\n\n// @Summary 更新客户来源\n// @Description 更新客户来源\n// @Tags 客户来源\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCustomerSourceReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CustomerSourceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-source/update [post]\nfunc (controller *CustomerSourceController) UpdateCustomerSource(ctx *gin.Context) {\n\treqDto := req.UpdateCustomerSourceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcustomerSource, err := customerSourceService.FindCustomerSourceById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\tcustomerSource.Name = reqDto.Name\n\t}\n\terr = customerSourceService.UpdateCustomerSource(ctx, customerSource)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, customerSourceTransfer.PoToVo(*customerSource))\n}\n\n// @Summary 删除客户来源\n// @Description 删除客户来源\n// @Tags 客户来源\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCustomerSourceReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-source/delete [post]\nfunc (controller *CustomerSourceController) DeleteCustomerSource(ctx *gin.Context) {\n\treqDto := req.DeleteCustomerSourceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = customerSourceService.DeleteCustomerSource(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询客户来源\n// @Description 查询客户来源\n// @Tags 客户来源\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCustomerSourceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CustomerSourceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-source/query [post]\nfunc (controller *CustomerSourceController) QueryCustomerSources(ctx *gin.Context) {\n\treqDto := req.QueryCustomerSourceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := customerSourceService.FindAllCustomerSource(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CustomerSourceVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, customerSourceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询客户来源列表\n// @Description 查询客户来源列表\n// @Tags 客户来源\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCustomerSourceReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CustomerSourceVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/customer-source/list [post]\nfunc (a *CustomerSourceController) ListCustomerSources(ctx *gin.Context) {\n\treqDto := req.QueryCustomerSourceReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := customerSourceService.FindAllCustomerSourceWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CustomerSourceVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CustomerSourceVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, customerSourceTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CustomerSourceRoute struct {\n}\n\nfunc (s *CustomerSourceRoute) InitCustomerSourceRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcustomerSourceController := controller.CustomerSourceController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/customer-source/add\", customerSourceController.AddCustomerSource)    //add\n\t\troute.POST(\"/api/customer-source/update\", customerSourceController.UpdateCustomerSource) //update\n\t\troute.POST(\"/api/customer-source/delete\", customerSourceController.DeleteCustomerSource) //delete\n\t\troute.POST(\"/api/customer-source/query\", customerSourceController.QueryCustomerSources)     //query\n\t\troute.POST(\"/api/customer-source/list\", customerSourceController.ListCustomerSources)     //list\n\t}\n}\n"}]