[{"po": "package po\n\n// MemberCardLevel 会员卡等级实体\ntype MemberCardLevel struct {\n\tId                        *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tName                      *string  `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                    // 等级名称\n\tLevel                     *int     `gorm:\"column:level;type:int;default:0\" json:\"level\"`                         // 等级\n\tLogo                      *string  `gorm:\"column:logo;type:varchar(255);default:''\" json:\"logo\"`                  // logo\n\tCardType                  *string  `gorm:\"column:card_type;type:varchar(64);default:''\" json:\"cardType\"`          // 卡类型\n\tDefaultUsageScope         *string  `gorm:\"column:default_usage_scope;type:varchar(255)\" json:\"defaultUsageScope\"` // 默认使用范围\n\tDistributionChannels      *string  `gorm:\"column:distribution_channels;type:varchar(255)\" json:\"distributionChannels\"` // 分发渠道\n\tValidityPeriod            *string  `gorm:\"column:validity_period;type:varchar(64)\" json:\"validityPeriod\"`           // 有效期\n\tUpgradeConditions         *string  `gorm:\"column:upgrade_conditions;type:text\" json:\"upgradeConditions\"`           // 升级条件\n\tDowngradeConditions       *string  `gorm:\"column:downgrade_conditions;type:text\" json:\"downgradeConditions\"`       // 降级条件\n\tBirthdayBenefits          *string  `gorm:\"column:birthday_benefits;type:text\" json:\"birthdayBenefits\"`           // 生日福利\n\tRegistrationUpgradeBenefits *string `gorm:\"column:registration_upgrade_benefits;type:text\" json:\"registrationUpgradeBenefits\"` // 注册升级福利\n\tMonthlyVouchers           *string  `gorm:\"column:monthly_vouchers;type:text\" json:\"monthlyVouchers\"`             // 每月优惠券\n\tCardIssueFee              *int64   `gorm:\"column:card_issue_fee;type:bigint\" json:\"cardIssueFee\"`               // 办卡费用\n\tCardReplacementFee        *int64   `gorm:\"column:card_replacement_fee;type:bigint\" json:\"cardReplacementFee\"`   // 补卡费用\n\tCardRenewalFee            *int64   `gorm:\"column:card_renewal_fee;type:bigint\" json:\"cardRenewalFee\"`           // 续卡费用\n\tMinimumRechargeAmount     *int64   `gorm:\"column:minimum_recharge_amount;type:bigint\" json:\"minimumRechargeAmount\"` // 最低充值金额\n\tRoomDiscount              *float32 `gorm:\"column:room_discount;type:float\" json:\"roomDiscount\"`                 // 房间折扣\n\tBeverageDiscount          *float32 `gorm:\"column:beverage_discount;type:float\" json:\"beverageDiscount\"`         // 酒水折扣\n\tConsumptionPointsRule     *string  `gorm:\"column:consumption_points_rule;type:text\" json:\"consumptionPointsRule\"` // 消费积分规则\n\tRechargePointsRule        *string  `gorm:\"column:recharge_points_rule;type:text\" json:\"rechargePointsRule\"`     // 充值积分规则\n\tConsumptionTimeSlots      *string  `gorm:\"column:consumption_time_slots;type:text\" json:\"consumptionTimeSlots\"` // 消费时间段\n\tPaymentRestrictions       *string  `gorm:\"column:payment_restrictions;type:text\" json:\"paymentRestrictions\"`    // 支付限制\n\tDailyRoomLimit            *int     `gorm:\"column:daily_room_limit;type:int\" json:\"dailyRoomLimit\"`             // 每日房间限制\n\tMinimumConsumptionAmount  *int64   `gorm:\"column:minimum_consumption_amount;type:bigint\" json:\"minimumConsumptionAmount\"` // 最低消费金额\n\tMinimumBalanceForDiscount *int64   `gorm:\"column:minimum_balance_for_discount;type:bigint\" json:\"minimumBalanceForDiscount\"` // 享受折扣的最低余额\n\tCtime                     *int64   `gorm:\"column:ctime;type:bigint\" json:\"ctime\"`                             // 创建时间\n\tUtime                     *int64   `gorm:\"column:utime;type:bigint\" json:\"utime\"`                             // 更新时间\n\tState                     *int     `gorm:\"column:state;type:int\" json:\"state\"`                                 // 状态\n\tVersion                   *int     `gorm:\"column:version;type:int\" json:\"version\"`                             // 版本号\n}\n\n// TableName 设置表名\nfunc (MemberCardLevel) TableName() string {\n\treturn \"member_card_level\"\n}\n\nfunc (m MemberCardLevel) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MemberCardLevelVO 会员卡等级值对象\ntype MemberCardLevelVO struct {\n\tId                        string   `json:\"id\"`                         // ID\n\tName                      string   `json:\"name\"`                       // 等级名称\n\tLevel                     int      `json:\"level\"`                      // 等级\n\tLogo                      string   `json:\"logo\"`                       // logo\n\tCardType                  string   `json:\"cardType\"`                   // 卡类型\n\tDefaultUsageScope         string   `json:\"defaultUsageScope\"`          // 默认使用范围\n\tDistributionChannels      string   `json:\"distributionChannels\"`       // 分发渠道\n\tValidityPeriod            string   `json:\"validityPeriod\"`             // 有效期\n\tUpgradeConditions         string   `json:\"upgradeConditions\"`          // 升级条件\n\tDowngradeConditions       string   `json:\"downgradeConditions\"`        // 降级条件\n\tBirthdayBenefits          string   `json:\"birthdayBenefits\"`           // 生日福利\n\tRegistrationUpgradeBenefits string  `json:\"registrationUpgradeBenefits\"` // 注册升级福利\n\tMonthlyVouchers           string   `json:\"monthlyVouchers\"`            // 每月优惠券\n\tCardIssueFee              int64    `json:\"cardIssueFee\"`               // 办卡费用\n\tCardReplacementFee        int64    `json:\"cardReplacementFee\"`         // 补卡费用\n\tCardRenewalFee            int64    `json:\"cardRenewalFee\"`             // 续卡费用\n\tMinimumRechargeAmount     int64    `json:\"minimumRechargeAmount\"`      // 最低充值金额\n\tRoomDiscount              float32  `json:\"roomDiscount\"`               // 房间折扣\n\tBeverageDiscount          float32  `json:\"beverageDiscount\"`           // 酒水折扣\n\tConsumptionPointsRule     string   `json:\"consumptionPointsRule\"`      // 消费积分规则\n\tRechargePointsRule        string   `json:\"rechargePointsRule\"`         // 充值积分规则\n\tConsumptionTimeSlots      string   `json:\"consumptionTimeSlots\"`       // 消费时间段\n\tPaymentRestrictions       string   `json:\"paymentRestrictions\"`        // 支付限制\n\tDailyRoomLimit            int      `json:\"dailyRoomLimit\"`             // 每日房间限制\n\tMinimumConsumptionAmount  int64    `json:\"minimumConsumptionAmount\"`   // 最低消费金额\n\tMinimumBalanceForDiscount int64    `json:\"minimumBalanceForDiscount\"` // 享受折扣的最低余额\n\tCtime                     int64    `json:\"ctime\"`                      // 创建时间\n\tUtime                     int64    `json:\"utime\"`                      // 更新时间\n\tState                     int      `json:\"state\"`                      // 状态\n\tVersion                   int      `json:\"version\"`                    // 版本号\n}\n", "req_add": "package req\n\n// AddMemberCardLevelReqDto 创建会员卡等级请求DTO\ntype AddMemberCardLevelReqDto struct {\n\tName                      *string  `json:\"name\"`                       // 等级名称\n\tLevel                     *int     `json:\"level\"`                      // 等级\n\tLogo                      *string  `json:\"logo\"`                       // logo\n\tCardType                  *string  `json:\"cardType\"`                   // 卡类型\n\tDefaultUsageScope         *string  `json:\"defaultUsageScope\"`          // 默认使用范围\n\tDistributionChannels      *string  `json:\"distributionChannels\"`       // 分发渠道\n\tValidityPeriod            *string  `json:\"validityPeriod\"`             // 有效期\n\tUpgradeConditions         *string  `json:\"upgradeConditions\"`          // 升级条件\n\tDowngradeConditions       *string  `json:\"downgradeConditions\"`        // 降级条件\n\tBirthdayBenefits          *string  `json:\"birthdayBenefits\"`           // 生日福利\n\tRegistrationUpgradeBenefits *string `json:\"registrationUpgradeBenefits\"` // 注册升级福利\n\tMonthlyVouchers           *string  `json:\"monthlyVouchers\"`            // 每月优惠券\n\tCardIssueFee              *int64   `json:\"cardIssueFee\"`               // 办卡费用\n\tCardReplacementFee        *int64   `json:\"cardReplacementFee\"`         // 补卡费用\n\tCardRenewalFee            *int64   `json:\"cardRenewalFee\"`             // 续卡费用\n\tMinimumRechargeAmount     *int64   `json:\"minimumRechargeAmount\"`      // 最低充值金额\n\tRoomDiscount              *float32 `json:\"roomDiscount\"`               // 房间折扣\n\tBeverageDiscount          *float32 `json:\"beverageDiscount\"`           // 酒水折扣\n\tConsumptionPointsRule     *string  `json:\"consumptionPointsRule\"`      // 消费积分规则\n\tRechargePointsRule        *string  `json:\"rechargePointsRule\"`         // 充值积分规则\n\tConsumptionTimeSlots      *string  `json:\"consumptionTimeSlots\"`       // 消费时间段\n\tPaymentRestrictions       *string  `json:\"paymentRestrictions\"`        // 支付限制\n\tDailyRoomLimit            *int     `json:\"dailyRoomLimit\"`             // 每日房间限制\n\tMinimumConsumptionAmount  *int64   `json:\"minimumConsumptionAmount\"`   // 最低消费金额\n\tMinimumBalanceForDiscount *int64   `json:\"minimumBalanceForDiscount\"` // 享受折扣的最低余额\n}\n", "req_update": "package req\n\n// UpdateMemberCardLevelReqDto 更新会员卡等级请求DTO\ntype UpdateMemberCardLevelReqDto struct {\n\tId                        *string  `json:\"id\"`                         // ID\n\tName                      *string  `json:\"name\"`                       // 等级名称\n\tLevel                     *int     `json:\"level\"`                      // 等级\n\tLogo                      *string  `json:\"logo\"`                       // logo\n\tCardType                  *string  `json:\"cardType\"`                   // 卡类型\n\tDefaultUsageScope         *string  `json:\"defaultUsageScope\"`          // 默认使用范围\n\tDistributionChannels      *string  `json:\"distributionChannels\"`       // 分发渠道\n\tValidityPeriod            *string  `json:\"validityPeriod\"`             // 有效期\n\tUpgradeConditions         *string  `json:\"upgradeConditions\"`          // 升级条件\n\tDowngradeConditions       *string  `json:\"downgradeConditions\"`        // 降级条件\n\tBirthdayBenefits          *string  `json:\"birthdayBenefits\"`           // 生日福利\n\tRegistrationUpgradeBenefits *string `json:\"registrationUpgradeBenefits\"` // 注册升级福利\n\tMonthlyVouchers           *string  `json:\"monthlyVouchers\"`            // 每月优惠券\n\tCardIssueFee              *int64   `json:\"cardIssueFee\"`               // 办卡费用\n\tCardReplacementFee        *int64   `json:\"cardReplacementFee\"`         // 补卡费用\n\tCardRenewalFee            *int64   `json:\"cardRenewalFee\"`             // 续卡费用\n\tMinimumRechargeAmount     *int64   `json:\"minimumRechargeAmount\"`      // 最低充值金额\n\tRoomDiscount              *float32 `json:\"roomDiscount\"`               // 房间折扣\n\tBeverageDiscount          *float32 `json:\"beverageDiscount\"`           // 酒水折扣\n\tConsumptionPointsRule     *string  `json:\"consumptionPointsRule\"`      // 消费积分规则\n\tRechargePointsRule        *string  `json:\"rechargePointsRule\"`         // 充值积分规则\n\tConsumptionTimeSlots      *string  `json:\"consumptionTimeSlots\"`       // 消费时间段\n\tPaymentRestrictions       *string  `json:\"paymentRestrictions\"`        // 支付限制\n\tDailyRoomLimit            *int     `json:\"dailyRoomLimit\"`             // 每日房间限制\n\tMinimumConsumptionAmount  *int64   `json:\"minimumConsumptionAmount\"`   // 最低消费金额\n\tMinimumBalanceForDiscount *int64   `json:\"minimumBalanceForDiscount\"` // 享受折扣的最低余额\n}\n", "req_delete": "package req\n\ntype DeleteMemberCardLevelReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryMemberCardLevelReqDto struct {\n\tId                   *string  `json:\"id\"`                    // ID\n\tName                 *string  `json:\"name\"`                  // 等级名称\n\tLevel                *int     `json:\"level\"`                 // 等级\n\tCardType             *string  `json:\"cardType\"`              // 卡类型\n\tPageNum              *int     `json:\"pageNum\"`               // 页码\n\tPageSize             *int     `json:\"pageSize\"`              // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberCardLevelTransfer struct {\n}\n\nfunc (transfer *MemberCardLevelTransfer) PoToVo(po po.MemberCardLevel) vo.MemberCardLevelVO {\n\tvo := vo.MemberCardLevelVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MemberCardLevelTransfer) VoToPo(vo vo.MemberCardLevelVO) po.MemberCardLevel {\n\tpo := po.MemberCardLevel{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardLevelService struct {\n}\n\nfunc (service *MemberCardLevelService) CreateMemberCardLevel(logCtx *gin.Context, memberCardLevel *po.MemberCardLevel) error {\n\treturn Save(memberCardLevel)\n}\n\nfunc (service *MemberCardLevelService) UpdateMemberCardLevel(logCtx *gin.Context, memberCardLevel *po.MemberCardLevel) error {\n\treturn Update(memberCardLevel)\n}\n\nfunc (service *MemberCardLevelService) DeleteMemberCardLevel(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MemberCardLevel{Id: &id})\n}\n\nfunc (service *MemberCardLevelService) FindMemberCardLevelById(logCtx *gin.Context, id string) (memberCardLevel *po.MemberCardLevel, err error) {\n\tmemberCardLevel = &po.MemberCardLevel{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(memberCardLevel).Error\n\treturn\n}\n\nfunc (service *MemberCardLevelService) FindAllMemberCardLevel(logCtx *gin.Context, reqDto *req.QueryMemberCardLevelReqDto) (list *[]po.MemberCardLevel, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberCardLevel{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Level != nil {\n\t\tdb = db.Where(\"level=?\", *reqDto.Level)\n\t}\n\tif reqDto.CardType != nil && *reqDto.CardType != \"\" {\n\t\tdb = db.Where(\"card_type=?\", *reqDto.CardType)\n\t}\n\n\tdb = db.Order(\"level asc\")\n\tlist = &[]po.MemberCardLevel{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MemberCardLevelService) FindAllMemberCardLevelWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberCardLevelReqDto) (list *[]po.MemberCardLevel, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberCardLevel{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Level != nil {\n\t\tdb = db.Where(\"level=?\", *reqDto.Level)\n\t}\n\tif reqDto.CardType != nil && *reqDto.CardType != \"\" {\n\t\tdb = db.Where(\"card_type=?\", *reqDto.CardType)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MemberCardLevel{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"level asc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardLevelController struct{}\n\nvar (\n\tmemberCardLevelService  = impl.MemberCardLevelService{}\n\tmemberCardLevelTransfer = transfer.MemberCardLevelTransfer{}\n)\n\n// @Summary 添加会员卡等级\n// @Description 添加会员卡等级\n// @Tags 会员卡等级\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberCardLevelReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberCardLevelVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardLevel/add [post]\nfunc (controller *MemberCardLevelController) AddMemberCardLevel(ctx *gin.Context) {\n\treqDto := req.AddMemberCardLevelReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmemberCardLevel := po.MemberCardLevel{}\n\tif reqDto.Name != nil {\n\t\tmemberCardLevel.Name = reqDto.Name\n\t}\n\tif reqDto.Level != nil {\n\t\tmemberCardLevel.Level = reqDto.Level\n\t}\n\tif reqDto.Logo != nil {\n\t\tmemberCardLevel.Logo = reqDto.Logo\n\t}\n\tif reqDto.CardType != nil {\n\t\tmemberCardLevel.CardType = reqDto.CardType\n\t}\n\tif reqDto.DefaultUsageScope != nil {\n\t\tmemberCardLevel.DefaultUsageScope = reqDto.DefaultUsageScope\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tmemberCardLevel.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.ValidityPeriod != nil {\n\t\tmemberCardLevel.ValidityPeriod = reqDto.ValidityPeriod\n\t}\n\tif reqDto.UpgradeConditions != nil {\n\t\tmemberCardLevel.UpgradeConditions = reqDto.UpgradeConditions\n\t}\n\tif reqDto.DowngradeConditions != nil {\n\t\tmemberCardLevel.DowngradeConditions = reqDto.DowngradeConditions\n\t}\n\tif reqDto.BirthdayBenefits != nil {\n\t\tmemberCardLevel.BirthdayBenefits = reqDto.BirthdayBenefits\n\t}\n\tif reqDto.RegistrationUpgradeBenefits != nil {\n\t\tmemberCardLevel.RegistrationUpgradeBenefits = reqDto.RegistrationUpgradeBenefits\n\t}\n\tif reqDto.MonthlyVouchers != nil {\n\t\tmemberCardLevel.MonthlyVouchers = reqDto.MonthlyVouchers\n\t}\n\tif reqDto.CardIssueFee != nil {\n\t\tmemberCardLevel.CardIssueFee = reqDto.CardIssueFee\n\t}\n\tif reqDto.CardReplacementFee != nil {\n\t\tmemberCardLevel.CardReplacementFee = reqDto.CardReplacementFee\n\t}\n\tif reqDto.CardRenewalFee != nil {\n\t\tmemberCardLevel.CardRenewalFee = reqDto.CardRenewalFee\n\t}\n\tif reqDto.MinimumRechargeAmount != nil {\n\t\tmemberCardLevel.MinimumRechargeAmount = reqDto.MinimumRechargeAmount\n\t}\n\tif reqDto.RoomDiscount != nil {\n\t\tmemberCardLevel.RoomDiscount = reqDto.RoomDiscount\n\t}\n\tif reqDto.BeverageDiscount != nil {\n\t\tmemberCardLevel.BeverageDiscount = reqDto.BeverageDiscount\n\t}\n\tif reqDto.ConsumptionPointsRule != nil {\n\t\tmemberCardLevel.ConsumptionPointsRule = reqDto.ConsumptionPointsRule\n\t}\n\tif reqDto.RechargePointsRule != nil {\n\t\tmemberCardLevel.RechargePointsRule = reqDto.RechargePointsRule\n\t}\n\tif reqDto.ConsumptionTimeSlots != nil {\n\t\tmemberCardLevel.ConsumptionTimeSlots = reqDto.ConsumptionTimeSlots\n\t}\n\tif reqDto.PaymentRestrictions != nil {\n\t\tmemberCardLevel.PaymentRestrictions = reqDto.PaymentRestrictions\n\t}\n\tif reqDto.DailyRoomLimit != nil {\n\t\tmemberCardLevel.DailyRoomLimit = reqDto.DailyRoomLimit\n\t}\n\tif reqDto.MinimumConsumptionAmount != nil {\n\t\tmemberCardLevel.MinimumConsumptionAmount = reqDto.MinimumConsumptionAmount\n\t}\n\tif reqDto.MinimumBalanceForDiscount != nil {\n\t\tmemberCardLevel.MinimumBalanceForDiscount = reqDto.MinimumBalanceForDiscount\n\t}\n\n\terr = memberCardLevelService.CreateMemberCardLevel(ctx, &memberCardLevel)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberCardLevelTransfer.PoToVo(memberCardLevel))\n}\n\n// @Summary 更新会员卡等级\n// @Description 更新会员卡等级\n// @Tags 会员卡等级\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberCardLevelReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberCardLevelVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardLevel/update [post]\nfunc (controller *MemberCardLevelController) UpdateMemberCardLevel(ctx *gin.Context) {\n\treqDto := req.UpdateMemberCardLevelReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tmemberCardLevel, err := memberCardLevelService.FindMemberCardLevelById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tmemberCardLevel.Name = reqDto.Name\n\t}\n\tif reqDto.Level != nil {\n\t\tmemberCardLevel.Level = reqDto.Level\n\t}\n\tif reqDto.Logo != nil {\n\t\tmemberCardLevel.Logo = reqDto.Logo\n\t}\n\tif reqDto.CardType != nil {\n\t\tmemberCardLevel.CardType = reqDto.CardType\n\t}\n\tif reqDto.DefaultUsageScope != nil {\n\t\tmemberCardLevel.DefaultUsageScope = reqDto.DefaultUsageScope\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tmemberCardLevel.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.ValidityPeriod != nil {\n\t\tmemberCardLevel.ValidityPeriod = reqDto.ValidityPeriod\n\t}\n\tif reqDto.UpgradeConditions != nil {\n\t\tmemberCardLevel.UpgradeConditions = reqDto.UpgradeConditions\n\t}\n\tif reqDto.DowngradeConditions != nil {\n\t\tmemberCardLevel.DowngradeConditions = reqDto.DowngradeConditions\n\t}\n\tif reqDto.BirthdayBenefits != nil {\n\t\tmemberCardLevel.BirthdayBenefits = reqDto.BirthdayBenefits\n\t}\n\tif reqDto.RegistrationUpgradeBenefits != nil {\n\t\tmemberCardLevel.RegistrationUpgradeBenefits = reqDto.RegistrationUpgradeBenefits\n\t}\n\tif reqDto.MonthlyVouchers != nil {\n\t\tmemberCardLevel.MonthlyVouchers = reqDto.MonthlyVouchers\n\t}\n\tif reqDto.CardIssueFee != nil {\n\t\tmemberCardLevel.CardIssueFee = reqDto.CardIssueFee\n\t}\n\tif reqDto.CardReplacementFee != nil {\n\t\tmemberCardLevel.CardReplacementFee = reqDto.CardReplacementFee\n\t}\n\tif reqDto.CardRenewalFee != nil {\n\t\tmemberCardLevel.CardRenewalFee = reqDto.CardRenewalFee\n\t}\n\tif reqDto.MinimumRechargeAmount != nil {\n\t\tmemberCardLevel.MinimumRechargeAmount = reqDto.MinimumRechargeAmount\n\t}\n\tif reqDto.RoomDiscount != nil {\n\t\tmemberCardLevel.RoomDiscount = reqDto.RoomDiscount\n\t}\n\tif reqDto.BeverageDiscount != nil {\n\t\tmemberCardLevel.BeverageDiscount = reqDto.BeverageDiscount\n\t}\n\tif reqDto.ConsumptionPointsRule != nil {\n\t\tmemberCardLevel.ConsumptionPointsRule = reqDto.ConsumptionPointsRule\n\t}\n\tif reqDto.RechargePointsRule != nil {\n\t\tmemberCardLevel.RechargePointsRule = reqDto.RechargePointsRule\n\t}\n\tif reqDto.ConsumptionTimeSlots != nil {\n\t\tmemberCardLevel.ConsumptionTimeSlots = reqDto.ConsumptionTimeSlots\n\t}\n\tif reqDto.PaymentRestrictions != nil {\n\t\tmemberCardLevel.PaymentRestrictions = reqDto.PaymentRestrictions\n\t}\n\tif reqDto.DailyRoomLimit != nil {\n\t\tmemberCardLevel.DailyRoomLimit = reqDto.DailyRoomLimit\n\t}\n\tif reqDto.MinimumConsumptionAmount != nil {\n\t\tmemberCardLevel.MinimumConsumptionAmount = reqDto.MinimumConsumptionAmount\n\t}\n\tif reqDto.MinimumBalanceForDiscount != nil {\n\t\tmemberCardLevel.MinimumBalanceForDiscount = reqDto.MinimumBalanceForDiscount\n\t}\n\n\terr = memberCardLevelService.UpdateMemberCardLevel(ctx, memberCardLevel)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberCardLevelTransfer.PoToVo(*memberCardLevel))\n}\n\n// @Summary 删除会员卡等级\n// @Description 删除会员卡等级\n// @Tags 会员卡等级\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberCardLevelReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardLevel/delete [post]\nfunc (controller *MemberCardLevelController) DeleteMemberCardLevel(ctx *gin.Context) {\n\treqDto := req.DeleteMemberCardLevelReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = memberCardLevelService.DeleteMemberCardLevel(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员卡等级\n// @Description 查询会员卡等级\n// @Tags 会员卡等级\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberCardLevelReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberCardLevelVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardLevel/query [post]\nfunc (controller *MemberCardLevelController) QueryMemberCardLevels(ctx *gin.Context) {\n\treqDto := req.QueryMemberCardLevelReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := memberCardLevelService.FindAllMemberCardLevel(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.MemberCardLevelVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, memberCardLevelTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询会员卡等级列表\n// @Description 查询会员卡等级列表\n// @Tags 会员卡等级\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberCardLevelReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberCardLevelVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/memberCardLevel/list [post]\nfunc (controller *MemberCardLevelController) ListMemberCardLevels(ctx *gin.Context) {\n\treqDto := req.QueryMemberCardLevelReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := memberCardLevelService.FindAllMemberCardLevelWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.MemberCardLevelVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MemberCardLevelVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberCardLevelTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardLevelRoute struct {\n}\n\nfunc (s *MemberCardLevelRoute) InitMemberCardLevelRouter(g *gin.Engine) {\n\tmemberCardLevelController := controller.MemberCardLevelController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/memberCardLevel/add\", memberCardLevelController.AddMemberCardLevel)       // add\n\t\troute.POST(\"/api/memberCardLevel/update\", memberCardLevelController.UpdateMemberCardLevel)   // update\n\t\troute.POST(\"/api/memberCardLevel/delete\", memberCardLevelController.DeleteMemberCardLevel)   // delete\n\t\troute.POST(\"/api/memberCardLevel/query\", memberCardLevelController.QueryMemberCardLevels)    // query\n\t\troute.POST(\"/api/memberCardLevel/list\", memberCardLevelController.ListMemberCardLevels)      // list\n\t}\n}\n"}]