[{"po": "package po\n\n// ProductSalesTemplate 产品销售模板实体\ntype ProductSalesTemplate struct {\n\tId      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`       // 唯一id\n\tName    *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`         // 名称\n\tCtime   *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                // 创建时间\n\tUtime   *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                // 更新时间\n\tState   *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                // 状态\n\tVersion *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`            // 版本\n}\n\n// TableName 设置表名\nfunc (ProductSalesTemplate) TableName() string {\n\treturn \"product_sales_template\"\n}\n\nfunc (p ProductSalesTemplate) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductSalesTemplateVO 产品销售模板值对象\ntype ProductSalesTemplateVO struct {\n\tId      string `json:\"id\"`      // 唯一id\n\tName    string `json:\"name\"`    // 名称\n\tCtime   int64  `json:\"ctime\"`   // 创建时间\n\tUtime   int64  `json:\"utime\"`   // 更新时间\n\tState   int    `json:\"state\"`   // 状态\n\tVersion int    `json:\"version\"` // 版本\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductSalesTemplateTransfer struct {\n}\n\nfunc (transfer *ProductSalesTemplateTransfer) PoToVo(po po.ProductSalesTemplate) vo.ProductSalesTemplateVO {\n\tvo := vo.ProductSalesTemplateVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductSalesTemplateTransfer) VoToPo(vo vo.ProductSalesTemplateVO) po.ProductSalesTemplate {\n\tpo := po.ProductSalesTemplate{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "req_add": "package req\n\n// AddProductSalesTemplateReqDto 创建产品销售模板请求DTO\ntype AddProductSalesTemplateReqDto struct {\n\tName    *string `json:\"name\"`    // 名称\n}\n", "req_update": "package req\n\ntype UpdateProductSalesTemplateReqDto struct {\n\tId      *string `json:\"id\"`      // 唯一id\n\tName    *string `json:\"name\"`    // 名称\n}\n", "req_delete": "package req\n\ntype DeleteProductSalesTemplateReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}\n", "req_query": "package req\n\ntype QueryProductSalesTemplateReqDto struct {\n\tId        *string `json:\"id\"`        // 唯一id\n\tName      *string `json:\"name\"`      // 名称\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductSalesTemplateService struct {\n}\n\nfunc (service *ProductSalesTemplateService) CreateProductSalesTemplate(logCtx *gin.Context, template *po.ProductSalesTemplate) error {\n\treturn Save(template)\n}\n\nfunc (service *ProductSalesTemplateService) UpdateProductSalesTemplate(logCtx *gin.Context, template *po.ProductSalesTemplate) error {\n\treturn Update(template)\n}\n\nfunc (service *ProductSalesTemplateService) DeleteProductSalesTemplate(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductSalesTemplate{Id: &id})\n}\n\nfunc (service *ProductSalesTemplateService) FindProductSalesTemplateById(logCtx *gin.Context, id string) (template *po.ProductSalesTemplate, err error) {\n\ttemplate = &po.ProductSalesTemplate{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(template).Error\n\treturn\n}\n\nfunc (service *ProductSalesTemplateService) FindAllProductSalesTemplate(logCtx *gin.Context, reqDto *req.QueryProductSalesTemplateReqDto) (list *[]po.ProductSalesTemplate, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductSalesTemplate{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductSalesTemplate{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductSalesTemplateService) FindAllProductSalesTemplateWithPagination(logCtx *gin.Context, reqDto *req.QueryProductSalesTemplateReqDto) (list *[]po.ProductSalesTemplate, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductSalesTemplate{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductSalesTemplate{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductSalesTemplateController struct{}\n\nvar (\n\tproductSalesTemplateService  = impl.ProductSalesTemplateService{}\n\tproductSalesTemplateTransfer = transfer.ProductSalesTemplateTransfer{}\n)\n\n// @Summary 添加产品销售模板\n// @Description 添加产品销售模板\n// @Tags 产品销售模板\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductSalesTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductSalesTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productSalesTemplate/add [post]\nfunc (controller *ProductSalesTemplateController) AddProductSalesTemplate(ctx *gin.Context) {\n\treqDto := req.AddProductSalesTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\ttemplate := po.ProductSalesTemplate{}\n\tif reqDto.Name != nil {\n\t\ttemplate.Name = reqDto.Name\n\t}\n\terr = productSalesTemplateService.CreateProductSalesTemplate(ctx, &template)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productSalesTemplateTransfer.PoToVo(template))\n}\n\n// @Summary 更新产品销售模板\n// @Description 更新产品销售模板\n// @Tags 产品销售模板\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductSalesTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductSalesTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productSalesTemplate/update [post]\nfunc (controller *ProductSalesTemplateController) UpdateProductSalesTemplate(ctx *gin.Context) {\n\treqDto := req.UpdateProductSalesTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\ttemplate, err := productSalesTemplateService.FindProductSalesTemplateById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Name != nil {\n\t\ttemplate.Name = reqDto.Name\n\t}\n\terr = productSalesTemplateService.UpdateProductSalesTemplate(ctx, template)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productSalesTemplateTransfer.PoToVo(*template))\n}\n\n// @Summary 删除产品销售模板\n// @Description 删除产品销售模板\n// @Tags 产品销售模板\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductSalesTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productSalesTemplate/delete [post]\nfunc (controller *ProductSalesTemplateController) DeleteProductSalesTemplate(ctx *gin.Context) {\n\treqDto := req.DeleteProductSalesTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = productSalesTemplateService.DeleteProductSalesTemplate(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品销售模板\n// @Description 查询产品销售模板\n// @Tags 产品销售模板\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductSalesTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductSalesTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productSalesTemplate/query [post]\nfunc (controller *ProductSalesTemplateController) QueryProductSalesTemplates(ctx *gin.Context) {\n\treqDto := req.QueryProductSalesTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := productSalesTemplateService.FindAllProductSalesTemplate(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ProductSalesTemplateVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productSalesTemplateTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品销售模板列表\n// @Description 查询产品销售模板列表\n// @Tags 产品销售模板\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductSalesTemplateReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductSalesTemplateVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productSalesTemplate/list [post]\nfunc (controller *ProductSalesTemplateController) ListProductSalesTemplates(ctx *gin.Context) {\n\treqDto := req.QueryProductSalesTemplateReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := productSalesTemplateService.FindAllProductSalesTemplateWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ProductSalesTemplateVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductSalesTemplateVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productSalesTemplateTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductSalesTemplateRoute struct {\n}\n\nfunc (s *ProductSalesTemplateRoute) InitProductSalesTemplateRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductSalesTemplateController := controller.ProductSalesTemplateController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productSalesTemplate/add\", productSalesTemplateController.AddProductSalesTemplate)       //add\n\t\troute.POST(\"/api/productSalesTemplate/update\", productSalesTemplateController.UpdateProductSalesTemplate)   //update\n\t\troute.POST(\"/api/productSalesTemplate/delete\", productSalesTemplateController.DeleteProductSalesTemplate)   //delete\n\t\troute.POST(\"/api/productSalesTemplate/query\", productSalesTemplateController.QueryProductSalesTemplates)    //query\n\t\troute.POST(\"/api/productSalesTemplate/list\", productSalesTemplateController.ListProductSalesTemplates)     //list\n\t}\n}\n"}]