[{"po": "package po\n\n// CouponClaim 优惠券领取记录实体\ntype CouponClaim struct {\n\tId        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tClaimTime *int64  `gorm:\"column:claim_time;type:int;default:0\" json:\"claimTime\"`           // 领取时间\n\tCouponId  *string `gorm:\"column:coupon_id;type:varchar(64);default:''\" json:\"couponId\"`     // 优惠券ID\n\tMemberId  *string `gorm:\"column:member_id;type:varchar(64);default:''\" json:\"memberId\"`     // 会员ID\n\tCtime     *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间戳\n\tUtime     *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间戳\n\tState     *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态值\n\tVersion   *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (CouponClaim) TableName() string {\n\treturn \"coupon_claim\"\n}\n\nfunc (c CouponClaim) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CouponClaimVO 优惠券领取记录值对象\ntype CouponClaimVO struct {\n\tId        string `json:\"id\"`        // ID\n\tClaimTime int64  `json:\"claimTime\"` // 领取时间\n\tCouponId  string `json:\"couponId\"`  // 优惠券ID\n\tMemberId  string `json:\"memberId\"`  // 会员ID\n\tCtime     int64  `json:\"ctime\"`     // 创建时间戳\n\tUtime     int64  `json:\"utime\"`     // 更新时间戳\n\tState     int    `json:\"state\"`     // 状态值\n\tVersion   int    `json:\"version\"`   // 版本号\n}\n", "req_add": "package req\n\n// AddCouponClaimReqDto 创建优惠券领取记录请求DTO\ntype AddCouponClaimReqDto struct {\n\tClaimTime *int64  `json:\"claimTime\"` // 领取时间\n\tCouponId  *string `json:\"couponId\"`  // 优惠券ID\n\tMemberId  *string `json:\"memberId\"`  // 会员ID\n}\n", "req_update": "package req\n\ntype UpdateCouponClaimReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tClaimTime *int64  `json:\"claimTime\"` // 领取时间\n\tCouponId  *string `json:\"couponId\"`  // 优惠券ID\n\tMemberId  *string `json:\"memberId\"`  // 会员ID\n}\n", "req_delete": "package req\n\ntype DeleteCouponClaimReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCouponClaimReqDto struct {\n\tId        *string `json:\"id\"`        // ID\n\tClaimTime *int64  `json:\"claimTime\"` // 领取时间\n\tCouponId  *string `json:\"couponId\"`  // 优惠券ID\n\tMemberId  *string `json:\"memberId\"`  // 会员ID\n\tPageNum   *int    `json:\"pageNum\"`   // 页码\n\tPageSize  *int    `json:\"pageSize\"`  // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CouponClaimTransfer struct {\n}\n\nfunc (transfer *CouponClaimTransfer) PoToVo(po po.CouponClaim) vo.CouponClaimVO {\n\tvo := vo.CouponClaimVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CouponClaimTransfer) VoToPo(vo vo.CouponClaimVO) po.CouponClaim {\n\tpo := po.CouponClaim{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponClaimService struct {\n}\n\nfunc (service *CouponClaimService) CreateCouponClaim(logCtx *gin.Context, couponClaim *po.CouponClaim) error {\n\treturn Save(couponClaim)\n}\n\nfunc (service *CouponClaimService) UpdateCouponClaim(logCtx *gin.Context, couponClaim *po.CouponClaim) error {\n\treturn Update(couponClaim)\n}\n\nfunc (service *CouponClaimService) DeleteCouponClaim(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CouponClaim{Id: &id})\n}\n\nfunc (service *CouponClaimService) FindCouponClaimById(logCtx *gin.Context, id string) (couponClaim *po.CouponClaim, err error) {\n\tcouponClaim = &po.CouponClaim{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(couponClaim).Error\n\treturn\n}\n\nfunc (service *CouponClaimService) FindAllCouponClaim(logCtx *gin.Context, reqDto *req.QueryCouponClaimReqDto) (list *[]po.CouponClaim, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CouponClaim{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CouponClaim{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CouponClaimService) FindAllCouponClaimWithPagination(logCtx *gin.Context, reqDto *req.QueryCouponClaimReqDto) (list *[]po.CouponClaim, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CouponClaim{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CouponClaim{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponClaimController struct{}\n\nvar (\n\tcouponClaimService  = impl.CouponClaimService{}\n\tcouponClaimTransfer = transfer.CouponClaimTransfer{}\n)\n\n// @Summary 添加优惠券领取记录\n// @Description 添加优惠券领取记录\n// @Tags 优惠券领取记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddCouponClaimReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CouponClaimVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponClaim/add [post]\nfunc (controller *CouponClaimController) AddCouponClaim(ctx *gin.Context) {\n\treqDto := req.AddCouponClaimReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcouponClaim := po.CouponClaim{}\n\tif reqDto.ClaimTime != nil {\n\t\tcouponClaim.ClaimTime = reqDto.ClaimTime\n\t}\n\tif reqDto.CouponId != nil {\n\t\tcouponClaim.CouponId = reqDto.CouponId\n\t}\n\tif reqDto.MemberId != nil {\n\t\tcouponClaim.MemberId = reqDto.MemberId\n\t}\n\terr = couponClaimService.CreateCouponClaim(ctx, &couponClaim)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, couponClaimTransfer.PoToVo(couponClaim))\n}\n\n// @Summary 更新优惠券领取记录\n// @Description 更新优惠券领取记录\n// @Tags 优惠券领取记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCouponClaimReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CouponClaimVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponClaim/update [post]\nfunc (controller *CouponClaimController) UpdateCouponClaim(ctx *gin.Context) {\n\treqDto := req.UpdateCouponClaimReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcouponClaim, err := couponClaimService.FindCouponClaimById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.ClaimTime != nil {\n\t\tcouponClaim.ClaimTime = reqDto.ClaimTime\n\t}\n\tif reqDto.CouponId != nil {\n\t\tcouponClaim.CouponId = reqDto.CouponId\n\t}\n\tif reqDto.MemberId != nil {\n\t\tcouponClaim.MemberId = reqDto.MemberId\n\t}\n\terr = couponClaimService.UpdateCouponClaim(ctx, couponClaim)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, couponClaimTransfer.PoToVo(*couponClaim))\n}\n\n// @Summary 删除优惠券领取记录\n// @Description 删除优惠券领取记录\n// @Tags 优惠券领取记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCouponClaimReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponClaim/delete [post]\nfunc (controller *CouponClaimController) DeleteCouponClaim(ctx *gin.Context) {\n\treqDto := req.DeleteCouponClaimReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = couponClaimService.DeleteCouponClaim(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询优惠券领取记录\n// @Description 查询优惠券领取记录\n// @Tags 优惠券领取记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCouponClaimReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CouponClaimVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponClaim/query [post]\nfunc (controller *CouponClaimController) QueryCouponClaims(ctx *gin.Context) {\n\treqDto := req.QueryCouponClaimReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := couponClaimService.FindAllCouponClaim(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CouponClaimVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, couponClaimTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询优惠券领取记录列表\n// @Description 查询优惠券领取记录列表\n// @Tags 优惠券领取记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCouponClaimReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CouponClaimVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/couponClaim/list [post]\nfunc (a *CouponClaimController) ListCouponClaims(ctx *gin.Context) {\n\treqDto := req.QueryCouponClaimReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := couponClaimService.FindAllCouponClaimWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CouponClaimVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CouponClaimVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, couponClaimTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CouponClaimRoute struct {\n}\n\nfunc (s *CouponClaimRoute) InitCouponClaimRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcouponClaimController := controller.CouponClaimController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/couponClaim/add\", couponClaimController.AddCouponClaim)       //add\n\t\troute.POST(\"/api/couponClaim/update\", couponClaimController.UpdateCouponClaim) //update\n\t\troute.POST(\"/api/couponClaim/delete\", couponClaimController.DeleteCouponClaim) //delete\n\t\troute.POST(\"/api/couponClaim/query\", couponClaimController.QueryCouponClaims)  //query\n\t\troute.POST(\"/api/couponClaim/list\", couponClaimController.ListCouponClaims)   //list\n\t}\n}\n"}]