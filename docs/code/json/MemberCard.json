[{"po": "package po\n\n// MemberCard 会员卡实体\ntype MemberCard struct {\n\tId       *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tCardType *string  `gorm:\"column:card_type;type:varchar(64);default:''\" json:\"cardType\"` // 卡类型\n\tDiscount *float32 `gorm:\"column:discount;type:float;default:0\" json:\"discount\"`         // 折扣\n\tCtime    *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`               // 创建时间戳\n\tUtime    *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`               // 更新时间戳\n\tState    *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`               // 状态值\n\tVersion  *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`           // 版本号\n}\n\n// TableName 设置表名\nfunc (MemberCard) TableName() string {\n\treturn \"member_card\"\n}\n\nfunc (m MemberCard) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MemberCardVO 会员卡信息值对象\ntype MemberCardVO struct {\n\tId       string  `json:\"id\"`       // ID\n\tCardType string  `json:\"cardType\"` // 卡类型\n\tDiscount float32 `json:\"discount\"` // 折扣\n\tCtime    int64   `json:\"ctime\"`    // 创建时间戳\n\tUtime    int64   `json:\"utime\"`    // 更新时间戳\n\tState    int     `json:\"state\"`    // 状态值\n\tVersion  int     `json:\"version\"`  // 版本号\n}\n", "req_add": "package req\n\n// AddMemberCardReqDto 创建会员卡请求DTO\ntype AddMemberCardReqDto struct {\n\tCardType *string  `json:\"cardType\"` // 卡类型\n\tDiscount *float32 `json:\"discount\"` // 折扣\n}\n", "req_update": "package req\n\ntype UpdateMemberCardReqDto struct {\n\tId       *string  `json:\"id\"`       // ID\n\tCardType *string  `json:\"cardType\"` // 卡类型\n\tDiscount *float32 `json:\"discount\"` // 折扣\n}\n", "req_delete": "package req\n\ntype DeleteMemberCardReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryMemberCardReqDto struct {\n\tId       *string  `json:\"id\"`       // ID\n\tCardType *string  `json:\"cardType\"` // 卡类型\n\tDiscount *float32 `json:\"discount\"` // 折扣\n\tPageNum  *int     `json:\"pageNum\"`  // 页码\n\tPageSize *int     `json:\"pageSize\"` // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MemberCardTransfer struct {\n}\n\nfunc (transfer *MemberCardTransfer) PoToVo(po po.MemberCard) vo.MemberCardVO {\n\tvo := vo.MemberCardVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MemberCardTransfer) VoToPo(vo vo.MemberCardVO) po.MemberCard {\n\tpo := po.MemberCard{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardService struct {\n}\n\nfunc (service *MemberCardService) CreateMemberCard(logCtx *gin.Context, memberCard *po.MemberCard) error {\n\treturn Save(memberCard)\n}\n\nfunc (service *MemberCardService) UpdateMemberCard(logCtx *gin.Context, memberCard *po.MemberCard) error {\n\treturn Update(memberCard)\n}\n\nfunc (service *MemberCardService) DeleteMemberCard(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MemberCard{Id: &id})\n}\n\nfunc (service *MemberCardService) FindMemberCardById(logCtx *gin.Context, id string) (memberCard *po.MemberCard, err error) {\n\tmemberCard = &po.MemberCard{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(memberCard).Error\n\treturn\n}\n\nfunc (service *MemberCardService) FindAllMemberCard(logCtx *gin.Context, reqDto *req.QueryMemberCardReqDto) (list *[]po.MemberCard, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberCard{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MemberCard{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MemberCardService) FindAllMemberCardWithPagination(logCtx *gin.Context, reqDto *req.QueryMemberCardReqDto) (list *[]po.MemberCard, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MemberCard{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MemberCard{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardController struct{}\n\nvar (\n\tmemberCardService  = impl.MemberCardService{}\n\tmemberCardTransfer = transfer.MemberCardTransfer{}\n)\n\n// @Summary 添加会员卡\n// @Description 添加会员卡\n// @Tags 会员卡\n// @Accept json\n// @Produce json\n// @Param body body req.AddMemberCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-card/add [post]\nfunc (controller *MemberCardController) AddMemberCard(ctx *gin.Context) {\n\treqDto := req.AddMemberCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmemberCard := po.MemberCard{}\n\tif reqDto.CardType != nil {\n\t\tmemberCard.CardType = reqDto.CardType\n\t}\n\tif reqDto.Discount != nil {\n\t\tmemberCard.Discount = reqDto.Discount\n\t}\n\terr = memberCardService.CreateMemberCard(ctx, &memberCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberCardTransfer.PoToVo(memberCard))\n}\n\n// @Summary 更新会员卡\n// @Description 更新会员卡\n// @Tags 会员卡\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMemberCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MemberCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-card/update [post]\nfunc (controller *MemberCardController) UpdateMemberCard(ctx *gin.Context) {\n\treqDto := req.UpdateMemberCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tmemberCard, err := memberCardService.FindMemberCardById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CardType != nil {\n\t\tmemberCard.CardType = reqDto.CardType\n\t}\n\tif reqDto.Discount != nil {\n\t\tmemberCard.Discount = reqDto.Discount\n\t}\n\terr = memberCardService.UpdateMemberCard(ctx, memberCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, memberCardTransfer.PoToVo(*memberCard))\n}\n\n// @Summary 删除会员卡\n// @Description 删除会员卡\n// @Tags 会员卡\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMemberCardReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-card/delete [post]\nfunc (controller *MemberCardController) DeleteMemberCard(ctx *gin.Context) {\n\treqDto := req.DeleteMemberCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = memberCardService.DeleteMemberCard(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询会员卡\n// @Description 查询会员卡\n// @Tags 会员卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-card/query [post]\nfunc (controller *MemberCardController) QueryMemberCards(ctx *gin.Context) {\n\treqDto := req.QueryMemberCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := memberCardService.FindAllMemberCard(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.MemberCardVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, memberCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询会员卡列表\n// @Description 查询会员卡列表\n// @Tags 会员卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMemberCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MemberCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/member-card/list [post]\nfunc (a *MemberCardController) ListMemberCards(ctx *gin.Context) {\n\treqDto := req.QueryMemberCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := memberCardService.FindAllMemberCardWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.MemberCardVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MemberCardVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, memberCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MemberCardRoute struct {\n}\n\nfunc (s *MemberCardRoute) InitMemberCardRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmemberCardController := controller.MemberCardController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/member-card/add\", memberCardController.AddMemberCard)       //add\n\t\troute.POST(\"/api/member-card/update\", memberCardController.UpdateMemberCard) //update\n\t\troute.POST(\"/api/member-card/delete\", memberCardController.DeleteMemberCard) //delete\n\t\troute.POST(\"/api/member-card/query\", memberCardController.QueryMemberCards)  //query\n\t\troute.POST(\"/api/member-card/list\", memberCardController.ListMemberCards)   //list\n\t}\n}\n"}]