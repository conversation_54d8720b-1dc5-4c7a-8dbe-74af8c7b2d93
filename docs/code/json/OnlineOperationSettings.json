[{"po": "package po\n\n// OnlineOperationSettings 在线运营设置实体\ntype OnlineOperationSettings struct {\n\tId                        *string  `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tFirstBillingDuration      *int     `gorm:\"column:first_billing_duration;type:int;default:0\" json:\"firstBillingDuration\"`      // 首次计费时长\n\tMinBillingDuration        *int     `gorm:\"column:min_billing_duration;type:int;default:0\" json:\"minBillingDuration\"`        // 最小计费时长\n\tEarlyCheckinCompensation  *bool    `gorm:\"column:early_checkin_compensation;type:bool;default:false\" json:\"earlyCheckinCompensation\"`  // 是否允许提前入住补偿\n\tOrderMode                 *string  `gorm:\"column:order_mode;type:varchar(64);default:''\" json:\"orderMode\"`                 // 订单模式\n\tManualRoomInput          *bool    `gorm:\"column:manual_room_input;type:bool;default:false\" json:\"manualRoomInput\"`          // 是否允许手动输入房间号\n\tOnlineRoomBillingMode    *string  `gorm:\"column:online_room_billing_mode;type:varchar(64);default:''\" json:\"onlineRoomBillingMode\"`    // 在线房间计费模式\n\tInputSalesperson         *bool    `gorm:\"column:input_salesperson;type:bool;default:false\" json:\"inputSalesperson\"`         // 是否需要输入销售员\n\tPackagePriceDisplay      *string  `gorm:\"column:package_price_display;type:varchar(64);default:''\" json:\"packagePriceDisplay\"`      // 套餐价格展示方式\n\tRecommendedMemberCard    *string  `gorm:\"column:recommended_member_card;type:varchar(64);default:''\" json:\"recommendedMemberCard\"`    // 推荐会员卡\n\tUseOpenRoomMemberDiscount *bool    `gorm:\"column:use_open_room_member_discount;type:bool;default:false\" json:\"useOpenRoomMemberDiscount\"` // 开房是否使用会员折扣\n\tOrderPaymentTimeout      *int     `gorm:\"column:order_payment_timeout;type:int;default:0\" json:\"orderPaymentTimeout\"`      // 订单支付超时时间\n\tAllowMixedPayment        *bool    `gorm:\"column:allow_mixed_payment;type:bool;default:false\" json:\"allowMixedPayment\"`        // 是否允许混合支付\n\tQuickCardRegistration    *bool    `gorm:\"column:quick_card_registration;type:bool;default:false\" json:\"quickCardRegistration\"`    // 是否允许快速注册会员卡\n\tAutoWifiConnect          *bool    `gorm:\"column:auto_wifi_connect;type:bool;default:false\" json:\"autoWifiConnect\"`          // 是否自动连接WiFi\n\tWifiUsername             *string  `gorm:\"column:wifi_username;type:varchar(64);default:''\" json:\"wifiUsername\"`             // WiFi用户名\n\tWifiPassword             *string  `gorm:\"column:wifi_password;type:varchar(64);default:''\" json:\"wifiPassword\"`             // WiFi密码\n\tOnlineBusinessHours      *string  `gorm:\"column:online_business_hours;type:varchar(64);default:''\" json:\"onlineBusinessHours\"`      // 在线营业时间\n\tCtime                    *int64   `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime                    *int64   `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState                    *int     `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion                  *int     `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (OnlineOperationSettings) TableName() string {\n\treturn \"online_operation_settings\"\n}\n\nfunc (o OnlineOperationSettings) GetId() string {\n\treturn *o.Id\n}\n", "vo": "package vo\n\n// OnlineOperationSettingsVO 在线运营设置值对象\ntype OnlineOperationSettingsVO struct {\n\tId                        string  `json:\"id\"`                        // ID\n\tFirstBillingDuration      int     `json:\"firstBillingDuration\"`      // 首次计费时长\n\tMinBillingDuration        int     `json:\"minBillingDuration\"`        // 最小计费时长\n\tEarlyCheckinCompensation  bool    `json:\"earlyCheckinCompensation\"`  // 是否允许提前入住补偿\n\tOrderMode                 string  `json:\"orderMode\"`                 // 订单模式\n\tManualRoomInput          bool    `json:\"manualRoomInput\"`          // 是否允许手动输入房间号\n\tOnlineRoomBillingMode    string  `json:\"onlineRoomBillingMode\"`    // 在线房间计费模式\n\tInputSalesperson         bool    `json:\"inputSalesperson\"`         // 是否需要输入销售员\n\tPackagePriceDisplay      string  `json:\"packagePriceDisplay\"`      // 套餐价格展示方式\n\tRecommendedMemberCard    string  `json:\"recommendedMemberCard\"`    // 推荐会员卡\n\tUseOpenRoomMemberDiscount bool    `json:\"useOpenRoomMemberDiscount\"` // 开房是否使用会员折扣\n\tOrderPaymentTimeout      int     `json:\"orderPaymentTimeout\"`      // 订单支付超时时间\n\tAllowMixedPayment        bool    `json:\"allowMixedPayment\"`        // 是否允许混合支付\n\tQuickCardRegistration    bool    `json:\"quickCardRegistration\"`    // 是否允许快速注册会员卡\n\tAutoWifiConnect          bool    `json:\"autoWifiConnect\"`          // 是否自动连接WiFi\n\tWifiUsername             string  `json:\"wifiUsername\"`             // WiFi用户名\n\tWifiPassword             string  `json:\"wifiPassword\"`             // WiFi密码\n\tOnlineBusinessHours      string  `json:\"onlineBusinessHours\"`      // 在线营业时间\n\tCtime                    int64   `json:\"ctime\"`                    // 创建时间戳\n\tUtime                    int64   `json:\"utime\"`                    // 更新时间戳\n\tState                    int     `json:\"state\"`                    // 状态值\n\tVersion                  int     `json:\"version\"`                  // 版本号\n}\n", "req_add": "package req\n\n// AddOnlineOperationSettingsReqDto 创建在线运营设置请求DTO\ntype AddOnlineOperationSettingsReqDto struct {\n\tFirstBillingDuration      *int    `json:\"firstBillingDuration\"`      // 首次计费时长\n\tMinBillingDuration        *int    `json:\"minBillingDuration\"`        // 最小计费时长\n\tEarlyCheckinCompensation  *bool   `json:\"earlyCheckinCompensation\"`  // 是否允许提前入住补偿\n\tOrderMode                 *string `json:\"orderMode\"`                 // 订单模式\n\tManualRoomInput          *bool   `json:\"manualRoomInput\"`          // 是否允许手动输入房间号\n\tOnlineRoomBillingMode    *string `json:\"onlineRoomBillingMode\"`    // 在线房间计费模式\n\tInputSalesperson         *bool   `json:\"inputSalesperson\"`         // 是否需要输入销售员\n\tPackagePriceDisplay      *string `json:\"packagePriceDisplay\"`      // 套餐价格展示方式\n\tRecommendedMemberCard    *string `json:\"recommendedMemberCard\"`    // 推荐会员卡\n\tUseOpenRoomMemberDiscount *bool   `json:\"useOpenRoomMemberDiscount\"` // 开房是否使用会员折扣\n\tOrderPaymentTimeout      *int    `json:\"orderPaymentTimeout\"`      // 订单支付超时时间\n\tAllowMixedPayment        *bool   `json:\"allowMixedPayment\"`        // 是否允许混合支付\n\tQuickCardRegistration    *bool   `json:\"quickCardRegistration\"`    // 是否允许快速注册会员卡\n\tAutoWifiConnect          *bool   `json:\"autoWifiConnect\"`          // 是否自动连接WiFi\n\tWifiUsername             *string `json:\"wifiUsername\"`             // WiFi用户名\n\tWifiPassword             *string `json:\"wifiPassword\"`             // WiFi密码\n\tOnlineBusinessHours      *string `json:\"onlineBusinessHours\"`      // 在线营业时间\n}\n", "req_update": "package req\n\n// UpdateOnlineOperationSettingsReqDto 更新在线运营设置请求DTO\ntype UpdateOnlineOperationSettingsReqDto struct {\n\tId                        *string `json:\"id\"`                        // ID\n\tFirstBillingDuration      *int    `json:\"firstBillingDuration\"`      // 首次计费时长\n\tMinBillingDuration        *int    `json:\"minBillingDuration\"`        // 最小计费时长\n\tEarlyCheckinCompensation  *bool   `json:\"earlyCheckinCompensation\"`  // 是否允许提前入住补偿\n\tOrderMode                 *string `json:\"orderMode\"`                 // 订单模式\n\tManualRoomInput          *bool   `json:\"manualRoomInput\"`          // 是否允许手动输入房间号\n\tOnlineRoomBillingMode    *string `json:\"onlineRoomBillingMode\"`    // 在线房间计费模式\n\tInputSalesperson         *bool   `json:\"inputSalesperson\"`         // 是否需要输入销售员\n\tPackagePriceDisplay      *string `json:\"packagePriceDisplay\"`      // 套餐价格展示方式\n\tRecommendedMemberCard    *string `json:\"recommendedMemberCard\"`    // 推荐会员卡\n\tUseOpenRoomMemberDiscount *bool   `json:\"useOpenRoomMemberDiscount\"` // 开房是否使用会员折扣\n\tOrderPaymentTimeout      *int    `json:\"orderPaymentTimeout\"`      // 订单支付超时时间\n\tAllowMixedPayment        *bool   `json:\"allowMixedPayment\"`        // 是否允许混合支付\n\tQuickCardRegistration    *bool   `json:\"quickCardRegistration\"`    // 是否允许快速注册会员卡\n\tAutoWifiConnect          *bool   `json:\"autoWifiConnect\"`          // 是否自动连接WiFi\n\tWifiUsername             *string `json:\"wifiUsername\"`             // WiFi用户名\n\tWifiPassword             *string `json:\"wifiPassword\"`             // WiFi密码\n\tOnlineBusinessHours      *string `json:\"onlineBusinessHours\"`      // 在线营业时间\n}\n", "req_delete": "package req\n\ntype DeleteOnlineOperationSettingsReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryOnlineOperationSettingsReqDto struct {\n\tId                        *string `json:\"id\"`                        // ID\n\tFirstBillingDuration      *int    `json:\"firstBillingDuration\"`      // 首次计费时长\n\tMinBillingDuration        *int    `json:\"minBillingDuration\"`        // 最小计费时长\n\tEarlyCheckinCompensation  *bool   `json:\"earlyCheckinCompensation\"`  // 是否允许提前入住补偿\n\tOrderMode                 *string `json:\"orderMode\"`                 // 订单模式\n\tManualRoomInput          *bool   `json:\"manualRoomInput\"`          // 是否允许手动输入房间号\n\tOnlineRoomBillingMode    *string `json:\"onlineRoomBillingMode\"`    // 在线房间计费模式\n\tInputSalesperson         *bool   `json:\"inputSalesperson\"`         // 是否需要输入销售员\n\tPackagePriceDisplay      *string `json:\"packagePriceDisplay\"`      // 套餐价格展示方式\n\tRecommendedMemberCard    *string `json:\"recommendedMemberCard\"`    // 推荐会员卡\n\tUseOpenRoomMemberDiscount *bool   `json:\"useOpenRoomMemberDiscount\"` // 开房是否使用会员折扣\n\tOrderPaymentTimeout      *int    `json:\"orderPaymentTimeout\"`      // 订单支付超时时间\n\tAllowMixedPayment        *bool   `json:\"allowMixedPayment\"`        // 是否允许混合支付\n\tQuickCardRegistration    *bool   `json:\"quickCardRegistration\"`    // 是否允许快速注册会员卡\n\tAutoWifiConnect          *bool   `json:\"autoWifiConnect\"`          // 是否自动连接WiFi\n\tWifiUsername             *string `json:\"wifiUsername\"`             // WiFi用户名\n\tWifiPassword             *string `json:\"wifiPassword\"`             // WiFi密码\n\tOnlineBusinessHours      *string `json:\"onlineBusinessHours\"`      // 在线营业时间\n\tPageNum                  *int    `json:\"pageNum\"`                  // 页码\n\tPageSize                 *int    `json:\"pageSize\"`                 // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype OnlineOperationSettingsTransfer struct {\n}\n\nfunc (transfer *OnlineOperationSettingsTransfer) PoToVo(po po.OnlineOperationSettings) vo.OnlineOperationSettingsVO {\n\tvo := vo.OnlineOperationSettingsVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *OnlineOperationSettingsTransfer) VoToPo(vo vo.OnlineOperationSettingsVO) po.OnlineOperationSettings {\n\tpo := po.OnlineOperationSettings{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OnlineOperationSettingsService struct {\n}\n\nfunc (service *OnlineOperationSettingsService) CreateOnlineOperationSettings(logCtx *gin.Context, settings *po.OnlineOperationSettings) error {\n\treturn Save(settings)\n}\n\nfunc (service *OnlineOperationSettingsService) UpdateOnlineOperationSettings(logCtx *gin.Context, settings *po.OnlineOperationSettings) error {\n\treturn Update(settings)\n}\n\nfunc (service *OnlineOperationSettingsService) DeleteOnlineOperationSettings(logCtx *gin.Context, id string) error {\n\treturn Delete(po.OnlineOperationSettings{Id: &id})\n}\n\nfunc (service *OnlineOperationSettingsService) FindOnlineOperationSettingsById(logCtx *gin.Context, id string) (settings *po.OnlineOperationSettings, err error) {\n\tsettings = &po.OnlineOperationSettings{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(settings).Error\n\treturn\n}\n\nfunc (service *OnlineOperationSettingsService) FindAllOnlineOperationSettings(logCtx *gin.Context, reqDto *req.QueryOnlineOperationSettingsReqDto) (list *[]po.OnlineOperationSettings, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OnlineOperationSettings{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.OnlineOperationSettings{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *OnlineOperationSettingsService) FindAllOnlineOperationSettingsWithPagination(logCtx *gin.Context, reqDto *req.QueryOnlineOperationSettingsReqDto) (list *[]po.OnlineOperationSettings, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.OnlineOperationSettings{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.OnlineOperationSettings{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OnlineOperationSettingsController struct{}\n\nvar (\n\tonlineOperationSettingsService  = impl.OnlineOperationSettingsService{}\n\tonlineOperationSettingsTransfer = transfer.OnlineOperationSettingsTransfer{}\n)\n\n// @Summary 添加在线运营设置\n// @Description 添加在线运营设置\n// @Tags 在线运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddOnlineOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OnlineOperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/online-operation-settings/add [post]\nfunc (controller *OnlineOperationSettingsController) AddOnlineOperationSettings(ctx *gin.Context) {\n\treqDto := req.AddOnlineOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsettings := po.OnlineOperationSettings{}\n\tif reqDto.FirstBillingDuration != nil {\n\t\tsettings.FirstBillingDuration = reqDto.FirstBillingDuration\n\t}\n\tif reqDto.MinBillingDuration != nil {\n\t\tsettings.MinBillingDuration = reqDto.MinBillingDuration\n\t}\n\tif reqDto.EarlyCheckinCompensation != nil {\n\t\tsettings.EarlyCheckinCompensation = reqDto.EarlyCheckinCompensation\n\t}\n\tif reqDto.OrderMode != nil {\n\t\tsettings.OrderMode = reqDto.OrderMode\n\t}\n\tif reqDto.ManualRoomInput != nil {\n\t\tsettings.ManualRoomInput = reqDto.ManualRoomInput\n\t}\n\tif reqDto.OnlineRoomBillingMode != nil {\n\t\tsettings.OnlineRoomBillingMode = reqDto.OnlineRoomBillingMode\n\t}\n\tif reqDto.InputSalesperson != nil {\n\t\tsettings.InputSalesperson = reqDto.InputSalesperson\n\t}\n\tif reqDto.PackagePriceDisplay != nil {\n\t\tsettings.PackagePriceDisplay = reqDto.PackagePriceDisplay\n\t}\n\tif reqDto.RecommendedMemberCard != nil {\n\t\tsettings.RecommendedMemberCard = reqDto.RecommendedMemberCard\n\t}\n\tif reqDto.UseOpenRoomMemberDiscount != nil {\n\t\tsettings.UseOpenRoomMemberDiscount = reqDto.UseOpenRoomMemberDiscount\n\t}\n\tif reqDto.OrderPaymentTimeout != nil {\n\t\tsettings.OrderPaymentTimeout = reqDto.OrderPaymentTimeout\n\t}\n\tif reqDto.AllowMixedPayment != nil {\n\t\tsettings.AllowMixedPayment = reqDto.AllowMixedPayment\n\t}\n\tif reqDto.QuickCardRegistration != nil {\n\t\tsettings.QuickCardRegistration = reqDto.QuickCardRegistration\n\t}\n\tif reqDto.AutoWifiConnect != nil {\n\t\tsettings.AutoWifiConnect = reqDto.AutoWifiConnect\n\t}\n\tif reqDto.WifiUsername != nil {\n\t\tsettings.WifiUsername = reqDto.WifiUsername\n\t}\n\tif reqDto.WifiPassword != nil {\n\t\tsettings.WifiPassword = reqDto.WifiPassword\n\t}\n\tif reqDto.OnlineBusinessHours != nil {\n\t\tsettings.OnlineBusinessHours = reqDto.OnlineBusinessHours\n\t}\n\n\terr = onlineOperationSettingsService.CreateOnlineOperationSettings(ctx, &settings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, onlineOperationSettingsTransfer.PoToVo(settings))\n}\n\n// @Summary 更新在线运营设置\n// @Description 更新在线运营设置\n// @Tags 在线运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateOnlineOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.OnlineOperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/online-operation-settings/update [post]\nfunc (controller *OnlineOperationSettingsController) UpdateOnlineOperationSettings(ctx *gin.Context) {\n\treqDto := req.UpdateOnlineOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tsettings, err := onlineOperationSettingsService.FindOnlineOperationSettingsById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.FirstBillingDuration != nil {\n\t\tsettings.FirstBillingDuration = reqDto.FirstBillingDuration\n\t}\n\tif reqDto.MinBillingDuration != nil {\n\t\tsettings.MinBillingDuration = reqDto.MinBillingDuration\n\t}\n\tif reqDto.EarlyCheckinCompensation != nil {\n\t\tsettings.EarlyCheckinCompensation = reqDto.EarlyCheckinCompensation\n\t}\n\tif reqDto.OrderMode != nil {\n\t\tsettings.OrderMode = reqDto.OrderMode\n\t}\n\tif reqDto.ManualRoomInput != nil {\n\t\tsettings.ManualRoomInput = reqDto.ManualRoomInput\n\t}\n\tif reqDto.OnlineRoomBillingMode != nil {\n\t\tsettings.OnlineRoomBillingMode = reqDto.OnlineRoomBillingMode\n\t}\n\tif reqDto.InputSalesperson != nil {\n\t\tsettings.InputSalesperson = reqDto.InputSalesperson\n\t}\n\tif reqDto.PackagePriceDisplay != nil {\n\t\tsettings.PackagePriceDisplay = reqDto.PackagePriceDisplay\n\t}\n\tif reqDto.RecommendedMemberCard != nil {\n\t\tsettings.RecommendedMemberCard = reqDto.RecommendedMemberCard\n\t}\n\tif reqDto.UseOpenRoomMemberDiscount != nil {\n\t\tsettings.UseOpenRoomMemberDiscount = reqDto.UseOpenRoomMemberDiscount\n\t}\n\tif reqDto.OrderPaymentTimeout != nil {\n\t\tsettings.OrderPaymentTimeout = reqDto.OrderPaymentTimeout\n\t}\n\tif reqDto.AllowMixedPayment != nil {\n\t\tsettings.AllowMixedPayment = reqDto.AllowMixedPayment\n\t}\n\tif reqDto.QuickCardRegistration != nil {\n\t\tsettings.QuickCardRegistration = reqDto.QuickCardRegistration\n\t}\n\tif reqDto.AutoWifiConnect != nil {\n\t\tsettings.AutoWifiConnect = reqDto.AutoWifiConnect\n\t}\n\tif reqDto.WifiUsername != nil {\n\t\tsettings.WifiUsername = reqDto.WifiUsername\n\t}\n\tif reqDto.WifiPassword != nil {\n\t\tsettings.WifiPassword = reqDto.WifiPassword\n\t}\n\tif reqDto.OnlineBusinessHours != nil {\n\t\tsettings.OnlineBusinessHours = reqDto.OnlineBusinessHours\n\t}\n\n\terr = onlineOperationSettingsService.UpdateOnlineOperationSettings(ctx, settings)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, onlineOperationSettingsTransfer.PoToVo(*settings))\n}\n\n// @Summary 删除在线运营设置\n// @Description 删除在线运营设置\n// @Tags 在线运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteOnlineOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/online-operation-settings/delete [post]\nfunc (controller *OnlineOperationSettingsController) DeleteOnlineOperationSettings(ctx *gin.Context) {\n\treqDto := req.DeleteOnlineOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = onlineOperationSettingsService.DeleteOnlineOperationSettings(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询在线运营设置\n// @Description 查询在线运营设置\n// @Tags 在线运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOnlineOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OnlineOperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/online-operation-settings/query [post]\nfunc (controller *OnlineOperationSettingsController) QueryOnlineOperationSettings(ctx *gin.Context) {\n\treqDto := req.QueryOnlineOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := onlineOperationSettingsService.FindAllOnlineOperationSettings(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.OnlineOperationSettingsVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, onlineOperationSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询在线运营设置列表\n// @Description 查询在线运营设置列表\n// @Tags 在线运营设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryOnlineOperationSettingsReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.OnlineOperationSettingsVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/online-operation-settings/list [post]\nfunc (controller *OnlineOperationSettingsController) ListOnlineOperationSettings(ctx *gin.Context) {\n\treqDto := req.QueryOnlineOperationSettingsReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := onlineOperationSettingsService.FindAllOnlineOperationSettingsWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.OnlineOperationSettingsVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.OnlineOperationSettingsVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, onlineOperationSettingsTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype OnlineOperationSettingsRoute struct {\n}\n\nfunc (s *OnlineOperationSettingsRoute) InitOnlineOperationSettingsRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tonlineOperationSettingsController := controller.OnlineOperationSettingsController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/online-operation-settings/add\", onlineOperationSettingsController.AddOnlineOperationSettings)       //add\n\t\troute.POST(\"/api/online-operation-settings/update\", onlineOperationSettingsController.UpdateOnlineOperationSettings) //update\n\t\troute.POST(\"/api/online-operation-settings/delete\", onlineOperationSettingsController.DeleteOnlineOperationSettings) //delete\n\t\troute.POST(\"/api/online-operation-settings/query\", onlineOperationSettingsController.QueryOnlineOperationSettings)   //query\n\t\troute.POST(\"/api/online-operation-settings/list\", onlineOperationSettingsController.ListOnlineOperationSettings)     //list\n\t}\n}\n"}]