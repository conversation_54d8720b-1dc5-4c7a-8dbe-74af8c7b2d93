[{"po": "package po\n\n// ProductPackageType 产品套餐类型实体\ntype ProductPackageType struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                        // 套餐类型名称\n\tDistributionChannels *string `gorm:\"column:distribution_channels;type:varchar(255);default:''\" json:\"distributionChannels\"` // 分销渠道\n\tIsDisplayed         *bool   `gorm:\"column:is_displayed;type:tinyint(1);default:0\" json:\"isDisplayed\"`           // 是否显示\n\tSupportPoints       *bool   `gorm:\"column:support_points;type:tinyint(1);default:0\" json:\"supportPoints\"`       // 是否支持积分\n\tCtime              *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                          // 创建时间\n\tUtime              *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                          // 更新时间\n\tState              *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                            // 状态\n\tVersion            *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                        // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductPackageType) TableName() string {\n\treturn \"product_package_type\"\n}\n\nfunc (p ProductPackageType) GetId() string {\n\treturn *p.Id\n}", "vo": "package vo\n\n// ProductPackageTypeVO 产品套餐类型值对象\ntype ProductPackageTypeVO struct {\n\tId                  string `json:\"id\"`                  // ID\n\tName                string `json:\"name\"`                // 套餐类型名称\n\tDistributionChannels string `json:\"distributionChannels\"` // 分销渠道\n\tIsDisplayed         bool   `json:\"isDisplayed\"`         // 是否显示\n\tSupportPoints       bool   `json:\"supportPoints\"`       // 是否支持积分\n\tCtime              int64  `json:\"ctime\"`              // 创建时间\n\tUtime              int64  `json:\"utime\"`              // 更新时间\n\tState              int    `json:\"state\"`              // 状态\n\tVersion            int    `json:\"version\"`            // 版本号\n}", "req_add": "package req\n\n// AddProductPackageTypeReqDto 创建产品套餐类型请求DTO\ntype AddProductPackageTypeReqDto struct {\n\tName                *string `json:\"name\"`                // 套餐类型名称\n\tDistributionChannels *string `json:\"distributionChannels\"` // 分销渠道\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tSupportPoints       *bool   `json:\"supportPoints\"`       // 是否支持积分\n}", "req_update": "package req\n\n// UpdateProductPackageTypeReqDto 更新产品套餐类型请求DTO\ntype UpdateProductPackageTypeReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tName                *string `json:\"name\"`                // 套餐类型名称\n\tDistributionChannels *string `json:\"distributionChannels\"` // 分销渠道\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tSupportPoints       *bool   `json:\"supportPoints\"`       // 是否支持积分\n}", "req_delete": "package req\n\n// DeleteProductPackageTypeReqDto 删除产品套餐类型请求DTO\ntype DeleteProductPackageTypeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryProductPackageTypeReqDto 查询产品套餐类型请求DTO\ntype QueryProductPackageTypeReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tName                *string `json:\"name\"`                // 套餐类型名称\n\tDistributionChannels *string `json:\"distributionChannels\"` // 分销渠道\n\tIsDisplayed         *bool   `json:\"isDisplayed\"`         // 是否显示\n\tSupportPoints       *bool   `json:\"supportPoints\"`       // 是否支持积分\n\tPageNum             *int    `json:\"pageNum\"`             // 页码\n\tPageSize            *int    `json:\"pageSize\"`            // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductPackageTypeTransfer struct {\n}\n\nfunc (transfer *ProductPackageTypeTransfer) PoToVo(po po.ProductPackageType) vo.ProductPackageTypeVO {\n\tvo := vo.ProductPackageTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductPackageTypeTransfer) VoToPo(vo vo.ProductPackageTypeVO) po.ProductPackageType {\n\tpo := po.ProductPackageType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductPackageTypeService struct {\n}\n\nfunc (service *ProductPackageTypeService) CreateProductPackageType(logCtx *gin.Context, productPackageType *po.ProductPackageType) error {\n\treturn Save(productPackageType)\n}\n\nfunc (service *ProductPackageTypeService) UpdateProductPackageType(logCtx *gin.Context, productPackageType *po.ProductPackageType) error {\n\treturn Update(productPackageType)\n}\n\nfunc (service *ProductPackageTypeService) DeleteProductPackageType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductPackageType{Id: &id})\n}\n\nfunc (service *ProductPackageTypeService) FindProductPackageTypeById(logCtx *gin.Context, id string) (productPackageType *po.ProductPackageType, err error) {\n\tproductPackageType = &po.ProductPackageType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productPackageType).Error\n\treturn\n}\n\nfunc (service *ProductPackageTypeService) FindAllProductPackageType(logCtx *gin.Context, reqDto *req.QueryProductPackageTypeReqDto) (list *[]po.ProductPackageType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductPackageType{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.DistributionChannels != nil && *reqDto.DistributionChannels != \"\" {\n\t\tdb = db.Where(\"distribution_channels=?\", *reqDto.DistributionChannels)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\tif reqDto.SupportPoints != nil {\n\t\tdb = db.Where(\"support_points=?\", *reqDto.SupportPoints)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductPackageType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductPackageTypeService) FindAllProductPackageTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryProductPackageTypeReqDto) (list *[]po.ProductPackageType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductPackageType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.DistributionChannels != nil && *reqDto.DistributionChannels != \"\" {\n\t\tdb = db.Where(\"distribution_channels=?\", *reqDto.DistributionChannels)\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\tif reqDto.SupportPoints != nil {\n\t\tdb = db.Where(\"support_points=?\", *reqDto.SupportPoints)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductPackageType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductPackageTypeController struct{}\n\nvar (\n\tproductPackageTypeService  = impl.ProductPackageTypeService{}\n\tproductPackageTypeTransfer = transfer.ProductPackageTypeTransfer{}\n)\n\n// @Summary 添加产品套餐类型\n// @Description 添加产品套餐类型\n// @Tags 产品套餐类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductPackageTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductPackageTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productPackageType/add [post]\nfunc (controller *ProductPackageTypeController) AddProductPackageType(ctx *gin.Context) {\n\treqDto := req.AddProductPackageTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductPackageType := po.ProductPackageType{}\n\tif reqDto.Name != nil {\n\t\tproductPackageType.Name = reqDto.Name\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tproductPackageType.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tproductPackageType.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.SupportPoints != nil {\n\t\tproductPackageType.SupportPoints = reqDto.SupportPoints\n\t}\n\n\terr = productPackageTypeService.CreateProductPackageType(ctx, &productPackageType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productPackageTypeTransfer.PoToVo(productPackageType))\n}\n\n// @Summary 更新产品套餐类型\n// @Description 更新产品套餐类型\n// @Tags 产品套餐类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductPackageTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductPackageTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productPackageType/update [post]\nfunc (controller *ProductPackageTypeController) UpdateProductPackageType(ctx *gin.Context) {\n\treqDto := req.UpdateProductPackageTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproductPackageType, err := productPackageTypeService.FindProductPackageTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tproductPackageType.Name = reqDto.Name\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tproductPackageType.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tproductPackageType.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.SupportPoints != nil {\n\t\tproductPackageType.SupportPoints = reqDto.SupportPoints\n\t}\n\n\terr = productPackageTypeService.UpdateProductPackageType(ctx, productPackageType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productPackageTypeTransfer.PoToVo(*productPackageType))\n}\n\n// @Summary 删除产品套餐类型\n// @Description 删除产品套餐类型\n// @Tags 产品套餐类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductPackageTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productPackageType/delete [post]\nfunc (controller *ProductPackageTypeController) DeleteProductPackageType(ctx *gin.Context) {\n\treqDto := req.DeleteProductPackageTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = productPackageTypeService.DeleteProductPackageType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品套餐类型\n// @Description 查询产品套餐类型\n// @Tags 产品套餐类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductPackageTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductPackageTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productPackageType/query [post]\nfunc (controller *ProductPackageTypeController) QueryProductPackageTypes(ctx *gin.Context) {\n\treqDto := req.QueryProductPackageTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := productPackageTypeService.FindAllProductPackageType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.ProductPackageTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productPackageTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品套餐类型列表\n// @Description 查询产品套餐类型列表\n// @Tags 产品套餐类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductPackageTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductPackageTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/productPackageType/list [post]\nfunc (controller *ProductPackageTypeController) ListProductPackageTypes(ctx *gin.Context) {\n\treqDto := req.QueryProductPackageTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := productPackageTypeService.FindAllProductPackageTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.ProductPackageTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductPackageTypeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productPackageTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductPackageTypeRoute struct {\n}\n\nfunc (s *ProductPackageTypeRoute) InitProductPackageTypeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductPackageTypeController := controller.ProductPackageTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/productPackageType/add\", productPackageTypeController.AddProductPackageType)       // add\n\t\troute.POST(\"/api/productPackageType/update\", productPackageTypeController.UpdateProductPackageType)   // update\n\t\troute.POST(\"/api/productPackageType/delete\", productPackageTypeController.DeleteProductPackageType)   // delete\n\t\troute.POST(\"/api/productPackageType/query\", productPackageTypeController.QueryProductPackageTypes)    // query\n\t\troute.POST(\"/api/productPackageType/list\", productPackageTypeController.ListProductPackageTypes)     // list\n\t}\n}"}]