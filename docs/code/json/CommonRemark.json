[{"po": "package po\n\n// CommonRemark 备注信息实体\ntype CommonRemark struct {\n\tId      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // ID\n\tType    *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`               // 备注类型\n\tContent *string `gorm:\"column:content;type:varchar(255);default:''\" json:\"content\"`         // 备注内容\n\tCtime   *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                      // 创建时间戳\n\tUtime   *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                      // 更新时间戳\n\tState   *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态值\n\tVersion *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (CommonRemark) TableName() string {\n\treturn \"common_remark\"\n}\n\nfunc (c CommonRemark) GetId() string {\n\treturn *c.Id\n}\n", "vo": "package vo\n\n// CommonRemarkVO 备注信息值对象\ntype CommonRemarkVO struct {\n\tId      string `json:\"id\"`      // ID\n\tType    string `json:\"type\"`    // 备注类型\n\tContent string `json:\"content\"` // 备注内容\n\tCtime   int64  `json:\"ctime\"`   // 创建时间戳\n\tUtime   int64  `json:\"utime\"`   // 更新时间戳\n\tState   int    `json:\"state\"`   // 状态值\n\tVersion int    `json:\"version\"` // 版本号\n}\n", "req_add": "package req\n\n// AddCommonRemarkReqDto 创建备注请求DTO\ntype AddCommonRemarkReqDto struct {\n\tType    *string `json:\"type\"`    // 备注类型\n\tContent *string `json:\"content\"` // 备注内容\n}\n", "req_update": "package req\n\ntype UpdateCommonRemarkReqDto struct {\n\tId      *string `json:\"id\"`      // ID\n\tType    *string `json:\"type\"`    // 备注类型\n\tContent *string `json:\"content\"` // 备注内容\n}\n", "req_delete": "package req\n\ntype DeleteCommonRemarkReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryCommonRemarkReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tType     *string `json:\"type\"`     // 备注类型\n\tContent  *string `json:\"content\"`  // 备注内容\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype CommonRemarkTransfer struct {\n}\n\nfunc (transfer *CommonRemarkTransfer) PoToVo(po po.CommonRemark) vo.CommonRemarkVO {\n\tvo := vo.CommonRemarkVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *CommonRemarkTransfer) VoToPo(vo vo.CommonRemarkVO) po.CommonRemark {\n\tpo := po.CommonRemark{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommonRemarkService struct {\n}\n\nfunc (service *CommonRemarkService) CreateCommonRemark(logCtx *gin.Context, commonRemark *po.CommonRemark) error {\n\treturn Save(commonRemark)\n}\n\nfunc (service *CommonRemarkService) UpdateCommonRemark(logCtx *gin.Context, commonRemark *po.CommonRemark) error {\n\treturn Update(commonRemark)\n}\n\nfunc (service *CommonRemarkService) DeleteCommonRemark(logCtx *gin.Context, id string) error {\n\treturn Delete(po.CommonRemark{Id: &id})\n}\n\nfunc (service *CommonRemarkService) FindCommonRemarkById(logCtx *gin.Context, id string) (commonRemark *po.CommonRemark, err error) {\n\tcommonRemark = &po.CommonRemark{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(commonRemark).Error\n\treturn\n}\n\nfunc (service *CommonRemarkService) FindAllCommonRemark(logCtx *gin.Context, reqDto *req.QueryCommonRemarkReqDto) (list *[]po.CommonRemark, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CommonRemark{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.CommonRemark{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *CommonRemarkService) FindAllCommonRemarkWithPagination(logCtx *gin.Context, reqDto *req.QueryCommonRemarkReqDto) (list *[]po.CommonRemark, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.CommonRemark{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.CommonRemark{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommonRemarkController struct{}\n\nvar (\n\tcommonRemarkService  = impl.CommonRemarkService{}\n\tcommonRemarkTransfer = transfer.CommonRemarkTransfer{}\n)\n\n// @Summary 添加备注\n// @Description 添加备注\n// @Tags 备注\n// @Accept json\n// @Produce json\n// @Param body body req.AddCommonRemarkReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CommonRemarkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/common-remark/add [post]\nfunc (controller *CommonRemarkController) AddCommonRemark(ctx *gin.Context) {\n\treqDto := req.AddCommonRemarkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tcommonRemark := po.CommonRemark{}\n\tif reqDto.Type != nil {\n\t\tcommonRemark.Type = reqDto.Type\n\t}\n\tif reqDto.Content != nil {\n\t\tcommonRemark.Content = reqDto.Content\n\t}\n\terr = commonRemarkService.CreateCommonRemark(ctx, &commonRemark)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, commonRemarkTransfer.PoToVo(commonRemark))\n}\n\n// @Summary 更新备注\n// @Description 更新备注\n// @Tags 备注\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateCommonRemarkReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.CommonRemarkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/common-remark/update [post]\nfunc (controller *CommonRemarkController) UpdateCommonRemark(ctx *gin.Context) {\n\treqDto := req.UpdateCommonRemarkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tcommonRemark, err := commonRemarkService.FindCommonRemarkById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Type != nil {\n\t\tcommonRemark.Type = reqDto.Type\n\t}\n\tif reqDto.Content != nil {\n\t\tcommonRemark.Content = reqDto.Content\n\t}\n\terr = commonRemarkService.UpdateCommonRemark(ctx, commonRemark)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, commonRemarkTransfer.PoToVo(*commonRemark))\n}\n\n// @Summary 删除备注\n// @Description 删除备注\n// @Tags 备注\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteCommonRemarkReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/common-remark/delete [post]\nfunc (controller *CommonRemarkController) DeleteCommonRemark(ctx *gin.Context) {\n\treqDto := req.DeleteCommonRemarkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = commonRemarkService.DeleteCommonRemark(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询备注\n// @Description 查询备注\n// @Tags 备注\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCommonRemarkReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CommonRemarkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/common-remark/query [post]\nfunc (controller *CommonRemarkController) QueryCommonRemarks(ctx *gin.Context) {\n\treqDto := req.QueryCommonRemarkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := commonRemarkService.FindAllCommonRemark(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.CommonRemarkVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, commonRemarkTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询备注列表\n// @Description 查询备注列表\n// @Tags 备注\n// @Accept json\n// @Produce json\n// @Param body body req.QueryCommonRemarkReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.CommonRemarkVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/common-remark/list [post]\nfunc (a *CommonRemarkController) ListCommonRemarks(ctx *gin.Context) {\n\treqDto := req.QueryCommonRemarkReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := commonRemarkService.FindAllCommonRemarkWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.CommonRemarkVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.CommonRemarkVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, commonRemarkTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype CommonRemarkRoute struct {\n}\n\nfunc (s *CommonRemarkRoute) InitCommonRemarkRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tcommonRemarkController := controller.CommonRemarkController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/common-remark/add\", commonRemarkController.AddCommonRemark)       //add\n\t\troute.POST(\"/api/common-remark/update\", commonRemarkController.UpdateCommonRemark) //update\n\t\troute.POST(\"/api/common-remark/delete\", commonRemarkController.DeleteCommonRemark) //delete\n\t\troute.POST(\"/api/common-remark/query\", commonRemarkController.QueryCommonRemarks)  //query\n\t\troute.POST(\"/api/common-remark/list\", commonRemarkController.ListCommonRemarks)   //list\n\t}\n}\n"}]