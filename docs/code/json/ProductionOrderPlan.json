[{"po": "package po\n\n// ProductionOrderPlan 生产订单计划实体\ntype ProductionOrderPlan struct {\n\tId           *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`           // 唯一id\n\tName         *string `gorm:\"column:name;type:varchar(64);default:''\" json:\"name\"`                 // 名称\n\tPrintAreas   *string `gorm:\"column:print_areas;type:varchar(255);default:''\" json:\"printAreas\"`   // 打印区域\n\tProductTypes *string `gorm:\"column:product_types;type:varchar(255);default:''\" json:\"productTypes\"` // 产品类型\n\tCtime        *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间\n\tUtime        *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间\n\tState        *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态\n\tVersion      *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本\n}\n\n// TableName 设置表名\nfunc (ProductionOrderPlan) TableName() string {\n\treturn \"production_order_plan\"\n}\n\nfunc (p ProductionOrderPlan) GetId() string {\n\treturn *p.Id\n}", "vo": "package vo\n\n// ProductionOrderPlanVO 生产订单计划值对象\ntype ProductionOrderPlanVO struct {\n\tId           string `json:\"id\"`           // 唯一id\n\tName         string `json:\"name\"`         // 名称\n\tPrintAreas   string `json:\"printAreas\"`   // 打印区域\n\tProductTypes string `json:\"productTypes\"` // 产品类型\n\tCtime        int64  `json:\"ctime\"`        // 创建时间\n\tUtime        int64  `json:\"utime\"`        // 更新时间\n\tState        int    `json:\"state\"`        // 状态\n\tVersion      int    `json:\"version\"`      // 版本\n}", "req_add": "package req\n\n// AddProductionOrderPlanReqDto 创建生产订单计划请求DTO\ntype AddProductionOrderPlanReqDto struct {\n\tName         *string `json:\"name\"`         // 名称\n\tPrintAreas   *string `json:\"printAreas\"`   // 打印区域\n\tProductTypes *string `json:\"productTypes\"` // 产品类型\n}", "req_update": "package req\n\ntype UpdateProductionOrderPlanReqDto struct {\n\tId           *string `json:\"id\"`           // 唯一id\n\tName         *string `json:\"name\"`         // 名称\n\tPrintAreas   *string `json:\"printAreas\"`   // 打印区域\n\tProductTypes *string `json:\"productTypes\"` // 产品类型\n}", "req_delete": "package req\n\ntype DeleteProductionOrderPlanReqDto struct {\n\tId *string `json:\"id\"` // 唯一id\n}", "req_query": "package req\n\ntype QueryProductionOrderPlanReqDto struct {\n\tId           *string `json:\"id\"`           // 唯一id\n\tName         *string `json:\"name\"`         // 名称\n\tPrintAreas   *string `json:\"printAreas\"`   // 打印区域\n\tProductTypes *string `json:\"productTypes\"` // 产品类型\n\tPageNum      *int    `json:\"pageNum\"`      // 页码\n\tPageSize     *int    `json:\"pageSize\"`     // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductionOrderPlanTransfer struct {\n}\n\nfunc (transfer *ProductionOrderPlanTransfer) PoToVo(po po.ProductionOrderPlan) vo.ProductionOrderPlanVO {\n\tvo := vo.ProductionOrderPlanVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductionOrderPlanTransfer) VoToPo(vo vo.ProductionOrderPlanVO) po.ProductionOrderPlan {\n\tpo := po.ProductionOrderPlan{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductionOrderPlanService struct {\n}\n\nfunc (service *ProductionOrderPlanService) CreateProductionOrderPlan(logCtx *gin.Context, productionOrderPlan *po.ProductionOrderPlan) error {\n\treturn Save(productionOrderPlan)\n}\n\nfunc (service *ProductionOrderPlanService) UpdateProductionOrderPlan(logCtx *gin.Context, productionOrderPlan *po.ProductionOrderPlan) error {\n\treturn Update(productionOrderPlan)\n}\n\nfunc (service *ProductionOrderPlanService) DeleteProductionOrderPlan(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductionOrderPlan{Id: &id})\n}\n\nfunc (service *ProductionOrderPlanService) FindProductionOrderPlanById(logCtx *gin.Context, id string) (productionOrderPlan *po.ProductionOrderPlan, err error) {\n\tproductionOrderPlan = &po.ProductionOrderPlan{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productionOrderPlan).Error\n\treturn\n}\n\nfunc (service *ProductionOrderPlanService) FindAllProductionOrderPlan(logCtx *gin.Context, reqDto *req.QueryProductionOrderPlanReqDto) (list *[]po.ProductionOrderPlan, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductionOrderPlan{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.PrintAreas != nil && *reqDto.PrintAreas != \"\" {\n\t\tdb = db.Where(\"print_areas LIKE ?\", \"%\"+*reqDto.PrintAreas+\"%\")\n\t}\n\tif reqDto.ProductTypes != nil && *reqDto.ProductTypes != \"\" {\n\t\tdb = db.Where(\"product_types LIKE ?\", \"%\"+*reqDto.ProductTypes+\"%\")\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductionOrderPlan{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductionOrderPlanService) FindAllProductionOrderPlanWithPagination(logCtx *gin.Context, reqDto *req.QueryProductionOrderPlanReqDto) (list *[]po.ProductionOrderPlan, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductionOrderPlan{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.PrintAreas != nil && *reqDto.PrintAreas != \"\" {\n\t\tdb = db.Where(\"print_areas LIKE ?\", \"%\"+*reqDto.PrintAreas+\"%\")\n\t}\n\tif reqDto.ProductTypes != nil && *reqDto.ProductTypes != \"\" {\n\t\tdb = db.Where(\"product_types LIKE ?\", \"%\"+*reqDto.ProductTypes+\"%\")\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductionOrderPlan{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductionOrderPlanController struct{}\n\nvar (\n\tproductionOrderPlanService  = impl.ProductionOrderPlanService{}\n\tproductionOrderPlanTransfer = transfer.ProductionOrderPlanTransfer{}\n)\n\n// @Summary 添加生产订单计划\n// @Description 添加生产订单计划\n// @Tags 生产订单计划\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductionOrderPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductionOrderPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/production-order-plan/add [post]\nfunc (controller *ProductionOrderPlanController) AddProductionOrderPlan(ctx *gin.Context) {\n\treqDto := req.AddProductionOrderPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductionOrderPlan := po.ProductionOrderPlan{}\n\tif reqDto.Name != nil {\n\t\tproductionOrderPlan.Name = reqDto.Name\n\t}\n\tif reqDto.PrintAreas != nil {\n\t\tproductionOrderPlan.PrintAreas = reqDto.PrintAreas\n\t}\n\tif reqDto.ProductTypes != nil {\n\t\tproductionOrderPlan.ProductTypes = reqDto.ProductTypes\n\t}\n\n\terr = productionOrderPlanService.CreateProductionOrderPlan(ctx, &productionOrderPlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productionOrderPlanTransfer.PoToVo(productionOrderPlan))\n}\n\n// @Summary 更新生产订单计划\n// @Description 更新生产订单计划\n// @Tags 生产订单计划\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductionOrderPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductionOrderPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/production-order-plan/update [post]\nfunc (controller *ProductionOrderPlanController) UpdateProductionOrderPlan(ctx *gin.Context) {\n\treqDto := req.UpdateProductionOrderPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproductionOrderPlan, err := productionOrderPlanService.FindProductionOrderPlanById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tproductionOrderPlan.Name = reqDto.Name\n\t}\n\tif reqDto.PrintAreas != nil {\n\t\tproductionOrderPlan.PrintAreas = reqDto.PrintAreas\n\t}\n\tif reqDto.ProductTypes != nil {\n\t\tproductionOrderPlan.ProductTypes = reqDto.ProductTypes\n\t}\n\n\terr = productionOrderPlanService.UpdateProductionOrderPlan(ctx, productionOrderPlan)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productionOrderPlanTransfer.PoToVo(*productionOrderPlan))\n}\n\n// @Summary 删除生产订单计划\n// @Description 删除生产订单计划\n// @Tags 生产订单计划\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductionOrderPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/production-order-plan/delete [post]\nfunc (controller *ProductionOrderPlanController) DeleteProductionOrderPlan(ctx *gin.Context) {\n\treqDto := req.DeleteProductionOrderPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = productionOrderPlanService.DeleteProductionOrderPlan(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询生产订单计划\n// @Description 查询生产订单计划\n// @Tags 生产订单计划\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductionOrderPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductionOrderPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/production-order-plan/query [post]\nfunc (controller *ProductionOrderPlanController) QueryProductionOrderPlans(ctx *gin.Context) {\n\treqDto := req.QueryProductionOrderPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := productionOrderPlanService.FindAllProductionOrderPlan(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.ProductionOrderPlanVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productionOrderPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询生产订单计划列表\n// @Description 查询生产订单计划列表\n// @Tags 生产订单计划\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductionOrderPlanReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductionOrderPlanVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/production-order-plan/list [post]\nfunc (controller *ProductionOrderPlanController) ListProductionOrderPlans(ctx *gin.Context) {\n\treqDto := req.QueryProductionOrderPlanReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := productionOrderPlanService.FindAllProductionOrderPlanWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.ProductionOrderPlanVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductionOrderPlanVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productionOrderPlanTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductionOrderPlanRoute struct {\n}\n\nfunc (s *ProductionOrderPlanRoute) InitProductionOrderPlanRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductionOrderPlanController := controller.ProductionOrderPlanController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/production-order-plan/add\", productionOrderPlanController.AddProductionOrderPlan)       // add\n\t\troute.POST(\"/api/production-order-plan/update\", productionOrderPlanController.UpdateProductionOrderPlan)   // update\n\t\troute.POST(\"/api/production-order-plan/delete\", productionOrderPlanController.DeleteProductionOrderPlan)   // delete\n\t\troute.POST(\"/api/production-order-plan/query\", productionOrderPlanController.QueryProductionOrderPlans)    // query\n\t\troute.POST(\"/api/production-order-plan/list\", productionOrderPlanController.ListProductionOrderPlans)     // list\n\t}\n}"}]