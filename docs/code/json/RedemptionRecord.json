[{"po": "package po\n\n// RedemptionRecord 核销记录实体\ntype RedemptionRecord struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // 唯一ID\n\tRecordId       *string `gorm:\"column:record_id;type:varchar(64);default:''\" json:\"recordId\"`        // 记录ID\n\tVoucherId      *string `gorm:\"column:voucher_id;type:varchar(64);default:''\" json:\"voucherId\"`      // 凭证ID\n\tRedemptionTime *int64  `gorm:\"column:redemption_time;type:int;default:0\" json:\"redemptionTime\"`    // 核销时间\n\tEmployeeId     *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"`    // 员工ID\n\tCtime          *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间\n\tUtime          *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间\n\tState          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态\n\tVersion        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (RedemptionRecord) TableName() string {\n\treturn \"redemption_record\"\n}\n\nfunc (r RedemptionRecord) GetId() string {\n\treturn *r.Id\n}", "vo": "package vo\n\n// RedemptionRecordVO 核销记录值对象\ntype RedemptionRecordVO struct {\n\tId             string `json:\"id\"`             // 唯一ID\n\tRecordId       string `json:\"recordId\"`       // 记录ID\n\tVoucherId      string `json:\"voucherId\"`      // 凭证ID\n\tRedemptionTime int64  `json:\"redemptionTime\"` // 核销时间\n\tEmployeeId     string `json:\"employeeId\"`     // 员工ID\n\tCtime          int64  `json:\"ctime\"`          // 创建时间\n\tUtime          int64  `json:\"utime\"`          // 更新时间\n\tState          int    `json:\"state\"`          // 状态\n\tVersion        int    `json:\"version\"`        // 版本号\n}", "req_add": "package req\n\n// AddRedemptionRecordReqDto 创建核销记录请求DTO\ntype AddRedemptionRecordReqDto struct {\n\tRecordId       *string `json:\"recordId\"`       // 记录ID\n\tVoucherId      *string `json:\"voucherId\"`      // 凭证ID\n\tRedemptionTime *int64  `json:\"redemptionTime\"` // 核销时间\n\tEmployeeId     *string `json:\"employeeId\"`     // 员工ID\n}", "req_update": "package req\n\n// UpdateRedemptionRecordReqDto 更新核销记录请求DTO\ntype UpdateRedemptionRecordReqDto struct {\n\tId             *string `json:\"id\"`             // 唯一ID\n\tRecordId       *string `json:\"recordId\"`       // 记录ID\n\tVoucherId      *string `json:\"voucherId\"`      // 凭证ID\n\tRedemptionTime *int64  `json:\"redemptionTime\"` // 核销时间\n\tEmployeeId     *string `json:\"employeeId\"`     // 员工ID\n}", "req_delete": "package req\n\n// DeleteRedemptionRecordReqDto 删除核销记录请求DTO\ntype DeleteRedemptionRecordReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}", "req_query": "package req\n\n// QueryRedemptionRecordReqDto 查询核销记录请求DTO\ntype QueryRedemptionRecordReqDto struct {\n\tId             *string `json:\"id\"`             // 唯一ID\n\tRecordId       *string `json:\"recordId\"`       // 记录ID\n\tVoucherId      *string `json:\"voucherId\"`      // 凭证ID\n\tRedemptionTime *int64  `json:\"redemptionTime\"` // 核销时间\n\tEmployeeId     *string `json:\"employeeId\"`     // 员工ID\n\tPageNum        *int    `json:\"pageNum\"`        // 页码\n\tPageSize       *int    `json:\"pageSize\"`       // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RedemptionRecordTransfer struct {\n}\n\nfunc (transfer *RedemptionRecordTransfer) PoToVo(po po.RedemptionRecord) vo.RedemptionRecordVO {\n\tvo := vo.RedemptionRecordVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RedemptionRecordTransfer) VoToPo(vo vo.RedemptionRecordVO) po.RedemptionRecord {\n\tpo := po.RedemptionRecord{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RedemptionRecordService struct {\n}\n\nfunc (service *RedemptionRecordService) CreateRedemptionRecord(logCtx *gin.Context, record *po.RedemptionRecord) error {\n\treturn Save(record)\n}\n\nfunc (service *RedemptionRecordService) UpdateRedemptionRecord(logCtx *gin.Context, record *po.RedemptionRecord) error {\n\treturn Update(record)\n}\n\nfunc (service *RedemptionRecordService) DeleteRedemptionRecord(logCtx *gin.Context, id string) error {\n\treturn Delete(po.RedemptionRecord{Id: &id})\n}\n\nfunc (service *RedemptionRecordService) FindRedemptionRecordById(logCtx *gin.Context, id string) (record *po.RedemptionRecord, err error) {\n\trecord = &po.RedemptionRecord{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(record).Error\n\treturn\n}\n\nfunc (service *RedemptionRecordService) FindAllRedemptionRecord(logCtx *gin.Context, reqDto *req.QueryRedemptionRecordReqDto) (list *[]po.RedemptionRecord, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RedemptionRecord{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.RecordId != nil && *reqDto.RecordId != \"\" {\n\t\tdb = db.Where(\"record_id=?\", *reqDto.RecordId)\n\t}\n\tif reqDto.VoucherId != nil && *reqDto.VoucherId != \"\" {\n\t\tdb = db.Where(\"voucher_id=?\", *reqDto.VoucherId)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.RedemptionRecord{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RedemptionRecordService) FindAllRedemptionRecordWithPagination(logCtx *gin.Context, reqDto *req.QueryRedemptionRecordReqDto) (list *[]po.RedemptionRecord, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RedemptionRecord{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.RecordId != nil && *reqDto.RecordId != \"\" {\n\t\tdb = db.Where(\"record_id=?\", *reqDto.RecordId)\n\t}\n\tif reqDto.VoucherId != nil && *reqDto.VoucherId != \"\" {\n\t\tdb = db.Where(\"voucher_id=?\", *reqDto.VoucherId)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.RedemptionRecord{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RedemptionRecordController struct{}\n\nvar (\n\tredemptionRecordService  = impl.RedemptionRecordService{}\n\tredemptionRecordTransfer = transfer.RedemptionRecordTransfer{}\n)\n\n// @Summary 添加核销记录\n// @Description 添加核销记录\n// @Tags 核销记录\n// @Accept json\n// @Produce json\n// @Param body body req.AddRedemptionRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RedemptionRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/redemption-record/add [post]\nfunc (controller *RedemptionRecordController) AddRedemptionRecord(ctx *gin.Context) {\n\treqDto := req.AddRedemptionRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\trecord := po.RedemptionRecord{}\n\tif reqDto.RecordId != nil {\n\t\trecord.RecordId = reqDto.RecordId\n\t}\n\tif reqDto.VoucherId != nil {\n\t\trecord.VoucherId = reqDto.VoucherId\n\t}\n\tif reqDto.RedemptionTime != nil {\n\t\trecord.RedemptionTime = reqDto.RedemptionTime\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\trecord.EmployeeId = reqDto.EmployeeId\n\t}\n\n\terr = redemptionRecordService.CreateRedemptionRecord(ctx, &record)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, redemptionRecordTransfer.PoToVo(record))\n}\n\n// @Summary 更新核销记录\n// @Description 更新核销记录\n// @Tags 核销记录\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRedemptionRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RedemptionRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/redemption-record/update [post]\nfunc (controller *RedemptionRecordController) UpdateRedemptionRecord(ctx *gin.Context) {\n\treqDto := req.UpdateRedemptionRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\trecord, err := redemptionRecordService.FindRedemptionRecordById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.RecordId != nil {\n\t\trecord.RecordId = reqDto.RecordId\n\t}\n\tif reqDto.VoucherId != nil {\n\t\trecord.VoucherId = reqDto.VoucherId\n\t}\n\tif reqDto.RedemptionTime != nil {\n\t\trecord.RedemptionTime = reqDto.RedemptionTime\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\trecord.EmployeeId = reqDto.EmployeeId\n\t}\n\n\terr = redemptionRecordService.UpdateRedemptionRecord(ctx, record)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, redemptionRecordTransfer.PoToVo(*record))\n}\n\n// @Summary 删除核销记录\n// @Description 删除核销记录\n// @Tags 核销记录\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRedemptionRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/redemption-record/delete [post]\nfunc (controller *RedemptionRecordController) DeleteRedemptionRecord(ctx *gin.Context) {\n\treqDto := req.DeleteRedemptionRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = redemptionRecordService.DeleteRedemptionRecord(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询核销记录\n// @Description 查询核销记录\n// @Tags 核销记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRedemptionRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RedemptionRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/redemption-record/query [post]\nfunc (controller *RedemptionRecordController) QueryRedemptionRecords(ctx *gin.Context) {\n\treqDto := req.QueryRedemptionRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := redemptionRecordService.FindAllRedemptionRecord(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.RedemptionRecordVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, redemptionRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询核销记录列表\n// @Description 查询核销记录列表\n// @Tags 核销记录\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRedemptionRecordReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RedemptionRecordVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/redemption-record/list [post]\nfunc (controller *RedemptionRecordController) ListRedemptionRecords(ctx *gin.Context) {\n\treqDto := req.QueryRedemptionRecordReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := redemptionRecordService.FindAllRedemptionRecordWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.RedemptionRecordVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RedemptionRecordVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, redemptionRecordTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RedemptionRecordRoute struct {\n}\n\nfunc (s *RedemptionRecordRoute) InitRedemptionRecordRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tredemptionRecordController := controller.RedemptionRecordController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/redemption-record/add\", redemptionRecordController.AddRedemptionRecord)       // add\n\t\troute.POST(\"/api/redemption-record/update\", redemptionRecordController.UpdateRedemptionRecord)   // update\n\t\troute.POST(\"/api/redemption-record/delete\", redemptionRecordController.DeleteRedemptionRecord)   // delete\n\t\troute.POST(\"/api/redemption-record/query\", redemptionRecordController.QueryRedemptionRecords)   // query\n\t\troute.POST(\"/api/redemption-record/list\", redemptionRecordController.ListRedemptionRecords)     // list\n\t}\n}"}]