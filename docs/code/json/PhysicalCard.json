[{"po": "package po\n\n// PhysicalCard 实体卡实体\ntype PhysicalCard struct {\n\tId         *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // 唯一ID\n\tCardNumber *string `gorm:\"column:card_number;type:varchar(64);default:''\" json:\"cardNumber\"` // 卡号\n\tCtime      *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间戳\n\tUtime      *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间戳\n\tState      *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态值\n\tVersion    *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (PhysicalCard) TableName() string {\n\treturn \"physical_card\"\n}\n\nfunc (p PhysicalCard) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// PhysicalCardVO 实体卡值对象\ntype PhysicalCardVO struct {\n\tId         string `json:\"id\"`         // 唯一ID\n\tCardNumber string `json:\"cardNumber\"` // 卡号\n\tCtime      int64  `json:\"ctime\"`      // 创建时间戳\n\tUtime      int64  `json:\"utime\"`      // 更新时间戳\n\tState      int    `json:\"state\"`      // 状态值\n\tVersion    int    `json:\"version\"`    // 版本号\n}\n", "req_add": "package req\n\n// AddPhysicalCardReqDto 创建实体卡请求DTO\ntype AddPhysicalCardReqDto struct {\n\tCardNumber *string `json:\"cardNumber\"` // 卡号\n}\n", "req_update": "package req\n\ntype UpdatePhysicalCardReqDto struct {\n\tId         *string `json:\"id\"`         // 唯一ID\n\tCardNumber *string `json:\"cardNumber\"` // 卡号\n}\n", "req_delete": "package req\n\ntype DeletePhysicalCardReqDto struct {\n\tId *string `json:\"id\"` // 唯一ID\n}\n", "req_query": "package req\n\ntype QueryPhysicalCardReqDto struct {\n\tId         *string `json:\"id\"`         // 唯一ID\n\tCardNumber *string `json:\"cardNumber\"` // 卡号\n\tPageNum    *int    `json:\"pageNum\"`    // 页码\n\tPageSize   *int    `json:\"pageSize\"`   // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype PhysicalCardTransfer struct {\n}\n\nfunc (transfer *PhysicalCardTransfer) PoToVo(po po.PhysicalCard) vo.PhysicalCardVO {\n\tvo := vo.PhysicalCardVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *PhysicalCardTransfer) VoToPo(vo vo.PhysicalCardVO) po.PhysicalCard {\n\tpo := po.PhysicalCard{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PhysicalCardService struct {\n}\n\nfunc (service *PhysicalCardService) CreatePhysicalCard(logCtx *gin.Context, physicalCard *po.PhysicalCard) error {\n\treturn Save(physicalCard)\n}\n\nfunc (service *PhysicalCardService) UpdatePhysicalCard(logCtx *gin.Context, physicalCard *po.PhysicalCard) error {\n\treturn Update(physicalCard)\n}\n\nfunc (service *PhysicalCardService) DeletePhysicalCard(logCtx *gin.Context, id string) error {\n\treturn Delete(po.PhysicalCard{Id: &id})\n}\n\nfunc (service *PhysicalCardService) FindPhysicalCardById(logCtx *gin.Context, id string) (physicalCard *po.PhysicalCard, err error) {\n\tphysicalCard = &po.PhysicalCard{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(physicalCard).Error\n\treturn\n}\n\nfunc (service *PhysicalCardService) FindAllPhysicalCard(logCtx *gin.Context, reqDto *req.QueryPhysicalCardReqDto) (list *[]po.PhysicalCard, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PhysicalCard{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.PhysicalCard{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *PhysicalCardService) FindAllPhysicalCardWithPagination(logCtx *gin.Context, reqDto *req.QueryPhysicalCardReqDto) (list *[]po.PhysicalCard, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.PhysicalCard{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.CardNumber != nil && *reqDto.CardNumber != \"\" {\n\t\tdb = db.Where(\"card_number=?\", *reqDto.CardNumber)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.PhysicalCard{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PhysicalCardController struct{}\n\nvar (\n\tphysicalCardService  = impl.PhysicalCardService{}\n\tphysicalCardTransfer = transfer.PhysicalCardTransfer{}\n)\n\n// @Summary 添加实体卡\n// @Description 添加实体卡\n// @Tags 实体卡\n// @Accept json\n// @Produce json\n// @Param body body req.AddPhysicalCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PhysicalCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/physical-card/add [post]\nfunc (controller *PhysicalCardController) AddPhysicalCard(ctx *gin.Context) {\n\treqDto := req.AddPhysicalCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tphysicalCard := po.PhysicalCard{}\n\tif reqDto.CardNumber != nil {\n\t\tphysicalCard.CardNumber = reqDto.CardNumber\n\t}\n\n\terr = physicalCardService.CreatePhysicalCard(ctx, &physicalCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, physicalCardTransfer.PoToVo(physicalCard))\n}\n\n// @Summary 更新实体卡\n// @Description 更新实体卡\n// @Tags 实体卡\n// @Accept json\n// @Produce json\n// @Param body body req.UpdatePhysicalCardReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PhysicalCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/physical-card/update [post]\nfunc (controller *PhysicalCardController) UpdatePhysicalCard(ctx *gin.Context) {\n\treqDto := req.UpdatePhysicalCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tphysicalCard, err := physicalCardService.FindPhysicalCardById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.CardNumber != nil {\n\t\tphysicalCard.CardNumber = reqDto.CardNumber\n\t}\n\n\terr = physicalCardService.UpdatePhysicalCard(ctx, physicalCard)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, physicalCardTransfer.PoToVo(*physicalCard))\n}\n\n// @Summary 删除实体卡\n// @Description 删除实体卡\n// @Tags 实体卡\n// @Accept json\n// @Produce json\n// @Param body body req.DeletePhysicalCardReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/physical-card/delete [post]\nfunc (controller *PhysicalCardController) DeletePhysicalCard(ctx *gin.Context) {\n\treqDto := req.DeletePhysicalCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = physicalCardService.DeletePhysicalCard(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询实体卡\n// @Description 查询实体卡\n// @Tags 实体卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPhysicalCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PhysicalCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/physical-card/query [post]\nfunc (controller *PhysicalCardController) QueryPhysicalCards(ctx *gin.Context) {\n\treqDto := req.QueryPhysicalCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := physicalCardService.FindAllPhysicalCard(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.PhysicalCardVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, physicalCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询实体卡列表\n// @Description 查询实体卡列表\n// @Tags 实体卡\n// @Accept json\n// @Produce json\n// @Param body body req.QueryPhysicalCardReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.PhysicalCardVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/physical-card/list [post]\nfunc (a *PhysicalCardController) ListPhysicalCards(ctx *gin.Context) {\n\treqDto := req.QueryPhysicalCardReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := physicalCardService.FindAllPhysicalCardWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.PhysicalCardVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.PhysicalCardVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, physicalCardTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype PhysicalCardRoute struct {\n}\n\nfunc (s *PhysicalCardRoute) InitPhysicalCardRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tphysicalCardController := controller.PhysicalCardController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/physical-card/add\", physicalCardController.AddPhysicalCard)       //add\n\t\troute.POST(\"/api/physical-card/update\", physicalCardController.UpdatePhysicalCard) //update\n\t\troute.POST(\"/api/physical-card/delete\", physicalCardController.DeletePhysicalCard) //delete\n\t\troute.POST(\"/api/physical-card/query\", physicalCardController.QueryPhysicalCards)  //query\n\t\troute.POST(\"/api/physical-card/list\", physicalCardController.ListPhysicalCards)    //list\n\t}\n}\n"}]