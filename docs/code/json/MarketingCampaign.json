[{"po": "package po\n\n// MarketingCampaign 营销活动实体\ntype MarketingCampaign struct {\n\tId                  *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tName                *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                        // 活动名称\n\tStartDate           *int64  `gorm:\"column:start_date;type:int;default:0\" json:\"startDate\"`                      // 活动开始日期\n\tEndDate             *int64  `gorm:\"column:end_date;type:int;default:0\" json:\"endDate\"`                          // 活动结束日期\n\tType                *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`                         // 活动类型\n\tTargetAudience      *string `gorm:\"column:target_audience;type:varchar(255);default:''\" json:\"targetAudience\"`    // 目标受众\n\tDiscountDetails     *string `gorm:\"column:discount_details;type:varchar(255);default:''\" json:\"discountDetails\"`  // 折扣详情\n\tDistributionChannels *string `gorm:\"column:distribution_channels;type:varchar(255);default:''\" json:\"distributionChannels\"` // 分销渠道\n\tCtime               *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                               // 创建时间\n\tUtime               *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                               // 更新时间\n\tState               *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                               // 状态\n\tVersion             *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                           // 版本号\n}\n\n// TableName 设置表名\nfunc (MarketingCampaign) TableName() string {\n\treturn \"marketing_campaign\"\n}\n\nfunc (m MarketingCampaign) GetId() string {\n\treturn *m.Id\n}\n", "vo": "package vo\n\n// MarketingCampaignVO 营销活动值对象\ntype MarketingCampaignVO struct {\n\tId                  string `json:\"id\"`                  // ID\n\tName                string `json:\"name\"`                // 活动名称\n\tStartDate           int64  `json:\"startDate\"`           // 活动开始日期\n\tEndDate             int64  `json:\"endDate\"`             // 活动结束日期\n\tType                string `json:\"type\"`                // 活动类型\n\tTargetAudience      string `json:\"targetAudience\"`      // 目标受众\n\tDiscountDetails     string `json:\"discountDetails\"`     // 折扣详情\n\tDistributionChannels string `json:\"distributionChannels\"` // 分销渠道\n\tCtime               int64  `json:\"ctime\"`               // 创建时间\n\tUtime               int64  `json:\"utime\"`               // 更新时间\n\tState               int    `json:\"state\"`               // 状态\n\tVersion             int    `json:\"version\"`             // 版本号\n}\n", "req_add": "package req\n\n// AddMarketingCampaignReqDto 创建营销活动请求DTO\ntype AddMarketingCampaignReqDto struct {\n\tName                *string `json:\"name\"`                // 活动名称\n\tStartDate           *int64  `json:\"startDate\"`           // 活动开始日期\n\tEndDate             *int64  `json:\"endDate\"`             // 活动结束日期\n\tType                *string `json:\"type\"`                // 活动类型\n\tTargetAudience      *string `json:\"targetAudience\"`      // 目标受众\n\tDiscountDetails     *string `json:\"discountDetails\"`     // 折扣详情\n\tDistributionChannels *string `json:\"distributionChannels\"` // 分销渠道\n}\n", "req_update": "package req\n\n// UpdateMarketingCampaignReqDto 更新营销活动请求DTO\ntype UpdateMarketingCampaignReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tName                *string `json:\"name\"`                // 活动名称\n\tStartDate           *int64  `json:\"startDate\"`           // 活动开始日期\n\tEndDate             *int64  `json:\"endDate\"`             // 活动结束日期\n\tType                *string `json:\"type\"`                // 活动类型\n\tTargetAudience      *string `json:\"targetAudience\"`      // 目标受众\n\tDiscountDetails     *string `json:\"discountDetails\"`     // 折扣详情\n\tDistributionChannels *string `json:\"distributionChannels\"` // 分销渠道\n}\n", "req_delete": "package req\n\n// DeleteMarketingCampaignReqDto 删除营销活动请求DTO\ntype DeleteMarketingCampaignReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryMarketingCampaignReqDto 查询营销活动请求DTO\ntype QueryMarketingCampaignReqDto struct {\n\tId                  *string `json:\"id\"`                  // ID\n\tName                *string `json:\"name\"`                // 活动名称\n\tStartDate           *int64  `json:\"startDate\"`           // 活动开始日期\n\tEndDate             *int64  `json:\"endDate\"`             // 活动结束日期\n\tType                *string `json:\"type\"`                // 活动类型\n\tTargetAudience      *string `json:\"targetAudience\"`      // 目标受众\n\tDiscountDetails     *string `json:\"discountDetails\"`     // 折扣详情\n\tDistributionChannels *string `json:\"distributionChannels\"` // 分销渠道\n\tPageNum             *int    `json:\"pageNum\"`             // 页码\n\tPageSize            *int    `json:\"pageSize\"`            // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype MarketingCampaignTransfer struct {\n}\n\nfunc (transfer *MarketingCampaignTransfer) PoToVo(po po.MarketingCampaign) vo.MarketingCampaignVO {\n\tvo := vo.MarketingCampaignVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *MarketingCampaignTransfer) VoToPo(vo vo.MarketingCampaignVO) po.MarketingCampaign {\n\tpo := po.MarketingCampaign{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketingCampaignService struct {\n}\n\nfunc (service *MarketingCampaignService) CreateMarketingCampaign(logCtx *gin.Context, marketingCampaign *po.MarketingCampaign) error {\n\treturn Save(marketingCampaign)\n}\n\nfunc (service *MarketingCampaignService) UpdateMarketingCampaign(logCtx *gin.Context, marketingCampaign *po.MarketingCampaign) error {\n\treturn Update(marketingCampaign)\n}\n\nfunc (service *MarketingCampaignService) DeleteMarketingCampaign(logCtx *gin.Context, id string) error {\n\treturn Delete(po.MarketingCampaign{Id: &id})\n}\n\nfunc (service *MarketingCampaignService) FindMarketingCampaignById(logCtx *gin.Context, id string) (marketingCampaign *po.MarketingCampaign, err error) {\n\tmarketingCampaign = &po.MarketingCampaign{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(marketingCampaign).Error\n\treturn\n}\n\nfunc (service *MarketingCampaignService) FindAllMarketingCampaign(logCtx *gin.Context, reqDto *req.QueryMarketingCampaignReqDto) (list *[]po.MarketingCampaign, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MarketingCampaign{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.MarketingCampaign{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *MarketingCampaignService) FindAllMarketingCampaignWithPagination(logCtx *gin.Context, reqDto *req.QueryMarketingCampaignReqDto) (list *[]po.MarketingCampaign, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.MarketingCampaign{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.MarketingCampaign{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketingCampaignController struct{}\n\nvar (\n\tmarketingCampaignService  = impl.MarketingCampaignService{}\n\tmarketingCampaignTransfer = transfer.MarketingCampaignTransfer{}\n)\n\n// @Summary 添加营销活动\n// @Description 添加营销活动\n// @Tags 营销活动\n// @Accept json\n// @Produce json\n// @Param body body req.AddMarketingCampaignReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MarketingCampaignVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-campaign/add [post]\nfunc (controller *MarketingCampaignController) AddMarketingCampaign(ctx *gin.Context) {\n\treqDto := req.AddMarketingCampaignReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tmarketingCampaign := po.MarketingCampaign{}\n\tif reqDto.Name != nil {\n\t\tmarketingCampaign.Name = reqDto.Name\n\t}\n\tif reqDto.StartDate != nil {\n\t\tmarketingCampaign.StartDate = reqDto.StartDate\n\t}\n\tif reqDto.EndDate != nil {\n\t\tmarketingCampaign.EndDate = reqDto.EndDate\n\t}\n\tif reqDto.Type != nil {\n\t\tmarketingCampaign.Type = reqDto.Type\n\t}\n\tif reqDto.TargetAudience != nil {\n\t\tmarketingCampaign.TargetAudience = reqDto.TargetAudience\n\t}\n\tif reqDto.DiscountDetails != nil {\n\t\tmarketingCampaign.DiscountDetails = reqDto.DiscountDetails\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tmarketingCampaign.DistributionChannels = reqDto.DistributionChannels\n\t}\n\n\terr = marketingCampaignService.CreateMarketingCampaign(ctx, &marketingCampaign)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, marketingCampaignTransfer.PoToVo(marketingCampaign))\n}\n\n// @Summary 更新营销活动\n// @Description 更新营销活动\n// @Tags 营销活动\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateMarketingCampaignReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.MarketingCampaignVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-campaign/update [post]\nfunc (controller *MarketingCampaignController) UpdateMarketingCampaign(ctx *gin.Context) {\n\treqDto := req.UpdateMarketingCampaignReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tmarketingCampaign, err := marketingCampaignService.FindMarketingCampaignById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.Name != nil {\n\t\tmarketingCampaign.Name = reqDto.Name\n\t}\n\tif reqDto.StartDate != nil {\n\t\tmarketingCampaign.StartDate = reqDto.StartDate\n\t}\n\tif reqDto.EndDate != nil {\n\t\tmarketingCampaign.EndDate = reqDto.EndDate\n\t}\n\tif reqDto.Type != nil {\n\t\tmarketingCampaign.Type = reqDto.Type\n\t}\n\tif reqDto.TargetAudience != nil {\n\t\tmarketingCampaign.TargetAudience = reqDto.TargetAudience\n\t}\n\tif reqDto.DiscountDetails != nil {\n\t\tmarketingCampaign.DiscountDetails = reqDto.DiscountDetails\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tmarketingCampaign.DistributionChannels = reqDto.DistributionChannels\n\t}\n\n\terr = marketingCampaignService.UpdateMarketingCampaign(ctx, marketingCampaign)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, marketingCampaignTransfer.PoToVo(*marketingCampaign))\n}\n\n// @Summary 删除营销活动\n// @Description 删除营销活动\n// @Tags 营销活动\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteMarketingCampaignReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-campaign/delete [post]\nfunc (controller *MarketingCampaignController) DeleteMarketingCampaign(ctx *gin.Context) {\n\treqDto := req.DeleteMarketingCampaignReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = marketingCampaignService.DeleteMarketingCampaign(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询营销活动\n// @Description 查询营销活动\n// @Tags 营销活动\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMarketingCampaignReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MarketingCampaignVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-campaign/query [post]\nfunc (controller *MarketingCampaignController) QueryMarketingCampaigns(ctx *gin.Context) {\n\treqDto := req.QueryMarketingCampaignReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := marketingCampaignService.FindAllMarketingCampaign(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.MarketingCampaignVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, marketingCampaignTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询营销活动列表\n// @Description 查询营销活动列表\n// @Tags 营销活动\n// @Accept json\n// @Produce json\n// @Param body body req.QueryMarketingCampaignReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.MarketingCampaignVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/marketing-campaign/list [post]\nfunc (controller *MarketingCampaignController) ListMarketingCampaigns(ctx *gin.Context) {\n\treqDto := req.QueryMarketingCampaignReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := marketingCampaignService.FindAllMarketingCampaignWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.MarketingCampaignVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.MarketingCampaignVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, marketingCampaignTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype MarketingCampaignRoute struct {\n}\n\nfunc (s *MarketingCampaignRoute) InitMarketingCampaignRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tmarketingCampaignController := controller.MarketingCampaignController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/marketing-campaign/add\", marketingCampaignController.AddMarketingCampaign)       // add\n\t\troute.POST(\"/api/marketing-campaign/update\", marketingCampaignController.UpdateMarketingCampaign)   // update\n\t\troute.POST(\"/api/marketing-campaign/delete\", marketingCampaignController.DeleteMarketingCampaign)   // delete\n\t\troute.POST(\"/api/marketing-campaign/query\", marketingCampaignController.QueryMarketingCampaigns)    // query\n\t\troute.POST(\"/api/marketing-campaign/list\", marketingCampaignController.ListMarketingCampaigns)     // list\n\t}\n}\n"}]