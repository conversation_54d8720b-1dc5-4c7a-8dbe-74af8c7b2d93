[{"po": "package po\n\n// ProductType 产品类型实体\ntype ProductType struct {\n\tId                        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tVenueId                   *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`              // 所属门店ID\n\tName                      *string `gorm:\"column:name;type:varchar(255);default:''\" json:\"name\"`                    // 产品类型名称\n\tDistributionChannels      *string `gorm:\"column:distribution_channels;type:text\" json:\"distributionChannels\"`      // 分销渠道列表\n\tIsDisplayed              *bool   `gorm:\"column:is_displayed;type:tinyint(1);default:0\" json:\"isDisplayed\"`        // 是否显示\n\tDeliveryTimeout          *int    `gorm:\"column:delivery_timeout;type:int;default:0\" json:\"deliveryTimeout\"`        // 配送超时时间\n\tCustomStorageConfig      *string `gorm:\"column:custom_storage_config;type:text\" json:\"customStorageConfig\"`      // 自定义存储配置\n\tSupportsPoints           *bool   `gorm:\"column:supports_points;type:tinyint(1);default:0\" json:\"supportsPoints\"`    // 是否支持积分\n\tIsIncludedInDrinkAnalysis *bool   `gorm:\"column:is_included_in_drink_analysis;type:tinyint(1);default:0\" json:\"isIncludedInDrinkAnalysis\"` // 是否计入酒水分析\n\tIsKitchenMonitoring      *bool   `gorm:\"column:is_kitchen_monitoring;type:tinyint(1);default:0\" json:\"isKitchenMonitoring\"` // 是否启用后厨监控\n\tCtime                    *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                      // 创建时间\n\tUtime                    *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                      // 更新时间\n\tState                    *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态\n\tVersion                  *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (ProductType) TableName() string {\n\treturn \"product_type\"\n}\n\nfunc (p ProductType) GetId() string {\n\treturn *p.Id\n}\n", "vo": "package vo\n\n// ProductTypeVO 产品类型值对象\ntype ProductTypeVO struct {\n\tId                        string `json:\"id\"`                         // ID\n\tVenueId                   string `json:\"venueId\"`                    // 所属门店ID\n\tName                      string `json:\"name\"`                       // 产品类型名称\n\tDistributionChannels      string `json:\"distributionChannels\"`       // 分销渠道列表\n\tIsDisplayed              bool   `json:\"isDisplayed\"`                // 是否显示\n\tDeliveryTimeout          int    `json:\"deliveryTimeout\"`            // 配送超时时间\n\tCustomStorageConfig      string `json:\"customStorageConfig\"`        // 自定义存储配置\n\tSupportsPoints           bool   `json:\"supportsPoints\"`             // 是否支持积分\n\tIsIncludedInDrinkAnalysis bool   `json:\"isIncludedInDrinkAnalysis\"` // 是否计入酒水分析\n\tIsKitchenMonitoring      bool   `json:\"isKitchenMonitoring\"`       // 是否启用后厨监控\n\tCtime                    int64  `json:\"ctime\"`                      // 创建时间\n\tUtime                    int64  `json:\"utime\"`                      // 更新时间\n\tState                    int    `json:\"state\"`                      // 状态\n\tVersion                  int    `json:\"version\"`                    // 版本号\n}\n", "req_add": "package req\n\n// AddProductTypeReqDto 创建产品类型请求DTO\ntype AddProductTypeReqDto struct {\n\tVenueId                   *string `json:\"venueId\"`                    // 所属门店ID\n\tName                      *string `json:\"name\"`                       // 产品类型名称\n\tDistributionChannels      *string `json:\"distributionChannels\"`       // 分销渠道列表\n\tIsDisplayed              *bool   `json:\"isDisplayed\"`                // 是否显示\n\tDeliveryTimeout          *int    `json:\"deliveryTimeout\"`            // 配送超时时间\n\tCustomStorageConfig      *string `json:\"customStorageConfig\"`        // 自定义存储配置\n\tSupportsPoints           *bool   `json:\"supportsPoints\"`             // 是否支持积分\n\tIsIncludedInDrinkAnalysis *bool   `json:\"isIncludedInDrinkAnalysis\"` // 是否计入酒水分析\n\tIsKitchenMonitoring      *bool   `json:\"isKitchenMonitoring\"`       // 是否启用后厨监控\n}\n", "req_update": "package req\n\n// UpdateProductTypeReqDto 更新产品类型请求DTO\ntype UpdateProductTypeReqDto struct {\n\tId                        *string `json:\"id\"`                         // ID\n\tVenueId                   *string `json:\"venueId\"`                    // 所属门店ID\n\tName                      *string `json:\"name\"`                       // 产品类型名称\n\tDistributionChannels      *string `json:\"distributionChannels\"`       // 分销渠道列表\n\tIsDisplayed              *bool   `json:\"isDisplayed\"`                // 是否显示\n\tDeliveryTimeout          *int    `json:\"deliveryTimeout\"`            // 配送超时时间\n\tCustomStorageConfig      *string `json:\"customStorageConfig\"`        // 自定义存储配置\n\tSupportsPoints           *bool   `json:\"supportsPoints\"`             // 是否支持积分\n\tIsIncludedInDrinkAnalysis *bool   `json:\"isIncludedInDrinkAnalysis\"` // 是否计入酒水分析\n\tIsKitchenMonitoring      *bool   `json:\"isKitchenMonitoring\"`       // 是否启用后厨监控\n}\n", "req_delete": "package req\n\n// DeleteProductTypeReqDto 删除产品类型请求DTO\ntype DeleteProductTypeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryProductTypeReqDto 查询产品类型请求DTO\ntype QueryProductTypeReqDto struct {\n\tId                        *string `json:\"id\"`                         // ID\n\tVenueId                   *string `json:\"venueId\"`                    // 所属门店ID\n\tName                      *string `json:\"name\"`                       // 产品类型名称\n\tDistributionChannels      *string `json:\"distributionChannels\"`       // 分销渠道列表\n\tIsDisplayed              *bool   `json:\"isDisplayed\"`                // 是否显示\n\tDeliveryTimeout          *int    `json:\"deliveryTimeout\"`            // 配送超时时间\n\tCustomStorageConfig      *string `json:\"customStorageConfig\"`        // 自定义存储配置\n\tSupportsPoints           *bool   `json:\"supportsPoints\"`             // 是否支持积分\n\tIsIncludedInDrinkAnalysis *bool   `json:\"isIncludedInDrinkAnalysis\"` // 是否计入酒水分析\n\tIsKitchenMonitoring      *bool   `json:\"isKitchenMonitoring\"`       // 是否启用后厨监控\n\tPageNum                  *int    `json:\"pageNum\"`                    // 页码\n\tPageSize                 *int    `json:\"pageSize\"`                   // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ProductTypeTransfer struct {\n}\n\nfunc (transfer *ProductTypeTransfer) PoToVo(po po.ProductType) vo.ProductTypeVO {\n\tvo := vo.ProductTypeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ProductTypeTransfer) VoToPo(vo vo.ProductTypeVO) po.ProductType {\n\tpo := po.ProductType{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductTypeService struct {\n}\n\nfunc (service *ProductTypeService) CreateProductType(logCtx *gin.Context, productType *po.ProductType) error {\n\treturn Save(productType)\n}\n\nfunc (service *ProductTypeService) UpdateProductType(logCtx *gin.Context, productType *po.ProductType) error {\n\treturn Update(productType)\n}\n\nfunc (service *ProductTypeService) DeleteProductType(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ProductType{Id: &id})\n}\n\nfunc (service *ProductTypeService) FindProductTypeById(logCtx *gin.Context, id string) (productType *po.ProductType, err error) {\n\tproductType = &po.ProductType{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(productType).Error\n\treturn\n}\n\nfunc (service *ProductTypeService) FindAllProductType(logCtx *gin.Context, reqDto *req.QueryProductTypeReqDto) (list *[]po.ProductType, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductType{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tdb = db.Where(\"supports_points=?\", *reqDto.SupportsPoints)\n\t}\n\tif reqDto.IsIncludedInDrinkAnalysis != nil {\n\t\tdb = db.Where(\"is_included_in_drink_analysis=?\", *reqDto.IsIncludedInDrinkAnalysis)\n\t}\n\tif reqDto.IsKitchenMonitoring != nil {\n\t\tdb = db.Where(\"is_kitchen_monitoring=?\", *reqDto.IsKitchenMonitoring)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ProductType{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ProductTypeService) FindAllProductTypeWithPagination(logCtx *gin.Context, reqDto *req.QueryProductTypeReqDto) (list *[]po.ProductType, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ProductType{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Name != nil && *reqDto.Name != \"\" {\n\t\tdb = db.Where(\"name LIKE ?\", \"%\"+*reqDto.Name+\"%\")\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tdb = db.Where(\"is_displayed=?\", *reqDto.IsDisplayed)\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tdb = db.Where(\"supports_points=?\", *reqDto.SupportsPoints)\n\t}\n\tif reqDto.IsIncludedInDrinkAnalysis != nil {\n\t\tdb = db.Where(\"is_included_in_drink_analysis=?\", *reqDto.IsIncludedInDrinkAnalysis)\n\t}\n\tif reqDto.IsKitchenMonitoring != nil {\n\t\tdb = db.Where(\"is_kitchen_monitoring=?\", *reqDto.IsKitchenMonitoring)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ProductType{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductTypeController struct{}\n\nvar (\n\tproductTypeService  = impl.ProductTypeService{}\n\tproductTypeTransfer = transfer.ProductTypeTransfer{}\n)\n\n// @Summary 添加产品类型\n// @Description 添加产品类型\n// @Tags 产品类型\n// @Accept json\n// @Produce json\n// @Param body body req.AddProductTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-type/add [post]\nfunc (controller *ProductTypeController) AddProductType(ctx *gin.Context) {\n\treqDto := req.AddProductTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tproductType := po.ProductType{}\n\tif reqDto.VenueId != nil {\n\t\tproductType.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproductType.Name = reqDto.Name\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tproductType.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tproductType.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.DeliveryTimeout != nil {\n\t\tproductType.DeliveryTimeout = reqDto.DeliveryTimeout\n\t}\n\tif reqDto.CustomStorageConfig != nil {\n\t\tproductType.CustomStorageConfig = reqDto.CustomStorageConfig\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tproductType.SupportsPoints = reqDto.SupportsPoints\n\t}\n\tif reqDto.IsIncludedInDrinkAnalysis != nil {\n\t\tproductType.IsIncludedInDrinkAnalysis = reqDto.IsIncludedInDrinkAnalysis\n\t}\n\tif reqDto.IsKitchenMonitoring != nil {\n\t\tproductType.IsKitchenMonitoring = reqDto.IsKitchenMonitoring\n\t}\n\n\terr = productTypeService.CreateProductType(ctx, &productType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productTypeTransfer.PoToVo(productType))\n}\n\n// @Summary 更新产品类型\n// @Description 更新产品类型\n// @Tags 产品类型\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateProductTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ProductTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-type/update [post]\nfunc (controller *ProductTypeController) UpdateProductType(ctx *gin.Context) {\n\treqDto := req.UpdateProductTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tproductType, err := productTypeService.FindProductTypeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tproductType.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Name != nil {\n\t\tproductType.Name = reqDto.Name\n\t}\n\tif reqDto.DistributionChannels != nil {\n\t\tproductType.DistributionChannels = reqDto.DistributionChannels\n\t}\n\tif reqDto.IsDisplayed != nil {\n\t\tproductType.IsDisplayed = reqDto.IsDisplayed\n\t}\n\tif reqDto.DeliveryTimeout != nil {\n\t\tproductType.DeliveryTimeout = reqDto.DeliveryTimeout\n\t}\n\tif reqDto.CustomStorageConfig != nil {\n\t\tproductType.CustomStorageConfig = reqDto.CustomStorageConfig\n\t}\n\tif reqDto.SupportsPoints != nil {\n\t\tproductType.SupportsPoints = reqDto.SupportsPoints\n\t}\n\tif reqDto.IsIncludedInDrinkAnalysis != nil {\n\t\tproductType.IsIncludedInDrinkAnalysis = reqDto.IsIncludedInDrinkAnalysis\n\t}\n\tif reqDto.IsKitchenMonitoring != nil {\n\t\tproductType.IsKitchenMonitoring = reqDto.IsKitchenMonitoring\n\t}\n\n\terr = productTypeService.UpdateProductType(ctx, productType)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, productTypeTransfer.PoToVo(*productType))\n}\n\n// @Summary 删除产品类型\n// @Description 删除产品类型\n// @Tags 产品类型\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteProductTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-type/delete [post]\nfunc (controller *ProductTypeController) DeleteProductType(ctx *gin.Context) {\n\treqDto := req.DeleteProductTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = productTypeService.DeleteProductType(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询产品类型\n// @Description 查询产品类型\n// @Tags 产品类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-type/query [post]\nfunc (controller *ProductTypeController) QueryProductTypes(ctx *gin.Context) {\n\treqDto := req.QueryProductTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := productTypeService.FindAllProductType(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.ProductTypeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, productTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询产品类型列表\n// @Description 查询产品类型列表\n// @Tags 产品类型\n// @Accept json\n// @Produce json\n// @Param body body req.QueryProductTypeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ProductTypeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/product-type/list [post]\nfunc (controller *ProductTypeController) ListProductTypes(ctx *gin.Context) {\n\treqDto := req.QueryProductTypeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := productTypeService.FindAllProductTypeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.ProductTypeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ProductTypeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, productTypeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ProductTypeRoute struct {\n}\n\nfunc (s *ProductTypeRoute) InitProductTypeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tproductTypeController := controller.ProductTypeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/product-type/add\", productTypeController.AddProductType)       // add\n\t\troute.POST(\"/api/product-type/update\", productTypeController.UpdateProductType) // update\n\t\troute.POST(\"/api/product-type/delete\", productTypeController.DeleteProductType) // delete\n\t\troute.POST(\"/api/product-type/query\", productTypeController.QueryProductTypes)  // query\n\t\troute.POST(\"/api/product-type/list\", productTypeController.ListProductTypes)   // list\n\t}\n}\n"}]