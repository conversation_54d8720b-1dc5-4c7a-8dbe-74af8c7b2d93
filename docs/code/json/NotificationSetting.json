[{"po": "package po\n\n// NotificationSetting 通知设置实体类\ntype NotificationSetting struct {\n\tId               *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                // ID\n\tVenueId          *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`          // 所属门店ID\n\tType             *string `gorm:\"column:type;type:varchar(64);default:''\" json:\"type\"`                  // 通知类型\n\tRecipient        *string `gorm:\"column:recipient;type:varchar(64);default:''\" json:\"recipient\"`        // 接收人\n\tTriggerCondition *string `gorm:\"column:trigger_condition;type:varchar(64);default:''\" json:\"triggerCondition\"` // 触发条件\n\tCtime            *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                        // 创建时间\n\tUtime            *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                        // 更新时间\n\tState            *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                        // 状态\n\tVersion          *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                    // 版本号\n}\n\n// TableName 设置表名\nfunc (NotificationSetting) TableName() string {\n\treturn \"notification_setting\"\n}\n\nfunc (n NotificationSetting) GetId() string {\n\treturn *n.Id\n}\n", "vo": "package vo\n\n// NotificationSettingVO 通知设置值对象\ntype NotificationSettingVO struct {\n\tId               string `json:\"id\"`               // ID\n\tVenueId          string `json:\"venueId\"`          // 所属门店ID\n\tType             string `json:\"type\"`             // 通知类型\n\tRecipient        string `json:\"recipient\"`        // 接收人\n\tTriggerCondition string `json:\"triggerCondition\"` // 触发条件\n\tCtime            int64  `json:\"ctime\"`            // 创建时间\n\tUtime            int64  `json:\"utime\"`            // 更新时间\n\tState            int    `json:\"state\"`            // 状态\n\tVersion          int    `json:\"version\"`          // 版本号\n}\n", "req_add": "package req\n\n// AddNotificationSettingReqDto 创建通知设置请求DTO\ntype AddNotificationSettingReqDto struct {\n\tVenueId          *string `json:\"venueId\"`          // 所属门店ID\n\tType             *string `json:\"type\"`             // 通知类型\n\tRecipient        *string `json:\"recipient\"`        // 接收人\n\tTriggerCondition *string `json:\"triggerCondition\"` // 触发条件\n}\n", "req_update": "package req\n\ntype UpdateNotificationSettingReqDto struct {\n\tId               *string `json:\"id\"`               // ID\n\tVenueId          *string `json:\"venueId\"`          // 所属门店ID\n\tType             *string `json:\"type\"`             // 通知类型\n\tRecipient        *string `json:\"recipient\"`        // 接收人\n\tTriggerCondition *string `json:\"triggerCondition\"` // 触发条件\n}\n", "req_delete": "package req\n\ntype DeleteNotificationSettingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryNotificationSettingReqDto struct {\n\tId               *string `json:\"id\"`               // ID\n\tVenueId          *string `json:\"venueId\"`          // 所属门店ID\n\tType             *string `json:\"type\"`             // 通知类型\n\tRecipient        *string `json:\"recipient\"`        // 接收人\n\tTriggerCondition *string `json:\"triggerCondition\"` // 触发条件\n\tPageNum          *int    `json:\"pageNum\"`          // 页码\n\tPageSize         *int    `json:\"pageSize\"`         // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype NotificationSettingTransfer struct {\n}\n\nfunc (transfer *NotificationSettingTransfer) PoToVo(po po.NotificationSetting) vo.NotificationSettingVO {\n\tvo := vo.NotificationSettingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *NotificationSettingTransfer) VoToPo(vo vo.NotificationSettingVO) po.NotificationSetting {\n\tpo := po.NotificationSetting{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype NotificationSettingService struct {\n}\n\nfunc (service *NotificationSettingService) CreateNotificationSetting(logCtx *gin.Context, notificationSetting *po.NotificationSetting) error {\n\treturn Save(notificationSetting)\n}\n\nfunc (service *NotificationSettingService) UpdateNotificationSetting(logCtx *gin.Context, notificationSetting *po.NotificationSetting) error {\n\treturn Update(notificationSetting)\n}\n\nfunc (service *NotificationSettingService) DeleteNotificationSetting(logCtx *gin.Context, id string) error {\n\treturn Delete(po.NotificationSetting{Id: &id})\n}\n\nfunc (service *NotificationSettingService) FindNotificationSettingById(logCtx *gin.Context, id string) (notificationSetting *po.NotificationSetting, err error) {\n\tnotificationSetting = &po.NotificationSetting{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(notificationSetting).Error\n\treturn\n}\n\nfunc (service *NotificationSettingService) FindAllNotificationSetting(logCtx *gin.Context, reqDto *req.QueryNotificationSettingReqDto) (list *[]po.NotificationSetting, err error) {\n\tdb := model.DBSlave.Self.Model(&po.NotificationSetting{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.Recipient != nil && *reqDto.Recipient != \"\" {\n\t\tdb = db.Where(\"recipient=?\", *reqDto.Recipient)\n\t}\n\tif reqDto.TriggerCondition != nil && *reqDto.TriggerCondition != \"\" {\n\t\tdb = db.Where(\"trigger_condition=?\", *reqDto.TriggerCondition)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.NotificationSetting{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *NotificationSettingService) FindAllNotificationSettingWithPagination(logCtx *gin.Context, reqDto *req.QueryNotificationSettingReqDto) (list *[]po.NotificationSetting, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.NotificationSetting{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Type != nil && *reqDto.Type != \"\" {\n\t\tdb = db.Where(\"type=?\", *reqDto.Type)\n\t}\n\tif reqDto.Recipient != nil && *reqDto.Recipient != \"\" {\n\t\tdb = db.Where(\"recipient=?\", *reqDto.Recipient)\n\t}\n\tif reqDto.TriggerCondition != nil && *reqDto.TriggerCondition != \"\" {\n\t\tdb = db.Where(\"trigger_condition=?\", *reqDto.TriggerCondition)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.NotificationSetting{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype NotificationSettingController struct{}\n\nvar (\n\tnotificationSettingService  = impl.NotificationSettingService{}\n\tnotificationSettingTransfer = transfer.NotificationSettingTransfer{}\n)\n\n// @Summary 添加通知设置\n// @Description 添加通知设置\n// @Tags 通知设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddNotificationSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.NotificationSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/notification-setting/add [post]\nfunc (controller *NotificationSettingController) AddNotificationSetting(ctx *gin.Context) {\n\treqDto := req.AddNotificationSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnotificationSetting := po.NotificationSetting{}\n\tif reqDto.VenueId != nil {\n\t\tnotificationSetting.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Type != nil {\n\t\tnotificationSetting.Type = reqDto.Type\n\t}\n\tif reqDto.Recipient != nil {\n\t\tnotificationSetting.Recipient = reqDto.Recipient\n\t}\n\tif reqDto.TriggerCondition != nil {\n\t\tnotificationSetting.TriggerCondition = reqDto.TriggerCondition\n\t}\n\n\terr = notificationSettingService.CreateNotificationSetting(ctx, &notificationSetting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, notificationSettingTransfer.PoToVo(notificationSetting))\n}\n\n// @Summary 更新通知设置\n// @Description 更新通知设置\n// @Tags 通知设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateNotificationSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.NotificationSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/notification-setting/update [post]\nfunc (controller *NotificationSettingController) UpdateNotificationSetting(ctx *gin.Context) {\n\treqDto := req.UpdateNotificationSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tnotificationSetting, err := notificationSettingService.FindNotificationSettingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tnotificationSetting.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Type != nil {\n\t\tnotificationSetting.Type = reqDto.Type\n\t}\n\tif reqDto.Recipient != nil {\n\t\tnotificationSetting.Recipient = reqDto.Recipient\n\t}\n\tif reqDto.TriggerCondition != nil {\n\t\tnotificationSetting.TriggerCondition = reqDto.TriggerCondition\n\t}\n\n\terr = notificationSettingService.UpdateNotificationSetting(ctx, notificationSetting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, notificationSettingTransfer.PoToVo(*notificationSetting))\n}\n\n// @Summary 删除通知设置\n// @Description 删除通知设置\n// @Tags 通知设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteNotificationSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/notification-setting/delete [post]\nfunc (controller *NotificationSettingController) DeleteNotificationSetting(ctx *gin.Context) {\n\treqDto := req.DeleteNotificationSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = notificationSettingService.DeleteNotificationSetting(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询通知设置\n// @Description 查询通知设置\n// @Tags 通知设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryNotificationSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.NotificationSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/notification-setting/query [post]\nfunc (controller *NotificationSettingController) QueryNotificationSettings(ctx *gin.Context) {\n\treqDto := req.QueryNotificationSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := notificationSettingService.FindAllNotificationSetting(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.NotificationSettingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, notificationSettingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询通知设置列表\n// @Description 查询通知设置列表\n// @Tags 通知设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryNotificationSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.NotificationSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/notification-setting/list [post]\nfunc (controller *NotificationSettingController) ListNotificationSettings(ctx *gin.Context) {\n\treqDto := req.QueryNotificationSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := notificationSettingService.FindAllNotificationSettingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.NotificationSettingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.NotificationSettingVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, notificationSettingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype NotificationSettingRoute struct {\n}\n\nfunc (s *NotificationSettingRoute) InitNotificationSettingRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tnotificationSettingController := controller.NotificationSettingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/notification-setting/add\", notificationSettingController.AddNotificationSetting)       // add\n\t\troute.POST(\"/api/notification-setting/update\", notificationSettingController.UpdateNotificationSetting)   // update\n\t\troute.POST(\"/api/notification-setting/delete\", notificationSettingController.DeleteNotificationSetting)   // delete\n\t\troute.POST(\"/api/notification-setting/query\", notificationSettingController.QueryNotificationSettings)    // query\n\t\troute.POST(\"/api/notification-setting/list\", notificationSettingController.ListNotificationSettings)     // list\n\t}\n}\n"}]