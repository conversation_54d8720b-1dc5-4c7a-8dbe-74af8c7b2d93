[{"po": "package po\n\n// WineStorageSetting 存储设置实体\ntype WineStorageSetting struct {\n\tId                        *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`                    // ID\n\tVenueId                   *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`                 // 门店ID\n\tStorageDays              *int    `gorm:\"column:storage_days;type:int;default:0\" json:\"storageDays\"`                // 存储天数\n\tRenewalTimes             *int    `gorm:\"column:renewal_times;type:int;default:0\" json:\"renewalTimes\"`              // 续期次数\n\tRenewalDays              *int    `gorm:\"column:renewal_days;type:int;default:0\" json:\"renewalDays\"`                // 续期天数\n\tCustomerNotificationDays  *int    `gorm:\"column:customer_notification_days;type:int;default:0\" json:\"customerNotificationDays\"` // 客户通知天数\n\tMerchantNotificationDays *int    `gorm:\"column:merchant_notification_days;type:int;default:0\" json:\"merchantNotificationDays\"` // 商户通知天数\n\tMerchantExpirationReminder *string `gorm:\"column:merchant_expiration_reminder;type:varchar(255);default:''\" json:\"merchantExpirationReminder\"` // 商户到期提醒\n\tAgentExpirationReminder   *string `gorm:\"column:agent_expiration_reminder;type:varchar(255);default:''\" json:\"agentExpirationReminder\"` // 代理商到期提醒\n\tAutoConfiscate           *bool   `gorm:\"column:auto_confiscate;type:tinyint(1);default:0\" json:\"autoConfiscate\"`    // 是否自动没收\n\tOverdueWithdrawalLimit   *int    `gorm:\"column:overdue_withdrawal_limit;type:int;default:0\" json:\"overdueWithdrawalLimit\"` // 逾期取酒限制\n\tAutoConfiscateDays       *int    `gorm:\"column:auto_confiscate_days;type:int;default:0\" json:\"autoConfiscateDays\"` // 过期多少天后自动充公\n\tConfiscateWarehouse      *string `gorm:\"column:confiscate_warehouse;type:varchar(64);default:''\" json:\"confiscateWarehouse\"` // 充公仓库\n\tCtime                    *int64  `gorm:\"column:ctime;type:bigint;default:0\" json:\"ctime\"`                         // 创建时间\n\tUtime                    *int64  `gorm:\"column:utime;type:bigint;default:0\" json:\"utime\"`                         // 更新时间\n\tState                    *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                           // 状态\n\tVersion                  *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                       // 版本\n}\n\n// TableName 设置表名\nfunc (WineStorageSetting) TableName() string {\n\treturn \"wine_storage_setting\"\n}\n\nfunc (w WineStorageSetting) GetId() string {\n\treturn *w.Id\n}", "vo": "package vo\n\n// WineStorageSettingVO 存储设置值对象\ntype WineStorageSettingVO struct {\n\tId                        string `json:\"id\"`                         // ID\n\tVenueId                   string `json:\"venueId\"`                    // 门店ID\n\tStorageDays              int    `json:\"storageDays\"`                 // 存储天数\n\tRenewalTimes             int    `json:\"renewalTimes\"`                // 续期次数\n\tRenewalDays              int    `json:\"renewalDays\"`                 // 续期天数\n\tCustomerNotificationDays  int    `json:\"customerNotificationDays\"`    // 客户通知天数\n\tMerchantNotificationDays int    `json:\"merchantNotificationDays\"`    // 商户通知天数\n\tMerchantExpirationReminder string `json:\"merchantExpirationReminder\"` // 商户到期提醒\n\tAgentExpirationReminder   string `json:\"agentExpirationReminder\"`    // 代理商到期提醒\n\tAutoConfiscate           bool   `json:\"autoConfiscate\"`              // 是否自动没收\n\tOverdueWithdrawalLimit   int    `json:\"overdueWithdrawalLimit\"`      // 逾期取酒限制\n\tAutoConfiscateDays       int    `json:\"autoConfiscateDays\"`         // 过期多少天后自动充公\n\tConfiscateWarehouse      string `json:\"confiscateWarehouse\"`         // 充公仓库\n\tCtime                    int64  `json:\"ctime\"`                       // 创建时间\n\tUtime                    int64  `json:\"utime\"`                       // 更新时间\n\tState                    int    `json:\"state\"`                       // 状态\n\tVersion                  int    `json:\"version\"`                     // 版本\n}", "req_add": "package req\n\n// AddWineStorageSettingReqDto 创建存储设置请求DTO\ntype AddWineStorageSettingReqDto struct {\n\tVenueId                   *string `json:\"venueId\"`                    // 门店ID\n\tStorageDays              *int    `json:\"storageDays\"`                 // 存储天数\n\tRenewalTimes             *int    `json:\"renewalTimes\"`                // 续期次数\n\tRenewalDays              *int    `json:\"renewalDays\"`                 // 续期天数\n\tCustomerNotificationDays  *int    `json:\"customerNotificationDays\"`    // 客户通知天数\n\tMerchantNotificationDays *int    `json:\"merchantNotificationDays\"`    // 商户通知天数\n\tMerchantExpirationReminder *string `json:\"merchantExpirationReminder\"` // 商户到期提醒\n\tAgentExpirationReminder   *string `json:\"agentExpirationReminder\"`    // 代理商到期提醒\n\tAutoConfiscate           *bool   `json:\"autoConfiscate\"`              // 是否自动没收\n\tOverdueWithdrawalLimit   *int    `json:\"overdueWithdrawalLimit\"`      // 逾期取酒限制\n\tAutoConfiscateDays       *int    `json:\"autoConfiscateDays\"`         // 过期多少天后自动充公\n\tConfiscateWarehouse      *string `json:\"confiscateWarehouse\"`         // 充公仓库\n}", "req_update": "package req\n\n// UpdateWineStorageSettingReqDto 更新存储设置请求DTO\ntype UpdateWineStorageSettingReqDto struct {\n\tId                        *string `json:\"id\"`                         // ID\n\tVenueId                   *string `json:\"venueId\"`                    // 门店ID\n\tStorageDays              *int    `json:\"storageDays\"`                 // 存储天数\n\tRenewalTimes             *int    `json:\"renewalTimes\"`                // 续期次数\n\tRenewalDays              *int    `json:\"renewalDays\"`                 // 续期天数\n\tCustomerNotificationDays  *int    `json:\"customerNotificationDays\"`    // 客户通知天数\n\tMerchantNotificationDays *int    `json:\"merchantNotificationDays\"`    // 商户通知天数\n\tMerchantExpirationReminder *string `json:\"merchantExpirationReminder\"` // 商户到期提醒\n\tAgentExpirationReminder   *string `json:\"agentExpirationReminder\"`    // 代理商到期提醒\n\tAutoConfiscate           *bool   `json:\"autoConfiscate\"`              // 是否自动没收\n\tOverdueWithdrawalLimit   *int    `json:\"overdueWithdrawalLimit\"`      // 逾期取酒限制\n\tAutoConfiscateDays       *int    `json:\"autoConfiscateDays\"`         // 过期多少天后自动充公\n\tConfiscateWarehouse      *string `json:\"confiscateWarehouse\"`         // 充公仓库\n}", "req_delete": "package req\n\n// DeleteWineStorageSettingReqDto 删除存储设置请求DTO\ntype DeleteWineStorageSettingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryWineStorageSettingReqDto 查询存储设置请求DTO\ntype QueryWineStorageSettingReqDto struct {\n\tId                        *string `json:\"id\"`                         // ID\n\tVenueId                   *string `json:\"venueId\"`                    // 门店ID\n\tStorageDays              *int    `json:\"storageDays\"`                 // 存储天数\n\tRenewalTimes             *int    `json:\"renewalTimes\"`                // 续期次数\n\tRenewalDays              *int    `json:\"renewalDays\"`                 // 续期天数\n\tCustomerNotificationDays  *int    `json:\"customerNotificationDays\"`    // 客户通知天数\n\tMerchantNotificationDays *int    `json:\"merchantNotificationDays\"`    // 商户通知天数\n\tMerchantExpirationReminder *string `json:\"merchantExpirationReminder\"` // 商户到期提醒\n\tAgentExpirationReminder   *string `json:\"agentExpirationReminder\"`    // 代理商到期提醒\n\tAutoConfiscate           *bool   `json:\"autoConfiscate\"`              // 是否自动没收\n\tOverdueWithdrawalLimit   *int    `json:\"overdueWithdrawalLimit\"`      // 逾期取酒限制\n\tAutoConfiscateDays       *int    `json:\"autoConfiscateDays\"`         // 过期多少天后自动充公\n\tConfiscateWarehouse      *string `json:\"confiscateWarehouse\"`         // 充公仓库\n\tPageNum                  *int    `json:\"pageNum\"`                     // 页码\n\tPageSize                 *int    `json:\"pageSize\"`                    // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype WineStorageSettingTransfer struct {\n}\n\nfunc (transfer *WineStorageSettingTransfer) PoToVo(po po.WineStorageSetting) vo.WineStorageSettingVO {\n\tvo := vo.WineStorageSettingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *WineStorageSettingTransfer) VoToPo(vo vo.WineStorageSettingVO) po.WineStorageSetting {\n\tpo := po.WineStorageSetting{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WineStorageSettingService struct {\n}\n\nfunc (service *WineStorageSettingService) CreateWineStorageSetting(logCtx *gin.Context, setting *po.WineStorageSetting) error {\n\treturn Save(setting)\n}\n\nfunc (service *WineStorageSettingService) UpdateWineStorageSetting(logCtx *gin.Context, setting *po.WineStorageSetting) error {\n\treturn Update(setting)\n}\n\nfunc (service *WineStorageSettingService) DeleteWineStorageSetting(logCtx *gin.Context, id string) error {\n\treturn Delete(po.WineStorageSetting{Id: &id})\n}\n\nfunc (service *WineStorageSettingService) FindWineStorageSettingById(logCtx *gin.Context, id string) (setting *po.WineStorageSetting, err error) {\n\tsetting = &po.WineStorageSetting{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(setting).Error\n\treturn\n}\n\nfunc (service *WineStorageSettingService) FindAllWineStorageSetting(logCtx *gin.Context, reqDto *req.QueryWineStorageSettingReqDto) (list *[]po.WineStorageSetting, err error) {\n\tdb := model.DBSlave.Self.Model(&po.WineStorageSetting{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.WineStorageSetting{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *WineStorageSettingService) FindAllWineStorageSettingWithPagination(logCtx *gin.Context, reqDto *req.QueryWineStorageSettingReqDto) (list *[]po.WineStorageSetting, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.WineStorageSetting{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.WineStorageSetting{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WineStorageSettingController struct{}\n\nvar (\n\twineStorageSettingService  = impl.WineStorageSettingService{}\n\twineStorageSettingTransfer = transfer.WineStorageSettingTransfer{}\n)\n\n// @Summary 添加存储设置\n// @Description 添加存储设置\n// @Tags 存储设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddWineStorageSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.WineStorageSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wineStorageSetting/add [post]\nfunc (controller *WineStorageSettingController) AddWineStorageSetting(ctx *gin.Context) {\n\treqDto := req.AddWineStorageSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tsetting := po.WineStorageSetting{}\n\tif reqDto.VenueId != nil {\n\t\tsetting.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.StorageDays != nil {\n\t\tsetting.StorageDays = reqDto.StorageDays\n\t}\n\tif reqDto.RenewalTimes != nil {\n\t\tsetting.RenewalTimes = reqDto.RenewalTimes\n\t}\n\tif reqDto.RenewalDays != nil {\n\t\tsetting.RenewalDays = reqDto.RenewalDays\n\t}\n\tif reqDto.CustomerNotificationDays != nil {\n\t\tsetting.CustomerNotificationDays = reqDto.CustomerNotificationDays\n\t}\n\tif reqDto.MerchantNotificationDays != nil {\n\t\tsetting.MerchantNotificationDays = reqDto.MerchantNotificationDays\n\t}\n\tif reqDto.MerchantExpirationReminder != nil {\n\t\tsetting.MerchantExpirationReminder = reqDto.MerchantExpirationReminder\n\t}\n\tif reqDto.AgentExpirationReminder != nil {\n\t\tsetting.AgentExpirationReminder = reqDto.AgentExpirationReminder\n\t}\n\tif reqDto.AutoConfiscate != nil {\n\t\tsetting.AutoConfiscate = reqDto.AutoConfiscate\n\t}\n\tif reqDto.OverdueWithdrawalLimit != nil {\n\t\tsetting.OverdueWithdrawalLimit = reqDto.OverdueWithdrawalLimit\n\t}\n\tif reqDto.AutoConfiscateDays != nil {\n\t\tsetting.AutoConfiscateDays = reqDto.AutoConfiscateDays\n\t}\n\tif reqDto.ConfiscateWarehouse != nil {\n\t\tsetting.ConfiscateWarehouse = reqDto.ConfiscateWarehouse\n\t}\n\n\terr = wineStorageSettingService.CreateWineStorageSetting(ctx, &setting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, wineStorageSettingTransfer.PoToVo(setting))\n}\n\n// @Summary 更新存储设置\n// @Description 更新存储设置\n// @Tags 存储设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateWineStorageSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.WineStorageSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wineStorageSetting/update [post]\nfunc (controller *WineStorageSettingController) UpdateWineStorageSetting(ctx *gin.Context) {\n\treqDto := req.UpdateWineStorageSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tsetting, err := wineStorageSettingService.FindWineStorageSettingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tsetting.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.StorageDays != nil {\n\t\tsetting.StorageDays = reqDto.StorageDays\n\t}\n\tif reqDto.RenewalTimes != nil {\n\t\tsetting.RenewalTimes = reqDto.RenewalTimes\n\t}\n\tif reqDto.RenewalDays != nil {\n\t\tsetting.RenewalDays = reqDto.RenewalDays\n\t}\n\tif reqDto.CustomerNotificationDays != nil {\n\t\tsetting.CustomerNotificationDays = reqDto.CustomerNotificationDays\n\t}\n\tif reqDto.MerchantNotificationDays != nil {\n\t\tsetting.MerchantNotificationDays = reqDto.MerchantNotificationDays\n\t}\n\tif reqDto.MerchantExpirationReminder != nil {\n\t\tsetting.MerchantExpirationReminder = reqDto.MerchantExpirationReminder\n\t}\n\tif reqDto.AgentExpirationReminder != nil {\n\t\tsetting.AgentExpirationReminder = reqDto.AgentExpirationReminder\n\t}\n\tif reqDto.AutoConfiscate != nil {\n\t\tsetting.AutoConfiscate = reqDto.AutoConfiscate\n\t}\n\tif reqDto.OverdueWithdrawalLimit != nil {\n\t\tsetting.OverdueWithdrawalLimit = reqDto.OverdueWithdrawalLimit\n\t}\n\tif reqDto.AutoConfiscateDays != nil {\n\t\tsetting.AutoConfiscateDays = reqDto.AutoConfiscateDays\n\t}\n\tif reqDto.ConfiscateWarehouse != nil {\n\t\tsetting.ConfiscateWarehouse = reqDto.ConfiscateWarehouse\n\t}\n\n\terr = wineStorageSettingService.UpdateWineStorageSetting(ctx, setting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, wineStorageSettingTransfer.PoToVo(*setting))\n}\n\n// @Summary 删除存储设置\n// @Description 删除存储设置\n// @Tags 存储设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteWineStorageSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wineStorageSetting/delete [post]\nfunc (controller *WineStorageSettingController) DeleteWineStorageSetting(ctx *gin.Context) {\n\treqDto := req.DeleteWineStorageSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = wineStorageSettingService.DeleteWineStorageSetting(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询存储设置\n// @Description 查询存储设置\n// @Tags 存储设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryWineStorageSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.WineStorageSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wineStorageSetting/query [post]\nfunc (controller *WineStorageSettingController) QueryWineStorageSettings(ctx *gin.Context) {\n\treqDto := req.QueryWineStorageSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := wineStorageSettingService.FindAllWineStorageSetting(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.WineStorageSettingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, wineStorageSettingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询存储设置列表\n// @Description 查询存储设置列表\n// @Tags 存储设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryWineStorageSettingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.WineStorageSettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/wineStorageSetting/list [post]\nfunc (a *WineStorageSettingController) ListWineStorageSettings(ctx *gin.Context) {\n\treqDto := req.QueryWineStorageSettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := wineStorageSettingService.FindAllWineStorageSettingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.WineStorageSettingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.WineStorageSettingVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, wineStorageSettingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype WineStorageSettingRoute struct {\n}\n\nfunc (s *WineStorageSettingRoute) InitWineStorageSettingRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\twineStorageSettingController := controller.WineStorageSettingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/wineStorageSetting/add\", wineStorageSettingController.AddWineStorageSetting)       //add\n\t\troute.POST(\"/api/wineStorageSetting/update\", wineStorageSettingController.UpdateWineStorageSetting)   //update\n\t\troute.POST(\"/api/wineStorageSetting/delete\", wineStorageSettingController.DeleteWineStorageSetting)   //delete\n\t\troute.POST(\"/api/wineStorageSetting/query\", wineStorageSettingController.QueryWineStorageSettings)    //query\n\t\troute.POST(\"/api/wineStorageSetting/list\", wineStorageSettingController.ListWineStorageSettings)      //list\n\t}\n}"}]