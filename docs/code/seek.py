import json
import os

def ensure_dir(file_path):
    directory = os.path.dirname(file_path)
    if not os.path.exists(directory):
        os.makedirs(directory)

filename = 'ShiftHandoverFormAndPayBill.json'
base_path = '/Users/<USER>/sbin/app/work/erp/erp-lt-vv/'
# 读取JSON文件
with open(base_path + 'docs/code/' + filename, 'r') as file:
    data = json.load(file)

# 从第一个项目的vo字段获取模型名称（假设遵循 XXXPlanVO 的模式）
vo_code = data[0].get('vo', '')
model_name = ''
if 'type ' in vo_code:
    # 提取类型名称（例如，从 "type BuyoutPlanVO struct" 中提取 "BuyoutPlan"）
    type_line = [line for line in vo_code.split('\n') if 'type ' in line][0]
    model_name = type_line.split()[1].replace('VO', '')

if not model_name:
    raise ValueError("Could not determine model name from VO code")

# 文件映射，使用动态模型名称
file_mappings = {
    'vo': f'{base_path}/api/vo/{model_name}VO.go',
    'req_add': f'{base_path}/api/req/Add{model_name}ReqDto.go',
    'req_update': f'{base_path}/api/req/Update{model_name}ReqDto.go',
    'req_delete': f'{base_path}/api/req/Delete{model_name}ReqDto.go',
    'req_query': f'{base_path}/api/req/Query{model_name}ReqDto.go',
    'po': f'{base_path}/service/po/{model_name}.go',
    'transfer': f'{base_path}/service/transfer/{model_name}Transfer.go',
    'service': f'{base_path}/service/impl/{model_name}Service.go',
    'controller': f'{base_path}/controller/{model_name}Controller.go',
    'router': f'{base_path}/router/{model_name}Route.go'
}

# 将每个组件写入其文件
count = 0
for item in data:
    for key, file_path in file_mappings.items():
        if key in item:
            full_path = file_path
            ensure_dir(full_path)
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(item[key])
            print(f"Written {file_path}")
            count += 1
print(f"Total files written: {count}")
