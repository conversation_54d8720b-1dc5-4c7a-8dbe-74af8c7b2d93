[{"po": "package po\n\n// ShiftHandoverForm 班次交接单实体\ntype ShiftHandoverForm struct {\n\tId                        *string     `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`\n\tVenueId                   *string     `gorm:\"column:venue_id;type:varchar(64)\" json:\"venueId\"`\n\tEmployeeId                *string     `gorm:\"column:employee_id;type:varchar(64)\" json:\"employeeId\"`\n\tBusinessReceivable        *int64      `gorm:\"column:business_receivable\" json:\"businessReceivable\"`\n\tBusinessActual            *int64      `gorm:\"column:business_actual\" json:\"businessActual\"`\n\tBusinessNet               *int64      `gorm:\"column:business_net\" json:\"businessNet\"`\n\tMerchantDiscount          *int64      `gorm:\"column:merchant_discount\" json:\"merchantDiscount\"`\n\tMemberDiscount            *int64      `gorm:\"column:member_discount\" json:\"memberDiscount\"`\n\tMinimumAdjustment         *int64      `gorm:\"column:minimum_adjustment\" json:\"minimumAdjustment\"`\n\tWriteOffReceivable        *int64      `gorm:\"column:write_off_receivable\" json:\"writeOffReceivable\"`\n\tWriteOffActual            *int64      `gorm:\"column:write_off_actual\" json:\"writeOffActual\"`\n\tWriteOffDiscount          *int64      `gorm:\"column:write_off_discount\" json:\"writeOffDiscount\"`\n\tMemberCardPay             *int64      `gorm:\"column:member_card_pay\" json:\"memberCardPay\"`\n\tCreditAmount              *int64      `gorm:\"column:credit_amount\" json:\"creditAmount\"`\n\tRechargeAmount            *int64      `gorm:\"column:recharge_amount\" json:\"rechargeAmount\"`\n\tRechargeGift              *int64      `gorm:\"column:recharge_gift\" json:\"rechargeGift\"`\n\tCustomerBatches           *int64      `gorm:\"column:customer_batches\" json:\"customerBatches\"`\n\tStaffGifts                *int64      `gorm:\"column:staff_gifts\" json:\"staffGifts\"`\n\tWechatPayAmount           *int64      `gorm:\"column:wechat_pay_amount\" json:\"wechatPayAmount\"`\n\tAlipayPayAmount           *int64      `gorm:\"column:alipay_pay_amount\" json:\"alipayPayAmount\"`\n\tCashPayAmount             *int64      `gorm:\"column:cash_pay_amount\" json:\"cashPayAmount\"`\n\tMemberCardPrincipalAmount *int64      `gorm:\"column:member_card_principal_amount\" json:\"memberCardPrincipalAmount\"`\n\tGeneralGiftAmount         *int64      `gorm:\"column:general_gift_amount\" json:\"generalGiftAmount\"`\n\tRoomGiftAmount            *int64      `gorm:\"column:room_gift_amount\" json:\"roomGiftAmount\"`\n\tProductGiftAmount         *int64      `gorm:\"column:product_gift_amount\" json:\"productGiftAmount\"`\n\tBankCardPayAmount         *int64      `gorm:\"column:bank_card_pay_amount\" json:\"bankCardPayAmount\"`\n\tMeituanPayAmount          *int64      `gorm:\"column:meituan_pay_amount\" json:\"meituanPayAmount\"`\n\tKoubeiPayAmount           *int64      `gorm:\"column:koubei_pay_amount\" json:\"koubeiPayAmount\"`\n\tCouponPayAmount           *int64      `gorm:\"column:coupon_pay_amount\" json:\"couponPayAmount\"`\n\tShiftStartTime            *int64      `gorm:\"column:shift_start_time\" json:\"shiftStartTime\"`\n\tShiftEndTime              *int64      `gorm:\"column:shift_end_time\" json:\"shiftEndTime\"`\n\tOperator                  *string     `gorm:\"column:operator;type:varchar(64)\" json:\"operator\"`\n\tRemark                    *string     `gorm:\"column:remark;type:varchar(255)\" json:\"remark\"`\n\tCtime                     *int64      `gorm:\"column:ctime\" json:\"ctime\"`\n\tUtime                     *int64      `gorm:\"column:utime\" json:\"utime\"`\n\tState                     *int        `gorm:\"column:state\" json:\"state\"`\n\tVersion                   *int        `gorm:\"column:version\" json:\"version\"`\n}\n\nfunc (ShiftHandoverForm) TableName() string {\n\treturn \"shift_handover_form\"\n}\n\nfunc (s ShiftHandoverForm) GetId() string {\n\treturn *s.Id\n}", "vo": "package vo\n\n// ShiftHandoverFormVO 班次交接单值对象\ntype ShiftHandoverFormVO struct {\n\tId                        string `json:\"id\"`\n\tVenueId                   string `json:\"venueId\"`\n\tEmployeeId                string `json:\"employeeId\"`\n\tBusinessReceivable        int64  `json:\"businessReceivable\"`\n\tBusinessActual            int64  `json:\"businessActual\"`\n\tBusinessNet               int64  `json:\"businessNet\"`\n\tMerchantDiscount          int64  `json:\"merchantDiscount\"`\n\tMemberDiscount            int64  `json:\"memberDiscount\"`\n\tMinimumAdjustment         int64  `json:\"minimumAdjustment\"`\n\tWriteOffReceivable        int64  `json:\"writeOffReceivable\"`\n\tWriteOffActual            int64  `json:\"writeOffActual\"`\n\tWriteOffDiscount          int64  `json:\"writeOffDiscount\"`\n\tMemberCardPay             int64  `json:\"memberCardPay\"`\n\tCreditAmount              int64  `json:\"creditAmount\"`\n\tRechargeAmount            int64  `json:\"rechargeAmount\"`\n\tRechargeGift              int64  `json:\"rechargeGift\"`\n\tCustomerBatches           int64  `json:\"customerBatches\"`\n\tStaffGifts                int64  `json:\"staffGifts\"`\n\tWechatPayAmount           int64  `json:\"wechatPayAmount\"`\n\tAlipayPayAmount           int64  `json:\"alipayPayAmount\"`\n\tCashPayAmount             int64  `json:\"cashPayAmount\"`\n\tMemberCardPrincipalAmount int64  `json:\"memberCardPrincipalAmount\"`\n\tGeneralGiftAmount         int64  `json:\"generalGiftAmount\"`\n\tRoomGiftAmount            int64  `json:\"roomGiftAmount\"`\n\tProductGiftAmount         int64  `json:\"productGiftAmount\"`\n\tBankCardPayAmount         int64  `json:\"bankCardPayAmount\"`\n\tMeituanPayAmount          int64  `json:\"meituanPayAmount\"`\n\tKoubeiPayAmount           int64  `json:\"koubeiPayAmount\"`\n\tCouponPayAmount           int64  `json:\"couponPayAmount\"`\n\tShiftStartTime            int64  `json:\"shiftStartTime\"`\n\tShiftEndTime              int64  `json:\"shiftEndTime\"`\n\tOperator                  string `json:\"operator\"`\n\tRemark                    string `json:\"remark\"`\n\tCtime                     int64  `json:\"ctime\"`\n\tUtime                     int64  `json:\"utime\"`\n\tState                     int    `json:\"state\"`\n\tVersion                   int    `json:\"version\"`\n}", "req_add": "package req\n\n// AddShiftHandoverFormReqDto 创建班次交接单请求DTO\ntype AddShiftHandoverFormReqDto struct {\n\tVenueId                   *string `json:\"venueId\"`\n\tEmployeeId                *string `json:\"employeeId\"`\n\tBusinessReceivable        *int64  `json:\"businessReceivable\"`\n\tBusinessActual            *int64  `json:\"businessActual\"`\n\tBusinessNet               *int64  `json:\"businessNet\"`\n\tMerchantDiscount          *int64  `json:\"merchantDiscount\"`\n\tMemberDiscount            *int64  `json:\"memberDiscount\"`\n\tMinimumAdjustment         *int64  `json:\"minimumAdjustment\"`\n\tWriteOffReceivable        *int64  `json:\"writeOffReceivable\"`\n\tWriteOffActual            *int64  `json:\"writeOffActual\"`\n\tWriteOffDiscount          *int64  `json:\"writeOffDiscount\"`\n\tMemberCardPay             *int64  `json:\"memberCardPay\"`\n\tCreditAmount              *int64  `json:\"creditAmount\"`\n\tRechargeAmount            *int64  `json:\"rechargeAmount\"`\n\tRechargeGift              *int64  `json:\"rechargeGift\"`\n\tCustomerBatches           *int64  `json:\"customerBatches\"`\n\tStaffGifts                *int64  `json:\"staffGifts\"`\n\tWechatPayAmount           *int64  `json:\"wechatPayAmount\"`\n\tAlipayPayAmount           *int64  `json:\"alipayPayAmount\"`\n\tCashPayAmount             *int64  `json:\"cashPayAmount\"`\n\tMemberCardPrincipalAmount *int64  `json:\"memberCardPrincipalAmount\"`\n\tGeneralGiftAmount         *int64  `json:\"generalGiftAmount\"`\n\tRoomGiftAmount            *int64  `json:\"roomGiftAmount\"`\n\tProductGiftAmount         *int64  `json:\"productGiftAmount\"`\n\tBankCardPayAmount         *int64  `json:\"bankCardPayAmount\"`\n\tMeituanPayAmount          *int64  `json:\"meituanPayAmount\"`\n\tKoubeiPayAmount           *int64  `json:\"koubeiPayAmount\"`\n\tCouponPayAmount           *int64  `json:\"couponPayAmount\"`\n\tShiftStartTime            *int64  `json:\"shiftStartTime\"`\n\tShiftEndTime              *int64  `json:\"shiftEndTime\"`\n\tOperator                  *string `json:\"operator\"`\n\tRemark                    *string `json:\"remark\"`\n}", "req_update": "package req\n\n// UpdateShiftHandoverFormReqDto 更新班次交接单请求DTO\ntype UpdateShiftHandoverFormReqDto struct {\n\tId                        *string `json:\"id\"`\n\tVenueId                   *string `json:\"venueId\"`\n\tEmployeeId                *string `json:\"employeeId\"`\n\tBusinessReceivable        *int64  `json:\"businessReceivable\"`\n\tBusinessActual            *int64  `json:\"businessActual\"`\n\tBusinessNet               *int64  `json:\"businessNet\"`\n\tMerchantDiscount          *int64  `json:\"merchantDiscount\"`\n\tMemberDiscount            *int64  `json:\"memberDiscount\"`\n\tMinimumAdjustment         *int64  `json:\"minimumAdjustment\"`\n\tWriteOffReceivable        *int64  `json:\"writeOffReceivable\"`\n\tWriteOffActual            *int64  `json:\"writeOffActual\"`\n\tWriteOffDiscount          *int64  `json:\"writeOffDiscount\"`\n\tMemberCardPay             *int64  `json:\"memberCardPay\"`\n\tCreditAmount              *int64  `json:\"creditAmount\"`\n\tRechargeAmount            *int64  `json:\"rechargeAmount\"`\n\tRechargeGift              *int64  `json:\"rechargeGift\"`\n\tCustomerBatches           *int64  `json:\"customerBatches\"`\n\tStaffGifts                *int64  `json:\"staffGifts\"`\n\tWechatPayAmount           *int64  `json:\"wechatPayAmount\"`\n\tAlipayPayAmount           *int64  `json:\"alipayPayAmount\"`\n\tCashPayAmount             *int64  `json:\"cashPayAmount\"`\n\tMemberCardPrincipalAmount *int64  `json:\"memberCardPrincipalAmount\"`\n\tGeneralGiftAmount         *int64  `json:\"generalGiftAmount\"`\n\tRoomGiftAmount            *int64  `json:\"roomGiftAmount\"`\n\tProductGiftAmount         *int64  `json:\"productGiftAmount\"`\n\tBankCardPayAmount         *int64  `json:\"bankCardPayAmount\"`\n\tMeituanPayAmount          *int64  `json:\"meituanPayAmount\"`\n\tKoubeiPayAmount           *int64  `json:\"koubeiPayAmount\"`\n\tCouponPayAmount           *int64  `json:\"couponPayAmount\"`\n\tShiftStartTime            *int64  `json:\"shiftStartTime\"`\n\tShiftEndTime              *int64  `json:\"shiftEndTime\"`\n\tOperator                  *string `json:\"operator\"`\n\tRemark                    *string `json:\"remark\"`\n}", "req_delete": "package req\n\ntype DeleteShiftHandoverFormReqDto struct {\n\tId *string `json:\"id\"`\n}", "req_query": "package req\n\ntype QueryShiftHandoverFormReqDto struct {\n\tId                        *string `json:\"id\"`\n\tVenueId                   *string `json:\"venueId\"`\n\tEmployeeId                *string `json:\"employeeId\"`\n\tBusinessReceivable        *int64  `json:\"businessReceivable\"`\n\tBusinessActual            *int64  `json:\"businessActual\"`\n\tBusinessNet               *int64  `json:\"businessNet\"`\n\tMerchantDiscount          *int64  `json:\"merchantDiscount\"`\n\tMemberDiscount            *int64  `json:\"memberDiscount\"`\n\tMinimumAdjustment         *int64  `json:\"minimumAdjustment\"`\n\tWriteOffReceivable        *int64  `json:\"writeOffReceivable\"`\n\tWriteOffActual            *int64  `json:\"writeOffActual\"`\n\tWriteOffDiscount          *int64  `json:\"writeOffDiscount\"`\n\tMemberCardPay             *int64  `json:\"memberCardPay\"`\n\tCreditAmount              *int64  `json:\"creditAmount\"`\n\tRechargeAmount            *int64  `json:\"rechargeAmount\"`\n\tRechargeGift              *int64  `json:\"rechargeGift\"`\n\tCustomerBatches           *int64  `json:\"customerBatches\"`\n\tStaffGifts                *int64  `json:\"staffGifts\"`\n\tWechatPayAmount           *int64  `json:\"wechatPayAmount\"`\n\tAlipayPayAmount           *int64  `json:\"alipayPayAmount\"`\n\tCashPayAmount             *int64  `json:\"cashPayAmount\"`\n\tMemberCardPrincipalAmount *int64  `json:\"memberCardPrincipalAmount\"`\n\tGeneralGiftAmount         *int64  `json:\"generalGiftAmount\"`\n\tRoomGiftAmount            *int64  `json:\"roomGiftAmount\"`\n\tProductGiftAmount         *int64  `json:\"productGiftAmount\"`\n\tBankCardPayAmount         *int64  `json:\"bankCardPayAmount\"`\n\tMeituanPayAmount          *int64  `json:\"meituanPayAmount\"`\n\tKoubeiPayAmount           *int64  `json:\"koubeiPayAmount\"`\n\tCouponPayAmount           *int64  `json:\"couponPayAmount\"`\n\tShiftStartTime            *int64  `json:\"shiftStartTime\"`\n\tShiftEndTime              *int64  `json:\"shiftEndTime\"`\n\tOperator                  *string `json:\"operator\"`\n\tRemark                    *string `json:\"remark\"`\n\tPageNum                   *int    `json:\"pageNum\"`\n\tPageSize                  *int    `json:\"pageSize\"`\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ShiftHandoverFormTransfer struct {\n}\n\nfunc (transfer *ShiftHandoverFormTransfer) PoToVo(po po.ShiftHandoverForm) vo.ShiftHandoverFormVO {\n\tvo := vo.ShiftHandoverFormVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ShiftHandoverFormTransfer) VoToPo(vo vo.ShiftHandoverFormVO) po.ShiftHandoverForm {\n\tpo := po.ShiftHandoverForm{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype ShiftHandoverFormService struct {\n}\n\nvar (\n\tshiftHandoverFormService = ShiftHandoverFormService{}\n\tshiftHandoverFormTransfer = transfer.ShiftHandoverFormTransfer{}\n)\n\nfunc (service *ShiftHandoverFormService) CreateShiftHandoverForm(logCtx *gin.Context, form *po.ShiftHandoverForm) error {\n\treturn Save(form)\n}\n\nfunc (service *ShiftHandoverFormService) CreateShiftHandoverFormWithTx(logCtx *gin.Context, form *po.ShiftHandoverForm, tx *gorm.DB) error {\n\treturn SaveWithTx(form, tx)\n}\n\nfunc (service *ShiftHandoverFormService) UpdateShiftHandoverForm(logCtx *gin.Context, form *po.ShiftHandoverForm) error {\n\treturn Update(form)\n}\n\nfunc (service *ShiftHandoverFormService) UpdateShiftHandoverFormPartial(logCtx *gin.Context, form *po.ShiftHandoverForm) error {\n\treturn UpdateNotNull(form)\n}\n\nfunc (service *ShiftHandoverFormService) UpdateShiftHandoverFormPartialWithTx(logCtx *gin.Context, form *po.ShiftHandoverForm, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(form, tx)\n}\n\nfunc (service *ShiftHandoverFormService) DeleteShiftHandoverForm(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ShiftHandoverForm{Id: &id})\n}\n\nfunc (service *ShiftHandoverFormService) FindShiftHandoverFormById(logCtx *gin.Context, id string) (form *po.ShiftHandoverForm, err error) {\n\tform = &po.ShiftHandoverForm{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(form).Error\n\treturn\n}\n\nfunc (service *ShiftHandoverFormService) FindAllShiftHandoverForm(logCtx *gin.Context, reqDto *req.QueryShiftHandoverFormReqDto) (list *[]po.ShiftHandoverForm, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ShiftHandoverForm{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ShiftHandoverForm{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ShiftHandoverFormService) FindAllShiftHandoverFormWithPagination(logCtx *gin.Context, reqDto *req.QueryShiftHandoverFormReqDto) (list *[]po.ShiftHandoverForm, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ShiftHandoverForm{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ShiftHandoverForm{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftHandoverFormController struct{}\n\nvar (\n\tshiftHandoverFormService  = impl.ShiftHandoverFormService{}\n\tshiftHandoverFormTransfer = transfer.ShiftHandoverFormTransfer{}\n)\n\n// @Summary 添加班次交接单\n// @Description 添加班次交接单\n// @Tags 班次交接单\n// @Accept json\n// @Produce json\n// @Param body body req.AddShiftHandoverFormReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ShiftHandoverFormVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverForm/add [post]\nfunc (controller *ShiftHandoverFormController) AddShiftHandoverForm(ctx *gin.Context) {\n\treqDto := req.AddShiftHandoverFormReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tform := po.ShiftHandoverForm{}\n\t// 设置所有字段\n\tform.VenueId = reqDto.VenueId\n\tform.EmployeeId = reqDto.EmployeeId\n\tform.BusinessReceivable = reqDto.BusinessReceivable\n\tform.BusinessActual = reqDto.BusinessActual\n\tform.BusinessNet = reqDto.BusinessNet\n\tform.MerchantDiscount = reqDto.MerchantDiscount\n\tform.MemberDiscount = reqDto.MemberDiscount\n\tform.MinimumAdjustment = reqDto.MinimumAdjustment\n\tform.WriteOffReceivable = reqDto.WriteOffReceivable\n\tform.WriteOffActual = reqDto.WriteOffActual\n\tform.WriteOffDiscount = reqDto.WriteOffDiscount\n\tform.MemberCardPay = reqDto.MemberCardPay\n\tform.CreditAmount = reqDto.CreditAmount\n\tform.RechargeAmount = reqDto.RechargeAmount\n\tform.RechargeGift = reqDto.RechargeGift\n\tform.CustomerBatches = reqDto.CustomerBatches\n\tform.StaffGifts = reqDto.StaffGifts\n\tform.WechatPayAmount = reqDto.WechatPayAmount\n\tform.AlipayPayAmount = reqDto.AlipayPayAmount\n\tform.CashPayAmount = reqDto.CashPayAmount\n\tform.MemberCardPrincipalAmount = reqDto.MemberCardPrincipalAmount\n\tform.GeneralGiftAmount = reqDto.GeneralGiftAmount\n\tform.RoomGiftAmount = reqDto.RoomGiftAmount\n\tform.ProductGiftAmount = reqDto.ProductGiftAmount\n\tform.BankCardPayAmount = reqDto.BankCardPayAmount\n\tform.MeituanPayAmount = reqDto.MeituanPayAmount\n\tform.KoubeiPayAmount = reqDto.KoubeiPayAmount\n\tform.CouponPayAmount = reqDto.CouponPayAmount\n\tform.ShiftStartTime = reqDto.ShiftStartTime\n\tform.ShiftEndTime = reqDto.ShiftEndTime\n\tform.Operator = reqDto.Operator\n\tform.Remark = reqDto.Remark\n\n\terr = shiftHandoverFormService.CreateShiftHandoverForm(ctx, &form)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, shiftHandoverFormTransfer.PoToVo(form))\n}\n\n// @Summary 更新班次交接单\n// @Description 更新班次交接单\n// @Tags 班次交接单\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateShiftHandoverFormReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ShiftHandoverFormVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverForm/update [post]\nfunc (controller *ShiftHandoverFormController) UpdateShiftHandoverForm(ctx *gin.Context) {\n\treqDto := req.UpdateShiftHandoverFormReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tform, err := shiftHandoverFormService.FindShiftHandoverFormById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\t// 更新所有字段\n\tform.VenueId = reqDto.VenueId\n\tform.EmployeeId = reqDto.EmployeeId\n\tform.BusinessReceivable = reqDto.BusinessReceivable\n\tform.BusinessActual = reqDto.BusinessActual\n\tform.BusinessNet = reqDto.BusinessNet\n\tform.MerchantDiscount = reqDto.MerchantDiscount\n\tform.MemberDiscount = reqDto.MemberDiscount\n\tform.MinimumAdjustment = reqDto.MinimumAdjustment\n\tform.WriteOffReceivable = reqDto.WriteOffReceivable\n\tform.WriteOffActual = reqDto.WriteOffActual\n\tform.WriteOffDiscount = reqDto.WriteOffDiscount\n\tform.MemberCardPay = reqDto.MemberCardPay\n\tform.CreditAmount = reqDto.CreditAmount\n\tform.RechargeAmount = reqDto.RechargeAmount\n\tform.RechargeGift = reqDto.RechargeGift\n\tform.CustomerBatches = reqDto.CustomerBatches\n\tform.StaffGifts = reqDto.StaffGifts\n\tform.WechatPayAmount = reqDto.WechatPayAmount\n\tform.AlipayPayAmount = reqDto.AlipayPayAmount\n\tform.CashPayAmount = reqDto.CashPayAmount\n\tform.MemberCardPrincipalAmount = reqDto.MemberCardPrincipalAmount\n\tform.GeneralGiftAmount = reqDto.GeneralGiftAmount\n\tform.RoomGiftAmount = reqDto.RoomGiftAmount\n\tform.ProductGiftAmount = reqDto.ProductGiftAmount\n\tform.BankCardPayAmount = reqDto.BankCardPayAmount\n\tform.MeituanPayAmount = reqDto.MeituanPayAmount\n\tform.KoubeiPayAmount = reqDto.KoubeiPayAmount\n\tform.CouponPayAmount = reqDto.CouponPayAmount\n\tform.ShiftStartTime = reqDto.ShiftStartTime\n\tform.ShiftEndTime = reqDto.ShiftEndTime\n\tform.Operator = reqDto.Operator\n\tform.Remark = reqDto.Remark\n\n\terr = shiftHandoverFormService.UpdateShiftHandoverForm(ctx, form)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, shiftHandoverFormTransfer.PoToVo(*form))\n}\n\n// @Summary 删除班次交接单\n// @Description 删除班次交接单\n// @Tags 班次交接单\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteShiftHandoverFormReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverForm/delete [post]\nfunc (controller *ShiftHandoverFormController) DeleteShiftHandoverForm(ctx *gin.Context) {\n\treqDto := req.DeleteShiftHandoverFormReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = shiftHandoverFormService.DeleteShiftHandoverForm(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询班次交接单\n// @Description 查询班次交接单\n// @Tags 班次交接单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryShiftHandoverFormReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ShiftHandoverFormVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverForm/query [post]\nfunc (controller *ShiftHandoverFormController) QueryShiftHandoverForms(ctx *gin.Context) {\n\treqDto := req.QueryShiftHandoverFormReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := shiftHandoverFormService.FindAllShiftHandoverForm(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ShiftHandoverFormVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, shiftHandoverFormTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询班次交接单列表\n// @Description 查询班次交接单列表\n// @Tags 班次交接单\n// @Accept json\n// @Produce json\n// @Param body body req.QueryShiftHandoverFormReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.ShiftHandoverFormVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverForm/list [post]\nfunc (a *ShiftHandoverFormController) ListShiftHandoverForms(ctx *gin.Context) {\n\treqDto := req.QueryShiftHandoverFormReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := shiftHandoverFormService.FindAllShiftHandoverFormWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ShiftHandoverFormVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ShiftHandoverFormVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, shiftHandoverFormTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftHandoverFormRoute struct {\n}\n\nfunc (s *ShiftHandoverFormRoute) InitShiftHandoverFormRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tshiftHandoverFormController := controller.ShiftHandoverFormController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/shiftHandoverForm/add\", shiftHandoverFormController.AddShiftHandoverForm)       //add\n\t\troute.POST(\"/api/shiftHandoverForm/update\", shiftHandoverFormController.UpdateShiftHandoverForm)   //update\n\t\troute.POST(\"/api/shiftHandoverForm/delete\", shiftHandoverFormController.DeleteShiftHandoverForm)   //delete\n\t\troute.POST(\"/api/shiftHandoverForm/query\", shiftHandoverFormController.QueryShiftHandoverForms)   //query\n\t\troute.POST(\"/api/shiftHandoverForm/list\", shiftHandoverFormController.ListShiftHandoverForms)     //list\n\t}\n}"}]