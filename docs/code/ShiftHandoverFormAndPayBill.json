[{"po": "package po\n\n// ShiftHandoverFormAndPayBill 班次交接单中间表实体\ntype ShiftHandoverFormAndPayBill struct {\n\tId      *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`          // ID\n\tVenueId  *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`    // 门店ID\n\tShiftNo  *string `gorm:\"column:shift_no;type:varchar(64);default:''\" json:\"shiftNo\"`    // 员工ID\n\tPayId    *string `gorm:\"column:pay_id;type:varchar(64);default:''\" json:\"payId\"`        // 支付id\n\tRemark   *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`      // 备注\n\tCtime    *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                  // 创建时间\n\tUtime    *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                  // 更新时间\n\tState    *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                  // 状态\n\tVersion  *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`              // 版本\n}\n\n// TableName 设置表名\nfunc (ShiftHandoverFormAndPayBill) TableName() string {\n\treturn \"shift_handover_form_and_pay_bill\"\n}\n\nfunc (s ShiftHandoverFormAndPayBill) GetId() string {\n\treturn *s.Id\n}", "vo": "package vo\n\n// ShiftHandoverFormAndPayBillVO 班次交接单中间表值对象\ntype ShiftHandoverFormAndPayBillVO struct {\n\tId      string `json:\"id\"`      // ID\n\tVenueId  string `json:\"venueId\"` // 门店ID\n\tShiftNo  string `json:\"shiftNo\"` // 员工ID\n\tPayId    string `json:\"payId\"`   // 支付id\n\tRemark   string `json:\"remark\"`  // 备注\n\tCtime    int64  `json:\"ctime\"`   // 创建时间\n\tUtime    int64  `json:\"utime\"`   // 更新时间\n\tState    int    `json:\"state\"`   // 状态\n\tVersion  int    `json:\"version\"` // 版本\n}", "req_add": "package req\n\n// AddShiftHandoverFormAndPayBillReqDto 创建班次交接单中间表请求DTO\ntype AddShiftHandoverFormAndPayBillReqDto struct {\n\tVenueId  *string `json:\"venueId\"` // 门店ID\n\tShiftNo  *string `json:\"shiftNo\"` // 员工ID\n\tPayId    *string `json:\"payId\"`   // 支付id\n\tRemark   *string `json:\"remark\"`  // 备注\n}", "req_update": "package req\n\n// UpdateShiftHandoverFormAndPayBillReqDto 更新班次交接单中间表请求DTO\ntype UpdateShiftHandoverFormAndPayBillReqDto struct {\n\tId      *string `json:\"id\"`      // ID\n\tVenueId  *string `json:\"venueId\"` // 门店ID\n\tShiftNo  *string `json:\"shiftNo\"` // 员工ID\n\tPayId    *string `json:\"payId\"`   // 支付id\n\tRemark   *string `json:\"remark\"`  // 备注\n}", "req_delete": "package req\n\n// DeleteShiftHandoverFormAndPayBillReqDto 删除班次交接单中间表请求DTO\ntype DeleteShiftHandoverFormAndPayBillReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryShiftHandoverFormAndPayBillReqDto 查询班次交接单中间表请求DTO\ntype QueryShiftHandoverFormAndPayBillReqDto struct {\n\tId       *string `json:\"id\"`       // ID\n\tVenueId  *string `json:\"venueId\"`  // 门店ID\n\tShiftNo  *string `json:\"shiftNo\"`  // 员工ID\n\tPayId    *string `json:\"payId\"`    // 支付id\n\tRemark   *string `json:\"remark\"`   // 备注\n\tPageNum  *int    `json:\"pageNum\"`  // 页码\n\tPageSize *int    `json:\"pageSize\"` // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype ShiftHandoverFormAndPayBillTransfer struct {\n}\n\nfunc (transfer *ShiftHandoverFormAndPayBillTransfer) PoToVo(po po.ShiftHandoverFormAndPayBill) vo.ShiftHandoverFormAndPayBillVO {\n\tvo := vo.ShiftHandoverFormAndPayBillVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *ShiftHandoverFormAndPayBillTransfer) VoToPo(vo vo.ShiftHandoverFormAndPayBillVO) po.ShiftHandoverFormAndPayBill {\n\tpo := po.ShiftHandoverFormAndPayBill{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype ShiftHandoverFormAndPayBillService struct {\n}\n\nvar (\n\tshiftHandoverFormAndPayBillService = ShiftHandoverFormAndPayBillService{}\n\tshiftHandoverFormAndPayBillTransfer = transfer.ShiftHandoverFormAndPayBillTransfer{}\n)\n\nfunc (service *ShiftHandoverFormAndPayBillService) CreateShiftHandoverFormAndPayBill(logCtx *gin.Context, shiftHandoverFormAndPayBill *po.ShiftHandoverFormAndPayBill) error {\n\treturn Save(shiftHandoverFormAndPayBill)\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) CreateShiftHandoverFormAndPayBillWithTx(logCtx *gin.Context, shiftHandoverFormAndPayBill *po.ShiftHandoverFormAndPayBill, tx *gorm.DB) error {\n\treturn SaveWithTx(shiftHandoverFormAndPayBill, tx)\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) UpdateShiftHandoverFormAndPayBill(logCtx *gin.Context, shiftHandoverFormAndPayBill *po.ShiftHandoverFormAndPayBill) error {\n\treturn Update(shiftHandoverFormAndPayBill)\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) UpdateShiftHandoverFormAndPayBillPartial(logCtx *gin.Context, shiftHandoverFormAndPayBill *po.ShiftHandoverFormAndPayBill) error {\n\treturn UpdateNotNull(shiftHandoverFormAndPayBill)\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) UpdateShiftHandoverFormAndPayBillPartialWithTx(logCtx *gin.Context, shiftHandoverFormAndPayBill *po.ShiftHandoverFormAndPayBill, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(shiftHandoverFormAndPayBill, tx)\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) DeleteShiftHandoverFormAndPayBill(logCtx *gin.Context, id string) error {\n\treturn Delete(po.ShiftHandoverFormAndPayBill{Id: &id})\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) FindShiftHandoverFormAndPayBillById(logCtx *gin.Context, id string) (shiftHandoverFormAndPayBill *po.ShiftHandoverFormAndPayBill, err error) {\n\tshiftHandoverFormAndPayBill = &po.ShiftHandoverFormAndPayBill{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(shiftHandoverFormAndPayBill).Error\n\treturn\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) FindAllShiftHandoverFormAndPayBill(logCtx *gin.Context, reqDto *req.QueryShiftHandoverFormAndPayBillReqDto) (list *[]po.ShiftHandoverFormAndPayBill, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ShiftHandoverFormAndPayBill{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.ShiftHandoverFormAndPayBill{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *ShiftHandoverFormAndPayBillService) FindAllShiftHandoverFormAndPayBillWithPagination(logCtx *gin.Context, reqDto *req.QueryShiftHandoverFormAndPayBillReqDto) (list *[]po.ShiftHandoverFormAndPayBill, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.ShiftHandoverFormAndPayBill{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.ShiftHandoverFormAndPayBill{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftHandoverFormAndPayBillController struct{}\n\nvar (\n\tshiftHandoverFormAndPayBillService  = impl.ShiftHandoverFormAndPayBillService{}\n\tshiftHandoverFormAndPayBillTransfer = transfer.ShiftHandoverFormAndPayBillTransfer{}\n)\n\n// @Summary 添加班次交接单中间表\n// @Description 添加班次交接单中间表\n// @Tags 班次交接单中间表\n// @Accept json\n// @Produce json\n// @Param body body req.AddShiftHandoverFormAndPayBillReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ShiftHandoverFormAndPayBillVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverFormAndPayBill/add [post]\nfunc (controller *ShiftHandoverFormAndPayBillController) AddShiftHandoverFormAndPayBill(ctx *gin.Context) {\n\treqDto := req.AddShiftHandoverFormAndPayBillReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tshiftHandoverFormAndPayBill := po.ShiftHandoverFormAndPayBill{}\n\tif reqDto.VenueId != nil {\n\t\tshiftHandoverFormAndPayBill.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.ShiftNo != nil {\n\t\tshiftHandoverFormAndPayBill.ShiftNo = reqDto.ShiftNo\n\t}\n\tif reqDto.PayId != nil {\n\t\tshiftHandoverFormAndPayBill.PayId = reqDto.PayId\n\t}\n\tif reqDto.Remark != nil {\n\t\tshiftHandoverFormAndPayBill.Remark = reqDto.Remark\n\t}\n\terr = shiftHandoverFormAndPayBillService.CreateShiftHandoverFormAndPayBill(ctx, &shiftHandoverFormAndPayBill)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, shiftHandoverFormAndPayBillTransfer.PoToVo(shiftHandoverFormAndPayBill))\n}\n\n// @Summary 更新班次交接单中间表\n// @Description 更新班次交接单中间表\n// @Tags 班次交接单中间表\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateShiftHandoverFormAndPayBillReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.ShiftHandoverFormAndPayBillVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverFormAndPayBill/update [post]\nfunc (controller *ShiftHandoverFormAndPayBillController) UpdateShiftHandoverFormAndPayBill(ctx *gin.Context) {\n\treqDto := req.UpdateShiftHandoverFormAndPayBillReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tshiftHandoverFormAndPayBill, err := shiftHandoverFormAndPayBillService.FindShiftHandoverFormAndPayBillById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.VenueId != nil {\n\t\tshiftHandoverFormAndPayBill.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.ShiftNo != nil {\n\t\tshiftHandoverFormAndPayBill.ShiftNo = reqDto.ShiftNo\n\t}\n\tif reqDto.PayId != nil {\n\t\tshiftHandoverFormAndPayBill.PayId = reqDto.PayId\n\t}\n\tif reqDto.Remark != nil {\n\t\tshiftHandoverFormAndPayBill.Remark = reqDto.Remark\n\t}\n\terr = shiftHandoverFormAndPayBillService.UpdateShiftHandoverFormAndPayBill(ctx, shiftHandoverFormAndPayBill)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, shiftHandoverFormAndPayBillTransfer.PoToVo(*shiftHandoverFormAndPayBill))\n}\n\n// @Summary 删除班次交接单中间表\n// @Description 删除班次交接单中间表\n// @Tags 班次交接单中间表\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteShiftHandoverFormAndPayBillReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverFormAndPayBill/delete [post]\nfunc (controller *ShiftHandoverFormAndPayBillController) DeleteShiftHandoverFormAndPayBill(ctx *gin.Context) {\n\treqDto := req.DeleteShiftHandoverFormAndPayBillReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = shiftHandoverFormAndPayBillService.DeleteShiftHandoverFormAndPayBill(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询班次交接单中间表\n// @Description 查询班次交接单中间表\n// @Tags 班次交接单中间表\n// @Accept json\n// @Produce json\n// @Param body body req.QueryShiftHandoverFormAndPayBillReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.ShiftHandoverFormAndPayBillVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverFormAndPayBill/query [post]\nfunc (controller *ShiftHandoverFormAndPayBillController) QueryShiftHandoverFormAndPayBills(ctx *gin.Context) {\n\treqDto := req.QueryShiftHandoverFormAndPayBillReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := shiftHandoverFormAndPayBillService.FindAllShiftHandoverFormAndPayBill(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.ShiftHandoverFormAndPayBillVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, shiftHandoverFormAndPayBillTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询班次交接单中间表列表\n// @Description 查询班次交接单中间表列表\n// @Tags 班次交接单中间表\n// @Accept json\n// @Produce json\n// @Param body body req.QueryShiftHandoverFormAndPayBillReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.ShiftHandoverFormAndPayBillVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/shiftHandoverFormAndPayBill/list [post]\nfunc (a *ShiftHandoverFormAndPayBillController) ListShiftHandoverFormAndPayBills(ctx *gin.Context) {\n\treqDto := req.QueryShiftHandoverFormAndPayBillReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := shiftHandoverFormAndPayBillService.FindAllShiftHandoverFormAndPayBillWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.ShiftHandoverFormAndPayBillVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.ShiftHandoverFormAndPayBillVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, shiftHandoverFormAndPayBillTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype ShiftHandoverFormAndPayBillRoute struct {\n}\n\nfunc (s *ShiftHandoverFormAndPayBillRoute) InitShiftHandoverFormAndPayBillRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tshiftHandoverFormAndPayBillController := controller.ShiftHandoverFormAndPayBillController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/shiftHandoverFormAndPayBill/add\", shiftHandoverFormAndPayBillController.AddShiftHandoverFormAndPayBill)    //add\n\t\troute.POST(\"/api/shiftHandoverFormAndPayBill/update\", shiftHandoverFormAndPayBillController.UpdateShiftHandoverFormAndPayBill) //update\n\t\troute.POST(\"/api/shiftHandoverFormAndPayBill/delete\", shiftHandoverFormAndPayBillController.DeleteShiftHandoverFormAndPayBill) //delete\n\t\troute.POST(\"/api/shiftHandoverFormAndPayBill/query\", shiftHandoverFormAndPayBillController.QueryShiftHandoverFormAndPayBills)     //query\n\t\troute.POST(\"/api/shiftHandoverFormAndPayBill/list\", shiftHandoverFormAndPayBillController.ListShiftHandoverFormAndPayBills)     //list\n\t}\n}"}]