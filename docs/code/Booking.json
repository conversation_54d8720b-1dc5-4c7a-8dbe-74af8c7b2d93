[{"transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype BookingTransfer struct {\n}\n\nfunc (transfer *BookingTransfer) PoToVo(po po.Booking) vo.BookingVO {\n\tvo := vo.BookingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *BookingTransfer) VoToPo(vo vo.BookingVO) po.Booking {\n\tpo := po.Booking{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype BookingService struct {\n}\n\nfunc (service *BookingService) CreateBooking(logCtx *gin.Context, booking *po.Booking) error {\n\treturn Save(booking)\n}\n\nfunc (service *BookingService) UpdateBooking(logCtx *gin.Context, booking *po.Booking) error {\n\treturn Update(booking)\n}\n\nfunc (service *BookingService) DeleteBooking(logCtx *gin.Context, id string) error {\n\treturn Delete(po.Booking{Id: &id})\n}\n\nfunc (service *BookingService) FindBookingById(logCtx *gin.Context, id string) (booking *po.Booking, err error) {\n\tbooking = &po.Booking{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(booking).Error\n\treturn\n}\n\nfunc (service *BookingService) FindAllBooking(logCtx *gin.Context, reqDto *req.QueryBookingReqDto) (list *[]po.Booking, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Booking{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.CustomerName != nil && *reqDto.CustomerName != \"\" {\n\t\tdb = db.Where(\"customer_name LIKE ?\", \"%\"+*reqDto.CustomerName+\"%\")\n\t}\n\tif reqDto.Gender != nil && *reqDto.Gender != \"\" {\n\t\tdb = db.Where(\"gender=?\", *reqDto.Gender)\n\t}\n\tif reqDto.CustomerPhone != nil && *reqDto.CustomerPhone != \"\" {\n\t\tdb = db.Where(\"customer_phone=?\", *reqDto.CustomerPhone)\n\t}\n\tif reqDto.MemberCard != nil && *reqDto.MemberCard != \"\" {\n\t\tdb = db.Where(\"member_card=?\", *reqDto.MemberCard)\n\t}\n\tif reqDto.MemberCardId != nil && *reqDto.MemberCardId != \"\" {\n\t\tdb = db.Where(\"member_card_id=?\", *reqDto.MemberCardId)\n\t}\n\tif reqDto.CustomerSource != nil && *reqDto.CustomerSource != \"\" {\n\t\tdb = db.Where(\"customer_source=?\", *reqDto.CustomerSource)\n\t}\n\tif reqDto.ArrivalTime != nil {\n\t\tdb = db.Where(\"arrival_time=?\", *reqDto.ArrivalTime)\n\t}\n\tif reqDto.OpenTablePlan != nil && *reqDto.OpenTablePlan != \"\" {\n\t\tdb = db.Where(\"open_table_plan=?\", *reqDto.OpenTablePlan)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.RoomName != nil && *reqDto.RoomName != \"\" {\n\t\tdb = db.Where(\"room_name=?\", *reqDto.RoomName)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.Booking{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *BookingService) FindAllBookingWithPagination(logCtx *gin.Context, reqDto *req.QueryBookingReqDto) (list *[]po.Booking, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.Booking{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.CustomerName != nil && *reqDto.CustomerName != \"\" {\n\t\tdb = db.Where(\"customer_name LIKE ?\", \"%\"+*reqDto.CustomerName+\"%\")\n\t}\n\tif reqDto.Gender != nil && *reqDto.Gender != \"\" {\n\t\tdb = db.Where(\"gender=?\", *reqDto.Gender)\n\t}\n\tif reqDto.CustomerPhone != nil && *reqDto.CustomerPhone != \"\" {\n\t\tdb = db.Where(\"customer_phone=?\", *reqDto.CustomerPhone)\n\t}\n\tif reqDto.MemberCard != nil && *reqDto.MemberCard != \"\" {\n\t\tdb = db.Where(\"member_card=?\", *reqDto.MemberCard)\n\t}\n\tif reqDto.MemberCardId != nil && *reqDto.MemberCardId != \"\" {\n\t\tdb = db.Where(\"member_card_id=?\", *reqDto.MemberCardId)\n\t}\n\tif reqDto.CustomerSource != nil && *reqDto.CustomerSource != \"\" {\n\t\tdb = db.Where(\"customer_source=?\", *reqDto.CustomerSource)\n\t}\n\tif reqDto.ArrivalTime != nil {\n\t\tdb = db.Where(\"arrival_time=?\", *reqDto.ArrivalTime)\n\t}\n\tif reqDto.OpenTablePlan != nil && *reqDto.OpenTablePlan != \"\" {\n\t\tdb = db.Where(\"open_table_plan=?\", *reqDto.OpenTablePlan)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.RoomName != nil && *reqDto.RoomName != \"\" {\n\t\tdb = db.Where(\"room_name=?\", *reqDto.RoomName)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.Booking{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype BookingController struct{}\n\nvar (\n\tbookingService  = impl.BookingService{}\n\tbookingTransfer = transfer.BookingTransfer{}\n)\n\n// @Summary 添加预订\n// @Description 添加预订\n// @Tags 预订\n// @Accept json\n// @Produce json\n// @Param body body req.AddBookingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.BookingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/booking/add [post]\nfunc (controller *BookingController) AddBooking(ctx *gin.Context) {\n\treqDto := req.AddBookingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tbooking := po.Booking{}\n\tif reqDto.VenueId != nil {\n\t\tbooking.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.CustomerName != nil {\n\t\tbooking.CustomerName = reqDto.CustomerName\n\t}\n\tif reqDto.Gender != nil {\n\t\tbooking.Gender = reqDto.Gender\n\t}\n\tif reqDto.CustomerPhone != nil {\n\t\tbooking.CustomerPhone = reqDto.CustomerPhone\n\t}\n\tif reqDto.MemberCard != nil {\n\t\tbooking.MemberCard = reqDto.MemberCard\n\t}\n\tif reqDto.MemberCardId != nil {\n\t\tbooking.MemberCardId = reqDto.MemberCardId\n\t}\n\tif reqDto.CustomerSource != nil {\n\t\tbooking.CustomerSource = reqDto.CustomerSource\n\t}\n\tif reqDto.ArrivalTime != nil {\n\t\tbooking.ArrivalTime = reqDto.ArrivalTime\n\t}\n\tif reqDto.OpenTablePlan != nil {\n\t\tbooking.OpenTablePlan = reqDto.OpenTablePlan\n\t}\n\tif reqDto.RoomId != nil {\n\t\tbooking.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.RoomName != nil {\n\t\tbooking.RoomName = reqDto.RoomName\n\t}\n\tif reqDto.Remark != nil {\n\t\tbooking.Remark = reqDto.Remark\n\t}\n\n\terr = bookingService.CreateBooking(ctx, &booking)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, bookingTransfer.PoToVo(booking))\n}\n\n// @Summary 更新预订\n// @Description 更新预订\n// @Tags 预订\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateBookingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.BookingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/booking/update [post]\nfunc (controller *BookingController) UpdateBooking(ctx *gin.Context) {\n\treqDto := req.UpdateBookingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\tbooking, err := bookingService.FindBookingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tbooking.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.CustomerName != nil {\n\t\tbooking.CustomerName = reqDto.CustomerName\n\t}\n\tif reqDto.Gender != nil {\n\t\tbooking.Gender = reqDto.Gender\n\t}\n\tif reqDto.CustomerPhone != nil {\n\t\tbooking.CustomerPhone = reqDto.CustomerPhone\n\t}\n\tif reqDto.MemberCard != nil {\n\t\tbooking.MemberCard = reqDto.MemberCard\n\t}\n\tif reqDto.MemberCardId != nil {\n\t\tbooking.MemberCardId = reqDto.MemberCardId\n\t}\n\tif reqDto.CustomerSource != nil {\n\t\tbooking.CustomerSource = reqDto.CustomerSource\n\t}\n\tif reqDto.ArrivalTime != nil {\n\t\tbooking.ArrivalTime = reqDto.ArrivalTime\n\t}\n\tif reqDto.OpenTablePlan != nil {\n\t\tbooking.OpenTablePlan = reqDto.OpenTablePlan\n\t}\n\tif reqDto.RoomId != nil {\n\t\tbooking.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.RoomName != nil {\n\t\tbooking.RoomName = reqDto.RoomName\n\t}\n\tif reqDto.Remark != nil {\n\t\tbooking.Remark = reqDto.Remark\n\t}\n\n\terr = bookingService.UpdateBooking(ctx, booking)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, bookingTransfer.PoToVo(*booking))\n}\n\n// @Summary 删除预订\n// @Description 删除预订\n// @Tags 预订\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteBookingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/booking/delete [post]\nfunc (controller *BookingController) DeleteBooking(ctx *gin.Context) {\n\treqDto := req.DeleteBookingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = bookingService.DeleteBooking(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询预订\n// @Description 查询预订\n// @Tags 预订\n// @Accept json\n// @Produce json\n// @Param body body req.QueryBookingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.BookingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/booking/query [post]\nfunc (controller *BookingController) QueryBookings(ctx *gin.Context) {\n\treqDto := req.QueryBookingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := bookingService.FindAllBooking(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.BookingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, bookingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询预订列表\n// @Description 查询预订列表\n// @Tags 预订\n// @Accept json\n// @Produce json\n// @Param body body req.QueryBookingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.BookingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/booking/list [post]\nfunc (a *BookingController) ListBookings(ctx *gin.Context) {\n\treqDto := req.QueryBookingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := bookingService.FindAllBookingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.BookingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.BookingVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, bookingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype BookingRoute struct {\n}\n\nfunc (s *BookingRoute) InitBookingRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tbookingController := controller.BookingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/booking/add\", bookingController.AddBooking)    //add\n\t\troute.POST(\"/api/booking/update\", bookingController.UpdateBooking) //update\n\t\troute.POST(\"/api/booking/delete\", bookingController.DeleteBooking) //delete\n\t\troute.POST(\"/api/booking/query\", bookingController.QueryBookings)     //query\n\t\troute.POST(\"/api/booking/list\", bookingController.ListBookings)     //list\n\t}\n}\n", "po": "package po\n\n// Booking 预订实体\ntype Booking struct {\n\tId             *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tVenueId        *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`        // 门店ID\n\tCustomerName   *string `gorm:\"column:customer_name;type:varchar(64);default:''\" json:\"customerName\"`   // 客户名称\n\tGender         *string `gorm:\"column:gender;type:varchar(2);default:''\" json:\"gender\"`               // 性别 0 男 1 女 3 未知\n\tCustomerPhone  *string `gorm:\"column:customer_phone;type:varchar(20);default:''\" json:\"customerPhone\"`  // 客户电话\n\tMemberCard     *string `gorm:\"column:member_card;type:varchar(64);default:''\" json:\"memberCard\"`     // 会员卡\n\tMemberCardId   *string `gorm:\"column:member_card_id;type:varchar(64);default:''\" json:\"memberCardId\"` // 会员卡id\n\tCustomerSource *string `gorm:\"column:customer_source;type:varchar(64);default:''\" json:\"customerSource\"` // 客户来源\n\tArrivalTime    *int64  `gorm:\"column:arrival_time;type:int;default:0\" json:\"arrivalTime\"`    // 预抵时间\n\tOpenTablePlan  *string `gorm:\"column:open_table_plan;type:varchar(64);default:''\" json:\"openTablePlan\"`  // 开台方案\n\tRoomId         *string `gorm:\"column:room_id;type:varchar(64);default:''\" json:\"roomId\"`         // 房间ID\n\tRoomName       *string `gorm:\"column:room_name;type:varchar(64);default:''\" json:\"roomName\"`       // 房间名称\n\tRemark         *string `gorm:\"column:remark;type:text;default:''\" json:\"remark\"`                 // 备注\n\tCtime          *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime          *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间\n\tState          *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态\n\tVersion        *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (Booking) TableName() string {\n\treturn \"booking\"\n}\n\nfunc (b Booking) GetId() string {\n\treturn *b.Id\n}\n", "vo": "package vo\n\n// BookingVO 预订信息值对象\ntype BookingVO struct {\n\tId             string `json:\"id\"`             // ID\n\tVenueId        string `json:\"venueId\"`        // 门店ID\n\tCustomerName   string `json:\"customerName\"`   // 客户名称\n\tGender         string `json:\"gender\"`         // 性别 0 男 1 女 3 未知\n\tCustomerPhone  string `json:\"customerPhone\"`  // 客户电话\n\tMemberCard     string `json:\"memberCard\"`     // 会员卡\n\tMemberCardId   string `json:\"memberCardId\"`   // 会员卡id\n\tCustomerSource string `json:\"customerSource\"` // 客户来源\n\tArrivalTime    int64  `json:\"arrivalTime\"`    // 预抵时间\n\tOpenTablePlan  string `json:\"openTablePlan\"`  // 开台方案\n\tRoomId         string `json:\"roomId\"`         // 房间ID\n\tRoomName       string `json:\"roomName\"`       // 房间名称\n\tRemark         string `json:\"remark\"`         // 备注\n\tCtime          int64  `json:\"ctime\"`          // 创建时间\n\tUtime          int64  `json:\"utime\"`          // 更新时间\n\tState          int    `json:\"state\"`          // 状态\n\tVersion        int    `json:\"version\"`        // 版本号\n}\n", "req_add": "package req\n\n// AddBookingReqDto 创建预订请求DTO\ntype AddBookingReqDto struct {\n\tVenueId        *string `json:\"venueId\"`        // 门店ID\n\tCustomerName   *string `json:\"customerName\"`   // 客户名称\n\tGender         *string `json:\"gender\"`         // 性别 0 男 1 女 3 未知\n\tCustomerPhone  *string `json:\"customerPhone\"`  // 客户电话\n\tMemberCard     *string `json:\"memberCard\"`     // 会员卡\n\tMemberCardId   *string `json:\"memberCardId\"`   // 会员卡id\n\tCustomerSource *string `json:\"customerSource\"` // 客户来源\n\tArrivalTime    *int64  `json:\"arrivalTime\"`    // 预抵时间\n\tOpenTablePlan  *string `json:\"openTablePlan\"`  // 开台方案\n\tRoomId         *string `json:\"roomId\"`         // 房间ID\n\tRoomName       *string `json:\"roomName\"`       // 房间名称\n\tRemark         *string `json:\"remark\"`         // 备注\n}\n", "req_update": "package req\n\ntype UpdateBookingReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tVenueId        *string `json:\"venueId\"`        // 门店ID\n\tCustomerName   *string `json:\"customerName\"`   // 客户名称\n\tGender         *string `json:\"gender\"`         // 性别 0 男 1 女 3 未知\n\tCustomerPhone  *string `json:\"customerPhone\"`  // 客户电话\n\tMemberCard     *string `json:\"memberCard\"`     // 会员卡\n\tMemberCardId   *string `json:\"memberCardId\"`   // 会员卡id\n\tCustomerSource *string `json:\"customerSource\"` // 客户来源\n\tArrivalTime    *int64  `json:\"arrivalTime\"`    // 预抵时间\n\tOpenTablePlan  *string `json:\"openTablePlan\"`  // 开台方案\n\tRoomId         *string `json:\"roomId\"`         // 房间ID\n\tRoomName       *string `json:\"roomName\"`       // 房间名称\n\tRemark         *string `json:\"remark\"`         // 备注\n}\n", "req_delete": "package req\n\ntype DeleteBookingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryBookingReqDto struct {\n\tId             *string `json:\"id\"`             // ID\n\tVenueId        *string `json:\"venueId\"`        // 门店ID\n\tCustomerName   *string `json:\"customerName\"`   // 客户名称\n\tGender         *string `json:\"gender\"`         // 性别 0 男 1 女 3 未知\n\tCustomerPhone  *string `json:\"customerPhone\"`  // 客户电话\n\tMemberCard     *string `json:\"memberCard\"`     // 会员卡\n\tMemberCardId   *string `json:\"memberCardId\"`   // 会员卡id\n\tCustomerSource *string `json:\"customerSource\"` // 客户来源\n\tArrivalTime    *int64  `json:\"arrivalTime\"`    // 预抵时间\n\tOpenTablePlan  *string `json:\"openTablePlan\"`  // 开台方案\n\tRoomId         *string `json:\"roomId\"`         // 房间ID\n\tRoomName       *string `json:\"roomName\"`       // 房间名称\n\tPageNum        *int    `json:\"pageNum\"`        // 页码\n\tPageSize       *int    `json:\"pageSize\"`       // 每页记录数\n}\n"}]