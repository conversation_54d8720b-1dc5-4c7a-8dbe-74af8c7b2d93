[{"po": "package po\n\n// EmployeeGroupEmployee 员工组员工中间表\ntype EmployeeGroupEmployee struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tVenueId         *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`         // 门店ID\n\tEmployeeGroupId *string `gorm:\"column:employee_group_id;type:varchar(64);default:''\" json:\"employeeGroupId\"` // 员工组ID\n\tEmployeeId      *string `gorm:\"column:employee_id;type:varchar(64);default:''\" json:\"employeeId\"`      // 员工ID\n\tRemark          *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`          // 备注\n\tCtime           *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                   // 创建时间\n\tUtime           *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                   // 更新时间\n\tState           *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                   // 状态\n\tVersion         *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`               // 版本号\n}\n\n// TableName 设置表名\nfunc (EmployeeGroupEmployee) TableName() string {\n\treturn \"employee_group_employee\"\n}\n\nfunc (e EmployeeGroupEmployee) GetId() string {\n\treturn *e.Id\n}", "vo": "package vo\n\n// EmployeeGroupEmployeeVO 员工组员工中间表值对象\ntype EmployeeGroupEmployeeVO struct {\n\tId              string `json:\"id\"`              // ID\n\tVenueId         string `json:\"venueId\"`         // 门店ID\n\tEmployeeGroupId string `json:\"employeeGroupId\"` // 员工组ID\n\tEmployeeId      string `json:\"employeeId\"`      // 员工ID\n\tRemark          string `json:\"remark\"`          // 备注\n\tCtime           int64  `json:\"ctime\"`           // 创建时间\n\tUtime           int64  `json:\"utime\"`           // 更新时间\n\tState           int    `json:\"state\"`           // 状态\n\tVersion         int    `json:\"version\"`         // 版本号\n}", "req_add": "package req\n\n// AddEmployeeGroupEmployeeReqDto 创建员工组员工关系请求DTO\ntype AddEmployeeGroupEmployeeReqDto struct {\n\tVenueId         *string `json:\"venueId\"`         // 门店ID\n\tEmployeeGroupId *string `json:\"employeeGroupId\"` // 员工组ID\n\tEmployeeId      *string `json:\"employeeId\"`      // 员工ID\n\tRemark          *string `json:\"remark\"`          // 备注\n}", "req_update": "package req\n\n// UpdateEmployeeGroupEmployeeReqDto 更新员工组员工关系请求DTO\ntype UpdateEmployeeGroupEmployeeReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tVenueId         *string `json:\"venueId\"`         // 门店ID\n\tEmployeeGroupId *string `json:\"employeeGroupId\"` // 员工组ID\n\tEmployeeId      *string `json:\"employeeId\"`      // 员工ID\n\tRemark          *string `json:\"remark\"`          // 备注\n}", "req_delete": "package req\n\n// DeleteEmployeeGroupEmployeeReqDto 删除员工组员工关系请求DTO\ntype DeleteEmployeeGroupEmployeeReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryEmployeeGroupEmployeeReqDto 查询员工组员工关系请求DTO\ntype QueryEmployeeGroupEmployeeReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tVenueId         *string `json:\"venueId\"`         // 门店ID\n\tEmployeeGroupId *string `json:\"employeeGroupId\"` // 员工组ID\n\tEmployeeId      *string `json:\"employeeId\"`      // 员工ID\n\tRemark          *string `json:\"remark\"`          // 备注\n\tPageNum         *int    `json:\"pageNum\"`         // 页码\n\tPageSize        *int    `json:\"pageSize\"`        // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype EmployeeGroupEmployeeTransfer struct {\n}\n\nfunc (transfer *EmployeeGroupEmployeeTransfer) PoToVo(po po.EmployeeGroupEmployee) vo.EmployeeGroupEmployeeVO {\n\tvo := vo.EmployeeGroupEmployeeVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *EmployeeGroupEmployeeTransfer) VoToPo(vo vo.EmployeeGroupEmployeeVO) po.EmployeeGroupEmployee {\n\tpo := po.EmployeeGroupEmployee{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype EmployeeGroupEmployeeService struct {\n}\n\nvar (\n\temployeeGroupEmployeeService = EmployeeGroupEmployeeService{}\n\temployeeGroupEmployeeTransfer = transfer.EmployeeGroupEmployeeTransfer{}\n)\n\nfunc (service *EmployeeGroupEmployeeService) CreateEmployeeGroupEmployee(logCtx *gin.Context, employeeGroupEmployee *po.EmployeeGroupEmployee) error {\n\treturn Save(employeeGroupEmployee)\n}\n\nfunc (service *EmployeeGroupEmployeeService) CreateEmployeeGroupEmployeeWithTx(logCtx *gin.Context, employeeGroupEmployee *po.EmployeeGroupEmployee, tx *gorm.DB) error {\n\treturn SaveWithTx(employeeGroupEmployee, tx)\n}\n\nfunc (service *EmployeeGroupEmployeeService) UpdateEmployeeGroupEmployee(logCtx *gin.Context, employeeGroupEmployee *po.EmployeeGroupEmployee) error {\n\treturn Update(employeeGroupEmployee)\n}\n\nfunc (service *EmployeeGroupEmployeeService) UpdateEmployeeGroupEmployeePartial(logCtx *gin.Context, employeeGroupEmployee *po.EmployeeGroupEmployee) error {\n\treturn UpdateNotNull(employeeGroupEmployee)\n}\n\nfunc (service *EmployeeGroupEmployeeService) UpdateEmployeeGroupEmployeePartialWithTx(logCtx *gin.Context, employeeGroupEmployee *po.EmployeeGroupEmployee, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(employeeGroupEmployee, tx)\n}\n\nfunc (service *EmployeeGroupEmployeeService) DeleteEmployeeGroupEmployee(logCtx *gin.Context, id string) error {\n\treturn Delete(po.EmployeeGroupEmployee{Id: &id})\n}\n\nfunc (service *EmployeeGroupEmployeeService) FindEmployeeGroupEmployeeById(logCtx *gin.Context, id string) (employeeGroupEmployee *po.EmployeeGroupEmployee, err error) {\n\temployeeGroupEmployee = &po.EmployeeGroupEmployee{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(employeeGroupEmployee).Error\n\treturn\n}\n\nfunc (service *EmployeeGroupEmployeeService) FindAllEmployeeGroupEmployee(logCtx *gin.Context, reqDto *req.QueryEmployeeGroupEmployeeReqDto) (list *[]po.EmployeeGroupEmployee, err error) {\n\tdb := model.DBSlave.Self.Model(&po.EmployeeGroupEmployee{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.EmployeeGroupId != nil && *reqDto.EmployeeGroupId != \"\" {\n\t\tdb = db.Where(\"employee_group_id=?\", *reqDto.EmployeeGroupId)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.EmployeeGroupEmployee{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *EmployeeGroupEmployeeService) FindAllEmployeeGroupEmployeeWithPagination(logCtx *gin.Context, reqDto *req.QueryEmployeeGroupEmployeeReqDto) (list *[]po.EmployeeGroupEmployee, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.EmployeeGroupEmployee{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.EmployeeGroupId != nil && *reqDto.EmployeeGroupId != \"\" {\n\t\tdb = db.Where(\"employee_group_id=?\", *reqDto.EmployeeGroupId)\n\t}\n\tif reqDto.EmployeeId != nil && *reqDto.EmployeeId != \"\" {\n\t\tdb = db.Where(\"employee_id=?\", *reqDto.EmployeeId)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.EmployeeGroupEmployee{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeGroupEmployeeController struct{}\n\nvar (\n\temployeeGroupEmployeeService  = impl.EmployeeGroupEmployeeService{}\n\temployeeGroupEmployeeTransfer = transfer.EmployeeGroupEmployeeTransfer{}\n)\n\n// @Summary 添加员工组员工关系\n// @Description 添加员工组员工关系\n// @Tags 员工组员工关系\n// @Accept json\n// @Produce json\n// @Param body body req.AddEmployeeGroupEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.EmployeeGroupEmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group-employee/add [post]\nfunc (controller *EmployeeGroupEmployeeController) AddEmployeeGroupEmployee(ctx *gin.Context) {\n\treqDto := req.AddEmployeeGroupEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\temployeeGroupEmployee := po.EmployeeGroupEmployee{}\n\tif reqDto.VenueId != nil {\n\t\temployeeGroupEmployee.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.EmployeeGroupId != nil {\n\t\temployeeGroupEmployee.EmployeeGroupId = reqDto.EmployeeGroupId\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\temployeeGroupEmployee.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.Remark != nil {\n\t\temployeeGroupEmployee.Remark = reqDto.Remark\n\t}\n\n\terr = employeeGroupEmployeeService.CreateEmployeeGroupEmployee(ctx, &employeeGroupEmployee)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, employeeGroupEmployeeTransfer.PoToVo(employeeGroupEmployee))\n}\n\n// @Summary 更新员工组员工关系\n// @Description 更新员工组员工关系\n// @Tags 员工组员工关系\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateEmployeeGroupEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.EmployeeGroupEmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group-employee/update [post]\nfunc (controller *EmployeeGroupEmployeeController) UpdateEmployeeGroupEmployee(ctx *gin.Context) {\n\treqDto := req.UpdateEmployeeGroupEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\temployeeGroupEmployee, err := employeeGroupEmployeeService.FindEmployeeGroupEmployeeById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\temployeeGroupEmployee.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.EmployeeGroupId != nil {\n\t\temployeeGroupEmployee.EmployeeGroupId = reqDto.EmployeeGroupId\n\t}\n\tif reqDto.EmployeeId != nil {\n\t\temployeeGroupEmployee.EmployeeId = reqDto.EmployeeId\n\t}\n\tif reqDto.Remark != nil {\n\t\temployeeGroupEmployee.Remark = reqDto.Remark\n\t}\n\n\terr = employeeGroupEmployeeService.UpdateEmployeeGroupEmployee(ctx, employeeGroupEmployee)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, employeeGroupEmployeeTransfer.PoToVo(*employeeGroupEmployee))\n}\n\n// @Summary 删除员工组员工关系\n// @Description 删除员工组员工关系\n// @Tags 员工组员工关系\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteEmployeeGroupEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group-employee/delete [post]\nfunc (controller *EmployeeGroupEmployeeController) DeleteEmployeeGroupEmployee(ctx *gin.Context) {\n\treqDto := req.DeleteEmployeeGroupEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = employeeGroupEmployeeService.DeleteEmployeeGroupEmployee(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询员工组员工关系\n// @Description 查询员工组员工关系\n// @Tags 员工组员工关系\n// @Accept json\n// @Produce json\n// @Param body body req.QueryEmployeeGroupEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.EmployeeGroupEmployeeVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group-employee/query [post]\nfunc (controller *EmployeeGroupEmployeeController) QueryEmployeeGroupEmployees(ctx *gin.Context) {\n\treqDto := req.QueryEmployeeGroupEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := employeeGroupEmployeeService.FindAllEmployeeGroupEmployee(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.EmployeeGroupEmployeeVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, employeeGroupEmployeeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询员工组员工关系列表\n// @Description 查询员工组员工关系列表\n// @Tags 员工组员工关系\n// @Accept json\n// @Produce json\n// @Param body body req.QueryEmployeeGroupEmployeeReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.EmployeeGroupEmployeeVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/employee-group-employee/list [post]\nfunc (controller *EmployeeGroupEmployeeController) ListEmployeeGroupEmployees(ctx *gin.Context) {\n\treqDto := req.QueryEmployeeGroupEmployeeReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := employeeGroupEmployeeService.FindAllEmployeeGroupEmployeeWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.EmployeeGroupEmployeeVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.EmployeeGroupEmployeeVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, employeeGroupEmployeeTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype EmployeeGroupEmployeeRoute struct {\n}\n\nfunc (s *EmployeeGroupEmployeeRoute) InitEmployeeGroupEmployeeRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\temployeeGroupEmployeeController := controller.EmployeeGroupEmployeeController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/employee-group-employee/add\", employeeGroupEmployeeController.AddEmployeeGroupEmployee)       // add\n\t\troute.POST(\"/api/employee-group-employee/update\", employeeGroupEmployeeController.UpdateEmployeeGroupEmployee)   // update\n\t\troute.POST(\"/api/employee-group-employee/delete\", employeeGroupEmployeeController.DeleteEmployeeGroupEmployee)   // delete\n\t\troute.POST(\"/api/employee-group-employee/query\", employeeGroupEmployeeController.QueryEmployeeGroupEmployees)    // query\n\t\troute.POST(\"/api/employee-group-employee/list\", employeeGroupEmployeeController.ListEmployeeGroupEmployees)     // list\n\t}\n}"}]