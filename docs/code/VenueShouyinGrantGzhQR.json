[{"po": "package po\n\n// VenueShouyinGrantGzhQR 门店收银机授权公众号二维码实体\ntype VenueShouyinGrantGzhQR struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tVenueId       *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`        // 门店ID\n\tSceneStr      *string `gorm:\"column:scene_str;type:varchar(64);default:''\" json:\"sceneStr\"`      // 场景str-微信公众号\n\tMac           *string `gorm:\"column:mac;type:varchar(64);default:''\" json:\"mac\"`                  // mac地址\n\tQrGzhUrl      *string `gorm:\"column:qr_gzh_url;type:varchar(255);default:''\" json:\"qrGzhUrl\"`    // 授权公众号二维码URL\n\tUrlExpireTime *int64  `gorm:\"column:url_expire_time;type:int;default:0\" json:\"urlExpireTime\"`    // 二维码过期时间\n\tRemark        *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`          // 备注\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                     // 创建时间\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                     // 更新时间\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                     // 状态\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                 // 版本号\n}\n\n// TableName 设置表名\nfunc (VenueShouyinGrantGzhQR) TableName() string {\n\treturn \"venue_shouyin_grant_gzh_qr\"\n}\n\nfunc (v VenueShouyinGrantGzhQR) GetId() string {\n\treturn *v.Id\n}\n", "vo": "package vo\n\n// VenueShouyinGrantGzhQRVO 门店收银机授权公众号二维码值对象\ntype VenueShouyinGrantGzhQRVO struct {\n\tId            string `json:\"id\"`            // ID\n\tVenueId       string `json:\"venueId\"`       // 门店ID\n\tSceneStr      string `json:\"sceneStr\"`      // 场景str-微信公众号\n\tMac           string `json:\"mac\"`           // mac地址\n\tQrGzhUrl      string `json:\"qrGzhUrl\"`      // 授权公众号二维码URL\n\tUrlExpireTime int64  `json:\"urlExpireTime\"` // 二维码过期时间\n\tRemark        string `json:\"remark\"`        // 备注\n\tCtime         int64  `json:\"ctime\"`         // 创建时间\n\tUtime         int64  `json:\"utime\"`         // 更新时间\n\tState         int    `json:\"state\"`         // 状态\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddVenueShouyinGrantGzhQRReqDto 创建门店收银机授权公众号二维码请求DTO\ntype AddVenueShouyinGrantGzhQRReqDto struct {\n\tVenueId       *string `json:\"venueId\"`       // 门店ID\n\tSceneStr      *string `json:\"sceneStr\"`      // 场景str-微信公众号\n\tMac           *string `json:\"mac\"`           // mac地址\n\tQrGzhUrl      *string `json:\"qrGzhUrl\"`      // 授权公众号二维码URL\n\tUrlExpireTime *int64  `json:\"urlExpireTime\"` // 二维码过期时间\n\tRemark        *string `json:\"remark\"`        // 备注\n}\n", "req_update": "package req\n\n// UpdateVenueShouyinGrantGzhQRReqDto 更新门店收银机授权公众号二维码请求DTO\ntype UpdateVenueShouyinGrantGzhQRReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tVenueId       *string `json:\"venueId\"`       // 门店ID\n\tSceneStr      *string `json:\"sceneStr\"`      // 场景str-微信公众号\n\tMac           *string `json:\"mac\"`           // mac地址\n\tQrGzhUrl      *string `json:\"qrGzhUrl\"`      // 授权公众号二维码URL\n\tUrlExpireTime *int64  `json:\"urlExpireTime\"` // 二维码过期时间\n\tRemark        *string `json:\"remark\"`        // 备注\n}\n", "req_delete": "package req\n\n// DeleteVenueShouyinGrantGzhQRReqDto 删除门店收银机授权公众号二维码请求DTO\ntype DeleteVenueShouyinGrantGzhQRReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\n// QueryVenueShouyinGrantGzhQRReqDto 查询门店收银机授权公众号二维码请求DTO\ntype QueryVenueShouyinGrantGzhQRReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tVenueId       *string `json:\"venueId\"`       // 门店ID\n\tSceneStr      *string `json:\"sceneStr\"`      // 场景str-微信公众号\n\tMac           *string `json:\"mac\"`           // mac地址\n\tQrGzhUrl      *string `json:\"qrGzhUrl\"`      // 授权公众号二维码URL\n\tUrlExpireTime *int64  `json:\"urlExpireTime\"` // 二维码过期时间\n\tRemark        *string `json:\"remark\"`        // 备注\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VenueShouyinGrantGzhQRTransfer struct {\n}\n\nfunc (transfer *VenueShouyinGrantGzhQRTransfer) PoToVo(po po.VenueShouyinGrantGzhQR) vo.VenueShouyinGrantGzhQRVO {\n\tvo := vo.VenueShouyinGrantGzhQRVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VenueShouyinGrantGzhQRTransfer) VoToPo(vo vo.VenueShouyinGrantGzhQRVO) po.VenueShouyinGrantGzhQR {\n\tpo := po.VenueShouyinGrantGzhQR{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype VenueShouyinGrantGzhQRService struct {\n}\n\nvar (\n\tvenueShouyinGrantGzhQRService = VenueShouyinGrantGzhQRService{}\n\tvenueShouyinGrantGzhQRTransfer = transfer.VenueShouyinGrantGzhQRTransfer{}\n)\n\nfunc (service *VenueShouyinGrantGzhQRService) CreateVenueShouyinGrantGzhQR(logCtx *gin.Context, venueShouyinGrantGzhQR *po.VenueShouyinGrantGzhQR) error {\n\treturn Save(venueShouyinGrantGzhQR)\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) CreateVenueShouyinGrantGzhQRWithTx(logCtx *gin.Context, venueShouyinGrantGzhQR *po.VenueShouyinGrantGzhQR, tx *gorm.DB) error {\n\treturn SaveWithTx(venueShouyinGrantGzhQR, tx)\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) UpdateVenueShouyinGrantGzhQR(logCtx *gin.Context, venueShouyinGrantGzhQR *po.VenueShouyinGrantGzhQR) error {\n\treturn Update(venueShouyinGrantGzhQR)\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) UpdateVenueShouyinGrantGzhQRPartial(logCtx *gin.Context, venueShouyinGrantGzhQR *po.VenueShouyinGrantGzhQR) error {\n\treturn UpdateNotNull(venueShouyinGrantGzhQR)\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) UpdateVenueShouyinGrantGzhQRPartialWithTx(logCtx *gin.Context, venueShouyinGrantGzhQR *po.VenueShouyinGrantGzhQR, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(venueShouyinGrantGzhQR, tx)\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) DeleteVenueShouyinGrantGzhQR(logCtx *gin.Context, id string) error {\n\treturn Delete(po.VenueShouyinGrantGzhQR{Id: &id})\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) FindVenueShouyinGrantGzhQRById(logCtx *gin.Context, id string) (venueShouyinGrantGzhQR *po.VenueShouyinGrantGzhQR, err error) {\n\tvenueShouyinGrantGzhQR = &po.VenueShouyinGrantGzhQR{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(venueShouyinGrantGzhQR).Error\n\treturn\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) FindAllVenueShouyinGrantGzhQR(logCtx *gin.Context, reqDto *req.QueryVenueShouyinGrantGzhQRReqDto) (list *[]po.VenueShouyinGrantGzhQR, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenueShouyinGrantGzhQR{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.SceneStr != nil && *reqDto.SceneStr != \"\" {\n\t\tdb = db.Where(\"scene_str=?\", *reqDto.SceneStr)\n\t}\n\tif reqDto.Mac != nil && *reqDto.Mac != \"\" {\n\t\tdb = db.Where(\"mac=?\", *reqDto.Mac)\n\t}\n\tif reqDto.QrGzhUrl != nil && *reqDto.QrGzhUrl != \"\" {\n\t\tdb = db.Where(\"qr_gzh_url=?\", *reqDto.QrGzhUrl)\n\t}\n\tif reqDto.UrlExpireTime != nil {\n\t\tdb = db.Where(\"url_expire_time=?\", *reqDto.UrlExpireTime)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark=?\", *reqDto.Remark)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.VenueShouyinGrantGzhQR{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VenueShouyinGrantGzhQRService) FindAllVenueShouyinGrantGzhQRWithPagination(logCtx *gin.Context, reqDto *req.QueryVenueShouyinGrantGzhQRReqDto) (list *[]po.VenueShouyinGrantGzhQR, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenueShouyinGrantGzhQR{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.SceneStr != nil && *reqDto.SceneStr != \"\" {\n\t\tdb = db.Where(\"scene_str=?\", *reqDto.SceneStr)\n\t}\n\tif reqDto.Mac != nil && *reqDto.Mac != \"\" {\n\t\tdb = db.Where(\"mac=?\", *reqDto.Mac)\n\t}\n\tif reqDto.QrGzhUrl != nil && *reqDto.QrGzhUrl != \"\" {\n\t\tdb = db.Where(\"qr_gzh_url=?\", *reqDto.QrGzhUrl)\n\t}\n\tif reqDto.UrlExpireTime != nil {\n\t\tdb = db.Where(\"url_expire_time=?\", *reqDto.UrlExpireTime)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark=?\", *reqDto.Remark)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.VenueShouyinGrantGzhQR{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueShouyinGrantGzhQRController struct{}\n\nvar (\n\tvenueShouyinGrantGzhQRService  = impl.VenueShouyinGrantGzhQRService{}\n\tvenueShouyinGrantGzhQRTransfer = transfer.VenueShouyinGrantGzhQRTransfer{}\n)\n\n// @Summary 添加门店收银机授权公众号二维码\n// @Description 添加门店收银机授权公众号二维码\n// @Tags 门店收银机授权公众号二维码\n// @Accept json\n// @Produce json\n// @Param body body req.AddVenueShouyinGrantGzhQRReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueShouyinGrantGzhQRVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-shouyin-grant-gzh-qr/add [post]\nfunc (controller *VenueShouyinGrantGzhQRController) AddVenueShouyinGrantGzhQR(ctx *gin.Context) {\n\treqDto := req.AddVenueShouyinGrantGzhQRReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvenueShouyinGrantGzhQR := po.VenueShouyinGrantGzhQR{}\n\tif reqDto.VenueId != nil {\n\t\tvenueShouyinGrantGzhQR.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.SceneStr != nil {\n\t\tvenueShouyinGrantGzhQR.SceneStr = reqDto.SceneStr\n\t}\n\tif reqDto.Mac != nil {\n\t\tvenueShouyinGrantGzhQR.Mac = reqDto.Mac\n\t}\n\tif reqDto.QrGzhUrl != nil {\n\t\tvenueShouyinGrantGzhQR.QrGzhUrl = reqDto.QrGzhUrl\n\t}\n\tif reqDto.UrlExpireTime != nil {\n\t\tvenueShouyinGrantGzhQR.UrlExpireTime = reqDto.UrlExpireTime\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenueShouyinGrantGzhQR.Remark = reqDto.Remark\n\t}\n\n\terr = venueShouyinGrantGzhQRService.CreateVenueShouyinGrantGzhQR(ctx, &venueShouyinGrantGzhQR)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueShouyinGrantGzhQRTransfer.PoToVo(venueShouyinGrantGzhQR))\n}\n\n// @Summary 更新门店收银机授权公众号二维码\n// @Description 更新门店收银机授权公众号二维码\n// @Tags 门店收银机授权公众号二维码\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVenueShouyinGrantGzhQRReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenueShouyinGrantGzhQRVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-shouyin-grant-gzh-qr/update [post]\nfunc (controller *VenueShouyinGrantGzhQRController) UpdateVenueShouyinGrantGzhQR(ctx *gin.Context) {\n\treqDto := req.UpdateVenueShouyinGrantGzhQRReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tvenueShouyinGrantGzhQR, err := venueShouyinGrantGzhQRService.FindVenueShouyinGrantGzhQRById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tvenueShouyinGrantGzhQR.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.SceneStr != nil {\n\t\tvenueShouyinGrantGzhQR.SceneStr = reqDto.SceneStr\n\t}\n\tif reqDto.Mac != nil {\n\t\tvenueShouyinGrantGzhQR.Mac = reqDto.Mac\n\t}\n\tif reqDto.QrGzhUrl != nil {\n\t\tvenueShouyinGrantGzhQR.QrGzhUrl = reqDto.QrGzhUrl\n\t}\n\tif reqDto.UrlExpireTime != nil {\n\t\tvenueShouyinGrantGzhQR.UrlExpireTime = reqDto.UrlExpireTime\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenueShouyinGrantGzhQR.Remark = reqDto.Remark\n\t}\n\n\terr = venueShouyinGrantGzhQRService.UpdateVenueShouyinGrantGzhQR(ctx, venueShouyinGrantGzhQR)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venueShouyinGrantGzhQRTransfer.PoToVo(*venueShouyinGrantGzhQR))\n}\n\n// @Summary 删除门店收银机授权公众号二维码\n// @Description 删除门店收银机授权公众号二维码\n// @Tags 门店收银机授权公众号二维码\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVenueShouyinGrantGzhQRReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-shouyin-grant-gzh-qr/delete [post]\nfunc (controller *VenueShouyinGrantGzhQRController) DeleteVenueShouyinGrantGzhQR(ctx *gin.Context) {\n\treqDto := req.DeleteVenueShouyinGrantGzhQRReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = venueShouyinGrantGzhQRService.DeleteVenueShouyinGrantGzhQR(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询门店收银机授权公众号二维码\n// @Description 查询门店收银机授权公众号二维码\n// @Tags 门店收银机授权公众号二维码\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueShouyinGrantGzhQRReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VenueShouyinGrantGzhQRVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-shouyin-grant-gzh-qr/query [post]\nfunc (controller *VenueShouyinGrantGzhQRController) QueryVenueShouyinGrantGzhQRs(ctx *gin.Context) {\n\treqDto := req.QueryVenueShouyinGrantGzhQRReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := venueShouyinGrantGzhQRService.FindAllVenueShouyinGrantGzhQR(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.VenueShouyinGrantGzhQRVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, venueShouyinGrantGzhQRTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询门店收银机授权公众号二维码列表\n// @Description 查询门店收银机授权公众号二维码列表\n// @Tags 门店收银机授权公众号二维码\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenueShouyinGrantGzhQRReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.VenueShouyinGrantGzhQRVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-shouyin-grant-gzh-qr/list [post]\nfunc (controller *VenueShouyinGrantGzhQRController) ListVenueShouyinGrantGzhQRs(ctx *gin.Context) {\n\treqDto := req.QueryVenueShouyinGrantGzhQRReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := venueShouyinGrantGzhQRService.FindAllVenueShouyinGrantGzhQRWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.VenueShouyinGrantGzhQRVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VenueShouyinGrantGzhQRVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, venueShouyinGrantGzhQRTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenueShouyinGrantGzhQRRoute struct {\n}\n\nfunc (s *VenueShouyinGrantGzhQRRoute) InitVenueShouyinGrantGzhQRRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvenueShouyinGrantGzhQRController := controller.VenueShouyinGrantGzhQRController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/venue-shouyin-grant-gzh-qr/add\", venueShouyinGrantGzhQRController.AddVenueShouyinGrantGzhQR)       // add\n\t\troute.POST(\"/api/venue-shouyin-grant-gzh-qr/update\", venueShouyinGrantGzhQRController.UpdateVenueShouyinGrantGzhQR)   // update\n\t\troute.POST(\"/api/venue-shouyin-grant-gzh-qr/delete\", venueShouyinGrantGzhQRController.DeleteVenueShouyinGrantGzhQR)   // delete\n\t\troute.POST(\"/api/venue-shouyin-grant-gzh-qr/query\", venueShouyinGrantGzhQRController.QueryVenueShouyinGrantGzhQRs)    // query\n\t\troute.POST(\"/api/venue-shouyin-grant-gzh-qr/list\", venueShouyinGrantGzhQRController.ListVenueShouyinGrantGzhQRs)      // list\n\t}\n}\n"}]