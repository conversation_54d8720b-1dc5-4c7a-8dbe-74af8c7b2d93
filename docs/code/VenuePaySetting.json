[{"po": "package po\n\n// VenuePaySetting 门店支付设置实体\ntype VenuePaySetting struct {\n\tId            *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tVenueId       *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`        // 门店ID\n\tKtvid         *string `gorm:\"column:ktvid;type:varchar(64);default:''\" json:\"ktvid\"`            // 房间ID\n\tSubMerchantId *string `gorm:\"column:sub_merchant_id;type:varchar(64);default:''\" json:\"subMerchantId\"` // 乐刷子商户ID\n\tRemark        *string `gorm:\"column:remark;type:varchar(255);default:''\" json:\"remark\"`         // 备注\n\tCtime         *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                    // 创建时间\n\tUtime         *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                    // 更新时间\n\tState         *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                    // 状态\n\tVersion       *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                // 版本号\n}\n\n// TableName 设置表名\nfunc (VenuePaySetting) TableName() string {\n\treturn \"venue_pay_setting\"\n}\n\nfunc (v VenuePaySetting) GetId() string {\n\treturn *v.Id\n}\n", "vo": "package vo\n\n// VenuePaySettingVO 门店支付设置值对象\ntype VenuePaySettingVO struct {\n\tId            string `json:\"id\"`            // ID\n\tVenueId       string `json:\"venueId\"`       // 门店ID\n\tKtvid         string `json:\"ktvid\"`         // 房间ID\n\tSubMerchantId string `json:\"subMerchantId\"` // 乐刷子商户ID\n\tRemark        string `json:\"remark\"`        // 备注\n\tCtime         int64  `json:\"ctime\"`         // 创建时间\n\tUtime         int64  `json:\"utime\"`         // 更新时间\n\tState         int    `json:\"state\"`         // 状态\n\tVersion       int    `json:\"version\"`       // 版本号\n}\n", "req_add": "package req\n\n// AddVenuePaySettingReqDto 创建门店支付设置请求DTO\ntype AddVenuePaySettingReqDto struct {\n\tVenueId       *string `json:\"venueId\"`       // 门店ID\n\tKtvid         *string `json:\"ktvid\"`         // 房间ID\n\tSubMerchantId *string `json:\"subMerchantId\"` // 乐刷子商户ID\n\tRemark        *string `json:\"remark\"`        // 备注\n}\n", "req_update": "package req\n\ntype UpdateVenuePaySettingReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tVenueId       *string `json:\"venueId\"`       // 门店ID\n\tKtvid         *string `json:\"ktvid\"`         // 房间ID\n\tSubMerchantId *string `json:\"subMerchantId\"` // 乐刷子商户ID\n\tRemark        *string `json:\"remark\"`        // 备注\n}\n", "req_delete": "package req\n\ntype DeleteVenuePaySettingReqDto struct {\n\tId *string `json:\"id\"` // ID\n}\n", "req_query": "package req\n\ntype QueryVenuePaySettingReqDto struct {\n\tId            *string `json:\"id\"`            // ID\n\tVenueId       *string `json:\"venueId\"`       // 门店ID\n\tKtvid         *string `json:\"ktvid\"`         // 房间ID\n\tSubMerchantId *string `json:\"subMerchantId\"` // 乐刷子商户ID\n\tRemark        *string `json:\"remark\"`        // 备注\n\tPageNum       *int    `json:\"pageNum\"`       // 页码\n\tPageSize      *int    `json:\"pageSize\"`      // 每页记录数\n}\n", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype VenuePaySettingTransfer struct {\n}\n\nfunc (transfer *VenuePaySettingTransfer) PoToVo(po po.VenuePaySetting) vo.VenuePaySettingVO {\n\tvo := vo.VenuePaySettingVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *VenuePaySettingTransfer) VoToPo(vo vo.VenuePaySettingVO) po.VenuePaySetting {\n\tpo := po.VenuePaySetting{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}\n", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\t\"voderpltvv/util\"\n\n\t\"github.com/gin-gonic/gin\"\n\t\"gorm.io/gorm\"\n)\n\ntype VenuePaySettingService struct {\n}\n\nvar (\n\tvenuePaySettingService = VenuePaySettingService{}\n\tvenuePaySettingTransfer = transfer.VenuePaySettingTransfer{}\n)\n\nfunc (service *VenuePaySettingService) CreateVenuePaySetting(logCtx *gin.Context, venuePaySetting *po.VenuePaySetting) error {\n\treturn Save(venuePaySetting)\n}\n\nfunc (service *VenuePaySettingService) CreateVenuePaySettingWithTx(logCtx *gin.Context, venuePaySetting *po.VenuePaySetting, tx *gorm.DB) error {\n\treturn SaveWithTx(venuePaySetting, tx)\n}\n\nfunc (service *VenuePaySettingService) UpdateVenuePaySetting(logCtx *gin.Context, venuePaySetting *po.VenuePaySetting) error {\n\treturn Update(venuePaySetting)\n}\n\nfunc (service *VenuePaySettingService) UpdateVenuePaySettingPartial(logCtx *gin.Context, venuePaySetting *po.VenuePaySetting) error {\n\treturn UpdateNotNull(venuePaySetting)\n}\n\nfunc (service *VenuePaySettingService) UpdateVenuePaySettingPartialWithTx(logCtx *gin.Context, venuePaySetting *po.VenuePaySetting, tx *gorm.DB) error {\n\treturn UpdateNotNullWithTx(venuePaySetting, tx)\n}\n\nfunc (service *VenuePaySettingService) DeleteVenuePaySetting(logCtx *gin.Context, id string) error {\n\treturn Delete(po.VenuePaySetting{Id: &id})\n}\n\nfunc (service *VenuePaySettingService) FindVenuePaySettingById(logCtx *gin.Context, id string) (venuePaySetting *po.VenuePaySetting, err error) {\n\tvenuePaySetting = &po.VenuePaySetting{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(venuePaySetting).Error\n\treturn\n}\n\nfunc (service *VenuePaySettingService) FindAllVenuePaySetting(logCtx *gin.Context, reqDto *req.QueryVenuePaySettingReqDto) (list *[]po.VenuePaySetting, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenuePaySetting{})\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Ktvid != nil && *reqDto.Ktvid != \"\" {\n\t\tdb = db.Where(\"ktvid=?\", *reqDto.Ktvid)\n\t}\n\tif reqDto.SubMerchantId != nil && *reqDto.SubMerchantId != \"\" {\n\t\tdb = db.Where(\"sub_merchant_id=?\", *reqDto.SubMerchantId)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark=?\", *reqDto.Remark)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.VenuePaySetting{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *VenuePaySettingService) FindAllVenuePaySettingWithPagination(logCtx *gin.Context, reqDto *req.QueryVenuePaySettingReqDto) (list *[]po.VenuePaySetting, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.VenuePaySetting{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\treqDto.PageNum = util.GetItPtr(1)\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\treqDto.PageSize = util.GetItPtr(10)\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.Ktvid != nil && *reqDto.Ktvid != \"\" {\n\t\tdb = db.Where(\"ktvid=?\", *reqDto.Ktvid)\n\t}\n\tif reqDto.SubMerchantId != nil && *reqDto.SubMerchantId != \"\" {\n\t\tdb = db.Where(\"sub_merchant_id=?\", *reqDto.SubMerchantId)\n\t}\n\tif reqDto.Remark != nil && *reqDto.Remark != \"\" {\n\t\tdb = db.Where(\"remark=?\", *reqDto.Remark)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.VenuePaySetting{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}\n", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenuePaySettingController struct{}\n\nvar (\n\tvenuePaySettingService  = impl.VenuePaySettingService{}\n\tvenuePaySettingTransfer = transfer.VenuePaySettingTransfer{}\n)\n\n// @Summary 添加门店支付设置\n// @Description 添加门店支付设置\n// @Tags 门店支付设置\n// @Accept json\n// @Produce json\n// @Param body body req.AddVenuePaySettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenuePaySettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-pay-setting/add [post]\nfunc (controller *VenuePaySettingController) AddVenuePaySetting(ctx *gin.Context) {\n\treqDto := req.AddVenuePaySettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tvenuePaySetting := po.VenuePaySetting{}\n\tif reqDto.VenueId != nil {\n\t\tvenuePaySetting.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Ktvid != nil {\n\t\tvenuePaySetting.Ktvid = reqDto.Ktvid\n\t}\n\tif reqDto.SubMerchantId != nil {\n\t\tvenuePaySetting.SubMerchantId = reqDto.SubMerchantId\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenuePaySetting.Remark = reqDto.Remark\n\t}\n\n\terr = venuePaySettingService.CreateVenuePaySetting(ctx, &venuePaySetting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venuePaySettingTransfer.PoToVo(venuePaySetting))\n}\n\n// @Summary 更新门店支付设置\n// @Description 更新门店支付设置\n// @Tags 门店支付设置\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateVenuePaySettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.VenuePaySettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-pay-setting/update [post]\nfunc (controller *VenuePaySettingController) UpdateVenuePaySetting(ctx *gin.Context) {\n\treqDto := req.UpdateVenuePaySettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\n\tvenuePaySetting, err := venuePaySettingService.FindVenuePaySettingById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tif reqDto.VenueId != nil {\n\t\tvenuePaySetting.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.Ktvid != nil {\n\t\tvenuePaySetting.Ktvid = reqDto.Ktvid\n\t}\n\tif reqDto.SubMerchantId != nil {\n\t\tvenuePaySetting.SubMerchantId = reqDto.SubMerchantId\n\t}\n\tif reqDto.Remark != nil {\n\t\tvenuePaySetting.Remark = reqDto.Remark\n\t}\n\n\terr = venuePaySettingService.UpdateVenuePaySetting(ctx, venuePaySetting)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, venuePaySettingTransfer.PoToVo(*venuePaySetting))\n}\n\n// @Summary 删除门店支付设置\n// @Description 删除门店支付设置\n// @Tags 门店支付设置\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteVenuePaySettingReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-pay-setting/delete [post]\nfunc (controller *VenuePaySettingController) DeleteVenuePaySetting(ctx *gin.Context) {\n\treqDto := req.DeleteVenuePaySettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\terr = venuePaySettingService.DeleteVenuePaySetting(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询门店支付设置\n// @Description 查询门店支付设置\n// @Tags 门店支付设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenuePaySettingReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.VenuePaySettingVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-pay-setting/query [post]\nfunc (controller *VenuePaySettingController) QueryVenuePaySettings(ctx *gin.Context) {\n\treqDto := req.QueryVenuePaySettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, err := venuePaySettingService.FindAllVenuePaySetting(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tnewList := []vo.VenuePaySettingVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, venuePaySettingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询门店支付设置列表\n// @Description 查询门店支付设置列表\n// @Tags 门店支付设置\n// @Accept json\n// @Produce json\n// @Param body body req.QueryVenuePaySettingReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.PageVO[[]vo.VenuePaySettingVO]] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/venue-pay-setting/list [post]\nfunc (controller *VenuePaySettingController) ListVenuePaySettings(ctx *gin.Context) {\n\treqDto := req.QueryVenuePaySettingReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\n\tlist, totalCount, err := venuePaySettingService.FindAllVenuePaySettingWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\tpage := vo.PageVO[[]vo.VenuePaySettingVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.VenuePaySettingVO{}\n\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, venuePaySettingTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}\n", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype VenuePaySettingRoute struct {\n}\n\nfunc (s *VenuePaySettingRoute) InitVenuePaySettingRouter(g *gin.Engine) {\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\tvenuePaySettingController := controller.VenuePaySettingController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/venue-pay-setting/add\", venuePaySettingController.AddVenuePaySetting)       // add\n\t\troute.POST(\"/api/venue-pay-setting/update\", venuePaySettingController.UpdateVenuePaySetting)   // update\n\t\troute.POST(\"/api/venue-pay-setting/delete\", venuePaySettingController.DeleteVenuePaySetting)   // delete\n\t\troute.POST(\"/api/venue-pay-setting/query\", venuePaySettingController.QueryVenuePaySettings)    // query\n\t\troute.POST(\"/api/venue-pay-setting/list\", venuePaySettingController.ListVenuePaySettings)     // list\n\t}\n}\n"}]