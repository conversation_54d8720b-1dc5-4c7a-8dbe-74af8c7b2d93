[{"po": "package po\n\n// RoomFault 房间故障实体\ntype RoomFault struct {\n\tId              *string `gorm:\"column:id;type:varchar(64);primaryKey;not null\" json:\"id\"`              // ID\n\tVenueId         *string `gorm:\"column:venue_id;type:varchar(64);default:''\" json:\"venueId\"`         // 门店ID\n\tRoomId          *string `gorm:\"column:room_id;type:varchar(64);default:''\" json:\"roomId\"`          // 房间ID\n\tFaultType       *string `gorm:\"column:fault_type;type:varchar(64);default:''\" json:\"faultType\"`       // 故障类型\n\tFaultDescription *string `gorm:\"column:fault_description;type:text;\" json:\"faultDescription\"`         // 故障描述\n\tFaultTime       *int64  `gorm:\"column:fault_time;type:int;default:0\" json:\"faultTime\"`              // 故障时间\n\tCtime           *int64  `gorm:\"column:ctime;type:int;default:0\" json:\"ctime\"`                      // 创建时间\n\tUtime           *int64  `gorm:\"column:utime;type:int;default:0\" json:\"utime\"`                      // 更新时间\n\tState           *int    `gorm:\"column:state;type:int;default:0\" json:\"state\"`                      // 状态\n\tVersion         *int    `gorm:\"column:version;type:int;default:0\" json:\"version\"`                  // 版本号\n}\n\n// TableName 设置表名\nfunc (RoomFault) TableName() string {\n\treturn \"room_fault\"\n}\n\nfunc (r RoomFault) GetId() string {\n\treturn *r.Id\n}", "vo": "package vo\n\n// RoomFaultVO 房间故障值对象\ntype RoomFaultVO struct {\n\tId              string `json:\"id\"`              // ID\n\tVenueId         string `json:\"venueId\"`         // 门店ID\n\tRoomId          string `json:\"roomId\"`          // 房间ID\n\tFaultType       string `json:\"faultType\"`       // 故障类型\n\tFaultDescription string `json:\"faultDescription\"` // 故障描述\n\tFaultTime       int64  `json:\"faultTime\"`       // 故障时间\n\tCtime           int64  `json:\"ctime\"`           // 创建时间\n\tUtime           int64  `json:\"utime\"`           // 更新时间\n\tState           int    `json:\"state\"`           // 状态\n\tVersion         int    `json:\"version\"`         // 版本号\n}", "req_add": "package req\n\n// AddRoomFaultReqDto 创建房间故障请求DTO\ntype AddRoomFaultReqDto struct {\n\tVenueId         *string `json:\"venueId\"`         // 门店ID\n\tRoomId          *string `json:\"roomId\"`          // 房间ID\n\tFaultType       *string `json:\"faultType\"`       // 故障类型\n\tFaultDescription *string `json:\"faultDescription\"` // 故障描述\n\tFaultTime       *int64  `json:\"faultTime\"`       // 故障时间\n}", "req_update": "package req\n\n// UpdateRoomFaultReqDto 更新房间故障请求DTO\ntype UpdateRoomFaultReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tVenueId         *string `json:\"venueId\"`         // 门店ID\n\tRoomId          *string `json:\"roomId\"`          // 房间ID\n\tFaultType       *string `json:\"faultType\"`       // 故障类型\n\tFaultDescription *string `json:\"faultDescription\"` // 故障描述\n\tFaultTime       *int64  `json:\"faultTime\"`       // 故障时间\n}", "req_delete": "package req\n\n// DeleteRoomFaultReqDto 删除房间故障请求DTO\ntype DeleteRoomFaultReqDto struct {\n\tId *string `json:\"id\"` // ID\n}", "req_query": "package req\n\n// QueryRoomFaultReqDto 查询房间故障请求DTO\ntype QueryRoomFaultReqDto struct {\n\tId              *string `json:\"id\"`              // ID\n\tVenueId         *string `json:\"venueId\"`         // 门店ID\n\tRoomId          *string `json:\"roomId\"`          // 房间ID\n\tFaultType       *string `json:\"faultType\"`       // 故障类型\n\tFaultDescription *string `json:\"faultDescription\"` // 故障描述\n\tFaultTime       *int64  `json:\"faultTime\"`       // 故障时间\n\tPageNum         *int    `json:\"pageNum\"`         // 页码\n\tPageSize        *int    `json:\"pageSize\"`        // 每页记录数\n}", "transfer": "package transfer\n\nimport (\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/jinzhu/copier\"\n)\n\ntype RoomFaultTransfer struct {\n}\n\nfunc (transfer *RoomFaultTransfer) PoToVo(po po.RoomFault) vo.RoomFaultVO {\n\tvo := vo.RoomFaultVO{}\n\tcopier.Copy(&vo, &po)\n\treturn vo\n}\n\nfunc (transfer *RoomFaultTransfer) VoToPo(vo vo.RoomFaultVO) po.RoomFault {\n\tpo := po.RoomFault{}\n\tcopier.Copy(&po, &vo)\n\treturn po\n}", "service": "package impl\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/model\"\n\t\"voderpltvv/service/po\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomFaultService struct {\n}\n\nfunc (service *RoomFaultService) CreateRoomFault(logCtx *gin.Context, roomFault *po.RoomFault) error {\n\treturn Save(roomFault)\n}\n\nfunc (service *RoomFaultService) UpdateRoomFault(logCtx *gin.Context, roomFault *po.RoomFault) error {\n\treturn Update(roomFault)\n}\n\nfunc (service *RoomFaultService) DeleteRoomFault(logCtx *gin.Context, id string) error {\n\treturn Delete(po.RoomFault{Id: &id})\n}\n\nfunc (service *RoomFaultService) FindRoomFaultById(logCtx *gin.Context, id string) (roomFault *po.RoomFault, err error) {\n\troomFault = &po.RoomFault{}\n\terr = model.DBMaster.Self.Where(\"id=?\", id).First(roomFault).Error\n\treturn\n}\n\nfunc (service *RoomFaultService) FindAllRoomFault(logCtx *gin.Context, reqDto *req.QueryRoomFaultReqDto) (list *[]po.RoomFault, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomFault{})\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.FaultType != nil && *reqDto.FaultType != \"\" {\n\t\tdb = db.Where(\"fault_type=?\", *reqDto.FaultType)\n\t}\n\n\tdb = db.Order(\"ctime desc\")\n\tlist = &[]po.RoomFault{}\n\tresult := db.Find(list)\n\terr = result.Error\n\tif err != nil {\n\t\treturn\n\t}\n\treturn\n}\n\nfunc (service *RoomFaultService) FindAllRoomFaultWithPagination(logCtx *gin.Context, reqDto *req.QueryRoomFaultReqDto) (list *[]po.RoomFault, total int64, err error) {\n\tdb := model.DBSlave.Self.Model(&po.RoomFault{})\n\n\tif reqDto.PageNum == nil || *reqDto.PageNum <= 0 {\n\t\t*reqDto.PageNum = 1\n\t}\n\tif reqDto.PageSize == nil || *reqDto.PageSize <= 0 {\n\t\t*reqDto.PageSize = 10\n\t}\n\n\tif reqDto.Id != nil && *reqDto.Id != \"\" {\n\t\tdb = db.Where(\"id=?\", *reqDto.Id)\n\t}\n\tif reqDto.VenueId != nil && *reqDto.VenueId != \"\" {\n\t\tdb = db.Where(\"venue_id=?\", *reqDto.VenueId)\n\t}\n\tif reqDto.RoomId != nil && *reqDto.RoomId != \"\" {\n\t\tdb = db.Where(\"room_id=?\", *reqDto.RoomId)\n\t}\n\tif reqDto.FaultType != nil && *reqDto.FaultType != \"\" {\n\t\tdb = db.Where(\"fault_type=?\", *reqDto.FaultType)\n\t}\n\n\terr = db.Count(&total).Error\n\tif err != nil {\n\t\treturn\n\t}\n\tlist = &[]po.RoomFault{}\n\tif total <= 0 {\n\t\treturn\n\t}\n\t// 分页+排序\n\tdb = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)\n\tdb = db.Order(\"ctime desc\")\n\terr = db.Find(list).Error\n\treturn\n}", "controller": "package controller\n\nimport (\n\t\"voderpltvv/api/req\"\n\t\"voderpltvv/api/vo\"\n\t\"voderpltvv/service/impl\"\n\t\"voderpltvv/service/po\"\n\t\"voderpltvv/service/transfer\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomFaultController struct{}\n\nvar (\n\troomFaultService  = impl.RoomFaultService{}\n\troomFaultTransfer = transfer.RoomFaultTransfer{}\n)\n\n// @Summary 添加房间故障\n// @Description 添加房间故障\n// @Tags 房间故障\n// @Accept json\n// @Produce json\n// @Param body body req.AddRoomFaultReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomFaultVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomFault/add [post]\nfunc (controller *RoomFaultController) AddRoomFault(ctx *gin.Context) {\n\treqDto := req.AddRoomFaultReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\n\troomFault := po.RoomFault{}\n\tif reqDto.VenueId != nil {\n\t\troomFault.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.RoomId != nil {\n\t\troomFault.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.FaultType != nil {\n\t\troomFault.FaultType = reqDto.FaultType\n\t}\n\tif reqDto.FaultDescription != nil {\n\t\troomFault.FaultDescription = reqDto.FaultDescription\n\t}\n\tif reqDto.FaultTime != nil {\n\t\troomFault.FaultTime = reqDto.FaultTime\n\t}\n\terr = roomFaultService.CreateRoomFault(ctx, &roomFault)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomFaultTransfer.PoToVo(roomFault))\n}\n\n// @Summary 更新房间故障\n// @Description 更新房间故障\n// @Tags 房间故障\n// @Accept json\n// @Produce json\n// @Param body body req.UpdateRoomFaultReqDto true \"请求体\"\n// @Success 200 {object} Result[vo.RoomFaultVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomFault/update [post]\nfunc (controller *RoomFaultController) UpdateRoomFault(ctx *gin.Context) {\n\treqDto := req.UpdateRoomFaultReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.Id == nil || *reqDto.Id == \"\" {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, \"id不能为空\")\n\t\treturn\n\t}\n\troomFault, err := roomFaultService.FindRoomFaultById(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tif reqDto.VenueId != nil {\n\t\troomFault.VenueId = reqDto.VenueId\n\t}\n\tif reqDto.RoomId != nil {\n\t\troomFault.RoomId = reqDto.RoomId\n\t}\n\tif reqDto.FaultType != nil {\n\t\troomFault.FaultType = reqDto.FaultType\n\t}\n\tif reqDto.FaultDescription != nil {\n\t\troomFault.FaultDescription = reqDto.FaultDescription\n\t}\n\tif reqDto.FaultTime != nil {\n\t\troomFault.FaultTime = reqDto.FaultTime\n\t}\n\terr = roomFaultService.UpdateRoomFault(ctx, roomFault)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, roomFaultTransfer.PoToVo(*roomFault))\n}\n\n// @Summary 删除房间故障\n// @Description 删除房间故障\n// @Tags 房间故障\n// @Accept json\n// @Produce json\n// @Param body body req.DeleteRoomFaultReqDto true \"请求体\"\n// @Success 200 {object} Result[any] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomFault/delete [post]\nfunc (controller *RoomFaultController) DeleteRoomFault(ctx *gin.Context) {\n\treqDto := req.DeleteRoomFaultReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\terr = roomFaultService.DeleteRoomFault(ctx, *reqDto.Id)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tResult_success[any](ctx, nil)\n}\n\n// @Summary 查询房间故障\n// @Description 查询房间故障\n// @Tags 房间故障\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomFaultReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomFaultVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomFault/query [post]\nfunc (controller *RoomFaultController) QueryRoomFaults(ctx *gin.Context) {\n\treqDto := req.QueryRoomFaultReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tlist, err := roomFaultService.FindAllRoomFault(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tnewList := []vo.RoomFaultVO{}\n\tfor _, v := range *list {\n\t\tnewList = append(newList, roomFaultTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &newList)\n}\n\n// @Summary 查询房间故障列表\n// @Description 查询房间故障列表\n// @Tags 房间故障\n// @Accept json\n// @Produce json\n// @Param body body req.QueryRoomFaultReqDto true \"请求体\"\n// @Success 200 {object} Result[[]vo.RoomFaultVO] \"成功\"\n// @Failure 500 {object} Result[any] \"失败\"\n// @Router /api/roomFault/list [post]\nfunc (a *RoomFaultController) ListRoomFaults(ctx *gin.Context) {\n\treqDto := req.QueryRoomFaultReqDto{}\n\terr := ctx.ShouldBindJSON(&reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ParamError.Code, err.Error())\n\t\treturn\n\t}\n\tlist, totalCount, err := roomFaultService.FindAllRoomFaultWithPagination(ctx, &reqDto)\n\tif err != nil {\n\t\tResult_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())\n\t\treturn\n\t}\n\tpage := vo.PageVO[[]vo.RoomFaultVO]{}\n\tpage.PageNum = *reqDto.PageNum\n\tpage.PageSize = *reqDto.PageSize\n\tpage.Total = totalCount\n\tpage.Data = []vo.RoomFaultVO{}\n\tfor _, v := range *list {\n\t\tpage.Data = append(page.Data, roomFaultTransfer.PoToVo(v))\n\t}\n\tResult_success[any](ctx, &page)\n}", "router": "package router\n\nimport (\n\t\"voderpltvv/controller\"\n\n\t\"github.com/gin-gonic/gin\"\n)\n\ntype RoomFaultRoute struct {\n}\n\nfunc (s *RoomFaultRoute) InitRoomFaultRouter(g *gin.Engine) {\n\n\t// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码\n\troomFaultController := controller.RoomFaultController{}\n\troute := g.Group(\"\")\n\t{\n\t\troute.POST(\"/api/roomFault/add\", roomFaultController.AddRoomFault)    //add\n\t\troute.POST(\"/api/roomFault/update\", roomFaultController.UpdateRoomFault) //update\n\t\troute.POST(\"/api/roomFault/delete\", roomFaultController.DeleteRoomFault) //delete\n\t\troute.POST(\"/api/roomFault/query\", roomFaultController.QueryRoomFaults)     //query\n\t\troute.POST(\"/api/roomFault/list\", roomFaultController.ListRoomFaults)     //list\n\t}\n}"}]