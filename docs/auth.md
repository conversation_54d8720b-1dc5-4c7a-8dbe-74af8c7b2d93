收银台   /auth   req.mac   req.token（unionid、手机号）
1. 查询 mac 记录 
    有记录
        存在 venueId？
            -> :login

        不存在 venueId？ 无权限
            取 scene_str -> 公众号
    无记录
        创建mac记录+生成关注二维码 scene_str -> 公众号

/login 
    mac + venueId  (手机号 + password) || ( unionid)
    :login.有token
    无对应关系 -> 返回403
    
    

:login
    有token
        过期 
            -> 返回407
        未过期
            token 解析出 手机号、unionid 对比 employee 表 unionid 或者 手机号
                存在对应关系
                    -> 更新token  + 权限  + 门店信息  
                不存在对应关系
                    -> 返回401
    无token
        -> 返回407
           

公众号
    scene_str 生成小程序连接

小程序
pages/auth/login?scene_str=xxx
    无scene_str
        有token
            过期 
                -> 返回407 login流程
                        
            未过期
                token 解析出 unionid 对比 employee 表 unionid 
                    存在对应关系，返回 门店id
                    不存在对应关系，返回401 创建门店流程
        无token
            -> 返回407  login流程
        
    有scene_str
        1. 调用api/grant/wx/miniapp/login  
            获取unionid openid 
        2. 调用api/auth/check/bind?scene_str=xxx&unionid=xxx&openid=xxx
            1. 通过scene_str 查询mac地址 
                检查mac对应的venueId
                    存在venueId
                        -> :login.未过期
                    不存在venueId
                        -> 返回401 + 创建/绑定门店
        3. bind门店
            1. 获取手机号
                调用 api/grant/wx/miniapp/phone 获取手机号
            2. 调用 api/bind/list?手机号&unionid
                查询老板身份创建的门店：手机号创建的门店列表
            3. 调用 api/bind?scene_str=xxx&venueId=xxx&phone=xxx&unionid=xxx
                1. 绑定对应关系
                2. 更新 employee 表，unionid/phone 变成老板
                3. 返回token
        4. 创建门店
            1. 获取手机号
                调用 api/grant/wx/miniapp/phone 获取手机号
            2. 调用 api/venue/create?phone=xxx&unionid=xxx 
                创建门店 绑定关系 【3.3】
                返回token
login流程
    获取unionId，调用服务获取unionId
    /login 判断 unionId是否有对应的门店
        有门店
            更新token  + 权限  + 门店信息  
        无门店
            返回401 创建门店流程
