# 会员系统表结构说明

## 表关系图

```mermaid
erDiagram
    member ||--o{ member_points : "has points"
    member ||--o{ member_consumption : "consumes"
    member ||--o{ member_order : "places"
    member ||--o{ points_exchange_record : "exchanges"
    member ||--o{ member_card_operation : "operates"
    member ||--o{ venue_and_member : "belongs to"
    member_card_level ||--o{ member : "defines level"

    venue ||--o{ member_card_level : "has levels"
    venue ||--o{ venue_and_member : "has members"
    points_goods ||--o{ points_exchange_record : "is exchanged"
    member ||--o{ member_transfer_money_from : "transfers from"
    member ||--o{ member_transfer_money_to : "transfers to"

    member {
        string id PK
        string name
        string phone
        string card_number
        string card_type
        string level FK
        decimal balance
        int points
    }

    venue {
        string id PK
        string name
        string address
    }

    member_card_level {
        string id PK
        string venue_id FK
        string name
        int level
        decimal room_discount
        decimal product_discount
    }

    member_points {
        string id PK
        string member_id FK
        int total_points
        int available_points
        int used_points
    }

    member_consumption {
        string id PK
        string member_id FK
        string order_number
        string venue_id FK
        decimal amount
    }

    points_exchange_record {
        string id PK
        string member_id FK
        string goods_id FK
        int points_used
        int quantity
    }

    points_goods {
        string id PK
        string goods_name
        int points_required
        int quantity_per_exchange
    }

    member_card_operation {
        string id PK
        string member_id FK
        string card_id FK
        string operation_type
        decimal balance
    }

    member_transfer_money_from {
        string id PK
        string from_member_id FK
        string to_member_id FK
        decimal amount
        int points
    }

    member_transfer_money_to {
        string id PK
        string from_member_id FK
        string to_member_id FK
        decimal amount
        int points
    }

    venue_and_member {
        string id PK
        string venue_id FK
        string member_id FK
        string remark
    }
```

## 图例说明

- `||` 表示一对一关系
- `|o` 表示一对零或一关系
- `}o` 表示零或多对一关系
- `{o` 表示一对零或多关系
- `}|` 表示多对一关系
- `||` 表示必需的关系
- `o|` 表示可选的关系

关系线说明：
1. 实线表示强关联（必需关系）
2. 虚线表示弱关联（可选关系）
3. 箭头指向依赖方向

## 核心会员表

### 1. member (会员基础信息表)
主要字段：
- `id`: 主键
- `name`: 会员姓名
- `phone`: 手机号
- `card_number`: 卡号
- `card_type`: 卡类型
- `card_id`: 卡ID
- `points`: 积分
- `balance`: 余额
- `birthday`: 生日
- `gender`: 性别
- `total_consumption_times`: 总消费次数
- `total_consumption_amount`: 总消费金额
- `principal_balance`: 本金余额
- `bonus_balance`: 赠送余额
- `status`: 状态
- `level`: 等级

### 2. member_card_level (会员卡等级表)
主要字段：
- `id`: 主键
- `venue_id`: 场所ID
- `name`: 等级名称
- `level`: 等级值
- `card_type`: 卡类型
- `room_discount`: 包厢折扣
- `product_discount`: 商品折扣
- `service_fee_discount`: 服务费折扣
- `consumption_points_base`: 消费积分基数
- `consumption_points_per_base`: 每基数积分数

### 3. member_points (会员积分表)
主要字段：
- `id`: 主键
- `member_id`: 会员ID
- `total_points`: 总积分
- `available_points`: 可用积分
- `used_points`: 已使用积分

## 会员消费相关表

### 1. member_consumption (会员消费明细记录表)
主要字段：
- `id`: 主键
- `member_id`: 会员ID
- `order_number`: 结账单号
- `venue_id`: 场所ID
- `consumption_type`: 消费类型
- `amount`: 消费金额
- `remaining_balance`: 余额
- `operator_id`: 操作人ID

### 2. member_order (会员订单表)
主要字段：
- `id`: 主键
- `order_id`: 订单ID
- `member_id`: 会员ID
- `order_time`: 订单时间
- `status`: 状态

## 积分兑换相关表

### 1. points_exchange_record (积分兑换记录表)
主要字段：
- `id`: 主键
- `member_id`: 会员ID
- `card_number`: 会员卡号
- `goods_id`: 商品ID
- `points_used`: 使用积分
- `quantity`: 兑换数量
- `status`: 状态

### 2. points_goods (积分兑换商品表)
主要字段：
- `id`: 主键
- `goods_name`: 商品名称
- `points_required`: 所需积分
- `quantity_per_exchange`: 每份数量
- `exchange_limit`: 兑换限制

## 会员卡操作相关表

### 1. member_card_operation (会员办理记录表)
主要字段：
- `id`: 主键
- `member_id`: 会员ID
- `card_id`: 卡等级ID
- `card_number`: 卡号
- `operation_type`: 操作类型
- `balance`: 余额
- `receivable_amount`: 应收金额
- `actual_amount`: 实收金额

### 2. member_transfer_money (会员转账记录表)
主要字段：
- `id`: 主键
- `from_member_id`: 转出会员ID
- `to_member_id`: 转入会员ID
- `amount`: 转账金额
- `points`: 转账积分
- `status`: 状态

## 场所关联表

### venue_and_member (场所会员关联表)
主要字段：
- `id`: 主键
- `venue_id`: 场所ID
- `member_id`: 会员ID
- `remark`: 备注

## 表关系说明

1. 会员核心关系：
   - `member` 表是核心表，通过 `card_template_id` 关联 `member_card_level` 表
   - `member_points`