# MongoDB 数据库操作指南

## 1. Go 语言连接 MongoDB
### 1.1 多数据源连接配置
- 主数据库配置，包括uri，数据库名，用户名，密码
- 从数据库配置，包括uri，数据库名，用户名，密码
- 配置从viper中读取配置，需要验证用户名密码
    ```go
        // 获取 MongoDB 配置
        uri := viper.GetString("mongodbMaster.uri")
        dbName := viper.GetString("mongodbMaster.dbname")
        username := viper.GetString("mongodbMaster.username")
        password := viper.GetString("mongodbMaster.password")
    ```
- 读写分离策略

### 1.2 连接池配置
- 最大连接数
- 最小连接数
- 连接超时时间
- 空闲连接超时时间
- 最大连接生命周期

### 1.3 全局变量初始化
- 启动时初始化连接
- 主从数据库对象管理
- 错误处理机制

### 1.4 错误处理与重试机制
- 连接错误重试
- 超时处理
- 错误分类处理

### 1.5 批量操作
- 批量写入
- 批量更新
- 批量删除

## 2. MongoDB 操作封装
### 2.1 基础操作
- 插入文档 (Insert)
- 查询文档 (Find)
- 更新文档 (Update)
- 删除文档 (Delete)

### 2.2 高级操作
- 分页查询
  - 基础分页查询
  - 聚合分页查询
  - 批量分页查询
- 排序功能
  - 单字段排序
    - 升序/降序排序
    - 数值类型排序
    - 字符串类型排序
  - 多字段排序
    - 优先级排序
    - 组合排序规则
    - 自定义排序
  - 复合排序
    - 分页排序结合
    - 聚合排序结合
    - 条件排序
- 聚合统计
  - 分组统计
  - 聚合分页
- 事务处理
  - 事务ACID特性
  - 事务回滚
  - 事务超时

## 3. 用户模块测试用例
### 3.1 测试案例
- 用户注册
- 用户登录
- 用户信息修改
- 用户删除

### 3.2 文档生成
- API 接口文档
- 数据模型文档
- 测试报告

## 4. 常见问题与解决方案
### 4.1 连接失败处理
- 检查网络连接
- 验证认证信息
- 重试机制
- 错误日志记录

### 4.2 性能优化建议
- 合理设置连接池大小

### 4.3 最佳实践指南
- 合理使用事务
- 定期监控连接池状态
- 错误处理和重试策略
